<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Events/ServiceRequest</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Events</a></li>
         <li class="breadcrumb-item"><a href="index.html">ServiceRequest</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#11">App\Events\ServiceRequest\ActivityLog\BaseIssueEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/ServiceRequestActivityLogCreated.php.html#14">App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/ServiceRequestActivityLogDeleted.php.html#15">App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/ServiceRequestActivityLogUpdated.php.html#14">App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateServiceRequestActivityLog.php.html#13">App\Events\ServiceRequest\CreateServiceRequestActivityLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NewServiceRequestCreated.php.html#13">App\Events\ServiceRequest\NewServiceRequestCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaDeleteLulaWebhook.php.html#13">App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaDeleteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#16">App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityBroadcast.php.html#14">App\Events\ServiceRequest\ResidentAvailabilityBroadcast</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeleted.php.html#14">App\Events\ServiceRequest\ResidentAvailabilityDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityStore.php.html#14">App\Events\ServiceRequest\ResidentAvailabilityStore</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAccessInfoUpdated.php.html#10">App\Events\ServiceRequest\ServiceRequestAccessInfoUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAddressUpdated.php.html#13">App\Events\ServiceRequest\ServiceRequestAddressUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeAdded.php.html#16">App\Events\ServiceRequest\ServiceRequestAssigneeAdded</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeRemoved.php.html#13">App\Events\ServiceRequest\ServiceRequestAssigneeRemoved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeUpdated.php.html#16">App\Events\ServiceRequest\ServiceRequestAssigneeUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAwaitingAvailability.php.html#10">App\Events\ServiceRequest\ServiceRequestAwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestCreated.php.html#10">App\Events\ServiceRequest\ServiceRequestCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDescriptionUpdated.php.html#18">App\Events\ServiceRequest\ServiceRequestDescriptionUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteCreated.php.html#22">App\Events\ServiceRequest\ServiceRequestNoteCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteDeleted.php.html#16">App\Events\ServiceRequest\ServiceRequestNoteDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteUpdated.php.html#17">App\Events\ServiceRequest\ServiceRequestNoteUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChanged.php.html#14">App\Events\ServiceRequest\ServiceRequestPriorityChanged</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestResidentUpdated.php.html#14">App\Events\ServiceRequest\ServiceRequestResidentUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStateChange.php.html#17">App\Events\ServiceRequest\ServiceRequestStateChange</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#15">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderCreated.php.html#15">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderDeleted.php.html#13">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderIssueAssigned.php.html#14">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderIssueUpdated.php.html#14">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/SyncServiceRequestWorkOrder.php.html#12">App\Events\ServiceRequest\WorkOrder\SyncServiceRequestWorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseServiceRequestEvent.php.html#11">App\Events\ServiceRequest\BaseServiceRequestEvent</a></td><td class="text-right">4%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ServiceRequestNoteCreated.php.html#22">App\Events\ServiceRequest\ServiceRequestNoteCreated</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="ServiceRequestStateChange.php.html#17">App\Events\ServiceRequest\ServiceRequestStateChange</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ServiceRequestDescriptionUpdated.php.html#18">App\Events\ServiceRequest\ServiceRequestDescriptionUpdated</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ServiceRequestNoteUpdated.php.html#17">App\Events\ServiceRequest\ServiceRequestNoteUpdated</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#15">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#16">App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequestAssigneeUpdated.php.html#16">App\Events\ServiceRequest\ServiceRequestAssigneeUpdated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BaseServiceRequestEvent.php.html#11">App\Events\ServiceRequest\BaseServiceRequestEvent</a></td><td class="text-right">27</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#15"><abbr title="App\Events\ServiceRequest\ActivityLog\BaseIssueEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#17"><abbr title="App\Events\ServiceRequest\ActivityLog\BaseIssueEvent::getServiceRequestActivityLog">getServiceRequestActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#35"><abbr title="App\Events\ServiceRequest\ActivityLog\BaseIssueEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#45"><abbr title="App\Events\ServiceRequest\ActivityLog\BaseIssueEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/BaseIssueEvent.php.html#61"><abbr title="App\Events\ServiceRequest\ActivityLog\BaseIssueEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/ServiceRequestActivityLogCreated.php.html#21"><abbr title="App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/ServiceRequestActivityLogDeleted.php.html#22"><abbr title="App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/ServiceRequestActivityLogDeleted.php.html#32"><abbr title="App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/ServiceRequestActivityLogDeleted.php.html#48"><abbr title="App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleted::getServiceRequestActivityLog">getServiceRequestActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ActivityLog/ServiceRequestActivityLogUpdated.php.html#21"><abbr title="App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseServiceRequestEvent.php.html#25"><abbr title="App\Events\ServiceRequest\BaseServiceRequestEvent::getServiceRequestDetails">getServiceRequestDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseServiceRequestEvent.php.html#53"><abbr title="App\Events\ServiceRequest\BaseServiceRequestEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseServiceRequestEvent.php.html#63"><abbr title="App\Events\ServiceRequest\BaseServiceRequestEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateServiceRequestActivityLog.php.html#31"><abbr title="App\Events\ServiceRequest\CreateServiceRequestActivityLog::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NewServiceRequestCreated.php.html#20"><abbr title="App\Events\ServiceRequest\NewServiceRequestCreated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NewServiceRequestCreated.php.html#29"><abbr title="App\Events\ServiceRequest\NewServiceRequestCreated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NewServiceRequestCreated.php.html#39"><abbr title="App\Events\ServiceRequest\NewServiceRequestCreated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NewServiceRequestCreated.php.html#49"><abbr title="App\Events\ServiceRequest\NewServiceRequestCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NewServiceRequestCreated.php.html#59"><abbr title="App\Events\ServiceRequest\NewServiceRequestCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaDeleteLulaWebhook.php.html#20"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaDeleteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaDeleteLulaWebhook.php.html#25"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaDeleteLulaWebhook::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaDeleteLulaWebhook.php.html#30"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaDeleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#28"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#32"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::fetchMedia">fetchMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#51"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#62"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityBroadcast.php.html#18"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityBroadcast::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityBroadcast.php.html#29"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityBroadcast::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityBroadcast.php.html#39"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityBroadcast::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityBroadcast.php.html#49"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityBroadcast::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeleted.php.html#21"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityDeleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityStore.php.html#21"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityStore::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAccessInfoUpdated.php.html#14"><abbr title="App\Events\ServiceRequest\ServiceRequestAccessInfoUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAddressUpdated.php.html#22"><abbr title="App\Events\ServiceRequest\ServiceRequestAddressUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAddressUpdated.php.html#34"><abbr title="App\Events\ServiceRequest\ServiceRequestAddressUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAddressUpdated.php.html#44"><abbr title="App\Events\ServiceRequest\ServiceRequestAddressUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeAdded.php.html#23"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeAdded::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeRemoved.php.html#20"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeRemoved::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeUpdated.php.html#23"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeUpdated.php.html#32"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeUpdated.php.html#42"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeUpdated.php.html#52"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeUpdated.php.html#66"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAwaitingAvailability.php.html#17"><abbr title="App\Events\ServiceRequest\ServiceRequestAwaitingAvailability::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestCreated.php.html#18"><abbr title="App\Events\ServiceRequest\ServiceRequestCreated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDescriptionUpdated.php.html#25"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDescriptionUpdated.php.html#30"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDescriptionUpdated.php.html#40"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDescriptionUpdated.php.html#54"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDescriptionUpdated.php.html#64"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDescriptionUpdated.php.html#73"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::getServiceRequestDescription">getServiceRequestDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteCreated.php.html#32"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteCreated.php.html#66"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteCreated.php.html#76"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteCreated.php.html#93"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteCreated.php.html#103"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteDeleted.php.html#23"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteDeleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteDeleted.php.html#31"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteDeleted::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteDeleted.php.html#41"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteDeleted::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteDeleted.php.html#51"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteDeleted.php.html#61"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteUpdated.php.html#24"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteUpdated.php.html#32"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteUpdated.php.html#42"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteUpdated.php.html#52"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteUpdated.php.html#62"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChanged.php.html#22"><abbr title="App\Events\ServiceRequest\ServiceRequestPriorityChanged::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChanged.php.html#32"><abbr title="App\Events\ServiceRequest\ServiceRequestPriorityChanged::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestResidentUpdated.php.html#18"><abbr title="App\Events\ServiceRequest\ServiceRequestResidentUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestResidentUpdated.php.html#28"><abbr title="App\Events\ServiceRequest\ServiceRequestResidentUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestResidentUpdated.php.html#38"><abbr title="App\Events\ServiceRequest\ServiceRequestResidentUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStateChange.php.html#27"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStateChange.php.html#38"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStateChange.php.html#51"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStateChange.php.html#61"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStateChange.php.html#109"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStateChange.php.html#123"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::getServiceRequestActivityLog">getServiceRequestActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#22"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#29"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#44"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#52"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#62"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#78"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#101"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::currentWorkOrderAbilities">currentWorkOrderAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderCreated.php.html#22"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderCreated.php.html#32"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderCreated.php.html#44"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderDeleted.php.html#20"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderDeleted.php.html#30"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderIssueAssigned.php.html#23"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssigned::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderIssueUpdated.php.html#23"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/SyncServiceRequestWorkOrder.php.html#16"><abbr title="App\Events\ServiceRequest\WorkOrder\SyncServiceRequestWorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ServiceRequestNoteCreated.php.html#32"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::__construct">__construct</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequestNoteCreated.php.html#103"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequestDescriptionUpdated.php.html#73"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::getServiceRequestDescription">getServiceRequestDescription</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestNoteUpdated.php.html#62"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#32"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::fetchMedia">fetchMedia</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestNoteCreated.php.html#76"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestStateChange.php.html#61"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BaseServiceRequestEvent.php.html#25"><abbr title="App\Events\ServiceRequest\BaseServiceRequestEvent::getServiceRequestDetails">getServiceRequestDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#51"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequestAssigneeUpdated.php.html#52"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequestStateChange.php.html#109"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequestStateChange.php.html#123"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::getServiceRequestActivityLog">getServiceRequestActivityLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#101"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::currentWorkOrderAbilities">currentWorkOrderAbilities</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Mon Jun 30 0:32:48 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([31,1,0,0,0,0,0,0,0,0,0,3], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([92,0,0,0,0,0,0,0,0,0,0,1], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#11\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent<\/a>"],[0,1,"<a href=\"ActivityLog\/ServiceRequestActivityLogCreated.php.html#14\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogCreated<\/a>"],[0,3,"<a href=\"ActivityLog\/ServiceRequestActivityLogDeleted.php.html#15\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogDeleted<\/a>"],[0,1,"<a href=\"ActivityLog\/ServiceRequestActivityLogUpdated.php.html#14\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogUpdated<\/a>"],[4.166666666666666,5,"<a href=\"BaseServiceRequestEvent.php.html#11\">App\\Events\\ServiceRequest\\BaseServiceRequestEvent<\/a>"],[0,1,"<a href=\"CreateServiceRequestActivityLog.php.html#13\">App\\Events\\ServiceRequest\\CreateServiceRequestActivityLog<\/a>"],[0,5,"<a href=\"NewServiceRequestCreated.php.html#13\">App\\Events\\ServiceRequest\\NewServiceRequestCreated<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/ServiceRequestMediaDeleteLulaWebhook.php.html#13\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaDeleteLulaWebhook<\/a>"],[0,7,"<a href=\"PublicWebhook\/Lula\/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#16\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaUploadCompleteLulaWebhook<\/a>"],[0,4,"<a href=\"ResidentAvailabilityBroadcast.php.html#14\">App\\Events\\ServiceRequest\\ResidentAvailabilityBroadcast<\/a>"],[0,1,"<a href=\"ResidentAvailabilityDeleted.php.html#14\">App\\Events\\ServiceRequest\\ResidentAvailabilityDeleted<\/a>"],[0,1,"<a href=\"ResidentAvailabilityStore.php.html#14\">App\\Events\\ServiceRequest\\ResidentAvailabilityStore<\/a>"],[0,1,"<a href=\"ServiceRequestAccessInfoUpdated.php.html#10\">App\\Events\\ServiceRequest\\ServiceRequestAccessInfoUpdated<\/a>"],[0,3,"<a href=\"ServiceRequestAddressUpdated.php.html#13\">App\\Events\\ServiceRequest\\ServiceRequestAddressUpdated<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeAdded.php.html#16\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeAdded<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeRemoved.php.html#13\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeRemoved<\/a>"],[0,6,"<a href=\"ServiceRequestAssigneeUpdated.php.html#16\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated<\/a>"],[0,1,"<a href=\"ServiceRequestAwaitingAvailability.php.html#10\">App\\Events\\ServiceRequest\\ServiceRequestAwaitingAvailability<\/a>"],[0,1,"<a href=\"ServiceRequestCreated.php.html#10\">App\\Events\\ServiceRequest\\ServiceRequestCreated<\/a>"],[0,9,"<a href=\"ServiceRequestDescriptionUpdated.php.html#18\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated<\/a>"],[0,17,"<a href=\"ServiceRequestNoteCreated.php.html#22\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated<\/a>"],[0,5,"<a href=\"ServiceRequestNoteDeleted.php.html#16\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted<\/a>"],[0,8,"<a href=\"ServiceRequestNoteUpdated.php.html#17\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated<\/a>"],[0,2,"<a href=\"ServiceRequestPriorityChanged.php.html#14\">App\\Events\\ServiceRequest\\ServiceRequestPriorityChanged<\/a>"],[0,3,"<a href=\"ServiceRequestResidentUpdated.php.html#14\">App\\Events\\ServiceRequest\\ServiceRequestResidentUpdated<\/a>"],[0,10,"<a href=\"ServiceRequestStateChange.php.html#17\">App\\Events\\ServiceRequest\\ServiceRequestStateChange<\/a>"],[100,0,"<a href=\"ServiceRequestStateChanged.php.html#7\">App\\Events\\ServiceRequest\\ServiceRequestStateChanged<\/a>"],[0,8,"<a href=\"WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#15\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent<\/a>"],[100,0,"<a href=\"WorkOrder\/ServiceRequestWorkOrderCanceled.php.html#13\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderCanceled<\/a>"],[0,3,"<a href=\"WorkOrder\/ServiceRequestWorkOrderCreated.php.html#15\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderCreated<\/a>"],[0,2,"<a href=\"WorkOrder\/ServiceRequestWorkOrderDeleted.php.html#13\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderDeleted<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderIssueAssigned.php.html#14\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderIssueAssigned<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderIssueUpdated.php.html#14\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderIssueUpdated<\/a>"],[100,0,"<a href=\"WorkOrder\/ServiceRequestWorkOrderUpdated.php.html#13\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderUpdated<\/a>"],[0,1,"<a href=\"WorkOrder\/SyncServiceRequestWorkOrder.php.html#12\">App\\Events\\ServiceRequest\\WorkOrder\\SyncServiceRequestWorkOrder<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#15\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent::__construct<\/a>"],[0,1,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#17\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent::getServiceRequestActivityLog<\/a>"],[0,1,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#35\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#45\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent::broadcastOn<\/a>"],[0,1,"<a href=\"ActivityLog\/BaseIssueEvent.php.html#61\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent::broadcastWith<\/a>"],[0,1,"<a href=\"ActivityLog\/ServiceRequestActivityLogCreated.php.html#21\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogCreated::broadcastAs<\/a>"],[0,1,"<a href=\"ActivityLog\/ServiceRequestActivityLogDeleted.php.html#22\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogDeleted::broadcastAs<\/a>"],[0,1,"<a href=\"ActivityLog\/ServiceRequestActivityLogDeleted.php.html#32\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"ActivityLog\/ServiceRequestActivityLogDeleted.php.html#48\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogDeleted::getServiceRequestActivityLog<\/a>"],[0,1,"<a href=\"ActivityLog\/ServiceRequestActivityLogUpdated.php.html#21\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogUpdated::broadcastAs<\/a>"],[100,1,"<a href=\"BaseServiceRequestEvent.php.html#20\">App\\Events\\ServiceRequest\\BaseServiceRequestEvent::__construct<\/a>"],[0,2,"<a href=\"BaseServiceRequestEvent.php.html#25\">App\\Events\\ServiceRequest\\BaseServiceRequestEvent::getServiceRequestDetails<\/a>"],[0,1,"<a href=\"BaseServiceRequestEvent.php.html#53\">App\\Events\\ServiceRequest\\BaseServiceRequestEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"BaseServiceRequestEvent.php.html#63\">App\\Events\\ServiceRequest\\BaseServiceRequestEvent::broadcastOn<\/a>"],[0,1,"<a href=\"CreateServiceRequestActivityLog.php.html#31\">App\\Events\\ServiceRequest\\CreateServiceRequestActivityLog::__construct<\/a>"],[0,1,"<a href=\"NewServiceRequestCreated.php.html#20\">App\\Events\\ServiceRequest\\NewServiceRequestCreated::__construct<\/a>"],[0,1,"<a href=\"NewServiceRequestCreated.php.html#29\">App\\Events\\ServiceRequest\\NewServiceRequestCreated::broadcastQueue<\/a>"],[0,1,"<a href=\"NewServiceRequestCreated.php.html#39\">App\\Events\\ServiceRequest\\NewServiceRequestCreated::broadcastOn<\/a>"],[0,1,"<a href=\"NewServiceRequestCreated.php.html#49\">App\\Events\\ServiceRequest\\NewServiceRequestCreated::broadcastAs<\/a>"],[0,1,"<a href=\"NewServiceRequestCreated.php.html#59\">App\\Events\\ServiceRequest\\NewServiceRequestCreated::broadcastWith<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/ServiceRequestMediaDeleteLulaWebhook.php.html#20\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaDeleteLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/ServiceRequestMediaDeleteLulaWebhook.php.html#25\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaDeleteLulaWebhook::getServiceRequest<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/ServiceRequestMediaDeleteLulaWebhook.php.html#30\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaDeleteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#28\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaUploadCompleteLulaWebhook::__construct<\/a>"],[0,3,"<a href=\"PublicWebhook\/Lula\/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#32\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaUploadCompleteLulaWebhook::fetchMedia<\/a>"],[0,2,"<a href=\"PublicWebhook\/Lula\/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#51\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaUploadCompleteLulaWebhook::getServiceRequest<\/a>"],[0,1,"<a href=\"PublicWebhook\/Lula\/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#62\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaUploadCompleteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"ResidentAvailabilityBroadcast.php.html#18\">App\\Events\\ServiceRequest\\ResidentAvailabilityBroadcast::__construct<\/a>"],[0,1,"<a href=\"ResidentAvailabilityBroadcast.php.html#29\">App\\Events\\ServiceRequest\\ResidentAvailabilityBroadcast::broadcastOn<\/a>"],[0,1,"<a href=\"ResidentAvailabilityBroadcast.php.html#39\">App\\Events\\ServiceRequest\\ResidentAvailabilityBroadcast::broadcastAs<\/a>"],[0,1,"<a href=\"ResidentAvailabilityBroadcast.php.html#49\">App\\Events\\ServiceRequest\\ResidentAvailabilityBroadcast::broadcastWith<\/a>"],[0,1,"<a href=\"ResidentAvailabilityDeleted.php.html#21\">App\\Events\\ServiceRequest\\ResidentAvailabilityDeleted::__construct<\/a>"],[0,1,"<a href=\"ResidentAvailabilityStore.php.html#21\">App\\Events\\ServiceRequest\\ResidentAvailabilityStore::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestAccessInfoUpdated.php.html#14\">App\\Events\\ServiceRequest\\ServiceRequestAccessInfoUpdated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestAddressUpdated.php.html#22\">App\\Events\\ServiceRequest\\ServiceRequestAddressUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequestAddressUpdated.php.html#34\">App\\Events\\ServiceRequest\\ServiceRequestAddressUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestAddressUpdated.php.html#44\">App\\Events\\ServiceRequest\\ServiceRequestAddressUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeAdded.php.html#23\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeAdded::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeRemoved.php.html#20\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeRemoved::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeUpdated.php.html#23\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeUpdated.php.html#32\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeUpdated.php.html#42\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated::broadcastOn<\/a>"],[0,2,"<a href=\"ServiceRequestAssigneeUpdated.php.html#52\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeUpdated.php.html#66\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequestAwaitingAvailability.php.html#17\">App\\Events\\ServiceRequest\\ServiceRequestAwaitingAvailability::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestCreated.php.html#18\">App\\Events\\ServiceRequest\\ServiceRequestCreated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestDescriptionUpdated.php.html#25\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestDescriptionUpdated.php.html#30\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequestDescriptionUpdated.php.html#40\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequestDescriptionUpdated.php.html#54\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestDescriptionUpdated.php.html#64\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::broadcastWith<\/a>"],[0,4,"<a href=\"ServiceRequestDescriptionUpdated.php.html#73\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::getServiceRequestDescription<\/a>"],[0,6,"<a href=\"ServiceRequestNoteCreated.php.html#32\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestNoteCreated.php.html#66\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated::broadcastQueue<\/a>"],[0,3,"<a href=\"ServiceRequestNoteCreated.php.html#76\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequestNoteCreated.php.html#93\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated::broadcastAs<\/a>"],[0,6,"<a href=\"ServiceRequestNoteCreated.php.html#103\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequestNoteDeleted.php.html#23\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestNoteDeleted.php.html#31\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequestNoteDeleted.php.html#41\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequestNoteDeleted.php.html#51\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestNoteDeleted.php.html#61\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequestNoteUpdated.php.html#24\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestNoteUpdated.php.html#32\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequestNoteUpdated.php.html#42\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequestNoteUpdated.php.html#52\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated::broadcastAs<\/a>"],[0,4,"<a href=\"ServiceRequestNoteUpdated.php.html#62\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequestPriorityChanged.php.html#22\">App\\Events\\ServiceRequest\\ServiceRequestPriorityChanged::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestPriorityChanged.php.html#32\">App\\Events\\ServiceRequest\\ServiceRequestPriorityChanged::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequestResidentUpdated.php.html#18\">App\\Events\\ServiceRequest\\ServiceRequestResidentUpdated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestResidentUpdated.php.html#28\">App\\Events\\ServiceRequest\\ServiceRequestResidentUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestResidentUpdated.php.html#38\">App\\Events\\ServiceRequest\\ServiceRequestResidentUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequestStateChange.php.html#27\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestStateChange.php.html#38\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequestStateChange.php.html#51\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::broadcastAs<\/a>"],[0,3,"<a href=\"ServiceRequestStateChange.php.html#61\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::broadcastWith<\/a>"],[0,2,"<a href=\"ServiceRequestStateChange.php.html#109\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::getServiceRequest<\/a>"],[0,2,"<a href=\"ServiceRequestStateChange.php.html#123\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::getServiceRequestActivityLog<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#22\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#29\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#44\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#52\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#62\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#78\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::getWorkOrderDetails<\/a>"],[0,2,"<a href=\"WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#101\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::currentWorkOrderAbilities<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderCreated.php.html#22\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderCreated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderCreated.php.html#32\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderCreated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderCreated.php.html#44\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderCreated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderDeleted.php.html#20\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderDeleted::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderDeleted.php.html#30\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderIssueAssigned.php.html#23\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderIssueAssigned::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceRequestWorkOrderIssueUpdated.php.html#23\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderIssueUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/SyncServiceRequestWorkOrder.php.html#16\">App\\Events\\ServiceRequest\\WorkOrder\\SyncServiceRequestWorkOrder::__construct<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Listeners/WorkOrder/WorkOrderReadyToScheduleListener.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Listeners</a></li>
         <li class="breadcrumb-item"><a href="index.html">WorkOrder</a></li>
         <li class="breadcrumb-item active">WorkOrderReadyToScheduleListener.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="90.48" aria-valuemin="0" aria-valuemax="100" style="width: 90.48%">
           <span class="sr-only">90.48% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">90.48%</div></td>
       <td class="success small"><div align="right">57&nbsp;/&nbsp;63</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="sr-only">50.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">50.00%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;2</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><abbr title="App\Listeners\WorkOrder\WorkOrderReadyToScheduleListener">WorkOrderReadyToScheduleListener</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="90.48" aria-valuemin="0" aria-valuemax="100" style="width: 90.48%">
           <span class="sr-only">90.48% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">90.48%</div></td>
       <td class="success small"><div align="right">57&nbsp;/&nbsp;63</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="sr-only">50.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">50.00%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;2</div></td>
       <td class="danger small">7.04</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#27"><abbr title="__construct()">__construct</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#35"><abbr title="handle(App\Events\WorkOrder\WorkOrderReadyToSchedule $event): void">handle</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="90.32" aria-valuemin="0" aria-valuemax="100" style="width: 90.32%">
           <span class="sr-only">90.32% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">90.32%</div></td>
       <td class="success small"><div align="right">56&nbsp;/&nbsp;62</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6.03</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Listeners\WorkOrder</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Enums\WorkOrderSourceTypes</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Events\WorkOrder\ExternalWorkOrderUpdateEvent</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderReadyToScheduleLulaWebhook</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Events\WorkOrder\WorkOrderReadyToSchedule</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Events\WorkOrder\WorkOrderStateChange</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\WorkOrder</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\WorkOrderActivityLog</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\WorkOrderStatusLog</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Notifications\WorkOrder\WorkOrderStateChangedNotification</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Services\WorkOrderActivity\Enums\ActivityLogEventTypes</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Traits\BasicNotificationTrait</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Contracts\Queue\ShouldQueue</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\Notification</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">WorkOrderReadyToScheduleListener</span><span class="default">&nbsp;</span><span class="keyword">implements</span><span class="default">&nbsp;</span><span class="default">ShouldHandleEventsAfterCommit</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">ShouldQueue</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">BasicNotificationTrait</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Create&nbsp;the&nbsp;event&nbsp;listener.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">__construct</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 30" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Handle&nbsp;the&nbsp;event.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handle</span><span class="keyword">(</span><span class="default">WorkOrderReadyToSchedule</span><span class="default">&nbsp;</span><span class="default">$event</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 37" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$workOrder</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">WorkOrder</span><span class="default">::</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 38" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'resident:resident_id,resident_uuid,property_id,first_name,last_name,email,phone_number,company_name'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 39" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'workOrderSource:work_order_source_id,slug'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 40" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'status:work_order_status_id,label,slug'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 41" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 42" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 43" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'organization_uuid'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'is_appfolio_enabled'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'appfolio_vendor_id'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 44" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'name'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'appfolio_client_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'appfolio_client_secret'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'appfolio_customer_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'domain'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 45" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'appfolio_integrated_at'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'webhook_enabled'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'webhook_api_url'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'webhook_secret_key'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 46" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 47" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 48" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 49" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 50" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_order_uuid'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'organization_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'vendor_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'state'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 51" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_status_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'property_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'timezone_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_order_source_id'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 52" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'canceled_at'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'canceled_reason'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_order_reference_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_order_reference_number'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 53" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'requesting_resident_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_order_number'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 54" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 55" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">where</span><span class="keyword">(</span><span class="default">'work_order_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$event</span><span class="default">-&gt;</span><span class="default">workOrderId</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 56" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 58" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">WorkOrderStatusLog</span><span class="default">::</span><span class="default">create</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 59" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">work_order_id</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 60" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'updated_by_user_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$event</span><span class="default">-&gt;</span><span class="default">userId</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 61" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_status_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">work_order_status_id</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 62" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 64" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$workOrderActivityLog</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">WorkOrderActivityLog</span><span class="default">::</span><span class="default">create</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 65" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$event</span><span class="default">-&gt;</span><span class="default">workOrderId</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 66" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_task_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$event</span><span class="default">-&gt;</span><span class="default">taskId</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 67" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 68" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'triggered_by'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$event</span><span class="default">-&gt;</span><span class="default">userId</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 69" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'event'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">ActivityLogEventTypes</span><span class="default">::</span><span class="default">WORK_ORDER_STATUS_CHANGED</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 70" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'event_attributes'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$event</span><span class="default">-&gt;</span><span class="default">activityLogEventAttributes</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 71" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Trigger&nbsp;work&nbsp;order&nbsp;state&nbsp;change&nbsp;notification</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 74" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$assignees</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getAssigneesToNotify</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$event</span><span class="default">-&gt;</span><span class="default">userId</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 75" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$assignees</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">loadMissing</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'property:property_id,street_address,unit_number,city,state_id,postal_zip_code'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'property.state:state_id,state_code'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'timezone:timezone_id,name'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Notification</span><span class="default">::</span><span class="default">send</span><span class="keyword">(</span><span class="default">$assignees</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">WorkOrderStateChangedNotification</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$workOrderActivityLog</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;TO&nbsp;DO:&nbsp;create&nbsp;a&nbsp;new&nbsp;event&nbsp;for&nbsp;work&nbsp;order&nbsp;activity&nbsp;log.</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 85" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">event</span><span class="keyword">(</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">WorkOrderStateChange</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">work_order_id</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$workOrderActivityLog</span><span class="default">-&gt;</span><span class="default">work_order_activity_log_id</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">state</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 87" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">workOrderSource</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 88" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">loadMissing</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 89" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'tasks:work_order_task_id,work_order_id,problem_diagnosis_id'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 90" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'tasks.latestServiceCalls'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 91" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 92" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.work_order_service_call_id'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 93" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.technician_appointment_id'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 94" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.lula_appointment_id'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 95" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.scheduled_start_time'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 96" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.scheduled_end_time'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 97" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.duration_minutes'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 98" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.created_at'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 99" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 100" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 101" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Check&nbsp;the&nbsp;work&nbsp;order&nbsp;source&nbsp;and&nbsp;trigger&nbsp;nessasery&nbsp;event</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="13 tests cover line 105" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">workOrderSource</span><span class="default">-&gt;</span><span class="default">slug</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 106" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">workOrderSource</span><span class="default">-&gt;</span><span class="default">slug</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">WorkOrderSourceTypes</span><span class="default">::</span><span class="default">APPFOLIO</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 107" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">event</span><span class="keyword">(</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">ExternalWorkOrderUpdateEvent</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'Assigned'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 108" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">elseif</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">workOrderSource</span><span class="default">-&gt;</span><span class="default">slug</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">WorkOrderSourceTypes</span><span class="default">::</span><span class="default">LULA</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 109" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">event</span><span class="keyword">(</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">WorkOrderReadyToScheduleLulaWebhook</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.17</a> at Wed Jun 25 16:26:03 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/routes/web.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="index.html">routes</a></li>
         <li class="breadcrumb-item active">web.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;37</div></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
       <td class=" small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Controllers\Developer\HealthCheckController</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Controllers\Developer\LogViewerController</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Controllers\LulaWebhookController</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Controllers\PDFController</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Controllers\PublicAccessController</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Controllers\WebhookController</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\Route</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="comment">/*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="comment">|--------------------------------------------------------------------------</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="comment">|&nbsp;Web&nbsp;Routes</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="comment">|--------------------------------------------------------------------------</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="comment">|</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="comment">|&nbsp;Here&nbsp;is&nbsp;where&nbsp;you&nbsp;can&nbsp;register&nbsp;web&nbsp;routes&nbsp;for&nbsp;your&nbsp;application.&nbsp;These</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="comment">|&nbsp;routes&nbsp;are&nbsp;loaded&nbsp;by&nbsp;the&nbsp;RouteServiceProvider&nbsp;and&nbsp;all&nbsp;of&nbsp;them&nbsp;will</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="comment">|&nbsp;be&nbsp;assigned&nbsp;to&nbsp;the&nbsp;&quot;web&quot;&nbsp;middleware&nbsp;group.&nbsp;Make&nbsp;something&nbsp;great!</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="comment">|</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="comment">*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="comment">//&nbsp;Health&nbsp;Check&nbsp;Routes</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">Route</span><span class="default">::</span><span class="default">prefix</span><span class="keyword">(</span><span class="default">'hcp'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">group</span><span class="keyword">(</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">'/'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">HealthCheckController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'basicCheck'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">'utilities'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">HealthCheckController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'utilitiesCheck'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">Route</span><span class="default">::</span><span class="default">group</span><span class="keyword">(</span><span class="keyword">[</span><span class="default">'middleware'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'horizon.auth.basic'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'prefix'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'debug'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'as'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'Debug::'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">view</span><span class="keyword">(</span><span class="default">'dashboard'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'developer.dashboard.index'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'dashboard'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">group</span><span class="keyword">(</span><span class="keyword">[</span><span class="default">'prefix'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'logs'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'as'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'logs.'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">group</span><span class="keyword">(</span><span class="keyword">[</span><span class="default">'prefix'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'requests'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'as'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'request.'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">''</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">LogViewerController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'requestLogs'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'index'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">'{request_log:request_uuid}'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">LogViewerController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'requestLogShow'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'show'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">group</span><span class="keyword">(</span><span class="keyword">[</span><span class="default">'prefix'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'developer-alerts'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'as'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'developer.alerts.'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">''</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">LogViewerController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'developerAlerts'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'index'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">'{developer_alert}'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">LogViewerController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'developerAlertShow'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'show'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">group</span><span class="keyword">(</span><span class="keyword">[</span><span class="default">'prefix'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'vendor/public/api'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'as'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'vendor.public.api.'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">''</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">LogViewerController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'vendorPublicApiLogs'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'index'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">'{vendor_public_api_log}'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">LogViewerController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'vendorPublicApiLogShow'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'show'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">group</span><span class="keyword">(</span><span class="keyword">[</span><span class="default">'prefix'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'webhooks'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'as'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'webhook.'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">group</span><span class="keyword">(</span><span class="keyword">[</span><span class="default">'prefix'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'incoming'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">''</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">LogViewerController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'incomingWebhookLogs'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'incoming.index'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">'{webhook_call:webhook_call_uuid}'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">LogViewerController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'incomingWebhookLogShow'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'incoming.show'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">group</span><span class="keyword">(</span><span class="keyword">[</span><span class="default">'prefix'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'outgoing'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">''</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">LogViewerController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'outgoingWebhookLogs'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'outgoing.index'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">'{public_api_wo_webhook_event_uuid}'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">LogViewerController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'outgoingWebhookLogShow'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'outgoing.show'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">'pdf/quote/{quote}'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">PDFController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'viewQuote'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'pdf.quote'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">middleware</span><span class="keyword">(</span><span class="default">'signed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">'media/{media}/{type?}/{filename?}'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">PublicAccessController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'getMedia'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'public.media'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">middleware</span><span class="keyword">(</span><span class="default">'signed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">'service-request/media/{media}/{type?}/{filename?}'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">PublicAccessController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'getSrMedia'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'public.media.service_request'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">middleware</span><span class="keyword">(</span><span class="default">'signed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">Route</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="default">'pdf/invoice/{invoice}'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">PDFController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'viewInvoice'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'pdf.invoice'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">middleware</span><span class="keyword">(</span><span class="default">'signed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">Route</span><span class="default">::</span><span class="default">post</span><span class="keyword">(</span><span class="default">'webhook'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">WebhookController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'webhook'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">Route</span><span class="default">::</span><span class="default">post</span><span class="keyword">(</span><span class="default">'vendor/lula/webhook'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">LulaWebhookController</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">(</span><span class="default">'webhook.lula'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

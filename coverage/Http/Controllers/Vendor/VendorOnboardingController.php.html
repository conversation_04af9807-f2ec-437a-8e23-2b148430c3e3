<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Http/Controllers/Vendor/VendorOnboardingController.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Controllers</a></li>
         <li class="breadcrumb-item"><a href="index.html">Vendor</a></li>
         <li class="breadcrumb-item active">VendorOnboardingController.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="92.71" aria-valuemin="0" aria-valuemax="100" style="width: 92.71%">
           <span class="sr-only">92.71% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">92.71%</div></td>
       <td class="success small"><div align="right">305&nbsp;/&nbsp;329</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="11.11" aria-valuemin="0" aria-valuemax="100" style="width: 11.11%">
           <span class="sr-only">11.11% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">11.11%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;9</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController">VendorOnboardingController</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="92.71" aria-valuemin="0" aria-valuemax="100" style="width: 92.71%">
           <span class="sr-only">92.71% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">92.71%</div></td>
       <td class="success small"><div align="right">305&nbsp;/&nbsp;329</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="11.11" aria-valuemin="0" aria-valuemax="100" style="width: 11.11%">
           <span class="sr-only">11.11% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">11.11%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;9</div></td>
       <td class="danger small">61.40</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#50"><abbr title="sendVendorOnboardingLink(Illuminate\Http\Request $request): Illuminate\Http\JsonResponse">sendVendorOnboardingLink</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="98.15" aria-valuemin="0" aria-valuemax="100" style="width: 98.15%">
           <span class="sr-only">98.15% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">98.15%</div></td>
       <td class="success small"><div align="right">53&nbsp;/&nbsp;54</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">8</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#141"><abbr title="setBasicInfo(App\Http\Requests\Vendor\VendorOnboardingSetBasicInfoRequest $request): \Illuminate\Http\JsonResponse|\App\Http\Resources\Vendor\BasicInfoResource">setBasicInfo</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="98.11" aria-valuemin="0" aria-valuemax="100" style="width: 98.11%">
           <span class="sr-only">98.11% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">98.11%</div></td>
       <td class="success small"><div align="right">52&nbsp;/&nbsp;53</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">8</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#222"><abbr title="getVendorOnboarding(Illuminate\Http\Request $request): \Illuminate\Http\JsonResponse|\App\Http\Resources\Vendor\VendorOnboardingResource">getVendorOnboarding</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="95.00" aria-valuemin="0" aria-valuemax="100" style="width: 95.00%">
           <span class="sr-only">95.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">95.00%</div></td>
       <td class="success small"><div align="right">19&nbsp;/&nbsp;20</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">5</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#257"><abbr title="getStatus(Illuminate\Http\Request $request): Illuminate\Http\JsonResponse">getStatus</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="98.18" aria-valuemin="0" aria-valuemax="100" style="width: 98.18%">
           <span class="sr-only">98.18% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">98.18%</div></td>
       <td class="success small"><div align="right">54&nbsp;/&nbsp;55</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#331"><abbr title="setStatus(App\Http\Requests\Vendor\VendorOnboardingSetStatusRequest $request): Illuminate\Http\JsonResponse">setStatus</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="92.86" aria-valuemin="0" aria-valuemax="100" style="width: 92.86%">
           <span class="sr-only">92.86% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">92.86%</div></td>
       <td class="success small"><div align="right">39&nbsp;/&nbsp;42</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">14.07</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#407"><abbr title="setServices(App\Http\Requests\Vendor\VendorOnboardingSetServicesRequest $request): Illuminate\Http\JsonResponse">setServices</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="96.77" aria-valuemin="0" aria-valuemax="100" style="width: 96.77%">
           <span class="sr-only">96.77% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">96.77%</div></td>
       <td class="success small"><div align="right">30&nbsp;/&nbsp;31</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#467"><abbr title="setServiceAreas(App\Http\Requests\Vendor\VendorOnBoardingSetServiceAreasRequest $request): \Illuminate\Http\JsonResponse|\App\Http\Resources\Vendor\ServiceAreaResource">setServiceAreas</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="97.44" aria-valuemin="0" aria-valuemax="100" style="width: 97.44%">
           <span class="sr-only">97.44% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">97.44%</div></td>
       <td class="success small"><div align="right">38&nbsp;/&nbsp;39</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#535"><abbr title="processServices(array $services, App\Models\VendorOnboarding $vendorOnboarding): array">processServices</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="55.88" aria-valuemin="0" aria-valuemax="100" style="width: 55.88%">
           <span class="sr-only">55.88% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">55.88%</div></td>
       <td class="warning small"><div align="right">19&nbsp;/&nbsp;34</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">9.09</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#585"><abbr title="minutesToDaysSimple($minutes)">minutesToDaysSimple</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Http\Controllers\Vendor</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Enums\UserTypes</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Enums\VendorOnBoardingStatus</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">VendorOnBoardingStatusEnum</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Enums\VendorOnboardingStatuses</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Exceptions\ForbiddenException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Exceptions\NotFoundException\UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Helpers\OnboardingHelper</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Controllers\Controller</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Requests\Vendor\VendorOnboardingSetBasicInfoRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Requests\Vendor\VendorOnBoardingSetServiceAreasRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Requests\Vendor\VendorOnboardingSetServicesRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Requests\Vendor\VendorOnboardingSetStatusRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Resources\Vendor\BasicInfoResource</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Resources\Vendor\ServiceAreaResource</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Resources\Vendor\VendorOnboardingResource</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Mail\OnboardingMail</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\Organization</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\ProblemSubCategory</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\State</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\User</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\Vendor</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\VendorOnboarding</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\VendorOnboardingStatus</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\VendorService</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\VendorServiceArea</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\VendorUser</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Traits\ApiExceptionHandler</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Traits\ApiResponse</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Carbon\Carbon</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Http\JsonResponse</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Http\Request</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\DB</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\Mail</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Spatie\ModelStates\Exceptions\CouldNotPerformTransition</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Throwable</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingController</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">Controller</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">ApiExceptionHandler</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">ApiResponse</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">const</span><span class="default">&nbsp;</span><span class="default">STATUS_SELECT_FIELDS</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">'status:vendor_onboarding_status_id,sort_order,slug'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Send&nbsp;onboarding&nbsp;link</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">sendVendorOnboardingLink</span><span class="keyword">(</span><span class="default">Request</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">JsonResponse</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Get&nbsp;user</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 54" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 55" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Validate&nbsp;inputs</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 60" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">validate</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 61" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'type'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'required'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'in:third-party'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 62" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'required'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'array'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'min:1'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 63" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor_id.*'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'required'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'uuid'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 64" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Get&nbsp;request&nbsp;inputs</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 67" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorUuids</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'vendor_id'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 68" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$type</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'type'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 69" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$isAccountUser</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_type</span><span class="default">&nbsp;</span><span class="default">==</span><span class="default">&nbsp;</span><span class="default">UserTypes</span><span class="default">::</span><span class="default">ACCOUNT_USER</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;the&nbsp;account&nbsp;user&nbsp;to&nbsp;access</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 72" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$isAccountUser</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 73" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 76" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">beginTransaction</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Get&nbsp;vendor&nbsp;status&nbsp;and&nbsp;organization</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 79" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$initialStatus</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatus</span><span class="default">::</span><span class="default">where</span><span class="keyword">(</span><span class="default">'slug'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatuses</span><span class="default">::</span><span class="default">INITIAL</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 80" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$organization</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">Organization</span><span class="default">::</span><span class="default">where</span><span class="keyword">(</span><span class="default">'organization_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Generate&nbsp;and&nbsp;send&nbsp;signed&nbsp;url&nbsp;to&nbsp;all&nbsp;vendor</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 83" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">foreach</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$vendorUuids</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">$vendorUuid</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 84" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendor</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">Vendor</span><span class="default">::</span><span class="default">whereUuid</span><span class="keyword">(</span><span class="default">$vendorUuid</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Create&nbsp;or&nbsp;update&nbsp;vendor&nbsp;(reset&nbsp;the&nbsp;access_count&nbsp;to&nbsp;0&nbsp;if&nbsp;onboarding&nbsp;already&nbsp;exist)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 87" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnboarding</span><span class="default">::</span><span class="default">updateOrCreate</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 88" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 89" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$vendor</span><span class="default">-&gt;</span><span class="default">vendor_id</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 90" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$organization</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 91" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 92" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 93" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'type'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$type</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 94" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor_onboarding_status_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$initialStatus</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_status_id</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 95" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'access_count'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">0</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 96" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 97" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Generate&nbsp;signed&nbsp;url</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 100" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$signedUrl</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">OnboardingHelper</span><span class="default">::</span><span class="default">generateSignedUrl</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 101" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor_onboarding.invite'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 102" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_uuid</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 103" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$organization</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 104" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">config</span><span class="keyword">(</span><span class="default">'services.cognito.provider.onboarding_link_expiration'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">10080</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 105" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Create&nbsp;frontend&nbsp;signed&nbsp;url</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 108" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$url</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">parse_url</span><span class="keyword">(</span><span class="default">$signedUrl</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 109" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$queryParams</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 110" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$url</span><span class="keyword">[</span><span class="default">'query'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 111" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">parse_str</span><span class="keyword">(</span><span class="default">$url</span><span class="keyword">[</span><span class="default">'query'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$queryParams</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 113" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">'/onboarding'</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 114" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$url</span><span class="keyword">[</span><span class="default">'query'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$url</span><span class="keyword">[</span><span class="default">'query'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 115" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">&nbsp;</span><span class="default">.=</span><span class="default">&nbsp;</span><span class="default">'?'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$url</span><span class="keyword">[</span><span class="default">'query'</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 117" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$frontendUrl</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">config</span><span class="keyword">(</span><span class="default">'services.cognito.provider.vendor_domain'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Send&nbsp;signed&nbsp;url&nbsp;to&nbsp;vendor&nbsp;email</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 120" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Mail</span><span class="default">::</span><span class="default">to</span><span class="keyword">(</span><span class="default">$vendor</span><span class="default">-&gt;</span><span class="default">email</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 121" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">queue</span><span class="keyword">(</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">OnboardingMail</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 122" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$frontendUrl</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 123" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">minutesToDaysSimple</span><span class="keyword">(</span><span class="default">config</span><span class="keyword">(</span><span class="default">'services.cognito.provider.onboarding_link_expiration'</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">30</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 124" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$organization</span><span class="default">?-&gt;</span><span class="default">name</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 125" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 128" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">commit</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 130" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">successResponse</span><span class="keyword">(</span><span class="default">null</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'Onboarding&nbsp;emails&nbsp;are&nbsp;being&nbsp;processed.'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 131" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Throwable</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 132" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollBack</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 134" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handleApiExceptions</span><span class="keyword">(</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'Send&nbsp;onboarding&nbsp;signed&nbsp;url&nbsp;failed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Update&nbsp;Basic&nbsp;Information</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setBasicInfo</span><span class="keyword">(</span><span class="default">VendorOnboardingSetBasicInfoRequest</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">JsonResponse</span><span class="keyword">|</span><span class="default">BasicInfoResource</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 144" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 145" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 149" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$userId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 150" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$onboardingId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'onboardingId'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 151" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$isThirdPartyUser</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_type</span><span class="default">&nbsp;</span><span class="default">==</span><span class="default">&nbsp;</span><span class="default">UserTypes</span><span class="default">::</span><span class="default">VENDOR</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;the&nbsp;vendor&nbsp;to&nbsp;get&nbsp;access</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 154" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$isThirdPartyUser</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 155" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 158" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">beginTransaction</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 160" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnboarding</span><span class="default">::</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 161" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">self</span><span class="default">::</span><span class="default">STATUS_SELECT_FIELDS</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 162" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor:vendor_id,on_boarding_status'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 163" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 164" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereUuid</span><span class="keyword">(</span><span class="default">$onboardingId</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 165" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 167" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$newStatus</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatus</span><span class="default">::</span><span class="default">where</span><span class="keyword">(</span><span class="default">'slug'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatuses</span><span class="default">::</span><span class="default">SERVICE_OFFERED</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;the&nbsp;vendor&nbsp;that&nbsp;owns&nbsp;the&nbsp;onboarding&nbsp;can&nbsp;update&nbsp;the&nbsp;services&nbsp;offered</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 170" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">$userId</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 171" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;update&nbsp;if&nbsp;vendor&nbsp;onboarding&nbsp;have&nbsp;correct&nbsp;status</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 175" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">status</span><span class="default">-&gt;</span><span class="default">slug</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatuses</span><span class="default">::</span><span class="default">BASIC_INFO</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 176" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">CouldNotPerformTransition</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 179" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$state</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">State</span><span class="default">::</span><span class="default">whereUuid</span><span class="keyword">(</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'state_id'</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 180" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$updateData</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 181" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'first_name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'first_name'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 182" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'last_name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'last_name'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 183" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'company_name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'company_name'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 184" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'phone_number'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'phone_number'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 185" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'address1'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'address1'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 186" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'address2'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'apt_unit'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 187" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'city'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'city'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 188" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'postal_zip_code'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'postal_zip_code'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 189" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'state_province'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$state</span><span class="default">-&gt;</span><span class="default">state_code</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 190" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'gl_insurance_expire_at'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'gl_insurance_expire_at'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">!=</span><span class="default">&nbsp;</span><span class="default">''</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">Carbon</span><span class="default">::</span><span class="default">parse</span><span class="keyword">(</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'gl_insurance_expire_at'</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">toDateTimeString</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 191" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 193" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorUser</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorUser</span><span class="default">::</span><span class="default">where</span><span class="keyword">(</span><span class="default">'vendor_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor</span><span class="default">-&gt;</span><span class="default">vendor_id</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 194" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$vendorUser</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 195" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">User</span><span class="default">::</span><span class="default">where</span><span class="keyword">(</span><span class="default">'user_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$vendorUser</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">update</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 196" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'first_name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'first_name'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 197" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'last_name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'last_name'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 198" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'state_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$state</span><span class="default">-&gt;</span><span class="default">state_id</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 199" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'city'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'city'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 200" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'apt_suite_unit'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'apt_unit'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 201" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'street_address'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'address1'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 202" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'phone_number'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'phone_number'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 203" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 206" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Vendor</span><span class="default">::</span><span class="default">where</span><span class="keyword">(</span><span class="default">'vendor_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor</span><span class="default">-&gt;</span><span class="default">vendor_id</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">update</span><span class="keyword">(</span><span class="default">$updateData</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 208" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_status_id</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$newStatus</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_status_id</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 210" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">save</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 211" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">refresh</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 212" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">commit</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="213" href="#213">213</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 214" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="214" href="#214">214</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">BasicInfoResource</span><span class="keyword">(</span><span class="default">$vendorOnboarding</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 215" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="215" href="#215">215</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Throwable</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 216" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="216" href="#216">216</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollBack</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="217" href="#217">217</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 218" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="218" href="#218">218</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handleApiExceptions</span><span class="keyword">(</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'Set&nbsp;onboarding&nbsp;basic&nbsp;failed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="219" href="#219">219</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="220" href="#220">220</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="221" href="#221">221</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="222" href="#222">222</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getVendorOnboarding</span><span class="keyword">(</span><span class="default">Request</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">JsonResponse</span><span class="keyword">|</span><span class="default">VendorOnboardingResource</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="223" href="#223">223</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="224" href="#224">224</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 225" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="225" href="#225">225</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 226" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="226" href="#226">226</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="227" href="#227">227</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="228" href="#228">228</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="229" href="#229">229</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 230" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="230" href="#230">230</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$userId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 231" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="231" href="#231">231</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$onboardingId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">query</span><span class="keyword">(</span><span class="default">'data'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 232" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="232" href="#232">232</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$isThirdPartyUser</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_type</span><span class="default">&nbsp;</span><span class="default">==</span><span class="default">&nbsp;</span><span class="default">UserTypes</span><span class="default">::</span><span class="default">VENDOR</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="233" href="#233">233</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="234" href="#234">234</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;the&nbsp;vendor&nbsp;to&nbsp;get&nbsp;access</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 235" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="235" href="#235">235</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$isThirdPartyUser</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 236" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="236" href="#236">236</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="237" href="#237">237</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="238" href="#238">238</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 239" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="239" href="#239">239</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnboarding</span><span class="default">::</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 240" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="240" href="#240">240</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'status'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 241" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="241" href="#241">241</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor.vendorServices.problemDiagnosis.subCategory.problemCategory'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 242" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="242" href="#242">242</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor.vendorServiceArea:vendor_service_area_id,vendor_service_area_uuid,zip_codes,radius'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 243" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="243" href="#243">243</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 244" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="244" href="#244">244</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereUuid</span><span class="keyword">(</span><span class="default">$onboardingId</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 245" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="245" href="#245">245</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="246" href="#246">246</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 247" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="247" href="#247">247</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="default">&nbsp;</span><span class="default">!=</span><span class="default">&nbsp;</span><span class="default">$userId</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 248" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="248" href="#248">248</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="249" href="#249">249</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="250" href="#250">250</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 251" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="251" href="#251">251</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingResource</span><span class="keyword">(</span><span class="default">$vendorOnboarding</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 252" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="252" href="#252">252</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Throwable</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 253" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="253" href="#253">253</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handleApiExceptions</span><span class="keyword">(</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'Get&nbsp;onboarding&nbsp;details&nbsp;API&nbsp;Failed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="254" href="#254">254</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="255" href="#255">255</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="256" href="#256">256</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="257" href="#257">257</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getStatus</span><span class="keyword">(</span><span class="default">Request</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">JsonResponse</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="258" href="#258">258</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="259" href="#259">259</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 260" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="260" href="#260">260</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 261" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="261" href="#261">261</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="262" href="#262">262</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="263" href="#263">263</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="264" href="#264">264</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 265" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="265" href="#265">265</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$userId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 266" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="266" href="#266">266</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$isThirdPartyUser</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_type</span><span class="default">&nbsp;</span><span class="default">==</span><span class="default">&nbsp;</span><span class="default">UserTypes</span><span class="default">::</span><span class="default">VENDOR</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="267" href="#267">267</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="268" href="#268">268</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;the&nbsp;vendor&nbsp;to&nbsp;get&nbsp;access</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 269" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="269" href="#269">269</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$isThirdPartyUser</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 270" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="270" href="#270">270</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="271" href="#271">271</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="272" href="#272">272</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 273" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="273" href="#273">273</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendor</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">Vendor</span><span class="default">::</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 274" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="274" href="#274">274</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendorOnboardings'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 275" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="275" href="#275">275</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'vendor_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'vendor_onboarding_uuid'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'vendor_onboarding_status_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'organization_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 276" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="276" href="#276">276</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 277" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="277" href="#277">277</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'status'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$q</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 278" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="278" href="#278">278</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$q</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'vendor_onboarding_status_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'slug'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'label'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 279" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="279" href="#279">279</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 280" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="280" href="#280">280</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$q</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 281" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="281" href="#281">281</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$q</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'organization_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'name'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 282" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="282" href="#282">282</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 283" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="283" href="#283">283</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 284" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="284" href="#284">284</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">orderBy</span><span class="keyword">(</span><span class="default">'created_at'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'desc'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 285" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="285" href="#285">285</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 286" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="286" href="#286">286</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 287" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="287" href="#287">287</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereHas</span><span class="keyword">(</span><span class="default">'vendorUsers'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$userId</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 288" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="288" href="#288">288</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">where</span><span class="keyword">(</span><span class="default">'user_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$userId</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 289" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="289" href="#289">289</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="290" href="#290">290</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="291" href="#291">291</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Generate&nbsp;signed&nbsp;url</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 292" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="292" href="#292">292</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$signedUrl</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">OnboardingHelper</span><span class="default">::</span><span class="default">generateSignedUrl</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 293" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="293" href="#293">293</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor_onboarding.invite'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 294" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="294" href="#294">294</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendor</span><span class="default">-&gt;</span><span class="default">vendorOnboardings</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_uuid</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 295" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="295" href="#295">295</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendor</span><span class="default">-&gt;</span><span class="default">vendorOnboardings</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">organization</span><span class="default">-&gt;</span><span class="default">name</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 296" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="296" href="#296">296</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">config</span><span class="keyword">(</span><span class="default">'services.cognito.provider.onboarding_link_expiration'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">??</span><span class="default">&nbsp;</span><span class="default">10080</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 297" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="297" href="#297">297</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="298" href="#298">298</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="299" href="#299">299</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Generate&nbsp;frontend</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 300" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="300" href="#300">300</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$url</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">parse_url</span><span class="keyword">(</span><span class="default">$signedUrl</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 301" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="301" href="#301">301</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$queryString</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 302" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="302" href="#302">302</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$url</span><span class="keyword">[</span><span class="default">'query'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$url</span><span class="keyword">[</span><span class="default">'query'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 303" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="303" href="#303">303</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$queryString</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">'?'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$url</span><span class="keyword">[</span><span class="default">'query'</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="304" href="#304">304</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 305" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="305" href="#305">305</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$frontendUrl</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$queryString</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="306" href="#306">306</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 307" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="307" href="#307">307</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$resData</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 308" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="308" href="#308">308</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'onboarding'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 309" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="309" href="#309">309</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$vendor</span><span class="default">-&gt;</span><span class="default">vendor_uuid</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 310" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="310" href="#310">310</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'onboarding_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$vendor</span><span class="default">-&gt;</span><span class="default">vendorOnboardings</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_uuid</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 311" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="311" href="#311">311</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'onboarding_step'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 312" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="312" href="#312">312</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'slug'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$vendor</span><span class="default">-&gt;</span><span class="default">vendorOnboardings</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">status</span><span class="default">-&gt;</span><span class="default">slug</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 313" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="313" href="#313">313</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'label'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$vendor</span><span class="default">-&gt;</span><span class="default">vendorOnboardings</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">status</span><span class="default">-&gt;</span><span class="default">label</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 314" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="314" href="#314">314</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 315" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="315" href="#315">315</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'onboarding_status'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$vendor</span><span class="default">-&gt;</span><span class="default">on_boarding_status</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 316" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="316" href="#316">316</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'signed_url_params'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$frontendUrl</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 317" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="317" href="#317">317</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 318" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="318" href="#318">318</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'profile'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 319" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="319" href="#319">319</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'first_name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">first_name</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 320" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="320" href="#320">320</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'last_name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">last_name</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 321" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="321" href="#321">321</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'user_type'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_type</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 322" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="322" href="#322">322</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 323" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="323" href="#323">323</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="324" href="#324">324</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 325" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="325" href="#325">325</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">successResponse</span><span class="keyword">(</span><span class="default">$resData</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 326" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="326" href="#326">326</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Throwable</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 327" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="327" href="#327">327</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handleApiExceptions</span><span class="keyword">(</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'Get&nbsp;onboarding&nbsp;details&nbsp;API&nbsp;Failed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="328" href="#328">328</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="329" href="#329">329</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="330" href="#330">330</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="331" href="#331">331</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setStatus</span><span class="keyword">(</span><span class="default">VendorOnboardingSetStatusRequest</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">JsonResponse</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="332" href="#332">332</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="333" href="#333">333</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 334" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="334" href="#334">334</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 335" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="335" href="#335">335</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="336" href="#336">336</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="337" href="#337">337</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="338" href="#338">338</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 339" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="339" href="#339">339</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$userId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 340" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="340" href="#340">340</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$onboardingId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'onboardingId'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 341" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="341" href="#341">341</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$status</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'status'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="342" href="#342">342</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 343" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="343" href="#343">343</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">beginTransaction</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="344" href="#344">344</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="345" href="#345">345</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Get&nbsp;vendor&nbsp;onboarding</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 346" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="346" href="#346">346</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnboarding</span><span class="default">::</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 347" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="347" href="#347">347</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'status:vendor_onboarding_status_id,sort_order'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 348" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="348" href="#348">348</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor:vendor_id,on_boarding_status'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 349" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="349" href="#349">349</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 350" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="350" href="#350">350</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereUuid</span><span class="keyword">(</span><span class="default">$onboardingId</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 351" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="351" href="#351">351</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="352" href="#352">352</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="353" href="#353">353</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Get&nbsp;new&nbsp;status</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 354" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="354" href="#354">354</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$newStatus</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatus</span><span class="default">::</span><span class="default">where</span><span class="keyword">(</span><span class="default">'slug'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$status</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="355" href="#355">355</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 356" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="356" href="#356">356</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$isApprovedStatus</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$newStatus</span><span class="default">-&gt;</span><span class="default">slug</span><span class="default">&nbsp;</span><span class="default">==</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatuses</span><span class="default">::</span><span class="default">APPROVED</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 357" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="357" href="#357">357</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$isSetPasswordStatus</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$newStatus</span><span class="default">-&gt;</span><span class="default">slug</span><span class="default">&nbsp;</span><span class="default">==</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatuses</span><span class="default">::</span><span class="default">SET_PASSWORD</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 358" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="358" href="#358">358</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$isAccountUser</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_type</span><span class="default">&nbsp;</span><span class="default">==</span><span class="default">&nbsp;</span><span class="default">UserTypes</span><span class="default">::</span><span class="default">ACCOUNT_USER</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="359" href="#359">359</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="360" href="#360">360</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Changing&nbsp;the&nbsp;status&nbsp;back&nbsp;to&nbsp;set-password&nbsp;is&nbsp;not&nbsp;allowed</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 361" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="361" href="#361">361</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$isSetPasswordStatus</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">status</span><span class="default">-&gt;</span><span class="default">sort_order</span><span class="default">&nbsp;</span><span class="keyword">&gt;</span><span class="default">&nbsp;</span><span class="default">$newStatus</span><span class="default">-&gt;</span><span class="default">sort_order</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 362" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="362" href="#362">362</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">CouldNotPerformTransition</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="363" href="#363">363</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="364" href="#364">364</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="365" href="#365">365</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;the&nbsp;vendor&nbsp;who&nbsp;owns&nbsp;the&nbsp;onboarding&nbsp;can&nbsp;update&nbsp;the&nbsp;status&nbsp;(except&nbsp;approve)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 366" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="366" href="#366">366</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$isAccountUser</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$isApprovedStatus</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">$userId</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 367" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="367" href="#367">367</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="368" href="#368">368</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="369" href="#369">369</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="370" href="#370">370</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;coordinator&nbsp;to&nbsp;approve&nbsp;onboarding</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 371" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="371" href="#371">371</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$isApprovedStatus</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$isAccountUser</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 372" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="372" href="#372">372</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="373" href="#373">373</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="374" href="#374">374</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="375" href="#375">375</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;coordinator&nbsp;with&nbsp;correct&nbsp;organization&nbsp;can&nbsp;approve&nbsp;the&nbsp;onboarding</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 376" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="376" href="#376">376</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$isApprovedStatus</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$isAccountUser</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 377" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="377" href="#377">377</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$accountOrganization</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">?-&gt;</span><span class="default">organization</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 378" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="378" href="#378">378</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="default">&nbsp;</span><span class="default">!=</span><span class="default">&nbsp;</span><span class="default">$accountOrganization</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 379" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="379" href="#379">379</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="380" href="#380">380</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="381" href="#381">381</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="382" href="#382">382</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="383" href="#383">383</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Change&nbsp;vendor&nbsp;status&nbsp;if&nbsp;status&nbsp;is&nbsp;approved</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 384" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="384" href="#384">384</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$isApprovedStatus</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="385" href="#385">385</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor</span><span class="default">-&gt;</span><span class="default">on_boarding_status</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnBoardingStatusEnum</span><span class="default">::</span><span class="default">COMPLETE</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="386" href="#386">386</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor</span><span class="default">-&gt;</span><span class="default">save</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="387" href="#387">387</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="388" href="#388">388</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 389" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="389" href="#389">389</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_status_id</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$newStatus</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_status_id</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 390" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="390" href="#390">390</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">save</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="391" href="#391">391</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 392" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="392" href="#392">392</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">commit</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="393" href="#393">393</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 394" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="394" href="#394">394</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">response</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">json</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 395" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="395" href="#395">395</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'status'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 396" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="396" href="#396">396</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'slug'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$status</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 397" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="397" href="#397">397</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatuses</span><span class="default">::</span><span class="default">label</span><span class="keyword">(</span><span class="default">$status</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 398" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="398" href="#398">398</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 399" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="399" href="#399">399</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">200</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 400" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="400" href="#400">400</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Throwable</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 401" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="401" href="#401">401</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollBack</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="402" href="#402">402</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 403" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="403" href="#403">403</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handleApiExceptions</span><span class="keyword">(</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'Get&nbsp;onboarding&nbsp;set&nbsp;status&nbsp;API&nbsp;Failed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="404" href="#404">404</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="405" href="#405">405</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="406" href="#406">406</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="407" href="#407">407</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setServices</span><span class="keyword">(</span><span class="default">VendorOnboardingSetServicesRequest</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">JsonResponse</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="408" href="#408">408</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="409" href="#409">409</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 410" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="410" href="#410">410</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 411" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="411" href="#411">411</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="412" href="#412">412</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="413" href="#413">413</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="414" href="#414">414</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 415" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="415" href="#415">415</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$userId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 416" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="416" href="#416">416</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$onboardingId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'onboardingId'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 417" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="417" href="#417">417</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$services</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'services'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 418" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="418" href="#418">418</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$isThirdPartyUser</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_type</span><span class="default">&nbsp;</span><span class="default">==</span><span class="default">&nbsp;</span><span class="default">UserTypes</span><span class="default">::</span><span class="default">VENDOR</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="419" href="#419">419</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="420" href="#420">420</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;the&nbsp;vendor&nbsp;to&nbsp;get&nbsp;access</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 421" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="421" href="#421">421</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$isThirdPartyUser</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 422" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="422" href="#422">422</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="423" href="#423">423</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="424" href="#424">424</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 425" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="425" href="#425">425</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">beginTransaction</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="426" href="#426">426</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="427" href="#427">427</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Get&nbsp;vendor&nbsp;onboarding</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 428" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="428" href="#428">428</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnboarding</span><span class="default">::</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 429" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="429" href="#429">429</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">self</span><span class="default">::</span><span class="default">STATUS_SELECT_FIELDS</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 430" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="430" href="#430">430</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 431" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="431" href="#431">431</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereUuid</span><span class="keyword">(</span><span class="default">$onboardingId</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 432" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="432" href="#432">432</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="433" href="#433">433</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 434" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="434" href="#434">434</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$newStatus</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatus</span><span class="default">::</span><span class="default">where</span><span class="keyword">(</span><span class="default">'slug'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatuses</span><span class="default">::</span><span class="default">SERVICE_AREA</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="435" href="#435">435</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="436" href="#436">436</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;the&nbsp;vendor&nbsp;that&nbsp;owns&nbsp;the&nbsp;onboarding&nbsp;can&nbsp;update&nbsp;the&nbsp;services&nbsp;offered</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 437" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="437" href="#437">437</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">$userId</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 438" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="438" href="#438">438</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="439" href="#439">439</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="440" href="#440">440</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="441" href="#441">441</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;update&nbsp;if&nbsp;vendor&nbsp;onboarding&nbsp;have&nbsp;correct&nbsp;status</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 442" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="442" href="#442">442</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">status</span><span class="default">-&gt;</span><span class="default">slug</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatuses</span><span class="default">::</span><span class="default">SERVICE_OFFERED</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 443" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="443" href="#443">443</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">CouldNotPerformTransition</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="444" href="#444">444</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="445" href="#445">445</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="446" href="#446">446</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Save&nbsp;the&nbsp;service&nbsp;offered</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 447" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="447" href="#447">447</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$selectedSkills</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">processServices</span><span class="keyword">(</span><span class="default">$services</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$vendorOnboarding</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="448" href="#448">448</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="449" href="#449">449</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Change&nbsp;status</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 450" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="450" href="#450">450</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_status_id</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$newStatus</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_status_id</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="451" href="#451">451</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 452" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="452" href="#452">452</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">save</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 453" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="453" href="#453">453</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">refresh</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="454" href="#454">454</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 455" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="455" href="#455">455</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">commit</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="456" href="#456">456</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 457" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="457" href="#457">457</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">response</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">json</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 458" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="458" href="#458">458</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'selected_skills'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$selectedSkills</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 459" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="459" href="#459">459</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">200</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 460" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="460" href="#460">460</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Throwable</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 461" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="461" href="#461">461</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollBack</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="462" href="#462">462</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 463" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="463" href="#463">463</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handleApiExceptions</span><span class="keyword">(</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'Onboarding&nbsp;set&nbsp;services&nbsp;API&nbsp;Failed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="464" href="#464">464</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="465" href="#465">465</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="466" href="#466">466</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="467" href="#467">467</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">setServiceAreas</span><span class="keyword">(</span><span class="default">VendorOnBoardingSetServiceAreasRequest</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">JsonResponse</span><span class="keyword">|</span><span class="default">ServiceAreaResource</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="468" href="#468">468</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="469" href="#469">469</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 470" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="470" href="#470">470</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 471" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="471" href="#471">471</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="472" href="#472">472</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="473" href="#473">473</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="474" href="#474">474</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 475" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="475" href="#475">475</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$userId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 476" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="476" href="#476">476</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$onboardingId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'onboardingId'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 477" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="477" href="#477">477</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$zipCodes</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'zip_codes'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 478" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="478" href="#478">478</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$radius</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">input</span><span class="keyword">(</span><span class="default">'radius'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 479" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="479" href="#479">479</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$isThirdPartyUser</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_type</span><span class="default">&nbsp;</span><span class="default">==</span><span class="default">&nbsp;</span><span class="default">UserTypes</span><span class="default">::</span><span class="default">VENDOR</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="480" href="#480">480</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="481" href="#481">481</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;the&nbsp;vendor&nbsp;to&nbsp;get&nbsp;access</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 482" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="482" href="#482">482</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$isThirdPartyUser</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 483" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="483" href="#483">483</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="484" href="#484">484</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="485" href="#485">485</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 486" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="486" href="#486">486</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">beginTransaction</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="487" href="#487">487</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="488" href="#488">488</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Get&nbsp;vendor&nbsp;onboarding</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 489" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="489" href="#489">489</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnboarding</span><span class="default">::</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 490" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="490" href="#490">490</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">self</span><span class="default">::</span><span class="default">STATUS_SELECT_FIELDS</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 491" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="491" href="#491">491</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 492" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="492" href="#492">492</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereUuid</span><span class="keyword">(</span><span class="default">$onboardingId</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 493" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="493" href="#493">493</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="494" href="#494">494</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 495" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="495" href="#495">495</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$newStatus</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatus</span><span class="default">::</span><span class="default">where</span><span class="keyword">(</span><span class="default">'slug'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatuses</span><span class="default">::</span><span class="default">UNDER_REVIEW</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="496" href="#496">496</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="497" href="#497">497</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;the&nbsp;vendor&nbsp;that&nbsp;owns&nbsp;the&nbsp;onboarding&nbsp;can&nbsp;update&nbsp;the&nbsp;service&nbsp;areas</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 498" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="498" href="#498">498</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">$userId</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 499" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="499" href="#499">499</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ForbiddenException</span><span class="default">::</span><span class="default">accessDenied</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="500" href="#500">500</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="501" href="#501">501</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="502" href="#502">502</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;allow&nbsp;update&nbsp;if&nbsp;vendor&nbsp;onboarding&nbsp;have&nbsp;correct&nbsp;status</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 503" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="503" href="#503">503</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">status</span><span class="default">-&gt;</span><span class="default">slug</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">VendorOnboardingStatuses</span><span class="default">::</span><span class="default">SERVICE_AREA</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 504" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="504" href="#504">504</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">CouldNotPerformTransition</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="505" href="#505">505</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="506" href="#506">506</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="507" href="#507">507</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Save&nbsp;service&nbsp;Areas</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 508" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="508" href="#508">508</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$serviceArea</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorServiceArea</span><span class="default">::</span><span class="default">updateOrCreate</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 509" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="509" href="#509">509</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 510" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="510" href="#510">510</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor_id</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 511" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="511" href="#511">511</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 512" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="512" href="#512">512</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 513" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="513" href="#513">513</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor_id</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 514" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="514" href="#514">514</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'radius'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$radius</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 515" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="515" href="#515">515</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'zip_codes'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$zipCodes</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 516" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="516" href="#516">516</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 517" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="517" href="#517">517</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="518" href="#518">518</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 519" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="519" href="#519">519</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_status_id</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$newStatus</span><span class="default">-&gt;</span><span class="default">vendor_onboarding_status_id</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="520" href="#520">520</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 521" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="521" href="#521">521</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">save</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 522" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="522" href="#522">522</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">refresh</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="523" href="#523">523</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 524" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="524" href="#524">524</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">commit</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="525" href="#525">525</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 526" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="526" href="#526">526</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">ServiceAreaResource</span><span class="keyword">(</span><span class="default">$serviceArea</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="527" href="#527">527</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 528" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="528" href="#528">528</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Throwable</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 529" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="529" href="#529">529</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollBack</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="530" href="#530">530</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 531" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="531" href="#531">531</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handleApiExceptions</span><span class="keyword">(</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'Onboarding&nbsp;set&nbsp;service&nbsp;areas&nbsp;API&nbsp;Failed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="532" href="#532">532</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="533" href="#533">533</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="534" href="#534">534</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="535" href="#535">535</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">processServices</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$services</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">VendorOnboarding</span><span class="default">&nbsp;</span><span class="default">$vendorOnboarding</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="536" href="#536">536</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 537" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="537" href="#537">537</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$selectedSkills</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 538" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="538" href="#538">538</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'selected_diagnosis_ids'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 539" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="539" href="#539">539</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'selected_sub_category_ids'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 540" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="540" href="#540">540</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'selected_problem_category_ids'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 541" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="541" href="#541">541</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="542" href="#542">542</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 543" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="543" href="#543">543</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">foreach</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$services</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">$service</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 544" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="544" href="#544">544</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$subCategory</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">ProblemSubCategory</span><span class="default">::</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span><span class="default">'problemCategory'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'problemDiagnoses'</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 545" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="545" href="#545">545</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereUuid</span><span class="keyword">(</span><span class="default">$service</span><span class="keyword">[</span><span class="default">'problem_sub_category_id'</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 546" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="546" href="#546">546</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="547" href="#547">547</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 548" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="548" href="#548">548</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$category</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$subCategory</span><span class="default">-&gt;</span><span class="default">problemCategory</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 549" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="549" href="#549">549</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$diagnoses</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$subCategory</span><span class="default">-&gt;</span><span class="default">problemDiagnoses</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="550" href="#550">550</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 551" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="551" href="#551">551</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">foreach</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$diagnoses</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">$diagnosis</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="552" href="#552">552</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">VendorService</span><span class="default">::</span><span class="default">updateOrCreate</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="553" href="#553">553</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="554" href="#554">554</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendor_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">vendor_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="555" href="#555">555</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$vendorOnboarding</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="556" href="#556">556</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'problem_category_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$category</span><span class="default">-&gt;</span><span class="default">problem_category_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="557" href="#557">557</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'problem_sub_category_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$subCategory</span><span class="default">-&gt;</span><span class="default">problem_sub_category_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="558" href="#558">558</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'problem_diagnosis_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$diagnosis</span><span class="default">-&gt;</span><span class="default">problem_diagnosis_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="559" href="#559">559</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="560" href="#560">560</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="561" href="#561">561</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'updated_at'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">now</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="562" href="#562">562</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="563" href="#563">563</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="564" href="#564">564</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="565" href="#565">565</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$uuid</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$diagnosis</span><span class="default">-&gt;</span><span class="default">problem_diagnosis_uuid</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="566" href="#566">566</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">in_array</span><span class="keyword">(</span><span class="default">$uuid</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$selectedSkills</span><span class="keyword">[</span><span class="default">'selected_diagnosis_ids'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="567" href="#567">567</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$selectedSkills</span><span class="keyword">[</span><span class="default">'selected_diagnosis_ids'</span><span class="keyword">]</span><span class="keyword">[</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="568" href="#568">568</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="569" href="#569">569</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="570" href="#570">570</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 571" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="571" href="#571">571</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$subCategoryUuid</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$subCategory</span><span class="default">-&gt;</span><span class="default">problem_sub_category_uuid</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 572" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="572" href="#572">572</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">in_array</span><span class="keyword">(</span><span class="default">$subCategoryUuid</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$selectedSkills</span><span class="keyword">[</span><span class="default">'selected_sub_category_ids'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 573" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="573" href="#573">573</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$selectedSkills</span><span class="keyword">[</span><span class="default">'selected_sub_category_ids'</span><span class="keyword">]</span><span class="keyword">[</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$subCategoryUuid</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="574" href="#574">574</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="575" href="#575">575</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 576" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="576" href="#576">576</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$categoryUuid</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$category</span><span class="default">-&gt;</span><span class="default">problem_category_uuid</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 577" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="577" href="#577">577</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">in_array</span><span class="keyword">(</span><span class="default">$categoryUuid</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$selectedSkills</span><span class="keyword">[</span><span class="default">'selected_problem_category_ids'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 578" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="578" href="#578">578</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$selectedSkills</span><span class="keyword">[</span><span class="default">'selected_problem_category_ids'</span><span class="keyword">]</span><span class="keyword">[</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$categoryUuid</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="579" href="#579">579</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="580" href="#580">580</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="581" href="#581">581</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 582" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="582" href="#582">582</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$selectedSkills</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="583" href="#583">583</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="584" href="#584">584</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="585" href="#585">585</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">minutesToDaysSimple</span><span class="keyword">(</span><span class="default">$minutes</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="586" href="#586">586</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 587" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="587" href="#587">587</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$minutes</span><span class="default">&nbsp;</span><span class="keyword">/</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">60</span><span class="default">&nbsp;</span><span class="keyword">*</span><span class="default">&nbsp;</span><span class="default">24</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'&nbsp;days'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="588" href="#588">588</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="589" href="#589">589</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Mon Jun 30 0:32:48 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Http</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Http</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Controllers/AppVersionController.php.html#14">App\Http\Controllers\AppVersionController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/SSOController.php.html#22">App\Http\Controllers\Auth\SSOController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/DatabaseNotificationController.php.html#16">App\Http\Controllers\DatabaseNotificationController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/HealthCheckController.php.html#14">App\Http\Controllers\Developer\HealthCheckController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/LogViewerController.php.html#19">App\Http\Controllers\Developer\LogViewerController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/InvoiceController.php.html#21">App\Http\Controllers\InvoiceController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#39">App\Http\Controllers\LookupController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#28">App\Http\Controllers\LulaWebhookController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/MediaController.php.html#16">App\Http\Controllers\MediaController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/OrganizationController.php.html#20">App\Http\Controllers\OrganizationController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PDFController.php.html#30">App\Http\Controllers\PDFController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ProfileController.php.html#9">App\Http\Controllers\ProfileController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PublicAccessController.php.html#17">App\Http\Controllers\PublicAccessController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PublicApiWorkOrderWebhookEventsController.php.html#8">App\Http\Controllers\PublicApiWorkOrderWebhookEventsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#49">App\Http\Controllers\QuoteController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ResidentAvailabilityController.php.html#19">App\Http\Controllers\ResidentAvailabilityController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#25">App\Http\Controllers\RoleController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#31">App\Http\Controllers\SchedulingController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestActivityLogController.php.html#24">App\Http\Controllers\ServiceRequestActivityLogController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestAssigneeController.php.html#22">App\Http\Controllers\ServiceRequestAssigneeController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#89">App\Http\Controllers\ServiceRequestController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestMediaController.php.html#39">App\Http\Controllers\ServiceRequestMediaController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestNoteController.php.html#31">App\Http\Controllers\ServiceRequestNoteController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TagController.php.html#24">App\Http\Controllers\TagController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#37">App\Http\Controllers\TechnicianAppointmentsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianController.php.html#26">App\Http\Controllers\TechnicianController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#61">App\Http\Controllers\UserController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/VendorController.php.html#33">App\Http\Controllers\VendorController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#43">App\Http\Controllers\ViewController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookCallsController.php.html#9">App\Http\Controllers\WebhookCallsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#30">App\Http\Controllers\WebhookController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrder/WorkOrderIssueController.php.html#17">App\Http\Controllers\WorkOrder\WorkOrderIssueController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderActivityLogController.php.html#22">App\Http\Controllers\WorkOrderActivityLogController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderAssigneeController.php.html#22">App\Http\Controllers\WorkOrderAssigneeController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#115">App\Http\Controllers\WorkOrderController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#43">App\Http\Controllers\WorkOrderMediaController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderNoteController.php.html#27">App\Http\Controllers\WorkOrderNoteController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/QuoteListFilter.php.html#10">App\Http\Filters\QuoteListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/RoleListFilter.php.html#5">App\Http\Filters\RoleListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ServiceRequestListFilter.php.html#10">App\Http\Filters\ServiceRequestListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/TagListFilter.php.html#8">App\Http\Filters\TagListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/UserListFilter.php.html#11">App\Http\Filters\UserListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/VendorListFilter.php.html#5">App\Http\Filters\VendorListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/Authenticate.php.html#8">App\Http\Middleware\Authenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/HorizonBasicAuth.php.html#7">App\Http\Middleware\HorizonBasicAuth</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/MakeTusMediaFileName.php.html#12">App\Http\Middleware\MakeTusMediaFileName</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/RedirectIfAuthenticated.php.html#10">App\Http\Middleware\RedirectIfAuthenticated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/TrustHosts.php.html#7">App\Http\Middleware\TrustHosts</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Auth/PublicApiAuthenticateRequest.php.html#7">App\Http\Requests\Auth\PublicApiAuthenticateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Auth/SearchAddressRequest.php.html#7">App\Http\Requests\Auth\SearchAddressRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Invoice/InvoiceRequest.php.html#24">App\Http\Requests\Invoice\InvoiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Issue/AssignRequest.php.html#9">App\Http\Requests\Issue\AssignRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Issue/CreateIssueRequest.php.html#11">App\Http\Requests\Issue\CreateIssueRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Issue/UnassignRequest.php.html#9">App\Http\Requests\Issue\UnassignRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Organization/UpdateRequest.php.html#10">App\Http\Requests\Organization\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Quote/ListRequest.php.html#7">App\Http\Requests\Quote\ListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Schedule/GetTechnicianListRequest.php.html#10">App\Http\Requests\Schedule\GetTechnicianListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Schedule/ReScheduleAppointmentRequest.php.html#14">App\Http\Requests\Schedule\ReScheduleAppointmentRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Schedule/StoreAppointmentRequest.php.html#10">App\Http\Requests\Schedule\StoreAppointmentRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/AccessMethodUpdateRequest.php.html#9">App\Http\Requests\ServiceRequest\AccessMethodUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/DescriptionUpdateRequest.php.html#7">App\Http\Requests\ServiceRequest\DescriptionUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/FilterValuesRequest.php.html#7">App\Http\Requests\ServiceRequest\FilterValuesRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/GroupViewRequest.php.html#7">App\Http\Requests\ServiceRequest\GroupViewRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ListRequest.php.html#8">App\Http\Requests\ServiceRequest\ListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/Media/MediaUploadRequest.php.html#7">App\Http\Requests\ServiceRequest\Media\MediaUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/Media/ThumbnailUploadRequest.php.html#8">App\Http\Requests\ServiceRequest\Media\ThumbnailUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/Note/CreateRequest.php.html#7">App\Http\Requests\ServiceRequest\Note\CreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/Note/UpdateRequest.php.html#7">App\Http\Requests\ServiceRequest\Note\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/PropertyAddressUpdateRequest.php.html#10">App\Http\Requests\ServiceRequest\PropertyAddressUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ResidentAvailabilityRequest.php.html#15">App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ResidentUpdateRequest.php.html#10">App\Http\Requests\ServiceRequest\ResidentUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreWorkOrderRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreWorkOrderRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/UpdateAvailabilityRequest.php.html#12">App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Tag/StoreRequest.php.html#7">App\Http\Requests\Tag\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Tag/UpdateRequest.php.html#7">App\Http\Requests\Tag\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/AvailabilityDatesRequest.php.html#7">App\Http\Requests\Technician\AvailabilityDatesRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/BlockOutUpdateRequest.php.html#14">App\Http\Requests\Technician\BlockOutUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/ScheduleWorkOrderRequest.php.html#8">App\Http\Requests\Technician\ScheduleWorkOrderRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/SkillsUpdateRequest.php.html#12">App\Http\Requests\Technician\SkillsUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/StoreBlockOutRequest.php.html#14">App\Http\Requests\Technician\StoreBlockOutRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/WorkingHoursUpdateRequest.php.html#10">App\Http\Requests\Technician\WorkingHoursUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/FilterValuesRequest.php.html#7">App\Http\Requests\User\FilterValuesRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/GroupViewRequest.php.html#7">App\Http\Requests\User\GroupViewRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/ListRequest.php.html#7">App\Http\Requests\User\ListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/StoreRequest.php.html#11">App\Http\Requests\User\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/StoreVendorUserRequest.php.html#9">App\Http\Requests\User\StoreVendorUserRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/UpdateRequest.php.html#17">App\Http\Requests\User\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Vendor/StoreRequest.php.html#9">App\Http\Requests\Vendor\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Vendor/VendorOnboardingGenerateSignedUrlRequest.php.html#7">App\Http\Requests\Vendor\VendorOnboardingGenerateSignedUrlRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Vendor/VendorOnboardingInitialRequest.php.html#9">App\Http\Requests\Vendor\VendorOnboardingInitialRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/ConfigurationRequest.php.html#7">App\Http\Requests\View\ConfigurationRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/CountRequest.php.html#7">App\Http\Requests\View\CountRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/DuplicateRequest.php.html#12">App\Http\Requests\View\DuplicateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/PinRequest.php.html#8">App\Http\Requests\View\PinRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/RenameRequest.php.html#11">App\Http\Requests\View\RenameRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/StoreRequest.php.html#13">App\Http\Requests\View\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/UpdateRequest.php.html#8">App\Http\Requests\View\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/AccessMethodUpdateRequest.php.html#9">App\Http\Requests\WorkOrder\AccessMethodUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ApproveQuoteRequest.php.html#9">App\Http\Requests\WorkOrder\ApproveQuoteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/BookmarkRequest.php.html#7">App\Http\Requests\WorkOrder\BookmarkRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/CancelRequest.php.html#7">App\Http\Requests\WorkOrder\CancelRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/CloseRequest.php.html#7">App\Http\Requests\WorkOrder\CloseRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/CompleteRequest.php.html#12">App\Http\Requests\WorkOrder\CompleteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/CreateQuoteRequest.php.html#14">App\Http\Requests\WorkOrder\CreateQuoteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/DescriptionUpdateRequest.php.html#7">App\Http\Requests\WorkOrder\DescriptionUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/DueDateUpdateRequest.php.html#7">App\Http\Requests\WorkOrder\DueDateUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/FilterValuesRequest.php.html#7">App\Http\Requests\WorkOrder\FilterValuesRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/GroupViewRequest.php.html#7">App\Http\Requests\WorkOrder\GroupViewRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Media/MediaUploadRequest.php.html#7">App\Http\Requests\WorkOrder\Media\MediaUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Media/ThumbnailUploadRequest.php.html#10">App\Http\Requests\WorkOrder\Media\ThumbnailUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Note/CreateRequest.php.html#7">App\Http\Requests\WorkOrder\Note\CreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Note/UpdateRequest.php.html#7">App\Http\Requests\WorkOrder\Note\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/PauseRequest.php.html#7">App\Http\Requests\WorkOrder\PauseRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/PriorityUpdateRequest.php.html#9">App\Http\Requests\WorkOrder\PriorityUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ProblemCategoryCreateRequest.php.html#9">App\Http\Requests\WorkOrder\ProblemCategoryCreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ProblemCategoryDeleteRequest.php.html#10">App\Http\Requests\WorkOrder\ProblemCategoryDeleteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ProblemCategoryUpdateRequest.php.html#10">App\Http\Requests\WorkOrder\ProblemCategoryUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/PropertyAddressUpdateRequest.php.html#10">App\Http\Requests\WorkOrder\PropertyAddressUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Quote/TaskCreateRequest.php.html#14">App\Http\Requests\WorkOrder\Quote\TaskCreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Quote/TaskUpdateRequest.php.html#15">App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ReadyToInvoiceRequest.php.html#11">App\Http\Requests\WorkOrder\ReadyToInvoiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ResidentUpdateRequest.php.html#10">App\Http\Requests\WorkOrder\ResidentUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/SendToVendorRequest.php.html#12">App\Http\Requests\WorkOrder\SendToVendorRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/StoreRequest.php.html#11">App\Http\Requests\WorkOrder\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/TagRequest.php.html#9">App\Http\Requests\WorkOrder\TagRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/UpdateNteAmountRequest.php.html#7">App\Http\Requests\WorkOrder\UpdateNteAmountRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/UpdateTripDetailsRequest.php.html#13">App\Http\Requests\WorkOrder\UpdateTripDetailsRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/UpdateTripRequest.php.html#14">App\Http\Requests\WorkOrder\UpdateTripRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrderIssue/DeclineIssueRequest.php.html#7">App\Http\Requests\WorkOrderIssue\DeclineIssueRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrderIssue/MarkAsDoneIssueRequest.php.html#12">App\Http\Requests\WorkOrderIssue\MarkAsDoneIssueRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrderIssue/WorkOrderIssueUpdateRequest.php.html#13">App\Http\Requests\WorkOrderIssue\WorkOrderIssueUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/APITokenResource.php.html#8">App\Http\Resources\APITokenResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/AppfolioVendorResource.php.html#14">App\Http\Resources\AppfolioVendorResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/CountryResource.php.html#12">App\Http\Resources\CountryResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/FeatureResource.php.html#12">App\Http\Resources\FeatureResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/CreateInvoiceResource.php.html#13">App\Http\Resources\Invoice\CreateInvoiceResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/DeleteInvoiceResource.php.html#13">App\Http\Resources\Invoice\DeleteInvoiceResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceLineItemSubsidiariesResource.php.html#14">App\Http\Resources\Invoice\InvoiceLineItemSubsidiariesResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceLineItemsResource.php.html#14">App\Http\Resources\Invoice\InvoiceLineItemsResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceListResource.php.html#14">App\Http\Resources\Invoice\InvoiceListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceResource.php.html#12">App\Http\Resources\Invoice\InvoiceResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceStateResource.php.html#12">App\Http\Resources\Invoice\InvoiceStateResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/TripResource.php.html#16">App\Http\Resources\Invoice\TripResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/VoidInvoiceResource.php.html#13">App\Http\Resources\Invoice\VoidInvoiceResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Issue/AssignIssueResource.php.html#14">App\Http\Resources\Issue\AssignIssueResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Issue/IssueStatusResource.php.html#13">App\Http\Resources\Issue\IssueStatusResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Issue/UnassignIssueResource.php.html#14">App\Http\Resources\Issue\UnassignIssueResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/LookupResource.php.html#8">App\Http\Resources\LookupResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Notification/NotificationResource.php.html#13">App\Http\Resources\Notification\NotificationResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Notification/ReadNotificationResource.php.html#12">App\Http\Resources\Notification\ReadNotificationResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Organization/OrganizationResource.php.html#12">App\Http\Resources\Organization\OrganizationResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/PermissionResource.php.html#12">App\Http\Resources\PermissionResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/PriorityResource.php.html#13">App\Http\Resources\PriorityResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ProblemCategoryDeleteResource.php.html#12">App\Http\Resources\ProblemCategoryDeleteResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ProblemCategoryUpdateResource.php.html#12">App\Http\Resources\ProblemCategoryUpdateResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/QuantityTypeResources.php.html#8">App\Http\Resources\QuantityTypeResources</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/AssigneeBasedGroupDataResource.php.html#8">App\Http\Resources\Quote\Group\AssigneeBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/CategoryBasedGroupDataResource.php.html#8">App\Http\Resources\Quote\Group\CategoryBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/StatusBasedGroupDataResource.php.html#14">App\Http\Resources\Quote\Group\StatusBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/SubmittedBasedGroupDataResource.php.html#8">App\Http\Resources\Quote\Group\SubmittedBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/TagBasedGroupDataResource.php.html#8">App\Http\Resources\Quote\Group\TagBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/WorkOrderNumberBasedGroupDataResource.php.html#8">App\Http\Resources\Quote\Group\WorkOrderNumberBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/ListResource.php.html#18">App\Http\Resources\Quote\ListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/ListStatusResource.php.html#7">App\Http\Resources\Quote\ListStatusResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/ListTaskResource.php.html#8">App\Http\Resources\Quote\ListTaskResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ResidentAvailability/ResidentAvailabilityShowResource.php.html#14">App\Http\Resources\ResidentAvailability\ResidentAvailabilityShowResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/RoleResource.php.html#12">App\Http\Resources\RoleResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/GetTechnicianListResource.php.html#14">App\Http\Resources\Scheduling\GetTechnicianListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/GetTechnicianSchedulesResources.php.html#15">App\Http\Resources\Scheduling\GetTechnicianSchedulesResources</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/GetVendorAvailabilityResources.php.html#10">App\Http\Resources\Scheduling\GetVendorAvailabilityResources</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/ScheduleContextResource.php.html#20">App\Http\Resources\Scheduling\ScheduleContextResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/ScheduleVendorResource.php.html#12">App\Http\Resources\Scheduling\ScheduleVendorResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/SearchAddressResource.php.html#8">App\Http\Resources\SearchAddressResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ActivityLogResource.php.html#11">App\Http\Resources\ServiceRequest\ActivityLogResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/AssigneeResource.php.html#10">App\Http\Resources\ServiceRequest\AssigneeResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/AwaitingAvailabilityResource.php.html#12">App\Http\Resources\ServiceRequest\AwaitingAvailabilityResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ClosedResource.php.html#12">App\Http\Resources\ServiceRequest\ClosedResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/CreateWorkOrderResource.php.html#12">App\Http\Resources\ServiceRequest\CreateWorkOrderResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/DescriptionResource.php.html#12">App\Http\Resources\ServiceRequest\DescriptionResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Filter/AddedDateFilterResource.php.html#9">App\Http\Resources\ServiceRequest\Filter\AddedDateFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Filter/AssigneeFilterResource.php.html#12">App\Http\Resources\ServiceRequest\Filter\AssigneeFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Filter/ImportedFromFilterResource.php.html#12">App\Http\Resources\ServiceRequest\Filter\ImportedFromFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Filter/StatusFilterResource.php.html#12">App\Http\Resources\ServiceRequest\Filter\StatusFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Group/AssigneeBasedGroupDataResource.php.html#8">App\Http\Resources\ServiceRequest\Group\AssigneeBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Group/ImportFromBasedGroupDataResource.php.html#8">App\Http\Resources\ServiceRequest\Group\ImportFromBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Group/PriorityBasedGroupDataResource.php.html#14">App\Http\Resources\ServiceRequest\Group\PriorityBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Group/StatusBasedGroupDataResource.php.html#12">App\Http\Resources\ServiceRequest\Group\StatusBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/InProgressResource.php.html#12">App\Http\Resources\ServiceRequest\InProgressResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ListResource.php.html#14">App\Http\Resources\ServiceRequest\ListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Media/OriginalMediaResource.php.html#17">App\Http\Resources\ServiceRequest\Media\OriginalMediaResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Media/ThumbnailMediaResource.php.html#8">App\Http\Resources\ServiceRequest\Media\ThumbnailMediaResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/MediaResource.php.html#14">App\Http\Resources\ServiceRequest\MediaResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Note/NoteListResource.php.html#13">App\Http\Resources\ServiceRequest\Note\NoteListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PriorityResource.php.html#10">App\Http\Resources\ServiceRequest\PriorityResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ProblemCategoryResource.php.html#12">App\Http\Resources\ServiceRequest\ProblemCategoryResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PropertyAccessInfoResource.php.html#13">App\Http\Resources\ServiceRequest\PropertyAccessInfoResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PropertyAddressResource.php.html#12">App\Http\Resources\ServiceRequest\PropertyAddressResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PropertyResource.php.html#13">App\Http\Resources\ServiceRequest\PropertyResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ResidentResource.php.html#12">App\Http\Resources\ServiceRequest\ResidentResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestDescriptionResource.php.html#12">App\Http\Resources\ServiceRequest\ServiceRequestDescriptionResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestResource.php.html#21">App\Http\Resources\ServiceRequest\ServiceRequestResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestSourceResource.php.html#13">App\Http\Resources\ServiceRequest\ServiceRequestSourceResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestTypeResource.php.html#13">App\Http\Resources\ServiceRequest\ServiceRequestTypeResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/StoreResource.php.html#12">App\Http\Resources\ServiceRequest\StoreResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrder/WorkOrderIssuesResource.php.html#14">App\Http\Resources\ServiceRequest\WorkOrder\WorkOrderIssuesResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrder/WorkOrderStoreIssueResource.php.html#15">App\Http\Resources\ServiceRequest\WorkOrder\WorkOrderStoreIssueResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderResource.php.html#30">App\Http\Resources\ServiceRequest\WorkOrderResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderStoreResource.php.html#15">App\Http\Resources\ServiceRequest\WorkOrderStoreResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Tag/TagResource.php.html#12">App\Http\Resources\Tag\TagResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/AvailabilityDateResource.php.html#8">App\Http\Resources\Technician\AvailabilityDateResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/SelectedSkillsResource.php.html#15">App\Http\Resources\Technician\SelectedSkillsResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianAgendaResource.php.html#19">App\Http\Resources\Technician\TechnicianAgendaResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianBlockOutDeleteResource.php.html#12">App\Http\Resources\Technician\TechnicianBlockOutDeleteResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianBlockOutResource.php.html#14">App\Http\Resources\Technician\TechnicianBlockOutResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianCalendarViewResource.php.html#12">App\Http\Resources\Technician\TechnicianCalendarViewResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/WorkOrderPropertyResource.php.html#12">App\Http\Resources\Technician\WorkOrderPropertyResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/WorkOrderTaskResource.php.html#14">App\Http\Resources\Technician\WorkOrderTaskResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/TemplateResource.php.html#8">App\Http\Resources\TemplateResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/AccountInfoResource.php.html#14">App\Http\Resources\User\AccountInfoResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/Group/RoleBasedGroupDataResource.php.html#8">App\Http\Resources\User\Group\RoleBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/Group/StatusBasedGroupDataResource.php.html#13">App\Http\Resources\User\Group\StatusBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/ProfileResource.php.html#13">App\Http\Resources\User\ProfileResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/RoleResource.php.html#11">App\Http\Resources\User\RoleResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/TechnicianResource.php.html#12">App\Http\Resources\User\TechnicianResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/UserListResource.php.html#14">App\Http\Resources\User\UserListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/UserResource.php.html#16">App\Http\Resources\User\UserResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/UserRoleResource.php.html#12">App\Http\Resources\User\UserRoleResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/UserStatusResource.php.html#8">App\Http\Resources\User\UserStatusResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Vendor/ProfileResource.php.html#15">App\Http\Resources\Vendor\ProfileResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Vendor/UpdateVendorResource.php.html#12">App\Http\Resources\Vendor\UpdateVendorResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/VendorUserResource.php.html#13">App\Http\Resources\VendorUserResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/ConfigurationResource.php.html#12">App\Http\Resources\View\ConfigurationResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/DeleteResource.php.html#12">App\Http\Resources\View\DeleteResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/PinResource.php.html#13">App\Http\Resources\View\PinResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/PinnedViewCount.php.html#8">App\Http\Resources\View\PinnedViewCount</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/SetDefaultResource.php.html#12">App\Http\Resources\View\SetDefaultResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/StoreResource.php.html#12">App\Http\Resources\View\StoreResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/UpdatedResource.php.html#14">App\Http\Resources\View\UpdatedResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/ViewResource.php.html#12">App\Http\Resources\View\ViewResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ActivityLogResource.php.html#11">App\Http\Resources\WorkOrder\ActivityLogResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/AppointmentRescheduledResource.php.html#14">App\Http\Resources\WorkOrder\AppointmentRescheduledResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/AppointmentTechnicianResource.php.html#10">App\Http\Resources\WorkOrder\AppointmentTechnicianResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/AssigneeListResource.php.html#10">App\Http\Resources\WorkOrder\AssigneeListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/AwaitingAvailabilityResources.php.html#12">App\Http\Resources\WorkOrder\AwaitingAvailabilityResources</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/CancelResource.php.html#13">App\Http\Resources\WorkOrder\CancelResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ClosedResource.php.html#14">App\Http\Resources\WorkOrder\ClosedResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/CompletedResource.php.html#12">App\Http\Resources\WorkOrder\CompletedResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/CreateQuoteResource.php.html#13">App\Http\Resources\WorkOrder\CreateQuoteResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/DueDateResource.php.html#13">App\Http\Resources\WorkOrder\DueDateResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/EnRouteResource.php.html#15">App\Http\Resources\WorkOrder\EnRouteResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/AssigneeFilterResource.php.html#12">App\Http\Resources\WorkOrder\Filter\AssigneeFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/CategoryFilterResource.php.html#12">App\Http\Resources\WorkOrder\Filter\CategoryFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/CreatedDateFilterResource.php.html#9">App\Http\Resources\WorkOrder\Filter\CreatedDateFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/HealthScoreFilterResource.php.html#9">App\Http\Resources\WorkOrder\Filter\HealthScoreFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/OverdueFilterResource.php.html#9">App\Http\Resources\WorkOrder\Filter\OverdueFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/PriorityFilterResource.php.html#9">App\Http\Resources\WorkOrder\Filter\PriorityFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/ProviderFilterResource.php.html#12">App\Http\Resources\WorkOrder\Filter\ProviderFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/ScheduledDateFilterResource.php.html#9">App\Http\Resources\WorkOrder\Filter\ScheduledDateFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/StatusFilterResource.php.html#12">App\Http\Resources\WorkOrder\Filter\StatusFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/TagFilterResource.php.html#12">App\Http\Resources\WorkOrder\Filter\TagFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/TechnicianFilterResource.php.html#12">App\Http\Resources\WorkOrder\Filter\TechnicianFilterResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/AssigneeBasedGroupDataResource.php.html#8">App\Http\Resources\WorkOrder\Group\AssigneeBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/CategoryBasedGroupDataResource.php.html#8">App\Http\Resources\WorkOrder\Group\CategoryBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/HealthScoreGroupDataResource.php.html#8">App\Http\Resources\WorkOrder\Group\HealthScoreGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/PriorityBasedGroupDataResource.php.html#14">App\Http\Resources\WorkOrder\Group\PriorityBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/StatusBasedGroupDataResource.php.html#12">App\Http\Resources\WorkOrder\Group\StatusBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/TagBasedGroupDataResource.php.html#8">App\Http\Resources\WorkOrder\Group\TagBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/TechnicianBasedGroupDataResource.php.html#8">App\Http\Resources\WorkOrder\Group\TechnicianBasedGroupDataResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/HealthScore/HealthScoreResource.php.html#13">App\Http\Resources\WorkOrder\HealthScore\HealthScoreResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Invoice/FullyPaidResource.php.html#14">App\Http\Resources\WorkOrder\Invoice\FullyPaidResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Invoice/InvoiceResource.php.html#13">App\Http\Resources\WorkOrder\Invoice\InvoiceResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Invoice/PartiallyPaidResource.php.html#14">App\Http\Resources\WorkOrder\Invoice\PartiallyPaidResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ListTaskAppointmentResource.php.html#22">App\Http\Resources\WorkOrder\List\ListTaskAppointmentResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#31">App\Http\Resources\WorkOrder\ListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Media/OriginalMediaResource.php.html#17">App\Http\Resources\WorkOrder\Media\OriginalMediaResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Media/ThumbnailMediaResource.php.html#8">App\Http\Resources\WorkOrder\Media\ThumbnailMediaResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/MediaResource.php.html#14">App\Http\Resources\WorkOrder\MediaResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Note/NoteListResource.php.html#13">App\Http\Resources\WorkOrder\Note\NoteListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseEnRouteResource.php.html#15">App\Http\Resources\WorkOrder\PauseEnRouteResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseResource.php.html#12">App\Http\Resources\WorkOrder\PauseResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseWorkResource.php.html#14">App\Http\Resources\WorkOrder\PauseWorkResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PriorityUpdateResource.php.html#14">App\Http\Resources\WorkOrder\PriorityUpdateResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ProblemCategoryResource.php.html#12">App\Http\Resources\WorkOrder\ProblemCategoryResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PropertyAccessInfoResource.php.html#13">App\Http\Resources\WorkOrder\PropertyAccessInfoResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PropertyAddressResource.php.html#12">App\Http\Resources\WorkOrder\PropertyAddressResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ProviderResource.php.html#16">App\Http\Resources\WorkOrder\ProviderResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Quote/QuoteMaterialResource.php.html#12">App\Http\Resources\WorkOrder\Quote\QuoteMaterialResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Quote/TaskDeleteResource.php.html#12">App\Http\Resources\WorkOrder\Quote\TaskDeleteResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteApproveResource.php.html#13">App\Http\Resources\WorkOrder\QuoteApproveResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteRejectResource.php.html#13">App\Http\Resources\WorkOrder\QuoteRejectResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteResource.php.html#14">App\Http\Resources\WorkOrder\QuoteResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteTaskResource.php.html#14">App\Http\Resources\WorkOrder\QuoteTaskResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ReOpenResource.php.html#12">App\Http\Resources\WorkOrder\ReOpenResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ReadyToInvoiceResource.php.html#12">App\Http\Resources\WorkOrder\ReadyToInvoiceResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ReadyToScheduleResource.php.html#13">App\Http\Resources\WorkOrder\ReadyToScheduleResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResidentResource.php.html#12">App\Http\Resources\WorkOrder\ResidentResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResolveResource.php.html#12">App\Http\Resources\WorkOrder\ResolveResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeEnRouteResource.php.html#15">App\Http\Resources\WorkOrder\ResumeEnRouteResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeWorkResource.php.html#14">App\Http\Resources\WorkOrder\ResumeWorkResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ServiceRequestResource.php.html#18">App\Http\Resources\WorkOrder\ServiceRequestResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/StartWorkResource.php.html#13">App\Http\Resources\WorkOrder\StartWorkResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/StopTripResource.php.html#12">App\Http\Resources\WorkOrder\StopTripResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/StoreResources.php.html#12">App\Http\Resources\WorkOrder\StoreResources</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskAppointmentResource.php.html#13">App\Http\Resources\WorkOrder\TaskAppointmentResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskListResource.php.html#12">App\Http\Resources\WorkOrder\TaskListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskMaterialResource.php.html#12">App\Http\Resources\WorkOrder\TaskMaterialResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskResource.php.html#18">App\Http\Resources\WorkOrder\TaskResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskScheduledAppointmentResource.php.html#15">App\Http\Resources\WorkOrder\TaskScheduledAppointmentResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TechnicianAppointmentResource.php.html#13">App\Http\Resources\WorkOrder\TechnicianAppointmentResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Trip/TripDetailsResource.php.html#15">App\Http\Resources\WorkOrder\Trip\TripDetailsResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TripResource.php.html#24">App\Http\Resources\WorkOrder\TripResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderAssigneeResource.php.html#10">App\Http\Resources\WorkOrder\WorkOrderAssigneeResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderDescriptionResource.php.html#12">App\Http\Resources\WorkOrder\WorkOrderDescriptionResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderNteResource.php.html#12">App\Http\Resources\WorkOrder\WorkOrderNteResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderResource.php.html#37">App\Http\Resources\WorkOrder\WorkOrderResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderScheduledResource.php.html#12">App\Http\Resources\WorkOrder\WorkOrderScheduledResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderSendToVendorResource.php.html#10">App\Http\Resources\WorkOrder\WorkOrderSendToVendorResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderServiceRequestStatusResource.php.html#12">App\Http\Resources\WorkOrder\WorkOrderServiceRequestStatusResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderSubResource.php.html#28">App\Http\Resources\WorkOrder\WorkOrderSubResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/IssueDeclinedReasonResource.php.html#13">App\Http\Resources\WorkOrderIssue\IssueDeclinedReasonResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/IssueDeclinedResource.php.html#13">App\Http\Resources\WorkOrderIssue\IssueDeclinedResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/MaterialResource.php.html#12">App\Http\Resources\WorkOrderIssue\MaterialResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/PendingIssueResource.php.html#11">App\Http\Resources\WorkOrderIssue\PendingIssueResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/WorkOrderIssueDetailsResource.php.html#13">App\Http\Resources\WorkOrderIssue\WorkOrderIssueDetailsResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/WorkOrderIssueListResource.php.html#13">App\Http\Resources\WorkOrderIssue\WorkOrderIssueListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#19">App\Http\Middleware\LogRequestsToDatabase</a></td><td class="text-right">3%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#56">App\Http\Controllers\Auth\RegisterController</a></td><td class="text-right">32%</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ActiveTripResource.php.html#15">App\Http\Resources\WorkOrder\List\ActiveTripResource</a></td><td class="text-right">41%</td></tr>
       <tr><td><a href="Middleware/MinimumAppVeriosnRequiredMiddleware.php.html#12">App\Http\Middleware\MinimumAppVeriosnRequiredMiddleware</a></td><td class="text-right">43%</td></tr>
       <tr><td><a href="Middleware/AppAccessPermissionMiddleware.php.html#15">App\Http\Middleware\AppAccessPermissionMiddleware</a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderTripResource.php.html#17">App\Http\Resources\WorkOrder\WorkOrderTripResource</a></td><td class="text-right">54%</td></tr>
       <tr><td><a href="Filters/WorkOrderListFilter.php.html#10">App\Http\Filters\WorkOrderListFilter</a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="Middleware/ValidateCognitoToken.php.html#18">App\Http\Middleware\ValidateCognitoToken</a></td><td class="text-right">63%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderStatusResource.php.html#14">App\Http\Resources\WorkOrder\WorkOrderStatusResource</a></td><td class="text-right">63%</td></tr>
       <tr><td><a href="Resources/ResidentAvailability/ResidentAvailabilityResource.php.html#13">App\Http\Resources\ResidentAvailability\ResidentAvailabilityResource</a></td><td class="text-right">64%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ListRequest.php.html#7">App\Http\Requests\WorkOrder\ListRequest</a></td><td class="text-right">65%</td></tr>
       <tr><td><a href="Resources/Vendor/VendorOnboardingResource.php.html#10">App\Http\Resources\Vendor\VendorOnboardingResource</a></td><td class="text-right">69%</td></tr>
       <tr><td><a href="Controllers/Vendor/VendorOnboardingLookupController.php.html#23">App\Http\Controllers\Vendor\VendorOnboardingLookupController</a></td><td class="text-right">83%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/WorkOrderIssueStatusResource.php.html#13">App\Http\Resources\WorkOrderIssue\WorkOrderIssueStatusResource</a></td><td class="text-right">85%</td></tr>
       <tr><td><a href="Requests/Vendor/ListRequest.php.html#7">App\Http\Requests\Vendor\ListRequest</a></td><td class="text-right">86%</td></tr>
       <tr><td><a href="Resources/Issue/IssueResource.php.html#15">App\Http\Resources\Issue\IssueResource</a></td><td class="text-right">86%</td></tr>
       <tr><td><a href="Resources/Vendor/WorkOrderResource.php.html#33">App\Http\Resources\Vendor\WorkOrderResource</a></td><td class="text-right">89%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Controllers/WorkOrderController.php.html#115">App\Http\Controllers\WorkOrderController</a></td><td class="text-right">34782</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#89">App\Http\Controllers\ServiceRequestController</a></td><td class="text-right">12656</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#61">App\Http\Controllers\UserController</a></td><td class="text-right">7310</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#43">App\Http\Controllers\ViewController</a></td><td class="text-right">2862</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#49">App\Http\Controllers\QuoteController</a></td><td class="text-right">2756</td></tr>
       <tr><td><a href="Requests/Invoice/InvoiceRequest.php.html#24">App\Http\Requests\Invoice\InvoiceRequest</a></td><td class="text-right">2756</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#43">App\Http\Controllers\WorkOrderMediaController</a></td><td class="text-right">2450</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#56">App\Http\Controllers\Auth\RegisterController</a></td><td class="text-right">2027</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#19">App\Http\Middleware\LogRequestsToDatabase</a></td><td class="text-right">1606</td></tr>
       <tr><td><a href="Controllers/ServiceRequestMediaController.php.html#39">App\Http\Controllers\ServiceRequestMediaController</a></td><td class="text-right">1482</td></tr>
       <tr><td><a href="Controllers/PDFController.php.html#30">App\Http\Controllers\PDFController</a></td><td class="text-right">1260</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#31">App\Http\Resources\WorkOrder\ListResource</a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#37">App\Http\Controllers\TechnicianAppointmentsController</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#30">App\Http\Controllers\WebhookController</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="Controllers/TechnicianController.php.html#26">App\Http\Controllers\TechnicianController</a></td><td class="text-right">930</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskResource.php.html#18">App\Http\Resources\WorkOrder\TaskResource</a></td><td class="text-right">870</td></tr>
       <tr><td><a href="Resources/WorkOrder/TripResource.php.html#24">App\Http\Resources\WorkOrder\TripResource</a></td><td class="text-right">870</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#39">App\Http\Controllers\LookupController</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#31">App\Http\Controllers\SchedulingController</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderResource.php.html#37">App\Http\Resources\WorkOrder\WorkOrderResource</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#25">App\Http\Controllers\RoleController</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="Controllers/VendorController.php.html#33">App\Http\Controllers\VendorController</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="Controllers/WorkOrderNoteController.php.html#27">App\Http\Controllers\WorkOrderNoteController</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#28">App\Http\Controllers\LulaWebhookController</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="Resources/Quote/ListResource.php.html#18">App\Http\Resources\Quote\ListResource</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Controllers/ServiceRequestNoteController.php.html#31">App\Http\Controllers\ServiceRequestNoteController</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderSubResource.php.html#28">App\Http\Resources\WorkOrder\WorkOrderSubResource</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="Controllers/TagController.php.html#24">App\Http\Controllers\TagController</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Controllers/Developer/HealthCheckController.php.html#14">App\Http\Controllers\Developer\HealthCheckController</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Controllers/ResidentAvailabilityController.php.html#19">App\Http\Controllers\ResidentAvailabilityController</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ResidentAvailabilityRequest.php.html#15">App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Requests/Technician/WorkingHoursUpdateRequest.php.html#10">App\Http\Requests\Technician\WorkingHoursUpdateRequest</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Requests/WorkOrder/CreateQuoteRequest.php.html#14">App\Http\Requests\WorkOrder\CreateQuoteRequest</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Controllers/OrganizationController.php.html#20">App\Http\Controllers\OrganizationController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Controllers/ServiceRequestAssigneeController.php.html#22">App\Http\Controllers\ServiceRequestAssigneeController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Controllers/WorkOrderAssigneeController.php.html#22">App\Http\Controllers\WorkOrderAssigneeController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Middleware/MakeTusMediaFileName.php.html#12">App\Http\Middleware\MakeTusMediaFileName</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Requests/WorkOrder/Quote/TaskCreateRequest.php.html#14">App\Http\Requests\WorkOrder\Quote\TaskCreateRequest</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Requests/WorkOrder/Quote/TaskUpdateRequest.php.html#15">App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderResource.php.html#30">App\Http\Resources\ServiceRequest\WorkOrderResource</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Controllers/PublicAccessController.php.html#17">App\Http\Controllers\PublicAccessController</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Resources/Invoice/TripResource.php.html#16">App\Http\Resources\Invoice\TripResource</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Controllers/InvoiceController.php.html#21">App\Http\Controllers\InvoiceController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/ServiceRequestActivityLogController.php.html#24">App\Http\Controllers\ServiceRequestActivityLogController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Resources/Scheduling/ScheduleContextResource.php.html#20">App\Http\Resources\Scheduling\ScheduleContextResource</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestResource.php.html#21">App\Http\Resources\ServiceRequest\ServiceRequestResource</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/Auth/SSOController.php.html#22">App\Http\Controllers\Auth\SSOController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Controllers/WorkOrderActivityLogController.php.html#22">App\Http\Controllers\WorkOrderActivityLogController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Filters/ServiceRequestListFilter.php.html#10">App\Http\Filters\ServiceRequestListFilter</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderTripResource.php.html#17">App\Http\Resources\WorkOrder\WorkOrderTripResource</a></td><td class="text-right">78</td></tr>
       <tr><td><a href="Controllers/AppVersionController.php.html#14">App\Http\Controllers\AppVersionController</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/DatabaseNotificationController.php.html#16">App\Http\Controllers\DatabaseNotificationController</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Filters/QuoteListFilter.php.html#10">App\Http\Filters\QuoteListFilter</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ListRequest.php.html#8">App\Http\Requests\ServiceRequest\ListRequest</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreWorkOrderRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreWorkOrderRequest</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Requests/Technician/BlockOutUpdateRequest.php.html#14">App\Http\Requests\Technician\BlockOutUpdateRequest</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Requests/Technician/SkillsUpdateRequest.php.html#12">App\Http\Requests\Technician\SkillsUpdateRequest</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Resources/View/ViewResource.php.html#12">App\Http\Resources\View\ViewResource</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ListTaskAppointmentResource.php.html#22">App\Http\Resources\WorkOrder\List\ListTaskAppointmentResource</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ActiveTripResource.php.html#15">App\Http\Resources\WorkOrder\List\ActiveTripResource</a></td><td class="text-right">67</td></tr>
       <tr><td><a href="Controllers/MediaController.php.html#16">App\Http\Controllers\MediaController</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Filters/UserListFilter.php.html#11">App\Http\Filters\UserListFilter</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Requests/Quote/ListRequest.php.html#7">App\Http\Requests\Quote\ListRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Requests/User/ListRequest.php.html#7">App\Http\Requests\User\ListRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Requests/WorkOrder/SendToVendorRequest.php.html#12">App\Http\Requests\WorkOrder\SendToVendorRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Resources/Notification/NotificationResource.php.html#13">App\Http\Resources\Notification\NotificationResource</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Resources/Scheduling/GetTechnicianSchedulesResources.php.html#15">App\Http\Resources\Scheduling\GetTechnicianSchedulesResources</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteResource.php.html#14">App\Http\Resources\WorkOrder\QuoteResource</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/WorkOrder/WorkOrderIssueController.php.html#17">App\Http\Controllers\WorkOrder\WorkOrderIssueController</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Middleware/HorizonBasicAuth.php.html#7">App\Http\Middleware\HorizonBasicAuth</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Requests/WorkOrder/StoreRequest.php.html#11">App\Http\Requests\WorkOrder\StoreRequest</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PropertyResource.php.html#13">App\Http\Resources\ServiceRequest\PropertyResource</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resources/WorkOrder/CreateQuoteResource.php.html#13">App\Http\Resources\WorkOrder\CreateQuoteResource</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resources/WorkOrder/EnRouteResource.php.html#15">App\Http\Resources\WorkOrder\EnRouteResource</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseEnRouteResource.php.html#15">App\Http\Resources\WorkOrder\PauseEnRouteResource</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeEnRouteResource.php.html#15">App\Http\Resources\WorkOrder\ResumeEnRouteResource</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resources/WorkOrder/ServiceRequestResource.php.html#18">App\Http\Resources\WorkOrder\ServiceRequestResource</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Filters/WorkOrderListFilter.php.html#10">App\Http\Filters\WorkOrderListFilter</a></td><td class="text-right">38</td></tr>
       <tr><td><a href="Filters/TagListFilter.php.html#8">App\Http\Filters\TagListFilter</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Requests/Technician/StoreBlockOutRequest.php.html#14">App\Http\Requests\Technician\StoreBlockOutRequest</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Requests/User/UpdateRequest.php.html#17">App\Http\Requests\User\UpdateRequest</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ListResource.php.html#14">App\Http\Resources\ServiceRequest\ListResource</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Media/OriginalMediaResource.php.html#17">App\Http\Resources\ServiceRequest\Media\OriginalMediaResource</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/ServiceRequest/MediaResource.php.html#14">App\Http\Resources\ServiceRequest\MediaResource</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianAgendaResource.php.html#19">App\Http\Resources\Technician\TechnicianAgendaResource</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianBlockOutResource.php.html#14">App\Http\Resources\Technician\TechnicianBlockOutResource</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/User/UserListResource.php.html#14">App\Http\Resources\User\UserListResource</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/AppointmentRescheduledResource.php.html#14">App\Http\Resources\WorkOrder\AppointmentRescheduledResource</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/HealthScore/HealthScoreResource.php.html#13">App\Http\Resources\WorkOrder\HealthScore\HealthScoreResource</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/Media/OriginalMediaResource.php.html#17">App\Http\Resources\WorkOrder\Media\OriginalMediaResource</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/MediaResource.php.html#14">App\Http\Resources\WorkOrder\MediaResource</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Middleware/ValidateCognitoToken.php.html#18">App\Http\Middleware\ValidateCognitoToken</a></td><td class="text-right">21</td></tr>
       <tr><td><a href="Middleware/RedirectIfAuthenticated.php.html#10">App\Http\Middleware\RedirectIfAuthenticated</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Requests/View/DuplicateRequest.php.html#12">App\Http\Requests\View\DuplicateRequest</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Requests/View/RenameRequest.php.html#11">App\Http\Requests\View\RenameRequest</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Requests/View/StoreRequest.php.html#13">App\Http\Requests\View\StoreRequest</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/AppfolioVendorResource.php.html#14">App\Http\Resources\AppfolioVendorResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/Scheduling/GetVendorAvailabilityResources.php.html#10">App\Http\Resources\Scheduling\GetVendorAvailabilityResources</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Note/NoteListResource.php.html#13">App\Http\Resources\ServiceRequest\Note\NoteListResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianCalendarViewResource.php.html#12">App\Http\Resources\Technician\TechnicianCalendarViewResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/User/UserResource.php.html#16">App\Http\Resources\User\UserResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/View/UpdatedResource.php.html#14">App\Http\Resources\View\UpdatedResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/WorkOrder/ClosedResource.php.html#14">App\Http\Resources\WorkOrder\ClosedResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/WorkOrder/Note/NoteListResource.php.html#13">App\Http\Resources\WorkOrder\Note\NoteListResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseWorkResource.php.html#14">App\Http\Resources\WorkOrder\PauseWorkResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/WorkOrder/ProviderResource.php.html#16">App\Http\Resources\WorkOrder\ProviderResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeWorkResource.php.html#14">App\Http\Resources\WorkOrder\ResumeWorkResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/WorkOrder/StartWorkResource.php.html#13">App\Http\Resources\WorkOrder\StartWorkResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/WorkOrder/TechnicianAppointmentResource.php.html#13">App\Http\Resources\WorkOrder\TechnicianAppointmentResource</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/Vendor/VendorOnboardingResource.php.html#10">App\Http\Resources\Vendor\VendorOnboardingResource</a></td><td class="text-right">19</td></tr>
       <tr><td><a href="Resources/Vendor/WorkOrderResource.php.html#33">App\Http\Resources\Vendor\WorkOrderResource</a></td><td class="text-right">17</td></tr>
       <tr><td><a href="Filters/RoleListFilter.php.html#5">App\Http\Filters\RoleListFilter</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Requests/ServiceRequest/UpdateAvailabilityRequest.php.html#12">App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Requests/WorkOrder/CompleteRequest.php.html#12">App\Http\Requests\WorkOrder\CompleteRequest</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/Invoice/CreateInvoiceResource.php.html#13">App\Http\Resources\Invoice\CreateInvoiceResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/PriorityResource.php.html#13">App\Http\Resources\PriorityResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Filter/AddedDateFilterResource.php.html#9">App\Http\Resources\ServiceRequest\Filter\AddedDateFilterResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PropertyAccessInfoResource.php.html#13">App\Http\Resources\ServiceRequest\PropertyAccessInfoResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderStoreResource.php.html#15">App\Http\Resources\ServiceRequest\WorkOrderStoreResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/Technician/WorkOrderPropertyResource.php.html#12">App\Http\Resources\Technician\WorkOrderPropertyResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/Technician/WorkOrderTaskResource.php.html#14">App\Http\Resources\Technician\WorkOrderTaskResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/User/AccountInfoResource.php.html#14">App\Http\Resources\User\AccountInfoResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/CreatedDateFilterResource.php.html#9">App\Http\Resources\WorkOrder\Filter\CreatedDateFilterResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/OverdueFilterResource.php.html#9">App\Http\Resources\WorkOrder\Filter\OverdueFilterResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/PriorityFilterResource.php.html#9">App\Http\Resources\WorkOrder\Filter\PriorityFilterResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/ScheduledDateFilterResource.php.html#9">App\Http\Resources\WorkOrder\Filter\ScheduledDateFilterResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/PropertyAccessInfoResource.php.html#13">App\Http\Resources\WorkOrder\PropertyAccessInfoResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskAppointmentResource.php.html#13">App\Http\Resources\WorkOrder\TaskAppointmentResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/Trip/TripDetailsResource.php.html#15">App\Http\Resources\WorkOrder\Trip\TripDetailsResource</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/Vendor/VendorOnboardingLookupController.php.html#23">App\Http\Controllers\Vendor\VendorOnboardingLookupController</a></td><td class="text-right">9</td></tr>
       <tr><td><a href="Requests/WorkOrder/ListRequest.php.html#7">App\Http\Requests\WorkOrder\ListRequest</a></td><td class="text-right">9</td></tr>
       <tr><td><a href="Middleware/AppAccessPermissionMiddleware.php.html#15">App\Http\Middleware\AppAccessPermissionMiddleware</a></td><td class="text-right">8</td></tr>
       <tr><td><a href="Middleware/MinimumAppVeriosnRequiredMiddleware.php.html#12">App\Http\Middleware\MinimumAppVeriosnRequiredMiddleware</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Requests/Vendor/ListRequest.php.html#7">App\Http\Requests\Vendor\ListRequest</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Middleware/Authenticate.php.html#8">App\Http\Middleware\Authenticate</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Requests/WorkOrder/ReadyToInvoiceRequest.php.html#11">App\Http\Requests\WorkOrder\ReadyToInvoiceRequest</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceLineItemSubsidiariesResource.php.html#14">App\Http\Resources\Invoice\InvoiceLineItemSubsidiariesResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceListResource.php.html#14">App\Http\Resources\Invoice\InvoiceListResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ResidentAvailability/ResidentAvailabilityShowResource.php.html#14">App\Http\Resources\ResidentAvailability\ResidentAvailabilityShowResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ActivityLogResource.php.html#11">App\Http\Resources\ServiceRequest\ActivityLogResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/AssigneeResource.php.html#10">App\Http\Resources\ServiceRequest\AssigneeResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Group/PriorityBasedGroupDataResource.php.html#14">App\Http\Resources\ServiceRequest\Group\PriorityBasedGroupDataResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PriorityResource.php.html#10">App\Http\Resources\ServiceRequest\PriorityResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestDescriptionResource.php.html#12">App\Http\Resources\ServiceRequest\ServiceRequestDescriptionResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestSourceResource.php.html#13">App\Http\Resources\ServiceRequest\ServiceRequestSourceResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestTypeResource.php.html#13">App\Http\Resources\ServiceRequest\ServiceRequestTypeResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/StoreResource.php.html#12">App\Http\Resources\ServiceRequest\StoreResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/User/ProfileResource.php.html#13">App\Http\Resources\User\ProfileResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/View/ConfigurationResource.php.html#12">App\Http\Resources\View\ConfigurationResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/ActivityLogResource.php.html#11">App\Http\Resources\WorkOrder\ActivityLogResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/CompletedResource.php.html#12">App\Http\Resources\WorkOrder\CompletedResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/DueDateResource.php.html#13">App\Http\Resources\WorkOrder\DueDateResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/HealthScoreFilterResource.php.html#9">App\Http\Resources\WorkOrder\Filter\HealthScoreFilterResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/PriorityBasedGroupDataResource.php.html#14">App\Http\Resources\WorkOrder\Group\PriorityBasedGroupDataResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/Invoice/InvoiceResource.php.html#13">App\Http\Resources\WorkOrder\Invoice\InvoiceResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/PriorityUpdateResource.php.html#14">App\Http\Resources\WorkOrder\PriorityUpdateResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/Quote/QuoteMaterialResource.php.html#12">App\Http\Resources\WorkOrder\Quote\QuoteMaterialResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteApproveResource.php.html#13">App\Http\Resources\WorkOrder\QuoteApproveResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteRejectResource.php.html#13">App\Http\Resources\WorkOrder\QuoteRejectResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteTaskResource.php.html#14">App\Http\Resources\WorkOrder\QuoteTaskResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/StoreResources.php.html#12">App\Http\Resources\WorkOrder\StoreResources</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskListResource.php.html#12">App\Http\Resources\WorkOrder\TaskListResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/WorkOrderIssueDetailsResource.php.html#13">App\Http\Resources\WorkOrderIssue\WorkOrderIssueDetailsResource</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderStatusResource.php.html#14">App\Http\Resources\WorkOrder\WorkOrderStatusResource</a></td><td class="text-right">4</td></tr>
       <tr><td><a href="Resources/ResidentAvailability/ResidentAvailabilityResource.php.html#13">App\Http\Resources\ResidentAvailability\ResidentAvailabilityResource</a></td><td class="text-right">4</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/WorkOrderIssueStatusResource.php.html#13">App\Http\Resources\WorkOrderIssue\WorkOrderIssueStatusResource</a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Resources/Issue/IssueResource.php.html#15">App\Http\Resources\Issue\IssueResource</a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Controllers/AppVersionController.php.html#19"><abbr title="App\Http\Controllers\AppVersionController::checkLatestAppVersion">checkLatestAppVersion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#167"><abbr title="App\Http\Controllers\Auth\RegisterController::searchAddress">searchAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#402"><abbr title="App\Http\Controllers\Auth\RegisterController::signup">signup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#510"><abbr title="App\Http\Controllers\Auth\RegisterController::createOrganization">createOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#536"><abbr title="App\Http\Controllers\Auth\RegisterController::setupUserPool">setupUserPool</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#642"><abbr title="App\Http\Controllers\Auth\RegisterController::findLoginConfig">findLoginConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#709"><abbr title="App\Http\Controllers\Auth\RegisterController::findProviderLoginConfig">findProviderLoginConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#726"><abbr title="App\Http\Controllers\Auth\RegisterController::authenticateClientCredential">authenticateClientCredential</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#786"><abbr title="App\Http\Controllers\Auth\RegisterController::createUserPoolClient">createUserPoolClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#823"><abbr title="App\Http\Controllers\Auth\RegisterController::createUserPool">createUserPool</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#890"><abbr title="App\Http\Controllers\Auth\RegisterController::syncUserRolePermissions">syncUserRolePermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/SSOController.php.html#26"><abbr title="App\Http\Controllers\Auth\SSOController::validateUser">validateUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/SSOController.php.html#76"><abbr title="App\Http\Controllers\Auth\SSOController::getCognitoUserDetails">getCognitoUserDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/DatabaseNotificationController.php.html#18"><abbr title="App\Http\Controllers\DatabaseNotificationController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/DatabaseNotificationController.php.html#60"><abbr title="App\Http\Controllers\DatabaseNotificationController::clearedNotifications">clearedNotifications</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/HealthCheckController.php.html#26"><abbr title="App\Http\Controllers\Developer\HealthCheckController::basicCheck">basicCheck</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/HealthCheckController.php.html#38"><abbr title="App\Http\Controllers\Developer\HealthCheckController::utilitiesCheck">utilitiesCheck</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/HealthCheckController.php.html#67"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkDatabaseConnection">checkDatabaseConnection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/HealthCheckController.php.html#88"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkCacheConnection">checkCacheConnection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/HealthCheckController.php.html#110"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkLogging">checkLogging</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/LogViewerController.php.html#24"><abbr title="App\Http\Controllers\Developer\LogViewerController::requestLogs">requestLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/LogViewerController.php.html#34"><abbr title="App\Http\Controllers\Developer\LogViewerController::requestLogShow">requestLogShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/LogViewerController.php.html#42"><abbr title="App\Http\Controllers\Developer\LogViewerController::incomingWebhookLogs">incomingWebhookLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/LogViewerController.php.html#50"><abbr title="App\Http\Controllers\Developer\LogViewerController::incomingWebhookLogShow">incomingWebhookLogShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/LogViewerController.php.html#58"><abbr title="App\Http\Controllers\Developer\LogViewerController::outgoingWebhookLogs">outgoingWebhookLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/LogViewerController.php.html#66"><abbr title="App\Http\Controllers\Developer\LogViewerController::outgoingWebhookLogShow">outgoingWebhookLogShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/LogViewerController.php.html#76"><abbr title="App\Http\Controllers\Developer\LogViewerController::developerAlerts">developerAlerts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/LogViewerController.php.html#86"><abbr title="App\Http\Controllers\Developer\LogViewerController::developerAlertShow">developerAlertShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/LogViewerController.php.html#93"><abbr title="App\Http\Controllers\Developer\LogViewerController::vendorPublicApiLogs">vendorPublicApiLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Developer/LogViewerController.php.html#98"><abbr title="App\Http\Controllers\Developer\LogViewerController::vendorPublicApiLogShow">vendorPublicApiLogShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/InvoiceController.php.html#23"><abbr title="App\Http\Controllers\InvoiceController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/InvoiceController.php.html#41"><abbr title="App\Http\Controllers\InvoiceController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/InvoiceController.php.html#92"><abbr title="App\Http\Controllers\InvoiceController::workOrderInvoiceSummary">workOrderInvoiceSummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#44"><abbr title="App\Http\Controllers\LookupController::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#85"><abbr title="App\Http\Controllers\LookupController::getFieldAppFilters">getFieldAppFilters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#168"><abbr title="App\Http\Controllers\LookupController::getPriorities">getPriorities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#173"><abbr title="App\Http\Controllers\LookupController::getPropertyAccessMethods">getPropertyAccessMethods</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#178"><abbr title="App\Http\Controllers\LookupController::getStates">getStates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#189"><abbr title="App\Http\Controllers\LookupController::getCountry">getCountry</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#200"><abbr title="App\Http\Controllers\LookupController::getProblemCategory">getProblemCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#225"><abbr title="App\Http\Controllers\LookupController::getTechnicians">getTechnicians</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#246"><abbr title="App\Http\Controllers\LookupController::getAssignees">getAssignees</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#260"><abbr title="App\Http\Controllers\LookupController::getQuantityTypes">getQuantityTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#265"><abbr title="App\Http\Controllers\LookupController::endTripeOptions">endTripeOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#276"><abbr title="App\Http\Controllers\LookupController::expectedDurations">expectedDurations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#312"><abbr title="App\Http\Controllers\LookupController::getOnboardingStatuses">getOnboardingStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#34"><abbr title="App\Http\Controllers\LulaWebhookController::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#146"><abbr title="App\Http\Controllers\LulaWebhookController::invalidAction">invalidAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#153"><abbr title="App\Http\Controllers\LulaWebhookController::validateScheduleInProgressPayload">validateScheduleInProgressPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#160"><abbr title="App\Http\Controllers\LulaWebhookController::validateSchedulePayload">validateSchedulePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#171"><abbr title="App\Http\Controllers\LulaWebhookController::validateWorkInProgressPayload">validateWorkInProgressPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#178"><abbr title="App\Http\Controllers\LulaWebhookController::validateWorkPausedPayload">validateWorkPausedPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#186"><abbr title="App\Http\Controllers\LulaWebhookController::validateQualityCheckPayload">validateQualityCheckPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#193"><abbr title="App\Http\Controllers\LulaWebhookController::validateWorkCompletedPayload">validateWorkCompletedPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#203"><abbr title="App\Http\Controllers\LulaWebhookController::validateWorkCancelPayload">validateWorkCancelPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#211"><abbr title="App\Http\Controllers\LulaWebhookController::validateNoteAddedPayload">validateNoteAddedPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#221"><abbr title="App\Http\Controllers\LulaWebhookController::validateInvoicePayload">validateInvoicePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/MediaController.php.html#18"><abbr title="App\Http\Controllers\MediaController::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/OrganizationController.php.html#24"><abbr title="App\Http\Controllers\OrganizationController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/OrganizationController.php.html#32"><abbr title="App\Http\Controllers\OrganizationController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/OrganizationController.php.html#94"><abbr title="App\Http\Controllers\OrganizationController::getTemplates">getTemplates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/OrganizationController.php.html#115"><abbr title="App\Http\Controllers\OrganizationController::updateTemplate">updateTemplate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PDFController.php.html#32"><abbr title="App\Http\Controllers\PDFController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PDFController.php.html#38"><abbr title="App\Http\Controllers\PDFController::viewQuote">viewQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PDFController.php.html#193"><abbr title="App\Http\Controllers\PDFController::viewInvoice">viewInvoice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ProfileController.php.html#14"><abbr title="App\Http\Controllers\ProfileController::profile">profile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PublicAccessController.php.html#19"><abbr title="App\Http\Controllers\PublicAccessController::getMedia">getMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PublicAccessController.php.html#82"><abbr title="App\Http\Controllers\PublicAccessController::getSrMedia">getSrMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PublicApiWorkOrderWebhookEventsController.php.html#13"><abbr title="App\Http\Controllers\PublicApiWorkOrderWebhookEventsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PublicApiWorkOrderWebhookEventsController.php.html#21"><abbr title="App\Http\Controllers\PublicApiWorkOrderWebhookEventsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#60"><abbr title="App\Http\Controllers\QuoteController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#250"><abbr title="App\Http\Controllers\QuoteController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#339"><abbr title="App\Http\Controllers\QuoteController::getGroupView">getGroupView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#399"><abbr title="App\Http\Controllers\QuoteController::workOrderNumberBasedGroupData">workOrderNumberBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#462"><abbr title="App\Http\Controllers\QuoteController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#525"><abbr title="App\Http\Controllers\QuoteController::categoryBasedGroupData">categoryBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#603"><abbr title="App\Http\Controllers\QuoteController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#678"><abbr title="App\Http\Controllers\QuoteController::submittedBasedGroupData">submittedBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#751"><abbr title="App\Http\Controllers\QuoteController::tagBasedGroupData">tagBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ResidentAvailabilityController.php.html#21"><abbr title="App\Http\Controllers\ResidentAvailabilityController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ResidentAvailabilityController.php.html#101"><abbr title="App\Http\Controllers\ResidentAvailabilityController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#32"><abbr title="App\Http\Controllers\RoleController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#51"><abbr title="App\Http\Controllers\RoleController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#122"><abbr title="App\Http\Controllers\RoleController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#136"><abbr title="App\Http\Controllers\RoleController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#212"><abbr title="App\Http\Controllers\RoleController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#232"><abbr title="App\Http\Controllers\RoleController::listPermissions">listPermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#259"><abbr title="App\Http\Controllers\RoleController::hasPermissionsFromInvalidFeatures">hasPermissionsFromInvalidFeatures</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#33"><abbr title="App\Http\Controllers\SchedulingController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#38"><abbr title="App\Http\Controllers\SchedulingController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#43"><abbr title="App\Http\Controllers\SchedulingController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#51"><abbr title="App\Http\Controllers\SchedulingController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#59"><abbr title="App\Http\Controllers\SchedulingController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#67"><abbr title="App\Http\Controllers\SchedulingController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#75"><abbr title="App\Http\Controllers\SchedulingController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#80"><abbr title="App\Http\Controllers\SchedulingController::getContext">getContext</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#126"><abbr title="App\Http\Controllers\SchedulingController::getVendors">getVendors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#176"><abbr title="App\Http\Controllers\SchedulingController::getTechnicianList">getTechnicianList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#204"><abbr title="App\Http\Controllers\SchedulingController::getTechnicianSchedules">getTechnicianSchedules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#234"><abbr title="App\Http\Controllers\SchedulingController::getVendorAvailability">getVendorAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestActivityLogController.php.html#29"><abbr title="App\Http\Controllers\ServiceRequestActivityLogController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestActivityLogController.php.html#132"><abbr title="App\Http\Controllers\ServiceRequestActivityLogController::paginate">paginate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestAssigneeController.php.html#25"><abbr title="App\Http\Controllers\ServiceRequestAssigneeController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestAssigneeController.php.html#78"><abbr title="App\Http\Controllers\ServiceRequestAssigneeController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#98"><abbr title="App\Http\Controllers\ServiceRequestController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#200"><abbr title="App\Http\Controllers\ServiceRequestController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#259"><abbr title="App\Http\Controllers\ServiceRequestController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#595"><abbr title="App\Http\Controllers\ServiceRequestController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#642"><abbr title="App\Http\Controllers\ServiceRequestController::markAdminAvailabilityViewed">markAdminAvailabilityViewed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#661"><abbr title="App\Http\Controllers\ServiceRequestController::getGroupView">getGroupView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#710"><abbr title="App\Http\Controllers\ServiceRequestController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#777"><abbr title="App\Http\Controllers\ServiceRequestController::updateDescription">updateDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#853"><abbr title="App\Http\Controllers\ServiceRequestController::updateAccessMethod">updateAccessMethod</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#878"><abbr title="App\Http\Controllers\ServiceRequestController::createWorkOrder">createWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#986"><abbr title="App\Http\Controllers\ServiceRequestController::triggerIssueUpdatedEvents">triggerIssueUpdatedEvents</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#995"><abbr title="App\Http\Controllers\ServiceRequestController::markAsComplete">markAsComplete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#1113"><abbr title="App\Http\Controllers\ServiceRequestController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#1156"><abbr title="App\Http\Controllers\ServiceRequestController::importedFromBasedGroupData">importedFromBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#1203"><abbr title="App\Http\Controllers\ServiceRequestController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#1252"><abbr title="App\Http\Controllers\ServiceRequestController::needToCreateServiceRequestDescription">needToCreateServiceRequestDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestMediaController.php.html#44"><abbr title="App\Http\Controllers\ServiceRequestMediaController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestMediaController.php.html#177"><abbr title="App\Http\Controllers\ServiceRequestMediaController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestMediaController.php.html#218"><abbr title="App\Http\Controllers\ServiceRequestMediaController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestMediaController.php.html#258"><abbr title="App\Http\Controllers\ServiceRequestMediaController::uploadOriginalMedia">uploadOriginalMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestNoteController.php.html#38"><abbr title="App\Http\Controllers\ServiceRequestNoteController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestNoteController.php.html#87"><abbr title="App\Http\Controllers\ServiceRequestNoteController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestNoteController.php.html#139"><abbr title="App\Http\Controllers\ServiceRequestNoteController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ServiceRequestNoteController.php.html#187"><abbr title="App\Http\Controllers\ServiceRequestNoteController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TagController.php.html#31"><abbr title="App\Http\Controllers\TagController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TagController.php.html#48"><abbr title="App\Http\Controllers\TagController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TagController.php.html#90"><abbr title="App\Http\Controllers\TagController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TagController.php.html#103"><abbr title="App\Http\Controllers\TagController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TagController.php.html#143"><abbr title="App\Http\Controllers\TagController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#44"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#142"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#225"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#299"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#318"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::viewTechnicianCalendar">viewTechnicianCalendar</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#387"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::getAvailabilityDates">getAvailabilityDates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianController.php.html#31"><abbr title="App\Http\Controllers\TechnicianController::updateWorkingHours">updateWorkingHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianController.php.html#97"><abbr title="App\Http\Controllers\TechnicianController::importWorkingHours">importWorkingHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianController.php.html#130"><abbr title="App\Http\Controllers\TechnicianController::updateTechnicianSkills">updateTechnicianSkills</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/TechnicianController.php.html#272"><abbr title="App\Http\Controllers\TechnicianController::importTechnicianSkills">importTechnicianSkills</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#70"><abbr title="App\Http\Controllers\UserController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#84"><abbr title="App\Http\Controllers\UserController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#129"><abbr title="App\Http\Controllers\UserController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#265"><abbr title="App\Http\Controllers\UserController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#281"><abbr title="App\Http\Controllers\UserController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#395"><abbr title="App\Http\Controllers\UserController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#450"><abbr title="App\Http\Controllers\UserController::filters">filters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#492"><abbr title="App\Http\Controllers\UserController::updateCognitoUserDetails">updateCognitoUserDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#551"><abbr title="App\Http\Controllers\UserController::updateNotificationSubscription">updateNotificationSubscription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#580"><abbr title="App\Http\Controllers\UserController::updateUserStatus">updateUserStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#661"><abbr title="App\Http\Controllers\UserController::resetUserPassword">resetUserPassword</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#700"><abbr title="App\Http\Controllers\UserController::getGroupView">getGroupView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#748"><abbr title="App\Http\Controllers\UserController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#801"><abbr title="App\Http\Controllers\UserController::roleBasedGroupData">roleBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Vendor/VendorOnboardingLookupController.php.html#74"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLocationApiKey">getLocationApiKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/VendorController.php.html#40"><abbr title="App\Http\Controllers\VendorController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/VendorController.php.html#54"><abbr title="App\Http\Controllers\VendorController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/VendorController.php.html#99"><abbr title="App\Http\Controllers\VendorController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/VendorController.php.html#234"><abbr title="App\Http\Controllers\VendorController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/VendorController.php.html#276"><abbr title="App\Http\Controllers\VendorController::storeVendor">storeVendor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#50"><abbr title="App\Http\Controllers\ViewController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#117"><abbr title="App\Http\Controllers\ViewController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#168"><abbr title="App\Http\Controllers\ViewController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#196"><abbr title="App\Http\Controllers\ViewController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#247"><abbr title="App\Http\Controllers\ViewController::setAsDefault">setAsDefault</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#297"><abbr title="App\Http\Controllers\ViewController::pinView">pinView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#331"><abbr title="App\Http\Controllers\ViewController::rename">rename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#355"><abbr title="App\Http\Controllers\ViewController::duplicate">duplicate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#404"><abbr title="App\Http\Controllers\ViewController::getViewConfig">getViewConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#446"><abbr title="App\Http\Controllers\ViewController::getViewCount">getViewCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#537"><abbr title="App\Http\Controllers\ViewController::workOrderViewType">workOrderViewType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookCallsController.php.html#14"><abbr title="App\Http\Controllers\WebhookCallsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookCallsController.php.html#22"><abbr title="App\Http\Controllers\WebhookCallsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#36"><abbr title="App\Http\Controllers\WebhookController::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#99"><abbr title="App\Http\Controllers\WebhookController::invalidAction">invalidAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#112"><abbr title="App\Http\Controllers\WebhookController::validateQuoteApprovePayload">validateQuoteApprovePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#151"><abbr title="App\Http\Controllers\WebhookController::validateQuoteRejectPayload">validateQuoteRejectPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#179"><abbr title="App\Http\Controllers\WebhookController::validateQuoteExpirePayload">validateQuoteExpirePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#207"><abbr title="App\Http\Controllers\WebhookController::validateQuoteUpdatePayload">validateQuoteUpdatePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#261"><abbr title="App\Http\Controllers\WebhookController::validateQuoteRestorePayload">validateQuoteRestorePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#289"><abbr title="App\Http\Controllers\WebhookController::validateQuoteSubmittedForApprovalPayload">validateQuoteSubmittedForApprovalPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrder/WorkOrderIssueController.php.html#22"><abbr title="App\Http\Controllers\WorkOrder\WorkOrderIssueController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderActivityLogController.php.html#27"><abbr title="App\Http\Controllers\WorkOrderActivityLogController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderActivityLogController.php.html#99"><abbr title="App\Http\Controllers\WorkOrderActivityLogController::paginate">paginate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderAssigneeController.php.html#25"><abbr title="App\Http\Controllers\WorkOrderAssigneeController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderAssigneeController.php.html#88"><abbr title="App\Http\Controllers\WorkOrderAssigneeController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#124"><abbr title="App\Http\Controllers\WorkOrderController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#168"><abbr title="App\Http\Controllers\WorkOrderController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#226"><abbr title="App\Http\Controllers\WorkOrderController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#274"><abbr title="App\Http\Controllers\WorkOrderController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#321"><abbr title="App\Http\Controllers\WorkOrderController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#350"><abbr title="App\Http\Controllers\WorkOrderController::getGroupView">getGroupView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#416"><abbr title="App\Http\Controllers\WorkOrderController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#566"><abbr title="App\Http\Controllers\WorkOrderController::updatePriority">updatePriority</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#608"><abbr title="App\Http\Controllers\WorkOrderController::updateDueDate">updateDueDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#633"><abbr title="App\Http\Controllers\WorkOrderController::deleteDueDate">deleteDueDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#657"><abbr title="App\Http\Controllers\WorkOrderController::updateAccessMethod">updateAccessMethod</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#697"><abbr title="App\Http\Controllers\WorkOrderController::updateResidentInfo">updateResidentInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#763"><abbr title="App\Http\Controllers\WorkOrderController::updatePropertyAddress">updatePropertyAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#831"><abbr title="App\Http\Controllers\WorkOrderController::getCount">getCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#867"><abbr title="App\Http\Controllers\WorkOrderController::handleIndexException">handleIndexException</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#893"><abbr title="App\Http\Controllers\WorkOrderController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#961"><abbr title="App\Http\Controllers\WorkOrderController::priorityBasedGroupData">priorityBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1022"><abbr title="App\Http\Controllers\WorkOrderController::categoryBasedGroupData">categoryBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1092"><abbr title="App\Http\Controllers\WorkOrderController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1153"><abbr title="App\Http\Controllers\WorkOrderController::tagBasedGroupData">tagBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1214"><abbr title="App\Http\Controllers\WorkOrderController::healthScoreBaseGroupData">healthScoreBaseGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1273"><abbr title="App\Http\Controllers\WorkOrderController::technicianBasedGroupData">technicianBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1330"><abbr title="App\Http\Controllers\WorkOrderController::tripSummary">tripSummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1382"><abbr title="App\Http\Controllers\WorkOrderController::getTripDetails">getTripDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1431"><abbr title="App\Http\Controllers\WorkOrderController::markAsComplete">markAsComplete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1545"><abbr title="App\Http\Controllers\WorkOrderController::loadWorkOrderRelationsForWeb">loadWorkOrderRelationsForWeb</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1666"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderRelations">getWorkOrderRelations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1704"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderSelectFields">getWorkOrderSelectFields</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1827"><abbr title="App\Http\Controllers\WorkOrderController::getServiceCallSelectFieldsForApp">getServiceCallSelectFieldsForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1860"><abbr title="App\Http\Controllers\WorkOrderController::getServiceCallRelationsForApp">getServiceCallRelationsForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1875"><abbr title="App\Http\Controllers\WorkOrderController::shouldListForTechnicianMobile">shouldListForTechnicianMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1885"><abbr title="App\Http\Controllers\WorkOrderController::listWorkOrdersForTechnicianMobile">listWorkOrdersForTechnicianMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1933"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderRelationsForMobile">getWorkOrderRelationsForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1981"><abbr title="App\Http\Controllers\WorkOrderController::getServiceRequestColumnForApp">getServiceRequestColumnForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#2005"><abbr title="App\Http\Controllers\WorkOrderController::getServiceRequestRelationsForApp">getServiceRequestRelationsForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#2017"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderIssuesColumnForApp">getWorkOrderIssuesColumnForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#2032"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderIssuesRelationsForApp">getWorkOrderIssuesRelationsForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#48"><abbr title="App\Http\Controllers\WorkOrderMediaController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#199"><abbr title="App\Http\Controllers\WorkOrderMediaController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#240"><abbr title="App\Http\Controllers\WorkOrderMediaController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#282"><abbr title="App\Http\Controllers\WorkOrderMediaController::uploadOriginalMedia">uploadOriginalMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#353"><abbr title="App\Http\Controllers\WorkOrderMediaController::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderNoteController.php.html#34"><abbr title="App\Http\Controllers\WorkOrderNoteController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderNoteController.php.html#74"><abbr title="App\Http\Controllers\WorkOrderNoteController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderNoteController.php.html#128"><abbr title="App\Http\Controllers\WorkOrderNoteController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/WorkOrderNoteController.php.html#186"><abbr title="App\Http\Controllers\WorkOrderNoteController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/QueryFilter.php.html#99"><abbr title="App\Http\Filters\QueryFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/QuoteListFilter.php.html#34"><abbr title="App\Http\Filters\QuoteListFilter::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/QuoteListFilter.php.html#42"><abbr title="App\Http\Filters\QuoteListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/QuoteListFilter.php.html#60"><abbr title="App\Http\Filters\QuoteListFilter::workOrderId">workOrderId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/QuoteListFilter.php.html#65"><abbr title="App\Http\Filters\QuoteListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/QuoteListFilter.php.html#95"><abbr title="App\Http\Filters\QuoteListFilter::filter">filter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/RoleListFilter.php.html#20"><abbr title="App\Http\Filters\RoleListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/RoleListFilter.php.html#28"><abbr title="App\Http\Filters\RoleListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ServiceRequestListFilter.php.html#29"><abbr title="App\Http\Filters\ServiceRequestListFilter::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ServiceRequestListFilter.php.html#34"><abbr title="App\Http\Filters\ServiceRequestListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ServiceRequestListFilter.php.html#49"><abbr title="App\Http\Filters\ServiceRequestListFilter::serviceRequestId">serviceRequestId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ServiceRequestListFilter.php.html#54"><abbr title="App\Http\Filters\ServiceRequestListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/ServiceRequestListFilter.php.html#84"><abbr title="App\Http\Filters\ServiceRequestListFilter::filter">filter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/TagListFilter.php.html#22"><abbr title="App\Http\Filters\TagListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/TagListFilter.php.html#32"><abbr title="App\Http\Filters\TagListFilter::workOrderId">workOrderId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/TagListFilter.php.html#44"><abbr title="App\Http\Filters\TagListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/UserListFilter.php.html#33"><abbr title="App\Http\Filters\UserListFilter::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/UserListFilter.php.html#41"><abbr title="App\Http\Filters\UserListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/UserListFilter.php.html#50"><abbr title="App\Http\Filters\UserListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/UserListFilter.php.html#80"><abbr title="App\Http\Filters\UserListFilter::filter">filter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/VendorListFilter.php.html#10"><abbr title="App\Http\Filters\VendorListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filters/WorkOrderListFilter.php.html#67"><abbr title="App\Http\Filters\WorkOrderListFilter::workOrderId">workOrderId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/Authenticate.php.html#13"><abbr title="App\Http\Middleware\Authenticate::redirectTo">redirectTo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/HorizonBasicAuth.php.html#15"><abbr title="App\Http\Middleware\HorizonBasicAuth::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#67"><abbr title="App\Http\Middleware\LogRequestsToDatabase::given">given</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#135"><abbr title="App\Http\Middleware\LogRequestsToDatabase::contentWithinLimits">contentWithinLimits</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#147"><abbr title="App\Http\Middleware\LogRequestsToDatabase::headers">headers</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#162"><abbr title="App\Http\Middleware\LogRequestsToDatabase::payload">payload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#175"><abbr title="App\Http\Middleware\LogRequestsToDatabase::hideParameters">hideParameters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#191"><abbr title="App\Http\Middleware\LogRequestsToDatabase::response">response</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#234"><abbr title="App\Http\Middleware\LogRequestsToDatabase::extractDataFromView">extractDataFromView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#255"><abbr title="App\Http\Middleware\LogRequestsToDatabase::shouldIgnoreUnauthenticated">shouldIgnoreUnauthenticated</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#270"><abbr title="App\Http\Middleware\LogRequestsToDatabase::inExceptArray">inExceptArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#288"><abbr title="App\Http\Middleware\LogRequestsToDatabase::shouldIgnoreRoute">shouldIgnoreRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#298"><abbr title="App\Http\Middleware\LogRequestsToDatabase::input">input</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/MakeTusMediaFileName.php.html#17"><abbr title="App\Http\Middleware\MakeTusMediaFileName::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/MakeTusMediaFileName.php.html#28"><abbr title="App\Http\Middleware\MakeTusMediaFileName::setNameInUploadMetaRequestHeader">setNameInUploadMetaRequestHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/MakeTusMediaFileName.php.html#58"><abbr title="App\Http\Middleware\MakeTusMediaFileName::fileNameResolver">fileNameResolver</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/RedirectIfAuthenticated.php.html#17"><abbr title="App\Http\Middleware\RedirectIfAuthenticated::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/TrustHosts.php.html#14"><abbr title="App\Http\Middleware\TrustHosts::hosts">hosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Auth/PublicApiAuthenticateRequest.php.html#14"><abbr title="App\Http\Requests\Auth\PublicApiAuthenticateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Auth/SearchAddressRequest.php.html#9"><abbr title="App\Http\Requests\Auth\SearchAddressRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Auth/SearchAddressRequest.php.html#19"><abbr title="App\Http\Requests\Auth\SearchAddressRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Invoice/InvoiceRequest.php.html#31"><abbr title="App\Http\Requests\Invoice\InvoiceRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Invoice/InvoiceRequest.php.html#162"><abbr title="App\Http\Requests\Invoice\InvoiceRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Issue/AssignRequest.php.html#16"><abbr title="App\Http\Requests\Issue\AssignRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Issue/CreateIssueRequest.php.html#18"><abbr title="App\Http\Requests\Issue\CreateIssueRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Issue/UnassignRequest.php.html#16"><abbr title="App\Http\Requests\Issue\UnassignRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Organization/UpdateRequest.php.html#17"><abbr title="App\Http\Requests\Organization\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Quote/ListRequest.php.html#14"><abbr title="App\Http\Requests\Quote\ListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Quote/ListRequest.php.html#34"><abbr title="App\Http\Requests\Quote\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Schedule/GetTechnicianListRequest.php.html#17"><abbr title="App\Http\Requests\Schedule\GetTechnicianListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Schedule/ReScheduleAppointmentRequest.php.html#21"><abbr title="App\Http\Requests\Schedule\ReScheduleAppointmentRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Schedule/StoreAppointmentRequest.php.html#17"><abbr title="App\Http\Requests\Schedule\StoreAppointmentRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/AccessMethodUpdateRequest.php.html#16"><abbr title="App\Http\Requests\ServiceRequest\AccessMethodUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/DescriptionUpdateRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\DescriptionUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/FilterValuesRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\FilterValuesRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/GroupViewRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\GroupViewRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ListRequest.php.html#13"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ListRequest.php.html#23"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ListRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/Media/MediaUploadRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\Media\MediaUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/Media/ThumbnailUploadRequest.php.html#15"><abbr title="App\Http\Requests\ServiceRequest\Media\ThumbnailUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/Note/CreateRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\Note\CreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/Note/UpdateRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\Note\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/PropertyAddressUpdateRequest.php.html#17"><abbr title="App\Http\Requests\ServiceRequest\PropertyAddressUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ResidentAvailabilityRequest.php.html#22"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ResidentAvailabilityRequest.php.html#59"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ResidentAvailabilityRequest.php.html#72"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ResidentAvailabilityRequest.php.html#94"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ResidentUpdateRequest.php.html#17"><abbr title="App\Http\Requests\ServiceRequest\ResidentUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreRequest.php.html#20"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreRequest.php.html#81"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreRequest.php.html#128"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreRequest.php.html#152"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreWorkOrderRequest.php.html#20"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreWorkOrderRequest.php.html#51"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreWorkOrderRequest.php.html#87"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreWorkOrderRequest.php.html#112"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreWorkOrderRequest.php.html#138"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::propertyRules">propertyRules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreWorkOrderRequest.php.html#169"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::residentRules">residentRules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/UpdateAvailabilityRequest.php.html#19"><abbr title="App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/ServiceRequest/UpdateAvailabilityRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Tag/StoreRequest.php.html#14"><abbr title="App\Http\Requests\Tag\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Tag/StoreRequest.php.html#31"><abbr title="App\Http\Requests\Tag\StoreRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Tag/UpdateRequest.php.html#14"><abbr title="App\Http\Requests\Tag\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Tag/UpdateRequest.php.html#33"><abbr title="App\Http\Requests\Tag\UpdateRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/AvailabilityDatesRequest.php.html#14"><abbr title="App\Http\Requests\Technician\AvailabilityDatesRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/BlockOutUpdateRequest.php.html#21"><abbr title="App\Http\Requests\Technician\BlockOutUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/BlockOutUpdateRequest.php.html#41"><abbr title="App\Http\Requests\Technician\BlockOutUpdateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/ScheduleWorkOrderRequest.php.html#15"><abbr title="App\Http\Requests\Technician\ScheduleWorkOrderRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/SkillsUpdateRequest.php.html#19"><abbr title="App\Http\Requests\Technician\SkillsUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/SkillsUpdateRequest.php.html#35"><abbr title="App\Http\Requests\Technician\SkillsUpdateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/StoreBlockOutRequest.php.html#21"><abbr title="App\Http\Requests\Technician\StoreBlockOutRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/StoreBlockOutRequest.php.html#41"><abbr title="App\Http\Requests\Technician\StoreBlockOutRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/WorkingHoursUpdateRequest.php.html#17"><abbr title="App\Http\Requests\Technician\WorkingHoursUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/WorkingHoursUpdateRequest.php.html#33"><abbr title="App\Http\Requests\Technician\WorkingHoursUpdateRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Technician/WorkingHoursUpdateRequest.php.html#52"><abbr title="App\Http\Requests\Technician\WorkingHoursUpdateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/FilterValuesRequest.php.html#14"><abbr title="App\Http\Requests\User\FilterValuesRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/GroupViewRequest.php.html#14"><abbr title="App\Http\Requests\User\GroupViewRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/ListRequest.php.html#14"><abbr title="App\Http\Requests\User\ListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/ListRequest.php.html#33"><abbr title="App\Http\Requests\User\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/StoreRequest.php.html#18"><abbr title="App\Http\Requests\User\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/StoreVendorUserRequest.php.html#16"><abbr title="App\Http\Requests\User\StoreVendorUserRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/UpdateRequest.php.html#24"><abbr title="App\Http\Requests\User\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/User/UpdateRequest.php.html#79"><abbr title="App\Http\Requests\User\UpdateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Vendor/StoreRequest.php.html#16"><abbr title="App\Http\Requests\Vendor\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Vendor/VendorOnboardingGenerateSignedUrlRequest.php.html#9"><abbr title="App\Http\Requests\Vendor\VendorOnboardingGenerateSignedUrlRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Vendor/VendorOnboardingGenerateSignedUrlRequest.php.html#19"><abbr title="App\Http\Requests\Vendor\VendorOnboardingGenerateSignedUrlRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Vendor/VendorOnboardingInitialRequest.php.html#11"><abbr title="App\Http\Requests\Vendor\VendorOnboardingInitialRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Vendor/VendorOnboardingInitialRequest.php.html#21"><abbr title="App\Http\Requests\Vendor\VendorOnboardingInitialRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/ConfigurationRequest.php.html#14"><abbr title="App\Http\Requests\View\ConfigurationRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/CountRequest.php.html#14"><abbr title="App\Http\Requests\View\CountRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/DuplicateRequest.php.html#19"><abbr title="App\Http\Requests\View\DuplicateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/DuplicateRequest.php.html#32"><abbr title="App\Http\Requests\View\DuplicateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/PinRequest.php.html#15"><abbr title="App\Http\Requests\View\PinRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/RenameRequest.php.html#18"><abbr title="App\Http\Requests\View\RenameRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/RenameRequest.php.html#31"><abbr title="App\Http\Requests\View\RenameRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/StoreRequest.php.html#20"><abbr title="App\Http\Requests\View\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/StoreRequest.php.html#43"><abbr title="App\Http\Requests\View\StoreRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/View/UpdateRequest.php.html#15"><abbr title="App\Http\Requests\View\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/AccessMethodUpdateRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\AccessMethodUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ApproveQuoteRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\ApproveQuoteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/BookmarkRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\BookmarkRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/CancelRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\CancelRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/CloseRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\CloseRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/CompleteRequest.php.html#19"><abbr title="App\Http\Requests\WorkOrder\CompleteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/CreateQuoteRequest.php.html#21"><abbr title="App\Http\Requests\WorkOrder\CreateQuoteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/CreateQuoteRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\CreateQuoteRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/DescriptionUpdateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\DescriptionUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/DueDateUpdateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\DueDateUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/FilterValuesRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\FilterValuesRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/GroupViewRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\GroupViewRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Media/MediaUploadRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\Media\MediaUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Media/ThumbnailUploadRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\Media\ThumbnailUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Note/CreateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\Note\CreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Note/UpdateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\Note\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/PauseRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\PauseRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/PriorityUpdateRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\PriorityUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ProblemCategoryCreateRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\ProblemCategoryCreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ProblemCategoryDeleteRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\ProblemCategoryDeleteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ProblemCategoryUpdateRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\ProblemCategoryUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/PropertyAddressUpdateRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\PropertyAddressUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Quote/TaskCreateRequest.php.html#21"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskCreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Quote/TaskCreateRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskCreateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Quote/TaskUpdateRequest.php.html#22"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/Quote/TaskUpdateRequest.php.html#52"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ReadyToInvoiceRequest.php.html#18"><abbr title="App\Http\Requests\WorkOrder\ReadyToInvoiceRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ResidentUpdateRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\ResidentUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/SendToVendorRequest.php.html#19"><abbr title="App\Http\Requests\WorkOrder\SendToVendorRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/SendToVendorRequest.php.html#40"><abbr title="App\Http\Requests\WorkOrder\SendToVendorRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/StoreRequest.php.html#18"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/StoreRequest.php.html#81"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/StoreRequest.php.html#123"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/StoreRequest.php.html#149"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/TagRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\TagRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/UpdateNteAmountRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\UpdateNteAmountRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/UpdateNteAmountRequest.php.html#31"><abbr title="App\Http\Requests\WorkOrder\UpdateNteAmountRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/UpdateTripDetailsRequest.php.html#20"><abbr title="App\Http\Requests\WorkOrder\UpdateTripDetailsRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrder/UpdateTripRequest.php.html#21"><abbr title="App\Http\Requests\WorkOrder\UpdateTripRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrderIssue/DeclineIssueRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrderIssue\DeclineIssueRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrderIssue/MarkAsDoneIssueRequest.php.html#19"><abbr title="App\Http\Requests\WorkOrderIssue\MarkAsDoneIssueRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/WorkOrderIssue/WorkOrderIssueUpdateRequest.php.html#20"><abbr title="App\Http\Requests\WorkOrderIssue\WorkOrderIssueUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/APITokenResource.php.html#15"><abbr title="App\Http\Resources\APITokenResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/AppfolioVendorResource.php.html#21"><abbr title="App\Http\Resources\AppfolioVendorResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/CountryResource.php.html#19"><abbr title="App\Http\Resources\CountryResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/FeatureResource.php.html#19"><abbr title="App\Http\Resources\FeatureResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/CreateInvoiceResource.php.html#20"><abbr title="App\Http\Resources\Invoice\CreateInvoiceResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/DeleteInvoiceResource.php.html#20"><abbr title="App\Http\Resources\Invoice\DeleteInvoiceResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceLineItemSubsidiariesResource.php.html#21"><abbr title="App\Http\Resources\Invoice\InvoiceLineItemSubsidiariesResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceLineItemsResource.php.html#21"><abbr title="App\Http\Resources\Invoice\InvoiceLineItemsResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceListResource.php.html#21"><abbr title="App\Http\Resources\Invoice\InvoiceListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceResource.php.html#19"><abbr title="App\Http\Resources\Invoice\InvoiceResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceStateResource.php.html#19"><abbr title="App\Http\Resources\Invoice\InvoiceStateResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/TripResource.php.html#23"><abbr title="App\Http\Resources\Invoice\TripResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/TripResource.php.html#70"><abbr title="App\Http\Resources\Invoice\TripResource::tripStatusLabel">tripStatusLabel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Invoice/VoidInvoiceResource.php.html#20"><abbr title="App\Http\Resources\Invoice\VoidInvoiceResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Issue/AssignIssueResource.php.html#21"><abbr title="App\Http\Resources\Issue\AssignIssueResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Issue/IssueStatusResource.php.html#20"><abbr title="App\Http\Resources\Issue\IssueStatusResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Issue/UnassignIssueResource.php.html#21"><abbr title="App\Http\Resources\Issue\UnassignIssueResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/LookupResource.php.html#15"><abbr title="App\Http\Resources\LookupResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Notification/NotificationResource.php.html#20"><abbr title="App\Http\Resources\Notification\NotificationResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Notification/NotificationResource.php.html#32"><abbr title="App\Http\Resources\Notification\NotificationResource::buildData">buildData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Notification/NotificationResource.php.html#50"><abbr title="App\Http\Resources\Notification\NotificationResource::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Notification/NotificationResource.php.html#71"><abbr title="App\Http\Resources\Notification\NotificationResource::getServiceRequestDetails">getServiceRequestDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Notification/NotificationResource.php.html#96"><abbr title="App\Http\Resources\Notification\NotificationResource::getUserData">getUserData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Notification/ReadNotificationResource.php.html#19"><abbr title="App\Http\Resources\Notification\ReadNotificationResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Organization/OrganizationResource.php.html#19"><abbr title="App\Http\Resources\Organization\OrganizationResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/PermissionResource.php.html#19"><abbr title="App\Http\Resources\PermissionResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/PriorityResource.php.html#20"><abbr title="App\Http\Resources\PriorityResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ProblemCategoryDeleteResource.php.html#19"><abbr title="App\Http\Resources\ProblemCategoryDeleteResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ProblemCategoryUpdateResource.php.html#19"><abbr title="App\Http\Resources\ProblemCategoryUpdateResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/QuantityTypeResources.php.html#15"><abbr title="App\Http\Resources\QuantityTypeResources::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/AssigneeBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\Quote\Group\AssigneeBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/CategoryBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\Quote\Group\CategoryBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/StatusBasedGroupDataResource.php.html#23"><abbr title="App\Http\Resources\Quote\Group\StatusBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/SubmittedBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\Quote\Group\SubmittedBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/TagBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\Quote\Group\TagBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/Group/WorkOrderNumberBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\Quote\Group\WorkOrderNumberBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/ListResource.php.html#25"><abbr title="App\Http\Resources\Quote\ListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/ListResource.php.html#69"><abbr title="App\Http\Resources\Quote\ListResource::getFullName">getFullName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/ListResource.php.html#74"><abbr title="App\Http\Resources\Quote\ListResource::findGroupSlug">findGroupSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/ListStatusResource.php.html#14"><abbr title="App\Http\Resources\Quote\ListStatusResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Quote/ListTaskResource.php.html#15"><abbr title="App\Http\Resources\Quote\ListTaskResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ResidentAvailability/ResidentAvailabilityShowResource.php.html#21"><abbr title="App\Http\Resources\ResidentAvailability\ResidentAvailabilityShowResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/RoleResource.php.html#21"><abbr title="App\Http\Resources\RoleResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/GetTechnicianListResource.php.html#21"><abbr title="App\Http\Resources\Scheduling\GetTechnicianListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/GetTechnicianListResource.php.html#47"><abbr title="App\Http\Resources\Scheduling\GetTechnicianListResource::toWindowArray">toWindowArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/GetTechnicianSchedulesResources.php.html#19"><abbr title="App\Http\Resources\Scheduling\GetTechnicianSchedulesResources::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/GetTechnicianSchedulesResources.php.html#32"><abbr title="App\Http\Resources\Scheduling\GetTechnicianSchedulesResources::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/GetTechnicianSchedulesResources.php.html#76"><abbr title="App\Http\Resources\Scheduling\GetTechnicianSchedulesResources::getTimeLabel">getTimeLabel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/GetVendorAvailabilityResources.php.html#15"><abbr title="App\Http\Resources\Scheduling\GetVendorAvailabilityResources::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/GetVendorAvailabilityResources.php.html#22"><abbr title="App\Http\Resources\Scheduling\GetVendorAvailabilityResources::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/ScheduleContextResource.php.html#27"><abbr title="App\Http\Resources\Scheduling\ScheduleContextResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/ScheduleContextResource.php.html#84"><abbr title="App\Http\Resources\Scheduling\ScheduleContextResource::scheduleOption">scheduleOption</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/ScheduleContextResource.php.html#110"><abbr title="App\Http\Resources\Scheduling\ScheduleContextResource::getAvailableDurations">getAvailableDurations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Scheduling/ScheduleVendorResource.php.html#19"><abbr title="App\Http\Resources\Scheduling\ScheduleVendorResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/SearchAddressResource.php.html#10"><abbr title="App\Http\Resources\SearchAddressResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ActivityLogResource.php.html#18"><abbr title="App\Http\Resources\ServiceRequest\ActivityLogResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/AssigneeResource.php.html#17"><abbr title="App\Http\Resources\ServiceRequest\AssigneeResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/AwaitingAvailabilityResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\AwaitingAvailabilityResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ClosedResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\ClosedResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/CreateWorkOrderResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\CreateWorkOrderResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/DescriptionResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\DescriptionResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Filter/AddedDateFilterResource.php.html#16"><abbr title="App\Http\Resources\ServiceRequest\Filter\AddedDateFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Filter/AssigneeFilterResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\Filter\AssigneeFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Filter/ImportedFromFilterResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\Filter\ImportedFromFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Filter/StatusFilterResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\Filter\StatusFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Group/AssigneeBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\ServiceRequest\Group\AssigneeBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Group/ImportFromBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\ServiceRequest\Group\ImportFromBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Group/PriorityBasedGroupDataResource.php.html#21"><abbr title="App\Http\Resources\ServiceRequest\Group\PriorityBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Group/StatusBasedGroupDataResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\Group\StatusBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/InProgressResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\InProgressResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ListResource.php.html#21"><abbr title="App\Http\Resources\ServiceRequest\ListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ListResource.php.html#40"><abbr title="App\Http\Resources\ServiceRequest\ListResource::findGroupSlug">findGroupSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Media/OriginalMediaResource.php.html#26"><abbr title="App\Http\Resources\ServiceRequest\Media\OriginalMediaResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Media/ThumbnailMediaResource.php.html#15"><abbr title="App\Http\Resources\ServiceRequest\Media\ThumbnailMediaResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/MediaResource.php.html#21"><abbr title="App\Http\Resources\ServiceRequest\MediaResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Note/NoteListResource.php.html#20"><abbr title="App\Http\Resources\ServiceRequest\Note\NoteListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PriorityResource.php.html#17"><abbr title="App\Http\Resources\ServiceRequest\PriorityResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ProblemCategoryResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\ProblemCategoryResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PropertyAccessInfoResource.php.html#20"><abbr title="App\Http\Resources\ServiceRequest\PropertyAccessInfoResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PropertyAddressResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\PropertyAddressResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PropertyResource.php.html#27"><abbr title="App\Http\Resources\ServiceRequest\PropertyResource::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PropertyResource.php.html#41"><abbr title="App\Http\Resources\ServiceRequest\PropertyResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ResidentResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\ResidentResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestDescriptionResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\ServiceRequestDescriptionResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestResource.php.html#30"><abbr title="App\Http\Resources\ServiceRequest\ServiceRequestResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestResource.php.html#99"><abbr title="App\Http\Resources\ServiceRequest\ServiceRequestResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestResource.php.html#106"><abbr title="App\Http\Resources\ServiceRequest\ServiceRequestResource::isLatestWorkOrderTripOfAuthenticatedTechnician">isLatestWorkOrderTripOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestSourceResource.php.html#20"><abbr title="App\Http\Resources\ServiceRequest\ServiceRequestSourceResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestTypeResource.php.html#20"><abbr title="App\Http\Resources\ServiceRequest\ServiceRequestTypeResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/StoreResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\StoreResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrder/WorkOrderIssuesResource.php.html#21"><abbr title="App\Http\Resources\ServiceRequest\WorkOrder\WorkOrderIssuesResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrder/WorkOrderStoreIssueResource.php.html#22"><abbr title="App\Http\Resources\ServiceRequest\WorkOrder\WorkOrderStoreIssueResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderResource.php.html#39"><abbr title="App\Http\Resources\ServiceRequest\WorkOrderResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderResource.php.html#92"><abbr title="App\Http\Resources\ServiceRequest\WorkOrderResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderResource.php.html#99"><abbr title="App\Http\Resources\ServiceRequest\WorkOrderResource::isLatestWorkOrderTripOfAuthenticatedTechnician">isLatestWorkOrderTripOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderResource.php.html#110"><abbr title="App\Http\Resources\ServiceRequest\WorkOrderResource::generateMobileResponse">generateMobileResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderStoreResource.php.html#22"><abbr title="App\Http\Resources\ServiceRequest\WorkOrderStoreResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Tag/TagResource.php.html#19"><abbr title="App\Http\Resources\Tag\TagResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/AvailabilityDateResource.php.html#15"><abbr title="App\Http\Resources\Technician\AvailabilityDateResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/SelectedSkillsResource.php.html#22"><abbr title="App\Http\Resources\Technician\SelectedSkillsResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianAgendaResource.php.html#26"><abbr title="App\Http\Resources\Technician\TechnicianAgendaResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianBlockOutDeleteResource.php.html#19"><abbr title="App\Http\Resources\Technician\TechnicianBlockOutDeleteResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianBlockOutResource.php.html#21"><abbr title="App\Http\Resources\Technician\TechnicianBlockOutResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianCalendarViewResource.php.html#19"><abbr title="App\Http\Resources\Technician\TechnicianCalendarViewResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/WorkOrderPropertyResource.php.html#19"><abbr title="App\Http\Resources\Technician\WorkOrderPropertyResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Technician/WorkOrderTaskResource.php.html#21"><abbr title="App\Http\Resources\Technician\WorkOrderTaskResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/TemplateResource.php.html#15"><abbr title="App\Http\Resources\TemplateResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/AccountInfoResource.php.html#21"><abbr title="App\Http\Resources\User\AccountInfoResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/Group/RoleBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\User\Group\RoleBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/Group/StatusBasedGroupDataResource.php.html#20"><abbr title="App\Http\Resources\User\Group\StatusBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/ProfileResource.php.html#20"><abbr title="App\Http\Resources\User\ProfileResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/RoleResource.php.html#20"><abbr title="App\Http\Resources\User\RoleResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/TechnicianResource.php.html#19"><abbr title="App\Http\Resources\User\TechnicianResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/UserListResource.php.html#21"><abbr title="App\Http\Resources\User\UserListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/UserListResource.php.html#43"><abbr title="App\Http\Resources\User\UserListResource::findGroupSlug">findGroupSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/UserResource.php.html#23"><abbr title="App\Http\Resources\User\UserResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/UserRoleResource.php.html#19"><abbr title="App\Http\Resources\User\UserRoleResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/User/UserStatusResource.php.html#15"><abbr title="App\Http\Resources\User\UserStatusResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Vendor/ProfileResource.php.html#22"><abbr title="App\Http\Resources\Vendor\ProfileResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Vendor/UpdateVendorResource.php.html#19"><abbr title="App\Http\Resources\Vendor\UpdateVendorResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Vendor/VendorOnboardingResource.php.html#69"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::processService">processService</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Vendor/VendorOnboardingResource.php.html#80"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::isValidDiagnosisChain">isValidDiagnosisChain</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Vendor/VendorOnboardingResource.php.html#87"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::addUniqueIds">addUniqueIds</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/Vendor/VendorOnboardingResource.php.html#98"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::addIfNotPresent">addIfNotPresent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/VendorUserResource.php.html#20"><abbr title="App\Http\Resources\VendorUserResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/ConfigurationResource.php.html#19"><abbr title="App\Http\Resources\View\ConfigurationResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/DeleteResource.php.html#19"><abbr title="App\Http\Resources\View\DeleteResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/PinResource.php.html#22"><abbr title="App\Http\Resources\View\PinResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/PinnedViewCount.php.html#15"><abbr title="App\Http\Resources\View\PinnedViewCount::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/SetDefaultResource.php.html#19"><abbr title="App\Http\Resources\View\SetDefaultResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/StoreResource.php.html#19"><abbr title="App\Http\Resources\View\StoreResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/UpdatedResource.php.html#23"><abbr title="App\Http\Resources\View\UpdatedResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/View/ViewResource.php.html#19"><abbr title="App\Http\Resources\View\ViewResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ActivityLogResource.php.html#18"><abbr title="App\Http\Resources\WorkOrder\ActivityLogResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/AppointmentRescheduledResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\AppointmentRescheduledResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/AppointmentTechnicianResource.php.html#17"><abbr title="App\Http\Resources\WorkOrder\AppointmentTechnicianResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/AssigneeListResource.php.html#17"><abbr title="App\Http\Resources\WorkOrder\AssigneeListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/AwaitingAvailabilityResources.php.html#19"><abbr title="App\Http\Resources\WorkOrder\AwaitingAvailabilityResources::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/CancelResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\CancelResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ClosedResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\ClosedResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ClosedResource.php.html#55"><abbr title="App\Http\Resources\WorkOrder\ClosedResource::getTripDetails">getTripDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/CompletedResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\CompletedResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/CreateQuoteResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\CreateQuoteResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/DueDateResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\DueDateResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/EnRouteResource.php.html#24"><abbr title="App\Http\Resources\WorkOrder\EnRouteResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/EnRouteResource.php.html#60"><abbr title="App\Http\Resources\WorkOrder\EnRouteResource::getWorkOrderStatus">getWorkOrderStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/EnRouteResource.php.html#76"><abbr title="App\Http\Resources\WorkOrder\EnRouteResource::getTripDetails">getTripDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/AssigneeFilterResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\Filter\AssigneeFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/CategoryFilterResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\Filter\CategoryFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/CreatedDateFilterResource.php.html#16"><abbr title="App\Http\Resources\WorkOrder\Filter\CreatedDateFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/HealthScoreFilterResource.php.html#16"><abbr title="App\Http\Resources\WorkOrder\Filter\HealthScoreFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/OverdueFilterResource.php.html#16"><abbr title="App\Http\Resources\WorkOrder\Filter\OverdueFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/PriorityFilterResource.php.html#16"><abbr title="App\Http\Resources\WorkOrder\Filter\PriorityFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/ProviderFilterResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\Filter\ProviderFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/ScheduledDateFilterResource.php.html#16"><abbr title="App\Http\Resources\WorkOrder\Filter\ScheduledDateFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/StatusFilterResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\Filter\StatusFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/TagFilterResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\Filter\TagFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/TechnicianFilterResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\Filter\TechnicianFilterResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/AssigneeBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\WorkOrder\Group\AssigneeBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/CategoryBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\WorkOrder\Group\CategoryBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/HealthScoreGroupDataResource.php.html#15"><abbr title="App\Http\Resources\WorkOrder\Group\HealthScoreGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/PriorityBasedGroupDataResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\Group\PriorityBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/StatusBasedGroupDataResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\Group\StatusBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/TagBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\WorkOrder\Group\TagBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/TechnicianBasedGroupDataResource.php.html#15"><abbr title="App\Http\Resources\WorkOrder\Group\TechnicianBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/HealthScore/HealthScoreResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\HealthScore\HealthScoreResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/HealthScore/HealthScoreResource.php.html#33"><abbr title="App\Http\Resources\WorkOrder\HealthScore\HealthScoreResource::elapseTime">elapseTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Invoice/FullyPaidResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\Invoice\FullyPaidResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Invoice/InvoiceResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\Invoice\InvoiceResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Invoice/PartiallyPaidResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\Invoice\PartiallyPaidResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ActiveTripResource.php.html#37"><abbr title="App\Http\Resources\WorkOrder\List\ActiveTripResource::mobileDeviceTripDetails">mobileDeviceTripDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ListTaskAppointmentResource.php.html#29"><abbr title="App\Http\Resources\WorkOrder\List\ListTaskAppointmentResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ListTaskAppointmentResource.php.html#65"><abbr title="App\Http\Resources\WorkOrder\List\ListTaskAppointmentResource::inHouserTechnicianTripData">inHouserTechnicianTripData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ListTaskAppointmentResource.php.html#99"><abbr title="App\Http\Resources\WorkOrder\List\ListTaskAppointmentResource::lulaNetWorkProTripData">lulaNetWorkProTripData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#38"><abbr title="App\Http\Resources\WorkOrder\ListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#78"><abbr title="App\Http\Resources\WorkOrder\ListResource::transformResponseForDevice">transformResponseForDevice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#101"><abbr title="App\Http\Resources\WorkOrder\ListResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#108"><abbr title="App\Http\Resources\WorkOrder\ListResource::isLatestWorkOrderTripOfAuthenticatedTechnician">isLatestWorkOrderTripOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#113"><abbr title="App\Http\Resources\WorkOrder\ListResource::getStatusForApp">getStatusForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#134"><abbr title="App\Http\Resources\WorkOrder\ListResource::findGroupSlug">findGroupSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Media/OriginalMediaResource.php.html#26"><abbr title="App\Http\Resources\WorkOrder\Media\OriginalMediaResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Media/ThumbnailMediaResource.php.html#15"><abbr title="App\Http\Resources\WorkOrder\Media\ThumbnailMediaResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/MediaResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\MediaResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Note/NoteListResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\Note\NoteListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseEnRouteResource.php.html#24"><abbr title="App\Http\Resources\WorkOrder\PauseEnRouteResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseEnRouteResource.php.html#61"><abbr title="App\Http\Resources\WorkOrder\PauseEnRouteResource::getWorkOrderStatus">getWorkOrderStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseEnRouteResource.php.html#77"><abbr title="App\Http\Resources\WorkOrder\PauseEnRouteResource::getTripDetails">getTripDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\PauseResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseWorkResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\PauseWorkResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseWorkResource.php.html#57"><abbr title="App\Http\Resources\WorkOrder\PauseWorkResource::getTripDetails">getTripDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PriorityUpdateResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\PriorityUpdateResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ProblemCategoryResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\ProblemCategoryResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PropertyAccessInfoResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\PropertyAccessInfoResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/PropertyAddressResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\PropertyAddressResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ProviderResource.php.html#23"><abbr title="App\Http\Resources\WorkOrder\ProviderResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Quote/QuoteMaterialResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\Quote\QuoteMaterialResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Quote/TaskDeleteResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\Quote\TaskDeleteResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteApproveResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\QuoteApproveResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteRejectResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\QuoteRejectResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\QuoteResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteTaskResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\QuoteTaskResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ReOpenResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\ReOpenResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ReadyToInvoiceResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\ReadyToInvoiceResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ReadyToScheduleResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\ReadyToScheduleResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResidentResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\ResidentResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResolveResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\ResolveResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeEnRouteResource.php.html#22"><abbr title="App\Http\Resources\WorkOrder\ResumeEnRouteResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeEnRouteResource.php.html#59"><abbr title="App\Http\Resources\WorkOrder\ResumeEnRouteResource::getWorkOrderStatus">getWorkOrderStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeEnRouteResource.php.html#75"><abbr title="App\Http\Resources\WorkOrder\ResumeEnRouteResource::getTripDetails">getTripDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeWorkResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\ResumeWorkResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeWorkResource.php.html#58"><abbr title="App\Http\Resources\WorkOrder\ResumeWorkResource::getTripDetails">getTripDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ServiceRequestResource.php.html#27"><abbr title="App\Http\Resources\WorkOrder\ServiceRequestResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ServiceRequestResource.php.html#55"><abbr title="App\Http\Resources\WorkOrder\ServiceRequestResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/ServiceRequestResource.php.html#62"><abbr title="App\Http\Resources\WorkOrder\ServiceRequestResource::isLatestWorkOrderTripOfAuthenticatedTechnician">isLatestWorkOrderTripOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/StartWorkResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\StartWorkResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/StartWorkResource.php.html#58"><abbr title="App\Http\Resources\WorkOrder\StartWorkResource::getTripDetails">getTripDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/StopTripResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\StopTripResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/StoreResources.php.html#19"><abbr title="App\Http\Resources\WorkOrder\StoreResources::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskAppointmentResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\TaskAppointmentResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskListResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\TaskListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskMaterialResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\TaskMaterialResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskResource.php.html#25"><abbr title="App\Http\Resources\WorkOrder\TaskResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskScheduledAppointmentResource.php.html#22"><abbr title="App\Http\Resources\WorkOrder\TaskScheduledAppointmentResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TechnicianAppointmentResource.php.html#15"><abbr title="App\Http\Resources\WorkOrder\TechnicianAppointmentResource::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TechnicianAppointmentResource.php.html#27"><abbr title="App\Http\Resources\WorkOrder\TechnicianAppointmentResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/Trip/TripDetailsResource.php.html#22"><abbr title="App\Http\Resources\WorkOrder\Trip\TripDetailsResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TripResource.php.html#31"><abbr title="App\Http\Resources\WorkOrder\TripResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TripResource.php.html#74"><abbr title="App\Http\Resources\WorkOrder\TripResource::inHouserTechnicianTripData">inHouserTechnicianTripData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TripResource.php.html#142"><abbr title="App\Http\Resources\WorkOrder\TripResource::lulaNetWorkProTripData">lulaNetWorkProTripData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/TripResource.php.html#204"><abbr title="App\Http\Resources\WorkOrder\TripResource::vendorTripData">vendorTripData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderAssigneeResource.php.html#17"><abbr title="App\Http\Resources\WorkOrder\WorkOrderAssigneeResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderDescriptionResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\WorkOrderDescriptionResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderNteResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\WorkOrderNteResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderResource.php.html#46"><abbr title="App\Http\Resources\WorkOrder\WorkOrderResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderResource.php.html#138"><abbr title="App\Http\Resources\WorkOrder\WorkOrderResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderResource.php.html#145"><abbr title="App\Http\Resources\WorkOrder\WorkOrderResource::isLatestWorkOrderTripOfAuthenticatedTechnician">isLatestWorkOrderTripOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderResource.php.html#156"><abbr title="App\Http\Resources\WorkOrder\WorkOrderResource::generateMobileResponse">generateMobileResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderResource.php.html#196"><abbr title="App\Http\Resources\WorkOrder\WorkOrderResource::hasMissingData">hasMissingData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderScheduledResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\WorkOrderScheduledResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderSendToVendorResource.php.html#17"><abbr title="App\Http\Resources\WorkOrder\WorkOrderSendToVendorResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderServiceRequestStatusResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\WorkOrderServiceRequestStatusResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderSubResource.php.html#37"><abbr title="App\Http\Resources\WorkOrder\WorkOrderSubResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderSubResource.php.html#93"><abbr title="App\Http\Resources\WorkOrder\WorkOrderSubResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderSubResource.php.html#100"><abbr title="App\Http\Resources\WorkOrder\WorkOrderSubResource::isLatestWorkOrderTripOfAuthenticatedTechnician">isLatestWorkOrderTripOfAuthenticatedTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderSubResource.php.html#111"><abbr title="App\Http\Resources\WorkOrder\WorkOrderSubResource::generateMobileResponse">generateMobileResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderTripResource.php.html#161"><abbr title="App\Http\Resources\WorkOrder\WorkOrderTripResource::issueResponse">issueResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/IssueDeclinedReasonResource.php.html#20"><abbr title="App\Http\Resources\WorkOrderIssue\IssueDeclinedReasonResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/IssueDeclinedResource.php.html#20"><abbr title="App\Http\Resources\WorkOrderIssue\IssueDeclinedResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/MaterialResource.php.html#19"><abbr title="App\Http\Resources\WorkOrderIssue\MaterialResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/PendingIssueResource.php.html#18"><abbr title="App\Http\Resources\WorkOrderIssue\PendingIssueResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/WorkOrderIssueDetailsResource.php.html#20"><abbr title="App\Http\Resources\WorkOrderIssue\WorkOrderIssueDetailsResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/WorkOrderIssueListResource.php.html#20"><abbr title="App\Http\Resources\WorkOrderIssue\WorkOrderIssueListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#85"><abbr title="App\Http\Middleware\LogRequestsToDatabase::terminate">terminate</abbr></a></td><td class="text-right">8%</td></tr>
       <tr><td><a href="Requests/WorkOrder/ListRequest.php.html#34"><abbr title="App\Http\Requests\WorkOrder\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">37%</td></tr>
       <tr><td><a href="Filters/WorkOrderListFilter.php.html#72"><abbr title="App\Http\Filters\WorkOrderListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">42%</td></tr>
       <tr><td><a href="Middleware/MinimumAppVeriosnRequiredMiddleware.php.html#19"><abbr title="App\Http\Middleware\MinimumAppVeriosnRequiredMiddleware::handle">handle</abbr></a></td><td class="text-right">43%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#1041"><abbr title="App\Http\Controllers\Auth\RegisterController::processPasswordReset">processPasswordReset</abbr></a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="Middleware/AppAccessPermissionMiddleware.php.html#17"><abbr title="App\Http\Middleware\AppAccessPermissionMiddleware::handle">handle</abbr></a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ActiveTripResource.php.html#99"><abbr title="App\Http\Resources\WorkOrder\List\ActiveTripResource::getAppointmentDetails">getAppointmentDetails</abbr></a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderTripResource.php.html#188"><abbr title="App\Http\Resources\WorkOrder\WorkOrderTripResource::getAppointmentDetails">getAppointmentDetails</abbr></a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="Controllers/Vendor/VendorOnboardingController.php.html#535"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::processServices">processServices</abbr></a></td><td class="text-right">55%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#960"><abbr title="App\Http\Controllers\Auth\RegisterController::sendEmail">sendEmail</abbr></a></td><td class="text-right">57%</td></tr>
       <tr><td><a href="Controllers/Vendor/WorkOrderController.php.html#220"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getSkippedMediaTypes">getSkippedMediaTypes</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="Middleware/ValidateCognitoToken.php.html#27"><abbr title="App\Http\Middleware\ValidateCognitoToken::handle">handle</abbr></a></td><td class="text-right">62%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderTripResource.php.html#24"><abbr title="App\Http\Resources\WorkOrder\WorkOrderTripResource::toArray">toArray</abbr></a></td><td class="text-right">63%</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderStatusResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\WorkOrderStatusResource::toArray">toArray</abbr></a></td><td class="text-right">63%</td></tr>
       <tr><td><a href="Resources/ResidentAvailability/ResidentAvailabilityResource.php.html#20"><abbr title="App\Http\Resources\ResidentAvailability\ResidentAvailabilityResource::toArray">toArray</abbr></a></td><td class="text-right">64%</td></tr>
       <tr><td><a href="Controllers/Vendor/WorkOrderController.php.html#77"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::show">show</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="Requests/Vendor/ListRequest.php.html#34"><abbr title="App\Http\Requests\Vendor\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#145"><abbr title="App\Http\Controllers\Auth\RegisterController::verify">verify</abbr></a></td><td class="text-right">70%</td></tr>
       <tr><td><a href="Controllers/Vendor/VendorOnboardingLookupController.php.html#25"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLookups">getLookups</abbr></a></td><td class="text-right">75%</td></tr>
       <tr><td><a href="Resources/Vendor/VendorOnboardingResource.php.html#50"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::getServiceOffered">getServiceOffered</abbr></a></td><td class="text-right">80%</td></tr>
       <tr><td><a href="Resources/Vendor/WorkOrderResource.php.html#146"><abbr title="App\Http\Resources\Vendor\WorkOrderResource::hasMissingData">hasMissingData</abbr></a></td><td class="text-right">80%</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ActiveTripResource.php.html#128"><abbr title="App\Http\Resources\WorkOrder\List\ActiveTripResource::getFormattedScheduleDate">getFormattedScheduleDate</abbr></a></td><td class="text-right">80%</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/WorkOrderIssueStatusResource.php.html#20"><abbr title="App\Http\Resources\WorkOrderIssue\WorkOrderIssueStatusResource::toArray">toArray</abbr></a></td><td class="text-right">85%</td></tr>
       <tr><td><a href="Resources/Issue/IssueResource.php.html#22"><abbr title="App\Http\Resources\Issue\IssueResource::toArray">toArray</abbr></a></td><td class="text-right">86%</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#87"><abbr title="App\Http\Controllers\Auth\RegisterController::forgotPassword">forgotPassword</abbr></a></td><td class="text-right">87%</td></tr>
       <tr><td><a href="Controllers/Vendor/WorkOrderController.php.html#35"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::index">index</abbr></a></td><td class="text-right">88%</td></tr>
       <tr><td><a href="Filters/QueryFilter.php.html#84"><abbr title="App\Http\Filters\QueryFilter::sort">sort</abbr></a></td><td class="text-right">88%</td></tr>
       <tr><td><a href="Resources/Vendor/WorkOrderResource.php.html#42"><abbr title="App\Http\Resources\Vendor\WorkOrderResource::toArray">toArray</abbr></a></td><td class="text-right">89%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Requests/Invoice/InvoiceRequest.php.html#162"><abbr title="App\Http\Requests\Invoice\InvoiceRequest::after">after</abbr></a></td><td class="text-right">2652</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskResource.php.html#25"><abbr title="App\Http\Resources\WorkOrder\TaskResource::toArray">toArray</abbr></a></td><td class="text-right">870</td></tr>
       <tr><td><a href="Controllers/PDFController.php.html#193"><abbr title="App\Http\Controllers\PDFController::viewInvoice">viewInvoice</abbr></a></td><td class="text-right">756</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#416"><abbr title="App\Http\Controllers\WorkOrderController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#48"><abbr title="App\Http\Controllers\WorkOrderMediaController::store">store</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="Controllers/ServiceRequestMediaController.php.html#44"><abbr title="App\Http\Controllers\ServiceRequestMediaController::store">store</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#878"><abbr title="App\Http\Controllers\ServiceRequestController::createWorkOrder">createWorkOrder</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Controllers/TechnicianController.php.html#130"><abbr title="App\Http\Controllers\TechnicianController::updateTechnicianSkills">updateTechnicianSkills</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#350"><abbr title="App\Http\Controllers\WorkOrderController::getGroupView">getGroupView</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#995"><abbr title="App\Http\Controllers\ServiceRequestController::markAsComplete">markAsComplete</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1431"><abbr title="App\Http\Controllers\WorkOrderController::markAsComplete">markAsComplete</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#134"><abbr title="App\Http\Resources\WorkOrder\ListResource::findGroupSlug">findGroupSlug</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#339"><abbr title="App\Http\Controllers\QuoteController::getGroupView">getGroupView</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Requests/WorkOrder/CreateQuoteRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\CreateQuoteRequest::after">after</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderResource.php.html#46"><abbr title="App\Http\Resources\WorkOrder\WorkOrderResource::toArray">toArray</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#250"><abbr title="App\Http\Controllers\QuoteController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#580"><abbr title="App\Http\Controllers\UserController::updateUserStatus">updateUserStatus</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#191"><abbr title="App\Http\Middleware\LogRequestsToDatabase::response">response</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Requests/Technician/WorkingHoursUpdateRequest.php.html#52"><abbr title="App\Http\Requests\Technician\WorkingHoursUpdateRequest::after">after</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Requests/WorkOrder/Quote/TaskCreateRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskCreateRequest::after">after</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Requests/WorkOrder/Quote/TaskUpdateRequest.php.html#52"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest::after">after</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Resources/Quote/ListResource.php.html#74"><abbr title="App\Http\Resources\Quote\ListResource::findGroupSlug">findGroupSlug</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Controllers/ServiceRequestMediaController.php.html#258"><abbr title="App\Http\Controllers\ServiceRequestMediaController::uploadOriginalMedia">uploadOriginalMedia</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#446"><abbr title="App\Http\Controllers\ViewController::getViewCount">getViewCount</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#893"><abbr title="App\Http\Controllers\WorkOrderController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#282"><abbr title="App\Http\Controllers\WorkOrderMediaController::uploadOriginalMedia">uploadOriginalMedia</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Resources/WorkOrder/TripResource.php.html#142"><abbr title="App\Http\Resources\WorkOrder\TripResource::lulaNetWorkProTripData">lulaNetWorkProTripData</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#726"><abbr title="App\Http\Controllers\Auth\RegisterController::authenticateClientCredential">authenticateClientCredential</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/LulaWebhookController.php.html#34"><abbr title="App\Http\Controllers\LulaWebhookController::__invoke">__invoke</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/OrganizationController.php.html#32"><abbr title="App\Http\Controllers\OrganizationController::update">update</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/ResidentAvailabilityController.php.html#21"><abbr title="App\Http\Controllers\ResidentAvailabilityController::store">store</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#661"><abbr title="App\Http\Controllers\ServiceRequestController::getGroupView">getGroupView</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#710"><abbr title="App\Http\Controllers\ServiceRequestController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#142"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::store">store</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#281"><abbr title="App\Http\Controllers\UserController::update">update</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/VendorController.php.html#99"><abbr title="App\Http\Controllers\VendorController::store">store</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#136"><abbr title="App\Http\Controllers\RoleController::update">update</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#129"><abbr title="App\Http\Controllers\UserController::store">store</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#700"><abbr title="App\Http\Controllers\UserController::getGroupView">getGroupView</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#961"><abbr title="App\Http\Controllers\WorkOrderController::priorityBasedGroupData">priorityBasedGroupData</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Resources/WorkOrder/TripResource.php.html#74"><abbr title="App\Http\Resources\WorkOrder\TripResource::inHouserTechnicianTripData">inHouserTechnicianTripData</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Controllers/AppVersionController.php.html#19"><abbr title="App\Http\Controllers\AppVersionController::checkLatestAppVersion">checkLatestAppVersion</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#402"><abbr title="App\Http\Controllers\Auth\RegisterController::signup">signup</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#60"><abbr title="App\Http\Controllers\QuoteController::index">index</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/ServiceRequestAssigneeController.php.html#25"><abbr title="App\Http\Controllers\ServiceRequestAssigneeController::store">store</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#200"><abbr title="App\Http\Controllers\ServiceRequestController::store">store</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#225"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::update">update</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#492"><abbr title="App\Http\Controllers\UserController::updateCognitoUserDetails">updateCognitoUserDetails</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/WorkOrderAssigneeController.php.html#25"><abbr title="App\Http\Controllers\WorkOrderAssigneeController::store">store</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#168"><abbr title="App\Http\Controllers\WorkOrderController::store">store</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1092"><abbr title="App\Http\Controllers\WorkOrderController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1153"><abbr title="App\Http\Controllers\WorkOrderController::tagBasedGroupData">tagBasedGroupData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1214"><abbr title="App\Http\Controllers\WorkOrderController::healthScoreBaseGroupData">healthScoreBaseGroupData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1273"><abbr title="App\Http\Controllers\WorkOrderController::technicianBasedGroupData">technicianBasedGroupData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Resources/View/ViewResource.php.html#19"><abbr title="App\Http\Resources\View\ViewResource::toArray">toArray</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderSubResource.php.html#37"><abbr title="App\Http\Resources\WorkOrder\WorkOrderSubResource::toArray">toArray</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#276"><abbr title="App\Http\Controllers\LookupController::expectedDurations">expectedDurations</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/MediaController.php.html#18"><abbr title="App\Http\Controllers\MediaController::__invoke">__invoke</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/PDFController.php.html#38"><abbr title="App\Http\Controllers\PDFController::viewQuote">viewQuote</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/ServiceRequestActivityLogController.php.html#29"><abbr title="App\Http\Controllers\ServiceRequestActivityLogController::index">index</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#98"><abbr title="App\Http\Controllers\ServiceRequestController::index">index</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#259"><abbr title="App\Http\Controllers\ServiceRequestController::show">show</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#1203"><abbr title="App\Http\Controllers\ServiceRequestController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/ServiceRequestNoteController.php.html#38"><abbr title="App\Http\Controllers\ServiceRequestNoteController::index">index</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/TechnicianController.php.html#31"><abbr title="App\Http\Controllers\TechnicianController::updateWorkingHours">updateWorkingHours</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#395"><abbr title="App\Http\Controllers\UserController::destroy">destroy</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#763"><abbr title="App\Http\Controllers\WorkOrderController::updatePropertyAddress">updatePropertyAddress</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1022"><abbr title="App\Http\Controllers\WorkOrderController::categoryBasedGroupData">categoryBasedGroupData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#353"><abbr title="App\Http\Controllers\WorkOrderMediaController::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ResidentAvailabilityRequest.php.html#94"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Requests/Technician/BlockOutUpdateRequest.php.html#41"><abbr title="App\Http\Requests\Technician\BlockOutUpdateRequest::after">after</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Requests/Technician/SkillsUpdateRequest.php.html#35"><abbr title="App\Http\Requests\Technician\SkillsUpdateRequest::after">after</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ActiveTripResource.php.html#37"><abbr title="App\Http\Resources\WorkOrder\List\ActiveTripResource::mobileDeviceTripDetails">mobileDeviceTripDetails</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\QuoteResource::toArray">toArray</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Filters/WorkOrderListFilter.php.html#72"><abbr title="App\Http\Filters\WorkOrderListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">52</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#85"><abbr title="App\Http\Middleware\LogRequestsToDatabase::terminate">terminate</abbr></a></td><td class="text-right">44</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#85"><abbr title="App\Http\Controllers\LookupController::getFieldAppFilters">getFieldAppFilters</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/PublicAccessController.php.html#19"><abbr title="App\Http\Controllers\PublicAccessController::getMedia">getMedia</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#51"><abbr title="App\Http\Controllers\RoleController::store">store</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#80"><abbr title="App\Http\Controllers\SchedulingController::getContext">getContext</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#595"><abbr title="App\Http\Controllers\ServiceRequestController::update">update</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#777"><abbr title="App\Http\Controllers\ServiceRequestController::updateDescription">updateDescription</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#1156"><abbr title="App\Http\Controllers\ServiceRequestController::importedFromBasedGroupData">importedFromBasedGroupData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/ServiceRequestMediaController.php.html#177"><abbr title="App\Http\Controllers\ServiceRequestMediaController::show">show</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#661"><abbr title="App\Http\Controllers\UserController::resetUserPassword">resetUserPassword</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#801"><abbr title="App\Http\Controllers\UserController::roleBasedGroupData">roleBasedGroupData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#50"><abbr title="App\Http\Controllers\ViewController::index">index</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#404"><abbr title="App\Http\Controllers\ViewController::getViewConfig">getViewConfig</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/WorkOrder/WorkOrderIssueController.php.html#22"><abbr title="App\Http\Controllers\WorkOrder\WorkOrderIssueController::show">show</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/WorkOrderActivityLogController.php.html#27"><abbr title="App\Http\Controllers\WorkOrderActivityLogController::index">index</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#226"><abbr title="App\Http\Controllers\WorkOrderController::show">show</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#274"><abbr title="App\Http\Controllers\WorkOrderController::update">update</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#697"><abbr title="App\Http\Controllers\WorkOrderController::updateResidentInfo">updateResidentInfo</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#199"><abbr title="App\Http\Controllers\WorkOrderMediaController::show">show</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/WorkOrderNoteController.php.html#128"><abbr title="App\Http\Controllers\WorkOrderNoteController::update">update</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/WorkOrderNoteController.php.html#186"><abbr title="App\Http\Controllers\WorkOrderNoteController::destroy">destroy</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Middleware/HorizonBasicAuth.php.html#15"><abbr title="App\Http\Middleware\HorizonBasicAuth::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Middleware/MakeTusMediaFileName.php.html#58"><abbr title="App\Http\Middleware\MakeTusMediaFileName::fileNameResolver">fileNameResolver</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Requests/Quote/ListRequest.php.html#34"><abbr title="App\Http\Requests\Quote\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ListRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Requests/User/ListRequest.php.html#33"><abbr title="App\Http\Requests\User\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Requests/WorkOrder/SendToVendorRequest.php.html#40"><abbr title="App\Http\Requests\WorkOrder\SendToVendorRequest::after">after</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resources/Invoice/TripResource.php.html#23"><abbr title="App\Http\Resources\Invoice\TripResource::toArray">toArray</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestResource.php.html#30"><abbr title="App\Http\Resources\ServiceRequest\ServiceRequestResource::toArray">toArray</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderResource.php.html#39"><abbr title="App\Http\Resources\ServiceRequest\WorkOrderResource::toArray">toArray</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resources/WorkOrder/CreateQuoteResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\CreateQuoteResource::toArray">toArray</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#78"><abbr title="App\Http\Resources\WorkOrder\ListResource::transformResponseForDevice">transformResponseForDevice</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#536"><abbr title="App\Http\Controllers\Auth\RegisterController::setupUserPool">setupUserPool</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#642"><abbr title="App\Http\Controllers\Auth\RegisterController::findLoginConfig">findLoginConfig</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/Auth/SSOController.php.html#76"><abbr title="App\Http\Controllers\Auth\SSOController::getCognitoUserDetails">getCognitoUserDetails</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/PublicAccessController.php.html#82"><abbr title="App\Http\Controllers\PublicAccessController::getSrMedia">getSrMedia</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/ServiceRequestAssigneeController.php.html#78"><abbr title="App\Http\Controllers\ServiceRequestAssigneeController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#1113"><abbr title="App\Http\Controllers\ServiceRequestController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/ServiceRequestMediaController.php.html#218"><abbr title="App\Http\Controllers\ServiceRequestMediaController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/TagController.php.html#143"><abbr title="App\Http\Controllers\TagController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#44"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::index">index</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#450"><abbr title="App\Http\Controllers\UserController::filters">filters</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#748"><abbr title="App\Http\Controllers\UserController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#117"><abbr title="App\Http\Controllers\ViewController::store">store</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#196"><abbr title="App\Http\Controllers\ViewController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#247"><abbr title="App\Http\Controllers\ViewController::setAsDefault">setAsDefault</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#355"><abbr title="App\Http\Controllers\ViewController::duplicate">duplicate</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#36"><abbr title="App\Http\Controllers\WebhookController::__invoke">__invoke</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#261"><abbr title="App\Http\Controllers\WebhookController::validateQuoteRestorePayload">validateQuoteRestorePayload</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/WorkOrderAssigneeController.php.html#88"><abbr title="App\Http\Controllers\WorkOrderAssigneeController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/WorkOrderMediaController.php.html#240"><abbr title="App\Http\Controllers\WorkOrderMediaController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/WorkOrderNoteController.php.html#34"><abbr title="App\Http\Controllers\WorkOrderNoteController::index">index</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Controllers/WorkOrderNoteController.php.html#74"><abbr title="App\Http\Controllers\WorkOrderNoteController::store">store</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Filters/ServiceRequestListFilter.php.html#54"><abbr title="App\Http\Filters\ServiceRequestListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#270"><abbr title="App\Http\Middleware\LogRequestsToDatabase::inExceptArray">inExceptArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Middleware/MakeTusMediaFileName.php.html#28"><abbr title="App\Http\Middleware\MakeTusMediaFileName::setNameInUploadMetaRequestHeader">setNameInUploadMetaRequestHeader</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/Invoice/TripResource.php.html#70"><abbr title="App\Http\Resources\Invoice\TripResource::tripStatusLabel">tripStatusLabel</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/Quote/ListResource.php.html#25"><abbr title="App\Http\Resources\Quote\ListResource::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/Scheduling/ScheduleContextResource.php.html#27"><abbr title="App\Http\Resources\Scheduling\ScheduleContextResource::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Media/OriginalMediaResource.php.html#26"><abbr title="App\Http\Resources\ServiceRequest\Media\OriginalMediaResource::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/ServiceRequest/MediaResource.php.html#21"><abbr title="App\Http\Resources\ServiceRequest\MediaResource::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PropertyResource.php.html#41"><abbr title="App\Http\Resources\ServiceRequest\PropertyResource::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianAgendaResource.php.html#26"><abbr title="App\Http\Resources\Technician\TechnicianAgendaResource::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianBlockOutResource.php.html#21"><abbr title="App\Http\Resources\Technician\TechnicianBlockOutResource::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/AppointmentRescheduledResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\AppointmentRescheduledResource::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#38"><abbr title="App\Http\Resources\WorkOrder\ListResource::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/Media/OriginalMediaResource.php.html#26"><abbr title="App\Http\Resources\WorkOrder\Media\OriginalMediaResource::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/MediaResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\MediaResource::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/TripResource.php.html#204"><abbr title="App\Http\Resources\WorkOrder\TripResource::vendorTripData">vendorTripData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderResource.php.html#156"><abbr title="App\Http\Resources\WorkOrder\WorkOrderResource::generateMobileResponse">generateMobileResponse</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderSubResource.php.html#111"><abbr title="App\Http\Resources\WorkOrder\WorkOrderSubResource::generateMobileResponse">generateMobileResponse</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderTripResource.php.html#24"><abbr title="App\Http\Resources\WorkOrder\WorkOrderTripResource::toArray">toArray</abbr></a></td><td class="text-right">23</td></tr>
       <tr><td><a href="Controllers/Auth/SSOController.php.html#26"><abbr title="App\Http\Controllers\Auth\SSOController::validateUser">validateUser</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/DatabaseNotificationController.php.html#18"><abbr title="App\Http\Controllers\DatabaseNotificationController::index">index</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/DatabaseNotificationController.php.html#60"><abbr title="App\Http\Controllers\DatabaseNotificationController::clearedNotifications">clearedNotifications</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/Developer/HealthCheckController.php.html#38"><abbr title="App\Http\Controllers\Developer\HealthCheckController::utilitiesCheck">utilitiesCheck</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/InvoiceController.php.html#41"><abbr title="App\Http\Controllers\InvoiceController::show">show</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/InvoiceController.php.html#92"><abbr title="App\Http\Controllers\InvoiceController::workOrderInvoiceSummary">workOrderInvoiceSummary</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/LookupController.php.html#44"><abbr title="App\Http\Controllers\LookupController::__invoke">__invoke</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#603"><abbr title="App\Http\Controllers\QuoteController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#678"><abbr title="App\Http\Controllers\QuoteController::submittedBasedGroupData">submittedBasedGroupData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#751"><abbr title="App\Http\Controllers\QuoteController::tagBasedGroupData">tagBasedGroupData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/ResidentAvailabilityController.php.html#101"><abbr title="App\Http\Controllers\ResidentAvailabilityController::show">show</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#126"><abbr title="App\Http\Controllers\SchedulingController::getVendors">getVendors</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#1252"><abbr title="App\Http\Controllers\ServiceRequestController::needToCreateServiceRequestDescription">needToCreateServiceRequestDescription</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/ServiceRequestNoteController.php.html#87"><abbr title="App\Http\Controllers\ServiceRequestNoteController::store">store</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/TagController.php.html#48"><abbr title="App\Http\Controllers\TagController::store">store</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/TagController.php.html#103"><abbr title="App\Http\Controllers\TagController::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/TechnicianController.php.html#97"><abbr title="App\Http\Controllers\TechnicianController::importWorkingHours">importWorkingHours</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/TechnicianController.php.html#272"><abbr title="App\Http\Controllers\TechnicianController::importTechnicianSkills">importTechnicianSkills</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#84"><abbr title="App\Http\Controllers\UserController::index">index</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/VendorController.php.html#54"><abbr title="App\Http\Controllers\VendorController::index">index</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/VendorController.php.html#234"><abbr title="App\Http\Controllers\VendorController::show">show</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#112"><abbr title="App\Http\Controllers\WebhookController::validateQuoteApprovePayload">validateQuoteApprovePayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#151"><abbr title="App\Http\Controllers\WebhookController::validateQuoteRejectPayload">validateQuoteRejectPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#179"><abbr title="App\Http\Controllers\WebhookController::validateQuoteExpirePayload">validateQuoteExpirePayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#207"><abbr title="App\Http\Controllers\WebhookController::validateQuoteUpdatePayload">validateQuoteUpdatePayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/WebhookController.php.html#289"><abbr title="App\Http\Controllers\WebhookController::validateQuoteSubmittedForApprovalPayload">validateQuoteSubmittedForApprovalPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#124"><abbr title="App\Http\Controllers\WorkOrderController::index">index</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#657"><abbr title="App\Http\Controllers\WorkOrderController::updateAccessMethod">updateAccessMethod</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#831"><abbr title="App\Http\Controllers\WorkOrderController::getCount">getCount</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1330"><abbr title="App\Http\Controllers\WorkOrderController::tripSummary">tripSummary</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Filters/QuoteListFilter.php.html#65"><abbr title="App\Http\Filters\QuoteListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Filters/UserListFilter.php.html#50"><abbr title="App\Http\Filters\UserListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#255"><abbr title="App\Http\Middleware\LogRequestsToDatabase::shouldIgnoreUnauthenticated">shouldIgnoreUnauthenticated</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Middleware/RedirectIfAuthenticated.php.html#17"><abbr title="App\Http\Middleware\RedirectIfAuthenticated::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ResidentAvailabilityRequest.php.html#22"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::rules">rules</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreRequest.php.html#152"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Requests/Technician/StoreBlockOutRequest.php.html#41"><abbr title="App\Http\Requests\Technician\StoreBlockOutRequest::after">after</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Requests/User/UpdateRequest.php.html#79"><abbr title="App\Http\Requests\User\UpdateRequest::after">after</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/AppfolioVendorResource.php.html#21"><abbr title="App\Http\Resources\AppfolioVendorResource::toArray">toArray</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/Scheduling/GetTechnicianSchedulesResources.php.html#76"><abbr title="App\Http\Resources\Scheduling\GetTechnicianSchedulesResources::getTimeLabel">getTimeLabel</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Note/NoteListResource.php.html#20"><abbr title="App\Http\Resources\ServiceRequest\Note\NoteListResource::toArray">toArray</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/Technician/TechnicianCalendarViewResource.php.html#19"><abbr title="App\Http\Resources\Technician\TechnicianCalendarViewResource::toArray">toArray</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/User/UserResource.php.html#23"><abbr title="App\Http\Resources\User\UserResource::toArray">toArray</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/View/UpdatedResource.php.html#23"><abbr title="App\Http\Resources\View\UpdatedResource::toArray">toArray</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/WorkOrder/Note/NoteListResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\Note\NoteListResource::toArray">toArray</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/WorkOrder/ProviderResource.php.html#23"><abbr title="App\Http\Resources\WorkOrder\ProviderResource::toArray">toArray</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/WorkOrder/TripResource.php.html#31"><abbr title="App\Http\Resources\WorkOrder\TripResource::toArray">toArray</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderTripResource.php.html#161"><abbr title="App\Http\Resources\WorkOrder\WorkOrderTripResource::issueResponse">issueResponse</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Middleware/ValidateCognitoToken.php.html#27"><abbr title="App\Http\Middleware\ValidateCognitoToken::handle">handle</abbr></a></td><td class="text-right">19</td></tr>
       <tr><td><a href="Requests/WorkOrder/ListRequest.php.html#34"><abbr title="App\Http\Requests\WorkOrder\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">14</td></tr>
       <tr><td><a href="Resources/Vendor/WorkOrderResource.php.html#42"><abbr title="App\Http\Resources\Vendor\WorkOrderResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#167"><abbr title="App\Http\Controllers\Auth\RegisterController::searchAddress">searchAddress</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#510"><abbr title="App\Http\Controllers\Auth\RegisterController::createOrganization">createOrganization</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/Developer/HealthCheckController.php.html#67"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkDatabaseConnection">checkDatabaseConnection</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/Developer/HealthCheckController.php.html#88"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkCacheConnection">checkCacheConnection</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/Developer/HealthCheckController.php.html#110"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkLogging">checkLogging</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#525"><abbr title="App\Http\Controllers\QuoteController::categoryBasedGroupData">categoryBasedGroupData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#176"><abbr title="App\Http\Controllers\SchedulingController::getTechnicianList">getTechnicianList</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#204"><abbr title="App\Http\Controllers\SchedulingController::getTechnicianSchedules">getTechnicianSchedules</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/SchedulingController.php.html#234"><abbr title="App\Http\Controllers\SchedulingController::getVendorAvailability">getVendorAvailability</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/ServiceRequestActivityLogController.php.html#132"><abbr title="App\Http\Controllers\ServiceRequestActivityLogController::paginate">paginate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#986"><abbr title="App\Http\Controllers\ServiceRequestController::triggerIssueUpdatedEvents">triggerIssueUpdatedEvents</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/ServiceRequestNoteController.php.html#139"><abbr title="App\Http\Controllers\ServiceRequestNoteController::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/ServiceRequestNoteController.php.html#187"><abbr title="App\Http\Controllers\ServiceRequestNoteController::destroy">destroy</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#318"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::viewTechnicianCalendar">viewTechnicianCalendar</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#387"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::getAvailabilityDates">getAvailabilityDates</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/VendorController.php.html#276"><abbr title="App\Http\Controllers\VendorController::storeVendor">storeVendor</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#168"><abbr title="App\Http\Controllers\ViewController::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#297"><abbr title="App\Http\Controllers\ViewController::pinView">pinView</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/ViewController.php.html#331"><abbr title="App\Http\Controllers\ViewController::rename">rename</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/WorkOrderActivityLogController.php.html#99"><abbr title="App\Http\Controllers\WorkOrderActivityLogController::paginate">paginate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#321"><abbr title="App\Http\Controllers\WorkOrderController::destroy">destroy</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#566"><abbr title="App\Http\Controllers\WorkOrderController::updatePriority">updatePriority</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#175"><abbr title="App\Http\Middleware\LogRequestsToDatabase::hideParameters">hideParameters</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#234"><abbr title="App\Http\Middleware\LogRequestsToDatabase::extractDataFromView">extractDataFromView</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Requests/ServiceRequest/StoreWorkOrderRequest.php.html#112"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Requests/View/DuplicateRequest.php.html#32"><abbr title="App\Http\Requests\View\DuplicateRequest::after">after</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Requests/View/RenameRequest.php.html#31"><abbr title="App\Http\Requests\View\RenameRequest::after">after</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Requests/View/StoreRequest.php.html#43"><abbr title="App\Http\Requests\View\StoreRequest::after">after</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Requests/WorkOrder/CompleteRequest.php.html#19"><abbr title="App\Http\Requests\WorkOrder\CompleteRequest::rules">rules</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Requests/WorkOrder/StoreRequest.php.html#149"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/Invoice/CreateInvoiceResource.php.html#20"><abbr title="App\Http\Resources\Invoice\CreateInvoiceResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/PriorityResource.php.html#20"><abbr title="App\Http\Resources\PriorityResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/Scheduling/GetVendorAvailabilityResources.php.html#22"><abbr title="App\Http\Resources\Scheduling\GetVendorAvailabilityResources::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/Scheduling/ScheduleContextResource.php.html#84"><abbr title="App\Http\Resources\Scheduling\ScheduleContextResource::scheduleOption">scheduleOption</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Filter/AddedDateFilterResource.php.html#16"><abbr title="App\Http\Resources\ServiceRequest\Filter\AddedDateFilterResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ListResource.php.html#40"><abbr title="App\Http\Resources\ServiceRequest\ListResource::findGroupSlug">findGroupSlug</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PropertyAccessInfoResource.php.html#20"><abbr title="App\Http\Resources\ServiceRequest\PropertyAccessInfoResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestResource.php.html#99"><abbr title="App\Http\Resources\ServiceRequest\ServiceRequestResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderResource.php.html#92"><abbr title="App\Http\Resources\ServiceRequest\WorkOrderResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderResource.php.html#110"><abbr title="App\Http\Resources\ServiceRequest\WorkOrderResource::generateMobileResponse">generateMobileResponse</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/ServiceRequest/WorkOrderStoreResource.php.html#22"><abbr title="App\Http\Resources\ServiceRequest\WorkOrderStoreResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/Technician/WorkOrderPropertyResource.php.html#19"><abbr title="App\Http\Resources\Technician\WorkOrderPropertyResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/Technician/WorkOrderTaskResource.php.html#21"><abbr title="App\Http\Resources\Technician\WorkOrderTaskResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/User/AccountInfoResource.php.html#21"><abbr title="App\Http\Resources\User\AccountInfoResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/User/UserListResource.php.html#21"><abbr title="App\Http\Resources\User\UserListResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/Vendor/VendorOnboardingResource.php.html#80"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::isValidDiagnosisChain">isValidDiagnosisChain</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/ClosedResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\ClosedResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/EnRouteResource.php.html#24"><abbr title="App\Http\Resources\WorkOrder\EnRouteResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/CreatedDateFilterResource.php.html#16"><abbr title="App\Http\Resources\WorkOrder\Filter\CreatedDateFilterResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/OverdueFilterResource.php.html#16"><abbr title="App\Http\Resources\WorkOrder\Filter\OverdueFilterResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/PriorityFilterResource.php.html#16"><abbr title="App\Http\Resources\WorkOrder\Filter\PriorityFilterResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/ScheduledDateFilterResource.php.html#16"><abbr title="App\Http\Resources\WorkOrder\Filter\ScheduledDateFilterResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/HealthScore/HealthScoreResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\HealthScore\HealthScoreResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ListTaskAppointmentResource.php.html#29"><abbr title="App\Http\Resources\WorkOrder\List\ListTaskAppointmentResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ListTaskAppointmentResource.php.html#65"><abbr title="App\Http\Resources\WorkOrder\List\ListTaskAppointmentResource::inHouserTechnicianTripData">inHouserTechnicianTripData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#101"><abbr title="App\Http\Resources\WorkOrder\ListResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/ListResource.php.html#113"><abbr title="App\Http\Resources\WorkOrder\ListResource::getStatusForApp">getStatusForApp</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseEnRouteResource.php.html#24"><abbr title="App\Http\Resources\WorkOrder\PauseEnRouteResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseWorkResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\PauseWorkResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/PropertyAccessInfoResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\PropertyAccessInfoResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeEnRouteResource.php.html#22"><abbr title="App\Http\Resources\WorkOrder\ResumeEnRouteResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeWorkResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\ResumeWorkResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/ServiceRequestResource.php.html#55"><abbr title="App\Http\Resources\WorkOrder\ServiceRequestResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/StartWorkResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\StartWorkResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskAppointmentResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\TaskAppointmentResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/TechnicianAppointmentResource.php.html#27"><abbr title="App\Http\Resources\WorkOrder\TechnicianAppointmentResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/Trip/TripDetailsResource.php.html#22"><abbr title="App\Http\Resources\WorkOrder\Trip\TripDetailsResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderResource.php.html#138"><abbr title="App\Http\Resources\WorkOrder\WorkOrderResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderResource.php.html#196"><abbr title="App\Http\Resources\WorkOrder\WorkOrderResource::hasMissingData">hasMissingData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderSubResource.php.html#93"><abbr title="App\Http\Resources\WorkOrder\WorkOrderSubResource::isOpenWorkOrderOfAuthenticatedTechnician">isOpenWorkOrderOfAuthenticatedTechnician</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderTripResource.php.html#188"><abbr title="App\Http\Resources\WorkOrder\WorkOrderTripResource::getAppointmentDetails">getAppointmentDetails</abbr></a></td><td class="text-right">10</td></tr>
       <tr><td><a href="Controllers/Vendor/VendorOnboardingController.php.html#535"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::processServices">processServices</abbr></a></td><td class="text-right">9</td></tr>
       <tr><td><a href="Middleware/AppAccessPermissionMiddleware.php.html#17"><abbr title="App\Http\Middleware\AppAccessPermissionMiddleware::handle">handle</abbr></a></td><td class="text-right">8</td></tr>
       <tr><td><a href="Middleware/MinimumAppVeriosnRequiredMiddleware.php.html#19"><abbr title="App\Http\Middleware\MinimumAppVeriosnRequiredMiddleware::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#786"><abbr title="App\Http\Controllers\Auth\RegisterController::createUserPoolClient">createUserPoolClient</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#890"><abbr title="App\Http\Controllers\Auth\RegisterController::syncUserRolePermissions">syncUserRolePermissions</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/InvoiceController.php.html#23"><abbr title="App\Http\Controllers\InvoiceController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#399"><abbr title="App\Http\Controllers\QuoteController::workOrderNumberBasedGroupData">workOrderNumberBasedGroupData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/QuoteController.php.html#462"><abbr title="App\Http\Controllers\QuoteController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#212"><abbr title="App\Http\Controllers\RoleController::destroy">destroy</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/RoleController.php.html#232"><abbr title="App\Http\Controllers\RoleController::listPermissions">listPermissions</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#642"><abbr title="App\Http\Controllers\ServiceRequestController::markAdminAvailabilityViewed">markAdminAvailabilityViewed</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/ServiceRequestController.php.html#853"><abbr title="App\Http\Controllers\ServiceRequestController::updateAccessMethod">updateAccessMethod</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/TechnicianAppointmentsController.php.html#299"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::destroy">destroy</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/UserController.php.html#551"><abbr title="App\Http\Controllers\UserController::updateNotificationSubscription">updateNotificationSubscription</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/Vendor/VendorOnboardingLookupController.php.html#74"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLocationApiKey">getLocationApiKey</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#608"><abbr title="App\Http\Controllers\WorkOrderController::updateDueDate">updateDueDate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#633"><abbr title="App\Http\Controllers\WorkOrderController::deleteDueDate">deleteDueDate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1382"><abbr title="App\Http\Controllers\WorkOrderController::getTripDetails">getTripDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1875"><abbr title="App\Http\Controllers\WorkOrderController::shouldListForTechnicianMobile">shouldListForTechnicianMobile</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1933"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderRelationsForMobile">getWorkOrderRelationsForMobile</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#1981"><abbr title="App\Http\Controllers\WorkOrderController::getServiceRequestColumnForApp">getServiceRequestColumnForApp</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/WorkOrderController.php.html#2032"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderIssuesRelationsForApp">getWorkOrderIssuesRelationsForApp</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Filters/RoleListFilter.php.html#28"><abbr title="App\Http\Filters\RoleListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Filters/TagListFilter.php.html#32"><abbr title="App\Http\Filters\TagListFilter::workOrderId">workOrderId</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Filters/TagListFilter.php.html#44"><abbr title="App\Http\Filters\TagListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Middleware/Authenticate.php.html#13"><abbr title="App\Http\Middleware\Authenticate::redirectTo">redirectTo</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Middleware/LogRequestsToDatabase.php.html#298"><abbr title="App\Http\Middleware\LogRequestsToDatabase::input">input</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Middleware/MakeTusMediaFileName.php.html#17"><abbr title="App\Http\Middleware\MakeTusMediaFileName::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Requests/ServiceRequest/ResidentAvailabilityRequest.php.html#72"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::after">after</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Requests/ServiceRequest/UpdateAvailabilityRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest::after">after</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Requests/WorkOrder/ReadyToInvoiceRequest.php.html#18"><abbr title="App\Http\Requests\WorkOrder\ReadyToInvoiceRequest::rules">rules</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceLineItemSubsidiariesResource.php.html#21"><abbr title="App\Http\Resources\Invoice\InvoiceLineItemSubsidiariesResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/Invoice/InvoiceListResource.php.html#21"><abbr title="App\Http\Resources\Invoice\InvoiceListResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/Notification/NotificationResource.php.html#20"><abbr title="App\Http\Resources\Notification\NotificationResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/Notification/NotificationResource.php.html#71"><abbr title="App\Http\Resources\Notification\NotificationResource::getServiceRequestDetails">getServiceRequestDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ResidentAvailability/ResidentAvailabilityShowResource.php.html#21"><abbr title="App\Http\Resources\ResidentAvailability\ResidentAvailabilityShowResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/Scheduling/GetTechnicianSchedulesResources.php.html#32"><abbr title="App\Http\Resources\Scheduling\GetTechnicianSchedulesResources::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/Scheduling/ScheduleContextResource.php.html#110"><abbr title="App\Http\Resources\Scheduling\ScheduleContextResource::getAvailableDurations">getAvailableDurations</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ActivityLogResource.php.html#18"><abbr title="App\Http\Resources\ServiceRequest\ActivityLogResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/AssigneeResource.php.html#17"><abbr title="App\Http\Resources\ServiceRequest\AssigneeResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/Group/PriorityBasedGroupDataResource.php.html#21"><abbr title="App\Http\Resources\ServiceRequest\Group\PriorityBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ListResource.php.html#21"><abbr title="App\Http\Resources\ServiceRequest\ListResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/PriorityResource.php.html#17"><abbr title="App\Http\Resources\ServiceRequest\PriorityResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestDescriptionResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\ServiceRequestDescriptionResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestSourceResource.php.html#20"><abbr title="App\Http\Resources\ServiceRequest\ServiceRequestSourceResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/ServiceRequestTypeResource.php.html#20"><abbr title="App\Http\Resources\ServiceRequest\ServiceRequestTypeResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/ServiceRequest/StoreResource.php.html#19"><abbr title="App\Http\Resources\ServiceRequest\StoreResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/User/ProfileResource.php.html#20"><abbr title="App\Http\Resources\User\ProfileResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/User/UserListResource.php.html#43"><abbr title="App\Http\Resources\User\UserListResource::findGroupSlug">findGroupSlug</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/Vendor/VendorOnboardingResource.php.html#69"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::processService">processService</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/Vendor/VendorOnboardingResource.php.html#98"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::addIfNotPresent">addIfNotPresent</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/View/ConfigurationResource.php.html#19"><abbr title="App\Http\Resources\View\ConfigurationResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/ActivityLogResource.php.html#18"><abbr title="App\Http\Resources\WorkOrder\ActivityLogResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/CompletedResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\CompletedResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/DueDateResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\DueDateResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/EnRouteResource.php.html#60"><abbr title="App\Http\Resources\WorkOrder\EnRouteResource::getWorkOrderStatus">getWorkOrderStatus</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/Filter/HealthScoreFilterResource.php.html#16"><abbr title="App\Http\Resources\WorkOrder\Filter\HealthScoreFilterResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/Group/PriorityBasedGroupDataResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\Group\PriorityBasedGroupDataResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/HealthScore/HealthScoreResource.php.html#33"><abbr title="App\Http\Resources\WorkOrder\HealthScore\HealthScoreResource::elapseTime">elapseTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/Invoice/InvoiceResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\Invoice\InvoiceResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ActiveTripResource.php.html#99"><abbr title="App\Http\Resources\WorkOrder\List\ActiveTripResource::getAppointmentDetails">getAppointmentDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ListTaskAppointmentResource.php.html#99"><abbr title="App\Http\Resources\WorkOrder\List\ListTaskAppointmentResource::lulaNetWorkProTripData">lulaNetWorkProTripData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/PauseEnRouteResource.php.html#61"><abbr title="App\Http\Resources\WorkOrder\PauseEnRouteResource::getWorkOrderStatus">getWorkOrderStatus</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/PriorityUpdateResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\PriorityUpdateResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/Quote/QuoteMaterialResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\Quote\QuoteMaterialResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteApproveResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\QuoteApproveResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteRejectResource.php.html#20"><abbr title="App\Http\Resources\WorkOrder\QuoteRejectResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/QuoteTaskResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\QuoteTaskResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/ResumeEnRouteResource.php.html#59"><abbr title="App\Http\Resources\WorkOrder\ResumeEnRouteResource::getWorkOrderStatus">getWorkOrderStatus</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/ServiceRequestResource.php.html#27"><abbr title="App\Http\Resources\WorkOrder\ServiceRequestResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/StoreResources.php.html#19"><abbr title="App\Http\Resources\WorkOrder\StoreResources::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrder/TaskListResource.php.html#19"><abbr title="App\Http\Resources\WorkOrder\TaskListResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/WorkOrderIssueDetailsResource.php.html#20"><abbr title="App\Http\Resources\WorkOrderIssue\WorkOrderIssueDetailsResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Requests/Vendor/ListRequest.php.html#34"><abbr title="App\Http\Requests\Vendor\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">5</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#87"><abbr title="App\Http\Controllers\Auth\RegisterController::forgotPassword">forgotPassword</abbr></a></td><td class="text-right">5</td></tr>
       <tr><td><a href="Resources/WorkOrder/WorkOrderStatusResource.php.html#21"><abbr title="App\Http\Resources\WorkOrder\WorkOrderStatusResource::toArray">toArray</abbr></a></td><td class="text-right">4</td></tr>
       <tr><td><a href="Resources/ResidentAvailability/ResidentAvailabilityResource.php.html#20"><abbr title="App\Http\Resources\ResidentAvailability\ResidentAvailabilityResource::toArray">toArray</abbr></a></td><td class="text-right">4</td></tr>
       <tr><td><a href="Controllers/Vendor/VendorOnboardingLookupController.php.html#25"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLookups">getLookups</abbr></a></td><td class="text-right">4</td></tr>
       <tr><td><a href="Controllers/Vendor/WorkOrderController.php.html#77"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::show">show</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#145"><abbr title="App\Http\Controllers\Auth\RegisterController::verify">verify</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Resources/Vendor/VendorOnboardingResource.php.html#50"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::getServiceOffered">getServiceOffered</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Resources/Vendor/WorkOrderResource.php.html#146"><abbr title="App\Http\Resources\Vendor\WorkOrderResource::hasMissingData">hasMissingData</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Resources/WorkOrder/List/ActiveTripResource.php.html#128"><abbr title="App\Http\Resources\WorkOrder\List\ActiveTripResource::getFormattedScheduleDate">getFormattedScheduleDate</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Resources/WorkOrderIssue/WorkOrderIssueStatusResource.php.html#20"><abbr title="App\Http\Resources\WorkOrderIssue\WorkOrderIssueStatusResource::toArray">toArray</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Controllers/Vendor/WorkOrderController.php.html#35"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::index">index</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#1041"><abbr title="App\Http\Controllers\Auth\RegisterController::processPasswordReset">processPasswordReset</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="Controllers/Auth/RegisterController.php.html#960"><abbr title="App\Http\Controllers\Auth\RegisterController::sendEmail">sendEmail</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="Controllers/Vendor/WorkOrderController.php.html#220"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getSkippedMediaTypes">getSkippedMediaTypes</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="Filters/QueryFilter.php.html#84"><abbr title="App\Http\Filters\QueryFilter::sort">sort</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="Resources/Issue/IssueResource.php.html#22"><abbr title="App\Http\Resources\Issue\IssueResource::toArray">toArray</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Mon Jun 30 0:32:48 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([326,1,0,0,1,2,2,6,0,5,4,29], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([650,1,0,0,1,2,6,7,2,9,11,55], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,8,"<a href=\"Controllers\/AppVersionController.php.html#14\">App\\Http\\Controllers\\AppVersionController<\/a>"],[32.158590308370044,79,"<a href=\"Controllers\/Auth\/RegisterController.php.html#56\">App\\Http\\Controllers\\Auth\\RegisterController<\/a>"],[0,9,"<a href=\"Controllers\/Auth\/SSOController.php.html#22\">App\\Http\\Controllers\\Auth\\SSOController<\/a>"],[100,0,"<a href=\"Controllers\/Controller.php.html#9\">App\\Http\\Controllers\\Controller<\/a>"],[0,8,"<a href=\"Controllers\/DatabaseNotificationController.php.html#16\">App\\Http\\Controllers\\DatabaseNotificationController<\/a>"],[0,14,"<a href=\"Controllers\/Developer\/HealthCheckController.php.html#14\">App\\Http\\Controllers\\Developer\\HealthCheckController<\/a>"],[0,10,"<a href=\"Controllers\/Developer\/LogViewerController.php.html#19\">App\\Http\\Controllers\\Developer\\LogViewerController<\/a>"],[0,10,"<a href=\"Controllers\/InvoiceController.php.html#21\">App\\Http\\Controllers\\InvoiceController<\/a>"],[0,27,"<a href=\"Controllers\/LookupController.php.html#39\">App\\Http\\Controllers\\LookupController<\/a>"],[0,20,"<a href=\"Controllers\/LulaWebhookController.php.html#28\">App\\Http\\Controllers\\LulaWebhookController<\/a>"],[0,7,"<a href=\"Controllers\/MediaController.php.html#16\">App\\Http\\Controllers\\MediaController<\/a>"],[0,13,"<a href=\"Controllers\/OrganizationController.php.html#20\">App\\Http\\Controllers\\OrganizationController<\/a>"],[0,35,"<a href=\"Controllers\/PDFController.php.html#30\">App\\Http\\Controllers\\PDFController<\/a>"],[0,1,"<a href=\"Controllers\/ProfileController.php.html#9\">App\\Http\\Controllers\\ProfileController<\/a>"],[0,11,"<a href=\"Controllers\/PublicAccessController.php.html#17\">App\\Http\\Controllers\\PublicAccessController<\/a>"],[0,2,"<a href=\"Controllers\/PublicApiWorkOrderWebhookEventsController.php.html#8\">App\\Http\\Controllers\\PublicApiWorkOrderWebhookEventsController<\/a>"],[0,52,"<a href=\"Controllers\/QuoteController.php.html#49\">App\\Http\\Controllers\\QuoteController<\/a>"],[0,14,"<a href=\"Controllers\/ResidentAvailabilityController.php.html#19\">App\\Http\\Controllers\\ResidentAvailabilityController<\/a>"],[0,22,"<a href=\"Controllers\/RoleController.php.html#25\">App\\Http\\Controllers\\RoleController<\/a>"],[0,26,"<a href=\"Controllers\/SchedulingController.php.html#31\">App\\Http\\Controllers\\SchedulingController<\/a>"],[0,10,"<a href=\"Controllers\/ServiceRequestActivityLogController.php.html#24\">App\\Http\\Controllers\\ServiceRequestActivityLogController<\/a>"],[0,13,"<a href=\"Controllers\/ServiceRequestAssigneeController.php.html#22\">App\\Http\\Controllers\\ServiceRequestAssigneeController<\/a>"],[0,112,"<a href=\"Controllers\/ServiceRequestController.php.html#89\">App\\Http\\Controllers\\ServiceRequestController<\/a>"],[0,38,"<a href=\"Controllers\/ServiceRequestMediaController.php.html#39\">App\\Http\\Controllers\\ServiceRequestMediaController<\/a>"],[0,17,"<a href=\"Controllers\/ServiceRequestNoteController.php.html#31\">App\\Http\\Controllers\\ServiceRequestNoteController<\/a>"],[0,15,"<a href=\"Controllers\/TagController.php.html#24\">App\\Http\\Controllers\\TagController<\/a>"],[0,31,"<a href=\"Controllers\/TechnicianAppointmentsController.php.html#37\">App\\Http\\Controllers\\TechnicianAppointmentsController<\/a>"],[0,30,"<a href=\"Controllers\/TechnicianController.php.html#26\">App\\Http\\Controllers\\TechnicianController<\/a>"],[0,85,"<a href=\"Controllers\/UserController.php.html#61\">App\\Http\\Controllers\\UserController<\/a>"],[92.70516717325228,60,"<a href=\"Controllers\/Vendor\/VendorOnboardingController.php.html#40\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController<\/a>"],[83.6734693877551,9,"<a href=\"Controllers\/Vendor\/VendorOnboardingLookupController.php.html#23\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController<\/a>"],[96.7741935483871,12,"<a href=\"Controllers\/Vendor\/WorkOrderController.php.html#27\">App\\Http\\Controllers\\Vendor\\WorkOrderController<\/a>"],[0,22,"<a href=\"Controllers\/VendorController.php.html#33\">App\\Http\\Controllers\\VendorController<\/a>"],[0,53,"<a href=\"Controllers\/ViewController.php.html#43\">App\\Http\\Controllers\\ViewController<\/a>"],[0,2,"<a href=\"Controllers\/WebhookCallsController.php.html#9\">App\\Http\\Controllers\\WebhookCallsController<\/a>"],[0,31,"<a href=\"Controllers\/WebhookController.php.html#30\">App\\Http\\Controllers\\WebhookController<\/a>"],[0,6,"<a href=\"Controllers\/WorkOrder\/WorkOrderIssueController.php.html#17\">App\\Http\\Controllers\\WorkOrder\\WorkOrderIssueController<\/a>"],[0,9,"<a href=\"Controllers\/WorkOrderActivityLogController.php.html#22\">App\\Http\\Controllers\\WorkOrderActivityLogController<\/a>"],[0,13,"<a href=\"Controllers\/WorkOrderAssigneeController.php.html#22\">App\\Http\\Controllers\\WorkOrderAssigneeController<\/a>"],[0,186,"<a href=\"Controllers\/WorkOrderController.php.html#115\">App\\Http\\Controllers\\WorkOrderController<\/a>"],[0,49,"<a href=\"Controllers\/WorkOrderMediaController.php.html#43\">App\\Http\\Controllers\\WorkOrderMediaController<\/a>"],[0,22,"<a href=\"Controllers\/WorkOrderNoteController.php.html#27\">App\\Http\\Controllers\\WorkOrderNoteController<\/a>"],[90.9090909090909,11,"<a href=\"Filters\/QueryFilter.php.html#12\">App\\Http\\Filters\\QueryFilter<\/a>"],[0,8,"<a href=\"Filters\/QuoteListFilter.php.html#10\">App\\Http\\Filters\\QuoteListFilter<\/a>"],[0,3,"<a href=\"Filters\/RoleListFilter.php.html#5\">App\\Http\\Filters\\RoleListFilter<\/a>"],[0,9,"<a href=\"Filters\/ServiceRequestListFilter.php.html#10\">App\\Http\\Filters\\ServiceRequestListFilter<\/a>"],[0,5,"<a href=\"Filters\/TagListFilter.php.html#8\">App\\Http\\Filters\\TagListFilter<\/a>"],[0,7,"<a href=\"Filters\/UserListFilter.php.html#11\">App\\Http\\Filters\\UserListFilter<\/a>"],[0,1,"<a href=\"Filters\/VendorListFilter.php.html#5\">App\\Http\\Filters\\VendorListFilter<\/a>"],[60,18,"<a href=\"Filters\/WorkOrderListFilter.php.html#10\">App\\Http\\Filters\\WorkOrderListFilter<\/a>"],[100,0,"<a href=\"Kernel.php.html#7\">App\\Http\\Kernel<\/a>"],[50,5,"<a href=\"Middleware\/AppAccessPermissionMiddleware.php.html#15\">App\\Http\\Middleware\\AppAccessPermissionMiddleware<\/a>"],[0,2,"<a href=\"Middleware\/Authenticate.php.html#8\">App\\Http\\Middleware\\Authenticate<\/a>"],[100,0,"<a href=\"Middleware\/EncryptCookies.php.html#7\">App\\Http\\Middleware\\EncryptCookies<\/a>"],[0,6,"<a href=\"Middleware\/HorizonBasicAuth.php.html#7\">App\\Http\\Middleware\\HorizonBasicAuth<\/a>"],[3.9215686274509802,42,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#19\">App\\Http\\Middleware\\LogRequestsToDatabase<\/a>"],[0,13,"<a href=\"Middleware\/MakeTusMediaFileName.php.html#12\">App\\Http\\Middleware\\MakeTusMediaFileName<\/a>"],[43.75,4,"<a href=\"Middleware\/MinimumAppVeriosnRequiredMiddleware.php.html#12\">App\\Http\\Middleware\\MinimumAppVeriosnRequiredMiddleware<\/a>"],[100,0,"<a href=\"Middleware\/PreventRequestsDuringMaintenance.php.html#7\">App\\Http\\Middleware\\PreventRequestsDuringMaintenance<\/a>"],[0,4,"<a href=\"Middleware\/RedirectIfAuthenticated.php.html#10\">App\\Http\\Middleware\\RedirectIfAuthenticated<\/a>"],[100,0,"<a href=\"Middleware\/TrimStrings.php.html#7\">App\\Http\\Middleware\\TrimStrings<\/a>"],[0,1,"<a href=\"Middleware\/TrustHosts.php.html#7\">App\\Http\\Middleware\\TrustHosts<\/a>"],[100,0,"<a href=\"Middleware\/TrustProxies.php.html#8\">App\\Http\\Middleware\\TrustProxies<\/a>"],[63.46153846153846,13,"<a href=\"Middleware\/ValidateCognitoToken.php.html#18\">App\\Http\\Middleware\\ValidateCognitoToken<\/a>"],[100,0,"<a href=\"Middleware\/ValidateSignature.php.html#7\">App\\Http\\Middleware\\ValidateSignature<\/a>"],[100,0,"<a href=\"Middleware\/VerifyCsrfToken.php.html#7\">App\\Http\\Middleware\\VerifyCsrfToken<\/a>"],[0,1,"<a href=\"Requests\/Auth\/PublicApiAuthenticateRequest.php.html#7\">App\\Http\\Requests\\Auth\\PublicApiAuthenticateRequest<\/a>"],[0,2,"<a href=\"Requests\/Auth\/SearchAddressRequest.php.html#7\">App\\Http\\Requests\\Auth\\SearchAddressRequest<\/a>"],[100,2,"<a href=\"Requests\/Auth\/SetVendorPasswordRequest.php.html#7\">App\\Http\\Requests\\Auth\\SetVendorPasswordRequest<\/a>"],[100,1,"<a href=\"Requests\/BaseApiRequest.php.html#7\">App\\Http\\Requests\\BaseApiRequest<\/a>"],[0,52,"<a href=\"Requests\/Invoice\/InvoiceRequest.php.html#24\">App\\Http\\Requests\\Invoice\\InvoiceRequest<\/a>"],[0,1,"<a href=\"Requests\/Issue\/AssignRequest.php.html#9\">App\\Http\\Requests\\Issue\\AssignRequest<\/a>"],[0,1,"<a href=\"Requests\/Issue\/CreateIssueRequest.php.html#11\">App\\Http\\Requests\\Issue\\CreateIssueRequest<\/a>"],[0,1,"<a href=\"Requests\/Issue\/UnassignRequest.php.html#9\">App\\Http\\Requests\\Issue\\UnassignRequest<\/a>"],[0,1,"<a href=\"Requests\/Organization\/UpdateRequest.php.html#10\">App\\Http\\Requests\\Organization\\UpdateRequest<\/a>"],[0,7,"<a href=\"Requests\/Quote\/ListRequest.php.html#7\">App\\Http\\Requests\\Quote\\ListRequest<\/a>"],[0,1,"<a href=\"Requests\/Schedule\/GetTechnicianListRequest.php.html#10\">App\\Http\\Requests\\Schedule\\GetTechnicianListRequest<\/a>"],[0,1,"<a href=\"Requests\/Schedule\/ReScheduleAppointmentRequest.php.html#14\">App\\Http\\Requests\\Schedule\\ReScheduleAppointmentRequest<\/a>"],[0,1,"<a href=\"Requests\/Schedule\/StoreAppointmentRequest.php.html#10\">App\\Http\\Requests\\Schedule\\StoreAppointmentRequest<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/AccessMethodUpdateRequest.php.html#9\">App\\Http\\Requests\\ServiceRequest\\AccessMethodUpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/DescriptionUpdateRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\DescriptionUpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/FilterValuesRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\FilterValuesRequest<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/GroupViewRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\GroupViewRequest<\/a>"],[0,8,"<a href=\"Requests\/ServiceRequest\/ListRequest.php.html#8\">App\\Http\\Requests\\ServiceRequest\\ListRequest<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/Media\/MediaUploadRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\Media\\MediaUploadRequest<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/Media\/ThumbnailUploadRequest.php.html#8\">App\\Http\\Requests\\ServiceRequest\\Media\\ThumbnailUploadRequest<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/Note\/CreateRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\Note\\CreateRequest<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/Note\/UpdateRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\Note\\UpdateRequest<\/a>"],[100,1,"<a href=\"Requests\/ServiceRequest\/PriorityUpdateRequest.php.html#9\">App\\Http\\Requests\\ServiceRequest\\PriorityUpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/PropertyAddressUpdateRequest.php.html#10\">App\\Http\\Requests\\ServiceRequest\\PropertyAddressUpdateRequest<\/a>"],[0,14,"<a href=\"Requests\/ServiceRequest\/ResidentAvailabilityRequest.php.html#15\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/ResidentUpdateRequest.php.html#10\">App\\Http\\Requests\\ServiceRequest\\ResidentUpdateRequest<\/a>"],[0,7,"<a href=\"Requests\/ServiceRequest\/StoreRequest.php.html#13\">App\\Http\\Requests\\ServiceRequest\\StoreRequest<\/a>"],[0,8,"<a href=\"Requests\/ServiceRequest\/StoreWorkOrderRequest.php.html#13\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest<\/a>"],[0,3,"<a href=\"Requests\/ServiceRequest\/UpdateAvailabilityRequest.php.html#12\">App\\Http\\Requests\\ServiceRequest\\UpdateAvailabilityRequest<\/a>"],[0,2,"<a href=\"Requests\/Tag\/StoreRequest.php.html#7\">App\\Http\\Requests\\Tag\\StoreRequest<\/a>"],[0,2,"<a href=\"Requests\/Tag\/UpdateRequest.php.html#7\">App\\Http\\Requests\\Tag\\UpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/Technician\/AvailabilityDatesRequest.php.html#7\">App\\Http\\Requests\\Technician\\AvailabilityDatesRequest<\/a>"],[0,8,"<a href=\"Requests\/Technician\/BlockOutUpdateRequest.php.html#14\">App\\Http\\Requests\\Technician\\BlockOutUpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/Technician\/ScheduleWorkOrderRequest.php.html#8\">App\\Http\\Requests\\Technician\\ScheduleWorkOrderRequest<\/a>"],[0,8,"<a href=\"Requests\/Technician\/SkillsUpdateRequest.php.html#12\">App\\Http\\Requests\\Technician\\SkillsUpdateRequest<\/a>"],[0,5,"<a href=\"Requests\/Technician\/StoreBlockOutRequest.php.html#14\">App\\Http\\Requests\\Technician\\StoreBlockOutRequest<\/a>"],[0,14,"<a href=\"Requests\/Technician\/WorkingHoursUpdateRequest.php.html#10\">App\\Http\\Requests\\Technician\\WorkingHoursUpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/User\/FilterValuesRequest.php.html#7\">App\\Http\\Requests\\User\\FilterValuesRequest<\/a>"],[0,1,"<a href=\"Requests\/User\/GroupViewRequest.php.html#7\">App\\Http\\Requests\\User\\GroupViewRequest<\/a>"],[0,7,"<a href=\"Requests\/User\/ListRequest.php.html#7\">App\\Http\\Requests\\User\\ListRequest<\/a>"],[0,1,"<a href=\"Requests\/User\/StoreRequest.php.html#11\">App\\Http\\Requests\\User\\StoreRequest<\/a>"],[0,1,"<a href=\"Requests\/User\/StoreVendorUserRequest.php.html#9\">App\\Http\\Requests\\User\\StoreVendorUserRequest<\/a>"],[0,5,"<a href=\"Requests\/User\/UpdateRequest.php.html#17\">App\\Http\\Requests\\User\\UpdateRequest<\/a>"],[86.36363636363636,6,"<a href=\"Requests\/Vendor\/ListRequest.php.html#7\">App\\Http\\Requests\\Vendor\\ListRequest<\/a>"],[0,1,"<a href=\"Requests\/Vendor\/StoreRequest.php.html#9\">App\\Http\\Requests\\Vendor\\StoreRequest<\/a>"],[100,2,"<a href=\"Requests\/Vendor\/VendorOnBoardingSetServiceAreasRequest.php.html#7\">App\\Http\\Requests\\Vendor\\VendorOnBoardingSetServiceAreasRequest<\/a>"],[0,2,"<a href=\"Requests\/Vendor\/VendorOnboardingGenerateSignedUrlRequest.php.html#7\">App\\Http\\Requests\\Vendor\\VendorOnboardingGenerateSignedUrlRequest<\/a>"],[0,2,"<a href=\"Requests\/Vendor\/VendorOnboardingInitialRequest.php.html#9\">App\\Http\\Requests\\Vendor\\VendorOnboardingInitialRequest<\/a>"],[100,2,"<a href=\"Requests\/Vendor\/VendorOnboardingSetBasicInfoRequest.php.html#7\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetBasicInfoRequest<\/a>"],[100,2,"<a href=\"Requests\/Vendor\/VendorOnboardingSetServicesRequest.php.html#7\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetServicesRequest<\/a>"],[100,2,"<a href=\"Requests\/Vendor\/VendorOnboardingSetStatusRequest.php.html#7\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetStatusRequest<\/a>"],[0,1,"<a href=\"Requests\/View\/ConfigurationRequest.php.html#7\">App\\Http\\Requests\\View\\ConfigurationRequest<\/a>"],[0,1,"<a href=\"Requests\/View\/CountRequest.php.html#7\">App\\Http\\Requests\\View\\CountRequest<\/a>"],[0,4,"<a href=\"Requests\/View\/DuplicateRequest.php.html#12\">App\\Http\\Requests\\View\\DuplicateRequest<\/a>"],[0,1,"<a href=\"Requests\/View\/PinRequest.php.html#8\">App\\Http\\Requests\\View\\PinRequest<\/a>"],[0,4,"<a href=\"Requests\/View\/RenameRequest.php.html#11\">App\\Http\\Requests\\View\\RenameRequest<\/a>"],[0,4,"<a href=\"Requests\/View\/StoreRequest.php.html#13\">App\\Http\\Requests\\View\\StoreRequest<\/a>"],[0,1,"<a href=\"Requests\/View\/UpdateRequest.php.html#8\">App\\Http\\Requests\\View\\UpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/AccessMethodUpdateRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\AccessMethodUpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/ApproveQuoteRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\ApproveQuoteRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/BookmarkRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\BookmarkRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/CancelRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\CancelRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/CloseRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\CloseRequest<\/a>"],[0,3,"<a href=\"Requests\/WorkOrder\/CompleteRequest.php.html#12\">App\\Http\\Requests\\WorkOrder\\CompleteRequest<\/a>"],[0,14,"<a href=\"Requests\/WorkOrder\/CreateQuoteRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\CreateQuoteRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/DescriptionUpdateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\DescriptionUpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/DueDateUpdateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\DueDateUpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/FilterValuesRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\FilterValuesRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/GroupViewRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\GroupViewRequest<\/a>"],[65.51724137931035,7,"<a href=\"Requests\/WorkOrder\/ListRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\ListRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/Media\/MediaUploadRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\Media\\MediaUploadRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/Media\/ThumbnailUploadRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\Media\\ThumbnailUploadRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/Note\/CreateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\Note\\CreateRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/Note\/UpdateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\Note\\UpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/PauseRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\PauseRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/PriorityUpdateRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\PriorityUpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/ProblemCategoryCreateRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryCreateRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/ProblemCategoryDeleteRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryDeleteRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/ProblemCategoryUpdateRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryUpdateRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/PropertyAddressUpdateRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\PropertyAddressUpdateRequest<\/a>"],[0,13,"<a href=\"Requests\/WorkOrder\/Quote\/TaskCreateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskCreateRequest<\/a>"],[0,13,"<a href=\"Requests\/WorkOrder\/Quote\/TaskUpdateRequest.php.html#15\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskUpdateRequest<\/a>"],[0,2,"<a href=\"Requests\/WorkOrder\/ReadyToInvoiceRequest.php.html#11\">App\\Http\\Requests\\WorkOrder\\ReadyToInvoiceRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/ResidentUpdateRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\ResidentUpdateRequest<\/a>"],[0,7,"<a href=\"Requests\/WorkOrder\/SendToVendorRequest.php.html#12\">App\\Http\\Requests\\WorkOrder\\SendToVendorRequest<\/a>"],[0,6,"<a href=\"Requests\/WorkOrder\/StoreRequest.php.html#11\">App\\Http\\Requests\\WorkOrder\\StoreRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/TagRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\TagRequest<\/a>"],[0,2,"<a href=\"Requests\/WorkOrder\/UpdateNteAmountRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\UpdateNteAmountRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/UpdateTripDetailsRequest.php.html#13\">App\\Http\\Requests\\WorkOrder\\UpdateTripDetailsRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/UpdateTripRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\UpdateTripRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrderIssue\/DeclineIssueRequest.php.html#7\">App\\Http\\Requests\\WorkOrderIssue\\DeclineIssueRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrderIssue\/MarkAsDoneIssueRequest.php.html#12\">App\\Http\\Requests\\WorkOrderIssue\\MarkAsDoneIssueRequest<\/a>"],[0,1,"<a href=\"Requests\/WorkOrderIssue\/WorkOrderIssueUpdateRequest.php.html#13\">App\\Http\\Requests\\WorkOrderIssue\\WorkOrderIssueUpdateRequest<\/a>"],[0,1,"<a href=\"Resources\/APITokenResource.php.html#8\">App\\Http\\Resources\\APITokenResource<\/a>"],[0,4,"<a href=\"Resources\/AppfolioVendorResource.php.html#14\">App\\Http\\Resources\\AppfolioVendorResource<\/a>"],[0,1,"<a href=\"Resources\/CountryResource.php.html#12\">App\\Http\\Resources\\CountryResource<\/a>"],[0,1,"<a href=\"Resources\/FeatureResource.php.html#12\">App\\Http\\Resources\\FeatureResource<\/a>"],[0,3,"<a href=\"Resources\/Invoice\/CreateInvoiceResource.php.html#13\">App\\Http\\Resources\\Invoice\\CreateInvoiceResource<\/a>"],[0,1,"<a href=\"Resources\/Invoice\/DeleteInvoiceResource.php.html#13\">App\\Http\\Resources\\Invoice\\DeleteInvoiceResource<\/a>"],[0,2,"<a href=\"Resources\/Invoice\/InvoiceLineItemSubsidiariesResource.php.html#14\">App\\Http\\Resources\\Invoice\\InvoiceLineItemSubsidiariesResource<\/a>"],[0,1,"<a href=\"Resources\/Invoice\/InvoiceLineItemsResource.php.html#14\">App\\Http\\Resources\\Invoice\\InvoiceLineItemsResource<\/a>"],[0,2,"<a href=\"Resources\/Invoice\/InvoiceListResource.php.html#14\">App\\Http\\Resources\\Invoice\\InvoiceListResource<\/a>"],[0,1,"<a href=\"Resources\/Invoice\/InvoiceResource.php.html#12\">App\\Http\\Resources\\Invoice\\InvoiceResource<\/a>"],[0,1,"<a href=\"Resources\/Invoice\/InvoiceStateResource.php.html#12\">App\\Http\\Resources\\Invoice\\InvoiceStateResource<\/a>"],[0,11,"<a href=\"Resources\/Invoice\/TripResource.php.html#16\">App\\Http\\Resources\\Invoice\\TripResource<\/a>"],[0,1,"<a href=\"Resources\/Invoice\/VoidInvoiceResource.php.html#13\">App\\Http\\Resources\\Invoice\\VoidInvoiceResource<\/a>"],[0,1,"<a href=\"Resources\/Issue\/AssignIssueResource.php.html#14\">App\\Http\\Resources\\Issue\\AssignIssueResource<\/a>"],[86.66666666666667,2,"<a href=\"Resources\/Issue\/IssueResource.php.html#15\">App\\Http\\Resources\\Issue\\IssueResource<\/a>"],[0,1,"<a href=\"Resources\/Issue\/IssueStatusResource.php.html#13\">App\\Http\\Resources\\Issue\\IssueStatusResource<\/a>"],[0,1,"<a href=\"Resources\/Issue\/UnassignIssueResource.php.html#14\">App\\Http\\Resources\\Issue\\UnassignIssueResource<\/a>"],[0,1,"<a href=\"Resources\/LookupResource.php.html#8\">App\\Http\\Resources\\LookupResource<\/a>"],[0,7,"<a href=\"Resources\/Notification\/NotificationResource.php.html#13\">App\\Http\\Resources\\Notification\\NotificationResource<\/a>"],[0,1,"<a href=\"Resources\/Notification\/ReadNotificationResource.php.html#12\">App\\Http\\Resources\\Notification\\ReadNotificationResource<\/a>"],[0,1,"<a href=\"Resources\/Organization\/OrganizationResource.php.html#12\">App\\Http\\Resources\\Organization\\OrganizationResource<\/a>"],[0,1,"<a href=\"Resources\/PermissionResource.php.html#12\">App\\Http\\Resources\\PermissionResource<\/a>"],[0,3,"<a href=\"Resources\/PriorityResource.php.html#13\">App\\Http\\Resources\\PriorityResource<\/a>"],[0,1,"<a href=\"Resources\/ProblemCategoryDeleteResource.php.html#12\">App\\Http\\Resources\\ProblemCategoryDeleteResource<\/a>"],[100,2,"<a href=\"Resources\/ProblemCategoryResource.php.html#12\">App\\Http\\Resources\\ProblemCategoryResource<\/a>"],[0,1,"<a href=\"Resources\/ProblemCategoryUpdateResource.php.html#12\">App\\Http\\Resources\\ProblemCategoryUpdateResource<\/a>"],[100,1,"<a href=\"Resources\/ProblemDiagnosisResource.php.html#12\">App\\Http\\Resources\\ProblemDiagnosisResource<\/a>"],[100,2,"<a href=\"Resources\/ProblemSubCategoryResource.php.html#12\">App\\Http\\Resources\\ProblemSubCategoryResource<\/a>"],[0,1,"<a href=\"Resources\/QuantityTypeResources.php.html#8\">App\\Http\\Resources\\QuantityTypeResources<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/AssigneeBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\Quote\\Group\\AssigneeBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/CategoryBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\Quote\\Group\\CategoryBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/StatusBasedGroupDataResource.php.html#14\">App\\Http\\Resources\\Quote\\Group\\StatusBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/SubmittedBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\Quote\\Group\\SubmittedBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/TagBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\Quote\\Group\\TagBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/WorkOrderNumberBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\Quote\\Group\\WorkOrderNumberBasedGroupDataResource<\/a>"],[0,18,"<a href=\"Resources\/Quote\/ListResource.php.html#18\">App\\Http\\Resources\\Quote\\ListResource<\/a>"],[0,1,"<a href=\"Resources\/Quote\/ListStatusResource.php.html#7\">App\\Http\\Resources\\Quote\\ListStatusResource<\/a>"],[0,1,"<a href=\"Resources\/Quote\/ListTaskResource.php.html#8\">App\\Http\\Resources\\Quote\\ListTaskResource<\/a>"],[64.28571428571429,4,"<a href=\"Resources\/ResidentAvailability\/ResidentAvailabilityResource.php.html#13\">App\\Http\\Resources\\ResidentAvailability\\ResidentAvailabilityResource<\/a>"],[0,2,"<a href=\"Resources\/ResidentAvailability\/ResidentAvailabilityShowResource.php.html#14\">App\\Http\\Resources\\ResidentAvailability\\ResidentAvailabilityShowResource<\/a>"],[0,1,"<a href=\"Resources\/RoleResource.php.html#12\">App\\Http\\Resources\\RoleResource<\/a>"],[0,2,"<a href=\"Resources\/Scheduling\/GetTechnicianListResource.php.html#14\">App\\Http\\Resources\\Scheduling\\GetTechnicianListResource<\/a>"],[0,7,"<a href=\"Resources\/Scheduling\/GetTechnicianSchedulesResources.php.html#15\">App\\Http\\Resources\\Scheduling\\GetTechnicianSchedulesResources<\/a>"],[0,4,"<a href=\"Resources\/Scheduling\/GetVendorAvailabilityResources.php.html#10\">App\\Http\\Resources\\Scheduling\\GetVendorAvailabilityResources<\/a>"],[0,10,"<a href=\"Resources\/Scheduling\/ScheduleContextResource.php.html#20\">App\\Http\\Resources\\Scheduling\\ScheduleContextResource<\/a>"],[0,1,"<a href=\"Resources\/Scheduling\/ScheduleVendorResource.php.html#12\">App\\Http\\Resources\\Scheduling\\ScheduleVendorResource<\/a>"],[0,1,"<a href=\"Resources\/SearchAddressResource.php.html#8\">App\\Http\\Resources\\SearchAddressResource<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/ActivityLogResource.php.html#11\">App\\Http\\Resources\\ServiceRequest\\ActivityLogResource<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/AssigneeResource.php.html#10\">App\\Http\\Resources\\ServiceRequest\\AssigneeResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/AwaitingAvailabilityResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\AwaitingAvailabilityResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/ClosedResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\ClosedResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/CreateWorkOrderResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\CreateWorkOrderResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/DescriptionResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\DescriptionResource<\/a>"],[0,3,"<a href=\"Resources\/ServiceRequest\/Filter\/AddedDateFilterResource.php.html#9\">App\\Http\\Resources\\ServiceRequest\\Filter\\AddedDateFilterResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Filter\/AssigneeFilterResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\Filter\\AssigneeFilterResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Filter\/ImportedFromFilterResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\Filter\\ImportedFromFilterResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Filter\/StatusFilterResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\Filter\\StatusFilterResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Group\/AssigneeBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\ServiceRequest\\Group\\AssigneeBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Group\/ImportFromBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\ServiceRequest\\Group\\ImportFromBasedGroupDataResource<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/Group\/PriorityBasedGroupDataResource.php.html#14\">App\\Http\\Resources\\ServiceRequest\\Group\\PriorityBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Group\/StatusBasedGroupDataResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\Group\\StatusBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/InProgressResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\InProgressResource<\/a>"],[0,5,"<a href=\"Resources\/ServiceRequest\/ListResource.php.html#14\">App\\Http\\Resources\\ServiceRequest\\ListResource<\/a>"],[0,5,"<a href=\"Resources\/ServiceRequest\/Media\/OriginalMediaResource.php.html#17\">App\\Http\\Resources\\ServiceRequest\\Media\\OriginalMediaResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Media\/ThumbnailMediaResource.php.html#8\">App\\Http\\Resources\\ServiceRequest\\Media\\ThumbnailMediaResource<\/a>"],[0,5,"<a href=\"Resources\/ServiceRequest\/MediaResource.php.html#14\">App\\Http\\Resources\\ServiceRequest\\MediaResource<\/a>"],[0,4,"<a href=\"Resources\/ServiceRequest\/Note\/NoteListResource.php.html#13\">App\\Http\\Resources\\ServiceRequest\\Note\\NoteListResource<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/PriorityResource.php.html#10\">App\\Http\\Resources\\ServiceRequest\\PriorityResource<\/a>"],[100,2,"<a href=\"Resources\/ServiceRequest\/PriorityUpdateResource.php.html#14\">App\\Http\\Resources\\ServiceRequest\\PriorityUpdateResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/ProblemCategoryResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\ProblemCategoryResource<\/a>"],[0,3,"<a href=\"Resources\/ServiceRequest\/PropertyAccessInfoResource.php.html#13\">App\\Http\\Resources\\ServiceRequest\\PropertyAccessInfoResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/PropertyAddressResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\PropertyAddressResource<\/a>"],[0,6,"<a href=\"Resources\/ServiceRequest\/PropertyResource.php.html#13\">App\\Http\\Resources\\ServiceRequest\\PropertyResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/ResidentResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\ResidentResource<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/ServiceRequestDescriptionResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestDescriptionResource<\/a>"],[0,10,"<a href=\"Resources\/ServiceRequest\/ServiceRequestResource.php.html#21\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestResource<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/ServiceRequestSourceResource.php.html#13\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestSourceResource<\/a>"],[100,1,"<a href=\"Resources\/ServiceRequest\/ServiceRequestStatusResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestStatusResource<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/ServiceRequestTypeResource.php.html#13\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestTypeResource<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/StoreResource.php.html#12\">App\\Http\\Resources\\ServiceRequest\\StoreResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/WorkOrder\/WorkOrderIssuesResource.php.html#14\">App\\Http\\Resources\\ServiceRequest\\WorkOrder\\WorkOrderIssuesResource<\/a>"],[100,1,"<a href=\"Resources\/ServiceRequest\/WorkOrder\/WorkOrderServiceRequestResource.php.html#14\">App\\Http\\Resources\\ServiceRequest\\WorkOrder\\WorkOrderServiceRequestResource<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/WorkOrder\/WorkOrderStoreIssueResource.php.html#15\">App\\Http\\Resources\\ServiceRequest\\WorkOrder\\WorkOrderStoreIssueResource<\/a>"],[0,13,"<a href=\"Resources\/ServiceRequest\/WorkOrderResource.php.html#30\">App\\Http\\Resources\\ServiceRequest\\WorkOrderResource<\/a>"],[0,3,"<a href=\"Resources\/ServiceRequest\/WorkOrderStoreResource.php.html#15\">App\\Http\\Resources\\ServiceRequest\\WorkOrderStoreResource<\/a>"],[100,1,"<a href=\"Resources\/StateResource.php.html#12\">App\\Http\\Resources\\StateResource<\/a>"],[0,1,"<a href=\"Resources\/Tag\/TagResource.php.html#12\">App\\Http\\Resources\\Tag\\TagResource<\/a>"],[0,1,"<a href=\"Resources\/Technician\/AvailabilityDateResource.php.html#8\">App\\Http\\Resources\\Technician\\AvailabilityDateResource<\/a>"],[0,1,"<a href=\"Resources\/Technician\/SelectedSkillsResource.php.html#15\">App\\Http\\Resources\\Technician\\SelectedSkillsResource<\/a>"],[0,5,"<a href=\"Resources\/Technician\/TechnicianAgendaResource.php.html#19\">App\\Http\\Resources\\Technician\\TechnicianAgendaResource<\/a>"],[0,1,"<a href=\"Resources\/Technician\/TechnicianBlockOutDeleteResource.php.html#12\">App\\Http\\Resources\\Technician\\TechnicianBlockOutDeleteResource<\/a>"],[0,5,"<a href=\"Resources\/Technician\/TechnicianBlockOutResource.php.html#14\">App\\Http\\Resources\\Technician\\TechnicianBlockOutResource<\/a>"],[0,4,"<a href=\"Resources\/Technician\/TechnicianCalendarViewResource.php.html#12\">App\\Http\\Resources\\Technician\\TechnicianCalendarViewResource<\/a>"],[0,3,"<a href=\"Resources\/Technician\/WorkOrderPropertyResource.php.html#12\">App\\Http\\Resources\\Technician\\WorkOrderPropertyResource<\/a>"],[0,3,"<a href=\"Resources\/Technician\/WorkOrderTaskResource.php.html#14\">App\\Http\\Resources\\Technician\\WorkOrderTaskResource<\/a>"],[0,1,"<a href=\"Resources\/TemplateResource.php.html#8\">App\\Http\\Resources\\TemplateResource<\/a>"],[0,3,"<a href=\"Resources\/User\/AccountInfoResource.php.html#14\">App\\Http\\Resources\\User\\AccountInfoResource<\/a>"],[0,1,"<a href=\"Resources\/User\/Group\/RoleBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\User\\Group\\RoleBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/User\/Group\/StatusBasedGroupDataResource.php.html#13\">App\\Http\\Resources\\User\\Group\\StatusBasedGroupDataResource<\/a>"],[0,2,"<a href=\"Resources\/User\/ProfileResource.php.html#13\">App\\Http\\Resources\\User\\ProfileResource<\/a>"],[0,1,"<a href=\"Resources\/User\/RoleResource.php.html#11\">App\\Http\\Resources\\User\\RoleResource<\/a>"],[0,1,"<a href=\"Resources\/User\/TechnicianResource.php.html#12\">App\\Http\\Resources\\User\\TechnicianResource<\/a>"],[0,5,"<a href=\"Resources\/User\/UserListResource.php.html#14\">App\\Http\\Resources\\User\\UserListResource<\/a>"],[0,4,"<a href=\"Resources\/User\/UserResource.php.html#16\">App\\Http\\Resources\\User\\UserResource<\/a>"],[0,1,"<a href=\"Resources\/User\/UserRoleResource.php.html#12\">App\\Http\\Resources\\User\\UserRoleResource<\/a>"],[0,1,"<a href=\"Resources\/User\/UserStatusResource.php.html#8\">App\\Http\\Resources\\User\\UserStatusResource<\/a>"],[100,1,"<a href=\"Resources\/Vendor\/BasicInfoResource.php.html#9\">App\\Http\\Resources\\Vendor\\BasicInfoResource<\/a>"],[100,3,"<a href=\"Resources\/Vendor\/ListResource.php.html#18\">App\\Http\\Resources\\Vendor\\ListResource<\/a>"],[0,1,"<a href=\"Resources\/Vendor\/ProfileResource.php.html#15\">App\\Http\\Resources\\Vendor\\ProfileResource<\/a>"],[100,1,"<a href=\"Resources\/Vendor\/ServiceAreaResource.php.html#10\">App\\Http\\Resources\\Vendor\\ServiceAreaResource<\/a>"],[0,1,"<a href=\"Resources\/Vendor\/UpdateVendorResource.php.html#12\">App\\Http\\Resources\\Vendor\\UpdateVendorResource<\/a>"],[69.0909090909091,14,"<a href=\"Resources\/Vendor\/VendorOnboardingResource.php.html#10\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource<\/a>"],[89.6103896103896,17,"<a href=\"Resources\/Vendor\/WorkOrderResource.php.html#33\">App\\Http\\Resources\\Vendor\\WorkOrderResource<\/a>"],[0,1,"<a href=\"Resources\/VendorUserResource.php.html#13\">App\\Http\\Resources\\VendorUserResource<\/a>"],[0,2,"<a href=\"Resources\/View\/ConfigurationResource.php.html#12\">App\\Http\\Resources\\View\\ConfigurationResource<\/a>"],[0,1,"<a href=\"Resources\/View\/DeleteResource.php.html#12\">App\\Http\\Resources\\View\\DeleteResource<\/a>"],[0,1,"<a href=\"Resources\/View\/PinResource.php.html#13\">App\\Http\\Resources\\View\\PinResource<\/a>"],[0,1,"<a href=\"Resources\/View\/PinnedViewCount.php.html#8\">App\\Http\\Resources\\View\\PinnedViewCount<\/a>"],[0,1,"<a href=\"Resources\/View\/SetDefaultResource.php.html#12\">App\\Http\\Resources\\View\\SetDefaultResource<\/a>"],[0,1,"<a href=\"Resources\/View\/StoreResource.php.html#12\">App\\Http\\Resources\\View\\StoreResource<\/a>"],[0,4,"<a href=\"Resources\/View\/UpdatedResource.php.html#14\">App\\Http\\Resources\\View\\UpdatedResource<\/a>"],[0,8,"<a href=\"Resources\/View\/ViewResource.php.html#12\">App\\Http\\Resources\\View\\ViewResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/ActivityLogResource.php.html#11\">App\\Http\\Resources\\WorkOrder\\ActivityLogResource<\/a>"],[0,5,"<a href=\"Resources\/WorkOrder\/AppointmentRescheduledResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\AppointmentRescheduledResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/AppointmentTechnicianResource.php.html#10\">App\\Http\\Resources\\WorkOrder\\AppointmentTechnicianResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/AssigneeListResource.php.html#10\">App\\Http\\Resources\\WorkOrder\\AssigneeListResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/AwaitingAvailabilityResources.php.html#12\">App\\Http\\Resources\\WorkOrder\\AwaitingAvailabilityResources<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/CancelResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\CancelResource<\/a>"],[0,4,"<a href=\"Resources\/WorkOrder\/ClosedResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\ClosedResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/CompletedResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\CompletedResource<\/a>"],[0,6,"<a href=\"Resources\/WorkOrder\/CreateQuoteResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\CreateQuoteResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/DueDateResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\DueDateResource<\/a>"],[0,6,"<a href=\"Resources\/WorkOrder\/EnRouteResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\EnRouteResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/AssigneeFilterResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\Filter\\AssigneeFilterResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/CategoryFilterResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\Filter\\CategoryFilterResource<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/Filter\/CreatedDateFilterResource.php.html#9\">App\\Http\\Resources\\WorkOrder\\Filter\\CreatedDateFilterResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/Filter\/HealthScoreFilterResource.php.html#9\">App\\Http\\Resources\\WorkOrder\\Filter\\HealthScoreFilterResource<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/Filter\/OverdueFilterResource.php.html#9\">App\\Http\\Resources\\WorkOrder\\Filter\\OverdueFilterResource<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/Filter\/PriorityFilterResource.php.html#9\">App\\Http\\Resources\\WorkOrder\\Filter\\PriorityFilterResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/ProviderFilterResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\Filter\\ProviderFilterResource<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/Filter\/ScheduledDateFilterResource.php.html#9\">App\\Http\\Resources\\WorkOrder\\Filter\\ScheduledDateFilterResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/StatusFilterResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\Filter\\StatusFilterResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/TagFilterResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\Filter\\TagFilterResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/TechnicianFilterResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\Filter\\TechnicianFilterResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/AssigneeBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\WorkOrder\\Group\\AssigneeBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/CategoryBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\WorkOrder\\Group\\CategoryBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/HealthScoreGroupDataResource.php.html#8\">App\\Http\\Resources\\WorkOrder\\Group\\HealthScoreGroupDataResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/Group\/PriorityBasedGroupDataResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\Group\\PriorityBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/StatusBasedGroupDataResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\Group\\StatusBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/TagBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\WorkOrder\\Group\\TagBasedGroupDataResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/TechnicianBasedGroupDataResource.php.html#8\">App\\Http\\Resources\\WorkOrder\\Group\\TechnicianBasedGroupDataResource<\/a>"],[0,5,"<a href=\"Resources\/WorkOrder\/HealthScore\/HealthScoreResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\HealthScore\\HealthScoreResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Invoice\/FullyPaidResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\Invoice\\FullyPaidResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/Invoice\/InvoiceResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\Invoice\\InvoiceResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Invoice\/PartiallyPaidResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\Invoice\\PartiallyPaidResource<\/a>"],[41.37931034482759,16,"<a href=\"Resources\/WorkOrder\/List\/ActiveTripResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\List\\ActiveTripResource<\/a>"],[100,1,"<a href=\"Resources\/WorkOrder\/List\/ListPropertyResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\List\\ListPropertyResource<\/a>"],[0,8,"<a href=\"Resources\/WorkOrder\/List\/ListTaskAppointmentResource.php.html#22\">App\\Http\\Resources\\WorkOrder\\List\\ListTaskAppointmentResource<\/a>"],[0,32,"<a href=\"Resources\/WorkOrder\/ListResource.php.html#31\">App\\Http\\Resources\\WorkOrder\\ListResource<\/a>"],[0,5,"<a href=\"Resources\/WorkOrder\/Media\/OriginalMediaResource.php.html#17\">App\\Http\\Resources\\WorkOrder\\Media\\OriginalMediaResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Media\/ThumbnailMediaResource.php.html#8\">App\\Http\\Resources\\WorkOrder\\Media\\ThumbnailMediaResource<\/a>"],[0,5,"<a href=\"Resources\/WorkOrder\/MediaResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\MediaResource<\/a>"],[0,4,"<a href=\"Resources\/WorkOrder\/Note\/NoteListResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\Note\\NoteListResource<\/a>"],[0,6,"<a href=\"Resources\/WorkOrder\/PauseEnRouteResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\PauseEnRouteResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/PauseResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\PauseResource<\/a>"],[0,4,"<a href=\"Resources\/WorkOrder\/PauseWorkResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\PauseWorkResource<\/a>"],[100,2,"<a href=\"Resources\/WorkOrder\/PriorityResource.php.html#10\">App\\Http\\Resources\\WorkOrder\\PriorityResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/PriorityUpdateResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\PriorityUpdateResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ProblemCategoryResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\ProblemCategoryResource<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/PropertyAccessInfoResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\PropertyAccessInfoResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/PropertyAddressResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\PropertyAddressResource<\/a>"],[100,6,"<a href=\"Resources\/WorkOrder\/PropertyResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\PropertyResource<\/a>"],[0,4,"<a href=\"Resources\/WorkOrder\/ProviderResource.php.html#16\">App\\Http\\Resources\\WorkOrder\\ProviderResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/Quote\/QuoteMaterialResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\Quote\\QuoteMaterialResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Quote\/TaskDeleteResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\Quote\\TaskDeleteResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/QuoteApproveResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\QuoteApproveResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/QuoteRejectResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\QuoteRejectResource<\/a>"],[0,7,"<a href=\"Resources\/WorkOrder\/QuoteResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\QuoteResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/QuoteTaskResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\QuoteTaskResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ReOpenResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\ReOpenResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ReadyToInvoiceResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\ReadyToInvoiceResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ReadyToScheduleResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\ReadyToScheduleResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ResidentResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\ResidentResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ResolveResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\ResolveResource<\/a>"],[0,6,"<a href=\"Resources\/WorkOrder\/ResumeEnRouteResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\ResumeEnRouteResource<\/a>"],[0,4,"<a href=\"Resources\/WorkOrder\/ResumeWorkResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\ResumeWorkResource<\/a>"],[0,6,"<a href=\"Resources\/WorkOrder\/ServiceRequestResource.php.html#18\">App\\Http\\Resources\\WorkOrder\\ServiceRequestResource<\/a>"],[0,4,"<a href=\"Resources\/WorkOrder\/StartWorkResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\StartWorkResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/StopTripResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\StopTripResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/StoreResources.php.html#12\">App\\Http\\Resources\\WorkOrder\\StoreResources<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/TaskAppointmentResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\TaskAppointmentResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/TaskListResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\TaskListResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/TaskMaterialResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\TaskMaterialResource<\/a>"],[0,29,"<a href=\"Resources\/WorkOrder\/TaskResource.php.html#18\">App\\Http\\Resources\\WorkOrder\\TaskResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/TaskScheduledAppointmentResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\TaskScheduledAppointmentResource<\/a>"],[0,4,"<a href=\"Resources\/WorkOrder\/TechnicianAppointmentResource.php.html#13\">App\\Http\\Resources\\WorkOrder\\TechnicianAppointmentResource<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/Trip\/TripDetailsResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\Trip\\TripDetailsResource<\/a>"],[0,29,"<a href=\"Resources\/WorkOrder\/TripResource.php.html#24\">App\\Http\\Resources\\WorkOrder\\TripResource<\/a>"],[100,2,"<a href=\"Resources\/WorkOrder\/TripStatusResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\TripStatusResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderAssigneeResource.php.html#10\">App\\Http\\Resources\\WorkOrder\\WorkOrderAssigneeResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderDescriptionResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\WorkOrderDescriptionResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderNteResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\WorkOrderNteResource<\/a>"],[0,25,"<a href=\"Resources\/WorkOrder\/WorkOrderResource.php.html#37\">App\\Http\\Resources\\WorkOrder\\WorkOrderResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderScheduledResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\WorkOrderScheduledResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderSendToVendorResource.php.html#10\">App\\Http\\Resources\\WorkOrder\\WorkOrderSendToVendorResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderServiceRequestStatusResource.php.html#12\">App\\Http\\Resources\\WorkOrder\\WorkOrderServiceRequestStatusResource<\/a>"],[63.63636363636363,4,"<a href=\"Resources\/WorkOrder\/WorkOrderStatusResource.php.html#14\">App\\Http\\Resources\\WorkOrder\\WorkOrderStatusResource<\/a>"],[0,17,"<a href=\"Resources\/WorkOrder\/WorkOrderSubResource.php.html#28\">App\\Http\\Resources\\WorkOrder\\WorkOrderSubResource<\/a>"],[54.54545454545454,24,"<a href=\"Resources\/WorkOrder\/WorkOrderTripResource.php.html#17\">App\\Http\\Resources\\WorkOrder\\WorkOrderTripResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrderIssue\/IssueDeclinedReasonResource.php.html#13\">App\\Http\\Resources\\WorkOrderIssue\\IssueDeclinedReasonResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrderIssue\/IssueDeclinedResource.php.html#13\">App\\Http\\Resources\\WorkOrderIssue\\IssueDeclinedResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrderIssue\/MaterialResource.php.html#12\">App\\Http\\Resources\\WorkOrderIssue\\MaterialResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrderIssue\/PendingIssueResource.php.html#11\">App\\Http\\Resources\\WorkOrderIssue\\PendingIssueResource<\/a>"],[0,2,"<a href=\"Resources\/WorkOrderIssue\/WorkOrderIssueDetailsResource.php.html#13\">App\\Http\\Resources\\WorkOrderIssue\\WorkOrderIssueDetailsResource<\/a>"],[0,1,"<a href=\"Resources\/WorkOrderIssue\/WorkOrderIssueListResource.php.html#13\">App\\Http\\Resources\\WorkOrderIssue\\WorkOrderIssueListResource<\/a>"],[92.5,5,"<a href=\"Resources\/WorkOrderIssue\/WorkOrderIssueResource.php.html#20\">App\\Http\\Resources\\WorkOrderIssue\\WorkOrderIssueResource<\/a>"],[85.71428571428571,3,"<a href=\"Resources\/WorkOrderIssue\/WorkOrderIssueStatusResource.php.html#13\">App\\Http\\Resources\\WorkOrderIssue\\WorkOrderIssueStatusResource<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,8,"<a href=\"Controllers\/AppVersionController.php.html#19\">App\\Http\\Controllers\\AppVersionController::checkLatestAppVersion<\/a>"],[100,1,"<a href=\"Controllers\/Auth\/RegisterController.php.html#68\">App\\Http\\Controllers\\Auth\\RegisterController::__construct<\/a>"],[87.5,5,"<a href=\"Controllers\/Auth\/RegisterController.php.html#87\">App\\Http\\Controllers\\Auth\\RegisterController::forgotPassword<\/a>"],[100,3,"<a href=\"Controllers\/Auth\/RegisterController.php.html#128\">App\\Http\\Controllers\\Auth\\RegisterController::resetPassword<\/a>"],[70,3,"<a href=\"Controllers\/Auth\/RegisterController.php.html#145\">App\\Http\\Controllers\\Auth\\RegisterController::verify<\/a>"],[0,3,"<a href=\"Controllers\/Auth\/RegisterController.php.html#167\">App\\Http\\Controllers\\Auth\\RegisterController::searchAddress<\/a>"],[93.6842105263158,4,"<a href=\"Controllers\/Auth\/RegisterController.php.html#197\">App\\Http\\Controllers\\Auth\\RegisterController::getVendorOnboardingInvite<\/a>"],[97.43589743589743,6,"<a href=\"Controllers\/Auth\/RegisterController.php.html#333\">App\\Http\\Controllers\\Auth\\RegisterController::setVendorPassword<\/a>"],[0,8,"<a href=\"Controllers\/Auth\/RegisterController.php.html#402\">App\\Http\\Controllers\\Auth\\RegisterController::signup<\/a>"],[0,3,"<a href=\"Controllers\/Auth\/RegisterController.php.html#510\">App\\Http\\Controllers\\Auth\\RegisterController::createOrganization<\/a>"],[0,5,"<a href=\"Controllers\/Auth\/RegisterController.php.html#536\">App\\Http\\Controllers\\Auth\\RegisterController::setupUserPool<\/a>"],[0,5,"<a href=\"Controllers\/Auth\/RegisterController.php.html#642\">App\\Http\\Controllers\\Auth\\RegisterController::findLoginConfig<\/a>"],[0,1,"<a href=\"Controllers\/Auth\/RegisterController.php.html#709\">App\\Http\\Controllers\\Auth\\RegisterController::findProviderLoginConfig<\/a>"],[0,10,"<a href=\"Controllers\/Auth\/RegisterController.php.html#726\">App\\Http\\Controllers\\Auth\\RegisterController::authenticateClientCredential<\/a>"],[0,2,"<a href=\"Controllers\/Auth\/RegisterController.php.html#786\">App\\Http\\Controllers\\Auth\\RegisterController::createUserPoolClient<\/a>"],[0,1,"<a href=\"Controllers\/Auth\/RegisterController.php.html#823\">App\\Http\\Controllers\\Auth\\RegisterController::createUserPool<\/a>"],[0,2,"<a href=\"Controllers\/Auth\/RegisterController.php.html#890\">App\\Http\\Controllers\\Auth\\RegisterController::syncUserRolePermissions<\/a>"],[57.14285714285714,2,"<a href=\"Controllers\/Auth\/RegisterController.php.html#960\">App\\Http\\Controllers\\Auth\\RegisterController::sendEmail<\/a>"],[100,3,"<a href=\"Controllers\/Auth\/RegisterController.php.html#975\">App\\Http\\Controllers\\Auth\\RegisterController::validateRequest<\/a>"],[91.66666666666666,6,"<a href=\"Controllers\/Auth\/RegisterController.php.html#988\">App\\Http\\Controllers\\Auth\\RegisterController::validateSignedUrl<\/a>"],[100,4,"<a href=\"Controllers\/Auth\/RegisterController.php.html#1013\">App\\Http\\Controllers\\Auth\\RegisterController::validatePassword<\/a>"],[50,2,"<a href=\"Controllers\/Auth\/RegisterController.php.html#1041\">App\\Http\\Controllers\\Auth\\RegisterController::processPasswordReset<\/a>"],[0,4,"<a href=\"Controllers\/Auth\/SSOController.php.html#26\">App\\Http\\Controllers\\Auth\\SSOController::validateUser<\/a>"],[0,5,"<a href=\"Controllers\/Auth\/SSOController.php.html#76\">App\\Http\\Controllers\\Auth\\SSOController::getCognitoUserDetails<\/a>"],[0,4,"<a href=\"Controllers\/DatabaseNotificationController.php.html#18\">App\\Http\\Controllers\\DatabaseNotificationController::index<\/a>"],[0,4,"<a href=\"Controllers\/DatabaseNotificationController.php.html#60\">App\\Http\\Controllers\\DatabaseNotificationController::clearedNotifications<\/a>"],[0,1,"<a href=\"Controllers\/Developer\/HealthCheckController.php.html#26\">App\\Http\\Controllers\\Developer\\HealthCheckController::basicCheck<\/a>"],[0,4,"<a href=\"Controllers\/Developer\/HealthCheckController.php.html#38\">App\\Http\\Controllers\\Developer\\HealthCheckController::utilitiesCheck<\/a>"],[0,3,"<a href=\"Controllers\/Developer\/HealthCheckController.php.html#67\">App\\Http\\Controllers\\Developer\\HealthCheckController::checkDatabaseConnection<\/a>"],[0,3,"<a href=\"Controllers\/Developer\/HealthCheckController.php.html#88\">App\\Http\\Controllers\\Developer\\HealthCheckController::checkCacheConnection<\/a>"],[0,3,"<a href=\"Controllers\/Developer\/HealthCheckController.php.html#110\">App\\Http\\Controllers\\Developer\\HealthCheckController::checkLogging<\/a>"],[0,1,"<a href=\"Controllers\/Developer\/LogViewerController.php.html#24\">App\\Http\\Controllers\\Developer\\LogViewerController::requestLogs<\/a>"],[0,1,"<a href=\"Controllers\/Developer\/LogViewerController.php.html#34\">App\\Http\\Controllers\\Developer\\LogViewerController::requestLogShow<\/a>"],[0,1,"<a href=\"Controllers\/Developer\/LogViewerController.php.html#42\">App\\Http\\Controllers\\Developer\\LogViewerController::incomingWebhookLogs<\/a>"],[0,1,"<a href=\"Controllers\/Developer\/LogViewerController.php.html#50\">App\\Http\\Controllers\\Developer\\LogViewerController::incomingWebhookLogShow<\/a>"],[0,1,"<a href=\"Controllers\/Developer\/LogViewerController.php.html#58\">App\\Http\\Controllers\\Developer\\LogViewerController::outgoingWebhookLogs<\/a>"],[0,1,"<a href=\"Controllers\/Developer\/LogViewerController.php.html#66\">App\\Http\\Controllers\\Developer\\LogViewerController::outgoingWebhookLogShow<\/a>"],[0,1,"<a href=\"Controllers\/Developer\/LogViewerController.php.html#76\">App\\Http\\Controllers\\Developer\\LogViewerController::developerAlerts<\/a>"],[0,1,"<a href=\"Controllers\/Developer\/LogViewerController.php.html#86\">App\\Http\\Controllers\\Developer\\LogViewerController::developerAlertShow<\/a>"],[0,1,"<a href=\"Controllers\/Developer\/LogViewerController.php.html#93\">App\\Http\\Controllers\\Developer\\LogViewerController::vendorPublicApiLogs<\/a>"],[0,1,"<a href=\"Controllers\/Developer\/LogViewerController.php.html#98\">App\\Http\\Controllers\\Developer\\LogViewerController::vendorPublicApiLogShow<\/a>"],[0,2,"<a href=\"Controllers\/InvoiceController.php.html#23\">App\\Http\\Controllers\\InvoiceController::index<\/a>"],[0,4,"<a href=\"Controllers\/InvoiceController.php.html#41\">App\\Http\\Controllers\\InvoiceController::show<\/a>"],[0,4,"<a href=\"Controllers\/InvoiceController.php.html#92\">App\\Http\\Controllers\\InvoiceController::workOrderInvoiceSummary<\/a>"],[0,4,"<a href=\"Controllers\/LookupController.php.html#44\">App\\Http\\Controllers\\LookupController::__invoke<\/a>"],[0,6,"<a href=\"Controllers\/LookupController.php.html#85\">App\\Http\\Controllers\\LookupController::getFieldAppFilters<\/a>"],[0,1,"<a href=\"Controllers\/LookupController.php.html#168\">App\\Http\\Controllers\\LookupController::getPriorities<\/a>"],[0,1,"<a href=\"Controllers\/LookupController.php.html#173\">App\\Http\\Controllers\\LookupController::getPropertyAccessMethods<\/a>"],[0,1,"<a href=\"Controllers\/LookupController.php.html#178\">App\\Http\\Controllers\\LookupController::getStates<\/a>"],[0,1,"<a href=\"Controllers\/LookupController.php.html#189\">App\\Http\\Controllers\\LookupController::getCountry<\/a>"],[0,1,"<a href=\"Controllers\/LookupController.php.html#200\">App\\Http\\Controllers\\LookupController::getProblemCategory<\/a>"],[0,1,"<a href=\"Controllers\/LookupController.php.html#225\">App\\Http\\Controllers\\LookupController::getTechnicians<\/a>"],[0,1,"<a href=\"Controllers\/LookupController.php.html#246\">App\\Http\\Controllers\\LookupController::getAssignees<\/a>"],[0,1,"<a href=\"Controllers\/LookupController.php.html#260\">App\\Http\\Controllers\\LookupController::getQuantityTypes<\/a>"],[0,1,"<a href=\"Controllers\/LookupController.php.html#265\">App\\Http\\Controllers\\LookupController::endTripeOptions<\/a>"],[0,7,"<a href=\"Controllers\/LookupController.php.html#276\">App\\Http\\Controllers\\LookupController::expectedDurations<\/a>"],[0,1,"<a href=\"Controllers\/LookupController.php.html#312\">App\\Http\\Controllers\\LookupController::getOnboardingStatuses<\/a>"],[0,10,"<a href=\"Controllers\/LulaWebhookController.php.html#34\">App\\Http\\Controllers\\LulaWebhookController::__invoke<\/a>"],[0,1,"<a href=\"Controllers\/LulaWebhookController.php.html#146\">App\\Http\\Controllers\\LulaWebhookController::invalidAction<\/a>"],[0,1,"<a href=\"Controllers\/LulaWebhookController.php.html#153\">App\\Http\\Controllers\\LulaWebhookController::validateScheduleInProgressPayload<\/a>"],[0,1,"<a href=\"Controllers\/LulaWebhookController.php.html#160\">App\\Http\\Controllers\\LulaWebhookController::validateSchedulePayload<\/a>"],[0,1,"<a href=\"Controllers\/LulaWebhookController.php.html#171\">App\\Http\\Controllers\\LulaWebhookController::validateWorkInProgressPayload<\/a>"],[0,1,"<a href=\"Controllers\/LulaWebhookController.php.html#178\">App\\Http\\Controllers\\LulaWebhookController::validateWorkPausedPayload<\/a>"],[0,1,"<a href=\"Controllers\/LulaWebhookController.php.html#186\">App\\Http\\Controllers\\LulaWebhookController::validateQualityCheckPayload<\/a>"],[0,1,"<a href=\"Controllers\/LulaWebhookController.php.html#193\">App\\Http\\Controllers\\LulaWebhookController::validateWorkCompletedPayload<\/a>"],[0,1,"<a href=\"Controllers\/LulaWebhookController.php.html#203\">App\\Http\\Controllers\\LulaWebhookController::validateWorkCancelPayload<\/a>"],[0,1,"<a href=\"Controllers\/LulaWebhookController.php.html#211\">App\\Http\\Controllers\\LulaWebhookController::validateNoteAddedPayload<\/a>"],[0,1,"<a href=\"Controllers\/LulaWebhookController.php.html#221\">App\\Http\\Controllers\\LulaWebhookController::validateInvoicePayload<\/a>"],[0,7,"<a href=\"Controllers\/MediaController.php.html#18\">App\\Http\\Controllers\\MediaController::__invoke<\/a>"],[0,1,"<a href=\"Controllers\/OrganizationController.php.html#24\">App\\Http\\Controllers\\OrganizationController::show<\/a>"],[0,10,"<a href=\"Controllers\/OrganizationController.php.html#32\">App\\Http\\Controllers\\OrganizationController::update<\/a>"],[0,1,"<a href=\"Controllers\/OrganizationController.php.html#94\">App\\Http\\Controllers\\OrganizationController::getTemplates<\/a>"],[0,1,"<a href=\"Controllers\/OrganizationController.php.html#115\">App\\Http\\Controllers\\OrganizationController::updateTemplate<\/a>"],[0,1,"<a href=\"Controllers\/PDFController.php.html#32\">App\\Http\\Controllers\\PDFController::__construct<\/a>"],[0,7,"<a href=\"Controllers\/PDFController.php.html#38\">App\\Http\\Controllers\\PDFController::viewQuote<\/a>"],[0,27,"<a href=\"Controllers\/PDFController.php.html#193\">App\\Http\\Controllers\\PDFController::viewInvoice<\/a>"],[0,1,"<a href=\"Controllers\/ProfileController.php.html#14\">App\\Http\\Controllers\\ProfileController::profile<\/a>"],[0,6,"<a href=\"Controllers\/PublicAccessController.php.html#19\">App\\Http\\Controllers\\PublicAccessController::getMedia<\/a>"],[0,5,"<a href=\"Controllers\/PublicAccessController.php.html#82\">App\\Http\\Controllers\\PublicAccessController::getSrMedia<\/a>"],[0,1,"<a href=\"Controllers\/PublicApiWorkOrderWebhookEventsController.php.html#13\">App\\Http\\Controllers\\PublicApiWorkOrderWebhookEventsController::index<\/a>"],[0,1,"<a href=\"Controllers\/PublicApiWorkOrderWebhookEventsController.php.html#21\">App\\Http\\Controllers\\PublicApiWorkOrderWebhookEventsController::show<\/a>"],[0,8,"<a href=\"Controllers\/QuoteController.php.html#60\">App\\Http\\Controllers\\QuoteController::index<\/a>"],[0,12,"<a href=\"Controllers\/QuoteController.php.html#250\">App\\Http\\Controllers\\QuoteController::getFilterValues<\/a>"],[0,13,"<a href=\"Controllers\/QuoteController.php.html#339\">App\\Http\\Controllers\\QuoteController::getGroupView<\/a>"],[0,2,"<a href=\"Controllers\/QuoteController.php.html#399\">App\\Http\\Controllers\\QuoteController::workOrderNumberBasedGroupData<\/a>"],[0,2,"<a href=\"Controllers\/QuoteController.php.html#462\">App\\Http\\Controllers\\QuoteController::statusBasedGroupData<\/a>"],[0,3,"<a href=\"Controllers\/QuoteController.php.html#525\">App\\Http\\Controllers\\QuoteController::categoryBasedGroupData<\/a>"],[0,4,"<a href=\"Controllers\/QuoteController.php.html#603\">App\\Http\\Controllers\\QuoteController::assigneeBasedGroupData<\/a>"],[0,4,"<a href=\"Controllers\/QuoteController.php.html#678\">App\\Http\\Controllers\\QuoteController::submittedBasedGroupData<\/a>"],[0,4,"<a href=\"Controllers\/QuoteController.php.html#751\">App\\Http\\Controllers\\QuoteController::tagBasedGroupData<\/a>"],[0,10,"<a href=\"Controllers\/ResidentAvailabilityController.php.html#21\">App\\Http\\Controllers\\ResidentAvailabilityController::store<\/a>"],[0,4,"<a href=\"Controllers\/ResidentAvailabilityController.php.html#101\">App\\Http\\Controllers\\ResidentAvailabilityController::show<\/a>"],[0,1,"<a href=\"Controllers\/RoleController.php.html#32\">App\\Http\\Controllers\\RoleController::index<\/a>"],[0,6,"<a href=\"Controllers\/RoleController.php.html#51\">App\\Http\\Controllers\\RoleController::store<\/a>"],[0,1,"<a href=\"Controllers\/RoleController.php.html#122\">App\\Http\\Controllers\\RoleController::show<\/a>"],[0,9,"<a href=\"Controllers\/RoleController.php.html#136\">App\\Http\\Controllers\\RoleController::update<\/a>"],[0,2,"<a href=\"Controllers\/RoleController.php.html#212\">App\\Http\\Controllers\\RoleController::destroy<\/a>"],[0,2,"<a href=\"Controllers\/RoleController.php.html#232\">App\\Http\\Controllers\\RoleController::listPermissions<\/a>"],[0,1,"<a href=\"Controllers\/RoleController.php.html#259\">App\\Http\\Controllers\\RoleController::hasPermissionsFromInvalidFeatures<\/a>"],[0,1,"<a href=\"Controllers\/SchedulingController.php.html#33\">App\\Http\\Controllers\\SchedulingController::__construct<\/a>"],[0,1,"<a href=\"Controllers\/SchedulingController.php.html#38\">App\\Http\\Controllers\\SchedulingController::index<\/a>"],[0,1,"<a href=\"Controllers\/SchedulingController.php.html#43\">App\\Http\\Controllers\\SchedulingController::create<\/a>"],[0,1,"<a href=\"Controllers\/SchedulingController.php.html#51\">App\\Http\\Controllers\\SchedulingController::show<\/a>"],[0,1,"<a href=\"Controllers\/SchedulingController.php.html#59\">App\\Http\\Controllers\\SchedulingController::edit<\/a>"],[0,1,"<a href=\"Controllers\/SchedulingController.php.html#67\">App\\Http\\Controllers\\SchedulingController::update<\/a>"],[0,1,"<a href=\"Controllers\/SchedulingController.php.html#75\">App\\Http\\Controllers\\SchedulingController::destroy<\/a>"],[0,6,"<a href=\"Controllers\/SchedulingController.php.html#80\">App\\Http\\Controllers\\SchedulingController::getContext<\/a>"],[0,4,"<a href=\"Controllers\/SchedulingController.php.html#126\">App\\Http\\Controllers\\SchedulingController::getVendors<\/a>"],[0,3,"<a href=\"Controllers\/SchedulingController.php.html#176\">App\\Http\\Controllers\\SchedulingController::getTechnicianList<\/a>"],[0,3,"<a href=\"Controllers\/SchedulingController.php.html#204\">App\\Http\\Controllers\\SchedulingController::getTechnicianSchedules<\/a>"],[0,3,"<a href=\"Controllers\/SchedulingController.php.html#234\">App\\Http\\Controllers\\SchedulingController::getVendorAvailability<\/a>"],[0,7,"<a href=\"Controllers\/ServiceRequestActivityLogController.php.html#29\">App\\Http\\Controllers\\ServiceRequestActivityLogController::index<\/a>"],[0,3,"<a href=\"Controllers\/ServiceRequestActivityLogController.php.html#132\">App\\Http\\Controllers\\ServiceRequestActivityLogController::paginate<\/a>"],[0,8,"<a href=\"Controllers\/ServiceRequestAssigneeController.php.html#25\">App\\Http\\Controllers\\ServiceRequestAssigneeController::store<\/a>"],[0,5,"<a href=\"Controllers\/ServiceRequestAssigneeController.php.html#78\">App\\Http\\Controllers\\ServiceRequestAssigneeController::destroy<\/a>"],[0,7,"<a href=\"Controllers\/ServiceRequestController.php.html#98\">App\\Http\\Controllers\\ServiceRequestController::index<\/a>"],[0,8,"<a href=\"Controllers\/ServiceRequestController.php.html#200\">App\\Http\\Controllers\\ServiceRequestController::store<\/a>"],[0,7,"<a href=\"Controllers\/ServiceRequestController.php.html#259\">App\\Http\\Controllers\\ServiceRequestController::show<\/a>"],[0,6,"<a href=\"Controllers\/ServiceRequestController.php.html#595\">App\\Http\\Controllers\\ServiceRequestController::update<\/a>"],[0,2,"<a href=\"Controllers\/ServiceRequestController.php.html#642\">App\\Http\\Controllers\\ServiceRequestController::markAdminAvailabilityViewed<\/a>"],[0,10,"<a href=\"Controllers\/ServiceRequestController.php.html#661\">App\\Http\\Controllers\\ServiceRequestController::getGroupView<\/a>"],[0,10,"<a href=\"Controllers\/ServiceRequestController.php.html#710\">App\\Http\\Controllers\\ServiceRequestController::getFilterValues<\/a>"],[0,6,"<a href=\"Controllers\/ServiceRequestController.php.html#777\">App\\Http\\Controllers\\ServiceRequestController::updateDescription<\/a>"],[0,2,"<a href=\"Controllers\/ServiceRequestController.php.html#853\">App\\Http\\Controllers\\ServiceRequestController::updateAccessMethod<\/a>"],[0,15,"<a href=\"Controllers\/ServiceRequestController.php.html#878\">App\\Http\\Controllers\\ServiceRequestController::createWorkOrder<\/a>"],[0,3,"<a href=\"Controllers\/ServiceRequestController.php.html#986\">App\\Http\\Controllers\\ServiceRequestController::triggerIssueUpdatedEvents<\/a>"],[0,14,"<a href=\"Controllers\/ServiceRequestController.php.html#995\">App\\Http\\Controllers\\ServiceRequestController::markAsComplete<\/a>"],[0,5,"<a href=\"Controllers\/ServiceRequestController.php.html#1113\">App\\Http\\Controllers\\ServiceRequestController::statusBasedGroupData<\/a>"],[0,6,"<a href=\"Controllers\/ServiceRequestController.php.html#1156\">App\\Http\\Controllers\\ServiceRequestController::importedFromBasedGroupData<\/a>"],[0,7,"<a href=\"Controllers\/ServiceRequestController.php.html#1203\">App\\Http\\Controllers\\ServiceRequestController::assigneeBasedGroupData<\/a>"],[0,4,"<a href=\"Controllers\/ServiceRequestController.php.html#1252\">App\\Http\\Controllers\\ServiceRequestController::needToCreateServiceRequestDescription<\/a>"],[0,16,"<a href=\"Controllers\/ServiceRequestMediaController.php.html#44\">App\\Http\\Controllers\\ServiceRequestMediaController::store<\/a>"],[0,6,"<a href=\"Controllers\/ServiceRequestMediaController.php.html#177\">App\\Http\\Controllers\\ServiceRequestMediaController::show<\/a>"],[0,5,"<a href=\"Controllers\/ServiceRequestMediaController.php.html#218\">App\\Http\\Controllers\\ServiceRequestMediaController::destroy<\/a>"],[0,11,"<a href=\"Controllers\/ServiceRequestMediaController.php.html#258\">App\\Http\\Controllers\\ServiceRequestMediaController::uploadOriginalMedia<\/a>"],[0,7,"<a href=\"Controllers\/ServiceRequestNoteController.php.html#38\">App\\Http\\Controllers\\ServiceRequestNoteController::index<\/a>"],[0,4,"<a href=\"Controllers\/ServiceRequestNoteController.php.html#87\">App\\Http\\Controllers\\ServiceRequestNoteController::store<\/a>"],[0,3,"<a href=\"Controllers\/ServiceRequestNoteController.php.html#139\">App\\Http\\Controllers\\ServiceRequestNoteController::update<\/a>"],[0,3,"<a href=\"Controllers\/ServiceRequestNoteController.php.html#187\">App\\Http\\Controllers\\ServiceRequestNoteController::destroy<\/a>"],[0,1,"<a href=\"Controllers\/TagController.php.html#31\">App\\Http\\Controllers\\TagController::index<\/a>"],[0,4,"<a href=\"Controllers\/TagController.php.html#48\">App\\Http\\Controllers\\TagController::store<\/a>"],[0,1,"<a href=\"Controllers\/TagController.php.html#90\">App\\Http\\Controllers\\TagController::show<\/a>"],[0,4,"<a href=\"Controllers\/TagController.php.html#103\">App\\Http\\Controllers\\TagController::update<\/a>"],[0,5,"<a href=\"Controllers\/TagController.php.html#143\">App\\Http\\Controllers\\TagController::destroy<\/a>"],[0,5,"<a href=\"Controllers\/TechnicianAppointmentsController.php.html#44\">App\\Http\\Controllers\\TechnicianAppointmentsController::index<\/a>"],[0,10,"<a href=\"Controllers\/TechnicianAppointmentsController.php.html#142\">App\\Http\\Controllers\\TechnicianAppointmentsController::store<\/a>"],[0,8,"<a href=\"Controllers\/TechnicianAppointmentsController.php.html#225\">App\\Http\\Controllers\\TechnicianAppointmentsController::update<\/a>"],[0,2,"<a href=\"Controllers\/TechnicianAppointmentsController.php.html#299\">App\\Http\\Controllers\\TechnicianAppointmentsController::destroy<\/a>"],[0,3,"<a href=\"Controllers\/TechnicianAppointmentsController.php.html#318\">App\\Http\\Controllers\\TechnicianAppointmentsController::viewTechnicianCalendar<\/a>"],[0,3,"<a href=\"Controllers\/TechnicianAppointmentsController.php.html#387\">App\\Http\\Controllers\\TechnicianAppointmentsController::getAvailabilityDates<\/a>"],[0,7,"<a href=\"Controllers\/TechnicianController.php.html#31\">App\\Http\\Controllers\\TechnicianController::updateWorkingHours<\/a>"],[0,4,"<a href=\"Controllers\/TechnicianController.php.html#97\">App\\Http\\Controllers\\TechnicianController::importWorkingHours<\/a>"],[0,15,"<a href=\"Controllers\/TechnicianController.php.html#130\">App\\Http\\Controllers\\TechnicianController::updateTechnicianSkills<\/a>"],[0,4,"<a href=\"Controllers\/TechnicianController.php.html#272\">App\\Http\\Controllers\\TechnicianController::importTechnicianSkills<\/a>"],[0,1,"<a href=\"Controllers\/UserController.php.html#70\">App\\Http\\Controllers\\UserController::__construct<\/a>"],[0,4,"<a href=\"Controllers\/UserController.php.html#84\">App\\Http\\Controllers\\UserController::index<\/a>"],[0,9,"<a href=\"Controllers\/UserController.php.html#129\">App\\Http\\Controllers\\UserController::store<\/a>"],[0,1,"<a href=\"Controllers\/UserController.php.html#265\">App\\Http\\Controllers\\UserController::show<\/a>"],[0,10,"<a href=\"Controllers\/UserController.php.html#281\">App\\Http\\Controllers\\UserController::update<\/a>"],[0,7,"<a href=\"Controllers\/UserController.php.html#395\">App\\Http\\Controllers\\UserController::destroy<\/a>"],[0,5,"<a href=\"Controllers\/UserController.php.html#450\">App\\Http\\Controllers\\UserController::filters<\/a>"],[0,8,"<a href=\"Controllers\/UserController.php.html#492\">App\\Http\\Controllers\\UserController::updateCognitoUserDetails<\/a>"],[0,2,"<a href=\"Controllers\/UserController.php.html#551\">App\\Http\\Controllers\\UserController::updateNotificationSubscription<\/a>"],[0,12,"<a href=\"Controllers\/UserController.php.html#580\">App\\Http\\Controllers\\UserController::updateUserStatus<\/a>"],[0,6,"<a href=\"Controllers\/UserController.php.html#661\">App\\Http\\Controllers\\UserController::resetUserPassword<\/a>"],[0,9,"<a href=\"Controllers\/UserController.php.html#700\">App\\Http\\Controllers\\UserController::getGroupView<\/a>"],[0,5,"<a href=\"Controllers\/UserController.php.html#748\">App\\Http\\Controllers\\UserController::statusBasedGroupData<\/a>"],[0,6,"<a href=\"Controllers\/UserController.php.html#801\">App\\Http\\Controllers\\UserController::roleBasedGroupData<\/a>"],[98.14814814814815,8,"<a href=\"Controllers\/Vendor\/VendorOnboardingController.php.html#50\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::sendVendorOnboardingLink<\/a>"],[98.11320754716981,8,"<a href=\"Controllers\/Vendor\/VendorOnboardingController.php.html#141\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setBasicInfo<\/a>"],[95,5,"<a href=\"Controllers\/Vendor\/VendorOnboardingController.php.html#222\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::getVendorOnboarding<\/a>"],[98.18181818181819,6,"<a href=\"Controllers\/Vendor\/VendorOnboardingController.php.html#257\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::getStatus<\/a>"],[92.85714285714286,14,"<a href=\"Controllers\/Vendor\/VendorOnboardingController.php.html#331\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setStatus<\/a>"],[96.7741935483871,6,"<a href=\"Controllers\/Vendor\/VendorOnboardingController.php.html#407\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setServices<\/a>"],[97.43589743589743,6,"<a href=\"Controllers\/Vendor\/VendorOnboardingController.php.html#467\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setServiceAreas<\/a>"],[55.88235294117647,6,"<a href=\"Controllers\/Vendor\/VendorOnboardingController.php.html#535\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::processServices<\/a>"],[100,1,"<a href=\"Controllers\/Vendor\/VendorOnboardingController.php.html#585\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::minutesToDaysSimple<\/a>"],[75,4,"<a href=\"Controllers\/Vendor\/VendorOnboardingLookupController.php.html#25\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getLookups<\/a>"],[100,1,"<a href=\"Controllers\/Vendor\/VendorOnboardingLookupController.php.html#50\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getProblemCategory<\/a>"],[0,2,"<a href=\"Controllers\/Vendor\/VendorOnboardingLookupController.php.html#74\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getLocationApiKey<\/a>"],[100,1,"<a href=\"Controllers\/Vendor\/VendorOnboardingLookupController.php.html#87\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getOnboardingStatuses<\/a>"],[100,1,"<a href=\"Controllers\/Vendor\/VendorOnboardingLookupController.php.html#96\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getStates<\/a>"],[88,3,"<a href=\"Controllers\/Vendor\/WorkOrderController.php.html#35\">App\\Http\\Controllers\\Vendor\\WorkOrderController::index<\/a>"],[66.66666666666666,3,"<a href=\"Controllers\/Vendor\/WorkOrderController.php.html#77\">App\\Http\\Controllers\\Vendor\\WorkOrderController::show<\/a>"],[100,1,"<a href=\"Controllers\/Vendor\/WorkOrderController.php.html#100\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getWorkOrderRelations<\/a>"],[100,1,"<a href=\"Controllers\/Vendor\/WorkOrderController.php.html#155\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getWorkOrderSelectFields<\/a>"],[100,1,"<a href=\"Controllers\/Vendor\/WorkOrderController.php.html#186\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getTabTotalCounts<\/a>"],[60,2,"<a href=\"Controllers\/Vendor\/WorkOrderController.php.html#220\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getSkippedMediaTypes<\/a>"],[100,1,"<a href=\"Controllers\/Vendor\/WorkOrderController.php.html#235\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getShowEagerLoadRelations<\/a>"],[0,1,"<a href=\"Controllers\/VendorController.php.html#40\">App\\Http\\Controllers\\VendorController::__construct<\/a>"],[0,4,"<a href=\"Controllers\/VendorController.php.html#54\">App\\Http\\Controllers\\VendorController::index<\/a>"],[0,10,"<a href=\"Controllers\/VendorController.php.html#99\">App\\Http\\Controllers\\VendorController::store<\/a>"],[0,4,"<a href=\"Controllers\/VendorController.php.html#234\">App\\Http\\Controllers\\VendorController::show<\/a>"],[0,3,"<a href=\"Controllers\/VendorController.php.html#276\">App\\Http\\Controllers\\VendorController::storeVendor<\/a>"],[0,6,"<a href=\"Controllers\/ViewController.php.html#50\">App\\Http\\Controllers\\ViewController::index<\/a>"],[0,5,"<a href=\"Controllers\/ViewController.php.html#117\">App\\Http\\Controllers\\ViewController::store<\/a>"],[0,3,"<a href=\"Controllers\/ViewController.php.html#168\">App\\Http\\Controllers\\ViewController::update<\/a>"],[0,5,"<a href=\"Controllers\/ViewController.php.html#196\">App\\Http\\Controllers\\ViewController::destroy<\/a>"],[0,5,"<a href=\"Controllers\/ViewController.php.html#247\">App\\Http\\Controllers\\ViewController::setAsDefault<\/a>"],[0,3,"<a href=\"Controllers\/ViewController.php.html#297\">App\\Http\\Controllers\\ViewController::pinView<\/a>"],[0,3,"<a href=\"Controllers\/ViewController.php.html#331\">App\\Http\\Controllers\\ViewController::rename<\/a>"],[0,5,"<a href=\"Controllers\/ViewController.php.html#355\">App\\Http\\Controllers\\ViewController::duplicate<\/a>"],[0,6,"<a href=\"Controllers\/ViewController.php.html#404\">App\\Http\\Controllers\\ViewController::getViewConfig<\/a>"],[0,11,"<a href=\"Controllers\/ViewController.php.html#446\">App\\Http\\Controllers\\ViewController::getViewCount<\/a>"],[0,1,"<a href=\"Controllers\/ViewController.php.html#537\">App\\Http\\Controllers\\ViewController::workOrderViewType<\/a>"],[0,1,"<a href=\"Controllers\/WebhookCallsController.php.html#14\">App\\Http\\Controllers\\WebhookCallsController::index<\/a>"],[0,1,"<a href=\"Controllers\/WebhookCallsController.php.html#22\">App\\Http\\Controllers\\WebhookCallsController::show<\/a>"],[0,5,"<a href=\"Controllers\/WebhookController.php.html#36\">App\\Http\\Controllers\\WebhookController::__invoke<\/a>"],[0,1,"<a href=\"Controllers\/WebhookController.php.html#99\">App\\Http\\Controllers\\WebhookController::invalidAction<\/a>"],[0,4,"<a href=\"Controllers\/WebhookController.php.html#112\">App\\Http\\Controllers\\WebhookController::validateQuoteApprovePayload<\/a>"],[0,4,"<a href=\"Controllers\/WebhookController.php.html#151\">App\\Http\\Controllers\\WebhookController::validateQuoteRejectPayload<\/a>"],[0,4,"<a href=\"Controllers\/WebhookController.php.html#179\">App\\Http\\Controllers\\WebhookController::validateQuoteExpirePayload<\/a>"],[0,4,"<a href=\"Controllers\/WebhookController.php.html#207\">App\\Http\\Controllers\\WebhookController::validateQuoteUpdatePayload<\/a>"],[0,5,"<a href=\"Controllers\/WebhookController.php.html#261\">App\\Http\\Controllers\\WebhookController::validateQuoteRestorePayload<\/a>"],[0,4,"<a href=\"Controllers\/WebhookController.php.html#289\">App\\Http\\Controllers\\WebhookController::validateQuoteSubmittedForApprovalPayload<\/a>"],[0,6,"<a href=\"Controllers\/WorkOrder\/WorkOrderIssueController.php.html#22\">App\\Http\\Controllers\\WorkOrder\\WorkOrderIssueController::show<\/a>"],[0,6,"<a href=\"Controllers\/WorkOrderActivityLogController.php.html#27\">App\\Http\\Controllers\\WorkOrderActivityLogController::index<\/a>"],[0,3,"<a href=\"Controllers\/WorkOrderActivityLogController.php.html#99\">App\\Http\\Controllers\\WorkOrderActivityLogController::paginate<\/a>"],[0,8,"<a href=\"Controllers\/WorkOrderAssigneeController.php.html#25\">App\\Http\\Controllers\\WorkOrderAssigneeController::store<\/a>"],[0,5,"<a href=\"Controllers\/WorkOrderAssigneeController.php.html#88\">App\\Http\\Controllers\\WorkOrderAssigneeController::destroy<\/a>"],[0,4,"<a href=\"Controllers\/WorkOrderController.php.html#124\">App\\Http\\Controllers\\WorkOrderController::index<\/a>"],[0,8,"<a href=\"Controllers\/WorkOrderController.php.html#168\">App\\Http\\Controllers\\WorkOrderController::store<\/a>"],[0,6,"<a href=\"Controllers\/WorkOrderController.php.html#226\">App\\Http\\Controllers\\WorkOrderController::show<\/a>"],[0,6,"<a href=\"Controllers\/WorkOrderController.php.html#274\">App\\Http\\Controllers\\WorkOrderController::update<\/a>"],[0,3,"<a href=\"Controllers\/WorkOrderController.php.html#321\">App\\Http\\Controllers\\WorkOrderController::destroy<\/a>"],[0,15,"<a href=\"Controllers\/WorkOrderController.php.html#350\">App\\Http\\Controllers\\WorkOrderController::getGroupView<\/a>"],[0,20,"<a href=\"Controllers\/WorkOrderController.php.html#416\">App\\Http\\Controllers\\WorkOrderController::getFilterValues<\/a>"],[0,3,"<a href=\"Controllers\/WorkOrderController.php.html#566\">App\\Http\\Controllers\\WorkOrderController::updatePriority<\/a>"],[0,2,"<a href=\"Controllers\/WorkOrderController.php.html#608\">App\\Http\\Controllers\\WorkOrderController::updateDueDate<\/a>"],[0,2,"<a href=\"Controllers\/WorkOrderController.php.html#633\">App\\Http\\Controllers\\WorkOrderController::deleteDueDate<\/a>"],[0,4,"<a href=\"Controllers\/WorkOrderController.php.html#657\">App\\Http\\Controllers\\WorkOrderController::updateAccessMethod<\/a>"],[0,6,"<a href=\"Controllers\/WorkOrderController.php.html#697\">App\\Http\\Controllers\\WorkOrderController::updateResidentInfo<\/a>"],[0,7,"<a href=\"Controllers\/WorkOrderController.php.html#763\">App\\Http\\Controllers\\WorkOrderController::updatePropertyAddress<\/a>"],[0,4,"<a href=\"Controllers\/WorkOrderController.php.html#831\">App\\Http\\Controllers\\WorkOrderController::getCount<\/a>"],[0,1,"<a href=\"Controllers\/WorkOrderController.php.html#867\">App\\Http\\Controllers\\WorkOrderController::handleIndexException<\/a>"],[0,11,"<a href=\"Controllers\/WorkOrderController.php.html#893\">App\\Http\\Controllers\\WorkOrderController::statusBasedGroupData<\/a>"],[0,9,"<a href=\"Controllers\/WorkOrderController.php.html#961\">App\\Http\\Controllers\\WorkOrderController::priorityBasedGroupData<\/a>"],[0,7,"<a href=\"Controllers\/WorkOrderController.php.html#1022\">App\\Http\\Controllers\\WorkOrderController::categoryBasedGroupData<\/a>"],[0,8,"<a href=\"Controllers\/WorkOrderController.php.html#1092\">App\\Http\\Controllers\\WorkOrderController::assigneeBasedGroupData<\/a>"],[0,8,"<a href=\"Controllers\/WorkOrderController.php.html#1153\">App\\Http\\Controllers\\WorkOrderController::tagBasedGroupData<\/a>"],[0,8,"<a href=\"Controllers\/WorkOrderController.php.html#1214\">App\\Http\\Controllers\\WorkOrderController::healthScoreBaseGroupData<\/a>"],[0,8,"<a href=\"Controllers\/WorkOrderController.php.html#1273\">App\\Http\\Controllers\\WorkOrderController::technicianBasedGroupData<\/a>"],[0,4,"<a href=\"Controllers\/WorkOrderController.php.html#1330\">App\\Http\\Controllers\\WorkOrderController::tripSummary<\/a>"],[0,2,"<a href=\"Controllers\/WorkOrderController.php.html#1382\">App\\Http\\Controllers\\WorkOrderController::getTripDetails<\/a>"],[0,14,"<a href=\"Controllers\/WorkOrderController.php.html#1431\">App\\Http\\Controllers\\WorkOrderController::markAsComplete<\/a>"],[0,1,"<a href=\"Controllers\/WorkOrderController.php.html#1545\">App\\Http\\Controllers\\WorkOrderController::loadWorkOrderRelationsForWeb<\/a>"],[0,1,"<a href=\"Controllers\/WorkOrderController.php.html#1666\">App\\Http\\Controllers\\WorkOrderController::getWorkOrderRelations<\/a>"],[0,1,"<a href=\"Controllers\/WorkOrderController.php.html#1704\">App\\Http\\Controllers\\WorkOrderController::getWorkOrderSelectFields<\/a>"],[0,1,"<a href=\"Controllers\/WorkOrderController.php.html#1827\">App\\Http\\Controllers\\WorkOrderController::getServiceCallSelectFieldsForApp<\/a>"],[0,1,"<a href=\"Controllers\/WorkOrderController.php.html#1860\">App\\Http\\Controllers\\WorkOrderController::getServiceCallRelationsForApp<\/a>"],[0,2,"<a href=\"Controllers\/WorkOrderController.php.html#1875\">App\\Http\\Controllers\\WorkOrderController::shouldListForTechnicianMobile<\/a>"],[0,1,"<a href=\"Controllers\/WorkOrderController.php.html#1885\">App\\Http\\Controllers\\WorkOrderController::listWorkOrdersForTechnicianMobile<\/a>"],[0,2,"<a href=\"Controllers\/WorkOrderController.php.html#1933\">App\\Http\\Controllers\\WorkOrderController::getWorkOrderRelationsForMobile<\/a>"],[0,2,"<a href=\"Controllers\/WorkOrderController.php.html#1981\">App\\Http\\Controllers\\WorkOrderController::getServiceRequestColumnForApp<\/a>"],[0,1,"<a href=\"Controllers\/WorkOrderController.php.html#2005\">App\\Http\\Controllers\\WorkOrderController::getServiceRequestRelationsForApp<\/a>"],[0,1,"<a href=\"Controllers\/WorkOrderController.php.html#2017\">App\\Http\\Controllers\\WorkOrderController::getWorkOrderIssuesColumnForApp<\/a>"],[0,2,"<a href=\"Controllers\/WorkOrderController.php.html#2032\">App\\Http\\Controllers\\WorkOrderController::getWorkOrderIssuesRelationsForApp<\/a>"],[0,20,"<a href=\"Controllers\/WorkOrderMediaController.php.html#48\">App\\Http\\Controllers\\WorkOrderMediaController::store<\/a>"],[0,6,"<a href=\"Controllers\/WorkOrderMediaController.php.html#199\">App\\Http\\Controllers\\WorkOrderMediaController::show<\/a>"],[0,5,"<a href=\"Controllers\/WorkOrderMediaController.php.html#240\">App\\Http\\Controllers\\WorkOrderMediaController::destroy<\/a>"],[0,11,"<a href=\"Controllers\/WorkOrderMediaController.php.html#282\">App\\Http\\Controllers\\WorkOrderMediaController::uploadOriginalMedia<\/a>"],[0,7,"<a href=\"Controllers\/WorkOrderMediaController.php.html#353\">App\\Http\\Controllers\\WorkOrderMediaController::validateMediaLimit<\/a>"],[0,5,"<a href=\"Controllers\/WorkOrderNoteController.php.html#34\">App\\Http\\Controllers\\WorkOrderNoteController::index<\/a>"],[0,5,"<a href=\"Controllers\/WorkOrderNoteController.php.html#74\">App\\Http\\Controllers\\WorkOrderNoteController::store<\/a>"],[0,6,"<a href=\"Controllers\/WorkOrderNoteController.php.html#128\">App\\Http\\Controllers\\WorkOrderNoteController::update<\/a>"],[0,6,"<a href=\"Controllers\/WorkOrderNoteController.php.html#186\">App\\Http\\Controllers\\WorkOrderNoteController::destroy<\/a>"],[100,1,"<a href=\"Filters\/QueryFilter.php.html#36\">App\\Http\\Filters\\QueryFilter::__construct<\/a>"],[100,6,"<a href=\"Filters\/QueryFilter.php.html#49\">App\\Http\\Filters\\QueryFilter::apply<\/a>"],[100,1,"<a href=\"Filters\/QueryFilter.php.html#75\">App\\Http\\Filters\\QueryFilter::filters<\/a>"],[88.88888888888889,2,"<a href=\"Filters\/QueryFilter.php.html#84\">App\\Http\\Filters\\QueryFilter::sort<\/a>"],[0,1,"<a href=\"Filters\/QueryFilter.php.html#99\">App\\Http\\Filters\\QueryFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"Filters\/QuoteListFilter.php.html#34\">App\\Http\\Filters\\QuoteListFilter::__construct<\/a>"],[0,1,"<a href=\"Filters\/QuoteListFilter.php.html#42\">App\\Http\\Filters\\QuoteListFilter::search<\/a>"],[0,1,"<a href=\"Filters\/QuoteListFilter.php.html#60\">App\\Http\\Filters\\QuoteListFilter::workOrderId<\/a>"],[0,4,"<a href=\"Filters\/QuoteListFilter.php.html#65\">App\\Http\\Filters\\QuoteListFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"Filters\/QuoteListFilter.php.html#95\">App\\Http\\Filters\\QuoteListFilter::filter<\/a>"],[0,1,"<a href=\"Filters\/RoleListFilter.php.html#20\">App\\Http\\Filters\\RoleListFilter::search<\/a>"],[0,2,"<a href=\"Filters\/RoleListFilter.php.html#28\">App\\Http\\Filters\\RoleListFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"Filters\/ServiceRequestListFilter.php.html#29\">App\\Http\\Filters\\ServiceRequestListFilter::__construct<\/a>"],[0,1,"<a href=\"Filters\/ServiceRequestListFilter.php.html#34\">App\\Http\\Filters\\ServiceRequestListFilter::search<\/a>"],[0,1,"<a href=\"Filters\/ServiceRequestListFilter.php.html#49\">App\\Http\\Filters\\ServiceRequestListFilter::serviceRequestId<\/a>"],[0,5,"<a href=\"Filters\/ServiceRequestListFilter.php.html#54\">App\\Http\\Filters\\ServiceRequestListFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"Filters\/ServiceRequestListFilter.php.html#84\">App\\Http\\Filters\\ServiceRequestListFilter::filter<\/a>"],[0,1,"<a href=\"Filters\/TagListFilter.php.html#22\">App\\Http\\Filters\\TagListFilter::search<\/a>"],[0,2,"<a href=\"Filters\/TagListFilter.php.html#32\">App\\Http\\Filters\\TagListFilter::workOrderId<\/a>"],[0,2,"<a href=\"Filters\/TagListFilter.php.html#44\">App\\Http\\Filters\\TagListFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"Filters\/UserListFilter.php.html#33\">App\\Http\\Filters\\UserListFilter::__construct<\/a>"],[0,1,"<a href=\"Filters\/UserListFilter.php.html#41\">App\\Http\\Filters\\UserListFilter::search<\/a>"],[0,4,"<a href=\"Filters\/UserListFilter.php.html#50\">App\\Http\\Filters\\UserListFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"Filters\/UserListFilter.php.html#80\">App\\Http\\Filters\\UserListFilter::filter<\/a>"],[0,1,"<a href=\"Filters\/VendorListFilter.php.html#10\">App\\Http\\Filters\\VendorListFilter::search<\/a>"],[100,1,"<a href=\"Filters\/WorkOrderListFilter.php.html#40\">App\\Http\\Filters\\WorkOrderListFilter::__construct<\/a>"],[100,1,"<a href=\"Filters\/WorkOrderListFilter.php.html#48\">App\\Http\\Filters\\WorkOrderListFilter::search<\/a>"],[0,1,"<a href=\"Filters\/WorkOrderListFilter.php.html#67\">App\\Http\\Filters\\WorkOrderListFilter::workOrderId<\/a>"],[42.10526315789473,14,"<a href=\"Filters\/WorkOrderListFilter.php.html#72\">App\\Http\\Filters\\WorkOrderListFilter::prepareForFiltering<\/a>"],[100,1,"<a href=\"Filters\/WorkOrderListFilter.php.html#119\">App\\Http\\Filters\\WorkOrderListFilter::filter<\/a>"],[50,5,"<a href=\"Middleware\/AppAccessPermissionMiddleware.php.html#17\">App\\Http\\Middleware\\AppAccessPermissionMiddleware::handle<\/a>"],[0,2,"<a href=\"Middleware\/Authenticate.php.html#13\">App\\Http\\Middleware\\Authenticate::redirectTo<\/a>"],[0,6,"<a href=\"Middleware\/HorizonBasicAuth.php.html#15\">App\\Http\\Middleware\\HorizonBasicAuth::handle<\/a>"],[0,1,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#67\">App\\Http\\Middleware\\LogRequestsToDatabase::given<\/a>"],[100,1,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#77\">App\\Http\\Middleware\\LogRequestsToDatabase::handle<\/a>"],[8.823529411764707,7,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#85\">App\\Http\\Middleware\\LogRequestsToDatabase::terminate<\/a>"],[0,1,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#135\">App\\Http\\Middleware\\LogRequestsToDatabase::contentWithinLimits<\/a>"],[0,1,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#147\">App\\Http\\Middleware\\LogRequestsToDatabase::headers<\/a>"],[0,1,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#162\">App\\Http\\Middleware\\LogRequestsToDatabase::payload<\/a>"],[0,3,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#175\">App\\Http\\Middleware\\LogRequestsToDatabase::hideParameters<\/a>"],[0,12,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#191\">App\\Http\\Middleware\\LogRequestsToDatabase::response<\/a>"],[0,3,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#234\">App\\Http\\Middleware\\LogRequestsToDatabase::extractDataFromView<\/a>"],[0,4,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#255\">App\\Http\\Middleware\\LogRequestsToDatabase::shouldIgnoreUnauthenticated<\/a>"],[0,5,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#270\">App\\Http\\Middleware\\LogRequestsToDatabase::inExceptArray<\/a>"],[0,1,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#288\">App\\Http\\Middleware\\LogRequestsToDatabase::shouldIgnoreRoute<\/a>"],[0,2,"<a href=\"Middleware\/LogRequestsToDatabase.php.html#298\">App\\Http\\Middleware\\LogRequestsToDatabase::input<\/a>"],[0,2,"<a href=\"Middleware\/MakeTusMediaFileName.php.html#17\">App\\Http\\Middleware\\MakeTusMediaFileName::handle<\/a>"],[0,5,"<a href=\"Middleware\/MakeTusMediaFileName.php.html#28\">App\\Http\\Middleware\\MakeTusMediaFileName::setNameInUploadMetaRequestHeader<\/a>"],[0,6,"<a href=\"Middleware\/MakeTusMediaFileName.php.html#58\">App\\Http\\Middleware\\MakeTusMediaFileName::fileNameResolver<\/a>"],[43.75,4,"<a href=\"Middleware\/MinimumAppVeriosnRequiredMiddleware.php.html#19\">App\\Http\\Middleware\\MinimumAppVeriosnRequiredMiddleware::handle<\/a>"],[0,4,"<a href=\"Middleware\/RedirectIfAuthenticated.php.html#17\">App\\Http\\Middleware\\RedirectIfAuthenticated::handle<\/a>"],[0,1,"<a href=\"Middleware\/TrustHosts.php.html#14\">App\\Http\\Middleware\\TrustHosts::hosts<\/a>"],[62,12,"<a href=\"Middleware\/ValidateCognitoToken.php.html#27\">App\\Http\\Middleware\\ValidateCognitoToken::handle<\/a>"],[100,1,"<a href=\"Middleware\/ValidateCognitoToken.php.html#111\">App\\Http\\Middleware\\ValidateCognitoToken::updateLastActivity<\/a>"],[0,1,"<a href=\"Requests\/Auth\/PublicApiAuthenticateRequest.php.html#14\">App\\Http\\Requests\\Auth\\PublicApiAuthenticateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Auth\/SearchAddressRequest.php.html#9\">App\\Http\\Requests\\Auth\\SearchAddressRequest::authorize<\/a>"],[0,1,"<a href=\"Requests\/Auth\/SearchAddressRequest.php.html#19\">App\\Http\\Requests\\Auth\\SearchAddressRequest::rules<\/a>"],[100,1,"<a href=\"Requests\/Auth\/SetVendorPasswordRequest.php.html#9\">App\\Http\\Requests\\Auth\\SetVendorPasswordRequest::authorize<\/a>"],[100,1,"<a href=\"Requests\/Auth\/SetVendorPasswordRequest.php.html#19\">App\\Http\\Requests\\Auth\\SetVendorPasswordRequest::rules<\/a>"],[100,1,"<a href=\"Requests\/BaseApiRequest.php.html#12\">App\\Http\\Requests\\BaseApiRequest::authorize<\/a>"],[0,1,"<a href=\"Requests\/Invoice\/InvoiceRequest.php.html#31\">App\\Http\\Requests\\Invoice\\InvoiceRequest::rules<\/a>"],[0,51,"<a href=\"Requests\/Invoice\/InvoiceRequest.php.html#162\">App\\Http\\Requests\\Invoice\\InvoiceRequest::after<\/a>"],[0,1,"<a href=\"Requests\/Issue\/AssignRequest.php.html#16\">App\\Http\\Requests\\Issue\\AssignRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Issue\/CreateIssueRequest.php.html#18\">App\\Http\\Requests\\Issue\\CreateIssueRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Issue\/UnassignRequest.php.html#16\">App\\Http\\Requests\\Issue\\UnassignRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Organization\/UpdateRequest.php.html#17\">App\\Http\\Requests\\Organization\\UpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Quote\/ListRequest.php.html#14\">App\\Http\\Requests\\Quote\\ListRequest::rules<\/a>"],[0,6,"<a href=\"Requests\/Quote\/ListRequest.php.html#34\">App\\Http\\Requests\\Quote\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Requests\/Schedule\/GetTechnicianListRequest.php.html#17\">App\\Http\\Requests\\Schedule\\GetTechnicianListRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Schedule\/ReScheduleAppointmentRequest.php.html#21\">App\\Http\\Requests\\Schedule\\ReScheduleAppointmentRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Schedule\/StoreAppointmentRequest.php.html#17\">App\\Http\\Requests\\Schedule\\StoreAppointmentRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/AccessMethodUpdateRequest.php.html#16\">App\\Http\\Requests\\ServiceRequest\\AccessMethodUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/DescriptionUpdateRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\DescriptionUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/FilterValuesRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\FilterValuesRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/GroupViewRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\GroupViewRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/ListRequest.php.html#13\">App\\Http\\Requests\\ServiceRequest\\ListRequest::authorize<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/ListRequest.php.html#23\">App\\Http\\Requests\\ServiceRequest\\ListRequest::rules<\/a>"],[0,6,"<a href=\"Requests\/ServiceRequest\/ListRequest.php.html#43\">App\\Http\\Requests\\ServiceRequest\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/Media\/MediaUploadRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\Media\\MediaUploadRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/Media\/ThumbnailUploadRequest.php.html#15\">App\\Http\\Requests\\ServiceRequest\\Media\\ThumbnailUploadRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/Note\/CreateRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\Note\\CreateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/Note\/UpdateRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\Note\\UpdateRequest::rules<\/a>"],[100,1,"<a href=\"Requests\/ServiceRequest\/PriorityUpdateRequest.php.html#16\">App\\Http\\Requests\\ServiceRequest\\PriorityUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/PropertyAddressUpdateRequest.php.html#17\">App\\Http\\Requests\\ServiceRequest\\PropertyAddressUpdateRequest::rules<\/a>"],[0,4,"<a href=\"Requests\/ServiceRequest\/ResidentAvailabilityRequest.php.html#22\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/ResidentAvailabilityRequest.php.html#59\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::attributes<\/a>"],[0,2,"<a href=\"Requests\/ServiceRequest\/ResidentAvailabilityRequest.php.html#72\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::after<\/a>"],[0,7,"<a href=\"Requests\/ServiceRequest\/ResidentAvailabilityRequest.php.html#94\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/ResidentUpdateRequest.php.html#17\">App\\Http\\Requests\\ServiceRequest\\ResidentUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/StoreRequest.php.html#20\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/StoreRequest.php.html#81\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::attributes<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/StoreRequest.php.html#128\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::messages<\/a>"],[0,4,"<a href=\"Requests\/ServiceRequest\/StoreRequest.php.html#152\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/StoreWorkOrderRequest.php.html#20\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/StoreWorkOrderRequest.php.html#51\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::attributes<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/StoreWorkOrderRequest.php.html#87\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::messages<\/a>"],[0,3,"<a href=\"Requests\/ServiceRequest\/StoreWorkOrderRequest.php.html#112\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/StoreWorkOrderRequest.php.html#138\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::propertyRules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/StoreWorkOrderRequest.php.html#169\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::residentRules<\/a>"],[0,1,"<a href=\"Requests\/ServiceRequest\/UpdateAvailabilityRequest.php.html#19\">App\\Http\\Requests\\ServiceRequest\\UpdateAvailabilityRequest::rules<\/a>"],[0,2,"<a href=\"Requests\/ServiceRequest\/UpdateAvailabilityRequest.php.html#43\">App\\Http\\Requests\\ServiceRequest\\UpdateAvailabilityRequest::after<\/a>"],[0,1,"<a href=\"Requests\/Tag\/StoreRequest.php.html#14\">App\\Http\\Requests\\Tag\\StoreRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Tag\/StoreRequest.php.html#31\">App\\Http\\Requests\\Tag\\StoreRequest::messages<\/a>"],[0,1,"<a href=\"Requests\/Tag\/UpdateRequest.php.html#14\">App\\Http\\Requests\\Tag\\UpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Tag\/UpdateRequest.php.html#33\">App\\Http\\Requests\\Tag\\UpdateRequest::messages<\/a>"],[0,1,"<a href=\"Requests\/Technician\/AvailabilityDatesRequest.php.html#14\">App\\Http\\Requests\\Technician\\AvailabilityDatesRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Technician\/BlockOutUpdateRequest.php.html#21\">App\\Http\\Requests\\Technician\\BlockOutUpdateRequest::rules<\/a>"],[0,7,"<a href=\"Requests\/Technician\/BlockOutUpdateRequest.php.html#41\">App\\Http\\Requests\\Technician\\BlockOutUpdateRequest::after<\/a>"],[0,1,"<a href=\"Requests\/Technician\/ScheduleWorkOrderRequest.php.html#15\">App\\Http\\Requests\\Technician\\ScheduleWorkOrderRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Technician\/SkillsUpdateRequest.php.html#19\">App\\Http\\Requests\\Technician\\SkillsUpdateRequest::rules<\/a>"],[0,7,"<a href=\"Requests\/Technician\/SkillsUpdateRequest.php.html#35\">App\\Http\\Requests\\Technician\\SkillsUpdateRequest::after<\/a>"],[0,1,"<a href=\"Requests\/Technician\/StoreBlockOutRequest.php.html#21\">App\\Http\\Requests\\Technician\\StoreBlockOutRequest::rules<\/a>"],[0,4,"<a href=\"Requests\/Technician\/StoreBlockOutRequest.php.html#41\">App\\Http\\Requests\\Technician\\StoreBlockOutRequest::after<\/a>"],[0,1,"<a href=\"Requests\/Technician\/WorkingHoursUpdateRequest.php.html#17\">App\\Http\\Requests\\Technician\\WorkingHoursUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Technician\/WorkingHoursUpdateRequest.php.html#33\">App\\Http\\Requests\\Technician\\WorkingHoursUpdateRequest::messages<\/a>"],[0,12,"<a href=\"Requests\/Technician\/WorkingHoursUpdateRequest.php.html#52\">App\\Http\\Requests\\Technician\\WorkingHoursUpdateRequest::after<\/a>"],[0,1,"<a href=\"Requests\/User\/FilterValuesRequest.php.html#14\">App\\Http\\Requests\\User\\FilterValuesRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/User\/GroupViewRequest.php.html#14\">App\\Http\\Requests\\User\\GroupViewRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/User\/ListRequest.php.html#14\">App\\Http\\Requests\\User\\ListRequest::rules<\/a>"],[0,6,"<a href=\"Requests\/User\/ListRequest.php.html#33\">App\\Http\\Requests\\User\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Requests\/User\/StoreRequest.php.html#18\">App\\Http\\Requests\\User\\StoreRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/User\/StoreVendorUserRequest.php.html#16\">App\\Http\\Requests\\User\\StoreVendorUserRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/User\/UpdateRequest.php.html#24\">App\\Http\\Requests\\User\\UpdateRequest::rules<\/a>"],[0,4,"<a href=\"Requests\/User\/UpdateRequest.php.html#79\">App\\Http\\Requests\\User\\UpdateRequest::after<\/a>"],[100,1,"<a href=\"Requests\/Vendor\/ListRequest.php.html#14\">App\\Http\\Requests\\Vendor\\ListRequest::rules<\/a>"],[66.66666666666666,5,"<a href=\"Requests\/Vendor\/ListRequest.php.html#34\">App\\Http\\Requests\\Vendor\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Requests\/Vendor\/StoreRequest.php.html#16\">App\\Http\\Requests\\Vendor\\StoreRequest::rules<\/a>"],[100,1,"<a href=\"Requests\/Vendor\/VendorOnBoardingSetServiceAreasRequest.php.html#9\">App\\Http\\Requests\\Vendor\\VendorOnBoardingSetServiceAreasRequest::authorize<\/a>"],[100,1,"<a href=\"Requests\/Vendor\/VendorOnBoardingSetServiceAreasRequest.php.html#19\">App\\Http\\Requests\\Vendor\\VendorOnBoardingSetServiceAreasRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Vendor\/VendorOnboardingGenerateSignedUrlRequest.php.html#9\">App\\Http\\Requests\\Vendor\\VendorOnboardingGenerateSignedUrlRequest::authorize<\/a>"],[0,1,"<a href=\"Requests\/Vendor\/VendorOnboardingGenerateSignedUrlRequest.php.html#19\">App\\Http\\Requests\\Vendor\\VendorOnboardingGenerateSignedUrlRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/Vendor\/VendorOnboardingInitialRequest.php.html#11\">App\\Http\\Requests\\Vendor\\VendorOnboardingInitialRequest::authorize<\/a>"],[0,1,"<a href=\"Requests\/Vendor\/VendorOnboardingInitialRequest.php.html#21\">App\\Http\\Requests\\Vendor\\VendorOnboardingInitialRequest::rules<\/a>"],[100,1,"<a href=\"Requests\/Vendor\/VendorOnboardingSetBasicInfoRequest.php.html#12\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetBasicInfoRequest::authorize<\/a>"],[100,1,"<a href=\"Requests\/Vendor\/VendorOnboardingSetBasicInfoRequest.php.html#22\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetBasicInfoRequest::rules<\/a>"],[100,1,"<a href=\"Requests\/Vendor\/VendorOnboardingSetServicesRequest.php.html#9\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetServicesRequest::authorize<\/a>"],[100,1,"<a href=\"Requests\/Vendor\/VendorOnboardingSetServicesRequest.php.html#19\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetServicesRequest::rules<\/a>"],[100,1,"<a href=\"Requests\/Vendor\/VendorOnboardingSetStatusRequest.php.html#9\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetStatusRequest::authorize<\/a>"],[100,1,"<a href=\"Requests\/Vendor\/VendorOnboardingSetStatusRequest.php.html#19\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetStatusRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/View\/ConfigurationRequest.php.html#14\">App\\Http\\Requests\\View\\ConfigurationRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/View\/CountRequest.php.html#14\">App\\Http\\Requests\\View\\CountRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/View\/DuplicateRequest.php.html#19\">App\\Http\\Requests\\View\\DuplicateRequest::rules<\/a>"],[0,3,"<a href=\"Requests\/View\/DuplicateRequest.php.html#32\">App\\Http\\Requests\\View\\DuplicateRequest::after<\/a>"],[0,1,"<a href=\"Requests\/View\/PinRequest.php.html#15\">App\\Http\\Requests\\View\\PinRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/View\/RenameRequest.php.html#18\">App\\Http\\Requests\\View\\RenameRequest::rules<\/a>"],[0,3,"<a href=\"Requests\/View\/RenameRequest.php.html#31\">App\\Http\\Requests\\View\\RenameRequest::after<\/a>"],[0,1,"<a href=\"Requests\/View\/StoreRequest.php.html#20\">App\\Http\\Requests\\View\\StoreRequest::rules<\/a>"],[0,3,"<a href=\"Requests\/View\/StoreRequest.php.html#43\">App\\Http\\Requests\\View\\StoreRequest::after<\/a>"],[0,1,"<a href=\"Requests\/View\/UpdateRequest.php.html#15\">App\\Http\\Requests\\View\\UpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/AccessMethodUpdateRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\AccessMethodUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/ApproveQuoteRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\ApproveQuoteRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/BookmarkRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\BookmarkRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/CancelRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\CancelRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/CloseRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\CloseRequest::rules<\/a>"],[0,3,"<a href=\"Requests\/WorkOrder\/CompleteRequest.php.html#19\">App\\Http\\Requests\\WorkOrder\\CompleteRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/CreateQuoteRequest.php.html#21\">App\\Http\\Requests\\WorkOrder\\CreateQuoteRequest::rules<\/a>"],[0,13,"<a href=\"Requests\/WorkOrder\/CreateQuoteRequest.php.html#50\">App\\Http\\Requests\\WorkOrder\\CreateQuoteRequest::after<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/DescriptionUpdateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\DescriptionUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/DueDateUpdateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\DueDateUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/FilterValuesRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\FilterValuesRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/GroupViewRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\GroupViewRequest::rules<\/a>"],[100,1,"<a href=\"Requests\/WorkOrder\/ListRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\ListRequest::rules<\/a>"],[37.5,6,"<a href=\"Requests\/WorkOrder\/ListRequest.php.html#34\">App\\Http\\Requests\\WorkOrder\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/Media\/MediaUploadRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Media\\MediaUploadRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/Media\/ThumbnailUploadRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\Media\\ThumbnailUploadRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/Note\/CreateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Note\\CreateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/Note\/UpdateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Note\\UpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/PauseRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\PauseRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/PriorityUpdateRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\PriorityUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/ProblemCategoryCreateRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryCreateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/ProblemCategoryDeleteRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryDeleteRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/ProblemCategoryUpdateRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/PropertyAddressUpdateRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\PropertyAddressUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/Quote\/TaskCreateRequest.php.html#21\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskCreateRequest::rules<\/a>"],[0,12,"<a href=\"Requests\/WorkOrder\/Quote\/TaskCreateRequest.php.html#50\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskCreateRequest::after<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/Quote\/TaskUpdateRequest.php.html#22\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskUpdateRequest::rules<\/a>"],[0,12,"<a href=\"Requests\/WorkOrder\/Quote\/TaskUpdateRequest.php.html#52\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskUpdateRequest::after<\/a>"],[0,2,"<a href=\"Requests\/WorkOrder\/ReadyToInvoiceRequest.php.html#18\">App\\Http\\Requests\\WorkOrder\\ReadyToInvoiceRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/ResidentUpdateRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\ResidentUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/SendToVendorRequest.php.html#19\">App\\Http\\Requests\\WorkOrder\\SendToVendorRequest::rules<\/a>"],[0,6,"<a href=\"Requests\/WorkOrder\/SendToVendorRequest.php.html#40\">App\\Http\\Requests\\WorkOrder\\SendToVendorRequest::after<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/StoreRequest.php.html#18\">App\\Http\\Requests\\WorkOrder\\StoreRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/StoreRequest.php.html#81\">App\\Http\\Requests\\WorkOrder\\StoreRequest::attributes<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/StoreRequest.php.html#123\">App\\Http\\Requests\\WorkOrder\\StoreRequest::messages<\/a>"],[0,3,"<a href=\"Requests\/WorkOrder\/StoreRequest.php.html#149\">App\\Http\\Requests\\WorkOrder\\StoreRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/TagRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\TagRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/UpdateNteAmountRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\UpdateNteAmountRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/UpdateNteAmountRequest.php.html#31\">App\\Http\\Requests\\WorkOrder\\UpdateNteAmountRequest::messages<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/UpdateTripDetailsRequest.php.html#20\">App\\Http\\Requests\\WorkOrder\\UpdateTripDetailsRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrder\/UpdateTripRequest.php.html#21\">App\\Http\\Requests\\WorkOrder\\UpdateTripRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrderIssue\/DeclineIssueRequest.php.html#14\">App\\Http\\Requests\\WorkOrderIssue\\DeclineIssueRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrderIssue\/MarkAsDoneIssueRequest.php.html#19\">App\\Http\\Requests\\WorkOrderIssue\\MarkAsDoneIssueRequest::rules<\/a>"],[0,1,"<a href=\"Requests\/WorkOrderIssue\/WorkOrderIssueUpdateRequest.php.html#20\">App\\Http\\Requests\\WorkOrderIssue\\WorkOrderIssueUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Resources\/APITokenResource.php.html#15\">App\\Http\\Resources\\APITokenResource::toArray<\/a>"],[0,4,"<a href=\"Resources\/AppfolioVendorResource.php.html#21\">App\\Http\\Resources\\AppfolioVendorResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/CountryResource.php.html#19\">App\\Http\\Resources\\CountryResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/FeatureResource.php.html#19\">App\\Http\\Resources\\FeatureResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/Invoice\/CreateInvoiceResource.php.html#20\">App\\Http\\Resources\\Invoice\\CreateInvoiceResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Invoice\/DeleteInvoiceResource.php.html#20\">App\\Http\\Resources\\Invoice\\DeleteInvoiceResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/Invoice\/InvoiceLineItemSubsidiariesResource.php.html#21\">App\\Http\\Resources\\Invoice\\InvoiceLineItemSubsidiariesResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Invoice\/InvoiceLineItemsResource.php.html#21\">App\\Http\\Resources\\Invoice\\InvoiceLineItemsResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/Invoice\/InvoiceListResource.php.html#21\">App\\Http\\Resources\\Invoice\\InvoiceListResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Invoice\/InvoiceResource.php.html#19\">App\\Http\\Resources\\Invoice\\InvoiceResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Invoice\/InvoiceStateResource.php.html#19\">App\\Http\\Resources\\Invoice\\InvoiceStateResource::toArray<\/a>"],[0,6,"<a href=\"Resources\/Invoice\/TripResource.php.html#23\">App\\Http\\Resources\\Invoice\\TripResource::toArray<\/a>"],[0,5,"<a href=\"Resources\/Invoice\/TripResource.php.html#70\">App\\Http\\Resources\\Invoice\\TripResource::tripStatusLabel<\/a>"],[0,1,"<a href=\"Resources\/Invoice\/VoidInvoiceResource.php.html#20\">App\\Http\\Resources\\Invoice\\VoidInvoiceResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Issue\/AssignIssueResource.php.html#21\">App\\Http\\Resources\\Issue\\AssignIssueResource::toArray<\/a>"],[86.66666666666667,2,"<a href=\"Resources\/Issue\/IssueResource.php.html#22\">App\\Http\\Resources\\Issue\\IssueResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Issue\/IssueStatusResource.php.html#20\">App\\Http\\Resources\\Issue\\IssueStatusResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Issue\/UnassignIssueResource.php.html#21\">App\\Http\\Resources\\Issue\\UnassignIssueResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/LookupResource.php.html#15\">App\\Http\\Resources\\LookupResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/Notification\/NotificationResource.php.html#20\">App\\Http\\Resources\\Notification\\NotificationResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Notification\/NotificationResource.php.html#32\">App\\Http\\Resources\\Notification\\NotificationResource::buildData<\/a>"],[0,1,"<a href=\"Resources\/Notification\/NotificationResource.php.html#50\">App\\Http\\Resources\\Notification\\NotificationResource::getWorkOrderDetails<\/a>"],[0,2,"<a href=\"Resources\/Notification\/NotificationResource.php.html#71\">App\\Http\\Resources\\Notification\\NotificationResource::getServiceRequestDetails<\/a>"],[0,1,"<a href=\"Resources\/Notification\/NotificationResource.php.html#96\">App\\Http\\Resources\\Notification\\NotificationResource::getUserData<\/a>"],[0,1,"<a href=\"Resources\/Notification\/ReadNotificationResource.php.html#19\">App\\Http\\Resources\\Notification\\ReadNotificationResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Organization\/OrganizationResource.php.html#19\">App\\Http\\Resources\\Organization\\OrganizationResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/PermissionResource.php.html#19\">App\\Http\\Resources\\PermissionResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/PriorityResource.php.html#20\">App\\Http\\Resources\\PriorityResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ProblemCategoryDeleteResource.php.html#19\">App\\Http\\Resources\\ProblemCategoryDeleteResource::toArray<\/a>"],[100,2,"<a href=\"Resources\/ProblemCategoryResource.php.html#19\">App\\Http\\Resources\\ProblemCategoryResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ProblemCategoryUpdateResource.php.html#19\">App\\Http\\Resources\\ProblemCategoryUpdateResource::toArray<\/a>"],[100,1,"<a href=\"Resources\/ProblemDiagnosisResource.php.html#19\">App\\Http\\Resources\\ProblemDiagnosisResource::toArray<\/a>"],[100,2,"<a href=\"Resources\/ProblemSubCategoryResource.php.html#19\">App\\Http\\Resources\\ProblemSubCategoryResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/QuantityTypeResources.php.html#15\">App\\Http\\Resources\\QuantityTypeResources::toArray<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/AssigneeBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\Quote\\Group\\AssigneeBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/CategoryBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\Quote\\Group\\CategoryBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/StatusBasedGroupDataResource.php.html#23\">App\\Http\\Resources\\Quote\\Group\\StatusBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/SubmittedBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\Quote\\Group\\SubmittedBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/TagBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\Quote\\Group\\TagBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Quote\/Group\/WorkOrderNumberBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\Quote\\Group\\WorkOrderNumberBasedGroupDataResource::toArray<\/a>"],[0,5,"<a href=\"Resources\/Quote\/ListResource.php.html#25\">App\\Http\\Resources\\Quote\\ListResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Quote\/ListResource.php.html#69\">App\\Http\\Resources\\Quote\\ListResource::getFullName<\/a>"],[0,12,"<a href=\"Resources\/Quote\/ListResource.php.html#74\">App\\Http\\Resources\\Quote\\ListResource::findGroupSlug<\/a>"],[0,1,"<a href=\"Resources\/Quote\/ListStatusResource.php.html#14\">App\\Http\\Resources\\Quote\\ListStatusResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Quote\/ListTaskResource.php.html#15\">App\\Http\\Resources\\Quote\\ListTaskResource::toArray<\/a>"],[64.28571428571429,4,"<a href=\"Resources\/ResidentAvailability\/ResidentAvailabilityResource.php.html#20\">App\\Http\\Resources\\ResidentAvailability\\ResidentAvailabilityResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/ResidentAvailability\/ResidentAvailabilityShowResource.php.html#21\">App\\Http\\Resources\\ResidentAvailability\\ResidentAvailabilityShowResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/RoleResource.php.html#21\">App\\Http\\Resources\\RoleResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Scheduling\/GetTechnicianListResource.php.html#21\">App\\Http\\Resources\\Scheduling\\GetTechnicianListResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Scheduling\/GetTechnicianListResource.php.html#47\">App\\Http\\Resources\\Scheduling\\GetTechnicianListResource::toWindowArray<\/a>"],[0,1,"<a href=\"Resources\/Scheduling\/GetTechnicianSchedulesResources.php.html#19\">App\\Http\\Resources\\Scheduling\\GetTechnicianSchedulesResources::__construct<\/a>"],[0,2,"<a href=\"Resources\/Scheduling\/GetTechnicianSchedulesResources.php.html#32\">App\\Http\\Resources\\Scheduling\\GetTechnicianSchedulesResources::toArray<\/a>"],[0,4,"<a href=\"Resources\/Scheduling\/GetTechnicianSchedulesResources.php.html#76\">App\\Http\\Resources\\Scheduling\\GetTechnicianSchedulesResources::getTimeLabel<\/a>"],[0,1,"<a href=\"Resources\/Scheduling\/GetVendorAvailabilityResources.php.html#15\">App\\Http\\Resources\\Scheduling\\GetVendorAvailabilityResources::__construct<\/a>"],[0,3,"<a href=\"Resources\/Scheduling\/GetVendorAvailabilityResources.php.html#22\">App\\Http\\Resources\\Scheduling\\GetVendorAvailabilityResources::toArray<\/a>"],[0,5,"<a href=\"Resources\/Scheduling\/ScheduleContextResource.php.html#27\">App\\Http\\Resources\\Scheduling\\ScheduleContextResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/Scheduling\/ScheduleContextResource.php.html#84\">App\\Http\\Resources\\Scheduling\\ScheduleContextResource::scheduleOption<\/a>"],[0,2,"<a href=\"Resources\/Scheduling\/ScheduleContextResource.php.html#110\">App\\Http\\Resources\\Scheduling\\ScheduleContextResource::getAvailableDurations<\/a>"],[0,1,"<a href=\"Resources\/Scheduling\/ScheduleVendorResource.php.html#19\">App\\Http\\Resources\\Scheduling\\ScheduleVendorResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/SearchAddressResource.php.html#10\">App\\Http\\Resources\\SearchAddressResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/ActivityLogResource.php.html#18\">App\\Http\\Resources\\ServiceRequest\\ActivityLogResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/AssigneeResource.php.html#17\">App\\Http\\Resources\\ServiceRequest\\AssigneeResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/AwaitingAvailabilityResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\AwaitingAvailabilityResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/ClosedResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\ClosedResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/CreateWorkOrderResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\CreateWorkOrderResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/DescriptionResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\DescriptionResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/ServiceRequest\/Filter\/AddedDateFilterResource.php.html#16\">App\\Http\\Resources\\ServiceRequest\\Filter\\AddedDateFilterResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Filter\/AssigneeFilterResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\Filter\\AssigneeFilterResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Filter\/ImportedFromFilterResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\Filter\\ImportedFromFilterResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Filter\/StatusFilterResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\Filter\\StatusFilterResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Group\/AssigneeBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\ServiceRequest\\Group\\AssigneeBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Group\/ImportFromBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\ServiceRequest\\Group\\ImportFromBasedGroupDataResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/Group\/PriorityBasedGroupDataResource.php.html#21\">App\\Http\\Resources\\ServiceRequest\\Group\\PriorityBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Group\/StatusBasedGroupDataResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\Group\\StatusBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/InProgressResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\InProgressResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/ListResource.php.html#21\">App\\Http\\Resources\\ServiceRequest\\ListResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/ServiceRequest\/ListResource.php.html#40\">App\\Http\\Resources\\ServiceRequest\\ListResource::findGroupSlug<\/a>"],[0,5,"<a href=\"Resources\/ServiceRequest\/Media\/OriginalMediaResource.php.html#26\">App\\Http\\Resources\\ServiceRequest\\Media\\OriginalMediaResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/Media\/ThumbnailMediaResource.php.html#15\">App\\Http\\Resources\\ServiceRequest\\Media\\ThumbnailMediaResource::toArray<\/a>"],[0,5,"<a href=\"Resources\/ServiceRequest\/MediaResource.php.html#21\">App\\Http\\Resources\\ServiceRequest\\MediaResource::toArray<\/a>"],[0,4,"<a href=\"Resources\/ServiceRequest\/Note\/NoteListResource.php.html#20\">App\\Http\\Resources\\ServiceRequest\\Note\\NoteListResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/PriorityResource.php.html#17\">App\\Http\\Resources\\ServiceRequest\\PriorityResource::toArray<\/a>"],[100,2,"<a href=\"Resources\/ServiceRequest\/PriorityUpdateResource.php.html#21\">App\\Http\\Resources\\ServiceRequest\\PriorityUpdateResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/ProblemCategoryResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\ProblemCategoryResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/ServiceRequest\/PropertyAccessInfoResource.php.html#20\">App\\Http\\Resources\\ServiceRequest\\PropertyAccessInfoResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/PropertyAddressResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\PropertyAddressResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/PropertyResource.php.html#27\">App\\Http\\Resources\\ServiceRequest\\PropertyResource::__construct<\/a>"],[0,5,"<a href=\"Resources\/ServiceRequest\/PropertyResource.php.html#41\">App\\Http\\Resources\\ServiceRequest\\PropertyResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/ResidentResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\ResidentResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/ServiceRequestDescriptionResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestDescriptionResource::toArray<\/a>"],[0,6,"<a href=\"Resources\/ServiceRequest\/ServiceRequestResource.php.html#30\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/ServiceRequest\/ServiceRequestResource.php.html#99\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestResource::isOpenWorkOrderOfAuthenticatedTechnician<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/ServiceRequestResource.php.html#106\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestResource::isLatestWorkOrderTripOfAuthenticatedTechnician<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/ServiceRequestSourceResource.php.html#20\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestSourceResource::toArray<\/a>"],[100,1,"<a href=\"Resources\/ServiceRequest\/ServiceRequestStatusResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestStatusResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/ServiceRequestTypeResource.php.html#20\">App\\Http\\Resources\\ServiceRequest\\ServiceRequestTypeResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/ServiceRequest\/StoreResource.php.html#19\">App\\Http\\Resources\\ServiceRequest\\StoreResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/WorkOrder\/WorkOrderIssuesResource.php.html#21\">App\\Http\\Resources\\ServiceRequest\\WorkOrder\\WorkOrderIssuesResource::toArray<\/a>"],[100,1,"<a href=\"Resources\/ServiceRequest\/WorkOrder\/WorkOrderServiceRequestResource.php.html#23\">App\\Http\\Resources\\ServiceRequest\\WorkOrder\\WorkOrderServiceRequestResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/WorkOrder\/WorkOrderStoreIssueResource.php.html#22\">App\\Http\\Resources\\ServiceRequest\\WorkOrder\\WorkOrderStoreIssueResource::toArray<\/a>"],[0,6,"<a href=\"Resources\/ServiceRequest\/WorkOrderResource.php.html#39\">App\\Http\\Resources\\ServiceRequest\\WorkOrderResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/ServiceRequest\/WorkOrderResource.php.html#92\">App\\Http\\Resources\\ServiceRequest\\WorkOrderResource::isOpenWorkOrderOfAuthenticatedTechnician<\/a>"],[0,1,"<a href=\"Resources\/ServiceRequest\/WorkOrderResource.php.html#99\">App\\Http\\Resources\\ServiceRequest\\WorkOrderResource::isLatestWorkOrderTripOfAuthenticatedTechnician<\/a>"],[0,3,"<a href=\"Resources\/ServiceRequest\/WorkOrderResource.php.html#110\">App\\Http\\Resources\\ServiceRequest\\WorkOrderResource::generateMobileResponse<\/a>"],[0,3,"<a href=\"Resources\/ServiceRequest\/WorkOrderStoreResource.php.html#22\">App\\Http\\Resources\\ServiceRequest\\WorkOrderStoreResource::toArray<\/a>"],[100,1,"<a href=\"Resources\/StateResource.php.html#19\">App\\Http\\Resources\\StateResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Tag\/TagResource.php.html#19\">App\\Http\\Resources\\Tag\\TagResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Technician\/AvailabilityDateResource.php.html#15\">App\\Http\\Resources\\Technician\\AvailabilityDateResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Technician\/SelectedSkillsResource.php.html#22\">App\\Http\\Resources\\Technician\\SelectedSkillsResource::toArray<\/a>"],[0,5,"<a href=\"Resources\/Technician\/TechnicianAgendaResource.php.html#26\">App\\Http\\Resources\\Technician\\TechnicianAgendaResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Technician\/TechnicianBlockOutDeleteResource.php.html#19\">App\\Http\\Resources\\Technician\\TechnicianBlockOutDeleteResource::toArray<\/a>"],[0,5,"<a href=\"Resources\/Technician\/TechnicianBlockOutResource.php.html#21\">App\\Http\\Resources\\Technician\\TechnicianBlockOutResource::toArray<\/a>"],[0,4,"<a href=\"Resources\/Technician\/TechnicianCalendarViewResource.php.html#19\">App\\Http\\Resources\\Technician\\TechnicianCalendarViewResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/Technician\/WorkOrderPropertyResource.php.html#19\">App\\Http\\Resources\\Technician\\WorkOrderPropertyResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/Technician\/WorkOrderTaskResource.php.html#21\">App\\Http\\Resources\\Technician\\WorkOrderTaskResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/TemplateResource.php.html#15\">App\\Http\\Resources\\TemplateResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/User\/AccountInfoResource.php.html#21\">App\\Http\\Resources\\User\\AccountInfoResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/User\/Group\/RoleBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\User\\Group\\RoleBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/User\/Group\/StatusBasedGroupDataResource.php.html#20\">App\\Http\\Resources\\User\\Group\\StatusBasedGroupDataResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/User\/ProfileResource.php.html#20\">App\\Http\\Resources\\User\\ProfileResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/User\/RoleResource.php.html#20\">App\\Http\\Resources\\User\\RoleResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/User\/TechnicianResource.php.html#19\">App\\Http\\Resources\\User\\TechnicianResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/User\/UserListResource.php.html#21\">App\\Http\\Resources\\User\\UserListResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/User\/UserListResource.php.html#43\">App\\Http\\Resources\\User\\UserListResource::findGroupSlug<\/a>"],[0,4,"<a href=\"Resources\/User\/UserResource.php.html#23\">App\\Http\\Resources\\User\\UserResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/User\/UserRoleResource.php.html#19\">App\\Http\\Resources\\User\\UserRoleResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/User\/UserStatusResource.php.html#15\">App\\Http\\Resources\\User\\UserStatusResource::toArray<\/a>"],[100,1,"<a href=\"Resources\/Vendor\/BasicInfoResource.php.html#11\">App\\Http\\Resources\\Vendor\\BasicInfoResource::toArray<\/a>"],[100,3,"<a href=\"Resources\/Vendor\/ListResource.php.html#25\">App\\Http\\Resources\\Vendor\\ListResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Vendor\/ProfileResource.php.html#22\">App\\Http\\Resources\\Vendor\\ProfileResource::toArray<\/a>"],[100,1,"<a href=\"Resources\/Vendor\/ServiceAreaResource.php.html#12\">App\\Http\\Resources\\Vendor\\ServiceAreaResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/Vendor\/UpdateVendorResource.php.html#19\">App\\Http\\Resources\\Vendor\\UpdateVendorResource::toArray<\/a>"],[100,2,"<a href=\"Resources\/Vendor\/VendorOnboardingResource.php.html#12\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::toArray<\/a>"],[100,1,"<a href=\"Resources\/Vendor\/VendorOnboardingResource.php.html#28\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::getBasicInfo<\/a>"],[80,3,"<a href=\"Resources\/Vendor\/VendorOnboardingResource.php.html#50\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::getServiceOffered<\/a>"],[0,2,"<a href=\"Resources\/Vendor\/VendorOnboardingResource.php.html#69\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::processService<\/a>"],[0,3,"<a href=\"Resources\/Vendor\/VendorOnboardingResource.php.html#80\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::isValidDiagnosisChain<\/a>"],[0,1,"<a href=\"Resources\/Vendor\/VendorOnboardingResource.php.html#87\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::addUniqueIds<\/a>"],[0,2,"<a href=\"Resources\/Vendor\/VendorOnboardingResource.php.html#98\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::addIfNotPresent<\/a>"],[89.70588235294117,12,"<a href=\"Resources\/Vendor\/WorkOrderResource.php.html#42\">App\\Http\\Resources\\Vendor\\WorkOrderResource::toArray<\/a>"],[100,2,"<a href=\"Resources\/Vendor\/WorkOrderResource.php.html#135\">App\\Http\\Resources\\Vendor\\WorkOrderResource::getTotalTabCount<\/a>"],[80,3,"<a href=\"Resources\/Vendor\/WorkOrderResource.php.html#146\">App\\Http\\Resources\\Vendor\\WorkOrderResource::hasMissingData<\/a>"],[0,1,"<a href=\"Resources\/VendorUserResource.php.html#20\">App\\Http\\Resources\\VendorUserResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/View\/ConfigurationResource.php.html#19\">App\\Http\\Resources\\View\\ConfigurationResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/View\/DeleteResource.php.html#19\">App\\Http\\Resources\\View\\DeleteResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/View\/PinResource.php.html#22\">App\\Http\\Resources\\View\\PinResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/View\/PinnedViewCount.php.html#15\">App\\Http\\Resources\\View\\PinnedViewCount::toArray<\/a>"],[0,1,"<a href=\"Resources\/View\/SetDefaultResource.php.html#19\">App\\Http\\Resources\\View\\SetDefaultResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/View\/StoreResource.php.html#19\">App\\Http\\Resources\\View\\StoreResource::toArray<\/a>"],[0,4,"<a href=\"Resources\/View\/UpdatedResource.php.html#23\">App\\Http\\Resources\\View\\UpdatedResource::toArray<\/a>"],[0,8,"<a href=\"Resources\/View\/ViewResource.php.html#19\">App\\Http\\Resources\\View\\ViewResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/ActivityLogResource.php.html#18\">App\\Http\\Resources\\WorkOrder\\ActivityLogResource::toArray<\/a>"],[0,5,"<a href=\"Resources\/WorkOrder\/AppointmentRescheduledResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\AppointmentRescheduledResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/AppointmentTechnicianResource.php.html#17\">App\\Http\\Resources\\WorkOrder\\AppointmentTechnicianResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/AssigneeListResource.php.html#17\">App\\Http\\Resources\\WorkOrder\\AssigneeListResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/AwaitingAvailabilityResources.php.html#19\">App\\Http\\Resources\\WorkOrder\\AwaitingAvailabilityResources::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/CancelResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\CancelResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/ClosedResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\ClosedResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ClosedResource.php.html#55\">App\\Http\\Resources\\WorkOrder\\ClosedResource::getTripDetails<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/CompletedResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\CompletedResource::toArray<\/a>"],[0,6,"<a href=\"Resources\/WorkOrder\/CreateQuoteResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\CreateQuoteResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/DueDateResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\DueDateResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/EnRouteResource.php.html#24\">App\\Http\\Resources\\WorkOrder\\EnRouteResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/EnRouteResource.php.html#60\">App\\Http\\Resources\\WorkOrder\\EnRouteResource::getWorkOrderStatus<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/EnRouteResource.php.html#76\">App\\Http\\Resources\\WorkOrder\\EnRouteResource::getTripDetails<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/AssigneeFilterResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\Filter\\AssigneeFilterResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/CategoryFilterResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\Filter\\CategoryFilterResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/Filter\/CreatedDateFilterResource.php.html#16\">App\\Http\\Resources\\WorkOrder\\Filter\\CreatedDateFilterResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/Filter\/HealthScoreFilterResource.php.html#16\">App\\Http\\Resources\\WorkOrder\\Filter\\HealthScoreFilterResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/Filter\/OverdueFilterResource.php.html#16\">App\\Http\\Resources\\WorkOrder\\Filter\\OverdueFilterResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/Filter\/PriorityFilterResource.php.html#16\">App\\Http\\Resources\\WorkOrder\\Filter\\PriorityFilterResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/ProviderFilterResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\Filter\\ProviderFilterResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/Filter\/ScheduledDateFilterResource.php.html#16\">App\\Http\\Resources\\WorkOrder\\Filter\\ScheduledDateFilterResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/StatusFilterResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\Filter\\StatusFilterResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/TagFilterResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\Filter\\TagFilterResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Filter\/TechnicianFilterResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\Filter\\TechnicianFilterResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/AssigneeBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\Group\\AssigneeBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/CategoryBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\Group\\CategoryBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/HealthScoreGroupDataResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\Group\\HealthScoreGroupDataResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/Group\/PriorityBasedGroupDataResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\Group\\PriorityBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/StatusBasedGroupDataResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\Group\\StatusBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/TagBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\Group\\TagBasedGroupDataResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Group\/TechnicianBasedGroupDataResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\Group\\TechnicianBasedGroupDataResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/HealthScore\/HealthScoreResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\HealthScore\\HealthScoreResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/HealthScore\/HealthScoreResource.php.html#33\">App\\Http\\Resources\\WorkOrder\\HealthScore\\HealthScoreResource::elapseTime<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Invoice\/FullyPaidResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\Invoice\\FullyPaidResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/Invoice\/InvoiceResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\Invoice\\InvoiceResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Invoice\/PartiallyPaidResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\Invoice\\PartiallyPaidResource::toArray<\/a>"],[100,1,"<a href=\"Resources\/WorkOrder\/List\/ActiveTripResource.php.html#23\">App\\Http\\Resources\\WorkOrder\\List\\ActiveTripResource::toArray<\/a>"],[0,7,"<a href=\"Resources\/WorkOrder\/List\/ActiveTripResource.php.html#37\">App\\Http\\Resources\\WorkOrder\\List\\ActiveTripResource::mobileDeviceTripDetails<\/a>"],[100,1,"<a href=\"Resources\/WorkOrder\/List\/ActiveTripResource.php.html#82\">App\\Http\\Resources\\WorkOrder\\List\\ActiveTripResource::webTripDetails<\/a>"],[50,4,"<a href=\"Resources\/WorkOrder\/List\/ActiveTripResource.php.html#99\">App\\Http\\Resources\\WorkOrder\\List\\ActiveTripResource::getAppointmentDetails<\/a>"],[80,3,"<a href=\"Resources\/WorkOrder\/List\/ActiveTripResource.php.html#128\">App\\Http\\Resources\\WorkOrder\\List\\ActiveTripResource::getFormattedScheduleDate<\/a>"],[100,1,"<a href=\"Resources\/WorkOrder\/List\/ListPropertyResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\List\\ListPropertyResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/List\/ListTaskAppointmentResource.php.html#29\">App\\Http\\Resources\\WorkOrder\\List\\ListTaskAppointmentResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/List\/ListTaskAppointmentResource.php.html#65\">App\\Http\\Resources\\WorkOrder\\List\\ListTaskAppointmentResource::inHouserTechnicianTripData<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/List\/ListTaskAppointmentResource.php.html#99\">App\\Http\\Resources\\WorkOrder\\List\\ListTaskAppointmentResource::lulaNetWorkProTripData<\/a>"],[0,5,"<a href=\"Resources\/WorkOrder\/ListResource.php.html#38\">App\\Http\\Resources\\WorkOrder\\ListResource::toArray<\/a>"],[0,6,"<a href=\"Resources\/WorkOrder\/ListResource.php.html#78\">App\\Http\\Resources\\WorkOrder\\ListResource::transformResponseForDevice<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/ListResource.php.html#101\">App\\Http\\Resources\\WorkOrder\\ListResource::isOpenWorkOrderOfAuthenticatedTechnician<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ListResource.php.html#108\">App\\Http\\Resources\\WorkOrder\\ListResource::isLatestWorkOrderTripOfAuthenticatedTechnician<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/ListResource.php.html#113\">App\\Http\\Resources\\WorkOrder\\ListResource::getStatusForApp<\/a>"],[0,14,"<a href=\"Resources\/WorkOrder\/ListResource.php.html#134\">App\\Http\\Resources\\WorkOrder\\ListResource::findGroupSlug<\/a>"],[0,5,"<a href=\"Resources\/WorkOrder\/Media\/OriginalMediaResource.php.html#26\">App\\Http\\Resources\\WorkOrder\\Media\\OriginalMediaResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Media\/ThumbnailMediaResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\Media\\ThumbnailMediaResource::toArray<\/a>"],[0,5,"<a href=\"Resources\/WorkOrder\/MediaResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\MediaResource::toArray<\/a>"],[0,4,"<a href=\"Resources\/WorkOrder\/Note\/NoteListResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\Note\\NoteListResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/PauseEnRouteResource.php.html#24\">App\\Http\\Resources\\WorkOrder\\PauseEnRouteResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/PauseEnRouteResource.php.html#61\">App\\Http\\Resources\\WorkOrder\\PauseEnRouteResource::getWorkOrderStatus<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/PauseEnRouteResource.php.html#77\">App\\Http\\Resources\\WorkOrder\\PauseEnRouteResource::getTripDetails<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/PauseResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\PauseResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/PauseWorkResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\PauseWorkResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/PauseWorkResource.php.html#57\">App\\Http\\Resources\\WorkOrder\\PauseWorkResource::getTripDetails<\/a>"],[100,2,"<a href=\"Resources\/WorkOrder\/PriorityResource.php.html#17\">App\\Http\\Resources\\WorkOrder\\PriorityResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/PriorityUpdateResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\PriorityUpdateResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ProblemCategoryResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\ProblemCategoryResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/PropertyAccessInfoResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\PropertyAccessInfoResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/PropertyAddressResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\PropertyAddressResource::toArray<\/a>"],[100,1,"<a href=\"Resources\/WorkOrder\/PropertyResource.php.html#27\">App\\Http\\Resources\\WorkOrder\\PropertyResource::__construct<\/a>"],[100,5,"<a href=\"Resources\/WorkOrder\/PropertyResource.php.html#41\">App\\Http\\Resources\\WorkOrder\\PropertyResource::toArray<\/a>"],[0,4,"<a href=\"Resources\/WorkOrder\/ProviderResource.php.html#23\">App\\Http\\Resources\\WorkOrder\\ProviderResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/Quote\/QuoteMaterialResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\Quote\\QuoteMaterialResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/Quote\/TaskDeleteResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\Quote\\TaskDeleteResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/QuoteApproveResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\QuoteApproveResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/QuoteRejectResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\QuoteRejectResource::toArray<\/a>"],[0,7,"<a href=\"Resources\/WorkOrder\/QuoteResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\QuoteResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/QuoteTaskResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\QuoteTaskResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ReOpenResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\ReOpenResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ReadyToInvoiceResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\ReadyToInvoiceResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ReadyToScheduleResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\ReadyToScheduleResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ResidentResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\ResidentResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ResolveResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\ResolveResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/ResumeEnRouteResource.php.html#22\">App\\Http\\Resources\\WorkOrder\\ResumeEnRouteResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/ResumeEnRouteResource.php.html#59\">App\\Http\\Resources\\WorkOrder\\ResumeEnRouteResource::getWorkOrderStatus<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ResumeEnRouteResource.php.html#75\">App\\Http\\Resources\\WorkOrder\\ResumeEnRouteResource::getTripDetails<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/ResumeWorkResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\ResumeWorkResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ResumeWorkResource.php.html#58\">App\\Http\\Resources\\WorkOrder\\ResumeWorkResource::getTripDetails<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/ServiceRequestResource.php.html#27\">App\\Http\\Resources\\WorkOrder\\ServiceRequestResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/ServiceRequestResource.php.html#55\">App\\Http\\Resources\\WorkOrder\\ServiceRequestResource::isOpenWorkOrderOfAuthenticatedTechnician<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/ServiceRequestResource.php.html#62\">App\\Http\\Resources\\WorkOrder\\ServiceRequestResource::isLatestWorkOrderTripOfAuthenticatedTechnician<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/StartWorkResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\StartWorkResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/StartWorkResource.php.html#58\">App\\Http\\Resources\\WorkOrder\\StartWorkResource::getTripDetails<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/StopTripResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\StopTripResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/StoreResources.php.html#19\">App\\Http\\Resources\\WorkOrder\\StoreResources::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/TaskAppointmentResource.php.html#20\">App\\Http\\Resources\\WorkOrder\\TaskAppointmentResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrder\/TaskListResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\TaskListResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/TaskMaterialResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\TaskMaterialResource::toArray<\/a>"],[0,29,"<a href=\"Resources\/WorkOrder\/TaskResource.php.html#25\">App\\Http\\Resources\\WorkOrder\\TaskResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/TaskScheduledAppointmentResource.php.html#22\">App\\Http\\Resources\\WorkOrder\\TaskScheduledAppointmentResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/TechnicianAppointmentResource.php.html#15\">App\\Http\\Resources\\WorkOrder\\TechnicianAppointmentResource::__construct<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/TechnicianAppointmentResource.php.html#27\">App\\Http\\Resources\\WorkOrder\\TechnicianAppointmentResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/Trip\/TripDetailsResource.php.html#22\">App\\Http\\Resources\\WorkOrder\\Trip\\TripDetailsResource::toArray<\/a>"],[0,4,"<a href=\"Resources\/WorkOrder\/TripResource.php.html#31\">App\\Http\\Resources\\WorkOrder\\TripResource::toArray<\/a>"],[0,9,"<a href=\"Resources\/WorkOrder\/TripResource.php.html#74\">App\\Http\\Resources\\WorkOrder\\TripResource::inHouserTechnicianTripData<\/a>"],[0,11,"<a href=\"Resources\/WorkOrder\/TripResource.php.html#142\">App\\Http\\Resources\\WorkOrder\\TripResource::lulaNetWorkProTripData<\/a>"],[0,5,"<a href=\"Resources\/WorkOrder\/TripResource.php.html#204\">App\\Http\\Resources\\WorkOrder\\TripResource::vendorTripData<\/a>"],[100,2,"<a href=\"Resources\/WorkOrder\/TripStatusResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\TripStatusResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderAssigneeResource.php.html#17\">App\\Http\\Resources\\WorkOrder\\WorkOrderAssigneeResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderDescriptionResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\WorkOrderDescriptionResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderNteResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\WorkOrderNteResource::toArray<\/a>"],[0,13,"<a href=\"Resources\/WorkOrder\/WorkOrderResource.php.html#46\">App\\Http\\Resources\\WorkOrder\\WorkOrderResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/WorkOrderResource.php.html#138\">App\\Http\\Resources\\WorkOrder\\WorkOrderResource::isOpenWorkOrderOfAuthenticatedTechnician<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderResource.php.html#145\">App\\Http\\Resources\\WorkOrder\\WorkOrderResource::isLatestWorkOrderTripOfAuthenticatedTechnician<\/a>"],[0,5,"<a href=\"Resources\/WorkOrder\/WorkOrderResource.php.html#156\">App\\Http\\Resources\\WorkOrder\\WorkOrderResource::generateMobileResponse<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/WorkOrderResource.php.html#196\">App\\Http\\Resources\\WorkOrder\\WorkOrderResource::hasMissingData<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderScheduledResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\WorkOrderScheduledResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderSendToVendorResource.php.html#17\">App\\Http\\Resources\\WorkOrder\\WorkOrderSendToVendorResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderServiceRequestStatusResource.php.html#19\">App\\Http\\Resources\\WorkOrder\\WorkOrderServiceRequestStatusResource::toArray<\/a>"],[63.63636363636363,4,"<a href=\"Resources\/WorkOrder\/WorkOrderStatusResource.php.html#21\">App\\Http\\Resources\\WorkOrder\\WorkOrderStatusResource::toArray<\/a>"],[0,8,"<a href=\"Resources\/WorkOrder\/WorkOrderSubResource.php.html#37\">App\\Http\\Resources\\WorkOrder\\WorkOrderSubResource::toArray<\/a>"],[0,3,"<a href=\"Resources\/WorkOrder\/WorkOrderSubResource.php.html#93\">App\\Http\\Resources\\WorkOrder\\WorkOrderSubResource::isOpenWorkOrderOfAuthenticatedTechnician<\/a>"],[0,1,"<a href=\"Resources\/WorkOrder\/WorkOrderSubResource.php.html#100\">App\\Http\\Resources\\WorkOrder\\WorkOrderSubResource::isLatestWorkOrderTripOfAuthenticatedTechnician<\/a>"],[0,5,"<a href=\"Resources\/WorkOrder\/WorkOrderSubResource.php.html#111\">App\\Http\\Resources\\WorkOrder\\WorkOrderSubResource::generateMobileResponse<\/a>"],[63.33333333333333,14,"<a href=\"Resources\/WorkOrder\/WorkOrderTripResource.php.html#24\">App\\Http\\Resources\\WorkOrder\\WorkOrderTripResource::toArray<\/a>"],[0,4,"<a href=\"Resources\/WorkOrder\/WorkOrderTripResource.php.html#161\">App\\Http\\Resources\\WorkOrder\\WorkOrderTripResource::issueResponse<\/a>"],[50,6,"<a href=\"Resources\/WorkOrder\/WorkOrderTripResource.php.html#188\">App\\Http\\Resources\\WorkOrder\\WorkOrderTripResource::getAppointmentDetails<\/a>"],[0,1,"<a href=\"Resources\/WorkOrderIssue\/IssueDeclinedReasonResource.php.html#20\">App\\Http\\Resources\\WorkOrderIssue\\IssueDeclinedReasonResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrderIssue\/IssueDeclinedResource.php.html#20\">App\\Http\\Resources\\WorkOrderIssue\\IssueDeclinedResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrderIssue\/MaterialResource.php.html#19\">App\\Http\\Resources\\WorkOrderIssue\\MaterialResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrderIssue\/PendingIssueResource.php.html#18\">App\\Http\\Resources\\WorkOrderIssue\\PendingIssueResource::toArray<\/a>"],[0,2,"<a href=\"Resources\/WorkOrderIssue\/WorkOrderIssueDetailsResource.php.html#20\">App\\Http\\Resources\\WorkOrderIssue\\WorkOrderIssueDetailsResource::toArray<\/a>"],[0,1,"<a href=\"Resources\/WorkOrderIssue\/WorkOrderIssueListResource.php.html#20\">App\\Http\\Resources\\WorkOrderIssue\\WorkOrderIssueListResource::toArray<\/a>"],[100,1,"<a href=\"Resources\/WorkOrderIssue\/WorkOrderIssueResource.php.html#22\">App\\Http\\Resources\\WorkOrderIssue\\WorkOrderIssueResource::detailCollection<\/a>"],[91.42857142857143,4,"<a href=\"Resources\/WorkOrderIssue\/WorkOrderIssueResource.php.html#37\">App\\Http\\Resources\\WorkOrderIssue\\WorkOrderIssueResource::toArray<\/a>"],[85.71428571428571,3,"<a href=\"Resources\/WorkOrderIssue\/WorkOrderIssueStatusResource.php.html#20\">App\\Http\\Resources\\WorkOrderIssue\\WorkOrderIssueStatusResource::toArray<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

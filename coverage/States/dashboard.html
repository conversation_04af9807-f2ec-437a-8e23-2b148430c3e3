<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/States</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">States</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Invoices/Draft.php.html#5">App\States\Invoices\Draft</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Paid.php.html#5">App\States\Invoices\Paid</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PartiallyPaid.php.html#5">App\States\Invoices\PartiallyPaid</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PaymentPending.php.html#5">App\States\Invoices\PaymentPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToDraft.php.html#12">App\States\Invoices\Transitions\ToDraft</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToPaid.php.html#12">App\States\Invoices\Transitions\ToPaid</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToPartiallyPaid.php.html#12">App\States\Invoices\Transitions\ToPartiallyPaid</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToPaymentPending.php.html#12">App\States\Invoices\Transitions\ToPaymentPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToVoided.php.html#12">App\States\Invoices\Transitions\ToVoided</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Voided.php.html#5">App\States\Invoices\Voided</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Assigned.php.html#9">App\States\Issue\Assigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Canceled.php.html#9">App\States\Issue\Canceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Done.php.html#9">App\States\Issue\Done</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/QualityCheck.php.html#9">App\States\Issue\QualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToAssigned.php.html#16">App\States\Issue\Transitions\ToAssigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToCanceled.php.html#12">App\States\Issue\Transitions\ToCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToQualityCheck.php.html#10">App\States\Issue\Transitions\ToQualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToUnassigned.php.html#10">App\States\Issue\Transitions\ToUnassigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToUnresolved.php.html#10">App\States\Issue\Transitions\ToUnresolved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Unresolved.php.html#9">App\States\Issue\Unresolved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Canceled.php.html#5">App\States\ServiceCalls\Canceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ClaimPending.php.html#5">App\States\ServiceCalls\ClaimPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Done.php.html#5">App\States\ServiceCalls\Done</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/EnRoute.php.html#5">App\States\ServiceCalls\EnRoute</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/EnRoutePaused.php.html#5">App\States\ServiceCalls\EnRoutePaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Ended.php.html#5">App\States\ServiceCalls\Ended</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/MissingInfo.php.html#7">App\States\ServiceCalls\MissingInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Paused.php.html#5">App\States\ServiceCalls\Paused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ReScheduled.php.html#5">App\States\ServiceCalls\ReScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ScheduleInPending.php.html#5">App\States\ServiceCalls\ScheduleInPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ScheduleInProgress.php.html#5">App\States\ServiceCalls\ScheduleInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToCanceled.php.html#13">App\States\ServiceCalls\Transitions\ToCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToDone.php.html#11">App\States\ServiceCalls\Transitions\ToDone</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnRoute.php.html#17">App\States\ServiceCalls\Transitions\ToEnRoute</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnRoutePaused.php.html#15">App\States\ServiceCalls\Transitions\ToEnRoutePaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnded.php.html#18">App\States\ServiceCalls\Transitions\ToEnded</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToMissingInfo.php.html#11">App\States\ServiceCalls\Transitions\ToMissingInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToPaused.php.html#14">App\States\ServiceCalls\Transitions\ToPaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToReScheduled.php.html#19">App\States\ServiceCalls\Transitions\ToReScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToScheduleInPending.php.html#11">App\States\ServiceCalls\Transitions\ToScheduleInPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToScheduleInProgress.php.html#13">App\States\ServiceCalls\Transitions\ToScheduleInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToScheduled.php.html#13">App\States\ServiceCalls\Transitions\ToScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToUnresolved.php.html#11">App\States\ServiceCalls\Transitions\ToUnresolved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToWorking.php.html#17">App\States\ServiceCalls\Transitions\ToWorking</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Unresolved.php.html#7">App\States\ServiceCalls\Unresolved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Working.php.html#5">App\States\ServiceCalls\Working</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/AwaitingAvailability.php.html#5">App\States\ServiceRequests\AwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Closed.php.html#5">App\States\ServiceRequests\Closed</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/CreateWorkOrder.php.html#5">App\States\ServiceRequests\CreateWorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/InProgress.php.html#5">App\States\ServiceRequests\InProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Scoping.php.html#5">App\States\ServiceRequests\Scoping</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToAwaitingAvailability.php.html#13">App\States\ServiceRequests\Transitions\ToAwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToClosed.php.html#13">App\States\ServiceRequests\Transitions\ToClosed</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToCreateWorkOrder.php.html#13">App\States\ServiceRequests\Transitions\ToCreateWorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToInProgress.php.html#13">App\States\ServiceRequests\Transitions\ToInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToScoping.php.html#13">App\States\ServiceRequests\Transitions\ToScoping</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Canceled.php.html#9">App\States\WorkOrderIssue\Canceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Done.php.html#9">App\States\WorkOrderIssue\Done</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MissingInfo.php.html#7">App\States\WorkOrderIssue\MissingInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/QualityCheck.php.html#9">App\States\WorkOrderIssue\QualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToAssigned.php.html#10">App\States\WorkOrderIssue\Transitions\ToAssigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToCanceled.php.html#11">App\States\WorkOrderIssue\Transitions\ToCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToDone.php.html#11">App\States\WorkOrderIssue\Transitions\ToDone</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToMissingInfo.php.html#10">App\States\WorkOrderIssue\Transitions\ToMissingInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToQualityCheck.php.html#10">App\States\WorkOrderIssue\Transitions\ToQualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToUnresolved.php.html#11">App\States\WorkOrderIssue\Transitions\ToUnresolved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Unresolved.php.html#9">App\States\WorkOrderIssue\Unresolved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailability.php.html#7">App\States\WorkOrders\AwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ClaimPending.php.html#7">App\States\WorkOrders\ClaimPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Created.php.html#7">App\States\WorkOrders\Created</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/QualityCheck.php.html#7">App\States\WorkOrders\QualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoice.php.html#7">App\States\WorkOrders\ReadyToInvoice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToSchedule.php.html#7">App\States\WorkOrders\ReadyToSchedule</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/SchedulingInProgress.php.html#7">App\States\WorkOrders\SchedulingInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToAwaitingAvailability.php.html#14">App\States\WorkOrders\Transitions\ToAwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCanceled.php.html#15">App\States\WorkOrders\Transitions\ToCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToClaimPending.php.html#14">App\States\WorkOrders\Transitions\ToClaimPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCompleted.php.html#15">App\States\WorkOrders\Transitions\ToCompleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCreated.php.html#18">App\States\WorkOrders\Transitions\ToCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToPaused.php.html#16">App\States\WorkOrders\Transitions\ToPaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToQualityCheck.php.html#15">App\States\WorkOrders\Transitions\ToQualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToReadyToInvoice.php.html#15">App\States\WorkOrders\Transitions\ToReadyToInvoice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToReadyToSchedule.php.html#15">App\States\WorkOrders\Transitions\ToReadyToSchedule</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToScheduled.php.html#29">App\States\WorkOrders\Transitions\ToScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToWorkInProgress.php.html#15">App\States\WorkOrders\Transitions\ToWorkInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Created.php.html#5">App\States\ServiceRequests\Created</a></td><td class="text-right">28%</td></tr>
       <tr><td><a href="ServiceCalls/Scheduled.php.html#5">App\States\ServiceCalls\Scheduled</a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="WorkOrderIssue/Assigned.php.html#9">App\States\WorkOrderIssue\Assigned</a></td><td class="text-right">80%</td></tr>
       <tr><td><a href="WorkOrders/Completed.php.html#7">App\States\WorkOrders\Completed</a></td><td class="text-right">83%</td></tr>
       <tr><td><a href="WorkOrders/Canceled.php.html#7">App\States\WorkOrders\Canceled</a></td><td class="text-right">85%</td></tr>
       <tr><td><a href="WorkOrders/Paused.php.html#7">App\States\WorkOrders\Paused</a></td><td class="text-right">85%</td></tr>
       <tr><td><a href="Issue/Unassigned.php.html#9">App\States\Issue\Unassigned</a></td><td class="text-right">87%</td></tr>
       <tr><td><a href="WorkOrders/Scheduled.php.html#7">App\States\WorkOrders\Scheduled</a></td><td class="text-right">88%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ServiceCalls/Transitions/ToReScheduled.php.html#19">App\States\ServiceCalls\Transitions\ToReScheduled</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToScheduled.php.html#29">App\States\WorkOrders\Transitions\ToScheduled</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnded.php.html#18">App\States\ServiceCalls\Transitions\ToEnded</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCreated.php.html#18">App\States\WorkOrders\Transitions\ToCreated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToWorking.php.html#17">App\States\ServiceCalls\Transitions\ToWorking</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToPaused.php.html#16">App\States\WorkOrders\Transitions\ToPaused</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnRoutePaused.php.html#15">App\States\ServiceCalls\Transitions\ToEnRoutePaused</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToPaused.php.html#14">App\States\ServiceCalls\Transitions\ToPaused</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCompleted.php.html#15">App\States\WorkOrders\Transitions\ToCompleted</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnRoute.php.html#17">App\States\ServiceCalls\Transitions\ToEnRoute</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCanceled.php.html#15">App\States\WorkOrders\Transitions\ToCanceled</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Invoices/Draft.php.html#12"><abbr title="App\States\Invoices\Draft::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Draft.php.html#17"><abbr title="App\States\Invoices\Draft::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Draft.php.html#22"><abbr title="App\States\Invoices\Draft::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Draft.php.html#30"><abbr title="App\States\Invoices\Draft::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Paid.php.html#12"><abbr title="App\States\Invoices\Paid::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Paid.php.html#17"><abbr title="App\States\Invoices\Paid::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Paid.php.html#22"><abbr title="App\States\Invoices\Paid::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Paid.php.html#30"><abbr title="App\States\Invoices\Paid::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PartiallyPaid.php.html#12"><abbr title="App\States\Invoices\PartiallyPaid::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PartiallyPaid.php.html#17"><abbr title="App\States\Invoices\PartiallyPaid::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PartiallyPaid.php.html#22"><abbr title="App\States\Invoices\PartiallyPaid::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PartiallyPaid.php.html#30"><abbr title="App\States\Invoices\PartiallyPaid::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PaymentPending.php.html#12"><abbr title="App\States\Invoices\PaymentPending::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PaymentPending.php.html#17"><abbr title="App\States\Invoices\PaymentPending::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PaymentPending.php.html#22"><abbr title="App\States\Invoices\PaymentPending::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PaymentPending.php.html#30"><abbr title="App\States\Invoices\PaymentPending::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToDraft.php.html#14"><abbr title="App\States\Invoices\Transitions\ToDraft::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToDraft.php.html#16"><abbr title="App\States\Invoices\Transitions\ToDraft::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToPaid.php.html#14"><abbr title="App\States\Invoices\Transitions\ToPaid::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToPaid.php.html#16"><abbr title="App\States\Invoices\Transitions\ToPaid::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToPartiallyPaid.php.html#14"><abbr title="App\States\Invoices\Transitions\ToPartiallyPaid::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToPartiallyPaid.php.html#16"><abbr title="App\States\Invoices\Transitions\ToPartiallyPaid::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToPaymentPending.php.html#14"><abbr title="App\States\Invoices\Transitions\ToPaymentPending::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToPaymentPending.php.html#16"><abbr title="App\States\Invoices\Transitions\ToPaymentPending::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToVoided.php.html#14"><abbr title="App\States\Invoices\Transitions\ToVoided::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Transitions/ToVoided.php.html#16"><abbr title="App\States\Invoices\Transitions\ToVoided::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Voided.php.html#12"><abbr title="App\States\Invoices\Voided::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Voided.php.html#17"><abbr title="App\States\Invoices\Voided::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Voided.php.html#22"><abbr title="App\States\Invoices\Voided::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Voided.php.html#30"><abbr title="App\States\Invoices\Voided::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Assigned.php.html#16"><abbr title="App\States\Issue\Assigned::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Assigned.php.html#24"><abbr title="App\States\Issue\Assigned::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Canceled.php.html#16"><abbr title="App\States\Issue\Canceled::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Canceled.php.html#24"><abbr title="App\States\Issue\Canceled::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Done.php.html#16"><abbr title="App\States\Issue\Done::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Done.php.html#24"><abbr title="App\States\Issue\Done::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/QualityCheck.php.html#16"><abbr title="App\States\Issue\QualityCheck::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/QualityCheck.php.html#24"><abbr title="App\States\Issue\QualityCheck::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToAssigned.php.html#18"><abbr title="App\States\Issue\Transitions\ToAssigned::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToAssigned.php.html#24"><abbr title="App\States\Issue\Transitions\ToAssigned::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToCanceled.php.html#14"><abbr title="App\States\Issue\Transitions\ToCanceled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToCanceled.php.html#19"><abbr title="App\States\Issue\Transitions\ToCanceled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToQualityCheck.php.html#12"><abbr title="App\States\Issue\Transitions\ToQualityCheck::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToQualityCheck.php.html#16"><abbr title="App\States\Issue\Transitions\ToQualityCheck::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToUnassigned.php.html#12"><abbr title="App\States\Issue\Transitions\ToUnassigned::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToUnassigned.php.html#16"><abbr title="App\States\Issue\Transitions\ToUnassigned::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToUnresolved.php.html#12"><abbr title="App\States\Issue\Transitions\ToUnresolved::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Transitions/ToUnresolved.php.html#16"><abbr title="App\States\Issue\Transitions\ToUnresolved::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Unassigned.php.html#16"><abbr title="App\States\Issue\Unassigned::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Unresolved.php.html#16"><abbr title="App\States\Issue\Unresolved::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/Unresolved.php.html#24"><abbr title="App\States\Issue\Unresolved::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Canceled.php.html#12"><abbr title="App\States\ServiceCalls\Canceled::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Canceled.php.html#17"><abbr title="App\States\ServiceCalls\Canceled::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Canceled.php.html#22"><abbr title="App\States\ServiceCalls\Canceled::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ClaimPending.php.html#12"><abbr title="App\States\ServiceCalls\ClaimPending::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ClaimPending.php.html#17"><abbr title="App\States\ServiceCalls\ClaimPending::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ClaimPending.php.html#22"><abbr title="App\States\ServiceCalls\ClaimPending::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Done.php.html#12"><abbr title="App\States\ServiceCalls\Done::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Done.php.html#17"><abbr title="App\States\ServiceCalls\Done::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Done.php.html#22"><abbr title="App\States\ServiceCalls\Done::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/EnRoute.php.html#12"><abbr title="App\States\ServiceCalls\EnRoute::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/EnRoute.php.html#17"><abbr title="App\States\ServiceCalls\EnRoute::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/EnRoute.php.html#22"><abbr title="App\States\ServiceCalls\EnRoute::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/EnRoutePaused.php.html#12"><abbr title="App\States\ServiceCalls\EnRoutePaused::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/EnRoutePaused.php.html#17"><abbr title="App\States\ServiceCalls\EnRoutePaused::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/EnRoutePaused.php.html#22"><abbr title="App\States\ServiceCalls\EnRoutePaused::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Ended.php.html#12"><abbr title="App\States\ServiceCalls\Ended::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Ended.php.html#17"><abbr title="App\States\ServiceCalls\Ended::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Ended.php.html#22"><abbr title="App\States\ServiceCalls\Ended::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/MissingInfo.php.html#14"><abbr title="App\States\ServiceCalls\MissingInfo::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/MissingInfo.php.html#19"><abbr title="App\States\ServiceCalls\MissingInfo::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/MissingInfo.php.html#24"><abbr title="App\States\ServiceCalls\MissingInfo::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Paused.php.html#12"><abbr title="App\States\ServiceCalls\Paused::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Paused.php.html#17"><abbr title="App\States\ServiceCalls\Paused::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Paused.php.html#22"><abbr title="App\States\ServiceCalls\Paused::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ReScheduled.php.html#12"><abbr title="App\States\ServiceCalls\ReScheduled::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ReScheduled.php.html#17"><abbr title="App\States\ServiceCalls\ReScheduled::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ReScheduled.php.html#22"><abbr title="App\States\ServiceCalls\ReScheduled::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ScheduleInPending.php.html#12"><abbr title="App\States\ServiceCalls\ScheduleInPending::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ScheduleInPending.php.html#17"><abbr title="App\States\ServiceCalls\ScheduleInPending::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ScheduleInPending.php.html#22"><abbr title="App\States\ServiceCalls\ScheduleInPending::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ScheduleInProgress.php.html#12"><abbr title="App\States\ServiceCalls\ScheduleInProgress::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ScheduleInProgress.php.html#17"><abbr title="App\States\ServiceCalls\ScheduleInProgress::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/ScheduleInProgress.php.html#22"><abbr title="App\States\ServiceCalls\ScheduleInProgress::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Scheduled.php.html#17"><abbr title="App\States\ServiceCalls\Scheduled::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToCanceled.php.html#15"><abbr title="App\States\ServiceCalls\Transitions\ToCanceled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToCanceled.php.html#22"><abbr title="App\States\ServiceCalls\Transitions\ToCanceled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToDone.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToDone::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToDone.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToDone::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnRoute.php.html#21"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoute::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnRoute.php.html#34"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoute::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnRoutePaused.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnRoutePaused.php.html#31"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnRoutePaused.php.html#77"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::calculateDriveTime">calculateDriveTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnded.php.html#25"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnded.php.html#37"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnded.php.html#104"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToMissingInfo.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToMissingInfo::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToMissingInfo.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToMissingInfo::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToPaused.php.html#18"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToPaused.php.html#26"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToPaused.php.html#66"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToReScheduled.php.html#21"><abbr title="App\States\ServiceCalls\Transitions\ToReScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToReScheduled.php.html#36"><abbr title="App\States\ServiceCalls\Transitions\ToReScheduled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToScheduleInPending.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInPending::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToScheduleInPending.php.html#18"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInPending::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToScheduleInProgress.php.html#15"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInProgress::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToScheduleInProgress.php.html#22"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInProgress::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToScheduled.php.html#15"><abbr title="App\States\ServiceCalls\Transitions\ToScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToScheduled.php.html#22"><abbr title="App\States\ServiceCalls\Transitions\ToScheduled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToUnresolved.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToUnresolved::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToUnresolved.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToUnresolved::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToWorking.php.html#21"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToWorking.php.html#32"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToWorking.php.html#87"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::calculateTravelTime">calculateTravelTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Unresolved.php.html#14"><abbr title="App\States\ServiceCalls\Unresolved::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Unresolved.php.html#19"><abbr title="App\States\ServiceCalls\Unresolved::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Unresolved.php.html#24"><abbr title="App\States\ServiceCalls\Unresolved::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Working.php.html#12"><abbr title="App\States\ServiceCalls\Working::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Working.php.html#17"><abbr title="App\States\ServiceCalls\Working::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceCalls/Working.php.html#22"><abbr title="App\States\ServiceCalls\Working::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/AwaitingAvailability.php.html#12"><abbr title="App\States\ServiceRequests\AwaitingAvailability::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/AwaitingAvailability.php.html#17"><abbr title="App\States\ServiceRequests\AwaitingAvailability::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/AwaitingAvailability.php.html#22"><abbr title="App\States\ServiceRequests\AwaitingAvailability::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/AwaitingAvailability.php.html#30"><abbr title="App\States\ServiceRequests\AwaitingAvailability::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Closed.php.html#12"><abbr title="App\States\ServiceRequests\Closed::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Closed.php.html#17"><abbr title="App\States\ServiceRequests\Closed::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Closed.php.html#22"><abbr title="App\States\ServiceRequests\Closed::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Closed.php.html#30"><abbr title="App\States\ServiceRequests\Closed::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/CreateWorkOrder.php.html#12"><abbr title="App\States\ServiceRequests\CreateWorkOrder::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/CreateWorkOrder.php.html#17"><abbr title="App\States\ServiceRequests\CreateWorkOrder::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/CreateWorkOrder.php.html#22"><abbr title="App\States\ServiceRequests\CreateWorkOrder::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/CreateWorkOrder.php.html#30"><abbr title="App\States\ServiceRequests\CreateWorkOrder::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Created.php.html#12"><abbr title="App\States\ServiceRequests\Created::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Created.php.html#30"><abbr title="App\States\ServiceRequests\Created::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/InProgress.php.html#12"><abbr title="App\States\ServiceRequests\InProgress::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/InProgress.php.html#17"><abbr title="App\States\ServiceRequests\InProgress::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/InProgress.php.html#22"><abbr title="App\States\ServiceRequests\InProgress::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/InProgress.php.html#30"><abbr title="App\States\ServiceRequests\InProgress::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Scoping.php.html#12"><abbr title="App\States\ServiceRequests\Scoping::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Scoping.php.html#17"><abbr title="App\States\ServiceRequests\Scoping::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Scoping.php.html#22"><abbr title="App\States\ServiceRequests\Scoping::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Scoping.php.html#30"><abbr title="App\States\ServiceRequests\Scoping::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToAwaitingAvailability.php.html#15"><abbr title="App\States\ServiceRequests\Transitions\ToAwaitingAvailability::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToAwaitingAvailability.php.html#21"><abbr title="App\States\ServiceRequests\Transitions\ToAwaitingAvailability::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToClosed.php.html#15"><abbr title="App\States\ServiceRequests\Transitions\ToClosed::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToClosed.php.html#21"><abbr title="App\States\ServiceRequests\Transitions\ToClosed::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToCreateWorkOrder.php.html#15"><abbr title="App\States\ServiceRequests\Transitions\ToCreateWorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToCreateWorkOrder.php.html#21"><abbr title="App\States\ServiceRequests\Transitions\ToCreateWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToInProgress.php.html#15"><abbr title="App\States\ServiceRequests\Transitions\ToInProgress::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToInProgress.php.html#21"><abbr title="App\States\ServiceRequests\Transitions\ToInProgress::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToScoping.php.html#15"><abbr title="App\States\ServiceRequests\Transitions\ToScoping::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequests/Transitions/ToScoping.php.html#21"><abbr title="App\States\ServiceRequests\Transitions\ToScoping::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Assigned.php.html#21"><abbr title="App\States\WorkOrderIssue\Assigned::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Assigned.php.html#26"><abbr title="App\States\WorkOrderIssue\Assigned::colorClassForMobile">colorClassForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Canceled.php.html#16"><abbr title="App\States\WorkOrderIssue\Canceled::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Canceled.php.html#21"><abbr title="App\States\WorkOrderIssue\Canceled::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Canceled.php.html#26"><abbr title="App\States\WorkOrderIssue\Canceled::colorClassForMobile">colorClassForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Canceled.php.html#34"><abbr title="App\States\WorkOrderIssue\Canceled::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Done.php.html#16"><abbr title="App\States\WorkOrderIssue\Done::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Done.php.html#21"><abbr title="App\States\WorkOrderIssue\Done::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Done.php.html#26"><abbr title="App\States\WorkOrderIssue\Done::colorClassForMobile">colorClassForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Done.php.html#34"><abbr title="App\States\WorkOrderIssue\Done::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MissingInfo.php.html#14"><abbr title="App\States\WorkOrderIssue\MissingInfo::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MissingInfo.php.html#19"><abbr title="App\States\WorkOrderIssue\MissingInfo::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MissingInfo.php.html#24"><abbr title="App\States\WorkOrderIssue\MissingInfo::colorClassForMobile">colorClassForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MissingInfo.php.html#32"><abbr title="App\States\WorkOrderIssue\MissingInfo::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/QualityCheck.php.html#16"><abbr title="App\States\WorkOrderIssue\QualityCheck::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/QualityCheck.php.html#21"><abbr title="App\States\WorkOrderIssue\QualityCheck::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/QualityCheck.php.html#26"><abbr title="App\States\WorkOrderIssue\QualityCheck::colorClassForMobile">colorClassForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/QualityCheck.php.html#34"><abbr title="App\States\WorkOrderIssue\QualityCheck::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToAssigned.php.html#12"><abbr title="App\States\WorkOrderIssue\Transitions\ToAssigned::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToAssigned.php.html#16"><abbr title="App\States\WorkOrderIssue\Transitions\ToAssigned::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToCanceled.php.html#13"><abbr title="App\States\WorkOrderIssue\Transitions\ToCanceled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToCanceled.php.html#15"><abbr title="App\States\WorkOrderIssue\Transitions\ToCanceled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToDone.php.html#16"><abbr title="App\States\WorkOrderIssue\Transitions\ToDone::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToDone.php.html#21"><abbr title="App\States\WorkOrderIssue\Transitions\ToDone::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToMissingInfo.php.html#12"><abbr title="App\States\WorkOrderIssue\Transitions\ToMissingInfo::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToMissingInfo.php.html#14"><abbr title="App\States\WorkOrderIssue\Transitions\ToMissingInfo::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToQualityCheck.php.html#12"><abbr title="App\States\WorkOrderIssue\Transitions\ToQualityCheck::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToQualityCheck.php.html#14"><abbr title="App\States\WorkOrderIssue\Transitions\ToQualityCheck::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToUnresolved.php.html#13"><abbr title="App\States\WorkOrderIssue\Transitions\ToUnresolved::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Transitions/ToUnresolved.php.html#15"><abbr title="App\States\WorkOrderIssue\Transitions\ToUnresolved::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Unresolved.php.html#16"><abbr title="App\States\WorkOrderIssue\Unresolved::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Unresolved.php.html#21"><abbr title="App\States\WorkOrderIssue\Unresolved::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Unresolved.php.html#26"><abbr title="App\States\WorkOrderIssue\Unresolved::colorClassForMobile">colorClassForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/Unresolved.php.html#34"><abbr title="App\States\WorkOrderIssue\Unresolved::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailability.php.html#14"><abbr title="App\States\WorkOrders\AwaitingAvailability::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailability.php.html#19"><abbr title="App\States\WorkOrders\AwaitingAvailability::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailability.php.html#24"><abbr title="App\States\WorkOrders\AwaitingAvailability::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailability.php.html#32"><abbr title="App\States\WorkOrders\AwaitingAvailability::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Canceled.php.html#14"><abbr title="App\States\WorkOrders\Canceled::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ClaimPending.php.html#14"><abbr title="App\States\WorkOrders\ClaimPending::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ClaimPending.php.html#19"><abbr title="App\States\WorkOrders\ClaimPending::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ClaimPending.php.html#24"><abbr title="App\States\WorkOrders\ClaimPending::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ClaimPending.php.html#32"><abbr title="App\States\WorkOrders\ClaimPending::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Completed.php.html#14"><abbr title="App\States\WorkOrders\Completed::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Created.php.html#14"><abbr title="App\States\WorkOrders\Created::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Created.php.html#19"><abbr title="App\States\WorkOrders\Created::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Created.php.html#24"><abbr title="App\States\WorkOrders\Created::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Created.php.html#32"><abbr title="App\States\WorkOrders\Created::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Paused.php.html#14"><abbr title="App\States\WorkOrders\Paused::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/QualityCheck.php.html#14"><abbr title="App\States\WorkOrders\QualityCheck::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/QualityCheck.php.html#19"><abbr title="App\States\WorkOrders\QualityCheck::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/QualityCheck.php.html#24"><abbr title="App\States\WorkOrders\QualityCheck::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/QualityCheck.php.html#32"><abbr title="App\States\WorkOrders\QualityCheck::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoice.php.html#14"><abbr title="App\States\WorkOrders\ReadyToInvoice::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoice.php.html#19"><abbr title="App\States\WorkOrders\ReadyToInvoice::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoice.php.html#24"><abbr title="App\States\WorkOrders\ReadyToInvoice::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoice.php.html#32"><abbr title="App\States\WorkOrders\ReadyToInvoice::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToSchedule.php.html#14"><abbr title="App\States\WorkOrders\ReadyToSchedule::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToSchedule.php.html#19"><abbr title="App\States\WorkOrders\ReadyToSchedule::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToSchedule.php.html#24"><abbr title="App\States\WorkOrders\ReadyToSchedule::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToSchedule.php.html#32"><abbr title="App\States\WorkOrders\ReadyToSchedule::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Scheduled.php.html#14"><abbr title="App\States\WorkOrders\Scheduled::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/SchedulingInProgress.php.html#14"><abbr title="App\States\WorkOrders\SchedulingInProgress::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/SchedulingInProgress.php.html#19"><abbr title="App\States\WorkOrders\SchedulingInProgress::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/SchedulingInProgress.php.html#24"><abbr title="App\States\WorkOrders\SchedulingInProgress::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/SchedulingInProgress.php.html#32"><abbr title="App\States\WorkOrders\SchedulingInProgress::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToAwaitingAvailability.php.html#16"><abbr title="App\States\WorkOrders\Transitions\ToAwaitingAvailability::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToAwaitingAvailability.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToAwaitingAvailability::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCanceled.php.html#18"><abbr title="App\States\WorkOrders\Transitions\ToCanceled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCanceled.php.html#20"><abbr title="App\States\WorkOrders\Transitions\ToCanceled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToClaimPending.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToClaimPending::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToClaimPending.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToClaimPending::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCompleted.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToCompleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCompleted.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToCompleted::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCreated.php.html#20"><abbr title="App\States\WorkOrders\Transitions\ToCreated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCreated.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToCreated::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToPaused.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToPaused::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToPaused.php.html#30"><abbr title="App\States\WorkOrders\Transitions\ToPaused::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToQualityCheck.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToQualityCheck::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToQualityCheck.php.html#27"><abbr title="App\States\WorkOrders\Transitions\ToQualityCheck::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToReadyToInvoice.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToReadyToInvoice::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToReadyToInvoice.php.html#23"><abbr title="App\States\WorkOrders\Transitions\ToReadyToInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToReadyToSchedule.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToReadyToSchedule::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToReadyToSchedule.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToReadyToSchedule::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToScheduled.php.html#32"><abbr title="App\States\WorkOrders\Transitions\ToScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToScheduled.php.html#46"><abbr title="App\States\WorkOrders\Transitions\ToScheduled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToWorkInProgress.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToWorkInProgress::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToWorkInProgress.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToWorkInProgress::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/WorkInProgress.php.html#14"><abbr title="App\States\WorkOrders\WorkInProgress::color">color</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ServiceCalls/Transitions/ToReScheduled.php.html#36"><abbr title="App\States\ServiceCalls\Transitions\ToReScheduled::handle">handle</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToScheduled.php.html#46"><abbr title="App\States\WorkOrders\Transitions\ToScheduled::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCreated.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToCreated::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToPaused.php.html#30"><abbr title="App\States\WorkOrders\Transitions\ToPaused::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnded.php.html#37"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCompleted.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToCompleted::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnRoute.php.html#34"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoute::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnRoutePaused.php.html#77"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::calculateDriveTime">calculateDriveTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToEnded.php.html#104"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToPaused.php.html#66"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToWorking.php.html#32"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceCalls/Transitions/ToWorking.php.html#87"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::calculateTravelTime">calculateTravelTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/Transitions/ToCanceled.php.html#20"><abbr title="App\States\WorkOrders\Transitions\ToCanceled::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Mon Jun 30 0:32:48 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([85,0,0,1,0,0,0,1,0,6,1,6], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([242,0,0,0,0,0,0,0,0,0,0,49], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"Invoices\/Draft.php.html#5\">App\\States\\Invoices\\Draft<\/a>"],[100,1,"<a href=\"Invoices\/InvoiceState.php.html#17\">App\\States\\Invoices\\InvoiceState<\/a>"],[0,4,"<a href=\"Invoices\/Paid.php.html#5\">App\\States\\Invoices\\Paid<\/a>"],[0,4,"<a href=\"Invoices\/PartiallyPaid.php.html#5\">App\\States\\Invoices\\PartiallyPaid<\/a>"],[0,4,"<a href=\"Invoices\/PaymentPending.php.html#5\">App\\States\\Invoices\\PaymentPending<\/a>"],[0,2,"<a href=\"Invoices\/Transitions\/ToDraft.php.html#12\">App\\States\\Invoices\\Transitions\\ToDraft<\/a>"],[0,2,"<a href=\"Invoices\/Transitions\/ToPaid.php.html#12\">App\\States\\Invoices\\Transitions\\ToPaid<\/a>"],[0,2,"<a href=\"Invoices\/Transitions\/ToPartiallyPaid.php.html#12\">App\\States\\Invoices\\Transitions\\ToPartiallyPaid<\/a>"],[0,2,"<a href=\"Invoices\/Transitions\/ToPaymentPending.php.html#12\">App\\States\\Invoices\\Transitions\\ToPaymentPending<\/a>"],[0,2,"<a href=\"Invoices\/Transitions\/ToVoided.php.html#12\">App\\States\\Invoices\\Transitions\\ToVoided<\/a>"],[0,4,"<a href=\"Invoices\/Voided.php.html#5\">App\\States\\Invoices\\Voided<\/a>"],[0,2,"<a href=\"Issue\/Assigned.php.html#9\">App\\States\\Issue\\Assigned<\/a>"],[0,2,"<a href=\"Issue\/Canceled.php.html#9\">App\\States\\Issue\\Canceled<\/a>"],[0,2,"<a href=\"Issue\/Done.php.html#9\">App\\States\\Issue\\Done<\/a>"],[100,1,"<a href=\"Issue\/IssueState.php.html#19\">App\\States\\Issue\\IssueState<\/a>"],[0,2,"<a href=\"Issue\/QualityCheck.php.html#9\">App\\States\\Issue\\QualityCheck<\/a>"],[0,2,"<a href=\"Issue\/Transitions\/ToAssigned.php.html#16\">App\\States\\Issue\\Transitions\\ToAssigned<\/a>"],[0,2,"<a href=\"Issue\/Transitions\/ToCanceled.php.html#12\">App\\States\\Issue\\Transitions\\ToCanceled<\/a>"],[0,2,"<a href=\"Issue\/Transitions\/ToQualityCheck.php.html#10\">App\\States\\Issue\\Transitions\\ToQualityCheck<\/a>"],[0,2,"<a href=\"Issue\/Transitions\/ToUnassigned.php.html#10\">App\\States\\Issue\\Transitions\\ToUnassigned<\/a>"],[0,2,"<a href=\"Issue\/Transitions\/ToUnresolved.php.html#10\">App\\States\\Issue\\Transitions\\ToUnresolved<\/a>"],[87.5,2,"<a href=\"Issue\/Unassigned.php.html#9\">App\\States\\Issue\\Unassigned<\/a>"],[0,2,"<a href=\"Issue\/Unresolved.php.html#9\">App\\States\\Issue\\Unresolved<\/a>"],[0,3,"<a href=\"ServiceCalls\/Canceled.php.html#5\">App\\States\\ServiceCalls\\Canceled<\/a>"],[0,3,"<a href=\"ServiceCalls\/ClaimPending.php.html#5\">App\\States\\ServiceCalls\\ClaimPending<\/a>"],[0,3,"<a href=\"ServiceCalls\/Done.php.html#5\">App\\States\\ServiceCalls\\Done<\/a>"],[0,3,"<a href=\"ServiceCalls\/EnRoute.php.html#5\">App\\States\\ServiceCalls\\EnRoute<\/a>"],[0,3,"<a href=\"ServiceCalls\/EnRoutePaused.php.html#5\">App\\States\\ServiceCalls\\EnRoutePaused<\/a>"],[0,3,"<a href=\"ServiceCalls\/Ended.php.html#5\">App\\States\\ServiceCalls\\Ended<\/a>"],[0,3,"<a href=\"ServiceCalls\/MissingInfo.php.html#7\">App\\States\\ServiceCalls\\MissingInfo<\/a>"],[0,3,"<a href=\"ServiceCalls\/Paused.php.html#5\">App\\States\\ServiceCalls\\Paused<\/a>"],[0,3,"<a href=\"ServiceCalls\/ReScheduled.php.html#5\">App\\States\\ServiceCalls\\ReScheduled<\/a>"],[0,3,"<a href=\"ServiceCalls\/ScheduleInPending.php.html#5\">App\\States\\ServiceCalls\\ScheduleInPending<\/a>"],[0,3,"<a href=\"ServiceCalls\/ScheduleInProgress.php.html#5\">App\\States\\ServiceCalls\\ScheduleInProgress<\/a>"],[66.66666666666666,3,"<a href=\"ServiceCalls\/Scheduled.php.html#5\">App\\States\\ServiceCalls\\Scheduled<\/a>"],[100,1,"<a href=\"ServiceCalls\/ServiceCallState.php.html#25\">App\\States\\ServiceCalls\\ServiceCallState<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToCanceled.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToCanceled<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToDone.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToDone<\/a>"],[0,3,"<a href=\"ServiceCalls\/Transitions\/ToEnRoute.php.html#17\">App\\States\\ServiceCalls\\Transitions\\ToEnRoute<\/a>"],[0,4,"<a href=\"ServiceCalls\/Transitions\/ToEnRoutePaused.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused<\/a>"],[0,6,"<a href=\"ServiceCalls\/Transitions\/ToEnded.php.html#18\">App\\States\\ServiceCalls\\Transitions\\ToEnded<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToMissingInfo.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToMissingInfo<\/a>"],[0,4,"<a href=\"ServiceCalls\/Transitions\/ToPaused.php.html#14\">App\\States\\ServiceCalls\\Transitions\\ToPaused<\/a>"],[0,14,"<a href=\"ServiceCalls\/Transitions\/ToReScheduled.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToReScheduled<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToScheduleInPending.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInPending<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToScheduleInProgress.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInProgress<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToScheduled.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToScheduled<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToUnresolved.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToUnresolved<\/a>"],[0,5,"<a href=\"ServiceCalls\/Transitions\/ToWorking.php.html#17\">App\\States\\ServiceCalls\\Transitions\\ToWorking<\/a>"],[0,3,"<a href=\"ServiceCalls\/Unresolved.php.html#7\">App\\States\\ServiceCalls\\Unresolved<\/a>"],[0,3,"<a href=\"ServiceCalls\/Working.php.html#5\">App\\States\\ServiceCalls\\Working<\/a>"],[0,4,"<a href=\"ServiceRequests\/AwaitingAvailability.php.html#5\">App\\States\\ServiceRequests\\AwaitingAvailability<\/a>"],[0,4,"<a href=\"ServiceRequests\/Closed.php.html#5\">App\\States\\ServiceRequests\\Closed<\/a>"],[0,4,"<a href=\"ServiceRequests\/CreateWorkOrder.php.html#5\">App\\States\\ServiceRequests\\CreateWorkOrder<\/a>"],[28.57142857142857,4,"<a href=\"ServiceRequests\/Created.php.html#5\">App\\States\\ServiceRequests\\Created<\/a>"],[0,4,"<a href=\"ServiceRequests\/InProgress.php.html#5\">App\\States\\ServiceRequests\\InProgress<\/a>"],[0,4,"<a href=\"ServiceRequests\/Scoping.php.html#5\">App\\States\\ServiceRequests\\Scoping<\/a>"],[100,1,"<a href=\"ServiceRequests\/ServiceRequestState.php.html#19\">App\\States\\ServiceRequests\\ServiceRequestState<\/a>"],[0,2,"<a href=\"ServiceRequests\/Transitions\/ToAwaitingAvailability.php.html#13\">App\\States\\ServiceRequests\\Transitions\\ToAwaitingAvailability<\/a>"],[0,2,"<a href=\"ServiceRequests\/Transitions\/ToClosed.php.html#13\">App\\States\\ServiceRequests\\Transitions\\ToClosed<\/a>"],[0,2,"<a href=\"ServiceRequests\/Transitions\/ToCreateWorkOrder.php.html#13\">App\\States\\ServiceRequests\\Transitions\\ToCreateWorkOrder<\/a>"],[0,2,"<a href=\"ServiceRequests\/Transitions\/ToInProgress.php.html#13\">App\\States\\ServiceRequests\\Transitions\\ToInProgress<\/a>"],[0,2,"<a href=\"ServiceRequests\/Transitions\/ToScoping.php.html#13\">App\\States\\ServiceRequests\\Transitions\\ToScoping<\/a>"],[80,4,"<a href=\"WorkOrderIssue\/Assigned.php.html#9\">App\\States\\WorkOrderIssue\\Assigned<\/a>"],[0,4,"<a href=\"WorkOrderIssue\/Canceled.php.html#9\">App\\States\\WorkOrderIssue\\Canceled<\/a>"],[0,4,"<a href=\"WorkOrderIssue\/Done.php.html#9\">App\\States\\WorkOrderIssue\\Done<\/a>"],[0,4,"<a href=\"WorkOrderIssue\/MissingInfo.php.html#7\">App\\States\\WorkOrderIssue\\MissingInfo<\/a>"],[0,4,"<a href=\"WorkOrderIssue\/QualityCheck.php.html#9\">App\\States\\WorkOrderIssue\\QualityCheck<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/Transitions\/ToAssigned.php.html#10\">App\\States\\WorkOrderIssue\\Transitions\\ToAssigned<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/Transitions\/ToCanceled.php.html#11\">App\\States\\WorkOrderIssue\\Transitions\\ToCanceled<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/Transitions\/ToDone.php.html#11\">App\\States\\WorkOrderIssue\\Transitions\\ToDone<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/Transitions\/ToMissingInfo.php.html#10\">App\\States\\WorkOrderIssue\\Transitions\\ToMissingInfo<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/Transitions\/ToQualityCheck.php.html#10\">App\\States\\WorkOrderIssue\\Transitions\\ToQualityCheck<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/Transitions\/ToUnresolved.php.html#11\">App\\States\\WorkOrderIssue\\Transitions\\ToUnresolved<\/a>"],[0,4,"<a href=\"WorkOrderIssue\/Unresolved.php.html#9\">App\\States\\WorkOrderIssue\\Unresolved<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/WorkOrderIssueState.php.html#19\">App\\States\\WorkOrderIssue\\WorkOrderIssueState<\/a>"],[0,4,"<a href=\"WorkOrders\/AwaitingAvailability.php.html#7\">App\\States\\WorkOrders\\AwaitingAvailability<\/a>"],[85.71428571428571,4,"<a href=\"WorkOrders\/Canceled.php.html#7\">App\\States\\WorkOrders\\Canceled<\/a>"],[0,4,"<a href=\"WorkOrders\/ClaimPending.php.html#7\">App\\States\\WorkOrders\\ClaimPending<\/a>"],[83.33333333333334,4,"<a href=\"WorkOrders\/Completed.php.html#7\">App\\States\\WorkOrders\\Completed<\/a>"],[0,4,"<a href=\"WorkOrders\/Created.php.html#7\">App\\States\\WorkOrders\\Created<\/a>"],[85.71428571428571,4,"<a href=\"WorkOrders\/Paused.php.html#7\">App\\States\\WorkOrders\\Paused<\/a>"],[0,4,"<a href=\"WorkOrders\/QualityCheck.php.html#7\">App\\States\\WorkOrders\\QualityCheck<\/a>"],[0,4,"<a href=\"WorkOrders\/ReadyToInvoice.php.html#7\">App\\States\\WorkOrders\\ReadyToInvoice<\/a>"],[0,4,"<a href=\"WorkOrders\/ReadyToSchedule.php.html#7\">App\\States\\WorkOrders\\ReadyToSchedule<\/a>"],[88.88888888888889,4,"<a href=\"WorkOrders\/Scheduled.php.html#7\">App\\States\\WorkOrders\\Scheduled<\/a>"],[0,4,"<a href=\"WorkOrders\/SchedulingInProgress.php.html#7\">App\\States\\WorkOrders\\SchedulingInProgress<\/a>"],[0,2,"<a href=\"WorkOrders\/Transitions\/ToAwaitingAvailability.php.html#14\">App\\States\\WorkOrders\\Transitions\\ToAwaitingAvailability<\/a>"],[0,3,"<a href=\"WorkOrders\/Transitions\/ToCanceled.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToCanceled<\/a>"],[0,2,"<a href=\"WorkOrders\/Transitions\/ToClaimPending.php.html#14\">App\\States\\WorkOrders\\Transitions\\ToClaimPending<\/a>"],[0,4,"<a href=\"WorkOrders\/Transitions\/ToCompleted.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToCompleted<\/a>"],[0,6,"<a href=\"WorkOrders\/Transitions\/ToCreated.php.html#18\">App\\States\\WorkOrders\\Transitions\\ToCreated<\/a>"],[0,5,"<a href=\"WorkOrders\/Transitions\/ToPaused.php.html#16\">App\\States\\WorkOrders\\Transitions\\ToPaused<\/a>"],[0,2,"<a href=\"WorkOrders\/Transitions\/ToQualityCheck.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToQualityCheck<\/a>"],[0,2,"<a href=\"WorkOrders\/Transitions\/ToReadyToInvoice.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToReadyToInvoice<\/a>"],[0,2,"<a href=\"WorkOrders\/Transitions\/ToReadyToSchedule.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToReadyToSchedule<\/a>"],[0,10,"<a href=\"WorkOrders\/Transitions\/ToScheduled.php.html#29\">App\\States\\WorkOrders\\Transitions\\ToScheduled<\/a>"],[0,2,"<a href=\"WorkOrders\/Transitions\/ToWorkInProgress.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToWorkInProgress<\/a>"],[90.9090909090909,4,"<a href=\"WorkOrders\/WorkInProgress.php.html#7\">App\\States\\WorkOrders\\WorkInProgress<\/a>"],[100,1,"<a href=\"WorkOrders\/WorkOrderState.php.html#24\">App\\States\\WorkOrders\\WorkOrderState<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Invoices\/Draft.php.html#12\">App\\States\\Invoices\\Draft::color<\/a>"],[0,1,"<a href=\"Invoices\/Draft.php.html#17\">App\\States\\Invoices\\Draft::label<\/a>"],[0,1,"<a href=\"Invoices\/Draft.php.html#22\">App\\States\\Invoices\\Draft::colorClass<\/a>"],[0,1,"<a href=\"Invoices\/Draft.php.html#30\">App\\States\\Invoices\\Draft::actions<\/a>"],[100,1,"<a href=\"Invoices\/InvoiceState.php.html#22\">App\\States\\Invoices\\InvoiceState::config<\/a>"],[100,0,"<a href=\"Invoices\/InvoiceState.php.html#47\">App\\States\\Invoices\\InvoiceState::color<\/a>"],[100,0,"<a href=\"Invoices\/InvoiceState.php.html#49\">App\\States\\Invoices\\InvoiceState::colorClass<\/a>"],[100,0,"<a href=\"Invoices\/InvoiceState.php.html#51\">App\\States\\Invoices\\InvoiceState::label<\/a>"],[100,0,"<a href=\"Invoices\/InvoiceState.php.html#56\">App\\States\\Invoices\\InvoiceState::actions<\/a>"],[0,1,"<a href=\"Invoices\/Paid.php.html#12\">App\\States\\Invoices\\Paid::color<\/a>"],[0,1,"<a href=\"Invoices\/Paid.php.html#17\">App\\States\\Invoices\\Paid::label<\/a>"],[0,1,"<a href=\"Invoices\/Paid.php.html#22\">App\\States\\Invoices\\Paid::colorClass<\/a>"],[0,1,"<a href=\"Invoices\/Paid.php.html#30\">App\\States\\Invoices\\Paid::actions<\/a>"],[0,1,"<a href=\"Invoices\/PartiallyPaid.php.html#12\">App\\States\\Invoices\\PartiallyPaid::color<\/a>"],[0,1,"<a href=\"Invoices\/PartiallyPaid.php.html#17\">App\\States\\Invoices\\PartiallyPaid::label<\/a>"],[0,1,"<a href=\"Invoices\/PartiallyPaid.php.html#22\">App\\States\\Invoices\\PartiallyPaid::colorClass<\/a>"],[0,1,"<a href=\"Invoices\/PartiallyPaid.php.html#30\">App\\States\\Invoices\\PartiallyPaid::actions<\/a>"],[0,1,"<a href=\"Invoices\/PaymentPending.php.html#12\">App\\States\\Invoices\\PaymentPending::color<\/a>"],[0,1,"<a href=\"Invoices\/PaymentPending.php.html#17\">App\\States\\Invoices\\PaymentPending::label<\/a>"],[0,1,"<a href=\"Invoices\/PaymentPending.php.html#22\">App\\States\\Invoices\\PaymentPending::colorClass<\/a>"],[0,1,"<a href=\"Invoices\/PaymentPending.php.html#30\">App\\States\\Invoices\\PaymentPending::actions<\/a>"],[0,1,"<a href=\"Invoices\/Transitions\/ToDraft.php.html#14\">App\\States\\Invoices\\Transitions\\ToDraft::__construct<\/a>"],[0,1,"<a href=\"Invoices\/Transitions\/ToDraft.php.html#16\">App\\States\\Invoices\\Transitions\\ToDraft::handle<\/a>"],[0,1,"<a href=\"Invoices\/Transitions\/ToPaid.php.html#14\">App\\States\\Invoices\\Transitions\\ToPaid::__construct<\/a>"],[0,1,"<a href=\"Invoices\/Transitions\/ToPaid.php.html#16\">App\\States\\Invoices\\Transitions\\ToPaid::handle<\/a>"],[0,1,"<a href=\"Invoices\/Transitions\/ToPartiallyPaid.php.html#14\">App\\States\\Invoices\\Transitions\\ToPartiallyPaid::__construct<\/a>"],[0,1,"<a href=\"Invoices\/Transitions\/ToPartiallyPaid.php.html#16\">App\\States\\Invoices\\Transitions\\ToPartiallyPaid::handle<\/a>"],[0,1,"<a href=\"Invoices\/Transitions\/ToPaymentPending.php.html#14\">App\\States\\Invoices\\Transitions\\ToPaymentPending::__construct<\/a>"],[0,1,"<a href=\"Invoices\/Transitions\/ToPaymentPending.php.html#16\">App\\States\\Invoices\\Transitions\\ToPaymentPending::handle<\/a>"],[0,1,"<a href=\"Invoices\/Transitions\/ToVoided.php.html#14\">App\\States\\Invoices\\Transitions\\ToVoided::__construct<\/a>"],[0,1,"<a href=\"Invoices\/Transitions\/ToVoided.php.html#16\">App\\States\\Invoices\\Transitions\\ToVoided::handle<\/a>"],[0,1,"<a href=\"Invoices\/Voided.php.html#12\">App\\States\\Invoices\\Voided::color<\/a>"],[0,1,"<a href=\"Invoices\/Voided.php.html#17\">App\\States\\Invoices\\Voided::label<\/a>"],[0,1,"<a href=\"Invoices\/Voided.php.html#22\">App\\States\\Invoices\\Voided::colorClass<\/a>"],[0,1,"<a href=\"Invoices\/Voided.php.html#30\">App\\States\\Invoices\\Voided::actions<\/a>"],[0,1,"<a href=\"Issue\/Assigned.php.html#16\">App\\States\\Issue\\Assigned::label<\/a>"],[0,1,"<a href=\"Issue\/Assigned.php.html#24\">App\\States\\Issue\\Assigned::actions<\/a>"],[0,1,"<a href=\"Issue\/Canceled.php.html#16\">App\\States\\Issue\\Canceled::label<\/a>"],[0,1,"<a href=\"Issue\/Canceled.php.html#24\">App\\States\\Issue\\Canceled::actions<\/a>"],[0,1,"<a href=\"Issue\/Done.php.html#16\">App\\States\\Issue\\Done::label<\/a>"],[0,1,"<a href=\"Issue\/Done.php.html#24\">App\\States\\Issue\\Done::actions<\/a>"],[100,1,"<a href=\"Issue\/IssueState.php.html#24\">App\\States\\Issue\\IssueState::config<\/a>"],[100,0,"<a href=\"Issue\/IssueState.php.html#58\">App\\States\\Issue\\IssueState::label<\/a>"],[100,0,"<a href=\"Issue\/IssueState.php.html#63\">App\\States\\Issue\\IssueState::actions<\/a>"],[0,1,"<a href=\"Issue\/QualityCheck.php.html#16\">App\\States\\Issue\\QualityCheck::label<\/a>"],[0,1,"<a href=\"Issue\/QualityCheck.php.html#24\">App\\States\\Issue\\QualityCheck::actions<\/a>"],[0,1,"<a href=\"Issue\/Transitions\/ToAssigned.php.html#18\">App\\States\\Issue\\Transitions\\ToAssigned::__construct<\/a>"],[0,1,"<a href=\"Issue\/Transitions\/ToAssigned.php.html#24\">App\\States\\Issue\\Transitions\\ToAssigned::handle<\/a>"],[0,1,"<a href=\"Issue\/Transitions\/ToCanceled.php.html#14\">App\\States\\Issue\\Transitions\\ToCanceled::__construct<\/a>"],[0,1,"<a href=\"Issue\/Transitions\/ToCanceled.php.html#19\">App\\States\\Issue\\Transitions\\ToCanceled::handle<\/a>"],[0,1,"<a href=\"Issue\/Transitions\/ToQualityCheck.php.html#12\">App\\States\\Issue\\Transitions\\ToQualityCheck::__construct<\/a>"],[0,1,"<a href=\"Issue\/Transitions\/ToQualityCheck.php.html#16\">App\\States\\Issue\\Transitions\\ToQualityCheck::handle<\/a>"],[0,1,"<a href=\"Issue\/Transitions\/ToUnassigned.php.html#12\">App\\States\\Issue\\Transitions\\ToUnassigned::__construct<\/a>"],[0,1,"<a href=\"Issue\/Transitions\/ToUnassigned.php.html#16\">App\\States\\Issue\\Transitions\\ToUnassigned::handle<\/a>"],[0,1,"<a href=\"Issue\/Transitions\/ToUnresolved.php.html#12\">App\\States\\Issue\\Transitions\\ToUnresolved::__construct<\/a>"],[0,1,"<a href=\"Issue\/Transitions\/ToUnresolved.php.html#16\">App\\States\\Issue\\Transitions\\ToUnresolved::handle<\/a>"],[0,1,"<a href=\"Issue\/Unassigned.php.html#16\">App\\States\\Issue\\Unassigned::label<\/a>"],[100,1,"<a href=\"Issue\/Unassigned.php.html#24\">App\\States\\Issue\\Unassigned::actions<\/a>"],[0,1,"<a href=\"Issue\/Unresolved.php.html#16\">App\\States\\Issue\\Unresolved::label<\/a>"],[0,1,"<a href=\"Issue\/Unresolved.php.html#24\">App\\States\\Issue\\Unresolved::actions<\/a>"],[0,1,"<a href=\"ServiceCalls\/Canceled.php.html#12\">App\\States\\ServiceCalls\\Canceled::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/Canceled.php.html#17\">App\\States\\ServiceCalls\\Canceled::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/Canceled.php.html#22\">App\\States\\ServiceCalls\\Canceled::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/ClaimPending.php.html#12\">App\\States\\ServiceCalls\\ClaimPending::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/ClaimPending.php.html#17\">App\\States\\ServiceCalls\\ClaimPending::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/ClaimPending.php.html#22\">App\\States\\ServiceCalls\\ClaimPending::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/Done.php.html#12\">App\\States\\ServiceCalls\\Done::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/Done.php.html#17\">App\\States\\ServiceCalls\\Done::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/Done.php.html#22\">App\\States\\ServiceCalls\\Done::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/EnRoute.php.html#12\">App\\States\\ServiceCalls\\EnRoute::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/EnRoute.php.html#17\">App\\States\\ServiceCalls\\EnRoute::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/EnRoute.php.html#22\">App\\States\\ServiceCalls\\EnRoute::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/EnRoutePaused.php.html#12\">App\\States\\ServiceCalls\\EnRoutePaused::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/EnRoutePaused.php.html#17\">App\\States\\ServiceCalls\\EnRoutePaused::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/EnRoutePaused.php.html#22\">App\\States\\ServiceCalls\\EnRoutePaused::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/Ended.php.html#12\">App\\States\\ServiceCalls\\Ended::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/Ended.php.html#17\">App\\States\\ServiceCalls\\Ended::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/Ended.php.html#22\">App\\States\\ServiceCalls\\Ended::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/MissingInfo.php.html#14\">App\\States\\ServiceCalls\\MissingInfo::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/MissingInfo.php.html#19\">App\\States\\ServiceCalls\\MissingInfo::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/MissingInfo.php.html#24\">App\\States\\ServiceCalls\\MissingInfo::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/Paused.php.html#12\">App\\States\\ServiceCalls\\Paused::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/Paused.php.html#17\">App\\States\\ServiceCalls\\Paused::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/Paused.php.html#22\">App\\States\\ServiceCalls\\Paused::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/ReScheduled.php.html#12\">App\\States\\ServiceCalls\\ReScheduled::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/ReScheduled.php.html#17\">App\\States\\ServiceCalls\\ReScheduled::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/ReScheduled.php.html#22\">App\\States\\ServiceCalls\\ReScheduled::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/ScheduleInPending.php.html#12\">App\\States\\ServiceCalls\\ScheduleInPending::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/ScheduleInPending.php.html#17\">App\\States\\ServiceCalls\\ScheduleInPending::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/ScheduleInPending.php.html#22\">App\\States\\ServiceCalls\\ScheduleInPending::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/ScheduleInProgress.php.html#12\">App\\States\\ServiceCalls\\ScheduleInProgress::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/ScheduleInProgress.php.html#17\">App\\States\\ServiceCalls\\ScheduleInProgress::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/ScheduleInProgress.php.html#22\">App\\States\\ServiceCalls\\ScheduleInProgress::colorClass<\/a>"],[100,1,"<a href=\"ServiceCalls\/Scheduled.php.html#12\">App\\States\\ServiceCalls\\Scheduled::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/Scheduled.php.html#17\">App\\States\\ServiceCalls\\Scheduled::labelForMobile<\/a>"],[100,1,"<a href=\"ServiceCalls\/Scheduled.php.html#22\">App\\States\\ServiceCalls\\Scheduled::colorClass<\/a>"],[100,1,"<a href=\"ServiceCalls\/ServiceCallState.php.html#30\">App\\States\\ServiceCalls\\ServiceCallState::config<\/a>"],[100,0,"<a href=\"ServiceCalls\/ServiceCallState.php.html#86\">App\\States\\ServiceCalls\\ServiceCallState::label<\/a>"],[100,0,"<a href=\"ServiceCalls\/ServiceCallState.php.html#88\">App\\States\\ServiceCalls\\ServiceCallState::labelForMobile<\/a>"],[100,0,"<a href=\"ServiceCalls\/ServiceCallState.php.html#90\">App\\States\\ServiceCalls\\ServiceCallState::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToCanceled.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToCanceled::__construct<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToCanceled.php.html#22\">App\\States\\ServiceCalls\\Transitions\\ToCanceled::handle<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToDone.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToDone::__construct<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToDone.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToDone::handle<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToEnRoute.php.html#21\">App\\States\\ServiceCalls\\Transitions\\ToEnRoute::__construct<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToEnRoute.php.html#34\">App\\States\\ServiceCalls\\Transitions\\ToEnRoute::handle<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToEnRoutePaused.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused::__construct<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToEnRoutePaused.php.html#31\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused::handle<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToEnRoutePaused.php.html#77\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused::calculateDriveTime<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToEnded.php.html#25\">App\\States\\ServiceCalls\\Transitions\\ToEnded::__construct<\/a>"],[0,3,"<a href=\"ServiceCalls\/Transitions\/ToEnded.php.html#37\">App\\States\\ServiceCalls\\Transitions\\ToEnded::handle<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToEnded.php.html#104\">App\\States\\ServiceCalls\\Transitions\\ToEnded::calculateWorkTime<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToMissingInfo.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToMissingInfo::__construct<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToMissingInfo.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToMissingInfo::handle<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToPaused.php.html#18\">App\\States\\ServiceCalls\\Transitions\\ToPaused::__construct<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToPaused.php.html#26\">App\\States\\ServiceCalls\\Transitions\\ToPaused::handle<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToPaused.php.html#66\">App\\States\\ServiceCalls\\Transitions\\ToPaused::calculateWorkTime<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToReScheduled.php.html#21\">App\\States\\ServiceCalls\\Transitions\\ToReScheduled::__construct<\/a>"],[0,13,"<a href=\"ServiceCalls\/Transitions\/ToReScheduled.php.html#36\">App\\States\\ServiceCalls\\Transitions\\ToReScheduled::handle<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToScheduleInPending.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInPending::__construct<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToScheduleInPending.php.html#18\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInPending::handle<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToScheduleInProgress.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInProgress::__construct<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToScheduleInProgress.php.html#22\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInProgress::handle<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToScheduled.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToScheduled::__construct<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToScheduled.php.html#22\">App\\States\\ServiceCalls\\Transitions\\ToScheduled::handle<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToUnresolved.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToUnresolved::__construct<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToUnresolved.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToUnresolved::handle<\/a>"],[0,1,"<a href=\"ServiceCalls\/Transitions\/ToWorking.php.html#21\">App\\States\\ServiceCalls\\Transitions\\ToWorking::__construct<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToWorking.php.html#32\">App\\States\\ServiceCalls\\Transitions\\ToWorking::handle<\/a>"],[0,2,"<a href=\"ServiceCalls\/Transitions\/ToWorking.php.html#87\">App\\States\\ServiceCalls\\Transitions\\ToWorking::calculateTravelTime<\/a>"],[0,1,"<a href=\"ServiceCalls\/Unresolved.php.html#14\">App\\States\\ServiceCalls\\Unresolved::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/Unresolved.php.html#19\">App\\States\\ServiceCalls\\Unresolved::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/Unresolved.php.html#24\">App\\States\\ServiceCalls\\Unresolved::colorClass<\/a>"],[0,1,"<a href=\"ServiceCalls\/Working.php.html#12\">App\\States\\ServiceCalls\\Working::label<\/a>"],[0,1,"<a href=\"ServiceCalls\/Working.php.html#17\">App\\States\\ServiceCalls\\Working::labelForMobile<\/a>"],[0,1,"<a href=\"ServiceCalls\/Working.php.html#22\">App\\States\\ServiceCalls\\Working::colorClass<\/a>"],[0,1,"<a href=\"ServiceRequests\/AwaitingAvailability.php.html#12\">App\\States\\ServiceRequests\\AwaitingAvailability::color<\/a>"],[0,1,"<a href=\"ServiceRequests\/AwaitingAvailability.php.html#17\">App\\States\\ServiceRequests\\AwaitingAvailability::label<\/a>"],[0,1,"<a href=\"ServiceRequests\/AwaitingAvailability.php.html#22\">App\\States\\ServiceRequests\\AwaitingAvailability::colorClass<\/a>"],[0,1,"<a href=\"ServiceRequests\/AwaitingAvailability.php.html#30\">App\\States\\ServiceRequests\\AwaitingAvailability::actions<\/a>"],[0,1,"<a href=\"ServiceRequests\/Closed.php.html#12\">App\\States\\ServiceRequests\\Closed::color<\/a>"],[0,1,"<a href=\"ServiceRequests\/Closed.php.html#17\">App\\States\\ServiceRequests\\Closed::label<\/a>"],[0,1,"<a href=\"ServiceRequests\/Closed.php.html#22\">App\\States\\ServiceRequests\\Closed::colorClass<\/a>"],[0,1,"<a href=\"ServiceRequests\/Closed.php.html#30\">App\\States\\ServiceRequests\\Closed::actions<\/a>"],[0,1,"<a href=\"ServiceRequests\/CreateWorkOrder.php.html#12\">App\\States\\ServiceRequests\\CreateWorkOrder::color<\/a>"],[0,1,"<a href=\"ServiceRequests\/CreateWorkOrder.php.html#17\">App\\States\\ServiceRequests\\CreateWorkOrder::label<\/a>"],[0,1,"<a href=\"ServiceRequests\/CreateWorkOrder.php.html#22\">App\\States\\ServiceRequests\\CreateWorkOrder::colorClass<\/a>"],[0,1,"<a href=\"ServiceRequests\/CreateWorkOrder.php.html#30\">App\\States\\ServiceRequests\\CreateWorkOrder::actions<\/a>"],[0,1,"<a href=\"ServiceRequests\/Created.php.html#12\">App\\States\\ServiceRequests\\Created::color<\/a>"],[100,1,"<a href=\"ServiceRequests\/Created.php.html#17\">App\\States\\ServiceRequests\\Created::label<\/a>"],[100,1,"<a href=\"ServiceRequests\/Created.php.html#22\">App\\States\\ServiceRequests\\Created::colorClass<\/a>"],[0,1,"<a href=\"ServiceRequests\/Created.php.html#30\">App\\States\\ServiceRequests\\Created::actions<\/a>"],[0,1,"<a href=\"ServiceRequests\/InProgress.php.html#12\">App\\States\\ServiceRequests\\InProgress::color<\/a>"],[0,1,"<a href=\"ServiceRequests\/InProgress.php.html#17\">App\\States\\ServiceRequests\\InProgress::label<\/a>"],[0,1,"<a href=\"ServiceRequests\/InProgress.php.html#22\">App\\States\\ServiceRequests\\InProgress::colorClass<\/a>"],[0,1,"<a href=\"ServiceRequests\/InProgress.php.html#30\">App\\States\\ServiceRequests\\InProgress::actions<\/a>"],[0,1,"<a href=\"ServiceRequests\/Scoping.php.html#12\">App\\States\\ServiceRequests\\Scoping::color<\/a>"],[0,1,"<a href=\"ServiceRequests\/Scoping.php.html#17\">App\\States\\ServiceRequests\\Scoping::label<\/a>"],[0,1,"<a href=\"ServiceRequests\/Scoping.php.html#22\">App\\States\\ServiceRequests\\Scoping::colorClass<\/a>"],[0,1,"<a href=\"ServiceRequests\/Scoping.php.html#30\">App\\States\\ServiceRequests\\Scoping::actions<\/a>"],[100,1,"<a href=\"ServiceRequests\/ServiceRequestState.php.html#24\">App\\States\\ServiceRequests\\ServiceRequestState::config<\/a>"],[100,0,"<a href=\"ServiceRequests\/ServiceRequestState.php.html#53\">App\\States\\ServiceRequests\\ServiceRequestState::color<\/a>"],[100,0,"<a href=\"ServiceRequests\/ServiceRequestState.php.html#55\">App\\States\\ServiceRequests\\ServiceRequestState::colorClass<\/a>"],[100,0,"<a href=\"ServiceRequests\/ServiceRequestState.php.html#57\">App\\States\\ServiceRequests\\ServiceRequestState::label<\/a>"],[100,0,"<a href=\"ServiceRequests\/ServiceRequestState.php.html#62\">App\\States\\ServiceRequests\\ServiceRequestState::actions<\/a>"],[0,1,"<a href=\"ServiceRequests\/Transitions\/ToAwaitingAvailability.php.html#15\">App\\States\\ServiceRequests\\Transitions\\ToAwaitingAvailability::__construct<\/a>"],[0,1,"<a href=\"ServiceRequests\/Transitions\/ToAwaitingAvailability.php.html#21\">App\\States\\ServiceRequests\\Transitions\\ToAwaitingAvailability::handle<\/a>"],[0,1,"<a href=\"ServiceRequests\/Transitions\/ToClosed.php.html#15\">App\\States\\ServiceRequests\\Transitions\\ToClosed::__construct<\/a>"],[0,1,"<a href=\"ServiceRequests\/Transitions\/ToClosed.php.html#21\">App\\States\\ServiceRequests\\Transitions\\ToClosed::handle<\/a>"],[0,1,"<a href=\"ServiceRequests\/Transitions\/ToCreateWorkOrder.php.html#15\">App\\States\\ServiceRequests\\Transitions\\ToCreateWorkOrder::__construct<\/a>"],[0,1,"<a href=\"ServiceRequests\/Transitions\/ToCreateWorkOrder.php.html#21\">App\\States\\ServiceRequests\\Transitions\\ToCreateWorkOrder::handle<\/a>"],[0,1,"<a href=\"ServiceRequests\/Transitions\/ToInProgress.php.html#15\">App\\States\\ServiceRequests\\Transitions\\ToInProgress::__construct<\/a>"],[0,1,"<a href=\"ServiceRequests\/Transitions\/ToInProgress.php.html#21\">App\\States\\ServiceRequests\\Transitions\\ToInProgress::handle<\/a>"],[0,1,"<a href=\"ServiceRequests\/Transitions\/ToScoping.php.html#15\">App\\States\\ServiceRequests\\Transitions\\ToScoping::__construct<\/a>"],[0,1,"<a href=\"ServiceRequests\/Transitions\/ToScoping.php.html#21\">App\\States\\ServiceRequests\\Transitions\\ToScoping::handle<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/Assigned.php.html#16\">App\\States\\WorkOrderIssue\\Assigned::label<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Assigned.php.html#21\">App\\States\\WorkOrderIssue\\Assigned::labelForMobile<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Assigned.php.html#26\">App\\States\\WorkOrderIssue\\Assigned::colorClassForMobile<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/Assigned.php.html#34\">App\\States\\WorkOrderIssue\\Assigned::actions<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Canceled.php.html#16\">App\\States\\WorkOrderIssue\\Canceled::label<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Canceled.php.html#21\">App\\States\\WorkOrderIssue\\Canceled::labelForMobile<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Canceled.php.html#26\">App\\States\\WorkOrderIssue\\Canceled::colorClassForMobile<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Canceled.php.html#34\">App\\States\\WorkOrderIssue\\Canceled::actions<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Done.php.html#16\">App\\States\\WorkOrderIssue\\Done::label<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Done.php.html#21\">App\\States\\WorkOrderIssue\\Done::labelForMobile<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Done.php.html#26\">App\\States\\WorkOrderIssue\\Done::colorClassForMobile<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Done.php.html#34\">App\\States\\WorkOrderIssue\\Done::actions<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/MissingInfo.php.html#14\">App\\States\\WorkOrderIssue\\MissingInfo::label<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/MissingInfo.php.html#19\">App\\States\\WorkOrderIssue\\MissingInfo::labelForMobile<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/MissingInfo.php.html#24\">App\\States\\WorkOrderIssue\\MissingInfo::colorClassForMobile<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/MissingInfo.php.html#32\">App\\States\\WorkOrderIssue\\MissingInfo::actions<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/QualityCheck.php.html#16\">App\\States\\WorkOrderIssue\\QualityCheck::label<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/QualityCheck.php.html#21\">App\\States\\WorkOrderIssue\\QualityCheck::labelForMobile<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/QualityCheck.php.html#26\">App\\States\\WorkOrderIssue\\QualityCheck::colorClassForMobile<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/QualityCheck.php.html#34\">App\\States\\WorkOrderIssue\\QualityCheck::actions<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToAssigned.php.html#12\">App\\States\\WorkOrderIssue\\Transitions\\ToAssigned::__construct<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToAssigned.php.html#16\">App\\States\\WorkOrderIssue\\Transitions\\ToAssigned::handle<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToCanceled.php.html#13\">App\\States\\WorkOrderIssue\\Transitions\\ToCanceled::__construct<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToCanceled.php.html#15\">App\\States\\WorkOrderIssue\\Transitions\\ToCanceled::handle<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToDone.php.html#16\">App\\States\\WorkOrderIssue\\Transitions\\ToDone::__construct<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToDone.php.html#21\">App\\States\\WorkOrderIssue\\Transitions\\ToDone::handle<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToMissingInfo.php.html#12\">App\\States\\WorkOrderIssue\\Transitions\\ToMissingInfo::__construct<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToMissingInfo.php.html#14\">App\\States\\WorkOrderIssue\\Transitions\\ToMissingInfo::handle<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToQualityCheck.php.html#12\">App\\States\\WorkOrderIssue\\Transitions\\ToQualityCheck::__construct<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToQualityCheck.php.html#14\">App\\States\\WorkOrderIssue\\Transitions\\ToQualityCheck::handle<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToUnresolved.php.html#13\">App\\States\\WorkOrderIssue\\Transitions\\ToUnresolved::__construct<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Transitions\/ToUnresolved.php.html#15\">App\\States\\WorkOrderIssue\\Transitions\\ToUnresolved::handle<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Unresolved.php.html#16\">App\\States\\WorkOrderIssue\\Unresolved::label<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Unresolved.php.html#21\">App\\States\\WorkOrderIssue\\Unresolved::labelForMobile<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Unresolved.php.html#26\">App\\States\\WorkOrderIssue\\Unresolved::colorClassForMobile<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/Unresolved.php.html#34\">App\\States\\WorkOrderIssue\\Unresolved::actions<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/WorkOrderIssueState.php.html#24\">App\\States\\WorkOrderIssue\\WorkOrderIssueState::config<\/a>"],[100,0,"<a href=\"WorkOrderIssue\/WorkOrderIssueState.php.html#55\">App\\States\\WorkOrderIssue\\WorkOrderIssueState::label<\/a>"],[100,0,"<a href=\"WorkOrderIssue\/WorkOrderIssueState.php.html#57\">App\\States\\WorkOrderIssue\\WorkOrderIssueState::labelForMobile<\/a>"],[100,0,"<a href=\"WorkOrderIssue\/WorkOrderIssueState.php.html#59\">App\\States\\WorkOrderIssue\\WorkOrderIssueState::colorClassForMobile<\/a>"],[100,0,"<a href=\"WorkOrderIssue\/WorkOrderIssueState.php.html#64\">App\\States\\WorkOrderIssue\\WorkOrderIssueState::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/AwaitingAvailability.php.html#14\">App\\States\\WorkOrders\\AwaitingAvailability::color<\/a>"],[0,1,"<a href=\"WorkOrders\/AwaitingAvailability.php.html#19\">App\\States\\WorkOrders\\AwaitingAvailability::label<\/a>"],[0,1,"<a href=\"WorkOrders\/AwaitingAvailability.php.html#24\">App\\States\\WorkOrders\\AwaitingAvailability::colorClass<\/a>"],[0,1,"<a href=\"WorkOrders\/AwaitingAvailability.php.html#32\">App\\States\\WorkOrders\\AwaitingAvailability::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/Canceled.php.html#14\">App\\States\\WorkOrders\\Canceled::color<\/a>"],[100,1,"<a href=\"WorkOrders\/Canceled.php.html#19\">App\\States\\WorkOrders\\Canceled::label<\/a>"],[100,1,"<a href=\"WorkOrders\/Canceled.php.html#24\">App\\States\\WorkOrders\\Canceled::colorClass<\/a>"],[100,1,"<a href=\"WorkOrders\/Canceled.php.html#32\">App\\States\\WorkOrders\\Canceled::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/ClaimPending.php.html#14\">App\\States\\WorkOrders\\ClaimPending::color<\/a>"],[0,1,"<a href=\"WorkOrders\/ClaimPending.php.html#19\">App\\States\\WorkOrders\\ClaimPending::label<\/a>"],[0,1,"<a href=\"WorkOrders\/ClaimPending.php.html#24\">App\\States\\WorkOrders\\ClaimPending::colorClass<\/a>"],[0,1,"<a href=\"WorkOrders\/ClaimPending.php.html#32\">App\\States\\WorkOrders\\ClaimPending::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/Completed.php.html#14\">App\\States\\WorkOrders\\Completed::color<\/a>"],[100,1,"<a href=\"WorkOrders\/Completed.php.html#19\">App\\States\\WorkOrders\\Completed::label<\/a>"],[100,1,"<a href=\"WorkOrders\/Completed.php.html#24\">App\\States\\WorkOrders\\Completed::colorClass<\/a>"],[100,1,"<a href=\"WorkOrders\/Completed.php.html#32\">App\\States\\WorkOrders\\Completed::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/Created.php.html#14\">App\\States\\WorkOrders\\Created::color<\/a>"],[0,1,"<a href=\"WorkOrders\/Created.php.html#19\">App\\States\\WorkOrders\\Created::label<\/a>"],[0,1,"<a href=\"WorkOrders\/Created.php.html#24\">App\\States\\WorkOrders\\Created::colorClass<\/a>"],[0,1,"<a href=\"WorkOrders\/Created.php.html#32\">App\\States\\WorkOrders\\Created::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/Paused.php.html#14\">App\\States\\WorkOrders\\Paused::color<\/a>"],[100,1,"<a href=\"WorkOrders\/Paused.php.html#19\">App\\States\\WorkOrders\\Paused::label<\/a>"],[100,1,"<a href=\"WorkOrders\/Paused.php.html#24\">App\\States\\WorkOrders\\Paused::colorClass<\/a>"],[100,1,"<a href=\"WorkOrders\/Paused.php.html#32\">App\\States\\WorkOrders\\Paused::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/QualityCheck.php.html#14\">App\\States\\WorkOrders\\QualityCheck::color<\/a>"],[0,1,"<a href=\"WorkOrders\/QualityCheck.php.html#19\">App\\States\\WorkOrders\\QualityCheck::label<\/a>"],[0,1,"<a href=\"WorkOrders\/QualityCheck.php.html#24\">App\\States\\WorkOrders\\QualityCheck::colorClass<\/a>"],[0,1,"<a href=\"WorkOrders\/QualityCheck.php.html#32\">App\\States\\WorkOrders\\QualityCheck::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/ReadyToInvoice.php.html#14\">App\\States\\WorkOrders\\ReadyToInvoice::color<\/a>"],[0,1,"<a href=\"WorkOrders\/ReadyToInvoice.php.html#19\">App\\States\\WorkOrders\\ReadyToInvoice::label<\/a>"],[0,1,"<a href=\"WorkOrders\/ReadyToInvoice.php.html#24\">App\\States\\WorkOrders\\ReadyToInvoice::colorClass<\/a>"],[0,1,"<a href=\"WorkOrders\/ReadyToInvoice.php.html#32\">App\\States\\WorkOrders\\ReadyToInvoice::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/ReadyToSchedule.php.html#14\">App\\States\\WorkOrders\\ReadyToSchedule::color<\/a>"],[0,1,"<a href=\"WorkOrders\/ReadyToSchedule.php.html#19\">App\\States\\WorkOrders\\ReadyToSchedule::label<\/a>"],[0,1,"<a href=\"WorkOrders\/ReadyToSchedule.php.html#24\">App\\States\\WorkOrders\\ReadyToSchedule::colorClass<\/a>"],[0,1,"<a href=\"WorkOrders\/ReadyToSchedule.php.html#32\">App\\States\\WorkOrders\\ReadyToSchedule::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/Scheduled.php.html#14\">App\\States\\WorkOrders\\Scheduled::color<\/a>"],[100,1,"<a href=\"WorkOrders\/Scheduled.php.html#19\">App\\States\\WorkOrders\\Scheduled::label<\/a>"],[100,1,"<a href=\"WorkOrders\/Scheduled.php.html#24\">App\\States\\WorkOrders\\Scheduled::colorClass<\/a>"],[100,1,"<a href=\"WorkOrders\/Scheduled.php.html#32\">App\\States\\WorkOrders\\Scheduled::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/SchedulingInProgress.php.html#14\">App\\States\\WorkOrders\\SchedulingInProgress::color<\/a>"],[0,1,"<a href=\"WorkOrders\/SchedulingInProgress.php.html#19\">App\\States\\WorkOrders\\SchedulingInProgress::label<\/a>"],[0,1,"<a href=\"WorkOrders\/SchedulingInProgress.php.html#24\">App\\States\\WorkOrders\\SchedulingInProgress::colorClass<\/a>"],[0,1,"<a href=\"WorkOrders\/SchedulingInProgress.php.html#32\">App\\States\\WorkOrders\\SchedulingInProgress::actions<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToAwaitingAvailability.php.html#16\">App\\States\\WorkOrders\\Transitions\\ToAwaitingAvailability::__construct<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToAwaitingAvailability.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToAwaitingAvailability::handle<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToCanceled.php.html#18\">App\\States\\WorkOrders\\Transitions\\ToCanceled::__construct<\/a>"],[0,2,"<a href=\"WorkOrders\/Transitions\/ToCanceled.php.html#20\">App\\States\\WorkOrders\\Transitions\\ToCanceled::handle<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToClaimPending.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToClaimPending::__construct<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToClaimPending.php.html#19\">App\\States\\WorkOrders\\Transitions\\ToClaimPending::handle<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToCompleted.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToCompleted::__construct<\/a>"],[0,3,"<a href=\"WorkOrders\/Transitions\/ToCompleted.php.html#19\">App\\States\\WorkOrders\\Transitions\\ToCompleted::handle<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToCreated.php.html#20\">App\\States\\WorkOrders\\Transitions\\ToCreated::__construct<\/a>"],[0,5,"<a href=\"WorkOrders\/Transitions\/ToCreated.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToCreated::handle<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToPaused.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToPaused::__construct<\/a>"],[0,4,"<a href=\"WorkOrders\/Transitions\/ToPaused.php.html#30\">App\\States\\WorkOrders\\Transitions\\ToPaused::handle<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToQualityCheck.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToQualityCheck::__construct<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToQualityCheck.php.html#27\">App\\States\\WorkOrders\\Transitions\\ToQualityCheck::handle<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToReadyToInvoice.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToReadyToInvoice::__construct<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToReadyToInvoice.php.html#23\">App\\States\\WorkOrders\\Transitions\\ToReadyToInvoice::handle<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToReadyToSchedule.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToReadyToSchedule::__construct<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToReadyToSchedule.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToReadyToSchedule::handle<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToScheduled.php.html#32\">App\\States\\WorkOrders\\Transitions\\ToScheduled::__construct<\/a>"],[0,9,"<a href=\"WorkOrders\/Transitions\/ToScheduled.php.html#46\">App\\States\\WorkOrders\\Transitions\\ToScheduled::handle<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToWorkInProgress.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToWorkInProgress::__construct<\/a>"],[0,1,"<a href=\"WorkOrders\/Transitions\/ToWorkInProgress.php.html#19\">App\\States\\WorkOrders\\Transitions\\ToWorkInProgress::handle<\/a>"],[0,1,"<a href=\"WorkOrders\/WorkInProgress.php.html#14\">App\\States\\WorkOrders\\WorkInProgress::color<\/a>"],[100,1,"<a href=\"WorkOrders\/WorkInProgress.php.html#19\">App\\States\\WorkOrders\\WorkInProgress::label<\/a>"],[100,1,"<a href=\"WorkOrders\/WorkInProgress.php.html#24\">App\\States\\WorkOrders\\WorkInProgress::colorClass<\/a>"],[100,1,"<a href=\"WorkOrders\/WorkInProgress.php.html#32\">App\\States\\WorkOrders\\WorkInProgress::actions<\/a>"],[100,1,"<a href=\"WorkOrders\/WorkOrderState.php.html#29\">App\\States\\WorkOrders\\WorkOrderState::config<\/a>"],[100,0,"<a href=\"WorkOrders\/WorkOrderState.php.html#91\">App\\States\\WorkOrders\\WorkOrderState::color<\/a>"],[100,0,"<a href=\"WorkOrders\/WorkOrderState.php.html#93\">App\\States\\WorkOrders\\WorkOrderState::colorClass<\/a>"],[100,0,"<a href=\"WorkOrders\/WorkOrderState.php.html#95\">App\\States\\WorkOrders\\WorkOrderState::label<\/a>"],[100,0,"<a href=\"WorkOrders\/WorkOrderState.php.html#100\">App\\States\\WorkOrders\\WorkOrderState::actions<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

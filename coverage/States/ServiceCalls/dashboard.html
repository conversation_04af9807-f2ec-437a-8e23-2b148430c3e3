<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/States/ServiceCalls</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">States</a></li>
         <li class="breadcrumb-item"><a href="index.html">ServiceCalls</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Canceled.php.html#5">App\States\ServiceCalls\Canceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClaimPending.php.html#5">App\States\ServiceCalls\ClaimPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Done.php.html#5">App\States\ServiceCalls\Done</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnRoute.php.html#5">App\States\ServiceCalls\EnRoute</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnRoutePaused.php.html#5">App\States\ServiceCalls\EnRoutePaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ended.php.html#5">App\States\ServiceCalls\Ended</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MissingInfo.php.html#7">App\States\ServiceCalls\MissingInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paused.php.html#5">App\States\ServiceCalls\Paused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReScheduled.php.html#5">App\States\ServiceCalls\ReScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleInPending.php.html#5">App\States\ServiceCalls\ScheduleInPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleInProgress.php.html#5">App\States\ServiceCalls\ScheduleInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCanceled.php.html#13">App\States\ServiceCalls\Transitions\ToCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToDone.php.html#11">App\States\ServiceCalls\Transitions\ToDone</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToEnRoute.php.html#17">App\States\ServiceCalls\Transitions\ToEnRoute</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToEnRoutePaused.php.html#15">App\States\ServiceCalls\Transitions\ToEnRoutePaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToEnded.php.html#18">App\States\ServiceCalls\Transitions\ToEnded</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToMissingInfo.php.html#11">App\States\ServiceCalls\Transitions\ToMissingInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaused.php.html#14">App\States\ServiceCalls\Transitions\ToPaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToReScheduled.php.html#19">App\States\ServiceCalls\Transitions\ToReScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduleInPending.php.html#11">App\States\ServiceCalls\Transitions\ToScheduleInPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduleInProgress.php.html#13">App\States\ServiceCalls\Transitions\ToScheduleInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduled.php.html#13">App\States\ServiceCalls\Transitions\ToScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToUnresolved.php.html#11">App\States\ServiceCalls\Transitions\ToUnresolved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToWorking.php.html#17">App\States\ServiceCalls\Transitions\ToWorking</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Unresolved.php.html#7">App\States\ServiceCalls\Unresolved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Working.php.html#5">App\States\ServiceCalls\Working</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduled.php.html#5">App\States\ServiceCalls\Scheduled</a></td><td class="text-right">66%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Transitions/ToReScheduled.php.html#19">App\States\ServiceCalls\Transitions\ToReScheduled</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Transitions/ToEnded.php.html#18">App\States\ServiceCalls\Transitions\ToEnded</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Transitions/ToWorking.php.html#17">App\States\ServiceCalls\Transitions\ToWorking</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Transitions/ToEnRoutePaused.php.html#15">App\States\ServiceCalls\Transitions\ToEnRoutePaused</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Transitions/ToPaused.php.html#14">App\States\ServiceCalls\Transitions\ToPaused</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Transitions/ToEnRoute.php.html#17">App\States\ServiceCalls\Transitions\ToEnRoute</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Canceled.php.html#12"><abbr title="App\States\ServiceCalls\Canceled::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Canceled.php.html#17"><abbr title="App\States\ServiceCalls\Canceled::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Canceled.php.html#22"><abbr title="App\States\ServiceCalls\Canceled::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClaimPending.php.html#12"><abbr title="App\States\ServiceCalls\ClaimPending::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClaimPending.php.html#17"><abbr title="App\States\ServiceCalls\ClaimPending::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClaimPending.php.html#22"><abbr title="App\States\ServiceCalls\ClaimPending::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Done.php.html#12"><abbr title="App\States\ServiceCalls\Done::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Done.php.html#17"><abbr title="App\States\ServiceCalls\Done::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Done.php.html#22"><abbr title="App\States\ServiceCalls\Done::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnRoute.php.html#12"><abbr title="App\States\ServiceCalls\EnRoute::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnRoute.php.html#17"><abbr title="App\States\ServiceCalls\EnRoute::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnRoute.php.html#22"><abbr title="App\States\ServiceCalls\EnRoute::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnRoutePaused.php.html#12"><abbr title="App\States\ServiceCalls\EnRoutePaused::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnRoutePaused.php.html#17"><abbr title="App\States\ServiceCalls\EnRoutePaused::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnRoutePaused.php.html#22"><abbr title="App\States\ServiceCalls\EnRoutePaused::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ended.php.html#12"><abbr title="App\States\ServiceCalls\Ended::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ended.php.html#17"><abbr title="App\States\ServiceCalls\Ended::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Ended.php.html#22"><abbr title="App\States\ServiceCalls\Ended::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MissingInfo.php.html#14"><abbr title="App\States\ServiceCalls\MissingInfo::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MissingInfo.php.html#19"><abbr title="App\States\ServiceCalls\MissingInfo::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MissingInfo.php.html#24"><abbr title="App\States\ServiceCalls\MissingInfo::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paused.php.html#12"><abbr title="App\States\ServiceCalls\Paused::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paused.php.html#17"><abbr title="App\States\ServiceCalls\Paused::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paused.php.html#22"><abbr title="App\States\ServiceCalls\Paused::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReScheduled.php.html#12"><abbr title="App\States\ServiceCalls\ReScheduled::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReScheduled.php.html#17"><abbr title="App\States\ServiceCalls\ReScheduled::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReScheduled.php.html#22"><abbr title="App\States\ServiceCalls\ReScheduled::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleInPending.php.html#12"><abbr title="App\States\ServiceCalls\ScheduleInPending::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleInPending.php.html#17"><abbr title="App\States\ServiceCalls\ScheduleInPending::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleInPending.php.html#22"><abbr title="App\States\ServiceCalls\ScheduleInPending::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleInProgress.php.html#12"><abbr title="App\States\ServiceCalls\ScheduleInProgress::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleInProgress.php.html#17"><abbr title="App\States\ServiceCalls\ScheduleInProgress::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleInProgress.php.html#22"><abbr title="App\States\ServiceCalls\ScheduleInProgress::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduled.php.html#17"><abbr title="App\States\ServiceCalls\Scheduled::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCanceled.php.html#15"><abbr title="App\States\ServiceCalls\Transitions\ToCanceled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCanceled.php.html#22"><abbr title="App\States\ServiceCalls\Transitions\ToCanceled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToDone.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToDone::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToDone.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToDone::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToEnRoute.php.html#21"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoute::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToEnRoute.php.html#34"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoute::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToEnRoutePaused.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToEnRoutePaused.php.html#31"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToEnRoutePaused.php.html#77"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::calculateDriveTime">calculateDriveTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToEnded.php.html#25"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToEnded.php.html#37"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToEnded.php.html#104"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToMissingInfo.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToMissingInfo::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToMissingInfo.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToMissingInfo::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaused.php.html#18"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaused.php.html#26"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaused.php.html#66"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToReScheduled.php.html#21"><abbr title="App\States\ServiceCalls\Transitions\ToReScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToReScheduled.php.html#36"><abbr title="App\States\ServiceCalls\Transitions\ToReScheduled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduleInPending.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInPending::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduleInPending.php.html#18"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInPending::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduleInProgress.php.html#15"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInProgress::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduleInProgress.php.html#22"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInProgress::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduled.php.html#15"><abbr title="App\States\ServiceCalls\Transitions\ToScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduled.php.html#22"><abbr title="App\States\ServiceCalls\Transitions\ToScheduled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToUnresolved.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToUnresolved::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToUnresolved.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToUnresolved::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToWorking.php.html#21"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToWorking.php.html#32"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToWorking.php.html#87"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::calculateTravelTime">calculateTravelTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Unresolved.php.html#14"><abbr title="App\States\ServiceCalls\Unresolved::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Unresolved.php.html#19"><abbr title="App\States\ServiceCalls\Unresolved::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Unresolved.php.html#24"><abbr title="App\States\ServiceCalls\Unresolved::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Working.php.html#12"><abbr title="App\States\ServiceCalls\Working::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Working.php.html#17"><abbr title="App\States\ServiceCalls\Working::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Working.php.html#22"><abbr title="App\States\ServiceCalls\Working::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Transitions/ToReScheduled.php.html#36"><abbr title="App\States\ServiceCalls\Transitions\ToReScheduled::handle">handle</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Transitions/ToEnded.php.html#37"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Transitions/ToEnRoute.php.html#34"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoute::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Transitions/ToEnRoutePaused.php.html#77"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::calculateDriveTime">calculateDriveTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Transitions/ToEnded.php.html#104"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Transitions/ToPaused.php.html#66"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Transitions/ToWorking.php.html#32"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Transitions/ToWorking.php.html#87"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::calculateTravelTime">calculateTravelTime</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Mon Jun 30 0:32:48 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([26,0,0,0,0,0,0,1,0,0,0,1], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([70,0,0,0,0,0,0,0,0,0,0,6], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,3,"<a href=\"Canceled.php.html#5\">App\\States\\ServiceCalls\\Canceled<\/a>"],[0,3,"<a href=\"ClaimPending.php.html#5\">App\\States\\ServiceCalls\\ClaimPending<\/a>"],[0,3,"<a href=\"Done.php.html#5\">App\\States\\ServiceCalls\\Done<\/a>"],[0,3,"<a href=\"EnRoute.php.html#5\">App\\States\\ServiceCalls\\EnRoute<\/a>"],[0,3,"<a href=\"EnRoutePaused.php.html#5\">App\\States\\ServiceCalls\\EnRoutePaused<\/a>"],[0,3,"<a href=\"Ended.php.html#5\">App\\States\\ServiceCalls\\Ended<\/a>"],[0,3,"<a href=\"MissingInfo.php.html#7\">App\\States\\ServiceCalls\\MissingInfo<\/a>"],[0,3,"<a href=\"Paused.php.html#5\">App\\States\\ServiceCalls\\Paused<\/a>"],[0,3,"<a href=\"ReScheduled.php.html#5\">App\\States\\ServiceCalls\\ReScheduled<\/a>"],[0,3,"<a href=\"ScheduleInPending.php.html#5\">App\\States\\ServiceCalls\\ScheduleInPending<\/a>"],[0,3,"<a href=\"ScheduleInProgress.php.html#5\">App\\States\\ServiceCalls\\ScheduleInProgress<\/a>"],[66.66666666666666,3,"<a href=\"Scheduled.php.html#5\">App\\States\\ServiceCalls\\Scheduled<\/a>"],[100,1,"<a href=\"ServiceCallState.php.html#25\">App\\States\\ServiceCalls\\ServiceCallState<\/a>"],[0,2,"<a href=\"Transitions\/ToCanceled.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToCanceled<\/a>"],[0,2,"<a href=\"Transitions\/ToDone.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToDone<\/a>"],[0,3,"<a href=\"Transitions\/ToEnRoute.php.html#17\">App\\States\\ServiceCalls\\Transitions\\ToEnRoute<\/a>"],[0,4,"<a href=\"Transitions\/ToEnRoutePaused.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused<\/a>"],[0,6,"<a href=\"Transitions\/ToEnded.php.html#18\">App\\States\\ServiceCalls\\Transitions\\ToEnded<\/a>"],[0,2,"<a href=\"Transitions\/ToMissingInfo.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToMissingInfo<\/a>"],[0,4,"<a href=\"Transitions\/ToPaused.php.html#14\">App\\States\\ServiceCalls\\Transitions\\ToPaused<\/a>"],[0,14,"<a href=\"Transitions\/ToReScheduled.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToReScheduled<\/a>"],[0,2,"<a href=\"Transitions\/ToScheduleInPending.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInPending<\/a>"],[0,2,"<a href=\"Transitions\/ToScheduleInProgress.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInProgress<\/a>"],[0,2,"<a href=\"Transitions\/ToScheduled.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToScheduled<\/a>"],[0,2,"<a href=\"Transitions\/ToUnresolved.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToUnresolved<\/a>"],[0,5,"<a href=\"Transitions\/ToWorking.php.html#17\">App\\States\\ServiceCalls\\Transitions\\ToWorking<\/a>"],[0,3,"<a href=\"Unresolved.php.html#7\">App\\States\\ServiceCalls\\Unresolved<\/a>"],[0,3,"<a href=\"Working.php.html#5\">App\\States\\ServiceCalls\\Working<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Canceled.php.html#12\">App\\States\\ServiceCalls\\Canceled::label<\/a>"],[0,1,"<a href=\"Canceled.php.html#17\">App\\States\\ServiceCalls\\Canceled::labelForMobile<\/a>"],[0,1,"<a href=\"Canceled.php.html#22\">App\\States\\ServiceCalls\\Canceled::colorClass<\/a>"],[0,1,"<a href=\"ClaimPending.php.html#12\">App\\States\\ServiceCalls\\ClaimPending::label<\/a>"],[0,1,"<a href=\"ClaimPending.php.html#17\">App\\States\\ServiceCalls\\ClaimPending::labelForMobile<\/a>"],[0,1,"<a href=\"ClaimPending.php.html#22\">App\\States\\ServiceCalls\\ClaimPending::colorClass<\/a>"],[0,1,"<a href=\"Done.php.html#12\">App\\States\\ServiceCalls\\Done::label<\/a>"],[0,1,"<a href=\"Done.php.html#17\">App\\States\\ServiceCalls\\Done::labelForMobile<\/a>"],[0,1,"<a href=\"Done.php.html#22\">App\\States\\ServiceCalls\\Done::colorClass<\/a>"],[0,1,"<a href=\"EnRoute.php.html#12\">App\\States\\ServiceCalls\\EnRoute::label<\/a>"],[0,1,"<a href=\"EnRoute.php.html#17\">App\\States\\ServiceCalls\\EnRoute::labelForMobile<\/a>"],[0,1,"<a href=\"EnRoute.php.html#22\">App\\States\\ServiceCalls\\EnRoute::colorClass<\/a>"],[0,1,"<a href=\"EnRoutePaused.php.html#12\">App\\States\\ServiceCalls\\EnRoutePaused::label<\/a>"],[0,1,"<a href=\"EnRoutePaused.php.html#17\">App\\States\\ServiceCalls\\EnRoutePaused::labelForMobile<\/a>"],[0,1,"<a href=\"EnRoutePaused.php.html#22\">App\\States\\ServiceCalls\\EnRoutePaused::colorClass<\/a>"],[0,1,"<a href=\"Ended.php.html#12\">App\\States\\ServiceCalls\\Ended::label<\/a>"],[0,1,"<a href=\"Ended.php.html#17\">App\\States\\ServiceCalls\\Ended::labelForMobile<\/a>"],[0,1,"<a href=\"Ended.php.html#22\">App\\States\\ServiceCalls\\Ended::colorClass<\/a>"],[0,1,"<a href=\"MissingInfo.php.html#14\">App\\States\\ServiceCalls\\MissingInfo::label<\/a>"],[0,1,"<a href=\"MissingInfo.php.html#19\">App\\States\\ServiceCalls\\MissingInfo::labelForMobile<\/a>"],[0,1,"<a href=\"MissingInfo.php.html#24\">App\\States\\ServiceCalls\\MissingInfo::colorClass<\/a>"],[0,1,"<a href=\"Paused.php.html#12\">App\\States\\ServiceCalls\\Paused::label<\/a>"],[0,1,"<a href=\"Paused.php.html#17\">App\\States\\ServiceCalls\\Paused::labelForMobile<\/a>"],[0,1,"<a href=\"Paused.php.html#22\">App\\States\\ServiceCalls\\Paused::colorClass<\/a>"],[0,1,"<a href=\"ReScheduled.php.html#12\">App\\States\\ServiceCalls\\ReScheduled::label<\/a>"],[0,1,"<a href=\"ReScheduled.php.html#17\">App\\States\\ServiceCalls\\ReScheduled::labelForMobile<\/a>"],[0,1,"<a href=\"ReScheduled.php.html#22\">App\\States\\ServiceCalls\\ReScheduled::colorClass<\/a>"],[0,1,"<a href=\"ScheduleInPending.php.html#12\">App\\States\\ServiceCalls\\ScheduleInPending::label<\/a>"],[0,1,"<a href=\"ScheduleInPending.php.html#17\">App\\States\\ServiceCalls\\ScheduleInPending::labelForMobile<\/a>"],[0,1,"<a href=\"ScheduleInPending.php.html#22\">App\\States\\ServiceCalls\\ScheduleInPending::colorClass<\/a>"],[0,1,"<a href=\"ScheduleInProgress.php.html#12\">App\\States\\ServiceCalls\\ScheduleInProgress::label<\/a>"],[0,1,"<a href=\"ScheduleInProgress.php.html#17\">App\\States\\ServiceCalls\\ScheduleInProgress::labelForMobile<\/a>"],[0,1,"<a href=\"ScheduleInProgress.php.html#22\">App\\States\\ServiceCalls\\ScheduleInProgress::colorClass<\/a>"],[100,1,"<a href=\"Scheduled.php.html#12\">App\\States\\ServiceCalls\\Scheduled::label<\/a>"],[0,1,"<a href=\"Scheduled.php.html#17\">App\\States\\ServiceCalls\\Scheduled::labelForMobile<\/a>"],[100,1,"<a href=\"Scheduled.php.html#22\">App\\States\\ServiceCalls\\Scheduled::colorClass<\/a>"],[100,1,"<a href=\"ServiceCallState.php.html#30\">App\\States\\ServiceCalls\\ServiceCallState::config<\/a>"],[100,0,"<a href=\"ServiceCallState.php.html#86\">App\\States\\ServiceCalls\\ServiceCallState::label<\/a>"],[100,0,"<a href=\"ServiceCallState.php.html#88\">App\\States\\ServiceCalls\\ServiceCallState::labelForMobile<\/a>"],[100,0,"<a href=\"ServiceCallState.php.html#90\">App\\States\\ServiceCalls\\ServiceCallState::colorClass<\/a>"],[0,1,"<a href=\"Transitions\/ToCanceled.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToCanceled::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToCanceled.php.html#22\">App\\States\\ServiceCalls\\Transitions\\ToCanceled::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToDone.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToDone::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToDone.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToDone::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToEnRoute.php.html#21\">App\\States\\ServiceCalls\\Transitions\\ToEnRoute::__construct<\/a>"],[0,2,"<a href=\"Transitions\/ToEnRoute.php.html#34\">App\\States\\ServiceCalls\\Transitions\\ToEnRoute::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToEnRoutePaused.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToEnRoutePaused.php.html#31\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused::handle<\/a>"],[0,2,"<a href=\"Transitions\/ToEnRoutePaused.php.html#77\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused::calculateDriveTime<\/a>"],[0,1,"<a href=\"Transitions\/ToEnded.php.html#25\">App\\States\\ServiceCalls\\Transitions\\ToEnded::__construct<\/a>"],[0,3,"<a href=\"Transitions\/ToEnded.php.html#37\">App\\States\\ServiceCalls\\Transitions\\ToEnded::handle<\/a>"],[0,2,"<a href=\"Transitions\/ToEnded.php.html#104\">App\\States\\ServiceCalls\\Transitions\\ToEnded::calculateWorkTime<\/a>"],[0,1,"<a href=\"Transitions\/ToMissingInfo.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToMissingInfo::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToMissingInfo.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToMissingInfo::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToPaused.php.html#18\">App\\States\\ServiceCalls\\Transitions\\ToPaused::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToPaused.php.html#26\">App\\States\\ServiceCalls\\Transitions\\ToPaused::handle<\/a>"],[0,2,"<a href=\"Transitions\/ToPaused.php.html#66\">App\\States\\ServiceCalls\\Transitions\\ToPaused::calculateWorkTime<\/a>"],[0,1,"<a href=\"Transitions\/ToReScheduled.php.html#21\">App\\States\\ServiceCalls\\Transitions\\ToReScheduled::__construct<\/a>"],[0,13,"<a href=\"Transitions\/ToReScheduled.php.html#36\">App\\States\\ServiceCalls\\Transitions\\ToReScheduled::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToScheduleInPending.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInPending::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToScheduleInPending.php.html#18\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInPending::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToScheduleInProgress.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInProgress::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToScheduleInProgress.php.html#22\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInProgress::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToScheduled.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToScheduled::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToScheduled.php.html#22\">App\\States\\ServiceCalls\\Transitions\\ToScheduled::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToUnresolved.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToUnresolved::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToUnresolved.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToUnresolved::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToWorking.php.html#21\">App\\States\\ServiceCalls\\Transitions\\ToWorking::__construct<\/a>"],[0,2,"<a href=\"Transitions\/ToWorking.php.html#32\">App\\States\\ServiceCalls\\Transitions\\ToWorking::handle<\/a>"],[0,2,"<a href=\"Transitions\/ToWorking.php.html#87\">App\\States\\ServiceCalls\\Transitions\\ToWorking::calculateTravelTime<\/a>"],[0,1,"<a href=\"Unresolved.php.html#14\">App\\States\\ServiceCalls\\Unresolved::label<\/a>"],[0,1,"<a href=\"Unresolved.php.html#19\">App\\States\\ServiceCalls\\Unresolved::labelForMobile<\/a>"],[0,1,"<a href=\"Unresolved.php.html#24\">App\\States\\ServiceCalls\\Unresolved::colorClass<\/a>"],[0,1,"<a href=\"Working.php.html#12\">App\\States\\ServiceCalls\\Working::label<\/a>"],[0,1,"<a href=\"Working.php.html#17\">App\\States\\ServiceCalls\\Working::labelForMobile<\/a>"],[0,1,"<a href=\"Working.php.html#22\">App\\States\\ServiceCalls\\Working::colorClass<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

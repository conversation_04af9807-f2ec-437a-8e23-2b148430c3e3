<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/States/ServiceCalls/Transitions</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">States</a></li>
         <li class="breadcrumb-item"><a href="../index.html">ServiceCalls</a></li>
         <li class="breadcrumb-item"><a href="index.html">Transitions</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ToCanceled.php.html#13">App\States\ServiceCalls\Transitions\ToCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToDone.php.html#11">App\States\ServiceCalls\Transitions\ToDone</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToEnRoute.php.html#17">App\States\ServiceCalls\Transitions\ToEnRoute</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToEnRoutePaused.php.html#15">App\States\ServiceCalls\Transitions\ToEnRoutePaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToEnded.php.html#18">App\States\ServiceCalls\Transitions\ToEnded</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToMissingInfo.php.html#11">App\States\ServiceCalls\Transitions\ToMissingInfo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToPaused.php.html#14">App\States\ServiceCalls\Transitions\ToPaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToReScheduled.php.html#19">App\States\ServiceCalls\Transitions\ToReScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduleInPending.php.html#11">App\States\ServiceCalls\Transitions\ToScheduleInPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduleInProgress.php.html#13">App\States\ServiceCalls\Transitions\ToScheduleInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduled.php.html#13">App\States\ServiceCalls\Transitions\ToScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToUnresolved.php.html#11">App\States\ServiceCalls\Transitions\ToUnresolved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToWorking.php.html#17">App\States\ServiceCalls\Transitions\ToWorking</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ToReScheduled.php.html#19">App\States\ServiceCalls\Transitions\ToReScheduled</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ToEnded.php.html#18">App\States\ServiceCalls\Transitions\ToEnded</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ToWorking.php.html#17">App\States\ServiceCalls\Transitions\ToWorking</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ToEnRoutePaused.php.html#15">App\States\ServiceCalls\Transitions\ToEnRoutePaused</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ToPaused.php.html#14">App\States\ServiceCalls\Transitions\ToPaused</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ToEnRoute.php.html#17">App\States\ServiceCalls\Transitions\ToEnRoute</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ToCanceled.php.html#15"><abbr title="App\States\ServiceCalls\Transitions\ToCanceled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToCanceled.php.html#22"><abbr title="App\States\ServiceCalls\Transitions\ToCanceled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToDone.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToDone::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToDone.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToDone::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToEnRoute.php.html#21"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoute::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToEnRoute.php.html#34"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoute::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToEnRoutePaused.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToEnRoutePaused.php.html#31"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToEnRoutePaused.php.html#77"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::calculateDriveTime">calculateDriveTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToEnded.php.html#25"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToEnded.php.html#37"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToEnded.php.html#104"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToMissingInfo.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToMissingInfo::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToMissingInfo.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToMissingInfo::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToPaused.php.html#18"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToPaused.php.html#26"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToPaused.php.html#66"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToReScheduled.php.html#21"><abbr title="App\States\ServiceCalls\Transitions\ToReScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToReScheduled.php.html#36"><abbr title="App\States\ServiceCalls\Transitions\ToReScheduled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduleInPending.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInPending::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduleInPending.php.html#18"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInPending::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduleInProgress.php.html#15"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInProgress::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduleInProgress.php.html#22"><abbr title="App\States\ServiceCalls\Transitions\ToScheduleInProgress::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduled.php.html#15"><abbr title="App\States\ServiceCalls\Transitions\ToScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduled.php.html#22"><abbr title="App\States\ServiceCalls\Transitions\ToScheduled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToUnresolved.php.html#13"><abbr title="App\States\ServiceCalls\Transitions\ToUnresolved::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToUnresolved.php.html#19"><abbr title="App\States\ServiceCalls\Transitions\ToUnresolved::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToWorking.php.html#21"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToWorking.php.html#32"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToWorking.php.html#87"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::calculateTravelTime">calculateTravelTime</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ToReScheduled.php.html#36"><abbr title="App\States\ServiceCalls\Transitions\ToReScheduled::handle">handle</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ToEnded.php.html#37"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ToEnRoute.php.html#34"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoute::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToEnRoutePaused.php.html#77"><abbr title="App\States\ServiceCalls\Transitions\ToEnRoutePaused::calculateDriveTime">calculateDriveTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToEnded.php.html#104"><abbr title="App\States\ServiceCalls\Transitions\ToEnded::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToPaused.php.html#66"><abbr title="App\States\ServiceCalls\Transitions\ToPaused::calculateWorkTime">calculateWorkTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToWorking.php.html#32"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToWorking.php.html#87"><abbr title="App\States\ServiceCalls\Transitions\ToWorking::calculateTravelTime">calculateTravelTime</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:24:01 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([13,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([30,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"ToCanceled.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToCanceled<\/a>"],[0,2,"<a href=\"ToDone.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToDone<\/a>"],[0,3,"<a href=\"ToEnRoute.php.html#17\">App\\States\\ServiceCalls\\Transitions\\ToEnRoute<\/a>"],[0,4,"<a href=\"ToEnRoutePaused.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused<\/a>"],[0,6,"<a href=\"ToEnded.php.html#18\">App\\States\\ServiceCalls\\Transitions\\ToEnded<\/a>"],[0,2,"<a href=\"ToMissingInfo.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToMissingInfo<\/a>"],[0,4,"<a href=\"ToPaused.php.html#14\">App\\States\\ServiceCalls\\Transitions\\ToPaused<\/a>"],[0,14,"<a href=\"ToReScheduled.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToReScheduled<\/a>"],[0,2,"<a href=\"ToScheduleInPending.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInPending<\/a>"],[0,2,"<a href=\"ToScheduleInProgress.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInProgress<\/a>"],[0,2,"<a href=\"ToScheduled.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToScheduled<\/a>"],[0,2,"<a href=\"ToUnresolved.php.html#11\">App\\States\\ServiceCalls\\Transitions\\ToUnresolved<\/a>"],[0,5,"<a href=\"ToWorking.php.html#17\">App\\States\\ServiceCalls\\Transitions\\ToWorking<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ToCanceled.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToCanceled::__construct<\/a>"],[0,1,"<a href=\"ToCanceled.php.html#22\">App\\States\\ServiceCalls\\Transitions\\ToCanceled::handle<\/a>"],[0,1,"<a href=\"ToDone.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToDone::__construct<\/a>"],[0,1,"<a href=\"ToDone.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToDone::handle<\/a>"],[0,1,"<a href=\"ToEnRoute.php.html#21\">App\\States\\ServiceCalls\\Transitions\\ToEnRoute::__construct<\/a>"],[0,2,"<a href=\"ToEnRoute.php.html#34\">App\\States\\ServiceCalls\\Transitions\\ToEnRoute::handle<\/a>"],[0,1,"<a href=\"ToEnRoutePaused.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused::__construct<\/a>"],[0,1,"<a href=\"ToEnRoutePaused.php.html#31\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused::handle<\/a>"],[0,2,"<a href=\"ToEnRoutePaused.php.html#77\">App\\States\\ServiceCalls\\Transitions\\ToEnRoutePaused::calculateDriveTime<\/a>"],[0,1,"<a href=\"ToEnded.php.html#25\">App\\States\\ServiceCalls\\Transitions\\ToEnded::__construct<\/a>"],[0,3,"<a href=\"ToEnded.php.html#37\">App\\States\\ServiceCalls\\Transitions\\ToEnded::handle<\/a>"],[0,2,"<a href=\"ToEnded.php.html#104\">App\\States\\ServiceCalls\\Transitions\\ToEnded::calculateWorkTime<\/a>"],[0,1,"<a href=\"ToMissingInfo.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToMissingInfo::__construct<\/a>"],[0,1,"<a href=\"ToMissingInfo.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToMissingInfo::handle<\/a>"],[0,1,"<a href=\"ToPaused.php.html#18\">App\\States\\ServiceCalls\\Transitions\\ToPaused::__construct<\/a>"],[0,1,"<a href=\"ToPaused.php.html#26\">App\\States\\ServiceCalls\\Transitions\\ToPaused::handle<\/a>"],[0,2,"<a href=\"ToPaused.php.html#66\">App\\States\\ServiceCalls\\Transitions\\ToPaused::calculateWorkTime<\/a>"],[0,1,"<a href=\"ToReScheduled.php.html#21\">App\\States\\ServiceCalls\\Transitions\\ToReScheduled::__construct<\/a>"],[0,13,"<a href=\"ToReScheduled.php.html#36\">App\\States\\ServiceCalls\\Transitions\\ToReScheduled::handle<\/a>"],[0,1,"<a href=\"ToScheduleInPending.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInPending::__construct<\/a>"],[0,1,"<a href=\"ToScheduleInPending.php.html#18\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInPending::handle<\/a>"],[0,1,"<a href=\"ToScheduleInProgress.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInProgress::__construct<\/a>"],[0,1,"<a href=\"ToScheduleInProgress.php.html#22\">App\\States\\ServiceCalls\\Transitions\\ToScheduleInProgress::handle<\/a>"],[0,1,"<a href=\"ToScheduled.php.html#15\">App\\States\\ServiceCalls\\Transitions\\ToScheduled::__construct<\/a>"],[0,1,"<a href=\"ToScheduled.php.html#22\">App\\States\\ServiceCalls\\Transitions\\ToScheduled::handle<\/a>"],[0,1,"<a href=\"ToUnresolved.php.html#13\">App\\States\\ServiceCalls\\Transitions\\ToUnresolved::__construct<\/a>"],[0,1,"<a href=\"ToUnresolved.php.html#19\">App\\States\\ServiceCalls\\Transitions\\ToUnresolved::handle<\/a>"],[0,1,"<a href=\"ToWorking.php.html#21\">App\\States\\ServiceCalls\\Transitions\\ToWorking::__construct<\/a>"],[0,2,"<a href=\"ToWorking.php.html#32\">App\\States\\ServiceCalls\\Transitions\\ToWorking::handle<\/a>"],[0,2,"<a href=\"ToWorking.php.html#87\">App\\States\\ServiceCalls\\Transitions\\ToWorking::calculateTravelTime<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

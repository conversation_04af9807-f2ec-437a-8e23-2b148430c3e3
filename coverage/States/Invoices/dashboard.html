<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/States/Invoices</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">States</a></li>
         <li class="breadcrumb-item"><a href="index.html">Invoices</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Draft.php.html#5">App\States\Invoices\Draft</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceState.php.html#17">App\States\Invoices\InvoiceState</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paid.php.html#5">App\States\Invoices\Paid</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PartiallyPaid.php.html#5">App\States\Invoices\PartiallyPaid</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaymentPending.php.html#5">App\States\Invoices\PaymentPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToDraft.php.html#12">App\States\Invoices\Transitions\ToDraft</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaid.php.html#12">App\States\Invoices\Transitions\ToPaid</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPartiallyPaid.php.html#12">App\States\Invoices\Transitions\ToPartiallyPaid</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaymentPending.php.html#12">App\States\Invoices\Transitions\ToPaymentPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToVoided.php.html#12">App\States\Invoices\Transitions\ToVoided</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Voided.php.html#5">App\States\Invoices\Voided</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Draft.php.html#12"><abbr title="App\States\Invoices\Draft::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Draft.php.html#17"><abbr title="App\States\Invoices\Draft::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Draft.php.html#22"><abbr title="App\States\Invoices\Draft::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Draft.php.html#30"><abbr title="App\States\Invoices\Draft::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceState.php.html#22"><abbr title="App\States\Invoices\InvoiceState::config">config</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceState.php.html#47"><abbr title="App\States\Invoices\InvoiceState::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceState.php.html#49"><abbr title="App\States\Invoices\InvoiceState::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceState.php.html#51"><abbr title="App\States\Invoices\InvoiceState::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceState.php.html#56"><abbr title="App\States\Invoices\InvoiceState::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paid.php.html#12"><abbr title="App\States\Invoices\Paid::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paid.php.html#17"><abbr title="App\States\Invoices\Paid::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paid.php.html#22"><abbr title="App\States\Invoices\Paid::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paid.php.html#30"><abbr title="App\States\Invoices\Paid::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PartiallyPaid.php.html#12"><abbr title="App\States\Invoices\PartiallyPaid::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PartiallyPaid.php.html#17"><abbr title="App\States\Invoices\PartiallyPaid::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PartiallyPaid.php.html#22"><abbr title="App\States\Invoices\PartiallyPaid::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PartiallyPaid.php.html#30"><abbr title="App\States\Invoices\PartiallyPaid::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaymentPending.php.html#12"><abbr title="App\States\Invoices\PaymentPending::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaymentPending.php.html#17"><abbr title="App\States\Invoices\PaymentPending::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaymentPending.php.html#22"><abbr title="App\States\Invoices\PaymentPending::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaymentPending.php.html#30"><abbr title="App\States\Invoices\PaymentPending::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToDraft.php.html#14"><abbr title="App\States\Invoices\Transitions\ToDraft::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToDraft.php.html#16"><abbr title="App\States\Invoices\Transitions\ToDraft::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaid.php.html#14"><abbr title="App\States\Invoices\Transitions\ToPaid::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaid.php.html#16"><abbr title="App\States\Invoices\Transitions\ToPaid::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPartiallyPaid.php.html#14"><abbr title="App\States\Invoices\Transitions\ToPartiallyPaid::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPartiallyPaid.php.html#16"><abbr title="App\States\Invoices\Transitions\ToPartiallyPaid::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaymentPending.php.html#14"><abbr title="App\States\Invoices\Transitions\ToPaymentPending::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaymentPending.php.html#16"><abbr title="App\States\Invoices\Transitions\ToPaymentPending::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToVoided.php.html#14"><abbr title="App\States\Invoices\Transitions\ToVoided::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToVoided.php.html#16"><abbr title="App\States\Invoices\Transitions\ToVoided::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Voided.php.html#12"><abbr title="App\States\Invoices\Voided::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Voided.php.html#17"><abbr title="App\States\Invoices\Voided::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Voided.php.html#22"><abbr title="App\States\Invoices\Voided::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Voided.php.html#30"><abbr title="App\States\Invoices\Voided::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:24:01 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([11,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([35,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"Draft.php.html#5\">App\\States\\Invoices\\Draft<\/a>"],[0,1,"<a href=\"InvoiceState.php.html#17\">App\\States\\Invoices\\InvoiceState<\/a>"],[0,4,"<a href=\"Paid.php.html#5\">App\\States\\Invoices\\Paid<\/a>"],[0,4,"<a href=\"PartiallyPaid.php.html#5\">App\\States\\Invoices\\PartiallyPaid<\/a>"],[0,4,"<a href=\"PaymentPending.php.html#5\">App\\States\\Invoices\\PaymentPending<\/a>"],[0,2,"<a href=\"Transitions\/ToDraft.php.html#12\">App\\States\\Invoices\\Transitions\\ToDraft<\/a>"],[0,2,"<a href=\"Transitions\/ToPaid.php.html#12\">App\\States\\Invoices\\Transitions\\ToPaid<\/a>"],[0,2,"<a href=\"Transitions\/ToPartiallyPaid.php.html#12\">App\\States\\Invoices\\Transitions\\ToPartiallyPaid<\/a>"],[0,2,"<a href=\"Transitions\/ToPaymentPending.php.html#12\">App\\States\\Invoices\\Transitions\\ToPaymentPending<\/a>"],[0,2,"<a href=\"Transitions\/ToVoided.php.html#12\">App\\States\\Invoices\\Transitions\\ToVoided<\/a>"],[0,4,"<a href=\"Voided.php.html#5\">App\\States\\Invoices\\Voided<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Draft.php.html#12\">App\\States\\Invoices\\Draft::color<\/a>"],[0,1,"<a href=\"Draft.php.html#17\">App\\States\\Invoices\\Draft::label<\/a>"],[0,1,"<a href=\"Draft.php.html#22\">App\\States\\Invoices\\Draft::colorClass<\/a>"],[0,1,"<a href=\"Draft.php.html#30\">App\\States\\Invoices\\Draft::actions<\/a>"],[0,1,"<a href=\"InvoiceState.php.html#22\">App\\States\\Invoices\\InvoiceState::config<\/a>"],[0,0,"<a href=\"InvoiceState.php.html#47\">App\\States\\Invoices\\InvoiceState::color<\/a>"],[0,0,"<a href=\"InvoiceState.php.html#49\">App\\States\\Invoices\\InvoiceState::colorClass<\/a>"],[0,0,"<a href=\"InvoiceState.php.html#51\">App\\States\\Invoices\\InvoiceState::label<\/a>"],[0,0,"<a href=\"InvoiceState.php.html#56\">App\\States\\Invoices\\InvoiceState::actions<\/a>"],[0,1,"<a href=\"Paid.php.html#12\">App\\States\\Invoices\\Paid::color<\/a>"],[0,1,"<a href=\"Paid.php.html#17\">App\\States\\Invoices\\Paid::label<\/a>"],[0,1,"<a href=\"Paid.php.html#22\">App\\States\\Invoices\\Paid::colorClass<\/a>"],[0,1,"<a href=\"Paid.php.html#30\">App\\States\\Invoices\\Paid::actions<\/a>"],[0,1,"<a href=\"PartiallyPaid.php.html#12\">App\\States\\Invoices\\PartiallyPaid::color<\/a>"],[0,1,"<a href=\"PartiallyPaid.php.html#17\">App\\States\\Invoices\\PartiallyPaid::label<\/a>"],[0,1,"<a href=\"PartiallyPaid.php.html#22\">App\\States\\Invoices\\PartiallyPaid::colorClass<\/a>"],[0,1,"<a href=\"PartiallyPaid.php.html#30\">App\\States\\Invoices\\PartiallyPaid::actions<\/a>"],[0,1,"<a href=\"PaymentPending.php.html#12\">App\\States\\Invoices\\PaymentPending::color<\/a>"],[0,1,"<a href=\"PaymentPending.php.html#17\">App\\States\\Invoices\\PaymentPending::label<\/a>"],[0,1,"<a href=\"PaymentPending.php.html#22\">App\\States\\Invoices\\PaymentPending::colorClass<\/a>"],[0,1,"<a href=\"PaymentPending.php.html#30\">App\\States\\Invoices\\PaymentPending::actions<\/a>"],[0,1,"<a href=\"Transitions\/ToDraft.php.html#14\">App\\States\\Invoices\\Transitions\\ToDraft::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToDraft.php.html#16\">App\\States\\Invoices\\Transitions\\ToDraft::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToPaid.php.html#14\">App\\States\\Invoices\\Transitions\\ToPaid::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToPaid.php.html#16\">App\\States\\Invoices\\Transitions\\ToPaid::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToPartiallyPaid.php.html#14\">App\\States\\Invoices\\Transitions\\ToPartiallyPaid::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToPartiallyPaid.php.html#16\">App\\States\\Invoices\\Transitions\\ToPartiallyPaid::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToPaymentPending.php.html#14\">App\\States\\Invoices\\Transitions\\ToPaymentPending::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToPaymentPending.php.html#16\">App\\States\\Invoices\\Transitions\\ToPaymentPending::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToVoided.php.html#14\">App\\States\\Invoices\\Transitions\\ToVoided::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToVoided.php.html#16\">App\\States\\Invoices\\Transitions\\ToVoided::handle<\/a>"],[0,1,"<a href=\"Voided.php.html#12\">App\\States\\Invoices\\Voided::color<\/a>"],[0,1,"<a href=\"Voided.php.html#17\">App\\States\\Invoices\\Voided::label<\/a>"],[0,1,"<a href=\"Voided.php.html#22\">App\\States\\Invoices\\Voided::colorClass<\/a>"],[0,1,"<a href=\"Voided.php.html#30\">App\\States\\Invoices\\Voided::actions<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/States/WorkOrders</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">States</a></li>
         <li class="breadcrumb-item"><a href="index.html">WorkOrders</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AwaitingAvailability.php.html#7">App\States\WorkOrders\AwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Canceled.php.html#7">App\States\WorkOrders\Canceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClaimPending.php.html#7">App\States\WorkOrders\ClaimPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Completed.php.html#7">App\States\WorkOrders\Completed</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Created.php.html#7">App\States\WorkOrders\Created</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paused.php.html#7">App\States\WorkOrders\Paused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QualityCheck.php.html#7">App\States\WorkOrders\QualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToInvoice.php.html#7">App\States\WorkOrders\ReadyToInvoice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToSchedule.php.html#7">App\States\WorkOrders\ReadyToSchedule</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduled.php.html#7">App\States\WorkOrders\Scheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingInProgress.php.html#7">App\States\WorkOrders\SchedulingInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToAwaitingAvailability.php.html#14">App\States\WorkOrders\Transitions\ToAwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCanceled.php.html#15">App\States\WorkOrders\Transitions\ToCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToClaimPending.php.html#14">App\States\WorkOrders\Transitions\ToClaimPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCompleted.php.html#15">App\States\WorkOrders\Transitions\ToCompleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCreated.php.html#18">App\States\WorkOrders\Transitions\ToCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaused.php.html#16">App\States\WorkOrders\Transitions\ToPaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToQualityCheck.php.html#15">App\States\WorkOrders\Transitions\ToQualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToReadyToInvoice.php.html#15">App\States\WorkOrders\Transitions\ToReadyToInvoice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToReadyToSchedule.php.html#15">App\States\WorkOrders\Transitions\ToReadyToSchedule</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduled.php.html#29">App\States\WorkOrders\Transitions\ToScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToWorkInProgress.php.html#15">App\States\WorkOrders\Transitions\ToWorkInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkInProgress.php.html#7">App\States\WorkOrders\WorkInProgress</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Transitions/ToScheduled.php.html#29">App\States\WorkOrders\Transitions\ToScheduled</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Transitions/ToCreated.php.html#18">App\States\WorkOrders\Transitions\ToCreated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Transitions/ToPaused.php.html#16">App\States\WorkOrders\Transitions\ToPaused</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Transitions/ToCompleted.php.html#15">App\States\WorkOrders\Transitions\ToCompleted</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Transitions/ToCanceled.php.html#15">App\States\WorkOrders\Transitions\ToCanceled</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AwaitingAvailability.php.html#14"><abbr title="App\States\WorkOrders\AwaitingAvailability::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AwaitingAvailability.php.html#19"><abbr title="App\States\WorkOrders\AwaitingAvailability::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AwaitingAvailability.php.html#24"><abbr title="App\States\WorkOrders\AwaitingAvailability::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AwaitingAvailability.php.html#32"><abbr title="App\States\WorkOrders\AwaitingAvailability::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Canceled.php.html#14"><abbr title="App\States\WorkOrders\Canceled::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Canceled.php.html#19"><abbr title="App\States\WorkOrders\Canceled::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Canceled.php.html#24"><abbr title="App\States\WorkOrders\Canceled::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Canceled.php.html#32"><abbr title="App\States\WorkOrders\Canceled::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClaimPending.php.html#14"><abbr title="App\States\WorkOrders\ClaimPending::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClaimPending.php.html#19"><abbr title="App\States\WorkOrders\ClaimPending::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClaimPending.php.html#24"><abbr title="App\States\WorkOrders\ClaimPending::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClaimPending.php.html#32"><abbr title="App\States\WorkOrders\ClaimPending::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Completed.php.html#14"><abbr title="App\States\WorkOrders\Completed::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Completed.php.html#19"><abbr title="App\States\WorkOrders\Completed::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Completed.php.html#24"><abbr title="App\States\WorkOrders\Completed::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Completed.php.html#32"><abbr title="App\States\WorkOrders\Completed::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Created.php.html#14"><abbr title="App\States\WorkOrders\Created::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Created.php.html#19"><abbr title="App\States\WorkOrders\Created::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Created.php.html#24"><abbr title="App\States\WorkOrders\Created::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Created.php.html#32"><abbr title="App\States\WorkOrders\Created::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paused.php.html#14"><abbr title="App\States\WorkOrders\Paused::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paused.php.html#19"><abbr title="App\States\WorkOrders\Paused::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paused.php.html#24"><abbr title="App\States\WorkOrders\Paused::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Paused.php.html#32"><abbr title="App\States\WorkOrders\Paused::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QualityCheck.php.html#14"><abbr title="App\States\WorkOrders\QualityCheck::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QualityCheck.php.html#19"><abbr title="App\States\WorkOrders\QualityCheck::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QualityCheck.php.html#24"><abbr title="App\States\WorkOrders\QualityCheck::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QualityCheck.php.html#32"><abbr title="App\States\WorkOrders\QualityCheck::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToInvoice.php.html#14"><abbr title="App\States\WorkOrders\ReadyToInvoice::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToInvoice.php.html#19"><abbr title="App\States\WorkOrders\ReadyToInvoice::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToInvoice.php.html#24"><abbr title="App\States\WorkOrders\ReadyToInvoice::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToInvoice.php.html#32"><abbr title="App\States\WorkOrders\ReadyToInvoice::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToSchedule.php.html#14"><abbr title="App\States\WorkOrders\ReadyToSchedule::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToSchedule.php.html#19"><abbr title="App\States\WorkOrders\ReadyToSchedule::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToSchedule.php.html#24"><abbr title="App\States\WorkOrders\ReadyToSchedule::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToSchedule.php.html#32"><abbr title="App\States\WorkOrders\ReadyToSchedule::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduled.php.html#14"><abbr title="App\States\WorkOrders\Scheduled::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduled.php.html#19"><abbr title="App\States\WorkOrders\Scheduled::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduled.php.html#24"><abbr title="App\States\WorkOrders\Scheduled::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduled.php.html#32"><abbr title="App\States\WorkOrders\Scheduled::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingInProgress.php.html#14"><abbr title="App\States\WorkOrders\SchedulingInProgress::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingInProgress.php.html#19"><abbr title="App\States\WorkOrders\SchedulingInProgress::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingInProgress.php.html#24"><abbr title="App\States\WorkOrders\SchedulingInProgress::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingInProgress.php.html#32"><abbr title="App\States\WorkOrders\SchedulingInProgress::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToAwaitingAvailability.php.html#16"><abbr title="App\States\WorkOrders\Transitions\ToAwaitingAvailability::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToAwaitingAvailability.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToAwaitingAvailability::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCanceled.php.html#18"><abbr title="App\States\WorkOrders\Transitions\ToCanceled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCanceled.php.html#20"><abbr title="App\States\WorkOrders\Transitions\ToCanceled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToClaimPending.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToClaimPending::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToClaimPending.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToClaimPending::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCompleted.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToCompleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCompleted.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToCompleted::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCreated.php.html#20"><abbr title="App\States\WorkOrders\Transitions\ToCreated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToCreated.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToCreated::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaused.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToPaused::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToPaused.php.html#30"><abbr title="App\States\WorkOrders\Transitions\ToPaused::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToQualityCheck.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToQualityCheck::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToQualityCheck.php.html#27"><abbr title="App\States\WorkOrders\Transitions\ToQualityCheck::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToReadyToInvoice.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToReadyToInvoice::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToReadyToInvoice.php.html#23"><abbr title="App\States\WorkOrders\Transitions\ToReadyToInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToReadyToSchedule.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToReadyToSchedule::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToReadyToSchedule.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToReadyToSchedule::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduled.php.html#32"><abbr title="App\States\WorkOrders\Transitions\ToScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToScheduled.php.html#46"><abbr title="App\States\WorkOrders\Transitions\ToScheduled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToWorkInProgress.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToWorkInProgress::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Transitions/ToWorkInProgress.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToWorkInProgress::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkInProgress.php.html#14"><abbr title="App\States\WorkOrders\WorkInProgress::color">color</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkInProgress.php.html#19"><abbr title="App\States\WorkOrders\WorkInProgress::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkInProgress.php.html#24"><abbr title="App\States\WorkOrders\WorkInProgress::colorClass">colorClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkInProgress.php.html#32"><abbr title="App\States\WorkOrders\WorkInProgress::actions">actions</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Transitions/ToScheduled.php.html#46"><abbr title="App\States\WorkOrders\Transitions\ToScheduled::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Transitions/ToCreated.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToCreated::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Transitions/ToPaused.php.html#30"><abbr title="App\States\WorkOrders\Transitions\ToPaused::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Transitions/ToCompleted.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToCompleted::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Transitions/ToCanceled.php.html#20"><abbr title="App\States\WorkOrders\Transitions\ToCanceled::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:24:01 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([23,0,0,0,0,0,0,0,0,0,0,1], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([70,0,0,0,0,0,0,0,0,0,0,5], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"AwaitingAvailability.php.html#7\">App\\States\\WorkOrders\\AwaitingAvailability<\/a>"],[0,4,"<a href=\"Canceled.php.html#7\">App\\States\\WorkOrders\\Canceled<\/a>"],[0,4,"<a href=\"ClaimPending.php.html#7\">App\\States\\WorkOrders\\ClaimPending<\/a>"],[0,4,"<a href=\"Completed.php.html#7\">App\\States\\WorkOrders\\Completed<\/a>"],[0,4,"<a href=\"Created.php.html#7\">App\\States\\WorkOrders\\Created<\/a>"],[0,4,"<a href=\"Paused.php.html#7\">App\\States\\WorkOrders\\Paused<\/a>"],[0,4,"<a href=\"QualityCheck.php.html#7\">App\\States\\WorkOrders\\QualityCheck<\/a>"],[0,4,"<a href=\"ReadyToInvoice.php.html#7\">App\\States\\WorkOrders\\ReadyToInvoice<\/a>"],[0,4,"<a href=\"ReadyToSchedule.php.html#7\">App\\States\\WorkOrders\\ReadyToSchedule<\/a>"],[0,4,"<a href=\"Scheduled.php.html#7\">App\\States\\WorkOrders\\Scheduled<\/a>"],[0,4,"<a href=\"SchedulingInProgress.php.html#7\">App\\States\\WorkOrders\\SchedulingInProgress<\/a>"],[0,2,"<a href=\"Transitions\/ToAwaitingAvailability.php.html#14\">App\\States\\WorkOrders\\Transitions\\ToAwaitingAvailability<\/a>"],[0,3,"<a href=\"Transitions\/ToCanceled.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToCanceled<\/a>"],[0,2,"<a href=\"Transitions\/ToClaimPending.php.html#14\">App\\States\\WorkOrders\\Transitions\\ToClaimPending<\/a>"],[0,4,"<a href=\"Transitions\/ToCompleted.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToCompleted<\/a>"],[0,6,"<a href=\"Transitions\/ToCreated.php.html#18\">App\\States\\WorkOrders\\Transitions\\ToCreated<\/a>"],[0,5,"<a href=\"Transitions\/ToPaused.php.html#16\">App\\States\\WorkOrders\\Transitions\\ToPaused<\/a>"],[0,2,"<a href=\"Transitions\/ToQualityCheck.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToQualityCheck<\/a>"],[0,2,"<a href=\"Transitions\/ToReadyToInvoice.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToReadyToInvoice<\/a>"],[0,2,"<a href=\"Transitions\/ToReadyToSchedule.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToReadyToSchedule<\/a>"],[0,10,"<a href=\"Transitions\/ToScheduled.php.html#29\">App\\States\\WorkOrders\\Transitions\\ToScheduled<\/a>"],[0,2,"<a href=\"Transitions\/ToWorkInProgress.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToWorkInProgress<\/a>"],[0,4,"<a href=\"WorkInProgress.php.html#7\">App\\States\\WorkOrders\\WorkInProgress<\/a>"],[100,1,"<a href=\"WorkOrderState.php.html#24\">App\\States\\WorkOrders\\WorkOrderState<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AwaitingAvailability.php.html#14\">App\\States\\WorkOrders\\AwaitingAvailability::color<\/a>"],[0,1,"<a href=\"AwaitingAvailability.php.html#19\">App\\States\\WorkOrders\\AwaitingAvailability::label<\/a>"],[0,1,"<a href=\"AwaitingAvailability.php.html#24\">App\\States\\WorkOrders\\AwaitingAvailability::colorClass<\/a>"],[0,1,"<a href=\"AwaitingAvailability.php.html#32\">App\\States\\WorkOrders\\AwaitingAvailability::actions<\/a>"],[0,1,"<a href=\"Canceled.php.html#14\">App\\States\\WorkOrders\\Canceled::color<\/a>"],[0,1,"<a href=\"Canceled.php.html#19\">App\\States\\WorkOrders\\Canceled::label<\/a>"],[0,1,"<a href=\"Canceled.php.html#24\">App\\States\\WorkOrders\\Canceled::colorClass<\/a>"],[0,1,"<a href=\"Canceled.php.html#32\">App\\States\\WorkOrders\\Canceled::actions<\/a>"],[0,1,"<a href=\"ClaimPending.php.html#14\">App\\States\\WorkOrders\\ClaimPending::color<\/a>"],[0,1,"<a href=\"ClaimPending.php.html#19\">App\\States\\WorkOrders\\ClaimPending::label<\/a>"],[0,1,"<a href=\"ClaimPending.php.html#24\">App\\States\\WorkOrders\\ClaimPending::colorClass<\/a>"],[0,1,"<a href=\"ClaimPending.php.html#32\">App\\States\\WorkOrders\\ClaimPending::actions<\/a>"],[0,1,"<a href=\"Completed.php.html#14\">App\\States\\WorkOrders\\Completed::color<\/a>"],[0,1,"<a href=\"Completed.php.html#19\">App\\States\\WorkOrders\\Completed::label<\/a>"],[0,1,"<a href=\"Completed.php.html#24\">App\\States\\WorkOrders\\Completed::colorClass<\/a>"],[0,1,"<a href=\"Completed.php.html#32\">App\\States\\WorkOrders\\Completed::actions<\/a>"],[0,1,"<a href=\"Created.php.html#14\">App\\States\\WorkOrders\\Created::color<\/a>"],[0,1,"<a href=\"Created.php.html#19\">App\\States\\WorkOrders\\Created::label<\/a>"],[0,1,"<a href=\"Created.php.html#24\">App\\States\\WorkOrders\\Created::colorClass<\/a>"],[0,1,"<a href=\"Created.php.html#32\">App\\States\\WorkOrders\\Created::actions<\/a>"],[0,1,"<a href=\"Paused.php.html#14\">App\\States\\WorkOrders\\Paused::color<\/a>"],[0,1,"<a href=\"Paused.php.html#19\">App\\States\\WorkOrders\\Paused::label<\/a>"],[0,1,"<a href=\"Paused.php.html#24\">App\\States\\WorkOrders\\Paused::colorClass<\/a>"],[0,1,"<a href=\"Paused.php.html#32\">App\\States\\WorkOrders\\Paused::actions<\/a>"],[0,1,"<a href=\"QualityCheck.php.html#14\">App\\States\\WorkOrders\\QualityCheck::color<\/a>"],[0,1,"<a href=\"QualityCheck.php.html#19\">App\\States\\WorkOrders\\QualityCheck::label<\/a>"],[0,1,"<a href=\"QualityCheck.php.html#24\">App\\States\\WorkOrders\\QualityCheck::colorClass<\/a>"],[0,1,"<a href=\"QualityCheck.php.html#32\">App\\States\\WorkOrders\\QualityCheck::actions<\/a>"],[0,1,"<a href=\"ReadyToInvoice.php.html#14\">App\\States\\WorkOrders\\ReadyToInvoice::color<\/a>"],[0,1,"<a href=\"ReadyToInvoice.php.html#19\">App\\States\\WorkOrders\\ReadyToInvoice::label<\/a>"],[0,1,"<a href=\"ReadyToInvoice.php.html#24\">App\\States\\WorkOrders\\ReadyToInvoice::colorClass<\/a>"],[0,1,"<a href=\"ReadyToInvoice.php.html#32\">App\\States\\WorkOrders\\ReadyToInvoice::actions<\/a>"],[0,1,"<a href=\"ReadyToSchedule.php.html#14\">App\\States\\WorkOrders\\ReadyToSchedule::color<\/a>"],[0,1,"<a href=\"ReadyToSchedule.php.html#19\">App\\States\\WorkOrders\\ReadyToSchedule::label<\/a>"],[0,1,"<a href=\"ReadyToSchedule.php.html#24\">App\\States\\WorkOrders\\ReadyToSchedule::colorClass<\/a>"],[0,1,"<a href=\"ReadyToSchedule.php.html#32\">App\\States\\WorkOrders\\ReadyToSchedule::actions<\/a>"],[0,1,"<a href=\"Scheduled.php.html#14\">App\\States\\WorkOrders\\Scheduled::color<\/a>"],[0,1,"<a href=\"Scheduled.php.html#19\">App\\States\\WorkOrders\\Scheduled::label<\/a>"],[0,1,"<a href=\"Scheduled.php.html#24\">App\\States\\WorkOrders\\Scheduled::colorClass<\/a>"],[0,1,"<a href=\"Scheduled.php.html#32\">App\\States\\WorkOrders\\Scheduled::actions<\/a>"],[0,1,"<a href=\"SchedulingInProgress.php.html#14\">App\\States\\WorkOrders\\SchedulingInProgress::color<\/a>"],[0,1,"<a href=\"SchedulingInProgress.php.html#19\">App\\States\\WorkOrders\\SchedulingInProgress::label<\/a>"],[0,1,"<a href=\"SchedulingInProgress.php.html#24\">App\\States\\WorkOrders\\SchedulingInProgress::colorClass<\/a>"],[0,1,"<a href=\"SchedulingInProgress.php.html#32\">App\\States\\WorkOrders\\SchedulingInProgress::actions<\/a>"],[0,1,"<a href=\"Transitions\/ToAwaitingAvailability.php.html#16\">App\\States\\WorkOrders\\Transitions\\ToAwaitingAvailability::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToAwaitingAvailability.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToAwaitingAvailability::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToCanceled.php.html#18\">App\\States\\WorkOrders\\Transitions\\ToCanceled::__construct<\/a>"],[0,2,"<a href=\"Transitions\/ToCanceled.php.html#20\">App\\States\\WorkOrders\\Transitions\\ToCanceled::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToClaimPending.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToClaimPending::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToClaimPending.php.html#19\">App\\States\\WorkOrders\\Transitions\\ToClaimPending::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToCompleted.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToCompleted::__construct<\/a>"],[0,3,"<a href=\"Transitions\/ToCompleted.php.html#19\">App\\States\\WorkOrders\\Transitions\\ToCompleted::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToCreated.php.html#20\">App\\States\\WorkOrders\\Transitions\\ToCreated::__construct<\/a>"],[0,5,"<a href=\"Transitions\/ToCreated.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToCreated::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToPaused.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToPaused::__construct<\/a>"],[0,4,"<a href=\"Transitions\/ToPaused.php.html#30\">App\\States\\WorkOrders\\Transitions\\ToPaused::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToQualityCheck.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToQualityCheck::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToQualityCheck.php.html#27\">App\\States\\WorkOrders\\Transitions\\ToQualityCheck::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToReadyToInvoice.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToReadyToInvoice::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToReadyToInvoice.php.html#23\">App\\States\\WorkOrders\\Transitions\\ToReadyToInvoice::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToReadyToSchedule.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToReadyToSchedule::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToReadyToSchedule.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToReadyToSchedule::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToScheduled.php.html#32\">App\\States\\WorkOrders\\Transitions\\ToScheduled::__construct<\/a>"],[0,9,"<a href=\"Transitions\/ToScheduled.php.html#46\">App\\States\\WorkOrders\\Transitions\\ToScheduled::handle<\/a>"],[0,1,"<a href=\"Transitions\/ToWorkInProgress.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToWorkInProgress::__construct<\/a>"],[0,1,"<a href=\"Transitions\/ToWorkInProgress.php.html#19\">App\\States\\WorkOrders\\Transitions\\ToWorkInProgress::handle<\/a>"],[0,1,"<a href=\"WorkInProgress.php.html#14\">App\\States\\WorkOrders\\WorkInProgress::color<\/a>"],[0,1,"<a href=\"WorkInProgress.php.html#19\">App\\States\\WorkOrders\\WorkInProgress::label<\/a>"],[0,1,"<a href=\"WorkInProgress.php.html#24\">App\\States\\WorkOrders\\WorkInProgress::colorClass<\/a>"],[0,1,"<a href=\"WorkInProgress.php.html#32\">App\\States\\WorkOrders\\WorkInProgress::actions<\/a>"],[100,1,"<a href=\"WorkOrderState.php.html#29\">App\\States\\WorkOrders\\WorkOrderState::config<\/a>"],[100,0,"<a href=\"WorkOrderState.php.html#91\">App\\States\\WorkOrders\\WorkOrderState::color<\/a>"],[100,0,"<a href=\"WorkOrderState.php.html#93\">App\\States\\WorkOrders\\WorkOrderState::colorClass<\/a>"],[100,0,"<a href=\"WorkOrderState.php.html#95\">App\\States\\WorkOrders\\WorkOrderState::label<\/a>"],[100,0,"<a href=\"WorkOrderState.php.html#100\">App\\States\\WorkOrders\\WorkOrderState::actions<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

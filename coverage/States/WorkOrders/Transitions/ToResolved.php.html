<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/States/WorkOrders/Transitions/ToResolved.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">States</a></li>
         <li class="breadcrumb-item"><a href="../index.html">WorkOrders</a></li>
         <li class="breadcrumb-item"><a href="index.html">Transitions</a></li>
         <li class="breadcrumb-item active">ToResolved.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">23&nbsp;/&nbsp;23</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">2&nbsp;/&nbsp;2</div></td>
       <td class="success small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><abbr title="App\States\WorkOrders\Transitions\ToResolved">ToResolved</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">23&nbsp;/&nbsp;23</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">2&nbsp;/&nbsp;2</div></td>
       <td class="success small">4</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#17"><abbr title="__construct(App\Models\WorkOrder $workOrder, ?App\Models\User $user, ?App\Models\WorkOrderTask $workOrderTask)">__construct</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#19"><abbr title="handle(): App\Models\WorkOrder">handle</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">22&nbsp;/&nbsp;22</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">3</td>
       <td class="success" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\Transitions</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Enums\WorkOrderStatus</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">WorkOrderStatusEnum</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Events\WorkOrder\WorkOrderResolve</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\User</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\WorkOrder</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\WorkOrderStatus</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\WorkOrderTask</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\Resolved</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Carbon\CarbonImmutable</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Spatie\ModelStates\Transition</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">ToResolved</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">Transition</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="11 tests cover line 17" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_not_possible_to_access_fully_paid_invoice_API_with_invoice_state_other_than_PaymentPending__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">__construct</span><span class="keyword">(</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">WorkOrder</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">User</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">WorkOrderTask</span><span class="default">&nbsp;</span><span class="default">$workOrderTask</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handle</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">WorkOrder</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 21" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$oldState</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">state</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Change&nbsp;status</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 23" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$workOrderStatus</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">WorkOrderStatus</span><span class="default">::</span><span class="default">where</span><span class="keyword">(</span><span class="default">'slug'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">WorkOrderStatusEnum</span><span class="default">::</span><span class="default">RESOLVED</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 24" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">work_order_status_id</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$workOrderStatus</span><span class="default">-&gt;</span><span class="default">work_order_status_id</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 26" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">Resolved</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 27" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">state_updated_at</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">CarbonImmutable</span><span class="default">::</span><span class="default">now</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 28" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">resolved_at</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">CarbonImmutable</span><span class="default">::</span><span class="default">now</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 30" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">save</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 32" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrderTask</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;trigger&nbsp;event</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 34" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$activityLogEventAttributes</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 35" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'from'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$oldState</span><span class="default">-&gt;</span><span class="default">label</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 36" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'from_color_class'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$oldState</span><span class="default">-&gt;</span><span class="default">colorClass</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 37" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'to'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">label</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 38" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'to_color_class'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">colorClass</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 39" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'slug_of_to'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">getValue</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 40" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 42" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">event</span><span class="keyword">(</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">WorkOrderResolve</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 43" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">work_order_id</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 44" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrderTask</span><span class="default">-&gt;</span><span class="default">work_order_task_id</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 45" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$activityLogEventAttributes</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 46" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">user</span><span class="default">?-&gt;</span><span class="default">user_id</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 47" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 50" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.17</a> at Wed Jun 25 16:26:03 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

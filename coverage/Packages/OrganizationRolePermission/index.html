<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Packages/OrganizationRolePermission</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Packages</a></li>
         <li class="breadcrumb-item active">OrganizationRolePermission</li>
         <li class="breadcrumb-item">(<a href="dashboard.html">Dashboard</a>)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="warning">Total</td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="80.54" aria-valuemin="0" aria-valuemax="100" style="width: 80.54%">
           <span class="sr-only">80.54% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">80.54%</div></td>
       <td class="warning small"><div align="right">269&nbsp;/&nbsp;334</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="59.26" aria-valuemin="0" aria-valuemax="100" style="width: 59.26%">
           <span class="sr-only">59.26% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">59.26%</div></td>
       <td class="warning small"><div align="right">32&nbsp;/&nbsp;54</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="14.29" aria-valuemin="0" aria-valuemax="100" style="width: 14.29%">
           <span class="sr-only">14.29% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">14.29%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;7</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../../_icons/file-directory.svg" class="octicon" /><a href="Exceptions/index.html">Exceptions</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="../../_icons/file-directory.svg" class="octicon" /><a href="Traits/index.html">Traits</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="63.64" aria-valuemin="0" aria-valuemax="100" style="width: 63.64%">
           <span class="sr-only">63.64% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">63.64%</div></td>
       <td class="warning small"><div align="right">28&nbsp;/&nbsp;44</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="40.00" aria-valuemin="0" aria-valuemax="100" style="width: 40.00%">
           <span class="sr-only">40.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">40.00%</div></td>
       <td class="danger small"><div align="right">2&nbsp;/&nbsp;5</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;3</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="../../_icons/file-code.svg" class="octicon" /><a href="OrganizationManager.php.html">OrganizationManager.php</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="82.05" aria-valuemin="0" aria-valuemax="100" style="width: 82.05%">
           <span class="sr-only">82.05% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">82.05%</div></td>
       <td class="warning small"><div align="right">128&nbsp;/&nbsp;156</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="58.33" aria-valuemin="0" aria-valuemax="100" style="width: 58.33%">
           <span class="sr-only">58.33% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">58.33%</div></td>
       <td class="warning small"><div align="right">14&nbsp;/&nbsp;24</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../../_icons/file-code.svg" class="octicon" /><a href="OrganizationServiceProvider.php.html">OrganizationServiceProvider.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">9&nbsp;/&nbsp;9</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="../../_icons/file-code.svg" class="octicon" /><a href="OrganizationUserManager.php.html">OrganizationUserManager.php</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="83.87" aria-valuemin="0" aria-valuemax="100" style="width: 83.87%">
           <span class="sr-only">83.87% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">83.87%</div></td>
       <td class="warning small"><div align="right">104&nbsp;/&nbsp;124</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="65.22" aria-valuemin="0" aria-valuemax="100" style="width: 65.22%">
           <span class="sr-only">65.22% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">65.22%</div></td>
       <td class="warning small"><div align="right">15&nbsp;/&nbsp;23</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 50%</span>
     <span class="warning"><strong>Medium</strong>: 50% to 90%</span>
     <span class="success"><strong>High</strong>: 90% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Mon Jun 30 0:32:48 UTC 2025.</small>
    </p>
   </footer>
  </div>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Models</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Models</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DatabaseNotification.php.html#37">App\Models\DatabaseNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeveloperAlert.php.html#10">App\Models\DeveloperAlert</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItem.php.html#31">App\Models\InvoiceLineItem</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItemSubsidiary.php.html#38">App\Models\InvoiceLineItemSubsidiary</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Material.php.html#34">App\Models\Material</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media.php.html#66">App\Models\Media</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationIdentityProvider.php.html#11">App\Models\OrganizationIdentityProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiServiceRequestWebhookEvent.php.html#31">App\Models\PublicApiServiceRequestWebhookEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEvent.php.html#53">App\Models\PublicApiWorkOrderWebhookEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#45">App\Models\Quote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTask.php.html#42">App\Models\QuoteTask</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTaskMaterial.php.html#37">App\Models\QuoteTaskMaterial</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLog.php.html#8">App\Models\RequestLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident.php.html#64">App\Models\Resident</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailability.php.html#35">App\Models\ResidentAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivityLog.php.html#60">App\Models\ServiceRequestActivityLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignee.php.html#29">App\Models\ServiceRequestAssignee</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestCategory.php.html#12">App\Models\ServiceRequestCategory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDescription.php.html#32">App\Models\ServiceRequestDescription</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMedia.php.html#37">App\Models\ServiceRequestMedia</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNote.php.html#60">App\Models\ServiceRequestNote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStatus.php.html#20">App\Models\ServiceRequestStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStatusLog.php.html#9">App\Models\ServiceRequestStatusLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician.php.html#66">App\Models\Technician</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointment.php.html#105">App\Models\TechnicianAppointment</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointmentLog.php.html#41">App\Models\TechnicianAppointmentLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianSkill.php.html#53">App\Models\TechnicianSkill</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianWorkingHour.php.html#53">App\Models\TechnicianWorkingHour</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Timezone.php.html#23">App\Models\Timezone</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorAllocation.php.html#27">App\Models\VendorAllocation</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPublicApiLog.php.html#11">App\Models\VendorPublicApiLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View.php.html#62">App\Models\View</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCall.php.html#14">App\Models\WebhookCall</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivityLog.php.html#58">App\Models\WorkOrderActivityLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssignee.php.html#27">App\Models\WorkOrderAssignee</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthLog.php.html#24">App\Models\WorkOrderHealthLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthViolation.php.html#25">App\Models\WorkOrderHealthViolation</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssueStatus.php.html#8">App\Models\WorkOrderIssueStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMedia.php.html#62">App\Models\WorkOrderMedia</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNote.php.html#63">App\Models\WorkOrderNote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCallIssue.php.html#19">App\Models\WorkOrderServiceCallIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCallLog.php.html#49">App\Models\WorkOrderServiceCallLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCallTask.php.html#41">App\Models\WorkOrderServiceCallTask</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#30">App\Models\WorkOrderStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatusLog.php.html#34">App\Models\WorkOrderStatusLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#65">App\Models\WorkOrderTask</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskMaterial.php.html#35">App\Models\WorkOrderTaskMaterial</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#119">App\Models\WorkOrderServiceCall</a></td><td class="text-right">9%</td></tr>
       <tr><td><a href="Invoice.php.html#44">App\Models\Invoice</a></td><td class="text-right">13%</td></tr>
       <tr><td><a href="WorkOrder.php.html#152">App\Models\WorkOrder</a></td><td class="text-right">15%</td></tr>
       <tr><td><a href="Role.php.html#63">App\Models\Role</a></td><td class="text-right">15%</td></tr>
       <tr><td><a href="WorkOrderIssue.php.html#40">App\Models\WorkOrderIssue</a></td><td class="text-right">18%</td></tr>
       <tr><td><a href="User.php.html#133">App\Models\User</a></td><td class="text-right">28%</td></tr>
       <tr><td><a href="ServiceRequest.php.html#81">App\Models\ServiceRequest</a></td><td class="text-right">29%</td></tr>
       <tr><td><a href="Property.php.html#88">App\Models\Property</a></td><td class="text-right">37%</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#19">App\Models\QueryBuilder\WorkOrderListQueryBuilder</a></td><td class="text-right">44%</td></tr>
       <tr><td><a href="Organization.php.html#89">App\Models\Organization</a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="Permission.php.html#48">App\Models\Permission</a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="ProblemDiagnosis.php.html#53">App\Models\ProblemDiagnosis</a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="Tag.php.html#52">App\Models\Tag</a></td><td class="text-right">50%</td></tr>
       <tr><td><a href="WorkOrderHealthTracker.php.html#25">App\Models\WorkOrderHealthTracker</a></td><td class="text-right">62%</td></tr>
       <tr><td><a href="Issue.php.html#38">App\Models\Issue</a></td><td class="text-right">83%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrder.php.html#152">App\Models\WorkOrder</a></td><td class="text-right">4388</td></tr>
       <tr><td><a href="User.php.html#133">App\Models\User</a></td><td class="text-right">426</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#119">App\Models\WorkOrderServiceCall</a></td><td class="text-right">411</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#19">App\Models\QueryBuilder\WorkOrderListQueryBuilder</a></td><td class="text-right">320</td></tr>
       <tr><td><a href="ServiceRequest.php.html#81">App\Models\ServiceRequest</a></td><td class="text-right">243</td></tr>
       <tr><td><a href="Invoice.php.html#44">App\Models\Invoice</a></td><td class="text-right">123</td></tr>
       <tr><td><a href="QuoteTaskMaterial.php.html#37">App\Models\QuoteTaskMaterial</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Organization.php.html#89">App\Models\Organization</a></td><td class="text-right">43</td></tr>
       <tr><td><a href="WorkOrderTaskMaterial.php.html#35">App\Models\WorkOrderTaskMaterial</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Role.php.html#63">App\Models\Role</a></td><td class="text-right">27</td></tr>
       <tr><td><a href="Property.php.html#88">App\Models\Property</a></td><td class="text-right">23</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DatabaseNotification.php.html#87"><abbr title="App\Models\DatabaseNotification::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotification.php.html#98"><abbr title="App\Models\DatabaseNotification::scopeCleared">scopeCleared</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotification.php.html#106"><abbr title="App\Models\DatabaseNotification::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotification.php.html#114"><abbr title="App\Models\DatabaseNotification::serviceRequest">serviceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotification.php.html#122"><abbr title="App\Models\DatabaseNotification::user">user</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeveloperAlert.php.html#38"><abbr title="App\Models\DeveloperAlert::organization">organization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice.php.html#90"><abbr title="App\Models\Invoice::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice.php.html#98"><abbr title="App\Models\Invoice::organization">organization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice.php.html#106"><abbr title="App\Models\Invoice::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice.php.html#114"><abbr title="App\Models\Invoice::lineItems">lineItems</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice.php.html#122"><abbr title="App\Models\Invoice::createdByUser">createdByUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice.php.html#130"><abbr title="App\Models\Invoice::draftedByUser">draftedByUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice.php.html#138"><abbr title="App\Models\Invoice::getTotalMarkupFeeAttribute">getTotalMarkupFeeAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice.php.html#148"><abbr title="App\Models\Invoice::getTotalCostAttribute">getTotalCostAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice.php.html#158"><abbr title="App\Models\Invoice::getCostAttribute">getCostAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItem.php.html#62"><abbr title="App\Models\InvoiceLineItem::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItem.php.html#70"><abbr title="App\Models\InvoiceLineItem::invoice">invoice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItem.php.html#78"><abbr title="App\Models\InvoiceLineItem::serviceCall">serviceCall</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItem.php.html#86"><abbr title="App\Models\InvoiceLineItem::quoteTask">quoteTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItem.php.html#94"><abbr title="App\Models\InvoiceLineItem::quote">quote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItem.php.html#102"><abbr title="App\Models\InvoiceLineItem::subsidiaries">subsidiaries</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItemSubsidiary.php.html#78"><abbr title="App\Models\InvoiceLineItemSubsidiary::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItemSubsidiary.php.html#86"><abbr title="App\Models\InvoiceLineItemSubsidiary::invoiceLineItem">invoiceLineItem</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItemSubsidiary.php.html#94"><abbr title="App\Models\InvoiceLineItemSubsidiary::workOrderTaskMaterial">workOrderTaskMaterial</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItemSubsidiary.php.html#102"><abbr title="App\Models\InvoiceLineItemSubsidiary::quoteTaskMaterial">quoteTaskMaterial</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItemSubsidiary.php.html#110"><abbr title="App\Models\InvoiceLineItemSubsidiary::quoteTask">quoteTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue.php.html#133"><abbr title="App\Models\Issue::user">user</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Material.php.html#77"><abbr title="App\Models\Material::workOrderIssues">workOrderIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media.php.html#105"><abbr title="App\Models\Media::workOrderMedia">workOrderMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media.php.html#113"><abbr title="App\Models\Media::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media.php.html#129"><abbr title="App\Models\Media::serviceRequestMedia">serviceRequestMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media.php.html#137"><abbr title="App\Models\Media::serviceRequest">serviceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media.php.html#149"><abbr title="App\Models\Media::getBasePath">getBasePath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media.php.html#154"><abbr title="App\Models\Media::getServiceRequestBasePath">getServiceRequestBasePath</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media.php.html#162"><abbr title="App\Models\Media::getTemporaryMediaUrl">getTemporaryMediaUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media.php.html#189"><abbr title="App\Models\Media::getServiceRequestTemporaryMediaUrl">getServiceRequestTemporaryMediaUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization.php.html#157"><abbr title="App\Models\Organization::accounts">accounts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization.php.html#167"><abbr title="App\Models\Organization::users">users</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization.php.html#175"><abbr title="App\Models\Organization::roles">roles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization.php.html#193"><abbr title="App\Models\Organization::identityProviders">identityProviders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization.php.html#234"><abbr title="App\Models\Organization::getMediaPathPrefix">getMediaPathPrefix</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization.php.html#255"><abbr title="App\Models\Organization::logoUrl">logoUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization.php.html#271"><abbr title="App\Models\Organization::vendorSettings">vendorSettings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization.php.html#279"><abbr title="App\Models\Organization::vendors">vendors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationIdentityProvider.php.html#47"><abbr title="App\Models\OrganizationIdentityProvider::organization">organization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Permission.php.html#87"><abbr title="App\Models\Permission::role">role</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProblemDiagnosis.php.html#97"><abbr title="App\Models\ProblemDiagnosis::issues">issues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Property.php.html#150"><abbr title="App\Models\Property::country">country</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Property.php.html#155"><abbr title="App\Models\Property::getInlineFullAddressFormat">getInlineFullAddressFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Property.php.html#197"><abbr title="App\Models\Property::timezone">timezone</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiServiceRequestWebhookEvent.php.html#67"><abbr title="App\Models\PublicApiServiceRequestWebhookEvent::boot">boot</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEvent.php.html#89"><abbr title="App\Models\PublicApiWorkOrderWebhookEvent::boot">boot</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#155"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::joinTechnicianAppointmentRelations">joinTechnicianAppointmentRelations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#181"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::mobileAppListViewConditions">mobileAppListViewConditions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#246"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::applySort">applySort</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#251"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::applyWorkOrderHealthFilter">applyWorkOrderHealthFilter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#292"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::applySearch">applySearch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#320"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::orderByWorkOrder">orderByWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#339"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::getWorkOrders">getWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#344"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::getCount">getCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#101"><abbr title="App\Models\Quote::booted">booted</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#110"><abbr title="App\Models\Quote::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#120"><abbr title="App\Models\Quote::quoteTasks">quoteTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#128"><abbr title="App\Models\Quote::submittedUser">submittedUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#136"><abbr title="App\Models\Quote::approvedUser">approvedUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#144"><abbr title="App\Models\Quote::rejectedUser">rejectedUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#152"><abbr title="App\Models\Quote::reviewedUser">reviewedUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#160"><abbr title="App\Models\Quote::lastModifiedUser">lastModifiedUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#170"><abbr title="App\Models\Quote::serviceCalls">serviceCalls</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#178"><abbr title="App\Models\Quote::serviceCall">serviceCall</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#186"><abbr title="App\Models\Quote::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#194"><abbr title="App\Models\Quote::workOrderTask">workOrderTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#204"><abbr title="App\Models\Quote::assignedServiceCalls">assignedServiceCalls</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote.php.html#212"><abbr title="App\Models\Quote::latestAssignedServiceCall">latestAssignedServiceCall</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTask.php.html#85"><abbr title="App\Models\QuoteTask::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTask.php.html#93"><abbr title="App\Models\QuoteTask::user">user</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTask.php.html#101"><abbr title="App\Models\QuoteTask::task">task</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTask.php.html#109"><abbr title="App\Models\QuoteTask::quote">quote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTask.php.html#119"><abbr title="App\Models\QuoteTask::quoteTaskMaterials">quoteTaskMaterials</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTask.php.html#129"><abbr title="App\Models\QuoteTask::media">media</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTaskMaterial.php.html#74"><abbr title="App\Models\QuoteTaskMaterial::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTaskMaterial.php.html#82"><abbr title="App\Models\QuoteTaskMaterial::user">user</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTaskMaterial.php.html#90"><abbr title="App\Models\QuoteTaskMaterial::quoteTask">quoteTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTaskMaterial.php.html#95"><abbr title="App\Models\QuoteTaskMaterial::unitPrice">unitPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTaskMaterial.php.html#104"><abbr title="App\Models\QuoteTaskMaterial::unitPriceWithoutMarkUp">unitPriceWithoutMarkUp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLog.php.html#29"><abbr title="App\Models\RequestLog::user">user</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLog.php.html#37"><abbr title="App\Models\RequestLog::organization">organization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident.php.html#102"><abbr title="App\Models\Resident::getNameAttribute">getNameAttribute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident.php.html#107"><abbr title="App\Models\Resident::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident.php.html#115"><abbr title="App\Models\Resident::property">property</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident.php.html#120"><abbr title="App\Models\Resident::routeNotificationForTwilio">routeNotificationForTwilio</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailability.php.html#79"><abbr title="App\Models\ResidentAvailability::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailability.php.html#87"><abbr title="App\Models\ResidentAvailability::serviceRequest">serviceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Role.php.html#102"><abbr title="App\Models\Role::organizationRoles">organizationRoles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Role.php.html#123"><abbr title="App\Models\Role::syncPermissions">syncPermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Role.php.html#135"><abbr title="App\Models\Role::clearOrganizationRoleCache">clearOrganizationRoleCache</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest.php.html#210"><abbr title="App\Models\ServiceRequest::source">source</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest.php.html#218"><abbr title="App\Models\ServiceRequest::updatedByUser">updatedByUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest.php.html#226"><abbr title="App\Models\ServiceRequest::categories">categories</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest.php.html#234"><abbr title="App\Models\ServiceRequest::assignees">assignees</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest.php.html#242"><abbr title="App\Models\ServiceRequest::media">media</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest.php.html#269"><abbr title="App\Models\ServiceRequest::serviceRequestDescriptions">serviceRequestDescriptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest.php.html#277"><abbr title="App\Models\ServiceRequest::latestDescription">latestDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest.php.html#287"><abbr title="App\Models\ServiceRequest::issues">issues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest.php.html#295"><abbr title="App\Models\ServiceRequest::resolveStateAbilities">resolveStateAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivityLog.php.html#102"><abbr title="App\Models\ServiceRequestActivityLog::serviceRequest">serviceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivityLog.php.html#110"><abbr title="App\Models\ServiceRequestActivityLog::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivityLog.php.html#118"><abbr title="App\Models\ServiceRequestActivityLog::workOrderTask">workOrderTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivityLog.php.html#126"><abbr title="App\Models\ServiceRequestActivityLog::triggeredBy">triggeredBy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignee.php.html#62"><abbr title="App\Models\ServiceRequestAssignee::serviceRequest">serviceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignee.php.html#70"><abbr title="App\Models\ServiceRequestAssignee::user">user</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestCategory.php.html#49"><abbr title="App\Models\ServiceRequestCategory::serviceRequest">serviceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestCategory.php.html#57"><abbr title="App\Models\ServiceRequestCategory::problemCategory">problemCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestCategory.php.html#65"><abbr title="App\Models\ServiceRequestCategory::problemSubCategory">problemSubCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDescription.php.html#75"><abbr title="App\Models\ServiceRequestDescription::createdUser">createdUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDescription.php.html#85"><abbr title="App\Models\ServiceRequestDescription::serviceRequest">serviceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMedia.php.html#81"><abbr title="App\Models\ServiceRequestMedia::media">media</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMedia.php.html#89"><abbr title="App\Models\ServiceRequestMedia::serviceRequest">serviceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNote.php.html#99"><abbr title="App\Models\ServiceRequestNote::serviceRequest">serviceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNote.php.html#107"><abbr title="App\Models\ServiceRequestNote::user">user</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNote.php.html#115"><abbr title="App\Models\ServiceRequestNote::vendor">vendor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNote.php.html#123"><abbr title="App\Models\ServiceRequestNote::lastModifiedUser">lastModifiedUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStatus.php.html#47"><abbr title="App\Models\ServiceRequestStatus::serviceRequests">serviceRequests</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStatusLog.php.html#37"><abbr title="App\Models\ServiceRequestStatusLog::serviceRequest">serviceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStatusLog.php.html#45"><abbr title="App\Models\ServiceRequestStatusLog::updatedBy">updatedBy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStatusLog.php.html#53"><abbr title="App\Models\ServiceRequestStatusLog::serviceRequestStatus">serviceRequestStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag.php.html#100"><abbr title="App\Models\Tag::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician.php.html#103"><abbr title="App\Models\Technician::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician.php.html#111"><abbr title="App\Models\Technician::user">user</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician.php.html#121"><abbr title="App\Models\Technician::workingHours">workingHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician.php.html#131"><abbr title="App\Models\Technician::skills">skills</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician.php.html#141"><abbr title="App\Models\Technician::appointments">appointments</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician.php.html#149"><abbr title="App\Models\Technician::problemDiagnoses">problemDiagnoses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointment.php.html#175"><abbr title="App\Models\TechnicianAppointment::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointment.php.html#183"><abbr title="App\Models\TechnicianAppointment::technician">technician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointment.php.html#191"><abbr title="App\Models\TechnicianAppointment::serviceCall">serviceCall</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointment.php.html#199"><abbr title="App\Models\TechnicianAppointment::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointment.php.html#207"><abbr title="App\Models\TechnicianAppointment::property">property</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointment.php.html#217"><abbr title="App\Models\TechnicianAppointment::appointmentLogs">appointmentLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointmentLog.php.html#83"><abbr title="App\Models\TechnicianAppointmentLog::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointmentLog.php.html#91"><abbr title="App\Models\TechnicianAppointmentLog::technicianAppointment">technicianAppointment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianSkill.php.html#86"><abbr title="App\Models\TechnicianSkill::problemCategory">problemCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianSkill.php.html#94"><abbr title="App\Models\TechnicianSkill::problemSubCategory">problemSubCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianSkill.php.html#102"><abbr title="App\Models\TechnicianSkill::problemDiagnosis">problemDiagnosis</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianWorkingHour.php.html#99"><abbr title="App\Models\TechnicianWorkingHour::technician">technician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Timezone.php.html#55"><abbr title="App\Models\Timezone::scopeDefaultTimzone">scopeDefaultTimzone</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#214"><abbr title="App\Models\User::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#224"><abbr title="App\Models\User::portfolios">portfolios</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#250"><abbr title="App\Models\User::getCachedRoles">getCachedRoles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#280"><abbr title="App\Models\User::syncRoles">syncRoles</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#292"><abbr title="App\Models\User::permissions">permissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#297"><abbr title="App\Models\User::hasRole">hasRole</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#336"><abbr title="App\Models\User::vendor">vendor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#346"><abbr title="App\Models\User::lastPasswordUpdatedUser">lastPasswordUpdatedUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#369"><abbr title="App\Models\User::getInlineFullAddressFormat">getInlineFullAddressFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#396"><abbr title="App\Models\User::getAddress">getAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#410"><abbr title="App\Models\User::scopeActive">scopeActive</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#415"><abbr title="App\Models\User::routeNotificationForTwilio">routeNotificationForTwilio</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#423"><abbr title="App\Models\User::receivesBroadcastNotificationsOn">receivesBroadcastNotificationsOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#433"><abbr title="App\Models\User::notifications">notifications</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#443"><abbr title="App\Models\User::clearedNotifications">clearedNotifications</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#453"><abbr title="App\Models\User::unClearedUnreadNotifications">unClearedUnreadNotifications</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorAllocation.php.html#59"><abbr title="App\Models\VendorAllocation::vendor">vendor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPublicApiLog.php.html#38"><abbr title="App\Models\VendorPublicApiLog::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPublicApiLog.php.html#46"><abbr title="App\Models\VendorPublicApiLog::vendor">vendor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View.php.html#106"><abbr title="App\Models\View::userPinnedViews">userPinnedViews</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View.php.html#116"><abbr title="App\Models\View::userDefaultView">userDefaultView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View.php.html#126"><abbr title="App\Models\View::userDefaultViews">userDefaultViews</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View.php.html#136"><abbr title="App\Models\View::viewType">viewType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCall.php.html#39"><abbr title="App\Models\WebhookCall::storeWebhook">storeWebhook</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCall.php.html#56"><abbr title="App\Models\WebhookCall::organization">organization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder.php.html#244"><abbr title="App\Models\WorkOrder::organization">organization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder.php.html#285"><abbr title="App\Models\WorkOrder::tasks">tasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder.php.html#375"><abbr title="App\Models\WorkOrder::workOrderStatus">workOrderStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder.php.html#403"><abbr title="App\Models\WorkOrder::trips">trips</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder.php.html#408"><abbr title="App\Models\WorkOrder::activeTrip">activeTrip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder.php.html#424"><abbr title="App\Models\WorkOrder::resolveStateAbilities">resolveStateAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivityLog.php.html#97"><abbr title="App\Models\WorkOrderActivityLog::triggeredBy">triggeredBy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivityLog.php.html#105"><abbr title="App\Models\WorkOrderActivityLog::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssignee.php.html#60"><abbr title="App\Models\WorkOrderAssignee::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssignee.php.html#68"><abbr title="App\Models\WorkOrderAssignee::user">user</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthLog.php.html#49"><abbr title="App\Models\WorkOrderHealthLog::healthTracker">healthTracker</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthLog.php.html#57"><abbr title="App\Models\WorkOrderHealthLog::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthTracker.php.html#68"><abbr title="App\Models\WorkOrderHealthTracker::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthTracker.php.html#76"><abbr title="App\Models\WorkOrderHealthTracker::violation">violation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthTracker.php.html#84"><abbr title="App\Models\WorkOrderHealthTracker::healthLogs">healthLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthViolation.php.html#52"><abbr title="App\Models\WorkOrderHealthViolation::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderHealthViolation.php.html#60"><abbr title="App\Models\WorkOrderHealthViolation::healthTrackers">healthTrackers</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue.php.html#110"><abbr title="App\Models\WorkOrderIssue::workOrderIssueStatus">workOrderIssueStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue.php.html#120"><abbr title="App\Models\WorkOrderIssue::materials">materials</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue.php.html#133"><abbr title="App\Models\WorkOrderIssue::media">media</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssueStatus.php.html#32"><abbr title="App\Models\WorkOrderIssueStatus::workOrderIssues">workOrderIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMedia.php.html#110"><abbr title="App\Models\WorkOrderMedia::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMedia.php.html#118"><abbr title="App\Models\WorkOrderMedia::quoteTask">quoteTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMedia.php.html#126"><abbr title="App\Models\WorkOrderMedia::media">media</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNote.php.html#104"><abbr title="App\Models\WorkOrderNote::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNote.php.html#112"><abbr title="App\Models\WorkOrderNote::user">user</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNote.php.html#120"><abbr title="App\Models\WorkOrderNote::vendor">vendor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNote.php.html#128"><abbr title="App\Models\WorkOrderNote::lastModifiedUser">lastModifiedUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#213"><abbr title="App\Models\WorkOrderServiceCall::appointment">appointment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#245"><abbr title="App\Models\WorkOrderServiceCall::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#253"><abbr title="App\Models\WorkOrderServiceCall::tasks">tasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#268"><abbr title="App\Models\WorkOrderServiceCall::workOrderTaskMaterials">workOrderTaskMaterials</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#276"><abbr title="App\Models\WorkOrderServiceCall::serviceCallLogs">serviceCallLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#284"><abbr title="App\Models\WorkOrderServiceCall::latestServiceCallLogs">latestServiceCallLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#292"><abbr title="App\Models\WorkOrderServiceCall::latestTripLogs">latestTripLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#297"><abbr title="App\Models\WorkOrderServiceCall::latestTrip">latestTrip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#305"><abbr title="App\Models\WorkOrderServiceCall::media">media</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#316"><abbr title="App\Models\WorkOrderServiceCall::createdQuote">createdQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#324"><abbr title="App\Models\WorkOrderServiceCall::assignedQuote">assignedQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#332"><abbr title="App\Models\WorkOrderServiceCall::lastModifiedUser">lastModifiedUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#340"><abbr title="App\Models\WorkOrderServiceCall::tripIssues">tripIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#348"><abbr title="App\Models\WorkOrderServiceCall::workPerformed">workPerformed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#378"><abbr title="App\Models\WorkOrderServiceCall::vendorExternalInvoices">vendorExternalInvoices</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#388"><abbr title="App\Models\WorkOrderServiceCall::workOrderIssues">workOrderIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCallIssue.php.html#47"><abbr title="App\Models\WorkOrderServiceCallIssue::workOrderIssue">workOrderIssue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCallLog.php.html#86"><abbr title="App\Models\WorkOrderServiceCallLog::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCallTask.php.html#67"><abbr title="App\Models\WorkOrderServiceCallTask::task">task</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderServiceCallTask.php.html#75"><abbr title="App\Models\WorkOrderServiceCallTask::serviceCall">serviceCall</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#54"><abbr title="App\Models\WorkOrderStatus::workOrders">workOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatusLog.php.html#63"><abbr title="App\Models\WorkOrderStatusLog::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatusLog.php.html#71"><abbr title="App\Models\WorkOrderStatusLog::workOrderStatus">workOrderStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#86"><abbr title="App\Models\WorkOrderTask::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#94"><abbr title="App\Models\WorkOrderTask::organization">organization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#102"><abbr title="App\Models\WorkOrderTask::diagnosis">diagnosis</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#110"><abbr title="App\Models\WorkOrderTask::workOrder">workOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#118"><abbr title="App\Models\WorkOrderTask::serviceCalls">serviceCalls</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#133"><abbr title="App\Models\WorkOrderTask::completedServiceCalls">completedServiceCalls</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#148"><abbr title="App\Models\WorkOrderTask::allServiceCalls">allServiceCalls</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#163"><abbr title="App\Models\WorkOrderTask::latestServiceCalls">latestServiceCalls</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#171"><abbr title="App\Models\WorkOrderTask::problemDiagnosis">problemDiagnosis</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#181"><abbr title="App\Models\WorkOrderTask::quoteTasks">quoteTasks</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#191"><abbr title="App\Models\WorkOrderTask::quotes">quotes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#201"><abbr title="App\Models\WorkOrderTask::latestQuotes">latestQuotes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTask.php.html#211"><abbr title="App\Models\WorkOrderTask::materials">materials</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskMaterial.php.html#72"><abbr title="App\Models\WorkOrderTaskMaterial::uuidColumn">uuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskMaterial.php.html#80"><abbr title="App\Models\WorkOrderTaskMaterial::user">user</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskMaterial.php.html#88"><abbr title="App\Models\WorkOrderTaskMaterial::task">task</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskMaterial.php.html#93"><abbr title="App\Models\WorkOrderTaskMaterial::unitPrice">unitPrice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User.php.html#260"><abbr title="App\Models\User::assignRole">assignRole</abbr></a></td><td class="text-right">40%</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#280"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::applyFilter">applyFilter</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="Invoice.php.html#83"><abbr title="App\Models\Invoice::booted">booted</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="User.php.html#197"><abbr title="App\Models\User::booted">booted</abbr></a></td><td class="text-right">66%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrder.php.html#424"><abbr title="App\Models\WorkOrder::resolveStateAbilities">resolveStateAbilities</abbr></a></td><td class="text-right">3540</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#251"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::applyWorkOrderHealthFilter">applyWorkOrderHealthFilter</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequest.php.html#295"><abbr title="App\Models\ServiceRequest::resolveStateAbilities">resolveStateAbilities</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#181"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::mobileAppListViewConditions">mobileAppListViewConditions</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderServiceCall.php.html#348"><abbr title="App\Models\WorkOrderServiceCall::workPerformed">workPerformed</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Property.php.html#155"><abbr title="App\Models\Property::getInlineFullAddressFormat">getInlineFullAddressFormat</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuoteTaskMaterial.php.html#95"><abbr title="App\Models\QuoteTaskMaterial::unitPrice">unitPrice</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuoteTaskMaterial.php.html#104"><abbr title="App\Models\QuoteTaskMaterial::unitPriceWithoutMarkUp">unitPriceWithoutMarkUp</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="User.php.html#369"><abbr title="App\Models\User::getInlineFullAddressFormat">getInlineFullAddressFormat</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderTaskMaterial.php.html#93"><abbr title="App\Models\WorkOrderTaskMaterial::unitPrice">unitPrice</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="User.php.html#260"><abbr title="App\Models\User::assignRole">assignRole</abbr></a></td><td class="text-right">10</td></tr>
       <tr><td><a href="Invoice.php.html#138"><abbr title="App\Models\Invoice::getTotalMarkupFeeAttribute">getTotalMarkupFeeAttribute</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Invoice.php.html#148"><abbr title="App\Models\Invoice::getTotalCostAttribute">getTotalCostAttribute</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Invoice.php.html#158"><abbr title="App\Models\Invoice::getCostAttribute">getCostAttribute</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#320"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::orderByWorkOrder">orderByWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Role.php.html#102"><abbr title="App\Models\Role::organizationRoles">organizationRoles</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="User.php.html#197"><abbr title="App\Models\User::booted">booted</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="QueryBuilder/WorkOrderListQueryBuilder.php.html#280"><abbr title="App\Models\QueryBuilder\WorkOrderListQueryBuilder::applyFilter">applyFilter</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Mon Jun 30 0:32:48 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([47,1,4,2,1,1,4,1,0,1,0,44], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([239,0,0,0,0,1,0,3,0,0,0,102], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[100,0,"<a href=\"Account.php.html#38\">App\\Models\\Account<\/a>"],[100,0,"<a href=\"AppVersion.php.html#7\">App\\Models\\AppVersion<\/a>"],[100,0,"<a href=\"AppfolioApiLog.php.html#23\">App\\Models\\AppfolioApiLog<\/a>"],[100,0,"<a href=\"AppfolioIntegrationSkippedServiceRequest.php.html#8\">App\\Models\\AppfolioIntegrationSkippedServiceRequest<\/a>"],[100,0,"<a href=\"AppfolioIntegrationSkippedWorkOrder.php.html#8\">App\\Models\\AppfolioIntegrationSkippedWorkOrder<\/a>"],[100,0,"<a href=\"AssetOwner.php.html#57\">App\\Models\\AssetOwner<\/a>"],[100,0,"<a href=\"Country.php.html#41\">App\\Models\\Country<\/a>"],[0,5,"<a href=\"DatabaseNotification.php.html#37\">App\\Models\\DatabaseNotification<\/a>"],[0,1,"<a href=\"DeveloperAlert.php.html#10\">App\\Models\\DeveloperAlert<\/a>"],[100,1,"<a href=\"Feature.php.html#38\">App\\Models\\Feature<\/a>"],[13.***************,13,"<a href=\"Invoice.php.html#44\">App\\Models\\Invoice<\/a>"],[0,6,"<a href=\"InvoiceLineItem.php.html#31\">App\\Models\\InvoiceLineItem<\/a>"],[0,5,"<a href=\"InvoiceLineItemSubsidiary.php.html#38\">App\\Models\\InvoiceLineItemSubsidiary<\/a>"],[100,0,"<a href=\"InvoiceLog.php.html#35\">App\\Models\\InvoiceLog<\/a>"],[83.33333333333334,5,"<a href=\"Issue.php.html#38\">App\\Models\\Issue<\/a>"],[100,0,"<a href=\"IssueStatus.php.html#19\">App\\Models\\IssueStatus<\/a>"],[100,0,"<a href=\"LulaAppointment.php.html#64\">App\\Models\\LulaAppointment<\/a>"],[0,1,"<a href=\"Material.php.html#34\">App\\Models\\Material<\/a>"],[0,8,"<a href=\"Media.php.html#66\">App\\Models\\Media<\/a>"],[50,15,"<a href=\"Organization.php.html#89\">App\\Models\\Organization<\/a>"],[0,1,"<a href=\"OrganizationIdentityProvider.php.html#11\">App\\Models\\OrganizationIdentityProvider<\/a>"],[100,0,"<a href=\"OrganizationVendor.php.html#13\">App\\Models\\OrganizationVendor<\/a>"],[50,2,"<a href=\"Permission.php.html#48\">App\\Models\\Permission<\/a>"],[100,0,"<a href=\"Portfolio.php.html#40\">App\\Models\\Portfolio<\/a>"],[100,1,"<a href=\"ProblemCategory.php.html#52\">App\\Models\\ProblemCategory<\/a>"],[50,2,"<a href=\"ProblemDiagnosis.php.html#53\">App\\Models\\ProblemDiagnosis<\/a>"],[100,2,"<a href=\"ProblemSubCategory.php.html#59\">App\\Models\\ProblemSubCategory<\/a>"],[37.5,8,"<a href=\"Property.php.html#88\">App\\Models\\Property<\/a>"],[100,0,"<a href=\"PropertyOwner.php.html#47\">App\\Models\\PropertyOwner<\/a>"],[100,0,"<a href=\"PropertyResident.php.html#58\">App\\Models\\PropertyResident<\/a>"],[100,0,"<a href=\"PropertyType.php.html#22\">App\\Models\\PropertyType<\/a>"],[0,1,"<a href=\"PublicApiServiceRequestWebhookEvent.php.html#31\">App\\Models\\PublicApiServiceRequestWebhookEvent<\/a>"],[0,1,"<a href=\"PublicApiWorkOrderWebhookEvent.php.html#53\">App\\Models\\PublicApiWorkOrderWebhookEvent<\/a>"],[44,40,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#19\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder<\/a>"],[0,14,"<a href=\"Quote.php.html#45\">App\\Models\\Quote<\/a>"],[100,0,"<a href=\"QuoteStatus.php.html#7\">App\\Models\\QuoteStatus<\/a>"],[0,6,"<a href=\"QuoteTask.php.html#42\">App\\Models\\QuoteTask<\/a>"],[0,9,"<a href=\"QuoteTaskMaterial.php.html#37\">App\\Models\\QuoteTaskMaterial<\/a>"],[0,2,"<a href=\"RequestLog.php.html#8\">App\\Models\\RequestLog<\/a>"],[0,4,"<a href=\"Resident.php.html#64\">App\\Models\\Resident<\/a>"],[0,2,"<a href=\"ResidentAvailability.php.html#35\">App\\Models\\ResidentAvailability<\/a>"],[100,0,"<a href=\"ResidentStatus.php.html#23\">App\\Models\\ResidentStatus<\/a>"],[15.384615384615385,6,"<a href=\"Role.php.html#63\">App\\Models\\Role<\/a>"],[100,0,"<a href=\"ServiceCategory.php.html#25\">App\\Models\\ServiceCategory<\/a>"],[29.545454545454547,25,"<a href=\"ServiceRequest.php.html#81\">App\\Models\\ServiceRequest<\/a>"],[0,4,"<a href=\"ServiceRequestActivityLog.php.html#60\">App\\Models\\ServiceRequestActivityLog<\/a>"],[0,2,"<a href=\"ServiceRequestAssignee.php.html#29\">App\\Models\\ServiceRequestAssignee<\/a>"],[0,3,"<a href=\"ServiceRequestCategory.php.html#12\">App\\Models\\ServiceRequestCategory<\/a>"],[0,2,"<a href=\"ServiceRequestDescription.php.html#32\">App\\Models\\ServiceRequestDescription<\/a>"],[0,2,"<a href=\"ServiceRequestMedia.php.html#37\">App\\Models\\ServiceRequestMedia<\/a>"],[0,4,"<a href=\"ServiceRequestNote.php.html#60\">App\\Models\\ServiceRequestNote<\/a>"],[100,0,"<a href=\"ServiceRequestNoteLog.php.html#51\">App\\Models\\ServiceRequestNoteLog<\/a>"],[100,0,"<a href=\"ServiceRequestSource.php.html#10\">App\\Models\\ServiceRequestSource<\/a>"],[0,1,"<a href=\"ServiceRequestStatus.php.html#20\">App\\Models\\ServiceRequestStatus<\/a>"],[0,3,"<a href=\"ServiceRequestStatusLog.php.html#9\">App\\Models\\ServiceRequestStatusLog<\/a>"],[100,0,"<a href=\"ServiceRequestType.php.html#10\">App\\Models\\ServiceRequestType<\/a>"],[100,0,"<a href=\"State.php.html#37\">App\\Models\\State<\/a>"],[50,2,"<a href=\"Tag.php.html#52\">App\\Models\\Tag<\/a>"],[0,6,"<a href=\"Technician.php.html#66\">App\\Models\\Technician<\/a>"],[0,6,"<a href=\"TechnicianAppointment.php.html#105\">App\\Models\\TechnicianAppointment<\/a>"],[0,2,"<a href=\"TechnicianAppointmentLog.php.html#41\">App\\Models\\TechnicianAppointmentLog<\/a>"],[0,3,"<a href=\"TechnicianSkill.php.html#53\">App\\Models\\TechnicianSkill<\/a>"],[0,1,"<a href=\"TechnicianWorkingHour.php.html#53\">App\\Models\\TechnicianWorkingHour<\/a>"],[0,1,"<a href=\"Timezone.php.html#23\">App\\Models\\Timezone<\/a>"],[28.767123287671232,33,"<a href=\"User.php.html#133\">App\\Models\\User<\/a>"],[100,0,"<a href=\"UserDefaultView.php.html#23\">App\\Models\\UserDefaultView<\/a>"],[100,0,"<a href=\"UserPinnedView.php.html#23\">App\\Models\\UserPinnedView<\/a>"],[100,0,"<a href=\"UserWorkOrderBookmark.php.html#23\">App\\Models\\UserWorkOrderBookmark<\/a>"],[100,7,"<a href=\"Vendor.php.html#85\">App\\Models\\Vendor<\/a>"],[0,1,"<a href=\"VendorAllocation.php.html#27\">App\\Models\\VendorAllocation<\/a>"],[100,2,"<a href=\"VendorAppointment.php.html#52\">App\\Models\\VendorAppointment<\/a>"],[100,0,"<a href=\"VendorExternalInvoice.php.html#29\">App\\Models\\VendorExternalInvoice<\/a>"],[100,5,"<a href=\"VendorOnboarding.php.html#38\">App\\Models\\VendorOnboarding<\/a>"],[100,1,"<a href=\"VendorOnboardingStatus.php.html#19\">App\\Models\\VendorOnboardingStatus<\/a>"],[0,2,"<a href=\"VendorPublicApiLog.php.html#11\">App\\Models\\VendorPublicApiLog<\/a>"],[100,5,"<a href=\"VendorService.php.html#38\">App\\Models\\VendorService<\/a>"],[100,1,"<a href=\"VendorServiceArea.php.html#13\">App\\Models\\VendorServiceArea<\/a>"],[100,2,"<a href=\"VendorUser.php.html#32\">App\\Models\\VendorUser<\/a>"],[0,4,"<a href=\"View.php.html#62\">App\\Models\\View<\/a>"],[100,0,"<a href=\"ViewType.php.html#23\">App\\Models\\ViewType<\/a>"],[0,2,"<a href=\"WebhookCall.php.html#14\">App\\Models\\WebhookCall<\/a>"],[15.18987341772152,84,"<a href=\"WorkOrder.php.html#152\">App\\Models\\WorkOrder<\/a>"],[0,2,"<a href=\"WorkOrderActivityLog.php.html#58\">App\\Models\\WorkOrderActivityLog<\/a>"],[0,2,"<a href=\"WorkOrderAssignee.php.html#27\">App\\Models\\WorkOrderAssignee<\/a>"],[0,2,"<a href=\"WorkOrderHealthLog.php.html#24\">App\\Models\\WorkOrderHealthLog<\/a>"],[62.5,4,"<a href=\"WorkOrderHealthTracker.php.html#25\">App\\Models\\WorkOrderHealthTracker<\/a>"],[0,2,"<a href=\"WorkOrderHealthViolation.php.html#25\">App\\Models\\WorkOrderHealthViolation<\/a>"],[18.181818181818183,5,"<a href=\"WorkOrderIssue.php.html#40\">App\\Models\\WorkOrderIssue<\/a>"],[100,0,"<a href=\"WorkOrderIssueMaterial.php.html#8\">App\\Models\\WorkOrderIssueMaterial<\/a>"],[0,1,"<a href=\"WorkOrderIssueStatus.php.html#8\">App\\Models\\WorkOrderIssueStatus<\/a>"],[100,0,"<a href=\"WorkOrderIssueStatuses.php.html#7\">App\\Models\\WorkOrderIssueStatuses<\/a>"],[100,0,"<a href=\"WorkOrderLog.php.html#29\">App\\Models\\WorkOrderLog<\/a>"],[0,3,"<a href=\"WorkOrderMedia.php.html#62\">App\\Models\\WorkOrderMedia<\/a>"],[0,4,"<a href=\"WorkOrderNote.php.html#63\">App\\Models\\WorkOrderNote<\/a>"],[100,0,"<a href=\"WorkOrderNoteLog.php.html#48\">App\\Models\\WorkOrderNoteLog<\/a>"],[9.75609756097561,23,"<a href=\"WorkOrderServiceCall.php.html#119\">App\\Models\\WorkOrderServiceCall<\/a>"],[0,1,"<a href=\"WorkOrderServiceCallIssue.php.html#19\">App\\Models\\WorkOrderServiceCallIssue<\/a>"],[0,1,"<a href=\"WorkOrderServiceCallLog.php.html#49\">App\\Models\\WorkOrderServiceCallLog<\/a>"],[0,2,"<a href=\"WorkOrderServiceCallTask.php.html#41\">App\\Models\\WorkOrderServiceCallTask<\/a>"],[100,0,"<a href=\"WorkOrderSource.php.html#36\">App\\Models\\WorkOrderSource<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#30\">App\\Models\\WorkOrderStatus<\/a>"],[0,2,"<a href=\"WorkOrderStatusLog.php.html#34\">App\\Models\\WorkOrderStatusLog<\/a>"],[100,0,"<a href=\"WorkOrderTags.php.html#7\">App\\Models\\WorkOrderTags<\/a>"],[0,13,"<a href=\"WorkOrderTask.php.html#65\">App\\Models\\WorkOrderTask<\/a>"],[100,0,"<a href=\"WorkOrderTaskLog.php.html#38\">App\\Models\\WorkOrderTaskLog<\/a>"],[0,6,"<a href=\"WorkOrderTaskMaterial.php.html#35\">App\\Models\\WorkOrderTaskMaterial<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"DatabaseNotification.php.html#87\">App\\Models\\DatabaseNotification::uuidColumn<\/a>"],[0,1,"<a href=\"DatabaseNotification.php.html#98\">App\\Models\\DatabaseNotification::scopeCleared<\/a>"],[0,1,"<a href=\"DatabaseNotification.php.html#106\">App\\Models\\DatabaseNotification::workOrder<\/a>"],[0,1,"<a href=\"DatabaseNotification.php.html#114\">App\\Models\\DatabaseNotification::serviceRequest<\/a>"],[0,1,"<a href=\"DatabaseNotification.php.html#122\">App\\Models\\DatabaseNotification::user<\/a>"],[0,1,"<a href=\"DeveloperAlert.php.html#38\">App\\Models\\DeveloperAlert::organization<\/a>"],[100,1,"<a href=\"Feature.php.html#74\">App\\Models\\Feature::permissions<\/a>"],[66.66666666666666,1,"<a href=\"Invoice.php.html#83\">App\\Models\\Invoice::booted<\/a>"],[0,1,"<a href=\"Invoice.php.html#90\">App\\Models\\Invoice::uuidColumn<\/a>"],[0,1,"<a href=\"Invoice.php.html#98\">App\\Models\\Invoice::organization<\/a>"],[0,1,"<a href=\"Invoice.php.html#106\">App\\Models\\Invoice::workOrder<\/a>"],[0,1,"<a href=\"Invoice.php.html#114\">App\\Models\\Invoice::lineItems<\/a>"],[0,1,"<a href=\"Invoice.php.html#122\">App\\Models\\Invoice::createdByUser<\/a>"],[0,1,"<a href=\"Invoice.php.html#130\">App\\Models\\Invoice::draftedByUser<\/a>"],[0,2,"<a href=\"Invoice.php.html#138\">App\\Models\\Invoice::getTotalMarkupFeeAttribute<\/a>"],[0,2,"<a href=\"Invoice.php.html#148\">App\\Models\\Invoice::getTotalCostAttribute<\/a>"],[0,2,"<a href=\"Invoice.php.html#158\">App\\Models\\Invoice::getCostAttribute<\/a>"],[0,1,"<a href=\"InvoiceLineItem.php.html#62\">App\\Models\\InvoiceLineItem::uuidColumn<\/a>"],[0,1,"<a href=\"InvoiceLineItem.php.html#70\">App\\Models\\InvoiceLineItem::invoice<\/a>"],[0,1,"<a href=\"InvoiceLineItem.php.html#78\">App\\Models\\InvoiceLineItem::serviceCall<\/a>"],[0,1,"<a href=\"InvoiceLineItem.php.html#86\">App\\Models\\InvoiceLineItem::quoteTask<\/a>"],[0,1,"<a href=\"InvoiceLineItem.php.html#94\">App\\Models\\InvoiceLineItem::quote<\/a>"],[0,1,"<a href=\"InvoiceLineItem.php.html#102\">App\\Models\\InvoiceLineItem::subsidiaries<\/a>"],[0,1,"<a href=\"InvoiceLineItemSubsidiary.php.html#78\">App\\Models\\InvoiceLineItemSubsidiary::uuidColumn<\/a>"],[0,1,"<a href=\"InvoiceLineItemSubsidiary.php.html#86\">App\\Models\\InvoiceLineItemSubsidiary::invoiceLineItem<\/a>"],[0,1,"<a href=\"InvoiceLineItemSubsidiary.php.html#94\">App\\Models\\InvoiceLineItemSubsidiary::workOrderTaskMaterial<\/a>"],[0,1,"<a href=\"InvoiceLineItemSubsidiary.php.html#102\">App\\Models\\InvoiceLineItemSubsidiary::quoteTaskMaterial<\/a>"],[0,1,"<a href=\"InvoiceLineItemSubsidiary.php.html#110\">App\\Models\\InvoiceLineItemSubsidiary::quoteTask<\/a>"],[100,1,"<a href=\"Issue.php.html#92\">App\\Models\\Issue::serviceRequest<\/a>"],[100,1,"<a href=\"Issue.php.html#102\">App\\Models\\Issue::problemDiagnosis<\/a>"],[100,1,"<a href=\"Issue.php.html#112\">App\\Models\\Issue::workOrderIssues<\/a>"],[100,1,"<a href=\"Issue.php.html#122\">App\\Models\\Issue::workOrders<\/a>"],[0,1,"<a href=\"Issue.php.html#133\">App\\Models\\Issue::user<\/a>"],[0,1,"<a href=\"Material.php.html#77\">App\\Models\\Material::workOrderIssues<\/a>"],[0,1,"<a href=\"Media.php.html#105\">App\\Models\\Media::workOrderMedia<\/a>"],[0,1,"<a href=\"Media.php.html#113\">App\\Models\\Media::workOrder<\/a>"],[0,1,"<a href=\"Media.php.html#129\">App\\Models\\Media::serviceRequestMedia<\/a>"],[0,1,"<a href=\"Media.php.html#137\">App\\Models\\Media::serviceRequest<\/a>"],[0,1,"<a href=\"Media.php.html#149\">App\\Models\\Media::getBasePath<\/a>"],[0,1,"<a href=\"Media.php.html#154\">App\\Models\\Media::getServiceRequestBasePath<\/a>"],[0,1,"<a href=\"Media.php.html#162\">App\\Models\\Media::getTemporaryMediaUrl<\/a>"],[0,1,"<a href=\"Media.php.html#189\">App\\Models\\Media::getServiceRequestTemporaryMediaUrl<\/a>"],[0,1,"<a href=\"Organization.php.html#157\">App\\Models\\Organization::accounts<\/a>"],[0,1,"<a href=\"Organization.php.html#167\">App\\Models\\Organization::users<\/a>"],[0,1,"<a href=\"Organization.php.html#175\">App\\Models\\Organization::roles<\/a>"],[100,1,"<a href=\"Organization.php.html#185\">App\\Models\\Organization::features<\/a>"],[0,1,"<a href=\"Organization.php.html#193\">App\\Models\\Organization::identityProviders<\/a>"],[100,2,"<a href=\"Organization.php.html#201\">App\\Models\\Organization::getCachedFeatures<\/a>"],[100,1,"<a href=\"Organization.php.html#216\">App\\Models\\Organization::getCachedPermissions<\/a>"],[100,1,"<a href=\"Organization.php.html#227\">App\\Models\\Organization::getCachedRoles<\/a>"],[0,1,"<a href=\"Organization.php.html#234\">App\\Models\\Organization::getMediaPathPrefix<\/a>"],[100,1,"<a href=\"Organization.php.html#242\">App\\Models\\Organization::state<\/a>"],[100,1,"<a href=\"Organization.php.html#250\">App\\Models\\Organization::country<\/a>"],[0,1,"<a href=\"Organization.php.html#255\">App\\Models\\Organization::logoUrl<\/a>"],[0,1,"<a href=\"Organization.php.html#271\">App\\Models\\Organization::vendorSettings<\/a>"],[0,1,"<a href=\"Organization.php.html#279\">App\\Models\\Organization::vendors<\/a>"],[0,1,"<a href=\"OrganizationIdentityProvider.php.html#47\">App\\Models\\OrganizationIdentityProvider::organization<\/a>"],[0,1,"<a href=\"Permission.php.html#87\">App\\Models\\Permission::role<\/a>"],[100,1,"<a href=\"Permission.php.html#95\">App\\Models\\Permission::roles<\/a>"],[100,1,"<a href=\"ProblemCategory.php.html#85\">App\\Models\\ProblemCategory::problemSubCategories<\/a>"],[100,1,"<a href=\"ProblemDiagnosis.php.html#87\">App\\Models\\ProblemDiagnosis::subCategory<\/a>"],[0,1,"<a href=\"ProblemDiagnosis.php.html#97\">App\\Models\\ProblemDiagnosis::issues<\/a>"],[100,1,"<a href=\"ProblemSubCategory.php.html#92\">App\\Models\\ProblemSubCategory::problemCategory<\/a>"],[100,1,"<a href=\"ProblemSubCategory.php.html#102\">App\\Models\\ProblemSubCategory::problemDiagnoses<\/a>"],[100,1,"<a href=\"Property.php.html#142\">App\\Models\\Property::state<\/a>"],[0,1,"<a href=\"Property.php.html#150\">App\\Models\\Property::country<\/a>"],[0,3,"<a href=\"Property.php.html#155\">App\\Models\\Property::getInlineFullAddressFormat<\/a>"],[100,1,"<a href=\"Property.php.html#183\">App\\Models\\Property::getAddress<\/a>"],[0,1,"<a href=\"Property.php.html#197\">App\\Models\\Property::timezone<\/a>"],[100,1,"<a href=\"Property.php.html#205\">App\\Models\\Property::residents<\/a>"],[0,1,"<a href=\"PublicApiServiceRequestWebhookEvent.php.html#67\">App\\Models\\PublicApiServiceRequestWebhookEvent::boot<\/a>"],[0,1,"<a href=\"PublicApiWorkOrderWebhookEvent.php.html#89\">App\\Models\\PublicApiWorkOrderWebhookEvent::boot<\/a>"],[100,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#25\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::__construct<\/a>"],[100,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#30\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::make<\/a>"],[100,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#35\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::withRelations<\/a>"],[100,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#42\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::joinServiceCategoryRelations<\/a>"],[100,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#61\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::joinServiceRequestAddressAndResidentRelations<\/a>"],[100,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#79\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::joinStatusRelations<\/a>"],[100,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#86\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::joinWorkOrderHealthRelations<\/a>"],[100,3,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#97\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::joinWorkOrderAssigneeRelations<\/a>"],[100,3,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#113\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::joinWorkOrderTagRelations<\/a>"],[100,3,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#129\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::joinTripRelations<\/a>"],[0,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#155\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::joinTechnicianAppointmentRelations<\/a>"],[0,4,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#181\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::mobileAppListViewConditions<\/a>"],[0,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#246\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::applySort<\/a>"],[0,7,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#251\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::applyWorkOrderHealthFilter<\/a>"],[60,2,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#280\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::applyFilter<\/a>"],[0,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#292\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::applySearch<\/a>"],[100,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#306\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::selectFields<\/a>"],[100,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#313\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::groupByWorkOrder<\/a>"],[0,2,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#320\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::orderByWorkOrder<\/a>"],[100,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#331\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::getPaginatedData<\/a>"],[0,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#339\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::getWorkOrders<\/a>"],[0,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#344\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::getCount<\/a>"],[100,1,"<a href=\"QueryBuilder\/WorkOrderListQueryBuilder.php.html#349\">App\\Models\\QueryBuilder\\WorkOrderListQueryBuilder::pluckFields<\/a>"],[0,1,"<a href=\"Quote.php.html#101\">App\\Models\\Quote::booted<\/a>"],[0,1,"<a href=\"Quote.php.html#110\">App\\Models\\Quote::uuidColumn<\/a>"],[0,1,"<a href=\"Quote.php.html#120\">App\\Models\\Quote::quoteTasks<\/a>"],[0,1,"<a href=\"Quote.php.html#128\">App\\Models\\Quote::submittedUser<\/a>"],[0,1,"<a href=\"Quote.php.html#136\">App\\Models\\Quote::approvedUser<\/a>"],[0,1,"<a href=\"Quote.php.html#144\">App\\Models\\Quote::rejectedUser<\/a>"],[0,1,"<a href=\"Quote.php.html#152\">App\\Models\\Quote::reviewedUser<\/a>"],[0,1,"<a href=\"Quote.php.html#160\">App\\Models\\Quote::lastModifiedUser<\/a>"],[0,1,"<a href=\"Quote.php.html#170\">App\\Models\\Quote::serviceCalls<\/a>"],[0,1,"<a href=\"Quote.php.html#178\">App\\Models\\Quote::serviceCall<\/a>"],[0,1,"<a href=\"Quote.php.html#186\">App\\Models\\Quote::workOrder<\/a>"],[0,1,"<a href=\"Quote.php.html#194\">App\\Models\\Quote::workOrderTask<\/a>"],[0,1,"<a href=\"Quote.php.html#204\">App\\Models\\Quote::assignedServiceCalls<\/a>"],[0,1,"<a href=\"Quote.php.html#212\">App\\Models\\Quote::latestAssignedServiceCall<\/a>"],[0,1,"<a href=\"QuoteTask.php.html#85\">App\\Models\\QuoteTask::uuidColumn<\/a>"],[0,1,"<a href=\"QuoteTask.php.html#93\">App\\Models\\QuoteTask::user<\/a>"],[0,1,"<a href=\"QuoteTask.php.html#101\">App\\Models\\QuoteTask::task<\/a>"],[0,1,"<a href=\"QuoteTask.php.html#109\">App\\Models\\QuoteTask::quote<\/a>"],[0,1,"<a href=\"QuoteTask.php.html#119\">App\\Models\\QuoteTask::quoteTaskMaterials<\/a>"],[0,1,"<a href=\"QuoteTask.php.html#129\">App\\Models\\QuoteTask::media<\/a>"],[0,1,"<a href=\"QuoteTaskMaterial.php.html#74\">App\\Models\\QuoteTaskMaterial::uuidColumn<\/a>"],[0,1,"<a href=\"QuoteTaskMaterial.php.html#82\">App\\Models\\QuoteTaskMaterial::user<\/a>"],[0,1,"<a href=\"QuoteTaskMaterial.php.html#90\">App\\Models\\QuoteTaskMaterial::quoteTask<\/a>"],[0,3,"<a href=\"QuoteTaskMaterial.php.html#95\">App\\Models\\QuoteTaskMaterial::unitPrice<\/a>"],[0,3,"<a href=\"QuoteTaskMaterial.php.html#104\">App\\Models\\QuoteTaskMaterial::unitPriceWithoutMarkUp<\/a>"],[0,1,"<a href=\"RequestLog.php.html#29\">App\\Models\\RequestLog::user<\/a>"],[0,1,"<a href=\"RequestLog.php.html#37\">App\\Models\\RequestLog::organization<\/a>"],[0,1,"<a href=\"Resident.php.html#102\">App\\Models\\Resident::getNameAttribute<\/a>"],[0,1,"<a href=\"Resident.php.html#107\">App\\Models\\Resident::getName<\/a>"],[0,1,"<a href=\"Resident.php.html#115\">App\\Models\\Resident::property<\/a>"],[0,1,"<a href=\"Resident.php.html#120\">App\\Models\\Resident::routeNotificationForTwilio<\/a>"],[0,1,"<a href=\"ResidentAvailability.php.html#79\">App\\Models\\ResidentAvailability::workOrder<\/a>"],[0,1,"<a href=\"ResidentAvailability.php.html#87\">App\\Models\\ResidentAvailability::serviceRequest<\/a>"],[0,2,"<a href=\"Role.php.html#102\">App\\Models\\Role::organizationRoles<\/a>"],[100,1,"<a href=\"Role.php.html#114\">App\\Models\\Role::permissions<\/a>"],[0,1,"<a href=\"Role.php.html#123\">App\\Models\\Role::syncPermissions<\/a>"],[0,1,"<a href=\"Role.php.html#135\">App\\Models\\Role::clearOrganizationRoleCache<\/a>"],[100,1,"<a href=\"Role.php.html#143\">App\\Models\\Role::users<\/a>"],[100,1,"<a href=\"ServiceRequest.php.html#146\">App\\Models\\ServiceRequest::booted<\/a>"],[100,1,"<a href=\"ServiceRequest.php.html#154\">App\\Models\\ServiceRequest::uuidColumn<\/a>"],[100,1,"<a href=\"ServiceRequest.php.html#162\">App\\Models\\ServiceRequest::organization<\/a>"],[100,1,"<a href=\"ServiceRequest.php.html#170\">App\\Models\\ServiceRequest::property<\/a>"],[100,1,"<a href=\"ServiceRequest.php.html#178\">App\\Models\\ServiceRequest::resident<\/a>"],[100,1,"<a href=\"ServiceRequest.php.html#186\">App\\Models\\ServiceRequest::status<\/a>"],[100,1,"<a href=\"ServiceRequest.php.html#194\">App\\Models\\ServiceRequest::timezone<\/a>"],[100,1,"<a href=\"ServiceRequest.php.html#202\">App\\Models\\ServiceRequest::type<\/a>"],[0,1,"<a href=\"ServiceRequest.php.html#210\">App\\Models\\ServiceRequest::source<\/a>"],[0,1,"<a href=\"ServiceRequest.php.html#218\">App\\Models\\ServiceRequest::updatedByUser<\/a>"],[0,1,"<a href=\"ServiceRequest.php.html#226\">App\\Models\\ServiceRequest::categories<\/a>"],[0,1,"<a href=\"ServiceRequest.php.html#234\">App\\Models\\ServiceRequest::assignees<\/a>"],[0,1,"<a href=\"ServiceRequest.php.html#242\">App\\Models\\ServiceRequest::media<\/a>"],[100,1,"<a href=\"ServiceRequest.php.html#253\">App\\Models\\ServiceRequest::residentAvailabilities<\/a>"],[100,1,"<a href=\"ServiceRequest.php.html#261\">App\\Models\\ServiceRequest::workOrders<\/a>"],[0,1,"<a href=\"ServiceRequest.php.html#269\">App\\Models\\ServiceRequest::serviceRequestDescriptions<\/a>"],[0,1,"<a href=\"ServiceRequest.php.html#277\">App\\Models\\ServiceRequest::latestDescription<\/a>"],[0,1,"<a href=\"ServiceRequest.php.html#287\">App\\Models\\ServiceRequest::issues<\/a>"],[0,7,"<a href=\"ServiceRequest.php.html#295\">App\\Models\\ServiceRequest::resolveStateAbilities<\/a>"],[0,1,"<a href=\"ServiceRequestActivityLog.php.html#102\">App\\Models\\ServiceRequestActivityLog::serviceRequest<\/a>"],[0,1,"<a href=\"ServiceRequestActivityLog.php.html#110\">App\\Models\\ServiceRequestActivityLog::workOrder<\/a>"],[0,1,"<a href=\"ServiceRequestActivityLog.php.html#118\">App\\Models\\ServiceRequestActivityLog::workOrderTask<\/a>"],[0,1,"<a href=\"ServiceRequestActivityLog.php.html#126\">App\\Models\\ServiceRequestActivityLog::triggeredBy<\/a>"],[0,1,"<a href=\"ServiceRequestAssignee.php.html#62\">App\\Models\\ServiceRequestAssignee::serviceRequest<\/a>"],[0,1,"<a href=\"ServiceRequestAssignee.php.html#70\">App\\Models\\ServiceRequestAssignee::user<\/a>"],[0,1,"<a href=\"ServiceRequestCategory.php.html#49\">App\\Models\\ServiceRequestCategory::serviceRequest<\/a>"],[0,1,"<a href=\"ServiceRequestCategory.php.html#57\">App\\Models\\ServiceRequestCategory::problemCategory<\/a>"],[0,1,"<a href=\"ServiceRequestCategory.php.html#65\">App\\Models\\ServiceRequestCategory::problemSubCategory<\/a>"],[0,1,"<a href=\"ServiceRequestDescription.php.html#75\">App\\Models\\ServiceRequestDescription::createdUser<\/a>"],[0,1,"<a href=\"ServiceRequestDescription.php.html#85\">App\\Models\\ServiceRequestDescription::serviceRequest<\/a>"],[0,1,"<a href=\"ServiceRequestMedia.php.html#81\">App\\Models\\ServiceRequestMedia::media<\/a>"],[0,1,"<a href=\"ServiceRequestMedia.php.html#89\">App\\Models\\ServiceRequestMedia::serviceRequest<\/a>"],[0,1,"<a href=\"ServiceRequestNote.php.html#99\">App\\Models\\ServiceRequestNote::serviceRequest<\/a>"],[0,1,"<a href=\"ServiceRequestNote.php.html#107\">App\\Models\\ServiceRequestNote::user<\/a>"],[0,1,"<a href=\"ServiceRequestNote.php.html#115\">App\\Models\\ServiceRequestNote::vendor<\/a>"],[0,1,"<a href=\"ServiceRequestNote.php.html#123\">App\\Models\\ServiceRequestNote::lastModifiedUser<\/a>"],[0,1,"<a href=\"ServiceRequestStatus.php.html#47\">App\\Models\\ServiceRequestStatus::serviceRequests<\/a>"],[0,1,"<a href=\"ServiceRequestStatusLog.php.html#37\">App\\Models\\ServiceRequestStatusLog::serviceRequest<\/a>"],[0,1,"<a href=\"ServiceRequestStatusLog.php.html#45\">App\\Models\\ServiceRequestStatusLog::updatedBy<\/a>"],[0,1,"<a href=\"ServiceRequestStatusLog.php.html#53\">App\\Models\\ServiceRequestStatusLog::serviceRequestStatus<\/a>"],[0,1,"<a href=\"Tag.php.html#100\">App\\Models\\Tag::uuidColumn<\/a>"],[100,1,"<a href=\"Tag.php.html#108\">App\\Models\\Tag::workOrders<\/a>"],[0,1,"<a href=\"Technician.php.html#103\">App\\Models\\Technician::uuidColumn<\/a>"],[0,1,"<a href=\"Technician.php.html#111\">App\\Models\\Technician::user<\/a>"],[0,1,"<a href=\"Technician.php.html#121\">App\\Models\\Technician::workingHours<\/a>"],[0,1,"<a href=\"Technician.php.html#131\">App\\Models\\Technician::skills<\/a>"],[0,1,"<a href=\"Technician.php.html#141\">App\\Models\\Technician::appointments<\/a>"],[0,1,"<a href=\"Technician.php.html#149\">App\\Models\\Technician::problemDiagnoses<\/a>"],[0,1,"<a href=\"TechnicianAppointment.php.html#175\">App\\Models\\TechnicianAppointment::uuidColumn<\/a>"],[0,1,"<a href=\"TechnicianAppointment.php.html#183\">App\\Models\\TechnicianAppointment::technician<\/a>"],[0,1,"<a href=\"TechnicianAppointment.php.html#191\">App\\Models\\TechnicianAppointment::serviceCall<\/a>"],[0,1,"<a href=\"TechnicianAppointment.php.html#199\">App\\Models\\TechnicianAppointment::workOrder<\/a>"],[0,1,"<a href=\"TechnicianAppointment.php.html#207\">App\\Models\\TechnicianAppointment::property<\/a>"],[0,1,"<a href=\"TechnicianAppointment.php.html#217\">App\\Models\\TechnicianAppointment::appointmentLogs<\/a>"],[0,1,"<a href=\"TechnicianAppointmentLog.php.html#83\">App\\Models\\TechnicianAppointmentLog::uuidColumn<\/a>"],[0,1,"<a href=\"TechnicianAppointmentLog.php.html#91\">App\\Models\\TechnicianAppointmentLog::technicianAppointment<\/a>"],[0,1,"<a href=\"TechnicianSkill.php.html#86\">App\\Models\\TechnicianSkill::problemCategory<\/a>"],[0,1,"<a href=\"TechnicianSkill.php.html#94\">App\\Models\\TechnicianSkill::problemSubCategory<\/a>"],[0,1,"<a href=\"TechnicianSkill.php.html#102\">App\\Models\\TechnicianSkill::problemDiagnosis<\/a>"],[0,1,"<a href=\"TechnicianWorkingHour.php.html#99\">App\\Models\\TechnicianWorkingHour::technician<\/a>"],[0,1,"<a href=\"Timezone.php.html#55\">App\\Models\\Timezone::scopeDefaultTimzone<\/a>"],[66.66666666666666,3,"<a href=\"User.php.html#197\">App\\Models\\User::booted<\/a>"],[0,1,"<a href=\"User.php.html#214\">App\\Models\\User::getName<\/a>"],[0,1,"<a href=\"User.php.html#224\">App\\Models\\User::portfolios<\/a>"],[100,1,"<a href=\"User.php.html#232\">App\\Models\\User::roles<\/a>"],[100,1,"<a href=\"User.php.html#240\">App\\Models\\User::getCachedPermissions<\/a>"],[0,1,"<a href=\"User.php.html#250\">App\\Models\\User::getCachedRoles<\/a>"],[40,5,"<a href=\"User.php.html#260\">App\\Models\\User::assignRole<\/a>"],[0,1,"<a href=\"User.php.html#280\">App\\Models\\User::syncRoles<\/a>"],[0,1,"<a href=\"User.php.html#292\">App\\Models\\User::permissions<\/a>"],[0,1,"<a href=\"User.php.html#297\">App\\Models\\User::hasRole<\/a>"],[100,1,"<a href=\"User.php.html#308\">App\\Models\\User::clearPermissionCache<\/a>"],[100,1,"<a href=\"User.php.html#318\">App\\Models\\User::state<\/a>"],[100,1,"<a href=\"User.php.html#326\">App\\Models\\User::country<\/a>"],[0,1,"<a href=\"User.php.html#336\">App\\Models\\User::vendor<\/a>"],[0,1,"<a href=\"User.php.html#346\">App\\Models\\User::lastPasswordUpdatedUser<\/a>"],[100,1,"<a href=\"User.php.html#356\">App\\Models\\User::technician<\/a>"],[100,1,"<a href=\"User.php.html#364\">App\\Models\\User::timezone<\/a>"],[0,3,"<a href=\"User.php.html#369\">App\\Models\\User::getInlineFullAddressFormat<\/a>"],[0,1,"<a href=\"User.php.html#396\">App\\Models\\User::getAddress<\/a>"],[0,1,"<a href=\"User.php.html#410\">App\\Models\\User::scopeActive<\/a>"],[0,1,"<a href=\"User.php.html#415\">App\\Models\\User::routeNotificationForTwilio<\/a>"],[0,1,"<a href=\"User.php.html#423\">App\\Models\\User::receivesBroadcastNotificationsOn<\/a>"],[0,1,"<a href=\"User.php.html#433\">App\\Models\\User::notifications<\/a>"],[0,1,"<a href=\"User.php.html#443\">App\\Models\\User::clearedNotifications<\/a>"],[0,1,"<a href=\"User.php.html#453\">App\\Models\\User::unClearedUnreadNotifications<\/a>"],[100,1,"<a href=\"Vendor.php.html#143\">App\\Models\\Vendor::vendorServices<\/a>"],[100,1,"<a href=\"Vendor.php.html#153\">App\\Models\\Vendor::vendorServiceArea<\/a>"],[100,1,"<a href=\"Vendor.php.html#163\">App\\Models\\Vendor::vendorUsers<\/a>"],[100,1,"<a href=\"Vendor.php.html#173\">App\\Models\\Vendor::vendorOnboardings<\/a>"],[100,1,"<a href=\"Vendor.php.html#181\">App\\Models\\Vendor::getAddress<\/a>"],[100,1,"<a href=\"Vendor.php.html#199\">App\\Models\\Vendor::getName<\/a>"],[100,1,"<a href=\"Vendor.php.html#207\">App\\Models\\Vendor::organizations<\/a>"],[0,1,"<a href=\"VendorAllocation.php.html#59\">App\\Models\\VendorAllocation::vendor<\/a>"],[100,1,"<a href=\"VendorAppointment.php.html#92\">App\\Models\\VendorAppointment::vendorAllocations<\/a>"],[100,1,"<a href=\"VendorAppointment.php.html#100\">App\\Models\\VendorAppointment::vendor<\/a>"],[100,1,"<a href=\"VendorOnboarding.php.html#73\">App\\Models\\VendorOnboarding::uuidColumn<\/a>"],[100,1,"<a href=\"VendorOnboarding.php.html#81\">App\\Models\\VendorOnboarding::status<\/a>"],[100,1,"<a href=\"VendorOnboarding.php.html#89\">App\\Models\\VendorOnboarding::vendor<\/a>"],[100,1,"<a href=\"VendorOnboarding.php.html#97\">App\\Models\\VendorOnboarding::user<\/a>"],[100,1,"<a href=\"VendorOnboarding.php.html#105\">App\\Models\\VendorOnboarding::organization<\/a>"],[100,1,"<a href=\"VendorOnboardingStatus.php.html#50\">App\\Models\\VendorOnboardingStatus::vendorOnboardings<\/a>"],[0,1,"<a href=\"VendorPublicApiLog.php.html#38\">App\\Models\\VendorPublicApiLog::workOrder<\/a>"],[0,1,"<a href=\"VendorPublicApiLog.php.html#46\">App\\Models\\VendorPublicApiLog::vendor<\/a>"],[100,1,"<a href=\"VendorService.php.html#75\">App\\Models\\VendorService::vendor<\/a>"],[100,1,"<a href=\"VendorService.php.html#83\">App\\Models\\VendorService::problemCategory<\/a>"],[100,1,"<a href=\"VendorService.php.html#91\">App\\Models\\VendorService::problemSubCategory<\/a>"],[100,1,"<a href=\"VendorService.php.html#99\">App\\Models\\VendorService::problemDiagnosis<\/a>"],[100,1,"<a href=\"VendorService.php.html#107\">App\\Models\\VendorService::organization<\/a>"],[100,1,"<a href=\"VendorServiceArea.php.html#54\">App\\Models\\VendorServiceArea::vendor<\/a>"],[100,1,"<a href=\"VendorUser.php.html#66\">App\\Models\\VendorUser::vendor<\/a>"],[100,1,"<a href=\"VendorUser.php.html#74\">App\\Models\\VendorUser::user<\/a>"],[0,1,"<a href=\"View.php.html#106\">App\\Models\\View::userPinnedViews<\/a>"],[0,1,"<a href=\"View.php.html#116\">App\\Models\\View::userDefaultView<\/a>"],[0,1,"<a href=\"View.php.html#126\">App\\Models\\View::userDefaultViews<\/a>"],[0,1,"<a href=\"View.php.html#136\">App\\Models\\View::viewType<\/a>"],[0,1,"<a href=\"WebhookCall.php.html#39\">App\\Models\\WebhookCall::storeWebhook<\/a>"],[0,1,"<a href=\"WebhookCall.php.html#56\">App\\Models\\WebhookCall::organization<\/a>"],[100,2,"<a href=\"WorkOrder.php.html#226\">App\\Models\\WorkOrder::booted<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#236\">App\\Models\\WorkOrder::uuidColumn<\/a>"],[0,1,"<a href=\"WorkOrder.php.html#244\">App\\Models\\WorkOrder::organization<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#252\">App\\Models\\WorkOrder::status<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#260\">App\\Models\\WorkOrder::property<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#268\">App\\Models\\WorkOrder::resident<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#276\">App\\Models\\WorkOrder::media<\/a>"],[0,1,"<a href=\"WorkOrder.php.html#285\">App\\Models\\WorkOrder::tasks<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#293\">App\\Models\\WorkOrder::healthLogs<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#301\">App\\Models\\WorkOrder::workOrderSource<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#309\">App\\Models\\WorkOrder::serviceRequest<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#317\">App\\Models\\WorkOrder::timezone<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#325\">App\\Models\\WorkOrder::assignees<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#333\">App\\Models\\WorkOrder::tags<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#341\">App\\Models\\WorkOrder::invoices<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#349\">App\\Models\\WorkOrder::latestInvoices<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#357\">App\\Models\\WorkOrder::vendor<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#365\">App\\Models\\WorkOrder::createdUser<\/a>"],[0,1,"<a href=\"WorkOrder.php.html#375\">App\\Models\\WorkOrder::workOrderStatus<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#385\">App\\Models\\WorkOrder::workOrderIssues<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#395\">App\\Models\\WorkOrder::issues<\/a>"],[0,1,"<a href=\"WorkOrder.php.html#403\">App\\Models\\WorkOrder::trips<\/a>"],[0,1,"<a href=\"WorkOrder.php.html#408\">App\\Models\\WorkOrder::activeTrip<\/a>"],[100,1,"<a href=\"WorkOrder.php.html#416\">App\\Models\\WorkOrder::latestTrips<\/a>"],[0,59,"<a href=\"WorkOrder.php.html#424\">App\\Models\\WorkOrder::resolveStateAbilities<\/a>"],[0,1,"<a href=\"WorkOrderActivityLog.php.html#97\">App\\Models\\WorkOrderActivityLog::triggeredBy<\/a>"],[0,1,"<a href=\"WorkOrderActivityLog.php.html#105\">App\\Models\\WorkOrderActivityLog::workOrder<\/a>"],[0,1,"<a href=\"WorkOrderAssignee.php.html#60\">App\\Models\\WorkOrderAssignee::workOrder<\/a>"],[0,1,"<a href=\"WorkOrderAssignee.php.html#68\">App\\Models\\WorkOrderAssignee::user<\/a>"],[0,1,"<a href=\"WorkOrderHealthLog.php.html#49\">App\\Models\\WorkOrderHealthLog::healthTracker<\/a>"],[0,1,"<a href=\"WorkOrderHealthLog.php.html#57\">App\\Models\\WorkOrderHealthLog::workOrder<\/a>"],[100,1,"<a href=\"WorkOrderHealthTracker.php.html#58\">App\\Models\\WorkOrderHealthTracker::getTrackerIdsBySlug<\/a>"],[0,1,"<a href=\"WorkOrderHealthTracker.php.html#68\">App\\Models\\WorkOrderHealthTracker::uuidColumn<\/a>"],[0,1,"<a href=\"WorkOrderHealthTracker.php.html#76\">App\\Models\\WorkOrderHealthTracker::violation<\/a>"],[0,1,"<a href=\"WorkOrderHealthTracker.php.html#84\">App\\Models\\WorkOrderHealthTracker::healthLogs<\/a>"],[0,1,"<a href=\"WorkOrderHealthViolation.php.html#52\">App\\Models\\WorkOrderHealthViolation::uuidColumn<\/a>"],[0,1,"<a href=\"WorkOrderHealthViolation.php.html#60\">App\\Models\\WorkOrderHealthViolation::healthTrackers<\/a>"],[100,1,"<a href=\"WorkOrderIssue.php.html#92\">App\\Models\\WorkOrderIssue::workOrder<\/a>"],[100,1,"<a href=\"WorkOrderIssue.php.html#102\">App\\Models\\WorkOrderIssue::issue<\/a>"],[0,1,"<a href=\"WorkOrderIssue.php.html#110\">App\\Models\\WorkOrderIssue::workOrderIssueStatus<\/a>"],[0,1,"<a href=\"WorkOrderIssue.php.html#120\">App\\Models\\WorkOrderIssue::materials<\/a>"],[0,1,"<a href=\"WorkOrderIssue.php.html#133\">App\\Models\\WorkOrderIssue::media<\/a>"],[0,1,"<a href=\"WorkOrderIssueStatus.php.html#32\">App\\Models\\WorkOrderIssueStatus::workOrderIssues<\/a>"],[0,1,"<a href=\"WorkOrderMedia.php.html#110\">App\\Models\\WorkOrderMedia::workOrder<\/a>"],[0,1,"<a href=\"WorkOrderMedia.php.html#118\">App\\Models\\WorkOrderMedia::quoteTask<\/a>"],[0,1,"<a href=\"WorkOrderMedia.php.html#126\">App\\Models\\WorkOrderMedia::media<\/a>"],[0,1,"<a href=\"WorkOrderNote.php.html#104\">App\\Models\\WorkOrderNote::workOrder<\/a>"],[0,1,"<a href=\"WorkOrderNote.php.html#112\">App\\Models\\WorkOrderNote::user<\/a>"],[0,1,"<a href=\"WorkOrderNote.php.html#120\">App\\Models\\WorkOrderNote::vendor<\/a>"],[0,1,"<a href=\"WorkOrderNote.php.html#128\">App\\Models\\WorkOrderNote::lastModifiedUser<\/a>"],[100,1,"<a href=\"WorkOrderServiceCall.php.html#205\">App\\Models\\WorkOrderServiceCall::uuidColumn<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#213\">App\\Models\\WorkOrderServiceCall::appointment<\/a>"],[100,1,"<a href=\"WorkOrderServiceCall.php.html#221\">App\\Models\\WorkOrderServiceCall::technicianAppointment<\/a>"],[100,1,"<a href=\"WorkOrderServiceCall.php.html#229\">App\\Models\\WorkOrderServiceCall::lulaAppointment<\/a>"],[100,1,"<a href=\"WorkOrderServiceCall.php.html#237\">App\\Models\\WorkOrderServiceCall::vendorAppointment<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#245\">App\\Models\\WorkOrderServiceCall::workOrder<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#253\">App\\Models\\WorkOrderServiceCall::tasks<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#268\">App\\Models\\WorkOrderServiceCall::workOrderTaskMaterials<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#276\">App\\Models\\WorkOrderServiceCall::serviceCallLogs<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#284\">App\\Models\\WorkOrderServiceCall::latestServiceCallLogs<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#292\">App\\Models\\WorkOrderServiceCall::latestTripLogs<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#297\">App\\Models\\WorkOrderServiceCall::latestTrip<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#305\">App\\Models\\WorkOrderServiceCall::media<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#316\">App\\Models\\WorkOrderServiceCall::createdQuote<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#324\">App\\Models\\WorkOrderServiceCall::assignedQuote<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#332\">App\\Models\\WorkOrderServiceCall::lastModifiedUser<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#340\">App\\Models\\WorkOrderServiceCall::tripIssues<\/a>"],[0,4,"<a href=\"WorkOrderServiceCall.php.html#348\">App\\Models\\WorkOrderServiceCall::workPerformed<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#378\">App\\Models\\WorkOrderServiceCall::vendorExternalInvoices<\/a>"],[0,1,"<a href=\"WorkOrderServiceCall.php.html#388\">App\\Models\\WorkOrderServiceCall::workOrderIssues<\/a>"],[0,1,"<a href=\"WorkOrderServiceCallIssue.php.html#47\">App\\Models\\WorkOrderServiceCallIssue::workOrderIssue<\/a>"],[0,1,"<a href=\"WorkOrderServiceCallLog.php.html#86\">App\\Models\\WorkOrderServiceCallLog::uuidColumn<\/a>"],[0,1,"<a href=\"WorkOrderServiceCallTask.php.html#67\">App\\Models\\WorkOrderServiceCallTask::task<\/a>"],[0,1,"<a href=\"WorkOrderServiceCallTask.php.html#75\">App\\Models\\WorkOrderServiceCallTask::serviceCall<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#54\">App\\Models\\WorkOrderStatus::workOrders<\/a>"],[0,1,"<a href=\"WorkOrderStatusLog.php.html#63\">App\\Models\\WorkOrderStatusLog::workOrder<\/a>"],[0,1,"<a href=\"WorkOrderStatusLog.php.html#71\">App\\Models\\WorkOrderStatusLog::workOrderStatus<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#86\">App\\Models\\WorkOrderTask::uuidColumn<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#94\">App\\Models\\WorkOrderTask::organization<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#102\">App\\Models\\WorkOrderTask::diagnosis<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#110\">App\\Models\\WorkOrderTask::workOrder<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#118\">App\\Models\\WorkOrderTask::serviceCalls<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#133\">App\\Models\\WorkOrderTask::completedServiceCalls<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#148\">App\\Models\\WorkOrderTask::allServiceCalls<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#163\">App\\Models\\WorkOrderTask::latestServiceCalls<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#171\">App\\Models\\WorkOrderTask::problemDiagnosis<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#181\">App\\Models\\WorkOrderTask::quoteTasks<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#191\">App\\Models\\WorkOrderTask::quotes<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#201\">App\\Models\\WorkOrderTask::latestQuotes<\/a>"],[0,1,"<a href=\"WorkOrderTask.php.html#211\">App\\Models\\WorkOrderTask::materials<\/a>"],[0,1,"<a href=\"WorkOrderTaskMaterial.php.html#72\">App\\Models\\WorkOrderTaskMaterial::uuidColumn<\/a>"],[0,1,"<a href=\"WorkOrderTaskMaterial.php.html#80\">App\\Models\\WorkOrderTaskMaterial::user<\/a>"],[0,1,"<a href=\"WorkOrderTaskMaterial.php.html#88\">App\\Models\\WorkOrderTaskMaterial::task<\/a>"],[0,3,"<a href=\"WorkOrderTaskMaterial.php.html#93\">App\\Models\\WorkOrderTaskMaterial::unitPrice<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item active">/home/<USER>/Documents/Code/lula/lula-saas-platform</li>
         <li class="breadcrumb-item">(<a href="dashboard.html">Dashboard</a>)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="2.38" aria-valuemin="0" aria-valuemax="100" style="width: 2.38%">
           <span class="sr-only">2.38% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">2.38%</div></td>
       <td class="danger small"><div align="right">937&nbsp;/&nbsp;39377</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="5.89" aria-valuemin="0" aria-valuemax="100" style="width: 5.89%">
           <span class="sr-only">5.89% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">5.89%</div></td>
       <td class="danger small"><div align="right">214&nbsp;/&nbsp;3631</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="1.06" aria-valuemin="0" aria-valuemax="100" style="width: 1.06%">
           <span class="sr-only">1.06% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">1.06%</div></td>
       <td class="danger small"><div align="right">12&nbsp;/&nbsp;1132</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="_icons/file-directory.svg" class="octicon" /><a href="app/index.html">app</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="2.40" aria-valuemin="0" aria-valuemax="100" style="width: 2.40%">
           <span class="sr-only">2.40% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">2.40%</div></td>
       <td class="danger small"><div align="right">937&nbsp;/&nbsp;39095</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="5.89" aria-valuemin="0" aria-valuemax="100" style="width: 5.89%">
           <span class="sr-only">5.89% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">5.89%</div></td>
       <td class="danger small"><div align="right">214&nbsp;/&nbsp;3631</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="1.06" aria-valuemin="0" aria-valuemax="100" style="width: 1.06%">
           <span class="sr-only">1.06% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">1.06%</div></td>
       <td class="danger small"><div align="right">12&nbsp;/&nbsp;1132</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="_icons/file-directory.svg" class="octicon" /><a href="routes/index.html">routes</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;282</div></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 50%</span>
     <span class="warning"><strong>Medium</strong>: 50% to 90%</span>
     <span class="success"><strong>High</strong>: 90% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:22:08 UTC 2025.</small>
    </p>
   </footer>
  </div>
 </body>
</html>

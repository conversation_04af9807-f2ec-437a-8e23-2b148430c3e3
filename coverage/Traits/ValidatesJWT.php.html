<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Traits/ValidatesJWT.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Traits</a></li>
         <li class="breadcrumb-item active">ValidatesJWT.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="warning">Total</td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="80.00" aria-valuemin="0" aria-valuemax="100" style="width: 80.00%">
           <span class="sr-only">80.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">80.00%</div></td>
       <td class="warning small"><div align="right">24&nbsp;/&nbsp;30</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="sr-only">50.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">50.00%</div></td>
       <td class="danger small"><div align="right">2&nbsp;/&nbsp;4</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><abbr title="App\Traits\ValidatesJWT">ValidatesJWT</abbr></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="80.00" aria-valuemin="0" aria-valuemax="100" style="width: 80.00%">
           <span class="sr-only">80.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">80.00%</div></td>
       <td class="warning small"><div align="right">24&nbsp;/&nbsp;30</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="sr-only">50.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">50.00%</div></td>
       <td class="danger small"><div align="right">2&nbsp;/&nbsp;4</div></td>
       <td class="danger small">11.97</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#29"><abbr title="validateJWT(string $token): stdClass">validateJWT</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="66.67" aria-valuemin="0" aria-valuemax="100" style="width: 66.67%">
           <span class="sr-only">66.67% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">66.67%</div></td>
       <td class="warning small"><div align="right">8&nbsp;/&nbsp;12</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">5.93</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#54"><abbr title="getJsonWebKeys(string $token): array">getJsonWebKeys</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="86.67" aria-valuemin="0" aria-valuemax="100" style="width: 86.67%">
           <span class="sr-only">86.67% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">86.67%</div></td>
       <td class="warning small"><div align="right">13&nbsp;/&nbsp;15</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">4.04</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#83"><abbr title="getCacheName(string $iss, string $kid): string">getCacheName</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">2&nbsp;/&nbsp;2</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#93"><abbr title="getCacheExpiry(): Carbon\Carbon">getCacheExpiry</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Traits</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Exceptions\AWS\Cognito\JwtValidationException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Carbon\Carbon</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">DomainException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Exception</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Firebase\JWT\BeforeValidException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Firebase\JWT\ExpiredException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Firebase\JWT\JWK</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Firebase\JWT\JWT</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Firebase\JWT\Key</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Firebase\JWT\SignatureInvalidException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\Cache</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\Http</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Str</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">InvalidArgumentException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">stdClass</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">UnexpectedValueException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="keyword">trait</span><span class="default">&nbsp;</span><span class="default">ValidatesJWT</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Validate&nbsp;a&nbsp;JWT&nbsp;and&nbsp;return&nbsp;the&nbsp;payload</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;JwtValidationException&nbsp;Failed&nbsp;to&nbsp;parse&nbsp;the&nbsp;provided&nbsp;JWT</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">validateJWT</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$token</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">stdClass</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="58 tests cover line 31" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$token</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">JwtValidationException</span><span class="keyword">(</span><span class="default">__</span><span class="keyword">(</span><span class="default">'Access&nbsp;denied.'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="58 tests cover line 36" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">JWT</span><span class="default">::</span><span class="default">decode</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="58 tests cover line 37" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$token</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="58 tests cover line 38" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getJsonWebKeys</span><span class="keyword">(</span><span class="default">$token</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="58 tests cover line 39" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 40" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">BeforeValidException</span><span class="keyword">|</span><span class="default">ExpiredException</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">JwtValidationException</span><span class="keyword">(</span><span class="default">__</span><span class="keyword">(</span><span class="default">'Access&nbsp;denied&nbsp;due&nbsp;to&nbsp;invalid&nbsp;or&nbsp;expired&nbsp;token.'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 42" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">SignatureInvalidException</span><span class="keyword">|</span><span class="default">UnexpectedValueException</span><span class="keyword">|</span><span class="default">InvalidArgumentException</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 43" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">JwtValidationException</span><span class="keyword">(</span><span class="default">__</span><span class="keyword">(</span><span class="default">'Access&nbsp;denied&nbsp;due&nbsp;to&nbsp;invalid&nbsp;token'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">DomainException</span><span class="keyword">|</span><span class="default">Exception</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">JwtValidationException</span><span class="keyword">(</span><span class="default">__</span><span class="keyword">(</span><span class="default">'Access&nbsp;denied&nbsp;due&nbsp;to&nbsp;invalid&nbsp;token'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;json&nbsp;web&nbsp;key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&lt;string,&nbsp;Key&gt;&nbsp;An&nbsp;associative&nbsp;array&nbsp;of&nbsp;key&nbsp;IDs&nbsp;(kid)&nbsp;to&nbsp;Key&nbsp;objects</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getJsonWebKeys</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$token</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="58 tests cover line 56" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$tokenSegments</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">explode</span><span class="keyword">(</span><span class="default">'.'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$token</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="58 tests cover line 58" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">count</span><span class="keyword">(</span><span class="default">$tokenSegments</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">3</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 59" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UnexpectedValueException</span><span class="keyword">(</span><span class="default">__</span><span class="keyword">(</span><span class="default">'Wrong&nbsp;number&nbsp;of&nbsp;segments'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="56 tests cover line 62" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$tokenHeader</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">JWT</span><span class="default">::</span><span class="default">jsonDecode</span><span class="keyword">(</span><span class="default">JWT</span><span class="default">::</span><span class="default">urlsafeB64Decode</span><span class="keyword">(</span><span class="default">$tokenSegments</span><span class="keyword">[</span><span class="default">0</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="56 tests cover line 63" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$payload</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">JWT</span><span class="default">::</span><span class="default">jsonDecode</span><span class="keyword">(</span><span class="default">JWT</span><span class="default">::</span><span class="default">urlsafeB64Decode</span><span class="keyword">(</span><span class="default">$tokenSegments</span><span class="keyword">[</span><span class="default">1</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="56 tests cover line 65" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$tokenHeader</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">||</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$payload</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UnexpectedValueException</span><span class="keyword">(</span><span class="default">__</span><span class="keyword">(</span><span class="default">'Invalid&nbsp;Token'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="56 tests cover line 69" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$keys</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">(array)</span><span class="default">&nbsp;</span><span class="default">Cache</span><span class="default">::</span><span class="default">remember</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="56 tests cover line 70" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getCacheName</span><span class="keyword">(</span><span class="default">$payload</span><span class="default">-&gt;</span><span class="default">iss</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$tokenHeader</span><span class="default">-&gt;</span><span class="default">kid</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="56 tests cover line 71" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getCacheExpiry</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="56 tests cover line 72" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">static</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$payload</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Http</span><span class="default">::</span><span class="default">get</span><span class="keyword">(</span><span class="string">&quot;</span><span class="string">{</span><span class="string">$payload</span><span class="string">-&gt;</span><span class="string">iss</span><span class="keyword">}</span><span class="string">/.well-known/jwks.json</span><span class="string">&quot;</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">json</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="56 tests cover line 74" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="56 tests cover line 75" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="56 tests cover line 77" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">JWK</span><span class="default">::</span><span class="default">parseKeySet</span><span class="keyword">(</span><span class="default">$keys</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;cache&nbsp;name&nbsp;for&nbsp;cognito</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getCacheName</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$iss</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$kid</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="87 tests cover line 85" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Exception_during_service_request_priority_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_when_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Exception_during_property_address_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_true_for_a_user_with_the_correct_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_when_user_does_not_have_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Unauthorized_user_cannot_list_work_orders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_not_found_on_work_order_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Generating_signed_url_with_invalid_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Generating_signed_url_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Set_service_areas_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Get_onboarding_details_with_invalid_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Get_onboarding_details_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Successfully_return_vendor_onboarding_inte_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Return_403_error_for_if_signed_url_is_expired&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Get_lookup_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Set_password_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Set_status_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_no_token_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$iss</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">Str</span><span class="default">::</span><span class="default">afterLast</span><span class="keyword">(</span><span class="default">$iss</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'/'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="87 tests cover line 87" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Exception_during_service_request_priority_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_when_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Exception_during_property_address_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_true_for_a_user_with_the_correct_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_when_user_does_not_have_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Unauthorized_user_cannot_list_work_orders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_not_found_on_work_order_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Generating_signed_url_with_invalid_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Generating_signed_url_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Set_service_areas_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Get_onboarding_details_with_invalid_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Get_onboarding_details_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Successfully_return_vendor_onboarding_inte_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Return_403_error_for_if_signed_url_is_expired&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Get_lookup_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Set_password_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Set_status_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_no_token_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="string">&quot;</span><span class="string">cognito_</span><span class="string">{</span><span class="string">$iss</span><span class="keyword">}</span><span class="string">_</span><span class="string">{</span><span class="string">$kid</span><span class="keyword">}</span><span class="string">&quot;</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;&nbsp;Get&nbsp;cache&nbsp;expiry</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getCacheExpiry</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">Carbon</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="87 tests cover line 95" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Exception_during_service_request_priority_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_when_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Exception_during_property_address_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_true_for_a_user_with_the_correct_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_when_user_does_not_have_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Unauthorized_user_cannot_list_work_orders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_not_found_on_work_order_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Generating_signed_url_with_invalid_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Generating_signed_url_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Set_service_areas_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Get_onboarding_details_with_invalid_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Get_onboarding_details_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Successfully_return_vendor_onboarding_inte_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Return_403_error_for_if_signed_url_is_expired&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Get_lookup_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Set_password_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Set_status_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_no_token_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Carbon</span><span class="default">::</span><span class="default">now</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">addYear</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Mon Jun 30 0:32:48 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

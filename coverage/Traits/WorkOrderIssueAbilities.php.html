<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Traits/WorkOrderIssueAbilities.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Traits</a></li>
         <li class="breadcrumb-item active">WorkOrderIssueAbilities.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="90.91" aria-valuemin="0" aria-valuemax="100" style="width: 90.91%">
           <span class="sr-only">90.91% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">90.91%</div></td>
       <td class="success small"><div align="right">10&nbsp;/&nbsp;11</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="80.00" aria-valuemin="0" aria-valuemax="100" style="width: 80.00%">
           <span class="sr-only">80.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">80.00%</div></td>
       <td class="warning small"><div align="right">4&nbsp;/&nbsp;5</div></td>
       <td class="warning small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><abbr title="App\Traits\WorkOrderIssueAbilities">WorkOrderIssueAbilities</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="90.91" aria-valuemin="0" aria-valuemax="100" style="width: 90.91%">
           <span class="sr-only">90.91% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">90.91%</div></td>
       <td class="success small"><div align="right">10&nbsp;/&nbsp;11</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="80.00" aria-valuemin="0" aria-valuemax="100" style="width: 80.00%">
           <span class="sr-only">80.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">80.00%</div></td>
       <td class="warning small"><div align="right">4&nbsp;/&nbsp;5</div></td>
       <td class="warning small">8.05</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#19"><abbr title="abilities(): array">abilities</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">5&nbsp;/&nbsp;5</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">2</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#35"><abbr title="isDeleted(): bool">isDeleted</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#46"><abbr title="filterEditAction(array $actions): array">filterEditAction</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">3</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#58"><abbr title="isMappedToActiveWorkOrder(): bool">isMappedToActiveWorkOrder</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#66"><abbr title="isMappedToClosedWorkOrder(): bool">isMappedToClosedWorkOrder</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Traits</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Enums\WorkOrderIssueActions</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\AwaitingAvailability</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\Canceled</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">WorkOrderCanceled</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\Created</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\ReadyToSchedule</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\Scoping</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">trait</span><span class="default">&nbsp;</span><span class="default">WorkOrderIssueAbilities</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;the&nbsp;possible&nbsp;abilities&nbsp;for&nbsp;the&nbsp;current&nbsp;work&nbsp;order&nbsp;issue.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&lt;int|string&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">abilities</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="53 tests cover line 21" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'canceled', ['unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'claim_pending', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'created', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'paused', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'quality_check', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'resolved', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduled', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scoping', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'awaiting_availability', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'canceled', ['unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'claim_pending', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'created', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'paused', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'quality_check', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_schedule', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_invoice', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'resolved', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduled', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scoping', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduling_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'work_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('declined')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_and_dispatch_events_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\WorkOrderIssueResourceTest::__pest_evaluable_WorkOrderIssueResource_transforms_a_single_WorkOrderIssue_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_standard_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_device_request&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_updates_all_associated_work_order_issues_to_canceled_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_does_not_update_work_order_issues_for_unrelated_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event_when_work_order_is_in_progress_and_has_one_assigned_issue_in_assigned_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$possibleActions</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">actions</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="53 tests cover line 23" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'canceled', ['unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'claim_pending', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'created', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'paused', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'quality_check', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'resolved', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduled', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scoping', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'awaiting_availability', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'canceled', ['unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'claim_pending', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'created', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'paused', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'quality_check', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_schedule', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_invoice', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'resolved', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduled', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scoping', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduling_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'work_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('declined')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_and_dispatch_events_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\WorkOrderIssueResourceTest::__pest_evaluable_WorkOrderIssueResource_transforms_a_single_WorkOrderIssue_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_standard_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_device_request&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_updates_all_associated_work_order_issues_to_canceled_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_does_not_update_work_order_issues_for_unrelated_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event_when_work_order_is_in_progress_and_has_one_assigned_issue_in_assigned_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">isDeleted</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 24" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('declined')&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="50 tests cover line 27" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'canceled', ['unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'claim_pending', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'created', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'paused', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'quality_check', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'resolved', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduled', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scoping', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'awaiting_availability', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'canceled', ['unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'claim_pending', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'created', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'paused', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'quality_check', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_schedule', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_invoice', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'resolved', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduled', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scoping', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduling_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'work_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_and_dispatch_events_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\WorkOrderIssueResourceTest::__pest_evaluable_WorkOrderIssueResource_transforms_a_single_WorkOrderIssue_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_standard_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_device_request&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_updates_all_associated_work_order_issues_to_canceled_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_does_not_update_work_order_issues_for_unrelated_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event_when_work_order_is_in_progress_and_has_one_assigned_issue_in_assigned_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$possibleActions</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">filterEditAction</span><span class="keyword">(</span><span class="default">$possibleActions</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="50 tests cover line 29" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'canceled', ['unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'claim_pending', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'created', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'paused', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'quality_check', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'resolved', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduled', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scoping', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'awaiting_availability', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'canceled', ['unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'claim_pending', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'created', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'paused', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'quality_check', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_schedule', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_invoice', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'resolved', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduled', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scoping', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduling_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'work_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_and_dispatch_events_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\WorkOrderIssueResourceTest::__pest_evaluable_WorkOrderIssueResource_transforms_a_single_WorkOrderIssue_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_standard_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_device_request&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_updates_all_associated_work_order_issues_to_canceled_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_does_not_update_work_order_issues_for_unrelated_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event_when_work_order_is_in_progress_and_has_one_assigned_issue_in_assigned_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$possibleActions</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Check&nbsp;if&nbsp;the&nbsp;issue&nbsp;is&nbsp;deleted.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">isDeleted</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="53 tests cover line 37" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'canceled', ['unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'claim_pending', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'created', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'paused', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'quality_check', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'resolved', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduled', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scoping', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'awaiting_availability', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'canceled', ['unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'claim_pending', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'created', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'paused', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'quality_check', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_schedule', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_invoice', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'resolved', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduled', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scoping', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduling_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'work_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('declined')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_and_dispatch_events_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\WorkOrderIssueResourceTest::__pest_evaluable_WorkOrderIssueResource_transforms_a_single_WorkOrderIssue_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_standard_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_device_request&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_updates_all_associated_work_order_issues_to_canceled_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_does_not_update_work_order_issues_for_unrelated_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event_when_work_order_is_in_progress_and_has_one_assigned_issue_in_assigned_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">deleted_at</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Filter&nbsp;out&nbsp;the&nbsp;EDIT&nbsp;action&nbsp;if&nbsp;the&nbsp;work&nbsp;order&nbsp;issue&nbsp;if&nbsp;mapped&nbsp;work&nbsp;order&nbsp;is&nbsp;canceled.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;int|string&gt;&nbsp;&nbsp;$actions</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&lt;int|string&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">filterEditAction</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="50 tests cover line 48" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'canceled', ['unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'claim_pending', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'created', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'paused', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'quality_check', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'resolved', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduled', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scoping', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'awaiting_availability', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'canceled', ['unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'claim_pending', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'created', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'paused', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'quality_check', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_schedule', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_invoice', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'resolved', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduled', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scoping', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduling_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'work_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_and_dispatch_events_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\WorkOrderIssueResourceTest::__pest_evaluable_WorkOrderIssueResource_transforms_a_single_WorkOrderIssue_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_standard_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_device_request&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_updates_all_associated_work_order_issues_to_canceled_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_does_not_update_work_order_issues_for_unrelated_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event_when_work_order_is_in_progress_and_has_one_assigned_issue_in_assigned_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">in_array</span><span class="keyword">(</span><span class="default">WorkOrderIssueActions</span><span class="default">::</span><span class="default">EDIT</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">isMappedToClosedWorkOrder</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 49" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'canceled', ['unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'canceled', ['unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event_when_work_order_is_in_progress_and_has_one_assigned_issue_in_assigned_state_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">unset</span><span class="keyword">(</span><span class="default">$actions</span><span class="keyword">[</span><span class="default">array_search</span><span class="keyword">(</span><span class="default">WorkOrderIssueActions</span><span class="default">::</span><span class="default">EDIT</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">)</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="50 tests cover line 52" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'canceled', ['unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'claim_pending', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'created', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'paused', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'quality_check', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'resolved', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduled', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scoping', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'awaiting_availability', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'canceled', ['unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'claim_pending', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'created', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'paused', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'quality_check', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_schedule', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_invoice', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'resolved', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduled', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scoping', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduling_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'work_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_and_dispatch_events_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\WorkOrderIssueResourceTest::__pest_evaluable_WorkOrderIssueResource_transforms_a_single_WorkOrderIssue_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_standard_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_device_request&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_updates_all_associated_work_order_issues_to_canceled_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_does_not_update_work_order_issues_for_unrelated_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event_when_work_order_is_in_progress_and_has_one_assigned_issue_in_assigned_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Check&nbsp;if&nbsp;the&nbsp;issue&nbsp;is&nbsp;mapped&nbsp;to&nbsp;any&nbsp;scheduled&nbsp;or&nbsp;work&nbsp;in&nbsp;progress&nbsp;work&nbsp;order.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">isMappedToActiveWorkOrder</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">equals</span><span class="keyword">(</span><span class="default">AwaitingAvailability</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">ReadyToSchedule</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">WorkOrderCanceled</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">Created</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">Scoping</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Check&nbsp;if&nbsp;the&nbsp;work&nbsp;order&nbsp;associated&nbsp;with&nbsp;the&nbsp;work&nbsp;order&nbsp;issue&nbsp;is&nbsp;canceled.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">isMappedToClosedWorkOrder</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="50 tests cover line 68" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'canceled', ['unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'claim_pending', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'created', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'paused', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'quality_check', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'resolved', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduled', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scoping', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'awaiting_availability', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'canceled', ['unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'claim_pending', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'created', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'paused', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'quality_check', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_schedule', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_invoice', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'resolved', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduled', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scoping', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduling_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'work_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_and_dispatch_events_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\WorkOrderIssueResourceTest::__pest_evaluable_WorkOrderIssueResource_transforms_a_single_WorkOrderIssue_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_standard_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_device_request&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_updates_all_associated_work_order_issues_to_canceled_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_does_not_update_work_order_issues_for_unrelated_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event_when_work_order_is_in_progress_and_has_one_assigned_issue_in_assigned_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrder</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">equals</span><span class="keyword">(</span><span class="default">WorkOrderCanceled</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.17</a> at Wed Jun 25 16:26:03 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

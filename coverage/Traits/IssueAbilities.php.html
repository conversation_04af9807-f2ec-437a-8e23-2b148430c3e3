<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Traits/IssueAbilities.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Traits</a></li>
         <li class="breadcrumb-item active">IssueAbilities.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">21&nbsp;/&nbsp;21</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">6&nbsp;/&nbsp;6</div></td>
       <td class="success small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><abbr title="App\Traits\IssueAbilities">IssueAbilities</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">21&nbsp;/&nbsp;21</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">6&nbsp;/&nbsp;6</div></td>
       <td class="success small">14</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#22"><abbr title="abilities(): array">abilities</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">8&nbsp;/&nbsp;8</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">3</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#43"><abbr title="isDeleted(): bool">isDeleted</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#54"><abbr title="filterDeleteAction(array $actions): array">filterDeleteAction</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">3</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#69"><abbr title="filterCancelAction(array $actions): array">filterCancelAction</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">5&nbsp;/&nbsp;5</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">4</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#85"><abbr title="isMappedToActiveWorkOrder(): bool">isMappedToActiveWorkOrder</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">2</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#97"><abbr title="isClosedServiceRequest(): bool">isClosedServiceRequest</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Traits</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Enums\IssueActions</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\WorkOrder</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\Issue\Assigned</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\ServiceRequests\Closed</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\AwaitingAvailability</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\Canceled</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">WorkOrderCanceled</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\Created</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\ReadyToSchedule</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\Scoping</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="keyword">trait</span><span class="default">&nbsp;</span><span class="default">IssueAbilities</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;the&nbsp;possible&nbsp;abilities&nbsp;for&nbsp;the&nbsp;current&nbsp;issue.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&lt;int|string&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">abilities</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="92 tests cover line 24" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('done')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('done')&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$possibleActions</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">actions</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="92 tests cover line 26" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('done')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('done')&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">isDeleted</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 27" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('done')&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="88 tests cover line 30" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('done')&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">isClosedServiceRequest</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 31" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('done')&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">IssueActions</span><span class="default">::</span><span class="default">EDIT</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="84 tests cover line 34" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$possibleActions</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">filterDeleteAction</span><span class="keyword">(</span><span class="default">$possibleActions</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="84 tests cover line 35" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$possibleActions</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">filterCancelAction</span><span class="keyword">(</span><span class="default">$possibleActions</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="84 tests cover line 37" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$possibleActions</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Check&nbsp;if&nbsp;the&nbsp;issue&nbsp;is&nbsp;deleted.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">isDeleted</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="92 tests cover line 45" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('done')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('done')&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">deleted_at</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Filter&nbsp;out&nbsp;the&nbsp;DELETE&nbsp;action&nbsp;if&nbsp;the&nbsp;issue&nbsp;is&nbsp;mapped&nbsp;to&nbsp;any&nbsp;scheduled&nbsp;or&nbsp;in-progress&nbsp;work&nbsp;orders.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;int|string&gt;&nbsp;&nbsp;$actions</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&lt;int|string&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">filterDeleteAction</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="84 tests cover line 56" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">in_array</span><span class="keyword">(</span><span class="default">IssueActions</span><span class="default">::</span><span class="default">DELETE</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">isMappedToActiveWorkOrder</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="30 tests cover line 57" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">unset</span><span class="keyword">(</span><span class="default">$actions</span><span class="keyword">[</span><span class="default">array_search</span><span class="keyword">(</span><span class="default">IssueActions</span><span class="default">::</span><span class="default">DELETE</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">)</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="84 tests cover line 60" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Filter&nbsp;out&nbsp;the&nbsp;CANCEL&nbsp;action&nbsp;if&nbsp;the&nbsp;issue&nbsp;has&nbsp;scheduled&nbsp;or&nbsp;in-progress&nbsp;work&nbsp;orders.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;int|string&gt;&nbsp;&nbsp;$actions</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&lt;int|string&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">filterCancelAction</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="84 tests cover line 72" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">in_array</span><span class="keyword">(</span><span class="default">IssueActions</span><span class="default">::</span><span class="default">CANCEL</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="84 tests cover line 73" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">equals</span><span class="keyword">(</span><span class="default">Assigned</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="84 tests cover line 74" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">isMappedToActiveWorkOrder</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="12 tests cover line 76" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">unset</span><span class="keyword">(</span><span class="default">$actions</span><span class="keyword">[</span><span class="default">array_search</span><span class="keyword">(</span><span class="default">IssueActions</span><span class="default">::</span><span class="default">CANCEL</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">)</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="84 tests cover line 79" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$actions</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Check&nbsp;if&nbsp;the&nbsp;issue&nbsp;is&nbsp;mapped&nbsp;to&nbsp;any&nbsp;scheduled&nbsp;or&nbsp;work&nbsp;in&nbsp;progress&nbsp;work&nbsp;order.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">isMappedToActiveWorkOrder</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="71 tests cover line 87" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrders</span><span class="default">-&gt;</span><span class="default">isEmpty</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="18 tests cover line 88" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">false</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="53 tests cover line 91" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">workOrders</span><span class="default">-&gt;</span><span class="default">contains</span><span class="keyword">(</span><span class="keyword">fn</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">WorkOrder</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">equals</span><span class="keyword">(</span><span class="default">AwaitingAvailability</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">ReadyToSchedule</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">WorkOrderCanceled</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">Created</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">Scoping</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Check&nbsp;if&nbsp;the&nbsp;service&nbsp;request&nbsp;associated&nbsp;with&nbsp;the&nbsp;issue&nbsp;is&nbsp;closed.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">private</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">isClosedServiceRequest</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="88 tests cover line 99" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('done')&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">serviceRequest</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">equals</span><span class="keyword">(</span><span class="default">Closed</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.17</a> at Wed Jun 25 16:26:03 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Helpers</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Helpers</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DataFormatHelper.php.html#12">App\Helpers\DataFormatHelper</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helper.php.html#33">App\Helpers\Helper</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OnboardingHelper.php.html#9">App\Helpers\OnboardingHelper</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Helper.php.html#33">App\Helpers\Helper</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="DataFormatHelper.php.html#12">App\Helpers\DataFormatHelper</a></td><td class="text-right">90</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DataFormatHelper.php.html#19"><abbr title="App\Helpers\DataFormatHelper::convertWorkOrderFilterObjectToArray">convertWorkOrderFilterObjectToArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFormatHelper.php.html#47"><abbr title="App\Helpers\DataFormatHelper::formatWorkingHours">formatWorkingHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DataFormatHelper.php.html#75"><abbr title="App\Helpers\DataFormatHelper::dateFormat">dateFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helper.php.html#39"><abbr title="App\Helpers\Helper::generateRandomAlphaNumber">generateRandomAlphaNumber</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helper.php.html#60"><abbr title="App\Helpers\Helper::resolvePhoneNumberForTwilio">resolvePhoneNumberForTwilio</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helper.php.html#80"><abbr title="App\Helpers\Helper::developerAlert">developerAlert</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helper.php.html#117"><abbr title="App\Helpers\Helper::exceptionLog">exceptionLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helper.php.html#191"><abbr title="App\Helpers\Helper::displayPhoneNumber">displayPhoneNumber</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helper.php.html#198"><abbr title="App\Helpers\Helper::generateSecureLink">generateSecureLink</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helper.php.html#207"><abbr title="App\Helpers\Helper::getLocationApiKey">getLocationApiKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Helper.php.html#233"><abbr title="App\Helpers\Helper::buildStringUrl">buildStringUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OnboardingHelper.php.html#16"><abbr title="App\Helpers\OnboardingHelper::generateSignedUrl">generateSignedUrl</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Helper.php.html#117"><abbr title="App\Helpers\Helper::exceptionLog">exceptionLog</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="DataFormatHelper.php.html#19"><abbr title="App\Helpers\DataFormatHelper::convertWorkOrderFilterObjectToArray">convertWorkOrderFilterObjectToArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Helper.php.html#80"><abbr title="App\Helpers\Helper::developerAlert">developerAlert</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Helper.php.html#39"><abbr title="App\Helpers\Helper::generateRandomAlphaNumber">generateRandomAlphaNumber</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Helper.php.html#60"><abbr title="App\Helpers\Helper::resolvePhoneNumberForTwilio">resolvePhoneNumberForTwilio</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Helper.php.html#207"><abbr title="App\Helpers\Helper::getLocationApiKey">getLocationApiKey</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DataFormatHelper.php.html#47"><abbr title="App\Helpers\DataFormatHelper::formatWorkingHours">formatWorkingHours</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DataFormatHelper.php.html#75"><abbr title="App\Helpers\DataFormatHelper::dateFormat">dateFormat</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Helper.php.html#233"><abbr title="App\Helpers\Helper::buildStringUrl">buildStringUrl</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([3,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([12,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,9,"<a href=\"DataFormatHelper.php.html#12\">App\\Helpers\\DataFormatHelper<\/a>"],[0,27,"<a href=\"Helper.php.html#33\">App\\Helpers\\Helper<\/a>"],[0,1,"<a href=\"OnboardingHelper.php.html#9\">App\\Helpers\\OnboardingHelper<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"DataFormatHelper.php.html#19\">App\\Helpers\\DataFormatHelper::convertWorkOrderFilterObjectToArray<\/a>"],[0,2,"<a href=\"DataFormatHelper.php.html#47\">App\\Helpers\\DataFormatHelper::formatWorkingHours<\/a>"],[0,2,"<a href=\"DataFormatHelper.php.html#75\">App\\Helpers\\DataFormatHelper::dateFormat<\/a>"],[0,3,"<a href=\"Helper.php.html#39\">App\\Helpers\\Helper::generateRandomAlphaNumber<\/a>"],[0,3,"<a href=\"Helper.php.html#60\">App\\Helpers\\Helper::resolvePhoneNumberForTwilio<\/a>"],[0,4,"<a href=\"Helper.php.html#80\">App\\Helpers\\Helper::developerAlert<\/a>"],[0,10,"<a href=\"Helper.php.html#117\">App\\Helpers\\Helper::exceptionLog<\/a>"],[0,1,"<a href=\"Helper.php.html#191\">App\\Helpers\\Helper::displayPhoneNumber<\/a>"],[0,1,"<a href=\"Helper.php.html#198\">App\\Helpers\\Helper::generateSecureLink<\/a>"],[0,3,"<a href=\"Helper.php.html#207\">App\\Helpers\\Helper::getLocationApiKey<\/a>"],[0,2,"<a href=\"Helper.php.html#233\">App\\Helpers\\Helper::buildStringUrl<\/a>"],[0,1,"<a href=\"OnboardingHelper.php.html#16\">App\\Helpers\\OnboardingHelper::generateSignedUrl<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

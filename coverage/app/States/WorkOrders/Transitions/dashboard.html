<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/States/WorkOrders/Transitions</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">States</a></li>
         <li class="breadcrumb-item"><a href="../index.html">WorkOrders</a></li>
         <li class="breadcrumb-item"><a href="index.html">Transitions</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ToAwaitingAvailability.php.html#14">App\States\WorkOrders\Transitions\ToAwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToCanceled.php.html#15">App\States\WorkOrders\Transitions\ToCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToClaimPending.php.html#14">App\States\WorkOrders\Transitions\ToClaimPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToCompleted.php.html#15">App\States\WorkOrders\Transitions\ToCompleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToCreated.php.html#18">App\States\WorkOrders\Transitions\ToCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToPaused.php.html#16">App\States\WorkOrders\Transitions\ToPaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToQualityCheck.php.html#15">App\States\WorkOrders\Transitions\ToQualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToReadyToInvoice.php.html#15">App\States\WorkOrders\Transitions\ToReadyToInvoice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToReadyToSchedule.php.html#15">App\States\WorkOrders\Transitions\ToReadyToSchedule</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduled.php.html#29">App\States\WorkOrders\Transitions\ToScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToWorkInProgress.php.html#15">App\States\WorkOrders\Transitions\ToWorkInProgress</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ToScheduled.php.html#29">App\States\WorkOrders\Transitions\ToScheduled</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ToCreated.php.html#18">App\States\WorkOrders\Transitions\ToCreated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ToPaused.php.html#16">App\States\WorkOrders\Transitions\ToPaused</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ToCompleted.php.html#15">App\States\WorkOrders\Transitions\ToCompleted</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ToCanceled.php.html#15">App\States\WorkOrders\Transitions\ToCanceled</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ToAwaitingAvailability.php.html#16"><abbr title="App\States\WorkOrders\Transitions\ToAwaitingAvailability::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToAwaitingAvailability.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToAwaitingAvailability::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToCanceled.php.html#18"><abbr title="App\States\WorkOrders\Transitions\ToCanceled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToCanceled.php.html#20"><abbr title="App\States\WorkOrders\Transitions\ToCanceled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToClaimPending.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToClaimPending::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToClaimPending.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToClaimPending::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToCompleted.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToCompleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToCompleted.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToCompleted::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToCreated.php.html#20"><abbr title="App\States\WorkOrders\Transitions\ToCreated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToCreated.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToCreated::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToPaused.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToPaused::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToPaused.php.html#30"><abbr title="App\States\WorkOrders\Transitions\ToPaused::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToQualityCheck.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToQualityCheck::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToQualityCheck.php.html#27"><abbr title="App\States\WorkOrders\Transitions\ToQualityCheck::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToReadyToInvoice.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToReadyToInvoice::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToReadyToInvoice.php.html#23"><abbr title="App\States\WorkOrders\Transitions\ToReadyToInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToReadyToSchedule.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToReadyToSchedule::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToReadyToSchedule.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToReadyToSchedule::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduled.php.html#32"><abbr title="App\States\WorkOrders\Transitions\ToScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToScheduled.php.html#46"><abbr title="App\States\WorkOrders\Transitions\ToScheduled::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToWorkInProgress.php.html#17"><abbr title="App\States\WorkOrders\Transitions\ToWorkInProgress::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToWorkInProgress.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToWorkInProgress::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ToScheduled.php.html#46"><abbr title="App\States\WorkOrders\Transitions\ToScheduled::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ToCreated.php.html#22"><abbr title="App\States\WorkOrders\Transitions\ToCreated::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ToPaused.php.html#30"><abbr title="App\States\WorkOrders\Transitions\ToPaused::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ToCompleted.php.html#19"><abbr title="App\States\WorkOrders\Transitions\ToCompleted::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ToCanceled.php.html#20"><abbr title="App\States\WorkOrders\Transitions\ToCanceled::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Thu Jun 26 15:40:22 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([11,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([22,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"ToAwaitingAvailability.php.html#14\">App\\States\\WorkOrders\\Transitions\\ToAwaitingAvailability<\/a>"],[0,3,"<a href=\"ToCanceled.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToCanceled<\/a>"],[0,2,"<a href=\"ToClaimPending.php.html#14\">App\\States\\WorkOrders\\Transitions\\ToClaimPending<\/a>"],[0,4,"<a href=\"ToCompleted.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToCompleted<\/a>"],[0,6,"<a href=\"ToCreated.php.html#18\">App\\States\\WorkOrders\\Transitions\\ToCreated<\/a>"],[0,5,"<a href=\"ToPaused.php.html#16\">App\\States\\WorkOrders\\Transitions\\ToPaused<\/a>"],[0,2,"<a href=\"ToQualityCheck.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToQualityCheck<\/a>"],[0,2,"<a href=\"ToReadyToInvoice.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToReadyToInvoice<\/a>"],[0,2,"<a href=\"ToReadyToSchedule.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToReadyToSchedule<\/a>"],[0,10,"<a href=\"ToScheduled.php.html#29\">App\\States\\WorkOrders\\Transitions\\ToScheduled<\/a>"],[0,2,"<a href=\"ToWorkInProgress.php.html#15\">App\\States\\WorkOrders\\Transitions\\ToWorkInProgress<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ToAwaitingAvailability.php.html#16\">App\\States\\WorkOrders\\Transitions\\ToAwaitingAvailability::__construct<\/a>"],[0,1,"<a href=\"ToAwaitingAvailability.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToAwaitingAvailability::handle<\/a>"],[0,1,"<a href=\"ToCanceled.php.html#18\">App\\States\\WorkOrders\\Transitions\\ToCanceled::__construct<\/a>"],[0,2,"<a href=\"ToCanceled.php.html#20\">App\\States\\WorkOrders\\Transitions\\ToCanceled::handle<\/a>"],[0,1,"<a href=\"ToClaimPending.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToClaimPending::__construct<\/a>"],[0,1,"<a href=\"ToClaimPending.php.html#19\">App\\States\\WorkOrders\\Transitions\\ToClaimPending::handle<\/a>"],[0,1,"<a href=\"ToCompleted.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToCompleted::__construct<\/a>"],[0,3,"<a href=\"ToCompleted.php.html#19\">App\\States\\WorkOrders\\Transitions\\ToCompleted::handle<\/a>"],[0,1,"<a href=\"ToCreated.php.html#20\">App\\States\\WorkOrders\\Transitions\\ToCreated::__construct<\/a>"],[0,5,"<a href=\"ToCreated.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToCreated::handle<\/a>"],[0,1,"<a href=\"ToPaused.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToPaused::__construct<\/a>"],[0,4,"<a href=\"ToPaused.php.html#30\">App\\States\\WorkOrders\\Transitions\\ToPaused::handle<\/a>"],[0,1,"<a href=\"ToQualityCheck.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToQualityCheck::__construct<\/a>"],[0,1,"<a href=\"ToQualityCheck.php.html#27\">App\\States\\WorkOrders\\Transitions\\ToQualityCheck::handle<\/a>"],[0,1,"<a href=\"ToReadyToInvoice.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToReadyToInvoice::__construct<\/a>"],[0,1,"<a href=\"ToReadyToInvoice.php.html#23\">App\\States\\WorkOrders\\Transitions\\ToReadyToInvoice::handle<\/a>"],[0,1,"<a href=\"ToReadyToSchedule.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToReadyToSchedule::__construct<\/a>"],[0,1,"<a href=\"ToReadyToSchedule.php.html#22\">App\\States\\WorkOrders\\Transitions\\ToReadyToSchedule::handle<\/a>"],[0,1,"<a href=\"ToScheduled.php.html#32\">App\\States\\WorkOrders\\Transitions\\ToScheduled::__construct<\/a>"],[0,9,"<a href=\"ToScheduled.php.html#46\">App\\States\\WorkOrders\\Transitions\\ToScheduled::handle<\/a>"],[0,1,"<a href=\"ToWorkInProgress.php.html#17\">App\\States\\WorkOrders\\Transitions\\ToWorkInProgress::__construct<\/a>"],[0,1,"<a href=\"ToWorkInProgress.php.html#19\">App\\States\\WorkOrders\\Transitions\\ToWorkInProgress::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

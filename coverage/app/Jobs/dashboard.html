<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Jobs</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Jobs</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Appfolio/IngestAppfolioWorkOrdersJob.php.html#14">App\Jobs\Appfolio\IngestAppfolioWorkOrdersJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/IngestAppfolioWorkOrdersToServiceRequestJob.php.html#14">App\Jobs\Appfolio\IngestAppfolioWorkOrdersToServiceRequestJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/ProcessAppfolioNewWorkOrder.php.html#15">App\Jobs\Appfolio\ProcessAppfolioNewWorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/ProcessAppfolioNewWorkOrderToServiceRequest.php.html#15">App\Jobs\Appfolio\ProcessAppfolioNewWorkOrderToServiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/ServiceRequestStateUpdateJob.php.html#15">App\Jobs\Appfolio\ServiceRequestStateUpdateJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/SyncVendorsJob.php.html#24">App\Jobs\Appfolio\SyncVendorsJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/UpdateStatusInAppfolioWorkOrder.php.html#17">App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/WorkOrderStateUpdateJob.php.html#15">App\Jobs\Appfolio\WorkOrderStateUpdateJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachImageToQuoteTaskJob.php.html#36">App\Jobs\AttachImageToQuoteTaskJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachImageToServiceRequestJob.php.html#32">App\Jobs\AttachImageToServiceRequestJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachImageToWorkOrderTrip.php.html#29">App\Jobs\AttachImageToWorkOrderTrip</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#31">App\Jobs\AttachMediaToWorkOrderJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateVendorPublicApiLog.php.html#15">App\Jobs\CreateVendorPublicApiLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DispatchOnboardingEmail.php.html#22">App\Jobs\DispatchOnboardingEmail</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#25">App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImageResizeJob.php.html#23">App\Jobs\ImageResizeJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/CreateInvoiceLogJob.php.html#18">App\Jobs\Invoice\CreateInvoiceLogJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/UpdateServiceRequestIssueCanceledActivityLogJob.php.html#17">App\Jobs\ServiceRequest\ActivityLog\UpdateServiceRequestIssueCanceledActivityLogJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestImageResizeJob.php.html#23">App\Jobs\ServiceRequestImageResizeJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncWorkOrderDetailsWithExternalJob.php.html#20">App\Jobs\SyncWorkOrderDetailsWithExternalJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateExternalWorkOrderDetails.php.html#21">App\Jobs\UpdateExternalWorkOrderDetails</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#73">App\Jobs\Webhook\ProcessLulaWebhookJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#24">App\Jobs\Webhook\ProcessWebhookJob</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#73">App\Jobs\Webhook\ProcessLulaWebhookJob</a></td><td class="text-right">12882</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#24">App\Jobs\Webhook\ProcessWebhookJob</a></td><td class="text-right">1260</td></tr>
       <tr><td><a href="Appfolio/SyncVendorsJob.php.html#24">App\Jobs\Appfolio\SyncVendorsJob</a></td><td class="text-right">812</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#31">App\Jobs\AttachMediaToWorkOrderJob</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#25">App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="AttachImageToServiceRequestJob.php.html#32">App\Jobs\AttachImageToServiceRequestJob</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="ImageResizeJob.php.html#23">App\Jobs\ImageResizeJob</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ServiceRequestImageResizeJob.php.html#23">App\Jobs\ServiceRequestImageResizeJob</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="UpdateExternalWorkOrderDetails.php.html#21">App\Jobs\UpdateExternalWorkOrderDetails</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Appfolio/UpdateStatusInAppfolioWorkOrder.php.html#17">App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="AttachImageToQuoteTaskJob.php.html#36">App\Jobs\AttachImageToQuoteTaskJob</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="AttachImageToWorkOrderTrip.php.html#29">App\Jobs\AttachImageToWorkOrderTrip</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="DispatchOnboardingEmail.php.html#22">App\Jobs\DispatchOnboardingEmail</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Invoice/CreateInvoiceLogJob.php.html#18">App\Jobs\Invoice\CreateInvoiceLogJob</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="SyncWorkOrderDetailsWithExternalJob.php.html#20">App\Jobs\SyncWorkOrderDetailsWithExternalJob</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Appfolio/ServiceRequestStateUpdateJob.php.html#15">App\Jobs\Appfolio\ServiceRequestStateUpdateJob</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Appfolio/WorkOrderStateUpdateJob.php.html#15">App\Jobs\Appfolio\WorkOrderStateUpdateJob</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/UpdateServiceRequestIssueCanceledActivityLogJob.php.html#17">App\Jobs\ServiceRequest\ActivityLog\UpdateServiceRequestIssueCanceledActivityLogJob</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="CreateVendorPublicApiLog.php.html#15">App\Jobs\CreateVendorPublicApiLog</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Appfolio/IngestAppfolioWorkOrdersJob.php.html#28"><abbr title="App\Jobs\Appfolio\IngestAppfolioWorkOrdersJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/IngestAppfolioWorkOrdersJob.php.html#37"><abbr title="App\Jobs\Appfolio\IngestAppfolioWorkOrdersJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/IngestAppfolioWorkOrdersToServiceRequestJob.php.html#28"><abbr title="App\Jobs\Appfolio\IngestAppfolioWorkOrdersToServiceRequestJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/IngestAppfolioWorkOrdersToServiceRequestJob.php.html#37"><abbr title="App\Jobs\Appfolio\IngestAppfolioWorkOrdersToServiceRequestJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/ProcessAppfolioNewWorkOrder.php.html#40"><abbr title="App\Jobs\Appfolio\ProcessAppfolioNewWorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/ProcessAppfolioNewWorkOrder.php.html#51"><abbr title="App\Jobs\Appfolio\ProcessAppfolioNewWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/ProcessAppfolioNewWorkOrderToServiceRequest.php.html#40"><abbr title="App\Jobs\Appfolio\ProcessAppfolioNewWorkOrderToServiceRequest::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/ProcessAppfolioNewWorkOrderToServiceRequest.php.html#51"><abbr title="App\Jobs\Appfolio\ProcessAppfolioNewWorkOrderToServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/ServiceRequestStateUpdateJob.php.html#34"><abbr title="App\Jobs\Appfolio\ServiceRequestStateUpdateJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/ServiceRequestStateUpdateJob.php.html#44"><abbr title="App\Jobs\Appfolio\ServiceRequestStateUpdateJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/SyncVendorsJob.php.html#33"><abbr title="App\Jobs\Appfolio\SyncVendorsJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/SyncVendorsJob.php.html#43"><abbr title="App\Jobs\Appfolio\SyncVendorsJob::middleware">middleware</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/SyncVendorsJob.php.html#57"><abbr title="App\Jobs\Appfolio\SyncVendorsJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/UpdateStatusInAppfolioWorkOrder.php.html#42"><abbr title="App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/UpdateStatusInAppfolioWorkOrder.php.html#53"><abbr title="App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/UpdateStatusInAppfolioWorkOrder.php.html#111"><abbr title="App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder::toWorkOrderTimezone">toWorkOrderTimezone</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/WorkOrderStateUpdateJob.php.html#34"><abbr title="App\Jobs\Appfolio\WorkOrderStateUpdateJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/WorkOrderStateUpdateJob.php.html#44"><abbr title="App\Jobs\Appfolio\WorkOrderStateUpdateJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachImageToQuoteTaskJob.php.html#77"><abbr title="App\Jobs\AttachImageToQuoteTaskJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachImageToQuoteTaskJob.php.html#98"><abbr title="App\Jobs\AttachImageToQuoteTaskJob::backoff">backoff</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachImageToQuoteTaskJob.php.html#106"><abbr title="App\Jobs\AttachImageToQuoteTaskJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachImageToServiceRequestJob.php.html#56"><abbr title="App\Jobs\AttachImageToServiceRequestJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachImageToServiceRequestJob.php.html#67"><abbr title="App\Jobs\AttachImageToServiceRequestJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachImageToServiceRequestJob.php.html#241"><abbr title="App\Jobs\AttachImageToServiceRequestJob::heicHeifImageConversion">heicHeifImageConversion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachImageToWorkOrderTrip.php.html#33"><abbr title="App\Jobs\AttachImageToWorkOrderTrip::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachImageToWorkOrderTrip.php.html#47"><abbr title="App\Jobs\AttachImageToWorkOrderTrip::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#55"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#66"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#111"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::heicHeifImageConversion">heicHeifImageConversion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#153"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::fetchMedia">fetchMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#158"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::handleImageUpload">handleImageUpload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#205"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::handleVideoUpload">handleVideoUpload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#225"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::uploadVideoThumbnail">uploadVideoThumbnail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#268"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::processOriginalFile">processOriginalFile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#295"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::processThumbnail">processThumbnail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#317"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::logErrorDetails">logErrorDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateVendorPublicApiLog.php.html#22"><abbr title="App\Jobs\CreateVendorPublicApiLog::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateVendorPublicApiLog.php.html#29"><abbr title="App\Jobs\CreateVendorPublicApiLog::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DispatchOnboardingEmail.php.html#36"><abbr title="App\Jobs\DispatchOnboardingEmail::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DispatchOnboardingEmail.php.html#47"><abbr title="App\Jobs\DispatchOnboardingEmail::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DispatchOnboardingEmail.php.html#107"><abbr title="App\Jobs\DispatchOnboardingEmail::minutesToDaysSimple">minutesToDaysSimple</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#43"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#51"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#71"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::createWorkOrderActivityLog">createWorkOrderActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#93"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::createWorkOrderHealthActivityLog">createWorkOrderHealthActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#108"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::fetchWorkOrders">fetchWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#121"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::filterWorkOrdersByViolation">filterWorkOrdersByViolation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#134"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::calculateDuration">calculateDuration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#149"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::processHealthViolation">processHealthViolation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#169"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::isAtRisk">isAtRisk</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#178"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::getAtRiskMessage">getAtRiskMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#188"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::isCritical">isCritical</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#197"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::getIsCriticalMessage">getIsCriticalMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#207"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::extractHealthTrackerClassification">extractHealthTrackerClassification</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#212"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::logWorkOrderActivity">logWorkOrderActivity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImageResizeJob.php.html#41"><abbr title="App\Jobs\ImageResizeJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImageResizeJob.php.html#52"><abbr title="App\Jobs\ImageResizeJob::backoff">backoff</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImageResizeJob.php.html#60"><abbr title="App\Jobs\ImageResizeJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/CreateInvoiceLogJob.php.html#25"><abbr title="App\Jobs\Invoice\CreateInvoiceLogJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/CreateInvoiceLogJob.php.html#35"><abbr title="App\Jobs\Invoice\CreateInvoiceLogJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/UpdateServiceRequestIssueCanceledActivityLogJob.php.html#21"><abbr title="App\Jobs\ServiceRequest\ActivityLog\UpdateServiceRequestIssueCanceledActivityLogJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/UpdateServiceRequestIssueCanceledActivityLogJob.php.html#26"><abbr title="App\Jobs\ServiceRequest\ActivityLog\UpdateServiceRequestIssueCanceledActivityLogJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestImageResizeJob.php.html#41"><abbr title="App\Jobs\ServiceRequestImageResizeJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestImageResizeJob.php.html#52"><abbr title="App\Jobs\ServiceRequestImageResizeJob::backoff">backoff</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestImageResizeJob.php.html#60"><abbr title="App\Jobs\ServiceRequestImageResizeJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncWorkOrderDetailsWithExternalJob.php.html#24"><abbr title="App\Jobs\SyncWorkOrderDetailsWithExternalJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncWorkOrderDetailsWithExternalJob.php.html#31"><abbr title="App\Jobs\SyncWorkOrderDetailsWithExternalJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateExternalWorkOrderDetails.php.html#25"><abbr title="App\Jobs\UpdateExternalWorkOrderDetails::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateExternalWorkOrderDetails.php.html#33"><abbr title="App\Jobs\UpdateExternalWorkOrderDetails::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateExternalWorkOrderDetails.php.html#90"><abbr title="App\Jobs\UpdateExternalWorkOrderDetails::workOrderCancelPayload">workOrderCancelPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateExternalWorkOrderDetails.php.html#100"><abbr title="App\Jobs\UpdateExternalWorkOrderDetails::workOrderDescriptionPayload">workOrderDescriptionPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateExternalWorkOrderDetails.php.html#110"><abbr title="App\Jobs\UpdateExternalWorkOrderDetails::workOrderAccessInfoPayload">workOrderAccessInfoPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateExternalWorkOrderDetails.php.html#124"><abbr title="App\Jobs\UpdateExternalWorkOrderDetails::workOrderResidentInfoPayload">workOrderResidentInfoPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateExternalWorkOrderDetails.php.html#155"><abbr title="App\Jobs\UpdateExternalWorkOrderDetails::workOrderAddressPayload">workOrderAddressPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#78"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#132"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::saveExceptionDetails">saveExceptionDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#152"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::scheduleInProgress">scheduleInProgress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#221"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripScheduleInProgress">toTripScheduleInProgress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#262"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::createServiceLog">createServiceLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#286"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::scheduleWorkOrder">scheduleWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#413"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toWorkOrderSchedule">toWorkOrderSchedule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#439"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toWorkOrderReschedule">toWorkOrderReschedule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#465"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripSchedule">toTripSchedule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#514"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripReSchedule">toTripReSchedule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#568"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::startWorkOrder">startWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#649"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toWorkOrderWorkInProgress">toWorkOrderWorkInProgress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#677"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripWorking">toTripWorking</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#723"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::qualityCheck">qualityCheck</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#809"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripComplete">toTripComplete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#872"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::workComplete">workComplete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#985"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toResolved">toResolved</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1019"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::cancelWorkOrder">cancelWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1081"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toReadyToSchedule">toReadyToSchedule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1141"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripCancel">toTripCancel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1191"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::workPaused">workPaused</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1296"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toWorkOrderPause">toWorkOrderPause</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1327"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripPause">toTripPause</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1357"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::noteAdded">noteAdded</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1409"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::createInvoice">createInvoice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1547"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toWorkOrderQualityCheck">toWorkOrderQualityCheck</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1575"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toWorkOrderClaimPending">toWorkOrderClaimPending</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1601"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripReScheduleDueToPause">toTripReScheduleDueToPause</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1810"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::createNewLulaTrip">createNewLulaTrip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1888"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripEnd">toTripEnd</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#29"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#74"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::updateQuoteDetails">updateQuoteDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#150"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::saveExceptionDetails">saveExceptionDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#165"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::declineQuote">declineQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#196"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::restoreQuote">restoreQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#225"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::approveQuote">approveQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#272"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::sendForApproval">sendForApproval</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Appfolio/SyncVendorsJob.php.html#57"><abbr title="App\Jobs\Appfolio\SyncVendorsJob::handle">handle</abbr></a></td><td class="text-right">650</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#872"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::workComplete">workComplete</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1409"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::createInvoice">createInvoice</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="AttachImageToServiceRequestJob.php.html#67"><abbr title="App\Jobs\AttachImageToServiceRequestJob::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ImageResizeJob.php.html#60"><abbr title="App\Jobs\ImageResizeJob::handle">handle</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ServiceRequestImageResizeJob.php.html#60"><abbr title="App\Jobs\ServiceRequestImageResizeJob::handle">handle</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="AttachImageToQuoteTaskJob.php.html#106"><abbr title="App\Jobs\AttachImageToQuoteTaskJob::handle">handle</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#286"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::scheduleWorkOrder">scheduleWorkOrder</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#74"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::updateQuoteDetails">updateQuoteDetails</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Appfolio/UpdateStatusInAppfolioWorkOrder.php.html#53"><abbr title="App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="AttachImageToWorkOrderTrip.php.html#47"><abbr title="App\Jobs\AttachImageToWorkOrderTrip::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#66"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="DispatchOnboardingEmail.php.html#47"><abbr title="App\Jobs\DispatchOnboardingEmail::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Invoice/CreateInvoiceLogJob.php.html#35"><abbr title="App\Jobs\Invoice\CreateInvoiceLogJob::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#78"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1191"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::workPaused">workPaused</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1601"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripReScheduleDueToPause">toTripReScheduleDueToPause</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#225"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::approveQuote">approveQuote</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="SyncWorkOrderDetailsWithExternalJob.php.html#31"><abbr title="App\Jobs\SyncWorkOrderDetailsWithExternalJob::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#152"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::scheduleInProgress">scheduleInProgress</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AttachImageToServiceRequestJob.php.html#241"><abbr title="App\Jobs\AttachImageToServiceRequestJob::heicHeifImageConversion">heicHeifImageConversion</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#111"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::heicHeifImageConversion">heicHeifImageConversion</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#51"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UpdateExternalWorkOrderDetails.php.html#33"><abbr title="App\Jobs\UpdateExternalWorkOrderDetails::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1019"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::cancelWorkOrder">cancelWorkOrder</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#29"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#165"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::declineQuote">declineQuote</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#196"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::restoreQuote">restoreQuote</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#568"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::startWorkOrder">startWorkOrder</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1888"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripEnd">toTripEnd</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Appfolio/ServiceRequestStateUpdateJob.php.html#44"><abbr title="App\Jobs\Appfolio\ServiceRequestStateUpdateJob::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/WorkOrderStateUpdateJob.php.html#44"><abbr title="App\Jobs\Appfolio\WorkOrderStateUpdateJob::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#205"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::handleVideoUpload">handleVideoUpload</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#268"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::processOriginalFile">processOriginalFile</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#149"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::processHealthViolation">processHealthViolation</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#169"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::isAtRisk">isAtRisk</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#212"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::logWorkOrderActivity">logWorkOrderActivity</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/UpdateServiceRequestIssueCanceledActivityLogJob.php.html#26"><abbr title="App\Jobs\ServiceRequest\ActivityLog\UpdateServiceRequestIssueCanceledActivityLogJob::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#262"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::createServiceLog">createServiceLog</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#723"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::qualityCheck">qualityCheck</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#809"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripComplete">toTripComplete</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1810"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::createNewLulaTrip">createNewLulaTrip</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Webhook/ProcessWebhookJob.php.html#272"><abbr title="App\Jobs\Webhook\ProcessWebhookJob::sendForApproval">sendForApproval</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Appfolio/SyncVendorsJob.php.html#43"><abbr title="App\Jobs\Appfolio\SyncVendorsJob::middleware">middleware</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Appfolio/UpdateStatusInAppfolioWorkOrder.php.html#111"><abbr title="App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder::toWorkOrderTimezone">toWorkOrderTimezone</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#158"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::handleImageUpload">handleImageUpload</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#225"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::uploadVideoThumbnail">uploadVideoThumbnail</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AttachMediaToWorkOrderJob.php.html#295"><abbr title="App\Jobs\AttachMediaToWorkOrderJob::processThumbnail">processThumbnail</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CreateVendorPublicApiLog.php.html#29"><abbr title="App\Jobs\CreateVendorPublicApiLog::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="HealthScore/UpdateWorkOrderHealthScoreJob.php.html#134"><abbr title="App\Jobs\HealthScore\UpdateWorkOrderHealthScoreJob::calculateDuration">calculateDuration</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateExternalWorkOrderDetails.php.html#124"><abbr title="App\Jobs\UpdateExternalWorkOrderDetails::workOrderResidentInfoPayload">workOrderResidentInfoPayload</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1141"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripCancel">toTripCancel</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Webhook/ProcessLulaWebhookJob.php.html#1327"><abbr title="App\Jobs\Webhook\ProcessLulaWebhookJob::toTripPause">toTripPause</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Thu Jun 26 15:40:22 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([23,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([111,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"Appfolio\/IngestAppfolioWorkOrdersJob.php.html#14\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersJob<\/a>"],[0,2,"<a href=\"Appfolio\/IngestAppfolioWorkOrdersToServiceRequestJob.php.html#14\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersToServiceRequestJob<\/a>"],[0,2,"<a href=\"Appfolio\/ProcessAppfolioNewWorkOrder.php.html#15\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrder<\/a>"],[0,2,"<a href=\"Appfolio\/ProcessAppfolioNewWorkOrderToServiceRequest.php.html#15\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrderToServiceRequest<\/a>"],[0,4,"<a href=\"Appfolio\/ServiceRequestStateUpdateJob.php.html#15\">App\\Jobs\\Appfolio\\ServiceRequestStateUpdateJob<\/a>"],[0,28,"<a href=\"Appfolio\/SyncVendorsJob.php.html#24\">App\\Jobs\\Appfolio\\SyncVendorsJob<\/a>"],[0,11,"<a href=\"Appfolio\/UpdateStatusInAppfolioWorkOrder.php.html#17\">App\\Jobs\\Appfolio\\UpdateStatusInAppfolioWorkOrder<\/a>"],[0,4,"<a href=\"Appfolio\/WorkOrderStateUpdateJob.php.html#15\">App\\Jobs\\Appfolio\\WorkOrderStateUpdateJob<\/a>"],[0,11,"<a href=\"AttachImageToQuoteTaskJob.php.html#36\">App\\Jobs\\AttachImageToQuoteTaskJob<\/a>"],[0,20,"<a href=\"AttachImageToServiceRequestJob.php.html#32\">App\\Jobs\\AttachImageToServiceRequestJob<\/a>"],[0,9,"<a href=\"AttachImageToWorkOrderTrip.php.html#29\">App\\Jobs\\AttachImageToWorkOrderTrip<\/a>"],[0,27,"<a href=\"AttachMediaToWorkOrderJob.php.html#31\">App\\Jobs\\AttachMediaToWorkOrderJob<\/a>"],[0,3,"<a href=\"CreateVendorPublicApiLog.php.html#15\">App\\Jobs\\CreateVendorPublicApiLog<\/a>"],[0,9,"<a href=\"DispatchOnboardingEmail.php.html#22\">App\\Jobs\\DispatchOnboardingEmail<\/a>"],[0,25,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#25\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob<\/a>"],[0,14,"<a href=\"ImageResizeJob.php.html#23\">App\\Jobs\\ImageResizeJob<\/a>"],[0,8,"<a href=\"Invoice\/CreateInvoiceLogJob.php.html#18\">App\\Jobs\\Invoice\\CreateInvoiceLogJob<\/a>"],[0,4,"<a href=\"ServiceRequest\/ActivityLog\/UpdateServiceRequestIssueCanceledActivityLogJob.php.html#17\">App\\Jobs\\ServiceRequest\\ActivityLog\\UpdateServiceRequestIssueCanceledActivityLogJob<\/a>"],[0,14,"<a href=\"ServiceRequestImageResizeJob.php.html#23\">App\\Jobs\\ServiceRequestImageResizeJob<\/a>"],[0,7,"<a href=\"SyncWorkOrderDetailsWithExternalJob.php.html#20\">App\\Jobs\\SyncWorkOrderDetailsWithExternalJob<\/a>"],[0,12,"<a href=\"UpdateExternalWorkOrderDetails.php.html#21\">App\\Jobs\\UpdateExternalWorkOrderDetails<\/a>"],[0,113,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#73\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob<\/a>"],[0,35,"<a href=\"Webhook\/ProcessWebhookJob.php.html#24\">App\\Jobs\\Webhook\\ProcessWebhookJob<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Appfolio\/IngestAppfolioWorkOrdersJob.php.html#28\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersJob::__construct<\/a>"],[0,1,"<a href=\"Appfolio\/IngestAppfolioWorkOrdersJob.php.html#37\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersJob::handle<\/a>"],[0,1,"<a href=\"Appfolio\/IngestAppfolioWorkOrdersToServiceRequestJob.php.html#28\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersToServiceRequestJob::__construct<\/a>"],[0,1,"<a href=\"Appfolio\/IngestAppfolioWorkOrdersToServiceRequestJob.php.html#37\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersToServiceRequestJob::handle<\/a>"],[0,1,"<a href=\"Appfolio\/ProcessAppfolioNewWorkOrder.php.html#40\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrder::__construct<\/a>"],[0,1,"<a href=\"Appfolio\/ProcessAppfolioNewWorkOrder.php.html#51\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrder::handle<\/a>"],[0,1,"<a href=\"Appfolio\/ProcessAppfolioNewWorkOrderToServiceRequest.php.html#40\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrderToServiceRequest::__construct<\/a>"],[0,1,"<a href=\"Appfolio\/ProcessAppfolioNewWorkOrderToServiceRequest.php.html#51\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrderToServiceRequest::handle<\/a>"],[0,1,"<a href=\"Appfolio\/ServiceRequestStateUpdateJob.php.html#34\">App\\Jobs\\Appfolio\\ServiceRequestStateUpdateJob::__construct<\/a>"],[0,3,"<a href=\"Appfolio\/ServiceRequestStateUpdateJob.php.html#44\">App\\Jobs\\Appfolio\\ServiceRequestStateUpdateJob::handle<\/a>"],[0,1,"<a href=\"Appfolio\/SyncVendorsJob.php.html#33\">App\\Jobs\\Appfolio\\SyncVendorsJob::__construct<\/a>"],[0,2,"<a href=\"Appfolio\/SyncVendorsJob.php.html#43\">App\\Jobs\\Appfolio\\SyncVendorsJob::middleware<\/a>"],[0,25,"<a href=\"Appfolio\/SyncVendorsJob.php.html#57\">App\\Jobs\\Appfolio\\SyncVendorsJob::handle<\/a>"],[0,1,"<a href=\"Appfolio\/UpdateStatusInAppfolioWorkOrder.php.html#42\">App\\Jobs\\Appfolio\\UpdateStatusInAppfolioWorkOrder::__construct<\/a>"],[0,8,"<a href=\"Appfolio\/UpdateStatusInAppfolioWorkOrder.php.html#53\">App\\Jobs\\Appfolio\\UpdateStatusInAppfolioWorkOrder::handle<\/a>"],[0,2,"<a href=\"Appfolio\/UpdateStatusInAppfolioWorkOrder.php.html#111\">App\\Jobs\\Appfolio\\UpdateStatusInAppfolioWorkOrder::toWorkOrderTimezone<\/a>"],[0,1,"<a href=\"Appfolio\/WorkOrderStateUpdateJob.php.html#34\">App\\Jobs\\Appfolio\\WorkOrderStateUpdateJob::__construct<\/a>"],[0,3,"<a href=\"Appfolio\/WorkOrderStateUpdateJob.php.html#44\">App\\Jobs\\Appfolio\\WorkOrderStateUpdateJob::handle<\/a>"],[0,1,"<a href=\"AttachImageToQuoteTaskJob.php.html#77\">App\\Jobs\\AttachImageToQuoteTaskJob::__construct<\/a>"],[0,1,"<a href=\"AttachImageToQuoteTaskJob.php.html#98\">App\\Jobs\\AttachImageToQuoteTaskJob::backoff<\/a>"],[0,9,"<a href=\"AttachImageToQuoteTaskJob.php.html#106\">App\\Jobs\\AttachImageToQuoteTaskJob::handle<\/a>"],[0,1,"<a href=\"AttachImageToServiceRequestJob.php.html#56\">App\\Jobs\\AttachImageToServiceRequestJob::__construct<\/a>"],[0,14,"<a href=\"AttachImageToServiceRequestJob.php.html#67\">App\\Jobs\\AttachImageToServiceRequestJob::handle<\/a>"],[0,5,"<a href=\"AttachImageToServiceRequestJob.php.html#241\">App\\Jobs\\AttachImageToServiceRequestJob::heicHeifImageConversion<\/a>"],[0,1,"<a href=\"AttachImageToWorkOrderTrip.php.html#33\">App\\Jobs\\AttachImageToWorkOrderTrip::__construct<\/a>"],[0,8,"<a href=\"AttachImageToWorkOrderTrip.php.html#47\">App\\Jobs\\AttachImageToWorkOrderTrip::handle<\/a>"],[0,1,"<a href=\"AttachMediaToWorkOrderJob.php.html#55\">App\\Jobs\\AttachMediaToWorkOrderJob::__construct<\/a>"],[0,7,"<a href=\"AttachMediaToWorkOrderJob.php.html#66\">App\\Jobs\\AttachMediaToWorkOrderJob::handle<\/a>"],[0,5,"<a href=\"AttachMediaToWorkOrderJob.php.html#111\">App\\Jobs\\AttachMediaToWorkOrderJob::heicHeifImageConversion<\/a>"],[0,1,"<a href=\"AttachMediaToWorkOrderJob.php.html#153\">App\\Jobs\\AttachMediaToWorkOrderJob::fetchMedia<\/a>"],[0,2,"<a href=\"AttachMediaToWorkOrderJob.php.html#158\">App\\Jobs\\AttachMediaToWorkOrderJob::handleImageUpload<\/a>"],[0,3,"<a href=\"AttachMediaToWorkOrderJob.php.html#205\">App\\Jobs\\AttachMediaToWorkOrderJob::handleVideoUpload<\/a>"],[0,2,"<a href=\"AttachMediaToWorkOrderJob.php.html#225\">App\\Jobs\\AttachMediaToWorkOrderJob::uploadVideoThumbnail<\/a>"],[0,3,"<a href=\"AttachMediaToWorkOrderJob.php.html#268\">App\\Jobs\\AttachMediaToWorkOrderJob::processOriginalFile<\/a>"],[0,2,"<a href=\"AttachMediaToWorkOrderJob.php.html#295\">App\\Jobs\\AttachMediaToWorkOrderJob::processThumbnail<\/a>"],[0,1,"<a href=\"AttachMediaToWorkOrderJob.php.html#317\">App\\Jobs\\AttachMediaToWorkOrderJob::logErrorDetails<\/a>"],[0,1,"<a href=\"CreateVendorPublicApiLog.php.html#22\">App\\Jobs\\CreateVendorPublicApiLog::__construct<\/a>"],[0,2,"<a href=\"CreateVendorPublicApiLog.php.html#29\">App\\Jobs\\CreateVendorPublicApiLog::handle<\/a>"],[0,1,"<a href=\"DispatchOnboardingEmail.php.html#36\">App\\Jobs\\DispatchOnboardingEmail::__construct<\/a>"],[0,7,"<a href=\"DispatchOnboardingEmail.php.html#47\">App\\Jobs\\DispatchOnboardingEmail::handle<\/a>"],[0,1,"<a href=\"DispatchOnboardingEmail.php.html#107\">App\\Jobs\\DispatchOnboardingEmail::minutesToDaysSimple<\/a>"],[0,1,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#43\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::__construct<\/a>"],[0,5,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#51\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::handle<\/a>"],[0,1,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#71\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::createWorkOrderActivityLog<\/a>"],[0,1,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#93\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::createWorkOrderHealthActivityLog<\/a>"],[0,1,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#108\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::fetchWorkOrders<\/a>"],[0,1,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#121\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::filterWorkOrdersByViolation<\/a>"],[0,2,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#134\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::calculateDuration<\/a>"],[0,3,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#149\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::processHealthViolation<\/a>"],[0,3,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#169\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::isAtRisk<\/a>"],[0,1,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#178\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::getAtRiskMessage<\/a>"],[0,1,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#188\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::isCritical<\/a>"],[0,1,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#197\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::getIsCriticalMessage<\/a>"],[0,1,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#207\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::extractHealthTrackerClassification<\/a>"],[0,3,"<a href=\"HealthScore\/UpdateWorkOrderHealthScoreJob.php.html#212\">App\\Jobs\\HealthScore\\UpdateWorkOrderHealthScoreJob::logWorkOrderActivity<\/a>"],[0,1,"<a href=\"ImageResizeJob.php.html#41\">App\\Jobs\\ImageResizeJob::__construct<\/a>"],[0,1,"<a href=\"ImageResizeJob.php.html#52\">App\\Jobs\\ImageResizeJob::backoff<\/a>"],[0,12,"<a href=\"ImageResizeJob.php.html#60\">App\\Jobs\\ImageResizeJob::handle<\/a>"],[0,1,"<a href=\"Invoice\/CreateInvoiceLogJob.php.html#25\">App\\Jobs\\Invoice\\CreateInvoiceLogJob::__construct<\/a>"],[0,7,"<a href=\"Invoice\/CreateInvoiceLogJob.php.html#35\">App\\Jobs\\Invoice\\CreateInvoiceLogJob::handle<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/UpdateServiceRequestIssueCanceledActivityLogJob.php.html#21\">App\\Jobs\\ServiceRequest\\ActivityLog\\UpdateServiceRequestIssueCanceledActivityLogJob::__construct<\/a>"],[0,3,"<a href=\"ServiceRequest\/ActivityLog\/UpdateServiceRequestIssueCanceledActivityLogJob.php.html#26\">App\\Jobs\\ServiceRequest\\ActivityLog\\UpdateServiceRequestIssueCanceledActivityLogJob::handle<\/a>"],[0,1,"<a href=\"ServiceRequestImageResizeJob.php.html#41\">App\\Jobs\\ServiceRequestImageResizeJob::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestImageResizeJob.php.html#52\">App\\Jobs\\ServiceRequestImageResizeJob::backoff<\/a>"],[0,12,"<a href=\"ServiceRequestImageResizeJob.php.html#60\">App\\Jobs\\ServiceRequestImageResizeJob::handle<\/a>"],[0,1,"<a href=\"SyncWorkOrderDetailsWithExternalJob.php.html#24\">App\\Jobs\\SyncWorkOrderDetailsWithExternalJob::__construct<\/a>"],[0,6,"<a href=\"SyncWorkOrderDetailsWithExternalJob.php.html#31\">App\\Jobs\\SyncWorkOrderDetailsWithExternalJob::handle<\/a>"],[0,1,"<a href=\"UpdateExternalWorkOrderDetails.php.html#25\">App\\Jobs\\UpdateExternalWorkOrderDetails::__construct<\/a>"],[0,5,"<a href=\"UpdateExternalWorkOrderDetails.php.html#33\">App\\Jobs\\UpdateExternalWorkOrderDetails::handle<\/a>"],[0,1,"<a href=\"UpdateExternalWorkOrderDetails.php.html#90\">App\\Jobs\\UpdateExternalWorkOrderDetails::workOrderCancelPayload<\/a>"],[0,1,"<a href=\"UpdateExternalWorkOrderDetails.php.html#100\">App\\Jobs\\UpdateExternalWorkOrderDetails::workOrderDescriptionPayload<\/a>"],[0,1,"<a href=\"UpdateExternalWorkOrderDetails.php.html#110\">App\\Jobs\\UpdateExternalWorkOrderDetails::workOrderAccessInfoPayload<\/a>"],[0,2,"<a href=\"UpdateExternalWorkOrderDetails.php.html#124\">App\\Jobs\\UpdateExternalWorkOrderDetails::workOrderResidentInfoPayload<\/a>"],[0,1,"<a href=\"UpdateExternalWorkOrderDetails.php.html#155\">App\\Jobs\\UpdateExternalWorkOrderDetails::workOrderAddressPayload<\/a>"],[0,7,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#78\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::handle<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#132\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::saveExceptionDetails<\/a>"],[0,6,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#152\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::scheduleInProgress<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#221\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toTripScheduleInProgress<\/a>"],[0,3,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#262\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::createServiceLog<\/a>"],[0,9,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#286\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::scheduleWorkOrder<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#413\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toWorkOrderSchedule<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#439\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toWorkOrderReschedule<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#465\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toTripSchedule<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#514\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toTripReSchedule<\/a>"],[0,4,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#568\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::startWorkOrder<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#649\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toWorkOrderWorkInProgress<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#677\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toTripWorking<\/a>"],[0,3,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#723\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::qualityCheck<\/a>"],[0,3,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#809\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toTripComplete<\/a>"],[0,18,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#872\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::workComplete<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#985\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toResolved<\/a>"],[0,5,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1019\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::cancelWorkOrder<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1081\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toReadyToSchedule<\/a>"],[0,2,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1141\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toTripCancel<\/a>"],[0,7,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1191\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::workPaused<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1296\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toWorkOrderPause<\/a>"],[0,2,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1327\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toTripPause<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1357\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::noteAdded<\/a>"],[0,16,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1409\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::createInvoice<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1547\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toWorkOrderQualityCheck<\/a>"],[0,1,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1575\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toWorkOrderClaimPending<\/a>"],[0,7,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1601\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toTripReScheduleDueToPause<\/a>"],[0,3,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1810\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::createNewLulaTrip<\/a>"],[0,4,"<a href=\"Webhook\/ProcessLulaWebhookJob.php.html#1888\">App\\Jobs\\Webhook\\ProcessLulaWebhookJob::toTripEnd<\/a>"],[0,5,"<a href=\"Webhook\/ProcessWebhookJob.php.html#29\">App\\Jobs\\Webhook\\ProcessWebhookJob::handle<\/a>"],[0,9,"<a href=\"Webhook\/ProcessWebhookJob.php.html#74\">App\\Jobs\\Webhook\\ProcessWebhookJob::updateQuoteDetails<\/a>"],[0,1,"<a href=\"Webhook\/ProcessWebhookJob.php.html#150\">App\\Jobs\\Webhook\\ProcessWebhookJob::saveExceptionDetails<\/a>"],[0,5,"<a href=\"Webhook\/ProcessWebhookJob.php.html#165\">App\\Jobs\\Webhook\\ProcessWebhookJob::declineQuote<\/a>"],[0,5,"<a href=\"Webhook\/ProcessWebhookJob.php.html#196\">App\\Jobs\\Webhook\\ProcessWebhookJob::restoreQuote<\/a>"],[0,7,"<a href=\"Webhook\/ProcessWebhookJob.php.html#225\">App\\Jobs\\Webhook\\ProcessWebhookJob::approveQuote<\/a>"],[0,3,"<a href=\"Webhook\/ProcessWebhookJob.php.html#272\">App\\Jobs\\Webhook\\ProcessWebhookJob::sendForApproval<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

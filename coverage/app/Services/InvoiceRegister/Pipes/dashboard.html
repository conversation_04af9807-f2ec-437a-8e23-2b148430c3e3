<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Services/InvoiceRegister/Pipes</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="../index.html">InvoiceRegister</a></li>
         <li class="breadcrumb-item"><a href="index.html">Pipes</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CreateOrUpdateInvoice.php.html#12">App\Services\InvoiceRegister\Pipes\CreateOrUpdateInvoice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateMaterials.php.html#10">App\Services\InvoiceRegister\Pipes\UpdateMaterials</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateOrCreateInvoiceLineItems.php.html#28">App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateQuoteTasks.php.html#11">App\Services\InvoiceRegister\Pipes\UpdateQuoteTasks</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateServiceCalls.php.html#11">App\Services\InvoiceRegister\Pipes\UpdateServiceCalls</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="UpdateOrCreateInvoiceLineItems.php.html#28">App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems</a></td><td class="text-right">5852</td></tr>
       <tr><td><a href="CreateOrUpdateInvoice.php.html#12">App\Services\InvoiceRegister\Pipes\CreateOrUpdateInvoice</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CreateOrUpdateInvoice.php.html#14"><abbr title="App\Services\InvoiceRegister\Pipes\CreateOrUpdateInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateMaterials.php.html#12"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateMaterials::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateOrCreateInvoiceLineItems.php.html#30"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateOrCreateInvoiceLineItems.php.html#262"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::createOrUpdateSubsidiaries">createOrUpdateSubsidiaries</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateOrCreateInvoiceLineItems.php.html#370"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::validateMedia">validateMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateQuoteTasks.php.html#13"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateQuoteTasks::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateServiceCalls.php.html#13"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateServiceCalls::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="UpdateOrCreateInvoiceLineItems.php.html#30"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::handle">handle</abbr></a></td><td class="text-right">1640</td></tr>
       <tr><td><a href="UpdateOrCreateInvoiceLineItems.php.html#262"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::createOrUpdateSubsidiaries">createOrUpdateSubsidiaries</abbr></a></td><td class="text-right">812</td></tr>
       <tr><td><a href="UpdateOrCreateInvoiceLineItems.php.html#370"><abbr title="App\Services\InvoiceRegister\Pipes\UpdateOrCreateInvoiceLineItems::validateMedia">validateMedia</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CreateOrUpdateInvoice.php.html#14"><abbr title="App\Services\InvoiceRegister\Pipes\CreateOrUpdateInvoice::handle">handle</abbr></a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([5,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([7,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"CreateOrUpdateInvoice.php.html#12\">App\\Services\\InvoiceRegister\\Pipes\\CreateOrUpdateInvoice<\/a>"],[0,1,"<a href=\"UpdateMaterials.php.html#10\">App\\Services\\InvoiceRegister\\Pipes\\UpdateMaterials<\/a>"],[0,76,"<a href=\"UpdateOrCreateInvoiceLineItems.php.html#28\">App\\Services\\InvoiceRegister\\Pipes\\UpdateOrCreateInvoiceLineItems<\/a>"],[0,1,"<a href=\"UpdateQuoteTasks.php.html#11\">App\\Services\\InvoiceRegister\\Pipes\\UpdateQuoteTasks<\/a>"],[0,1,"<a href=\"UpdateServiceCalls.php.html#11\">App\\Services\\InvoiceRegister\\Pipes\\UpdateServiceCalls<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"CreateOrUpdateInvoice.php.html#14\">App\\Services\\InvoiceRegister\\Pipes\\CreateOrUpdateInvoice::handle<\/a>"],[0,1,"<a href=\"UpdateMaterials.php.html#12\">App\\Services\\InvoiceRegister\\Pipes\\UpdateMaterials::handle<\/a>"],[0,40,"<a href=\"UpdateOrCreateInvoiceLineItems.php.html#30\">App\\Services\\InvoiceRegister\\Pipes\\UpdateOrCreateInvoiceLineItems::handle<\/a>"],[0,28,"<a href=\"UpdateOrCreateInvoiceLineItems.php.html#262\">App\\Services\\InvoiceRegister\\Pipes\\UpdateOrCreateInvoiceLineItems::createOrUpdateSubsidiaries<\/a>"],[0,8,"<a href=\"UpdateOrCreateInvoiceLineItems.php.html#370\">App\\Services\\InvoiceRegister\\Pipes\\UpdateOrCreateInvoiceLineItems::validateMedia<\/a>"],[0,1,"<a href=\"UpdateQuoteTasks.php.html#13\">App\\Services\\InvoiceRegister\\Pipes\\UpdateQuoteTasks::handle<\/a>"],[0,1,"<a href=\"UpdateServiceCalls.php.html#13\">App\\Services\\InvoiceRegister\\Pipes\\UpdateServiceCalls::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

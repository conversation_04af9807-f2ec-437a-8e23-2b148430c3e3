<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Services/Appfolio</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="index.html">Appfolio</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#37">App\Services\Appfolio\AppfolioForServiceRequestService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#36">App\Services\Appfolio\AppfolioService</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AppfolioService.php.html#36">App\Services\Appfolio\AppfolioService</a></td><td class="text-right">28730</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#37">App\Services\Appfolio\AppfolioForServiceRequestService</a></td><td class="text-right">22350</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#93"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#108"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::get">get</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#132"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::post">post</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#152"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::patch">patch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#171"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::postAttachment">postAttachment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#196"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::propertyDetails">propertyDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#222"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::unitDetails">unitDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#249"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getTenants">getTenants</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#281"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::tenantDetails">tenantDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#305"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::ingestNewWorkOrdersToServiceRequests">ingestNewWorkOrdersToServiceRequests</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#403"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::processNewWorkOrder">processNewWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#505"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::fetchAdditionalDataFromScrapper">fetchAdditionalDataFromScrapper</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#568"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatServiceRequestPayload">formatServiceRequestPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#619"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addSkippedWorkOrders">addSkippedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#639"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getPaginatedWorkOrders">getPaginatedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#671"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::updateWorkOrder">updateWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#682"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getVendors">getVendors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#710"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::processNewWorkOrders">processNewWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#808"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::checkIntegration">checkIntegration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#818"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::connect">connect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#835"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::responseFormatter">responseFormatter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#857"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::logApiResponse">logApiResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#945"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getAppfolioWorkOrders">getAppfolioWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#962"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getEntityListByUuid">getEntityListByUuid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#987"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::createNewServiceRequest">createNewServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1041"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addAppfolioApiWorkOrderLog">addAppfolioApiWorkOrderLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1069"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatPropertyInfo">formatPropertyInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1129"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatTenantData">formatTenantData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1146"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatPhotos">formatPhotos</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1165"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatTenantInput">formatTenantInput</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1176"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addUnAssignedWorkOrders">addUnAssignedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1212"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addExistingWorkOrders">addExistingWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1248"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addAppfolioIntegrationSkippedWorkOrders">addAppfolioIntegrationSkippedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#92"><abbr title="App\Services\Appfolio\AppfolioService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#107"><abbr title="App\Services\Appfolio\AppfolioService::get">get</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#131"><abbr title="App\Services\Appfolio\AppfolioService::post">post</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#151"><abbr title="App\Services\Appfolio\AppfolioService::patch">patch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#170"><abbr title="App\Services\Appfolio\AppfolioService::postAttachment">postAttachment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#190"><abbr title="App\Services\Appfolio\AppfolioService::createNotes">createNotes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#219"><abbr title="App\Services\Appfolio\AppfolioService::propertyDetails">propertyDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#245"><abbr title="App\Services\Appfolio\AppfolioService::unitDetails">unitDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#272"><abbr title="App\Services\Appfolio\AppfolioService::getTenants">getTenants</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#304"><abbr title="App\Services\Appfolio\AppfolioService::tenantDetails">tenantDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#327"><abbr title="App\Services\Appfolio\AppfolioService::ingestNewWorkOrders">ingestNewWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#417"><abbr title="App\Services\Appfolio\AppfolioService::processNewWorkOrder">processNewWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#574"><abbr title="App\Services\Appfolio\AppfolioService::fetchAdditionalDataFromScrapper">fetchAdditionalDataFromScrapper</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#637"><abbr title="App\Services\Appfolio\AppfolioService::formatWorkOrderPayload">formatWorkOrderPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#687"><abbr title="App\Services\Appfolio\AppfolioService::addSkippedWorkOrders">addSkippedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#707"><abbr title="App\Services\Appfolio\AppfolioService::getPaginatedWorkOrders">getPaginatedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#739"><abbr title="App\Services\Appfolio\AppfolioService::updateWorkOrder">updateWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#750"><abbr title="App\Services\Appfolio\AppfolioService::getVendors">getVendors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#777"><abbr title="App\Services\Appfolio\AppfolioService::checkIntegration">checkIntegration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#787"><abbr title="App\Services\Appfolio\AppfolioService::connect">connect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#804"><abbr title="App\Services\Appfolio\AppfolioService::responseFormatter">responseFormatter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#826"><abbr title="App\Services\Appfolio\AppfolioService::logApiResponse">logApiResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#914"><abbr title="App\Services\Appfolio\AppfolioService::getWorkOrders">getWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#931"><abbr title="App\Services\Appfolio\AppfolioService::processNewWorkOrders">processNewWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1022"><abbr title="App\Services\Appfolio\AppfolioService::getEntityListByUuid">getEntityListByUuid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1045"><abbr title="App\Services\Appfolio\AppfolioService::syncExistingWorkOrders">syncExistingWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1088"><abbr title="App\Services\Appfolio\AppfolioService::validateAndUpdateJobs">validateAndUpdateJobs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1098"><abbr title="App\Services\Appfolio\AppfolioService::validatePropertyLocation">validatePropertyLocation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1111"><abbr title="App\Services\Appfolio\AppfolioService::requestNewWorkOrder">requestNewWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1165"><abbr title="App\Services\Appfolio\AppfolioService::addAppfolioApiWorkOrderLog">addAppfolioApiWorkOrderLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1191"><abbr title="App\Services\Appfolio\AppfolioService::findServiceCategory">findServiceCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1209"><abbr title="App\Services\Appfolio\AppfolioService::formatPropertyInfo">formatPropertyInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1269"><abbr title="App\Services\Appfolio\AppfolioService::formatTenantData">formatTenantData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1286"><abbr title="App\Services\Appfolio\AppfolioService::formatPhotos">formatPhotos</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1305"><abbr title="App\Services\Appfolio\AppfolioService::formatTenantInput">formatTenantInput</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1316"><abbr title="App\Services\Appfolio\AppfolioService::addUnAssignedWorkOrders">addUnAssignedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1352"><abbr title="App\Services\Appfolio\AppfolioService::addExistingWorkOrders">addExistingWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AppfolioService.php.html#1388"><abbr title="App\Services\Appfolio\AppfolioService::addAppfolioIntegrationSkippedWorkOrders">addAppfolioIntegrationSkippedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AppfolioService.php.html#417"><abbr title="App\Services\Appfolio\AppfolioService::processNewWorkOrder">processNewWorkOrder</abbr></a></td><td class="text-right">380</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#857"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::logApiResponse">logApiResponse</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1069"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatPropertyInfo">formatPropertyInfo</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="AppfolioService.php.html#826"><abbr title="App\Services\Appfolio\AppfolioService::logApiResponse">logApiResponse</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="AppfolioService.php.html#1209"><abbr title="App\Services\Appfolio\AppfolioService::formatPropertyInfo">formatPropertyInfo</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#710"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::processNewWorkOrders">processNewWorkOrders</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="AppfolioService.php.html#931"><abbr title="App\Services\Appfolio\AppfolioService::processNewWorkOrders">processNewWorkOrders</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#403"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::processNewWorkOrder">processNewWorkOrder</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="AppfolioService.php.html#637"><abbr title="App\Services\Appfolio\AppfolioService::formatWorkOrderPayload">formatWorkOrderPayload</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1176"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addUnAssignedWorkOrders">addUnAssignedWorkOrders</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1212"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addExistingWorkOrders">addExistingWorkOrders</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AppfolioService.php.html#1316"><abbr title="App\Services\Appfolio\AppfolioService::addUnAssignedWorkOrders">addUnAssignedWorkOrders</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AppfolioService.php.html#1352"><abbr title="App\Services\Appfolio\AppfolioService::addExistingWorkOrders">addExistingWorkOrders</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#305"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::ingestNewWorkOrdersToServiceRequests">ingestNewWorkOrdersToServiceRequests</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#505"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::fetchAdditionalDataFromScrapper">fetchAdditionalDataFromScrapper</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#568"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatServiceRequestPayload">formatServiceRequestPayload</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#835"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::responseFormatter">responseFormatter</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1129"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatTenantData">formatTenantData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AppfolioService.php.html#327"><abbr title="App\Services\Appfolio\AppfolioService::ingestNewWorkOrders">ingestNewWorkOrders</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AppfolioService.php.html#574"><abbr title="App\Services\Appfolio\AppfolioService::fetchAdditionalDataFromScrapper">fetchAdditionalDataFromScrapper</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AppfolioService.php.html#804"><abbr title="App\Services\Appfolio\AppfolioService::responseFormatter">responseFormatter</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AppfolioService.php.html#1269"><abbr title="App\Services\Appfolio\AppfolioService::formatTenantData">formatTenantData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#249"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getTenants">getTenants</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#639"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getPaginatedWorkOrders">getPaginatedWorkOrders</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#987"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::createNewServiceRequest">createNewServiceRequest</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AppfolioService.php.html#272"><abbr title="App\Services\Appfolio\AppfolioService::getTenants">getTenants</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AppfolioService.php.html#707"><abbr title="App\Services\Appfolio\AppfolioService::getPaginatedWorkOrders">getPaginatedWorkOrders</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AppfolioService.php.html#1111"><abbr title="App\Services\Appfolio\AppfolioService::requestNewWorkOrder">requestNewWorkOrder</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#108"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::get">get</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#682"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getVendors">getVendors</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#808"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::checkIntegration">checkIntegration</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#945"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getAppfolioWorkOrders">getAppfolioWorkOrders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#962"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::getEntityListByUuid">getEntityListByUuid</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1146"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatPhotos">formatPhotos</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1165"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::formatTenantInput">formatTenantInput</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioService.php.html#107"><abbr title="App\Services\Appfolio\AppfolioService::get">get</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioService.php.html#750"><abbr title="App\Services\Appfolio\AppfolioService::getVendors">getVendors</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioService.php.html#777"><abbr title="App\Services\Appfolio\AppfolioService::checkIntegration">checkIntegration</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioService.php.html#914"><abbr title="App\Services\Appfolio\AppfolioService::getWorkOrders">getWorkOrders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioService.php.html#1022"><abbr title="App\Services\Appfolio\AppfolioService::getEntityListByUuid">getEntityListByUuid</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioService.php.html#1045"><abbr title="App\Services\Appfolio\AppfolioService::syncExistingWorkOrders">syncExistingWorkOrders</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioService.php.html#1191"><abbr title="App\Services\Appfolio\AppfolioService::findServiceCategory">findServiceCategory</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioService.php.html#1286"><abbr title="App\Services\Appfolio\AppfolioService::formatPhotos">formatPhotos</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioService.php.html#1305"><abbr title="App\Services\Appfolio\AppfolioService::formatTenantInput">formatTenantInput</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#171"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::postAttachment">postAttachment</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#196"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::propertyDetails">propertyDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#222"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::unitDetails">unitDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#281"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::tenantDetails">tenantDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#619"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addSkippedWorkOrders">addSkippedWorkOrders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioForServiceRequestService.php.html#1041"><abbr title="App\Services\Appfolio\AppfolioForServiceRequestService::addAppfolioApiWorkOrderLog">addAppfolioApiWorkOrderLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioService.php.html#170"><abbr title="App\Services\Appfolio\AppfolioService::postAttachment">postAttachment</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioService.php.html#190"><abbr title="App\Services\Appfolio\AppfolioService::createNotes">createNotes</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioService.php.html#219"><abbr title="App\Services\Appfolio\AppfolioService::propertyDetails">propertyDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioService.php.html#245"><abbr title="App\Services\Appfolio\AppfolioService::unitDetails">unitDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioService.php.html#304"><abbr title="App\Services\Appfolio\AppfolioService::tenantDetails">tenantDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioService.php.html#687"><abbr title="App\Services\Appfolio\AppfolioService::addSkippedWorkOrders">addSkippedWorkOrders</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AppfolioService.php.html#1165"><abbr title="App\Services\Appfolio\AppfolioService::addAppfolioApiWorkOrderLog">addAppfolioApiWorkOrderLog</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([2,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([71,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,149,"<a href=\"AppfolioForServiceRequestService.php.html#37\">App\\Services\\Appfolio\\AppfolioForServiceRequestService<\/a>"],[0,169,"<a href=\"AppfolioService.php.html#36\">App\\Services\\Appfolio\\AppfolioService<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AppfolioForServiceRequestService.php.html#93\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::__construct<\/a>"],[0,3,"<a href=\"AppfolioForServiceRequestService.php.html#108\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::get<\/a>"],[0,1,"<a href=\"AppfolioForServiceRequestService.php.html#132\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::post<\/a>"],[0,1,"<a href=\"AppfolioForServiceRequestService.php.html#152\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::patch<\/a>"],[0,2,"<a href=\"AppfolioForServiceRequestService.php.html#171\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::postAttachment<\/a>"],[0,2,"<a href=\"AppfolioForServiceRequestService.php.html#196\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::propertyDetails<\/a>"],[0,2,"<a href=\"AppfolioForServiceRequestService.php.html#222\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::unitDetails<\/a>"],[0,4,"<a href=\"AppfolioForServiceRequestService.php.html#249\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::getTenants<\/a>"],[0,2,"<a href=\"AppfolioForServiceRequestService.php.html#281\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::tenantDetails<\/a>"],[0,5,"<a href=\"AppfolioForServiceRequestService.php.html#305\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::ingestNewWorkOrdersToServiceRequests<\/a>"],[0,11,"<a href=\"AppfolioForServiceRequestService.php.html#403\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::processNewWorkOrder<\/a>"],[0,5,"<a href=\"AppfolioForServiceRequestService.php.html#505\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::fetchAdditionalDataFromScrapper<\/a>"],[0,5,"<a href=\"AppfolioForServiceRequestService.php.html#568\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::formatServiceRequestPayload<\/a>"],[0,2,"<a href=\"AppfolioForServiceRequestService.php.html#619\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::addSkippedWorkOrders<\/a>"],[0,4,"<a href=\"AppfolioForServiceRequestService.php.html#639\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::getPaginatedWorkOrders<\/a>"],[0,1,"<a href=\"AppfolioForServiceRequestService.php.html#671\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::updateWorkOrder<\/a>"],[0,3,"<a href=\"AppfolioForServiceRequestService.php.html#682\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::getVendors<\/a>"],[0,14,"<a href=\"AppfolioForServiceRequestService.php.html#710\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::processNewWorkOrders<\/a>"],[0,3,"<a href=\"AppfolioForServiceRequestService.php.html#808\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::checkIntegration<\/a>"],[0,1,"<a href=\"AppfolioForServiceRequestService.php.html#818\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::connect<\/a>"],[0,5,"<a href=\"AppfolioForServiceRequestService.php.html#835\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::responseFormatter<\/a>"],[0,18,"<a href=\"AppfolioForServiceRequestService.php.html#857\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::logApiResponse<\/a>"],[0,3,"<a href=\"AppfolioForServiceRequestService.php.html#945\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::getAppfolioWorkOrders<\/a>"],[0,3,"<a href=\"AppfolioForServiceRequestService.php.html#962\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::getEntityListByUuid<\/a>"],[0,4,"<a href=\"AppfolioForServiceRequestService.php.html#987\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::createNewServiceRequest<\/a>"],[0,2,"<a href=\"AppfolioForServiceRequestService.php.html#1041\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::addAppfolioApiWorkOrderLog<\/a>"],[0,18,"<a href=\"AppfolioForServiceRequestService.php.html#1069\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::formatPropertyInfo<\/a>"],[0,5,"<a href=\"AppfolioForServiceRequestService.php.html#1129\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::formatTenantData<\/a>"],[0,3,"<a href=\"AppfolioForServiceRequestService.php.html#1146\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::formatPhotos<\/a>"],[0,3,"<a href=\"AppfolioForServiceRequestService.php.html#1165\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::formatTenantInput<\/a>"],[0,6,"<a href=\"AppfolioForServiceRequestService.php.html#1176\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::addUnAssignedWorkOrders<\/a>"],[0,6,"<a href=\"AppfolioForServiceRequestService.php.html#1212\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::addExistingWorkOrders<\/a>"],[0,1,"<a href=\"AppfolioForServiceRequestService.php.html#1248\">App\\Services\\Appfolio\\AppfolioForServiceRequestService::addAppfolioIntegrationSkippedWorkOrders<\/a>"],[0,1,"<a href=\"AppfolioService.php.html#92\">App\\Services\\Appfolio\\AppfolioService::__construct<\/a>"],[0,3,"<a href=\"AppfolioService.php.html#107\">App\\Services\\Appfolio\\AppfolioService::get<\/a>"],[0,1,"<a href=\"AppfolioService.php.html#131\">App\\Services\\Appfolio\\AppfolioService::post<\/a>"],[0,1,"<a href=\"AppfolioService.php.html#151\">App\\Services\\Appfolio\\AppfolioService::patch<\/a>"],[0,2,"<a href=\"AppfolioService.php.html#170\">App\\Services\\Appfolio\\AppfolioService::postAttachment<\/a>"],[0,2,"<a href=\"AppfolioService.php.html#190\">App\\Services\\Appfolio\\AppfolioService::createNotes<\/a>"],[0,2,"<a href=\"AppfolioService.php.html#219\">App\\Services\\Appfolio\\AppfolioService::propertyDetails<\/a>"],[0,2,"<a href=\"AppfolioService.php.html#245\">App\\Services\\Appfolio\\AppfolioService::unitDetails<\/a>"],[0,4,"<a href=\"AppfolioService.php.html#272\">App\\Services\\Appfolio\\AppfolioService::getTenants<\/a>"],[0,2,"<a href=\"AppfolioService.php.html#304\">App\\Services\\Appfolio\\AppfolioService::tenantDetails<\/a>"],[0,5,"<a href=\"AppfolioService.php.html#327\">App\\Services\\Appfolio\\AppfolioService::ingestNewWorkOrders<\/a>"],[0,19,"<a href=\"AppfolioService.php.html#417\">App\\Services\\Appfolio\\AppfolioService::processNewWorkOrder<\/a>"],[0,5,"<a href=\"AppfolioService.php.html#574\">App\\Services\\Appfolio\\AppfolioService::fetchAdditionalDataFromScrapper<\/a>"],[0,8,"<a href=\"AppfolioService.php.html#637\">App\\Services\\Appfolio\\AppfolioService::formatWorkOrderPayload<\/a>"],[0,2,"<a href=\"AppfolioService.php.html#687\">App\\Services\\Appfolio\\AppfolioService::addSkippedWorkOrders<\/a>"],[0,4,"<a href=\"AppfolioService.php.html#707\">App\\Services\\Appfolio\\AppfolioService::getPaginatedWorkOrders<\/a>"],[0,1,"<a href=\"AppfolioService.php.html#739\">App\\Services\\Appfolio\\AppfolioService::updateWorkOrder<\/a>"],[0,3,"<a href=\"AppfolioService.php.html#750\">App\\Services\\Appfolio\\AppfolioService::getVendors<\/a>"],[0,3,"<a href=\"AppfolioService.php.html#777\">App\\Services\\Appfolio\\AppfolioService::checkIntegration<\/a>"],[0,1,"<a href=\"AppfolioService.php.html#787\">App\\Services\\Appfolio\\AppfolioService::connect<\/a>"],[0,5,"<a href=\"AppfolioService.php.html#804\">App\\Services\\Appfolio\\AppfolioService::responseFormatter<\/a>"],[0,18,"<a href=\"AppfolioService.php.html#826\">App\\Services\\Appfolio\\AppfolioService::logApiResponse<\/a>"],[0,3,"<a href=\"AppfolioService.php.html#914\">App\\Services\\Appfolio\\AppfolioService::getWorkOrders<\/a>"],[0,13,"<a href=\"AppfolioService.php.html#931\">App\\Services\\Appfolio\\AppfolioService::processNewWorkOrders<\/a>"],[0,3,"<a href=\"AppfolioService.php.html#1022\">App\\Services\\Appfolio\\AppfolioService::getEntityListByUuid<\/a>"],[0,3,"<a href=\"AppfolioService.php.html#1045\">App\\Services\\Appfolio\\AppfolioService::syncExistingWorkOrders<\/a>"],[0,1,"<a href=\"AppfolioService.php.html#1088\">App\\Services\\Appfolio\\AppfolioService::validateAndUpdateJobs<\/a>"],[0,1,"<a href=\"AppfolioService.php.html#1098\">App\\Services\\Appfolio\\AppfolioService::validatePropertyLocation<\/a>"],[0,4,"<a href=\"AppfolioService.php.html#1111\">App\\Services\\Appfolio\\AppfolioService::requestNewWorkOrder<\/a>"],[0,2,"<a href=\"AppfolioService.php.html#1165\">App\\Services\\Appfolio\\AppfolioService::addAppfolioApiWorkOrderLog<\/a>"],[0,3,"<a href=\"AppfolioService.php.html#1191\">App\\Services\\Appfolio\\AppfolioService::findServiceCategory<\/a>"],[0,18,"<a href=\"AppfolioService.php.html#1209\">App\\Services\\Appfolio\\AppfolioService::formatPropertyInfo<\/a>"],[0,5,"<a href=\"AppfolioService.php.html#1269\">App\\Services\\Appfolio\\AppfolioService::formatTenantData<\/a>"],[0,3,"<a href=\"AppfolioService.php.html#1286\">App\\Services\\Appfolio\\AppfolioService::formatPhotos<\/a>"],[0,3,"<a href=\"AppfolioService.php.html#1305\">App\\Services\\Appfolio\\AppfolioService::formatTenantInput<\/a>"],[0,6,"<a href=\"AppfolioService.php.html#1316\">App\\Services\\Appfolio\\AppfolioService::addUnAssignedWorkOrders<\/a>"],[0,6,"<a href=\"AppfolioService.php.html#1352\">App\\Services\\Appfolio\\AppfolioService::addExistingWorkOrders<\/a>"],[0,1,"<a href=\"AppfolioService.php.html#1388\">App\\Services\\Appfolio\\AppfolioService::addAppfolioIntegrationSkippedWorkOrders<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Services/ServiceRequestRegister/Pipes</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="../index.html">ServiceRequestRegister</a></li>
         <li class="breadcrumb-item"><a href="index.html">Pipes</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="PropertyRegister.php.html#13">App\Services\ServiceRequestRegister\Pipes\PropertyRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentRegister.php.html#11">App\Services\ServiceRequestRegister\Pipes\ResidentRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestCategoryRegister.php.html#10">App\Services\ServiceRequestRegister\Pipes\ServiceRequestCategoryRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPhotosRegister.php.html#16">App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister.php.html#17">App\Services\ServiceRequestRegister\Pipes\ServiceRequestRegister</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ServiceRequestPhotosRegister.php.html#16">App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ResidentRegister.php.html#11">App\Services\ServiceRequestRegister\Pipes\ResidentRegister</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ServiceRequestRegister.php.html#17">App\Services\ServiceRequestRegister\Pipes\ServiceRequestRegister</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ServiceRequestCategoryRegister.php.html#10">App\Services\ServiceRequestRegister\Pipes\ServiceRequestCategoryRegister</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="PropertyRegister.php.html#13">App\Services\ServiceRequestRegister\Pipes\PropertyRegister</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="PropertyRegister.php.html#15"><abbr title="App\Services\ServiceRequestRegister\Pipes\PropertyRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentRegister.php.html#13"><abbr title="App\Services\ServiceRequestRegister\Pipes\ResidentRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestCategoryRegister.php.html#12"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestCategoryRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPhotosRegister.php.html#18"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPhotosRegister.php.html#41"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::isAppfolioSource">isAppfolioSource</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPhotosRegister.php.html#48"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::dispatchPhotoJobs">dispatchPhotoJobs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPhotosRegister.php.html#67"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::updateAdditionalInfo">updateAdditionalInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestRegister.php.html#19"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ResidentRegister.php.html#13"><abbr title="App\Services\ServiceRequestRegister\Pipes\ResidentRegister::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ServiceRequestRegister.php.html#19"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestRegister::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ServiceRequestPhotosRegister.php.html#18"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequestCategoryRegister.php.html#12"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestCategoryRegister::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestPhotosRegister.php.html#48"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::dispatchPhotoJobs">dispatchPhotoJobs</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestPhotosRegister.php.html#67"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::updateAdditionalInfo">updateAdditionalInfo</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PropertyRegister.php.html#15"><abbr title="App\Services\ServiceRequestRegister\Pipes\PropertyRegister::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequestPhotosRegister.php.html#41"><abbr title="App\Services\ServiceRequestRegister\Pipes\ServiceRequestPhotosRegister::isAppfolioSource">isAppfolioSource</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([5,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([8,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"PropertyRegister.php.html#13\">App\\Services\\ServiceRequestRegister\\Pipes\\PropertyRegister<\/a>"],[0,11,"<a href=\"ResidentRegister.php.html#11\">App\\Services\\ServiceRequestRegister\\Pipes\\ResidentRegister<\/a>"],[0,4,"<a href=\"ServiceRequestCategoryRegister.php.html#10\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestCategoryRegister<\/a>"],[0,13,"<a href=\"ServiceRequestPhotosRegister.php.html#16\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestPhotosRegister<\/a>"],[0,10,"<a href=\"ServiceRequestRegister.php.html#17\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestRegister<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"PropertyRegister.php.html#15\">App\\Services\\ServiceRequestRegister\\Pipes\\PropertyRegister::handle<\/a>"],[0,11,"<a href=\"ResidentRegister.php.html#13\">App\\Services\\ServiceRequestRegister\\Pipes\\ResidentRegister::handle<\/a>"],[0,4,"<a href=\"ServiceRequestCategoryRegister.php.html#12\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestCategoryRegister::handle<\/a>"],[0,5,"<a href=\"ServiceRequestPhotosRegister.php.html#18\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestPhotosRegister::handle<\/a>"],[0,2,"<a href=\"ServiceRequestPhotosRegister.php.html#41\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestPhotosRegister::isAppfolioSource<\/a>"],[0,3,"<a href=\"ServiceRequestPhotosRegister.php.html#48\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestPhotosRegister::dispatchPhotoJobs<\/a>"],[0,3,"<a href=\"ServiceRequestPhotosRegister.php.html#67\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestPhotosRegister::updateAdditionalInfo<\/a>"],[0,10,"<a href=\"ServiceRequestRegister.php.html#19\">App\\Services\\ServiceRequestRegister\\Pipes\\ServiceRequestRegister::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

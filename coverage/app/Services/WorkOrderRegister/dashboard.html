<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Services/WorkOrderRegister</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="index.html">WorkOrderRegister</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#14">App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pipes/PropertyRegister.php.html#14">App\Services\WorkOrderRegister\Pipes\PropertyRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pipes/ResidentRegister.php.html#12">App\Services\WorkOrderRegister\Pipes\ResidentRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pipes/WorkOrderMediaRegister.php.html#10">App\Services\WorkOrderRegister\Pipes\WorkOrderMediaRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pipes/WorkOrderRegister.php.html#13">App\Services\WorkOrderRegister\Pipes\WorkOrderRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pipes/WorkOrderTaskRegister.php.html#11">App\Services\WorkOrderRegister\Pipes\WorkOrderTaskRegister</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegisterClient.php.html#31">App\Services\WorkOrderRegister\WorkOrderRegisterClient</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderRegisterClient.php.html#31">App\Services\WorkOrderRegister\WorkOrderRegisterClient</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Pipes/ResidentRegister.php.html#12">App\Services\WorkOrderRegister\Pipes\ResidentRegister</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Pipes/WorkOrderRegister.php.html#13">App\Services\WorkOrderRegister\Pipes\WorkOrderRegister</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Pipes/WorkOrderMediaRegister.php.html#10">App\Services\WorkOrderRegister\Pipes\WorkOrderMediaRegister</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Pipes/WorkOrderTaskRegister.php.html#11">App\Services\WorkOrderRegister\Pipes\WorkOrderTaskRegister</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Pipes/PropertyRegister.php.html#14">App\Services\WorkOrderRegister\Pipes\PropertyRegister</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#16"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#27"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setRequest">setRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#32"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getRequest">getRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#37"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setOrganization">setOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#42"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getOrganization">getOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#47"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setUser">setUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#52"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getUser">getUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#57"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setTimezone">setTimezone</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#62"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getTimezone">getTimezone</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#67"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setProperty">setProperty</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#72"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getProperty">getProperty</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#77"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setResident">setResident</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#82"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getResident">getResident</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#87"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setWorkOrder">setWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#92"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#97"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::setServiceRequest">setServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DTO/WorkOrderRegisterTransport.php.html#102"><abbr title="App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pipes/PropertyRegister.php.html#20"><abbr title="App\Services\WorkOrderRegister\Pipes\PropertyRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pipes/ResidentRegister.php.html#14"><abbr title="App\Services\WorkOrderRegister\Pipes\ResidentRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pipes/WorkOrderMediaRegister.php.html#12"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderMediaRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pipes/WorkOrderRegister.php.html#15"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Pipes/WorkOrderTaskRegister.php.html#13"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderTaskRegister::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegisterClient.php.html#33"><abbr title="App\Services\WorkOrderRegister\WorkOrderRegisterClient::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderRegisterClient.php.html#137"><abbr title="App\Services\WorkOrderRegister\WorkOrderRegisterClient::assignIssueToWorkOrder">assignIssueToWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Pipes/ResidentRegister.php.html#14"><abbr title="App\Services\WorkOrderRegister\Pipes\ResidentRegister::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Pipes/WorkOrderRegister.php.html#15"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderRegister::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="WorkOrderRegisterClient.php.html#33"><abbr title="App\Services\WorkOrderRegister\WorkOrderRegisterClient::register">register</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Pipes/WorkOrderMediaRegister.php.html#12"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderMediaRegister::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Pipes/WorkOrderTaskRegister.php.html#13"><abbr title="App\Services\WorkOrderRegister\Pipes\WorkOrderTaskRegister::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderRegisterClient.php.html#137"><abbr title="App\Services\WorkOrderRegister\WorkOrderRegisterClient::assignIssueToWorkOrder">assignIssueToWorkOrder</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Pipes/PropertyRegister.php.html#20"><abbr title="App\Services\WorkOrderRegister\Pipes\PropertyRegister::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([7,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([24,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,17,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#14\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport<\/a>"],[0,2,"<a href=\"Pipes\/PropertyRegister.php.html#14\">App\\Services\\WorkOrderRegister\\Pipes\\PropertyRegister<\/a>"],[0,11,"<a href=\"Pipes\/ResidentRegister.php.html#12\">App\\Services\\WorkOrderRegister\\Pipes\\ResidentRegister<\/a>"],[0,4,"<a href=\"Pipes\/WorkOrderMediaRegister.php.html#10\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderMediaRegister<\/a>"],[0,11,"<a href=\"Pipes\/WorkOrderRegister.php.html#13\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderRegister<\/a>"],[0,4,"<a href=\"Pipes\/WorkOrderTaskRegister.php.html#11\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderTaskRegister<\/a>"],[0,12,"<a href=\"WorkOrderRegisterClient.php.html#31\">App\\Services\\WorkOrderRegister\\WorkOrderRegisterClient<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#16\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::__construct<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#27\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setRequest<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#32\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getRequest<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#37\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setOrganization<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#42\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getOrganization<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#47\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setUser<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#52\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getUser<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#57\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setTimezone<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#62\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getTimezone<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#67\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setProperty<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#72\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getProperty<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#77\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setResident<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#82\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getResident<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#87\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setWorkOrder<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#92\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getWorkOrder<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#97\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::setServiceRequest<\/a>"],[0,1,"<a href=\"DTO\/WorkOrderRegisterTransport.php.html#102\">App\\Services\\WorkOrderRegister\\DTO\\WorkOrderRegisterTransport::getServiceRequest<\/a>"],[0,2,"<a href=\"Pipes\/PropertyRegister.php.html#20\">App\\Services\\WorkOrderRegister\\Pipes\\PropertyRegister::handle<\/a>"],[0,11,"<a href=\"Pipes\/ResidentRegister.php.html#14\">App\\Services\\WorkOrderRegister\\Pipes\\ResidentRegister::handle<\/a>"],[0,4,"<a href=\"Pipes\/WorkOrderMediaRegister.php.html#12\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderMediaRegister::handle<\/a>"],[0,11,"<a href=\"Pipes\/WorkOrderRegister.php.html#15\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderRegister::handle<\/a>"],[0,4,"<a href=\"Pipes\/WorkOrderTaskRegister.php.html#13\">App\\Services\\WorkOrderRegister\\Pipes\\WorkOrderTaskRegister::handle<\/a>"],[0,9,"<a href=\"WorkOrderRegisterClient.php.html#33\">App\\Services\\WorkOrderRegister\\WorkOrderRegisterClient::register<\/a>"],[0,3,"<a href=\"WorkOrderRegisterClient.php.html#137\">App\\Services\\WorkOrderRegister\\WorkOrderRegisterClient::assignIssueToWorkOrder<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

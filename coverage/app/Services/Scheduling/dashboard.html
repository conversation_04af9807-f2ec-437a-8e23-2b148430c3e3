<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Services/Scheduling</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="index.html">Scheduling</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Domain/Collections/Appointments.php.html#15">App\Services\Scheduling\Domain\Collections\Appointments</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Collections/Technicians.php.html#14">App\Services\Scheduling\Domain\Collections\Technicians</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Collections/WorkingHours.php.html#19">App\Services\Scheduling\Domain\Collections\WorkingHours</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/DTOs/AvailableProviders.php.html#9">App\Services\Scheduling\Domain\DTOs\AvailableProviders</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/DTOs/TaskSchedulingOptions.php.html#10">App\Services\Scheduling\Domain\DTOs\TaskSchedulingOptions</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/DTOs/TechnicianList.php.html#9">App\Services\Scheduling\Domain\DTOs\TechnicianList</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/DTOs/TechnicianSchedule.php.html#9">App\Services\Scheduling\Domain\DTOs\TechnicianSchedule</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/AgendaSlot.php.html#7">App\Services\Scheduling\Domain\Entities\AgendaSlot</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/Appointment.php.html#10">App\Services\Scheduling\Domain\Entities\Appointment</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#15">App\Services\Scheduling\Domain\Entities\DailyAgenda</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/Location.php.html#9">App\Services\Scheduling\Domain\Entities\Location</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/RankedServiceWindow.php.html#5">App\Services\Scheduling\Domain\Entities\RankedServiceWindow</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#10">App\Services\Scheduling\Domain\Entities\ServiceWindow</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/Technician.php.html#9">App\Services\Scheduling\Domain\Entities\Technician</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#17">App\Services\Scheduling\Domain\Entities\TechnicianCalendar</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/WorkOrder.php.html#8">App\Services\Scheduling\Domain\Entities\WorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/WorkOrderQuote.php.html#8">App\Services\Scheduling\Domain\Entities\WorkOrderQuote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/WorkingDay.php.html#12">App\Services\Scheduling\Domain\Entities\WorkingDay</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingRepository.php.html#26">App\Services\Scheduling\SchedulingRepository</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#37">App\Services\Scheduling\SchedulingService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Strategies/EarliestStrategy.php.html#13">App\Services\Scheduling\Strategies\EarliestStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Strategies/EfficientStrategy.php.html#13">App\Services\Scheduling\Strategies\EfficientStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Strategies/EmergencyStrategy.php.html#12">App\Services\Scheduling\Strategies\EmergencyStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Enums/CarbonDayOfWeek.php.html#8">App\Services\Scheduling\Domain\Enums\CarbonDayOfWeek</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Traits/DistanceHelperTrait.php.html#7">App\Services\Scheduling\Domain\Traits\DistanceHelperTrait</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SchedulingService.php.html#37">App\Services\Scheduling\SchedulingService</a></td><td class="text-right">1260</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#15">App\Services\Scheduling\Domain\Entities\DailyAgenda</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#17">App\Services\Scheduling\Domain\Entities\TechnicianCalendar</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#10">App\Services\Scheduling\Domain\Entities\ServiceWindow</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="SchedulingRepository.php.html#26">App\Services\Scheduling\SchedulingRepository</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Domain/Traits/DistanceHelperTrait.php.html#7">App\Services\Scheduling\Domain\Traits\DistanceHelperTrait</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Domain/Collections/WorkingHours.php.html#19">App\Services\Scheduling\Domain\Collections\WorkingHours</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Domain/Entities/Appointment.php.html#10">App\Services\Scheduling\Domain\Entities\Appointment</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Domain/Entities/WorkOrder.php.html#8">App\Services\Scheduling\Domain\Entities\WorkOrder</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Domain/Collections/Appointments.php.html#20"><abbr title="App\Services\Scheduling\Domain\Collections\Appointments::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Collections/Appointments.php.html#25"><abbr title="App\Services\Scheduling\Domain\Collections\Appointments::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Collections/Technicians.php.html#19"><abbr title="App\Services\Scheduling\Domain\Collections\Technicians::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Collections/Technicians.php.html#27"><abbr title="App\Services\Scheduling\Domain\Collections\Technicians::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Collections/WorkingHours.php.html#24"><abbr title="App\Services\Scheduling\Domain\Collections\WorkingHours::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Collections/WorkingHours.php.html#29"><abbr title="App\Services\Scheduling\Domain\Collections\WorkingHours::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/DTOs/AvailableProviders.php.html#15"><abbr title="App\Services\Scheduling\Domain\DTOs\AvailableProviders::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/DTOs/TaskSchedulingOptions.php.html#16"><abbr title="App\Services\Scheduling\Domain\DTOs\TaskSchedulingOptions::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/DTOs/TechnicianList.php.html#16"><abbr title="App\Services\Scheduling\Domain\DTOs\TechnicianList::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/DTOs/TechnicianSchedule.php.html#15"><abbr title="App\Services\Scheduling\Domain\DTOs\TechnicianSchedule::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/AgendaSlot.php.html#9"><abbr title="App\Services\Scheduling\Domain\Entities\AgendaSlot::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/AgendaSlot.php.html#11"><abbr title="App\Services\Scheduling\Domain\Entities\AgendaSlot::getStartTime">getStartTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/AgendaSlot.php.html#16"><abbr title="App\Services\Scheduling\Domain\Entities\AgendaSlot::getEndTime">getEndTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/AgendaSlot.php.html#21"><abbr title="App\Services\Scheduling\Domain\Entities\AgendaSlot::getAppointment">getAppointment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/Appointment.php.html#12"><abbr title="App\Services\Scheduling\Domain\Entities\Appointment::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/Appointment.php.html#20"><abbr title="App\Services\Scheduling\Domain\Entities\Appointment::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/Appointment.php.html#39"><abbr title="App\Services\Scheduling\Domain\Entities\Appointment::is">is</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#27"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#45"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::setAppointment">setAppointment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#119"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getOpenSlots">getOpenSlots</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#131"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getSlotsByAppointment">getSlotsByAppointment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#149"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getOccupiedSlots">getOccupiedSlots</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#154"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getAgendaDate">getAgendaDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#163"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::findAndAppendTimeIntervals">findAndAppendTimeIntervals</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#180"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getAgendaSlotByTime">getAgendaSlotByTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#198"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getRequiredTimeIntervals">getRequiredTimeIntervals</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/Location.php.html#11"><abbr title="App\Services\Scheduling\Domain\Entities\Location::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/Location.php.html#13"><abbr title="App\Services\Scheduling\Domain\Entities\Location::fromProperty">fromProperty</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/Location.php.html#18"><abbr title="App\Services\Scheduling\Domain\Entities\Location::fromTechnician">fromTechnician</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/RankedServiceWindow.php.html#7"><abbr title="App\Services\Scheduling\Domain\Entities\RankedServiceWindow::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#18"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#28"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::fromReference">fromReference</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#45"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::getResidentStartTime">getResidentStartTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#50"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::getResidentEndTime">getResidentEndTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#55"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::getProviderStartTime">getProviderStartTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#60"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::getProviderEndTime">getProviderEndTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#65"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::setEndTime">setEndTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#70"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::setDestination">setDestination</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#83"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::encodedReference">encodedReference</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/Technician.php.html#11"><abbr title="App\Services\Scheduling\Domain\Entities\Technician::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/Technician.php.html#22"><abbr title="App\Services\Scheduling\Domain\Entities\Technician::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#25"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#55"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::isAvailableServiceWindow">isAvailableServiceWindow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#79"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::getOpenServiceWindows">getOpenServiceWindows</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#163"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::applyAppointments">applyAppointments</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#194"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::getOccupiedDays">getOccupiedDays</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#204"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::getFreeSlots">getFreeSlots</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#211"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::getNearestAppointment">getNearestAppointment</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/WorkOrder.php.html#10"><abbr title="App\Services\Scheduling\Domain\Entities\WorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/WorkOrder.php.html#27"><abbr title="App\Services\Scheduling\Domain\Entities\WorkOrder::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/WorkOrderQuote.php.html#10"><abbr title="App\Services\Scheduling\Domain\Entities\WorkOrderQuote::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/WorkOrderQuote.php.html#12"><abbr title="App\Services\Scheduling\Domain\Entities\WorkOrderQuote::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/WorkingDay.php.html#14"><abbr title="App\Services\Scheduling\Domain\Entities\WorkingDay::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/WorkingDay.php.html#19"><abbr title="App\Services\Scheduling\Domain\Entities\WorkingDay::from">from</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Entities/WorkingDay.php.html#28"><abbr title="App\Services\Scheduling\Domain\Entities\WorkingDay::is">is</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingRepository.php.html#31"><abbr title="App\Services\Scheduling\SchedulingRepository::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingRepository.php.html#49"><abbr title="App\Services\Scheduling\SchedulingRepository::getTechniciansForTask">getTechniciansForTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingRepository.php.html#108"><abbr title="App\Services\Scheduling\SchedulingRepository::getTechnicianByUUID">getTechnicianByUUID</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingRepository.php.html#169"><abbr title="App\Services\Scheduling\SchedulingRepository::registerServiceCall">registerServiceCall</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#41"><abbr title="App\Services\Scheduling\SchedulingService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#43"><abbr title="App\Services\Scheduling\SchedulingService::getTaskSchedulingOptions">getTaskSchedulingOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#104"><abbr title="App\Services\Scheduling\SchedulingService::getTechnicianList">getTechnicianList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#173"><abbr title="App\Services\Scheduling\SchedulingService::getTechnicianSchedules">getTechnicianSchedules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#225"><abbr title="App\Services\Scheduling\SchedulingService::registerServiceCall">registerServiceCall</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#275"><abbr title="App\Services\Scheduling\SchedulingService::findStartAndEndTimes">findStartAndEndTimes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#305"><abbr title="App\Services\Scheduling\SchedulingService::getVendors">getVendors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#313"><abbr title="App\Services\Scheduling\SchedulingService::getScheduler">getScheduler</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#322"><abbr title="App\Services\Scheduling\SchedulingService::getEstimatedDuration">getEstimatedDuration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#327"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentDuration">getCurrentDuration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#332"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentMode">getCurrentMode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#337"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentMethod">getCurrentMethod</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#342"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentWorkPerform">getCurrentWorkPerform</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#350"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentLinkedQuote">getCurrentLinkedQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#365"><abbr title="App\Services\Scheduling\SchedulingService::getQualifiedTechnicians">getQualifiedTechnicians</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingService.php.html#391"><abbr title="App\Services\Scheduling\SchedulingService::filterWindowsByResidentAvailability">filterWindowsByResidentAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Strategies/EarliestStrategy.php.html#19"><abbr title="App\Services\Scheduling\Strategies\EarliestStrategy::getPreferredWindows">getPreferredWindows</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Strategies/EfficientStrategy.php.html#19"><abbr title="App\Services\Scheduling\Strategies\EfficientStrategy::getPreferredWindows">getPreferredWindows</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Strategies/EmergencyStrategy.php.html#18"><abbr title="App\Services\Scheduling\Strategies\EmergencyStrategy::getPreferredWindows">getPreferredWindows</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Enums/CarbonDayOfWeek.php.html#21"><abbr title="App\Services\Scheduling\Domain\Enums\CarbonDayOfWeek::fromString">fromString</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Enums/CarbonDayOfWeek.php.html#35"><abbr title="App\Services\Scheduling\Domain\Enums\CarbonDayOfWeek::toString">toString</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Traits/DistanceHelperTrait.php.html#9"><abbr title="App\Services\Scheduling\Domain\Traits\DistanceHelperTrait::calculateStraightLineDistance">calculateStraightLineDistance</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#79"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::getOpenServiceWindows">getOpenServiceWindows</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#45"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::setAppointment">setAppointment</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="SchedulingService.php.html#391"><abbr title="App\Services\Scheduling\SchedulingService::filterWindowsByResidentAvailability">filterWindowsByResidentAvailability</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#198"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getRequiredTimeIntervals">getRequiredTimeIntervals</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SchedulingRepository.php.html#169"><abbr title="App\Services\Scheduling\SchedulingRepository::registerServiceCall">registerServiceCall</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SchedulingService.php.html#275"><abbr title="App\Services\Scheduling\SchedulingService::findStartAndEndTimes">findStartAndEndTimes</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Domain/Traits/DistanceHelperTrait.php.html#9"><abbr title="App\Services\Scheduling\Domain\Traits\DistanceHelperTrait::calculateStraightLineDistance">calculateStraightLineDistance</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#131"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getSlotsByAppointment">getSlotsByAppointment</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#163"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::applyAppointments">applyAppointments</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="SchedulingService.php.html#104"><abbr title="App\Services\Scheduling\SchedulingService::getTechnicianList">getTechnicianList</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Domain/Collections/WorkingHours.php.html#29"><abbr title="App\Services\Scheduling\Domain\Collections\WorkingHours::from">from</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#55"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::isAvailableServiceWindow">isAvailableServiceWindow</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SchedulingService.php.html#43"><abbr title="App\Services\Scheduling\SchedulingService::getTaskSchedulingOptions">getTaskSchedulingOptions</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SchedulingService.php.html#225"><abbr title="App\Services\Scheduling\SchedulingService::registerServiceCall">registerServiceCall</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Domain/Entities/Appointment.php.html#20"><abbr title="App\Services\Scheduling\Domain\Entities\Appointment::from">from</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Domain/Entities/DailyAgenda.php.html#180"><abbr title="App\Services\Scheduling\Domain\Entities\DailyAgenda::getAgendaSlotByTime">getAgendaSlotByTime</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#28"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::fromReference">fromReference</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Domain/Entities/ServiceWindow.php.html#70"><abbr title="App\Services\Scheduling\Domain\Entities\ServiceWindow::setDestination">setDestination</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Domain/Entities/TechnicianCalendar.php.html#25"><abbr title="App\Services\Scheduling\Domain\Entities\TechnicianCalendar::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Domain/Entities/WorkOrder.php.html#27"><abbr title="App\Services\Scheduling\Domain\Entities\WorkOrder::from">from</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SchedulingRepository.php.html#49"><abbr title="App\Services\Scheduling\SchedulingRepository::getTechniciansForTask">getTechniciansForTask</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SchedulingRepository.php.html#108"><abbr title="App\Services\Scheduling\SchedulingRepository::getTechnicianByUUID">getTechnicianByUUID</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SchedulingService.php.html#173"><abbr title="App\Services\Scheduling\SchedulingService::getTechnicianSchedules">getTechnicianSchedules</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SchedulingService.php.html#350"><abbr title="App\Services\Scheduling\SchedulingService::getCurrentLinkedQuote">getCurrentLinkedQuote</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([25,0,0,0,0,0,0,0,0,0,0,2], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([81,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"Domain\/Collections\/Appointments.php.html#15\">App\\Services\\Scheduling\\Domain\\Collections\\Appointments<\/a>"],[0,2,"<a href=\"Domain\/Collections\/Technicians.php.html#14\">App\\Services\\Scheduling\\Domain\\Collections\\Technicians<\/a>"],[0,4,"<a href=\"Domain\/Collections\/WorkingHours.php.html#19\">App\\Services\\Scheduling\\Domain\\Collections\\WorkingHours<\/a>"],[0,1,"<a href=\"Domain\/DTOs\/AvailableProviders.php.html#9\">App\\Services\\Scheduling\\Domain\\DTOs\\AvailableProviders<\/a>"],[0,1,"<a href=\"Domain\/DTOs\/TaskSchedulingOptions.php.html#10\">App\\Services\\Scheduling\\Domain\\DTOs\\TaskSchedulingOptions<\/a>"],[0,1,"<a href=\"Domain\/DTOs\/TechnicianList.php.html#9\">App\\Services\\Scheduling\\Domain\\DTOs\\TechnicianList<\/a>"],[0,1,"<a href=\"Domain\/DTOs\/TechnicianSchedule.php.html#9\">App\\Services\\Scheduling\\Domain\\DTOs\\TechnicianSchedule<\/a>"],[0,4,"<a href=\"Domain\/Entities\/AgendaSlot.php.html#7\">App\\Services\\Scheduling\\Domain\\Entities\\AgendaSlot<\/a>"],[0,4,"<a href=\"Domain\/Entities\/Appointment.php.html#10\">App\\Services\\Scheduling\\Domain\\Entities\\Appointment<\/a>"],[0,26,"<a href=\"Domain\/Entities\/DailyAgenda.php.html#15\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda<\/a>"],[0,3,"<a href=\"Domain\/Entities\/Location.php.html#9\">App\\Services\\Scheduling\\Domain\\Entities\\Location<\/a>"],[0,1,"<a href=\"Domain\/Entities\/RankedServiceWindow.php.html#5\">App\\Services\\Scheduling\\Domain\\Entities\\RankedServiceWindow<\/a>"],[0,11,"<a href=\"Domain\/Entities\/ServiceWindow.php.html#10\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow<\/a>"],[0,2,"<a href=\"Domain\/Entities\/Technician.php.html#9\">App\\Services\\Scheduling\\Domain\\Entities\\Technician<\/a>"],[0,22,"<a href=\"Domain\/Entities\/TechnicianCalendar.php.html#17\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar<\/a>"],[0,3,"<a href=\"Domain\/Entities\/WorkOrder.php.html#8\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrder<\/a>"],[0,2,"<a href=\"Domain\/Entities\/WorkOrderQuote.php.html#8\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrderQuote<\/a>"],[0,3,"<a href=\"Domain\/Entities\/WorkingDay.php.html#12\">App\\Services\\Scheduling\\Domain\\Entities\\WorkingDay<\/a>"],[100,0,"<a href=\"Domain\/Exceptions\/AppointmentCollisionException.php.html#7\">App\\Services\\Scheduling\\Domain\\Exceptions\\AppointmentCollisionException<\/a>"],[100,0,"<a href=\"Domain\/Exceptions\/InvalidAppointmentException.php.html#7\">App\\Services\\Scheduling\\Domain\\Exceptions\\InvalidAppointmentException<\/a>"],[0,11,"<a href=\"SchedulingRepository.php.html#26\">App\\Services\\Scheduling\\SchedulingRepository<\/a>"],[0,35,"<a href=\"SchedulingService.php.html#37\">App\\Services\\Scheduling\\SchedulingService<\/a>"],[0,1,"<a href=\"Strategies\/EarliestStrategy.php.html#13\">App\\Services\\Scheduling\\Strategies\\EarliestStrategy<\/a>"],[0,1,"<a href=\"Strategies\/EfficientStrategy.php.html#13\">App\\Services\\Scheduling\\Strategies\\EfficientStrategy<\/a>"],[0,1,"<a href=\"Strategies\/EmergencyStrategy.php.html#12\">App\\Services\\Scheduling\\Strategies\\EmergencyStrategy<\/a>"],[0,2,"<a href=\"Domain\/Enums\/CarbonDayOfWeek.php.html#8\">App\\Services\\Scheduling\\Domain\\Enums\\CarbonDayOfWeek<\/a>"],[0,5,"<a href=\"Domain\/Traits\/DistanceHelperTrait.php.html#7\">App\\Services\\Scheduling\\Domain\\Traits\\DistanceHelperTrait<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Domain\/Collections\/Appointments.php.html#20\">App\\Services\\Scheduling\\Domain\\Collections\\Appointments::__construct<\/a>"],[0,1,"<a href=\"Domain\/Collections\/Appointments.php.html#25\">App\\Services\\Scheduling\\Domain\\Collections\\Appointments::from<\/a>"],[0,1,"<a href=\"Domain\/Collections\/Technicians.php.html#19\">App\\Services\\Scheduling\\Domain\\Collections\\Technicians::__construct<\/a>"],[0,1,"<a href=\"Domain\/Collections\/Technicians.php.html#27\">App\\Services\\Scheduling\\Domain\\Collections\\Technicians::from<\/a>"],[0,1,"<a href=\"Domain\/Collections\/WorkingHours.php.html#24\">App\\Services\\Scheduling\\Domain\\Collections\\WorkingHours::__construct<\/a>"],[0,3,"<a href=\"Domain\/Collections\/WorkingHours.php.html#29\">App\\Services\\Scheduling\\Domain\\Collections\\WorkingHours::from<\/a>"],[0,1,"<a href=\"Domain\/DTOs\/AvailableProviders.php.html#15\">App\\Services\\Scheduling\\Domain\\DTOs\\AvailableProviders::__construct<\/a>"],[0,1,"<a href=\"Domain\/DTOs\/TaskSchedulingOptions.php.html#16\">App\\Services\\Scheduling\\Domain\\DTOs\\TaskSchedulingOptions::__construct<\/a>"],[0,1,"<a href=\"Domain\/DTOs\/TechnicianList.php.html#16\">App\\Services\\Scheduling\\Domain\\DTOs\\TechnicianList::__construct<\/a>"],[0,1,"<a href=\"Domain\/DTOs\/TechnicianSchedule.php.html#15\">App\\Services\\Scheduling\\Domain\\DTOs\\TechnicianSchedule::__construct<\/a>"],[0,1,"<a href=\"Domain\/Entities\/AgendaSlot.php.html#9\">App\\Services\\Scheduling\\Domain\\Entities\\AgendaSlot::__construct<\/a>"],[0,1,"<a href=\"Domain\/Entities\/AgendaSlot.php.html#11\">App\\Services\\Scheduling\\Domain\\Entities\\AgendaSlot::getStartTime<\/a>"],[0,1,"<a href=\"Domain\/Entities\/AgendaSlot.php.html#16\">App\\Services\\Scheduling\\Domain\\Entities\\AgendaSlot::getEndTime<\/a>"],[0,1,"<a href=\"Domain\/Entities\/AgendaSlot.php.html#21\">App\\Services\\Scheduling\\Domain\\Entities\\AgendaSlot::getAppointment<\/a>"],[0,1,"<a href=\"Domain\/Entities\/Appointment.php.html#12\">App\\Services\\Scheduling\\Domain\\Entities\\Appointment::__construct<\/a>"],[0,2,"<a href=\"Domain\/Entities\/Appointment.php.html#20\">App\\Services\\Scheduling\\Domain\\Entities\\Appointment::from<\/a>"],[0,1,"<a href=\"Domain\/Entities\/Appointment.php.html#39\">App\\Services\\Scheduling\\Domain\\Entities\\Appointment::is<\/a>"],[0,1,"<a href=\"Domain\/Entities\/DailyAgenda.php.html#27\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::__construct<\/a>"],[0,9,"<a href=\"Domain\/Entities\/DailyAgenda.php.html#45\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::setAppointment<\/a>"],[0,1,"<a href=\"Domain\/Entities\/DailyAgenda.php.html#119\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getOpenSlots<\/a>"],[0,4,"<a href=\"Domain\/Entities\/DailyAgenda.php.html#131\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getSlotsByAppointment<\/a>"],[0,1,"<a href=\"Domain\/Entities\/DailyAgenda.php.html#149\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getOccupiedSlots<\/a>"],[0,1,"<a href=\"Domain\/Entities\/DailyAgenda.php.html#154\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getAgendaDate<\/a>"],[0,1,"<a href=\"Domain\/Entities\/DailyAgenda.php.html#163\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::findAndAppendTimeIntervals<\/a>"],[0,2,"<a href=\"Domain\/Entities\/DailyAgenda.php.html#180\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getAgendaSlotByTime<\/a>"],[0,6,"<a href=\"Domain\/Entities\/DailyAgenda.php.html#198\">App\\Services\\Scheduling\\Domain\\Entities\\DailyAgenda::getRequiredTimeIntervals<\/a>"],[0,1,"<a href=\"Domain\/Entities\/Location.php.html#11\">App\\Services\\Scheduling\\Domain\\Entities\\Location::__construct<\/a>"],[0,1,"<a href=\"Domain\/Entities\/Location.php.html#13\">App\\Services\\Scheduling\\Domain\\Entities\\Location::fromProperty<\/a>"],[0,1,"<a href=\"Domain\/Entities\/Location.php.html#18\">App\\Services\\Scheduling\\Domain\\Entities\\Location::fromTechnician<\/a>"],[0,1,"<a href=\"Domain\/Entities\/RankedServiceWindow.php.html#7\">App\\Services\\Scheduling\\Domain\\Entities\\RankedServiceWindow::__construct<\/a>"],[0,1,"<a href=\"Domain\/Entities\/ServiceWindow.php.html#18\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::__construct<\/a>"],[0,2,"<a href=\"Domain\/Entities\/ServiceWindow.php.html#28\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::fromReference<\/a>"],[0,1,"<a href=\"Domain\/Entities\/ServiceWindow.php.html#45\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::getResidentStartTime<\/a>"],[0,1,"<a href=\"Domain\/Entities\/ServiceWindow.php.html#50\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::getResidentEndTime<\/a>"],[0,1,"<a href=\"Domain\/Entities\/ServiceWindow.php.html#55\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::getProviderStartTime<\/a>"],[0,1,"<a href=\"Domain\/Entities\/ServiceWindow.php.html#60\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::getProviderEndTime<\/a>"],[0,1,"<a href=\"Domain\/Entities\/ServiceWindow.php.html#65\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::setEndTime<\/a>"],[0,2,"<a href=\"Domain\/Entities\/ServiceWindow.php.html#70\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::setDestination<\/a>"],[0,1,"<a href=\"Domain\/Entities\/ServiceWindow.php.html#83\">App\\Services\\Scheduling\\Domain\\Entities\\ServiceWindow::encodedReference<\/a>"],[0,1,"<a href=\"Domain\/Entities\/Technician.php.html#11\">App\\Services\\Scheduling\\Domain\\Entities\\Technician::__construct<\/a>"],[0,1,"<a href=\"Domain\/Entities\/Technician.php.html#22\">App\\Services\\Scheduling\\Domain\\Entities\\Technician::from<\/a>"],[0,2,"<a href=\"Domain\/Entities\/TechnicianCalendar.php.html#25\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::__construct<\/a>"],[0,3,"<a href=\"Domain\/Entities\/TechnicianCalendar.php.html#55\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::isAvailableServiceWindow<\/a>"],[0,10,"<a href=\"Domain\/Entities\/TechnicianCalendar.php.html#79\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::getOpenServiceWindows<\/a>"],[0,4,"<a href=\"Domain\/Entities\/TechnicianCalendar.php.html#163\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::applyAppointments<\/a>"],[0,1,"<a href=\"Domain\/Entities\/TechnicianCalendar.php.html#194\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::getOccupiedDays<\/a>"],[0,1,"<a href=\"Domain\/Entities\/TechnicianCalendar.php.html#204\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::getFreeSlots<\/a>"],[0,1,"<a href=\"Domain\/Entities\/TechnicianCalendar.php.html#211\">App\\Services\\Scheduling\\Domain\\Entities\\TechnicianCalendar::getNearestAppointment<\/a>"],[0,1,"<a href=\"Domain\/Entities\/WorkOrder.php.html#10\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrder::__construct<\/a>"],[0,2,"<a href=\"Domain\/Entities\/WorkOrder.php.html#27\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrder::from<\/a>"],[0,1,"<a href=\"Domain\/Entities\/WorkOrderQuote.php.html#10\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrderQuote::__construct<\/a>"],[0,1,"<a href=\"Domain\/Entities\/WorkOrderQuote.php.html#12\">App\\Services\\Scheduling\\Domain\\Entities\\WorkOrderQuote::from<\/a>"],[0,1,"<a href=\"Domain\/Entities\/WorkingDay.php.html#14\">App\\Services\\Scheduling\\Domain\\Entities\\WorkingDay::__construct<\/a>"],[0,1,"<a href=\"Domain\/Entities\/WorkingDay.php.html#19\">App\\Services\\Scheduling\\Domain\\Entities\\WorkingDay::from<\/a>"],[0,1,"<a href=\"Domain\/Entities\/WorkingDay.php.html#28\">App\\Services\\Scheduling\\Domain\\Entities\\WorkingDay::is<\/a>"],[0,1,"<a href=\"SchedulingRepository.php.html#31\">App\\Services\\Scheduling\\SchedulingRepository::getWorkOrder<\/a>"],[0,2,"<a href=\"SchedulingRepository.php.html#49\">App\\Services\\Scheduling\\SchedulingRepository::getTechniciansForTask<\/a>"],[0,2,"<a href=\"SchedulingRepository.php.html#108\">App\\Services\\Scheduling\\SchedulingRepository::getTechnicianByUUID<\/a>"],[0,6,"<a href=\"SchedulingRepository.php.html#169\">App\\Services\\Scheduling\\SchedulingRepository::registerServiceCall<\/a>"],[0,1,"<a href=\"SchedulingService.php.html#41\">App\\Services\\Scheduling\\SchedulingService::__construct<\/a>"],[0,3,"<a href=\"SchedulingService.php.html#43\">App\\Services\\Scheduling\\SchedulingService::getTaskSchedulingOptions<\/a>"],[0,4,"<a href=\"SchedulingService.php.html#104\">App\\Services\\Scheduling\\SchedulingService::getTechnicianList<\/a>"],[0,2,"<a href=\"SchedulingService.php.html#173\">App\\Services\\Scheduling\\SchedulingService::getTechnicianSchedules<\/a>"],[0,3,"<a href=\"SchedulingService.php.html#225\">App\\Services\\Scheduling\\SchedulingService::registerServiceCall<\/a>"],[0,5,"<a href=\"SchedulingService.php.html#275\">App\\Services\\Scheduling\\SchedulingService::findStartAndEndTimes<\/a>"],[0,1,"<a href=\"SchedulingService.php.html#305\">App\\Services\\Scheduling\\SchedulingService::getVendors<\/a>"],[0,1,"<a href=\"SchedulingService.php.html#313\">App\\Services\\Scheduling\\SchedulingService::getScheduler<\/a>"],[0,1,"<a href=\"SchedulingService.php.html#322\">App\\Services\\Scheduling\\SchedulingService::getEstimatedDuration<\/a>"],[0,1,"<a href=\"SchedulingService.php.html#327\">App\\Services\\Scheduling\\SchedulingService::getCurrentDuration<\/a>"],[0,1,"<a href=\"SchedulingService.php.html#332\">App\\Services\\Scheduling\\SchedulingService::getCurrentMode<\/a>"],[0,1,"<a href=\"SchedulingService.php.html#337\">App\\Services\\Scheduling\\SchedulingService::getCurrentMethod<\/a>"],[0,1,"<a href=\"SchedulingService.php.html#342\">App\\Services\\Scheduling\\SchedulingService::getCurrentWorkPerform<\/a>"],[0,2,"<a href=\"SchedulingService.php.html#350\">App\\Services\\Scheduling\\SchedulingService::getCurrentLinkedQuote<\/a>"],[0,1,"<a href=\"SchedulingService.php.html#365\">App\\Services\\Scheduling\\SchedulingService::getQualifiedTechnicians<\/a>"],[0,7,"<a href=\"SchedulingService.php.html#391\">App\\Services\\Scheduling\\SchedulingService::filterWindowsByResidentAvailability<\/a>"],[0,1,"<a href=\"Strategies\/EarliestStrategy.php.html#19\">App\\Services\\Scheduling\\Strategies\\EarliestStrategy::getPreferredWindows<\/a>"],[0,1,"<a href=\"Strategies\/EfficientStrategy.php.html#19\">App\\Services\\Scheduling\\Strategies\\EfficientStrategy::getPreferredWindows<\/a>"],[0,1,"<a href=\"Strategies\/EmergencyStrategy.php.html#18\">App\\Services\\Scheduling\\Strategies\\EmergencyStrategy::getPreferredWindows<\/a>"],[0,1,"<a href=\"Domain\/Enums\/CarbonDayOfWeek.php.html#21\">App\\Services\\Scheduling\\Domain\\Enums\\CarbonDayOfWeek::fromString<\/a>"],[0,1,"<a href=\"Domain\/Enums\/CarbonDayOfWeek.php.html#35\">App\\Services\\Scheduling\\Domain\\Enums\\CarbonDayOfWeek::toString<\/a>"],[0,5,"<a href=\"Domain\/Traits\/DistanceHelperTrait.php.html#9\">App\\Services\\Scheduling\\Domain\\Traits\\DistanceHelperTrait::calculateStraightLineDistance<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

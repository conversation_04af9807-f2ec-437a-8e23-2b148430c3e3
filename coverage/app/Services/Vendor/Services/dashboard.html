<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Services/Vendor/Services</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Vendor</a></li>
         <li class="breadcrumb-item"><a href="index.html">Services</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Lula/Authentication.php.html#15">App\Services\Vendor\Services\Lula\Authentication</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Config.php.html#8">App\Services\Vendor\Services\Lula\Config</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/AuthenticationException.php.html#7">App\Services\Vendor\Services\Lula\Exception\AuthenticationException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/WorkOrderSendException.php.html#7">App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaClient.php.html#10">App\Services\Vendor\Services\Lula\LulaClient</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#42">App\Services\Vendor\Services\Lula\LulaService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Webhook/LulaWebhookConfig.php.html#7">App\Services\Vendor\Services\Lula\Webhook\LulaWebhookConfig</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Webhook/LulaWebhookSignatureValidator.php.html#10">App\Services\Vendor\Services\Lula\Webhook\LulaWebhookSignatureValidator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty/ThirdPartyService.php.html#33">App\Services\Vendor\Services\ThirdParty\ThirdPartyService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Scope/WorkOrderScope.php.html#8">App\Services\Vendor\Services\Lula\Scope\WorkOrderScope</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Lula/LulaService.php.html#42">App\Services\Vendor\Services\Lula\LulaService</a></td><td class="text-right">1332</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#15">App\Services\Vendor\Services\Lula\Authentication</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="ThirdParty/ThirdPartyService.php.html#33">App\Services\Vendor\Services\ThirdParty\ThirdPartyService</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="Lula/Config.php.html#8">App\Services\Vendor\Services\Lula\Config</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Lula/Webhook/LulaWebhookSignatureValidator.php.html#10">App\Services\Vendor\Services\Lula\Webhook\LulaWebhookSignatureValidator</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Lula/Authentication.php.html#38"><abbr title="App\Services\Vendor\Services\Lula\Authentication::authenticate">authenticate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#55"><abbr title="App\Services\Vendor\Services\Lula\Authentication::getToken">getToken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#65"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setClientId">setClientId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#72"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setClientKey">setClientKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#83"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setScopes">setScopes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#90"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setCacheKey">setCacheKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#97"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setOrganization">setOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#116"><abbr title="App\Services\Vendor\Services\Lula\Authentication::invalidateToken">invalidateToken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#131"><abbr title="App\Services\Vendor\Services\Lula\Authentication::attempt">attempt</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#162"><abbr title="App\Services\Vendor\Services\Lula\Authentication::getAuthorizationToken">getAuthorizationToken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#167"><abbr title="App\Services\Vendor\Services\Lula\Authentication::getCacheKey">getCacheKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#172"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setToken">setToken</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Config.php.html#19"><abbr title="App\Services\Vendor\Services\Lula\Config::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Config.php.html#24"><abbr title="App\Services\Vendor\Services\Lula\Config::getInstance">getInstance</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Config.php.html#36"><abbr title="App\Services\Vendor\Services\Lula\Config::baseUrl">baseUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Config.php.html#49"><abbr title="App\Services\Vendor\Services\Lula\Config::version">version</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Config.php.html#54"><abbr title="App\Services\Vendor\Services\Lula\Config::getEnvironmentBaseUrl">getEnvironmentBaseUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/AuthenticationException.php.html#9"><abbr title="App\Services\Vendor\Services\Lula\Exception\AuthenticationException::invalidClient">invalidClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/AuthenticationException.php.html#14"><abbr title="App\Services\Vendor\Services\Lula\Exception\AuthenticationException::invalidClientKey">invalidClientKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/AuthenticationException.php.html#19"><abbr title="App\Services\Vendor\Services\Lula\Exception\AuthenticationException::invalidRequest">invalidRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/AuthenticationException.php.html#24"><abbr title="App\Services\Vendor\Services\Lula\Exception\AuthenticationException::accessTokenMissing">accessTokenMissing</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/WorkOrderSendException.php.html#9"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::emptyWorkOrderTask">emptyWorkOrderTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/WorkOrderSendException.php.html#14"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::serviceCallRegisterFailed">serviceCallRegisterFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/WorkOrderSendException.php.html#19"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::failedResponse">failedResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/WorkOrderSendException.php.html#24"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::lulaAppointmentRegisterFailed">lulaAppointmentRegisterFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/WorkOrderSendException.php.html#29"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::lulaWorkOrder">lulaWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/WorkOrderSendException.php.html#34"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::unexpectedResponseData">unexpectedResponseData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/WorkOrderSendException.php.html#39"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::emptyVendor">emptyVendor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Exception/WorkOrderSendException.php.html#44"><abbr title="App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException::vendorAppointmentRegisterFailed">vendorAppointmentRegisterFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaClient.php.html#12"><abbr title="App\Services\Vendor\Services\Lula\LulaClient::getInstance">getInstance</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaClient.php.html#22"><abbr title="App\Services\Vendor\Services\Lula\LulaClient::send">send</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaClient.php.html#36"><abbr title="App\Services\Vendor\Services\Lula\LulaClient::__callStatic">__callStatic</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#50"><abbr title="App\Services\Vendor\Services\Lula\LulaService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#55"><abbr title="App\Services\Vendor\Services\Lula\LulaService::authenticate">authenticate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#62"><abbr title="App\Services\Vendor\Services\Lula\LulaService::isAuthenticate">isAuthenticate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#67"><abbr title="App\Services\Vendor\Services\Lula\LulaService::token">token</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#72"><abbr title="App\Services\Vendor\Services\Lula\LulaService::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#92"><abbr title="App\Services\Vendor\Services\Lula\LulaService::setOrganization">setOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#104"><abbr title="App\Services\Vendor\Services\Lula\LulaService::setMode">setMode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#118"><abbr title="App\Services\Vendor\Services\Lula\LulaService::sendWorkOrder">sendWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#319"><abbr title="App\Services\Vendor\Services\Lula\LulaService::workOrderList">workOrderList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#329"><abbr title="App\Services\Vendor\Services\Lula\LulaService::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#376"><abbr title="App\Services\Vendor\Services\Lula\LulaService::updateWorkOrder">updateWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Webhook/LulaWebhookConfig.php.html#16"><abbr title="App\Services\Vendor\Services\Lula\Webhook\LulaWebhookConfig::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Webhook/LulaWebhookSignatureValidator.php.html#12"><abbr title="App\Services\Vendor\Services\Lula\Webhook\LulaWebhookSignatureValidator::isValid">isValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty/ThirdPartyService.php.html#35"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::authenticate">authenticate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty/ThirdPartyService.php.html#42"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::token">token</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty/ThirdPartyService.php.html#52"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::sendWorkOrder">sendWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty/ThirdPartyService.php.html#203"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::setOrganization">setOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty/ThirdPartyService.php.html#209"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty/ThirdPartyService.php.html#216"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::setMode">setMode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty/ThirdPartyService.php.html#224"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ThirdParty/ThirdPartyService.php.html#232"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::updateWorkOrder">updateWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Scope/WorkOrderScope.php.html#19"><abbr title="App\Services\Vendor\Services\Lula\Scope\WorkOrderScope::all">all</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Scope/WorkOrderScope.php.html#28"><abbr title="App\Services\Vendor\Services\Lula\Scope\WorkOrderScope::generateScope">generateScope</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Lula/Scope/WorkOrderScope.php.html#34"><abbr title="App\Services\Vendor\Services\Lula\Scope\WorkOrderScope::getPrefix">getPrefix</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Lula/LulaService.php.html#118"><abbr title="App\Services\Vendor\Services\Lula\LulaService::sendWorkOrder">sendWorkOrder</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ThirdParty/ThirdPartyService.php.html#52"><abbr title="App\Services\Vendor\Services\ThirdParty\ThirdPartyService::sendWorkOrder">sendWorkOrder</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#329"><abbr title="App\Services\Vendor\Services\Lula\LulaService::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#376"><abbr title="App\Services\Vendor\Services\Lula\LulaService::updateWorkOrder">updateWorkOrder</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#97"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setOrganization">setOrganization</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#38"><abbr title="App\Services\Vendor\Services\Lula\Authentication::authenticate">authenticate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#131"><abbr title="App\Services\Vendor\Services\Lula\Authentication::attempt">attempt</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Lula/Config.php.html#24"><abbr title="App\Services\Vendor\Services\Lula\Config::getInstance">getInstance</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Lula/LulaService.php.html#72"><abbr title="App\Services\Vendor\Services\Lula\LulaService::getClient">getClient</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Lula/Webhook/LulaWebhookSignatureValidator.php.html#12"><abbr title="App\Services\Vendor\Services\Lula\Webhook\LulaWebhookSignatureValidator::isValid">isValid</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#55"><abbr title="App\Services\Vendor\Services\Lula\Authentication::getToken">getToken</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#116"><abbr title="App\Services\Vendor\Services\Lula\Authentication::invalidateToken">invalidateToken</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Lula/Authentication.php.html#172"><abbr title="App\Services\Vendor\Services\Lula\Authentication::setToken">setToken</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Lula/Config.php.html#36"><abbr title="App\Services\Vendor\Services\Lula\Config::baseUrl">baseUrl</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Thu Jun 26 15:40:22 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([10,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([56,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,22,"<a href=\"Lula\/Authentication.php.html#15\">App\\Services\\Vendor\\Services\\Lula\\Authentication<\/a>"],[0,8,"<a href=\"Lula\/Config.php.html#8\">App\\Services\\Vendor\\Services\\Lula\\Config<\/a>"],[0,4,"<a href=\"Lula\/Exception\/AuthenticationException.php.html#7\">App\\Services\\Vendor\\Services\\Lula\\Exception\\AuthenticationException<\/a>"],[0,8,"<a href=\"Lula\/Exception\/WorkOrderSendException.php.html#7\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException<\/a>"],[0,3,"<a href=\"Lula\/LulaClient.php.html#10\">App\\Services\\Vendor\\Services\\Lula\\LulaClient<\/a>"],[0,36,"<a href=\"Lula\/LulaService.php.html#42\">App\\Services\\Vendor\\Services\\Lula\\LulaService<\/a>"],[0,1,"<a href=\"Lula\/Webhook\/LulaWebhookConfig.php.html#7\">App\\Services\\Vendor\\Services\\Lula\\Webhook\\LulaWebhookConfig<\/a>"],[0,3,"<a href=\"Lula\/Webhook\/LulaWebhookSignatureValidator.php.html#10\">App\\Services\\Vendor\\Services\\Lula\\Webhook\\LulaWebhookSignatureValidator<\/a>"],[0,19,"<a href=\"ThirdParty\/ThirdPartyService.php.html#33\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService<\/a>"],[0,3,"<a href=\"Lula\/Scope\/WorkOrderScope.php.html#8\">App\\Services\\Vendor\\Services\\Lula\\Scope\\WorkOrderScope<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,3,"<a href=\"Lula\/Authentication.php.html#38\">App\\Services\\Vendor\\Services\\Lula\\Authentication::authenticate<\/a>"],[0,2,"<a href=\"Lula\/Authentication.php.html#55\">App\\Services\\Vendor\\Services\\Lula\\Authentication::getToken<\/a>"],[0,1,"<a href=\"Lula\/Authentication.php.html#65\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setClientId<\/a>"],[0,1,"<a href=\"Lula\/Authentication.php.html#72\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setClientKey<\/a>"],[0,1,"<a href=\"Lula\/Authentication.php.html#83\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setScopes<\/a>"],[0,1,"<a href=\"Lula\/Authentication.php.html#90\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setCacheKey<\/a>"],[0,4,"<a href=\"Lula\/Authentication.php.html#97\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setOrganization<\/a>"],[0,2,"<a href=\"Lula\/Authentication.php.html#116\">App\\Services\\Vendor\\Services\\Lula\\Authentication::invalidateToken<\/a>"],[0,3,"<a href=\"Lula\/Authentication.php.html#131\">App\\Services\\Vendor\\Services\\Lula\\Authentication::attempt<\/a>"],[0,1,"<a href=\"Lula\/Authentication.php.html#162\">App\\Services\\Vendor\\Services\\Lula\\Authentication::getAuthorizationToken<\/a>"],[0,1,"<a href=\"Lula\/Authentication.php.html#167\">App\\Services\\Vendor\\Services\\Lula\\Authentication::getCacheKey<\/a>"],[0,2,"<a href=\"Lula\/Authentication.php.html#172\">App\\Services\\Vendor\\Services\\Lula\\Authentication::setToken<\/a>"],[0,1,"<a href=\"Lula\/Config.php.html#19\">App\\Services\\Vendor\\Services\\Lula\\Config::__construct<\/a>"],[0,3,"<a href=\"Lula\/Config.php.html#24\">App\\Services\\Vendor\\Services\\Lula\\Config::getInstance<\/a>"],[0,2,"<a href=\"Lula\/Config.php.html#36\">App\\Services\\Vendor\\Services\\Lula\\Config::baseUrl<\/a>"],[0,1,"<a href=\"Lula\/Config.php.html#49\">App\\Services\\Vendor\\Services\\Lula\\Config::version<\/a>"],[0,1,"<a href=\"Lula\/Config.php.html#54\">App\\Services\\Vendor\\Services\\Lula\\Config::getEnvironmentBaseUrl<\/a>"],[0,1,"<a href=\"Lula\/Exception\/AuthenticationException.php.html#9\">App\\Services\\Vendor\\Services\\Lula\\Exception\\AuthenticationException::invalidClient<\/a>"],[0,1,"<a href=\"Lula\/Exception\/AuthenticationException.php.html#14\">App\\Services\\Vendor\\Services\\Lula\\Exception\\AuthenticationException::invalidClientKey<\/a>"],[0,1,"<a href=\"Lula\/Exception\/AuthenticationException.php.html#19\">App\\Services\\Vendor\\Services\\Lula\\Exception\\AuthenticationException::invalidRequest<\/a>"],[0,1,"<a href=\"Lula\/Exception\/AuthenticationException.php.html#24\">App\\Services\\Vendor\\Services\\Lula\\Exception\\AuthenticationException::accessTokenMissing<\/a>"],[0,1,"<a href=\"Lula\/Exception\/WorkOrderSendException.php.html#9\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::emptyWorkOrderTask<\/a>"],[0,1,"<a href=\"Lula\/Exception\/WorkOrderSendException.php.html#14\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::serviceCallRegisterFailed<\/a>"],[0,1,"<a href=\"Lula\/Exception\/WorkOrderSendException.php.html#19\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::failedResponse<\/a>"],[0,1,"<a href=\"Lula\/Exception\/WorkOrderSendException.php.html#24\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::lulaAppointmentRegisterFailed<\/a>"],[0,1,"<a href=\"Lula\/Exception\/WorkOrderSendException.php.html#29\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::lulaWorkOrder<\/a>"],[0,1,"<a href=\"Lula\/Exception\/WorkOrderSendException.php.html#34\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::unexpectedResponseData<\/a>"],[0,1,"<a href=\"Lula\/Exception\/WorkOrderSendException.php.html#39\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::emptyVendor<\/a>"],[0,1,"<a href=\"Lula\/Exception\/WorkOrderSendException.php.html#44\">App\\Services\\Vendor\\Services\\Lula\\Exception\\WorkOrderSendException::vendorAppointmentRegisterFailed<\/a>"],[0,1,"<a href=\"Lula\/LulaClient.php.html#12\">App\\Services\\Vendor\\Services\\Lula\\LulaClient::getInstance<\/a>"],[0,1,"<a href=\"Lula\/LulaClient.php.html#22\">App\\Services\\Vendor\\Services\\Lula\\LulaClient::send<\/a>"],[0,1,"<a href=\"Lula\/LulaClient.php.html#36\">App\\Services\\Vendor\\Services\\Lula\\LulaClient::__callStatic<\/a>"],[0,1,"<a href=\"Lula\/LulaService.php.html#50\">App\\Services\\Vendor\\Services\\Lula\\LulaService::__construct<\/a>"],[0,1,"<a href=\"Lula\/LulaService.php.html#55\">App\\Services\\Vendor\\Services\\Lula\\LulaService::authenticate<\/a>"],[0,1,"<a href=\"Lula\/LulaService.php.html#62\">App\\Services\\Vendor\\Services\\Lula\\LulaService::isAuthenticate<\/a>"],[0,1,"<a href=\"Lula\/LulaService.php.html#67\">App\\Services\\Vendor\\Services\\Lula\\LulaService::token<\/a>"],[0,3,"<a href=\"Lula\/LulaService.php.html#72\">App\\Services\\Vendor\\Services\\Lula\\LulaService::getClient<\/a>"],[0,1,"<a href=\"Lula\/LulaService.php.html#92\">App\\Services\\Vendor\\Services\\Lula\\LulaService::setOrganization<\/a>"],[0,1,"<a href=\"Lula\/LulaService.php.html#104\">App\\Services\\Vendor\\Services\\Lula\\LulaService::setMode<\/a>"],[0,15,"<a href=\"Lula\/LulaService.php.html#118\">App\\Services\\Vendor\\Services\\Lula\\LulaService::sendWorkOrder<\/a>"],[0,1,"<a href=\"Lula\/LulaService.php.html#319\">App\\Services\\Vendor\\Services\\Lula\\LulaService::workOrderList<\/a>"],[0,6,"<a href=\"Lula\/LulaService.php.html#329\">App\\Services\\Vendor\\Services\\Lula\\LulaService::getWorkOrder<\/a>"],[0,5,"<a href=\"Lula\/LulaService.php.html#376\">App\\Services\\Vendor\\Services\\Lula\\LulaService::updateWorkOrder<\/a>"],[0,1,"<a href=\"Lula\/Webhook\/LulaWebhookConfig.php.html#16\">App\\Services\\Vendor\\Services\\Lula\\Webhook\\LulaWebhookConfig::__construct<\/a>"],[0,3,"<a href=\"Lula\/Webhook\/LulaWebhookSignatureValidator.php.html#12\">App\\Services\\Vendor\\Services\\Lula\\Webhook\\LulaWebhookSignatureValidator::isValid<\/a>"],[0,1,"<a href=\"ThirdParty\/ThirdPartyService.php.html#35\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::authenticate<\/a>"],[0,1,"<a href=\"ThirdParty\/ThirdPartyService.php.html#42\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::token<\/a>"],[0,12,"<a href=\"ThirdParty\/ThirdPartyService.php.html#52\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::sendWorkOrder<\/a>"],[0,1,"<a href=\"ThirdParty\/ThirdPartyService.php.html#203\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::setOrganization<\/a>"],[0,1,"<a href=\"ThirdParty\/ThirdPartyService.php.html#209\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::getClient<\/a>"],[0,1,"<a href=\"ThirdParty\/ThirdPartyService.php.html#216\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::setMode<\/a>"],[0,1,"<a href=\"ThirdParty\/ThirdPartyService.php.html#224\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::getWorkOrder<\/a>"],[0,1,"<a href=\"ThirdParty\/ThirdPartyService.php.html#232\">App\\Services\\Vendor\\Services\\ThirdParty\\ThirdPartyService::updateWorkOrder<\/a>"],[0,1,"<a href=\"Lula\/Scope\/WorkOrderScope.php.html#19\">App\\Services\\Vendor\\Services\\Lula\\Scope\\WorkOrderScope::all<\/a>"],[0,1,"<a href=\"Lula\/Scope\/WorkOrderScope.php.html#28\">App\\Services\\Vendor\\Services\\Lula\\Scope\\WorkOrderScope::generateScope<\/a>"],[0,1,"<a href=\"Lula\/Scope\/WorkOrderScope.php.html#34\">App\\Services\\Vendor\\Services\\Lula\\Scope\\WorkOrderScope::getPrefix<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

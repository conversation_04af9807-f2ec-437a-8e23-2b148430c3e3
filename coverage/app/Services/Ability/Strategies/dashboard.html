<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Services/Ability/Strategies</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Ability</a></li>
         <li class="breadcrumb-item"><a href="index.html">Strategies</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="IssueAbilityStrategy.php.html#16">App\Services\Ability\Strategies\IssueAbilityStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAbilityStrategy.php.html#16">App\Services\Ability\Strategies\WorkOrderAbilityStrategy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssueAbilityStrategy.php.html#15">App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderIssueAbilityStrategy.php.html#15">App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="IssueAbilityStrategy.php.html#16">App\Services\Ability\Strategies\IssueAbilityStrategy</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="WorkOrderAbilityStrategy.php.html#16">App\Services\Ability\Strategies\WorkOrderAbilityStrategy</a></td><td class="text-right">132</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="IssueAbilityStrategy.php.html#18"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueAbilityStrategy.php.html#40"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::isDeleted">isDeleted</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueAbilityStrategy.php.html#51"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::filterDeleteAction">filterDeleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueAbilityStrategy.php.html#66"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::filterCancelAction">filterCancelAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueAbilityStrategy.php.html#82"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::isMappedToActiveWorkOrder">isMappedToActiveWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueAbilityStrategy.php.html#94"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::isClosedServiceRequest">isClosedServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAbilityStrategy.php.html#18"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAbilityStrategy.php.html#43"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::abilitiesForWorkInProgressStatus">abilitiesForWorkInProgressStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAbilityStrategy.php.html#62"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::filterMobileAbilities">filterMobileAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAbilityStrategy.php.html#91"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::removeActions">removeActions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssueAbilityStrategy.php.html#17"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssueAbilityStrategy.php.html#38"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::isDeleted">isDeleted</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssueAbilityStrategy.php.html#49"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::filterEditAction">filterEditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssueAbilityStrategy.php.html#61"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::isMappedToClosedWorkOrder">isMappedToClosedWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssueAbilityStrategy.php.html#76"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::filterUnusedAbilitiesForMobile">filterUnusedAbilitiesForMobile</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderIssueAbilityStrategy.php.html#76"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::filterUnusedAbilitiesForMobile">filterUnusedAbilitiesForMobile</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="IssueAbilityStrategy.php.html#66"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::filterCancelAction">filterCancelAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderAbilityStrategy.php.html#18"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="IssueAbilityStrategy.php.html#18"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="IssueAbilityStrategy.php.html#51"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::filterDeleteAction">filterDeleteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderAbilityStrategy.php.html#62"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::filterMobileAbilities">filterMobileAbilities</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderAbilityStrategy.php.html#91"><abbr title="App\Services\Ability\Strategies\WorkOrderAbilityStrategy::removeActions">removeActions</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderIssueAbilityStrategy.php.html#17"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::getAbilities">getAbilities</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderIssueAbilityStrategy.php.html#49"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::filterEditAction">filterEditAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="IssueAbilityStrategy.php.html#82"><abbr title="App\Services\Ability\Strategies\IssueAbilityStrategy::isMappedToActiveWorkOrder">isMappedToActiveWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssueAbilityStrategy.php.html#61"><abbr title="App\Services\Ability\Strategies\WorkOrderIssueAbilityStrategy::isMappedToClosedWorkOrder">isMappedToClosedWorkOrder</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([3,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([15,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,14,"<a href=\"IssueAbilityStrategy.php.html#16\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy<\/a>"],[0,11,"<a href=\"WorkOrderAbilityStrategy.php.html#16\">App\\Services\\Ability\\Strategies\\WorkOrderAbilityStrategy<\/a>"],[0,15,"<a href=\"WorkOrderIssueAbilityStrategy.php.html#15\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,3,"<a href=\"IssueAbilityStrategy.php.html#18\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::getAbilities<\/a>"],[0,1,"<a href=\"IssueAbilityStrategy.php.html#40\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::isDeleted<\/a>"],[0,3,"<a href=\"IssueAbilityStrategy.php.html#51\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::filterDeleteAction<\/a>"],[0,4,"<a href=\"IssueAbilityStrategy.php.html#66\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::filterCancelAction<\/a>"],[0,2,"<a href=\"IssueAbilityStrategy.php.html#82\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::isMappedToActiveWorkOrder<\/a>"],[0,1,"<a href=\"IssueAbilityStrategy.php.html#94\">App\\Services\\Ability\\Strategies\\IssueAbilityStrategy::isClosedServiceRequest<\/a>"],[0,4,"<a href=\"WorkOrderAbilityStrategy.php.html#18\">App\\Services\\Ability\\Strategies\\WorkOrderAbilityStrategy::getAbilities<\/a>"],[0,1,"<a href=\"WorkOrderAbilityStrategy.php.html#43\">App\\Services\\Ability\\Strategies\\WorkOrderAbilityStrategy::abilitiesForWorkInProgressStatus<\/a>"],[0,3,"<a href=\"WorkOrderAbilityStrategy.php.html#62\">App\\Services\\Ability\\Strategies\\WorkOrderAbilityStrategy::filterMobileAbilities<\/a>"],[0,3,"<a href=\"WorkOrderAbilityStrategy.php.html#91\">App\\Services\\Ability\\Strategies\\WorkOrderAbilityStrategy::removeActions<\/a>"],[0,3,"<a href=\"WorkOrderIssueAbilityStrategy.php.html#17\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy::getAbilities<\/a>"],[0,1,"<a href=\"WorkOrderIssueAbilityStrategy.php.html#38\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy::isDeleted<\/a>"],[0,3,"<a href=\"WorkOrderIssueAbilityStrategy.php.html#49\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy::filterEditAction<\/a>"],[0,2,"<a href=\"WorkOrderIssueAbilityStrategy.php.html#61\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy::isMappedToClosedWorkOrder<\/a>"],[0,6,"<a href=\"WorkOrderIssueAbilityStrategy.php.html#76\">App\\Services\\Ability\\Strategies\\WorkOrderIssueAbilityStrategy::filterUnusedAbilitiesForMobile<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

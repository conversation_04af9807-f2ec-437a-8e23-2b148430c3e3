<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Traits</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Traits</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AbilityResolverTrait.php.html#7">App\Traits\AbilityResolverTrait</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiExceptionHandler.php.html#24">App\Traits\ApiExceptionHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiResponse.php.html#7">App\Traits\ApiResponse</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BasicNotificationTrait.php.html#10">App\Traits\BasicNotificationTrait</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BindsOnUuid.php.html#8">App\Traits\BindsOnUuid</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filterable.php.html#9">App\Traits\Filterable</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HandleFeatureAuthorization.php.html#9">App\Traits\HandleFeatureAuthorization</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#17">App\Traits\QuoteListFilterTrait</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#15">App\Traits\ServiceRequestListFilterTrait</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#17">App\Traits\TechnicianScheduleTrait</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TransactionHelper.php.html#9">App\Traits\TransactionHelper</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserListFilterTrait.php.html#10">App\Traits\UserListFilterTrait</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidatesJWT.php.html#22">App\Traits\ValidatesJWT</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewTrait.php.html#14">App\Traits\ViewTrait</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#18">App\Traits\WorkOrderListFilterTrait</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#24">App\Traits\GeneratesUuid</a></td><td class="text-right">25%</td></tr>
       <tr><td><a href="BelongsToOrganization.php.html#9">App\Traits\BelongsToOrganization</a></td><td class="text-right">75%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#18">App\Traits\WorkOrderListFilterTrait</a></td><td class="text-right">5852</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#17">App\Traits\TechnicianScheduleTrait</a></td><td class="text-right">3422</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#17">App\Traits\QuoteListFilterTrait</a></td><td class="text-right">3306</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#15">App\Traits\ServiceRequestListFilterTrait</a></td><td class="text-right">2352</td></tr>
       <tr><td><a href="UserListFilterTrait.php.html#10">App\Traits\UserListFilterTrait</a></td><td class="text-right">870</td></tr>
       <tr><td><a href="ViewTrait.php.html#14">App\Traits\ViewTrait</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#24">App\Traits\GeneratesUuid</a></td><td class="text-right">567</td></tr>
       <tr><td><a href="ValidatesJWT.php.html#22">App\Traits\ValidatesJWT</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="TransactionHelper.php.html#9">App\Traits\TransactionHelper</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="BindsOnUuid.php.html#8">App\Traits\BindsOnUuid</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="BasicNotificationTrait.php.html#10">App\Traits\BasicNotificationTrait</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ApiResponse.php.html#7">App\Traits\ApiResponse</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BelongsToOrganization.php.html#9">App\Traits\BelongsToOrganization</a></td><td class="text-right">3</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AbilityResolverTrait.php.html#14"><abbr title="App\Traits\AbilityResolverTrait::abilities">abilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiExceptionHandler.php.html#28"><abbr title="App\Traits\ApiExceptionHandler::handleApiExceptions">handleApiExceptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiResponse.php.html#16"><abbr title="App\Traits\ApiResponse::successResponse">successResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiResponse.php.html#37"><abbr title="App\Traits\ApiResponse::errorResponse">errorResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BasicNotificationTrait.php.html#17"><abbr title="App\Traits\BasicNotificationTrait::getAssigneesToNotify">getAssigneesToNotify</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BasicNotificationTrait.php.html#41"><abbr title="App\Traits\BasicNotificationTrait::getServiceRequestAssigneesToNotify">getServiceRequestAssigneesToNotify</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BelongsToOrganization.php.html#32"><abbr title="App\Traits\BelongsToOrganization::organization">organization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BindsOnUuid.php.html#17"><abbr title="App\Traits\BindsOnUuid::resolveRouteBinding">resolveRouteBinding</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BindsOnUuid.php.html#33"><abbr title="App\Traits\BindsOnUuid::getRouteKeyName">getRouteKeyName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BindsOnUuid.php.html#43"><abbr title="App\Traits\BindsOnUuid::routesIncludeTrashedRecords">routesIncludeTrashedRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Filterable.php.html#14"><abbr title="App\Traits\Filterable::scopeFilter">scopeFilter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#127"><abbr title="App\Traits\GeneratesUuid::scopeWhereUuid">scopeWhereUuid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#148"><abbr title="App\Traits\GeneratesUuid::scopeWhereNotUuid">scopeWhereNotUuid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#169"><abbr title="App\Traits\GeneratesUuid::scopeOrWhereUuid">scopeOrWhereUuid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#190"><abbr title="App\Traits\GeneratesUuid::scopeOrWhereNotUuid">scopeOrWhereNotUuid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#207"><abbr title="App\Traits\GeneratesUuid::bytesFromUuid">bytesFromUuid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#226"><abbr title="App\Traits\GeneratesUuid::normaliseUuids">normaliseUuids</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#242"><abbr title="App\Traits\GeneratesUuid::findUuidColumn">findUuidColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#262"><abbr title="App\Traits\GeneratesUuid::getUuid">getUuid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HandleFeatureAuthorization.php.html#14"><abbr title="App\Traits\HandleFeatureAuthorization::doesNotHaveFeature">doesNotHaveFeature</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#22"><abbr title="App\Traits\QuoteListFilterTrait::findFilterColumn">findFilterColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#36"><abbr title="App\Traits\QuoteListFilterTrait::resolveWhereClauseOperator">resolveWhereClauseOperator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#63"><abbr title="App\Traits\QuoteListFilterTrait::generateQuery">generateQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#172"><abbr title="App\Traits\QuoteListFilterTrait::resolveWhereClause">resolveWhereClause</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#212"><abbr title="App\Traits\QuoteListFilterTrait::filterQuery">filterQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#269"><abbr title="App\Traits\QuoteListFilterTrait::resolveOperatorSetForDateSlug">resolveOperatorSetForDateSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#285"><abbr title="App\Traits\QuoteListFilterTrait::dateValuesForIsOperation">dateValuesForIsOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#339"><abbr title="App\Traits\QuoteListFilterTrait::dateValuesForIsNotOperation">dateValuesForIsNotOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#393"><abbr title="App\Traits\QuoteListFilterTrait::dateValuesForIsAfterOperation">dateValuesForIsAfterOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#416"><abbr title="App\Traits\QuoteListFilterTrait::dateValuesForIsBeForeOperation">dateValuesForIsBeForeOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#439"><abbr title="App\Traits\QuoteListFilterTrait::dateOperationResponse">dateOperationResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#20"><abbr title="App\Traits\ServiceRequestListFilterTrait::findFilterColumn">findFilterColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#31"><abbr title="App\Traits\ServiceRequestListFilterTrait::resolveWhereClauseOperator">resolveWhereClauseOperator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#58"><abbr title="App\Traits\ServiceRequestListFilterTrait::generateQuery">generateQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#115"><abbr title="App\Traits\ServiceRequestListFilterTrait::resolveWhereClause">resolveWhereClause</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#155"><abbr title="App\Traits\ServiceRequestListFilterTrait::filterQuery">filterQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#212"><abbr title="App\Traits\ServiceRequestListFilterTrait::resolveOperatorSetForDateSlug">resolveOperatorSetForDateSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#228"><abbr title="App\Traits\ServiceRequestListFilterTrait::dateValuesForIsOperation">dateValuesForIsOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#270"><abbr title="App\Traits\ServiceRequestListFilterTrait::dateValuesForIsNotOperation">dateValuesForIsNotOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#312"><abbr title="App\Traits\ServiceRequestListFilterTrait::dateValuesForIsAfterOperation">dateValuesForIsAfterOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#335"><abbr title="App\Traits\ServiceRequestListFilterTrait::dateValuesForIsBeForeOperation">dateValuesForIsBeForeOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#358"><abbr title="App\Traits\ServiceRequestListFilterTrait::dateOperationResponse">dateOperationResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#22"><abbr title="App\Traits\TechnicianScheduleTrait::findTechAppointmentsBetweenTwoDays">findTechAppointmentsBetweenTwoDays</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#50"><abbr title="App\Traits\TechnicianScheduleTrait::isOffDay">isOffDay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#71"><abbr title="App\Traits\TechnicianScheduleTrait::filterWithAppointments">filterWithAppointments</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#183"><abbr title="App\Traits\TechnicianScheduleTrait::generateTimingsUnderTheWorkingHours">generateTimingsUnderTheWorkingHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#229"><abbr title="App\Traits\TechnicianScheduleTrait::isWorkingHourWindow">isWorkingHourWindow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#241"><abbr title="App\Traits\TechnicianScheduleTrait::checkDateTimeInsideTheWindow">checkDateTimeInsideTheWindow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#288"><abbr title="App\Traits\TechnicianScheduleTrait::checkIfItIsPassedWindow">checkIfItIsPassedWindow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#303"><abbr title="App\Traits\TechnicianScheduleTrait::findWindowStartEndTime">findWindowStartEndTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#390"><abbr title="App\Traits\TechnicianScheduleTrait::findMostEarliestTimeSlotFromWindow">findMostEarliestTimeSlotFromWindow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TransactionHelper.php.html#71"><abbr title="App\Traits\TransactionHelper::runInTransaction">runInTransaction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TransactionHelper.php.html#100"><abbr title="App\Traits\TransactionHelper::attemptRollback">attemptRollback</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TransactionHelper.php.html#115"><abbr title="App\Traits\TransactionHelper::logTransactionFailure">logTransactionFailure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserListFilterTrait.php.html#23"><abbr title="App\Traits\UserListFilterTrait::filterQuery">filterQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserListFilterTrait.php.html#77"><abbr title="App\Traits\UserListFilterTrait::findFilterColumn">findFilterColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserListFilterTrait.php.html#100"><abbr title="App\Traits\UserListFilterTrait::generateQuery">generateQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserListFilterTrait.php.html#123"><abbr title="App\Traits\UserListFilterTrait::resolveWhereClauseOperator">resolveWhereClauseOperator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserListFilterTrait.php.html#138"><abbr title="App\Traits\UserListFilterTrait::resolveWhereClause">resolveWhereClause</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidatesJWT.php.html#29"><abbr title="App\Traits\ValidatesJWT::validateJWT">validateJWT</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidatesJWT.php.html#54"><abbr title="App\Traits\ValidatesJWT::getJsonWebKeys">getJsonWebKeys</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidatesJWT.php.html#83"><abbr title="App\Traits\ValidatesJWT::getCacheName">getCacheName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidatesJWT.php.html#93"><abbr title="App\Traits\ValidatesJWT::getCacheExpiry">getCacheExpiry</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewTrait.php.html#19"><abbr title="App\Traits\ViewTrait::viewConfig">viewConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewTrait.php.html#60"><abbr title="App\Traits\ViewTrait::createView">createView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewTrait.php.html#89"><abbr title="App\Traits\ViewTrait::setAsDefaultView">setAsDefaultView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewTrait.php.html#125"><abbr title="App\Traits\ViewTrait::pinOrUnpinView">pinOrUnpinView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewTrait.php.html#162"><abbr title="App\Traits\ViewTrait::updateView">updateView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewTrait.php.html#180"><abbr title="App\Traits\ViewTrait::destroyView">destroyView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#24"><abbr title="App\Traits\WorkOrderListFilterTrait::bindUUIDS">bindUUIDS</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#38"><abbr title="App\Traits\WorkOrderListFilterTrait::tripStatusFilterQuery">tripStatusFilterQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#79"><abbr title="App\Traits\WorkOrderListFilterTrait::findFilterColumn">findFilterColumn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#101"><abbr title="App\Traits\WorkOrderListFilterTrait::resolveWhereClauseOperator">resolveWhereClauseOperator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#128"><abbr title="App\Traits\WorkOrderListFilterTrait::generateQuery">generateQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#237"><abbr title="App\Traits\WorkOrderListFilterTrait::resolveWhereClause">resolveWhereClause</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#277"><abbr title="App\Traits\WorkOrderListFilterTrait::filterQuery">filterQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#334"><abbr title="App\Traits\WorkOrderListFilterTrait::resolveOperatorSetForDateSlug">resolveOperatorSetForDateSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#350"><abbr title="App\Traits\WorkOrderListFilterTrait::dateValuesForIsOperation">dateValuesForIsOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#404"><abbr title="App\Traits\WorkOrderListFilterTrait::dateValuesForIsNotOperation">dateValuesForIsNotOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#458"><abbr title="App\Traits\WorkOrderListFilterTrait::dateValuesForIsAfterOperation">dateValuesForIsAfterOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#481"><abbr title="App\Traits\WorkOrderListFilterTrait::dateValuesForIsBeForeOperation">dateValuesForIsBeForeOperation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#504"><abbr title="App\Traits\WorkOrderListFilterTrait::dateOperationResponse">dateOperationResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#105"><abbr title="App\Traits\GeneratesUuid::resolveUuidVersion">resolveUuidVersion</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#86"><abbr title="App\Traits\GeneratesUuid::resolveUuid">resolveUuid</abbr></a></td><td class="text-right">75%</td></tr>
       <tr><td><a href="BelongsToOrganization.php.html#14"><abbr title="App\Traits\BelongsToOrganization::bootBelongsToOrganization">bootBelongsToOrganization</abbr></a></td><td class="text-right">85%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#128"><abbr title="App\Traits\WorkOrderListFilterTrait::generateQuery">generateQuery</abbr></a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#63"><abbr title="App\Traits\QuoteListFilterTrait::generateQuery">generateQuery</abbr></a></td><td class="text-right">506</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#212"><abbr title="App\Traits\QuoteListFilterTrait::filterQuery">filterQuery</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#155"><abbr title="App\Traits\ServiceRequestListFilterTrait::filterQuery">filterQuery</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="UserListFilterTrait.php.html#23"><abbr title="App\Traits\UserListFilterTrait::filterQuery">filterQuery</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#277"><abbr title="App\Traits\WorkOrderListFilterTrait::filterQuery">filterQuery</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#58"><abbr title="App\Traits\ServiceRequestListFilterTrait::generateQuery">generateQuery</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#71"><abbr title="App\Traits\TechnicianScheduleTrait::filterWithAppointments">filterWithAppointments</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#303"><abbr title="App\Traits\TechnicianScheduleTrait::findWindowStartEndTime">findWindowStartEndTime</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#183"><abbr title="App\Traits\TechnicianScheduleTrait::generateTimingsUnderTheWorkingHours">generateTimingsUnderTheWorkingHours</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#241"><abbr title="App\Traits\TechnicianScheduleTrait::checkDateTimeInsideTheWindow">checkDateTimeInsideTheWindow</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#390"><abbr title="App\Traits\TechnicianScheduleTrait::findMostEarliestTimeSlotFromWindow">findMostEarliestTimeSlotFromWindow</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#242"><abbr title="App\Traits\GeneratesUuid::findUuidColumn">findUuidColumn</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#36"><abbr title="App\Traits\QuoteListFilterTrait::resolveWhereClauseOperator">resolveWhereClauseOperator</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#31"><abbr title="App\Traits\ServiceRequestListFilterTrait::resolveWhereClauseOperator">resolveWhereClauseOperator</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ViewTrait.php.html#125"><abbr title="App\Traits\ViewTrait::pinOrUnpinView">pinOrUnpinView</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#38"><abbr title="App\Traits\WorkOrderListFilterTrait::tripStatusFilterQuery">tripStatusFilterQuery</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#101"><abbr title="App\Traits\WorkOrderListFilterTrait::resolveWhereClauseOperator">resolveWhereClauseOperator</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="UserListFilterTrait.php.html#123"><abbr title="App\Traits\UserListFilterTrait::resolveWhereClauseOperator">resolveWhereClauseOperator</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ValidatesJWT.php.html#29"><abbr title="App\Traits\ValidatesJWT::validateJWT">validateJWT</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ViewTrait.php.html#89"><abbr title="App\Traits\ViewTrait::setAsDefaultView">setAsDefaultView</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#262"><abbr title="App\Traits\GeneratesUuid::getUuid">getUuid</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserListFilterTrait.php.html#100"><abbr title="App\Traits\UserListFilterTrait::generateQuery">generateQuery</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ValidatesJWT.php.html#54"><abbr title="App\Traits\ValidatesJWT::getJsonWebKeys">getJsonWebKeys</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ViewTrait.php.html#60"><abbr title="App\Traits\ViewTrait::createView">createView</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BindsOnUuid.php.html#17"><abbr title="App\Traits\BindsOnUuid::resolveRouteBinding">resolveRouteBinding</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#207"><abbr title="App\Traits\GeneratesUuid::bytesFromUuid">bytesFromUuid</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#172"><abbr title="App\Traits\QuoteListFilterTrait::resolveWhereClause">resolveWhereClause</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuoteListFilterTrait.php.html#439"><abbr title="App\Traits\QuoteListFilterTrait::dateOperationResponse">dateOperationResponse</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#115"><abbr title="App\Traits\ServiceRequestListFilterTrait::resolveWhereClause">resolveWhereClause</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestListFilterTrait.php.html#358"><abbr title="App\Traits\ServiceRequestListFilterTrait::dateOperationResponse">dateOperationResponse</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#50"><abbr title="App\Traits\TechnicianScheduleTrait::isOffDay">isOffDay</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#229"><abbr title="App\Traits\TechnicianScheduleTrait::isWorkingHourWindow">isWorkingHourWindow</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TransactionHelper.php.html#71"><abbr title="App\Traits\TransactionHelper::runInTransaction">runInTransaction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TransactionHelper.php.html#100"><abbr title="App\Traits\TransactionHelper::attemptRollback">attemptRollback</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UserListFilterTrait.php.html#138"><abbr title="App\Traits\UserListFilterTrait::resolveWhereClause">resolveWhereClause</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ViewTrait.php.html#162"><abbr title="App\Traits\ViewTrait::updateView">updateView</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ViewTrait.php.html#180"><abbr title="App\Traits\ViewTrait::destroyView">destroyView</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#237"><abbr title="App\Traits\WorkOrderListFilterTrait::resolveWhereClause">resolveWhereClause</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderListFilterTrait.php.html#504"><abbr title="App\Traits\WorkOrderListFilterTrait::dateOperationResponse">dateOperationResponse</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiResponse.php.html#16"><abbr title="App\Traits\ApiResponse::successResponse">successResponse</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BasicNotificationTrait.php.html#17"><abbr title="App\Traits\BasicNotificationTrait::getAssigneesToNotify">getAssigneesToNotify</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BasicNotificationTrait.php.html#41"><abbr title="App\Traits\BasicNotificationTrait::getServiceRequestAssigneesToNotify">getServiceRequestAssigneesToNotify</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#127"><abbr title="App\Traits\GeneratesUuid::scopeWhereUuid">scopeWhereUuid</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#148"><abbr title="App\Traits\GeneratesUuid::scopeWhereNotUuid">scopeWhereNotUuid</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#169"><abbr title="App\Traits\GeneratesUuid::scopeOrWhereUuid">scopeOrWhereUuid</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#190"><abbr title="App\Traits\GeneratesUuid::scopeOrWhereNotUuid">scopeOrWhereNotUuid</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TechnicianScheduleTrait.php.html#288"><abbr title="App\Traits\TechnicianScheduleTrait::checkIfItIsPassedWindow">checkIfItIsPassedWindow</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TransactionHelper.php.html#115"><abbr title="App\Traits\TransactionHelper::logTransactionFailure">logTransactionFailure</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ViewTrait.php.html#19"><abbr title="App\Traits\ViewTrait::viewConfig">viewConfig</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#105"><abbr title="App\Traits\GeneratesUuid::resolveUuidVersion">resolveUuidVersion</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="GeneratesUuid.php.html#86"><abbr title="App\Traits\GeneratesUuid::resolveUuid">resolveUuid</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="BelongsToOrganization.php.html#14"><abbr title="App\Traits\BelongsToOrganization::bootBelongsToOrganization">bootBelongsToOrganization</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Thu Jun 26 15:40:22 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([15,0,0,1,0,0,0,0,1,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([82,0,0,0,0,0,0,1,1,1,0,4], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AbilityResolverTrait.php.html#7\">App\\Traits\\AbilityResolverTrait<\/a>"],[0,1,"<a href=\"ApiExceptionHandler.php.html#24\">App\\Traits\\ApiExceptionHandler<\/a>"],[0,3,"<a href=\"ApiResponse.php.html#7\">App\\Traits\\ApiResponse<\/a>"],[0,4,"<a href=\"BasicNotificationTrait.php.html#10\">App\\Traits\\BasicNotificationTrait<\/a>"],[75,3,"<a href=\"BelongsToOrganization.php.html#9\">App\\Traits\\BelongsToOrganization<\/a>"],[0,5,"<a href=\"BindsOnUuid.php.html#8\">App\\Traits\\BindsOnUuid<\/a>"],[0,1,"<a href=\"Filterable.php.html#9\">App\\Traits\\Filterable<\/a>"],[25.71428571428571,36,"<a href=\"GeneratesUuid.php.html#24\">App\\Traits\\GeneratesUuid<\/a>"],[0,1,"<a href=\"HandleFeatureAuthorization.php.html#9\">App\\Traits\\HandleFeatureAuthorization<\/a>"],[0,57,"<a href=\"QuoteListFilterTrait.php.html#17\">App\\Traits\\QuoteListFilterTrait<\/a>"],[0,48,"<a href=\"ServiceRequestListFilterTrait.php.html#15\">App\\Traits\\ServiceRequestListFilterTrait<\/a>"],[0,58,"<a href=\"TechnicianScheduleTrait.php.html#17\">App\\Traits\\TechnicianScheduleTrait<\/a>"],[0,8,"<a href=\"TransactionHelper.php.html#9\">App\\Traits\\TransactionHelper<\/a>"],[0,29,"<a href=\"UserListFilterTrait.php.html#10\">App\\Traits\\UserListFilterTrait<\/a>"],[0,11,"<a href=\"ValidatesJWT.php.html#22\">App\\Traits\\ValidatesJWT<\/a>"],[0,24,"<a href=\"ViewTrait.php.html#14\">App\\Traits\\ViewTrait<\/a>"],[0,76,"<a href=\"WorkOrderListFilterTrait.php.html#18\">App\\Traits\\WorkOrderListFilterTrait<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AbilityResolverTrait.php.html#14\">App\\Traits\\AbilityResolverTrait::abilities<\/a>"],[0,1,"<a href=\"ApiExceptionHandler.php.html#28\">App\\Traits\\ApiExceptionHandler::handleApiExceptions<\/a>"],[0,2,"<a href=\"ApiResponse.php.html#16\">App\\Traits\\ApiResponse::successResponse<\/a>"],[0,1,"<a href=\"ApiResponse.php.html#37\">App\\Traits\\ApiResponse::errorResponse<\/a>"],[0,2,"<a href=\"BasicNotificationTrait.php.html#17\">App\\Traits\\BasicNotificationTrait::getAssigneesToNotify<\/a>"],[0,2,"<a href=\"BasicNotificationTrait.php.html#41\">App\\Traits\\BasicNotificationTrait::getServiceRequestAssigneesToNotify<\/a>"],[85.71428571428571,2,"<a href=\"BelongsToOrganization.php.html#14\">App\\Traits\\BelongsToOrganization::bootBelongsToOrganization<\/a>"],[0,1,"<a href=\"BelongsToOrganization.php.html#32\">App\\Traits\\BelongsToOrganization::organization<\/a>"],[0,3,"<a href=\"BindsOnUuid.php.html#17\">App\\Traits\\BindsOnUuid::resolveRouteBinding<\/a>"],[0,1,"<a href=\"BindsOnUuid.php.html#33\">App\\Traits\\BindsOnUuid::getRouteKeyName<\/a>"],[0,1,"<a href=\"BindsOnUuid.php.html#43\">App\\Traits\\BindsOnUuid::routesIncludeTrashedRecords<\/a>"],[0,1,"<a href=\"Filterable.php.html#14\">App\\Traits\\Filterable::scopeFilter<\/a>"],[100,5,"<a href=\"GeneratesUuid.php.html#44\">App\\Traits\\GeneratesUuid::bootGeneratesUuid<\/a>"],[100,1,"<a href=\"GeneratesUuid.php.html#68\">App\\Traits\\GeneratesUuid::uuidColumn<\/a>"],[100,1,"<a href=\"GeneratesUuid.php.html#78\">App\\Traits\\GeneratesUuid::uuidColumns<\/a>"],[75,2,"<a href=\"GeneratesUuid.php.html#86\">App\\Traits\\GeneratesUuid::resolveUuid<\/a>"],[100,1,"<a href=\"GeneratesUuid.php.html#97\">App\\Traits\\GeneratesUuid::uuidVersion<\/a>"],[60,3,"<a href=\"GeneratesUuid.php.html#105\">App\\Traits\\GeneratesUuid::resolveUuidVersion<\/a>"],[0,2,"<a href=\"GeneratesUuid.php.html#127\">App\\Traits\\GeneratesUuid::scopeWhereUuid<\/a>"],[0,2,"<a href=\"GeneratesUuid.php.html#148\">App\\Traits\\GeneratesUuid::scopeWhereNotUuid<\/a>"],[0,2,"<a href=\"GeneratesUuid.php.html#169\">App\\Traits\\GeneratesUuid::scopeOrWhereUuid<\/a>"],[0,2,"<a href=\"GeneratesUuid.php.html#190\">App\\Traits\\GeneratesUuid::scopeOrWhereNotUuid<\/a>"],[0,3,"<a href=\"GeneratesUuid.php.html#207\">App\\Traits\\GeneratesUuid::bytesFromUuid<\/a>"],[0,1,"<a href=\"GeneratesUuid.php.html#226\">App\\Traits\\GeneratesUuid::normaliseUuids<\/a>"],[0,7,"<a href=\"GeneratesUuid.php.html#242\">App\\Traits\\GeneratesUuid::findUuidColumn<\/a>"],[0,4,"<a href=\"GeneratesUuid.php.html#262\">App\\Traits\\GeneratesUuid::getUuid<\/a>"],[0,1,"<a href=\"HandleFeatureAuthorization.php.html#14\">App\\Traits\\HandleFeatureAuthorization::doesNotHaveFeature<\/a>"],[0,1,"<a href=\"QuoteListFilterTrait.php.html#22\">App\\Traits\\QuoteListFilterTrait::findFilterColumn<\/a>"],[0,7,"<a href=\"QuoteListFilterTrait.php.html#36\">App\\Traits\\QuoteListFilterTrait::resolveWhereClauseOperator<\/a>"],[0,22,"<a href=\"QuoteListFilterTrait.php.html#63\">App\\Traits\\QuoteListFilterTrait::generateQuery<\/a>"],[0,3,"<a href=\"QuoteListFilterTrait.php.html#172\">App\\Traits\\QuoteListFilterTrait::resolveWhereClause<\/a>"],[0,16,"<a href=\"QuoteListFilterTrait.php.html#212\">App\\Traits\\QuoteListFilterTrait::filterQuery<\/a>"],[0,1,"<a href=\"QuoteListFilterTrait.php.html#269\">App\\Traits\\QuoteListFilterTrait::resolveOperatorSetForDateSlug<\/a>"],[0,1,"<a href=\"QuoteListFilterTrait.php.html#285\">App\\Traits\\QuoteListFilterTrait::dateValuesForIsOperation<\/a>"],[0,1,"<a href=\"QuoteListFilterTrait.php.html#339\">App\\Traits\\QuoteListFilterTrait::dateValuesForIsNotOperation<\/a>"],[0,1,"<a href=\"QuoteListFilterTrait.php.html#393\">App\\Traits\\QuoteListFilterTrait::dateValuesForIsAfterOperation<\/a>"],[0,1,"<a href=\"QuoteListFilterTrait.php.html#416\">App\\Traits\\QuoteListFilterTrait::dateValuesForIsBeForeOperation<\/a>"],[0,3,"<a href=\"QuoteListFilterTrait.php.html#439\">App\\Traits\\QuoteListFilterTrait::dateOperationResponse<\/a>"],[0,1,"<a href=\"ServiceRequestListFilterTrait.php.html#20\">App\\Traits\\ServiceRequestListFilterTrait::findFilterColumn<\/a>"],[0,7,"<a href=\"ServiceRequestListFilterTrait.php.html#31\">App\\Traits\\ServiceRequestListFilterTrait::resolveWhereClauseOperator<\/a>"],[0,13,"<a href=\"ServiceRequestListFilterTrait.php.html#58\">App\\Traits\\ServiceRequestListFilterTrait::generateQuery<\/a>"],[0,3,"<a href=\"ServiceRequestListFilterTrait.php.html#115\">App\\Traits\\ServiceRequestListFilterTrait::resolveWhereClause<\/a>"],[0,16,"<a href=\"ServiceRequestListFilterTrait.php.html#155\">App\\Traits\\ServiceRequestListFilterTrait::filterQuery<\/a>"],[0,1,"<a href=\"ServiceRequestListFilterTrait.php.html#212\">App\\Traits\\ServiceRequestListFilterTrait::resolveOperatorSetForDateSlug<\/a>"],[0,1,"<a href=\"ServiceRequestListFilterTrait.php.html#228\">App\\Traits\\ServiceRequestListFilterTrait::dateValuesForIsOperation<\/a>"],[0,1,"<a href=\"ServiceRequestListFilterTrait.php.html#270\">App\\Traits\\ServiceRequestListFilterTrait::dateValuesForIsNotOperation<\/a>"],[0,1,"<a href=\"ServiceRequestListFilterTrait.php.html#312\">App\\Traits\\ServiceRequestListFilterTrait::dateValuesForIsAfterOperation<\/a>"],[0,1,"<a href=\"ServiceRequestListFilterTrait.php.html#335\">App\\Traits\\ServiceRequestListFilterTrait::dateValuesForIsBeForeOperation<\/a>"],[0,3,"<a href=\"ServiceRequestListFilterTrait.php.html#358\">App\\Traits\\ServiceRequestListFilterTrait::dateOperationResponse<\/a>"],[0,1,"<a href=\"TechnicianScheduleTrait.php.html#22\">App\\Traits\\TechnicianScheduleTrait::findTechAppointmentsBetweenTwoDays<\/a>"],[0,3,"<a href=\"TechnicianScheduleTrait.php.html#50\">App\\Traits\\TechnicianScheduleTrait::isOffDay<\/a>"],[0,12,"<a href=\"TechnicianScheduleTrait.php.html#71\">App\\Traits\\TechnicianScheduleTrait::filterWithAppointments<\/a>"],[0,9,"<a href=\"TechnicianScheduleTrait.php.html#183\">App\\Traits\\TechnicianScheduleTrait::generateTimingsUnderTheWorkingHours<\/a>"],[0,3,"<a href=\"TechnicianScheduleTrait.php.html#229\">App\\Traits\\TechnicianScheduleTrait::isWorkingHourWindow<\/a>"],[0,9,"<a href=\"TechnicianScheduleTrait.php.html#241\">App\\Traits\\TechnicianScheduleTrait::checkDateTimeInsideTheWindow<\/a>"],[0,2,"<a href=\"TechnicianScheduleTrait.php.html#288\">App\\Traits\\TechnicianScheduleTrait::checkIfItIsPassedWindow<\/a>"],[0,10,"<a href=\"TechnicianScheduleTrait.php.html#303\">App\\Traits\\TechnicianScheduleTrait::findWindowStartEndTime<\/a>"],[0,9,"<a href=\"TechnicianScheduleTrait.php.html#390\">App\\Traits\\TechnicianScheduleTrait::findMostEarliestTimeSlotFromWindow<\/a>"],[0,3,"<a href=\"TransactionHelper.php.html#71\">App\\Traits\\TransactionHelper::runInTransaction<\/a>"],[0,3,"<a href=\"TransactionHelper.php.html#100\">App\\Traits\\TransactionHelper::attemptRollback<\/a>"],[0,2,"<a href=\"TransactionHelper.php.html#115\">App\\Traits\\TransactionHelper::logTransactionFailure<\/a>"],[0,16,"<a href=\"UserListFilterTrait.php.html#23\">App\\Traits\\UserListFilterTrait::filterQuery<\/a>"],[0,1,"<a href=\"UserListFilterTrait.php.html#77\">App\\Traits\\UserListFilterTrait::findFilterColumn<\/a>"],[0,4,"<a href=\"UserListFilterTrait.php.html#100\">App\\Traits\\UserListFilterTrait::generateQuery<\/a>"],[0,5,"<a href=\"UserListFilterTrait.php.html#123\">App\\Traits\\UserListFilterTrait::resolveWhereClauseOperator<\/a>"],[0,3,"<a href=\"UserListFilterTrait.php.html#138\">App\\Traits\\UserListFilterTrait::resolveWhereClause<\/a>"],[0,5,"<a href=\"ValidatesJWT.php.html#29\">App\\Traits\\ValidatesJWT::validateJWT<\/a>"],[0,4,"<a href=\"ValidatesJWT.php.html#54\">App\\Traits\\ValidatesJWT::getJsonWebKeys<\/a>"],[0,1,"<a href=\"ValidatesJWT.php.html#83\">App\\Traits\\ValidatesJWT::getCacheName<\/a>"],[0,1,"<a href=\"ValidatesJWT.php.html#93\">App\\Traits\\ValidatesJWT::getCacheExpiry<\/a>"],[0,2,"<a href=\"ViewTrait.php.html#19\">App\\Traits\\ViewTrait::viewConfig<\/a>"],[0,4,"<a href=\"ViewTrait.php.html#60\">App\\Traits\\ViewTrait::createView<\/a>"],[0,5,"<a href=\"ViewTrait.php.html#89\">App\\Traits\\ViewTrait::setAsDefaultView<\/a>"],[0,7,"<a href=\"ViewTrait.php.html#125\">App\\Traits\\ViewTrait::pinOrUnpinView<\/a>"],[0,3,"<a href=\"ViewTrait.php.html#162\">App\\Traits\\ViewTrait::updateView<\/a>"],[0,3,"<a href=\"ViewTrait.php.html#180\">App\\Traits\\ViewTrait::destroyView<\/a>"],[0,1,"<a href=\"WorkOrderListFilterTrait.php.html#24\">App\\Traits\\WorkOrderListFilterTrait::bindUUIDS<\/a>"],[0,7,"<a href=\"WorkOrderListFilterTrait.php.html#38\">App\\Traits\\WorkOrderListFilterTrait::tripStatusFilterQuery<\/a>"],[0,1,"<a href=\"WorkOrderListFilterTrait.php.html#79\">App\\Traits\\WorkOrderListFilterTrait::findFilterColumn<\/a>"],[0,7,"<a href=\"WorkOrderListFilterTrait.php.html#101\">App\\Traits\\WorkOrderListFilterTrait::resolveWhereClauseOperator<\/a>"],[0,33,"<a href=\"WorkOrderListFilterTrait.php.html#128\">App\\Traits\\WorkOrderListFilterTrait::generateQuery<\/a>"],[0,3,"<a href=\"WorkOrderListFilterTrait.php.html#237\">App\\Traits\\WorkOrderListFilterTrait::resolveWhereClause<\/a>"],[0,16,"<a href=\"WorkOrderListFilterTrait.php.html#277\">App\\Traits\\WorkOrderListFilterTrait::filterQuery<\/a>"],[0,1,"<a href=\"WorkOrderListFilterTrait.php.html#334\">App\\Traits\\WorkOrderListFilterTrait::resolveOperatorSetForDateSlug<\/a>"],[0,1,"<a href=\"WorkOrderListFilterTrait.php.html#350\">App\\Traits\\WorkOrderListFilterTrait::dateValuesForIsOperation<\/a>"],[0,1,"<a href=\"WorkOrderListFilterTrait.php.html#404\">App\\Traits\\WorkOrderListFilterTrait::dateValuesForIsNotOperation<\/a>"],[0,1,"<a href=\"WorkOrderListFilterTrait.php.html#458\">App\\Traits\\WorkOrderListFilterTrait::dateValuesForIsAfterOperation<\/a>"],[0,1,"<a href=\"WorkOrderListFilterTrait.php.html#481\">App\\Traits\\WorkOrderListFilterTrait::dateValuesForIsBeForeOperation<\/a>"],[0,3,"<a href=\"WorkOrderListFilterTrait.php.html#504\">App\\Traits\\WorkOrderListFilterTrait::dateOperationResponse<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

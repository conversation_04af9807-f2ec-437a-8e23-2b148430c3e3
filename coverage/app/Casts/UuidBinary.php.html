<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Casts/UuidBinary.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Casts</a></li>
         <li class="breadcrumb-item active">UuidBinary.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="warning">Total</td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="75.00" aria-valuemin="0" aria-valuemax="100" style="width: 75.00%">
           <span class="sr-only">75.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">75.00%</div></td>
       <td class="warning small"><div align="right">6&nbsp;/&nbsp;8</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><abbr title="App\Casts\UuidBinary">UuidBinary</abbr></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="75.00" aria-valuemin="0" aria-valuemax="100" style="width: 75.00%">
           <span class="sr-only">75.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">75.00%</div></td>
       <td class="warning small"><div align="right">6&nbsp;/&nbsp;8</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger small">4.25</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#21"><abbr title="get($model, string $key, $value, array $attributes): ?string">get</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="66.67" aria-valuemin="0" aria-valuemax="100" style="width: 66.67%">
           <span class="sr-only">66.67% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">66.67%</div></td>
       <td class="warning small"><div align="right">2&nbsp;/&nbsp;3</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2.15</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#38"><abbr title="set($model, string $key, $value, array $attributes): ?array">set</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="80.00" aria-valuemin="0" aria-valuemax="100" style="width: 80.00%">
           <span class="sr-only">80.00% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">80.00%</div></td>
       <td class="warning small"><div align="right">4&nbsp;/&nbsp;5</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2.03</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Casts</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Contracts\Database\Eloquent\CastsAttributes</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Ramsey\Uuid\Uuid</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Ramsey\Uuid\UuidInterface</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;@implements&nbsp;CastsAttributes&lt;string,&nbsp;UuidInterface&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">UuidBinary</span><span class="default">&nbsp;</span><span class="keyword">implements</span><span class="default">&nbsp;</span><span class="default">CastsAttributes</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Transform&nbsp;the&nbsp;attribute&nbsp;from&nbsp;the&nbsp;underlying&nbsp;model&nbsp;values.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;\Illuminate\Database\Eloquent\Model&nbsp;&nbsp;$model</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;mixed&nbsp;&nbsp;$value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;mixed&gt;&nbsp;&nbsp;$attributes</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">get</span><span class="keyword">(</span><span class="default">$model</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$key</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$attributes</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="14 tests cover line 23" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_can_be_instantiated_with_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_in_house_user_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_third_party_vendor_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_multiple_uuids_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_for_invalid_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_when_initial_status_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_continues_processing_after_individual_failures&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_with_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_without_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_passes_correct_parameters_to_onboarding_mail&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForOrOperation&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">blank</span><span class="keyword">(</span><span class="default">$value</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="14 tests cover line 27" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_can_be_instantiated_with_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_in_house_user_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_third_party_vendor_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_multiple_uuids_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_for_invalid_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_when_initial_status_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_continues_processing_after_individual_failures&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_with_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_without_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_passes_correct_parameters_to_onboarding_mail&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForOrOperation&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Uuid</span><span class="default">::</span><span class="default">fromBytes</span><span class="keyword">(</span><span class="default">$value</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">toString</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Transform&nbsp;the&nbsp;attribute&nbsp;to&nbsp;its&nbsp;underlying&nbsp;model&nbsp;values.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;\Illuminate\Database\Eloquent\Model&nbsp;&nbsp;$model</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;mixed&nbsp;&nbsp;$value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;mixed&gt;&nbsp;&nbsp;$attributes</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&lt;mixed&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">set</span><span class="keyword">(</span><span class="default">$model</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$key</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$value</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$attributes</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="37 tests cover line 40" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_can_be_instantiated_with_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_in_house_user_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_third_party_vendor_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_multiple_uuids_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_for_invalid_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_non_existent_user_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_when_initial_status_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_continues_processing_after_individual_failures&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::minutes_to_days_simple_method_converts_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_with_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_without_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_passes_correct_parameters_to_onboarding_mail&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_empty_uuids_array&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_implements_should_queue_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_uses_correct_queue_traits&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::public_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::authenticated_user_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::work_order_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::service_request_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::quote_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::view_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_onboarding_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::technician_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::notification_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::lookup_and_tag_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::middleware_groups_are_properly_applied&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::signed_routes_have_signed_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::throttled_routes_have_throttle_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::resource_routes_have_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::api_routes_have_correct_prefixes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::nested_resource_routes_are_properly_structured&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::route_methods_are_correctly_defined&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">blank</span><span class="keyword">(</span><span class="default">$value</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">null</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="37 tests cover line 44" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_can_be_instantiated_with_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_in_house_user_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_third_party_vendor_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_multiple_uuids_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_for_invalid_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_non_existent_user_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_when_initial_status_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_continues_processing_after_individual_failures&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::minutes_to_days_simple_method_converts_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_with_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_without_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_passes_correct_parameters_to_onboarding_mail&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_empty_uuids_array&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_implements_should_queue_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_uses_correct_queue_traits&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::public_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::authenticated_user_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::work_order_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::service_request_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::quote_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::view_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_onboarding_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::technician_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::notification_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::lookup_and_tag_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::middleware_groups_are_properly_applied&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::signed_routes_have_signed_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::throttled_routes_have_throttle_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::resource_routes_have_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::api_routes_have_correct_prefixes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::nested_resource_routes_are_properly_structured&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::route_methods_are_correctly_defined&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="37 tests cover line 45" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_can_be_instantiated_with_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_in_house_user_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_third_party_vendor_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_multiple_uuids_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_for_invalid_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_non_existent_user_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_when_initial_status_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_continues_processing_after_individual_failures&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::minutes_to_days_simple_method_converts_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_with_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_without_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_passes_correct_parameters_to_onboarding_mail&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_empty_uuids_array&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_implements_should_queue_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_uses_correct_queue_traits&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::public_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::authenticated_user_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::work_order_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::service_request_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::quote_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::view_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_onboarding_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::technician_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::notification_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::lookup_and_tag_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::middleware_groups_are_properly_applied&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::signed_routes_have_signed_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::throttled_routes_have_throttle_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::resource_routes_have_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::api_routes_have_correct_prefixes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::nested_resource_routes_are_properly_structured&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::route_methods_are_correctly_defined&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$key</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">Uuid</span><span class="default">::</span><span class="default">fromString</span><span class="keyword">(</span><span class="default">strtolower</span><span class="keyword">(</span><span class="default">$value</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">getBytes</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="37 tests cover line 46" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_can_be_instantiated_with_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_in_house_user_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_third_party_vendor_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_multiple_uuids_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_for_invalid_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_non_existent_user_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_when_initial_status_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_continues_processing_after_individual_failures&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::minutes_to_days_simple_method_converts_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_with_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_without_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_passes_correct_parameters_to_onboarding_mail&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_empty_uuids_array&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_implements_should_queue_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_uses_correct_queue_traits&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::public_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::authenticated_user_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::work_order_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::service_request_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::quote_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::view_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_onboarding_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::technician_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::notification_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::lookup_and_tag_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::middleware_groups_are_properly_applied&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::signed_routes_have_signed_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::throttled_routes_have_throttle_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::resource_routes_have_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::api_routes_have_correct_prefixes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::nested_resource_routes_are_properly_structured&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::route_methods_are_correctly_defined&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

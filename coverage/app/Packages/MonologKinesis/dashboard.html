<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Packages/MonologKinesis</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Packages</a></li>
         <li class="breadcrumb-item"><a href="index.html">MonologKinesis</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Formatter.php.html#10">App\Packages\MonologKinesis\Formatter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handler.php.html#10">App\Packages\MonologKinesis\Handler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Kinesis.php.html#10">App\Packages\MonologKinesis\Kinesis</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoggerFactory.php.html#11">App\Packages\MonologKinesis\LoggerFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MonologKinesisServiceProvider.php.html#10">App\Packages\MonologKinesis\MonologKinesisServiceProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplicationAwareFormatter.php.html#5">App\Packages\MonologKinesis\ApplicationAwareFormatter</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Kinesis.php.html#10">App\Packages\MonologKinesis\Kinesis</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Handler.php.html#10">App\Packages\MonologKinesis\Handler</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Formatter.php.html#22"><abbr title="App\Packages\MonologKinesis\Formatter::format">format</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Formatter.php.html#52"><abbr title="App\Packages\MonologKinesis\Formatter::formatBatch">formatBatch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handler.php.html#22"><abbr title="App\Packages\MonologKinesis\Handler::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handler.php.html#39"><abbr title="App\Packages\MonologKinesis\Handler::handleBatch">handleBatch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handler.php.html#54"><abbr title="App\Packages\MonologKinesis\Handler::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handler.php.html#62"><abbr title="App\Packages\MonologKinesis\Handler::write">write</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Kinesis.php.html#21"><abbr title="App\Packages\MonologKinesis\Kinesis::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Kinesis.php.html#32"><abbr title="App\Packages\MonologKinesis\Kinesis::configure">configure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Kinesis.php.html#58"><abbr title="App\Packages\MonologKinesis\Kinesis::putRecord">putRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Kinesis.php.html#68"><abbr title="App\Packages\MonologKinesis\Kinesis::putRecords">putRecords</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Kinesis.php.html#78"><abbr title="App\Packages\MonologKinesis\Kinesis::getAwsConfig">getAwsConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Kinesis.php.html#88"><abbr title="App\Packages\MonologKinesis\Kinesis::configHasCredentials">configHasCredentials</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoggerFactory.php.html#24"><abbr title="App\Packages\MonologKinesis\LoggerFactory::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoggerFactory.php.html#37"><abbr title="App\Packages\MonologKinesis\LoggerFactory::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MonologKinesisServiceProvider.php.html#17"><abbr title="App\Packages\MonologKinesis\MonologKinesisServiceProvider::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MonologKinesisServiceProvider.php.html#38"><abbr title="App\Packages\MonologKinesis\MonologKinesisServiceProvider::boot">boot</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplicationAwareFormatter.php.html#24"><abbr title="App\Packages\MonologKinesis\ApplicationAwareFormatter::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Kinesis.php.html#32"><abbr title="App\Packages\MonologKinesis\Kinesis::configure">configure</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Kinesis.php.html#88"><abbr title="App\Packages\MonologKinesis\Kinesis::configHasCredentials">configHasCredentials</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Handler.php.html#39"><abbr title="App\Packages\MonologKinesis\Handler::handleBatch">handleBatch</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Handler.php.html#62"><abbr title="App\Packages\MonologKinesis\Handler::write">write</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([6,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([17,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"Formatter.php.html#10\">App\\Packages\\MonologKinesis\\Formatter<\/a>"],[0,6,"<a href=\"Handler.php.html#10\">App\\Packages\\MonologKinesis\\Handler<\/a>"],[0,10,"<a href=\"Kinesis.php.html#10\">App\\Packages\\MonologKinesis\\Kinesis<\/a>"],[0,2,"<a href=\"LoggerFactory.php.html#11\">App\\Packages\\MonologKinesis\\LoggerFactory<\/a>"],[0,2,"<a href=\"MonologKinesisServiceProvider.php.html#10\">App\\Packages\\MonologKinesis\\MonologKinesisServiceProvider<\/a>"],[0,1,"<a href=\"ApplicationAwareFormatter.php.html#5\">App\\Packages\\MonologKinesis\\ApplicationAwareFormatter<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Formatter.php.html#22\">App\\Packages\\MonologKinesis\\Formatter::format<\/a>"],[0,1,"<a href=\"Formatter.php.html#52\">App\\Packages\\MonologKinesis\\Formatter::formatBatch<\/a>"],[0,1,"<a href=\"Handler.php.html#22\">App\\Packages\\MonologKinesis\\Handler::__construct<\/a>"],[0,2,"<a href=\"Handler.php.html#39\">App\\Packages\\MonologKinesis\\Handler::handleBatch<\/a>"],[0,1,"<a href=\"Handler.php.html#54\">App\\Packages\\MonologKinesis\\Handler::getClient<\/a>"],[0,2,"<a href=\"Handler.php.html#62\">App\\Packages\\MonologKinesis\\Handler::write<\/a>"],[0,1,"<a href=\"Kinesis.php.html#21\">App\\Packages\\MonologKinesis\\Kinesis::__construct<\/a>"],[0,3,"<a href=\"Kinesis.php.html#32\">App\\Packages\\MonologKinesis\\Kinesis::configure<\/a>"],[0,1,"<a href=\"Kinesis.php.html#58\">App\\Packages\\MonologKinesis\\Kinesis::putRecord<\/a>"],[0,1,"<a href=\"Kinesis.php.html#68\">App\\Packages\\MonologKinesis\\Kinesis::putRecords<\/a>"],[0,1,"<a href=\"Kinesis.php.html#78\">App\\Packages\\MonologKinesis\\Kinesis::getAwsConfig<\/a>"],[0,3,"<a href=\"Kinesis.php.html#88\">App\\Packages\\MonologKinesis\\Kinesis::configHasCredentials<\/a>"],[0,1,"<a href=\"LoggerFactory.php.html#24\">App\\Packages\\MonologKinesis\\LoggerFactory::__construct<\/a>"],[0,1,"<a href=\"LoggerFactory.php.html#37\">App\\Packages\\MonologKinesis\\LoggerFactory::create<\/a>"],[0,1,"<a href=\"MonologKinesisServiceProvider.php.html#17\">App\\Packages\\MonologKinesis\\MonologKinesisServiceProvider::register<\/a>"],[0,1,"<a href=\"MonologKinesisServiceProvider.php.html#38\">App\\Packages\\MonologKinesis\\MonologKinesisServiceProvider::boot<\/a>"],[0,1,"<a href=\"ApplicationAwareFormatter.php.html#24\">App\\Packages\\MonologKinesis\\ApplicationAwareFormatter::__construct<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

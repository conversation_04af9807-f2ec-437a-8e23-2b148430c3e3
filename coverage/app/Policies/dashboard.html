<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Policies</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Policies</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#8">App\Policies\DatabaseNotificationPolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#11">App\Policies\InvoicePolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssuePolicy.php.html#12">App\Policies\IssuePolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#10">App\Policies\OrganizationPolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#11">App\Policies\QuotePolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePolicy.php.html#10">App\Policies\RolePolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaPolicy.php.html#11">App\Policies\ServiceRequestMediaPolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNotePolicy.php.html#10">App\Policies\ServiceRequestNotePolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#10">App\Policies\ServiceRequestPolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagPolicy.php.html#10">App\Policies\TagPolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#13">App\Policies\UserPolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserWorkOrderBookmarkPolicy.php.html#10">App\Policies\UserWorkOrderBookmarkPolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPolicy.php.html#10">App\Policies\VendorPolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewPolicy.php.html#12">App\Policies\ViewPolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaPolicy.php.html#11">App\Policies\WorkOrderMediaPolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNotePolicy.php.html#10">App\Policies\WorkOrderNotePolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#13">App\Policies\WorkOrderPolicy</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskPolicy.php.html#9">App\Policies\WorkOrderTaskPolicy</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderPolicy.php.html#13">App\Policies\WorkOrderPolicy</a></td><td class="text-right">12656</td></tr>
       <tr><td><a href="ViewPolicy.php.html#12">App\Policies\ViewPolicy</a></td><td class="text-right">8190</td></tr>
       <tr><td><a href="UserPolicy.php.html#13">App\Policies\UserPolicy</a></td><td class="text-right">4422</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#10">App\Policies\ServiceRequestPolicy</a></td><td class="text-right">2352</td></tr>
       <tr><td><a href="QuotePolicy.php.html#11">App\Policies\QuotePolicy</a></td><td class="text-right">1406</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#11">App\Policies\InvoicePolicy</a></td><td class="text-right">930</td></tr>
       <tr><td><a href="TagPolicy.php.html#10">App\Policies\TagPolicy</a></td><td class="text-right">462</td></tr>
       <tr><td><a href="VendorPolicy.php.html#10">App\Policies\VendorPolicy</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="IssuePolicy.php.html#12">App\Policies\IssuePolicy</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="RolePolicy.php.html#10">App\Policies\RolePolicy</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#8">App\Policies\DatabaseNotificationPolicy</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="ServiceRequestNotePolicy.php.html#10">App\Policies\ServiceRequestNotePolicy</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="WorkOrderNotePolicy.php.html#10">App\Policies\WorkOrderNotePolicy</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#10">App\Policies\OrganizationPolicy</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="UserWorkOrderBookmarkPolicy.php.html#10">App\Policies\UserWorkOrderBookmarkPolicy</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ServiceRequestMediaPolicy.php.html#11">App\Policies\ServiceRequestMediaPolicy</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="WorkOrderMediaPolicy.php.html#11">App\Policies\WorkOrderMediaPolicy</a></td><td class="text-right">90</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#13"><abbr title="App\Policies\DatabaseNotificationPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#21"><abbr title="App\Policies\DatabaseNotificationPolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#29"><abbr title="App\Policies\DatabaseNotificationPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#37"><abbr title="App\Policies\DatabaseNotificationPolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#45"><abbr title="App\Policies\DatabaseNotificationPolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#53"><abbr title="App\Policies\DatabaseNotificationPolicy::restore">restore</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#61"><abbr title="App\Policies\DatabaseNotificationPolicy::forceDelete">forceDelete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#66"><abbr title="App\Policies\DatabaseNotificationPolicy::read">read</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#79"><abbr title="App\Policies\DatabaseNotificationPolicy::clear">clear</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#92"><abbr title="App\Policies\DatabaseNotificationPolicy::unClear">unClear</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#18"><abbr title="App\Policies\InvoicePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#34"><abbr title="App\Policies\InvoicePolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#54"><abbr title="App\Policies\InvoicePolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#74"><abbr title="App\Policies\InvoicePolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#82"><abbr title="App\Policies\InvoicePolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#103"><abbr title="App\Policies\InvoicePolicy::restore">restore</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#111"><abbr title="App\Policies\InvoicePolicy::forceDelete">forceDelete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#119"><abbr title="App\Policies\InvoicePolicy::fullyPaid">fullyPaid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#139"><abbr title="App\Policies\InvoicePolicy::partiallyPaid">partiallyPaid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#159"><abbr title="App\Policies\InvoicePolicy::voided">voided</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssuePolicy.php.html#19"><abbr title="App\Policies\IssuePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssuePolicy.php.html#35"><abbr title="App\Policies\IssuePolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssuePolicy.php.html#51"><abbr title="App\Policies\IssuePolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssuePolicy.php.html#71"><abbr title="App\Policies\IssuePolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssuePolicy.php.html#92"><abbr title="App\Policies\IssuePolicy::cancel">cancel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#17"><abbr title="App\Policies\OrganizationPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#25"><abbr title="App\Policies\OrganizationPolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#33"><abbr title="App\Policies\OrganizationPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#41"><abbr title="App\Policies\OrganizationPolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#49"><abbr title="App\Policies\OrganizationPolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#57"><abbr title="App\Policies\OrganizationPolicy::restore">restore</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#65"><abbr title="App\Policies\OrganizationPolicy::forceDelete">forceDelete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#73"><abbr title="App\Policies\OrganizationPolicy::templateView">templateView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#89"><abbr title="App\Policies\OrganizationPolicy::templateUpdate">templateUpdate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#18"><abbr title="App\Policies\QuotePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#34"><abbr title="App\Policies\QuotePolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#42"><abbr title="App\Policies\QuotePolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#50"><abbr title="App\Policies\QuotePolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#58"><abbr title="App\Policies\QuotePolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#66"><abbr title="App\Policies\QuotePolicy::restore">restore</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#74"><abbr title="App\Policies\QuotePolicy::approve">approve</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#94"><abbr title="App\Policies\QuotePolicy::reject">reject</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#114"><abbr title="App\Policies\QuotePolicy::createQuoteTask">createQuoteTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#134"><abbr title="App\Policies\QuotePolicy::updateQuoteTask">updateQuoteTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#158"><abbr title="App\Policies\QuotePolicy::deleteQuoteTask">deleteQuoteTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuotePolicy.php.html#182"><abbr title="App\Policies\QuotePolicy::submitForApproval">submitForApproval</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePolicy.php.html#17"><abbr title="App\Policies\RolePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePolicy.php.html#33"><abbr title="App\Policies\RolePolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePolicy.php.html#53"><abbr title="App\Policies\RolePolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePolicy.php.html#69"><abbr title="App\Policies\RolePolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RolePolicy.php.html#89"><abbr title="App\Policies\RolePolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaPolicy.php.html#18"><abbr title="App\Policies\ServiceRequestMediaPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaPolicy.php.html#38"><abbr title="App\Policies\ServiceRequestMediaPolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNotePolicy.php.html#17"><abbr title="App\Policies\ServiceRequestNotePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNotePolicy.php.html#33"><abbr title="App\Policies\ServiceRequestNotePolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNotePolicy.php.html#49"><abbr title="App\Policies\ServiceRequestNotePolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNotePolicy.php.html#69"><abbr title="App\Policies\ServiceRequestNotePolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#17"><abbr title="App\Policies\ServiceRequestPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#33"><abbr title="App\Policies\ServiceRequestPolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#58"><abbr title="App\Policies\ServiceRequestPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#81"><abbr title="App\Policies\ServiceRequestPolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#101"><abbr title="App\Policies\ServiceRequestPolicy::manageAssignee">manageAssignee</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#121"><abbr title="App\Policies\ServiceRequestPolicy::close">close</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#141"><abbr title="App\Policies\ServiceRequestPolicy::inprogress">inprogress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#161"><abbr title="App\Policies\ServiceRequestPolicy::readyToSchedule">readyToSchedule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#181"><abbr title="App\Policies\ServiceRequestPolicy::requestResidentAvailability">requestResidentAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#201"><abbr title="App\Policies\ServiceRequestPolicy::addAvailability">addAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#221"><abbr title="App\Policies\ServiceRequestPolicy::updateAvailability">updateAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#241"><abbr title="App\Policies\ServiceRequestPolicy::viewActivityLogs">viewActivityLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagPolicy.php.html#17"><abbr title="App\Policies\TagPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagPolicy.php.html#33"><abbr title="App\Policies\TagPolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagPolicy.php.html#53"><abbr title="App\Policies\TagPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagPolicy.php.html#69"><abbr title="App\Policies\TagPolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagPolicy.php.html#89"><abbr title="App\Policies\TagPolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagPolicy.php.html#109"><abbr title="App\Policies\TagPolicy::restore">restore</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagPolicy.php.html#117"><abbr title="App\Policies\TagPolicy::forceDelete">forceDelete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#20"><abbr title="App\Policies\UserPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#37"><abbr title="App\Policies\UserPolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#57"><abbr title="App\Policies\UserPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#73"><abbr title="App\Policies\UserPolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#93"><abbr title="App\Policies\UserPolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#109"><abbr title="App\Policies\UserPolicy::restore">restore</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#117"><abbr title="App\Policies\UserPolicy::forceDelete">forceDelete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#125"><abbr title="App\Policies\UserPolicy::updateWorkingHours">updateWorkingHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#145"><abbr title="App\Policies\UserPolicy::importWorkingHours">importWorkingHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#165"><abbr title="App\Policies\UserPolicy::updateTechnicianSkill">updateTechnicianSkill</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#185"><abbr title="App\Policies\UserPolicy::importTechnicianSkill">importTechnicianSkill</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#205"><abbr title="App\Policies\UserPolicy::viewTechnicianBlockOut">viewTechnicianBlockOut</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#233"><abbr title="App\Policies\UserPolicy::createTechnicianBlockOut">createTechnicianBlockOut</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#253"><abbr title="App\Policies\UserPolicy::updateTechnicianBlockOut">updateTechnicianBlockOut</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#281"><abbr title="App\Policies\UserPolicy::deleteTechnicianBlockOut">deleteTechnicianBlockOut</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#309"><abbr title="App\Policies\UserPolicy::accessApp">accessApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserPolicy.php.html#331"><abbr title="App\Policies\UserPolicy::scheduleTrip">scheduleTrip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserWorkOrderBookmarkPolicy.php.html#17"><abbr title="App\Policies\UserWorkOrderBookmarkPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserWorkOrderBookmarkPolicy.php.html#33"><abbr title="App\Policies\UserWorkOrderBookmarkPolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserWorkOrderBookmarkPolicy.php.html#41"><abbr title="App\Policies\UserWorkOrderBookmarkPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserWorkOrderBookmarkPolicy.php.html#57"><abbr title="App\Policies\UserWorkOrderBookmarkPolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserWorkOrderBookmarkPolicy.php.html#65"><abbr title="App\Policies\UserWorkOrderBookmarkPolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserWorkOrderBookmarkPolicy.php.html#73"><abbr title="App\Policies\UserWorkOrderBookmarkPolicy::restore">restore</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserWorkOrderBookmarkPolicy.php.html#81"><abbr title="App\Policies\UserWorkOrderBookmarkPolicy::forceDelete">forceDelete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPolicy.php.html#17"><abbr title="App\Policies\VendorPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPolicy.php.html#33"><abbr title="App\Policies\VendorPolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPolicy.php.html#49"><abbr title="App\Policies\VendorPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPolicy.php.html#65"><abbr title="App\Policies\VendorPolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPolicy.php.html#81"><abbr title="App\Policies\VendorPolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPolicy.php.html#97"><abbr title="App\Policies\VendorPolicy::restore">restore</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPolicy.php.html#105"><abbr title="App\Policies\VendorPolicy::forceDelete">forceDelete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPolicy.php.html#113"><abbr title="App\Policies\VendorPolicy::sync">sync</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewPolicy.php.html#19"><abbr title="App\Policies\ViewPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewPolicy.php.html#57"><abbr title="App\Policies\ViewPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewPolicy.php.html#94"><abbr title="App\Policies\ViewPolicy::duplicate">duplicate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewPolicy.php.html#142"><abbr title="App\Policies\ViewPolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewPolicy.php.html#196"><abbr title="App\Policies\ViewPolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewPolicy.php.html#248"><abbr title="App\Policies\ViewPolicy::setDefault">setDefault</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewPolicy.php.html#300"><abbr title="App\Policies\ViewPolicy::pinOrUnpin">pinOrUnpin</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaPolicy.php.html#18"><abbr title="App\Policies\WorkOrderMediaPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaPolicy.php.html#38"><abbr title="App\Policies\WorkOrderMediaPolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNotePolicy.php.html#17"><abbr title="App\Policies\WorkOrderNotePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNotePolicy.php.html#33"><abbr title="App\Policies\WorkOrderNotePolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNotePolicy.php.html#49"><abbr title="App\Policies\WorkOrderNotePolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNotePolicy.php.html#69"><abbr title="App\Policies\WorkOrderNotePolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#20"><abbr title="App\Policies\WorkOrderPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#36"><abbr title="App\Policies\WorkOrderPolicy::viewVendorWorkOrderList">viewVendorWorkOrderList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#53"><abbr title="App\Policies\WorkOrderPolicy::viewVendorWorkOrderShow">viewVendorWorkOrderShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#70"><abbr title="App\Policies\WorkOrderPolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#102"><abbr title="App\Policies\WorkOrderPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#124"><abbr title="App\Policies\WorkOrderPolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#144"><abbr title="App\Policies\WorkOrderPolicy::cancel">cancel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#164"><abbr title="App\Policies\WorkOrderPolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#184"><abbr title="App\Policies\WorkOrderPolicy::viewActivityLogs">viewActivityLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#201"><abbr title="App\Policies\WorkOrderPolicy::readyToSchedule">readyToSchedule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#218"><abbr title="App\Policies\WorkOrderPolicy::resolve">resolve</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#235"><abbr title="App\Policies\WorkOrderPolicy::reOpen">reOpen</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#252"><abbr title="App\Policies\WorkOrderPolicy::cancelAutoSchedule">cancelAutoSchedule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#272"><abbr title="App\Policies\WorkOrderPolicy::schedule">schedule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#292"><abbr title="App\Policies\WorkOrderPolicy::enroute">enroute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#312"><abbr title="App\Policies\WorkOrderPolicy::startTimer">startTimer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#329"><abbr title="App\Policies\WorkOrderPolicy::changeTags">changeTags</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#346"><abbr title="App\Policies\WorkOrderPolicy::createTags">createTags</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#366"><abbr title="App\Policies\WorkOrderPolicy::pause">pause</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#386"><abbr title="App\Policies\WorkOrderPolicy::stopTrip">stopTrip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#406"><abbr title="App\Policies\WorkOrderPolicy::complete">complete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#426"><abbr title="App\Policies\WorkOrderPolicy::submitQuote">submitQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#446"><abbr title="App\Policies\WorkOrderPolicy::updateTrip">updateTrip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#466"><abbr title="App\Policies\WorkOrderPolicy::manageAssignee">manageAssignee</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#486"><abbr title="App\Policies\WorkOrderPolicy::readyToInvoice">readyToInvoice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#506"><abbr title="App\Policies\WorkOrderPolicy::pauseTrip">pauseTrip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#526"><abbr title="App\Policies\WorkOrderPolicy::resumeTrip">resumeTrip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskPolicy.php.html#16"><abbr title="App\Policies\WorkOrderTaskPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskPolicy.php.html#24"><abbr title="App\Policies\WorkOrderTaskPolicy::view">view</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskPolicy.php.html#32"><abbr title="App\Policies\WorkOrderTaskPolicy::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskPolicy.php.html#40"><abbr title="App\Policies\WorkOrderTaskPolicy::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskPolicy.php.html#48"><abbr title="App\Policies\WorkOrderTaskPolicy::delete">delete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskPolicy.php.html#56"><abbr title="App\Policies\WorkOrderTaskPolicy::restore">restore</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderTaskPolicy.php.html#64"><abbr title="App\Policies\WorkOrderTaskPolicy::forceDelete">forceDelete</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ViewPolicy.php.html#300"><abbr title="App\Policies\ViewPolicy::pinOrUnpin">pinOrUnpin</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ViewPolicy.php.html#142"><abbr title="App\Policies\ViewPolicy::update">update</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ViewPolicy.php.html#196"><abbr title="App\Policies\ViewPolicy::delete">delete</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ViewPolicy.php.html#248"><abbr title="App\Policies\ViewPolicy::setDefault">setDefault</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ViewPolicy.php.html#94"><abbr title="App\Policies\ViewPolicy::duplicate">duplicate</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ViewPolicy.php.html#19"><abbr title="App\Policies\ViewPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ViewPolicy.php.html#57"><abbr title="App\Policies\ViewPolicy::create">create</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#70"><abbr title="App\Policies\WorkOrderPolicy::view">view</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="UserPolicy.php.html#205"><abbr title="App\Policies\UserPolicy::viewTechnicianBlockOut">viewTechnicianBlockOut</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="QuotePolicy.php.html#134"><abbr title="App\Policies\QuotePolicy::updateQuoteTask">updateQuoteTask</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="QuotePolicy.php.html#158"><abbr title="App\Policies\QuotePolicy::deleteQuoteTask">deleteQuoteTask</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="UserPolicy.php.html#253"><abbr title="App\Policies\UserPolicy::updateTechnicianBlockOut">updateTechnicianBlockOut</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="UserPolicy.php.html#281"><abbr title="App\Policies\UserPolicy::deleteTechnicianBlockOut">deleteTechnicianBlockOut</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#102"><abbr title="App\Policies\WorkOrderPolicy::create">create</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="QuotePolicy.php.html#114"><abbr title="App\Policies\QuotePolicy::createQuoteTask">createQuoteTask</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequestMediaPolicy.php.html#38"><abbr title="App\Policies\ServiceRequestMediaPolicy::delete">delete</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#33"><abbr title="App\Policies\ServiceRequestPolicy::view">view</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderMediaPolicy.php.html#38"><abbr title="App\Policies\WorkOrderMediaPolicy::delete">delete</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#34"><abbr title="App\Policies\InvoicePolicy::view">view</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#54"><abbr title="App\Policies\InvoicePolicy::create">create</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#82"><abbr title="App\Policies\InvoicePolicy::delete">delete</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#119"><abbr title="App\Policies\InvoicePolicy::fullyPaid">fullyPaid</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#139"><abbr title="App\Policies\InvoicePolicy::partiallyPaid">partiallyPaid</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#159"><abbr title="App\Policies\InvoicePolicy::voided">voided</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="IssuePolicy.php.html#51"><abbr title="App\Policies\IssuePolicy::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="IssuePolicy.php.html#71"><abbr title="App\Policies\IssuePolicy::delete">delete</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="IssuePolicy.php.html#92"><abbr title="App\Policies\IssuePolicy::cancel">cancel</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QuotePolicy.php.html#74"><abbr title="App\Policies\QuotePolicy::approve">approve</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QuotePolicy.php.html#94"><abbr title="App\Policies\QuotePolicy::reject">reject</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QuotePolicy.php.html#182"><abbr title="App\Policies\QuotePolicy::submitForApproval">submitForApproval</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RolePolicy.php.html#33"><abbr title="App\Policies\RolePolicy::view">view</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RolePolicy.php.html#69"><abbr title="App\Policies\RolePolicy::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RolePolicy.php.html#89"><abbr title="App\Policies\RolePolicy::delete">delete</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestMediaPolicy.php.html#18"><abbr title="App\Policies\ServiceRequestMediaPolicy::create">create</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestNotePolicy.php.html#49"><abbr title="App\Policies\ServiceRequestNotePolicy::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestNotePolicy.php.html#69"><abbr title="App\Policies\ServiceRequestNotePolicy::delete">delete</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#58"><abbr title="App\Policies\ServiceRequestPolicy::create">create</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#81"><abbr title="App\Policies\ServiceRequestPolicy::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#101"><abbr title="App\Policies\ServiceRequestPolicy::manageAssignee">manageAssignee</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#121"><abbr title="App\Policies\ServiceRequestPolicy::close">close</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#141"><abbr title="App\Policies\ServiceRequestPolicy::inprogress">inprogress</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#161"><abbr title="App\Policies\ServiceRequestPolicy::readyToSchedule">readyToSchedule</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#181"><abbr title="App\Policies\ServiceRequestPolicy::requestResidentAvailability">requestResidentAvailability</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#201"><abbr title="App\Policies\ServiceRequestPolicy::addAvailability">addAvailability</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#221"><abbr title="App\Policies\ServiceRequestPolicy::updateAvailability">updateAvailability</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#241"><abbr title="App\Policies\ServiceRequestPolicy::viewActivityLogs">viewActivityLogs</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TagPolicy.php.html#17"><abbr title="App\Policies\TagPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TagPolicy.php.html#33"><abbr title="App\Policies\TagPolicy::view">view</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TagPolicy.php.html#69"><abbr title="App\Policies\TagPolicy::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TagPolicy.php.html#89"><abbr title="App\Policies\TagPolicy::delete">delete</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserPolicy.php.html#37"><abbr title="App\Policies\UserPolicy::view">view</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserPolicy.php.html#73"><abbr title="App\Policies\UserPolicy::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserPolicy.php.html#125"><abbr title="App\Policies\UserPolicy::updateWorkingHours">updateWorkingHours</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserPolicy.php.html#145"><abbr title="App\Policies\UserPolicy::importWorkingHours">importWorkingHours</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserPolicy.php.html#165"><abbr title="App\Policies\UserPolicy::updateTechnicianSkill">updateTechnicianSkill</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserPolicy.php.html#185"><abbr title="App\Policies\UserPolicy::importTechnicianSkill">importTechnicianSkill</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserPolicy.php.html#233"><abbr title="App\Policies\UserPolicy::createTechnicianBlockOut">createTechnicianBlockOut</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserPolicy.php.html#309"><abbr title="App\Policies\UserPolicy::accessApp">accessApp</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserPolicy.php.html#331"><abbr title="App\Policies\UserPolicy::scheduleTrip">scheduleTrip</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderMediaPolicy.php.html#18"><abbr title="App\Policies\WorkOrderMediaPolicy::create">create</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderNotePolicy.php.html#49"><abbr title="App\Policies\WorkOrderNotePolicy::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderNotePolicy.php.html#69"><abbr title="App\Policies\WorkOrderNotePolicy::delete">delete</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#124"><abbr title="App\Policies\WorkOrderPolicy::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#144"><abbr title="App\Policies\WorkOrderPolicy::cancel">cancel</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#164"><abbr title="App\Policies\WorkOrderPolicy::delete">delete</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#184"><abbr title="App\Policies\WorkOrderPolicy::viewActivityLogs">viewActivityLogs</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#201"><abbr title="App\Policies\WorkOrderPolicy::readyToSchedule">readyToSchedule</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#218"><abbr title="App\Policies\WorkOrderPolicy::resolve">resolve</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#235"><abbr title="App\Policies\WorkOrderPolicy::reOpen">reOpen</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#252"><abbr title="App\Policies\WorkOrderPolicy::cancelAutoSchedule">cancelAutoSchedule</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#272"><abbr title="App\Policies\WorkOrderPolicy::schedule">schedule</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#292"><abbr title="App\Policies\WorkOrderPolicy::enroute">enroute</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#312"><abbr title="App\Policies\WorkOrderPolicy::startTimer">startTimer</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#329"><abbr title="App\Policies\WorkOrderPolicy::changeTags">changeTags</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#346"><abbr title="App\Policies\WorkOrderPolicy::createTags">createTags</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#366"><abbr title="App\Policies\WorkOrderPolicy::pause">pause</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#386"><abbr title="App\Policies\WorkOrderPolicy::stopTrip">stopTrip</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#406"><abbr title="App\Policies\WorkOrderPolicy::complete">complete</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#426"><abbr title="App\Policies\WorkOrderPolicy::submitQuote">submitQuote</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#446"><abbr title="App\Policies\WorkOrderPolicy::updateTrip">updateTrip</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#466"><abbr title="App\Policies\WorkOrderPolicy::manageAssignee">manageAssignee</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#486"><abbr title="App\Policies\WorkOrderPolicy::readyToInvoice">readyToInvoice</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#506"><abbr title="App\Policies\WorkOrderPolicy::pauseTrip">pauseTrip</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#526"><abbr title="App\Policies\WorkOrderPolicy::resumeTrip">resumeTrip</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#66"><abbr title="App\Policies\DatabaseNotificationPolicy::read">read</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#79"><abbr title="App\Policies\DatabaseNotificationPolicy::clear">clear</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DatabaseNotificationPolicy.php.html#92"><abbr title="App\Policies\DatabaseNotificationPolicy::unClear">unClear</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InvoicePolicy.php.html#18"><abbr title="App\Policies\InvoicePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="IssuePolicy.php.html#19"><abbr title="App\Policies\IssuePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="IssuePolicy.php.html#35"><abbr title="App\Policies\IssuePolicy::create">create</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#73"><abbr title="App\Policies\OrganizationPolicy::templateView">templateView</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OrganizationPolicy.php.html#89"><abbr title="App\Policies\OrganizationPolicy::templateUpdate">templateUpdate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuotePolicy.php.html#18"><abbr title="App\Policies\QuotePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RolePolicy.php.html#17"><abbr title="App\Policies\RolePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RolePolicy.php.html#53"><abbr title="App\Policies\RolePolicy::create">create</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestNotePolicy.php.html#17"><abbr title="App\Policies\ServiceRequestNotePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestNotePolicy.php.html#33"><abbr title="App\Policies\ServiceRequestNotePolicy::create">create</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestPolicy.php.html#17"><abbr title="App\Policies\ServiceRequestPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TagPolicy.php.html#53"><abbr title="App\Policies\TagPolicy::create">create</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UserPolicy.php.html#20"><abbr title="App\Policies\UserPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UserPolicy.php.html#57"><abbr title="App\Policies\UserPolicy::create">create</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UserPolicy.php.html#93"><abbr title="App\Policies\UserPolicy::delete">delete</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UserWorkOrderBookmarkPolicy.php.html#17"><abbr title="App\Policies\UserWorkOrderBookmarkPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UserWorkOrderBookmarkPolicy.php.html#41"><abbr title="App\Policies\UserWorkOrderBookmarkPolicy::create">create</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorPolicy.php.html#17"><abbr title="App\Policies\VendorPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorPolicy.php.html#33"><abbr title="App\Policies\VendorPolicy::view">view</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorPolicy.php.html#49"><abbr title="App\Policies\VendorPolicy::create">create</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorPolicy.php.html#65"><abbr title="App\Policies\VendorPolicy::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorPolicy.php.html#81"><abbr title="App\Policies\VendorPolicy::delete">delete</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorPolicy.php.html#113"><abbr title="App\Policies\VendorPolicy::sync">sync</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderNotePolicy.php.html#17"><abbr title="App\Policies\WorkOrderNotePolicy::viewAny">viewAny</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderNotePolicy.php.html#33"><abbr title="App\Policies\WorkOrderNotePolicy::create">create</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#20"><abbr title="App\Policies\WorkOrderPolicy::viewAny">viewAny</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#36"><abbr title="App\Policies\WorkOrderPolicy::viewVendorWorkOrderList">viewVendorWorkOrderList</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderPolicy.php.html#53"><abbr title="App\Policies\WorkOrderPolicy::viewVendorWorkOrderShow">viewVendorWorkOrderShow</abbr></a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([18,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([155,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,16,"<a href=\"DatabaseNotificationPolicy.php.html#8\">App\\Policies\\DatabaseNotificationPolicy<\/a>"],[0,30,"<a href=\"InvoicePolicy.php.html#11\">App\\Policies\\InvoicePolicy<\/a>"],[0,18,"<a href=\"IssuePolicy.php.html#12\">App\\Policies\\IssuePolicy<\/a>"],[0,13,"<a href=\"OrganizationPolicy.php.html#10\">App\\Policies\\OrganizationPolicy<\/a>"],[0,37,"<a href=\"QuotePolicy.php.html#11\">App\\Policies\\QuotePolicy<\/a>"],[0,18,"<a href=\"RolePolicy.php.html#10\">App\\Policies\\RolePolicy<\/a>"],[0,9,"<a href=\"ServiceRequestMediaPolicy.php.html#11\">App\\Policies\\ServiceRequestMediaPolicy<\/a>"],[0,14,"<a href=\"ServiceRequestNotePolicy.php.html#10\">App\\Policies\\ServiceRequestNotePolicy<\/a>"],[0,48,"<a href=\"ServiceRequestPolicy.php.html#10\">App\\Policies\\ServiceRequestPolicy<\/a>"],[0,21,"<a href=\"TagPolicy.php.html#10\">App\\Policies\\TagPolicy<\/a>"],[0,66,"<a href=\"UserPolicy.php.html#13\">App\\Policies\\UserPolicy<\/a>"],[0,11,"<a href=\"UserWorkOrderBookmarkPolicy.php.html#10\">App\\Policies\\UserWorkOrderBookmarkPolicy<\/a>"],[0,20,"<a href=\"VendorPolicy.php.html#10\">App\\Policies\\VendorPolicy<\/a>"],[0,90,"<a href=\"ViewPolicy.php.html#12\">App\\Policies\\ViewPolicy<\/a>"],[0,9,"<a href=\"WorkOrderMediaPolicy.php.html#11\">App\\Policies\\WorkOrderMediaPolicy<\/a>"],[0,14,"<a href=\"WorkOrderNotePolicy.php.html#10\">App\\Policies\\WorkOrderNotePolicy<\/a>"],[0,112,"<a href=\"WorkOrderPolicy.php.html#13\">App\\Policies\\WorkOrderPolicy<\/a>"],[0,7,"<a href=\"WorkOrderTaskPolicy.php.html#9\">App\\Policies\\WorkOrderTaskPolicy<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"DatabaseNotificationPolicy.php.html#13\">App\\Policies\\DatabaseNotificationPolicy::viewAny<\/a>"],[0,1,"<a href=\"DatabaseNotificationPolicy.php.html#21\">App\\Policies\\DatabaseNotificationPolicy::view<\/a>"],[0,1,"<a href=\"DatabaseNotificationPolicy.php.html#29\">App\\Policies\\DatabaseNotificationPolicy::create<\/a>"],[0,1,"<a href=\"DatabaseNotificationPolicy.php.html#37\">App\\Policies\\DatabaseNotificationPolicy::update<\/a>"],[0,1,"<a href=\"DatabaseNotificationPolicy.php.html#45\">App\\Policies\\DatabaseNotificationPolicy::delete<\/a>"],[0,1,"<a href=\"DatabaseNotificationPolicy.php.html#53\">App\\Policies\\DatabaseNotificationPolicy::restore<\/a>"],[0,1,"<a href=\"DatabaseNotificationPolicy.php.html#61\">App\\Policies\\DatabaseNotificationPolicy::forceDelete<\/a>"],[0,3,"<a href=\"DatabaseNotificationPolicy.php.html#66\">App\\Policies\\DatabaseNotificationPolicy::read<\/a>"],[0,3,"<a href=\"DatabaseNotificationPolicy.php.html#79\">App\\Policies\\DatabaseNotificationPolicy::clear<\/a>"],[0,3,"<a href=\"DatabaseNotificationPolicy.php.html#92\">App\\Policies\\DatabaseNotificationPolicy::unClear<\/a>"],[0,3,"<a href=\"InvoicePolicy.php.html#18\">App\\Policies\\InvoicePolicy::viewAny<\/a>"],[0,4,"<a href=\"InvoicePolicy.php.html#34\">App\\Policies\\InvoicePolicy::view<\/a>"],[0,4,"<a href=\"InvoicePolicy.php.html#54\">App\\Policies\\InvoicePolicy::create<\/a>"],[0,1,"<a href=\"InvoicePolicy.php.html#74\">App\\Policies\\InvoicePolicy::update<\/a>"],[0,4,"<a href=\"InvoicePolicy.php.html#82\">App\\Policies\\InvoicePolicy::delete<\/a>"],[0,1,"<a href=\"InvoicePolicy.php.html#103\">App\\Policies\\InvoicePolicy::restore<\/a>"],[0,1,"<a href=\"InvoicePolicy.php.html#111\">App\\Policies\\InvoicePolicy::forceDelete<\/a>"],[0,4,"<a href=\"InvoicePolicy.php.html#119\">App\\Policies\\InvoicePolicy::fullyPaid<\/a>"],[0,4,"<a href=\"InvoicePolicy.php.html#139\">App\\Policies\\InvoicePolicy::partiallyPaid<\/a>"],[0,4,"<a href=\"InvoicePolicy.php.html#159\">App\\Policies\\InvoicePolicy::voided<\/a>"],[0,3,"<a href=\"IssuePolicy.php.html#19\">App\\Policies\\IssuePolicy::viewAny<\/a>"],[0,3,"<a href=\"IssuePolicy.php.html#35\">App\\Policies\\IssuePolicy::create<\/a>"],[0,4,"<a href=\"IssuePolicy.php.html#51\">App\\Policies\\IssuePolicy::update<\/a>"],[0,4,"<a href=\"IssuePolicy.php.html#71\">App\\Policies\\IssuePolicy::delete<\/a>"],[0,4,"<a href=\"IssuePolicy.php.html#92\">App\\Policies\\IssuePolicy::cancel<\/a>"],[0,1,"<a href=\"OrganizationPolicy.php.html#17\">App\\Policies\\OrganizationPolicy::viewAny<\/a>"],[0,1,"<a href=\"OrganizationPolicy.php.html#25\">App\\Policies\\OrganizationPolicy::view<\/a>"],[0,1,"<a href=\"OrganizationPolicy.php.html#33\">App\\Policies\\OrganizationPolicy::create<\/a>"],[0,1,"<a href=\"OrganizationPolicy.php.html#41\">App\\Policies\\OrganizationPolicy::update<\/a>"],[0,1,"<a href=\"OrganizationPolicy.php.html#49\">App\\Policies\\OrganizationPolicy::delete<\/a>"],[0,1,"<a href=\"OrganizationPolicy.php.html#57\">App\\Policies\\OrganizationPolicy::restore<\/a>"],[0,1,"<a href=\"OrganizationPolicy.php.html#65\">App\\Policies\\OrganizationPolicy::forceDelete<\/a>"],[0,3,"<a href=\"OrganizationPolicy.php.html#73\">App\\Policies\\OrganizationPolicy::templateView<\/a>"],[0,3,"<a href=\"OrganizationPolicy.php.html#89\">App\\Policies\\OrganizationPolicy::templateUpdate<\/a>"],[0,3,"<a href=\"QuotePolicy.php.html#18\">App\\Policies\\QuotePolicy::viewAny<\/a>"],[0,1,"<a href=\"QuotePolicy.php.html#34\">App\\Policies\\QuotePolicy::view<\/a>"],[0,1,"<a href=\"QuotePolicy.php.html#42\">App\\Policies\\QuotePolicy::create<\/a>"],[0,1,"<a href=\"QuotePolicy.php.html#50\">App\\Policies\\QuotePolicy::update<\/a>"],[0,1,"<a href=\"QuotePolicy.php.html#58\">App\\Policies\\QuotePolicy::delete<\/a>"],[0,1,"<a href=\"QuotePolicy.php.html#66\">App\\Policies\\QuotePolicy::restore<\/a>"],[0,4,"<a href=\"QuotePolicy.php.html#74\">App\\Policies\\QuotePolicy::approve<\/a>"],[0,4,"<a href=\"QuotePolicy.php.html#94\">App\\Policies\\QuotePolicy::reject<\/a>"],[0,5,"<a href=\"QuotePolicy.php.html#114\">App\\Policies\\QuotePolicy::createQuoteTask<\/a>"],[0,6,"<a href=\"QuotePolicy.php.html#134\">App\\Policies\\QuotePolicy::updateQuoteTask<\/a>"],[0,6,"<a href=\"QuotePolicy.php.html#158\">App\\Policies\\QuotePolicy::deleteQuoteTask<\/a>"],[0,4,"<a href=\"QuotePolicy.php.html#182\">App\\Policies\\QuotePolicy::submitForApproval<\/a>"],[0,3,"<a href=\"RolePolicy.php.html#17\">App\\Policies\\RolePolicy::viewAny<\/a>"],[0,4,"<a href=\"RolePolicy.php.html#33\">App\\Policies\\RolePolicy::view<\/a>"],[0,3,"<a href=\"RolePolicy.php.html#53\">App\\Policies\\RolePolicy::create<\/a>"],[0,4,"<a href=\"RolePolicy.php.html#69\">App\\Policies\\RolePolicy::update<\/a>"],[0,4,"<a href=\"RolePolicy.php.html#89\">App\\Policies\\RolePolicy::delete<\/a>"],[0,4,"<a href=\"ServiceRequestMediaPolicy.php.html#18\">App\\Policies\\ServiceRequestMediaPolicy::create<\/a>"],[0,5,"<a href=\"ServiceRequestMediaPolicy.php.html#38\">App\\Policies\\ServiceRequestMediaPolicy::delete<\/a>"],[0,3,"<a href=\"ServiceRequestNotePolicy.php.html#17\">App\\Policies\\ServiceRequestNotePolicy::viewAny<\/a>"],[0,3,"<a href=\"ServiceRequestNotePolicy.php.html#33\">App\\Policies\\ServiceRequestNotePolicy::create<\/a>"],[0,4,"<a href=\"ServiceRequestNotePolicy.php.html#49\">App\\Policies\\ServiceRequestNotePolicy::update<\/a>"],[0,4,"<a href=\"ServiceRequestNotePolicy.php.html#69\">App\\Policies\\ServiceRequestNotePolicy::delete<\/a>"],[0,3,"<a href=\"ServiceRequestPolicy.php.html#17\">App\\Policies\\ServiceRequestPolicy::viewAny<\/a>"],[0,5,"<a href=\"ServiceRequestPolicy.php.html#33\">App\\Policies\\ServiceRequestPolicy::view<\/a>"],[0,4,"<a href=\"ServiceRequestPolicy.php.html#58\">App\\Policies\\ServiceRequestPolicy::create<\/a>"],[0,4,"<a href=\"ServiceRequestPolicy.php.html#81\">App\\Policies\\ServiceRequestPolicy::update<\/a>"],[0,4,"<a href=\"ServiceRequestPolicy.php.html#101\">App\\Policies\\ServiceRequestPolicy::manageAssignee<\/a>"],[0,4,"<a href=\"ServiceRequestPolicy.php.html#121\">App\\Policies\\ServiceRequestPolicy::close<\/a>"],[0,4,"<a href=\"ServiceRequestPolicy.php.html#141\">App\\Policies\\ServiceRequestPolicy::inprogress<\/a>"],[0,4,"<a href=\"ServiceRequestPolicy.php.html#161\">App\\Policies\\ServiceRequestPolicy::readyToSchedule<\/a>"],[0,4,"<a href=\"ServiceRequestPolicy.php.html#181\">App\\Policies\\ServiceRequestPolicy::requestResidentAvailability<\/a>"],[0,4,"<a href=\"ServiceRequestPolicy.php.html#201\">App\\Policies\\ServiceRequestPolicy::addAvailability<\/a>"],[0,4,"<a href=\"ServiceRequestPolicy.php.html#221\">App\\Policies\\ServiceRequestPolicy::updateAvailability<\/a>"],[0,4,"<a href=\"ServiceRequestPolicy.php.html#241\">App\\Policies\\ServiceRequestPolicy::viewActivityLogs<\/a>"],[0,4,"<a href=\"TagPolicy.php.html#17\">App\\Policies\\TagPolicy::viewAny<\/a>"],[0,4,"<a href=\"TagPolicy.php.html#33\">App\\Policies\\TagPolicy::view<\/a>"],[0,3,"<a href=\"TagPolicy.php.html#53\">App\\Policies\\TagPolicy::create<\/a>"],[0,4,"<a href=\"TagPolicy.php.html#69\">App\\Policies\\TagPolicy::update<\/a>"],[0,4,"<a href=\"TagPolicy.php.html#89\">App\\Policies\\TagPolicy::delete<\/a>"],[0,1,"<a href=\"TagPolicy.php.html#109\">App\\Policies\\TagPolicy::restore<\/a>"],[0,1,"<a href=\"TagPolicy.php.html#117\">App\\Policies\\TagPolicy::forceDelete<\/a>"],[0,3,"<a href=\"UserPolicy.php.html#20\">App\\Policies\\UserPolicy::viewAny<\/a>"],[0,4,"<a href=\"UserPolicy.php.html#37\">App\\Policies\\UserPolicy::view<\/a>"],[0,3,"<a href=\"UserPolicy.php.html#57\">App\\Policies\\UserPolicy::create<\/a>"],[0,4,"<a href=\"UserPolicy.php.html#73\">App\\Policies\\UserPolicy::update<\/a>"],[0,3,"<a href=\"UserPolicy.php.html#93\">App\\Policies\\UserPolicy::delete<\/a>"],[0,1,"<a href=\"UserPolicy.php.html#109\">App\\Policies\\UserPolicy::restore<\/a>"],[0,1,"<a href=\"UserPolicy.php.html#117\">App\\Policies\\UserPolicy::forceDelete<\/a>"],[0,4,"<a href=\"UserPolicy.php.html#125\">App\\Policies\\UserPolicy::updateWorkingHours<\/a>"],[0,4,"<a href=\"UserPolicy.php.html#145\">App\\Policies\\UserPolicy::importWorkingHours<\/a>"],[0,4,"<a href=\"UserPolicy.php.html#165\">App\\Policies\\UserPolicy::updateTechnicianSkill<\/a>"],[0,4,"<a href=\"UserPolicy.php.html#185\">App\\Policies\\UserPolicy::importTechnicianSkill<\/a>"],[0,7,"<a href=\"UserPolicy.php.html#205\">App\\Policies\\UserPolicy::viewTechnicianBlockOut<\/a>"],[0,4,"<a href=\"UserPolicy.php.html#233\">App\\Policies\\UserPolicy::createTechnicianBlockOut<\/a>"],[0,6,"<a href=\"UserPolicy.php.html#253\">App\\Policies\\UserPolicy::updateTechnicianBlockOut<\/a>"],[0,6,"<a href=\"UserPolicy.php.html#281\">App\\Policies\\UserPolicy::deleteTechnicianBlockOut<\/a>"],[0,4,"<a href=\"UserPolicy.php.html#309\">App\\Policies\\UserPolicy::accessApp<\/a>"],[0,4,"<a href=\"UserPolicy.php.html#331\">App\\Policies\\UserPolicy::scheduleTrip<\/a>"],[0,3,"<a href=\"UserWorkOrderBookmarkPolicy.php.html#17\">App\\Policies\\UserWorkOrderBookmarkPolicy::viewAny<\/a>"],[0,1,"<a href=\"UserWorkOrderBookmarkPolicy.php.html#33\">App\\Policies\\UserWorkOrderBookmarkPolicy::view<\/a>"],[0,3,"<a href=\"UserWorkOrderBookmarkPolicy.php.html#41\">App\\Policies\\UserWorkOrderBookmarkPolicy::create<\/a>"],[0,1,"<a href=\"UserWorkOrderBookmarkPolicy.php.html#57\">App\\Policies\\UserWorkOrderBookmarkPolicy::update<\/a>"],[0,1,"<a href=\"UserWorkOrderBookmarkPolicy.php.html#65\">App\\Policies\\UserWorkOrderBookmarkPolicy::delete<\/a>"],[0,1,"<a href=\"UserWorkOrderBookmarkPolicy.php.html#73\">App\\Policies\\UserWorkOrderBookmarkPolicy::restore<\/a>"],[0,1,"<a href=\"UserWorkOrderBookmarkPolicy.php.html#81\">App\\Policies\\UserWorkOrderBookmarkPolicy::forceDelete<\/a>"],[0,3,"<a href=\"VendorPolicy.php.html#17\">App\\Policies\\VendorPolicy::viewAny<\/a>"],[0,3,"<a href=\"VendorPolicy.php.html#33\">App\\Policies\\VendorPolicy::view<\/a>"],[0,3,"<a href=\"VendorPolicy.php.html#49\">App\\Policies\\VendorPolicy::create<\/a>"],[0,3,"<a href=\"VendorPolicy.php.html#65\">App\\Policies\\VendorPolicy::update<\/a>"],[0,3,"<a href=\"VendorPolicy.php.html#81\">App\\Policies\\VendorPolicy::delete<\/a>"],[0,1,"<a href=\"VendorPolicy.php.html#97\">App\\Policies\\VendorPolicy::restore<\/a>"],[0,1,"<a href=\"VendorPolicy.php.html#105\">App\\Policies\\VendorPolicy::forceDelete<\/a>"],[0,3,"<a href=\"VendorPolicy.php.html#113\">App\\Policies\\VendorPolicy::sync<\/a>"],[0,10,"<a href=\"ViewPolicy.php.html#19\">App\\Policies\\ViewPolicy::viewAny<\/a>"],[0,10,"<a href=\"ViewPolicy.php.html#57\">App\\Policies\\ViewPolicy::create<\/a>"],[0,13,"<a href=\"ViewPolicy.php.html#94\">App\\Policies\\ViewPolicy::duplicate<\/a>"],[0,14,"<a href=\"ViewPolicy.php.html#142\">App\\Policies\\ViewPolicy::update<\/a>"],[0,14,"<a href=\"ViewPolicy.php.html#196\">App\\Policies\\ViewPolicy::delete<\/a>"],[0,14,"<a href=\"ViewPolicy.php.html#248\">App\\Policies\\ViewPolicy::setDefault<\/a>"],[0,15,"<a href=\"ViewPolicy.php.html#300\">App\\Policies\\ViewPolicy::pinOrUnpin<\/a>"],[0,4,"<a href=\"WorkOrderMediaPolicy.php.html#18\">App\\Policies\\WorkOrderMediaPolicy::create<\/a>"],[0,5,"<a href=\"WorkOrderMediaPolicy.php.html#38\">App\\Policies\\WorkOrderMediaPolicy::delete<\/a>"],[0,3,"<a href=\"WorkOrderNotePolicy.php.html#17\">App\\Policies\\WorkOrderNotePolicy::viewAny<\/a>"],[0,3,"<a href=\"WorkOrderNotePolicy.php.html#33\">App\\Policies\\WorkOrderNotePolicy::create<\/a>"],[0,4,"<a href=\"WorkOrderNotePolicy.php.html#49\">App\\Policies\\WorkOrderNotePolicy::update<\/a>"],[0,4,"<a href=\"WorkOrderNotePolicy.php.html#69\">App\\Policies\\WorkOrderNotePolicy::delete<\/a>"],[0,3,"<a href=\"WorkOrderPolicy.php.html#20\">App\\Policies\\WorkOrderPolicy::viewAny<\/a>"],[0,3,"<a href=\"WorkOrderPolicy.php.html#36\">App\\Policies\\WorkOrderPolicy::viewVendorWorkOrderList<\/a>"],[0,3,"<a href=\"WorkOrderPolicy.php.html#53\">App\\Policies\\WorkOrderPolicy::viewVendorWorkOrderShow<\/a>"],[0,9,"<a href=\"WorkOrderPolicy.php.html#70\">App\\Policies\\WorkOrderPolicy::view<\/a>"],[0,6,"<a href=\"WorkOrderPolicy.php.html#102\">App\\Policies\\WorkOrderPolicy::create<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#124\">App\\Policies\\WorkOrderPolicy::update<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#144\">App\\Policies\\WorkOrderPolicy::cancel<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#164\">App\\Policies\\WorkOrderPolicy::delete<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#184\">App\\Policies\\WorkOrderPolicy::viewActivityLogs<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#201\">App\\Policies\\WorkOrderPolicy::readyToSchedule<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#218\">App\\Policies\\WorkOrderPolicy::resolve<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#235\">App\\Policies\\WorkOrderPolicy::reOpen<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#252\">App\\Policies\\WorkOrderPolicy::cancelAutoSchedule<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#272\">App\\Policies\\WorkOrderPolicy::schedule<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#292\">App\\Policies\\WorkOrderPolicy::enroute<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#312\">App\\Policies\\WorkOrderPolicy::startTimer<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#329\">App\\Policies\\WorkOrderPolicy::changeTags<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#346\">App\\Policies\\WorkOrderPolicy::createTags<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#366\">App\\Policies\\WorkOrderPolicy::pause<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#386\">App\\Policies\\WorkOrderPolicy::stopTrip<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#406\">App\\Policies\\WorkOrderPolicy::complete<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#426\">App\\Policies\\WorkOrderPolicy::submitQuote<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#446\">App\\Policies\\WorkOrderPolicy::updateTrip<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#466\">App\\Policies\\WorkOrderPolicy::manageAssignee<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#486\">App\\Policies\\WorkOrderPolicy::readyToInvoice<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#506\">App\\Policies\\WorkOrderPolicy::pauseTrip<\/a>"],[0,4,"<a href=\"WorkOrderPolicy.php.html#526\">App\\Policies\\WorkOrderPolicy::resumeTrip<\/a>"],[0,1,"<a href=\"WorkOrderTaskPolicy.php.html#16\">App\\Policies\\WorkOrderTaskPolicy::viewAny<\/a>"],[0,1,"<a href=\"WorkOrderTaskPolicy.php.html#24\">App\\Policies\\WorkOrderTaskPolicy::view<\/a>"],[0,1,"<a href=\"WorkOrderTaskPolicy.php.html#32\">App\\Policies\\WorkOrderTaskPolicy::create<\/a>"],[0,1,"<a href=\"WorkOrderTaskPolicy.php.html#40\">App\\Policies\\WorkOrderTaskPolicy::update<\/a>"],[0,1,"<a href=\"WorkOrderTaskPolicy.php.html#48\">App\\Policies\\WorkOrderTaskPolicy::delete<\/a>"],[0,1,"<a href=\"WorkOrderTaskPolicy.php.html#56\">App\\Policies\\WorkOrderTaskPolicy::restore<\/a>"],[0,1,"<a href=\"WorkOrderTaskPolicy.php.html#64\">App\\Policies\\WorkOrderTaskPolicy::forceDelete<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

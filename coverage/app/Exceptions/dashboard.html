<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Exceptions</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Exceptions</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AbilityException.php.html#7">App\Exceptions\AbilityException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioApiException.php.html#7">App\Exceptions\Appfolio\AppfolioApiException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioApiResponseException.php.html#7">App\Exceptions\Appfolio\AppfolioApiResponseException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CouldNotPerformAction.php.html#7">App\Exceptions\CouldNotPerformAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Debug/DatabaseProcessException.php.html#7">App\Exceptions\Debug\DatabaseProcessException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ForbiddenException.php.html#8">App\Exceptions\ForbiddenException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handler.php.html#22">App\Exceptions\Handler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvalidWorkOrderStateException.php.html#7">App\Exceptions\InvalidWorkOrderStateException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceException.php.html#7">App\Exceptions\InvoiceException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueException.php.html#8">App\Exceptions\IssueException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookException.php.html#7">App\Exceptions\LulaWebhookException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException.php.html#7">App\Exceptions\NotFoundException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException/OrganizationNotFoundException.php.html#7">App\Exceptions\NotFoundException\OrganizationNotFoundException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException/ServiceRequestNotFoundException.php.html#7">App\Exceptions\NotFoundException\ServiceRequestNotFoundException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException/UserNotFoundException.php.html#7">App\Exceptions\NotFoundException\UserNotFoundException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException/WorkOrderNotFoundException.php.html#7">App\Exceptions\NotFoundException\WorkOrderNotFoundException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization/OrganizationException.php.html#7">App\Exceptions\Organization\OrganizationException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PermissionsFromInvalidFeatureException.php.html#7">App\Exceptions\PermissionsFromInvalidFeatureException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#7">App\Exceptions\QuoteException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#7">App\Exceptions\ResidentAvailabilityException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleException.php.html#7">App\Exceptions\ScheduleException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeException.php.html#7">App\Exceptions\ServiceRequestAssigneeException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestException.php.html#9">App\Exceptions\ServiceRequestException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaException.php.html#7">App\Exceptions\ServiceRequestMediaException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaLimitException.php.html#7">App\Exceptions\ServiceRequestMediaLimitException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SignedUrlException.php.html#7">App\Exceptions\SignedUrlException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorageException.php.html#7">App\Exceptions\StorageException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripException.php.html#7">App\Exceptions\TripException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorException.php.html#7">App\Exceptions\VendorException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookException.php.html#7">App\Exceptions\WebhookException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeException.php.html#7">App\Exceptions\WorkOrderAssigneeException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#7">App\Exceptions\WorkOrderException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaException.php.html#7">App\Exceptions\WorkOrderMediaException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaLimitException.php.html#7">App\Exceptions\WorkOrderMediaLimitException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNoteException.php.html#7">App\Exceptions\WorkOrderNoteException</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Handler.php.html#22">App\Exceptions\Handler</a></td><td class="text-right">132</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AbilityException.php.html#9"><abbr title="App\Exceptions\AbilityException::undefinedModel">undefinedModel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioApiException.php.html#9"><abbr title="App\Exceptions\Appfolio\AppfolioApiException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Appfolio/AppfolioApiResponseException.php.html#9"><abbr title="App\Exceptions\Appfolio\AppfolioApiResponseException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CouldNotPerformAction.php.html#9"><abbr title="App\Exceptions\CouldNotPerformAction::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Debug/DatabaseProcessException.php.html#9"><abbr title="App\Exceptions\Debug\DatabaseProcessException::overKillingProcessHappening">overKillingProcessHappening</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ForbiddenException.php.html#10"><abbr title="App\Exceptions\ForbiddenException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ForbiddenException.php.html#15"><abbr title="App\Exceptions\ForbiddenException::accessDenied">accessDenied</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handler.php.html#56"><abbr title="App\Exceptions\Handler::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvalidWorkOrderStateException.php.html#9"><abbr title="App\Exceptions\InvalidWorkOrderStateException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceException.php.html#9"><abbr title="App\Exceptions\InvoiceException::invalidWorkOrderStatus">invalidWorkOrderStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceException.php.html#14"><abbr title="App\Exceptions\InvoiceException::invalidInvoiceStatus">invalidInvoiceStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceException.php.html#19"><abbr title="App\Exceptions\InvoiceException::invalidInvoice">invalidInvoice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceException.php.html#24"><abbr title="App\Exceptions\InvoiceException::invalidPayload">invalidPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceException.php.html#29"><abbr title="App\Exceptions\InvoiceException::invalidInvoiceItem">invalidInvoiceItem</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceException.php.html#34"><abbr title="App\Exceptions\InvoiceException::webhookFailed">webhookFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceException.php.html#39"><abbr title="App\Exceptions\InvoiceException::failed">failed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueException.php.html#10"><abbr title="App\Exceptions\IssueException::undefinedRouteSlug">undefinedRouteSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueException.php.html#15"><abbr title="App\Exceptions\IssueException::actionNotAllowed">actionNotAllowed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueException.php.html#20"><abbr title="App\Exceptions\IssueException::notFound">notFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueException.php.html#25"><abbr title="App\Exceptions\IssueException::invalid">invalid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueException.php.html#30"><abbr title="App\Exceptions\IssueException::invalidWorkOrderIssue">invalidWorkOrderIssue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueException.php.html#35"><abbr title="App\Exceptions\IssueException::issueAlreadyAssigned">issueAlreadyAssigned</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueException.php.html#40"><abbr title="App\Exceptions\IssueException::serviceRequestNotFound">serviceRequestNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueException.php.html#45"><abbr title="App\Exceptions\IssueException::couldNotPerformTransition">couldNotPerformTransition</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IssueException.php.html#50"><abbr title="App\Exceptions\IssueException::mediaValidation">mediaValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookException.php.html#9"><abbr title="App\Exceptions\LulaWebhookException::webhookFailed">webhookFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookException.php.html#14"><abbr title="App\Exceptions\LulaWebhookException::emptyPayload">emptyPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookException.php.html#19"><abbr title="App\Exceptions\LulaWebhookException::workOrderDetailsNotFound">workOrderDetailsNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookException.php.html#24"><abbr title="App\Exceptions\LulaWebhookException::emptyEvent">emptyEvent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookException.php.html#29"><abbr title="App\Exceptions\LulaWebhookException::invalidEventReceived">invalidEventReceived</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookException.php.html#34"><abbr title="App\Exceptions\LulaWebhookException::lulaAppointmentNotExists">lulaAppointmentNotExists</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookException.php.html#39"><abbr title="App\Exceptions\LulaWebhookException::scheduleDateMissing">scheduleDateMissing</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookException.php.html#44"><abbr title="App\Exceptions\LulaWebhookException::rescheduleFailed">rescheduleFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookException.php.html#49"><abbr title="App\Exceptions\LulaWebhookException::emptyReturnDate">emptyReturnDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException.php.html#9"><abbr title="App\Exceptions\NotFoundException::resourceNotFound">resourceNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException.php.html#14"><abbr title="App\Exceptions\NotFoundException::userNotFound">userNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException.php.html#19"><abbr title="App\Exceptions\NotFoundException::mediaNotExists">mediaNotExists</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException/OrganizationNotFoundException.php.html#9"><abbr title="App\Exceptions\NotFoundException\OrganizationNotFoundException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException/ServiceRequestNotFoundException.php.html#9"><abbr title="App\Exceptions\NotFoundException\ServiceRequestNotFoundException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException/UserNotFoundException.php.html#9"><abbr title="App\Exceptions\NotFoundException\UserNotFoundException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NotFoundException/WorkOrderNotFoundException.php.html#9"><abbr title="App\Exceptions\NotFoundException\WorkOrderNotFoundException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization/OrganizationException.php.html#9"><abbr title="App\Exceptions\Organization\OrganizationException::domainSuffixNotFound">domainSuffixNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PermissionsFromInvalidFeatureException.php.html#9"><abbr title="App\Exceptions\PermissionsFromInvalidFeatureException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#9"><abbr title="App\Exceptions\QuoteException::invalidWorkOrderStatus">invalidWorkOrderStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#14"><abbr title="App\Exceptions\QuoteException::invalidQuoteStatus">invalidQuoteStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#19"><abbr title="App\Exceptions\QuoteException::invalidQuote">invalidQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#24"><abbr title="App\Exceptions\QuoteException::invalidPayload">invalidPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#29"><abbr title="App\Exceptions\QuoteException::invalidQuoteTask">invalidQuoteTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#34"><abbr title="App\Exceptions\QuoteException::invalidWorkOrderTask">invalidWorkOrderTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#39"><abbr title="App\Exceptions\QuoteException::restoreFailed">restoreFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#44"><abbr title="App\Exceptions\QuoteException::webhookFailed">webhookFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#49"><abbr title="App\Exceptions\QuoteException::existingQuote">existingQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#54"><abbr title="App\Exceptions\QuoteException::atLeastOneQuoteTask">atLeastOneQuoteTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#59"><abbr title="App\Exceptions\QuoteException::mediaNotMatched">mediaNotMatched</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#64"><abbr title="App\Exceptions\QuoteException::quoteTaskNotFound">quoteTaskNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#69"><abbr title="App\Exceptions\QuoteException::tasksCountDoesNotMatch">tasksCountDoesNotMatch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#74"><abbr title="App\Exceptions\QuoteException::quoteExists">quoteExists</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteException.php.html#79"><abbr title="App\Exceptions\QuoteException::quoteActionFailed">quoteActionFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#9"><abbr title="App\Exceptions\ResidentAvailabilityException::invalidWorkOrder">invalidWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#14"><abbr title="App\Exceptions\ResidentAvailabilityException::invalidServiceRequest">invalidServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#19"><abbr title="App\Exceptions\ResidentAvailabilityException::invalidWorkOrderTask">invalidWorkOrderTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#24"><abbr title="App\Exceptions\ResidentAvailabilityException::invalidWorkOrderStatus">invalidWorkOrderStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#29"><abbr title="App\Exceptions\ResidentAvailabilityException::invalidServiceRequestStatus">invalidServiceRequestStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#34"><abbr title="App\Exceptions\ResidentAvailabilityException::invalidResidentAvailability">invalidResidentAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#39"><abbr title="App\Exceptions\ResidentAvailabilityException::residentNotFound">residentNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#44"><abbr title="App\Exceptions\ResidentAvailabilityException::categoryNotFound">categoryNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#49"><abbr title="App\Exceptions\ResidentAvailabilityException::residentPhoneNotFound">residentPhoneNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#54"><abbr title="App\Exceptions\ResidentAvailabilityException::recordAlreadyExisting">recordAlreadyExisting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityException.php.html#59"><abbr title="App\Exceptions\ResidentAvailabilityException::updateFailed">updateFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleException.php.html#9"><abbr title="App\Exceptions\ScheduleException::problemDiagnosisNotMatch">problemDiagnosisNotMatch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleException.php.html#14"><abbr title="App\Exceptions\ScheduleException::windowNotFound">windowNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleException.php.html#19"><abbr title="App\Exceptions\ScheduleException::noSkill">noSkill</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleException.php.html#24"><abbr title="App\Exceptions\ScheduleException::noWorkingHours">noWorkingHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeException.php.html#9"><abbr title="App\Exceptions\ServiceRequestAssigneeException::alreadyExisting">alreadyExisting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeException.php.html#14"><abbr title="App\Exceptions\ServiceRequestAssigneeException::invalidUserType">invalidUserType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeException.php.html#19"><abbr title="App\Exceptions\ServiceRequestAssigneeException::organizationMismatch">organizationMismatch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeException.php.html#24"><abbr title="App\Exceptions\ServiceRequestAssigneeException::assigneeNotFound">assigneeNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestException.php.html#11"><abbr title="App\Exceptions\ServiceRequestException::activityLogCreationMissingParameter">activityLogCreationMissingParameter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaException.php.html#9"><abbr title="App\Exceptions\ServiceRequestMediaException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaLimitException.php.html#9"><abbr title="App\Exceptions\ServiceRequestMediaLimitException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SignedUrlException.php.html#9"><abbr title="App\Exceptions\SignedUrlException::signedUrlNotProvided">signedUrlNotProvided</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SignedUrlException.php.html#14"><abbr title="App\Exceptions\SignedUrlException::signedUrlExpired">signedUrlExpired</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorageException.php.html#9"><abbr title="App\Exceptions\StorageException::uploadFailed">uploadFailed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripException.php.html#9"><abbr title="App\Exceptions\TripException::invalidWorkOrderTask">invalidWorkOrderTask</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripException.php.html#14"><abbr title="App\Exceptions\TripException::appointmentNotFound">appointmentNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripException.php.html#19"><abbr title="App\Exceptions\TripException::invalidTrip">invalidTrip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripException.php.html#24"><abbr title="App\Exceptions\TripException::userNotFound">userNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorException.php.html#9"><abbr title="App\Exceptions\VendorException::appfolioNotEnabled">appfolioNotEnabled</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookException.php.html#9"><abbr title="App\Exceptions\WebhookException::invalidEventRecieved">invalidEventRecieved</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookException.php.html#14"><abbr title="App\Exceptions\WebhookException::invalidEventPayload">invalidEventPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeException.php.html#9"><abbr title="App\Exceptions\WorkOrderAssigneeException::alreadyExisting">alreadyExisting</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeException.php.html#14"><abbr title="App\Exceptions\WorkOrderAssigneeException::invalidUserType">invalidUserType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeException.php.html#19"><abbr title="App\Exceptions\WorkOrderAssigneeException::organizationMismatch">organizationMismatch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeException.php.html#24"><abbr title="App\Exceptions\WorkOrderAssigneeException::assigneeNotFound">assigneeNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#9"><abbr title="App\Exceptions\WorkOrderException::invalidWorkOrder">invalidWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#14"><abbr title="App\Exceptions\WorkOrderException::serviceCallNotFound">serviceCallNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#19"><abbr title="App\Exceptions\WorkOrderException::activeServiceCallNotFound">activeServiceCallNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#24"><abbr title="App\Exceptions\WorkOrderException::appointmentNotFound">appointmentNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#29"><abbr title="App\Exceptions\WorkOrderException::couldNotPerformTransition">couldNotPerformTransition</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#34"><abbr title="App\Exceptions\WorkOrderException::activeTripNotFound">activeTripNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#39"><abbr title="App\Exceptions\WorkOrderException::technicianAppointmentNotFound">technicianAppointmentNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#44"><abbr title="App\Exceptions\WorkOrderException::workOrderIssuesNotFound">workOrderIssuesNotFound</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#49"><abbr title="App\Exceptions\WorkOrderException::openIssueExists">openIssueExists</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#54"><abbr title="App\Exceptions\WorkOrderException::missingEnRouteStartTime">missingEnRouteStartTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderException.php.html#59"><abbr title="App\Exceptions\WorkOrderException::missingStartTime">missingStartTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaException.php.html#9"><abbr title="App\Exceptions\WorkOrderMediaException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaLimitException.php.html#9"><abbr title="App\Exceptions\WorkOrderMediaLimitException::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNoteException.php.html#9"><abbr title="App\Exceptions\WorkOrderNoteException::inValidWorkOrder">inValidWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNoteException.php.html#14"><abbr title="App\Exceptions\WorkOrderNoteException::inValidWorkOrderNote">inValidWorkOrderNote</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Handler.php.html#56"><abbr title="App\Exceptions\Handler::register">register</abbr></a></td><td class="text-right">132</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([35,0,0,0,0,0,0,0,0,0,0,11], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([109,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[100,0,"<a href=\"AWS\/Cognito\/CognitoAPIFailedException.php.html#7\">App\\Exceptions\\AWS\\Cognito\\CognitoAPIFailedException<\/a>"],[100,0,"<a href=\"AWS\/Cognito\/JwtValidationException.php.html#7\">App\\Exceptions\\AWS\\Cognito\\JwtValidationException<\/a>"],[0,1,"<a href=\"AbilityException.php.html#7\">App\\Exceptions\\AbilityException<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioApiException.php.html#7\">App\\Exceptions\\Appfolio\\AppfolioApiException<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioApiResponseException.php.html#7\">App\\Exceptions\\Appfolio\\AppfolioApiResponseException<\/a>"],[0,1,"<a href=\"CouldNotPerformAction.php.html#7\">App\\Exceptions\\CouldNotPerformAction<\/a>"],[0,1,"<a href=\"Debug\/DatabaseProcessException.php.html#7\">App\\Exceptions\\Debug\\DatabaseProcessException<\/a>"],[0,2,"<a href=\"ForbiddenException.php.html#8\">App\\Exceptions\\ForbiddenException<\/a>"],[100,0,"<a href=\"GroupingExceptions\/QuoteGroupingsException.php.html#7\">App\\Exceptions\\GroupingExceptions\\QuoteGroupingsException<\/a>"],[100,0,"<a href=\"GroupingExceptions\/ServiceRequestGroupingException.php.html#7\">App\\Exceptions\\GroupingExceptions\\ServiceRequestGroupingException<\/a>"],[100,0,"<a href=\"GroupingExceptions\/UserGroupingException.php.html#7\">App\\Exceptions\\GroupingExceptions\\UserGroupingException<\/a>"],[100,0,"<a href=\"GroupingExceptions\/WorkOrderGroupingException.php.html#7\">App\\Exceptions\\GroupingExceptions\\WorkOrderGroupingException<\/a>"],[0,11,"<a href=\"Handler.php.html#22\">App\\Exceptions\\Handler<\/a>"],[0,1,"<a href=\"InvalidWorkOrderStateException.php.html#7\">App\\Exceptions\\InvalidWorkOrderStateException<\/a>"],[0,7,"<a href=\"InvoiceException.php.html#7\">App\\Exceptions\\InvoiceException<\/a>"],[0,9,"<a href=\"IssueException.php.html#8\">App\\Exceptions\\IssueException<\/a>"],[0,9,"<a href=\"LulaWebhookException.php.html#7\">App\\Exceptions\\LulaWebhookException<\/a>"],[100,0,"<a href=\"MediaUploadException.php.html#7\">App\\Exceptions\\MediaUploadException<\/a>"],[0,3,"<a href=\"NotFoundException.php.html#7\">App\\Exceptions\\NotFoundException<\/a>"],[0,1,"<a href=\"NotFoundException\/OrganizationNotFoundException.php.html#7\">App\\Exceptions\\NotFoundException\\OrganizationNotFoundException<\/a>"],[100,0,"<a href=\"NotFoundException\/OwnerNotExistsException.php.html#7\">App\\Exceptions\\NotFoundException\\OwnerNotExistsException<\/a>"],[0,1,"<a href=\"NotFoundException\/ServiceRequestNotFoundException.php.html#7\">App\\Exceptions\\NotFoundException\\ServiceRequestNotFoundException<\/a>"],[0,1,"<a href=\"NotFoundException\/UserNotFoundException.php.html#7\">App\\Exceptions\\NotFoundException\\UserNotFoundException<\/a>"],[0,1,"<a href=\"NotFoundException\/WorkOrderNotFoundException.php.html#7\">App\\Exceptions\\NotFoundException\\WorkOrderNotFoundException<\/a>"],[0,1,"<a href=\"Organization\/OrganizationException.php.html#7\">App\\Exceptions\\Organization\\OrganizationException<\/a>"],[100,0,"<a href=\"Organization\/Public\/ClientCredentialMissingException.php.html#7\">App\\Exceptions\\Organization\\Public\\ClientCredentialMissingException<\/a>"],[100,0,"<a href=\"Organization\/Public\/PublicApiNotEnabledException.php.html#7\">App\\Exceptions\\Organization\\Public\\PublicApiNotEnabledException<\/a>"],[0,1,"<a href=\"PermissionsFromInvalidFeatureException.php.html#7\">App\\Exceptions\\PermissionsFromInvalidFeatureException<\/a>"],[0,15,"<a href=\"QuoteException.php.html#7\">App\\Exceptions\\QuoteException<\/a>"],[0,11,"<a href=\"ResidentAvailabilityException.php.html#7\">App\\Exceptions\\ResidentAvailabilityException<\/a>"],[0,4,"<a href=\"ScheduleException.php.html#7\">App\\Exceptions\\ScheduleException<\/a>"],[0,4,"<a href=\"ServiceRequestAssigneeException.php.html#7\">App\\Exceptions\\ServiceRequestAssigneeException<\/a>"],[0,1,"<a href=\"ServiceRequestException.php.html#9\">App\\Exceptions\\ServiceRequestException<\/a>"],[0,1,"<a href=\"ServiceRequestMediaException.php.html#7\">App\\Exceptions\\ServiceRequestMediaException<\/a>"],[0,1,"<a href=\"ServiceRequestMediaLimitException.php.html#7\">App\\Exceptions\\ServiceRequestMediaLimitException<\/a>"],[0,2,"<a href=\"SignedUrlException.php.html#7\">App\\Exceptions\\SignedUrlException<\/a>"],[0,1,"<a href=\"StorageException.php.html#7\">App\\Exceptions\\StorageException<\/a>"],[0,4,"<a href=\"TripException.php.html#7\">App\\Exceptions\\TripException<\/a>"],[100,0,"<a href=\"UnknownGrammarClass.php.html#7\">App\\Exceptions\\UnknownGrammarClass<\/a>"],[0,1,"<a href=\"VendorException.php.html#7\">App\\Exceptions\\VendorException<\/a>"],[0,2,"<a href=\"WebhookException.php.html#7\">App\\Exceptions\\WebhookException<\/a>"],[0,4,"<a href=\"WorkOrderAssigneeException.php.html#7\">App\\Exceptions\\WorkOrderAssigneeException<\/a>"],[0,11,"<a href=\"WorkOrderException.php.html#7\">App\\Exceptions\\WorkOrderException<\/a>"],[0,1,"<a href=\"WorkOrderMediaException.php.html#7\">App\\Exceptions\\WorkOrderMediaException<\/a>"],[0,1,"<a href=\"WorkOrderMediaLimitException.php.html#7\">App\\Exceptions\\WorkOrderMediaLimitException<\/a>"],[0,2,"<a href=\"WorkOrderNoteException.php.html#7\">App\\Exceptions\\WorkOrderNoteException<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AbilityException.php.html#9\">App\\Exceptions\\AbilityException::undefinedModel<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioApiException.php.html#9\">App\\Exceptions\\Appfolio\\AppfolioApiException::__construct<\/a>"],[0,1,"<a href=\"Appfolio\/AppfolioApiResponseException.php.html#9\">App\\Exceptions\\Appfolio\\AppfolioApiResponseException::__construct<\/a>"],[0,1,"<a href=\"CouldNotPerformAction.php.html#9\">App\\Exceptions\\CouldNotPerformAction::__construct<\/a>"],[0,1,"<a href=\"Debug\/DatabaseProcessException.php.html#9\">App\\Exceptions\\Debug\\DatabaseProcessException::overKillingProcessHappening<\/a>"],[0,1,"<a href=\"ForbiddenException.php.html#10\">App\\Exceptions\\ForbiddenException::__construct<\/a>"],[0,1,"<a href=\"ForbiddenException.php.html#15\">App\\Exceptions\\ForbiddenException::accessDenied<\/a>"],[0,11,"<a href=\"Handler.php.html#56\">App\\Exceptions\\Handler::register<\/a>"],[0,1,"<a href=\"InvalidWorkOrderStateException.php.html#9\">App\\Exceptions\\InvalidWorkOrderStateException::__construct<\/a>"],[0,1,"<a href=\"InvoiceException.php.html#9\">App\\Exceptions\\InvoiceException::invalidWorkOrderStatus<\/a>"],[0,1,"<a href=\"InvoiceException.php.html#14\">App\\Exceptions\\InvoiceException::invalidInvoiceStatus<\/a>"],[0,1,"<a href=\"InvoiceException.php.html#19\">App\\Exceptions\\InvoiceException::invalidInvoice<\/a>"],[0,1,"<a href=\"InvoiceException.php.html#24\">App\\Exceptions\\InvoiceException::invalidPayload<\/a>"],[0,1,"<a href=\"InvoiceException.php.html#29\">App\\Exceptions\\InvoiceException::invalidInvoiceItem<\/a>"],[0,1,"<a href=\"InvoiceException.php.html#34\">App\\Exceptions\\InvoiceException::webhookFailed<\/a>"],[0,1,"<a href=\"InvoiceException.php.html#39\">App\\Exceptions\\InvoiceException::failed<\/a>"],[0,1,"<a href=\"IssueException.php.html#10\">App\\Exceptions\\IssueException::undefinedRouteSlug<\/a>"],[0,1,"<a href=\"IssueException.php.html#15\">App\\Exceptions\\IssueException::actionNotAllowed<\/a>"],[0,1,"<a href=\"IssueException.php.html#20\">App\\Exceptions\\IssueException::notFound<\/a>"],[0,1,"<a href=\"IssueException.php.html#25\">App\\Exceptions\\IssueException::invalid<\/a>"],[0,1,"<a href=\"IssueException.php.html#30\">App\\Exceptions\\IssueException::invalidWorkOrderIssue<\/a>"],[0,1,"<a href=\"IssueException.php.html#35\">App\\Exceptions\\IssueException::issueAlreadyAssigned<\/a>"],[0,1,"<a href=\"IssueException.php.html#40\">App\\Exceptions\\IssueException::serviceRequestNotFound<\/a>"],[0,1,"<a href=\"IssueException.php.html#45\">App\\Exceptions\\IssueException::couldNotPerformTransition<\/a>"],[0,1,"<a href=\"IssueException.php.html#50\">App\\Exceptions\\IssueException::mediaValidation<\/a>"],[0,1,"<a href=\"LulaWebhookException.php.html#9\">App\\Exceptions\\LulaWebhookException::webhookFailed<\/a>"],[0,1,"<a href=\"LulaWebhookException.php.html#14\">App\\Exceptions\\LulaWebhookException::emptyPayload<\/a>"],[0,1,"<a href=\"LulaWebhookException.php.html#19\">App\\Exceptions\\LulaWebhookException::workOrderDetailsNotFound<\/a>"],[0,1,"<a href=\"LulaWebhookException.php.html#24\">App\\Exceptions\\LulaWebhookException::emptyEvent<\/a>"],[0,1,"<a href=\"LulaWebhookException.php.html#29\">App\\Exceptions\\LulaWebhookException::invalidEventReceived<\/a>"],[0,1,"<a href=\"LulaWebhookException.php.html#34\">App\\Exceptions\\LulaWebhookException::lulaAppointmentNotExists<\/a>"],[0,1,"<a href=\"LulaWebhookException.php.html#39\">App\\Exceptions\\LulaWebhookException::scheduleDateMissing<\/a>"],[0,1,"<a href=\"LulaWebhookException.php.html#44\">App\\Exceptions\\LulaWebhookException::rescheduleFailed<\/a>"],[0,1,"<a href=\"LulaWebhookException.php.html#49\">App\\Exceptions\\LulaWebhookException::emptyReturnDate<\/a>"],[0,1,"<a href=\"NotFoundException.php.html#9\">App\\Exceptions\\NotFoundException::resourceNotFound<\/a>"],[0,1,"<a href=\"NotFoundException.php.html#14\">App\\Exceptions\\NotFoundException::userNotFound<\/a>"],[0,1,"<a href=\"NotFoundException.php.html#19\">App\\Exceptions\\NotFoundException::mediaNotExists<\/a>"],[0,1,"<a href=\"NotFoundException\/OrganizationNotFoundException.php.html#9\">App\\Exceptions\\NotFoundException\\OrganizationNotFoundException::__construct<\/a>"],[0,1,"<a href=\"NotFoundException\/ServiceRequestNotFoundException.php.html#9\">App\\Exceptions\\NotFoundException\\ServiceRequestNotFoundException::__construct<\/a>"],[0,1,"<a href=\"NotFoundException\/UserNotFoundException.php.html#9\">App\\Exceptions\\NotFoundException\\UserNotFoundException::__construct<\/a>"],[0,1,"<a href=\"NotFoundException\/WorkOrderNotFoundException.php.html#9\">App\\Exceptions\\NotFoundException\\WorkOrderNotFoundException::__construct<\/a>"],[0,1,"<a href=\"Organization\/OrganizationException.php.html#9\">App\\Exceptions\\Organization\\OrganizationException::domainSuffixNotFound<\/a>"],[0,1,"<a href=\"PermissionsFromInvalidFeatureException.php.html#9\">App\\Exceptions\\PermissionsFromInvalidFeatureException::__construct<\/a>"],[0,1,"<a href=\"QuoteException.php.html#9\">App\\Exceptions\\QuoteException::invalidWorkOrderStatus<\/a>"],[0,1,"<a href=\"QuoteException.php.html#14\">App\\Exceptions\\QuoteException::invalidQuoteStatus<\/a>"],[0,1,"<a href=\"QuoteException.php.html#19\">App\\Exceptions\\QuoteException::invalidQuote<\/a>"],[0,1,"<a href=\"QuoteException.php.html#24\">App\\Exceptions\\QuoteException::invalidPayload<\/a>"],[0,1,"<a href=\"QuoteException.php.html#29\">App\\Exceptions\\QuoteException::invalidQuoteTask<\/a>"],[0,1,"<a href=\"QuoteException.php.html#34\">App\\Exceptions\\QuoteException::invalidWorkOrderTask<\/a>"],[0,1,"<a href=\"QuoteException.php.html#39\">App\\Exceptions\\QuoteException::restoreFailed<\/a>"],[0,1,"<a href=\"QuoteException.php.html#44\">App\\Exceptions\\QuoteException::webhookFailed<\/a>"],[0,1,"<a href=\"QuoteException.php.html#49\">App\\Exceptions\\QuoteException::existingQuote<\/a>"],[0,1,"<a href=\"QuoteException.php.html#54\">App\\Exceptions\\QuoteException::atLeastOneQuoteTask<\/a>"],[0,1,"<a href=\"QuoteException.php.html#59\">App\\Exceptions\\QuoteException::mediaNotMatched<\/a>"],[0,1,"<a href=\"QuoteException.php.html#64\">App\\Exceptions\\QuoteException::quoteTaskNotFound<\/a>"],[0,1,"<a href=\"QuoteException.php.html#69\">App\\Exceptions\\QuoteException::tasksCountDoesNotMatch<\/a>"],[0,1,"<a href=\"QuoteException.php.html#74\">App\\Exceptions\\QuoteException::quoteExists<\/a>"],[0,1,"<a href=\"QuoteException.php.html#79\">App\\Exceptions\\QuoteException::quoteActionFailed<\/a>"],[0,1,"<a href=\"ResidentAvailabilityException.php.html#9\">App\\Exceptions\\ResidentAvailabilityException::invalidWorkOrder<\/a>"],[0,1,"<a href=\"ResidentAvailabilityException.php.html#14\">App\\Exceptions\\ResidentAvailabilityException::invalidServiceRequest<\/a>"],[0,1,"<a href=\"ResidentAvailabilityException.php.html#19\">App\\Exceptions\\ResidentAvailabilityException::invalidWorkOrderTask<\/a>"],[0,1,"<a href=\"ResidentAvailabilityException.php.html#24\">App\\Exceptions\\ResidentAvailabilityException::invalidWorkOrderStatus<\/a>"],[0,1,"<a href=\"ResidentAvailabilityException.php.html#29\">App\\Exceptions\\ResidentAvailabilityException::invalidServiceRequestStatus<\/a>"],[0,1,"<a href=\"ResidentAvailabilityException.php.html#34\">App\\Exceptions\\ResidentAvailabilityException::invalidResidentAvailability<\/a>"],[0,1,"<a href=\"ResidentAvailabilityException.php.html#39\">App\\Exceptions\\ResidentAvailabilityException::residentNotFound<\/a>"],[0,1,"<a href=\"ResidentAvailabilityException.php.html#44\">App\\Exceptions\\ResidentAvailabilityException::categoryNotFound<\/a>"],[0,1,"<a href=\"ResidentAvailabilityException.php.html#49\">App\\Exceptions\\ResidentAvailabilityException::residentPhoneNotFound<\/a>"],[0,1,"<a href=\"ResidentAvailabilityException.php.html#54\">App\\Exceptions\\ResidentAvailabilityException::recordAlreadyExisting<\/a>"],[0,1,"<a href=\"ResidentAvailabilityException.php.html#59\">App\\Exceptions\\ResidentAvailabilityException::updateFailed<\/a>"],[0,1,"<a href=\"ScheduleException.php.html#9\">App\\Exceptions\\ScheduleException::problemDiagnosisNotMatch<\/a>"],[0,1,"<a href=\"ScheduleException.php.html#14\">App\\Exceptions\\ScheduleException::windowNotFound<\/a>"],[0,1,"<a href=\"ScheduleException.php.html#19\">App\\Exceptions\\ScheduleException::noSkill<\/a>"],[0,1,"<a href=\"ScheduleException.php.html#24\">App\\Exceptions\\ScheduleException::noWorkingHours<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeException.php.html#9\">App\\Exceptions\\ServiceRequestAssigneeException::alreadyExisting<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeException.php.html#14\">App\\Exceptions\\ServiceRequestAssigneeException::invalidUserType<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeException.php.html#19\">App\\Exceptions\\ServiceRequestAssigneeException::organizationMismatch<\/a>"],[0,1,"<a href=\"ServiceRequestAssigneeException.php.html#24\">App\\Exceptions\\ServiceRequestAssigneeException::assigneeNotFound<\/a>"],[0,1,"<a href=\"ServiceRequestException.php.html#11\">App\\Exceptions\\ServiceRequestException::activityLogCreationMissingParameter<\/a>"],[0,1,"<a href=\"ServiceRequestMediaException.php.html#9\">App\\Exceptions\\ServiceRequestMediaException::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestMediaLimitException.php.html#9\">App\\Exceptions\\ServiceRequestMediaLimitException::__construct<\/a>"],[0,1,"<a href=\"SignedUrlException.php.html#9\">App\\Exceptions\\SignedUrlException::signedUrlNotProvided<\/a>"],[0,1,"<a href=\"SignedUrlException.php.html#14\">App\\Exceptions\\SignedUrlException::signedUrlExpired<\/a>"],[0,1,"<a href=\"StorageException.php.html#9\">App\\Exceptions\\StorageException::uploadFailed<\/a>"],[0,1,"<a href=\"TripException.php.html#9\">App\\Exceptions\\TripException::invalidWorkOrderTask<\/a>"],[0,1,"<a href=\"TripException.php.html#14\">App\\Exceptions\\TripException::appointmentNotFound<\/a>"],[0,1,"<a href=\"TripException.php.html#19\">App\\Exceptions\\TripException::invalidTrip<\/a>"],[0,1,"<a href=\"TripException.php.html#24\">App\\Exceptions\\TripException::userNotFound<\/a>"],[0,1,"<a href=\"VendorException.php.html#9\">App\\Exceptions\\VendorException::appfolioNotEnabled<\/a>"],[0,1,"<a href=\"WebhookException.php.html#9\">App\\Exceptions\\WebhookException::invalidEventRecieved<\/a>"],[0,1,"<a href=\"WebhookException.php.html#14\">App\\Exceptions\\WebhookException::invalidEventPayload<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeException.php.html#9\">App\\Exceptions\\WorkOrderAssigneeException::alreadyExisting<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeException.php.html#14\">App\\Exceptions\\WorkOrderAssigneeException::invalidUserType<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeException.php.html#19\">App\\Exceptions\\WorkOrderAssigneeException::organizationMismatch<\/a>"],[0,1,"<a href=\"WorkOrderAssigneeException.php.html#24\">App\\Exceptions\\WorkOrderAssigneeException::assigneeNotFound<\/a>"],[0,1,"<a href=\"WorkOrderException.php.html#9\">App\\Exceptions\\WorkOrderException::invalidWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrderException.php.html#14\">App\\Exceptions\\WorkOrderException::serviceCallNotFound<\/a>"],[0,1,"<a href=\"WorkOrderException.php.html#19\">App\\Exceptions\\WorkOrderException::activeServiceCallNotFound<\/a>"],[0,1,"<a href=\"WorkOrderException.php.html#24\">App\\Exceptions\\WorkOrderException::appointmentNotFound<\/a>"],[0,1,"<a href=\"WorkOrderException.php.html#29\">App\\Exceptions\\WorkOrderException::couldNotPerformTransition<\/a>"],[0,1,"<a href=\"WorkOrderException.php.html#34\">App\\Exceptions\\WorkOrderException::activeTripNotFound<\/a>"],[0,1,"<a href=\"WorkOrderException.php.html#39\">App\\Exceptions\\WorkOrderException::technicianAppointmentNotFound<\/a>"],[0,1,"<a href=\"WorkOrderException.php.html#44\">App\\Exceptions\\WorkOrderException::workOrderIssuesNotFound<\/a>"],[0,1,"<a href=\"WorkOrderException.php.html#49\">App\\Exceptions\\WorkOrderException::openIssueExists<\/a>"],[0,1,"<a href=\"WorkOrderException.php.html#54\">App\\Exceptions\\WorkOrderException::missingEnRouteStartTime<\/a>"],[0,1,"<a href=\"WorkOrderException.php.html#59\">App\\Exceptions\\WorkOrderException::missingStartTime<\/a>"],[0,1,"<a href=\"WorkOrderMediaException.php.html#9\">App\\Exceptions\\WorkOrderMediaException::__construct<\/a>"],[0,1,"<a href=\"WorkOrderMediaLimitException.php.html#9\">App\\Exceptions\\WorkOrderMediaLimitException::__construct<\/a>"],[0,1,"<a href=\"WorkOrderNoteException.php.html#9\">App\\Exceptions\\WorkOrderNoteException::inValidWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrderNoteException.php.html#14\">App\\Exceptions\\WorkOrderNoteException::inValidWorkOrderNote<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

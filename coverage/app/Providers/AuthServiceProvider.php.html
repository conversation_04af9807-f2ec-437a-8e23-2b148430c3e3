<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Providers/AuthServiceProvider.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Providers</a></li>
         <li class="breadcrumb-item active">AuthServiceProvider.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><abbr title="App\Providers\AuthServiceProvider">AuthServiceProvider</abbr></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#21"><abbr title="boot(): void">boot</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Providers</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Foundation\Support\Providers\AuthServiceProvider</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">ServiceProvider</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">AuthServiceProvider</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">ServiceProvider</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;The&nbsp;model&nbsp;to&nbsp;policy&nbsp;mappings&nbsp;for&nbsp;the&nbsp;application.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@var&nbsp;array&lt;class-string,&nbsp;class-string&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$policies</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;'App\Models\Model'&nbsp;=&gt;&nbsp;'App\Policies\ModelPolicy',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Register&nbsp;any&nbsp;authentication&nbsp;/&nbsp;authorization&nbsp;services.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="117 tests cover line 21" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateFilterTypeTest::__pest_evaluable_DateFilterType_enum_has_correct_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateFilterTypeTest::__pest_evaluable_DateFilterType_getDescription_returns_correct_descriptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateFilterTypeTest::__pest_evaluable_DateFilterType_can_be_invoked_statically&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateOperationTest::__pest_evaluable_DateOperation_enum_has_correct_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateOperationTest::__pest_evaluable_DateOperation_getDescription_returns_correct_descriptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateOperationTest::__pest_evaluable_DateOperation_can_be_invoked_statically&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_enum_has_correct_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_getDisplayName_returns_correct_names&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_getDateRange_returns_correct_date_ranges&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_makeCollection_returns_all_slugs_except_specified_ones&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_futureDates_returns_only_future_dates&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_can_be_invoked_statically&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_can_be_instantiated_with_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_in_house_user_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_third_party_vendor_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_multiple_uuids_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_for_invalid_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_non_existent_user_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_when_initial_status_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_continues_processing_after_individual_failures&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::minutes_to_days_simple_method_converts_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_with_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_without_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_passes_correct_parameters_to_onboarding_mail&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_empty_uuids_array&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_implements_should_queue_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_uses_correct_queue_traits&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::uuidConversionInApplyWorkOrderAssigneeFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::resolveWhereClauseReturnsCorrectClauseForAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::resolveWhereClauseReturnsCorrectClauseForOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithUnassignedValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithSingleDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithDateBetween&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithDateNotBetween&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithInvalidOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithInvalidDateOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::dateValuesForIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::dateValuesForIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::dateValuesForIsAfterOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::dateValuesForIsBeForeOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::dateOperationResponse&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::resolveWhereClauseOperator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::getDateRangeFromSlug&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::getDateRangeFromSlugThrowsExceptionForInvalidSlug&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithInvalidGroupOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::resolveOperatorSetForDateSlug&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::register_filter_handler&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_entity_id_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::filter_query&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::process_query&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::non_existent_filter_handler_returns_null&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::get_filter_type_for_field&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_where_clause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::is_direct_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::is_filter_group&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::has_filters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_simple_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_date_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_direct_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_filter_group&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::register_default_filter_handlers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::generate_query&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::get_model_for_field&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_work_order_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_vendor_work_order_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_service_request_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_user_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_quote_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_uuid_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::get_filter_type_for_field_with_special_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_where_clause_operator_with_special_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_where_clause_operator_with_invalid_operation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::process_query_with_all_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_work_order_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_vendor_work_order_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_service_request_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_user_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_quote_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SearchServiceTest::testRegisterSearchHandler&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SearchServiceTest::testApplySearch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SearchServiceTest::testEmptySearchTermReturnsUnmodifiedQuery&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SearchServiceTest::testDefaultSearchHandlersAreRegistered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testRegisterSortFieldMappings&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testGetSortFieldMappings&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testGetDefaultSortField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testApplySorting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testPrepareSortValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testPrepareSortValueForWorkOrderWithGroup&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testPrepareSortValueForWorkOrderWithHealthScore&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testPrepareSortValueForWorkOrderWithHealthScoreGrouping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testApplySortingWithMultipleFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testApplySortingWithNonExistentField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::public_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::authenticated_user_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::work_order_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::service_request_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::quote_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::view_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_onboarding_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::technician_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::notification_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::lookup_and_tag_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::middleware_groups_are_properly_applied&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::signed_routes_have_signed_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::throttled_routes_have_throttle_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::resource_routes_have_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::api_routes_have_correct_prefixes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::nested_resource_routes_are_properly_structured&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::route_methods_are_correctly_defined&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">boot</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span><span class="default">&nbsp;</span><span class="keyword">{</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

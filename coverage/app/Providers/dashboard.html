<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Providers</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Providers</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="TelescopeServiceProvider.php.html#10">App\Providers\TelescopeServiceProvider</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MediaServiceProvider.php.html#17">App\Providers\MediaServiceProvider</a></td><td class="text-right">7%</td></tr>
       <tr><td><a href="PaginationServiceProvider.php.html#8">App\Providers\PaginationServiceProvider</a></td><td class="text-right">20%</td></tr>
       <tr><td><a href="ResponseServiceProvider.php.html#11">App\Providers\ResponseServiceProvider</a></td><td class="text-right">22%</td></tr>
       <tr><td><a href="DatabaseGrammarServiceProvider.php.html#11">App\Providers\DatabaseGrammarServiceProvider</a></td><td class="text-right">35%</td></tr>
       <tr><td><a href="AppServiceProvider.php.html#15">App\Providers\AppServiceProvider</a></td><td class="text-right">62%</td></tr>
       <tr><td><a href="RequestServiceProvider.php.html#9">App\Providers\RequestServiceProvider</a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="HorizonServiceProvider.php.html#8">App\Providers\HorizonServiceProvider</a></td><td class="text-right">75%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ResponseServiceProvider.php.html#11">App\Providers\ResponseServiceProvider</a></td><td class="text-right">168</td></tr>
       <tr><td><a href="TelescopeServiceProvider.php.html#10">App\Providers\TelescopeServiceProvider</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="MediaServiceProvider.php.html#17">App\Providers\MediaServiceProvider</a></td><td class="text-right">58</td></tr>
       <tr><td><a href="DatabaseGrammarServiceProvider.php.html#11">App\Providers\DatabaseGrammarServiceProvider</a></td><td class="text-right">15</td></tr>
       <tr><td><a href="PaginationServiceProvider.php.html#8">App\Providers\PaginationServiceProvider</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RequestServiceProvider.php.html#9">App\Providers\RequestServiceProvider</a></td><td class="text-right">4</td></tr>
       <tr><td><a href="AppServiceProvider.php.html#15">App\Providers\AppServiceProvider</a></td><td class="text-right">3</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="TelescopeServiceProvider.php.html#15"><abbr title="App\Providers\TelescopeServiceProvider::register">register</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TelescopeServiceProvider.php.html#37"><abbr title="App\Providers\TelescopeServiceProvider::hideSensitiveRequestDetails">hideSensitiveRequestDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TelescopeServiceProvider.php.html#57"><abbr title="App\Providers\TelescopeServiceProvider::gate">gate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MediaServiceProvider.php.html#24"><abbr title="App\Providers\MediaServiceProvider::register">register</abbr></a></td><td class="text-right">7%</td></tr>
       <tr><td><a href="PaginationServiceProvider.php.html#21"><abbr title="App\Providers\PaginationServiceProvider::boot">boot</abbr></a></td><td class="text-right">15%</td></tr>
       <tr><td><a href="ResponseServiceProvider.php.html#24"><abbr title="App\Providers\ResponseServiceProvider::boot">boot</abbr></a></td><td class="text-right">21%</td></tr>
       <tr><td><a href="DatabaseGrammarServiceProvider.php.html#24"><abbr title="App\Providers\DatabaseGrammarServiceProvider::boot">boot</abbr></a></td><td class="text-right">30%</td></tr>
       <tr><td><a href="AppServiceProvider.php.html#20"><abbr title="App\Providers\AppServiceProvider::register">register</abbr></a></td><td class="text-right">40%</td></tr>
       <tr><td><a href="RequestServiceProvider.php.html#22"><abbr title="App\Providers\RequestServiceProvider::boot">boot</abbr></a></td><td class="text-right">62%</td></tr>
       <tr><td><a href="HorizonServiceProvider.php.html#28"><abbr title="App\Providers\HorizonServiceProvider::authorization">authorization</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="RouteServiceProvider.php.html#53"><abbr title="App\Providers\RouteServiceProvider::configureRateLimiting">configureRateLimiting</abbr></a></td><td class="text-right">66%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ResponseServiceProvider.php.html#24"><abbr title="App\Providers\ResponseServiceProvider::boot">boot</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="MediaServiceProvider.php.html#24"><abbr title="App\Providers\MediaServiceProvider::register">register</abbr></a></td><td class="text-right">58</td></tr>
       <tr><td><a href="TelescopeServiceProvider.php.html#15"><abbr title="App\Providers\TelescopeServiceProvider::register">register</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DatabaseGrammarServiceProvider.php.html#24"><abbr title="App\Providers\DatabaseGrammarServiceProvider::boot">boot</abbr></a></td><td class="text-right">13</td></tr>
       <tr><td><a href="PaginationServiceProvider.php.html#21"><abbr title="App\Providers\PaginationServiceProvider::boot">boot</abbr></a></td><td class="text-right">8</td></tr>
       <tr><td><a href="TelescopeServiceProvider.php.html#37"><abbr title="App\Providers\TelescopeServiceProvider::hideSensitiveRequestDetails">hideSensitiveRequestDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RequestServiceProvider.php.html#22"><abbr title="App\Providers\RequestServiceProvider::boot">boot</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="RouteServiceProvider.php.html#53"><abbr title="App\Providers\RouteServiceProvider::configureRateLimiting">configureRateLimiting</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="AppServiceProvider.php.html#20"><abbr title="App\Providers\AppServiceProvider::register">register</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([1,1,0,2,1,0,0,2,1,0,1,3], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([3,1,1,1,1,1,0,3,0,0,0,11], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[62.5,3,"<a href=\"AppServiceProvider.php.html#15\">App\\Providers\\AppServiceProvider<\/a>"],[100,1,"<a href=\"AuthServiceProvider.php.html#7\">App\\Providers\\AuthServiceProvider<\/a>"],[100,1,"<a href=\"BroadcastServiceProvider.php.html#8\">App\\Providers\\BroadcastServiceProvider<\/a>"],[35.714285714285715,6,"<a href=\"DatabaseGrammarServiceProvider.php.html#11\">App\\Providers\\DatabaseGrammarServiceProvider<\/a>"],[100,2,"<a href=\"EventServiceProvider.php.html#41\">App\\Providers\\EventServiceProvider<\/a>"],[75,2,"<a href=\"HorizonServiceProvider.php.html#8\">App\\Providers\\HorizonServiceProvider<\/a>"],[7.6923076923076925,8,"<a href=\"MediaServiceProvider.php.html#17\">App\\Providers\\MediaServiceProvider<\/a>"],[20,4,"<a href=\"PaginationServiceProvider.php.html#8\">App\\Providers\\PaginationServiceProvider<\/a>"],[66.66666666666666,4,"<a href=\"RequestServiceProvider.php.html#9\">App\\Providers\\RequestServiceProvider<\/a>"],[22.47191011235955,18,"<a href=\"ResponseServiceProvider.php.html#11\">App\\Providers\\ResponseServiceProvider<\/a>"],[91.66666666666666,4,"<a href=\"RouteServiceProvider.php.html#12\">App\\Providers\\RouteServiceProvider<\/a>"],[0,9,"<a href=\"TelescopeServiceProvider.php.html#10\">App\\Providers\\TelescopeServiceProvider<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[40,2,"<a href=\"AppServiceProvider.php.html#20\">App\\Providers\\AppServiceProvider::register<\/a>"],[100,1,"<a href=\"AppServiceProvider.php.html#44\">App\\Providers\\AppServiceProvider::boot<\/a>"],[100,1,"<a href=\"AuthServiceProvider.php.html#21\">App\\Providers\\AuthServiceProvider::boot<\/a>"],[100,1,"<a href=\"BroadcastServiceProvider.php.html#13\">App\\Providers\\BroadcastServiceProvider::boot<\/a>"],[100,1,"<a href=\"DatabaseGrammarServiceProvider.php.html#16\">App\\Providers\\DatabaseGrammarServiceProvider::register<\/a>"],[30.76923076923077,5,"<a href=\"DatabaseGrammarServiceProvider.php.html#24\">App\\Providers\\DatabaseGrammarServiceProvider::boot<\/a>"],[100,1,"<a href=\"EventServiceProvider.php.html#152\">App\\Providers\\EventServiceProvider::boot<\/a>"],[100,1,"<a href=\"EventServiceProvider.php.html#160\">App\\Providers\\EventServiceProvider::shouldDiscoverEvents<\/a>"],[100,1,"<a href=\"HorizonServiceProvider.php.html#13\">App\\Providers\\HorizonServiceProvider::boot<\/a>"],[66.66666666666666,1,"<a href=\"HorizonServiceProvider.php.html#28\">App\\Providers\\HorizonServiceProvider::authorization<\/a>"],[7.6923076923076925,8,"<a href=\"MediaServiceProvider.php.html#24\">App\\Providers\\MediaServiceProvider::register<\/a>"],[100,1,"<a href=\"PaginationServiceProvider.php.html#13\">App\\Providers\\PaginationServiceProvider::register<\/a>"],[15.789473684210526,3,"<a href=\"PaginationServiceProvider.php.html#21\">App\\Providers\\PaginationServiceProvider::boot<\/a>"],[100,1,"<a href=\"RequestServiceProvider.php.html#14\">App\\Providers\\RequestServiceProvider::register<\/a>"],[62.5,3,"<a href=\"RequestServiceProvider.php.html#22\">App\\Providers\\RequestServiceProvider::boot<\/a>"],[100,1,"<a href=\"ResponseServiceProvider.php.html#16\">App\\Providers\\ResponseServiceProvider::register<\/a>"],[21.59090909090909,17,"<a href=\"ResponseServiceProvider.php.html#24\">App\\Providers\\ResponseServiceProvider::boot<\/a>"],[100,1,"<a href=\"RouteServiceProvider.php.html#26\">App\\Providers\\RouteServiceProvider::boot<\/a>"],[66.66666666666666,3,"<a href=\"RouteServiceProvider.php.html#53\">App\\Providers\\RouteServiceProvider::configureRateLimiting<\/a>"],[0,6,"<a href=\"TelescopeServiceProvider.php.html#15\">App\\Providers\\TelescopeServiceProvider::register<\/a>"],[0,2,"<a href=\"TelescopeServiceProvider.php.html#37\">App\\Providers\\TelescopeServiceProvider::hideSensitiveRequestDetails<\/a>"],[0,1,"<a href=\"TelescopeServiceProvider.php.html#57\">App\\Providers\\TelescopeServiceProvider::gate<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

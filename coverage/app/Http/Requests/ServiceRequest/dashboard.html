<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Http/Requests/ServiceRequest</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Requests</a></li>
         <li class="breadcrumb-item"><a href="index.html">ServiceRequest</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AccessMethodUpdateRequest.php.html#9">App\Http\Requests\ServiceRequest\AccessMethodUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DescriptionUpdateRequest.php.html#7">App\Http\Requests\ServiceRequest\DescriptionUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FilterValuesRequest.php.html#7">App\Http\Requests\ServiceRequest\FilterValuesRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GroupViewRequest.php.html#7">App\Http\Requests\ServiceRequest\GroupViewRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListRequest.php.html#8">App\Http\Requests\ServiceRequest\ListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media/MediaUploadRequest.php.html#7">App\Http\Requests\ServiceRequest\Media\MediaUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media/ThumbnailUploadRequest.php.html#8">App\Http\Requests\ServiceRequest\Media\ThumbnailUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/CreateRequest.php.html#7">App\Http\Requests\ServiceRequest\Note\CreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/UpdateRequest.php.html#7">App\Http\Requests\ServiceRequest\Note\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriorityUpdateRequest.php.html#9">App\Http\Requests\ServiceRequest\PriorityUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PropertyAddressUpdateRequest.php.html#10">App\Http\Requests\ServiceRequest\PropertyAddressUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityRequest.php.html#15">App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdateRequest.php.html#10">App\Http\Requests\ServiceRequest\ResidentUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreWorkOrderRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreWorkOrderRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateAvailabilityRequest.php.html#12">App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ResidentAvailabilityRequest.php.html#15">App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ListRequest.php.html#8">App\Http\Requests\ServiceRequest\ListRequest</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="StoreWorkOrderRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreWorkOrderRequest</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="StoreRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="UpdateAvailabilityRequest.php.html#12">App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AccessMethodUpdateRequest.php.html#16"><abbr title="App\Http\Requests\ServiceRequest\AccessMethodUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DescriptionUpdateRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\DescriptionUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FilterValuesRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\FilterValuesRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GroupViewRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\GroupViewRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListRequest.php.html#13"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListRequest.php.html#23"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media/MediaUploadRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\Media\MediaUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media/ThumbnailUploadRequest.php.html#15"><abbr title="App\Http\Requests\ServiceRequest\Media\ThumbnailUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/CreateRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\Note\CreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/UpdateRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\Note\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriorityUpdateRequest.php.html#16"><abbr title="App\Http\Requests\ServiceRequest\PriorityUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PropertyAddressUpdateRequest.php.html#17"><abbr title="App\Http\Requests\ServiceRequest\PropertyAddressUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityRequest.php.html#22"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityRequest.php.html#59"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityRequest.php.html#72"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityRequest.php.html#94"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdateRequest.php.html#17"><abbr title="App\Http\Requests\ServiceRequest\ResidentUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreRequest.php.html#20"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreRequest.php.html#81"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreRequest.php.html#128"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreRequest.php.html#152"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreWorkOrderRequest.php.html#20"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreWorkOrderRequest.php.html#51"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreWorkOrderRequest.php.html#87"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreWorkOrderRequest.php.html#112"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreWorkOrderRequest.php.html#138"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::propertyRules">propertyRules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreWorkOrderRequest.php.html#169"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::residentRules">residentRules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateAvailabilityRequest.php.html#19"><abbr title="App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateAvailabilityRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ResidentAvailabilityRequest.php.html#94"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ListRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ResidentAvailabilityRequest.php.html#22"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::rules">rules</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="StoreRequest.php.html#152"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="StoreWorkOrderRequest.php.html#112"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ResidentAvailabilityRequest.php.html#72"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::after">after</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateAvailabilityRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest::after">after</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:04:51 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([16,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([30,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AccessMethodUpdateRequest.php.html#9\">App\\Http\\Requests\\ServiceRequest\\AccessMethodUpdateRequest<\/a>"],[0,1,"<a href=\"DescriptionUpdateRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\DescriptionUpdateRequest<\/a>"],[0,1,"<a href=\"FilterValuesRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\FilterValuesRequest<\/a>"],[0,1,"<a href=\"GroupViewRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\GroupViewRequest<\/a>"],[0,8,"<a href=\"ListRequest.php.html#8\">App\\Http\\Requests\\ServiceRequest\\ListRequest<\/a>"],[0,1,"<a href=\"Media\/MediaUploadRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\Media\\MediaUploadRequest<\/a>"],[0,1,"<a href=\"Media\/ThumbnailUploadRequest.php.html#8\">App\\Http\\Requests\\ServiceRequest\\Media\\ThumbnailUploadRequest<\/a>"],[0,1,"<a href=\"Note\/CreateRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\Note\\CreateRequest<\/a>"],[0,1,"<a href=\"Note\/UpdateRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\Note\\UpdateRequest<\/a>"],[0,1,"<a href=\"PriorityUpdateRequest.php.html#9\">App\\Http\\Requests\\ServiceRequest\\PriorityUpdateRequest<\/a>"],[0,1,"<a href=\"PropertyAddressUpdateRequest.php.html#10\">App\\Http\\Requests\\ServiceRequest\\PropertyAddressUpdateRequest<\/a>"],[0,14,"<a href=\"ResidentAvailabilityRequest.php.html#15\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest<\/a>"],[0,1,"<a href=\"ResidentUpdateRequest.php.html#10\">App\\Http\\Requests\\ServiceRequest\\ResidentUpdateRequest<\/a>"],[0,7,"<a href=\"StoreRequest.php.html#13\">App\\Http\\Requests\\ServiceRequest\\StoreRequest<\/a>"],[0,8,"<a href=\"StoreWorkOrderRequest.php.html#13\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest<\/a>"],[0,3,"<a href=\"UpdateAvailabilityRequest.php.html#12\">App\\Http\\Requests\\ServiceRequest\\UpdateAvailabilityRequest<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AccessMethodUpdateRequest.php.html#16\">App\\Http\\Requests\\ServiceRequest\\AccessMethodUpdateRequest::rules<\/a>"],[0,1,"<a href=\"DescriptionUpdateRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\DescriptionUpdateRequest::rules<\/a>"],[0,1,"<a href=\"FilterValuesRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\FilterValuesRequest::rules<\/a>"],[0,1,"<a href=\"GroupViewRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\GroupViewRequest::rules<\/a>"],[0,1,"<a href=\"ListRequest.php.html#13\">App\\Http\\Requests\\ServiceRequest\\ListRequest::authorize<\/a>"],[0,1,"<a href=\"ListRequest.php.html#23\">App\\Http\\Requests\\ServiceRequest\\ListRequest::rules<\/a>"],[0,6,"<a href=\"ListRequest.php.html#43\">App\\Http\\Requests\\ServiceRequest\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Media\/MediaUploadRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\Media\\MediaUploadRequest::rules<\/a>"],[0,1,"<a href=\"Media\/ThumbnailUploadRequest.php.html#15\">App\\Http\\Requests\\ServiceRequest\\Media\\ThumbnailUploadRequest::rules<\/a>"],[0,1,"<a href=\"Note\/CreateRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\Note\\CreateRequest::rules<\/a>"],[0,1,"<a href=\"Note\/UpdateRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\Note\\UpdateRequest::rules<\/a>"],[0,1,"<a href=\"PriorityUpdateRequest.php.html#16\">App\\Http\\Requests\\ServiceRequest\\PriorityUpdateRequest::rules<\/a>"],[0,1,"<a href=\"PropertyAddressUpdateRequest.php.html#17\">App\\Http\\Requests\\ServiceRequest\\PropertyAddressUpdateRequest::rules<\/a>"],[0,4,"<a href=\"ResidentAvailabilityRequest.php.html#22\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::rules<\/a>"],[0,1,"<a href=\"ResidentAvailabilityRequest.php.html#59\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::attributes<\/a>"],[0,2,"<a href=\"ResidentAvailabilityRequest.php.html#72\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::after<\/a>"],[0,7,"<a href=\"ResidentAvailabilityRequest.php.html#94\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"ResidentUpdateRequest.php.html#17\">App\\Http\\Requests\\ServiceRequest\\ResidentUpdateRequest::rules<\/a>"],[0,1,"<a href=\"StoreRequest.php.html#20\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::rules<\/a>"],[0,1,"<a href=\"StoreRequest.php.html#81\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::attributes<\/a>"],[0,1,"<a href=\"StoreRequest.php.html#128\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::messages<\/a>"],[0,4,"<a href=\"StoreRequest.php.html#152\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"StoreWorkOrderRequest.php.html#20\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::rules<\/a>"],[0,1,"<a href=\"StoreWorkOrderRequest.php.html#51\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::attributes<\/a>"],[0,1,"<a href=\"StoreWorkOrderRequest.php.html#87\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::messages<\/a>"],[0,3,"<a href=\"StoreWorkOrderRequest.php.html#112\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"StoreWorkOrderRequest.php.html#138\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::propertyRules<\/a>"],[0,1,"<a href=\"StoreWorkOrderRequest.php.html#169\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::residentRules<\/a>"],[0,1,"<a href=\"UpdateAvailabilityRequest.php.html#19\">App\\Http\\Requests\\ServiceRequest\\UpdateAvailabilityRequest::rules<\/a>"],[0,2,"<a href=\"UpdateAvailabilityRequest.php.html#43\">App\\Http\\Requests\\ServiceRequest\\UpdateAvailabilityRequest::after<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Http/Requests/WorkOrder</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Requests</a></li>
         <li class="breadcrumb-item"><a href="index.html">WorkOrder</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AccessMethodUpdateRequest.php.html#9">App\Http\Requests\WorkOrder\AccessMethodUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApproveQuoteRequest.php.html#9">App\Http\Requests\WorkOrder\ApproveQuoteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookmarkRequest.php.html#7">App\Http\Requests\WorkOrder\BookmarkRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelRequest.php.html#7">App\Http\Requests\WorkOrder\CancelRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CloseRequest.php.html#7">App\Http\Requests\WorkOrder\CloseRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CompleteRequest.php.html#12">App\Http\Requests\WorkOrder\CompleteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuoteRequest.php.html#14">App\Http\Requests\WorkOrder\CreateQuoteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DescriptionUpdateRequest.php.html#7">App\Http\Requests\WorkOrder\DescriptionUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DueDateUpdateRequest.php.html#7">App\Http\Requests\WorkOrder\DueDateUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FilterValuesRequest.php.html#7">App\Http\Requests\WorkOrder\FilterValuesRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GroupViewRequest.php.html#7">App\Http\Requests\WorkOrder\GroupViewRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListRequest.php.html#7">App\Http\Requests\WorkOrder\ListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media/MediaUploadRequest.php.html#7">App\Http\Requests\WorkOrder\Media\MediaUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media/ThumbnailUploadRequest.php.html#10">App\Http\Requests\WorkOrder\Media\ThumbnailUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/CreateRequest.php.html#7">App\Http\Requests\WorkOrder\Note\CreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/UpdateRequest.php.html#7">App\Http\Requests\WorkOrder\Note\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PauseRequest.php.html#7">App\Http\Requests\WorkOrder\PauseRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriorityUpdateRequest.php.html#9">App\Http\Requests\WorkOrder\PriorityUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProblemCategoryCreateRequest.php.html#9">App\Http\Requests\WorkOrder\ProblemCategoryCreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProblemCategoryDeleteRequest.php.html#10">App\Http\Requests\WorkOrder\ProblemCategoryDeleteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProblemCategoryUpdateRequest.php.html#10">App\Http\Requests\WorkOrder\ProblemCategoryUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PropertyAddressUpdateRequest.php.html#10">App\Http\Requests\WorkOrder\PropertyAddressUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/TaskCreateRequest.php.html#14">App\Http\Requests\WorkOrder\Quote\TaskCreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/TaskUpdateRequest.php.html#15">App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToInvoiceRequest.php.html#11">App\Http\Requests\WorkOrder\ReadyToInvoiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdateRequest.php.html#10">App\Http\Requests\WorkOrder\ResidentUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SendToVendorRequest.php.html#12">App\Http\Requests\WorkOrder\SendToVendorRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreRequest.php.html#11">App\Http\Requests\WorkOrder\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagRequest.php.html#9">App\Http\Requests\WorkOrder\TagRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateNteAmountRequest.php.html#7">App\Http\Requests\WorkOrder\UpdateNteAmountRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateTripDetailsRequest.php.html#13">App\Http\Requests\WorkOrder\UpdateTripDetailsRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateTripRequest.php.html#14">App\Http\Requests\WorkOrder\UpdateTripRequest</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CreateQuoteRequest.php.html#14">App\Http\Requests\WorkOrder\CreateQuoteRequest</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Quote/TaskCreateRequest.php.html#14">App\Http\Requests\WorkOrder\Quote\TaskCreateRequest</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Quote/TaskUpdateRequest.php.html#15">App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ListRequest.php.html#7">App\Http\Requests\WorkOrder\ListRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="SendToVendorRequest.php.html#12">App\Http\Requests\WorkOrder\SendToVendorRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="StoreRequest.php.html#11">App\Http\Requests\WorkOrder\StoreRequest</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="CompleteRequest.php.html#12">App\Http\Requests\WorkOrder\CompleteRequest</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ReadyToInvoiceRequest.php.html#11">App\Http\Requests\WorkOrder\ReadyToInvoiceRequest</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AccessMethodUpdateRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\AccessMethodUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApproveQuoteRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\ApproveQuoteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookmarkRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\BookmarkRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\CancelRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CloseRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\CloseRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CompleteRequest.php.html#19"><abbr title="App\Http\Requests\WorkOrder\CompleteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuoteRequest.php.html#21"><abbr title="App\Http\Requests\WorkOrder\CreateQuoteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuoteRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\CreateQuoteRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DescriptionUpdateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\DescriptionUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DueDateUpdateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\DueDateUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FilterValuesRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\FilterValuesRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GroupViewRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\GroupViewRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\ListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListRequest.php.html#34"><abbr title="App\Http\Requests\WorkOrder\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media/MediaUploadRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\Media\MediaUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Media/ThumbnailUploadRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\Media\ThumbnailUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/CreateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\Note\CreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/UpdateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\Note\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PauseRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\PauseRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriorityUpdateRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\PriorityUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProblemCategoryCreateRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\ProblemCategoryCreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProblemCategoryDeleteRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\ProblemCategoryDeleteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProblemCategoryUpdateRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\ProblemCategoryUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PropertyAddressUpdateRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\PropertyAddressUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/TaskCreateRequest.php.html#21"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskCreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/TaskCreateRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskCreateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/TaskUpdateRequest.php.html#22"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/TaskUpdateRequest.php.html#52"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadyToInvoiceRequest.php.html#18"><abbr title="App\Http\Requests\WorkOrder\ReadyToInvoiceRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentUpdateRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\ResidentUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SendToVendorRequest.php.html#19"><abbr title="App\Http\Requests\WorkOrder\SendToVendorRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SendToVendorRequest.php.html#40"><abbr title="App\Http\Requests\WorkOrder\SendToVendorRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreRequest.php.html#18"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreRequest.php.html#81"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreRequest.php.html#123"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StoreRequest.php.html#149"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\TagRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateNteAmountRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\UpdateNteAmountRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateNteAmountRequest.php.html#31"><abbr title="App\Http\Requests\WorkOrder\UpdateNteAmountRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateTripDetailsRequest.php.html#20"><abbr title="App\Http\Requests\WorkOrder\UpdateTripDetailsRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateTripRequest.php.html#21"><abbr title="App\Http\Requests\WorkOrder\UpdateTripRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CreateQuoteRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\CreateQuoteRequest::after">after</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Quote/TaskCreateRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskCreateRequest::after">after</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Quote/TaskUpdateRequest.php.html#52"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest::after">after</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ListRequest.php.html#34"><abbr title="App\Http\Requests\WorkOrder\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SendToVendorRequest.php.html#40"><abbr title="App\Http\Requests\WorkOrder\SendToVendorRequest::after">after</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="CompleteRequest.php.html#19"><abbr title="App\Http\Requests\WorkOrder\CompleteRequest::rules">rules</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="StoreRequest.php.html#149"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ReadyToInvoiceRequest.php.html#18"><abbr title="App\Http\Requests\WorkOrder\ReadyToInvoiceRequest::rules">rules</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Thu Jun 26 15:40:22 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([32,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([41,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AccessMethodUpdateRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\AccessMethodUpdateRequest<\/a>"],[0,1,"<a href=\"ApproveQuoteRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\ApproveQuoteRequest<\/a>"],[0,1,"<a href=\"BookmarkRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\BookmarkRequest<\/a>"],[0,1,"<a href=\"CancelRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\CancelRequest<\/a>"],[0,1,"<a href=\"CloseRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\CloseRequest<\/a>"],[0,3,"<a href=\"CompleteRequest.php.html#12\">App\\Http\\Requests\\WorkOrder\\CompleteRequest<\/a>"],[0,14,"<a href=\"CreateQuoteRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\CreateQuoteRequest<\/a>"],[0,1,"<a href=\"DescriptionUpdateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\DescriptionUpdateRequest<\/a>"],[0,1,"<a href=\"DueDateUpdateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\DueDateUpdateRequest<\/a>"],[0,1,"<a href=\"FilterValuesRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\FilterValuesRequest<\/a>"],[0,1,"<a href=\"GroupViewRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\GroupViewRequest<\/a>"],[0,7,"<a href=\"ListRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\ListRequest<\/a>"],[0,1,"<a href=\"Media\/MediaUploadRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\Media\\MediaUploadRequest<\/a>"],[0,1,"<a href=\"Media\/ThumbnailUploadRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\Media\\ThumbnailUploadRequest<\/a>"],[0,1,"<a href=\"Note\/CreateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\Note\\CreateRequest<\/a>"],[0,1,"<a href=\"Note\/UpdateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\Note\\UpdateRequest<\/a>"],[0,1,"<a href=\"PauseRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\PauseRequest<\/a>"],[0,1,"<a href=\"PriorityUpdateRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\PriorityUpdateRequest<\/a>"],[0,1,"<a href=\"ProblemCategoryCreateRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryCreateRequest<\/a>"],[0,1,"<a href=\"ProblemCategoryDeleteRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryDeleteRequest<\/a>"],[0,1,"<a href=\"ProblemCategoryUpdateRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryUpdateRequest<\/a>"],[0,1,"<a href=\"PropertyAddressUpdateRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\PropertyAddressUpdateRequest<\/a>"],[0,13,"<a href=\"Quote\/TaskCreateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskCreateRequest<\/a>"],[0,13,"<a href=\"Quote\/TaskUpdateRequest.php.html#15\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskUpdateRequest<\/a>"],[0,2,"<a href=\"ReadyToInvoiceRequest.php.html#11\">App\\Http\\Requests\\WorkOrder\\ReadyToInvoiceRequest<\/a>"],[0,1,"<a href=\"ResidentUpdateRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\ResidentUpdateRequest<\/a>"],[0,7,"<a href=\"SendToVendorRequest.php.html#12\">App\\Http\\Requests\\WorkOrder\\SendToVendorRequest<\/a>"],[0,6,"<a href=\"StoreRequest.php.html#11\">App\\Http\\Requests\\WorkOrder\\StoreRequest<\/a>"],[0,1,"<a href=\"TagRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\TagRequest<\/a>"],[0,2,"<a href=\"UpdateNteAmountRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\UpdateNteAmountRequest<\/a>"],[0,1,"<a href=\"UpdateTripDetailsRequest.php.html#13\">App\\Http\\Requests\\WorkOrder\\UpdateTripDetailsRequest<\/a>"],[0,1,"<a href=\"UpdateTripRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\UpdateTripRequest<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AccessMethodUpdateRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\AccessMethodUpdateRequest::rules<\/a>"],[0,1,"<a href=\"ApproveQuoteRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\ApproveQuoteRequest::rules<\/a>"],[0,1,"<a href=\"BookmarkRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\BookmarkRequest::rules<\/a>"],[0,1,"<a href=\"CancelRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\CancelRequest::rules<\/a>"],[0,1,"<a href=\"CloseRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\CloseRequest::rules<\/a>"],[0,3,"<a href=\"CompleteRequest.php.html#19\">App\\Http\\Requests\\WorkOrder\\CompleteRequest::rules<\/a>"],[0,1,"<a href=\"CreateQuoteRequest.php.html#21\">App\\Http\\Requests\\WorkOrder\\CreateQuoteRequest::rules<\/a>"],[0,13,"<a href=\"CreateQuoteRequest.php.html#50\">App\\Http\\Requests\\WorkOrder\\CreateQuoteRequest::after<\/a>"],[0,1,"<a href=\"DescriptionUpdateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\DescriptionUpdateRequest::rules<\/a>"],[0,1,"<a href=\"DueDateUpdateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\DueDateUpdateRequest::rules<\/a>"],[0,1,"<a href=\"FilterValuesRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\FilterValuesRequest::rules<\/a>"],[0,1,"<a href=\"GroupViewRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\GroupViewRequest::rules<\/a>"],[0,1,"<a href=\"ListRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\ListRequest::rules<\/a>"],[0,6,"<a href=\"ListRequest.php.html#34\">App\\Http\\Requests\\WorkOrder\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Media\/MediaUploadRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Media\\MediaUploadRequest::rules<\/a>"],[0,1,"<a href=\"Media\/ThumbnailUploadRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\Media\\ThumbnailUploadRequest::rules<\/a>"],[0,1,"<a href=\"Note\/CreateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Note\\CreateRequest::rules<\/a>"],[0,1,"<a href=\"Note\/UpdateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Note\\UpdateRequest::rules<\/a>"],[0,1,"<a href=\"PauseRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\PauseRequest::rules<\/a>"],[0,1,"<a href=\"PriorityUpdateRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\PriorityUpdateRequest::rules<\/a>"],[0,1,"<a href=\"ProblemCategoryCreateRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryCreateRequest::rules<\/a>"],[0,1,"<a href=\"ProblemCategoryDeleteRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryDeleteRequest::rules<\/a>"],[0,1,"<a href=\"ProblemCategoryUpdateRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryUpdateRequest::rules<\/a>"],[0,1,"<a href=\"PropertyAddressUpdateRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\PropertyAddressUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Quote\/TaskCreateRequest.php.html#21\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskCreateRequest::rules<\/a>"],[0,12,"<a href=\"Quote\/TaskCreateRequest.php.html#50\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskCreateRequest::after<\/a>"],[0,1,"<a href=\"Quote\/TaskUpdateRequest.php.html#22\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskUpdateRequest::rules<\/a>"],[0,12,"<a href=\"Quote\/TaskUpdateRequest.php.html#52\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskUpdateRequest::after<\/a>"],[0,2,"<a href=\"ReadyToInvoiceRequest.php.html#18\">App\\Http\\Requests\\WorkOrder\\ReadyToInvoiceRequest::rules<\/a>"],[0,1,"<a href=\"ResidentUpdateRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\ResidentUpdateRequest::rules<\/a>"],[0,1,"<a href=\"SendToVendorRequest.php.html#19\">App\\Http\\Requests\\WorkOrder\\SendToVendorRequest::rules<\/a>"],[0,6,"<a href=\"SendToVendorRequest.php.html#40\">App\\Http\\Requests\\WorkOrder\\SendToVendorRequest::after<\/a>"],[0,1,"<a href=\"StoreRequest.php.html#18\">App\\Http\\Requests\\WorkOrder\\StoreRequest::rules<\/a>"],[0,1,"<a href=\"StoreRequest.php.html#81\">App\\Http\\Requests\\WorkOrder\\StoreRequest::attributes<\/a>"],[0,1,"<a href=\"StoreRequest.php.html#123\">App\\Http\\Requests\\WorkOrder\\StoreRequest::messages<\/a>"],[0,3,"<a href=\"StoreRequest.php.html#149\">App\\Http\\Requests\\WorkOrder\\StoreRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"TagRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\TagRequest::rules<\/a>"],[0,1,"<a href=\"UpdateNteAmountRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\UpdateNteAmountRequest::rules<\/a>"],[0,1,"<a href=\"UpdateNteAmountRequest.php.html#31\">App\\Http\\Requests\\WorkOrder\\UpdateNteAmountRequest::messages<\/a>"],[0,1,"<a href=\"UpdateTripDetailsRequest.php.html#20\">App\\Http\\Requests\\WorkOrder\\UpdateTripDetailsRequest::rules<\/a>"],[0,1,"<a href=\"UpdateTripRequest.php.html#21\">App\\Http\\Requests\\WorkOrder\\UpdateTripRequest::rules<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

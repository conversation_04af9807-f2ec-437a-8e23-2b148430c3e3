<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Http/Requests</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="index.html">Requests</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Auth/PublicApiAuthenticateRequest.php.html#7">App\Http\Requests\Auth\PublicApiAuthenticateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/SearchAddressRequest.php.html#7">App\Http\Requests\Auth\SearchAddressRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/SetVendorPasswordRequest.php.html#7">App\Http\Requests\Auth\SetVendorPasswordRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseApiRequest.php.html#7">App\Http\Requests\BaseApiRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceRequest.php.html#24">App\Http\Requests\Invoice\InvoiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/AssignRequest.php.html#9">App\Http\Requests\Issue\AssignRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CreateIssueRequest.php.html#11">App\Http\Requests\Issue\CreateIssueRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UnassignRequest.php.html#9">App\Http\Requests\Issue\UnassignRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization/UpdateRequest.php.html#10">App\Http\Requests\Organization\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/ListRequest.php.html#7">App\Http\Requests\Quote\ListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Schedule/GetTechnicianListRequest.php.html#10">App\Http\Requests\Schedule\GetTechnicianListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Schedule/ReScheduleAppointmentRequest.php.html#14">App\Http\Requests\Schedule\ReScheduleAppointmentRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Schedule/StoreAppointmentRequest.php.html#10">App\Http\Requests\Schedule\StoreAppointmentRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/AccessMethodUpdateRequest.php.html#9">App\Http\Requests\ServiceRequest\AccessMethodUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/DescriptionUpdateRequest.php.html#7">App\Http\Requests\ServiceRequest\DescriptionUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/FilterValuesRequest.php.html#7">App\Http\Requests\ServiceRequest\FilterValuesRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/GroupViewRequest.php.html#7">App\Http\Requests\ServiceRequest\GroupViewRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ListRequest.php.html#8">App\Http\Requests\ServiceRequest\ListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/Media/MediaUploadRequest.php.html#7">App\Http\Requests\ServiceRequest\Media\MediaUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/Media/ThumbnailUploadRequest.php.html#8">App\Http\Requests\ServiceRequest\Media\ThumbnailUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/Note/CreateRequest.php.html#7">App\Http\Requests\ServiceRequest\Note\CreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/Note/UpdateRequest.php.html#7">App\Http\Requests\ServiceRequest\Note\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PriorityUpdateRequest.php.html#9">App\Http\Requests\ServiceRequest\PriorityUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PropertyAddressUpdateRequest.php.html#10">App\Http\Requests\ServiceRequest\PropertyAddressUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityRequest.php.html#15">App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentUpdateRequest.php.html#10">App\Http\Requests\ServiceRequest\ResidentUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreWorkOrderRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreWorkOrderRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdateAvailabilityRequest.php.html#12">App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/StoreRequest.php.html#7">App\Http\Requests\Tag\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/UpdateRequest.php.html#7">App\Http\Requests\Tag\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/AvailabilityDatesRequest.php.html#7">App\Http\Requests\Technician\AvailabilityDatesRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/BlockOutUpdateRequest.php.html#14">App\Http\Requests\Technician\BlockOutUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/ScheduleWorkOrderRequest.php.html#8">App\Http\Requests\Technician\ScheduleWorkOrderRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/SkillsUpdateRequest.php.html#12">App\Http\Requests\Technician\SkillsUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/StoreBlockOutRequest.php.html#14">App\Http\Requests\Technician\StoreBlockOutRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/WorkingHoursUpdateRequest.php.html#10">App\Http\Requests\Technician\WorkingHoursUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/FilterValuesRequest.php.html#7">App\Http\Requests\User\FilterValuesRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/GroupViewRequest.php.html#7">App\Http\Requests\User\GroupViewRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/ListRequest.php.html#7">App\Http\Requests\User\ListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/StoreRequest.php.html#11">App\Http\Requests\User\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/StoreVendorUserRequest.php.html#9">App\Http\Requests\User\StoreVendorUserRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/UpdateRequest.php.html#17">App\Http\Requests\User\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ListRequest.php.html#7">App\Http\Requests\Vendor\ListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/StoreRequest.php.html#9">App\Http\Requests\Vendor\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnBoardingSetServiceAreasRequest.php.html#7">App\Http\Requests\Vendor\VendorOnBoardingSetServiceAreasRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingGenerateSignedUrlRequest.php.html#7">App\Http\Requests\Vendor\VendorOnboardingGenerateSignedUrlRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingInitialRequest.php.html#9">App\Http\Requests\Vendor\VendorOnboardingInitialRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingSetBasicInfoRequest.php.html#7">App\Http\Requests\Vendor\VendorOnboardingSetBasicInfoRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingSetServicesRequest.php.html#7">App\Http\Requests\Vendor\VendorOnboardingSetServicesRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingSetStatusRequest.php.html#7">App\Http\Requests\Vendor\VendorOnboardingSetStatusRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/ConfigurationRequest.php.html#7">App\Http\Requests\View\ConfigurationRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/CountRequest.php.html#7">App\Http\Requests\View\CountRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/DuplicateRequest.php.html#12">App\Http\Requests\View\DuplicateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/PinRequest.php.html#8">App\Http\Requests\View\PinRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/RenameRequest.php.html#11">App\Http\Requests\View\RenameRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/StoreRequest.php.html#13">App\Http\Requests\View\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/UpdateRequest.php.html#8">App\Http\Requests\View\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AccessMethodUpdateRequest.php.html#9">App\Http\Requests\WorkOrder\AccessMethodUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ApproveQuoteRequest.php.html#9">App\Http\Requests\WorkOrder\ApproveQuoteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/BookmarkRequest.php.html#7">App\Http\Requests\WorkOrder\BookmarkRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/CancelRequest.php.html#7">App\Http\Requests\WorkOrder\CancelRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/CloseRequest.php.html#7">App\Http\Requests\WorkOrder\CloseRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/CompleteRequest.php.html#12">App\Http\Requests\WorkOrder\CompleteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/CreateQuoteRequest.php.html#14">App\Http\Requests\WorkOrder\CreateQuoteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DescriptionUpdateRequest.php.html#7">App\Http\Requests\WorkOrder\DescriptionUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateUpdateRequest.php.html#7">App\Http\Requests\WorkOrder\DueDateUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/FilterValuesRequest.php.html#7">App\Http\Requests\WorkOrder\FilterValuesRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/GroupViewRequest.php.html#7">App\Http\Requests\WorkOrder\GroupViewRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ListRequest.php.html#7">App\Http\Requests\WorkOrder\ListRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Media/MediaUploadRequest.php.html#7">App\Http\Requests\WorkOrder\Media\MediaUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Media/ThumbnailUploadRequest.php.html#10">App\Http\Requests\WorkOrder\Media\ThumbnailUploadRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/CreateRequest.php.html#7">App\Http\Requests\WorkOrder\Note\CreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/UpdateRequest.php.html#7">App\Http\Requests\WorkOrder\Note\UpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PauseRequest.php.html#7">App\Http\Requests\WorkOrder\PauseRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityUpdateRequest.php.html#9">App\Http\Requests\WorkOrder\PriorityUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ProblemCategoryCreateRequest.php.html#9">App\Http\Requests\WorkOrder\ProblemCategoryCreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ProblemCategoryDeleteRequest.php.html#10">App\Http\Requests\WorkOrder\ProblemCategoryDeleteRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ProblemCategoryUpdateRequest.php.html#10">App\Http\Requests\WorkOrder\ProblemCategoryUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PropertyAddressUpdateRequest.php.html#10">App\Http\Requests\WorkOrder\PropertyAddressUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/TaskCreateRequest.php.html#14">App\Http\Requests\WorkOrder\Quote\TaskCreateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/TaskUpdateRequest.php.html#15">App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ReadyToInvoiceRequest.php.html#11">App\Http\Requests\WorkOrder\ReadyToInvoiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentUpdateRequest.php.html#10">App\Http\Requests\WorkOrder\ResidentUpdateRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/SendToVendorRequest.php.html#12">App\Http\Requests\WorkOrder\SendToVendorRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/StoreRequest.php.html#11">App\Http\Requests\WorkOrder\StoreRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/TagRequest.php.html#9">App\Http\Requests\WorkOrder\TagRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/UpdateNteAmountRequest.php.html#7">App\Http\Requests\WorkOrder\UpdateNteAmountRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/UpdateTripDetailsRequest.php.html#13">App\Http\Requests\WorkOrder\UpdateTripDetailsRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/UpdateTripRequest.php.html#14">App\Http\Requests\WorkOrder\UpdateTripRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssueRequest.php.html#7">App\Http\Requests\WorkOrderIssue\DeclineIssueRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssueRequest.php.html#12">App\Http\Requests\WorkOrderIssue\MarkAsDoneIssueRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/WorkOrderIssueUpdateRequest.php.html#13">App\Http\Requests\WorkOrderIssue\WorkOrderIssueUpdateRequest</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Invoice/InvoiceRequest.php.html#24">App\Http\Requests\Invoice\InvoiceRequest</a></td><td class="text-right">2756</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityRequest.php.html#15">App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Technician/WorkingHoursUpdateRequest.php.html#10">App\Http\Requests\Technician\WorkingHoursUpdateRequest</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="WorkOrder/CreateQuoteRequest.php.html#14">App\Http\Requests\WorkOrder\CreateQuoteRequest</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="WorkOrder/Quote/TaskCreateRequest.php.html#14">App\Http\Requests\WorkOrder\Quote\TaskCreateRequest</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="WorkOrder/Quote/TaskUpdateRequest.php.html#15">App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ServiceRequest/ListRequest.php.html#8">App\Http\Requests\ServiceRequest\ListRequest</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ServiceRequest/StoreWorkOrderRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreWorkOrderRequest</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Technician/BlockOutUpdateRequest.php.html#14">App\Http\Requests\Technician\BlockOutUpdateRequest</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Technician/SkillsUpdateRequest.php.html#12">App\Http\Requests\Technician\SkillsUpdateRequest</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Quote/ListRequest.php.html#7">App\Http\Requests\Quote\ListRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequest/StoreRequest.php.html#13">App\Http\Requests\ServiceRequest\StoreRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="User/ListRequest.php.html#7">App\Http\Requests\User\ListRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrder/ListRequest.php.html#7">App\Http\Requests\WorkOrder\ListRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrder/SendToVendorRequest.php.html#12">App\Http\Requests\WorkOrder\SendToVendorRequest</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Vendor/ListRequest.php.html#7">App\Http\Requests\Vendor\ListRequest</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/StoreRequest.php.html#11">App\Http\Requests\WorkOrder\StoreRequest</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Technician/StoreBlockOutRequest.php.html#14">App\Http\Requests\Technician\StoreBlockOutRequest</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="User/UpdateRequest.php.html#17">App\Http\Requests\User\UpdateRequest</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="View/DuplicateRequest.php.html#12">App\Http\Requests\View\DuplicateRequest</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="View/RenameRequest.php.html#11">App\Http\Requests\View\RenameRequest</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="View/StoreRequest.php.html#13">App\Http\Requests\View\StoreRequest</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequest/UpdateAvailabilityRequest.php.html#12">App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/CompleteRequest.php.html#12">App\Http\Requests\WorkOrder\CompleteRequest</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/ReadyToInvoiceRequest.php.html#11">App\Http\Requests\WorkOrder\ReadyToInvoiceRequest</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Auth/PublicApiAuthenticateRequest.php.html#14"><abbr title="App\Http\Requests\Auth\PublicApiAuthenticateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/SearchAddressRequest.php.html#9"><abbr title="App\Http\Requests\Auth\SearchAddressRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/SearchAddressRequest.php.html#19"><abbr title="App\Http\Requests\Auth\SearchAddressRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/SetVendorPasswordRequest.php.html#9"><abbr title="App\Http\Requests\Auth\SetVendorPasswordRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/SetVendorPasswordRequest.php.html#19"><abbr title="App\Http\Requests\Auth\SetVendorPasswordRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseApiRequest.php.html#12"><abbr title="App\Http\Requests\BaseApiRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceRequest.php.html#31"><abbr title="App\Http\Requests\Invoice\InvoiceRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceRequest.php.html#162"><abbr title="App\Http\Requests\Invoice\InvoiceRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/AssignRequest.php.html#16"><abbr title="App\Http\Requests\Issue\AssignRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CreateIssueRequest.php.html#18"><abbr title="App\Http\Requests\Issue\CreateIssueRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UnassignRequest.php.html#16"><abbr title="App\Http\Requests\Issue\UnassignRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Organization/UpdateRequest.php.html#17"><abbr title="App\Http\Requests\Organization\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/ListRequest.php.html#14"><abbr title="App\Http\Requests\Quote\ListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/ListRequest.php.html#34"><abbr title="App\Http\Requests\Quote\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Schedule/GetTechnicianListRequest.php.html#17"><abbr title="App\Http\Requests\Schedule\GetTechnicianListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Schedule/ReScheduleAppointmentRequest.php.html#21"><abbr title="App\Http\Requests\Schedule\ReScheduleAppointmentRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Schedule/StoreAppointmentRequest.php.html#17"><abbr title="App\Http\Requests\Schedule\StoreAppointmentRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/AccessMethodUpdateRequest.php.html#16"><abbr title="App\Http\Requests\ServiceRequest\AccessMethodUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/DescriptionUpdateRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\DescriptionUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/FilterValuesRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\FilterValuesRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/GroupViewRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\GroupViewRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ListRequest.php.html#13"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ListRequest.php.html#23"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ListRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/Media/MediaUploadRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\Media\MediaUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/Media/ThumbnailUploadRequest.php.html#15"><abbr title="App\Http\Requests\ServiceRequest\Media\ThumbnailUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/Note/CreateRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\Note\CreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/Note/UpdateRequest.php.html#14"><abbr title="App\Http\Requests\ServiceRequest\Note\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PriorityUpdateRequest.php.html#16"><abbr title="App\Http\Requests\ServiceRequest\PriorityUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PropertyAddressUpdateRequest.php.html#17"><abbr title="App\Http\Requests\ServiceRequest\PropertyAddressUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityRequest.php.html#22"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityRequest.php.html#59"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityRequest.php.html#72"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityRequest.php.html#94"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentUpdateRequest.php.html#17"><abbr title="App\Http\Requests\ServiceRequest\ResidentUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreRequest.php.html#20"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreRequest.php.html#81"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreRequest.php.html#128"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreRequest.php.html#152"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreWorkOrderRequest.php.html#20"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreWorkOrderRequest.php.html#51"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreWorkOrderRequest.php.html#87"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreWorkOrderRequest.php.html#112"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreWorkOrderRequest.php.html#138"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::propertyRules">propertyRules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/StoreWorkOrderRequest.php.html#169"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::residentRules">residentRules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdateAvailabilityRequest.php.html#19"><abbr title="App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdateAvailabilityRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/StoreRequest.php.html#14"><abbr title="App\Http\Requests\Tag\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/StoreRequest.php.html#31"><abbr title="App\Http\Requests\Tag\StoreRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/UpdateRequest.php.html#14"><abbr title="App\Http\Requests\Tag\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/UpdateRequest.php.html#33"><abbr title="App\Http\Requests\Tag\UpdateRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/AvailabilityDatesRequest.php.html#14"><abbr title="App\Http\Requests\Technician\AvailabilityDatesRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/BlockOutUpdateRequest.php.html#21"><abbr title="App\Http\Requests\Technician\BlockOutUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/BlockOutUpdateRequest.php.html#41"><abbr title="App\Http\Requests\Technician\BlockOutUpdateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/ScheduleWorkOrderRequest.php.html#15"><abbr title="App\Http\Requests\Technician\ScheduleWorkOrderRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/SkillsUpdateRequest.php.html#19"><abbr title="App\Http\Requests\Technician\SkillsUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/SkillsUpdateRequest.php.html#35"><abbr title="App\Http\Requests\Technician\SkillsUpdateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/StoreBlockOutRequest.php.html#21"><abbr title="App\Http\Requests\Technician\StoreBlockOutRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/StoreBlockOutRequest.php.html#41"><abbr title="App\Http\Requests\Technician\StoreBlockOutRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/WorkingHoursUpdateRequest.php.html#17"><abbr title="App\Http\Requests\Technician\WorkingHoursUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/WorkingHoursUpdateRequest.php.html#33"><abbr title="App\Http\Requests\Technician\WorkingHoursUpdateRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Technician/WorkingHoursUpdateRequest.php.html#52"><abbr title="App\Http\Requests\Technician\WorkingHoursUpdateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/FilterValuesRequest.php.html#14"><abbr title="App\Http\Requests\User\FilterValuesRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/GroupViewRequest.php.html#14"><abbr title="App\Http\Requests\User\GroupViewRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/ListRequest.php.html#14"><abbr title="App\Http\Requests\User\ListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/ListRequest.php.html#33"><abbr title="App\Http\Requests\User\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/StoreRequest.php.html#18"><abbr title="App\Http\Requests\User\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/StoreVendorUserRequest.php.html#16"><abbr title="App\Http\Requests\User\StoreVendorUserRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/UpdateRequest.php.html#24"><abbr title="App\Http\Requests\User\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/UpdateRequest.php.html#79"><abbr title="App\Http\Requests\User\UpdateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ListRequest.php.html#14"><abbr title="App\Http\Requests\Vendor\ListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/ListRequest.php.html#34"><abbr title="App\Http\Requests\Vendor\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/StoreRequest.php.html#16"><abbr title="App\Http\Requests\Vendor\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnBoardingSetServiceAreasRequest.php.html#9"><abbr title="App\Http\Requests\Vendor\VendorOnBoardingSetServiceAreasRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnBoardingSetServiceAreasRequest.php.html#19"><abbr title="App\Http\Requests\Vendor\VendorOnBoardingSetServiceAreasRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingGenerateSignedUrlRequest.php.html#9"><abbr title="App\Http\Requests\Vendor\VendorOnboardingGenerateSignedUrlRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingGenerateSignedUrlRequest.php.html#19"><abbr title="App\Http\Requests\Vendor\VendorOnboardingGenerateSignedUrlRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingInitialRequest.php.html#11"><abbr title="App\Http\Requests\Vendor\VendorOnboardingInitialRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingInitialRequest.php.html#21"><abbr title="App\Http\Requests\Vendor\VendorOnboardingInitialRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingSetBasicInfoRequest.php.html#12"><abbr title="App\Http\Requests\Vendor\VendorOnboardingSetBasicInfoRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingSetBasicInfoRequest.php.html#22"><abbr title="App\Http\Requests\Vendor\VendorOnboardingSetBasicInfoRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingSetServicesRequest.php.html#9"><abbr title="App\Http\Requests\Vendor\VendorOnboardingSetServicesRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingSetServicesRequest.php.html#19"><abbr title="App\Http\Requests\Vendor\VendorOnboardingSetServicesRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingSetStatusRequest.php.html#9"><abbr title="App\Http\Requests\Vendor\VendorOnboardingSetStatusRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingSetStatusRequest.php.html#19"><abbr title="App\Http\Requests\Vendor\VendorOnboardingSetStatusRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/ConfigurationRequest.php.html#14"><abbr title="App\Http\Requests\View\ConfigurationRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/CountRequest.php.html#14"><abbr title="App\Http\Requests\View\CountRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/DuplicateRequest.php.html#19"><abbr title="App\Http\Requests\View\DuplicateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/DuplicateRequest.php.html#32"><abbr title="App\Http\Requests\View\DuplicateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/PinRequest.php.html#15"><abbr title="App\Http\Requests\View\PinRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/RenameRequest.php.html#18"><abbr title="App\Http\Requests\View\RenameRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/RenameRequest.php.html#31"><abbr title="App\Http\Requests\View\RenameRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/StoreRequest.php.html#20"><abbr title="App\Http\Requests\View\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/StoreRequest.php.html#43"><abbr title="App\Http\Requests\View\StoreRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="View/UpdateRequest.php.html#15"><abbr title="App\Http\Requests\View\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AccessMethodUpdateRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\AccessMethodUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ApproveQuoteRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\ApproveQuoteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/BookmarkRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\BookmarkRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/CancelRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\CancelRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/CloseRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\CloseRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/CompleteRequest.php.html#19"><abbr title="App\Http\Requests\WorkOrder\CompleteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/CreateQuoteRequest.php.html#21"><abbr title="App\Http\Requests\WorkOrder\CreateQuoteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/CreateQuoteRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\CreateQuoteRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DescriptionUpdateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\DescriptionUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateUpdateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\DueDateUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/FilterValuesRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\FilterValuesRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/GroupViewRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\GroupViewRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ListRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\ListRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ListRequest.php.html#34"><abbr title="App\Http\Requests\WorkOrder\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Media/MediaUploadRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\Media\MediaUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Media/ThumbnailUploadRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\Media\ThumbnailUploadRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/CreateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\Note\CreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/UpdateRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\Note\UpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PauseRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\PauseRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityUpdateRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\PriorityUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ProblemCategoryCreateRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\ProblemCategoryCreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ProblemCategoryDeleteRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\ProblemCategoryDeleteRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ProblemCategoryUpdateRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\ProblemCategoryUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PropertyAddressUpdateRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\PropertyAddressUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/TaskCreateRequest.php.html#21"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskCreateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/TaskCreateRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskCreateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/TaskUpdateRequest.php.html#22"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/TaskUpdateRequest.php.html#52"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ReadyToInvoiceRequest.php.html#18"><abbr title="App\Http\Requests\WorkOrder\ReadyToInvoiceRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentUpdateRequest.php.html#17"><abbr title="App\Http\Requests\WorkOrder\ResidentUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/SendToVendorRequest.php.html#19"><abbr title="App\Http\Requests\WorkOrder\SendToVendorRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/SendToVendorRequest.php.html#40"><abbr title="App\Http\Requests\WorkOrder\SendToVendorRequest::after">after</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/StoreRequest.php.html#18"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/StoreRequest.php.html#81"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::attributes">attributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/StoreRequest.php.html#123"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/StoreRequest.php.html#149"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/TagRequest.php.html#16"><abbr title="App\Http\Requests\WorkOrder\TagRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/UpdateNteAmountRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrder\UpdateNteAmountRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/UpdateNteAmountRequest.php.html#31"><abbr title="App\Http\Requests\WorkOrder\UpdateNteAmountRequest::messages">messages</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/UpdateTripDetailsRequest.php.html#20"><abbr title="App\Http\Requests\WorkOrder\UpdateTripDetailsRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/UpdateTripRequest.php.html#21"><abbr title="App\Http\Requests\WorkOrder\UpdateTripRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssueRequest.php.html#14"><abbr title="App\Http\Requests\WorkOrderIssue\DeclineIssueRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssueRequest.php.html#19"><abbr title="App\Http\Requests\WorkOrderIssue\MarkAsDoneIssueRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/WorkOrderIssueUpdateRequest.php.html#20"><abbr title="App\Http\Requests\WorkOrderIssue\WorkOrderIssueUpdateRequest::rules">rules</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Invoice/InvoiceRequest.php.html#162"><abbr title="App\Http\Requests\Invoice\InvoiceRequest::after">after</abbr></a></td><td class="text-right">2652</td></tr>
       <tr><td><a href="WorkOrder/CreateQuoteRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\CreateQuoteRequest::after">after</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Technician/WorkingHoursUpdateRequest.php.html#52"><abbr title="App\Http\Requests\Technician\WorkingHoursUpdateRequest::after">after</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="WorkOrder/Quote/TaskCreateRequest.php.html#50"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskCreateRequest::after">after</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="WorkOrder/Quote/TaskUpdateRequest.php.html#52"><abbr title="App\Http\Requests\WorkOrder\Quote\TaskUpdateRequest::after">after</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityRequest.php.html#94"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Technician/BlockOutUpdateRequest.php.html#41"><abbr title="App\Http\Requests\Technician\BlockOutUpdateRequest::after">after</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Technician/SkillsUpdateRequest.php.html#35"><abbr title="App\Http\Requests\Technician\SkillsUpdateRequest::after">after</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Quote/ListRequest.php.html#34"><abbr title="App\Http\Requests\Quote\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequest/ListRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="User/ListRequest.php.html#33"><abbr title="App\Http\Requests\User\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/ListRequest.php.html#34"><abbr title="App\Http\Requests\WorkOrder\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/SendToVendorRequest.php.html#40"><abbr title="App\Http\Requests\WorkOrder\SendToVendorRequest::after">after</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Vendor/ListRequest.php.html#34"><abbr title="App\Http\Requests\Vendor\ListRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityRequest.php.html#22"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::rules">rules</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequest/StoreRequest.php.html#152"><abbr title="App\Http\Requests\ServiceRequest\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Technician/StoreBlockOutRequest.php.html#41"><abbr title="App\Http\Requests\Technician\StoreBlockOutRequest::after">after</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="User/UpdateRequest.php.html#79"><abbr title="App\Http\Requests\User\UpdateRequest::after">after</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequest/StoreWorkOrderRequest.php.html#112"><abbr title="App\Http\Requests\ServiceRequest\StoreWorkOrderRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="View/DuplicateRequest.php.html#32"><abbr title="App\Http\Requests\View\DuplicateRequest::after">after</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="View/RenameRequest.php.html#31"><abbr title="App\Http\Requests\View\RenameRequest::after">after</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="View/StoreRequest.php.html#43"><abbr title="App\Http\Requests\View\StoreRequest::after">after</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/CompleteRequest.php.html#19"><abbr title="App\Http\Requests\WorkOrder\CompleteRequest::rules">rules</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/StoreRequest.php.html#149"><abbr title="App\Http\Requests\WorkOrder\StoreRequest::prepareForValidation">prepareForValidation</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityRequest.php.html#72"><abbr title="App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest::after">after</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/UpdateAvailabilityRequest.php.html#43"><abbr title="App\Http\Requests\ServiceRequest\UpdateAvailabilityRequest::after">after</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/ReadyToInvoiceRequest.php.html#18"><abbr title="App\Http\Requests\WorkOrder\ReadyToInvoiceRequest::rules">rules</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Thu Jun 26 15:40:22 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([93,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([139,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Auth\/PublicApiAuthenticateRequest.php.html#7\">App\\Http\\Requests\\Auth\\PublicApiAuthenticateRequest<\/a>"],[0,2,"<a href=\"Auth\/SearchAddressRequest.php.html#7\">App\\Http\\Requests\\Auth\\SearchAddressRequest<\/a>"],[0,2,"<a href=\"Auth\/SetVendorPasswordRequest.php.html#7\">App\\Http\\Requests\\Auth\\SetVendorPasswordRequest<\/a>"],[0,1,"<a href=\"BaseApiRequest.php.html#7\">App\\Http\\Requests\\BaseApiRequest<\/a>"],[0,52,"<a href=\"Invoice\/InvoiceRequest.php.html#24\">App\\Http\\Requests\\Invoice\\InvoiceRequest<\/a>"],[0,1,"<a href=\"Issue\/AssignRequest.php.html#9\">App\\Http\\Requests\\Issue\\AssignRequest<\/a>"],[0,1,"<a href=\"Issue\/CreateIssueRequest.php.html#11\">App\\Http\\Requests\\Issue\\CreateIssueRequest<\/a>"],[0,1,"<a href=\"Issue\/UnassignRequest.php.html#9\">App\\Http\\Requests\\Issue\\UnassignRequest<\/a>"],[0,1,"<a href=\"Organization\/UpdateRequest.php.html#10\">App\\Http\\Requests\\Organization\\UpdateRequest<\/a>"],[0,7,"<a href=\"Quote\/ListRequest.php.html#7\">App\\Http\\Requests\\Quote\\ListRequest<\/a>"],[0,1,"<a href=\"Schedule\/GetTechnicianListRequest.php.html#10\">App\\Http\\Requests\\Schedule\\GetTechnicianListRequest<\/a>"],[0,1,"<a href=\"Schedule\/ReScheduleAppointmentRequest.php.html#14\">App\\Http\\Requests\\Schedule\\ReScheduleAppointmentRequest<\/a>"],[0,1,"<a href=\"Schedule\/StoreAppointmentRequest.php.html#10\">App\\Http\\Requests\\Schedule\\StoreAppointmentRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/AccessMethodUpdateRequest.php.html#9\">App\\Http\\Requests\\ServiceRequest\\AccessMethodUpdateRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/DescriptionUpdateRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\DescriptionUpdateRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/FilterValuesRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\FilterValuesRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/GroupViewRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\GroupViewRequest<\/a>"],[0,8,"<a href=\"ServiceRequest\/ListRequest.php.html#8\">App\\Http\\Requests\\ServiceRequest\\ListRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/Media\/MediaUploadRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\Media\\MediaUploadRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/Media\/ThumbnailUploadRequest.php.html#8\">App\\Http\\Requests\\ServiceRequest\\Media\\ThumbnailUploadRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/Note\/CreateRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\Note\\CreateRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/Note\/UpdateRequest.php.html#7\">App\\Http\\Requests\\ServiceRequest\\Note\\UpdateRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/PriorityUpdateRequest.php.html#9\">App\\Http\\Requests\\ServiceRequest\\PriorityUpdateRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/PropertyAddressUpdateRequest.php.html#10\">App\\Http\\Requests\\ServiceRequest\\PropertyAddressUpdateRequest<\/a>"],[0,14,"<a href=\"ServiceRequest\/ResidentAvailabilityRequest.php.html#15\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/ResidentUpdateRequest.php.html#10\">App\\Http\\Requests\\ServiceRequest\\ResidentUpdateRequest<\/a>"],[0,7,"<a href=\"ServiceRequest\/StoreRequest.php.html#13\">App\\Http\\Requests\\ServiceRequest\\StoreRequest<\/a>"],[0,8,"<a href=\"ServiceRequest\/StoreWorkOrderRequest.php.html#13\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest<\/a>"],[0,3,"<a href=\"ServiceRequest\/UpdateAvailabilityRequest.php.html#12\">App\\Http\\Requests\\ServiceRequest\\UpdateAvailabilityRequest<\/a>"],[0,2,"<a href=\"Tag\/StoreRequest.php.html#7\">App\\Http\\Requests\\Tag\\StoreRequest<\/a>"],[0,2,"<a href=\"Tag\/UpdateRequest.php.html#7\">App\\Http\\Requests\\Tag\\UpdateRequest<\/a>"],[0,1,"<a href=\"Technician\/AvailabilityDatesRequest.php.html#7\">App\\Http\\Requests\\Technician\\AvailabilityDatesRequest<\/a>"],[0,8,"<a href=\"Technician\/BlockOutUpdateRequest.php.html#14\">App\\Http\\Requests\\Technician\\BlockOutUpdateRequest<\/a>"],[0,1,"<a href=\"Technician\/ScheduleWorkOrderRequest.php.html#8\">App\\Http\\Requests\\Technician\\ScheduleWorkOrderRequest<\/a>"],[0,8,"<a href=\"Technician\/SkillsUpdateRequest.php.html#12\">App\\Http\\Requests\\Technician\\SkillsUpdateRequest<\/a>"],[0,5,"<a href=\"Technician\/StoreBlockOutRequest.php.html#14\">App\\Http\\Requests\\Technician\\StoreBlockOutRequest<\/a>"],[0,14,"<a href=\"Technician\/WorkingHoursUpdateRequest.php.html#10\">App\\Http\\Requests\\Technician\\WorkingHoursUpdateRequest<\/a>"],[0,1,"<a href=\"User\/FilterValuesRequest.php.html#7\">App\\Http\\Requests\\User\\FilterValuesRequest<\/a>"],[0,1,"<a href=\"User\/GroupViewRequest.php.html#7\">App\\Http\\Requests\\User\\GroupViewRequest<\/a>"],[0,7,"<a href=\"User\/ListRequest.php.html#7\">App\\Http\\Requests\\User\\ListRequest<\/a>"],[0,1,"<a href=\"User\/StoreRequest.php.html#11\">App\\Http\\Requests\\User\\StoreRequest<\/a>"],[0,1,"<a href=\"User\/StoreVendorUserRequest.php.html#9\">App\\Http\\Requests\\User\\StoreVendorUserRequest<\/a>"],[0,5,"<a href=\"User\/UpdateRequest.php.html#17\">App\\Http\\Requests\\User\\UpdateRequest<\/a>"],[0,6,"<a href=\"Vendor\/ListRequest.php.html#7\">App\\Http\\Requests\\Vendor\\ListRequest<\/a>"],[0,1,"<a href=\"Vendor\/StoreRequest.php.html#9\">App\\Http\\Requests\\Vendor\\StoreRequest<\/a>"],[0,2,"<a href=\"Vendor\/VendorOnBoardingSetServiceAreasRequest.php.html#7\">App\\Http\\Requests\\Vendor\\VendorOnBoardingSetServiceAreasRequest<\/a>"],[0,2,"<a href=\"Vendor\/VendorOnboardingGenerateSignedUrlRequest.php.html#7\">App\\Http\\Requests\\Vendor\\VendorOnboardingGenerateSignedUrlRequest<\/a>"],[0,2,"<a href=\"Vendor\/VendorOnboardingInitialRequest.php.html#9\">App\\Http\\Requests\\Vendor\\VendorOnboardingInitialRequest<\/a>"],[0,2,"<a href=\"Vendor\/VendorOnboardingSetBasicInfoRequest.php.html#7\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetBasicInfoRequest<\/a>"],[0,2,"<a href=\"Vendor\/VendorOnboardingSetServicesRequest.php.html#7\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetServicesRequest<\/a>"],[0,2,"<a href=\"Vendor\/VendorOnboardingSetStatusRequest.php.html#7\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetStatusRequest<\/a>"],[0,1,"<a href=\"View\/ConfigurationRequest.php.html#7\">App\\Http\\Requests\\View\\ConfigurationRequest<\/a>"],[0,1,"<a href=\"View\/CountRequest.php.html#7\">App\\Http\\Requests\\View\\CountRequest<\/a>"],[0,4,"<a href=\"View\/DuplicateRequest.php.html#12\">App\\Http\\Requests\\View\\DuplicateRequest<\/a>"],[0,1,"<a href=\"View\/PinRequest.php.html#8\">App\\Http\\Requests\\View\\PinRequest<\/a>"],[0,4,"<a href=\"View\/RenameRequest.php.html#11\">App\\Http\\Requests\\View\\RenameRequest<\/a>"],[0,4,"<a href=\"View\/StoreRequest.php.html#13\">App\\Http\\Requests\\View\\StoreRequest<\/a>"],[0,1,"<a href=\"View\/UpdateRequest.php.html#8\">App\\Http\\Requests\\View\\UpdateRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/AccessMethodUpdateRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\AccessMethodUpdateRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/ApproveQuoteRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\ApproveQuoteRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/BookmarkRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\BookmarkRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/CancelRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\CancelRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/CloseRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\CloseRequest<\/a>"],[0,3,"<a href=\"WorkOrder\/CompleteRequest.php.html#12\">App\\Http\\Requests\\WorkOrder\\CompleteRequest<\/a>"],[0,14,"<a href=\"WorkOrder\/CreateQuoteRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\CreateQuoteRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/DescriptionUpdateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\DescriptionUpdateRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/DueDateUpdateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\DueDateUpdateRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/FilterValuesRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\FilterValuesRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/GroupViewRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\GroupViewRequest<\/a>"],[0,7,"<a href=\"WorkOrder\/ListRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\ListRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/Media\/MediaUploadRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\Media\\MediaUploadRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/Media\/ThumbnailUploadRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\Media\\ThumbnailUploadRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/CreateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\Note\\CreateRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/UpdateRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\Note\\UpdateRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/PauseRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\PauseRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/PriorityUpdateRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\PriorityUpdateRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/ProblemCategoryCreateRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryCreateRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/ProblemCategoryDeleteRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryDeleteRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/ProblemCategoryUpdateRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryUpdateRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/PropertyAddressUpdateRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\PropertyAddressUpdateRequest<\/a>"],[0,13,"<a href=\"WorkOrder\/Quote\/TaskCreateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskCreateRequest<\/a>"],[0,13,"<a href=\"WorkOrder\/Quote\/TaskUpdateRequest.php.html#15\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskUpdateRequest<\/a>"],[0,2,"<a href=\"WorkOrder\/ReadyToInvoiceRequest.php.html#11\">App\\Http\\Requests\\WorkOrder\\ReadyToInvoiceRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentUpdateRequest.php.html#10\">App\\Http\\Requests\\WorkOrder\\ResidentUpdateRequest<\/a>"],[0,7,"<a href=\"WorkOrder\/SendToVendorRequest.php.html#12\">App\\Http\\Requests\\WorkOrder\\SendToVendorRequest<\/a>"],[0,6,"<a href=\"WorkOrder\/StoreRequest.php.html#11\">App\\Http\\Requests\\WorkOrder\\StoreRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/TagRequest.php.html#9\">App\\Http\\Requests\\WorkOrder\\TagRequest<\/a>"],[0,2,"<a href=\"WorkOrder\/UpdateNteAmountRequest.php.html#7\">App\\Http\\Requests\\WorkOrder\\UpdateNteAmountRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/UpdateTripDetailsRequest.php.html#13\">App\\Http\\Requests\\WorkOrder\\UpdateTripDetailsRequest<\/a>"],[0,1,"<a href=\"WorkOrder\/UpdateTripRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\UpdateTripRequest<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/DeclineIssueRequest.php.html#7\">App\\Http\\Requests\\WorkOrderIssue\\DeclineIssueRequest<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/MarkAsDoneIssueRequest.php.html#12\">App\\Http\\Requests\\WorkOrderIssue\\MarkAsDoneIssueRequest<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/WorkOrderIssueUpdateRequest.php.html#13\">App\\Http\\Requests\\WorkOrderIssue\\WorkOrderIssueUpdateRequest<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Auth\/PublicApiAuthenticateRequest.php.html#14\">App\\Http\\Requests\\Auth\\PublicApiAuthenticateRequest::rules<\/a>"],[0,1,"<a href=\"Auth\/SearchAddressRequest.php.html#9\">App\\Http\\Requests\\Auth\\SearchAddressRequest::authorize<\/a>"],[0,1,"<a href=\"Auth\/SearchAddressRequest.php.html#19\">App\\Http\\Requests\\Auth\\SearchAddressRequest::rules<\/a>"],[0,1,"<a href=\"Auth\/SetVendorPasswordRequest.php.html#9\">App\\Http\\Requests\\Auth\\SetVendorPasswordRequest::authorize<\/a>"],[0,1,"<a href=\"Auth\/SetVendorPasswordRequest.php.html#19\">App\\Http\\Requests\\Auth\\SetVendorPasswordRequest::rules<\/a>"],[0,1,"<a href=\"BaseApiRequest.php.html#12\">App\\Http\\Requests\\BaseApiRequest::authorize<\/a>"],[0,1,"<a href=\"Invoice\/InvoiceRequest.php.html#31\">App\\Http\\Requests\\Invoice\\InvoiceRequest::rules<\/a>"],[0,51,"<a href=\"Invoice\/InvoiceRequest.php.html#162\">App\\Http\\Requests\\Invoice\\InvoiceRequest::after<\/a>"],[0,1,"<a href=\"Issue\/AssignRequest.php.html#16\">App\\Http\\Requests\\Issue\\AssignRequest::rules<\/a>"],[0,1,"<a href=\"Issue\/CreateIssueRequest.php.html#18\">App\\Http\\Requests\\Issue\\CreateIssueRequest::rules<\/a>"],[0,1,"<a href=\"Issue\/UnassignRequest.php.html#16\">App\\Http\\Requests\\Issue\\UnassignRequest::rules<\/a>"],[0,1,"<a href=\"Organization\/UpdateRequest.php.html#17\">App\\Http\\Requests\\Organization\\UpdateRequest::rules<\/a>"],[0,1,"<a href=\"Quote\/ListRequest.php.html#14\">App\\Http\\Requests\\Quote\\ListRequest::rules<\/a>"],[0,6,"<a href=\"Quote\/ListRequest.php.html#34\">App\\Http\\Requests\\Quote\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Schedule\/GetTechnicianListRequest.php.html#17\">App\\Http\\Requests\\Schedule\\GetTechnicianListRequest::rules<\/a>"],[0,1,"<a href=\"Schedule\/ReScheduleAppointmentRequest.php.html#21\">App\\Http\\Requests\\Schedule\\ReScheduleAppointmentRequest::rules<\/a>"],[0,1,"<a href=\"Schedule\/StoreAppointmentRequest.php.html#17\">App\\Http\\Requests\\Schedule\\StoreAppointmentRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/AccessMethodUpdateRequest.php.html#16\">App\\Http\\Requests\\ServiceRequest\\AccessMethodUpdateRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/DescriptionUpdateRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\DescriptionUpdateRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/FilterValuesRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\FilterValuesRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/GroupViewRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\GroupViewRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/ListRequest.php.html#13\">App\\Http\\Requests\\ServiceRequest\\ListRequest::authorize<\/a>"],[0,1,"<a href=\"ServiceRequest\/ListRequest.php.html#23\">App\\Http\\Requests\\ServiceRequest\\ListRequest::rules<\/a>"],[0,6,"<a href=\"ServiceRequest\/ListRequest.php.html#43\">App\\Http\\Requests\\ServiceRequest\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"ServiceRequest\/Media\/MediaUploadRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\Media\\MediaUploadRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/Media\/ThumbnailUploadRequest.php.html#15\">App\\Http\\Requests\\ServiceRequest\\Media\\ThumbnailUploadRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/Note\/CreateRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\Note\\CreateRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/Note\/UpdateRequest.php.html#14\">App\\Http\\Requests\\ServiceRequest\\Note\\UpdateRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/PriorityUpdateRequest.php.html#16\">App\\Http\\Requests\\ServiceRequest\\PriorityUpdateRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/PropertyAddressUpdateRequest.php.html#17\">App\\Http\\Requests\\ServiceRequest\\PropertyAddressUpdateRequest::rules<\/a>"],[0,4,"<a href=\"ServiceRequest\/ResidentAvailabilityRequest.php.html#22\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/ResidentAvailabilityRequest.php.html#59\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::attributes<\/a>"],[0,2,"<a href=\"ServiceRequest\/ResidentAvailabilityRequest.php.html#72\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::after<\/a>"],[0,7,"<a href=\"ServiceRequest\/ResidentAvailabilityRequest.php.html#94\">App\\Http\\Requests\\ServiceRequest\\ResidentAvailabilityRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"ServiceRequest\/ResidentUpdateRequest.php.html#17\">App\\Http\\Requests\\ServiceRequest\\ResidentUpdateRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/StoreRequest.php.html#20\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/StoreRequest.php.html#81\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::attributes<\/a>"],[0,1,"<a href=\"ServiceRequest\/StoreRequest.php.html#128\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::messages<\/a>"],[0,4,"<a href=\"ServiceRequest\/StoreRequest.php.html#152\">App\\Http\\Requests\\ServiceRequest\\StoreRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"ServiceRequest\/StoreWorkOrderRequest.php.html#20\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::rules<\/a>"],[0,1,"<a href=\"ServiceRequest\/StoreWorkOrderRequest.php.html#51\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::attributes<\/a>"],[0,1,"<a href=\"ServiceRequest\/StoreWorkOrderRequest.php.html#87\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::messages<\/a>"],[0,3,"<a href=\"ServiceRequest\/StoreWorkOrderRequest.php.html#112\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"ServiceRequest\/StoreWorkOrderRequest.php.html#138\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::propertyRules<\/a>"],[0,1,"<a href=\"ServiceRequest\/StoreWorkOrderRequest.php.html#169\">App\\Http\\Requests\\ServiceRequest\\StoreWorkOrderRequest::residentRules<\/a>"],[0,1,"<a href=\"ServiceRequest\/UpdateAvailabilityRequest.php.html#19\">App\\Http\\Requests\\ServiceRequest\\UpdateAvailabilityRequest::rules<\/a>"],[0,2,"<a href=\"ServiceRequest\/UpdateAvailabilityRequest.php.html#43\">App\\Http\\Requests\\ServiceRequest\\UpdateAvailabilityRequest::after<\/a>"],[0,1,"<a href=\"Tag\/StoreRequest.php.html#14\">App\\Http\\Requests\\Tag\\StoreRequest::rules<\/a>"],[0,1,"<a href=\"Tag\/StoreRequest.php.html#31\">App\\Http\\Requests\\Tag\\StoreRequest::messages<\/a>"],[0,1,"<a href=\"Tag\/UpdateRequest.php.html#14\">App\\Http\\Requests\\Tag\\UpdateRequest::rules<\/a>"],[0,1,"<a href=\"Tag\/UpdateRequest.php.html#33\">App\\Http\\Requests\\Tag\\UpdateRequest::messages<\/a>"],[0,1,"<a href=\"Technician\/AvailabilityDatesRequest.php.html#14\">App\\Http\\Requests\\Technician\\AvailabilityDatesRequest::rules<\/a>"],[0,1,"<a href=\"Technician\/BlockOutUpdateRequest.php.html#21\">App\\Http\\Requests\\Technician\\BlockOutUpdateRequest::rules<\/a>"],[0,7,"<a href=\"Technician\/BlockOutUpdateRequest.php.html#41\">App\\Http\\Requests\\Technician\\BlockOutUpdateRequest::after<\/a>"],[0,1,"<a href=\"Technician\/ScheduleWorkOrderRequest.php.html#15\">App\\Http\\Requests\\Technician\\ScheduleWorkOrderRequest::rules<\/a>"],[0,1,"<a href=\"Technician\/SkillsUpdateRequest.php.html#19\">App\\Http\\Requests\\Technician\\SkillsUpdateRequest::rules<\/a>"],[0,7,"<a href=\"Technician\/SkillsUpdateRequest.php.html#35\">App\\Http\\Requests\\Technician\\SkillsUpdateRequest::after<\/a>"],[0,1,"<a href=\"Technician\/StoreBlockOutRequest.php.html#21\">App\\Http\\Requests\\Technician\\StoreBlockOutRequest::rules<\/a>"],[0,4,"<a href=\"Technician\/StoreBlockOutRequest.php.html#41\">App\\Http\\Requests\\Technician\\StoreBlockOutRequest::after<\/a>"],[0,1,"<a href=\"Technician\/WorkingHoursUpdateRequest.php.html#17\">App\\Http\\Requests\\Technician\\WorkingHoursUpdateRequest::rules<\/a>"],[0,1,"<a href=\"Technician\/WorkingHoursUpdateRequest.php.html#33\">App\\Http\\Requests\\Technician\\WorkingHoursUpdateRequest::messages<\/a>"],[0,12,"<a href=\"Technician\/WorkingHoursUpdateRequest.php.html#52\">App\\Http\\Requests\\Technician\\WorkingHoursUpdateRequest::after<\/a>"],[0,1,"<a href=\"User\/FilterValuesRequest.php.html#14\">App\\Http\\Requests\\User\\FilterValuesRequest::rules<\/a>"],[0,1,"<a href=\"User\/GroupViewRequest.php.html#14\">App\\Http\\Requests\\User\\GroupViewRequest::rules<\/a>"],[0,1,"<a href=\"User\/ListRequest.php.html#14\">App\\Http\\Requests\\User\\ListRequest::rules<\/a>"],[0,6,"<a href=\"User\/ListRequest.php.html#33\">App\\Http\\Requests\\User\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"User\/StoreRequest.php.html#18\">App\\Http\\Requests\\User\\StoreRequest::rules<\/a>"],[0,1,"<a href=\"User\/StoreVendorUserRequest.php.html#16\">App\\Http\\Requests\\User\\StoreVendorUserRequest::rules<\/a>"],[0,1,"<a href=\"User\/UpdateRequest.php.html#24\">App\\Http\\Requests\\User\\UpdateRequest::rules<\/a>"],[0,4,"<a href=\"User\/UpdateRequest.php.html#79\">App\\Http\\Requests\\User\\UpdateRequest::after<\/a>"],[0,1,"<a href=\"Vendor\/ListRequest.php.html#14\">App\\Http\\Requests\\Vendor\\ListRequest::rules<\/a>"],[0,5,"<a href=\"Vendor\/ListRequest.php.html#34\">App\\Http\\Requests\\Vendor\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"Vendor\/StoreRequest.php.html#16\">App\\Http\\Requests\\Vendor\\StoreRequest::rules<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnBoardingSetServiceAreasRequest.php.html#9\">App\\Http\\Requests\\Vendor\\VendorOnBoardingSetServiceAreasRequest::authorize<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnBoardingSetServiceAreasRequest.php.html#19\">App\\Http\\Requests\\Vendor\\VendorOnBoardingSetServiceAreasRequest::rules<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingGenerateSignedUrlRequest.php.html#9\">App\\Http\\Requests\\Vendor\\VendorOnboardingGenerateSignedUrlRequest::authorize<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingGenerateSignedUrlRequest.php.html#19\">App\\Http\\Requests\\Vendor\\VendorOnboardingGenerateSignedUrlRequest::rules<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingInitialRequest.php.html#11\">App\\Http\\Requests\\Vendor\\VendorOnboardingInitialRequest::authorize<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingInitialRequest.php.html#21\">App\\Http\\Requests\\Vendor\\VendorOnboardingInitialRequest::rules<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingSetBasicInfoRequest.php.html#12\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetBasicInfoRequest::authorize<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingSetBasicInfoRequest.php.html#22\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetBasicInfoRequest::rules<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingSetServicesRequest.php.html#9\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetServicesRequest::authorize<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingSetServicesRequest.php.html#19\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetServicesRequest::rules<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingSetStatusRequest.php.html#9\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetStatusRequest::authorize<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingSetStatusRequest.php.html#19\">App\\Http\\Requests\\Vendor\\VendorOnboardingSetStatusRequest::rules<\/a>"],[0,1,"<a href=\"View\/ConfigurationRequest.php.html#14\">App\\Http\\Requests\\View\\ConfigurationRequest::rules<\/a>"],[0,1,"<a href=\"View\/CountRequest.php.html#14\">App\\Http\\Requests\\View\\CountRequest::rules<\/a>"],[0,1,"<a href=\"View\/DuplicateRequest.php.html#19\">App\\Http\\Requests\\View\\DuplicateRequest::rules<\/a>"],[0,3,"<a href=\"View\/DuplicateRequest.php.html#32\">App\\Http\\Requests\\View\\DuplicateRequest::after<\/a>"],[0,1,"<a href=\"View\/PinRequest.php.html#15\">App\\Http\\Requests\\View\\PinRequest::rules<\/a>"],[0,1,"<a href=\"View\/RenameRequest.php.html#18\">App\\Http\\Requests\\View\\RenameRequest::rules<\/a>"],[0,3,"<a href=\"View\/RenameRequest.php.html#31\">App\\Http\\Requests\\View\\RenameRequest::after<\/a>"],[0,1,"<a href=\"View\/StoreRequest.php.html#20\">App\\Http\\Requests\\View\\StoreRequest::rules<\/a>"],[0,3,"<a href=\"View\/StoreRequest.php.html#43\">App\\Http\\Requests\\View\\StoreRequest::after<\/a>"],[0,1,"<a href=\"View\/UpdateRequest.php.html#15\">App\\Http\\Requests\\View\\UpdateRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/AccessMethodUpdateRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\AccessMethodUpdateRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/ApproveQuoteRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\ApproveQuoteRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/BookmarkRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\BookmarkRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/CancelRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\CancelRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/CloseRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\CloseRequest::rules<\/a>"],[0,3,"<a href=\"WorkOrder\/CompleteRequest.php.html#19\">App\\Http\\Requests\\WorkOrder\\CompleteRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/CreateQuoteRequest.php.html#21\">App\\Http\\Requests\\WorkOrder\\CreateQuoteRequest::rules<\/a>"],[0,13,"<a href=\"WorkOrder\/CreateQuoteRequest.php.html#50\">App\\Http\\Requests\\WorkOrder\\CreateQuoteRequest::after<\/a>"],[0,1,"<a href=\"WorkOrder\/DescriptionUpdateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\DescriptionUpdateRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/DueDateUpdateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\DueDateUpdateRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/FilterValuesRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\FilterValuesRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/GroupViewRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\GroupViewRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/ListRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\ListRequest::rules<\/a>"],[0,6,"<a href=\"WorkOrder\/ListRequest.php.html#34\">App\\Http\\Requests\\WorkOrder\\ListRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"WorkOrder\/Media\/MediaUploadRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Media\\MediaUploadRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/Media\/ThumbnailUploadRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\Media\\ThumbnailUploadRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/CreateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Note\\CreateRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/UpdateRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\Note\\UpdateRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/PauseRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\PauseRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/PriorityUpdateRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\PriorityUpdateRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/ProblemCategoryCreateRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryCreateRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/ProblemCategoryDeleteRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryDeleteRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/ProblemCategoryUpdateRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\ProblemCategoryUpdateRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/PropertyAddressUpdateRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\PropertyAddressUpdateRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/Quote\/TaskCreateRequest.php.html#21\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskCreateRequest::rules<\/a>"],[0,12,"<a href=\"WorkOrder\/Quote\/TaskCreateRequest.php.html#50\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskCreateRequest::after<\/a>"],[0,1,"<a href=\"WorkOrder\/Quote\/TaskUpdateRequest.php.html#22\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskUpdateRequest::rules<\/a>"],[0,12,"<a href=\"WorkOrder\/Quote\/TaskUpdateRequest.php.html#52\">App\\Http\\Requests\\WorkOrder\\Quote\\TaskUpdateRequest::after<\/a>"],[0,2,"<a href=\"WorkOrder\/ReadyToInvoiceRequest.php.html#18\">App\\Http\\Requests\\WorkOrder\\ReadyToInvoiceRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentUpdateRequest.php.html#17\">App\\Http\\Requests\\WorkOrder\\ResidentUpdateRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/SendToVendorRequest.php.html#19\">App\\Http\\Requests\\WorkOrder\\SendToVendorRequest::rules<\/a>"],[0,6,"<a href=\"WorkOrder\/SendToVendorRequest.php.html#40\">App\\Http\\Requests\\WorkOrder\\SendToVendorRequest::after<\/a>"],[0,1,"<a href=\"WorkOrder\/StoreRequest.php.html#18\">App\\Http\\Requests\\WorkOrder\\StoreRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/StoreRequest.php.html#81\">App\\Http\\Requests\\WorkOrder\\StoreRequest::attributes<\/a>"],[0,1,"<a href=\"WorkOrder\/StoreRequest.php.html#123\">App\\Http\\Requests\\WorkOrder\\StoreRequest::messages<\/a>"],[0,3,"<a href=\"WorkOrder\/StoreRequest.php.html#149\">App\\Http\\Requests\\WorkOrder\\StoreRequest::prepareForValidation<\/a>"],[0,1,"<a href=\"WorkOrder\/TagRequest.php.html#16\">App\\Http\\Requests\\WorkOrder\\TagRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/UpdateNteAmountRequest.php.html#14\">App\\Http\\Requests\\WorkOrder\\UpdateNteAmountRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/UpdateNteAmountRequest.php.html#31\">App\\Http\\Requests\\WorkOrder\\UpdateNteAmountRequest::messages<\/a>"],[0,1,"<a href=\"WorkOrder\/UpdateTripDetailsRequest.php.html#20\">App\\Http\\Requests\\WorkOrder\\UpdateTripDetailsRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrder\/UpdateTripRequest.php.html#21\">App\\Http\\Requests\\WorkOrder\\UpdateTripRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/DeclineIssueRequest.php.html#14\">App\\Http\\Requests\\WorkOrderIssue\\DeclineIssueRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/MarkAsDoneIssueRequest.php.html#19\">App\\Http\\Requests\\WorkOrderIssue\\MarkAsDoneIssueRequest::rules<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/WorkOrderIssueUpdateRequest.php.html#20\">App\\Http\\Requests\\WorkOrderIssue\\WorkOrderIssueUpdateRequest::rules<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

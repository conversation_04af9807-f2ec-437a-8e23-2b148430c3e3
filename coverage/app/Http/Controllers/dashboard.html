<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Http/Controllers</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="index.html">Controllers</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AppVersionController.php.html#14">App\Http\Controllers\AppVersionController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/SSOController.php.html#22">App\Http\Controllers\Auth\SSOController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationController.php.html#16">App\Http\Controllers\DatabaseNotificationController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/HealthCheckController.php.html#14">App\Http\Controllers\Developer\HealthCheckController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/LogViewerController.php.html#19">App\Http\Controllers\Developer\LogViewerController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceController.php.html#21">App\Http\Controllers\InvoiceController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#39">App\Http\Controllers\LookupController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#28">App\Http\Controllers\LulaWebhookController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MediaController.php.html#16">App\Http\Controllers\MediaController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationController.php.html#20">App\Http\Controllers\OrganizationController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProfileController.php.html#9">App\Http\Controllers\ProfileController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicAccessController.php.html#17">App\Http\Controllers\PublicAccessController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEventsController.php.html#8">App\Http\Controllers\PublicApiWorkOrderWebhookEventsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteController.php.html#49">App\Http\Controllers\QuoteController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityController.php.html#19">App\Http\Controllers\ResidentAvailabilityController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#25">App\Http\Controllers\RoleController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivityLogController.php.html#24">App\Http\Controllers\ServiceRequestActivityLogController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeController.php.html#22">App\Http\Controllers\ServiceRequestAssigneeController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#89">App\Http\Controllers\ServiceRequestController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaController.php.html#39">App\Http\Controllers\ServiceRequestMediaController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteController.php.html#31">App\Http\Controllers\ServiceRequestNoteController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagController.php.html#24">App\Http\Controllers\TagController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#37">App\Http\Controllers\TechnicianAppointmentsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianController.php.html#26">App\Http\Controllers\TechnicianController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#40">App\Http\Controllers\Vendor\VendorOnboardingController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingLookupController.php.html#23">App\Http\Controllers\Vendor\VendorOnboardingLookupController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#27">App\Http\Controllers\Vendor\WorkOrderController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#43">App\Http\Controllers\ViewController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCallsController.php.html#9">App\Http\Controllers\WebhookCallsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookController.php.html#30">App\Http\Controllers\WebhookController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderIssueController.php.html#17">App\Http\Controllers\WorkOrder\WorkOrderIssueController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivityLogController.php.html#22">App\Http\Controllers\WorkOrderActivityLogController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeController.php.html#22">App\Http\Controllers\WorkOrderAssigneeController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#115">App\Http\Controllers\WorkOrderController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#43">App\Http\Controllers\WorkOrderMediaController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNoteController.php.html#27">App\Http\Controllers\WorkOrderNoteController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PDFController.php.html#30">App\Http\Controllers\PDFController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#31">App\Http\Controllers\SchedulingController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#61">App\Http\Controllers\UserController</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#56">App\Http\Controllers\Auth\RegisterController</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="VendorController.php.html#33">App\Http\Controllers\VendorController</a></td><td class="text-right">2%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderController.php.html#115">App\Http\Controllers\WorkOrderController</a></td><td class="text-right">34782</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#89">App\Http\Controllers\ServiceRequestController</a></td><td class="text-right">12656</td></tr>
       <tr><td><a href="UserController.php.html#61">App\Http\Controllers\UserController</a></td><td class="text-right">7089</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#56">App\Http\Controllers\Auth\RegisterController</a></td><td class="text-right">6075</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#40">App\Http\Controllers\Vendor\VendorOnboardingController</a></td><td class="text-right">3660</td></tr>
       <tr><td><a href="ViewController.php.html#43">App\Http\Controllers\ViewController</a></td><td class="text-right">2862</td></tr>
       <tr><td><a href="QuoteController.php.html#49">App\Http\Controllers\QuoteController</a></td><td class="text-right">2756</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#43">App\Http\Controllers\WorkOrderMediaController</a></td><td class="text-right">2450</td></tr>
       <tr><td><a href="ServiceRequestMediaController.php.html#39">App\Http\Controllers\ServiceRequestMediaController</a></td><td class="text-right">1482</td></tr>
       <tr><td><a href="PDFController.php.html#30">App\Http\Controllers\PDFController</a></td><td class="text-right">1237</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#37">App\Http\Controllers\TechnicianAppointmentsController</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="WebhookController.php.html#30">App\Http\Controllers\WebhookController</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="TechnicianController.php.html#26">App\Http\Controllers\TechnicianController</a></td><td class="text-right">930</td></tr>
       <tr><td><a href="LookupController.php.html#39">App\Http\Controllers\LookupController</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="SchedulingController.php.html#31">App\Http\Controllers\SchedulingController</a></td><td class="text-right">684</td></tr>
       <tr><td><a href="RoleController.php.html#25">App\Http\Controllers\RoleController</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="WorkOrderNoteController.php.html#27">App\Http\Controllers\WorkOrderNoteController</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="VendorController.php.html#33">App\Http\Controllers\VendorController</a></td><td class="text-right">466</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#28">App\Http\Controllers\LulaWebhookController</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="ServiceRequestNoteController.php.html#31">App\Http\Controllers\ServiceRequestNoteController</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="TagController.php.html#24">App\Http\Controllers\TagController</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Developer/HealthCheckController.php.html#14">App\Http\Controllers\Developer\HealthCheckController</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ResidentAvailabilityController.php.html#19">App\Http\Controllers\ResidentAvailabilityController</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="OrganizationController.php.html#20">App\Http\Controllers\OrganizationController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ServiceRequestAssigneeController.php.html#22">App\Http\Controllers\ServiceRequestAssigneeController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#27">App\Http\Controllers\Vendor\WorkOrderController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="WorkOrderAssigneeController.php.html#22">App\Http\Controllers\WorkOrderAssigneeController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="PublicAccessController.php.html#17">App\Http\Controllers\PublicAccessController</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="InvoiceController.php.html#21">App\Http\Controllers\InvoiceController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ServiceRequestActivityLogController.php.html#24">App\Http\Controllers\ServiceRequestActivityLogController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Auth/SSOController.php.html#22">App\Http\Controllers\Auth\SSOController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingLookupController.php.html#23">App\Http\Controllers\Vendor\VendorOnboardingLookupController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="WorkOrderActivityLogController.php.html#22">App\Http\Controllers\WorkOrderActivityLogController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="AppVersionController.php.html#14">App\Http\Controllers\AppVersionController</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="DatabaseNotificationController.php.html#16">App\Http\Controllers\DatabaseNotificationController</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="MediaController.php.html#16">App\Http\Controllers\MediaController</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderIssueController.php.html#17">App\Http\Controllers\WorkOrder\WorkOrderIssueController</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AppVersionController.php.html#19"><abbr title="App\Http\Controllers\AppVersionController::checkLatestAppVersion">checkLatestAppVersion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#87"><abbr title="App\Http\Controllers\Auth\RegisterController::forgotPassword">forgotPassword</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#128"><abbr title="App\Http\Controllers\Auth\RegisterController::resetPassword">resetPassword</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#145"><abbr title="App\Http\Controllers\Auth\RegisterController::verify">verify</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#167"><abbr title="App\Http\Controllers\Auth\RegisterController::searchAddress">searchAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#197"><abbr title="App\Http\Controllers\Auth\RegisterController::getVendorOnboardingInvite">getVendorOnboardingInvite</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#333"><abbr title="App\Http\Controllers\Auth\RegisterController::setVendorPassword">setVendorPassword</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#402"><abbr title="App\Http\Controllers\Auth\RegisterController::signup">signup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#510"><abbr title="App\Http\Controllers\Auth\RegisterController::createOrganization">createOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#536"><abbr title="App\Http\Controllers\Auth\RegisterController::setupUserPool">setupUserPool</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#642"><abbr title="App\Http\Controllers\Auth\RegisterController::findLoginConfig">findLoginConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#709"><abbr title="App\Http\Controllers\Auth\RegisterController::findProviderLoginConfig">findProviderLoginConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#726"><abbr title="App\Http\Controllers\Auth\RegisterController::authenticateClientCredential">authenticateClientCredential</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#786"><abbr title="App\Http\Controllers\Auth\RegisterController::createUserPoolClient">createUserPoolClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#823"><abbr title="App\Http\Controllers\Auth\RegisterController::createUserPool">createUserPool</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#890"><abbr title="App\Http\Controllers\Auth\RegisterController::syncUserRolePermissions">syncUserRolePermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#960"><abbr title="App\Http\Controllers\Auth\RegisterController::sendEmail">sendEmail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#975"><abbr title="App\Http\Controllers\Auth\RegisterController::validateRequest">validateRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#988"><abbr title="App\Http\Controllers\Auth\RegisterController::validateSignedUrl">validateSignedUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#1013"><abbr title="App\Http\Controllers\Auth\RegisterController::validatePassword">validatePassword</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#1041"><abbr title="App\Http\Controllers\Auth\RegisterController::processPasswordReset">processPasswordReset</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/SSOController.php.html#26"><abbr title="App\Http\Controllers\Auth\SSOController::validateUser">validateUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Auth/SSOController.php.html#76"><abbr title="App\Http\Controllers\Auth\SSOController::getCognitoUserDetails">getCognitoUserDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationController.php.html#18"><abbr title="App\Http\Controllers\DatabaseNotificationController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DatabaseNotificationController.php.html#60"><abbr title="App\Http\Controllers\DatabaseNotificationController::clearedNotifications">clearedNotifications</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/HealthCheckController.php.html#26"><abbr title="App\Http\Controllers\Developer\HealthCheckController::basicCheck">basicCheck</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/HealthCheckController.php.html#38"><abbr title="App\Http\Controllers\Developer\HealthCheckController::utilitiesCheck">utilitiesCheck</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/HealthCheckController.php.html#67"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkDatabaseConnection">checkDatabaseConnection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/HealthCheckController.php.html#88"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkCacheConnection">checkCacheConnection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/HealthCheckController.php.html#110"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkLogging">checkLogging</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/LogViewerController.php.html#24"><abbr title="App\Http\Controllers\Developer\LogViewerController::requestLogs">requestLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/LogViewerController.php.html#34"><abbr title="App\Http\Controllers\Developer\LogViewerController::requestLogShow">requestLogShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/LogViewerController.php.html#42"><abbr title="App\Http\Controllers\Developer\LogViewerController::incomingWebhookLogs">incomingWebhookLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/LogViewerController.php.html#50"><abbr title="App\Http\Controllers\Developer\LogViewerController::incomingWebhookLogShow">incomingWebhookLogShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/LogViewerController.php.html#58"><abbr title="App\Http\Controllers\Developer\LogViewerController::outgoingWebhookLogs">outgoingWebhookLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/LogViewerController.php.html#66"><abbr title="App\Http\Controllers\Developer\LogViewerController::outgoingWebhookLogShow">outgoingWebhookLogShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/LogViewerController.php.html#76"><abbr title="App\Http\Controllers\Developer\LogViewerController::developerAlerts">developerAlerts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/LogViewerController.php.html#86"><abbr title="App\Http\Controllers\Developer\LogViewerController::developerAlertShow">developerAlertShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/LogViewerController.php.html#93"><abbr title="App\Http\Controllers\Developer\LogViewerController::vendorPublicApiLogs">vendorPublicApiLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Developer/LogViewerController.php.html#98"><abbr title="App\Http\Controllers\Developer\LogViewerController::vendorPublicApiLogShow">vendorPublicApiLogShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceController.php.html#23"><abbr title="App\Http\Controllers\InvoiceController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceController.php.html#41"><abbr title="App\Http\Controllers\InvoiceController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceController.php.html#92"><abbr title="App\Http\Controllers\InvoiceController::workOrderInvoiceSummary">workOrderInvoiceSummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#44"><abbr title="App\Http\Controllers\LookupController::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#85"><abbr title="App\Http\Controllers\LookupController::getFieldAppFilters">getFieldAppFilters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#168"><abbr title="App\Http\Controllers\LookupController::getPriorities">getPriorities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#173"><abbr title="App\Http\Controllers\LookupController::getPropertyAccessMethods">getPropertyAccessMethods</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#178"><abbr title="App\Http\Controllers\LookupController::getStates">getStates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#189"><abbr title="App\Http\Controllers\LookupController::getCountry">getCountry</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#200"><abbr title="App\Http\Controllers\LookupController::getProblemCategory">getProblemCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#225"><abbr title="App\Http\Controllers\LookupController::getTechnicians">getTechnicians</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#246"><abbr title="App\Http\Controllers\LookupController::getAssignees">getAssignees</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#260"><abbr title="App\Http\Controllers\LookupController::getQuantityTypes">getQuantityTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#265"><abbr title="App\Http\Controllers\LookupController::endTripeOptions">endTripeOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#276"><abbr title="App\Http\Controllers\LookupController::expectedDurations">expectedDurations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LookupController.php.html#312"><abbr title="App\Http\Controllers\LookupController::getOnboardingStatuses">getOnboardingStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#34"><abbr title="App\Http\Controllers\LulaWebhookController::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#146"><abbr title="App\Http\Controllers\LulaWebhookController::invalidAction">invalidAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#153"><abbr title="App\Http\Controllers\LulaWebhookController::validateScheduleInProgressPayload">validateScheduleInProgressPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#160"><abbr title="App\Http\Controllers\LulaWebhookController::validateSchedulePayload">validateSchedulePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#171"><abbr title="App\Http\Controllers\LulaWebhookController::validateWorkInProgressPayload">validateWorkInProgressPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#178"><abbr title="App\Http\Controllers\LulaWebhookController::validateWorkPausedPayload">validateWorkPausedPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#186"><abbr title="App\Http\Controllers\LulaWebhookController::validateQualityCheckPayload">validateQualityCheckPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#193"><abbr title="App\Http\Controllers\LulaWebhookController::validateWorkCompletedPayload">validateWorkCompletedPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#203"><abbr title="App\Http\Controllers\LulaWebhookController::validateWorkCancelPayload">validateWorkCancelPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#211"><abbr title="App\Http\Controllers\LulaWebhookController::validateNoteAddedPayload">validateNoteAddedPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#221"><abbr title="App\Http\Controllers\LulaWebhookController::validateInvoicePayload">validateInvoicePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MediaController.php.html#18"><abbr title="App\Http\Controllers\MediaController::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationController.php.html#24"><abbr title="App\Http\Controllers\OrganizationController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationController.php.html#32"><abbr title="App\Http\Controllers\OrganizationController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationController.php.html#94"><abbr title="App\Http\Controllers\OrganizationController::getTemplates">getTemplates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrganizationController.php.html#115"><abbr title="App\Http\Controllers\OrganizationController::updateTemplate">updateTemplate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PDFController.php.html#38"><abbr title="App\Http\Controllers\PDFController::viewQuote">viewQuote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PDFController.php.html#193"><abbr title="App\Http\Controllers\PDFController::viewInvoice">viewInvoice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProfileController.php.html#14"><abbr title="App\Http\Controllers\ProfileController::profile">profile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicAccessController.php.html#19"><abbr title="App\Http\Controllers\PublicAccessController::getMedia">getMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicAccessController.php.html#82"><abbr title="App\Http\Controllers\PublicAccessController::getSrMedia">getSrMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEventsController.php.html#13"><abbr title="App\Http\Controllers\PublicApiWorkOrderWebhookEventsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEventsController.php.html#21"><abbr title="App\Http\Controllers\PublicApiWorkOrderWebhookEventsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteController.php.html#60"><abbr title="App\Http\Controllers\QuoteController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteController.php.html#250"><abbr title="App\Http\Controllers\QuoteController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteController.php.html#339"><abbr title="App\Http\Controllers\QuoteController::getGroupView">getGroupView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteController.php.html#399"><abbr title="App\Http\Controllers\QuoteController::workOrderNumberBasedGroupData">workOrderNumberBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteController.php.html#462"><abbr title="App\Http\Controllers\QuoteController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteController.php.html#525"><abbr title="App\Http\Controllers\QuoteController::categoryBasedGroupData">categoryBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteController.php.html#603"><abbr title="App\Http\Controllers\QuoteController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteController.php.html#678"><abbr title="App\Http\Controllers\QuoteController::submittedBasedGroupData">submittedBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteController.php.html#751"><abbr title="App\Http\Controllers\QuoteController::tagBasedGroupData">tagBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityController.php.html#21"><abbr title="App\Http\Controllers\ResidentAvailabilityController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityController.php.html#101"><abbr title="App\Http\Controllers\ResidentAvailabilityController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#32"><abbr title="App\Http\Controllers\RoleController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#51"><abbr title="App\Http\Controllers\RoleController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#122"><abbr title="App\Http\Controllers\RoleController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#136"><abbr title="App\Http\Controllers\RoleController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#212"><abbr title="App\Http\Controllers\RoleController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#232"><abbr title="App\Http\Controllers\RoleController::listPermissions">listPermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleController.php.html#259"><abbr title="App\Http\Controllers\RoleController::hasPermissionsFromInvalidFeatures">hasPermissionsFromInvalidFeatures</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#38"><abbr title="App\Http\Controllers\SchedulingController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#43"><abbr title="App\Http\Controllers\SchedulingController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#51"><abbr title="App\Http\Controllers\SchedulingController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#59"><abbr title="App\Http\Controllers\SchedulingController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#67"><abbr title="App\Http\Controllers\SchedulingController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#75"><abbr title="App\Http\Controllers\SchedulingController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#80"><abbr title="App\Http\Controllers\SchedulingController::getContext">getContext</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#126"><abbr title="App\Http\Controllers\SchedulingController::getVendors">getVendors</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#176"><abbr title="App\Http\Controllers\SchedulingController::getTechnicianList">getTechnicianList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#204"><abbr title="App\Http\Controllers\SchedulingController::getTechnicianSchedules">getTechnicianSchedules</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SchedulingController.php.html#234"><abbr title="App\Http\Controllers\SchedulingController::getVendorAvailability">getVendorAvailability</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivityLogController.php.html#29"><abbr title="App\Http\Controllers\ServiceRequestActivityLogController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestActivityLogController.php.html#132"><abbr title="App\Http\Controllers\ServiceRequestActivityLogController::paginate">paginate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeController.php.html#25"><abbr title="App\Http\Controllers\ServiceRequestAssigneeController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssigneeController.php.html#78"><abbr title="App\Http\Controllers\ServiceRequestAssigneeController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#98"><abbr title="App\Http\Controllers\ServiceRequestController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#200"><abbr title="App\Http\Controllers\ServiceRequestController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#259"><abbr title="App\Http\Controllers\ServiceRequestController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#595"><abbr title="App\Http\Controllers\ServiceRequestController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#642"><abbr title="App\Http\Controllers\ServiceRequestController::markAdminAvailabilityViewed">markAdminAvailabilityViewed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#661"><abbr title="App\Http\Controllers\ServiceRequestController::getGroupView">getGroupView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#710"><abbr title="App\Http\Controllers\ServiceRequestController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#777"><abbr title="App\Http\Controllers\ServiceRequestController::updateDescription">updateDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#853"><abbr title="App\Http\Controllers\ServiceRequestController::updateAccessMethod">updateAccessMethod</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#878"><abbr title="App\Http\Controllers\ServiceRequestController::createWorkOrder">createWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#986"><abbr title="App\Http\Controllers\ServiceRequestController::triggerIssueUpdatedEvents">triggerIssueUpdatedEvents</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#995"><abbr title="App\Http\Controllers\ServiceRequestController::markAsComplete">markAsComplete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#1113"><abbr title="App\Http\Controllers\ServiceRequestController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#1156"><abbr title="App\Http\Controllers\ServiceRequestController::importedFromBasedGroupData">importedFromBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#1203"><abbr title="App\Http\Controllers\ServiceRequestController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#1252"><abbr title="App\Http\Controllers\ServiceRequestController::needToCreateServiceRequestDescription">needToCreateServiceRequestDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaController.php.html#44"><abbr title="App\Http\Controllers\ServiceRequestMediaController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaController.php.html#177"><abbr title="App\Http\Controllers\ServiceRequestMediaController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaController.php.html#218"><abbr title="App\Http\Controllers\ServiceRequestMediaController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestMediaController.php.html#258"><abbr title="App\Http\Controllers\ServiceRequestMediaController::uploadOriginalMedia">uploadOriginalMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteController.php.html#38"><abbr title="App\Http\Controllers\ServiceRequestNoteController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteController.php.html#87"><abbr title="App\Http\Controllers\ServiceRequestNoteController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteController.php.html#139"><abbr title="App\Http\Controllers\ServiceRequestNoteController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestNoteController.php.html#187"><abbr title="App\Http\Controllers\ServiceRequestNoteController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagController.php.html#31"><abbr title="App\Http\Controllers\TagController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagController.php.html#48"><abbr title="App\Http\Controllers\TagController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagController.php.html#90"><abbr title="App\Http\Controllers\TagController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagController.php.html#103"><abbr title="App\Http\Controllers\TagController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagController.php.html#143"><abbr title="App\Http\Controllers\TagController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#44"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#142"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#225"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#299"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#318"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::viewTechnicianCalendar">viewTechnicianCalendar</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#387"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::getAvailabilityDates">getAvailabilityDates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianController.php.html#31"><abbr title="App\Http\Controllers\TechnicianController::updateWorkingHours">updateWorkingHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianController.php.html#97"><abbr title="App\Http\Controllers\TechnicianController::importWorkingHours">importWorkingHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianController.php.html#130"><abbr title="App\Http\Controllers\TechnicianController::updateTechnicianSkills">updateTechnicianSkills</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TechnicianController.php.html#272"><abbr title="App\Http\Controllers\TechnicianController::importTechnicianSkills">importTechnicianSkills</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#84"><abbr title="App\Http\Controllers\UserController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#129"><abbr title="App\Http\Controllers\UserController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#265"><abbr title="App\Http\Controllers\UserController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#281"><abbr title="App\Http\Controllers\UserController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#395"><abbr title="App\Http\Controllers\UserController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#450"><abbr title="App\Http\Controllers\UserController::filters">filters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#492"><abbr title="App\Http\Controllers\UserController::updateCognitoUserDetails">updateCognitoUserDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#551"><abbr title="App\Http\Controllers\UserController::updateNotificationSubscription">updateNotificationSubscription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#580"><abbr title="App\Http\Controllers\UserController::updateUserStatus">updateUserStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#661"><abbr title="App\Http\Controllers\UserController::resetUserPassword">resetUserPassword</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#700"><abbr title="App\Http\Controllers\UserController::getGroupView">getGroupView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#748"><abbr title="App\Http\Controllers\UserController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#801"><abbr title="App\Http\Controllers\UserController::roleBasedGroupData">roleBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#50"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::sendVendorOnboardingLink">sendVendorOnboardingLink</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#141"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setBasicInfo">setBasicInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#222"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::getVendorOnboarding">getVendorOnboarding</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#257"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::getStatus">getStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#331"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setStatus">setStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#407"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setServices">setServices</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#467"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setServiceAreas">setServiceAreas</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#535"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::processServices">processServices</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#585"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::minutesToDaysSimple">minutesToDaysSimple</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingLookupController.php.html#25"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLookups">getLookups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingLookupController.php.html#50"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getProblemCategory">getProblemCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingLookupController.php.html#74"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLocationApiKey">getLocationApiKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingLookupController.php.html#87"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getOnboardingStatuses">getOnboardingStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingLookupController.php.html#96"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getStates">getStates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#35"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#77"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#101"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getWorkOrderRelations">getWorkOrderRelations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#191"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getWorkOrderSelectFields">getWorkOrderSelectFields</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#227"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getTabTotalCounts">getTabTotalCounts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#261"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getSkippedMediaTypes">getSkippedMediaTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#278"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getIssueMediaTypes">getIssueMediaTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#288"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getShowEagerLoadRelations">getShowEagerLoadRelations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorController.php.html#54"><abbr title="App\Http\Controllers\VendorController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorController.php.html#99"><abbr title="App\Http\Controllers\VendorController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorController.php.html#234"><abbr title="App\Http\Controllers\VendorController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorController.php.html#276"><abbr title="App\Http\Controllers\VendorController::storeVendor">storeVendor</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#50"><abbr title="App\Http\Controllers\ViewController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#117"><abbr title="App\Http\Controllers\ViewController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#168"><abbr title="App\Http\Controllers\ViewController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#196"><abbr title="App\Http\Controllers\ViewController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#247"><abbr title="App\Http\Controllers\ViewController::setAsDefault">setAsDefault</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#297"><abbr title="App\Http\Controllers\ViewController::pinView">pinView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#331"><abbr title="App\Http\Controllers\ViewController::rename">rename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#355"><abbr title="App\Http\Controllers\ViewController::duplicate">duplicate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#404"><abbr title="App\Http\Controllers\ViewController::getViewConfig">getViewConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#446"><abbr title="App\Http\Controllers\ViewController::getViewCount">getViewCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ViewController.php.html#537"><abbr title="App\Http\Controllers\ViewController::workOrderViewType">workOrderViewType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCallsController.php.html#14"><abbr title="App\Http\Controllers\WebhookCallsController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCallsController.php.html#22"><abbr title="App\Http\Controllers\WebhookCallsController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookController.php.html#36"><abbr title="App\Http\Controllers\WebhookController::__invoke">__invoke</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookController.php.html#99"><abbr title="App\Http\Controllers\WebhookController::invalidAction">invalidAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookController.php.html#112"><abbr title="App\Http\Controllers\WebhookController::validateQuoteApprovePayload">validateQuoteApprovePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookController.php.html#151"><abbr title="App\Http\Controllers\WebhookController::validateQuoteRejectPayload">validateQuoteRejectPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookController.php.html#179"><abbr title="App\Http\Controllers\WebhookController::validateQuoteExpirePayload">validateQuoteExpirePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookController.php.html#207"><abbr title="App\Http\Controllers\WebhookController::validateQuoteUpdatePayload">validateQuoteUpdatePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookController.php.html#261"><abbr title="App\Http\Controllers\WebhookController::validateQuoteRestorePayload">validateQuoteRestorePayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookController.php.html#289"><abbr title="App\Http\Controllers\WebhookController::validateQuoteSubmittedForApprovalPayload">validateQuoteSubmittedForApprovalPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderIssueController.php.html#22"><abbr title="App\Http\Controllers\WorkOrder\WorkOrderIssueController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivityLogController.php.html#27"><abbr title="App\Http\Controllers\WorkOrderActivityLogController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderActivityLogController.php.html#99"><abbr title="App\Http\Controllers\WorkOrderActivityLogController::paginate">paginate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeController.php.html#25"><abbr title="App\Http\Controllers\WorkOrderAssigneeController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAssigneeController.php.html#88"><abbr title="App\Http\Controllers\WorkOrderAssigneeController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#124"><abbr title="App\Http\Controllers\WorkOrderController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#168"><abbr title="App\Http\Controllers\WorkOrderController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#226"><abbr title="App\Http\Controllers\WorkOrderController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#274"><abbr title="App\Http\Controllers\WorkOrderController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#321"><abbr title="App\Http\Controllers\WorkOrderController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#350"><abbr title="App\Http\Controllers\WorkOrderController::getGroupView">getGroupView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#416"><abbr title="App\Http\Controllers\WorkOrderController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#566"><abbr title="App\Http\Controllers\WorkOrderController::updatePriority">updatePriority</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#608"><abbr title="App\Http\Controllers\WorkOrderController::updateDueDate">updateDueDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#633"><abbr title="App\Http\Controllers\WorkOrderController::deleteDueDate">deleteDueDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#657"><abbr title="App\Http\Controllers\WorkOrderController::updateAccessMethod">updateAccessMethod</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#697"><abbr title="App\Http\Controllers\WorkOrderController::updateResidentInfo">updateResidentInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#763"><abbr title="App\Http\Controllers\WorkOrderController::updatePropertyAddress">updatePropertyAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#831"><abbr title="App\Http\Controllers\WorkOrderController::getCount">getCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#867"><abbr title="App\Http\Controllers\WorkOrderController::handleIndexException">handleIndexException</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#893"><abbr title="App\Http\Controllers\WorkOrderController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#961"><abbr title="App\Http\Controllers\WorkOrderController::priorityBasedGroupData">priorityBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1022"><abbr title="App\Http\Controllers\WorkOrderController::categoryBasedGroupData">categoryBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1092"><abbr title="App\Http\Controllers\WorkOrderController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1153"><abbr title="App\Http\Controllers\WorkOrderController::tagBasedGroupData">tagBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1214"><abbr title="App\Http\Controllers\WorkOrderController::healthScoreBaseGroupData">healthScoreBaseGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1273"><abbr title="App\Http\Controllers\WorkOrderController::technicianBasedGroupData">technicianBasedGroupData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1330"><abbr title="App\Http\Controllers\WorkOrderController::tripSummary">tripSummary</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1382"><abbr title="App\Http\Controllers\WorkOrderController::getTripDetails">getTripDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1431"><abbr title="App\Http\Controllers\WorkOrderController::markAsComplete">markAsComplete</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1545"><abbr title="App\Http\Controllers\WorkOrderController::loadWorkOrderRelationsForWeb">loadWorkOrderRelationsForWeb</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1666"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderRelations">getWorkOrderRelations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1704"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderSelectFields">getWorkOrderSelectFields</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1827"><abbr title="App\Http\Controllers\WorkOrderController::getServiceCallSelectFieldsForApp">getServiceCallSelectFieldsForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1860"><abbr title="App\Http\Controllers\WorkOrderController::getServiceCallRelationsForApp">getServiceCallRelationsForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1875"><abbr title="App\Http\Controllers\WorkOrderController::shouldListForTechnicianMobile">shouldListForTechnicianMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1885"><abbr title="App\Http\Controllers\WorkOrderController::listWorkOrdersForTechnicianMobile">listWorkOrdersForTechnicianMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1933"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderRelationsForMobile">getWorkOrderRelationsForMobile</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1981"><abbr title="App\Http\Controllers\WorkOrderController::getServiceRequestColumnForApp">getServiceRequestColumnForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#2005"><abbr title="App\Http\Controllers\WorkOrderController::getServiceRequestRelationsForApp">getServiceRequestRelationsForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#2017"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderIssuesColumnForApp">getWorkOrderIssuesColumnForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#2032"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderIssuesRelationsForApp">getWorkOrderIssuesRelationsForApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#48"><abbr title="App\Http\Controllers\WorkOrderMediaController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#199"><abbr title="App\Http\Controllers\WorkOrderMediaController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#240"><abbr title="App\Http\Controllers\WorkOrderMediaController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#282"><abbr title="App\Http\Controllers\WorkOrderMediaController::uploadOriginalMedia">uploadOriginalMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#353"><abbr title="App\Http\Controllers\WorkOrderMediaController::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNoteController.php.html#34"><abbr title="App\Http\Controllers\WorkOrderNoteController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNoteController.php.html#74"><abbr title="App\Http\Controllers\WorkOrderNoteController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNoteController.php.html#128"><abbr title="App\Http\Controllers\WorkOrderNoteController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNoteController.php.html#186"><abbr title="App\Http\Controllers\WorkOrderNoteController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="PDFController.php.html#193"><abbr title="App\Http\Controllers\PDFController::viewInvoice">viewInvoice</abbr></a></td><td class="text-right">756</td></tr>
       <tr><td><a href="WorkOrderController.php.html#416"><abbr title="App\Http\Controllers\WorkOrderController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#48"><abbr title="App\Http\Controllers\WorkOrderMediaController::store">store</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="ServiceRequestMediaController.php.html#44"><abbr title="App\Http\Controllers\ServiceRequestMediaController::store">store</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#878"><abbr title="App\Http\Controllers\ServiceRequestController::createWorkOrder">createWorkOrder</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="TechnicianController.php.html#130"><abbr title="App\Http\Controllers\TechnicianController::updateTechnicianSkills">updateTechnicianSkills</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="WorkOrderController.php.html#350"><abbr title="App\Http\Controllers\WorkOrderController::getGroupView">getGroupView</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#995"><abbr title="App\Http\Controllers\ServiceRequestController::markAsComplete">markAsComplete</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#331"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setStatus">setStatus</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1431"><abbr title="App\Http\Controllers\WorkOrderController::markAsComplete">markAsComplete</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="QuoteController.php.html#339"><abbr title="App\Http\Controllers\QuoteController::getGroupView">getGroupView</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="QuoteController.php.html#250"><abbr title="App\Http\Controllers\QuoteController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="UserController.php.html#580"><abbr title="App\Http\Controllers\UserController::updateUserStatus">updateUserStatus</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ServiceRequestMediaController.php.html#258"><abbr title="App\Http\Controllers\ServiceRequestMediaController::uploadOriginalMedia">uploadOriginalMedia</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ViewController.php.html#446"><abbr title="App\Http\Controllers\ViewController::getViewCount">getViewCount</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="WorkOrderController.php.html#893"><abbr title="App\Http\Controllers\WorkOrderController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#282"><abbr title="App\Http\Controllers\WorkOrderMediaController::uploadOriginalMedia">uploadOriginalMedia</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#726"><abbr title="App\Http\Controllers\Auth\RegisterController::authenticateClientCredential">authenticateClientCredential</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="LulaWebhookController.php.html#34"><abbr title="App\Http\Controllers\LulaWebhookController::__invoke">__invoke</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="OrganizationController.php.html#32"><abbr title="App\Http\Controllers\OrganizationController::update">update</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ResidentAvailabilityController.php.html#21"><abbr title="App\Http\Controllers\ResidentAvailabilityController::store">store</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#661"><abbr title="App\Http\Controllers\ServiceRequestController::getGroupView">getGroupView</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#710"><abbr title="App\Http\Controllers\ServiceRequestController::getFilterValues">getFilterValues</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#142"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::store">store</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="UserController.php.html#281"><abbr title="App\Http\Controllers\UserController::update">update</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="VendorController.php.html#99"><abbr title="App\Http\Controllers\VendorController::store">store</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="RoleController.php.html#136"><abbr title="App\Http\Controllers\RoleController::update">update</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="UserController.php.html#129"><abbr title="App\Http\Controllers\UserController::store">store</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="UserController.php.html#700"><abbr title="App\Http\Controllers\UserController::getGroupView">getGroupView</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="WorkOrderController.php.html#961"><abbr title="App\Http\Controllers\WorkOrderController::priorityBasedGroupData">priorityBasedGroupData</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="AppVersionController.php.html#19"><abbr title="App\Http\Controllers\AppVersionController::checkLatestAppVersion">checkLatestAppVersion</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#402"><abbr title="App\Http\Controllers\Auth\RegisterController::signup">signup</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="QuoteController.php.html#60"><abbr title="App\Http\Controllers\QuoteController::index">index</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ServiceRequestAssigneeController.php.html#25"><abbr title="App\Http\Controllers\ServiceRequestAssigneeController::store">store</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#200"><abbr title="App\Http\Controllers\ServiceRequestController::store">store</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#225"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::update">update</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UserController.php.html#492"><abbr title="App\Http\Controllers\UserController::updateCognitoUserDetails">updateCognitoUserDetails</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#50"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::sendVendorOnboardingLink">sendVendorOnboardingLink</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#141"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setBasicInfo">setBasicInfo</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrderAssigneeController.php.html#25"><abbr title="App\Http\Controllers\WorkOrderAssigneeController::store">store</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrderController.php.html#168"><abbr title="App\Http\Controllers\WorkOrderController::store">store</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1092"><abbr title="App\Http\Controllers\WorkOrderController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1153"><abbr title="App\Http\Controllers\WorkOrderController::tagBasedGroupData">tagBasedGroupData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1214"><abbr title="App\Http\Controllers\WorkOrderController::healthScoreBaseGroupData">healthScoreBaseGroupData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1273"><abbr title="App\Http\Controllers\WorkOrderController::technicianBasedGroupData">technicianBasedGroupData</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="LookupController.php.html#276"><abbr title="App\Http\Controllers\LookupController::expectedDurations">expectedDurations</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="MediaController.php.html#18"><abbr title="App\Http\Controllers\MediaController::__invoke">__invoke</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="PDFController.php.html#38"><abbr title="App\Http\Controllers\PDFController::viewQuote">viewQuote</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequestActivityLogController.php.html#29"><abbr title="App\Http\Controllers\ServiceRequestActivityLogController::index">index</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#98"><abbr title="App\Http\Controllers\ServiceRequestController::index">index</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#259"><abbr title="App\Http\Controllers\ServiceRequestController::show">show</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#1203"><abbr title="App\Http\Controllers\ServiceRequestController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequestNoteController.php.html#38"><abbr title="App\Http\Controllers\ServiceRequestNoteController::index">index</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="TechnicianController.php.html#31"><abbr title="App\Http\Controllers\TechnicianController::updateWorkingHours">updateWorkingHours</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="UserController.php.html#395"><abbr title="App\Http\Controllers\UserController::destroy">destroy</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrderController.php.html#763"><abbr title="App\Http\Controllers\WorkOrderController::updatePropertyAddress">updatePropertyAddress</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1022"><abbr title="App\Http\Controllers\WorkOrderController::categoryBasedGroupData">categoryBasedGroupData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#353"><abbr title="App\Http\Controllers\WorkOrderMediaController::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#333"><abbr title="App\Http\Controllers\Auth\RegisterController::setVendorPassword">setVendorPassword</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#988"><abbr title="App\Http\Controllers\Auth\RegisterController::validateSignedUrl">validateSignedUrl</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="LookupController.php.html#85"><abbr title="App\Http\Controllers\LookupController::getFieldAppFilters">getFieldAppFilters</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="PublicAccessController.php.html#19"><abbr title="App\Http\Controllers\PublicAccessController::getMedia">getMedia</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="RoleController.php.html#51"><abbr title="App\Http\Controllers\RoleController::store">store</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SchedulingController.php.html#80"><abbr title="App\Http\Controllers\SchedulingController::getContext">getContext</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#595"><abbr title="App\Http\Controllers\ServiceRequestController::update">update</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#777"><abbr title="App\Http\Controllers\ServiceRequestController::updateDescription">updateDescription</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#1156"><abbr title="App\Http\Controllers\ServiceRequestController::importedFromBasedGroupData">importedFromBasedGroupData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequestMediaController.php.html#177"><abbr title="App\Http\Controllers\ServiceRequestMediaController::show">show</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="UserController.php.html#661"><abbr title="App\Http\Controllers\UserController::resetUserPassword">resetUserPassword</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="UserController.php.html#801"><abbr title="App\Http\Controllers\UserController::roleBasedGroupData">roleBasedGroupData</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#257"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::getStatus">getStatus</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#407"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setServices">setServices</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#467"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setServiceAreas">setServiceAreas</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#535"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::processServices">processServices</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ViewController.php.html#50"><abbr title="App\Http\Controllers\ViewController::index">index</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ViewController.php.html#404"><abbr title="App\Http\Controllers\ViewController::getViewConfig">getViewConfig</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderIssueController.php.html#22"><abbr title="App\Http\Controllers\WorkOrder\WorkOrderIssueController::show">show</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderActivityLogController.php.html#27"><abbr title="App\Http\Controllers\WorkOrderActivityLogController::index">index</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderController.php.html#226"><abbr title="App\Http\Controllers\WorkOrderController::show">show</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderController.php.html#274"><abbr title="App\Http\Controllers\WorkOrderController::update">update</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderController.php.html#697"><abbr title="App\Http\Controllers\WorkOrderController::updateResidentInfo">updateResidentInfo</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#199"><abbr title="App\Http\Controllers\WorkOrderMediaController::show">show</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderNoteController.php.html#128"><abbr title="App\Http\Controllers\WorkOrderNoteController::update">update</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderNoteController.php.html#186"><abbr title="App\Http\Controllers\WorkOrderNoteController::destroy">destroy</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#87"><abbr title="App\Http\Controllers\Auth\RegisterController::forgotPassword">forgotPassword</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#536"><abbr title="App\Http\Controllers\Auth\RegisterController::setupUserPool">setupUserPool</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#642"><abbr title="App\Http\Controllers\Auth\RegisterController::findLoginConfig">findLoginConfig</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Auth/SSOController.php.html#76"><abbr title="App\Http\Controllers\Auth\SSOController::getCognitoUserDetails">getCognitoUserDetails</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PublicAccessController.php.html#82"><abbr title="App\Http\Controllers\PublicAccessController::getSrMedia">getSrMedia</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequestAssigneeController.php.html#78"><abbr title="App\Http\Controllers\ServiceRequestAssigneeController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#1113"><abbr title="App\Http\Controllers\ServiceRequestController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequestMediaController.php.html#218"><abbr title="App\Http\Controllers\ServiceRequestMediaController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="TagController.php.html#143"><abbr title="App\Http\Controllers\TagController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#44"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::index">index</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UserController.php.html#450"><abbr title="App\Http\Controllers\UserController::filters">filters</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UserController.php.html#748"><abbr title="App\Http\Controllers\UserController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingController.php.html#222"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::getVendorOnboarding">getVendorOnboarding</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ViewController.php.html#117"><abbr title="App\Http\Controllers\ViewController::store">store</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ViewController.php.html#196"><abbr title="App\Http\Controllers\ViewController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ViewController.php.html#247"><abbr title="App\Http\Controllers\ViewController::setAsDefault">setAsDefault</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ViewController.php.html#355"><abbr title="App\Http\Controllers\ViewController::duplicate">duplicate</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WebhookController.php.html#36"><abbr title="App\Http\Controllers\WebhookController::__invoke">__invoke</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WebhookController.php.html#261"><abbr title="App\Http\Controllers\WebhookController::validateQuoteRestorePayload">validateQuoteRestorePayload</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderAssigneeController.php.html#88"><abbr title="App\Http\Controllers\WorkOrderAssigneeController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderMediaController.php.html#240"><abbr title="App\Http\Controllers\WorkOrderMediaController::destroy">destroy</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderNoteController.php.html#34"><abbr title="App\Http\Controllers\WorkOrderNoteController::index">index</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderNoteController.php.html#74"><abbr title="App\Http\Controllers\WorkOrderNoteController::store">store</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#197"><abbr title="App\Http\Controllers\Auth\RegisterController::getVendorOnboardingInvite">getVendorOnboardingInvite</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#1013"><abbr title="App\Http\Controllers\Auth\RegisterController::validatePassword">validatePassword</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Auth/SSOController.php.html#26"><abbr title="App\Http\Controllers\Auth\SSOController::validateUser">validateUser</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DatabaseNotificationController.php.html#18"><abbr title="App\Http\Controllers\DatabaseNotificationController::index">index</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DatabaseNotificationController.php.html#60"><abbr title="App\Http\Controllers\DatabaseNotificationController::clearedNotifications">clearedNotifications</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Developer/HealthCheckController.php.html#38"><abbr title="App\Http\Controllers\Developer\HealthCheckController::utilitiesCheck">utilitiesCheck</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InvoiceController.php.html#41"><abbr title="App\Http\Controllers\InvoiceController::show">show</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InvoiceController.php.html#92"><abbr title="App\Http\Controllers\InvoiceController::workOrderInvoiceSummary">workOrderInvoiceSummary</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="LookupController.php.html#44"><abbr title="App\Http\Controllers\LookupController::__invoke">__invoke</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QuoteController.php.html#603"><abbr title="App\Http\Controllers\QuoteController::assigneeBasedGroupData">assigneeBasedGroupData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QuoteController.php.html#678"><abbr title="App\Http\Controllers\QuoteController::submittedBasedGroupData">submittedBasedGroupData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QuoteController.php.html#751"><abbr title="App\Http\Controllers\QuoteController::tagBasedGroupData">tagBasedGroupData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ResidentAvailabilityController.php.html#101"><abbr title="App\Http\Controllers\ResidentAvailabilityController::show">show</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="SchedulingController.php.html#126"><abbr title="App\Http\Controllers\SchedulingController::getVendors">getVendors</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#1252"><abbr title="App\Http\Controllers\ServiceRequestController::needToCreateServiceRequestDescription">needToCreateServiceRequestDescription</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequestNoteController.php.html#87"><abbr title="App\Http\Controllers\ServiceRequestNoteController::store">store</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TagController.php.html#48"><abbr title="App\Http\Controllers\TagController::store">store</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TagController.php.html#103"><abbr title="App\Http\Controllers\TagController::update">update</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TechnicianController.php.html#97"><abbr title="App\Http\Controllers\TechnicianController::importWorkingHours">importWorkingHours</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TechnicianController.php.html#272"><abbr title="App\Http\Controllers\TechnicianController::importTechnicianSkills">importTechnicianSkills</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserController.php.html#84"><abbr title="App\Http\Controllers\UserController::index">index</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingLookupController.php.html#25"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLookups">getLookups</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="VendorController.php.html#54"><abbr title="App\Http\Controllers\VendorController::index">index</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="VendorController.php.html#234"><abbr title="App\Http\Controllers\VendorController::show">show</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WebhookController.php.html#112"><abbr title="App\Http\Controllers\WebhookController::validateQuoteApprovePayload">validateQuoteApprovePayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WebhookController.php.html#151"><abbr title="App\Http\Controllers\WebhookController::validateQuoteRejectPayload">validateQuoteRejectPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WebhookController.php.html#179"><abbr title="App\Http\Controllers\WebhookController::validateQuoteExpirePayload">validateQuoteExpirePayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WebhookController.php.html#207"><abbr title="App\Http\Controllers\WebhookController::validateQuoteUpdatePayload">validateQuoteUpdatePayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WebhookController.php.html#289"><abbr title="App\Http\Controllers\WebhookController::validateQuoteSubmittedForApprovalPayload">validateQuoteSubmittedForApprovalPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderController.php.html#124"><abbr title="App\Http\Controllers\WorkOrderController::index">index</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderController.php.html#657"><abbr title="App\Http\Controllers\WorkOrderController::updateAccessMethod">updateAccessMethod</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderController.php.html#831"><abbr title="App\Http\Controllers\WorkOrderController::getCount">getCount</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1330"><abbr title="App\Http\Controllers\WorkOrderController::tripSummary">tripSummary</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#128"><abbr title="App\Http\Controllers\Auth\RegisterController::resetPassword">resetPassword</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#145"><abbr title="App\Http\Controllers\Auth\RegisterController::verify">verify</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#167"><abbr title="App\Http\Controllers\Auth\RegisterController::searchAddress">searchAddress</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#510"><abbr title="App\Http\Controllers\Auth\RegisterController::createOrganization">createOrganization</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#975"><abbr title="App\Http\Controllers\Auth\RegisterController::validateRequest">validateRequest</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Developer/HealthCheckController.php.html#67"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkDatabaseConnection">checkDatabaseConnection</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Developer/HealthCheckController.php.html#88"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkCacheConnection">checkCacheConnection</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Developer/HealthCheckController.php.html#110"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkLogging">checkLogging</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuoteController.php.html#525"><abbr title="App\Http\Controllers\QuoteController::categoryBasedGroupData">categoryBasedGroupData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SchedulingController.php.html#176"><abbr title="App\Http\Controllers\SchedulingController::getTechnicianList">getTechnicianList</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SchedulingController.php.html#204"><abbr title="App\Http\Controllers\SchedulingController::getTechnicianSchedules">getTechnicianSchedules</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SchedulingController.php.html#234"><abbr title="App\Http\Controllers\SchedulingController::getVendorAvailability">getVendorAvailability</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestActivityLogController.php.html#132"><abbr title="App\Http\Controllers\ServiceRequestActivityLogController::paginate">paginate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#986"><abbr title="App\Http\Controllers\ServiceRequestController::triggerIssueUpdatedEvents">triggerIssueUpdatedEvents</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestNoteController.php.html#139"><abbr title="App\Http\Controllers\ServiceRequestNoteController::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequestNoteController.php.html#187"><abbr title="App\Http\Controllers\ServiceRequestNoteController::destroy">destroy</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#318"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::viewTechnicianCalendar">viewTechnicianCalendar</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#387"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::getAvailabilityDates">getAvailabilityDates</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#35"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::index">index</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#77"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::show">show</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorController.php.html#276"><abbr title="App\Http\Controllers\VendorController::storeVendor">storeVendor</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ViewController.php.html#168"><abbr title="App\Http\Controllers\ViewController::update">update</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ViewController.php.html#297"><abbr title="App\Http\Controllers\ViewController::pinView">pinView</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ViewController.php.html#331"><abbr title="App\Http\Controllers\ViewController::rename">rename</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderActivityLogController.php.html#99"><abbr title="App\Http\Controllers\WorkOrderActivityLogController::paginate">paginate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderController.php.html#321"><abbr title="App\Http\Controllers\WorkOrderController::destroy">destroy</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderController.php.html#566"><abbr title="App\Http\Controllers\WorkOrderController::updatePriority">updatePriority</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#786"><abbr title="App\Http\Controllers\Auth\RegisterController::createUserPoolClient">createUserPoolClient</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#890"><abbr title="App\Http\Controllers\Auth\RegisterController::syncUserRolePermissions">syncUserRolePermissions</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#960"><abbr title="App\Http\Controllers\Auth\RegisterController::sendEmail">sendEmail</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Auth/RegisterController.php.html#1041"><abbr title="App\Http\Controllers\Auth\RegisterController::processPasswordReset">processPasswordReset</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InvoiceController.php.html#23"><abbr title="App\Http\Controllers\InvoiceController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuoteController.php.html#399"><abbr title="App\Http\Controllers\QuoteController::workOrderNumberBasedGroupData">workOrderNumberBasedGroupData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuoteController.php.html#462"><abbr title="App\Http\Controllers\QuoteController::statusBasedGroupData">statusBasedGroupData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RoleController.php.html#212"><abbr title="App\Http\Controllers\RoleController::destroy">destroy</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RoleController.php.html#232"><abbr title="App\Http\Controllers\RoleController::listPermissions">listPermissions</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#642"><abbr title="App\Http\Controllers\ServiceRequestController::markAdminAvailabilityViewed">markAdminAvailabilityViewed</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequestController.php.html#853"><abbr title="App\Http\Controllers\ServiceRequestController::updateAccessMethod">updateAccessMethod</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TechnicianAppointmentsController.php.html#299"><abbr title="App\Http\Controllers\TechnicianAppointmentsController::destroy">destroy</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#551"><abbr title="App\Http\Controllers\UserController::updateNotificationSubscription">updateNotificationSubscription</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Vendor/VendorOnboardingLookupController.php.html#74"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLocationApiKey">getLocationApiKey</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Vendor/WorkOrderController.php.html#261"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getSkippedMediaTypes">getSkippedMediaTypes</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderController.php.html#608"><abbr title="App\Http\Controllers\WorkOrderController::updateDueDate">updateDueDate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderController.php.html#633"><abbr title="App\Http\Controllers\WorkOrderController::deleteDueDate">deleteDueDate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1382"><abbr title="App\Http\Controllers\WorkOrderController::getTripDetails">getTripDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1875"><abbr title="App\Http\Controllers\WorkOrderController::shouldListForTechnicianMobile">shouldListForTechnicianMobile</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1933"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderRelationsForMobile">getWorkOrderRelationsForMobile</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderController.php.html#1981"><abbr title="App\Http\Controllers\WorkOrderController::getServiceRequestColumnForApp">getServiceRequestColumnForApp</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderController.php.html#2032"><abbr title="App\Http\Controllers\WorkOrderController::getWorkOrderIssuesRelationsForApp">getWorkOrderIssuesRelationsForApp</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:04:51 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([36,5,0,0,0,0,0,0,0,0,0,1], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([262,0,0,0,0,0,0,0,0,0,0,5], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,8,"<a href=\"AppVersionController.php.html#14\">App\\Http\\Controllers\\AppVersionController<\/a>"],[1.3215859030837005,79,"<a href=\"Auth\/RegisterController.php.html#56\">App\\Http\\Controllers\\Auth\\RegisterController<\/a>"],[0,9,"<a href=\"Auth\/SSOController.php.html#22\">App\\Http\\Controllers\\Auth\\SSOController<\/a>"],[100,0,"<a href=\"Controller.php.html#9\">App\\Http\\Controllers\\Controller<\/a>"],[0,8,"<a href=\"DatabaseNotificationController.php.html#16\">App\\Http\\Controllers\\DatabaseNotificationController<\/a>"],[0,14,"<a href=\"Developer\/HealthCheckController.php.html#14\">App\\Http\\Controllers\\Developer\\HealthCheckController<\/a>"],[0,10,"<a href=\"Developer\/LogViewerController.php.html#19\">App\\Http\\Controllers\\Developer\\LogViewerController<\/a>"],[0,10,"<a href=\"InvoiceController.php.html#21\">App\\Http\\Controllers\\InvoiceController<\/a>"],[0,27,"<a href=\"LookupController.php.html#39\">App\\Http\\Controllers\\LookupController<\/a>"],[0,20,"<a href=\"LulaWebhookController.php.html#28\">App\\Http\\Controllers\\LulaWebhookController<\/a>"],[0,7,"<a href=\"MediaController.php.html#16\">App\\Http\\Controllers\\MediaController<\/a>"],[0,13,"<a href=\"OrganizationController.php.html#20\">App\\Http\\Controllers\\OrganizationController<\/a>"],[0.6097560975609756,35,"<a href=\"PDFController.php.html#30\">App\\Http\\Controllers\\PDFController<\/a>"],[0,1,"<a href=\"ProfileController.php.html#9\">App\\Http\\Controllers\\ProfileController<\/a>"],[0,11,"<a href=\"PublicAccessController.php.html#17\">App\\Http\\Controllers\\PublicAccessController<\/a>"],[0,2,"<a href=\"PublicApiWorkOrderWebhookEventsController.php.html#8\">App\\Http\\Controllers\\PublicApiWorkOrderWebhookEventsController<\/a>"],[0,52,"<a href=\"QuoteController.php.html#49\">App\\Http\\Controllers\\QuoteController<\/a>"],[0,14,"<a href=\"ResidentAvailabilityController.php.html#19\">App\\Http\\Controllers\\ResidentAvailabilityController<\/a>"],[0,22,"<a href=\"RoleController.php.html#25\">App\\Http\\Controllers\\RoleController<\/a>"],[0.8771929824561403,26,"<a href=\"SchedulingController.php.html#31\">App\\Http\\Controllers\\SchedulingController<\/a>"],[0,10,"<a href=\"ServiceRequestActivityLogController.php.html#24\">App\\Http\\Controllers\\ServiceRequestActivityLogController<\/a>"],[0,13,"<a href=\"ServiceRequestAssigneeController.php.html#22\">App\\Http\\Controllers\\ServiceRequestAssigneeController<\/a>"],[0,112,"<a href=\"ServiceRequestController.php.html#89\">App\\Http\\Controllers\\ServiceRequestController<\/a>"],[0,38,"<a href=\"ServiceRequestMediaController.php.html#39\">App\\Http\\Controllers\\ServiceRequestMediaController<\/a>"],[0,17,"<a href=\"ServiceRequestNoteController.php.html#31\">App\\Http\\Controllers\\ServiceRequestNoteController<\/a>"],[0,15,"<a href=\"TagController.php.html#24\">App\\Http\\Controllers\\TagController<\/a>"],[0,31,"<a href=\"TechnicianAppointmentsController.php.html#37\">App\\Http\\Controllers\\TechnicianAppointmentsController<\/a>"],[0,30,"<a href=\"TechnicianController.php.html#26\">App\\Http\\Controllers\\TechnicianController<\/a>"],[1.0266940451745379,85,"<a href=\"UserController.php.html#61\">App\\Http\\Controllers\\UserController<\/a>"],[0,60,"<a href=\"Vendor\/VendorOnboardingController.php.html#40\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController<\/a>"],[0,9,"<a href=\"Vendor\/VendorOnboardingLookupController.php.html#23\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController<\/a>"],[0,13,"<a href=\"Vendor\/WorkOrderController.php.html#27\">App\\Http\\Controllers\\Vendor\\WorkOrderController<\/a>"],[2.793296089385475,22,"<a href=\"VendorController.php.html#33\">App\\Http\\Controllers\\VendorController<\/a>"],[0,53,"<a href=\"ViewController.php.html#43\">App\\Http\\Controllers\\ViewController<\/a>"],[0,2,"<a href=\"WebhookCallsController.php.html#9\">App\\Http\\Controllers\\WebhookCallsController<\/a>"],[0,31,"<a href=\"WebhookController.php.html#30\">App\\Http\\Controllers\\WebhookController<\/a>"],[0,6,"<a href=\"WorkOrder\/WorkOrderIssueController.php.html#17\">App\\Http\\Controllers\\WorkOrder\\WorkOrderIssueController<\/a>"],[0,9,"<a href=\"WorkOrderActivityLogController.php.html#22\">App\\Http\\Controllers\\WorkOrderActivityLogController<\/a>"],[0,13,"<a href=\"WorkOrderAssigneeController.php.html#22\">App\\Http\\Controllers\\WorkOrderAssigneeController<\/a>"],[0,186,"<a href=\"WorkOrderController.php.html#115\">App\\Http\\Controllers\\WorkOrderController<\/a>"],[0,49,"<a href=\"WorkOrderMediaController.php.html#43\">App\\Http\\Controllers\\WorkOrderMediaController<\/a>"],[0,22,"<a href=\"WorkOrderNoteController.php.html#27\">App\\Http\\Controllers\\WorkOrderNoteController<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,8,"<a href=\"AppVersionController.php.html#19\">App\\Http\\Controllers\\AppVersionController::checkLatestAppVersion<\/a>"],[100,1,"<a href=\"Auth\/RegisterController.php.html#68\">App\\Http\\Controllers\\Auth\\RegisterController::__construct<\/a>"],[0,5,"<a href=\"Auth\/RegisterController.php.html#87\">App\\Http\\Controllers\\Auth\\RegisterController::forgotPassword<\/a>"],[0,3,"<a href=\"Auth\/RegisterController.php.html#128\">App\\Http\\Controllers\\Auth\\RegisterController::resetPassword<\/a>"],[0,3,"<a href=\"Auth\/RegisterController.php.html#145\">App\\Http\\Controllers\\Auth\\RegisterController::verify<\/a>"],[0,3,"<a href=\"Auth\/RegisterController.php.html#167\">App\\Http\\Controllers\\Auth\\RegisterController::searchAddress<\/a>"],[0,4,"<a href=\"Auth\/RegisterController.php.html#197\">App\\Http\\Controllers\\Auth\\RegisterController::getVendorOnboardingInvite<\/a>"],[0,6,"<a href=\"Auth\/RegisterController.php.html#333\">App\\Http\\Controllers\\Auth\\RegisterController::setVendorPassword<\/a>"],[0,8,"<a href=\"Auth\/RegisterController.php.html#402\">App\\Http\\Controllers\\Auth\\RegisterController::signup<\/a>"],[0,3,"<a href=\"Auth\/RegisterController.php.html#510\">App\\Http\\Controllers\\Auth\\RegisterController::createOrganization<\/a>"],[0,5,"<a href=\"Auth\/RegisterController.php.html#536\">App\\Http\\Controllers\\Auth\\RegisterController::setupUserPool<\/a>"],[0,5,"<a href=\"Auth\/RegisterController.php.html#642\">App\\Http\\Controllers\\Auth\\RegisterController::findLoginConfig<\/a>"],[0,1,"<a href=\"Auth\/RegisterController.php.html#709\">App\\Http\\Controllers\\Auth\\RegisterController::findProviderLoginConfig<\/a>"],[0,10,"<a href=\"Auth\/RegisterController.php.html#726\">App\\Http\\Controllers\\Auth\\RegisterController::authenticateClientCredential<\/a>"],[0,2,"<a href=\"Auth\/RegisterController.php.html#786\">App\\Http\\Controllers\\Auth\\RegisterController::createUserPoolClient<\/a>"],[0,1,"<a href=\"Auth\/RegisterController.php.html#823\">App\\Http\\Controllers\\Auth\\RegisterController::createUserPool<\/a>"],[0,2,"<a href=\"Auth\/RegisterController.php.html#890\">App\\Http\\Controllers\\Auth\\RegisterController::syncUserRolePermissions<\/a>"],[0,2,"<a href=\"Auth\/RegisterController.php.html#960\">App\\Http\\Controllers\\Auth\\RegisterController::sendEmail<\/a>"],[0,3,"<a href=\"Auth\/RegisterController.php.html#975\">App\\Http\\Controllers\\Auth\\RegisterController::validateRequest<\/a>"],[0,6,"<a href=\"Auth\/RegisterController.php.html#988\">App\\Http\\Controllers\\Auth\\RegisterController::validateSignedUrl<\/a>"],[0,4,"<a href=\"Auth\/RegisterController.php.html#1013\">App\\Http\\Controllers\\Auth\\RegisterController::validatePassword<\/a>"],[0,2,"<a href=\"Auth\/RegisterController.php.html#1041\">App\\Http\\Controllers\\Auth\\RegisterController::processPasswordReset<\/a>"],[0,4,"<a href=\"Auth\/SSOController.php.html#26\">App\\Http\\Controllers\\Auth\\SSOController::validateUser<\/a>"],[0,5,"<a href=\"Auth\/SSOController.php.html#76\">App\\Http\\Controllers\\Auth\\SSOController::getCognitoUserDetails<\/a>"],[0,4,"<a href=\"DatabaseNotificationController.php.html#18\">App\\Http\\Controllers\\DatabaseNotificationController::index<\/a>"],[0,4,"<a href=\"DatabaseNotificationController.php.html#60\">App\\Http\\Controllers\\DatabaseNotificationController::clearedNotifications<\/a>"],[0,1,"<a href=\"Developer\/HealthCheckController.php.html#26\">App\\Http\\Controllers\\Developer\\HealthCheckController::basicCheck<\/a>"],[0,4,"<a href=\"Developer\/HealthCheckController.php.html#38\">App\\Http\\Controllers\\Developer\\HealthCheckController::utilitiesCheck<\/a>"],[0,3,"<a href=\"Developer\/HealthCheckController.php.html#67\">App\\Http\\Controllers\\Developer\\HealthCheckController::checkDatabaseConnection<\/a>"],[0,3,"<a href=\"Developer\/HealthCheckController.php.html#88\">App\\Http\\Controllers\\Developer\\HealthCheckController::checkCacheConnection<\/a>"],[0,3,"<a href=\"Developer\/HealthCheckController.php.html#110\">App\\Http\\Controllers\\Developer\\HealthCheckController::checkLogging<\/a>"],[0,1,"<a href=\"Developer\/LogViewerController.php.html#24\">App\\Http\\Controllers\\Developer\\LogViewerController::requestLogs<\/a>"],[0,1,"<a href=\"Developer\/LogViewerController.php.html#34\">App\\Http\\Controllers\\Developer\\LogViewerController::requestLogShow<\/a>"],[0,1,"<a href=\"Developer\/LogViewerController.php.html#42\">App\\Http\\Controllers\\Developer\\LogViewerController::incomingWebhookLogs<\/a>"],[0,1,"<a href=\"Developer\/LogViewerController.php.html#50\">App\\Http\\Controllers\\Developer\\LogViewerController::incomingWebhookLogShow<\/a>"],[0,1,"<a href=\"Developer\/LogViewerController.php.html#58\">App\\Http\\Controllers\\Developer\\LogViewerController::outgoingWebhookLogs<\/a>"],[0,1,"<a href=\"Developer\/LogViewerController.php.html#66\">App\\Http\\Controllers\\Developer\\LogViewerController::outgoingWebhookLogShow<\/a>"],[0,1,"<a href=\"Developer\/LogViewerController.php.html#76\">App\\Http\\Controllers\\Developer\\LogViewerController::developerAlerts<\/a>"],[0,1,"<a href=\"Developer\/LogViewerController.php.html#86\">App\\Http\\Controllers\\Developer\\LogViewerController::developerAlertShow<\/a>"],[0,1,"<a href=\"Developer\/LogViewerController.php.html#93\">App\\Http\\Controllers\\Developer\\LogViewerController::vendorPublicApiLogs<\/a>"],[0,1,"<a href=\"Developer\/LogViewerController.php.html#98\">App\\Http\\Controllers\\Developer\\LogViewerController::vendorPublicApiLogShow<\/a>"],[0,2,"<a href=\"InvoiceController.php.html#23\">App\\Http\\Controllers\\InvoiceController::index<\/a>"],[0,4,"<a href=\"InvoiceController.php.html#41\">App\\Http\\Controllers\\InvoiceController::show<\/a>"],[0,4,"<a href=\"InvoiceController.php.html#92\">App\\Http\\Controllers\\InvoiceController::workOrderInvoiceSummary<\/a>"],[0,4,"<a href=\"LookupController.php.html#44\">App\\Http\\Controllers\\LookupController::__invoke<\/a>"],[0,6,"<a href=\"LookupController.php.html#85\">App\\Http\\Controllers\\LookupController::getFieldAppFilters<\/a>"],[0,1,"<a href=\"LookupController.php.html#168\">App\\Http\\Controllers\\LookupController::getPriorities<\/a>"],[0,1,"<a href=\"LookupController.php.html#173\">App\\Http\\Controllers\\LookupController::getPropertyAccessMethods<\/a>"],[0,1,"<a href=\"LookupController.php.html#178\">App\\Http\\Controllers\\LookupController::getStates<\/a>"],[0,1,"<a href=\"LookupController.php.html#189\">App\\Http\\Controllers\\LookupController::getCountry<\/a>"],[0,1,"<a href=\"LookupController.php.html#200\">App\\Http\\Controllers\\LookupController::getProblemCategory<\/a>"],[0,1,"<a href=\"LookupController.php.html#225\">App\\Http\\Controllers\\LookupController::getTechnicians<\/a>"],[0,1,"<a href=\"LookupController.php.html#246\">App\\Http\\Controllers\\LookupController::getAssignees<\/a>"],[0,1,"<a href=\"LookupController.php.html#260\">App\\Http\\Controllers\\LookupController::getQuantityTypes<\/a>"],[0,1,"<a href=\"LookupController.php.html#265\">App\\Http\\Controllers\\LookupController::endTripeOptions<\/a>"],[0,7,"<a href=\"LookupController.php.html#276\">App\\Http\\Controllers\\LookupController::expectedDurations<\/a>"],[0,1,"<a href=\"LookupController.php.html#312\">App\\Http\\Controllers\\LookupController::getOnboardingStatuses<\/a>"],[0,10,"<a href=\"LulaWebhookController.php.html#34\">App\\Http\\Controllers\\LulaWebhookController::__invoke<\/a>"],[0,1,"<a href=\"LulaWebhookController.php.html#146\">App\\Http\\Controllers\\LulaWebhookController::invalidAction<\/a>"],[0,1,"<a href=\"LulaWebhookController.php.html#153\">App\\Http\\Controllers\\LulaWebhookController::validateScheduleInProgressPayload<\/a>"],[0,1,"<a href=\"LulaWebhookController.php.html#160\">App\\Http\\Controllers\\LulaWebhookController::validateSchedulePayload<\/a>"],[0,1,"<a href=\"LulaWebhookController.php.html#171\">App\\Http\\Controllers\\LulaWebhookController::validateWorkInProgressPayload<\/a>"],[0,1,"<a href=\"LulaWebhookController.php.html#178\">App\\Http\\Controllers\\LulaWebhookController::validateWorkPausedPayload<\/a>"],[0,1,"<a href=\"LulaWebhookController.php.html#186\">App\\Http\\Controllers\\LulaWebhookController::validateQualityCheckPayload<\/a>"],[0,1,"<a href=\"LulaWebhookController.php.html#193\">App\\Http\\Controllers\\LulaWebhookController::validateWorkCompletedPayload<\/a>"],[0,1,"<a href=\"LulaWebhookController.php.html#203\">App\\Http\\Controllers\\LulaWebhookController::validateWorkCancelPayload<\/a>"],[0,1,"<a href=\"LulaWebhookController.php.html#211\">App\\Http\\Controllers\\LulaWebhookController::validateNoteAddedPayload<\/a>"],[0,1,"<a href=\"LulaWebhookController.php.html#221\">App\\Http\\Controllers\\LulaWebhookController::validateInvoicePayload<\/a>"],[0,7,"<a href=\"MediaController.php.html#18\">App\\Http\\Controllers\\MediaController::__invoke<\/a>"],[0,1,"<a href=\"OrganizationController.php.html#24\">App\\Http\\Controllers\\OrganizationController::show<\/a>"],[0,10,"<a href=\"OrganizationController.php.html#32\">App\\Http\\Controllers\\OrganizationController::update<\/a>"],[0,1,"<a href=\"OrganizationController.php.html#94\">App\\Http\\Controllers\\OrganizationController::getTemplates<\/a>"],[0,1,"<a href=\"OrganizationController.php.html#115\">App\\Http\\Controllers\\OrganizationController::updateTemplate<\/a>"],[100,1,"<a href=\"PDFController.php.html#32\">App\\Http\\Controllers\\PDFController::__construct<\/a>"],[0,7,"<a href=\"PDFController.php.html#38\">App\\Http\\Controllers\\PDFController::viewQuote<\/a>"],[0,27,"<a href=\"PDFController.php.html#193\">App\\Http\\Controllers\\PDFController::viewInvoice<\/a>"],[0,1,"<a href=\"ProfileController.php.html#14\">App\\Http\\Controllers\\ProfileController::profile<\/a>"],[0,6,"<a href=\"PublicAccessController.php.html#19\">App\\Http\\Controllers\\PublicAccessController::getMedia<\/a>"],[0,5,"<a href=\"PublicAccessController.php.html#82\">App\\Http\\Controllers\\PublicAccessController::getSrMedia<\/a>"],[0,1,"<a href=\"PublicApiWorkOrderWebhookEventsController.php.html#13\">App\\Http\\Controllers\\PublicApiWorkOrderWebhookEventsController::index<\/a>"],[0,1,"<a href=\"PublicApiWorkOrderWebhookEventsController.php.html#21\">App\\Http\\Controllers\\PublicApiWorkOrderWebhookEventsController::show<\/a>"],[0,8,"<a href=\"QuoteController.php.html#60\">App\\Http\\Controllers\\QuoteController::index<\/a>"],[0,12,"<a href=\"QuoteController.php.html#250\">App\\Http\\Controllers\\QuoteController::getFilterValues<\/a>"],[0,13,"<a href=\"QuoteController.php.html#339\">App\\Http\\Controllers\\QuoteController::getGroupView<\/a>"],[0,2,"<a href=\"QuoteController.php.html#399\">App\\Http\\Controllers\\QuoteController::workOrderNumberBasedGroupData<\/a>"],[0,2,"<a href=\"QuoteController.php.html#462\">App\\Http\\Controllers\\QuoteController::statusBasedGroupData<\/a>"],[0,3,"<a href=\"QuoteController.php.html#525\">App\\Http\\Controllers\\QuoteController::categoryBasedGroupData<\/a>"],[0,4,"<a href=\"QuoteController.php.html#603\">App\\Http\\Controllers\\QuoteController::assigneeBasedGroupData<\/a>"],[0,4,"<a href=\"QuoteController.php.html#678\">App\\Http\\Controllers\\QuoteController::submittedBasedGroupData<\/a>"],[0,4,"<a href=\"QuoteController.php.html#751\">App\\Http\\Controllers\\QuoteController::tagBasedGroupData<\/a>"],[0,10,"<a href=\"ResidentAvailabilityController.php.html#21\">App\\Http\\Controllers\\ResidentAvailabilityController::store<\/a>"],[0,4,"<a href=\"ResidentAvailabilityController.php.html#101\">App\\Http\\Controllers\\ResidentAvailabilityController::show<\/a>"],[0,1,"<a href=\"RoleController.php.html#32\">App\\Http\\Controllers\\RoleController::index<\/a>"],[0,6,"<a href=\"RoleController.php.html#51\">App\\Http\\Controllers\\RoleController::store<\/a>"],[0,1,"<a href=\"RoleController.php.html#122\">App\\Http\\Controllers\\RoleController::show<\/a>"],[0,9,"<a href=\"RoleController.php.html#136\">App\\Http\\Controllers\\RoleController::update<\/a>"],[0,2,"<a href=\"RoleController.php.html#212\">App\\Http\\Controllers\\RoleController::destroy<\/a>"],[0,2,"<a href=\"RoleController.php.html#232\">App\\Http\\Controllers\\RoleController::listPermissions<\/a>"],[0,1,"<a href=\"RoleController.php.html#259\">App\\Http\\Controllers\\RoleController::hasPermissionsFromInvalidFeatures<\/a>"],[100,1,"<a href=\"SchedulingController.php.html#33\">App\\Http\\Controllers\\SchedulingController::__construct<\/a>"],[0,1,"<a href=\"SchedulingController.php.html#38\">App\\Http\\Controllers\\SchedulingController::index<\/a>"],[0,1,"<a href=\"SchedulingController.php.html#43\">App\\Http\\Controllers\\SchedulingController::create<\/a>"],[0,1,"<a href=\"SchedulingController.php.html#51\">App\\Http\\Controllers\\SchedulingController::show<\/a>"],[0,1,"<a href=\"SchedulingController.php.html#59\">App\\Http\\Controllers\\SchedulingController::edit<\/a>"],[0,1,"<a href=\"SchedulingController.php.html#67\">App\\Http\\Controllers\\SchedulingController::update<\/a>"],[0,1,"<a href=\"SchedulingController.php.html#75\">App\\Http\\Controllers\\SchedulingController::destroy<\/a>"],[0,6,"<a href=\"SchedulingController.php.html#80\">App\\Http\\Controllers\\SchedulingController::getContext<\/a>"],[0,4,"<a href=\"SchedulingController.php.html#126\">App\\Http\\Controllers\\SchedulingController::getVendors<\/a>"],[0,3,"<a href=\"SchedulingController.php.html#176\">App\\Http\\Controllers\\SchedulingController::getTechnicianList<\/a>"],[0,3,"<a href=\"SchedulingController.php.html#204\">App\\Http\\Controllers\\SchedulingController::getTechnicianSchedules<\/a>"],[0,3,"<a href=\"SchedulingController.php.html#234\">App\\Http\\Controllers\\SchedulingController::getVendorAvailability<\/a>"],[0,7,"<a href=\"ServiceRequestActivityLogController.php.html#29\">App\\Http\\Controllers\\ServiceRequestActivityLogController::index<\/a>"],[0,3,"<a href=\"ServiceRequestActivityLogController.php.html#132\">App\\Http\\Controllers\\ServiceRequestActivityLogController::paginate<\/a>"],[0,8,"<a href=\"ServiceRequestAssigneeController.php.html#25\">App\\Http\\Controllers\\ServiceRequestAssigneeController::store<\/a>"],[0,5,"<a href=\"ServiceRequestAssigneeController.php.html#78\">App\\Http\\Controllers\\ServiceRequestAssigneeController::destroy<\/a>"],[0,7,"<a href=\"ServiceRequestController.php.html#98\">App\\Http\\Controllers\\ServiceRequestController::index<\/a>"],[0,8,"<a href=\"ServiceRequestController.php.html#200\">App\\Http\\Controllers\\ServiceRequestController::store<\/a>"],[0,7,"<a href=\"ServiceRequestController.php.html#259\">App\\Http\\Controllers\\ServiceRequestController::show<\/a>"],[0,6,"<a href=\"ServiceRequestController.php.html#595\">App\\Http\\Controllers\\ServiceRequestController::update<\/a>"],[0,2,"<a href=\"ServiceRequestController.php.html#642\">App\\Http\\Controllers\\ServiceRequestController::markAdminAvailabilityViewed<\/a>"],[0,10,"<a href=\"ServiceRequestController.php.html#661\">App\\Http\\Controllers\\ServiceRequestController::getGroupView<\/a>"],[0,10,"<a href=\"ServiceRequestController.php.html#710\">App\\Http\\Controllers\\ServiceRequestController::getFilterValues<\/a>"],[0,6,"<a href=\"ServiceRequestController.php.html#777\">App\\Http\\Controllers\\ServiceRequestController::updateDescription<\/a>"],[0,2,"<a href=\"ServiceRequestController.php.html#853\">App\\Http\\Controllers\\ServiceRequestController::updateAccessMethod<\/a>"],[0,15,"<a href=\"ServiceRequestController.php.html#878\">App\\Http\\Controllers\\ServiceRequestController::createWorkOrder<\/a>"],[0,3,"<a href=\"ServiceRequestController.php.html#986\">App\\Http\\Controllers\\ServiceRequestController::triggerIssueUpdatedEvents<\/a>"],[0,14,"<a href=\"ServiceRequestController.php.html#995\">App\\Http\\Controllers\\ServiceRequestController::markAsComplete<\/a>"],[0,5,"<a href=\"ServiceRequestController.php.html#1113\">App\\Http\\Controllers\\ServiceRequestController::statusBasedGroupData<\/a>"],[0,6,"<a href=\"ServiceRequestController.php.html#1156\">App\\Http\\Controllers\\ServiceRequestController::importedFromBasedGroupData<\/a>"],[0,7,"<a href=\"ServiceRequestController.php.html#1203\">App\\Http\\Controllers\\ServiceRequestController::assigneeBasedGroupData<\/a>"],[0,4,"<a href=\"ServiceRequestController.php.html#1252\">App\\Http\\Controllers\\ServiceRequestController::needToCreateServiceRequestDescription<\/a>"],[0,16,"<a href=\"ServiceRequestMediaController.php.html#44\">App\\Http\\Controllers\\ServiceRequestMediaController::store<\/a>"],[0,6,"<a href=\"ServiceRequestMediaController.php.html#177\">App\\Http\\Controllers\\ServiceRequestMediaController::show<\/a>"],[0,5,"<a href=\"ServiceRequestMediaController.php.html#218\">App\\Http\\Controllers\\ServiceRequestMediaController::destroy<\/a>"],[0,11,"<a href=\"ServiceRequestMediaController.php.html#258\">App\\Http\\Controllers\\ServiceRequestMediaController::uploadOriginalMedia<\/a>"],[0,7,"<a href=\"ServiceRequestNoteController.php.html#38\">App\\Http\\Controllers\\ServiceRequestNoteController::index<\/a>"],[0,4,"<a href=\"ServiceRequestNoteController.php.html#87\">App\\Http\\Controllers\\ServiceRequestNoteController::store<\/a>"],[0,3,"<a href=\"ServiceRequestNoteController.php.html#139\">App\\Http\\Controllers\\ServiceRequestNoteController::update<\/a>"],[0,3,"<a href=\"ServiceRequestNoteController.php.html#187\">App\\Http\\Controllers\\ServiceRequestNoteController::destroy<\/a>"],[0,1,"<a href=\"TagController.php.html#31\">App\\Http\\Controllers\\TagController::index<\/a>"],[0,4,"<a href=\"TagController.php.html#48\">App\\Http\\Controllers\\TagController::store<\/a>"],[0,1,"<a href=\"TagController.php.html#90\">App\\Http\\Controllers\\TagController::show<\/a>"],[0,4,"<a href=\"TagController.php.html#103\">App\\Http\\Controllers\\TagController::update<\/a>"],[0,5,"<a href=\"TagController.php.html#143\">App\\Http\\Controllers\\TagController::destroy<\/a>"],[0,5,"<a href=\"TechnicianAppointmentsController.php.html#44\">App\\Http\\Controllers\\TechnicianAppointmentsController::index<\/a>"],[0,10,"<a href=\"TechnicianAppointmentsController.php.html#142\">App\\Http\\Controllers\\TechnicianAppointmentsController::store<\/a>"],[0,8,"<a href=\"TechnicianAppointmentsController.php.html#225\">App\\Http\\Controllers\\TechnicianAppointmentsController::update<\/a>"],[0,2,"<a href=\"TechnicianAppointmentsController.php.html#299\">App\\Http\\Controllers\\TechnicianAppointmentsController::destroy<\/a>"],[0,3,"<a href=\"TechnicianAppointmentsController.php.html#318\">App\\Http\\Controllers\\TechnicianAppointmentsController::viewTechnicianCalendar<\/a>"],[0,3,"<a href=\"TechnicianAppointmentsController.php.html#387\">App\\Http\\Controllers\\TechnicianAppointmentsController::getAvailabilityDates<\/a>"],[0,7,"<a href=\"TechnicianController.php.html#31\">App\\Http\\Controllers\\TechnicianController::updateWorkingHours<\/a>"],[0,4,"<a href=\"TechnicianController.php.html#97\">App\\Http\\Controllers\\TechnicianController::importWorkingHours<\/a>"],[0,15,"<a href=\"TechnicianController.php.html#130\">App\\Http\\Controllers\\TechnicianController::updateTechnicianSkills<\/a>"],[0,4,"<a href=\"TechnicianController.php.html#272\">App\\Http\\Controllers\\TechnicianController::importTechnicianSkills<\/a>"],[100,1,"<a href=\"UserController.php.html#70\">App\\Http\\Controllers\\UserController::__construct<\/a>"],[0,4,"<a href=\"UserController.php.html#84\">App\\Http\\Controllers\\UserController::index<\/a>"],[0,9,"<a href=\"UserController.php.html#129\">App\\Http\\Controllers\\UserController::store<\/a>"],[0,1,"<a href=\"UserController.php.html#265\">App\\Http\\Controllers\\UserController::show<\/a>"],[0,10,"<a href=\"UserController.php.html#281\">App\\Http\\Controllers\\UserController::update<\/a>"],[0,7,"<a href=\"UserController.php.html#395\">App\\Http\\Controllers\\UserController::destroy<\/a>"],[0,5,"<a href=\"UserController.php.html#450\">App\\Http\\Controllers\\UserController::filters<\/a>"],[0,8,"<a href=\"UserController.php.html#492\">App\\Http\\Controllers\\UserController::updateCognitoUserDetails<\/a>"],[0,2,"<a href=\"UserController.php.html#551\">App\\Http\\Controllers\\UserController::updateNotificationSubscription<\/a>"],[0,12,"<a href=\"UserController.php.html#580\">App\\Http\\Controllers\\UserController::updateUserStatus<\/a>"],[0,6,"<a href=\"UserController.php.html#661\">App\\Http\\Controllers\\UserController::resetUserPassword<\/a>"],[0,9,"<a href=\"UserController.php.html#700\">App\\Http\\Controllers\\UserController::getGroupView<\/a>"],[0,5,"<a href=\"UserController.php.html#748\">App\\Http\\Controllers\\UserController::statusBasedGroupData<\/a>"],[0,6,"<a href=\"UserController.php.html#801\">App\\Http\\Controllers\\UserController::roleBasedGroupData<\/a>"],[0,8,"<a href=\"Vendor\/VendorOnboardingController.php.html#50\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::sendVendorOnboardingLink<\/a>"],[0,8,"<a href=\"Vendor\/VendorOnboardingController.php.html#141\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setBasicInfo<\/a>"],[0,5,"<a href=\"Vendor\/VendorOnboardingController.php.html#222\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::getVendorOnboarding<\/a>"],[0,6,"<a href=\"Vendor\/VendorOnboardingController.php.html#257\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::getStatus<\/a>"],[0,14,"<a href=\"Vendor\/VendorOnboardingController.php.html#331\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setStatus<\/a>"],[0,6,"<a href=\"Vendor\/VendorOnboardingController.php.html#407\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setServices<\/a>"],[0,6,"<a href=\"Vendor\/VendorOnboardingController.php.html#467\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setServiceAreas<\/a>"],[0,6,"<a href=\"Vendor\/VendorOnboardingController.php.html#535\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::processServices<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingController.php.html#585\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::minutesToDaysSimple<\/a>"],[0,4,"<a href=\"Vendor\/VendorOnboardingLookupController.php.html#25\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getLookups<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingLookupController.php.html#50\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getProblemCategory<\/a>"],[0,2,"<a href=\"Vendor\/VendorOnboardingLookupController.php.html#74\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getLocationApiKey<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingLookupController.php.html#87\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getOnboardingStatuses<\/a>"],[0,1,"<a href=\"Vendor\/VendorOnboardingLookupController.php.html#96\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getStates<\/a>"],[0,3,"<a href=\"Vendor\/WorkOrderController.php.html#35\">App\\Http\\Controllers\\Vendor\\WorkOrderController::index<\/a>"],[0,3,"<a href=\"Vendor\/WorkOrderController.php.html#77\">App\\Http\\Controllers\\Vendor\\WorkOrderController::show<\/a>"],[0,1,"<a href=\"Vendor\/WorkOrderController.php.html#101\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getWorkOrderRelations<\/a>"],[0,1,"<a href=\"Vendor\/WorkOrderController.php.html#191\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getWorkOrderSelectFields<\/a>"],[0,1,"<a href=\"Vendor\/WorkOrderController.php.html#227\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getTabTotalCounts<\/a>"],[0,2,"<a href=\"Vendor\/WorkOrderController.php.html#261\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getSkippedMediaTypes<\/a>"],[0,1,"<a href=\"Vendor\/WorkOrderController.php.html#278\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getIssueMediaTypes<\/a>"],[0,1,"<a href=\"Vendor\/WorkOrderController.php.html#288\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getShowEagerLoadRelations<\/a>"],[100,1,"<a href=\"VendorController.php.html#40\">App\\Http\\Controllers\\VendorController::__construct<\/a>"],[0,4,"<a href=\"VendorController.php.html#54\">App\\Http\\Controllers\\VendorController::index<\/a>"],[0,10,"<a href=\"VendorController.php.html#99\">App\\Http\\Controllers\\VendorController::store<\/a>"],[0,4,"<a href=\"VendorController.php.html#234\">App\\Http\\Controllers\\VendorController::show<\/a>"],[0,3,"<a href=\"VendorController.php.html#276\">App\\Http\\Controllers\\VendorController::storeVendor<\/a>"],[0,6,"<a href=\"ViewController.php.html#50\">App\\Http\\Controllers\\ViewController::index<\/a>"],[0,5,"<a href=\"ViewController.php.html#117\">App\\Http\\Controllers\\ViewController::store<\/a>"],[0,3,"<a href=\"ViewController.php.html#168\">App\\Http\\Controllers\\ViewController::update<\/a>"],[0,5,"<a href=\"ViewController.php.html#196\">App\\Http\\Controllers\\ViewController::destroy<\/a>"],[0,5,"<a href=\"ViewController.php.html#247\">App\\Http\\Controllers\\ViewController::setAsDefault<\/a>"],[0,3,"<a href=\"ViewController.php.html#297\">App\\Http\\Controllers\\ViewController::pinView<\/a>"],[0,3,"<a href=\"ViewController.php.html#331\">App\\Http\\Controllers\\ViewController::rename<\/a>"],[0,5,"<a href=\"ViewController.php.html#355\">App\\Http\\Controllers\\ViewController::duplicate<\/a>"],[0,6,"<a href=\"ViewController.php.html#404\">App\\Http\\Controllers\\ViewController::getViewConfig<\/a>"],[0,11,"<a href=\"ViewController.php.html#446\">App\\Http\\Controllers\\ViewController::getViewCount<\/a>"],[0,1,"<a href=\"ViewController.php.html#537\">App\\Http\\Controllers\\ViewController::workOrderViewType<\/a>"],[0,1,"<a href=\"WebhookCallsController.php.html#14\">App\\Http\\Controllers\\WebhookCallsController::index<\/a>"],[0,1,"<a href=\"WebhookCallsController.php.html#22\">App\\Http\\Controllers\\WebhookCallsController::show<\/a>"],[0,5,"<a href=\"WebhookController.php.html#36\">App\\Http\\Controllers\\WebhookController::__invoke<\/a>"],[0,1,"<a href=\"WebhookController.php.html#99\">App\\Http\\Controllers\\WebhookController::invalidAction<\/a>"],[0,4,"<a href=\"WebhookController.php.html#112\">App\\Http\\Controllers\\WebhookController::validateQuoteApprovePayload<\/a>"],[0,4,"<a href=\"WebhookController.php.html#151\">App\\Http\\Controllers\\WebhookController::validateQuoteRejectPayload<\/a>"],[0,4,"<a href=\"WebhookController.php.html#179\">App\\Http\\Controllers\\WebhookController::validateQuoteExpirePayload<\/a>"],[0,4,"<a href=\"WebhookController.php.html#207\">App\\Http\\Controllers\\WebhookController::validateQuoteUpdatePayload<\/a>"],[0,5,"<a href=\"WebhookController.php.html#261\">App\\Http\\Controllers\\WebhookController::validateQuoteRestorePayload<\/a>"],[0,4,"<a href=\"WebhookController.php.html#289\">App\\Http\\Controllers\\WebhookController::validateQuoteSubmittedForApprovalPayload<\/a>"],[0,6,"<a href=\"WorkOrder\/WorkOrderIssueController.php.html#22\">App\\Http\\Controllers\\WorkOrder\\WorkOrderIssueController::show<\/a>"],[0,6,"<a href=\"WorkOrderActivityLogController.php.html#27\">App\\Http\\Controllers\\WorkOrderActivityLogController::index<\/a>"],[0,3,"<a href=\"WorkOrderActivityLogController.php.html#99\">App\\Http\\Controllers\\WorkOrderActivityLogController::paginate<\/a>"],[0,8,"<a href=\"WorkOrderAssigneeController.php.html#25\">App\\Http\\Controllers\\WorkOrderAssigneeController::store<\/a>"],[0,5,"<a href=\"WorkOrderAssigneeController.php.html#88\">App\\Http\\Controllers\\WorkOrderAssigneeController::destroy<\/a>"],[0,4,"<a href=\"WorkOrderController.php.html#124\">App\\Http\\Controllers\\WorkOrderController::index<\/a>"],[0,8,"<a href=\"WorkOrderController.php.html#168\">App\\Http\\Controllers\\WorkOrderController::store<\/a>"],[0,6,"<a href=\"WorkOrderController.php.html#226\">App\\Http\\Controllers\\WorkOrderController::show<\/a>"],[0,6,"<a href=\"WorkOrderController.php.html#274\">App\\Http\\Controllers\\WorkOrderController::update<\/a>"],[0,3,"<a href=\"WorkOrderController.php.html#321\">App\\Http\\Controllers\\WorkOrderController::destroy<\/a>"],[0,15,"<a href=\"WorkOrderController.php.html#350\">App\\Http\\Controllers\\WorkOrderController::getGroupView<\/a>"],[0,20,"<a href=\"WorkOrderController.php.html#416\">App\\Http\\Controllers\\WorkOrderController::getFilterValues<\/a>"],[0,3,"<a href=\"WorkOrderController.php.html#566\">App\\Http\\Controllers\\WorkOrderController::updatePriority<\/a>"],[0,2,"<a href=\"WorkOrderController.php.html#608\">App\\Http\\Controllers\\WorkOrderController::updateDueDate<\/a>"],[0,2,"<a href=\"WorkOrderController.php.html#633\">App\\Http\\Controllers\\WorkOrderController::deleteDueDate<\/a>"],[0,4,"<a href=\"WorkOrderController.php.html#657\">App\\Http\\Controllers\\WorkOrderController::updateAccessMethod<\/a>"],[0,6,"<a href=\"WorkOrderController.php.html#697\">App\\Http\\Controllers\\WorkOrderController::updateResidentInfo<\/a>"],[0,7,"<a href=\"WorkOrderController.php.html#763\">App\\Http\\Controllers\\WorkOrderController::updatePropertyAddress<\/a>"],[0,4,"<a href=\"WorkOrderController.php.html#831\">App\\Http\\Controllers\\WorkOrderController::getCount<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#867\">App\\Http\\Controllers\\WorkOrderController::handleIndexException<\/a>"],[0,11,"<a href=\"WorkOrderController.php.html#893\">App\\Http\\Controllers\\WorkOrderController::statusBasedGroupData<\/a>"],[0,9,"<a href=\"WorkOrderController.php.html#961\">App\\Http\\Controllers\\WorkOrderController::priorityBasedGroupData<\/a>"],[0,7,"<a href=\"WorkOrderController.php.html#1022\">App\\Http\\Controllers\\WorkOrderController::categoryBasedGroupData<\/a>"],[0,8,"<a href=\"WorkOrderController.php.html#1092\">App\\Http\\Controllers\\WorkOrderController::assigneeBasedGroupData<\/a>"],[0,8,"<a href=\"WorkOrderController.php.html#1153\">App\\Http\\Controllers\\WorkOrderController::tagBasedGroupData<\/a>"],[0,8,"<a href=\"WorkOrderController.php.html#1214\">App\\Http\\Controllers\\WorkOrderController::healthScoreBaseGroupData<\/a>"],[0,8,"<a href=\"WorkOrderController.php.html#1273\">App\\Http\\Controllers\\WorkOrderController::technicianBasedGroupData<\/a>"],[0,4,"<a href=\"WorkOrderController.php.html#1330\">App\\Http\\Controllers\\WorkOrderController::tripSummary<\/a>"],[0,2,"<a href=\"WorkOrderController.php.html#1382\">App\\Http\\Controllers\\WorkOrderController::getTripDetails<\/a>"],[0,14,"<a href=\"WorkOrderController.php.html#1431\">App\\Http\\Controllers\\WorkOrderController::markAsComplete<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#1545\">App\\Http\\Controllers\\WorkOrderController::loadWorkOrderRelationsForWeb<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#1666\">App\\Http\\Controllers\\WorkOrderController::getWorkOrderRelations<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#1704\">App\\Http\\Controllers\\WorkOrderController::getWorkOrderSelectFields<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#1827\">App\\Http\\Controllers\\WorkOrderController::getServiceCallSelectFieldsForApp<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#1860\">App\\Http\\Controllers\\WorkOrderController::getServiceCallRelationsForApp<\/a>"],[0,2,"<a href=\"WorkOrderController.php.html#1875\">App\\Http\\Controllers\\WorkOrderController::shouldListForTechnicianMobile<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#1885\">App\\Http\\Controllers\\WorkOrderController::listWorkOrdersForTechnicianMobile<\/a>"],[0,2,"<a href=\"WorkOrderController.php.html#1933\">App\\Http\\Controllers\\WorkOrderController::getWorkOrderRelationsForMobile<\/a>"],[0,2,"<a href=\"WorkOrderController.php.html#1981\">App\\Http\\Controllers\\WorkOrderController::getServiceRequestColumnForApp<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#2005\">App\\Http\\Controllers\\WorkOrderController::getServiceRequestRelationsForApp<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#2017\">App\\Http\\Controllers\\WorkOrderController::getWorkOrderIssuesColumnForApp<\/a>"],[0,2,"<a href=\"WorkOrderController.php.html#2032\">App\\Http\\Controllers\\WorkOrderController::getWorkOrderIssuesRelationsForApp<\/a>"],[0,20,"<a href=\"WorkOrderMediaController.php.html#48\">App\\Http\\Controllers\\WorkOrderMediaController::store<\/a>"],[0,6,"<a href=\"WorkOrderMediaController.php.html#199\">App\\Http\\Controllers\\WorkOrderMediaController::show<\/a>"],[0,5,"<a href=\"WorkOrderMediaController.php.html#240\">App\\Http\\Controllers\\WorkOrderMediaController::destroy<\/a>"],[0,11,"<a href=\"WorkOrderMediaController.php.html#282\">App\\Http\\Controllers\\WorkOrderMediaController::uploadOriginalMedia<\/a>"],[0,7,"<a href=\"WorkOrderMediaController.php.html#353\">App\\Http\\Controllers\\WorkOrderMediaController::validateMediaLimit<\/a>"],[0,5,"<a href=\"WorkOrderNoteController.php.html#34\">App\\Http\\Controllers\\WorkOrderNoteController::index<\/a>"],[0,5,"<a href=\"WorkOrderNoteController.php.html#74\">App\\Http\\Controllers\\WorkOrderNoteController::store<\/a>"],[0,6,"<a href=\"WorkOrderNoteController.php.html#128\">App\\Http\\Controllers\\WorkOrderNoteController::update<\/a>"],[0,6,"<a href=\"WorkOrderNoteController.php.html#186\">App\\Http\\Controllers\\WorkOrderNoteController::destroy<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

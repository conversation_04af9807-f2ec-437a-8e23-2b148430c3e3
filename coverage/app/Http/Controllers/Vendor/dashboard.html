<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Http/Controllers/Vendor</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Controllers</a></li>
         <li class="breadcrumb-item"><a href="index.html">Vendor</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="VendorOnboardingController.php.html#40">App\Http\Controllers\Vendor\VendorOnboardingController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingLookupController.php.html#23">App\Http\Controllers\Vendor\VendorOnboardingLookupController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#27">App\Http\Controllers\Vendor\WorkOrderController</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="VendorOnboardingController.php.html#40">App\Http\Controllers\Vendor\VendorOnboardingController</a></td><td class="text-right">3660</td></tr>
       <tr><td><a href="WorkOrderController.php.html#27">App\Http\Controllers\Vendor\WorkOrderController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="VendorOnboardingLookupController.php.html#23">App\Http\Controllers\Vendor\VendorOnboardingLookupController</a></td><td class="text-right">90</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="VendorOnboardingController.php.html#50"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::sendVendorOnboardingLink">sendVendorOnboardingLink</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#141"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setBasicInfo">setBasicInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#222"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::getVendorOnboarding">getVendorOnboarding</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#257"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::getStatus">getStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#331"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setStatus">setStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#407"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setServices">setServices</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#467"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setServiceAreas">setServiceAreas</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#535"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::processServices">processServices</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#585"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::minutesToDaysSimple">minutesToDaysSimple</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingLookupController.php.html#25"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLookups">getLookups</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingLookupController.php.html#50"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getProblemCategory">getProblemCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingLookupController.php.html#74"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLocationApiKey">getLocationApiKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingLookupController.php.html#87"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getOnboardingStatuses">getOnboardingStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingLookupController.php.html#96"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getStates">getStates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#35"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#77"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#101"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getWorkOrderRelations">getWorkOrderRelations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#191"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getWorkOrderSelectFields">getWorkOrderSelectFields</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#227"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getTabTotalCounts">getTabTotalCounts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#261"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getSkippedMediaTypes">getSkippedMediaTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#278"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getIssueMediaTypes">getIssueMediaTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderController.php.html#288"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getShowEagerLoadRelations">getShowEagerLoadRelations</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="VendorOnboardingController.php.html#331"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setStatus">setStatus</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#50"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::sendVendorOnboardingLink">sendVendorOnboardingLink</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#141"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setBasicInfo">setBasicInfo</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#257"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::getStatus">getStatus</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#407"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setServices">setServices</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#467"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::setServiceAreas">setServiceAreas</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#535"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::processServices">processServices</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="VendorOnboardingController.php.html#222"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingController::getVendorOnboarding">getVendorOnboarding</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="VendorOnboardingLookupController.php.html#25"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLookups">getLookups</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderController.php.html#35"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::index">index</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderController.php.html#77"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::show">show</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorOnboardingLookupController.php.html#74"><abbr title="App\Http\Controllers\Vendor\VendorOnboardingLookupController::getLocationApiKey">getLocationApiKey</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderController.php.html#261"><abbr title="App\Http\Controllers\Vendor\WorkOrderController::getSkippedMediaTypes">getSkippedMediaTypes</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:04:51 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([3,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([22,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,60,"<a href=\"VendorOnboardingController.php.html#40\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController<\/a>"],[0,9,"<a href=\"VendorOnboardingLookupController.php.html#23\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController<\/a>"],[0,13,"<a href=\"WorkOrderController.php.html#27\">App\\Http\\Controllers\\Vendor\\WorkOrderController<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,8,"<a href=\"VendorOnboardingController.php.html#50\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::sendVendorOnboardingLink<\/a>"],[0,8,"<a href=\"VendorOnboardingController.php.html#141\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setBasicInfo<\/a>"],[0,5,"<a href=\"VendorOnboardingController.php.html#222\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::getVendorOnboarding<\/a>"],[0,6,"<a href=\"VendorOnboardingController.php.html#257\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::getStatus<\/a>"],[0,14,"<a href=\"VendorOnboardingController.php.html#331\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setStatus<\/a>"],[0,6,"<a href=\"VendorOnboardingController.php.html#407\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setServices<\/a>"],[0,6,"<a href=\"VendorOnboardingController.php.html#467\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::setServiceAreas<\/a>"],[0,6,"<a href=\"VendorOnboardingController.php.html#535\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::processServices<\/a>"],[0,1,"<a href=\"VendorOnboardingController.php.html#585\">App\\Http\\Controllers\\Vendor\\VendorOnboardingController::minutesToDaysSimple<\/a>"],[0,4,"<a href=\"VendorOnboardingLookupController.php.html#25\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getLookups<\/a>"],[0,1,"<a href=\"VendorOnboardingLookupController.php.html#50\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getProblemCategory<\/a>"],[0,2,"<a href=\"VendorOnboardingLookupController.php.html#74\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getLocationApiKey<\/a>"],[0,1,"<a href=\"VendorOnboardingLookupController.php.html#87\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getOnboardingStatuses<\/a>"],[0,1,"<a href=\"VendorOnboardingLookupController.php.html#96\">App\\Http\\Controllers\\Vendor\\VendorOnboardingLookupController::getStates<\/a>"],[0,3,"<a href=\"WorkOrderController.php.html#35\">App\\Http\\Controllers\\Vendor\\WorkOrderController::index<\/a>"],[0,3,"<a href=\"WorkOrderController.php.html#77\">App\\Http\\Controllers\\Vendor\\WorkOrderController::show<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#101\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getWorkOrderRelations<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#191\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getWorkOrderSelectFields<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#227\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getTabTotalCounts<\/a>"],[0,2,"<a href=\"WorkOrderController.php.html#261\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getSkippedMediaTypes<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#278\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getIssueMediaTypes<\/a>"],[0,1,"<a href=\"WorkOrderController.php.html#288\">App\\Http\\Controllers\\Vendor\\WorkOrderController::getShowEagerLoadRelations<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Http/Controllers/Developer</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Controllers</a></li>
         <li class="breadcrumb-item"><a href="index.html">Developer</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="HealthCheckController.php.html#14">App\Http\Controllers\Developer\HealthCheckController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogViewerController.php.html#19">App\Http\Controllers\Developer\LogViewerController</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="HealthCheckController.php.html#14">App\Http\Controllers\Developer\HealthCheckController</a></td><td class="text-right">210</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="HealthCheckController.php.html#26"><abbr title="App\Http\Controllers\Developer\HealthCheckController::basicCheck">basicCheck</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthCheckController.php.html#38"><abbr title="App\Http\Controllers\Developer\HealthCheckController::utilitiesCheck">utilitiesCheck</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthCheckController.php.html#67"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkDatabaseConnection">checkDatabaseConnection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthCheckController.php.html#88"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkCacheConnection">checkCacheConnection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HealthCheckController.php.html#110"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkLogging">checkLogging</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogViewerController.php.html#24"><abbr title="App\Http\Controllers\Developer\LogViewerController::requestLogs">requestLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogViewerController.php.html#34"><abbr title="App\Http\Controllers\Developer\LogViewerController::requestLogShow">requestLogShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogViewerController.php.html#42"><abbr title="App\Http\Controllers\Developer\LogViewerController::incomingWebhookLogs">incomingWebhookLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogViewerController.php.html#50"><abbr title="App\Http\Controllers\Developer\LogViewerController::incomingWebhookLogShow">incomingWebhookLogShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogViewerController.php.html#58"><abbr title="App\Http\Controllers\Developer\LogViewerController::outgoingWebhookLogs">outgoingWebhookLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogViewerController.php.html#66"><abbr title="App\Http\Controllers\Developer\LogViewerController::outgoingWebhookLogShow">outgoingWebhookLogShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogViewerController.php.html#76"><abbr title="App\Http\Controllers\Developer\LogViewerController::developerAlerts">developerAlerts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogViewerController.php.html#86"><abbr title="App\Http\Controllers\Developer\LogViewerController::developerAlertShow">developerAlertShow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogViewerController.php.html#93"><abbr title="App\Http\Controllers\Developer\LogViewerController::vendorPublicApiLogs">vendorPublicApiLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogViewerController.php.html#98"><abbr title="App\Http\Controllers\Developer\LogViewerController::vendorPublicApiLogShow">vendorPublicApiLogShow</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="HealthCheckController.php.html#38"><abbr title="App\Http\Controllers\Developer\HealthCheckController::utilitiesCheck">utilitiesCheck</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="HealthCheckController.php.html#67"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkDatabaseConnection">checkDatabaseConnection</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HealthCheckController.php.html#88"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkCacheConnection">checkCacheConnection</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="HealthCheckController.php.html#110"><abbr title="App\Http\Controllers\Developer\HealthCheckController::checkLogging">checkLogging</abbr></a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:04:51 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([2,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([15,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,14,"<a href=\"HealthCheckController.php.html#14\">App\\Http\\Controllers\\Developer\\HealthCheckController<\/a>"],[0,10,"<a href=\"LogViewerController.php.html#19\">App\\Http\\Controllers\\Developer\\LogViewerController<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"HealthCheckController.php.html#26\">App\\Http\\Controllers\\Developer\\HealthCheckController::basicCheck<\/a>"],[0,4,"<a href=\"HealthCheckController.php.html#38\">App\\Http\\Controllers\\Developer\\HealthCheckController::utilitiesCheck<\/a>"],[0,3,"<a href=\"HealthCheckController.php.html#67\">App\\Http\\Controllers\\Developer\\HealthCheckController::checkDatabaseConnection<\/a>"],[0,3,"<a href=\"HealthCheckController.php.html#88\">App\\Http\\Controllers\\Developer\\HealthCheckController::checkCacheConnection<\/a>"],[0,3,"<a href=\"HealthCheckController.php.html#110\">App\\Http\\Controllers\\Developer\\HealthCheckController::checkLogging<\/a>"],[0,1,"<a href=\"LogViewerController.php.html#24\">App\\Http\\Controllers\\Developer\\LogViewerController::requestLogs<\/a>"],[0,1,"<a href=\"LogViewerController.php.html#34\">App\\Http\\Controllers\\Developer\\LogViewerController::requestLogShow<\/a>"],[0,1,"<a href=\"LogViewerController.php.html#42\">App\\Http\\Controllers\\Developer\\LogViewerController::incomingWebhookLogs<\/a>"],[0,1,"<a href=\"LogViewerController.php.html#50\">App\\Http\\Controllers\\Developer\\LogViewerController::incomingWebhookLogShow<\/a>"],[0,1,"<a href=\"LogViewerController.php.html#58\">App\\Http\\Controllers\\Developer\\LogViewerController::outgoingWebhookLogs<\/a>"],[0,1,"<a href=\"LogViewerController.php.html#66\">App\\Http\\Controllers\\Developer\\LogViewerController::outgoingWebhookLogShow<\/a>"],[0,1,"<a href=\"LogViewerController.php.html#76\">App\\Http\\Controllers\\Developer\\LogViewerController::developerAlerts<\/a>"],[0,1,"<a href=\"LogViewerController.php.html#86\">App\\Http\\Controllers\\Developer\\LogViewerController::developerAlertShow<\/a>"],[0,1,"<a href=\"LogViewerController.php.html#93\">App\\Http\\Controllers\\Developer\\LogViewerController::vendorPublicApiLogs<\/a>"],[0,1,"<a href=\"LogViewerController.php.html#98\">App\\Http\\Controllers\\Developer\\LogViewerController::vendorPublicApiLogShow<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

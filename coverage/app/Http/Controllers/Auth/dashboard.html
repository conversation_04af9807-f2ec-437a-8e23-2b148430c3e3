<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Http/Controllers/Auth</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Controllers</a></li>
         <li class="breadcrumb-item"><a href="index.html">Auth</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SSOController.php.html#22">App\Http\Controllers\Auth\SSOController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#56">App\Http\Controllers\Auth\RegisterController</a></td><td class="text-right">1%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="RegisterController.php.html#56">App\Http\Controllers\Auth\RegisterController</a></td><td class="text-right">6075</td></tr>
       <tr><td><a href="SSOController.php.html#22">App\Http\Controllers\Auth\SSOController</a></td><td class="text-right">90</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="RegisterController.php.html#87"><abbr title="App\Http\Controllers\Auth\RegisterController::forgotPassword">forgotPassword</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#128"><abbr title="App\Http\Controllers\Auth\RegisterController::resetPassword">resetPassword</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#145"><abbr title="App\Http\Controllers\Auth\RegisterController::verify">verify</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#167"><abbr title="App\Http\Controllers\Auth\RegisterController::searchAddress">searchAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#197"><abbr title="App\Http\Controllers\Auth\RegisterController::getVendorOnboardingInvite">getVendorOnboardingInvite</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#333"><abbr title="App\Http\Controllers\Auth\RegisterController::setVendorPassword">setVendorPassword</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#402"><abbr title="App\Http\Controllers\Auth\RegisterController::signup">signup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#510"><abbr title="App\Http\Controllers\Auth\RegisterController::createOrganization">createOrganization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#536"><abbr title="App\Http\Controllers\Auth\RegisterController::setupUserPool">setupUserPool</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#642"><abbr title="App\Http\Controllers\Auth\RegisterController::findLoginConfig">findLoginConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#709"><abbr title="App\Http\Controllers\Auth\RegisterController::findProviderLoginConfig">findProviderLoginConfig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#726"><abbr title="App\Http\Controllers\Auth\RegisterController::authenticateClientCredential">authenticateClientCredential</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#786"><abbr title="App\Http\Controllers\Auth\RegisterController::createUserPoolClient">createUserPoolClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#823"><abbr title="App\Http\Controllers\Auth\RegisterController::createUserPool">createUserPool</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#890"><abbr title="App\Http\Controllers\Auth\RegisterController::syncUserRolePermissions">syncUserRolePermissions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#960"><abbr title="App\Http\Controllers\Auth\RegisterController::sendEmail">sendEmail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#975"><abbr title="App\Http\Controllers\Auth\RegisterController::validateRequest">validateRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#988"><abbr title="App\Http\Controllers\Auth\RegisterController::validateSignedUrl">validateSignedUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#1013"><abbr title="App\Http\Controllers\Auth\RegisterController::validatePassword">validatePassword</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegisterController.php.html#1041"><abbr title="App\Http\Controllers\Auth\RegisterController::processPasswordReset">processPasswordReset</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SSOController.php.html#26"><abbr title="App\Http\Controllers\Auth\SSOController::validateUser">validateUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SSOController.php.html#76"><abbr title="App\Http\Controllers\Auth\SSOController::getCognitoUserDetails">getCognitoUserDetails</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="RegisterController.php.html#726"><abbr title="App\Http\Controllers\Auth\RegisterController::authenticateClientCredential">authenticateClientCredential</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="RegisterController.php.html#402"><abbr title="App\Http\Controllers\Auth\RegisterController::signup">signup</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="RegisterController.php.html#333"><abbr title="App\Http\Controllers\Auth\RegisterController::setVendorPassword">setVendorPassword</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="RegisterController.php.html#988"><abbr title="App\Http\Controllers\Auth\RegisterController::validateSignedUrl">validateSignedUrl</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="RegisterController.php.html#87"><abbr title="App\Http\Controllers\Auth\RegisterController::forgotPassword">forgotPassword</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="RegisterController.php.html#536"><abbr title="App\Http\Controllers\Auth\RegisterController::setupUserPool">setupUserPool</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="RegisterController.php.html#642"><abbr title="App\Http\Controllers\Auth\RegisterController::findLoginConfig">findLoginConfig</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SSOController.php.html#76"><abbr title="App\Http\Controllers\Auth\SSOController::getCognitoUserDetails">getCognitoUserDetails</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="RegisterController.php.html#197"><abbr title="App\Http\Controllers\Auth\RegisterController::getVendorOnboardingInvite">getVendorOnboardingInvite</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RegisterController.php.html#1013"><abbr title="App\Http\Controllers\Auth\RegisterController::validatePassword">validatePassword</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="SSOController.php.html#26"><abbr title="App\Http\Controllers\Auth\SSOController::validateUser">validateUser</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RegisterController.php.html#128"><abbr title="App\Http\Controllers\Auth\RegisterController::resetPassword">resetPassword</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RegisterController.php.html#145"><abbr title="App\Http\Controllers\Auth\RegisterController::verify">verify</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RegisterController.php.html#167"><abbr title="App\Http\Controllers\Auth\RegisterController::searchAddress">searchAddress</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RegisterController.php.html#510"><abbr title="App\Http\Controllers\Auth\RegisterController::createOrganization">createOrganization</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RegisterController.php.html#975"><abbr title="App\Http\Controllers\Auth\RegisterController::validateRequest">validateRequest</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RegisterController.php.html#786"><abbr title="App\Http\Controllers\Auth\RegisterController::createUserPoolClient">createUserPoolClient</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RegisterController.php.html#890"><abbr title="App\Http\Controllers\Auth\RegisterController::syncUserRolePermissions">syncUserRolePermissions</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RegisterController.php.html#960"><abbr title="App\Http\Controllers\Auth\RegisterController::sendEmail">sendEmail</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RegisterController.php.html#1041"><abbr title="App\Http\Controllers\Auth\RegisterController::processPasswordReset">processPasswordReset</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:04:51 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([1,1,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([22,0,0,0,0,0,0,0,0,0,0,1], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[1.3215859030837005,79,"<a href=\"RegisterController.php.html#56\">App\\Http\\Controllers\\Auth\\RegisterController<\/a>"],[0,9,"<a href=\"SSOController.php.html#22\">App\\Http\\Controllers\\Auth\\SSOController<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[100,1,"<a href=\"RegisterController.php.html#68\">App\\Http\\Controllers\\Auth\\RegisterController::__construct<\/a>"],[0,5,"<a href=\"RegisterController.php.html#87\">App\\Http\\Controllers\\Auth\\RegisterController::forgotPassword<\/a>"],[0,3,"<a href=\"RegisterController.php.html#128\">App\\Http\\Controllers\\Auth\\RegisterController::resetPassword<\/a>"],[0,3,"<a href=\"RegisterController.php.html#145\">App\\Http\\Controllers\\Auth\\RegisterController::verify<\/a>"],[0,3,"<a href=\"RegisterController.php.html#167\">App\\Http\\Controllers\\Auth\\RegisterController::searchAddress<\/a>"],[0,4,"<a href=\"RegisterController.php.html#197\">App\\Http\\Controllers\\Auth\\RegisterController::getVendorOnboardingInvite<\/a>"],[0,6,"<a href=\"RegisterController.php.html#333\">App\\Http\\Controllers\\Auth\\RegisterController::setVendorPassword<\/a>"],[0,8,"<a href=\"RegisterController.php.html#402\">App\\Http\\Controllers\\Auth\\RegisterController::signup<\/a>"],[0,3,"<a href=\"RegisterController.php.html#510\">App\\Http\\Controllers\\Auth\\RegisterController::createOrganization<\/a>"],[0,5,"<a href=\"RegisterController.php.html#536\">App\\Http\\Controllers\\Auth\\RegisterController::setupUserPool<\/a>"],[0,5,"<a href=\"RegisterController.php.html#642\">App\\Http\\Controllers\\Auth\\RegisterController::findLoginConfig<\/a>"],[0,1,"<a href=\"RegisterController.php.html#709\">App\\Http\\Controllers\\Auth\\RegisterController::findProviderLoginConfig<\/a>"],[0,10,"<a href=\"RegisterController.php.html#726\">App\\Http\\Controllers\\Auth\\RegisterController::authenticateClientCredential<\/a>"],[0,2,"<a href=\"RegisterController.php.html#786\">App\\Http\\Controllers\\Auth\\RegisterController::createUserPoolClient<\/a>"],[0,1,"<a href=\"RegisterController.php.html#823\">App\\Http\\Controllers\\Auth\\RegisterController::createUserPool<\/a>"],[0,2,"<a href=\"RegisterController.php.html#890\">App\\Http\\Controllers\\Auth\\RegisterController::syncUserRolePermissions<\/a>"],[0,2,"<a href=\"RegisterController.php.html#960\">App\\Http\\Controllers\\Auth\\RegisterController::sendEmail<\/a>"],[0,3,"<a href=\"RegisterController.php.html#975\">App\\Http\\Controllers\\Auth\\RegisterController::validateRequest<\/a>"],[0,6,"<a href=\"RegisterController.php.html#988\">App\\Http\\Controllers\\Auth\\RegisterController::validateSignedUrl<\/a>"],[0,4,"<a href=\"RegisterController.php.html#1013\">App\\Http\\Controllers\\Auth\\RegisterController::validatePassword<\/a>"],[0,2,"<a href=\"RegisterController.php.html#1041\">App\\Http\\Controllers\\Auth\\RegisterController::processPasswordReset<\/a>"],[0,4,"<a href=\"SSOController.php.html#26\">App\\Http\\Controllers\\Auth\\SSOController::validateUser<\/a>"],[0,5,"<a href=\"SSOController.php.html#76\">App\\Http\\Controllers\\Auth\\SSOController::getCognitoUserDetails<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

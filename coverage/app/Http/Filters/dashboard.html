<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Http/Filters</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="index.html">Filters</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="QueryFilter.php.html#12">App\Http\Filters\QueryFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilter.php.html#10">App\Http\Filters\QuoteListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleListFilter.php.html#5">App\Http\Filters\RoleListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilter.php.html#10">App\Http\Filters\ServiceRequestListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagListFilter.php.html#8">App\Http\Filters\TagListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserListFilter.php.html#11">App\Http\Filters\UserListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorListFilter.php.html#5">App\Http\Filters\VendorListFilter</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilter.php.html#10">App\Http\Filters\WorkOrderListFilter</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderListFilter.php.html#10">App\Http\Filters\WorkOrderListFilter</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="QueryFilter.php.html#12">App\Http\Filters\QueryFilter</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ServiceRequestListFilter.php.html#10">App\Http\Filters\ServiceRequestListFilter</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="QuoteListFilter.php.html#10">App\Http\Filters\QuoteListFilter</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UserListFilter.php.html#11">App\Http\Filters\UserListFilter</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="TagListFilter.php.html#8">App\Http\Filters\TagListFilter</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="RoleListFilter.php.html#5">App\Http\Filters\RoleListFilter</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="QueryFilter.php.html#36"><abbr title="App\Http\Filters\QueryFilter::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryFilter.php.html#49"><abbr title="App\Http\Filters\QueryFilter::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryFilter.php.html#75"><abbr title="App\Http\Filters\QueryFilter::filters">filters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryFilter.php.html#84"><abbr title="App\Http\Filters\QueryFilter::sort">sort</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QueryFilter.php.html#99"><abbr title="App\Http\Filters\QueryFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilter.php.html#34"><abbr title="App\Http\Filters\QuoteListFilter::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilter.php.html#42"><abbr title="App\Http\Filters\QuoteListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilter.php.html#60"><abbr title="App\Http\Filters\QuoteListFilter::workOrderId">workOrderId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilter.php.html#65"><abbr title="App\Http\Filters\QuoteListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteListFilter.php.html#95"><abbr title="App\Http\Filters\QuoteListFilter::filter">filter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleListFilter.php.html#20"><abbr title="App\Http\Filters\RoleListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RoleListFilter.php.html#28"><abbr title="App\Http\Filters\RoleListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilter.php.html#29"><abbr title="App\Http\Filters\ServiceRequestListFilter::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilter.php.html#34"><abbr title="App\Http\Filters\ServiceRequestListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilter.php.html#49"><abbr title="App\Http\Filters\ServiceRequestListFilter::serviceRequestId">serviceRequestId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilter.php.html#54"><abbr title="App\Http\Filters\ServiceRequestListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestListFilter.php.html#84"><abbr title="App\Http\Filters\ServiceRequestListFilter::filter">filter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagListFilter.php.html#22"><abbr title="App\Http\Filters\TagListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagListFilter.php.html#32"><abbr title="App\Http\Filters\TagListFilter::workOrderId">workOrderId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TagListFilter.php.html#44"><abbr title="App\Http\Filters\TagListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserListFilter.php.html#33"><abbr title="App\Http\Filters\UserListFilter::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserListFilter.php.html#41"><abbr title="App\Http\Filters\UserListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserListFilter.php.html#50"><abbr title="App\Http\Filters\UserListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserListFilter.php.html#80"><abbr title="App\Http\Filters\UserListFilter::filter">filter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorListFilter.php.html#10"><abbr title="App\Http\Filters\VendorListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilter.php.html#42"><abbr title="App\Http\Filters\WorkOrderListFilter::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilter.php.html#50"><abbr title="App\Http\Filters\WorkOrderListFilter::search">search</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilter.php.html#69"><abbr title="App\Http\Filters\WorkOrderListFilter::workOrderId">workOrderId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilter.php.html#74"><abbr title="App\Http\Filters\WorkOrderListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderListFilter.php.html#121"><abbr title="App\Http\Filters\WorkOrderListFilter::filter">filter</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderListFilter.php.html#74"><abbr title="App\Http\Filters\WorkOrderListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="QueryFilter.php.html#49"><abbr title="App\Http\Filters\QueryFilter::apply">apply</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequestListFilter.php.html#54"><abbr title="App\Http\Filters\ServiceRequestListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="QuoteListFilter.php.html#65"><abbr title="App\Http\Filters\QuoteListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserListFilter.php.html#50"><abbr title="App\Http\Filters\UserListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QueryFilter.php.html#84"><abbr title="App\Http\Filters\QueryFilter::sort">sort</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RoleListFilter.php.html#28"><abbr title="App\Http\Filters\RoleListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TagListFilter.php.html#32"><abbr title="App\Http\Filters\TagListFilter::workOrderId">workOrderId</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TagListFilter.php.html#44"><abbr title="App\Http\Filters\TagListFilter::prepareForFiltering">prepareForFiltering</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:51:49 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([8,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([30,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,11,"<a href=\"QueryFilter.php.html#12\">App\\Http\\Filters\\QueryFilter<\/a>"],[0,8,"<a href=\"QuoteListFilter.php.html#10\">App\\Http\\Filters\\QuoteListFilter<\/a>"],[0,3,"<a href=\"RoleListFilter.php.html#5\">App\\Http\\Filters\\RoleListFilter<\/a>"],[0,9,"<a href=\"ServiceRequestListFilter.php.html#10\">App\\Http\\Filters\\ServiceRequestListFilter<\/a>"],[0,5,"<a href=\"TagListFilter.php.html#8\">App\\Http\\Filters\\TagListFilter<\/a>"],[0,7,"<a href=\"UserListFilter.php.html#11\">App\\Http\\Filters\\UserListFilter<\/a>"],[0,1,"<a href=\"VendorListFilter.php.html#5\">App\\Http\\Filters\\VendorListFilter<\/a>"],[0,18,"<a href=\"WorkOrderListFilter.php.html#10\">App\\Http\\Filters\\WorkOrderListFilter<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"QueryFilter.php.html#36\">App\\Http\\Filters\\QueryFilter::__construct<\/a>"],[0,6,"<a href=\"QueryFilter.php.html#49\">App\\Http\\Filters\\QueryFilter::apply<\/a>"],[0,1,"<a href=\"QueryFilter.php.html#75\">App\\Http\\Filters\\QueryFilter::filters<\/a>"],[0,2,"<a href=\"QueryFilter.php.html#84\">App\\Http\\Filters\\QueryFilter::sort<\/a>"],[0,1,"<a href=\"QueryFilter.php.html#99\">App\\Http\\Filters\\QueryFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"QuoteListFilter.php.html#34\">App\\Http\\Filters\\QuoteListFilter::__construct<\/a>"],[0,1,"<a href=\"QuoteListFilter.php.html#42\">App\\Http\\Filters\\QuoteListFilter::search<\/a>"],[0,1,"<a href=\"QuoteListFilter.php.html#60\">App\\Http\\Filters\\QuoteListFilter::workOrderId<\/a>"],[0,4,"<a href=\"QuoteListFilter.php.html#65\">App\\Http\\Filters\\QuoteListFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"QuoteListFilter.php.html#95\">App\\Http\\Filters\\QuoteListFilter::filter<\/a>"],[0,1,"<a href=\"RoleListFilter.php.html#20\">App\\Http\\Filters\\RoleListFilter::search<\/a>"],[0,2,"<a href=\"RoleListFilter.php.html#28\">App\\Http\\Filters\\RoleListFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"ServiceRequestListFilter.php.html#29\">App\\Http\\Filters\\ServiceRequestListFilter::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestListFilter.php.html#34\">App\\Http\\Filters\\ServiceRequestListFilter::search<\/a>"],[0,1,"<a href=\"ServiceRequestListFilter.php.html#49\">App\\Http\\Filters\\ServiceRequestListFilter::serviceRequestId<\/a>"],[0,5,"<a href=\"ServiceRequestListFilter.php.html#54\">App\\Http\\Filters\\ServiceRequestListFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"ServiceRequestListFilter.php.html#84\">App\\Http\\Filters\\ServiceRequestListFilter::filter<\/a>"],[0,1,"<a href=\"TagListFilter.php.html#22\">App\\Http\\Filters\\TagListFilter::search<\/a>"],[0,2,"<a href=\"TagListFilter.php.html#32\">App\\Http\\Filters\\TagListFilter::workOrderId<\/a>"],[0,2,"<a href=\"TagListFilter.php.html#44\">App\\Http\\Filters\\TagListFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"UserListFilter.php.html#33\">App\\Http\\Filters\\UserListFilter::__construct<\/a>"],[0,1,"<a href=\"UserListFilter.php.html#41\">App\\Http\\Filters\\UserListFilter::search<\/a>"],[0,4,"<a href=\"UserListFilter.php.html#50\">App\\Http\\Filters\\UserListFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"UserListFilter.php.html#80\">App\\Http\\Filters\\UserListFilter::filter<\/a>"],[0,1,"<a href=\"VendorListFilter.php.html#10\">App\\Http\\Filters\\VendorListFilter::search<\/a>"],[0,1,"<a href=\"WorkOrderListFilter.php.html#42\">App\\Http\\Filters\\WorkOrderListFilter::__construct<\/a>"],[0,1,"<a href=\"WorkOrderListFilter.php.html#50\">App\\Http\\Filters\\WorkOrderListFilter::search<\/a>"],[0,1,"<a href=\"WorkOrderListFilter.php.html#69\">App\\Http\\Filters\\WorkOrderListFilter::workOrderId<\/a>"],[0,14,"<a href=\"WorkOrderListFilter.php.html#74\">App\\Http\\Filters\\WorkOrderListFilter::prepareForFiltering<\/a>"],[0,1,"<a href=\"WorkOrderListFilter.php.html#121\">App\\Http\\Filters\\WorkOrderListFilter::filter<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

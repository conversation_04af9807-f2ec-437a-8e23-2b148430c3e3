<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Http/Resources/Vendor</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Resources</a></li>
         <li class="breadcrumb-item"><a href="index.html">Vendor</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BasicInfoResource.php.html#9">App\Http\Resources\Vendor\BasicInfoResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListResource.php.html#18">App\Http\Resources\Vendor\ListResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProfileResource.php.html#15">App\Http\Resources\Vendor\ProfileResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceAreaResource.php.html#10">App\Http\Resources\Vendor\ServiceAreaResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateVendorResource.php.html#12">App\Http\Resources\Vendor\UpdateVendorResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#10">App\Http\Resources\Vendor\VendorOnboardingResource</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResource.php.html#33">App\Http\Resources\Vendor\WorkOrderResource</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderResource.php.html#33">App\Http\Resources\Vendor\WorkOrderResource</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#10">App\Http\Resources\Vendor\VendorOnboardingResource</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ListResource.php.html#18">App\Http\Resources\Vendor\ListResource</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BasicInfoResource.php.html#11"><abbr title="App\Http\Resources\Vendor\BasicInfoResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ListResource.php.html#25"><abbr title="App\Http\Resources\Vendor\ListResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProfileResource.php.html#22"><abbr title="App\Http\Resources\Vendor\ProfileResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceAreaResource.php.html#12"><abbr title="App\Http\Resources\Vendor\ServiceAreaResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateVendorResource.php.html#19"><abbr title="App\Http\Resources\Vendor\UpdateVendorResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#12"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#28"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::getBasicInfo">getBasicInfo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#51"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::getServiceOffered">getServiceOffered</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#70"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::processService">processService</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#81"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::isValidDiagnosisChain">isValidDiagnosisChain</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#88"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::addUniqueIds">addUniqueIds</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#99"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::addIfNotPresent">addIfNotPresent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResource.php.html#42"><abbr title="App\Http\Resources\Vendor\WorkOrderResource::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResource.php.html#135"><abbr title="App\Http\Resources\Vendor\WorkOrderResource::getTotalTabCount">getTotalTabCount</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResource.php.html#146"><abbr title="App\Http\Resources\Vendor\WorkOrderResource::hasMissingData">hasMissingData</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderResource.php.html#42"><abbr title="App\Http\Resources\Vendor\WorkOrderResource::toArray">toArray</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ListResource.php.html#25"><abbr title="App\Http\Resources\Vendor\ListResource::toArray">toArray</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#51"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::getServiceOffered">getServiceOffered</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#81"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::isValidDiagnosisChain">isValidDiagnosisChain</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderResource.php.html#146"><abbr title="App\Http\Resources\Vendor\WorkOrderResource::hasMissingData">hasMissingData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#12"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::toArray">toArray</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#70"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::processService">processService</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="VendorOnboardingResource.php.html#99"><abbr title="App\Http\Resources\Vendor\VendorOnboardingResource::addIfNotPresent">addIfNotPresent</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderResource.php.html#135"><abbr title="App\Http\Resources\Vendor\WorkOrderResource::getTotalTabCount">getTotalTabCount</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:04:51 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([7,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([15,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BasicInfoResource.php.html#9\">App\\Http\\Resources\\Vendor\\BasicInfoResource<\/a>"],[0,3,"<a href=\"ListResource.php.html#18\">App\\Http\\Resources\\Vendor\\ListResource<\/a>"],[0,1,"<a href=\"ProfileResource.php.html#15\">App\\Http\\Resources\\Vendor\\ProfileResource<\/a>"],[0,1,"<a href=\"ServiceAreaResource.php.html#10\">App\\Http\\Resources\\Vendor\\ServiceAreaResource<\/a>"],[0,1,"<a href=\"UpdateVendorResource.php.html#12\">App\\Http\\Resources\\Vendor\\UpdateVendorResource<\/a>"],[0,14,"<a href=\"VendorOnboardingResource.php.html#10\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource<\/a>"],[0,17,"<a href=\"WorkOrderResource.php.html#33\">App\\Http\\Resources\\Vendor\\WorkOrderResource<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BasicInfoResource.php.html#11\">App\\Http\\Resources\\Vendor\\BasicInfoResource::toArray<\/a>"],[0,3,"<a href=\"ListResource.php.html#25\">App\\Http\\Resources\\Vendor\\ListResource::toArray<\/a>"],[0,1,"<a href=\"ProfileResource.php.html#22\">App\\Http\\Resources\\Vendor\\ProfileResource::toArray<\/a>"],[0,1,"<a href=\"ServiceAreaResource.php.html#12\">App\\Http\\Resources\\Vendor\\ServiceAreaResource::toArray<\/a>"],[0,1,"<a href=\"UpdateVendorResource.php.html#19\">App\\Http\\Resources\\Vendor\\UpdateVendorResource::toArray<\/a>"],[0,2,"<a href=\"VendorOnboardingResource.php.html#12\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::toArray<\/a>"],[0,1,"<a href=\"VendorOnboardingResource.php.html#28\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::getBasicInfo<\/a>"],[0,3,"<a href=\"VendorOnboardingResource.php.html#51\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::getServiceOffered<\/a>"],[0,2,"<a href=\"VendorOnboardingResource.php.html#70\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::processService<\/a>"],[0,3,"<a href=\"VendorOnboardingResource.php.html#81\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::isValidDiagnosisChain<\/a>"],[0,1,"<a href=\"VendorOnboardingResource.php.html#88\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::addUniqueIds<\/a>"],[0,2,"<a href=\"VendorOnboardingResource.php.html#99\">App\\Http\\Resources\\Vendor\\VendorOnboardingResource::addIfNotPresent<\/a>"],[0,12,"<a href=\"WorkOrderResource.php.html#42\">App\\Http\\Resources\\Vendor\\WorkOrderResource::toArray<\/a>"],[0,2,"<a href=\"WorkOrderResource.php.html#135\">App\\Http\\Resources\\Vendor\\WorkOrderResource::getTotalTabCount<\/a>"],[0,3,"<a href=\"WorkOrderResource.php.html#146\">App\\Http\\Resources\\Vendor\\WorkOrderResource::hasMissingData<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

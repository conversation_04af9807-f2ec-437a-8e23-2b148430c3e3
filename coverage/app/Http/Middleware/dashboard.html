<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Http/Middleware</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Http</a></li>
         <li class="breadcrumb-item"><a href="index.html">Middleware</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AppAccessPermissionMiddleware.php.html#15">App\Http\Middleware\AppAccessPermissionMiddleware</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Authenticate.php.html#8">App\Http\Middleware\Authenticate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HorizonBasicAuth.php.html#7">App\Http\Middleware\HorizonBasicAuth</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#19">App\Http\Middleware\LogRequestsToDatabase</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MakeTusMediaFileName.php.html#12">App\Http\Middleware\MakeTusMediaFileName</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MinimumAppVeriosnRequiredMiddleware.php.html#12">App\Http\Middleware\MinimumAppVeriosnRequiredMiddleware</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RedirectIfAuthenticated.php.html#10">App\Http\Middleware\RedirectIfAuthenticated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TrustHosts.php.html#7">App\Http\Middleware\TrustHosts</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCognitoToken.php.html#18">App\Http\Middleware\ValidateCognitoToken</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="LogRequestsToDatabase.php.html#19">App\Http\Middleware\LogRequestsToDatabase</a></td><td class="text-right">1806</td></tr>
       <tr><td><a href="MakeTusMediaFileName.php.html#12">App\Http\Middleware\MakeTusMediaFileName</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ValidateCognitoToken.php.html#18">App\Http\Middleware\ValidateCognitoToken</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="HorizonBasicAuth.php.html#7">App\Http\Middleware\HorizonBasicAuth</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AppAccessPermissionMiddleware.php.html#15">App\Http\Middleware\AppAccessPermissionMiddleware</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="MinimumAppVeriosnRequiredMiddleware.php.html#12">App\Http\Middleware\MinimumAppVeriosnRequiredMiddleware</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RedirectIfAuthenticated.php.html#10">App\Http\Middleware\RedirectIfAuthenticated</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Authenticate.php.html#8">App\Http\Middleware\Authenticate</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AppAccessPermissionMiddleware.php.html#17"><abbr title="App\Http\Middleware\AppAccessPermissionMiddleware::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Authenticate.php.html#13"><abbr title="App\Http\Middleware\Authenticate::redirectTo">redirectTo</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HorizonBasicAuth.php.html#15"><abbr title="App\Http\Middleware\HorizonBasicAuth::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#67"><abbr title="App\Http\Middleware\LogRequestsToDatabase::given">given</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#77"><abbr title="App\Http\Middleware\LogRequestsToDatabase::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#85"><abbr title="App\Http\Middleware\LogRequestsToDatabase::terminate">terminate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#135"><abbr title="App\Http\Middleware\LogRequestsToDatabase::contentWithinLimits">contentWithinLimits</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#147"><abbr title="App\Http\Middleware\LogRequestsToDatabase::headers">headers</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#162"><abbr title="App\Http\Middleware\LogRequestsToDatabase::payload">payload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#175"><abbr title="App\Http\Middleware\LogRequestsToDatabase::hideParameters">hideParameters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#191"><abbr title="App\Http\Middleware\LogRequestsToDatabase::response">response</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#234"><abbr title="App\Http\Middleware\LogRequestsToDatabase::extractDataFromView">extractDataFromView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#255"><abbr title="App\Http\Middleware\LogRequestsToDatabase::shouldIgnoreUnauthenticated">shouldIgnoreUnauthenticated</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#270"><abbr title="App\Http\Middleware\LogRequestsToDatabase::inExceptArray">inExceptArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#288"><abbr title="App\Http\Middleware\LogRequestsToDatabase::shouldIgnoreRoute">shouldIgnoreRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#298"><abbr title="App\Http\Middleware\LogRequestsToDatabase::input">input</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MakeTusMediaFileName.php.html#17"><abbr title="App\Http\Middleware\MakeTusMediaFileName::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MakeTusMediaFileName.php.html#28"><abbr title="App\Http\Middleware\MakeTusMediaFileName::setNameInUploadMetaRequestHeader">setNameInUploadMetaRequestHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MakeTusMediaFileName.php.html#58"><abbr title="App\Http\Middleware\MakeTusMediaFileName::fileNameResolver">fileNameResolver</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MinimumAppVeriosnRequiredMiddleware.php.html#19"><abbr title="App\Http\Middleware\MinimumAppVeriosnRequiredMiddleware::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RedirectIfAuthenticated.php.html#17"><abbr title="App\Http\Middleware\RedirectIfAuthenticated::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TrustHosts.php.html#14"><abbr title="App\Http\Middleware\TrustHosts::hosts">hosts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCognitoToken.php.html#27"><abbr title="App\Http\Middleware\ValidateCognitoToken::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValidateCognitoToken.php.html#111"><abbr title="App\Http\Middleware\ValidateCognitoToken::updateLastActivity">updateLastActivity</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="LogRequestsToDatabase.php.html#191"><abbr title="App\Http\Middleware\LogRequestsToDatabase::response">response</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ValidateCognitoToken.php.html#27"><abbr title="App\Http\Middleware\ValidateCognitoToken::handle">handle</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#85"><abbr title="App\Http\Middleware\LogRequestsToDatabase::terminate">terminate</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="HorizonBasicAuth.php.html#15"><abbr title="App\Http\Middleware\HorizonBasicAuth::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="MakeTusMediaFileName.php.html#58"><abbr title="App\Http\Middleware\MakeTusMediaFileName::fileNameResolver">fileNameResolver</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AppAccessPermissionMiddleware.php.html#17"><abbr title="App\Http\Middleware\AppAccessPermissionMiddleware::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#270"><abbr title="App\Http\Middleware\LogRequestsToDatabase::inExceptArray">inExceptArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="MakeTusMediaFileName.php.html#28"><abbr title="App\Http\Middleware\MakeTusMediaFileName::setNameInUploadMetaRequestHeader">setNameInUploadMetaRequestHeader</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#255"><abbr title="App\Http\Middleware\LogRequestsToDatabase::shouldIgnoreUnauthenticated">shouldIgnoreUnauthenticated</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="MinimumAppVeriosnRequiredMiddleware.php.html#19"><abbr title="App\Http\Middleware\MinimumAppVeriosnRequiredMiddleware::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RedirectIfAuthenticated.php.html#17"><abbr title="App\Http\Middleware\RedirectIfAuthenticated::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#175"><abbr title="App\Http\Middleware\LogRequestsToDatabase::hideParameters">hideParameters</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#234"><abbr title="App\Http\Middleware\LogRequestsToDatabase::extractDataFromView">extractDataFromView</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Authenticate.php.html#13"><abbr title="App\Http\Middleware\Authenticate::redirectTo">redirectTo</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="LogRequestsToDatabase.php.html#298"><abbr title="App\Http\Middleware\LogRequestsToDatabase::input">input</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="MakeTusMediaFileName.php.html#17"><abbr title="App\Http\Middleware\MakeTusMediaFileName::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 10:03:06 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([9,0,0,0,0,0,0,0,0,0,0,6], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([24,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"AppAccessPermissionMiddleware.php.html#15\">App\\Http\\Middleware\\AppAccessPermissionMiddleware<\/a>"],[0,2,"<a href=\"Authenticate.php.html#8\">App\\Http\\Middleware\\Authenticate<\/a>"],[100,0,"<a href=\"EncryptCookies.php.html#7\">App\\Http\\Middleware\\EncryptCookies<\/a>"],[0,6,"<a href=\"HorizonBasicAuth.php.html#7\">App\\Http\\Middleware\\HorizonBasicAuth<\/a>"],[0,42,"<a href=\"LogRequestsToDatabase.php.html#19\">App\\Http\\Middleware\\LogRequestsToDatabase<\/a>"],[0,13,"<a href=\"MakeTusMediaFileName.php.html#12\">App\\Http\\Middleware\\MakeTusMediaFileName<\/a>"],[0,4,"<a href=\"MinimumAppVeriosnRequiredMiddleware.php.html#12\">App\\Http\\Middleware\\MinimumAppVeriosnRequiredMiddleware<\/a>"],[100,0,"<a href=\"PreventRequestsDuringMaintenance.php.html#7\">App\\Http\\Middleware\\PreventRequestsDuringMaintenance<\/a>"],[0,4,"<a href=\"RedirectIfAuthenticated.php.html#10\">App\\Http\\Middleware\\RedirectIfAuthenticated<\/a>"],[100,0,"<a href=\"TrimStrings.php.html#7\">App\\Http\\Middleware\\TrimStrings<\/a>"],[0,1,"<a href=\"TrustHosts.php.html#7\">App\\Http\\Middleware\\TrustHosts<\/a>"],[100,0,"<a href=\"TrustProxies.php.html#8\">App\\Http\\Middleware\\TrustProxies<\/a>"],[0,13,"<a href=\"ValidateCognitoToken.php.html#18\">App\\Http\\Middleware\\ValidateCognitoToken<\/a>"],[100,0,"<a href=\"ValidateSignature.php.html#7\">App\\Http\\Middleware\\ValidateSignature<\/a>"],[100,0,"<a href=\"VerifyCsrfToken.php.html#7\">App\\Http\\Middleware\\VerifyCsrfToken<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"AppAccessPermissionMiddleware.php.html#17\">App\\Http\\Middleware\\AppAccessPermissionMiddleware::handle<\/a>"],[0,2,"<a href=\"Authenticate.php.html#13\">App\\Http\\Middleware\\Authenticate::redirectTo<\/a>"],[0,6,"<a href=\"HorizonBasicAuth.php.html#15\">App\\Http\\Middleware\\HorizonBasicAuth::handle<\/a>"],[0,1,"<a href=\"LogRequestsToDatabase.php.html#67\">App\\Http\\Middleware\\LogRequestsToDatabase::given<\/a>"],[0,1,"<a href=\"LogRequestsToDatabase.php.html#77\">App\\Http\\Middleware\\LogRequestsToDatabase::handle<\/a>"],[0,7,"<a href=\"LogRequestsToDatabase.php.html#85\">App\\Http\\Middleware\\LogRequestsToDatabase::terminate<\/a>"],[0,1,"<a href=\"LogRequestsToDatabase.php.html#135\">App\\Http\\Middleware\\LogRequestsToDatabase::contentWithinLimits<\/a>"],[0,1,"<a href=\"LogRequestsToDatabase.php.html#147\">App\\Http\\Middleware\\LogRequestsToDatabase::headers<\/a>"],[0,1,"<a href=\"LogRequestsToDatabase.php.html#162\">App\\Http\\Middleware\\LogRequestsToDatabase::payload<\/a>"],[0,3,"<a href=\"LogRequestsToDatabase.php.html#175\">App\\Http\\Middleware\\LogRequestsToDatabase::hideParameters<\/a>"],[0,12,"<a href=\"LogRequestsToDatabase.php.html#191\">App\\Http\\Middleware\\LogRequestsToDatabase::response<\/a>"],[0,3,"<a href=\"LogRequestsToDatabase.php.html#234\">App\\Http\\Middleware\\LogRequestsToDatabase::extractDataFromView<\/a>"],[0,4,"<a href=\"LogRequestsToDatabase.php.html#255\">App\\Http\\Middleware\\LogRequestsToDatabase::shouldIgnoreUnauthenticated<\/a>"],[0,5,"<a href=\"LogRequestsToDatabase.php.html#270\">App\\Http\\Middleware\\LogRequestsToDatabase::inExceptArray<\/a>"],[0,1,"<a href=\"LogRequestsToDatabase.php.html#288\">App\\Http\\Middleware\\LogRequestsToDatabase::shouldIgnoreRoute<\/a>"],[0,2,"<a href=\"LogRequestsToDatabase.php.html#298\">App\\Http\\Middleware\\LogRequestsToDatabase::input<\/a>"],[0,2,"<a href=\"MakeTusMediaFileName.php.html#17\">App\\Http\\Middleware\\MakeTusMediaFileName::handle<\/a>"],[0,5,"<a href=\"MakeTusMediaFileName.php.html#28\">App\\Http\\Middleware\\MakeTusMediaFileName::setNameInUploadMetaRequestHeader<\/a>"],[0,6,"<a href=\"MakeTusMediaFileName.php.html#58\">App\\Http\\Middleware\\MakeTusMediaFileName::fileNameResolver<\/a>"],[0,4,"<a href=\"MinimumAppVeriosnRequiredMiddleware.php.html#19\">App\\Http\\Middleware\\MinimumAppVeriosnRequiredMiddleware::handle<\/a>"],[0,4,"<a href=\"RedirectIfAuthenticated.php.html#17\">App\\Http\\Middleware\\RedirectIfAuthenticated::handle<\/a>"],[0,1,"<a href=\"TrustHosts.php.html#14\">App\\Http\\Middleware\\TrustHosts::hosts<\/a>"],[0,12,"<a href=\"ValidateCognitoToken.php.html#27\">App\\Http\\Middleware\\ValidateCognitoToken::handle<\/a>"],[0,1,"<a href=\"ValidateCognitoToken.php.html#111\">App\\Http\\Middleware\\ValidateCognitoToken::updateLastActivity<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Enums</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Enums</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DateRanges.php.html#9">App\Enums\DateRanges</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItemTypes.php.html#9">App\Enums\InvoiceLineItemTypes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceSubsidiaryTypes.php.html#8">App\Enums\InvoiceSubsidiaryTypes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaterialQuantityType.php.html#8">App\Enums\MaterialQuantityType</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Priority.php.html#11">App\Enums\Priority</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PropertyAccessMethods.php.html#8">App\Enums\PropertyAccessMethods</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteStatus.php.html#8">App\Enums\QuoteStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTaskStatus.php.html#8">App\Enums\QuoteTaskStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleTimings.php.html#9">App\Enums\ScheduleTimings</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleTypes.php.html#7">App\Enums\ScheduleTypes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip.php.html#9">App\Enums\Trip</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripStates.php.html#8">App\Enums\TripStates</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserStatus.php.html#8">App\Enums\UserStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserTypes.php.html#7">App\Enums\UserTypes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnBoardingStatus.php.html#9">App\Enums\VendorOnBoardingStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingStatuses.php.html#8">App\Enums\VendorOnboardingStatuses</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#8">App\Enums\WorkOrderStatus</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderStatus.php.html#8">App\Enums\WorkOrderStatus</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Priority.php.html#11">App\Enums\Priority</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UserStatus.php.html#8">App\Enums\UserStatus</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InvoiceSubsidiaryTypes.php.html#8">App\Enums\InvoiceSubsidiaryTypes</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuoteStatus.php.html#8">App\Enums\QuoteStatus</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuoteTaskStatus.php.html#8">App\Enums\QuoteTaskStatus</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DateRanges.php.html#30"><abbr title="App\Enums\DateRanges::makeCollection">makeCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateRanges.php.html#40"><abbr title="App\Enums\DateRanges::excludeFutureDates">excludeFutureDates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceLineItemTypes.php.html#31"><abbr title="App\Enums\InvoiceLineItemTypes::hourlyTypes">hourlyTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvoiceSubsidiaryTypes.php.html#25"><abbr title="App\Enums\InvoiceSubsidiaryTypes::isLaborCostType">isLaborCostType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaterialQuantityType.php.html#23"><abbr title="App\Enums\MaterialQuantityType::allTypes">allTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Priority.php.html#26"><abbr title="App\Enums\Priority::makeCollection">makeCollection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Priority.php.html#36"><abbr title="App\Enums\Priority::priorityValueLabelFormat">priorityValueLabelFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PropertyAccessMethods.php.html#24"><abbr title="App\Enums\PropertyAccessMethods::apiResponseFormat">apiResponseFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PropertyAccessMethods.php.html#57"><abbr title="App\Enums\PropertyAccessMethods::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteStatus.php.html#18"><abbr title="App\Enums\QuoteStatus::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuoteTaskStatus.php.html#17"><abbr title="App\Enums\QuoteTaskStatus::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleTimings.php.html#24"><abbr title="App\Enums\ScheduleTimings::timeRange">timeRange</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleTimings.php.html#47"><abbr title="App\Enums\ScheduleTimings::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleTimings.php.html#64"><abbr title="App\Enums\ScheduleTimings::all">all</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleTimings.php.html#93"><abbr title="App\Enums\ScheduleTimings::scheduleTimingsFromApp">scheduleTimingsFromApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleTimings.php.html#106"><abbr title="App\Enums\ScheduleTimings::displaySortOrder">displaySortOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScheduleTypes.php.html#19"><abbr title="App\Enums\ScheduleTypes::getLabel">getLabel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip.php.html#22"><abbr title="App\Enums\Trip::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip.php.html#35"><abbr title="App\Enums\Trip::tripEndWith">tripEndWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip.php.html#47"><abbr title="App\Enums\Trip::tripEndWithTypes">tripEndWithTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip.php.html#61"><abbr title="App\Enums\Trip::workTypeOptions">workTypeOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip.php.html#84"><abbr title="App\Enums\Trip::partialCompleteOptions">partialCompleteOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip.php.html#107"><abbr title="App\Enums\Trip::noWorkOptions">noWorkOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripStates.php.html#22"><abbr title="App\Enums\TripStates::openTripStates">openTripStates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserStatus.php.html#18"><abbr title="App\Enums\UserStatus::find">find</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserStatus.php.html#32"><abbr title="App\Enums\UserStatus::statusValueLabelFormat">statusValueLabelFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserTypes.php.html#21"><abbr title="App\Enums\UserTypes::tripActionAllowedType">tripActionAllowedType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnBoardingStatus.php.html#17"><abbr title="App\Enums\VendorOnBoardingStatus::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorOnboardingStatuses.php.html#22"><abbr title="App\Enums\VendorOnboardingStatuses::label">label</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#34"><abbr title="App\Enums\WorkOrderStatus::newStatuses">newStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#42"><abbr title="App\Enums\WorkOrderStatus::triagingStatuses">triagingStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#53"><abbr title="App\Enums\WorkOrderStatus::schedulingStatuses">schedulingStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#64"><abbr title="App\Enums\WorkOrderStatus::scheduledStatuses">scheduledStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#74"><abbr title="App\Enums\WorkOrderStatus::workInProgressStatuses">workInProgressStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#84"><abbr title="App\Enums\WorkOrderStatus::quotingStatuses">quotingStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#95"><abbr title="App\Enums\WorkOrderStatus::pausedStatuses">pausedStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#105"><abbr title="App\Enums\WorkOrderStatus::qualityCheckStatuses">qualityCheckStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#115"><abbr title="App\Enums\WorkOrderStatus::invoiceStatuses">invoiceStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#126"><abbr title="App\Enums\WorkOrderStatus::closedStatuses">closedStatuses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#139"><abbr title="App\Enums\WorkOrderStatus::openWorkOrderStates">openWorkOrderStates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#152"><abbr title="App\Enums\WorkOrderStatus::healthScoreStates">healthScoreStates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#167"><abbr title="App\Enums\WorkOrderStatus::valueLabelForMobileApp">valueLabelForMobileApp</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#188"><abbr title="App\Enums\WorkOrderStatus::labelForMobile">labelForMobile</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="InvoiceSubsidiaryTypes.php.html#25"><abbr title="App\Enums\InvoiceSubsidiaryTypes::isLaborCostType">isLaborCostType</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Priority.php.html#36"><abbr title="App\Enums\Priority::priorityValueLabelFormat">priorityValueLabelFormat</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuoteStatus.php.html#18"><abbr title="App\Enums\QuoteStatus::label">label</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuoteTaskStatus.php.html#17"><abbr title="App\Enums\QuoteTaskStatus::label">label</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserStatus.php.html#32"><abbr title="App\Enums\UserStatus::statusValueLabelFormat">statusValueLabelFormat</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderStatus.php.html#167"><abbr title="App\Enums\WorkOrderStatus::valueLabelForMobileApp">valueLabelForMobileApp</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:22:08 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([17,0,0,0,0,0,0,0,0,0,1,2], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([43,0,0,0,0,0,0,0,0,0,1,5], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[100,1,"<a href=\"DateFilterType.php.html#8\">App\\Enums\\DateFilterType<\/a>"],[100,1,"<a href=\"DateOperation.php.html#8\">App\\Enums\\DateOperation<\/a>"],[0,2,"<a href=\"DateRanges.php.html#9\">App\\Enums\\DateRanges<\/a>"],[96.875,4,"<a href=\"DateSlug.php.html#11\">App\\Enums\\DateSlug<\/a>"],[0,1,"<a href=\"InvoiceLineItemTypes.php.html#9\">App\\Enums\\InvoiceLineItemTypes<\/a>"],[0,2,"<a href=\"InvoiceSubsidiaryTypes.php.html#8\">App\\Enums\\InvoiceSubsidiaryTypes<\/a>"],[0,1,"<a href=\"MaterialQuantityType.php.html#8\">App\\Enums\\MaterialQuantityType<\/a>"],[0,3,"<a href=\"Priority.php.html#11\">App\\Enums\\Priority<\/a>"],[0,2,"<a href=\"PropertyAccessMethods.php.html#8\">App\\Enums\\PropertyAccessMethods<\/a>"],[0,2,"<a href=\"QuoteStatus.php.html#8\">App\\Enums\\QuoteStatus<\/a>"],[0,2,"<a href=\"QuoteTaskStatus.php.html#8\">App\\Enums\\QuoteTaskStatus<\/a>"],[0,5,"<a href=\"ScheduleTimings.php.html#9\">App\\Enums\\ScheduleTimings<\/a>"],[0,1,"<a href=\"ScheduleTypes.php.html#7\">App\\Enums\\ScheduleTypes<\/a>"],[0,6,"<a href=\"Trip.php.html#9\">App\\Enums\\Trip<\/a>"],[0,1,"<a href=\"TripStates.php.html#8\">App\\Enums\\TripStates<\/a>"],[0,3,"<a href=\"UserStatus.php.html#8\">App\\Enums\\UserStatus<\/a>"],[0,1,"<a href=\"UserTypes.php.html#7\">App\\Enums\\UserTypes<\/a>"],[0,1,"<a href=\"VendorOnBoardingStatus.php.html#9\">App\\Enums\\VendorOnBoardingStatus<\/a>"],[0,1,"<a href=\"VendorOnboardingStatuses.php.html#8\">App\\Enums\\VendorOnboardingStatuses<\/a>"],[0,15,"<a href=\"WorkOrderStatus.php.html#8\">App\\Enums\\WorkOrderStatus<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[100,1,"<a href=\"DateFilterType.php.html#20\">App\\Enums\\DateFilterType::getDescription<\/a>"],[100,1,"<a href=\"DateOperation.php.html#21\">App\\Enums\\DateOperation::getDescription<\/a>"],[0,1,"<a href=\"DateRanges.php.html#30\">App\\Enums\\DateRanges::makeCollection<\/a>"],[0,1,"<a href=\"DateRanges.php.html#40\">App\\Enums\\DateRanges::excludeFutureDates<\/a>"],[100,1,"<a href=\"DateSlug.php.html#33\">App\\Enums\\DateSlug::makeCollection<\/a>"],[100,1,"<a href=\"DateSlug.php.html#47\">App\\Enums\\DateSlug::futureDates<\/a>"],[100,1,"<a href=\"DateSlug.php.html#55\">App\\Enums\\DateSlug::getDisplayName<\/a>"],[93.33333333333333,1,"<a href=\"DateSlug.php.html#76\">App\\Enums\\DateSlug::getDateRange<\/a>"],[0,1,"<a href=\"InvoiceLineItemTypes.php.html#31\">App\\Enums\\InvoiceLineItemTypes::hourlyTypes<\/a>"],[0,2,"<a href=\"InvoiceSubsidiaryTypes.php.html#25\">App\\Enums\\InvoiceSubsidiaryTypes::isLaborCostType<\/a>"],[0,1,"<a href=\"MaterialQuantityType.php.html#23\">App\\Enums\\MaterialQuantityType::allTypes<\/a>"],[0,1,"<a href=\"Priority.php.html#26\">App\\Enums\\Priority::makeCollection<\/a>"],[0,2,"<a href=\"Priority.php.html#36\">App\\Enums\\Priority::priorityValueLabelFormat<\/a>"],[0,1,"<a href=\"PropertyAccessMethods.php.html#24\">App\\Enums\\PropertyAccessMethods::apiResponseFormat<\/a>"],[0,1,"<a href=\"PropertyAccessMethods.php.html#57\">App\\Enums\\PropertyAccessMethods::label<\/a>"],[0,2,"<a href=\"QuoteStatus.php.html#18\">App\\Enums\\QuoteStatus::label<\/a>"],[0,2,"<a href=\"QuoteTaskStatus.php.html#17\">App\\Enums\\QuoteTaskStatus::label<\/a>"],[0,1,"<a href=\"ScheduleTimings.php.html#24\">App\\Enums\\ScheduleTimings::timeRange<\/a>"],[0,1,"<a href=\"ScheduleTimings.php.html#47\">App\\Enums\\ScheduleTimings::label<\/a>"],[0,1,"<a href=\"ScheduleTimings.php.html#64\">App\\Enums\\ScheduleTimings::all<\/a>"],[0,1,"<a href=\"ScheduleTimings.php.html#93\">App\\Enums\\ScheduleTimings::scheduleTimingsFromApp<\/a>"],[0,1,"<a href=\"ScheduleTimings.php.html#106\">App\\Enums\\ScheduleTimings::displaySortOrder<\/a>"],[0,1,"<a href=\"ScheduleTypes.php.html#19\">App\\Enums\\ScheduleTypes::getLabel<\/a>"],[0,1,"<a href=\"Trip.php.html#22\">App\\Enums\\Trip::label<\/a>"],[0,1,"<a href=\"Trip.php.html#35\">App\\Enums\\Trip::tripEndWith<\/a>"],[0,1,"<a href=\"Trip.php.html#47\">App\\Enums\\Trip::tripEndWithTypes<\/a>"],[0,1,"<a href=\"Trip.php.html#61\">App\\Enums\\Trip::workTypeOptions<\/a>"],[0,1,"<a href=\"Trip.php.html#84\">App\\Enums\\Trip::partialCompleteOptions<\/a>"],[0,1,"<a href=\"Trip.php.html#107\">App\\Enums\\Trip::noWorkOptions<\/a>"],[0,1,"<a href=\"TripStates.php.html#22\">App\\Enums\\TripStates::openTripStates<\/a>"],[0,1,"<a href=\"UserStatus.php.html#18\">App\\Enums\\UserStatus::find<\/a>"],[0,2,"<a href=\"UserStatus.php.html#32\">App\\Enums\\UserStatus::statusValueLabelFormat<\/a>"],[0,1,"<a href=\"UserTypes.php.html#21\">App\\Enums\\UserTypes::tripActionAllowedType<\/a>"],[0,1,"<a href=\"VendorOnBoardingStatus.php.html#17\">App\\Enums\\VendorOnBoardingStatus::label<\/a>"],[0,1,"<a href=\"VendorOnboardingStatuses.php.html#22\">App\\Enums\\VendorOnboardingStatuses::label<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#34\">App\\Enums\\WorkOrderStatus::newStatuses<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#42\">App\\Enums\\WorkOrderStatus::triagingStatuses<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#53\">App\\Enums\\WorkOrderStatus::schedulingStatuses<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#64\">App\\Enums\\WorkOrderStatus::scheduledStatuses<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#74\">App\\Enums\\WorkOrderStatus::workInProgressStatuses<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#84\">App\\Enums\\WorkOrderStatus::quotingStatuses<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#95\">App\\Enums\\WorkOrderStatus::pausedStatuses<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#105\">App\\Enums\\WorkOrderStatus::qualityCheckStatuses<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#115\">App\\Enums\\WorkOrderStatus::invoiceStatuses<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#126\">App\\Enums\\WorkOrderStatus::closedStatuses<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#139\">App\\Enums\\WorkOrderStatus::openWorkOrderStates<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#152\">App\\Enums\\WorkOrderStatus::healthScoreStates<\/a>"],[0,2,"<a href=\"WorkOrderStatus.php.html#167\">App\\Enums\\WorkOrderStatus::valueLabelForMobileApp<\/a>"],[0,1,"<a href=\"WorkOrderStatus.php.html#188\">App\\Enums\\WorkOrderStatus::labelForMobile<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

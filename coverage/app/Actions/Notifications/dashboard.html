<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Actions/Notifications</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">Notifications</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseAction.php.html#9">App\Actions\Notifications\BaseAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearAllNotification.php.html#18">App\Actions\Notifications\ClearAllNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearNotification.php.html#17">App\Actions\Notifications\ClearNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadAllNotification.php.html#17">App\Actions\Notifications\ReadAllNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadNotification.php.html#17">App\Actions\Notifications\ReadNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnclearNotification.php.html#16">App\Actions\Notifications\UnclearNotification</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ClearAllNotification.php.html#18">App\Actions\Notifications\ClearAllNotification</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ClearNotification.php.html#17">App\Actions\Notifications\ClearNotification</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ReadAllNotification.php.html#17">App\Actions\Notifications\ReadAllNotification</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ReadNotification.php.html#17">App\Actions\Notifications\ReadNotification</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="UnclearNotification.php.html#16">App\Actions\Notifications\UnclearNotification</a></td><td class="text-right">90</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseAction.php.html#13"><abbr title="App\Actions\Notifications\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#18"><abbr title="App\Actions\Notifications\BaseAction::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#23"><abbr title="App\Actions\Notifications\BaseAction::routes">routes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#33"><abbr title="App\Actions\Notifications\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearAllNotification.php.html#22"><abbr title="App\Actions\Notifications\ClearAllNotification::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearAllNotification.php.html#27"><abbr title="App\Actions\Notifications\ClearAllNotification::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearAllNotification.php.html#37"><abbr title="App\Actions\Notifications\ClearAllNotification::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearAllNotification.php.html#49"><abbr title="App\Actions\Notifications\ClearAllNotification::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearAllNotification.php.html#85"><abbr title="App\Actions\Notifications\ClearAllNotification::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearNotification.php.html#21"><abbr title="App\Actions\Notifications\ClearNotification::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearNotification.php.html#26"><abbr title="App\Actions\Notifications\ClearNotification::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearNotification.php.html#36"><abbr title="App\Actions\Notifications\ClearNotification::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearNotification.php.html#65"><abbr title="App\Actions\Notifications\ClearNotification::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadAllNotification.php.html#21"><abbr title="App\Actions\Notifications\ReadAllNotification::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadAllNotification.php.html#26"><abbr title="App\Actions\Notifications\ReadAllNotification::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadAllNotification.php.html#31"><abbr title="App\Actions\Notifications\ReadAllNotification::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadAllNotification.php.html#43"><abbr title="App\Actions\Notifications\ReadAllNotification::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadAllNotification.php.html#73"><abbr title="App\Actions\Notifications\ReadAllNotification::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadNotification.php.html#21"><abbr title="App\Actions\Notifications\ReadNotification::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadNotification.php.html#26"><abbr title="App\Actions\Notifications\ReadNotification::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadNotification.php.html#35"><abbr title="App\Actions\Notifications\ReadNotification::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReadNotification.php.html#64"><abbr title="App\Actions\Notifications\ReadNotification::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnclearNotification.php.html#20"><abbr title="App\Actions\Notifications\UnclearNotification::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnclearNotification.php.html#25"><abbr title="App\Actions\Notifications\UnclearNotification::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnclearNotification.php.html#35"><abbr title="App\Actions\Notifications\UnclearNotification::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnclearNotification.php.html#64"><abbr title="App\Actions\Notifications\UnclearNotification::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ClearAllNotification.php.html#49"><abbr title="App\Actions\Notifications\ClearAllNotification::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ClearNotification.php.html#36"><abbr title="App\Actions\Notifications\ClearNotification::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ReadAllNotification.php.html#43"><abbr title="App\Actions\Notifications\ReadAllNotification::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ReadNotification.php.html#35"><abbr title="App\Actions\Notifications\ReadNotification::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UnclearNotification.php.html#35"><abbr title="App\Actions\Notifications\UnclearNotification::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ClearAllNotification.php.html#37"><abbr title="App\Actions\Notifications\ClearAllNotification::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ClearAllNotification.php.html#85"><abbr title="App\Actions\Notifications\ClearAllNotification::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ClearNotification.php.html#65"><abbr title="App\Actions\Notifications\ClearNotification::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReadNotification.php.html#64"><abbr title="App\Actions\Notifications\ReadNotification::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UnclearNotification.php.html#64"><abbr title="App\Actions\Notifications\UnclearNotification::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([6,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([26,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"BaseAction.php.html#9\">App\\Actions\\Notifications\\BaseAction<\/a>"],[0,11,"<a href=\"ClearAllNotification.php.html#18\">App\\Actions\\Notifications\\ClearAllNotification<\/a>"],[0,9,"<a href=\"ClearNotification.php.html#17\">App\\Actions\\Notifications\\ClearNotification<\/a>"],[0,9,"<a href=\"ReadAllNotification.php.html#17\">App\\Actions\\Notifications\\ReadAllNotification<\/a>"],[0,9,"<a href=\"ReadNotification.php.html#17\">App\\Actions\\Notifications\\ReadNotification<\/a>"],[0,9,"<a href=\"UnclearNotification.php.html#16\">App\\Actions\\Notifications\\UnclearNotification<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BaseAction.php.html#13\">App\\Actions\\Notifications\\BaseAction::getSlug<\/a>"],[0,1,"<a href=\"BaseAction.php.html#18\">App\\Actions\\Notifications\\BaseAction::getRoute<\/a>"],[0,1,"<a href=\"BaseAction.php.html#23\">App\\Actions\\Notifications\\BaseAction::routes<\/a>"],[0,1,"<a href=\"BaseAction.php.html#33\">App\\Actions\\Notifications\\BaseAction::authorize<\/a>"],[0,1,"<a href=\"ClearAllNotification.php.html#22\">App\\Actions\\Notifications\\ClearAllNotification::getSlug<\/a>"],[0,1,"<a href=\"ClearAllNotification.php.html#27\">App\\Actions\\Notifications\\ClearAllNotification::getRoute<\/a>"],[0,2,"<a href=\"ClearAllNotification.php.html#37\">App\\Actions\\Notifications\\ClearAllNotification::handle<\/a>"],[0,5,"<a href=\"ClearAllNotification.php.html#49\">App\\Actions\\Notifications\\ClearAllNotification::asController<\/a>"],[0,2,"<a href=\"ClearAllNotification.php.html#85\">App\\Actions\\Notifications\\ClearAllNotification::authorize<\/a>"],[0,1,"<a href=\"ClearNotification.php.html#21\">App\\Actions\\Notifications\\ClearNotification::getSlug<\/a>"],[0,1,"<a href=\"ClearNotification.php.html#26\">App\\Actions\\Notifications\\ClearNotification::handle<\/a>"],[0,5,"<a href=\"ClearNotification.php.html#36\">App\\Actions\\Notifications\\ClearNotification::asController<\/a>"],[0,2,"<a href=\"ClearNotification.php.html#65\">App\\Actions\\Notifications\\ClearNotification::authorize<\/a>"],[0,1,"<a href=\"ReadAllNotification.php.html#21\">App\\Actions\\Notifications\\ReadAllNotification::getSlug<\/a>"],[0,1,"<a href=\"ReadAllNotification.php.html#26\">App\\Actions\\Notifications\\ReadAllNotification::getRoute<\/a>"],[0,1,"<a href=\"ReadAllNotification.php.html#31\">App\\Actions\\Notifications\\ReadAllNotification::handle<\/a>"],[0,5,"<a href=\"ReadAllNotification.php.html#43\">App\\Actions\\Notifications\\ReadAllNotification::asController<\/a>"],[0,1,"<a href=\"ReadAllNotification.php.html#73\">App\\Actions\\Notifications\\ReadAllNotification::authorize<\/a>"],[0,1,"<a href=\"ReadNotification.php.html#21\">App\\Actions\\Notifications\\ReadNotification::getSlug<\/a>"],[0,1,"<a href=\"ReadNotification.php.html#26\">App\\Actions\\Notifications\\ReadNotification::handle<\/a>"],[0,5,"<a href=\"ReadNotification.php.html#35\">App\\Actions\\Notifications\\ReadNotification::asController<\/a>"],[0,2,"<a href=\"ReadNotification.php.html#64\">App\\Actions\\Notifications\\ReadNotification::authorize<\/a>"],[0,1,"<a href=\"UnclearNotification.php.html#20\">App\\Actions\\Notifications\\UnclearNotification::getSlug<\/a>"],[0,1,"<a href=\"UnclearNotification.php.html#25\">App\\Actions\\Notifications\\UnclearNotification::handle<\/a>"],[0,5,"<a href=\"UnclearNotification.php.html#35\">App\\Actions\\Notifications\\UnclearNotification::asController<\/a>"],[0,2,"<a href=\"UnclearNotification.php.html#64\">App\\Actions\\Notifications\\UnclearNotification::authorize<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Actions/Invoices</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">Invoices</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseAction.php.html#12">App\Actions\Invoices\BaseAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteInvoice.php.html#25">App\Actions\Invoices\DeleteInvoice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FullyPaidInvoice.php.html#27">App\Actions\Invoices\FullyPaidInvoice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoicing.php.html#28">App\Actions\Invoices\Invoicing</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PartiallyPaidInvoice.php.html#26">App\Actions\Invoices\PartiallyPaidInvoice</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VoidInvoice.php.html#24">App\Actions\Invoices\VoidInvoice</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Invoicing.php.html#28">App\Actions\Invoices\Invoicing</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="FullyPaidInvoice.php.html#27">App\Actions\Invoices\FullyPaidInvoice</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="PartiallyPaidInvoice.php.html#26">App\Actions\Invoices\PartiallyPaidInvoice</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="DeleteInvoice.php.html#25">App\Actions\Invoices\DeleteInvoice</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="VoidInvoice.php.html#24">App\Actions\Invoices\VoidInvoice</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="BaseAction.php.html#12">App\Actions\Invoices\BaseAction</a></td><td class="text-right">56</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseAction.php.html#16"><abbr title="App\Actions\Invoices\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#21"><abbr title="App\Actions\Invoices\BaseAction::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#26"><abbr title="App\Actions\Invoices\BaseAction::routes">routes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#36"><abbr title="App\Actions\Invoices\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#44"><abbr title="App\Actions\Invoices\BaseAction::validateRouteParameterRelation">validateRouteParameterRelation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteInvoice.php.html#30"><abbr title="App\Actions\Invoices\DeleteInvoice::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteInvoice.php.html#35"><abbr title="App\Actions\Invoices\DeleteInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteInvoice.php.html#58"><abbr title="App\Actions\Invoices\DeleteInvoice::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteInvoice.php.html#101"><abbr title="App\Actions\Invoices\DeleteInvoice::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FullyPaidInvoice.php.html#32"><abbr title="App\Actions\Invoices\FullyPaidInvoice::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FullyPaidInvoice.php.html#37"><abbr title="App\Actions\Invoices\FullyPaidInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FullyPaidInvoice.php.html#50"><abbr title="App\Actions\Invoices\FullyPaidInvoice::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FullyPaidInvoice.php.html#106"><abbr title="App\Actions\Invoices\FullyPaidInvoice::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoicing.php.html#34"><abbr title="App\Actions\Invoices\Invoicing::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoicing.php.html#39"><abbr title="App\Actions\Invoices\Invoicing::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoicing.php.html#44"><abbr title="App\Actions\Invoices\Invoicing::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoicing.php.html#53"><abbr title="App\Actions\Invoices\Invoicing::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoicing.php.html#124"><abbr title="App\Actions\Invoices\Invoicing::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PartiallyPaidInvoice.php.html#31"><abbr title="App\Actions\Invoices\PartiallyPaidInvoice::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PartiallyPaidInvoice.php.html#36"><abbr title="App\Actions\Invoices\PartiallyPaidInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PartiallyPaidInvoice.php.html#48"><abbr title="App\Actions\Invoices\PartiallyPaidInvoice::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PartiallyPaidInvoice.php.html#100"><abbr title="App\Actions\Invoices\PartiallyPaidInvoice::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VoidInvoice.php.html#29"><abbr title="App\Actions\Invoices\VoidInvoice::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VoidInvoice.php.html#34"><abbr title="App\Actions\Invoices\VoidInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VoidInvoice.php.html#46"><abbr title="App\Actions\Invoices\VoidInvoice::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VoidInvoice.php.html#97"><abbr title="App\Actions\Invoices\VoidInvoice::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Invoicing.php.html#53"><abbr title="App\Actions\Invoices\Invoicing::asController">asController</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="FullyPaidInvoice.php.html#50"><abbr title="App\Actions\Invoices\FullyPaidInvoice::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="PartiallyPaidInvoice.php.html#48"><abbr title="App\Actions\Invoices\PartiallyPaidInvoice::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="DeleteInvoice.php.html#58"><abbr title="App\Actions\Invoices\DeleteInvoice::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="VoidInvoice.php.html#46"><abbr title="App\Actions\Invoices\VoidInvoice::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BaseAction.php.html#44"><abbr title="App\Actions\Invoices\BaseAction::validateRouteParameterRelation">validateRouteParameterRelation</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DeleteInvoice.php.html#35"><abbr title="App\Actions\Invoices\DeleteInvoice::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DeleteInvoice.php.html#101"><abbr title="App\Actions\Invoices\DeleteInvoice::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FullyPaidInvoice.php.html#106"><abbr title="App\Actions\Invoices\FullyPaidInvoice::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Invoicing.php.html#124"><abbr title="App\Actions\Invoices\Invoicing::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PartiallyPaidInvoice.php.html#100"><abbr title="App\Actions\Invoices\PartiallyPaidInvoice::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="VoidInvoice.php.html#97"><abbr title="App\Actions\Invoices\VoidInvoice::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([6,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([26,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,7,"<a href=\"BaseAction.php.html#12\">App\\Actions\\Invoices\\BaseAction<\/a>"],[0,11,"<a href=\"DeleteInvoice.php.html#25\">App\\Actions\\Invoices\\DeleteInvoice<\/a>"],[0,12,"<a href=\"FullyPaidInvoice.php.html#27\">App\\Actions\\Invoices\\FullyPaidInvoice<\/a>"],[0,16,"<a href=\"Invoicing.php.html#28\">App\\Actions\\Invoices\\Invoicing<\/a>"],[0,12,"<a href=\"PartiallyPaidInvoice.php.html#26\">App\\Actions\\Invoices\\PartiallyPaidInvoice<\/a>"],[0,10,"<a href=\"VoidInvoice.php.html#24\">App\\Actions\\Invoices\\VoidInvoice<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BaseAction.php.html#16\">App\\Actions\\Invoices\\BaseAction::getSlug<\/a>"],[0,1,"<a href=\"BaseAction.php.html#21\">App\\Actions\\Invoices\\BaseAction::getRoute<\/a>"],[0,1,"<a href=\"BaseAction.php.html#26\">App\\Actions\\Invoices\\BaseAction::routes<\/a>"],[0,1,"<a href=\"BaseAction.php.html#36\">App\\Actions\\Invoices\\BaseAction::authorize<\/a>"],[0,3,"<a href=\"BaseAction.php.html#44\">App\\Actions\\Invoices\\BaseAction::validateRouteParameterRelation<\/a>"],[0,1,"<a href=\"DeleteInvoice.php.html#30\">App\\Actions\\Invoices\\DeleteInvoice::getSlug<\/a>"],[0,2,"<a href=\"DeleteInvoice.php.html#35\">App\\Actions\\Invoices\\DeleteInvoice::handle<\/a>"],[0,6,"<a href=\"DeleteInvoice.php.html#58\">App\\Actions\\Invoices\\DeleteInvoice::asController<\/a>"],[0,2,"<a href=\"DeleteInvoice.php.html#101\">App\\Actions\\Invoices\\DeleteInvoice::authorize<\/a>"],[0,1,"<a href=\"FullyPaidInvoice.php.html#32\">App\\Actions\\Invoices\\FullyPaidInvoice::getSlug<\/a>"],[0,1,"<a href=\"FullyPaidInvoice.php.html#37\">App\\Actions\\Invoices\\FullyPaidInvoice::handle<\/a>"],[0,8,"<a href=\"FullyPaidInvoice.php.html#50\">App\\Actions\\Invoices\\FullyPaidInvoice::asController<\/a>"],[0,2,"<a href=\"FullyPaidInvoice.php.html#106\">App\\Actions\\Invoices\\FullyPaidInvoice::authorize<\/a>"],[0,1,"<a href=\"Invoicing.php.html#34\">App\\Actions\\Invoices\\Invoicing::getSlug<\/a>"],[0,1,"<a href=\"Invoicing.php.html#39\">App\\Actions\\Invoices\\Invoicing::getRoute<\/a>"],[0,1,"<a href=\"Invoicing.php.html#44\">App\\Actions\\Invoices\\Invoicing::handle<\/a>"],[0,11,"<a href=\"Invoicing.php.html#53\">App\\Actions\\Invoices\\Invoicing::asController<\/a>"],[0,2,"<a href=\"Invoicing.php.html#124\">App\\Actions\\Invoices\\Invoicing::authorize<\/a>"],[0,1,"<a href=\"PartiallyPaidInvoice.php.html#31\">App\\Actions\\Invoices\\PartiallyPaidInvoice::getSlug<\/a>"],[0,1,"<a href=\"PartiallyPaidInvoice.php.html#36\">App\\Actions\\Invoices\\PartiallyPaidInvoice::handle<\/a>"],[0,8,"<a href=\"PartiallyPaidInvoice.php.html#48\">App\\Actions\\Invoices\\PartiallyPaidInvoice::asController<\/a>"],[0,2,"<a href=\"PartiallyPaidInvoice.php.html#100\">App\\Actions\\Invoices\\PartiallyPaidInvoice::authorize<\/a>"],[0,1,"<a href=\"VoidInvoice.php.html#29\">App\\Actions\\Invoices\\VoidInvoice::getSlug<\/a>"],[0,1,"<a href=\"VoidInvoice.php.html#34\">App\\Actions\\Invoices\\VoidInvoice::handle<\/a>"],[0,6,"<a href=\"VoidInvoice.php.html#46\">App\\Actions\\Invoices\\VoidInvoice::asController<\/a>"],[0,2,"<a href=\"VoidInvoice.php.html#97\">App\\Actions\\Invoices\\VoidInvoice::authorize<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

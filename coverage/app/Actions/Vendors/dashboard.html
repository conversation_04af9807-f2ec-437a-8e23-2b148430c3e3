<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Actions/Vendors</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">Vendors</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseAction.php.html#9">App\Actions\Vendors\BaseAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncAllVendors.php.html#20">App\Actions\Vendors\SyncAllVendors</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncVendor.php.html#20">App\Actions\Vendors\SyncVendor</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateVendorStatus.php.html#19">App\Actions\Vendors\UpdateVendorStatus</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SyncVendor.php.html#20">App\Actions\Vendors\SyncVendor</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="SyncAllVendors.php.html#20">App\Actions\Vendors\SyncAllVendors</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="UpdateVendorStatus.php.html#19">App\Actions\Vendors\UpdateVendorStatus</a></td><td class="text-right">132</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseAction.php.html#13"><abbr title="App\Actions\Vendors\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#18"><abbr title="App\Actions\Vendors\BaseAction::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#23"><abbr title="App\Actions\Vendors\BaseAction::routes">routes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#32"><abbr title="App\Actions\Vendors\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncAllVendors.php.html#24"><abbr title="App\Actions\Vendors\SyncAllVendors::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncAllVendors.php.html#29"><abbr title="App\Actions\Vendors\SyncAllVendors::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncAllVendors.php.html#37"><abbr title="App\Actions\Vendors\SyncAllVendors::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncAllVendors.php.html#77"><abbr title="App\Actions\Vendors\SyncAllVendors::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncVendor.php.html#24"><abbr title="App\Actions\Vendors\SyncVendor::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncVendor.php.html#29"><abbr title="App\Actions\Vendors\SyncVendor::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncVendor.php.html#34"><abbr title="App\Actions\Vendors\SyncVendor::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncVendor.php.html#42"><abbr title="App\Actions\Vendors\SyncVendor::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncVendor.php.html#82"><abbr title="App\Actions\Vendors\SyncVendor::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateVendorStatus.php.html#25"><abbr title="App\Actions\Vendors\UpdateVendorStatus::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateVendorStatus.php.html#30"><abbr title="App\Actions\Vendors\UpdateVendorStatus::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateVendorStatus.php.html#35"><abbr title="App\Actions\Vendors\UpdateVendorStatus::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateVendorStatus.php.html#49"><abbr title="App\Actions\Vendors\UpdateVendorStatus::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateVendorStatus.php.html#83"><abbr title="App\Actions\Vendors\UpdateVendorStatus::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SyncAllVendors.php.html#37"><abbr title="App\Actions\Vendors\SyncAllVendors::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="SyncVendor.php.html#42"><abbr title="App\Actions\Vendors\SyncVendor::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="UpdateVendorStatus.php.html#49"><abbr title="App\Actions\Vendors\UpdateVendorStatus::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SyncAllVendors.php.html#77"><abbr title="App\Actions\Vendors\SyncAllVendors::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SyncVendor.php.html#82"><abbr title="App\Actions\Vendors\SyncVendor::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateVendorStatus.php.html#83"><abbr title="App\Actions\Vendors\UpdateVendorStatus::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([4,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([18,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"BaseAction.php.html#9\">App\\Actions\\Vendors\\BaseAction<\/a>"],[0,11,"<a href=\"SyncAllVendors.php.html#20\">App\\Actions\\Vendors\\SyncAllVendors<\/a>"],[0,12,"<a href=\"SyncVendor.php.html#20\">App\\Actions\\Vendors\\SyncVendor<\/a>"],[0,11,"<a href=\"UpdateVendorStatus.php.html#19\">App\\Actions\\Vendors\\UpdateVendorStatus<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BaseAction.php.html#13\">App\\Actions\\Vendors\\BaseAction::getSlug<\/a>"],[0,1,"<a href=\"BaseAction.php.html#18\">App\\Actions\\Vendors\\BaseAction::getRoute<\/a>"],[0,1,"<a href=\"BaseAction.php.html#23\">App\\Actions\\Vendors\\BaseAction::routes<\/a>"],[0,1,"<a href=\"BaseAction.php.html#32\">App\\Actions\\Vendors\\BaseAction::authorize<\/a>"],[0,1,"<a href=\"SyncAllVendors.php.html#24\">App\\Actions\\Vendors\\SyncAllVendors::getSlug<\/a>"],[0,1,"<a href=\"SyncAllVendors.php.html#29\">App\\Actions\\Vendors\\SyncAllVendors::handle<\/a>"],[0,7,"<a href=\"SyncAllVendors.php.html#37\">App\\Actions\\Vendors\\SyncAllVendors::asController<\/a>"],[0,2,"<a href=\"SyncAllVendors.php.html#77\">App\\Actions\\Vendors\\SyncAllVendors::authorize<\/a>"],[0,1,"<a href=\"SyncVendor.php.html#24\">App\\Actions\\Vendors\\SyncVendor::getSlug<\/a>"],[0,1,"<a href=\"SyncVendor.php.html#29\">App\\Actions\\Vendors\\SyncVendor::getRoute<\/a>"],[0,1,"<a href=\"SyncVendor.php.html#34\">App\\Actions\\Vendors\\SyncVendor::handle<\/a>"],[0,7,"<a href=\"SyncVendor.php.html#42\">App\\Actions\\Vendors\\SyncVendor::asController<\/a>"],[0,2,"<a href=\"SyncVendor.php.html#82\">App\\Actions\\Vendors\\SyncVendor::authorize<\/a>"],[0,1,"<a href=\"UpdateVendorStatus.php.html#25\">App\\Actions\\Vendors\\UpdateVendorStatus::getSlug<\/a>"],[0,1,"<a href=\"UpdateVendorStatus.php.html#30\">App\\Actions\\Vendors\\UpdateVendorStatus::getRoute<\/a>"],[0,1,"<a href=\"UpdateVendorStatus.php.html#35\">App\\Actions\\Vendors\\UpdateVendorStatus::handle<\/a>"],[0,6,"<a href=\"UpdateVendorStatus.php.html#49\">App\\Actions\\Vendors\\UpdateVendorStatus::asController<\/a>"],[0,2,"<a href=\"UpdateVendorStatus.php.html#83\">App\\Actions\\Vendors\\UpdateVendorStatus::authorize<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Actions</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Actions</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#34">App\Actions\WorkOrderIssue\MarkAsDoneIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CompleteWorkOrder.php.html#41">App\Actions\WorkOrders\CompleteWorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#30">App\Actions\WorkOrderIssue\UpdateWorkOrderIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/UpdateQuote.php.html#23">App\Actions\Quotes\UpdateQuote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/UpdateQuoteTask.php.html#37">App\Actions\Quotes\UpdateQuoteTask</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#39">App\Actions\WorkOrders\CancelWorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResidentAvailability.php.html#30">App\Actions\ServiceRequest\UpdateResidentAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoiceWorkOrder.php.html#28">App\Actions\WorkOrders\ReadyToInvoiceWorkOrder</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="WorkOrders/TechnicianSchedule.php.html#37">App\Actions\WorkOrders\TechnicianSchedule</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailabilityWorkOrder.php.html#26">App\Actions\WorkOrders\AwaitingAvailabilityWorkOrder</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="Quotes/CreateQuote.php.html#42">App\Actions\Quotes\CreateQuote</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="WorkOrders/UpdateWorkOrderTrip.php.html#36">App\Actions\WorkOrders\UpdateWorkOrderTrip</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="ServiceRequest/AddResidentAvailability.php.html#31">App\Actions\ServiceRequest\AddResidentAvailability</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="WorkOrders/ScheduleToVendor.php.html#32">App\Actions\WorkOrders\ScheduleToVendor</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssue.php.html#36">App\Actions\WorkOrderIssue\DeclineIssue</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="Quotes/CreateQuoteTask.php.html#35">App\Actions\Quotes\CreateQuoteTask</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="Quotes/ApproveQuote.php.html#30">App\Actions\Quotes\ApproveQuote</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="Quotes/DeleteQuoteTask.php.html#28">App\Actions\Quotes\DeleteQuoteTask</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="WorkOrders/PauseWorkOrder.php.html#29">App\Actions\WorkOrders\PauseWorkOrder</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResident.php.html#21">App\Actions\ServiceRequest\UpdateResident</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="ServiceRequest/InProgressServiceRequest.php.html#24">App\Actions\ServiceRequest\InProgressServiceRequest</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="ServiceRequest/RequestResidentAvailability.php.html#25">App\Actions\ServiceRequest\RequestResidentAvailability</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsPendingIssue.php.html#30">App\Actions\WorkOrderIssue\MarkAsPendingIssue</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="Issue/CreateIssue.php.html#28">App\Actions\Issue\CreateIssue</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="ServiceRequest/AwaitingAvailabilityServiceRequest.php.html#25">App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrders/ScheduleWorkOrder.php.html#22">App\Actions\WorkOrders\ScheduleWorkOrder</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="ServiceRequest/ClosedServiceRequest.php.html#24">App\Actions\ServiceRequest\ClosedServiceRequest</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="ServiceRequest/CreateWorkOrderServiceRequest.php.html#24">App\Actions\ServiceRequest\CreateWorkOrderServiceRequest</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrderIssue/AssignIssue.php.html#26">App\Actions\WorkOrderIssue\AssignIssue</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#37">App\Actions\WorkOrders\CloseWorkOrder</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#28">App\Actions\WorkOrders\UpdateTripDetails</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="Invoices/FullyPaidInvoice.php.html#27">App\Actions\Invoices\FullyPaidInvoice</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="Quotes/RejectQuote.php.html#29">App\Actions\Quotes\RejectQuote</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateIssueBasicDetails.php.html#22">App\Actions\WorkOrderIssue\UpdateIssueBasicDetails</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrderIssue/UnassignIssue.php.html#26">App\Actions\WorkOrderIssue\UnassignIssue</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="Invoices/VoidInvoice.php.html#24">App\Actions\Invoices\VoidInvoice</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="Issue/UpdateIssue.php.html#28">App\Actions\Issue\UpdateIssue</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePropertyAddress.php.html#21">App\Actions\ServiceRequest\UpdatePropertyAddress</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="Invoices/PartiallyPaidInvoice.php.html#26">App\Actions\Invoices\PartiallyPaidInvoice</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="Invoices/DeleteInvoice.php.html#25">App\Actions\Invoices\DeleteInvoice</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrders/ReOpenWorkOrder.php.html#24">App\Actions\WorkOrders\ReOpenWorkOrder</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrders/PauseTimer.php.html#24">App\Actions\WorkOrders\PauseTimer</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#34">App\Actions\Issue\CancelIssue</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="Quotes/RestoreQuote.php.html#21">App\Actions\Quotes\RestoreQuote</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="Quotes/SubmitForApproval.php.html#30">App\Actions\Quotes\SubmitForApproval</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrders/ResolveWorkOrder.php.html#23">App\Actions\WorkOrders\ResolveWorkOrder</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrders/ResumeWork.php.html#25">App\Actions\WorkOrders\ResumeWork</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrders/PauseEnRoute.php.html#25">App\Actions\WorkOrders\PauseEnRoute</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrders/StartWork.php.html#24">App\Actions\WorkOrders\StartWork</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrders/ResumeEnRoute.php.html#25">App\Actions\WorkOrders\ResumeEnRoute</a></td><td class="text-right">2%</td></tr>
       <tr><td><a href="WorkOrders/RescheduleWorkOrder.php.html#25">App\Actions\WorkOrders\RescheduleWorkOrder</a></td><td class="text-right">3%</td></tr>
       <tr><td><a href="Issue/UnassignIssue.php.html#35">App\Actions\Issue\UnassignIssue</a></td><td class="text-right">3%</td></tr>
       <tr><td><a href="WorkOrders/EnRouteWork.php.html#25">App\Actions\WorkOrders\EnRouteWork</a></td><td class="text-right">3%</td></tr>
       <tr><td><a href="Issue/UndoIssue.php.html#29">App\Actions\Issue\UndoIssue</a></td><td class="text-right">3%</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#35">App\Actions\Issue\DeleteIssue</a></td><td class="text-right">3%</td></tr>
       <tr><td><a href="Invoices/Invoicing.php.html#28">App\Actions\Invoices\Invoicing</a></td><td class="text-right">3%</td></tr>
       <tr><td><a href="WorkOrderIssue/CreateIssue.php.html#29">App\Actions\WorkOrderIssue\CreateIssue</a></td><td class="text-right">4%</td></tr>
       <tr><td><a href="Issue/AssignIssue.php.html#30">App\Actions\Issue\AssignIssue</a></td><td class="text-right">4%</td></tr>
       <tr><td><a href="Vendors/SyncAllVendors.php.html#20">App\Actions\Vendors\SyncAllVendors</a></td><td class="text-right">4%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateDeclineReason.php.html#20">App\Actions\WorkOrderIssue\UpdateDeclineReason</a></td><td class="text-right">4%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToScheduleWorkOrder.php.html#24">App\Actions\WorkOrders\ReadyToScheduleWorkOrder</a></td><td class="text-right">4%</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePriority.php.html#18">App\Actions\ServiceRequest\UpdatePriority</a></td><td class="text-right">4%</td></tr>
       <tr><td><a href="Notifications/ClearNotification.php.html#17">App\Actions\Notifications\ClearNotification</a></td><td class="text-right">5%</td></tr>
       <tr><td><a href="Notifications/UnclearNotification.php.html#16">App\Actions\Notifications\UnclearNotification</a></td><td class="text-right">5%</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#32">App\Actions\WorkOrders\DeleteWorkOrder</a></td><td class="text-right">5%</td></tr>
       <tr><td><a href="Notifications/ReadNotification.php.html#17">App\Actions\Notifications\ReadNotification</a></td><td class="text-right">5%</td></tr>
       <tr><td><a href="WorkOrders/BaseAction.php.html#24">App\Actions\WorkOrders\BaseAction</a></td><td class="text-right">6%</td></tr>
       <tr><td><a href="Issue/RestoreIssue.php.html#28">App\Actions\Issue\RestoreIssue</a></td><td class="text-right">6%</td></tr>
       <tr><td><a href="Notifications/ClearAllNotification.php.html#18">App\Actions\Notifications\ClearAllNotification</a></td><td class="text-right">7%</td></tr>
       <tr><td><a href="Vendors/SyncVendor.php.html#20">App\Actions\Vendors\SyncVendor</a></td><td class="text-right">7%</td></tr>
       <tr><td><a href="Vendors/UpdateVendorStatus.php.html#19">App\Actions\Vendors\UpdateVendorStatus</a></td><td class="text-right">7%</td></tr>
       <tr><td><a href="WorkOrders/TagWorkOrder.php.html#23">App\Actions\WorkOrders\TagWorkOrder</a></td><td class="text-right">8%</td></tr>
       <tr><td><a href="WorkOrders/UpdateNteAmount.php.html#16">App\Actions\WorkOrders\UpdateNteAmount</a></td><td class="text-right">9%</td></tr>
       <tr><td><a href="Notifications/ReadAllNotification.php.html#17">App\Actions\Notifications\ReadAllNotification</a></td><td class="text-right">10%</td></tr>
       <tr><td><a href="Quotes/BaseAction.php.html#14">App\Actions\Quotes\BaseAction</a></td><td class="text-right">23%</td></tr>
       <tr><td><a href="ServiceRequest/ScopingServiceRequest.php.html#11">App\Actions\ServiceRequest\ScopingServiceRequest</a></td><td class="text-right">25%</td></tr>
       <tr><td><a href="ServiceRequest/BaseAction.php.html#11">App\Actions\ServiceRequest\BaseAction</a></td><td class="text-right">27%</td></tr>
       <tr><td><a href="Invoices/BaseAction.php.html#12">App\Actions\Invoices\BaseAction</a></td><td class="text-right">44%</td></tr>
       <tr><td><a href="Issue/BaseAction.php.html#10">App\Actions\Issue\BaseAction</a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="Vendors/BaseAction.php.html#9">App\Actions\Vendors\BaseAction</a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="WorkOrderIssue/BaseAction.php.html#10">App\Actions\WorkOrderIssue\BaseAction</a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="Notifications/BaseAction.php.html#9">App\Actions\Notifications\BaseAction</a></td><td class="text-right">66%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrders/UpdateWorkOrderTrip.php.html#36">App\Actions\WorkOrders\UpdateWorkOrderTrip</a></td><td class="text-right">2078</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#30">App\Actions\WorkOrderIssue\UpdateWorkOrderIssue</a></td><td class="text-right">1099</td></tr>
       <tr><td><a href="Quotes/UpdateQuote.php.html#23">App\Actions\Quotes\UpdateQuote</a></td><td class="text-right">1033</td></tr>
       <tr><td><a href="Quotes/CreateQuote.php.html#42">App\Actions\Quotes\CreateQuote</a></td><td class="text-right">1017</td></tr>
       <tr><td><a href="WorkOrders/CompleteWorkOrder.php.html#41">App\Actions\WorkOrders\CompleteWorkOrder</a></td><td class="text-right">973</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#34">App\Actions\WorkOrderIssue\MarkAsDoneIssue</a></td><td class="text-right">912</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResidentAvailability.php.html#30">App\Actions\ServiceRequest\UpdateResidentAvailability</a></td><td class="text-right">845</td></tr>
       <tr><td><a href="WorkOrders/TechnicianSchedule.php.html#37">App\Actions\WorkOrders\TechnicianSchedule</a></td><td class="text-right">841</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoiceWorkOrder.php.html#28">App\Actions\WorkOrders\ReadyToInvoiceWorkOrder</a></td><td class="text-right">679</td></tr>
       <tr><td><a href="Quotes/ApproveQuote.php.html#30">App\Actions\Quotes\ApproveQuote</a></td><td class="text-right">616</td></tr>
       <tr><td><a href="Quotes/UpdateQuoteTask.php.html#37">App\Actions\Quotes\UpdateQuoteTask</a></td><td class="text-right">586</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#39">App\Actions\WorkOrders\CancelWorkOrder</a></td><td class="text-right">584</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#37">App\Actions\WorkOrders\CloseWorkOrder</a></td><td class="text-right">559</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#28">App\Actions\WorkOrders\UpdateTripDetails</a></td><td class="text-right">514</td></tr>
       <tr><td><a href="Quotes/CreateQuoteTask.php.html#35">App\Actions\Quotes\CreateQuoteTask</a></td><td class="text-right">480</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#34">App\Actions\Issue\CancelIssue</a></td><td class="text-right">466</td></tr>
       <tr><td><a href="ServiceRequest/AddResidentAvailability.php.html#31">App\Actions\ServiceRequest\AddResidentAvailability</a></td><td class="text-right">442</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssue.php.html#36">App\Actions\WorkOrderIssue\DeclineIssue</a></td><td class="text-right">441</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#35">App\Actions\Issue\DeleteIssue</a></td><td class="text-right">341</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#32">App\Actions\WorkOrders\DeleteWorkOrder</a></td><td class="text-right">328</td></tr>
       <tr><td><a href="WorkOrders/ScheduleToVendor.php.html#32">App\Actions\WorkOrders\ScheduleToVendor</a></td><td class="text-right">292</td></tr>
       <tr><td><a href="WorkOrders/PauseWorkOrder.php.html#29">App\Actions\WorkOrders\PauseWorkOrder</a></td><td class="text-right">290</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsPendingIssue.php.html#30">App\Actions\WorkOrderIssue\MarkAsPendingIssue</a></td><td class="text-right">289</td></tr>
       <tr><td><a href="WorkOrders/BaseAction.php.html#24">App\Actions\WorkOrders\BaseAction</a></td><td class="text-right">255</td></tr>
       <tr><td><a href="Quotes/RejectQuote.php.html#29">App\Actions\Quotes\RejectQuote</a></td><td class="text-right">253</td></tr>
       <tr><td><a href="Issue/UnassignIssue.php.html#35">App\Actions\Issue\UnassignIssue</a></td><td class="text-right">247</td></tr>
       <tr><td><a href="Invoices/Invoicing.php.html#28">App\Actions\Invoices\Invoicing</a></td><td class="text-right">243</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailabilityWorkOrder.php.html#26">App\Actions\WorkOrders\AwaitingAvailabilityWorkOrder</a></td><td class="text-right">231</td></tr>
       <tr><td><a href="Issue/UpdateIssue.php.html#28">App\Actions\Issue\UpdateIssue</a></td><td class="text-right">223</td></tr>
       <tr><td><a href="WorkOrders/StartWork.php.html#24">App\Actions\WorkOrders\StartWork</a></td><td class="text-right">221</td></tr>
       <tr><td><a href="Quotes/DeleteQuoteTask.php.html#28">App\Actions\Quotes\DeleteQuoteTask</a></td><td class="text-right">199</td></tr>
       <tr><td><a href="WorkOrders/PauseTimer.php.html#24">App\Actions\WorkOrders\PauseTimer</a></td><td class="text-right">194</td></tr>
       <tr><td><a href="WorkOrders/ResumeWork.php.html#25">App\Actions\WorkOrders\ResumeWork</a></td><td class="text-right">193</td></tr>
       <tr><td><a href="WorkOrders/PauseEnRoute.php.html#25">App\Actions\WorkOrders\PauseEnRoute</a></td><td class="text-right">193</td></tr>
       <tr><td><a href="WorkOrders/ResumeEnRoute.php.html#25">App\Actions\WorkOrders\ResumeEnRoute</a></td><td class="text-right">193</td></tr>
       <tr><td><a href="Issue/UndoIssue.php.html#29">App\Actions\Issue\UndoIssue</a></td><td class="text-right">190</td></tr>
       <tr><td><a href="Issue/AssignIssue.php.html#30">App\Actions\Issue\AssignIssue</a></td><td class="text-right">186</td></tr>
       <tr><td><a href="ServiceRequest/RequestResidentAvailability.php.html#25">App\Actions\ServiceRequest\RequestResidentAvailability</a></td><td class="text-right">172</td></tr>
       <tr><td><a href="Quotes/SubmitForApproval.php.html#30">App\Actions\Quotes\SubmitForApproval</a></td><td class="text-right">168</td></tr>
       <tr><td><a href="WorkOrders/RescheduleWorkOrder.php.html#25">App\Actions\WorkOrders\RescheduleWorkOrder</a></td><td class="text-right">167</td></tr>
       <tr><td><a href="WorkOrders/ReadyToScheduleWorkOrder.php.html#24">App\Actions\WorkOrders\ReadyToScheduleWorkOrder</a></td><td class="text-right">160</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResident.php.html#21">App\Actions\ServiceRequest\UpdateResident</a></td><td class="text-right">147</td></tr>
       <tr><td><a href="WorkOrders/ScheduleWorkOrder.php.html#22">App\Actions\WorkOrders\ScheduleWorkOrder</a></td><td class="text-right">146</td></tr>
       <tr><td><a href="WorkOrderIssue/AssignIssue.php.html#26">App\Actions\WorkOrderIssue\AssignIssue</a></td><td class="text-right">146</td></tr>
       <tr><td><a href="Invoices/FullyPaidInvoice.php.html#27">App\Actions\Invoices\FullyPaidInvoice</a></td><td class="text-right">145</td></tr>
       <tr><td><a href="Invoices/PartiallyPaidInvoice.php.html#26">App\Actions\Invoices\PartiallyPaidInvoice</a></td><td class="text-right">144</td></tr>
       <tr><td><a href="WorkOrders/EnRouteWork.php.html#25">App\Actions\WorkOrders\EnRouteWork</a></td><td class="text-right">142</td></tr>
       <tr><td><a href="WorkOrderIssue/CreateIssue.php.html#29">App\Actions\WorkOrderIssue\CreateIssue</a></td><td class="text-right">139</td></tr>
       <tr><td><a href="Issue/RestoreIssue.php.html#28">App\Actions\Issue\RestoreIssue</a></td><td class="text-right">129</td></tr>
       <tr><td><a href="Vendors/SyncVendor.php.html#20">App\Actions\Vendors\SyncVendor</a></td><td class="text-right">125</td></tr>
       <tr><td><a href="ServiceRequest/InProgressServiceRequest.php.html#24">App\Actions\ServiceRequest\InProgressServiceRequest</a></td><td class="text-right">125</td></tr>
       <tr><td><a href="Issue/CreateIssue.php.html#28">App\Actions\Issue\CreateIssue</a></td><td class="text-right">124</td></tr>
       <tr><td><a href="ServiceRequest/AwaitingAvailabilityServiceRequest.php.html#25">App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest</a></td><td class="text-right">124</td></tr>
       <tr><td><a href="ServiceRequest/ClosedServiceRequest.php.html#24">App\Actions\ServiceRequest\ClosedServiceRequest</a></td><td class="text-right">124</td></tr>
       <tr><td><a href="ServiceRequest/CreateWorkOrderServiceRequest.php.html#24">App\Actions\ServiceRequest\CreateWorkOrderServiceRequest</a></td><td class="text-right">124</td></tr>
       <tr><td><a href="WorkOrderIssue/UnassignIssue.php.html#26">App\Actions\WorkOrderIssue\UnassignIssue</a></td><td class="text-right">123</td></tr>
       <tr><td><a href="Invoices/DeleteInvoice.php.html#25">App\Actions\Invoices\DeleteInvoice</a></td><td class="text-right">122</td></tr>
       <tr><td><a href="WorkOrders/ReOpenWorkOrder.php.html#24">App\Actions\WorkOrders\ReOpenWorkOrder</a></td><td class="text-right">122</td></tr>
       <tr><td><a href="Quotes/RestoreQuote.php.html#21">App\Actions\Quotes\RestoreQuote</a></td><td class="text-right">122</td></tr>
       <tr><td><a href="WorkOrders/ResolveWorkOrder.php.html#23">App\Actions\WorkOrders\ResolveWorkOrder</a></td><td class="text-right">122</td></tr>
       <tr><td><a href="Vendors/SyncAllVendors.php.html#20">App\Actions\Vendors\SyncAllVendors</a></td><td class="text-right">117</td></tr>
       <tr><td><a href="Notifications/ClearAllNotification.php.html#18">App\Actions\Notifications\ClearAllNotification</a></td><td class="text-right">107</td></tr>
       <tr><td><a href="Vendors/UpdateVendorStatus.php.html#19">App\Actions\Vendors\UpdateVendorStatus</a></td><td class="text-right">106</td></tr>
       <tr><td><a href="WorkOrders/TagWorkOrder.php.html#23">App\Actions\WorkOrders\TagWorkOrder</a></td><td class="text-right">103</td></tr>
       <tr><td><a href="Invoices/VoidInvoice.php.html#24">App\Actions\Invoices\VoidInvoice</a></td><td class="text-right">102</td></tr>
       <tr><td><a href="Quotes/BaseAction.php.html#14">App\Actions\Quotes\BaseAction</a></td><td class="text-right">88</td></tr>
       <tr><td><a href="WorkOrders/UpdateNteAmount.php.html#16">App\Actions\WorkOrders\UpdateNteAmount</a></td><td class="text-right">85</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateIssueBasicDetails.php.html#22">App\Actions\WorkOrderIssue\UpdateIssueBasicDetails</a></td><td class="text-right">84</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePropertyAddress.php.html#21">App\Actions\ServiceRequest\UpdatePropertyAddress</a></td><td class="text-right">83</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateDeclineReason.php.html#20">App\Actions\WorkOrderIssue\UpdateDeclineReason</a></td><td class="text-right">79</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePriority.php.html#18">App\Actions\ServiceRequest\UpdatePriority</a></td><td class="text-right">78</td></tr>
       <tr><td><a href="Notifications/ClearNotification.php.html#17">App\Actions\Notifications\ClearNotification</a></td><td class="text-right">78</td></tr>
       <tr><td><a href="Notifications/UnclearNotification.php.html#16">App\Actions\Notifications\UnclearNotification</a></td><td class="text-right">78</td></tr>
       <tr><td><a href="Notifications/ReadNotification.php.html#17">App\Actions\Notifications\ReadNotification</a></td><td class="text-right">77</td></tr>
       <tr><td><a href="Notifications/ReadAllNotification.php.html#17">App\Actions\Notifications\ReadAllNotification</a></td><td class="text-right">68</td></tr>
       <tr><td><a href="ServiceRequest/BaseAction.php.html#11">App\Actions\ServiceRequest\BaseAction</a></td><td class="text-right">32</td></tr>
       <tr><td><a href="Invoices/BaseAction.php.html#12">App\Actions\Invoices\BaseAction</a></td><td class="text-right">15</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Invoices/BaseAction.php.html#16"><abbr title="App\Actions\Invoices\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/BaseAction.php.html#36"><abbr title="App\Actions\Invoices\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/BaseAction.php.html#44"><abbr title="App\Actions\Invoices\BaseAction::validateRouteParameterRelation">validateRouteParameterRelation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/DeleteInvoice.php.html#35"><abbr title="App\Actions\Invoices\DeleteInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/DeleteInvoice.php.html#58"><abbr title="App\Actions\Invoices\DeleteInvoice::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/DeleteInvoice.php.html#101"><abbr title="App\Actions\Invoices\DeleteInvoice::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/FullyPaidInvoice.php.html#37"><abbr title="App\Actions\Invoices\FullyPaidInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/FullyPaidInvoice.php.html#50"><abbr title="App\Actions\Invoices\FullyPaidInvoice::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/FullyPaidInvoice.php.html#106"><abbr title="App\Actions\Invoices\FullyPaidInvoice::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Invoicing.php.html#44"><abbr title="App\Actions\Invoices\Invoicing::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Invoicing.php.html#53"><abbr title="App\Actions\Invoices\Invoicing::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/Invoicing.php.html#124"><abbr title="App\Actions\Invoices\Invoicing::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PartiallyPaidInvoice.php.html#36"><abbr title="App\Actions\Invoices\PartiallyPaidInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PartiallyPaidInvoice.php.html#48"><abbr title="App\Actions\Invoices\PartiallyPaidInvoice::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/PartiallyPaidInvoice.php.html#100"><abbr title="App\Actions\Invoices\PartiallyPaidInvoice::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/VoidInvoice.php.html#34"><abbr title="App\Actions\Invoices\VoidInvoice::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/VoidInvoice.php.html#46"><abbr title="App\Actions\Invoices\VoidInvoice::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoices/VoidInvoice.php.html#97"><abbr title="App\Actions\Invoices\VoidInvoice::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/AssignIssue.php.html#49"><abbr title="App\Actions\Issue\AssignIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/AssignIssue.php.html#66"><abbr title="App\Actions\Issue\AssignIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/AssignIssue.php.html#125"><abbr title="App\Actions\Issue\AssignIssue::isIssueAlreadyAssigned">isIssueAlreadyAssigned</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/AssignIssue.php.html#133"><abbr title="App\Actions\Issue\AssignIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/BaseAction.php.html#14"><abbr title="App\Actions\Issue\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/BaseAction.php.html#33"><abbr title="App\Actions\Issue\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#48"><abbr title="App\Actions\Issue\CancelIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#75"><abbr title="App\Actions\Issue\CancelIssue::getAllAssociatedWorkOrders">getAllAssociatedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#96"><abbr title="App\Actions\Issue\CancelIssue::hasAnyScheduledOrInprogressWorkOrderExists">hasAnyScheduledOrInprogressWorkOrderExists</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#117"><abbr title="App\Actions\Issue\CancelIssue::getWorkOrdersWantToCancel">getWorkOrdersWantToCancel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#129"><abbr title="App\Actions\Issue\CancelIssue::cancelWorkOrder">cancelWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#140"><abbr title="App\Actions\Issue\CancelIssue::cancelWorkOrderIssues">cancelWorkOrderIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#156"><abbr title="App\Actions\Issue\CancelIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#202"><abbr title="App\Actions\Issue\CancelIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CreateIssue.php.html#42"><abbr title="App\Actions\Issue\CreateIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CreateIssue.php.html#59"><abbr title="App\Actions\Issue\CreateIssue::getProblemDiagnosis">getProblemDiagnosis</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CreateIssue.php.html#75"><abbr title="App\Actions\Issue\CreateIssue::createIssue">createIssue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CreateIssue.php.html#96"><abbr title="App\Actions\Issue\CreateIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/CreateIssue.php.html#131"><abbr title="App\Actions\Issue\CreateIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#51"><abbr title="App\Actions\Issue\DeleteIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#75"><abbr title="App\Actions\Issue\DeleteIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#103"><abbr title="App\Actions\Issue\DeleteIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#118"><abbr title="App\Actions\Issue\DeleteIssue::hasAnyScheduledOrInprogressWorkOrderExists">hasAnyScheduledOrInprogressWorkOrderExists</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#136"><abbr title="App\Actions\Issue\DeleteIssue::getWorkOrderIssues">getWorkOrderIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#153"><abbr title="App\Actions\Issue\DeleteIssue::handleWorkOrderIssues">handleWorkOrderIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/RestoreIssue.php.html#44"><abbr title="App\Actions\Issue\RestoreIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/RestoreIssue.php.html#60"><abbr title="App\Actions\Issue\RestoreIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/RestoreIssue.php.html#100"><abbr title="App\Actions\Issue\RestoreIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UnassignIssue.php.html#54"><abbr title="App\Actions\Issue\UnassignIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UnassignIssue.php.html#90"><abbr title="App\Actions\Issue\UnassignIssue::checkWorkOrderHasIssues">checkWorkOrderHasIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UnassignIssue.php.html#103"><abbr title="App\Actions\Issue\UnassignIssue::checkIssueHasAssigned">checkIssueHasAssigned</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UnassignIssue.php.html#113"><abbr title="App\Actions\Issue\UnassignIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UnassignIssue.php.html#165"><abbr title="App\Actions\Issue\UnassignIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UndoIssue.php.html#45"><abbr title="App\Actions\Issue\UndoIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UndoIssue.php.html#60"><abbr title="App\Actions\Issue\UndoIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UndoIssue.php.html#103"><abbr title="App\Actions\Issue\UndoIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UndoIssue.php.html#108"><abbr title="App\Actions\Issue\UndoIssue::restoreIssue">restoreIssue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UndoIssue.php.html#118"><abbr title="App\Actions\Issue\UndoIssue::deleteActivityLogs">deleteActivityLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UpdateIssue.php.html#51"><abbr title="App\Actions\Issue\UpdateIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UpdateIssue.php.html#112"><abbr title="App\Actions\Issue\UpdateIssue::triggerBroadCasts">triggerBroadCasts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UpdateIssue.php.html#131"><abbr title="App\Actions\Issue\UpdateIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/UpdateIssue.php.html#178"><abbr title="App\Actions\Issue\UpdateIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/BaseAction.php.html#13"><abbr title="App\Actions\Notifications\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/BaseAction.php.html#33"><abbr title="App\Actions\Notifications\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ClearAllNotification.php.html#37"><abbr title="App\Actions\Notifications\ClearAllNotification::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ClearAllNotification.php.html#49"><abbr title="App\Actions\Notifications\ClearAllNotification::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ClearAllNotification.php.html#85"><abbr title="App\Actions\Notifications\ClearAllNotification::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ClearNotification.php.html#26"><abbr title="App\Actions\Notifications\ClearNotification::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ClearNotification.php.html#36"><abbr title="App\Actions\Notifications\ClearNotification::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ClearNotification.php.html#65"><abbr title="App\Actions\Notifications\ClearNotification::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ReadAllNotification.php.html#31"><abbr title="App\Actions\Notifications\ReadAllNotification::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ReadAllNotification.php.html#43"><abbr title="App\Actions\Notifications\ReadAllNotification::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ReadAllNotification.php.html#73"><abbr title="App\Actions\Notifications\ReadAllNotification::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ReadNotification.php.html#26"><abbr title="App\Actions\Notifications\ReadNotification::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ReadNotification.php.html#35"><abbr title="App\Actions\Notifications\ReadNotification::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/ReadNotification.php.html#64"><abbr title="App\Actions\Notifications\ReadNotification::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/UnclearNotification.php.html#25"><abbr title="App\Actions\Notifications\UnclearNotification::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/UnclearNotification.php.html#35"><abbr title="App\Actions\Notifications\UnclearNotification::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Notifications/UnclearNotification.php.html#64"><abbr title="App\Actions\Notifications\UnclearNotification::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/ApproveQuote.php.html#48"><abbr title="App\Actions\Quotes\ApproveQuote::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/ApproveQuote.php.html#131"><abbr title="App\Actions\Quotes\ApproveQuote::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/ApproveQuote.php.html#219"><abbr title="App\Actions\Quotes\ApproveQuote::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/BaseAction.php.html#18"><abbr title="App\Actions\Quotes\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/BaseAction.php.html#38"><abbr title="App\Actions\Quotes\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/BaseAction.php.html#46"><abbr title="App\Actions\Quotes\BaseAction::validateRouteParameterRelation">validateRouteParameterRelation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/CreateQuote.php.html#59"><abbr title="App\Actions\Quotes\CreateQuote::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/CreateQuote.php.html#197"><abbr title="App\Actions\Quotes\CreateQuote::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/CreateQuote.php.html#290"><abbr title="App\Actions\Quotes\CreateQuote::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/CreateQuoteTask.php.html#52"><abbr title="App\Actions\Quotes\CreateQuoteTask::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/CreateQuoteTask.php.html#149"><abbr title="App\Actions\Quotes\CreateQuoteTask::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/CreateQuoteTask.php.html#210"><abbr title="App\Actions\Quotes\CreateQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/DeleteQuoteTask.php.html#39"><abbr title="App\Actions\Quotes\DeleteQuoteTask::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/DeleteQuoteTask.php.html#75"><abbr title="App\Actions\Quotes\DeleteQuoteTask::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/DeleteQuoteTask.php.html#130"><abbr title="App\Actions\Quotes\DeleteQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/RejectQuote.php.html#43"><abbr title="App\Actions\Quotes\RejectQuote::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/RejectQuote.php.html#91"><abbr title="App\Actions\Quotes\RejectQuote::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/RejectQuote.php.html#162"><abbr title="App\Actions\Quotes\RejectQuote::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/RestoreQuote.php.html#30"><abbr title="App\Actions\Quotes\RestoreQuote::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/SubmitForApproval.php.html#44"><abbr title="App\Actions\Quotes\SubmitForApproval::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/SubmitForApproval.php.html#62"><abbr title="App\Actions\Quotes\SubmitForApproval::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/SubmitForApproval.php.html#147"><abbr title="App\Actions\Quotes\SubmitForApproval::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/UpdateQuote.php.html#55"><abbr title="App\Actions\Quotes\UpdateQuote::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/UpdateQuoteTask.php.html#73"><abbr title="App\Actions\Quotes\UpdateQuoteTask::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/UpdateQuoteTask.php.html#188"><abbr title="App\Actions\Quotes\UpdateQuoteTask::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quotes/UpdateQuoteTask.php.html#254"><abbr title="App\Actions\Quotes\UpdateQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/AddResidentAvailability.php.html#43"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/AddResidentAvailability.php.html#107"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/AddResidentAvailability.php.html#162"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/AwaitingAvailabilityServiceRequest.php.html#34"><abbr title="App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/AwaitingAvailabilityServiceRequest.php.html#50"><abbr title="App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/AwaitingAvailabilityServiceRequest.php.html#113"><abbr title="App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseAction.php.html#15"><abbr title="App\Actions\ServiceRequest\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseAction.php.html#34"><abbr title="App\Actions\ServiceRequest\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseAction.php.html#42"><abbr title="App\Actions\ServiceRequest\BaseAction::validateServiceRequest">validateServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ClosedServiceRequest.php.html#33"><abbr title="App\Actions\ServiceRequest\ClosedServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ClosedServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\ClosedServiceRequest::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ClosedServiceRequest.php.html#107"><abbr title="App\Actions\ServiceRequest\ClosedServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/CreateWorkOrderServiceRequest.php.html#33"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/CreateWorkOrderServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/CreateWorkOrderServiceRequest.php.html#107"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/InProgressServiceRequest.php.html#33"><abbr title="App\Actions\ServiceRequest\InProgressServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/InProgressServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\InProgressServiceRequest::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/InProgressServiceRequest.php.html#112"><abbr title="App\Actions\ServiceRequest\InProgressServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/RequestResidentAvailability.php.html#34"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/RequestResidentAvailability.php.html#59"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/RequestResidentAvailability.php.html#115"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ScopingServiceRequest.php.html#20"><abbr title="App\Actions\ServiceRequest\ScopingServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePriority.php.html#32"><abbr title="App\Actions\ServiceRequest\UpdatePriority::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePriority.php.html#42"><abbr title="App\Actions\ServiceRequest\UpdatePriority::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePriority.php.html#74"><abbr title="App\Actions\ServiceRequest\UpdatePriority::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePropertyAddress.php.html#43"><abbr title="App\Actions\ServiceRequest\UpdatePropertyAddress::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePropertyAddress.php.html#68"><abbr title="App\Actions\ServiceRequest\UpdatePropertyAddress::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePropertyAddress.php.html#115"><abbr title="App\Actions\ServiceRequest\UpdatePropertyAddress::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResident.php.html#42"><abbr title="App\Actions\ServiceRequest\UpdateResident::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResident.php.html#83"><abbr title="App\Actions\ServiceRequest\UpdateResident::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResident.php.html#134"><abbr title="App\Actions\ServiceRequest\UpdateResident::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResidentAvailability.php.html#44"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResidentAvailability.php.html#176"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResidentAvailability.php.html#219"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendors/BaseAction.php.html#13"><abbr title="App\Actions\Vendors\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendors/BaseAction.php.html#32"><abbr title="App\Actions\Vendors\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendors/SyncAllVendors.php.html#29"><abbr title="App\Actions\Vendors\SyncAllVendors::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendors/SyncAllVendors.php.html#37"><abbr title="App\Actions\Vendors\SyncAllVendors::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendors/SyncAllVendors.php.html#77"><abbr title="App\Actions\Vendors\SyncAllVendors::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendors/SyncVendor.php.html#34"><abbr title="App\Actions\Vendors\SyncVendor::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendors/SyncVendor.php.html#42"><abbr title="App\Actions\Vendors\SyncVendor::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendors/SyncVendor.php.html#82"><abbr title="App\Actions\Vendors\SyncVendor::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendors/UpdateVendorStatus.php.html#35"><abbr title="App\Actions\Vendors\UpdateVendorStatus::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendors/UpdateVendorStatus.php.html#49"><abbr title="App\Actions\Vendors\UpdateVendorStatus::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Vendors/UpdateVendorStatus.php.html#83"><abbr title="App\Actions\Vendors\UpdateVendorStatus::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/AssignIssue.php.html#35"><abbr title="App\Actions\WorkOrderIssue\AssignIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/AssignIssue.php.html#43"><abbr title="App\Actions\WorkOrderIssue\AssignIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/AssignIssue.php.html#105"><abbr title="App\Actions\WorkOrderIssue\AssignIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/BaseAction.php.html#14"><abbr title="App\Actions\WorkOrderIssue\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/BaseAction.php.html#33"><abbr title="App\Actions\WorkOrderIssue\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/CreateIssue.php.html#48"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/CreateIssue.php.html#64"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/CreateIssue.php.html#123"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssue.php.html#48"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssue.php.html#70"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::changeIssueState">changeIssueState</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssue.php.html#87"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::assignedIssueExists">assignedIssueExists</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssue.php.html#95"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssue.php.html#165"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#48"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#101"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#192"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#202"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::handleIssueMedia">handleIssueMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#225"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::cancelIssuesAndRelatedWorkOrders">cancelIssuesAndRelatedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#274"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsPendingIssue.php.html#44"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsPendingIssue.php.html#78"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsPendingIssue.php.html#110"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsPendingIssue.php.html#115"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::clearDoneStateResources">clearDoneStateResources</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UnassignIssue.php.html#35"><abbr title="App\Actions\WorkOrderIssue\UnassignIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UnassignIssue.php.html#43"><abbr title="App\Actions\WorkOrderIssue\UnassignIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UnassignIssue.php.html#100"><abbr title="App\Actions\WorkOrderIssue\UnassignIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateDeclineReason.php.html#34"><abbr title="App\Actions\WorkOrderIssue\UpdateDeclineReason::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateDeclineReason.php.html#46"><abbr title="App\Actions\WorkOrderIssue\UpdateDeclineReason::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateDeclineReason.php.html#76"><abbr title="App\Actions\WorkOrderIssue\UpdateDeclineReason::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateIssueBasicDetails.php.html#40"><abbr title="App\Actions\WorkOrderIssue\UpdateIssueBasicDetails::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateIssueBasicDetails.php.html#48"><abbr title="App\Actions\WorkOrderIssue\UpdateIssueBasicDetails::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateIssueBasicDetails.php.html#103"><abbr title="App\Actions\WorkOrderIssue\UpdateIssueBasicDetails::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#44"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#66"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#148"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#158"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::updateMaterials">updateMaterials</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#208"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::authorizeTechnicianAccess">authorizeTechnicianAccess</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#227"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#261"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::handleIssueMedia">handleIssueMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailabilityWorkOrder.php.html#35"><abbr title="App\Actions\WorkOrders\AwaitingAvailabilityWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailabilityWorkOrder.php.html#48"><abbr title="App\Actions\WorkOrders\AwaitingAvailabilityWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailabilityWorkOrder.php.html#154"><abbr title="App\Actions\WorkOrders\AwaitingAvailabilityWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/BaseAction.php.html#28"><abbr title="App\Actions\WorkOrders\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/BaseAction.php.html#47"><abbr title="App\Actions\WorkOrders\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/BaseAction.php.html#55"><abbr title="App\Actions\WorkOrders\BaseAction::validateWorkOrder">validateWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/BaseAction.php.html#69"><abbr title="App\Actions\WorkOrders\BaseAction::handleException">handleException</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/BaseAction.php.html#90"><abbr title="App\Actions\WorkOrders\BaseAction::canPerformTripActionByUser">canPerformTripActionByUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#48"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#93"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::cancelWorkOrderIssues">cancelWorkOrderIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#115"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::updateIssueStatus">updateIssueStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#126"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::canIssueStatusBeUpdatedToUnassigned">canIssueStatusBeUpdatedToUnassigned</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#134"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#218"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#56"><abbr title="App\Actions\WorkOrders\CloseWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#104"><abbr title="App\Actions\WorkOrders\CloseWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#173"><abbr title="App\Actions\WorkOrders\CloseWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#181"><abbr title="App\Actions\WorkOrders\CloseWorkOrder::checkAllIssuesAreReadyToClose">checkAllIssuesAreReadyToClose</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#194"><abbr title="App\Actions\WorkOrders\CloseWorkOrder::updateWorkOrderIssusStatusToQualityCheck">updateWorkOrderIssusStatusToQualityCheck</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#200"><abbr title="App\Actions\WorkOrders\CloseWorkOrder::updateIssusStatusToQualityCheck">updateIssusStatusToQualityCheck</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CompleteWorkOrder.php.html#53"><abbr title="App\Actions\WorkOrders\CompleteWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CompleteWorkOrder.php.html#136"><abbr title="App\Actions\WorkOrders\CompleteWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/CompleteWorkOrder.php.html#270"><abbr title="App\Actions\WorkOrders\CompleteWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#48"><abbr title="App\Actions\WorkOrders\DeleteWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#61"><abbr title="App\Actions\WorkOrders\DeleteWorkOrder::deleteWorkOrderIssues">deleteWorkOrderIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#85"><abbr title="App\Actions\WorkOrders\DeleteWorkOrder::updateIssueStatus">updateIssueStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#95"><abbr title="App\Actions\WorkOrders\DeleteWorkOrder::canIssueStatusBeUpdatedToUnassigned">canIssueStatusBeUpdatedToUnassigned</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#103"><abbr title="App\Actions\WorkOrders\DeleteWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#129"><abbr title="App\Actions\WorkOrders\DeleteWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/EnRouteWork.php.html#41"><abbr title="App\Actions\WorkOrders\EnRouteWork::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/EnRouteWork.php.html#63"><abbr title="App\Actions\WorkOrders\EnRouteWork::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/EnRouteWork.php.html#135"><abbr title="App\Actions\WorkOrders\EnRouteWork::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/PauseEnRoute.php.html#44"><abbr title="App\Actions\WorkOrders\PauseEnRoute::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/PauseEnRoute.php.html#74"><abbr title="App\Actions\WorkOrders\PauseEnRoute::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/PauseEnRoute.php.html#151"><abbr title="App\Actions\WorkOrders\PauseEnRoute::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/PauseTimer.php.html#43"><abbr title="App\Actions\WorkOrders\PauseTimer::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/PauseTimer.php.html#75"><abbr title="App\Actions\WorkOrders\PauseTimer::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/PauseTimer.php.html#154"><abbr title="App\Actions\WorkOrders\PauseTimer::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/PauseWorkOrder.php.html#38"><abbr title="App\Actions\WorkOrders\PauseWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/PauseWorkOrder.php.html#54"><abbr title="App\Actions\WorkOrders\PauseWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/PauseWorkOrder.php.html#129"><abbr title="App\Actions\WorkOrders\PauseWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReOpenWorkOrder.php.html#33"><abbr title="App\Actions\WorkOrders\ReOpenWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReOpenWorkOrder.php.html#46"><abbr title="App\Actions\WorkOrders\ReOpenWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReOpenWorkOrder.php.html#100"><abbr title="App\Actions\WorkOrders\ReOpenWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoiceWorkOrder.php.html#40"><abbr title="App\Actions\WorkOrders\ReadyToInvoiceWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoiceWorkOrder.php.html#119"><abbr title="App\Actions\WorkOrders\ReadyToInvoiceWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoiceWorkOrder.php.html#182"><abbr title="App\Actions\WorkOrders\ReadyToInvoiceWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToScheduleWorkOrder.php.html#40"><abbr title="App\Actions\WorkOrders\ReadyToScheduleWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToScheduleWorkOrder.php.html#58"><abbr title="App\Actions\WorkOrders\ReadyToScheduleWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ReadyToScheduleWorkOrder.php.html#136"><abbr title="App\Actions\WorkOrders\ReadyToScheduleWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/RescheduleWorkOrder.php.html#29"><abbr title="App\Actions\WorkOrders\RescheduleWorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/RescheduleWorkOrder.php.html#41"><abbr title="App\Actions\WorkOrders\RescheduleWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/RescheduleWorkOrder.php.html#65"><abbr title="App\Actions\WorkOrders\RescheduleWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/RescheduleWorkOrder.php.html#135"><abbr title="App\Actions\WorkOrders\RescheduleWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ResolveWorkOrder.php.html#32"><abbr title="App\Actions\WorkOrders\ResolveWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ResolveWorkOrder.php.html#45"><abbr title="App\Actions\WorkOrders\ResolveWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ResolveWorkOrder.php.html#97"><abbr title="App\Actions\WorkOrders\ResolveWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ResumeEnRoute.php.html#41"><abbr title="App\Actions\WorkOrders\ResumeEnRoute::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ResumeEnRoute.php.html#73"><abbr title="App\Actions\WorkOrders\ResumeEnRoute::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ResumeEnRoute.php.html#148"><abbr title="App\Actions\WorkOrders\ResumeEnRoute::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ResumeWork.php.html#41"><abbr title="App\Actions\WorkOrders\ResumeWork::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ResumeWork.php.html#74"><abbr title="App\Actions\WorkOrders\ResumeWork::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ResumeWork.php.html#152"><abbr title="App\Actions\WorkOrders\ResumeWork::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ScheduleToVendor.php.html#51"><abbr title="App\Actions\WorkOrders\ScheduleToVendor::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ScheduleToVendor.php.html#107"><abbr title="App\Actions\WorkOrders\ScheduleToVendor::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ScheduleToVendor.php.html#214"><abbr title="App\Actions\WorkOrders\ScheduleToVendor::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ScheduleWorkOrder.php.html#26"><abbr title="App\Actions\WorkOrders\ScheduleWorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ScheduleWorkOrder.php.html#38"><abbr title="App\Actions\WorkOrders\ScheduleWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ScheduleWorkOrder.php.html#64"><abbr title="App\Actions\WorkOrders\ScheduleWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/ScheduleWorkOrder.php.html#159"><abbr title="App\Actions\WorkOrders\ScheduleWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/StartWork.php.html#38"><abbr title="App\Actions\WorkOrders\StartWork::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/StartWork.php.html#72"><abbr title="App\Actions\WorkOrders\StartWork::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/StartWork.php.html#148"><abbr title="App\Actions\WorkOrders\StartWork::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/TagWorkOrder.php.html#41"><abbr title="App\Actions\WorkOrders\TagWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/TagWorkOrder.php.html#66"><abbr title="App\Actions\WorkOrders\TagWorkOrder::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/TagWorkOrder.php.html#99"><abbr title="App\Actions\WorkOrders\TagWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/TechnicianSchedule.php.html#41"><abbr title="App\Actions\WorkOrders\TechnicianSchedule::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/TechnicianSchedule.php.html#53"><abbr title="App\Actions\WorkOrders\TechnicianSchedule::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/TechnicianSchedule.php.html#81"><abbr title="App\Actions\WorkOrders\TechnicianSchedule::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/TechnicianSchedule.php.html#306"><abbr title="App\Actions\WorkOrders\TechnicianSchedule::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateNteAmount.php.html#35"><abbr title="App\Actions\WorkOrders\UpdateNteAmount::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateNteAmount.php.html#55"><abbr title="App\Actions\WorkOrders\UpdateNteAmount::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateNteAmount.php.html#77"><abbr title="App\Actions\WorkOrders\UpdateNteAmount::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#42"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#65"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#104"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::updateTripState">updateTripState</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#125"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::handleIssueMedia">handleIssueMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#139"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::updateOrCreateMaterial">updateOrCreateMaterial</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#178"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::updateNotes">updateNotes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#194"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateWorkOrderTrip.php.html#55"><abbr title="App\Actions\WorkOrders\UpdateWorkOrderTrip::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateWorkOrderTrip.php.html#230"><abbr title="App\Actions\WorkOrders\UpdateWorkOrderTrip::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrders/UpdateWorkOrderTrip.php.html#303"><abbr title="App\Actions\WorkOrders\UpdateWorkOrderTrip::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrders/UpdateWorkOrderTrip.php.html#55"><abbr title="App\Actions\WorkOrders\UpdateWorkOrderTrip::handle">handle</abbr></a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="Quotes/UpdateQuote.php.html#55"><abbr title="App\Actions\Quotes\UpdateQuote::handle">handle</abbr></a></td><td class="text-right">992</td></tr>
       <tr><td><a href="WorkOrders/TechnicianSchedule.php.html#81"><abbr title="App\Actions\WorkOrders\TechnicianSchedule::asController">asController</abbr></a></td><td class="text-right">552</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResidentAvailability.php.html#44"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::handle">handle</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="WorkOrders/CompleteWorkOrder.php.html#136"><abbr title="App\Actions\WorkOrders\CompleteWorkOrder::asController">asController</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="Quotes/CreateQuote.php.html#59"><abbr title="App\Actions\Quotes\CreateQuote::handle">handle</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoiceWorkOrder.php.html#40"><abbr title="App\Actions\WorkOrders\ReadyToInvoiceWorkOrder::handle">handle</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="Quotes/UpdateQuoteTask.php.html#73"><abbr title="App\Actions\Quotes\UpdateQuoteTask::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Quotes/ApproveQuote.php.html#48"><abbr title="App\Actions\Quotes\ApproveQuote::handle">handle</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="Quotes/CreateQuote.php.html#197"><abbr title="App\Actions\Quotes\CreateQuote::asController">asController</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssue.php.html#95"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::asController">asController</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#101"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::asController">asController</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="WorkOrders/CompleteWorkOrder.php.html#53"><abbr title="App\Actions\WorkOrders\CompleteWorkOrder::handle">handle</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="Invoices/Invoicing.php.html#53"><abbr title="App\Actions\Invoices\Invoicing::asController">asController</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Quotes/CreateQuoteTask.php.html#52"><abbr title="App\Actions\Quotes\CreateQuoteTask::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailabilityWorkOrder.php.html#48"><abbr title="App\Actions\WorkOrders\AwaitingAvailabilityWorkOrder::asController">asController</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="WorkOrders/PauseWorkOrder.php.html#54"><abbr title="App\Actions\WorkOrders\PauseWorkOrder::asController">asController</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Quotes/RestoreQuote.php.html#30"><abbr title="App\Actions\Quotes\RestoreQuote::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ServiceRequest/AddResidentAvailability.php.html#43"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Quotes/BaseAction.php.html#46"><abbr title="App\Actions\Quotes\BaseAction::validateRouteParameterRelation">validateRouteParameterRelation</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#66"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::asController">asController</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="WorkOrders/UpdateWorkOrderTrip.php.html#230"><abbr title="App\Actions\WorkOrders\UpdateWorkOrderTrip::asController">asController</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Invoices/FullyPaidInvoice.php.html#50"><abbr title="App\Actions\Invoices\FullyPaidInvoice::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Invoices/PartiallyPaidInvoice.php.html#48"><abbr title="App\Actions\Invoices\PartiallyPaidInvoice::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Issue/AssignIssue.php.html#66"><abbr title="App\Actions\Issue\AssignIssue::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#156"><abbr title="App\Actions\Issue\CancelIssue::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Quotes/ApproveQuote.php.html#131"><abbr title="App\Actions\Quotes\ApproveQuote::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Quotes/DeleteQuoteTask.php.html#75"><abbr title="App\Actions\Quotes\DeleteQuoteTask::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Quotes/SubmitForApproval.php.html#62"><abbr title="App\Actions\Quotes\SubmitForApproval::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ServiceRequest/AddResidentAvailability.php.html#107"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ServiceRequest/RequestResidentAvailability.php.html#59"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrderIssue/AssignIssue.php.html#43"><abbr title="App\Actions\WorkOrderIssue\AssignIssue::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#158"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::updateMaterials">updateMaterials</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#134"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoiceWorkOrder.php.html#119"><abbr title="App\Actions\WorkOrders\ReadyToInvoiceWorkOrder::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrders/ReadyToScheduleWorkOrder.php.html#58"><abbr title="App\Actions\WorkOrders\ReadyToScheduleWorkOrder::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Issue/RestoreIssue.php.html#60"><abbr title="App\Actions\Issue\RestoreIssue::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Issue/UnassignIssue.php.html#113"><abbr title="App\Actions\Issue\UnassignIssue::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Quotes/CreateQuoteTask.php.html#149"><abbr title="App\Actions\Quotes\CreateQuoteTask::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Quotes/UpdateQuoteTask.php.html#188"><abbr title="App\Actions\Quotes\UpdateQuoteTask::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequest/AwaitingAvailabilityServiceRequest.php.html#50"><abbr title="App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequest/ClosedServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\ClosedServiceRequest::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequest/CreateWorkOrderServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequest/InProgressServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\InProgressServiceRequest::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Vendors/SyncAllVendors.php.html#37"><abbr title="App\Actions\Vendors\SyncAllVendors::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Vendors/SyncVendor.php.html#42"><abbr title="App\Actions\Vendors\SyncVendor::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrderIssue/CreateIssue.php.html#64"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrderIssue/UnassignIssue.php.html#43"><abbr title="App\Actions\WorkOrderIssue\UnassignIssue::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#104"><abbr title="App\Actions\WorkOrders\CloseWorkOrder::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrders/EnRouteWork.php.html#63"><abbr title="App\Actions\WorkOrders\EnRouteWork::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrders/PauseEnRoute.php.html#74"><abbr title="App\Actions\WorkOrders\PauseEnRoute::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrders/PauseTimer.php.html#75"><abbr title="App\Actions\WorkOrders\PauseTimer::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrders/ReOpenWorkOrder.php.html#46"><abbr title="App\Actions\WorkOrders\ReOpenWorkOrder::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrders/RescheduleWorkOrder.php.html#65"><abbr title="App\Actions\WorkOrders\RescheduleWorkOrder::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrders/ResolveWorkOrder.php.html#45"><abbr title="App\Actions\WorkOrders\ResolveWorkOrder::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrders/ResumeEnRoute.php.html#73"><abbr title="App\Actions\WorkOrders\ResumeEnRoute::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrders/ResumeWork.php.html#74"><abbr title="App\Actions\WorkOrders\ResumeWork::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrders/ScheduleToVendor.php.html#107"><abbr title="App\Actions\WorkOrders\ScheduleToVendor::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrders/StartWork.php.html#72"><abbr title="App\Actions\WorkOrders\StartWork::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Invoices/DeleteInvoice.php.html#58"><abbr title="App\Actions\Invoices\DeleteInvoice::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Invoices/VoidInvoice.php.html#46"><abbr title="App\Actions\Invoices\VoidInvoice::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#75"><abbr title="App\Actions\Issue\DeleteIssue::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Issue/UndoIssue.php.html#60"><abbr title="App\Actions\Issue\UndoIssue::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Quotes/RejectQuote.php.html#43"><abbr title="App\Actions\Quotes\RejectQuote::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Quotes/RejectQuote.php.html#91"><abbr title="App\Actions\Quotes\RejectQuote::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResidentAvailability.php.html#176"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Vendors/UpdateVendorStatus.php.html#49"><abbr title="App\Actions\Vendors\UpdateVendorStatus::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#225"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::cancelIssuesAndRelatedWorkOrders">cancelIssuesAndRelatedWorkOrders</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsPendingIssue.php.html#78"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#56"><abbr title="App\Actions\WorkOrders\CloseWorkOrder::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrders/ScheduleToVendor.php.html#51"><abbr title="App\Actions\WorkOrders\ScheduleToVendor::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrders/ScheduleWorkOrder.php.html#64"><abbr title="App\Actions\WorkOrders\ScheduleWorkOrder::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#139"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::updateOrCreateMaterial">updateOrCreateMaterial</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Issue/CreateIssue.php.html#96"><abbr title="App\Actions\Issue\CreateIssue::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#153"><abbr title="App\Actions\Issue\DeleteIssue::handleWorkOrderIssues">handleWorkOrderIssues</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Issue/UpdateIssue.php.html#51"><abbr title="App\Actions\Issue\UpdateIssue::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Issue/UpdateIssue.php.html#131"><abbr title="App\Actions\Issue\UpdateIssue::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Notifications/ClearAllNotification.php.html#49"><abbr title="App\Actions\Notifications\ClearAllNotification::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Notifications/ClearNotification.php.html#36"><abbr title="App\Actions\Notifications\ClearNotification::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Notifications/ReadAllNotification.php.html#43"><abbr title="App\Actions\Notifications\ReadAllNotification::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Notifications/ReadNotification.php.html#35"><abbr title="App\Actions\Notifications\ReadNotification::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Notifications/UnclearNotification.php.html#35"><abbr title="App\Actions\Notifications\UnclearNotification::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePriority.php.html#42"><abbr title="App\Actions\ServiceRequest\UpdatePriority::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePropertyAddress.php.html#68"><abbr title="App\Actions\ServiceRequest\UpdatePropertyAddress::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResident.php.html#83"><abbr title="App\Actions\ServiceRequest\UpdateResident::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateDeclineReason.php.html#46"><abbr title="App\Actions\WorkOrderIssue\UpdateDeclineReason::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateIssueBasicDetails.php.html#48"><abbr title="App\Actions\WorkOrderIssue\UpdateIssueBasicDetails::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrders/BaseAction.php.html#69"><abbr title="App\Actions\WorkOrders\BaseAction::handleException">handleException</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#48"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#103"><abbr title="App\Actions\WorkOrders\DeleteWorkOrder::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrders/TagWorkOrder.php.html#66"><abbr title="App\Actions\WorkOrders\TagWorkOrder::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequest/BaseAction.php.html#42"><abbr title="App\Actions\ServiceRequest\BaseAction::validateServiceRequest">validateServiceRequest</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResident.php.html#42"><abbr title="App\Actions\ServiceRequest\UpdateResident::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#48"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsPendingIssue.php.html#44"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsPendingIssue.php.html#115"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::clearDoneStateResources">clearDoneStateResources</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#208"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::authorizeTechnicianAccess">authorizeTechnicianAccess</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#261"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::handleIssueMedia">handleIssueMedia</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrders/BaseAction.php.html#55"><abbr title="App\Actions\WorkOrders\BaseAction::validateWorkOrder">validateWorkOrder</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrders/BaseAction.php.html#90"><abbr title="App\Actions\WorkOrders\BaseAction::canPerformTripActionByUser">canPerformTripActionByUser</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#181"><abbr title="App\Actions\WorkOrders\CloseWorkOrder::checkAllIssuesAreReadyToClose">checkAllIssuesAreReadyToClose</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#61"><abbr title="App\Actions\WorkOrders\DeleteWorkOrder::deleteWorkOrderIssues">deleteWorkOrderIssues</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrders/StartWork.php.html#38"><abbr title="App\Actions\WorkOrders\StartWork::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#65"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::asController">asController</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#178"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::updateNotes">updateNotes</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Invoices/BaseAction.php.html#44"><abbr title="App\Actions\Invoices\BaseAction::validateRouteParameterRelation">validateRouteParameterRelation</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#48"><abbr title="App\Actions\Issue\CancelIssue::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Issue/UnassignIssue.php.html#54"><abbr title="App\Actions\Issue\UnassignIssue::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Quotes/DeleteQuoteTask.php.html#39"><abbr title="App\Actions\Quotes\DeleteQuoteTask::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssue.php.html#70"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::changeIssueState">changeIssueState</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#274"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#227"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#93"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::cancelWorkOrderIssues">cancelWorkOrderIssues</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#115"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::updateIssueStatus">updateIssueStatus</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#85"><abbr title="App\Actions\WorkOrders\DeleteWorkOrder::updateIssueStatus">updateIssueStatus</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/PauseEnRoute.php.html#44"><abbr title="App\Actions\WorkOrders\PauseEnRoute::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/PauseTimer.php.html#43"><abbr title="App\Actions\WorkOrders\PauseTimer::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/PauseWorkOrder.php.html#38"><abbr title="App\Actions\WorkOrders\PauseWorkOrder::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/ResumeEnRoute.php.html#41"><abbr title="App\Actions\WorkOrders\ResumeEnRoute::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/ResumeWork.php.html#41"><abbr title="App\Actions\WorkOrders\ResumeWork::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/UpdateNteAmount.php.html#35"><abbr title="App\Actions\WorkOrders\UpdateNteAmount::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/UpdateNteAmount.php.html#55"><abbr title="App\Actions\WorkOrders\UpdateNteAmount::asController">asController</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#104"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::updateTripState">updateTripState</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Invoices/DeleteInvoice.php.html#35"><abbr title="App\Actions\Invoices\DeleteInvoice::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Invoices/DeleteInvoice.php.html#101"><abbr title="App\Actions\Invoices\DeleteInvoice::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Invoices/FullyPaidInvoice.php.html#106"><abbr title="App\Actions\Invoices\FullyPaidInvoice::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Invoices/Invoicing.php.html#124"><abbr title="App\Actions\Invoices\Invoicing::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Invoices/PartiallyPaidInvoice.php.html#100"><abbr title="App\Actions\Invoices\PartiallyPaidInvoice::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Invoices/VoidInvoice.php.html#97"><abbr title="App\Actions\Invoices\VoidInvoice::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/AssignIssue.php.html#133"><abbr title="App\Actions\Issue\AssignIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#129"><abbr title="App\Actions\Issue\CancelIssue::cancelWorkOrder">cancelWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#140"><abbr title="App\Actions\Issue\CancelIssue::cancelWorkOrderIssues">cancelWorkOrderIssues</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/CancelIssue.php.html#202"><abbr title="App\Actions\Issue\CancelIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/CreateIssue.php.html#131"><abbr title="App\Actions\Issue\CreateIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#51"><abbr title="App\Actions\Issue\DeleteIssue::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/DeleteIssue.php.html#103"><abbr title="App\Actions\Issue\DeleteIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/RestoreIssue.php.html#100"><abbr title="App\Actions\Issue\RestoreIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/UnassignIssue.php.html#165"><abbr title="App\Actions\Issue\UnassignIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/UndoIssue.php.html#103"><abbr title="App\Actions\Issue\UndoIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/UndoIssue.php.html#118"><abbr title="App\Actions\Issue\UndoIssue::deleteActivityLogs">deleteActivityLogs</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Issue/UpdateIssue.php.html#178"><abbr title="App\Actions\Issue\UpdateIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Notifications/ClearAllNotification.php.html#37"><abbr title="App\Actions\Notifications\ClearAllNotification::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Notifications/ClearAllNotification.php.html#85"><abbr title="App\Actions\Notifications\ClearAllNotification::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Notifications/ClearNotification.php.html#65"><abbr title="App\Actions\Notifications\ClearNotification::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Notifications/ReadNotification.php.html#64"><abbr title="App\Actions\Notifications\ReadNotification::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Notifications/UnclearNotification.php.html#64"><abbr title="App\Actions\Notifications\UnclearNotification::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quotes/ApproveQuote.php.html#219"><abbr title="App\Actions\Quotes\ApproveQuote::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quotes/CreateQuote.php.html#290"><abbr title="App\Actions\Quotes\CreateQuote::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quotes/CreateQuoteTask.php.html#210"><abbr title="App\Actions\Quotes\CreateQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quotes/DeleteQuoteTask.php.html#130"><abbr title="App\Actions\Quotes\DeleteQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quotes/RejectQuote.php.html#162"><abbr title="App\Actions\Quotes\RejectQuote::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quotes/SubmitForApproval.php.html#147"><abbr title="App\Actions\Quotes\SubmitForApproval::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quotes/UpdateQuoteTask.php.html#254"><abbr title="App\Actions\Quotes\UpdateQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/AddResidentAvailability.php.html#162"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/AwaitingAvailabilityServiceRequest.php.html#113"><abbr title="App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/ClosedServiceRequest.php.html#107"><abbr title="App\Actions\ServiceRequest\ClosedServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/CreateWorkOrderServiceRequest.php.html#107"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/InProgressServiceRequest.php.html#112"><abbr title="App\Actions\ServiceRequest\InProgressServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/RequestResidentAvailability.php.html#34"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/RequestResidentAvailability.php.html#115"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePriority.php.html#74"><abbr title="App\Actions\ServiceRequest\UpdatePriority::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/UpdatePropertyAddress.php.html#115"><abbr title="App\Actions\ServiceRequest\UpdatePropertyAddress::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResident.php.html#134"><abbr title="App\Actions\ServiceRequest\UpdateResident::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/UpdateResidentAvailability.php.html#219"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Vendors/SyncAllVendors.php.html#77"><abbr title="App\Actions\Vendors\SyncAllVendors::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Vendors/SyncVendor.php.html#82"><abbr title="App\Actions\Vendors\SyncVendor::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Vendors/UpdateVendorStatus.php.html#83"><abbr title="App\Actions\Vendors\UpdateVendorStatus::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/AssignIssue.php.html#105"><abbr title="App\Actions\WorkOrderIssue\AssignIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/CreateIssue.php.html#123"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssue.php.html#48"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/DeclineIssue.php.html#165"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#192"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsDoneIssue.php.html#202"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::handleIssueMedia">handleIssueMedia</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/MarkAsPendingIssue.php.html#110"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/UnassignIssue.php.html#100"><abbr title="App\Actions\WorkOrderIssue\UnassignIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateDeclineReason.php.html#76"><abbr title="App\Actions\WorkOrderIssue\UpdateDeclineReason::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateIssueBasicDetails.php.html#103"><abbr title="App\Actions\WorkOrderIssue\UpdateIssueBasicDetails::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#44"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderIssue/UpdateWorkOrderIssue.php.html#148"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/AwaitingAvailabilityWorkOrder.php.html#154"><abbr title="App\Actions\WorkOrders\AwaitingAvailabilityWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#126"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::canIssueStatusBeUpdatedToUnassigned">canIssueStatusBeUpdatedToUnassigned</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/CancelWorkOrder.php.html#218"><abbr title="App\Actions\WorkOrders\CancelWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#173"><abbr title="App\Actions\WorkOrders\CloseWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/CloseWorkOrder.php.html#200"><abbr title="App\Actions\WorkOrders\CloseWorkOrder::updateIssusStatusToQualityCheck">updateIssusStatusToQualityCheck</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/CompleteWorkOrder.php.html#270"><abbr title="App\Actions\WorkOrders\CompleteWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#95"><abbr title="App\Actions\WorkOrders\DeleteWorkOrder::canIssueStatusBeUpdatedToUnassigned">canIssueStatusBeUpdatedToUnassigned</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/DeleteWorkOrder.php.html#129"><abbr title="App\Actions\WorkOrders\DeleteWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/EnRouteWork.php.html#135"><abbr title="App\Actions\WorkOrders\EnRouteWork::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/PauseEnRoute.php.html#151"><abbr title="App\Actions\WorkOrders\PauseEnRoute::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/PauseTimer.php.html#154"><abbr title="App\Actions\WorkOrders\PauseTimer::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/PauseWorkOrder.php.html#129"><abbr title="App\Actions\WorkOrders\PauseWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/ReOpenWorkOrder.php.html#100"><abbr title="App\Actions\WorkOrders\ReOpenWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/ReadyToInvoiceWorkOrder.php.html#182"><abbr title="App\Actions\WorkOrders\ReadyToInvoiceWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/ReadyToScheduleWorkOrder.php.html#136"><abbr title="App\Actions\WorkOrders\ReadyToScheduleWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/RescheduleWorkOrder.php.html#135"><abbr title="App\Actions\WorkOrders\RescheduleWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/ResolveWorkOrder.php.html#97"><abbr title="App\Actions\WorkOrders\ResolveWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/ResumeEnRoute.php.html#148"><abbr title="App\Actions\WorkOrders\ResumeEnRoute::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/ResumeWork.php.html#152"><abbr title="App\Actions\WorkOrders\ResumeWork::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/ScheduleToVendor.php.html#214"><abbr title="App\Actions\WorkOrders\ScheduleToVendor::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/ScheduleWorkOrder.php.html#159"><abbr title="App\Actions\WorkOrders\ScheduleWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/StartWork.php.html#148"><abbr title="App\Actions\WorkOrders\StartWork::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/TagWorkOrder.php.html#41"><abbr title="App\Actions\WorkOrders\TagWorkOrder::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/TagWorkOrder.php.html#99"><abbr title="App\Actions\WorkOrders\TagWorkOrder::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/TechnicianSchedule.php.html#306"><abbr title="App\Actions\WorkOrders\TechnicianSchedule::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/UpdateNteAmount.php.html#77"><abbr title="App\Actions\WorkOrders\UpdateNteAmount::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/UpdateTripDetails.php.html#194"><abbr title="App\Actions\WorkOrders\UpdateTripDetails::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrders/UpdateWorkOrderTrip.php.html#303"><abbr title="App\Actions\WorkOrders\UpdateWorkOrderTrip::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Thu Jun 26 15:40:22 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([0,73,1,3,0,1,0,4,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([280,0,0,0,0,0,0,0,0,0,0,125], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[44.44444444444444,7,"<a href=\"Invoices\/BaseAction.php.html#12\">App\\Actions\\Invoices\\BaseAction<\/a>"],[2.7027027027027026,11,"<a href=\"Invoices\/DeleteInvoice.php.html#25\">App\\Actions\\Invoices\\DeleteInvoice<\/a>"],[2.4390243902439024,12,"<a href=\"Invoices\/FullyPaidInvoice.php.html#27\">App\\Actions\\Invoices\\FullyPaidInvoice<\/a>"],[3.8461538461538463,16,"<a href=\"Invoices\/Invoicing.php.html#28\">App\\Actions\\Invoices\\Invoicing<\/a>"],[2.631578947368421,12,"<a href=\"Invoices\/PartiallyPaidInvoice.php.html#26\">App\\Actions\\Invoices\\PartiallyPaidInvoice<\/a>"],[2.564102564102564,10,"<a href=\"Invoices\/VoidInvoice.php.html#24\">App\\Actions\\Invoices\\VoidInvoice<\/a>"],[4.166666666666666,14,"<a href=\"Issue\/AssignIssue.php.html#30\">App\\Actions\\Issue\\AssignIssue<\/a>"],[60,4,"<a href=\"Issue\/BaseAction.php.html#10\">App\\Actions\\Issue\\BaseAction<\/a>"],[2.7777777777777777,22,"<a href=\"Issue\/CancelIssue.php.html#34\">App\\Actions\\Issue\\CancelIssue<\/a>"],[2.0408163265306123,11,"<a href=\"Issue\/CreateIssue.php.html#28\">App\\Actions\\Issue\\CreateIssue<\/a>"],[3.7037037037037033,19,"<a href=\"Issue\/DeleteIssue.php.html#35\">App\\Actions\\Issue\\DeleteIssue<\/a>"],[6.666666666666667,12,"<a href=\"Issue\/RestoreIssue.php.html#28\">App\\Actions\\Issue\\RestoreIssue<\/a>"],[3.3333333333333335,16,"<a href=\"Issue\/UnassignIssue.php.html#35\">App\\Actions\\Issue\\UnassignIssue<\/a>"],[3.389830508474576,14,"<a href=\"Issue\/UndoIssue.php.html#29\">App\\Actions\\Issue\\UndoIssue<\/a>"],[2.564102564102564,15,"<a href=\"Issue\/UpdateIssue.php.html#28\">App\\Actions\\Issue\\UpdateIssue<\/a>"],[66.66666666666666,4,"<a href=\"Notifications\/BaseAction.php.html#9\">App\\Actions\\Notifications\\BaseAction<\/a>"],[7.4074074074074066,11,"<a href=\"Notifications\/ClearAllNotification.php.html#18\">App\\Actions\\Notifications\\ClearAllNotification<\/a>"],[5,9,"<a href=\"Notifications\/ClearNotification.php.html#17\">App\\Actions\\Notifications\\ClearNotification<\/a>"],[10,9,"<a href=\"Notifications\/ReadAllNotification.php.html#17\">App\\Actions\\Notifications\\ReadAllNotification<\/a>"],[5.263157894736842,9,"<a href=\"Notifications\/ReadNotification.php.html#17\">App\\Actions\\Notifications\\ReadNotification<\/a>"],[5,9,"<a href=\"Notifications\/UnclearNotification.php.html#16\">App\\Actions\\Notifications\\UnclearNotification<\/a>"],[1.8181818181818181,25,"<a href=\"Quotes\/ApproveQuote.php.html#30\">App\\Actions\\Quotes\\ApproveQuote<\/a>"],[23.52941176470588,13,"<a href=\"Quotes\/BaseAction.php.html#14\">App\\Actions\\Quotes\\BaseAction<\/a>"],[1.282051282051282,32,"<a href=\"Quotes\/CreateQuote.php.html#42\">App\\Actions\\Quotes\\CreateQuote<\/a>"],[1.7543859649122806,22,"<a href=\"Quotes\/CreateQuoteTask.php.html#35\">App\\Actions\\Quotes\\CreateQuoteTask<\/a>"],[1.8181818181818181,14,"<a href=\"Quotes\/DeleteQuoteTask.php.html#28\">App\\Actions\\Quotes\\DeleteQuoteTask<\/a>"],[2.4390243902439024,16,"<a href=\"Quotes\/RejectQuote.php.html#29\">App\\Actions\\Quotes\\RejectQuote<\/a>"],[2.7777777777777777,11,"<a href=\"Quotes\/RestoreQuote.php.html#21\">App\\Actions\\Quotes\\RestoreQuote<\/a>"],[2.7777777777777777,13,"<a href=\"Quotes\/SubmitForApproval.php.html#30\">App\\Actions\\Quotes\\SubmitForApproval<\/a>"],[0.7407407407407408,32,"<a href=\"Quotes\/UpdateQuote.php.html#23\">App\\Actions\\Quotes\\UpdateQuote<\/a>"],[0.8,24,"<a href=\"Quotes\/UpdateQuoteTask.php.html#37\">App\\Actions\\Quotes\\UpdateQuoteTask<\/a>"],[1.4705882352941175,21,"<a href=\"ServiceRequest\/AddResidentAvailability.php.html#31\">App\\Actions\\ServiceRequest\\AddResidentAvailability<\/a>"],[2.083333333333333,11,"<a href=\"ServiceRequest\/AwaitingAvailabilityServiceRequest.php.html#25\">App\\Actions\\ServiceRequest\\AwaitingAvailabilityServiceRequest<\/a>"],[27.27272727272727,8,"<a href=\"ServiceRequest\/BaseAction.php.html#11\">App\\Actions\\ServiceRequest\\BaseAction<\/a>"],[2.1739130434782608,11,"<a href=\"ServiceRequest\/ClosedServiceRequest.php.html#24\">App\\Actions\\ServiceRequest\\ClosedServiceRequest<\/a>"],[2.1739130434782608,11,"<a href=\"ServiceRequest\/CreateWorkOrderServiceRequest.php.html#24\">App\\Actions\\ServiceRequest\\CreateWorkOrderServiceRequest<\/a>"],[1.9607843137254901,11,"<a href=\"ServiceRequest\/InProgressServiceRequest.php.html#24\">App\\Actions\\ServiceRequest\\InProgressServiceRequest<\/a>"],[2,13,"<a href=\"ServiceRequest\/RequestResidentAvailability.php.html#25\">App\\Actions\\ServiceRequest\\RequestResidentAvailability<\/a>"],[25,2,"<a href=\"ServiceRequest\/ScopingServiceRequest.php.html#11\">App\\Actions\\ServiceRequest\\ScopingServiceRequest<\/a>"],[4.761904761904762,9,"<a href=\"ServiceRequest\/UpdatePriority.php.html#18\">App\\Actions\\ServiceRequest\\UpdatePriority<\/a>"],[2.564102564102564,9,"<a href=\"ServiceRequest\/UpdatePropertyAddress.php.html#21\">App\\Actions\\ServiceRequest\\UpdatePropertyAddress<\/a>"],[1.9230769230769231,12,"<a href=\"ServiceRequest\/UpdateResident.php.html#21\">App\\Actions\\ServiceRequest\\UpdateResident<\/a>"],[0.9708737864077669,29,"<a href=\"ServiceRequest\/UpdateResidentAvailability.php.html#30\">App\\Actions\\ServiceRequest\\UpdateResidentAvailability<\/a>"],[60,4,"<a href=\"Vendors\/BaseAction.php.html#9\">App\\Actions\\Vendors\\BaseAction<\/a>"],[4.166666666666666,11,"<a href=\"Vendors\/SyncAllVendors.php.html#20\">App\\Actions\\Vendors\\SyncAllVendors<\/a>"],[7.6923076923076925,12,"<a href=\"Vendors\/SyncVendor.php.html#20\">App\\Actions\\Vendors\\SyncVendor<\/a>"],[7.6923076923076925,11,"<a href=\"Vendors\/UpdateVendorStatus.php.html#19\">App\\Actions\\Vendors\\UpdateVendorStatus<\/a>"],[2.3255813953488373,12,"<a href=\"WorkOrderIssue\/AssignIssue.php.html#26\">App\\Actions\\WorkOrderIssue\\AssignIssue<\/a>"],[60,4,"<a href=\"WorkOrderIssue\/BaseAction.php.html#10\">App\\Actions\\WorkOrderIssue\\BaseAction<\/a>"],[4.081632653061225,12,"<a href=\"WorkOrderIssue\/CreateIssue.php.html#29\">App\\Actions\\WorkOrderIssue\\CreateIssue<\/a>"],[1.5625,21,"<a href=\"WorkOrderIssue\/DeclineIssue.php.html#36\">App\\Actions\\WorkOrderIssue\\DeclineIssue<\/a>"],[0.6369426751592357,30,"<a href=\"WorkOrderIssue\/MarkAsDoneIssue.php.html#34\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue<\/a>"],[2,17,"<a href=\"WorkOrderIssue\/MarkAsPendingIssue.php.html#30\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue<\/a>"],[2.5,11,"<a href=\"WorkOrderIssue\/UnassignIssue.php.html#26\">App\\Actions\\WorkOrderIssue\\UnassignIssue<\/a>"],[4.3478260869565215,9,"<a href=\"WorkOrderIssue\/UpdateDeclineReason.php.html#20\">App\\Actions\\WorkOrderIssue\\UpdateDeclineReason<\/a>"],[2.4390243902439024,9,"<a href=\"WorkOrderIssue\/UpdateIssueBasicDetails.php.html#22\">App\\Actions\\WorkOrderIssue\\UpdateIssueBasicDetails<\/a>"],[0.684931506849315,33,"<a href=\"WorkOrderIssue\/UpdateWorkOrderIssue.php.html#30\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue<\/a>"],[1.2048192771084338,15,"<a href=\"WorkOrders\/AwaitingAvailabilityWorkOrder.php.html#26\">App\\Actions\\WorkOrders\\AwaitingAvailabilityWorkOrder<\/a>"],[6.25,17,"<a href=\"WorkOrders\/BaseAction.php.html#24\">App\\Actions\\WorkOrders\\BaseAction<\/a>"],[0.9174311926605505,24,"<a href=\"WorkOrders\/CancelWorkOrder.php.html#39\">App\\Actions\\WorkOrders\\CancelWorkOrder<\/a>"],[2.4096385542168677,24,"<a href=\"WorkOrders\/CloseWorkOrder.php.html#37\">App\\Actions\\WorkOrders\\CloseWorkOrder<\/a>"],[0.6451612903225806,31,"<a href=\"WorkOrders\/CompleteWorkOrder.php.html#41\">App\\Actions\\WorkOrders\\CompleteWorkOrder<\/a>"],[5,19,"<a href=\"WorkOrders\/DeleteWorkOrder.php.html#32\">App\\Actions\\WorkOrders\\DeleteWorkOrder<\/a>"],[3.3333333333333335,12,"<a href=\"WorkOrders\/EnRouteWork.php.html#25\">App\\Actions\\WorkOrders\\EnRouteWork<\/a>"],[2.857142857142857,14,"<a href=\"WorkOrders\/PauseEnRoute.php.html#25\">App\\Actions\\WorkOrders\\PauseEnRoute<\/a>"],[2.73972602739726,14,"<a href=\"WorkOrders\/PauseTimer.php.html#24\">App\\Actions\\WorkOrders\\PauseTimer<\/a>"],[1.8181818181818181,17,"<a href=\"WorkOrders\/PauseWorkOrder.php.html#29\">App\\Actions\\WorkOrders\\PauseWorkOrder<\/a>"],[2.7027027027027026,11,"<a href=\"WorkOrders\/ReOpenWorkOrder.php.html#24\">App\\Actions\\WorkOrders\\ReOpenWorkOrder<\/a>"],[1.098901098901099,26,"<a href=\"WorkOrders\/ReadyToInvoiceWorkOrder.php.html#28\">App\\Actions\\WorkOrders\\ReadyToInvoiceWorkOrder<\/a>"],[4.3478260869565215,13,"<a href=\"WorkOrders\/ReadyToScheduleWorkOrder.php.html#24\">App\\Actions\\WorkOrders\\ReadyToScheduleWorkOrder<\/a>"],[3.0303030303030303,13,"<a href=\"WorkOrders\/RescheduleWorkOrder.php.html#25\">App\\Actions\\WorkOrders\\RescheduleWorkOrder<\/a>"],[2.7777777777777777,11,"<a href=\"WorkOrders\/ResolveWorkOrder.php.html#23\">App\\Actions\\WorkOrders\\ResolveWorkOrder<\/a>"],[2.941176470588235,14,"<a href=\"WorkOrders\/ResumeEnRoute.php.html#25\">App\\Actions\\WorkOrders\\ResumeEnRoute<\/a>"],[2.8169014084507045,14,"<a href=\"WorkOrders\/ResumeWork.php.html#25\">App\\Actions\\WorkOrders\\ResumeWork<\/a>"],[1.5384615384615385,17,"<a href=\"WorkOrders\/ScheduleToVendor.php.html#32\">App\\Actions\\WorkOrders\\ScheduleToVendor<\/a>"],[2.1505376344086025,12,"<a href=\"WorkOrders\/ScheduleWorkOrder.php.html#22\">App\\Actions\\WorkOrders\\ScheduleWorkOrder<\/a>"],[2.857142857142857,15,"<a href=\"WorkOrders\/StartWork.php.html#24\">App\\Actions\\WorkOrders\\StartWork<\/a>"],[8.571428571428571,11,"<a href=\"WorkOrders\/TagWorkOrder.php.html#23\">App\\Actions\\WorkOrders\\TagWorkOrder<\/a>"],[1.1299435028248588,29,"<a href=\"WorkOrders\/TechnicianSchedule.php.html#37\">App\\Actions\\WorkOrders\\TechnicianSchedule<\/a>"],[9.090909090909092,10,"<a href=\"WorkOrders\/UpdateNteAmount.php.html#16\">App\\Actions\\WorkOrders\\UpdateNteAmount<\/a>"],[2.4096385542168677,23,"<a href=\"WorkOrders\/UpdateTripDetails.php.html#28\">App\\Actions\\WorkOrders\\UpdateTripDetails<\/a>"],[1.3333333333333335,46,"<a href=\"WorkOrders\/UpdateWorkOrderTrip.php.html#36\">App\\Actions\\WorkOrders\\UpdateWorkOrderTrip<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Invoices\/BaseAction.php.html#16\">App\\Actions\\Invoices\\BaseAction::getSlug<\/a>"],[100,1,"<a href=\"Invoices\/BaseAction.php.html#21\">App\\Actions\\Invoices\\BaseAction::getRoute<\/a>"],[100,1,"<a href=\"Invoices\/BaseAction.php.html#26\">App\\Actions\\Invoices\\BaseAction::routes<\/a>"],[0,1,"<a href=\"Invoices\/BaseAction.php.html#36\">App\\Actions\\Invoices\\BaseAction::authorize<\/a>"],[0,3,"<a href=\"Invoices\/BaseAction.php.html#44\">App\\Actions\\Invoices\\BaseAction::validateRouteParameterRelation<\/a>"],[100,1,"<a href=\"Invoices\/DeleteInvoice.php.html#30\">App\\Actions\\Invoices\\DeleteInvoice::getSlug<\/a>"],[0,2,"<a href=\"Invoices\/DeleteInvoice.php.html#35\">App\\Actions\\Invoices\\DeleteInvoice::handle<\/a>"],[0,6,"<a href=\"Invoices\/DeleteInvoice.php.html#58\">App\\Actions\\Invoices\\DeleteInvoice::asController<\/a>"],[0,2,"<a href=\"Invoices\/DeleteInvoice.php.html#101\">App\\Actions\\Invoices\\DeleteInvoice::authorize<\/a>"],[100,1,"<a href=\"Invoices\/FullyPaidInvoice.php.html#32\">App\\Actions\\Invoices\\FullyPaidInvoice::getSlug<\/a>"],[0,1,"<a href=\"Invoices\/FullyPaidInvoice.php.html#37\">App\\Actions\\Invoices\\FullyPaidInvoice::handle<\/a>"],[0,8,"<a href=\"Invoices\/FullyPaidInvoice.php.html#50\">App\\Actions\\Invoices\\FullyPaidInvoice::asController<\/a>"],[0,2,"<a href=\"Invoices\/FullyPaidInvoice.php.html#106\">App\\Actions\\Invoices\\FullyPaidInvoice::authorize<\/a>"],[100,1,"<a href=\"Invoices\/Invoicing.php.html#34\">App\\Actions\\Invoices\\Invoicing::getSlug<\/a>"],[100,1,"<a href=\"Invoices\/Invoicing.php.html#39\">App\\Actions\\Invoices\\Invoicing::getRoute<\/a>"],[0,1,"<a href=\"Invoices\/Invoicing.php.html#44\">App\\Actions\\Invoices\\Invoicing::handle<\/a>"],[0,11,"<a href=\"Invoices\/Invoicing.php.html#53\">App\\Actions\\Invoices\\Invoicing::asController<\/a>"],[0,2,"<a href=\"Invoices\/Invoicing.php.html#124\">App\\Actions\\Invoices\\Invoicing::authorize<\/a>"],[100,1,"<a href=\"Invoices\/PartiallyPaidInvoice.php.html#31\">App\\Actions\\Invoices\\PartiallyPaidInvoice::getSlug<\/a>"],[0,1,"<a href=\"Invoices\/PartiallyPaidInvoice.php.html#36\">App\\Actions\\Invoices\\PartiallyPaidInvoice::handle<\/a>"],[0,8,"<a href=\"Invoices\/PartiallyPaidInvoice.php.html#48\">App\\Actions\\Invoices\\PartiallyPaidInvoice::asController<\/a>"],[0,2,"<a href=\"Invoices\/PartiallyPaidInvoice.php.html#100\">App\\Actions\\Invoices\\PartiallyPaidInvoice::authorize<\/a>"],[100,1,"<a href=\"Invoices\/VoidInvoice.php.html#29\">App\\Actions\\Invoices\\VoidInvoice::getSlug<\/a>"],[0,1,"<a href=\"Invoices\/VoidInvoice.php.html#34\">App\\Actions\\Invoices\\VoidInvoice::handle<\/a>"],[0,6,"<a href=\"Invoices\/VoidInvoice.php.html#46\">App\\Actions\\Invoices\\VoidInvoice::asController<\/a>"],[0,2,"<a href=\"Invoices\/VoidInvoice.php.html#97\">App\\Actions\\Invoices\\VoidInvoice::authorize<\/a>"],[100,1,"<a href=\"Issue\/AssignIssue.php.html#36\">App\\Actions\\Issue\\AssignIssue::getRoute<\/a>"],[100,1,"<a href=\"Issue\/AssignIssue.php.html#41\">App\\Actions\\Issue\\AssignIssue::getSlug<\/a>"],[0,1,"<a href=\"Issue\/AssignIssue.php.html#49\">App\\Actions\\Issue\\AssignIssue::handle<\/a>"],[0,8,"<a href=\"Issue\/AssignIssue.php.html#66\">App\\Actions\\Issue\\AssignIssue::asController<\/a>"],[0,1,"<a href=\"Issue\/AssignIssue.php.html#125\">App\\Actions\\Issue\\AssignIssue::isIssueAlreadyAssigned<\/a>"],[0,2,"<a href=\"Issue\/AssignIssue.php.html#133\">App\\Actions\\Issue\\AssignIssue::authorize<\/a>"],[0,1,"<a href=\"Issue\/BaseAction.php.html#14\">App\\Actions\\Issue\\BaseAction::getSlug<\/a>"],[100,1,"<a href=\"Issue\/BaseAction.php.html#19\">App\\Actions\\Issue\\BaseAction::getRoute<\/a>"],[100,1,"<a href=\"Issue\/BaseAction.php.html#24\">App\\Actions\\Issue\\BaseAction::routes<\/a>"],[0,1,"<a href=\"Issue\/BaseAction.php.html#33\">App\\Actions\\Issue\\BaseAction::authorize<\/a>"],[100,1,"<a href=\"Issue\/CancelIssue.php.html#38\">App\\Actions\\Issue\\CancelIssue::getSlug<\/a>"],[100,1,"<a href=\"Issue\/CancelIssue.php.html#43\">App\\Actions\\Issue\\CancelIssue::getRoute<\/a>"],[0,3,"<a href=\"Issue\/CancelIssue.php.html#48\">App\\Actions\\Issue\\CancelIssue::handle<\/a>"],[0,1,"<a href=\"Issue\/CancelIssue.php.html#75\">App\\Actions\\Issue\\CancelIssue::getAllAssociatedWorkOrders<\/a>"],[0,1,"<a href=\"Issue\/CancelIssue.php.html#96\">App\\Actions\\Issue\\CancelIssue::hasAnyScheduledOrInprogressWorkOrderExists<\/a>"],[0,1,"<a href=\"Issue\/CancelIssue.php.html#117\">App\\Actions\\Issue\\CancelIssue::getWorkOrdersWantToCancel<\/a>"],[0,2,"<a href=\"Issue\/CancelIssue.php.html#129\">App\\Actions\\Issue\\CancelIssue::cancelWorkOrder<\/a>"],[0,2,"<a href=\"Issue\/CancelIssue.php.html#140\">App\\Actions\\Issue\\CancelIssue::cancelWorkOrderIssues<\/a>"],[0,8,"<a href=\"Issue\/CancelIssue.php.html#156\">App\\Actions\\Issue\\CancelIssue::asController<\/a>"],[0,2,"<a href=\"Issue\/CancelIssue.php.html#202\">App\\Actions\\Issue\\CancelIssue::authorize<\/a>"],[100,1,"<a href=\"Issue\/CreateIssue.php.html#32\">App\\Actions\\Issue\\CreateIssue::getSlug<\/a>"],[0,1,"<a href=\"Issue\/CreateIssue.php.html#42\">App\\Actions\\Issue\\CreateIssue::handle<\/a>"],[0,1,"<a href=\"Issue\/CreateIssue.php.html#59\">App\\Actions\\Issue\\CreateIssue::getProblemDiagnosis<\/a>"],[0,1,"<a href=\"Issue\/CreateIssue.php.html#75\">App\\Actions\\Issue\\CreateIssue::createIssue<\/a>"],[0,5,"<a href=\"Issue\/CreateIssue.php.html#96\">App\\Actions\\Issue\\CreateIssue::asController<\/a>"],[0,2,"<a href=\"Issue\/CreateIssue.php.html#131\">App\\Actions\\Issue\\CreateIssue::authorize<\/a>"],[100,1,"<a href=\"Issue\/DeleteIssue.php.html#41\">App\\Actions\\Issue\\DeleteIssue::getSlug<\/a>"],[100,1,"<a href=\"Issue\/DeleteIssue.php.html#46\">App\\Actions\\Issue\\DeleteIssue::getRoute<\/a>"],[0,2,"<a href=\"Issue\/DeleteIssue.php.html#51\">App\\Actions\\Issue\\DeleteIssue::handle<\/a>"],[0,6,"<a href=\"Issue\/DeleteIssue.php.html#75\">App\\Actions\\Issue\\DeleteIssue::asController<\/a>"],[0,2,"<a href=\"Issue\/DeleteIssue.php.html#103\">App\\Actions\\Issue\\DeleteIssue::authorize<\/a>"],[0,1,"<a href=\"Issue\/DeleteIssue.php.html#118\">App\\Actions\\Issue\\DeleteIssue::hasAnyScheduledOrInprogressWorkOrderExists<\/a>"],[0,1,"<a href=\"Issue\/DeleteIssue.php.html#136\">App\\Actions\\Issue\\DeleteIssue::getWorkOrderIssues<\/a>"],[0,5,"<a href=\"Issue\/DeleteIssue.php.html#153\">App\\Actions\\Issue\\DeleteIssue::handleWorkOrderIssues<\/a>"],[100,1,"<a href=\"Issue\/RestoreIssue.php.html#34\">App\\Actions\\Issue\\RestoreIssue::getSlug<\/a>"],[100,1,"<a href=\"Issue\/RestoreIssue.php.html#39\">App\\Actions\\Issue\\RestoreIssue::getRoute<\/a>"],[0,1,"<a href=\"Issue\/RestoreIssue.php.html#44\">App\\Actions\\Issue\\RestoreIssue::handle<\/a>"],[0,7,"<a href=\"Issue\/RestoreIssue.php.html#60\">App\\Actions\\Issue\\RestoreIssue::asController<\/a>"],[0,2,"<a href=\"Issue\/RestoreIssue.php.html#100\">App\\Actions\\Issue\\RestoreIssue::authorize<\/a>"],[100,1,"<a href=\"Issue\/UnassignIssue.php.html#41\">App\\Actions\\Issue\\UnassignIssue::getRoute<\/a>"],[100,1,"<a href=\"Issue\/UnassignIssue.php.html#46\">App\\Actions\\Issue\\UnassignIssue::getSlug<\/a>"],[0,3,"<a href=\"Issue\/UnassignIssue.php.html#54\">App\\Actions\\Issue\\UnassignIssue::handle<\/a>"],[0,1,"<a href=\"Issue\/UnassignIssue.php.html#90\">App\\Actions\\Issue\\UnassignIssue::checkWorkOrderHasIssues<\/a>"],[0,1,"<a href=\"Issue\/UnassignIssue.php.html#103\">App\\Actions\\Issue\\UnassignIssue::checkIssueHasAssigned<\/a>"],[0,7,"<a href=\"Issue\/UnassignIssue.php.html#113\">App\\Actions\\Issue\\UnassignIssue::asController<\/a>"],[0,2,"<a href=\"Issue\/UnassignIssue.php.html#165\">App\\Actions\\Issue\\UnassignIssue::authorize<\/a>"],[100,1,"<a href=\"Issue\/UndoIssue.php.html#35\">App\\Actions\\Issue\\UndoIssue::getSlug<\/a>"],[100,1,"<a href=\"Issue\/UndoIssue.php.html#40\">App\\Actions\\Issue\\UndoIssue::getRoute<\/a>"],[0,1,"<a href=\"Issue\/UndoIssue.php.html#45\">App\\Actions\\Issue\\UndoIssue::handle<\/a>"],[0,6,"<a href=\"Issue\/UndoIssue.php.html#60\">App\\Actions\\Issue\\UndoIssue::asController<\/a>"],[0,2,"<a href=\"Issue\/UndoIssue.php.html#103\">App\\Actions\\Issue\\UndoIssue::authorize<\/a>"],[0,1,"<a href=\"Issue\/UndoIssue.php.html#108\">App\\Actions\\Issue\\UndoIssue::restoreIssue<\/a>"],[0,2,"<a href=\"Issue\/UndoIssue.php.html#118\">App\\Actions\\Issue\\UndoIssue::deleteActivityLogs<\/a>"],[100,1,"<a href=\"Issue\/UpdateIssue.php.html#34\">App\\Actions\\Issue\\UpdateIssue::getSlug<\/a>"],[100,1,"<a href=\"Issue\/UpdateIssue.php.html#39\">App\\Actions\\Issue\\UpdateIssue::getRoute<\/a>"],[0,5,"<a href=\"Issue\/UpdateIssue.php.html#51\">App\\Actions\\Issue\\UpdateIssue::handle<\/a>"],[0,1,"<a href=\"Issue\/UpdateIssue.php.html#112\">App\\Actions\\Issue\\UpdateIssue::triggerBroadCasts<\/a>"],[0,5,"<a href=\"Issue\/UpdateIssue.php.html#131\">App\\Actions\\Issue\\UpdateIssue::asController<\/a>"],[0,2,"<a href=\"Issue\/UpdateIssue.php.html#178\">App\\Actions\\Issue\\UpdateIssue::authorize<\/a>"],[0,1,"<a href=\"Notifications\/BaseAction.php.html#13\">App\\Actions\\Notifications\\BaseAction::getSlug<\/a>"],[100,1,"<a href=\"Notifications\/BaseAction.php.html#18\">App\\Actions\\Notifications\\BaseAction::getRoute<\/a>"],[100,1,"<a href=\"Notifications\/BaseAction.php.html#23\">App\\Actions\\Notifications\\BaseAction::routes<\/a>"],[0,1,"<a href=\"Notifications\/BaseAction.php.html#33\">App\\Actions\\Notifications\\BaseAction::authorize<\/a>"],[100,1,"<a href=\"Notifications\/ClearAllNotification.php.html#22\">App\\Actions\\Notifications\\ClearAllNotification::getSlug<\/a>"],[100,1,"<a href=\"Notifications\/ClearAllNotification.php.html#27\">App\\Actions\\Notifications\\ClearAllNotification::getRoute<\/a>"],[0,2,"<a href=\"Notifications\/ClearAllNotification.php.html#37\">App\\Actions\\Notifications\\ClearAllNotification::handle<\/a>"],[0,5,"<a href=\"Notifications\/ClearAllNotification.php.html#49\">App\\Actions\\Notifications\\ClearAllNotification::asController<\/a>"],[0,2,"<a href=\"Notifications\/ClearAllNotification.php.html#85\">App\\Actions\\Notifications\\ClearAllNotification::authorize<\/a>"],[100,1,"<a href=\"Notifications\/ClearNotification.php.html#21\">App\\Actions\\Notifications\\ClearNotification::getSlug<\/a>"],[0,1,"<a href=\"Notifications\/ClearNotification.php.html#26\">App\\Actions\\Notifications\\ClearNotification::handle<\/a>"],[0,5,"<a href=\"Notifications\/ClearNotification.php.html#36\">App\\Actions\\Notifications\\ClearNotification::asController<\/a>"],[0,2,"<a href=\"Notifications\/ClearNotification.php.html#65\">App\\Actions\\Notifications\\ClearNotification::authorize<\/a>"],[100,1,"<a href=\"Notifications\/ReadAllNotification.php.html#21\">App\\Actions\\Notifications\\ReadAllNotification::getSlug<\/a>"],[100,1,"<a href=\"Notifications\/ReadAllNotification.php.html#26\">App\\Actions\\Notifications\\ReadAllNotification::getRoute<\/a>"],[0,1,"<a href=\"Notifications\/ReadAllNotification.php.html#31\">App\\Actions\\Notifications\\ReadAllNotification::handle<\/a>"],[0,5,"<a href=\"Notifications\/ReadAllNotification.php.html#43\">App\\Actions\\Notifications\\ReadAllNotification::asController<\/a>"],[0,1,"<a href=\"Notifications\/ReadAllNotification.php.html#73\">App\\Actions\\Notifications\\ReadAllNotification::authorize<\/a>"],[100,1,"<a href=\"Notifications\/ReadNotification.php.html#21\">App\\Actions\\Notifications\\ReadNotification::getSlug<\/a>"],[0,1,"<a href=\"Notifications\/ReadNotification.php.html#26\">App\\Actions\\Notifications\\ReadNotification::handle<\/a>"],[0,5,"<a href=\"Notifications\/ReadNotification.php.html#35\">App\\Actions\\Notifications\\ReadNotification::asController<\/a>"],[0,2,"<a href=\"Notifications\/ReadNotification.php.html#64\">App\\Actions\\Notifications\\ReadNotification::authorize<\/a>"],[100,1,"<a href=\"Notifications\/UnclearNotification.php.html#20\">App\\Actions\\Notifications\\UnclearNotification::getSlug<\/a>"],[0,1,"<a href=\"Notifications\/UnclearNotification.php.html#25\">App\\Actions\\Notifications\\UnclearNotification::handle<\/a>"],[0,5,"<a href=\"Notifications\/UnclearNotification.php.html#35\">App\\Actions\\Notifications\\UnclearNotification::asController<\/a>"],[0,2,"<a href=\"Notifications\/UnclearNotification.php.html#64\">App\\Actions\\Notifications\\UnclearNotification::authorize<\/a>"],[100,1,"<a href=\"Quotes\/ApproveQuote.php.html#34\">App\\Actions\\Quotes\\ApproveQuote::getSlug<\/a>"],[100,1,"<a href=\"Quotes\/ApproveQuote.php.html#39\">App\\Actions\\Quotes\\ApproveQuote::getRoute<\/a>"],[0,13,"<a href=\"Quotes\/ApproveQuote.php.html#48\">App\\Actions\\Quotes\\ApproveQuote::handle<\/a>"],[0,8,"<a href=\"Quotes\/ApproveQuote.php.html#131\">App\\Actions\\Quotes\\ApproveQuote::asController<\/a>"],[0,2,"<a href=\"Quotes\/ApproveQuote.php.html#219\">App\\Actions\\Quotes\\ApproveQuote::authorize<\/a>"],[0,1,"<a href=\"Quotes\/BaseAction.php.html#18\">App\\Actions\\Quotes\\BaseAction::getSlug<\/a>"],[100,1,"<a href=\"Quotes\/BaseAction.php.html#23\">App\\Actions\\Quotes\\BaseAction::getRoute<\/a>"],[100,1,"<a href=\"Quotes\/BaseAction.php.html#28\">App\\Actions\\Quotes\\BaseAction::routes<\/a>"],[0,1,"<a href=\"Quotes\/BaseAction.php.html#38\">App\\Actions\\Quotes\\BaseAction::authorize<\/a>"],[0,9,"<a href=\"Quotes\/BaseAction.php.html#46\">App\\Actions\\Quotes\\BaseAction::validateRouteParameterRelation<\/a>"],[100,1,"<a href=\"Quotes\/CreateQuote.php.html#46\">App\\Actions\\Quotes\\CreateQuote::getSlug<\/a>"],[100,1,"<a href=\"Quotes\/CreateQuote.php.html#51\">App\\Actions\\Quotes\\CreateQuote::getRoute<\/a>"],[0,15,"<a href=\"Quotes\/CreateQuote.php.html#59\">App\\Actions\\Quotes\\CreateQuote::handle<\/a>"],[0,13,"<a href=\"Quotes\/CreateQuote.php.html#197\">App\\Actions\\Quotes\\CreateQuote::asController<\/a>"],[0,2,"<a href=\"Quotes\/CreateQuote.php.html#290\">App\\Actions\\Quotes\\CreateQuote::authorize<\/a>"],[100,1,"<a href=\"Quotes\/CreateQuoteTask.php.html#39\">App\\Actions\\Quotes\\CreateQuoteTask::getSlug<\/a>"],[100,1,"<a href=\"Quotes\/CreateQuoteTask.php.html#44\">App\\Actions\\Quotes\\CreateQuoteTask::getRoute<\/a>"],[0,11,"<a href=\"Quotes\/CreateQuoteTask.php.html#52\">App\\Actions\\Quotes\\CreateQuoteTask::handle<\/a>"],[0,7,"<a href=\"Quotes\/CreateQuoteTask.php.html#149\">App\\Actions\\Quotes\\CreateQuoteTask::asController<\/a>"],[0,2,"<a href=\"Quotes\/CreateQuoteTask.php.html#210\">App\\Actions\\Quotes\\CreateQuoteTask::authorize<\/a>"],[100,1,"<a href=\"Quotes\/DeleteQuoteTask.php.html#34\">App\\Actions\\Quotes\\DeleteQuoteTask::getSlug<\/a>"],[0,3,"<a href=\"Quotes\/DeleteQuoteTask.php.html#39\">App\\Actions\\Quotes\\DeleteQuoteTask::handle<\/a>"],[0,8,"<a href=\"Quotes\/DeleteQuoteTask.php.html#75\">App\\Actions\\Quotes\\DeleteQuoteTask::asController<\/a>"],[0,2,"<a href=\"Quotes\/DeleteQuoteTask.php.html#130\">App\\Actions\\Quotes\\DeleteQuoteTask::authorize<\/a>"],[100,1,"<a href=\"Quotes\/RejectQuote.php.html#33\">App\\Actions\\Quotes\\RejectQuote::getSlug<\/a>"],[100,1,"<a href=\"Quotes\/RejectQuote.php.html#38\">App\\Actions\\Quotes\\RejectQuote::getRoute<\/a>"],[0,6,"<a href=\"Quotes\/RejectQuote.php.html#43\">App\\Actions\\Quotes\\RejectQuote::handle<\/a>"],[0,6,"<a href=\"Quotes\/RejectQuote.php.html#91\">App\\Actions\\Quotes\\RejectQuote::asController<\/a>"],[0,2,"<a href=\"Quotes\/RejectQuote.php.html#162\">App\\Actions\\Quotes\\RejectQuote::authorize<\/a>"],[100,1,"<a href=\"Quotes\/RestoreQuote.php.html#25\">App\\Actions\\Quotes\\RestoreQuote::getSlug<\/a>"],[0,10,"<a href=\"Quotes\/RestoreQuote.php.html#30\">App\\Actions\\Quotes\\RestoreQuote::handle<\/a>"],[100,1,"<a href=\"Quotes\/SubmitForApproval.php.html#34\">App\\Actions\\Quotes\\SubmitForApproval::getSlug<\/a>"],[100,1,"<a href=\"Quotes\/SubmitForApproval.php.html#39\">App\\Actions\\Quotes\\SubmitForApproval::getRoute<\/a>"],[0,1,"<a href=\"Quotes\/SubmitForApproval.php.html#44\">App\\Actions\\Quotes\\SubmitForApproval::handle<\/a>"],[0,8,"<a href=\"Quotes\/SubmitForApproval.php.html#62\">App\\Actions\\Quotes\\SubmitForApproval::asController<\/a>"],[0,2,"<a href=\"Quotes\/SubmitForApproval.php.html#147\">App\\Actions\\Quotes\\SubmitForApproval::authorize<\/a>"],[100,1,"<a href=\"Quotes\/UpdateQuote.php.html#27\">App\\Actions\\Quotes\\UpdateQuote::getSlug<\/a>"],[0,31,"<a href=\"Quotes\/UpdateQuote.php.html#55\">App\\Actions\\Quotes\\UpdateQuote::handle<\/a>"],[100,1,"<a href=\"Quotes\/UpdateQuoteTask.php.html#43\">App\\Actions\\Quotes\\UpdateQuoteTask::getSlug<\/a>"],[0,14,"<a href=\"Quotes\/UpdateQuoteTask.php.html#73\">App\\Actions\\Quotes\\UpdateQuoteTask::handle<\/a>"],[0,7,"<a href=\"Quotes\/UpdateQuoteTask.php.html#188\">App\\Actions\\Quotes\\UpdateQuoteTask::asController<\/a>"],[0,2,"<a href=\"Quotes\/UpdateQuoteTask.php.html#254\">App\\Actions\\Quotes\\UpdateQuoteTask::authorize<\/a>"],[100,1,"<a href=\"ServiceRequest\/AddResidentAvailability.php.html#35\">App\\Actions\\ServiceRequest\\AddResidentAvailability::getSlug<\/a>"],[0,10,"<a href=\"ServiceRequest\/AddResidentAvailability.php.html#43\">App\\Actions\\ServiceRequest\\AddResidentAvailability::handle<\/a>"],[0,8,"<a href=\"ServiceRequest\/AddResidentAvailability.php.html#107\">App\\Actions\\ServiceRequest\\AddResidentAvailability::asController<\/a>"],[0,2,"<a href=\"ServiceRequest\/AddResidentAvailability.php.html#162\">App\\Actions\\ServiceRequest\\AddResidentAvailability::authorize<\/a>"],[100,1,"<a href=\"ServiceRequest\/AwaitingAvailabilityServiceRequest.php.html#29\">App\\Actions\\ServiceRequest\\AwaitingAvailabilityServiceRequest::getSlug<\/a>"],[0,1,"<a href=\"ServiceRequest\/AwaitingAvailabilityServiceRequest.php.html#34\">App\\Actions\\ServiceRequest\\AwaitingAvailabilityServiceRequest::handle<\/a>"],[0,7,"<a href=\"ServiceRequest\/AwaitingAvailabilityServiceRequest.php.html#50\">App\\Actions\\ServiceRequest\\AwaitingAvailabilityServiceRequest::asController<\/a>"],[0,2,"<a href=\"ServiceRequest\/AwaitingAvailabilityServiceRequest.php.html#113\">App\\Actions\\ServiceRequest\\AwaitingAvailabilityServiceRequest::authorize<\/a>"],[0,1,"<a href=\"ServiceRequest\/BaseAction.php.html#15\">App\\Actions\\ServiceRequest\\BaseAction::getSlug<\/a>"],[100,1,"<a href=\"ServiceRequest\/BaseAction.php.html#20\">App\\Actions\\ServiceRequest\\BaseAction::getRoute<\/a>"],[100,1,"<a href=\"ServiceRequest\/BaseAction.php.html#25\">App\\Actions\\ServiceRequest\\BaseAction::routes<\/a>"],[0,1,"<a href=\"ServiceRequest\/BaseAction.php.html#34\">App\\Actions\\ServiceRequest\\BaseAction::authorize<\/a>"],[0,4,"<a href=\"ServiceRequest\/BaseAction.php.html#42\">App\\Actions\\ServiceRequest\\BaseAction::validateServiceRequest<\/a>"],[100,1,"<a href=\"ServiceRequest\/ClosedServiceRequest.php.html#28\">App\\Actions\\ServiceRequest\\ClosedServiceRequest::getSlug<\/a>"],[0,1,"<a href=\"ServiceRequest\/ClosedServiceRequest.php.html#33\">App\\Actions\\ServiceRequest\\ClosedServiceRequest::handle<\/a>"],[0,7,"<a href=\"ServiceRequest\/ClosedServiceRequest.php.html#44\">App\\Actions\\ServiceRequest\\ClosedServiceRequest::asController<\/a>"],[0,2,"<a href=\"ServiceRequest\/ClosedServiceRequest.php.html#107\">App\\Actions\\ServiceRequest\\ClosedServiceRequest::authorize<\/a>"],[100,1,"<a href=\"ServiceRequest\/CreateWorkOrderServiceRequest.php.html#28\">App\\Actions\\ServiceRequest\\CreateWorkOrderServiceRequest::getSlug<\/a>"],[0,1,"<a href=\"ServiceRequest\/CreateWorkOrderServiceRequest.php.html#33\">App\\Actions\\ServiceRequest\\CreateWorkOrderServiceRequest::handle<\/a>"],[0,7,"<a href=\"ServiceRequest\/CreateWorkOrderServiceRequest.php.html#44\">App\\Actions\\ServiceRequest\\CreateWorkOrderServiceRequest::asController<\/a>"],[0,2,"<a href=\"ServiceRequest\/CreateWorkOrderServiceRequest.php.html#107\">App\\Actions\\ServiceRequest\\CreateWorkOrderServiceRequest::authorize<\/a>"],[100,1,"<a href=\"ServiceRequest\/InProgressServiceRequest.php.html#28\">App\\Actions\\ServiceRequest\\InProgressServiceRequest::getSlug<\/a>"],[0,1,"<a href=\"ServiceRequest\/InProgressServiceRequest.php.html#33\">App\\Actions\\ServiceRequest\\InProgressServiceRequest::handle<\/a>"],[0,7,"<a href=\"ServiceRequest\/InProgressServiceRequest.php.html#44\">App\\Actions\\ServiceRequest\\InProgressServiceRequest::asController<\/a>"],[0,2,"<a href=\"ServiceRequest\/InProgressServiceRequest.php.html#112\">App\\Actions\\ServiceRequest\\InProgressServiceRequest::authorize<\/a>"],[100,1,"<a href=\"ServiceRequest\/RequestResidentAvailability.php.html#29\">App\\Actions\\ServiceRequest\\RequestResidentAvailability::getSlug<\/a>"],[0,2,"<a href=\"ServiceRequest\/RequestResidentAvailability.php.html#34\">App\\Actions\\ServiceRequest\\RequestResidentAvailability::handle<\/a>"],[0,8,"<a href=\"ServiceRequest\/RequestResidentAvailability.php.html#59\">App\\Actions\\ServiceRequest\\RequestResidentAvailability::asController<\/a>"],[0,2,"<a href=\"ServiceRequest\/RequestResidentAvailability.php.html#115\">App\\Actions\\ServiceRequest\\RequestResidentAvailability::authorize<\/a>"],[100,1,"<a href=\"ServiceRequest\/ScopingServiceRequest.php.html#15\">App\\Actions\\ServiceRequest\\ScopingServiceRequest::getSlug<\/a>"],[0,1,"<a href=\"ServiceRequest\/ScopingServiceRequest.php.html#20\">App\\Actions\\ServiceRequest\\ScopingServiceRequest::handle<\/a>"],[100,1,"<a href=\"ServiceRequest\/UpdatePriority.php.html#27\">App\\Actions\\ServiceRequest\\UpdatePriority::getSlug<\/a>"],[0,1,"<a href=\"ServiceRequest\/UpdatePriority.php.html#32\">App\\Actions\\ServiceRequest\\UpdatePriority::handle<\/a>"],[0,5,"<a href=\"ServiceRequest\/UpdatePriority.php.html#42\">App\\Actions\\ServiceRequest\\UpdatePriority::asController<\/a>"],[0,2,"<a href=\"ServiceRequest\/UpdatePriority.php.html#74\">App\\Actions\\ServiceRequest\\UpdatePriority::authorize<\/a>"],[100,1,"<a href=\"ServiceRequest\/UpdatePropertyAddress.php.html#30\">App\\Actions\\ServiceRequest\\UpdatePropertyAddress::getSlug<\/a>"],[0,1,"<a href=\"ServiceRequest\/UpdatePropertyAddress.php.html#43\">App\\Actions\\ServiceRequest\\UpdatePropertyAddress::handle<\/a>"],[0,5,"<a href=\"ServiceRequest\/UpdatePropertyAddress.php.html#68\">App\\Actions\\ServiceRequest\\UpdatePropertyAddress::asController<\/a>"],[0,2,"<a href=\"ServiceRequest\/UpdatePropertyAddress.php.html#115\">App\\Actions\\ServiceRequest\\UpdatePropertyAddress::authorize<\/a>"],[100,1,"<a href=\"ServiceRequest\/UpdateResident.php.html#30\">App\\Actions\\ServiceRequest\\UpdateResident::getSlug<\/a>"],[0,4,"<a href=\"ServiceRequest\/UpdateResident.php.html#42\">App\\Actions\\ServiceRequest\\UpdateResident::handle<\/a>"],[0,5,"<a href=\"ServiceRequest\/UpdateResident.php.html#83\">App\\Actions\\ServiceRequest\\UpdateResident::asController<\/a>"],[0,2,"<a href=\"ServiceRequest\/UpdateResident.php.html#134\">App\\Actions\\ServiceRequest\\UpdateResident::authorize<\/a>"],[100,1,"<a href=\"ServiceRequest\/UpdateResidentAvailability.php.html#36\">App\\Actions\\ServiceRequest\\UpdateResidentAvailability::getSlug<\/a>"],[0,20,"<a href=\"ServiceRequest\/UpdateResidentAvailability.php.html#44\">App\\Actions\\ServiceRequest\\UpdateResidentAvailability::handle<\/a>"],[0,6,"<a href=\"ServiceRequest\/UpdateResidentAvailability.php.html#176\">App\\Actions\\ServiceRequest\\UpdateResidentAvailability::asController<\/a>"],[0,2,"<a href=\"ServiceRequest\/UpdateResidentAvailability.php.html#219\">App\\Actions\\ServiceRequest\\UpdateResidentAvailability::authorize<\/a>"],[0,1,"<a href=\"Vendors\/BaseAction.php.html#13\">App\\Actions\\Vendors\\BaseAction::getSlug<\/a>"],[100,1,"<a href=\"Vendors\/BaseAction.php.html#18\">App\\Actions\\Vendors\\BaseAction::getRoute<\/a>"],[100,1,"<a href=\"Vendors\/BaseAction.php.html#23\">App\\Actions\\Vendors\\BaseAction::routes<\/a>"],[0,1,"<a href=\"Vendors\/BaseAction.php.html#32\">App\\Actions\\Vendors\\BaseAction::authorize<\/a>"],[100,1,"<a href=\"Vendors\/SyncAllVendors.php.html#24\">App\\Actions\\Vendors\\SyncAllVendors::getSlug<\/a>"],[0,1,"<a href=\"Vendors\/SyncAllVendors.php.html#29\">App\\Actions\\Vendors\\SyncAllVendors::handle<\/a>"],[0,7,"<a href=\"Vendors\/SyncAllVendors.php.html#37\">App\\Actions\\Vendors\\SyncAllVendors::asController<\/a>"],[0,2,"<a href=\"Vendors\/SyncAllVendors.php.html#77\">App\\Actions\\Vendors\\SyncAllVendors::authorize<\/a>"],[100,1,"<a href=\"Vendors\/SyncVendor.php.html#24\">App\\Actions\\Vendors\\SyncVendor::getSlug<\/a>"],[100,1,"<a href=\"Vendors\/SyncVendor.php.html#29\">App\\Actions\\Vendors\\SyncVendor::getRoute<\/a>"],[0,1,"<a href=\"Vendors\/SyncVendor.php.html#34\">App\\Actions\\Vendors\\SyncVendor::handle<\/a>"],[0,7,"<a href=\"Vendors\/SyncVendor.php.html#42\">App\\Actions\\Vendors\\SyncVendor::asController<\/a>"],[0,2,"<a href=\"Vendors\/SyncVendor.php.html#82\">App\\Actions\\Vendors\\SyncVendor::authorize<\/a>"],[100,1,"<a href=\"Vendors\/UpdateVendorStatus.php.html#25\">App\\Actions\\Vendors\\UpdateVendorStatus::getSlug<\/a>"],[100,1,"<a href=\"Vendors\/UpdateVendorStatus.php.html#30\">App\\Actions\\Vendors\\UpdateVendorStatus::getRoute<\/a>"],[0,1,"<a href=\"Vendors\/UpdateVendorStatus.php.html#35\">App\\Actions\\Vendors\\UpdateVendorStatus::handle<\/a>"],[0,6,"<a href=\"Vendors\/UpdateVendorStatus.php.html#49\">App\\Actions\\Vendors\\UpdateVendorStatus::asController<\/a>"],[0,2,"<a href=\"Vendors\/UpdateVendorStatus.php.html#83\">App\\Actions\\Vendors\\UpdateVendorStatus::authorize<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/AssignIssue.php.html#30\">App\\Actions\\WorkOrderIssue\\AssignIssue::getSlug<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/AssignIssue.php.html#35\">App\\Actions\\WorkOrderIssue\\AssignIssue::handle<\/a>"],[0,8,"<a href=\"WorkOrderIssue\/AssignIssue.php.html#43\">App\\Actions\\WorkOrderIssue\\AssignIssue::asController<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/AssignIssue.php.html#105\">App\\Actions\\WorkOrderIssue\\AssignIssue::authorize<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/BaseAction.php.html#14\">App\\Actions\\WorkOrderIssue\\BaseAction::getSlug<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/BaseAction.php.html#19\">App\\Actions\\WorkOrderIssue\\BaseAction::getRoute<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/BaseAction.php.html#24\">App\\Actions\\WorkOrderIssue\\BaseAction::routes<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/BaseAction.php.html#33\">App\\Actions\\WorkOrderIssue\\BaseAction::authorize<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/CreateIssue.php.html#33\">App\\Actions\\WorkOrderIssue\\CreateIssue::getRoute<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/CreateIssue.php.html#38\">App\\Actions\\WorkOrderIssue\\CreateIssue::getSlug<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/CreateIssue.php.html#48\">App\\Actions\\WorkOrderIssue\\CreateIssue::handle<\/a>"],[0,7,"<a href=\"WorkOrderIssue\/CreateIssue.php.html#64\">App\\Actions\\WorkOrderIssue\\CreateIssue::asController<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/CreateIssue.php.html#123\">App\\Actions\\WorkOrderIssue\\CreateIssue::authorize<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/DeclineIssue.php.html#40\">App\\Actions\\WorkOrderIssue\\DeclineIssue::getSlug<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/DeclineIssue.php.html#48\">App\\Actions\\WorkOrderIssue\\DeclineIssue::handle<\/a>"],[0,3,"<a href=\"WorkOrderIssue\/DeclineIssue.php.html#70\">App\\Actions\\WorkOrderIssue\\DeclineIssue::changeIssueState<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/DeclineIssue.php.html#87\">App\\Actions\\WorkOrderIssue\\DeclineIssue::assignedIssueExists<\/a>"],[0,12,"<a href=\"WorkOrderIssue\/DeclineIssue.php.html#95\">App\\Actions\\WorkOrderIssue\\DeclineIssue::asController<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/DeclineIssue.php.html#165\">App\\Actions\\WorkOrderIssue\\DeclineIssue::authorize<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/MarkAsDoneIssue.php.html#40\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::getSlug<\/a>"],[0,4,"<a href=\"WorkOrderIssue\/MarkAsDoneIssue.php.html#48\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::handle<\/a>"],[0,12,"<a href=\"WorkOrderIssue\/MarkAsDoneIssue.php.html#101\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::asController<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/MarkAsDoneIssue.php.html#192\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::authorize<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/MarkAsDoneIssue.php.html#202\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::handleIssueMedia<\/a>"],[0,6,"<a href=\"WorkOrderIssue\/MarkAsDoneIssue.php.html#225\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::cancelIssuesAndRelatedWorkOrders<\/a>"],[0,3,"<a href=\"WorkOrderIssue\/MarkAsDoneIssue.php.html#274\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::validateMediaLimit<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/MarkAsPendingIssue.php.html#36\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue::getSlug<\/a>"],[0,4,"<a href=\"WorkOrderIssue\/MarkAsPendingIssue.php.html#44\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue::handle<\/a>"],[0,6,"<a href=\"WorkOrderIssue\/MarkAsPendingIssue.php.html#78\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue::asController<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/MarkAsPendingIssue.php.html#110\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue::authorize<\/a>"],[0,4,"<a href=\"WorkOrderIssue\/MarkAsPendingIssue.php.html#115\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue::clearDoneStateResources<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/UnassignIssue.php.html#30\">App\\Actions\\WorkOrderIssue\\UnassignIssue::getSlug<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/UnassignIssue.php.html#35\">App\\Actions\\WorkOrderIssue\\UnassignIssue::handle<\/a>"],[0,7,"<a href=\"WorkOrderIssue\/UnassignIssue.php.html#43\">App\\Actions\\WorkOrderIssue\\UnassignIssue::asController<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/UnassignIssue.php.html#100\">App\\Actions\\WorkOrderIssue\\UnassignIssue::authorize<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/UpdateDeclineReason.php.html#26\">App\\Actions\\WorkOrderIssue\\UpdateDeclineReason::getSlug<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/UpdateDeclineReason.php.html#34\">App\\Actions\\WorkOrderIssue\\UpdateDeclineReason::handle<\/a>"],[0,5,"<a href=\"WorkOrderIssue\/UpdateDeclineReason.php.html#46\">App\\Actions\\WorkOrderIssue\\UpdateDeclineReason::asController<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/UpdateDeclineReason.php.html#76\">App\\Actions\\WorkOrderIssue\\UpdateDeclineReason::authorize<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/UpdateIssueBasicDetails.php.html#28\">App\\Actions\\WorkOrderIssue\\UpdateIssueBasicDetails::getSlug<\/a>"],[0,1,"<a href=\"WorkOrderIssue\/UpdateIssueBasicDetails.php.html#40\">App\\Actions\\WorkOrderIssue\\UpdateIssueBasicDetails::handle<\/a>"],[0,5,"<a href=\"WorkOrderIssue\/UpdateIssueBasicDetails.php.html#48\">App\\Actions\\WorkOrderIssue\\UpdateIssueBasicDetails::asController<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/UpdateIssueBasicDetails.php.html#103\">App\\Actions\\WorkOrderIssue\\UpdateIssueBasicDetails::authorize<\/a>"],[100,1,"<a href=\"WorkOrderIssue\/UpdateWorkOrderIssue.php.html#36\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::getSlug<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/UpdateWorkOrderIssue.php.html#44\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::handle<\/a>"],[0,9,"<a href=\"WorkOrderIssue\/UpdateWorkOrderIssue.php.html#66\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::asController<\/a>"],[0,2,"<a href=\"WorkOrderIssue\/UpdateWorkOrderIssue.php.html#148\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::authorize<\/a>"],[0,8,"<a href=\"WorkOrderIssue\/UpdateWorkOrderIssue.php.html#158\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::updateMaterials<\/a>"],[0,4,"<a href=\"WorkOrderIssue\/UpdateWorkOrderIssue.php.html#208\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::authorizeTechnicianAccess<\/a>"],[0,3,"<a href=\"WorkOrderIssue\/UpdateWorkOrderIssue.php.html#227\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::validateMediaLimit<\/a>"],[0,4,"<a href=\"WorkOrderIssue\/UpdateWorkOrderIssue.php.html#261\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::handleIssueMedia<\/a>"],[100,1,"<a href=\"WorkOrders\/AwaitingAvailabilityWorkOrder.php.html#30\">App\\Actions\\WorkOrders\\AwaitingAvailabilityWorkOrder::getSlug<\/a>"],[0,1,"<a href=\"WorkOrders\/AwaitingAvailabilityWorkOrder.php.html#35\">App\\Actions\\WorkOrders\\AwaitingAvailabilityWorkOrder::handle<\/a>"],[0,11,"<a href=\"WorkOrders\/AwaitingAvailabilityWorkOrder.php.html#48\">App\\Actions\\WorkOrders\\AwaitingAvailabilityWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/AwaitingAvailabilityWorkOrder.php.html#154\">App\\Actions\\WorkOrders\\AwaitingAvailabilityWorkOrder::authorize<\/a>"],[0,1,"<a href=\"WorkOrders\/BaseAction.php.html#28\">App\\Actions\\WorkOrders\\BaseAction::getSlug<\/a>"],[100,1,"<a href=\"WorkOrders\/BaseAction.php.html#33\">App\\Actions\\WorkOrders\\BaseAction::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/BaseAction.php.html#38\">App\\Actions\\WorkOrders\\BaseAction::routes<\/a>"],[0,1,"<a href=\"WorkOrders\/BaseAction.php.html#47\">App\\Actions\\WorkOrders\\BaseAction::authorize<\/a>"],[0,4,"<a href=\"WorkOrders\/BaseAction.php.html#55\">App\\Actions\\WorkOrders\\BaseAction::validateWorkOrder<\/a>"],[0,5,"<a href=\"WorkOrders\/BaseAction.php.html#69\">App\\Actions\\WorkOrders\\BaseAction::handleException<\/a>"],[0,4,"<a href=\"WorkOrders\/BaseAction.php.html#90\">App\\Actions\\WorkOrders\\BaseAction::canPerformTripActionByUser<\/a>"],[100,1,"<a href=\"WorkOrders\/CancelWorkOrder.php.html#43\">App\\Actions\\WorkOrders\\CancelWorkOrder::getSlug<\/a>"],[0,5,"<a href=\"WorkOrders\/CancelWorkOrder.php.html#48\">App\\Actions\\WorkOrders\\CancelWorkOrder::handle<\/a>"],[0,3,"<a href=\"WorkOrders\/CancelWorkOrder.php.html#93\">App\\Actions\\WorkOrders\\CancelWorkOrder::cancelWorkOrderIssues<\/a>"],[0,3,"<a href=\"WorkOrders\/CancelWorkOrder.php.html#115\">App\\Actions\\WorkOrders\\CancelWorkOrder::updateIssueStatus<\/a>"],[0,2,"<a href=\"WorkOrders\/CancelWorkOrder.php.html#126\">App\\Actions\\WorkOrders\\CancelWorkOrder::canIssueStatusBeUpdatedToUnassigned<\/a>"],[0,8,"<a href=\"WorkOrders\/CancelWorkOrder.php.html#134\">App\\Actions\\WorkOrders\\CancelWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/CancelWorkOrder.php.html#218\">App\\Actions\\WorkOrders\\CancelWorkOrder::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/CloseWorkOrder.php.html#41\">App\\Actions\\WorkOrders\\CloseWorkOrder::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/CloseWorkOrder.php.html#46\">App\\Actions\\WorkOrders\\CloseWorkOrder::getSlug<\/a>"],[0,6,"<a href=\"WorkOrders\/CloseWorkOrder.php.html#56\">App\\Actions\\WorkOrders\\CloseWorkOrder::handle<\/a>"],[0,7,"<a href=\"WorkOrders\/CloseWorkOrder.php.html#104\">App\\Actions\\WorkOrders\\CloseWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/CloseWorkOrder.php.html#173\">App\\Actions\\WorkOrders\\CloseWorkOrder::authorize<\/a>"],[0,4,"<a href=\"WorkOrders\/CloseWorkOrder.php.html#181\">App\\Actions\\WorkOrders\\CloseWorkOrder::checkAllIssuesAreReadyToClose<\/a>"],[0,1,"<a href=\"WorkOrders\/CloseWorkOrder.php.html#194\">App\\Actions\\WorkOrders\\CloseWorkOrder::updateWorkOrderIssusStatusToQualityCheck<\/a>"],[0,2,"<a href=\"WorkOrders\/CloseWorkOrder.php.html#200\">App\\Actions\\WorkOrders\\CloseWorkOrder::updateIssusStatusToQualityCheck<\/a>"],[100,1,"<a href=\"WorkOrders\/CompleteWorkOrder.php.html#45\">App\\Actions\\WorkOrders\\CompleteWorkOrder::getSlug<\/a>"],[0,12,"<a href=\"WorkOrders\/CompleteWorkOrder.php.html#53\">App\\Actions\\WorkOrders\\CompleteWorkOrder::handle<\/a>"],[0,16,"<a href=\"WorkOrders\/CompleteWorkOrder.php.html#136\">App\\Actions\\WorkOrders\\CompleteWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/CompleteWorkOrder.php.html#270\">App\\Actions\\WorkOrders\\CompleteWorkOrder::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/DeleteWorkOrder.php.html#38\">App\\Actions\\WorkOrders\\DeleteWorkOrder::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/DeleteWorkOrder.php.html#43\">App\\Actions\\WorkOrders\\DeleteWorkOrder::getSlug<\/a>"],[0,1,"<a href=\"WorkOrders\/DeleteWorkOrder.php.html#48\">App\\Actions\\WorkOrders\\DeleteWorkOrder::handle<\/a>"],[0,4,"<a href=\"WorkOrders\/DeleteWorkOrder.php.html#61\">App\\Actions\\WorkOrders\\DeleteWorkOrder::deleteWorkOrderIssues<\/a>"],[0,3,"<a href=\"WorkOrders\/DeleteWorkOrder.php.html#85\">App\\Actions\\WorkOrders\\DeleteWorkOrder::updateIssueStatus<\/a>"],[0,2,"<a href=\"WorkOrders\/DeleteWorkOrder.php.html#95\">App\\Actions\\WorkOrders\\DeleteWorkOrder::canIssueStatusBeUpdatedToUnassigned<\/a>"],[0,5,"<a href=\"WorkOrders\/DeleteWorkOrder.php.html#103\">App\\Actions\\WorkOrders\\DeleteWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/DeleteWorkOrder.php.html#129\">App\\Actions\\WorkOrders\\DeleteWorkOrder::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/EnRouteWork.php.html#31\">App\\Actions\\WorkOrders\\EnRouteWork::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/EnRouteWork.php.html#36\">App\\Actions\\WorkOrders\\EnRouteWork::getSlug<\/a>"],[0,1,"<a href=\"WorkOrders\/EnRouteWork.php.html#41\">App\\Actions\\WorkOrders\\EnRouteWork::handle<\/a>"],[0,7,"<a href=\"WorkOrders\/EnRouteWork.php.html#63\">App\\Actions\\WorkOrders\\EnRouteWork::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/EnRouteWork.php.html#135\">App\\Actions\\WorkOrders\\EnRouteWork::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/PauseEnRoute.php.html#31\">App\\Actions\\WorkOrders\\PauseEnRoute::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/PauseEnRoute.php.html#36\">App\\Actions\\WorkOrders\\PauseEnRoute::getSlug<\/a>"],[0,3,"<a href=\"WorkOrders\/PauseEnRoute.php.html#44\">App\\Actions\\WorkOrders\\PauseEnRoute::handle<\/a>"],[0,7,"<a href=\"WorkOrders\/PauseEnRoute.php.html#74\">App\\Actions\\WorkOrders\\PauseEnRoute::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/PauseEnRoute.php.html#151\">App\\Actions\\WorkOrders\\PauseEnRoute::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/PauseTimer.php.html#30\">App\\Actions\\WorkOrders\\PauseTimer::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/PauseTimer.php.html#35\">App\\Actions\\WorkOrders\\PauseTimer::getSlug<\/a>"],[0,3,"<a href=\"WorkOrders\/PauseTimer.php.html#43\">App\\Actions\\WorkOrders\\PauseTimer::handle<\/a>"],[0,7,"<a href=\"WorkOrders\/PauseTimer.php.html#75\">App\\Actions\\WorkOrders\\PauseTimer::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/PauseTimer.php.html#154\">App\\Actions\\WorkOrders\\PauseTimer::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/PauseWorkOrder.php.html#33\">App\\Actions\\WorkOrders\\PauseWorkOrder::getSlug<\/a>"],[0,3,"<a href=\"WorkOrders\/PauseWorkOrder.php.html#38\">App\\Actions\\WorkOrders\\PauseWorkOrder::handle<\/a>"],[0,11,"<a href=\"WorkOrders\/PauseWorkOrder.php.html#54\">App\\Actions\\WorkOrders\\PauseWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/PauseWorkOrder.php.html#129\">App\\Actions\\WorkOrders\\PauseWorkOrder::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/ReOpenWorkOrder.php.html#28\">App\\Actions\\WorkOrders\\ReOpenWorkOrder::getSlug<\/a>"],[0,1,"<a href=\"WorkOrders\/ReOpenWorkOrder.php.html#33\">App\\Actions\\WorkOrders\\ReOpenWorkOrder::handle<\/a>"],[0,7,"<a href=\"WorkOrders\/ReOpenWorkOrder.php.html#46\">App\\Actions\\WorkOrders\\ReOpenWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/ReOpenWorkOrder.php.html#100\">App\\Actions\\WorkOrders\\ReOpenWorkOrder::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/ReadyToInvoiceWorkOrder.php.html#32\">App\\Actions\\WorkOrders\\ReadyToInvoiceWorkOrder::getSlug<\/a>"],[0,15,"<a href=\"WorkOrders\/ReadyToInvoiceWorkOrder.php.html#40\">App\\Actions\\WorkOrders\\ReadyToInvoiceWorkOrder::handle<\/a>"],[0,8,"<a href=\"WorkOrders\/ReadyToInvoiceWorkOrder.php.html#119\">App\\Actions\\WorkOrders\\ReadyToInvoiceWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/ReadyToInvoiceWorkOrder.php.html#182\">App\\Actions\\WorkOrders\\ReadyToInvoiceWorkOrder::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/ReadyToScheduleWorkOrder.php.html#30\">App\\Actions\\WorkOrders\\ReadyToScheduleWorkOrder::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/ReadyToScheduleWorkOrder.php.html#35\">App\\Actions\\WorkOrders\\ReadyToScheduleWorkOrder::getSlug<\/a>"],[0,1,"<a href=\"WorkOrders\/ReadyToScheduleWorkOrder.php.html#40\">App\\Actions\\WorkOrders\\ReadyToScheduleWorkOrder::handle<\/a>"],[0,8,"<a href=\"WorkOrders\/ReadyToScheduleWorkOrder.php.html#58\">App\\Actions\\WorkOrders\\ReadyToScheduleWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/ReadyToScheduleWorkOrder.php.html#136\">App\\Actions\\WorkOrders\\ReadyToScheduleWorkOrder::authorize<\/a>"],[0,1,"<a href=\"WorkOrders\/RescheduleWorkOrder.php.html#29\">App\\Actions\\WorkOrders\\RescheduleWorkOrder::__construct<\/a>"],[100,1,"<a href=\"WorkOrders\/RescheduleWorkOrder.php.html#31\">App\\Actions\\WorkOrders\\RescheduleWorkOrder::getSlug<\/a>"],[100,1,"<a href=\"WorkOrders\/RescheduleWorkOrder.php.html#36\">App\\Actions\\WorkOrders\\RescheduleWorkOrder::getRoute<\/a>"],[0,1,"<a href=\"WorkOrders\/RescheduleWorkOrder.php.html#41\">App\\Actions\\WorkOrders\\RescheduleWorkOrder::handle<\/a>"],[0,7,"<a href=\"WorkOrders\/RescheduleWorkOrder.php.html#65\">App\\Actions\\WorkOrders\\RescheduleWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/RescheduleWorkOrder.php.html#135\">App\\Actions\\WorkOrders\\RescheduleWorkOrder::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/ResolveWorkOrder.php.html#27\">App\\Actions\\WorkOrders\\ResolveWorkOrder::getSlug<\/a>"],[0,1,"<a href=\"WorkOrders\/ResolveWorkOrder.php.html#32\">App\\Actions\\WorkOrders\\ResolveWorkOrder::handle<\/a>"],[0,7,"<a href=\"WorkOrders\/ResolveWorkOrder.php.html#45\">App\\Actions\\WorkOrders\\ResolveWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/ResolveWorkOrder.php.html#97\">App\\Actions\\WorkOrders\\ResolveWorkOrder::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/ResumeEnRoute.php.html#31\">App\\Actions\\WorkOrders\\ResumeEnRoute::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/ResumeEnRoute.php.html#36\">App\\Actions\\WorkOrders\\ResumeEnRoute::getSlug<\/a>"],[0,3,"<a href=\"WorkOrders\/ResumeEnRoute.php.html#41\">App\\Actions\\WorkOrders\\ResumeEnRoute::handle<\/a>"],[0,7,"<a href=\"WorkOrders\/ResumeEnRoute.php.html#73\">App\\Actions\\WorkOrders\\ResumeEnRoute::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/ResumeEnRoute.php.html#148\">App\\Actions\\WorkOrders\\ResumeEnRoute::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/ResumeWork.php.html#31\">App\\Actions\\WorkOrders\\ResumeWork::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/ResumeWork.php.html#36\">App\\Actions\\WorkOrders\\ResumeWork::getSlug<\/a>"],[0,3,"<a href=\"WorkOrders\/ResumeWork.php.html#41\">App\\Actions\\WorkOrders\\ResumeWork::handle<\/a>"],[0,7,"<a href=\"WorkOrders\/ResumeWork.php.html#74\">App\\Actions\\WorkOrders\\ResumeWork::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/ResumeWork.php.html#152\">App\\Actions\\WorkOrders\\ResumeWork::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/ScheduleToVendor.php.html#38\">App\\Actions\\WorkOrders\\ScheduleToVendor::getSlug<\/a>"],[100,1,"<a href=\"WorkOrders\/ScheduleToVendor.php.html#43\">App\\Actions\\WorkOrders\\ScheduleToVendor::getRoute<\/a>"],[0,6,"<a href=\"WorkOrders\/ScheduleToVendor.php.html#51\">App\\Actions\\WorkOrders\\ScheduleToVendor::handle<\/a>"],[0,7,"<a href=\"WorkOrders\/ScheduleToVendor.php.html#107\">App\\Actions\\WorkOrders\\ScheduleToVendor::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/ScheduleToVendor.php.html#214\">App\\Actions\\WorkOrders\\ScheduleToVendor::authorize<\/a>"],[0,1,"<a href=\"WorkOrders\/ScheduleWorkOrder.php.html#26\">App\\Actions\\WorkOrders\\ScheduleWorkOrder::__construct<\/a>"],[100,1,"<a href=\"WorkOrders\/ScheduleWorkOrder.php.html#28\">App\\Actions\\WorkOrders\\ScheduleWorkOrder::getSlug<\/a>"],[100,1,"<a href=\"WorkOrders\/ScheduleWorkOrder.php.html#33\">App\\Actions\\WorkOrders\\ScheduleWorkOrder::getRoute<\/a>"],[0,1,"<a href=\"WorkOrders\/ScheduleWorkOrder.php.html#38\">App\\Actions\\WorkOrders\\ScheduleWorkOrder::handle<\/a>"],[0,6,"<a href=\"WorkOrders\/ScheduleWorkOrder.php.html#64\">App\\Actions\\WorkOrders\\ScheduleWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/ScheduleWorkOrder.php.html#159\">App\\Actions\\WorkOrders\\ScheduleWorkOrder::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/StartWork.php.html#28\">App\\Actions\\WorkOrders\\StartWork::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/StartWork.php.html#33\">App\\Actions\\WorkOrders\\StartWork::getSlug<\/a>"],[0,4,"<a href=\"WorkOrders\/StartWork.php.html#38\">App\\Actions\\WorkOrders\\StartWork::handle<\/a>"],[0,7,"<a href=\"WorkOrders\/StartWork.php.html#72\">App\\Actions\\WorkOrders\\StartWork::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/StartWork.php.html#148\">App\\Actions\\WorkOrders\\StartWork::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/TagWorkOrder.php.html#27\">App\\Actions\\WorkOrders\\TagWorkOrder::getSlug<\/a>"],[100,1,"<a href=\"WorkOrders\/TagWorkOrder.php.html#32\">App\\Actions\\WorkOrders\\TagWorkOrder::routes<\/a>"],[0,2,"<a href=\"WorkOrders\/TagWorkOrder.php.html#41\">App\\Actions\\WorkOrders\\TagWorkOrder::handle<\/a>"],[0,5,"<a href=\"WorkOrders\/TagWorkOrder.php.html#66\">App\\Actions\\WorkOrders\\TagWorkOrder::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/TagWorkOrder.php.html#99\">App\\Actions\\WorkOrders\\TagWorkOrder::authorize<\/a>"],[0,1,"<a href=\"WorkOrders\/TechnicianSchedule.php.html#41\">App\\Actions\\WorkOrders\\TechnicianSchedule::__construct<\/a>"],[100,1,"<a href=\"WorkOrders\/TechnicianSchedule.php.html#43\">App\\Actions\\WorkOrders\\TechnicianSchedule::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/TechnicianSchedule.php.html#48\">App\\Actions\\WorkOrders\\TechnicianSchedule::getSlug<\/a>"],[0,1,"<a href=\"WorkOrders\/TechnicianSchedule.php.html#53\">App\\Actions\\WorkOrders\\TechnicianSchedule::handle<\/a>"],[0,23,"<a href=\"WorkOrders\/TechnicianSchedule.php.html#81\">App\\Actions\\WorkOrders\\TechnicianSchedule::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/TechnicianSchedule.php.html#306\">App\\Actions\\WorkOrders\\TechnicianSchedule::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/UpdateNteAmount.php.html#22\">App\\Actions\\WorkOrders\\UpdateNteAmount::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/UpdateNteAmount.php.html#27\">App\\Actions\\WorkOrders\\UpdateNteAmount::getSlug<\/a>"],[0,3,"<a href=\"WorkOrders\/UpdateNteAmount.php.html#35\">App\\Actions\\WorkOrders\\UpdateNteAmount::handle<\/a>"],[0,3,"<a href=\"WorkOrders\/UpdateNteAmount.php.html#55\">App\\Actions\\WorkOrders\\UpdateNteAmount::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/UpdateNteAmount.php.html#77\">App\\Actions\\WorkOrders\\UpdateNteAmount::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/UpdateTripDetails.php.html#32\">App\\Actions\\WorkOrders\\UpdateTripDetails::getRoute<\/a>"],[100,1,"<a href=\"WorkOrders\/UpdateTripDetails.php.html#37\">App\\Actions\\WorkOrders\\UpdateTripDetails::getSlug<\/a>"],[0,1,"<a href=\"WorkOrders\/UpdateTripDetails.php.html#42\">App\\Actions\\WorkOrders\\UpdateTripDetails::handle<\/a>"],[0,4,"<a href=\"WorkOrders\/UpdateTripDetails.php.html#65\">App\\Actions\\WorkOrders\\UpdateTripDetails::asController<\/a>"],[0,3,"<a href=\"WorkOrders\/UpdateTripDetails.php.html#104\">App\\Actions\\WorkOrders\\UpdateTripDetails::updateTripState<\/a>"],[0,1,"<a href=\"WorkOrders\/UpdateTripDetails.php.html#125\">App\\Actions\\WorkOrders\\UpdateTripDetails::handleIssueMedia<\/a>"],[0,6,"<a href=\"WorkOrders\/UpdateTripDetails.php.html#139\">App\\Actions\\WorkOrders\\UpdateTripDetails::updateOrCreateMaterial<\/a>"],[0,4,"<a href=\"WorkOrders\/UpdateTripDetails.php.html#178\">App\\Actions\\WorkOrders\\UpdateTripDetails::updateNotes<\/a>"],[0,2,"<a href=\"WorkOrders\/UpdateTripDetails.php.html#194\">App\\Actions\\WorkOrders\\UpdateTripDetails::authorize<\/a>"],[100,1,"<a href=\"WorkOrders\/UpdateWorkOrderTrip.php.html#42\">App\\Actions\\WorkOrders\\UpdateWorkOrderTrip::getSlug<\/a>"],[100,1,"<a href=\"WorkOrders\/UpdateWorkOrderTrip.php.html#47\">App\\Actions\\WorkOrders\\UpdateWorkOrderTrip::getRoute<\/a>"],[0,33,"<a href=\"WorkOrders\/UpdateWorkOrderTrip.php.html#55\">App\\Actions\\WorkOrders\\UpdateWorkOrderTrip::handle<\/a>"],[0,9,"<a href=\"WorkOrders\/UpdateWorkOrderTrip.php.html#230\">App\\Actions\\WorkOrders\\UpdateWorkOrderTrip::asController<\/a>"],[0,2,"<a href=\"WorkOrders\/UpdateWorkOrderTrip.php.html#303\">App\\Actions\\WorkOrders\\UpdateWorkOrderTrip::authorize<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Actions/Issue</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">Issue</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AssignIssue.php.html#30">App\Actions\Issue\AssignIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#10">App\Actions\Issue\BaseAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelIssue.php.html#34">App\Actions\Issue\CancelIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#28">App\Actions\Issue\CreateIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteIssue.php.html#35">App\Actions\Issue\DeleteIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RestoreIssue.php.html#28">App\Actions\Issue\RestoreIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#35">App\Actions\Issue\UnassignIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UndoIssue.php.html#29">App\Actions\Issue\UndoIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssue.php.html#28">App\Actions\Issue\UpdateIssue</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CancelIssue.php.html#34">App\Actions\Issue\CancelIssue</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="DeleteIssue.php.html#35">App\Actions\Issue\DeleteIssue</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="UnassignIssue.php.html#35">App\Actions\Issue\UnassignIssue</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="UpdateIssue.php.html#28">App\Actions\Issue\UpdateIssue</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="AssignIssue.php.html#30">App\Actions\Issue\AssignIssue</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="UndoIssue.php.html#29">App\Actions\Issue\UndoIssue</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="RestoreIssue.php.html#28">App\Actions\Issue\RestoreIssue</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="CreateIssue.php.html#28">App\Actions\Issue\CreateIssue</a></td><td class="text-right">132</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AssignIssue.php.html#36"><abbr title="App\Actions\Issue\AssignIssue::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssignIssue.php.html#41"><abbr title="App\Actions\Issue\AssignIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssignIssue.php.html#49"><abbr title="App\Actions\Issue\AssignIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssignIssue.php.html#66"><abbr title="App\Actions\Issue\AssignIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssignIssue.php.html#125"><abbr title="App\Actions\Issue\AssignIssue::isIssueAlreadyAssigned">isIssueAlreadyAssigned</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssignIssue.php.html#133"><abbr title="App\Actions\Issue\AssignIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#14"><abbr title="App\Actions\Issue\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#19"><abbr title="App\Actions\Issue\BaseAction::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#24"><abbr title="App\Actions\Issue\BaseAction::routes">routes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#33"><abbr title="App\Actions\Issue\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelIssue.php.html#38"><abbr title="App\Actions\Issue\CancelIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelIssue.php.html#43"><abbr title="App\Actions\Issue\CancelIssue::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelIssue.php.html#48"><abbr title="App\Actions\Issue\CancelIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelIssue.php.html#75"><abbr title="App\Actions\Issue\CancelIssue::getAllAssociatedWorkOrders">getAllAssociatedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelIssue.php.html#96"><abbr title="App\Actions\Issue\CancelIssue::hasAnyScheduledOrInprogressWorkOrderExists">hasAnyScheduledOrInprogressWorkOrderExists</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelIssue.php.html#117"><abbr title="App\Actions\Issue\CancelIssue::getWorkOrdersWantToCancel">getWorkOrdersWantToCancel</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelIssue.php.html#129"><abbr title="App\Actions\Issue\CancelIssue::cancelWorkOrder">cancelWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelIssue.php.html#140"><abbr title="App\Actions\Issue\CancelIssue::cancelWorkOrderIssues">cancelWorkOrderIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelIssue.php.html#156"><abbr title="App\Actions\Issue\CancelIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CancelIssue.php.html#202"><abbr title="App\Actions\Issue\CancelIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#32"><abbr title="App\Actions\Issue\CreateIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#42"><abbr title="App\Actions\Issue\CreateIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#59"><abbr title="App\Actions\Issue\CreateIssue::getProblemDiagnosis">getProblemDiagnosis</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#75"><abbr title="App\Actions\Issue\CreateIssue::createIssue">createIssue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#96"><abbr title="App\Actions\Issue\CreateIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#131"><abbr title="App\Actions\Issue\CreateIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteIssue.php.html#41"><abbr title="App\Actions\Issue\DeleteIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteIssue.php.html#46"><abbr title="App\Actions\Issue\DeleteIssue::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteIssue.php.html#51"><abbr title="App\Actions\Issue\DeleteIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteIssue.php.html#75"><abbr title="App\Actions\Issue\DeleteIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteIssue.php.html#103"><abbr title="App\Actions\Issue\DeleteIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteIssue.php.html#118"><abbr title="App\Actions\Issue\DeleteIssue::hasAnyScheduledOrInprogressWorkOrderExists">hasAnyScheduledOrInprogressWorkOrderExists</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteIssue.php.html#136"><abbr title="App\Actions\Issue\DeleteIssue::getWorkOrderIssues">getWorkOrderIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteIssue.php.html#153"><abbr title="App\Actions\Issue\DeleteIssue::handleWorkOrderIssues">handleWorkOrderIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RestoreIssue.php.html#34"><abbr title="App\Actions\Issue\RestoreIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RestoreIssue.php.html#39"><abbr title="App\Actions\Issue\RestoreIssue::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RestoreIssue.php.html#44"><abbr title="App\Actions\Issue\RestoreIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RestoreIssue.php.html#60"><abbr title="App\Actions\Issue\RestoreIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RestoreIssue.php.html#100"><abbr title="App\Actions\Issue\RestoreIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#41"><abbr title="App\Actions\Issue\UnassignIssue::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#46"><abbr title="App\Actions\Issue\UnassignIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#54"><abbr title="App\Actions\Issue\UnassignIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#90"><abbr title="App\Actions\Issue\UnassignIssue::checkWorkOrderHasIssues">checkWorkOrderHasIssues</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#103"><abbr title="App\Actions\Issue\UnassignIssue::checkIssueHasAssigned">checkIssueHasAssigned</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#113"><abbr title="App\Actions\Issue\UnassignIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#165"><abbr title="App\Actions\Issue\UnassignIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UndoIssue.php.html#35"><abbr title="App\Actions\Issue\UndoIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UndoIssue.php.html#40"><abbr title="App\Actions\Issue\UndoIssue::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UndoIssue.php.html#45"><abbr title="App\Actions\Issue\UndoIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UndoIssue.php.html#60"><abbr title="App\Actions\Issue\UndoIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UndoIssue.php.html#103"><abbr title="App\Actions\Issue\UndoIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UndoIssue.php.html#108"><abbr title="App\Actions\Issue\UndoIssue::restoreIssue">restoreIssue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UndoIssue.php.html#118"><abbr title="App\Actions\Issue\UndoIssue::deleteActivityLogs">deleteActivityLogs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssue.php.html#34"><abbr title="App\Actions\Issue\UpdateIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssue.php.html#39"><abbr title="App\Actions\Issue\UpdateIssue::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssue.php.html#51"><abbr title="App\Actions\Issue\UpdateIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssue.php.html#112"><abbr title="App\Actions\Issue\UpdateIssue::triggerBroadCasts">triggerBroadCasts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssue.php.html#131"><abbr title="App\Actions\Issue\UpdateIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssue.php.html#178"><abbr title="App\Actions\Issue\UpdateIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AssignIssue.php.html#66"><abbr title="App\Actions\Issue\AssignIssue::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CancelIssue.php.html#156"><abbr title="App\Actions\Issue\CancelIssue::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="RestoreIssue.php.html#60"><abbr title="App\Actions\Issue\RestoreIssue::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="UnassignIssue.php.html#113"><abbr title="App\Actions\Issue\UnassignIssue::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="DeleteIssue.php.html#75"><abbr title="App\Actions\Issue\DeleteIssue::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="UndoIssue.php.html#60"><abbr title="App\Actions\Issue\UndoIssue::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="CreateIssue.php.html#96"><abbr title="App\Actions\Issue\CreateIssue::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="DeleteIssue.php.html#153"><abbr title="App\Actions\Issue\DeleteIssue::handleWorkOrderIssues">handleWorkOrderIssues</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UpdateIssue.php.html#51"><abbr title="App\Actions\Issue\UpdateIssue::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UpdateIssue.php.html#131"><abbr title="App\Actions\Issue\UpdateIssue::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="CancelIssue.php.html#48"><abbr title="App\Actions\Issue\CancelIssue::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UnassignIssue.php.html#54"><abbr title="App\Actions\Issue\UnassignIssue::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AssignIssue.php.html#133"><abbr title="App\Actions\Issue\AssignIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CancelIssue.php.html#129"><abbr title="App\Actions\Issue\CancelIssue::cancelWorkOrder">cancelWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CancelIssue.php.html#140"><abbr title="App\Actions\Issue\CancelIssue::cancelWorkOrderIssues">cancelWorkOrderIssues</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CancelIssue.php.html#202"><abbr title="App\Actions\Issue\CancelIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CreateIssue.php.html#131"><abbr title="App\Actions\Issue\CreateIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DeleteIssue.php.html#51"><abbr title="App\Actions\Issue\DeleteIssue::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DeleteIssue.php.html#103"><abbr title="App\Actions\Issue\DeleteIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RestoreIssue.php.html#100"><abbr title="App\Actions\Issue\RestoreIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UnassignIssue.php.html#165"><abbr title="App\Actions\Issue\UnassignIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UndoIssue.php.html#103"><abbr title="App\Actions\Issue\UndoIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UndoIssue.php.html#118"><abbr title="App\Actions\Issue\UndoIssue::deleteActivityLogs">deleteActivityLogs</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateIssue.php.html#178"><abbr title="App\Actions\Issue\UpdateIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([9,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([59,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,14,"<a href=\"AssignIssue.php.html#30\">App\\Actions\\Issue\\AssignIssue<\/a>"],[0,4,"<a href=\"BaseAction.php.html#10\">App\\Actions\\Issue\\BaseAction<\/a>"],[0,22,"<a href=\"CancelIssue.php.html#34\">App\\Actions\\Issue\\CancelIssue<\/a>"],[0,11,"<a href=\"CreateIssue.php.html#28\">App\\Actions\\Issue\\CreateIssue<\/a>"],[0,19,"<a href=\"DeleteIssue.php.html#35\">App\\Actions\\Issue\\DeleteIssue<\/a>"],[0,12,"<a href=\"RestoreIssue.php.html#28\">App\\Actions\\Issue\\RestoreIssue<\/a>"],[0,16,"<a href=\"UnassignIssue.php.html#35\">App\\Actions\\Issue\\UnassignIssue<\/a>"],[0,14,"<a href=\"UndoIssue.php.html#29\">App\\Actions\\Issue\\UndoIssue<\/a>"],[0,15,"<a href=\"UpdateIssue.php.html#28\">App\\Actions\\Issue\\UpdateIssue<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AssignIssue.php.html#36\">App\\Actions\\Issue\\AssignIssue::getRoute<\/a>"],[0,1,"<a href=\"AssignIssue.php.html#41\">App\\Actions\\Issue\\AssignIssue::getSlug<\/a>"],[0,1,"<a href=\"AssignIssue.php.html#49\">App\\Actions\\Issue\\AssignIssue::handle<\/a>"],[0,8,"<a href=\"AssignIssue.php.html#66\">App\\Actions\\Issue\\AssignIssue::asController<\/a>"],[0,1,"<a href=\"AssignIssue.php.html#125\">App\\Actions\\Issue\\AssignIssue::isIssueAlreadyAssigned<\/a>"],[0,2,"<a href=\"AssignIssue.php.html#133\">App\\Actions\\Issue\\AssignIssue::authorize<\/a>"],[0,1,"<a href=\"BaseAction.php.html#14\">App\\Actions\\Issue\\BaseAction::getSlug<\/a>"],[0,1,"<a href=\"BaseAction.php.html#19\">App\\Actions\\Issue\\BaseAction::getRoute<\/a>"],[0,1,"<a href=\"BaseAction.php.html#24\">App\\Actions\\Issue\\BaseAction::routes<\/a>"],[0,1,"<a href=\"BaseAction.php.html#33\">App\\Actions\\Issue\\BaseAction::authorize<\/a>"],[0,1,"<a href=\"CancelIssue.php.html#38\">App\\Actions\\Issue\\CancelIssue::getSlug<\/a>"],[0,1,"<a href=\"CancelIssue.php.html#43\">App\\Actions\\Issue\\CancelIssue::getRoute<\/a>"],[0,3,"<a href=\"CancelIssue.php.html#48\">App\\Actions\\Issue\\CancelIssue::handle<\/a>"],[0,1,"<a href=\"CancelIssue.php.html#75\">App\\Actions\\Issue\\CancelIssue::getAllAssociatedWorkOrders<\/a>"],[0,1,"<a href=\"CancelIssue.php.html#96\">App\\Actions\\Issue\\CancelIssue::hasAnyScheduledOrInprogressWorkOrderExists<\/a>"],[0,1,"<a href=\"CancelIssue.php.html#117\">App\\Actions\\Issue\\CancelIssue::getWorkOrdersWantToCancel<\/a>"],[0,2,"<a href=\"CancelIssue.php.html#129\">App\\Actions\\Issue\\CancelIssue::cancelWorkOrder<\/a>"],[0,2,"<a href=\"CancelIssue.php.html#140\">App\\Actions\\Issue\\CancelIssue::cancelWorkOrderIssues<\/a>"],[0,8,"<a href=\"CancelIssue.php.html#156\">App\\Actions\\Issue\\CancelIssue::asController<\/a>"],[0,2,"<a href=\"CancelIssue.php.html#202\">App\\Actions\\Issue\\CancelIssue::authorize<\/a>"],[0,1,"<a href=\"CreateIssue.php.html#32\">App\\Actions\\Issue\\CreateIssue::getSlug<\/a>"],[0,1,"<a href=\"CreateIssue.php.html#42\">App\\Actions\\Issue\\CreateIssue::handle<\/a>"],[0,1,"<a href=\"CreateIssue.php.html#59\">App\\Actions\\Issue\\CreateIssue::getProblemDiagnosis<\/a>"],[0,1,"<a href=\"CreateIssue.php.html#75\">App\\Actions\\Issue\\CreateIssue::createIssue<\/a>"],[0,5,"<a href=\"CreateIssue.php.html#96\">App\\Actions\\Issue\\CreateIssue::asController<\/a>"],[0,2,"<a href=\"CreateIssue.php.html#131\">App\\Actions\\Issue\\CreateIssue::authorize<\/a>"],[0,1,"<a href=\"DeleteIssue.php.html#41\">App\\Actions\\Issue\\DeleteIssue::getSlug<\/a>"],[0,1,"<a href=\"DeleteIssue.php.html#46\">App\\Actions\\Issue\\DeleteIssue::getRoute<\/a>"],[0,2,"<a href=\"DeleteIssue.php.html#51\">App\\Actions\\Issue\\DeleteIssue::handle<\/a>"],[0,6,"<a href=\"DeleteIssue.php.html#75\">App\\Actions\\Issue\\DeleteIssue::asController<\/a>"],[0,2,"<a href=\"DeleteIssue.php.html#103\">App\\Actions\\Issue\\DeleteIssue::authorize<\/a>"],[0,1,"<a href=\"DeleteIssue.php.html#118\">App\\Actions\\Issue\\DeleteIssue::hasAnyScheduledOrInprogressWorkOrderExists<\/a>"],[0,1,"<a href=\"DeleteIssue.php.html#136\">App\\Actions\\Issue\\DeleteIssue::getWorkOrderIssues<\/a>"],[0,5,"<a href=\"DeleteIssue.php.html#153\">App\\Actions\\Issue\\DeleteIssue::handleWorkOrderIssues<\/a>"],[0,1,"<a href=\"RestoreIssue.php.html#34\">App\\Actions\\Issue\\RestoreIssue::getSlug<\/a>"],[0,1,"<a href=\"RestoreIssue.php.html#39\">App\\Actions\\Issue\\RestoreIssue::getRoute<\/a>"],[0,1,"<a href=\"RestoreIssue.php.html#44\">App\\Actions\\Issue\\RestoreIssue::handle<\/a>"],[0,7,"<a href=\"RestoreIssue.php.html#60\">App\\Actions\\Issue\\RestoreIssue::asController<\/a>"],[0,2,"<a href=\"RestoreIssue.php.html#100\">App\\Actions\\Issue\\RestoreIssue::authorize<\/a>"],[0,1,"<a href=\"UnassignIssue.php.html#41\">App\\Actions\\Issue\\UnassignIssue::getRoute<\/a>"],[0,1,"<a href=\"UnassignIssue.php.html#46\">App\\Actions\\Issue\\UnassignIssue::getSlug<\/a>"],[0,3,"<a href=\"UnassignIssue.php.html#54\">App\\Actions\\Issue\\UnassignIssue::handle<\/a>"],[0,1,"<a href=\"UnassignIssue.php.html#90\">App\\Actions\\Issue\\UnassignIssue::checkWorkOrderHasIssues<\/a>"],[0,1,"<a href=\"UnassignIssue.php.html#103\">App\\Actions\\Issue\\UnassignIssue::checkIssueHasAssigned<\/a>"],[0,7,"<a href=\"UnassignIssue.php.html#113\">App\\Actions\\Issue\\UnassignIssue::asController<\/a>"],[0,2,"<a href=\"UnassignIssue.php.html#165\">App\\Actions\\Issue\\UnassignIssue::authorize<\/a>"],[0,1,"<a href=\"UndoIssue.php.html#35\">App\\Actions\\Issue\\UndoIssue::getSlug<\/a>"],[0,1,"<a href=\"UndoIssue.php.html#40\">App\\Actions\\Issue\\UndoIssue::getRoute<\/a>"],[0,1,"<a href=\"UndoIssue.php.html#45\">App\\Actions\\Issue\\UndoIssue::handle<\/a>"],[0,6,"<a href=\"UndoIssue.php.html#60\">App\\Actions\\Issue\\UndoIssue::asController<\/a>"],[0,2,"<a href=\"UndoIssue.php.html#103\">App\\Actions\\Issue\\UndoIssue::authorize<\/a>"],[0,1,"<a href=\"UndoIssue.php.html#108\">App\\Actions\\Issue\\UndoIssue::restoreIssue<\/a>"],[0,2,"<a href=\"UndoIssue.php.html#118\">App\\Actions\\Issue\\UndoIssue::deleteActivityLogs<\/a>"],[0,1,"<a href=\"UpdateIssue.php.html#34\">App\\Actions\\Issue\\UpdateIssue::getSlug<\/a>"],[0,1,"<a href=\"UpdateIssue.php.html#39\">App\\Actions\\Issue\\UpdateIssue::getRoute<\/a>"],[0,5,"<a href=\"UpdateIssue.php.html#51\">App\\Actions\\Issue\\UpdateIssue::handle<\/a>"],[0,1,"<a href=\"UpdateIssue.php.html#112\">App\\Actions\\Issue\\UpdateIssue::triggerBroadCasts<\/a>"],[0,5,"<a href=\"UpdateIssue.php.html#131\">App\\Actions\\Issue\\UpdateIssue::asController<\/a>"],[0,2,"<a href=\"UpdateIssue.php.html#178\">App\\Actions\\Issue\\UpdateIssue::authorize<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

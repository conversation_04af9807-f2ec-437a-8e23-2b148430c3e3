<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Actions/ServiceRequest/CreateWorkOrderServiceRequest.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">ServiceRequest</a></li>
         <li class="breadcrumb-item active">CreateWorkOrderServiceRequest.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="2.17" aria-valuemin="0" aria-valuemax="100" style="width: 2.17%">
           <span class="sr-only">2.17% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">2.17%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;46</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="25.00" aria-valuemin="0" aria-valuemax="100" style="width: 25.00%">
           <span class="sr-only">25.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">25.00%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;4</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest">CreateWorkOrderServiceRequest</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="2.17" aria-valuemin="0" aria-valuemax="100" style="width: 2.17%">
           <span class="sr-only">2.17% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">2.17%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;46</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="25.00" aria-valuemin="0" aria-valuemax="100" style="width: 25.00%">
           <span class="sr-only">25.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">25.00%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;4</div></td>
       <td class="danger small">124.28</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#28"><abbr title="getSlug(): string">getSlug</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#33"><abbr title="handle(App\Models\ServiceRequest $serviceRequest, ?App\Models\User $user): void">handle</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;3</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#44"><abbr title="asController(Illuminate\Http\Request $request, App\Models\ServiceRequest $serviceRequest): \App\Http\Resources\ServiceRequest\CreateWorkOrderResource|\Illuminate\Http\JsonResponse">asController</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;41</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">56</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#107"><abbr title="authorize(Lorisleiva\Actions\ActionRequest $request): bool">authorize</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Actions\ServiceRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Exceptions\CouldNotPerformAction</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Exceptions\NotFoundException\UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Helpers\Helper</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Resources\ServiceRequest\CreateWorkOrderResource</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\ServiceRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\User</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\ServiceRequests\CreateWorkOrder</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Exception</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Database\Eloquent\ModelNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Http\JsonResponse</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Http\Request</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\DB</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\Response</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">InvalidArgumentException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Lorisleiva\Actions\ActionRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Lorisleiva\Actions\Concerns\AsAction</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Spatie\ModelStates\Exceptions\CouldNotPerformTransition</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Throwable</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">CreateWorkOrderServiceRequest</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">BaseAction</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">AsAction</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">static</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getSlug</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="117 tests cover line 30" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateFilterTypeTest::__pest_evaluable_DateFilterType_enum_has_correct_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateFilterTypeTest::__pest_evaluable_DateFilterType_getDescription_returns_correct_descriptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateFilterTypeTest::__pest_evaluable_DateFilterType_can_be_invoked_statically&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateOperationTest::__pest_evaluable_DateOperation_enum_has_correct_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateOperationTest::__pest_evaluable_DateOperation_getDescription_returns_correct_descriptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateOperationTest::__pest_evaluable_DateOperation_can_be_invoked_statically&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_enum_has_correct_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_getDisplayName_returns_correct_names&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_getDateRange_returns_correct_date_ranges&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_makeCollection_returns_all_slugs_except_specified_ones&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_futureDates_returns_only_future_dates&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_can_be_invoked_statically&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_can_be_instantiated_with_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_in_house_user_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_third_party_vendor_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_processes_multiple_uuids_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_for_invalid_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_non_existent_user_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_throws_exception_when_initial_status_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_continues_processing_after_individual_failures&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::minutes_to_days_simple_method_converts_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_with_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_creates_correct_frontend_url_without_query_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_passes_correct_parameters_to_onboarding_mail&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_handles_empty_uuids_array&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_implements_should_queue_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Jobs\DispatchOnboardingEmailTest::job_uses_correct_queue_traits&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::uuidConversionInApplyWorkOrderAssigneeFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::workOrderAssigneeFilterUsesCorrectExistsMethodForOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::resolveWhereClauseReturnsCorrectClauseForAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::resolveWhereClauseReturnsCorrectClauseForOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\AssigneeFilterServiceTest::applyAssigneeFilterWithUnassignedValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithSingleDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithDateBetween&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithDateNotBetween&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithInvalidOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithInvalidDateOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::dateValuesForIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::dateValuesForIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::dateValuesForIsAfterOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::dateValuesForIsBeForeOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::dateOperationResponse&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::resolveWhereClauseOperator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::getDateRangeFromSlug&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::getDateRangeFromSlugThrowsExceptionForInvalidSlug&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::applyDateFilterWithInvalidGroupOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\DateFilterServiceTest::resolveOperatorSetForDateSlug&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::register_filter_handler&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_entity_id_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::filter_query&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::process_query&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::non_existent_filter_handler_returns_null&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::get_filter_type_for_field&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_where_clause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::is_direct_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::is_filter_group&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::has_filters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_simple_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_date_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_direct_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_filter_group&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::register_default_filter_handlers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::generate_query&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::get_model_for_field&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_work_order_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_vendor_work_order_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_service_request_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_user_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::find_quote_filter_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::apply_uuid_filter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::get_filter_type_for_field_with_special_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_where_clause_operator_with_special_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_where_clause_operator_with_invalid_operation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::process_query_with_all_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_work_order_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_vendor_work_order_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_service_request_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_user_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\FilterServiceTest::resolve_quote_where_clause_operator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SearchServiceTest::testRegisterSearchHandler&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SearchServiceTest::testApplySearch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SearchServiceTest::testEmptySearchTermReturnsUnmodifiedQuery&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SearchServiceTest::testDefaultSearchHandlersAreRegistered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testRegisterSortFieldMappings&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testGetSortFieldMappings&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testGetDefaultSortField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testApplySorting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testPrepareSortValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testPrepareSortValueForWorkOrderWithGroup&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testPrepareSortValueForWorkOrderWithHealthScore&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testPrepareSortValueForWorkOrderWithHealthScoreGrouping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testApplySortingWithMultipleFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\SortServiceTest::testApplySortingWithNonExistentField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::public_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::authenticated_user_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::work_order_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::service_request_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::quote_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::view_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::vendor_onboarding_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::technician_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::notification_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::lookup_and_tag_routes_are_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::middleware_groups_are_properly_applied&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::signed_routes_have_signed_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::throttled_routes_have_throttle_middleware&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::resource_routes_have_correct_parameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::api_routes_have_correct_prefixes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::nested_resource_routes_are_properly_structured&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Feature\Routes\ApiRoutesTest::route_methods_are_correctly_defined&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'createWorkOrderServiceRequest'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handle</span><span class="keyword">(</span><span class="default">ServiceRequest</span><span class="default">&nbsp;</span><span class="default">$serviceRequest</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">User</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">transaction</span><span class="keyword">(</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$serviceRequest</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$serviceRequest</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">transitionTo</span><span class="keyword">(</span><span class="default">CreateWorkOrder</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;Exception</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;Throwable</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">asController</span><span class="keyword">(</span><span class="default">Request</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">ServiceRequest</span><span class="default">&nbsp;</span><span class="default">$serviceRequest</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">CreateWorkOrderResource</span><span class="keyword">|</span><span class="default">JsonResponse</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">beginTransaction</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Load&nbsp;the&nbsp;latest&nbsp;description</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$serviceRequest</span><span class="default">-&gt;</span><span class="default">load</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'latestDescription:service_request_description_id,service_request_descriptions.service_request_id,description'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">validateServiceRequest</span><span class="keyword">(</span><span class="default">$serviceRequest</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$serviceRequest</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">canTransitionTo</span><span class="keyword">(</span><span class="default">CreateWorkOrder</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">CouldNotPerformTransition</span><span class="keyword">(</span><span class="default">__</span><span class="keyword">(</span><span class="default">'This&nbsp;transition[create&nbsp;work&nbsp;order]&nbsp;not&nbsp;allowed&nbsp;to&nbsp;perform&nbsp;with&nbsp;the&nbsp;service&nbsp;request.'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$serviceRequest</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">ServiceRequest</span><span class="default">::</span><span class="default">select</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span><span class="default">'service_request_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'organization_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'state'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'service_request_uuid'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'service_request_status_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'state_updated_at'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'property_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'service_request_number'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'timezone_id'</span><span class="keyword">]</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">where</span><span class="keyword">(</span><span class="default">'service_request_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$serviceRequest</span><span class="default">-&gt;</span><span class="default">service_request_id</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">lockForUpdate</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handle</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$serviceRequest</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">commit</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">CreateWorkOrderResource</span><span class="keyword">(</span><span class="default">$serviceRequest</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">CouldNotPerformTransition</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollBack</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Helper</span><span class="default">::</span><span class="default">exceptionLog</span><span class="keyword">(</span><span class="default">exception</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">unprocessableEntity</span><span class="keyword">(</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">CouldNotPerformAction</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollBack</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Helper</span><span class="default">::</span><span class="default">exceptionLog</span><span class="keyword">(</span><span class="default">exception</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">unprocessableEntity</span><span class="keyword">(</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">__</span><span class="keyword">(</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">UserNotFoundException</span><span class="keyword">|</span><span class="default">ModelNotFoundException</span><span class="keyword">|</span><span class="default">InvalidArgumentException</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollBack</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Helper</span><span class="default">::</span><span class="default">exceptionLog</span><span class="keyword">(</span><span class="default">exception</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'Service&nbsp;request&nbsp;Create&nbsp;Work&nbsp;Order&nbsp;API&nbsp;failed&nbsp;due&nbsp;to&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">get_class</span><span class="keyword">(</span><span class="default">$exception</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">notFound</span><span class="keyword">(</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Exception</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollBack</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Helper</span><span class="default">::</span><span class="default">exceptionLog</span><span class="keyword">(</span><span class="default">exception</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'Service&nbsp;request&nbsp;Create&nbsp;Work&nbsp;Order&nbsp;API&nbsp;failed[Exception]'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">additionalInfo</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'sserviceRequestId'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$serviceRequest</span><span class="default">-&gt;</span><span class="default">service_request_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">notify</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">internalServerError</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">authorize</span><span class="keyword">(</span><span class="default">ActionRequest</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;TODO:&nbsp;create&nbsp;a&nbsp;new&nbsp;permission&nbsp;for&nbsp;this&nbsp;action</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">can</span><span class="keyword">(</span><span class="default">'createWorkOrder'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">route</span><span class="keyword">(</span><span class="default">'serviceRequest'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:22:08 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

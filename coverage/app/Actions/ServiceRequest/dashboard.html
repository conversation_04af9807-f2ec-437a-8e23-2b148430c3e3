<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Actions/ServiceRequest</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">ServiceRequest</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AddResidentAvailability.php.html#31">App\Actions\ServiceRequest\AddResidentAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AwaitingAvailabilityServiceRequest.php.html#25">App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#11">App\Actions\ServiceRequest\BaseAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClosedServiceRequest.php.html#24">App\Actions\ServiceRequest\ClosedServiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateWorkOrderServiceRequest.php.html#24">App\Actions\ServiceRequest\CreateWorkOrderServiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InProgressServiceRequest.php.html#24">App\Actions\ServiceRequest\InProgressServiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestResidentAvailability.php.html#25">App\Actions\ServiceRequest\RequestResidentAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScopingServiceRequest.php.html#11">App\Actions\ServiceRequest\ScopingServiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdatePriority.php.html#18">App\Actions\ServiceRequest\UpdatePriority</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdatePropertyAddress.php.html#21">App\Actions\ServiceRequest\UpdatePropertyAddress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateResident.php.html#21">App\Actions\ServiceRequest\UpdateResident</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateResidentAvailability.php.html#30">App\Actions\ServiceRequest\UpdateResidentAvailability</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="UpdateResidentAvailability.php.html#30">App\Actions\ServiceRequest\UpdateResidentAvailability</a></td><td class="text-right">870</td></tr>
       <tr><td><a href="AddResidentAvailability.php.html#31">App\Actions\ServiceRequest\AddResidentAvailability</a></td><td class="text-right">462</td></tr>
       <tr><td><a href="RequestResidentAvailability.php.html#25">App\Actions\ServiceRequest\RequestResidentAvailability</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="UpdateResident.php.html#21">App\Actions\ServiceRequest\UpdateResident</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="AwaitingAvailabilityServiceRequest.php.html#25">App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ClosedServiceRequest.php.html#24">App\Actions\ServiceRequest\ClosedServiceRequest</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="CreateWorkOrderServiceRequest.php.html#24">App\Actions\ServiceRequest\CreateWorkOrderServiceRequest</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="InProgressServiceRequest.php.html#24">App\Actions\ServiceRequest\InProgressServiceRequest</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="UpdatePriority.php.html#18">App\Actions\ServiceRequest\UpdatePriority</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="UpdatePropertyAddress.php.html#21">App\Actions\ServiceRequest\UpdatePropertyAddress</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="BaseAction.php.html#11">App\Actions\ServiceRequest\BaseAction</a></td><td class="text-right">72</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AddResidentAvailability.php.html#35"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddResidentAvailability.php.html#43"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddResidentAvailability.php.html#107"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddResidentAvailability.php.html#162"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AwaitingAvailabilityServiceRequest.php.html#29"><abbr title="App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AwaitingAvailabilityServiceRequest.php.html#34"><abbr title="App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AwaitingAvailabilityServiceRequest.php.html#50"><abbr title="App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AwaitingAvailabilityServiceRequest.php.html#113"><abbr title="App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#15"><abbr title="App\Actions\ServiceRequest\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#20"><abbr title="App\Actions\ServiceRequest\BaseAction::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#25"><abbr title="App\Actions\ServiceRequest\BaseAction::routes">routes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#34"><abbr title="App\Actions\ServiceRequest\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#42"><abbr title="App\Actions\ServiceRequest\BaseAction::validateServiceRequest">validateServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClosedServiceRequest.php.html#28"><abbr title="App\Actions\ServiceRequest\ClosedServiceRequest::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClosedServiceRequest.php.html#33"><abbr title="App\Actions\ServiceRequest\ClosedServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClosedServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\ClosedServiceRequest::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClosedServiceRequest.php.html#107"><abbr title="App\Actions\ServiceRequest\ClosedServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateWorkOrderServiceRequest.php.html#28"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateWorkOrderServiceRequest.php.html#33"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateWorkOrderServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateWorkOrderServiceRequest.php.html#107"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InProgressServiceRequest.php.html#28"><abbr title="App\Actions\ServiceRequest\InProgressServiceRequest::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InProgressServiceRequest.php.html#33"><abbr title="App\Actions\ServiceRequest\InProgressServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InProgressServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\InProgressServiceRequest::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InProgressServiceRequest.php.html#112"><abbr title="App\Actions\ServiceRequest\InProgressServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestResidentAvailability.php.html#29"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestResidentAvailability.php.html#34"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestResidentAvailability.php.html#59"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestResidentAvailability.php.html#115"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScopingServiceRequest.php.html#15"><abbr title="App\Actions\ServiceRequest\ScopingServiceRequest::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ScopingServiceRequest.php.html#20"><abbr title="App\Actions\ServiceRequest\ScopingServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdatePriority.php.html#27"><abbr title="App\Actions\ServiceRequest\UpdatePriority::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdatePriority.php.html#32"><abbr title="App\Actions\ServiceRequest\UpdatePriority::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdatePriority.php.html#42"><abbr title="App\Actions\ServiceRequest\UpdatePriority::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdatePriority.php.html#74"><abbr title="App\Actions\ServiceRequest\UpdatePriority::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdatePropertyAddress.php.html#30"><abbr title="App\Actions\ServiceRequest\UpdatePropertyAddress::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdatePropertyAddress.php.html#43"><abbr title="App\Actions\ServiceRequest\UpdatePropertyAddress::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdatePropertyAddress.php.html#68"><abbr title="App\Actions\ServiceRequest\UpdatePropertyAddress::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdatePropertyAddress.php.html#115"><abbr title="App\Actions\ServiceRequest\UpdatePropertyAddress::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateResident.php.html#30"><abbr title="App\Actions\ServiceRequest\UpdateResident::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateResident.php.html#42"><abbr title="App\Actions\ServiceRequest\UpdateResident::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateResident.php.html#83"><abbr title="App\Actions\ServiceRequest\UpdateResident::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateResident.php.html#134"><abbr title="App\Actions\ServiceRequest\UpdateResident::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateResidentAvailability.php.html#36"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateResidentAvailability.php.html#44"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateResidentAvailability.php.html#176"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateResidentAvailability.php.html#219"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="UpdateResidentAvailability.php.html#44"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::handle">handle</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="AddResidentAvailability.php.html#43"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AddResidentAvailability.php.html#107"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="RequestResidentAvailability.php.html#59"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="AwaitingAvailabilityServiceRequest.php.html#50"><abbr title="App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ClosedServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\ClosedServiceRequest::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="CreateWorkOrderServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="InProgressServiceRequest.php.html#44"><abbr title="App\Actions\ServiceRequest\InProgressServiceRequest::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="UpdateResidentAvailability.php.html#176"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="UpdatePriority.php.html#42"><abbr title="App\Actions\ServiceRequest\UpdatePriority::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UpdatePropertyAddress.php.html#68"><abbr title="App\Actions\ServiceRequest\UpdatePropertyAddress::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UpdateResident.php.html#83"><abbr title="App\Actions\ServiceRequest\UpdateResident::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="BaseAction.php.html#42"><abbr title="App\Actions\ServiceRequest\BaseAction::validateServiceRequest">validateServiceRequest</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UpdateResident.php.html#42"><abbr title="App\Actions\ServiceRequest\UpdateResident::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AddResidentAvailability.php.html#162"><abbr title="App\Actions\ServiceRequest\AddResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AwaitingAvailabilityServiceRequest.php.html#113"><abbr title="App\Actions\ServiceRequest\AwaitingAvailabilityServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ClosedServiceRequest.php.html#107"><abbr title="App\Actions\ServiceRequest\ClosedServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CreateWorkOrderServiceRequest.php.html#107"><abbr title="App\Actions\ServiceRequest\CreateWorkOrderServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InProgressServiceRequest.php.html#112"><abbr title="App\Actions\ServiceRequest\InProgressServiceRequest::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RequestResidentAvailability.php.html#34"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RequestResidentAvailability.php.html#115"><abbr title="App\Actions\ServiceRequest\RequestResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdatePriority.php.html#74"><abbr title="App\Actions\ServiceRequest\UpdatePriority::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdatePropertyAddress.php.html#115"><abbr title="App\Actions\ServiceRequest\UpdatePropertyAddress::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateResident.php.html#134"><abbr title="App\Actions\ServiceRequest\UpdateResident::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateResidentAvailability.php.html#219"><abbr title="App\Actions\ServiceRequest\UpdateResidentAvailability::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([12,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([47,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,21,"<a href=\"AddResidentAvailability.php.html#31\">App\\Actions\\ServiceRequest\\AddResidentAvailability<\/a>"],[0,11,"<a href=\"AwaitingAvailabilityServiceRequest.php.html#25\">App\\Actions\\ServiceRequest\\AwaitingAvailabilityServiceRequest<\/a>"],[0,8,"<a href=\"BaseAction.php.html#11\">App\\Actions\\ServiceRequest\\BaseAction<\/a>"],[0,11,"<a href=\"ClosedServiceRequest.php.html#24\">App\\Actions\\ServiceRequest\\ClosedServiceRequest<\/a>"],[0,11,"<a href=\"CreateWorkOrderServiceRequest.php.html#24\">App\\Actions\\ServiceRequest\\CreateWorkOrderServiceRequest<\/a>"],[0,11,"<a href=\"InProgressServiceRequest.php.html#24\">App\\Actions\\ServiceRequest\\InProgressServiceRequest<\/a>"],[0,13,"<a href=\"RequestResidentAvailability.php.html#25\">App\\Actions\\ServiceRequest\\RequestResidentAvailability<\/a>"],[0,2,"<a href=\"ScopingServiceRequest.php.html#11\">App\\Actions\\ServiceRequest\\ScopingServiceRequest<\/a>"],[0,9,"<a href=\"UpdatePriority.php.html#18\">App\\Actions\\ServiceRequest\\UpdatePriority<\/a>"],[0,9,"<a href=\"UpdatePropertyAddress.php.html#21\">App\\Actions\\ServiceRequest\\UpdatePropertyAddress<\/a>"],[0,12,"<a href=\"UpdateResident.php.html#21\">App\\Actions\\ServiceRequest\\UpdateResident<\/a>"],[0,29,"<a href=\"UpdateResidentAvailability.php.html#30\">App\\Actions\\ServiceRequest\\UpdateResidentAvailability<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AddResidentAvailability.php.html#35\">App\\Actions\\ServiceRequest\\AddResidentAvailability::getSlug<\/a>"],[0,10,"<a href=\"AddResidentAvailability.php.html#43\">App\\Actions\\ServiceRequest\\AddResidentAvailability::handle<\/a>"],[0,8,"<a href=\"AddResidentAvailability.php.html#107\">App\\Actions\\ServiceRequest\\AddResidentAvailability::asController<\/a>"],[0,2,"<a href=\"AddResidentAvailability.php.html#162\">App\\Actions\\ServiceRequest\\AddResidentAvailability::authorize<\/a>"],[0,1,"<a href=\"AwaitingAvailabilityServiceRequest.php.html#29\">App\\Actions\\ServiceRequest\\AwaitingAvailabilityServiceRequest::getSlug<\/a>"],[0,1,"<a href=\"AwaitingAvailabilityServiceRequest.php.html#34\">App\\Actions\\ServiceRequest\\AwaitingAvailabilityServiceRequest::handle<\/a>"],[0,7,"<a href=\"AwaitingAvailabilityServiceRequest.php.html#50\">App\\Actions\\ServiceRequest\\AwaitingAvailabilityServiceRequest::asController<\/a>"],[0,2,"<a href=\"AwaitingAvailabilityServiceRequest.php.html#113\">App\\Actions\\ServiceRequest\\AwaitingAvailabilityServiceRequest::authorize<\/a>"],[0,1,"<a href=\"BaseAction.php.html#15\">App\\Actions\\ServiceRequest\\BaseAction::getSlug<\/a>"],[0,1,"<a href=\"BaseAction.php.html#20\">App\\Actions\\ServiceRequest\\BaseAction::getRoute<\/a>"],[0,1,"<a href=\"BaseAction.php.html#25\">App\\Actions\\ServiceRequest\\BaseAction::routes<\/a>"],[0,1,"<a href=\"BaseAction.php.html#34\">App\\Actions\\ServiceRequest\\BaseAction::authorize<\/a>"],[0,4,"<a href=\"BaseAction.php.html#42\">App\\Actions\\ServiceRequest\\BaseAction::validateServiceRequest<\/a>"],[0,1,"<a href=\"ClosedServiceRequest.php.html#28\">App\\Actions\\ServiceRequest\\ClosedServiceRequest::getSlug<\/a>"],[0,1,"<a href=\"ClosedServiceRequest.php.html#33\">App\\Actions\\ServiceRequest\\ClosedServiceRequest::handle<\/a>"],[0,7,"<a href=\"ClosedServiceRequest.php.html#44\">App\\Actions\\ServiceRequest\\ClosedServiceRequest::asController<\/a>"],[0,2,"<a href=\"ClosedServiceRequest.php.html#107\">App\\Actions\\ServiceRequest\\ClosedServiceRequest::authorize<\/a>"],[0,1,"<a href=\"CreateWorkOrderServiceRequest.php.html#28\">App\\Actions\\ServiceRequest\\CreateWorkOrderServiceRequest::getSlug<\/a>"],[0,1,"<a href=\"CreateWorkOrderServiceRequest.php.html#33\">App\\Actions\\ServiceRequest\\CreateWorkOrderServiceRequest::handle<\/a>"],[0,7,"<a href=\"CreateWorkOrderServiceRequest.php.html#44\">App\\Actions\\ServiceRequest\\CreateWorkOrderServiceRequest::asController<\/a>"],[0,2,"<a href=\"CreateWorkOrderServiceRequest.php.html#107\">App\\Actions\\ServiceRequest\\CreateWorkOrderServiceRequest::authorize<\/a>"],[0,1,"<a href=\"InProgressServiceRequest.php.html#28\">App\\Actions\\ServiceRequest\\InProgressServiceRequest::getSlug<\/a>"],[0,1,"<a href=\"InProgressServiceRequest.php.html#33\">App\\Actions\\ServiceRequest\\InProgressServiceRequest::handle<\/a>"],[0,7,"<a href=\"InProgressServiceRequest.php.html#44\">App\\Actions\\ServiceRequest\\InProgressServiceRequest::asController<\/a>"],[0,2,"<a href=\"InProgressServiceRequest.php.html#112\">App\\Actions\\ServiceRequest\\InProgressServiceRequest::authorize<\/a>"],[0,1,"<a href=\"RequestResidentAvailability.php.html#29\">App\\Actions\\ServiceRequest\\RequestResidentAvailability::getSlug<\/a>"],[0,2,"<a href=\"RequestResidentAvailability.php.html#34\">App\\Actions\\ServiceRequest\\RequestResidentAvailability::handle<\/a>"],[0,8,"<a href=\"RequestResidentAvailability.php.html#59\">App\\Actions\\ServiceRequest\\RequestResidentAvailability::asController<\/a>"],[0,2,"<a href=\"RequestResidentAvailability.php.html#115\">App\\Actions\\ServiceRequest\\RequestResidentAvailability::authorize<\/a>"],[0,1,"<a href=\"ScopingServiceRequest.php.html#15\">App\\Actions\\ServiceRequest\\ScopingServiceRequest::getSlug<\/a>"],[0,1,"<a href=\"ScopingServiceRequest.php.html#20\">App\\Actions\\ServiceRequest\\ScopingServiceRequest::handle<\/a>"],[0,1,"<a href=\"UpdatePriority.php.html#27\">App\\Actions\\ServiceRequest\\UpdatePriority::getSlug<\/a>"],[0,1,"<a href=\"UpdatePriority.php.html#32\">App\\Actions\\ServiceRequest\\UpdatePriority::handle<\/a>"],[0,5,"<a href=\"UpdatePriority.php.html#42\">App\\Actions\\ServiceRequest\\UpdatePriority::asController<\/a>"],[0,2,"<a href=\"UpdatePriority.php.html#74\">App\\Actions\\ServiceRequest\\UpdatePriority::authorize<\/a>"],[0,1,"<a href=\"UpdatePropertyAddress.php.html#30\">App\\Actions\\ServiceRequest\\UpdatePropertyAddress::getSlug<\/a>"],[0,1,"<a href=\"UpdatePropertyAddress.php.html#43\">App\\Actions\\ServiceRequest\\UpdatePropertyAddress::handle<\/a>"],[0,5,"<a href=\"UpdatePropertyAddress.php.html#68\">App\\Actions\\ServiceRequest\\UpdatePropertyAddress::asController<\/a>"],[0,2,"<a href=\"UpdatePropertyAddress.php.html#115\">App\\Actions\\ServiceRequest\\UpdatePropertyAddress::authorize<\/a>"],[0,1,"<a href=\"UpdateResident.php.html#30\">App\\Actions\\ServiceRequest\\UpdateResident::getSlug<\/a>"],[0,4,"<a href=\"UpdateResident.php.html#42\">App\\Actions\\ServiceRequest\\UpdateResident::handle<\/a>"],[0,5,"<a href=\"UpdateResident.php.html#83\">App\\Actions\\ServiceRequest\\UpdateResident::asController<\/a>"],[0,2,"<a href=\"UpdateResident.php.html#134\">App\\Actions\\ServiceRequest\\UpdateResident::authorize<\/a>"],[0,1,"<a href=\"UpdateResidentAvailability.php.html#36\">App\\Actions\\ServiceRequest\\UpdateResidentAvailability::getSlug<\/a>"],[0,20,"<a href=\"UpdateResidentAvailability.php.html#44\">App\\Actions\\ServiceRequest\\UpdateResidentAvailability::handle<\/a>"],[0,6,"<a href=\"UpdateResidentAvailability.php.html#176\">App\\Actions\\ServiceRequest\\UpdateResidentAvailability::asController<\/a>"],[0,2,"<a href=\"UpdateResidentAvailability.php.html#219\">App\\Actions\\ServiceRequest\\UpdateResidentAvailability::authorize<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Actions/WorkOrderIssue</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">WorkOrderIssue</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AssignIssue.php.html#26">App\Actions\WorkOrderIssue\AssignIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#10">App\Actions\WorkOrderIssue\BaseAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#29">App\Actions\WorkOrderIssue\CreateIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeclineIssue.php.html#36">App\Actions\WorkOrderIssue\DeclineIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#34">App\Actions\WorkOrderIssue\MarkAsDoneIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsPendingIssue.php.html#30">App\Actions\WorkOrderIssue\MarkAsPendingIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#26">App\Actions\WorkOrderIssue\UnassignIssue</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateDeclineReason.php.html#20">App\Actions\WorkOrderIssue\UpdateDeclineReason</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssueBasicDetails.php.html#22">App\Actions\WorkOrderIssue\UpdateIssueBasicDetails</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#30">App\Actions\WorkOrderIssue\UpdateWorkOrderIssue</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#30">App\Actions\WorkOrderIssue\UpdateWorkOrderIssue</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#34">App\Actions\WorkOrderIssue\MarkAsDoneIssue</a></td><td class="text-right">930</td></tr>
       <tr><td><a href="DeclineIssue.php.html#36">App\Actions\WorkOrderIssue\DeclineIssue</a></td><td class="text-right">462</td></tr>
       <tr><td><a href="MarkAsPendingIssue.php.html#30">App\Actions\WorkOrderIssue\MarkAsPendingIssue</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="AssignIssue.php.html#26">App\Actions\WorkOrderIssue\AssignIssue</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="CreateIssue.php.html#29">App\Actions\WorkOrderIssue\CreateIssue</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="UnassignIssue.php.html#26">App\Actions\WorkOrderIssue\UnassignIssue</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="UpdateDeclineReason.php.html#20">App\Actions\WorkOrderIssue\UpdateDeclineReason</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="UpdateIssueBasicDetails.php.html#22">App\Actions\WorkOrderIssue\UpdateIssueBasicDetails</a></td><td class="text-right">90</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AssignIssue.php.html#30"><abbr title="App\Actions\WorkOrderIssue\AssignIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssignIssue.php.html#35"><abbr title="App\Actions\WorkOrderIssue\AssignIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssignIssue.php.html#43"><abbr title="App\Actions\WorkOrderIssue\AssignIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AssignIssue.php.html#105"><abbr title="App\Actions\WorkOrderIssue\AssignIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#14"><abbr title="App\Actions\WorkOrderIssue\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#19"><abbr title="App\Actions\WorkOrderIssue\BaseAction::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#24"><abbr title="App\Actions\WorkOrderIssue\BaseAction::routes">routes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#33"><abbr title="App\Actions\WorkOrderIssue\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#33"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#38"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#48"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#64"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateIssue.php.html#123"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeclineIssue.php.html#40"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeclineIssue.php.html#48"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeclineIssue.php.html#70"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::changeIssueState">changeIssueState</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeclineIssue.php.html#87"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::assignedIssueExists">assignedIssueExists</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeclineIssue.php.html#95"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeclineIssue.php.html#165"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#40"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#48"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#101"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#192"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#202"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::handleIssueMedia">handleIssueMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#225"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::cancelIssuesAndRelatedWorkOrders">cancelIssuesAndRelatedWorkOrders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#274"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsPendingIssue.php.html#36"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsPendingIssue.php.html#44"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsPendingIssue.php.html#78"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsPendingIssue.php.html#110"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarkAsPendingIssue.php.html#115"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::clearDoneStateResources">clearDoneStateResources</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#30"><abbr title="App\Actions\WorkOrderIssue\UnassignIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#35"><abbr title="App\Actions\WorkOrderIssue\UnassignIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#43"><abbr title="App\Actions\WorkOrderIssue\UnassignIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignIssue.php.html#100"><abbr title="App\Actions\WorkOrderIssue\UnassignIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateDeclineReason.php.html#26"><abbr title="App\Actions\WorkOrderIssue\UpdateDeclineReason::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateDeclineReason.php.html#34"><abbr title="App\Actions\WorkOrderIssue\UpdateDeclineReason::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateDeclineReason.php.html#46"><abbr title="App\Actions\WorkOrderIssue\UpdateDeclineReason::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateDeclineReason.php.html#76"><abbr title="App\Actions\WorkOrderIssue\UpdateDeclineReason::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssueBasicDetails.php.html#28"><abbr title="App\Actions\WorkOrderIssue\UpdateIssueBasicDetails::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssueBasicDetails.php.html#40"><abbr title="App\Actions\WorkOrderIssue\UpdateIssueBasicDetails::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssueBasicDetails.php.html#48"><abbr title="App\Actions\WorkOrderIssue\UpdateIssueBasicDetails::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateIssueBasicDetails.php.html#103"><abbr title="App\Actions\WorkOrderIssue\UpdateIssueBasicDetails::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#36"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#44"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#66"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#148"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#158"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::updateMaterials">updateMaterials</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#208"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::authorizeTechnicianAccess">authorizeTechnicianAccess</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#227"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#261"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::handleIssueMedia">handleIssueMedia</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DeclineIssue.php.html#95"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::asController">asController</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#101"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::asController">asController</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#66"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::asController">asController</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="AssignIssue.php.html#43"><abbr title="App\Actions\WorkOrderIssue\AssignIssue::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#158"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::updateMaterials">updateMaterials</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CreateIssue.php.html#64"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="UnassignIssue.php.html#43"><abbr title="App\Actions\WorkOrderIssue\UnassignIssue::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#225"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::cancelIssuesAndRelatedWorkOrders">cancelIssuesAndRelatedWorkOrders</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="MarkAsPendingIssue.php.html#78"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="UpdateDeclineReason.php.html#46"><abbr title="App\Actions\WorkOrderIssue\UpdateDeclineReason::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UpdateIssueBasicDetails.php.html#48"><abbr title="App\Actions\WorkOrderIssue\UpdateIssueBasicDetails::asController">asController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#48"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="MarkAsPendingIssue.php.html#44"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="MarkAsPendingIssue.php.html#115"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::clearDoneStateResources">clearDoneStateResources</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#208"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::authorizeTechnicianAccess">authorizeTechnicianAccess</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#261"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::handleIssueMedia">handleIssueMedia</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DeclineIssue.php.html#70"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::changeIssueState">changeIssueState</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#274"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#227"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::validateMediaLimit">validateMediaLimit</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AssignIssue.php.html#105"><abbr title="App\Actions\WorkOrderIssue\AssignIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CreateIssue.php.html#123"><abbr title="App\Actions\WorkOrderIssue\CreateIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DeclineIssue.php.html#48"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DeclineIssue.php.html#165"><abbr title="App\Actions\WorkOrderIssue\DeclineIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#192"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="MarkAsDoneIssue.php.html#202"><abbr title="App\Actions\WorkOrderIssue\MarkAsDoneIssue::handleIssueMedia">handleIssueMedia</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="MarkAsPendingIssue.php.html#110"><abbr title="App\Actions\WorkOrderIssue\MarkAsPendingIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UnassignIssue.php.html#100"><abbr title="App\Actions\WorkOrderIssue\UnassignIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateDeclineReason.php.html#76"><abbr title="App\Actions\WorkOrderIssue\UpdateDeclineReason::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateIssueBasicDetails.php.html#103"><abbr title="App\Actions\WorkOrderIssue\UpdateIssueBasicDetails::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#44"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateWorkOrderIssue.php.html#148"><abbr title="App\Actions\WorkOrderIssue\UpdateWorkOrderIssue::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([10,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([51,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,12,"<a href=\"AssignIssue.php.html#26\">App\\Actions\\WorkOrderIssue\\AssignIssue<\/a>"],[0,4,"<a href=\"BaseAction.php.html#10\">App\\Actions\\WorkOrderIssue\\BaseAction<\/a>"],[0,12,"<a href=\"CreateIssue.php.html#29\">App\\Actions\\WorkOrderIssue\\CreateIssue<\/a>"],[0,21,"<a href=\"DeclineIssue.php.html#36\">App\\Actions\\WorkOrderIssue\\DeclineIssue<\/a>"],[0,30,"<a href=\"MarkAsDoneIssue.php.html#34\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue<\/a>"],[0,17,"<a href=\"MarkAsPendingIssue.php.html#30\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue<\/a>"],[0,11,"<a href=\"UnassignIssue.php.html#26\">App\\Actions\\WorkOrderIssue\\UnassignIssue<\/a>"],[0,9,"<a href=\"UpdateDeclineReason.php.html#20\">App\\Actions\\WorkOrderIssue\\UpdateDeclineReason<\/a>"],[0,9,"<a href=\"UpdateIssueBasicDetails.php.html#22\">App\\Actions\\WorkOrderIssue\\UpdateIssueBasicDetails<\/a>"],[0,33,"<a href=\"UpdateWorkOrderIssue.php.html#30\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AssignIssue.php.html#30\">App\\Actions\\WorkOrderIssue\\AssignIssue::getSlug<\/a>"],[0,1,"<a href=\"AssignIssue.php.html#35\">App\\Actions\\WorkOrderIssue\\AssignIssue::handle<\/a>"],[0,8,"<a href=\"AssignIssue.php.html#43\">App\\Actions\\WorkOrderIssue\\AssignIssue::asController<\/a>"],[0,2,"<a href=\"AssignIssue.php.html#105\">App\\Actions\\WorkOrderIssue\\AssignIssue::authorize<\/a>"],[0,1,"<a href=\"BaseAction.php.html#14\">App\\Actions\\WorkOrderIssue\\BaseAction::getSlug<\/a>"],[0,1,"<a href=\"BaseAction.php.html#19\">App\\Actions\\WorkOrderIssue\\BaseAction::getRoute<\/a>"],[0,1,"<a href=\"BaseAction.php.html#24\">App\\Actions\\WorkOrderIssue\\BaseAction::routes<\/a>"],[0,1,"<a href=\"BaseAction.php.html#33\">App\\Actions\\WorkOrderIssue\\BaseAction::authorize<\/a>"],[0,1,"<a href=\"CreateIssue.php.html#33\">App\\Actions\\WorkOrderIssue\\CreateIssue::getRoute<\/a>"],[0,1,"<a href=\"CreateIssue.php.html#38\">App\\Actions\\WorkOrderIssue\\CreateIssue::getSlug<\/a>"],[0,1,"<a href=\"CreateIssue.php.html#48\">App\\Actions\\WorkOrderIssue\\CreateIssue::handle<\/a>"],[0,7,"<a href=\"CreateIssue.php.html#64\">App\\Actions\\WorkOrderIssue\\CreateIssue::asController<\/a>"],[0,2,"<a href=\"CreateIssue.php.html#123\">App\\Actions\\WorkOrderIssue\\CreateIssue::authorize<\/a>"],[0,1,"<a href=\"DeclineIssue.php.html#40\">App\\Actions\\WorkOrderIssue\\DeclineIssue::getSlug<\/a>"],[0,2,"<a href=\"DeclineIssue.php.html#48\">App\\Actions\\WorkOrderIssue\\DeclineIssue::handle<\/a>"],[0,3,"<a href=\"DeclineIssue.php.html#70\">App\\Actions\\WorkOrderIssue\\DeclineIssue::changeIssueState<\/a>"],[0,1,"<a href=\"DeclineIssue.php.html#87\">App\\Actions\\WorkOrderIssue\\DeclineIssue::assignedIssueExists<\/a>"],[0,12,"<a href=\"DeclineIssue.php.html#95\">App\\Actions\\WorkOrderIssue\\DeclineIssue::asController<\/a>"],[0,2,"<a href=\"DeclineIssue.php.html#165\">App\\Actions\\WorkOrderIssue\\DeclineIssue::authorize<\/a>"],[0,1,"<a href=\"MarkAsDoneIssue.php.html#40\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::getSlug<\/a>"],[0,4,"<a href=\"MarkAsDoneIssue.php.html#48\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::handle<\/a>"],[0,12,"<a href=\"MarkAsDoneIssue.php.html#101\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::asController<\/a>"],[0,2,"<a href=\"MarkAsDoneIssue.php.html#192\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::authorize<\/a>"],[0,2,"<a href=\"MarkAsDoneIssue.php.html#202\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::handleIssueMedia<\/a>"],[0,6,"<a href=\"MarkAsDoneIssue.php.html#225\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::cancelIssuesAndRelatedWorkOrders<\/a>"],[0,3,"<a href=\"MarkAsDoneIssue.php.html#274\">App\\Actions\\WorkOrderIssue\\MarkAsDoneIssue::validateMediaLimit<\/a>"],[0,1,"<a href=\"MarkAsPendingIssue.php.html#36\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue::getSlug<\/a>"],[0,4,"<a href=\"MarkAsPendingIssue.php.html#44\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue::handle<\/a>"],[0,6,"<a href=\"MarkAsPendingIssue.php.html#78\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue::asController<\/a>"],[0,2,"<a href=\"MarkAsPendingIssue.php.html#110\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue::authorize<\/a>"],[0,4,"<a href=\"MarkAsPendingIssue.php.html#115\">App\\Actions\\WorkOrderIssue\\MarkAsPendingIssue::clearDoneStateResources<\/a>"],[0,1,"<a href=\"UnassignIssue.php.html#30\">App\\Actions\\WorkOrderIssue\\UnassignIssue::getSlug<\/a>"],[0,1,"<a href=\"UnassignIssue.php.html#35\">App\\Actions\\WorkOrderIssue\\UnassignIssue::handle<\/a>"],[0,7,"<a href=\"UnassignIssue.php.html#43\">App\\Actions\\WorkOrderIssue\\UnassignIssue::asController<\/a>"],[0,2,"<a href=\"UnassignIssue.php.html#100\">App\\Actions\\WorkOrderIssue\\UnassignIssue::authorize<\/a>"],[0,1,"<a href=\"UpdateDeclineReason.php.html#26\">App\\Actions\\WorkOrderIssue\\UpdateDeclineReason::getSlug<\/a>"],[0,1,"<a href=\"UpdateDeclineReason.php.html#34\">App\\Actions\\WorkOrderIssue\\UpdateDeclineReason::handle<\/a>"],[0,5,"<a href=\"UpdateDeclineReason.php.html#46\">App\\Actions\\WorkOrderIssue\\UpdateDeclineReason::asController<\/a>"],[0,2,"<a href=\"UpdateDeclineReason.php.html#76\">App\\Actions\\WorkOrderIssue\\UpdateDeclineReason::authorize<\/a>"],[0,1,"<a href=\"UpdateIssueBasicDetails.php.html#28\">App\\Actions\\WorkOrderIssue\\UpdateIssueBasicDetails::getSlug<\/a>"],[0,1,"<a href=\"UpdateIssueBasicDetails.php.html#40\">App\\Actions\\WorkOrderIssue\\UpdateIssueBasicDetails::handle<\/a>"],[0,5,"<a href=\"UpdateIssueBasicDetails.php.html#48\">App\\Actions\\WorkOrderIssue\\UpdateIssueBasicDetails::asController<\/a>"],[0,2,"<a href=\"UpdateIssueBasicDetails.php.html#103\">App\\Actions\\WorkOrderIssue\\UpdateIssueBasicDetails::authorize<\/a>"],[0,1,"<a href=\"UpdateWorkOrderIssue.php.html#36\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::getSlug<\/a>"],[0,2,"<a href=\"UpdateWorkOrderIssue.php.html#44\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::handle<\/a>"],[0,9,"<a href=\"UpdateWorkOrderIssue.php.html#66\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::asController<\/a>"],[0,2,"<a href=\"UpdateWorkOrderIssue.php.html#148\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::authorize<\/a>"],[0,8,"<a href=\"UpdateWorkOrderIssue.php.html#158\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::updateMaterials<\/a>"],[0,4,"<a href=\"UpdateWorkOrderIssue.php.html#208\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::authorizeTechnicianAccess<\/a>"],[0,3,"<a href=\"UpdateWorkOrderIssue.php.html#227\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::validateMediaLimit<\/a>"],[0,4,"<a href=\"UpdateWorkOrderIssue.php.html#261\">App\\Actions\\WorkOrderIssue\\UpdateWorkOrderIssue::handleIssueMedia<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/app/Actions/Quotes</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">Quotes</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ApproveQuote.php.html#30">App\Actions\Quotes\ApproveQuote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#14">App\Actions\Quotes\BaseAction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuote.php.html#42">App\Actions\Quotes\CreateQuote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuoteTask.php.html#35">App\Actions\Quotes\CreateQuoteTask</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteQuoteTask.php.html#28">App\Actions\Quotes\DeleteQuoteTask</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RejectQuote.php.html#29">App\Actions\Quotes\RejectQuote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RestoreQuote.php.html#21">App\Actions\Quotes\RestoreQuote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SubmitForApproval.php.html#30">App\Actions\Quotes\SubmitForApproval</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateQuote.php.html#23">App\Actions\Quotes\UpdateQuote</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateQuoteTask.php.html#37">App\Actions\Quotes\UpdateQuoteTask</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CreateQuote.php.html#42">App\Actions\Quotes\CreateQuote</a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="UpdateQuote.php.html#23">App\Actions\Quotes\UpdateQuote</a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="ApproveQuote.php.html#30">App\Actions\Quotes\ApproveQuote</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="UpdateQuoteTask.php.html#37">App\Actions\Quotes\UpdateQuoteTask</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="CreateQuoteTask.php.html#35">App\Actions\Quotes\CreateQuoteTask</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="RejectQuote.php.html#29">App\Actions\Quotes\RejectQuote</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="DeleteQuoteTask.php.html#28">App\Actions\Quotes\DeleteQuoteTask</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="BaseAction.php.html#14">App\Actions\Quotes\BaseAction</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="SubmitForApproval.php.html#30">App\Actions\Quotes\SubmitForApproval</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="RestoreQuote.php.html#21">App\Actions\Quotes\RestoreQuote</a></td><td class="text-right">132</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ApproveQuote.php.html#34"><abbr title="App\Actions\Quotes\ApproveQuote::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApproveQuote.php.html#39"><abbr title="App\Actions\Quotes\ApproveQuote::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApproveQuote.php.html#48"><abbr title="App\Actions\Quotes\ApproveQuote::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApproveQuote.php.html#131"><abbr title="App\Actions\Quotes\ApproveQuote::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApproveQuote.php.html#219"><abbr title="App\Actions\Quotes\ApproveQuote::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#18"><abbr title="App\Actions\Quotes\BaseAction::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#23"><abbr title="App\Actions\Quotes\BaseAction::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#28"><abbr title="App\Actions\Quotes\BaseAction::routes">routes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#38"><abbr title="App\Actions\Quotes\BaseAction::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseAction.php.html#46"><abbr title="App\Actions\Quotes\BaseAction::validateRouteParameterRelation">validateRouteParameterRelation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuote.php.html#46"><abbr title="App\Actions\Quotes\CreateQuote::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuote.php.html#51"><abbr title="App\Actions\Quotes\CreateQuote::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuote.php.html#59"><abbr title="App\Actions\Quotes\CreateQuote::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuote.php.html#197"><abbr title="App\Actions\Quotes\CreateQuote::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuote.php.html#290"><abbr title="App\Actions\Quotes\CreateQuote::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuoteTask.php.html#39"><abbr title="App\Actions\Quotes\CreateQuoteTask::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuoteTask.php.html#44"><abbr title="App\Actions\Quotes\CreateQuoteTask::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuoteTask.php.html#52"><abbr title="App\Actions\Quotes\CreateQuoteTask::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuoteTask.php.html#149"><abbr title="App\Actions\Quotes\CreateQuoteTask::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CreateQuoteTask.php.html#210"><abbr title="App\Actions\Quotes\CreateQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteQuoteTask.php.html#34"><abbr title="App\Actions\Quotes\DeleteQuoteTask::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteQuoteTask.php.html#39"><abbr title="App\Actions\Quotes\DeleteQuoteTask::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteQuoteTask.php.html#75"><abbr title="App\Actions\Quotes\DeleteQuoteTask::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeleteQuoteTask.php.html#130"><abbr title="App\Actions\Quotes\DeleteQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RejectQuote.php.html#33"><abbr title="App\Actions\Quotes\RejectQuote::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RejectQuote.php.html#38"><abbr title="App\Actions\Quotes\RejectQuote::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RejectQuote.php.html#43"><abbr title="App\Actions\Quotes\RejectQuote::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RejectQuote.php.html#91"><abbr title="App\Actions\Quotes\RejectQuote::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RejectQuote.php.html#162"><abbr title="App\Actions\Quotes\RejectQuote::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RestoreQuote.php.html#25"><abbr title="App\Actions\Quotes\RestoreQuote::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RestoreQuote.php.html#30"><abbr title="App\Actions\Quotes\RestoreQuote::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SubmitForApproval.php.html#34"><abbr title="App\Actions\Quotes\SubmitForApproval::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SubmitForApproval.php.html#39"><abbr title="App\Actions\Quotes\SubmitForApproval::getRoute">getRoute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SubmitForApproval.php.html#44"><abbr title="App\Actions\Quotes\SubmitForApproval::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SubmitForApproval.php.html#62"><abbr title="App\Actions\Quotes\SubmitForApproval::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SubmitForApproval.php.html#147"><abbr title="App\Actions\Quotes\SubmitForApproval::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateQuote.php.html#27"><abbr title="App\Actions\Quotes\UpdateQuote::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateQuote.php.html#55"><abbr title="App\Actions\Quotes\UpdateQuote::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateQuoteTask.php.html#43"><abbr title="App\Actions\Quotes\UpdateQuoteTask::getSlug">getSlug</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateQuoteTask.php.html#73"><abbr title="App\Actions\Quotes\UpdateQuoteTask::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateQuoteTask.php.html#188"><abbr title="App\Actions\Quotes\UpdateQuoteTask::asController">asController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateQuoteTask.php.html#254"><abbr title="App\Actions\Quotes\UpdateQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="UpdateQuote.php.html#55"><abbr title="App\Actions\Quotes\UpdateQuote::handle">handle</abbr></a></td><td class="text-right">992</td></tr>
       <tr><td><a href="CreateQuote.php.html#59"><abbr title="App\Actions\Quotes\CreateQuote::handle">handle</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="UpdateQuoteTask.php.html#73"><abbr title="App\Actions\Quotes\UpdateQuoteTask::handle">handle</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="ApproveQuote.php.html#48"><abbr title="App\Actions\Quotes\ApproveQuote::handle">handle</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="CreateQuote.php.html#197"><abbr title="App\Actions\Quotes\CreateQuote::asController">asController</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="CreateQuoteTask.php.html#52"><abbr title="App\Actions\Quotes\CreateQuoteTask::handle">handle</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="RestoreQuote.php.html#30"><abbr title="App\Actions\Quotes\RestoreQuote::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="BaseAction.php.html#46"><abbr title="App\Actions\Quotes\BaseAction::validateRouteParameterRelation">validateRouteParameterRelation</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ApproveQuote.php.html#131"><abbr title="App\Actions\Quotes\ApproveQuote::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="DeleteQuoteTask.php.html#75"><abbr title="App\Actions\Quotes\DeleteQuoteTask::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="SubmitForApproval.php.html#62"><abbr title="App\Actions\Quotes\SubmitForApproval::asController">asController</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CreateQuoteTask.php.html#149"><abbr title="App\Actions\Quotes\CreateQuoteTask::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="UpdateQuoteTask.php.html#188"><abbr title="App\Actions\Quotes\UpdateQuoteTask::asController">asController</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="RejectQuote.php.html#43"><abbr title="App\Actions\Quotes\RejectQuote::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="RejectQuote.php.html#91"><abbr title="App\Actions\Quotes\RejectQuote::asController">asController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DeleteQuoteTask.php.html#39"><abbr title="App\Actions\Quotes\DeleteQuoteTask::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApproveQuote.php.html#219"><abbr title="App\Actions\Quotes\ApproveQuote::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CreateQuote.php.html#290"><abbr title="App\Actions\Quotes\CreateQuote::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CreateQuoteTask.php.html#210"><abbr title="App\Actions\Quotes\CreateQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DeleteQuoteTask.php.html#130"><abbr title="App\Actions\Quotes\DeleteQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RejectQuote.php.html#162"><abbr title="App\Actions\Quotes\RejectQuote::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SubmitForApproval.php.html#147"><abbr title="App\Actions\Quotes\SubmitForApproval::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateQuoteTask.php.html#254"><abbr title="App\Actions\Quotes\UpdateQuoteTask::authorize">authorize</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.15</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:54:30 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([10,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([42,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,25,"<a href=\"ApproveQuote.php.html#30\">App\\Actions\\Quotes\\ApproveQuote<\/a>"],[0,13,"<a href=\"BaseAction.php.html#14\">App\\Actions\\Quotes\\BaseAction<\/a>"],[0,32,"<a href=\"CreateQuote.php.html#42\">App\\Actions\\Quotes\\CreateQuote<\/a>"],[0,22,"<a href=\"CreateQuoteTask.php.html#35\">App\\Actions\\Quotes\\CreateQuoteTask<\/a>"],[0,14,"<a href=\"DeleteQuoteTask.php.html#28\">App\\Actions\\Quotes\\DeleteQuoteTask<\/a>"],[0,16,"<a href=\"RejectQuote.php.html#29\">App\\Actions\\Quotes\\RejectQuote<\/a>"],[0,11,"<a href=\"RestoreQuote.php.html#21\">App\\Actions\\Quotes\\RestoreQuote<\/a>"],[0,13,"<a href=\"SubmitForApproval.php.html#30\">App\\Actions\\Quotes\\SubmitForApproval<\/a>"],[0,32,"<a href=\"UpdateQuote.php.html#23\">App\\Actions\\Quotes\\UpdateQuote<\/a>"],[0,24,"<a href=\"UpdateQuoteTask.php.html#37\">App\\Actions\\Quotes\\UpdateQuoteTask<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ApproveQuote.php.html#34\">App\\Actions\\Quotes\\ApproveQuote::getSlug<\/a>"],[0,1,"<a href=\"ApproveQuote.php.html#39\">App\\Actions\\Quotes\\ApproveQuote::getRoute<\/a>"],[0,13,"<a href=\"ApproveQuote.php.html#48\">App\\Actions\\Quotes\\ApproveQuote::handle<\/a>"],[0,8,"<a href=\"ApproveQuote.php.html#131\">App\\Actions\\Quotes\\ApproveQuote::asController<\/a>"],[0,2,"<a href=\"ApproveQuote.php.html#219\">App\\Actions\\Quotes\\ApproveQuote::authorize<\/a>"],[0,1,"<a href=\"BaseAction.php.html#18\">App\\Actions\\Quotes\\BaseAction::getSlug<\/a>"],[0,1,"<a href=\"BaseAction.php.html#23\">App\\Actions\\Quotes\\BaseAction::getRoute<\/a>"],[0,1,"<a href=\"BaseAction.php.html#28\">App\\Actions\\Quotes\\BaseAction::routes<\/a>"],[0,1,"<a href=\"BaseAction.php.html#38\">App\\Actions\\Quotes\\BaseAction::authorize<\/a>"],[0,9,"<a href=\"BaseAction.php.html#46\">App\\Actions\\Quotes\\BaseAction::validateRouteParameterRelation<\/a>"],[0,1,"<a href=\"CreateQuote.php.html#46\">App\\Actions\\Quotes\\CreateQuote::getSlug<\/a>"],[0,1,"<a href=\"CreateQuote.php.html#51\">App\\Actions\\Quotes\\CreateQuote::getRoute<\/a>"],[0,15,"<a href=\"CreateQuote.php.html#59\">App\\Actions\\Quotes\\CreateQuote::handle<\/a>"],[0,13,"<a href=\"CreateQuote.php.html#197\">App\\Actions\\Quotes\\CreateQuote::asController<\/a>"],[0,2,"<a href=\"CreateQuote.php.html#290\">App\\Actions\\Quotes\\CreateQuote::authorize<\/a>"],[0,1,"<a href=\"CreateQuoteTask.php.html#39\">App\\Actions\\Quotes\\CreateQuoteTask::getSlug<\/a>"],[0,1,"<a href=\"CreateQuoteTask.php.html#44\">App\\Actions\\Quotes\\CreateQuoteTask::getRoute<\/a>"],[0,11,"<a href=\"CreateQuoteTask.php.html#52\">App\\Actions\\Quotes\\CreateQuoteTask::handle<\/a>"],[0,7,"<a href=\"CreateQuoteTask.php.html#149\">App\\Actions\\Quotes\\CreateQuoteTask::asController<\/a>"],[0,2,"<a href=\"CreateQuoteTask.php.html#210\">App\\Actions\\Quotes\\CreateQuoteTask::authorize<\/a>"],[0,1,"<a href=\"DeleteQuoteTask.php.html#34\">App\\Actions\\Quotes\\DeleteQuoteTask::getSlug<\/a>"],[0,3,"<a href=\"DeleteQuoteTask.php.html#39\">App\\Actions\\Quotes\\DeleteQuoteTask::handle<\/a>"],[0,8,"<a href=\"DeleteQuoteTask.php.html#75\">App\\Actions\\Quotes\\DeleteQuoteTask::asController<\/a>"],[0,2,"<a href=\"DeleteQuoteTask.php.html#130\">App\\Actions\\Quotes\\DeleteQuoteTask::authorize<\/a>"],[0,1,"<a href=\"RejectQuote.php.html#33\">App\\Actions\\Quotes\\RejectQuote::getSlug<\/a>"],[0,1,"<a href=\"RejectQuote.php.html#38\">App\\Actions\\Quotes\\RejectQuote::getRoute<\/a>"],[0,6,"<a href=\"RejectQuote.php.html#43\">App\\Actions\\Quotes\\RejectQuote::handle<\/a>"],[0,6,"<a href=\"RejectQuote.php.html#91\">App\\Actions\\Quotes\\RejectQuote::asController<\/a>"],[0,2,"<a href=\"RejectQuote.php.html#162\">App\\Actions\\Quotes\\RejectQuote::authorize<\/a>"],[0,1,"<a href=\"RestoreQuote.php.html#25\">App\\Actions\\Quotes\\RestoreQuote::getSlug<\/a>"],[0,10,"<a href=\"RestoreQuote.php.html#30\">App\\Actions\\Quotes\\RestoreQuote::handle<\/a>"],[0,1,"<a href=\"SubmitForApproval.php.html#34\">App\\Actions\\Quotes\\SubmitForApproval::getSlug<\/a>"],[0,1,"<a href=\"SubmitForApproval.php.html#39\">App\\Actions\\Quotes\\SubmitForApproval::getRoute<\/a>"],[0,1,"<a href=\"SubmitForApproval.php.html#44\">App\\Actions\\Quotes\\SubmitForApproval::handle<\/a>"],[0,8,"<a href=\"SubmitForApproval.php.html#62\">App\\Actions\\Quotes\\SubmitForApproval::asController<\/a>"],[0,2,"<a href=\"SubmitForApproval.php.html#147\">App\\Actions\\Quotes\\SubmitForApproval::authorize<\/a>"],[0,1,"<a href=\"UpdateQuote.php.html#27\">App\\Actions\\Quotes\\UpdateQuote::getSlug<\/a>"],[0,31,"<a href=\"UpdateQuote.php.html#55\">App\\Actions\\Quotes\\UpdateQuote::handle<\/a>"],[0,1,"<a href=\"UpdateQuoteTask.php.html#43\">App\\Actions\\Quotes\\UpdateQuoteTask::getSlug<\/a>"],[0,14,"<a href=\"UpdateQuoteTask.php.html#73\">App\\Actions\\Quotes\\UpdateQuoteTask::handle<\/a>"],[0,7,"<a href=\"UpdateQuoteTask.php.html#188\">App\\Actions\\Quotes\\UpdateQuoteTask::asController<\/a>"],[0,2,"<a href=\"UpdateQuoteTask.php.html#254\">App\\Actions\\Quotes\\UpdateQuoteTask::authorize<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Listeners/User/PinnedQuotesViewListener.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Listeners</a></li>
         <li class="breadcrumb-item"><a href="index.html">User</a></li>
         <li class="breadcrumb-item active">PinnedQuotesViewListener.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;48</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><abbr title="App\Listeners\User\PinnedQuotesViewListener">PinnedQuotesViewListener</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;48</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger small">20</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#17"><abbr title="__construct()">__construct</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#25"><abbr title="handle(App\Events\User\DefaultPinnedQuotesView $event): void">handle</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;47</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">12</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Listeners\User</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Enums\QuoteStatus</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Enums\ViewTypes</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Events\User\DefaultPinnedQuotesView</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\UserPinnedView</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\View</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\ViewType</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">PinnedQuotesViewListener</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Create&nbsp;the&nbsp;event&nbsp;listener.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">__construct</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Handle&nbsp;the&nbsp;event.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handle</span><span class="keyword">(</span><span class="default">DefaultPinnedQuotesView</span><span class="default">&nbsp;</span><span class="default">$event</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$event</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$viewType</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">ViewType</span><span class="default">::</span><span class="default">where</span><span class="keyword">(</span><span class="default">'slug'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">ViewTypes</span><span class="default">::</span><span class="default">QUOTES</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$viewType</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$quoteSubMenus</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'scope'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'individual'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'payload'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">json_decode</span><span class="keyword">(</span><span class="default">'{&quot;columns&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;sub_fields&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Property&nbsp;Address&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;property_address&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true}],&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Parent&nbsp;WO&nbsp;ID&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;work_order_number&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Total&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;total&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Approved&nbsp;Total&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;total_approved&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;sub_fields&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;Date&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_date&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true}],&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Approved&nbsp;/&nbsp;Rejected&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;approved_by&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;WO&nbsp;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false}],&nbsp;&quot;filters&quot;:&nbsp;{&quot;fields&quot;:&nbsp;[{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;,&nbsp;&quot;is_not&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;Date&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_date&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}}],&nbsp;&quot;applied&quot;:&nbsp;{&quot;fl_group&quot;:&nbsp;[{&quot;field&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;},&nbsp;&quot;value&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Pending&nbsp;Approval&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;pending-approval&quot;}],&nbsp;&quot;operation&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;is&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is&quot;}}],&nbsp;&quot;group_op&quot;:&nbsp;null},&nbsp;&quot;operators&quot;:&nbsp;{&quot;is&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;is&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is&quot;},&nbsp;&quot;is_not&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;not&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_not&quot;},&nbsp;&quot;is_after&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;after&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_after&quot;},&nbsp;&quot;is_before&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;before&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_before&quot;},&nbsp;&quot;is_between&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;between&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_between&quot;}}},&nbsp;&quot;grouping&quot;:&nbsp;{&quot;g_by&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;None&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;none&quot;},&nbsp;&quot;default&quot;:&nbsp;&quot;none&quot;,&nbsp;&quot;g_options&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;None&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;none&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;WO&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;work_order_number&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;}]}}'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">QuoteStatus</span><span class="default">::</span><span class="default">label</span><span class="keyword">(</span><span class="default">QuoteStatus</span><span class="default">::</span><span class="default">PENDING_APPROVAL</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'web'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'view_type_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$viewType</span><span class="default">-&gt;</span><span class="default">view_type_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'user_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'scope'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'individual'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'payload'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">json_decode</span><span class="keyword">(</span><span class="default">'{&quot;columns&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;sub_fields&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Property&nbsp;Address&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;property_address&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true}],&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Parent&nbsp;WO&nbsp;ID&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;work_order_number&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Total&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;total&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Approved&nbsp;Total&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;total_approved&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;sub_fields&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;Date&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_date&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true}],&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Approved&nbsp;/&nbsp;Rejected&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;approved_by&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;WO&nbsp;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false}],&nbsp;&quot;filters&quot;:&nbsp;{&quot;fields&quot;:&nbsp;[{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;,&nbsp;&quot;is_not&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;Date&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_date&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}}],&nbsp;&quot;applied&quot;:&nbsp;{&quot;fl_group&quot;:&nbsp;[{&quot;field&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;},&nbsp;&quot;value&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Pending&nbsp;Review&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;quote-pending-review&quot;}],&nbsp;&quot;operation&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;is&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is&quot;}}],&nbsp;&quot;group_op&quot;:&nbsp;null},&nbsp;&quot;operators&quot;:&nbsp;{&quot;is&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;is&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is&quot;},&nbsp;&quot;is_not&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;not&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_not&quot;},&nbsp;&quot;is_after&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;after&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_after&quot;},&nbsp;&quot;is_before&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;before&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_before&quot;},&nbsp;&quot;is_between&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;between&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_between&quot;}}},&nbsp;&quot;grouping&quot;:&nbsp;{&quot;g_by&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;None&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;none&quot;},&nbsp;&quot;default&quot;:&nbsp;&quot;none&quot;,&nbsp;&quot;g_options&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;None&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;none&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;WO&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;work_order_number&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;}]}}'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">QuoteStatus</span><span class="default">::</span><span class="default">label</span><span class="keyword">(</span><span class="default">QuoteStatus</span><span class="default">::</span><span class="default">QUOTE_PENDING_REVIEW</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'web'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'view_type_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$viewType</span><span class="default">-&gt;</span><span class="default">view_type_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'user_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'scope'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'individual'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'payload'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">json_decode</span><span class="keyword">(</span><span class="default">'{&quot;columns&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;sub_fields&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Property&nbsp;Address&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;property_address&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true}],&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Parent&nbsp;WO&nbsp;ID&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;work_order_number&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Total&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;total&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Approved&nbsp;Total&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;total_approved&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;sub_fields&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;Date&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_date&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true}],&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Approved&nbsp;/&nbsp;Rejected&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;approved_by&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;WO&nbsp;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false}],&nbsp;&quot;filters&quot;:&nbsp;{&quot;fields&quot;:&nbsp;[{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;,&nbsp;&quot;is_not&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;Date&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_date&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}}],&nbsp;&quot;applied&quot;:&nbsp;{&quot;fl_group&quot;:&nbsp;[{&quot;field&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;},&nbsp;&quot;value&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Approved&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;approved&quot;}],&nbsp;&quot;operation&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;is&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is&quot;}}],&nbsp;&quot;group_op&quot;:&nbsp;null},&nbsp;&quot;operators&quot;:&nbsp;{&quot;is&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;is&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is&quot;},&nbsp;&quot;is_not&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;not&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_not&quot;},&nbsp;&quot;is_after&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;after&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_after&quot;},&nbsp;&quot;is_before&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;before&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_before&quot;},&nbsp;&quot;is_between&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;between&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_between&quot;}}},&nbsp;&quot;grouping&quot;:&nbsp;{&quot;g_by&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;None&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;none&quot;},&nbsp;&quot;default&quot;:&nbsp;&quot;none&quot;,&nbsp;&quot;g_options&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;None&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;none&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;WO&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;work_order_number&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;}]}}'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">QuoteStatus</span><span class="default">::</span><span class="default">label</span><span class="keyword">(</span><span class="default">QuoteStatus</span><span class="default">::</span><span class="default">APPROVED</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'web'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'view_type_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$viewType</span><span class="default">-&gt;</span><span class="default">view_type_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'user_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'scope'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'individual'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'payload'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">json_decode</span><span class="keyword">(</span><span class="default">'{&quot;columns&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;sub_fields&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Property&nbsp;Address&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;property_address&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true}],&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Parent&nbsp;WO&nbsp;ID&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;work_order_number&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;false,&nbsp;&quot;isStaticColumn&quot;:&nbsp;true},&nbsp;{&quot;label&quot;:&nbsp;&quot;Total&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;total&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Approved&nbsp;Total&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;total_approved&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;sub_fields&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;Date&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_date&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true}],&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;Approved&nbsp;/&nbsp;Rejected&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;approved_by&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false},&nbsp;{&quot;label&quot;:&nbsp;&quot;WO&nbsp;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;,&nbsp;&quot;selected&quot;:&nbsp;true,&nbsp;&quot;isEditable&quot;:&nbsp;true,&nbsp;&quot;isStaticColumn&quot;:&nbsp;false}],&nbsp;&quot;filters&quot;:&nbsp;{&quot;fields&quot;:&nbsp;[{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;,&nbsp;&quot;is_not&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;,&nbsp;&quot;is_not&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;Date&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_date&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}},&nbsp;{&quot;ops&quot;:&nbsp;[&quot;is&quot;],&nbsp;&quot;label&quot;:&nbsp;&quot;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;,&nbsp;&quot;values_type&quot;:&nbsp;{&quot;is&quot;:&nbsp;&quot;multi-select&quot;}}],&nbsp;&quot;applied&quot;:&nbsp;{&quot;fl_group&quot;:&nbsp;[{&quot;field&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;},&nbsp;&quot;value&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;Rejected&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;rejected&quot;}],&nbsp;&quot;operation&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;is&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is&quot;}}],&nbsp;&quot;group_op&quot;:&nbsp;null},&nbsp;&quot;operators&quot;:&nbsp;{&quot;is&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;is&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is&quot;},&nbsp;&quot;is_not&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;not&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_not&quot;},&nbsp;&quot;is_after&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;after&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_after&quot;},&nbsp;&quot;is_before&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;before&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_before&quot;},&nbsp;&quot;is_between&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;Is&nbsp;between&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;is_between&quot;}}},&nbsp;&quot;grouping&quot;:&nbsp;{&quot;g_by&quot;:&nbsp;{&quot;label&quot;:&nbsp;&quot;None&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;none&quot;},&nbsp;&quot;default&quot;:&nbsp;&quot;none&quot;,&nbsp;&quot;g_options&quot;:&nbsp;[{&quot;label&quot;:&nbsp;&quot;None&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;none&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;WO&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;work_order_number&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Status&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;status&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Category&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;category&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Assignee&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;assignee&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Submitted&nbsp;By&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;submitted_by&quot;},&nbsp;{&quot;label&quot;:&nbsp;&quot;Tags&quot;,&nbsp;&quot;value&quot;:&nbsp;&quot;tag&quot;}]}}'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">QuoteStatus</span><span class="default">::</span><span class="default">label</span><span class="keyword">(</span><span class="default">QuoteStatus</span><span class="default">::</span><span class="default">REJECTED</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'web'</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'view_type_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$viewType</span><span class="default">-&gt;</span><span class="default">view_type_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'user_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">foreach</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$quoteSubMenus</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">$quoteSubMenu</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$view</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">View</span><span class="default">::</span><span class="default">updateOrCreate</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'name'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$quoteSubMenu</span><span class="keyword">[</span><span class="default">'name'</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'user_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$quoteSubMenu</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">UserPinnedView</span><span class="default">::</span><span class="default">updateOrCreate</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'user_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">user_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'view_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$view</span><span class="default">-&gt;</span><span class="default">view_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:22:08 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Listeners/WorkOrder/Trip</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Listeners</a></li>
         <li class="breadcrumb-item"><a href="../index.html">WorkOrder</a></li>
         <li class="breadcrumb-item"><a href="index.html">Trip</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="TripCanceledListener.php.html#16">App\Listeners\WorkOrder\Trip\TripCanceledListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEnRoutePausedListener.php.html#14">App\Listeners\WorkOrder\Trip\TripEnRoutePausedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEnRouteResumeListener.php.html#14">App\Listeners\WorkOrder\Trip\TripEnRouteResumeListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEnRouteStartListener.php.html#17">App\Listeners\WorkOrder\Trip\TripEnRouteStartListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEndListener.php.html#17">App\Listeners\WorkOrder\Trip\TripEndListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripPausedListener.php.html#14">App\Listeners\WorkOrder\Trip\TripPausedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripReScheduledListener.php.html#21">App\Listeners\WorkOrder\Trip\TripReScheduledListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripResumeListener.php.html#13">App\Listeners\WorkOrder\Trip\TripResumeListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripScheduleInProgressListener.php.html#13">App\Listeners\WorkOrder\Trip\TripScheduleInProgressListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripScheduledListener.php.html#19">App\Listeners\WorkOrder\Trip\TripScheduledListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripWorkingListener.php.html#14">App\Listeners\WorkOrder\Trip\TripWorkingListener</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="TripReScheduledListener.php.html#21">App\Listeners\WorkOrder\Trip\TripReScheduledListener</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="TripScheduledListener.php.html#19">App\Listeners\WorkOrder\Trip\TripScheduledListener</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="TripCanceledListener.php.html#16">App\Listeners\WorkOrder\Trip\TripCanceledListener</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="TripEndListener.php.html#17">App\Listeners\WorkOrder\Trip\TripEndListener</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="TripEnRouteStartListener.php.html#17">App\Listeners\WorkOrder\Trip\TripEnRouteStartListener</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="TripCanceledListener.php.html#23"><abbr title="App\Listeners\WorkOrder\Trip\TripCanceledListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripCanceledListener.php.html#31"><abbr title="App\Listeners\WorkOrder\Trip\TripCanceledListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripCanceledListener.php.html#50"><abbr title="App\Listeners\WorkOrder\Trip\TripCanceledListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEnRoutePausedListener.php.html#21"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRoutePausedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEnRoutePausedListener.php.html#29"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRoutePausedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEnRouteResumeListener.php.html#21"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRouteResumeListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEnRouteResumeListener.php.html#29"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRouteResumeListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEnRouteStartListener.php.html#24"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRouteStartListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEnRouteStartListener.php.html#32"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRouteStartListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEndListener.php.html#24"><abbr title="App\Listeners\WorkOrder\Trip\TripEndListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEndListener.php.html#32"><abbr title="App\Listeners\WorkOrder\Trip\TripEndListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripEndListener.php.html#51"><abbr title="App\Listeners\WorkOrder\Trip\TripEndListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripPausedListener.php.html#21"><abbr title="App\Listeners\WorkOrder\Trip\TripPausedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripPausedListener.php.html#29"><abbr title="App\Listeners\WorkOrder\Trip\TripPausedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripReScheduledListener.php.html#28"><abbr title="App\Listeners\WorkOrder\Trip\TripReScheduledListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripReScheduledListener.php.html#36"><abbr title="App\Listeners\WorkOrder\Trip\TripReScheduledListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripReScheduledListener.php.html#95"><abbr title="App\Listeners\WorkOrder\Trip\TripReScheduledListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripResumeListener.php.html#18"><abbr title="App\Listeners\WorkOrder\Trip\TripResumeListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripResumeListener.php.html#26"><abbr title="App\Listeners\WorkOrder\Trip\TripResumeListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripScheduleInProgressListener.php.html#18"><abbr title="App\Listeners\WorkOrder\Trip\TripScheduleInProgressListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripScheduleInProgressListener.php.html#26"><abbr title="App\Listeners\WorkOrder\Trip\TripScheduleInProgressListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripScheduledListener.php.html#26"><abbr title="App\Listeners\WorkOrder\Trip\TripScheduledListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripScheduledListener.php.html#34"><abbr title="App\Listeners\WorkOrder\Trip\TripScheduledListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripWorkingListener.php.html#21"><abbr title="App\Listeners\WorkOrder\Trip\TripWorkingListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripWorkingListener.php.html#29"><abbr title="App\Listeners\WorkOrder\Trip\TripWorkingListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="TripScheduledListener.php.html#34"><abbr title="App\Listeners\WorkOrder\Trip\TripScheduledListener::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="TripReScheduledListener.php.html#95"><abbr title="App\Listeners\WorkOrder\Trip\TripReScheduledListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="TripReScheduledListener.php.html#36"><abbr title="App\Listeners\WorkOrder\Trip\TripReScheduledListener::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TripCanceledListener.php.html#50"><abbr title="App\Listeners\WorkOrder\Trip\TripCanceledListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TripEnRouteStartListener.php.html#32"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRouteStartListener::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TripEndListener.php.html#51"><abbr title="App\Listeners\WorkOrder\Trip\TripEndListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([11,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([25,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"TripCanceledListener.php.html#16\">App\\Listeners\\WorkOrder\\Trip\\TripCanceledListener<\/a>"],[0,2,"<a href=\"TripEnRoutePausedListener.php.html#14\">App\\Listeners\\WorkOrder\\Trip\\TripEnRoutePausedListener<\/a>"],[0,2,"<a href=\"TripEnRouteResumeListener.php.html#14\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteResumeListener<\/a>"],[0,4,"<a href=\"TripEnRouteStartListener.php.html#17\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteStartListener<\/a>"],[0,5,"<a href=\"TripEndListener.php.html#17\">App\\Listeners\\WorkOrder\\Trip\\TripEndListener<\/a>"],[0,2,"<a href=\"TripPausedListener.php.html#14\">App\\Listeners\\WorkOrder\\Trip\\TripPausedListener<\/a>"],[0,10,"<a href=\"TripReScheduledListener.php.html#21\">App\\Listeners\\WorkOrder\\Trip\\TripReScheduledListener<\/a>"],[0,2,"<a href=\"TripResumeListener.php.html#13\">App\\Listeners\\WorkOrder\\Trip\\TripResumeListener<\/a>"],[0,2,"<a href=\"TripScheduleInProgressListener.php.html#13\">App\\Listeners\\WorkOrder\\Trip\\TripScheduleInProgressListener<\/a>"],[0,8,"<a href=\"TripScheduledListener.php.html#19\">App\\Listeners\\WorkOrder\\Trip\\TripScheduledListener<\/a>"],[0,2,"<a href=\"TripWorkingListener.php.html#14\">App\\Listeners\\WorkOrder\\Trip\\TripWorkingListener<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"TripCanceledListener.php.html#23\">App\\Listeners\\WorkOrder\\Trip\\TripCanceledListener::__construct<\/a>"],[0,1,"<a href=\"TripCanceledListener.php.html#31\">App\\Listeners\\WorkOrder\\Trip\\TripCanceledListener::handle<\/a>"],[0,3,"<a href=\"TripCanceledListener.php.html#50\">App\\Listeners\\WorkOrder\\Trip\\TripCanceledListener::updateHealthScoreStatus<\/a>"],[0,1,"<a href=\"TripEnRoutePausedListener.php.html#21\">App\\Listeners\\WorkOrder\\Trip\\TripEnRoutePausedListener::__construct<\/a>"],[0,1,"<a href=\"TripEnRoutePausedListener.php.html#29\">App\\Listeners\\WorkOrder\\Trip\\TripEnRoutePausedListener::handle<\/a>"],[0,1,"<a href=\"TripEnRouteResumeListener.php.html#21\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteResumeListener::__construct<\/a>"],[0,1,"<a href=\"TripEnRouteResumeListener.php.html#29\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteResumeListener::handle<\/a>"],[0,1,"<a href=\"TripEnRouteStartListener.php.html#24\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteStartListener::__construct<\/a>"],[0,3,"<a href=\"TripEnRouteStartListener.php.html#32\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteStartListener::handle<\/a>"],[0,1,"<a href=\"TripEndListener.php.html#24\">App\\Listeners\\WorkOrder\\Trip\\TripEndListener::__construct<\/a>"],[0,1,"<a href=\"TripEndListener.php.html#32\">App\\Listeners\\WorkOrder\\Trip\\TripEndListener::handle<\/a>"],[0,3,"<a href=\"TripEndListener.php.html#51\">App\\Listeners\\WorkOrder\\Trip\\TripEndListener::updateHealthScoreStatus<\/a>"],[0,1,"<a href=\"TripPausedListener.php.html#21\">App\\Listeners\\WorkOrder\\Trip\\TripPausedListener::__construct<\/a>"],[0,1,"<a href=\"TripPausedListener.php.html#29\">App\\Listeners\\WorkOrder\\Trip\\TripPausedListener::handle<\/a>"],[0,1,"<a href=\"TripReScheduledListener.php.html#28\">App\\Listeners\\WorkOrder\\Trip\\TripReScheduledListener::__construct<\/a>"],[0,4,"<a href=\"TripReScheduledListener.php.html#36\">App\\Listeners\\WorkOrder\\Trip\\TripReScheduledListener::handle<\/a>"],[0,5,"<a href=\"TripReScheduledListener.php.html#95\">App\\Listeners\\WorkOrder\\Trip\\TripReScheduledListener::updateHealthScoreStatus<\/a>"],[0,1,"<a href=\"TripResumeListener.php.html#18\">App\\Listeners\\WorkOrder\\Trip\\TripResumeListener::__construct<\/a>"],[0,1,"<a href=\"TripResumeListener.php.html#26\">App\\Listeners\\WorkOrder\\Trip\\TripResumeListener::handle<\/a>"],[0,1,"<a href=\"TripScheduleInProgressListener.php.html#18\">App\\Listeners\\WorkOrder\\Trip\\TripScheduleInProgressListener::__construct<\/a>"],[0,1,"<a href=\"TripScheduleInProgressListener.php.html#26\">App\\Listeners\\WorkOrder\\Trip\\TripScheduleInProgressListener::handle<\/a>"],[0,1,"<a href=\"TripScheduledListener.php.html#26\">App\\Listeners\\WorkOrder\\Trip\\TripScheduledListener::__construct<\/a>"],[0,7,"<a href=\"TripScheduledListener.php.html#34\">App\\Listeners\\WorkOrder\\Trip\\TripScheduledListener::handle<\/a>"],[0,1,"<a href=\"TripWorkingListener.php.html#21\">App\\Listeners\\WorkOrder\\Trip\\TripWorkingListener::__construct<\/a>"],[0,1,"<a href=\"TripWorkingListener.php.html#29\">App\\Listeners\\WorkOrder\\Trip\\TripWorkingListener::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

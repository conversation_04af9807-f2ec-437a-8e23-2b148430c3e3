<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Listeners/WorkOrder</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Listeners</a></li>
         <li class="breadcrumb-item"><a href="index.html">WorkOrder</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Action/WorkOrderAccessInfoUpdatedListener.php.html#12">App\Listeners\WorkOrder\Action\WorkOrderAccessInfoUpdatedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderAwaitingAvailabilityListener.php.html#14">App\Listeners\WorkOrder\Action\WorkOrderAwaitingAvailabilityListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderCanceledListener.php.html#20">App\Listeners\WorkOrder\Action\WorkOrderCanceledListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderClaimPendingListener.php.html#17">App\Listeners\WorkOrder\Action\WorkOrderClaimPendingListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderCreatedListener.php.html#14">App\Listeners\WorkOrder\Action\WorkOrderCreatedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderDueDateChangedListener.php.html#13">App\Listeners\WorkOrder\Action\WorkOrderDueDateChangedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderNTESetListener.php.html#14">App\Listeners\WorkOrder\Action\WorkOrderNTESetListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderNTEUpdatedListener.php.html#14">App\Listeners\WorkOrder\Action\WorkOrderNTEUpdatedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderPriorityChangedListener.php.html#16">App\Listeners\WorkOrder\Action\WorkOrderPriorityChangedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderQualityCheckListener.php.html#19">App\Listeners\WorkOrder\Action\WorkOrderQualityCheckListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderReadyToScheduleListener.php.html#19">App\Listeners\WorkOrder\Action\WorkOrderReadyToScheduleListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderScheduledListener.php.html#15">App\Listeners\WorkOrder\Action\WorkOrderScheduledListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderWorkInProgressListener.php.html#18">App\Listeners\WorkOrder\Action\WorkOrderWorkInProgressListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExternalWorkOrderUpdateListener.php.html#9">App\Listeners\WorkOrder\ExternalWorkOrderUpdateListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteCreatedListener.php.html#13">App\Listeners\WorkOrder\Note\WorkOrderNoteCreatedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteApprovedListener.php.html#13">App\Listeners\WorkOrder\Quote\QuoteApprovedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteCreatedListener.php.html#14">App\Listeners\WorkOrder\Quote\QuoteCreatedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteRejectedListener.php.html#14">App\Listeners\WorkOrder\Quote\QuoteRejectedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteSubmitForApprovalListener.php.html#14">App\Listeners\WorkOrder\Quote\QuoteSubmitForApprovalListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeleteListener.php.html#13">App\Listeners\WorkOrder\ResidentAvailabilityDeleteListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripCanceledListener.php.html#16">App\Listeners\WorkOrder\Trip\TripCanceledListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEnRoutePausedListener.php.html#14">App\Listeners\WorkOrder\Trip\TripEnRoutePausedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEnRouteResumeListener.php.html#14">App\Listeners\WorkOrder\Trip\TripEnRouteResumeListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEnRouteStartListener.php.html#17">App\Listeners\WorkOrder\Trip\TripEnRouteStartListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEndListener.php.html#17">App\Listeners\WorkOrder\Trip\TripEndListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripPausedListener.php.html#14">App\Listeners\WorkOrder\Trip\TripPausedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripReScheduledListener.php.html#21">App\Listeners\WorkOrder\Trip\TripReScheduledListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripResumeListener.php.html#13">App\Listeners\WorkOrder\Trip\TripResumeListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripScheduleInProgressListener.php.html#13">App\Listeners\WorkOrder\Trip\TripScheduleInProgressListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripScheduledListener.php.html#19">App\Listeners\WorkOrder\Trip\TripScheduledListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripWorkingListener.php.html#14">App\Listeners\WorkOrder\Trip\TripWorkingListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAddressUpdatedListener.php.html#12">App\Listeners\WorkOrder\WorkOrderAddressUpdatedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDescriptionUpdatedListener.php.html#13">App\Listeners\WorkOrder\WorkOrderDescriptionUpdatedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPausedListener.php.html#21">App\Listeners\WorkOrder\WorkOrderPausedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReOpenListener.php.html#20">App\Listeners\WorkOrder\WorkOrderReOpenListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReScheduledListener.php.html#14">App\Listeners\WorkOrder\WorkOrderReScheduledListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResidentUpdatedListener.php.html#13">App\Listeners\WorkOrder\WorkOrderResidentUpdatedListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResolveListener.php.html#20">App\Listeners\WorkOrder\WorkOrderResolveListener</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderSendToVendorListener.php.html#15">App\Listeners\WorkOrder\WorkOrderSendToVendorListener</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExternalWorkOrderUpdateListener.php.html#9">App\Listeners\WorkOrder\ExternalWorkOrderUpdateListener</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="WorkOrderPausedListener.php.html#21">App\Listeners\WorkOrder\WorkOrderPausedListener</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Trip/TripReScheduledListener.php.html#21">App\Listeners\WorkOrder\Trip\TripReScheduledListener</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Action/WorkOrderCanceledListener.php.html#20">App\Listeners\WorkOrder\Action\WorkOrderCanceledListener</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Trip/TripScheduledListener.php.html#19">App\Listeners\WorkOrder\Trip\TripScheduledListener</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrderReOpenListener.php.html#20">App\Listeners\WorkOrder\WorkOrderReOpenListener</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrderResolveListener.php.html#20">App\Listeners\WorkOrder\WorkOrderResolveListener</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrderSendToVendorListener.php.html#15">App\Listeners\WorkOrder\WorkOrderSendToVendorListener</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Action/WorkOrderQualityCheckListener.php.html#19">App\Listeners\WorkOrder\Action\WorkOrderQualityCheckListener</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Action/WorkOrderAccessInfoUpdatedListener.php.html#12">App\Listeners\WorkOrder\Action\WorkOrderAccessInfoUpdatedListener</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Action/WorkOrderReadyToScheduleListener.php.html#19">App\Listeners\WorkOrder\Action\WorkOrderReadyToScheduleListener</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Trip/TripCanceledListener.php.html#16">App\Listeners\WorkOrder\Trip\TripCanceledListener</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Trip/TripEndListener.php.html#17">App\Listeners\WorkOrder\Trip\TripEndListener</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderAddressUpdatedListener.php.html#12">App\Listeners\WorkOrder\WorkOrderAddressUpdatedListener</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderDescriptionUpdatedListener.php.html#13">App\Listeners\WorkOrder\WorkOrderDescriptionUpdatedListener</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderResidentUpdatedListener.php.html#13">App\Listeners\WorkOrder\WorkOrderResidentUpdatedListener</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Note/WorkOrderNoteCreatedListener.php.html#13">App\Listeners\WorkOrder\Note\WorkOrderNoteCreatedListener</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Quote/QuoteApprovedListener.php.html#13">App\Listeners\WorkOrder\Quote\QuoteApprovedListener</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Trip/TripEnRouteStartListener.php.html#17">App\Listeners\WorkOrder\Trip\TripEnRouteStartListener</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Action/WorkOrderClaimPendingListener.php.html#17">App\Listeners\WorkOrder\Action\WorkOrderClaimPendingListener</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Action/WorkOrderDueDateChangedListener.php.html#13">App\Listeners\WorkOrder\Action\WorkOrderDueDateChangedListener</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Action/WorkOrderPriorityChangedListener.php.html#16">App\Listeners\WorkOrder\Action\WorkOrderPriorityChangedListener</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Action/WorkOrderWorkInProgressListener.php.html#18">App\Listeners\WorkOrder\Action\WorkOrderWorkInProgressListener</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Quote/QuoteCreatedListener.php.html#14">App\Listeners\WorkOrder\Quote\QuoteCreatedListener</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Quote/QuoteRejectedListener.php.html#14">App\Listeners\WorkOrder\Quote\QuoteRejectedListener</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Quote/QuoteSubmitForApprovalListener.php.html#14">App\Listeners\WorkOrder\Quote\QuoteSubmitForApprovalListener</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Action/WorkOrderAccessInfoUpdatedListener.php.html#17"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderAccessInfoUpdatedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderAccessInfoUpdatedListener.php.html#25"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderAccessInfoUpdatedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderAwaitingAvailabilityListener.php.html#21"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderAwaitingAvailabilityListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderAwaitingAvailabilityListener.php.html#29"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderAwaitingAvailabilityListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderCanceledListener.php.html#27"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderCanceledListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderCanceledListener.php.html#35"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderCanceledListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderClaimPendingListener.php.html#24"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderClaimPendingListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderClaimPendingListener.php.html#32"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderClaimPendingListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderCreatedListener.php.html#19"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderCreatedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderCreatedListener.php.html#27"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderCreatedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderDueDateChangedListener.php.html#20"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderDueDateChangedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderDueDateChangedListener.php.html#28"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderDueDateChangedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderNTESetListener.php.html#21"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderNTESetListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderNTESetListener.php.html#29"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderNTESetListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderNTEUpdatedListener.php.html#21"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderNTEUpdatedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderNTEUpdatedListener.php.html#29"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderNTEUpdatedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderPriorityChangedListener.php.html#23"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderPriorityChangedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderPriorityChangedListener.php.html#31"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderPriorityChangedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderQualityCheckListener.php.html#26"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderQualityCheckListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderQualityCheckListener.php.html#34"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderQualityCheckListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderReadyToScheduleListener.php.html#26"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderReadyToScheduleListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderReadyToScheduleListener.php.html#34"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderReadyToScheduleListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderScheduledListener.php.html#22"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderScheduledListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderScheduledListener.php.html#30"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderScheduledListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderWorkInProgressListener.php.html#25"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderWorkInProgressListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Action/WorkOrderWorkInProgressListener.php.html#33"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderWorkInProgressListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExternalWorkOrderUpdateListener.php.html#14"><abbr title="App\Listeners\WorkOrder\ExternalWorkOrderUpdateListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExternalWorkOrderUpdateListener.php.html#22"><abbr title="App\Listeners\WorkOrder\ExternalWorkOrderUpdateListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteCreatedListener.php.html#20"><abbr title="App\Listeners\WorkOrder\Note\WorkOrderNoteCreatedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Note/WorkOrderNoteCreatedListener.php.html#28"><abbr title="App\Listeners\WorkOrder\Note\WorkOrderNoteCreatedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteApprovedListener.php.html#18"><abbr title="App\Listeners\WorkOrder\Quote\QuoteApprovedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteApprovedListener.php.html#26"><abbr title="App\Listeners\WorkOrder\Quote\QuoteApprovedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteCreatedListener.php.html#19"><abbr title="App\Listeners\WorkOrder\Quote\QuoteCreatedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteCreatedListener.php.html#27"><abbr title="App\Listeners\WorkOrder\Quote\QuoteCreatedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteRejectedListener.php.html#19"><abbr title="App\Listeners\WorkOrder\Quote\QuoteRejectedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteRejectedListener.php.html#27"><abbr title="App\Listeners\WorkOrder\Quote\QuoteRejectedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteSubmitForApprovalListener.php.html#19"><abbr title="App\Listeners\WorkOrder\Quote\QuoteSubmitForApprovalListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quote/QuoteSubmitForApprovalListener.php.html#27"><abbr title="App\Listeners\WorkOrder\Quote\QuoteSubmitForApprovalListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeleteListener.php.html#20"><abbr title="App\Listeners\WorkOrder\ResidentAvailabilityDeleteListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ResidentAvailabilityDeleteListener.php.html#28"><abbr title="App\Listeners\WorkOrder\ResidentAvailabilityDeleteListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripCanceledListener.php.html#23"><abbr title="App\Listeners\WorkOrder\Trip\TripCanceledListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripCanceledListener.php.html#31"><abbr title="App\Listeners\WorkOrder\Trip\TripCanceledListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripCanceledListener.php.html#50"><abbr title="App\Listeners\WorkOrder\Trip\TripCanceledListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEnRoutePausedListener.php.html#21"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRoutePausedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEnRoutePausedListener.php.html#29"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRoutePausedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEnRouteResumeListener.php.html#21"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRouteResumeListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEnRouteResumeListener.php.html#29"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRouteResumeListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEnRouteStartListener.php.html#24"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRouteStartListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEnRouteStartListener.php.html#32"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRouteStartListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEndListener.php.html#24"><abbr title="App\Listeners\WorkOrder\Trip\TripEndListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEndListener.php.html#32"><abbr title="App\Listeners\WorkOrder\Trip\TripEndListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripEndListener.php.html#51"><abbr title="App\Listeners\WorkOrder\Trip\TripEndListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripPausedListener.php.html#21"><abbr title="App\Listeners\WorkOrder\Trip\TripPausedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripPausedListener.php.html#29"><abbr title="App\Listeners\WorkOrder\Trip\TripPausedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripReScheduledListener.php.html#28"><abbr title="App\Listeners\WorkOrder\Trip\TripReScheduledListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripReScheduledListener.php.html#36"><abbr title="App\Listeners\WorkOrder\Trip\TripReScheduledListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripReScheduledListener.php.html#95"><abbr title="App\Listeners\WorkOrder\Trip\TripReScheduledListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripResumeListener.php.html#18"><abbr title="App\Listeners\WorkOrder\Trip\TripResumeListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripResumeListener.php.html#26"><abbr title="App\Listeners\WorkOrder\Trip\TripResumeListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripScheduleInProgressListener.php.html#18"><abbr title="App\Listeners\WorkOrder\Trip\TripScheduleInProgressListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripScheduleInProgressListener.php.html#26"><abbr title="App\Listeners\WorkOrder\Trip\TripScheduleInProgressListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripScheduledListener.php.html#26"><abbr title="App\Listeners\WorkOrder\Trip\TripScheduledListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripScheduledListener.php.html#34"><abbr title="App\Listeners\WorkOrder\Trip\TripScheduledListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripWorkingListener.php.html#21"><abbr title="App\Listeners\WorkOrder\Trip\TripWorkingListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Trip/TripWorkingListener.php.html#29"><abbr title="App\Listeners\WorkOrder\Trip\TripWorkingListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAddressUpdatedListener.php.html#17"><abbr title="App\Listeners\WorkOrder\WorkOrderAddressUpdatedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAddressUpdatedListener.php.html#25"><abbr title="App\Listeners\WorkOrder\WorkOrderAddressUpdatedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDescriptionUpdatedListener.php.html#18"><abbr title="App\Listeners\WorkOrder\WorkOrderDescriptionUpdatedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDescriptionUpdatedListener.php.html#26"><abbr title="App\Listeners\WorkOrder\WorkOrderDescriptionUpdatedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPausedListener.php.html#28"><abbr title="App\Listeners\WorkOrder\WorkOrderPausedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPausedListener.php.html#36"><abbr title="App\Listeners\WorkOrder\WorkOrderPausedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReOpenListener.php.html#27"><abbr title="App\Listeners\WorkOrder\WorkOrderReOpenListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReOpenListener.php.html#35"><abbr title="App\Listeners\WorkOrder\WorkOrderReOpenListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReScheduledListener.php.html#19"><abbr title="App\Listeners\WorkOrder\WorkOrderReScheduledListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReScheduledListener.php.html#27"><abbr title="App\Listeners\WorkOrder\WorkOrderReScheduledListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResidentUpdatedListener.php.html#18"><abbr title="App\Listeners\WorkOrder\WorkOrderResidentUpdatedListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResidentUpdatedListener.php.html#26"><abbr title="App\Listeners\WorkOrder\WorkOrderResidentUpdatedListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResolveListener.php.html#27"><abbr title="App\Listeners\WorkOrder\WorkOrderResolveListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderResolveListener.php.html#35"><abbr title="App\Listeners\WorkOrder\WorkOrderResolveListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderSendToVendorListener.php.html#20"><abbr title="App\Listeners\WorkOrder\WorkOrderSendToVendorListener::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderSendToVendorListener.php.html#28"><abbr title="App\Listeners\WorkOrder\WorkOrderSendToVendorListener::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExternalWorkOrderUpdateListener.php.html#22"><abbr title="App\Listeners\WorkOrder\ExternalWorkOrderUpdateListener::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="WorkOrderPausedListener.php.html#36"><abbr title="App\Listeners\WorkOrder\WorkOrderPausedListener::handle">handle</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="Action/WorkOrderCanceledListener.php.html#35"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderCanceledListener::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Trip/TripScheduledListener.php.html#34"><abbr title="App\Listeners\WorkOrder\Trip\TripScheduledListener::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrderReOpenListener.php.html#35"><abbr title="App\Listeners\WorkOrder\WorkOrderReOpenListener::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrderResolveListener.php.html#35"><abbr title="App\Listeners\WorkOrder\WorkOrderResolveListener::handle">handle</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrderSendToVendorListener.php.html#28"><abbr title="App\Listeners\WorkOrder\WorkOrderSendToVendorListener::handle">handle</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Action/WorkOrderQualityCheckListener.php.html#34"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderQualityCheckListener::handle">handle</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Trip/TripReScheduledListener.php.html#95"><abbr title="App\Listeners\WorkOrder\Trip\TripReScheduledListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Action/WorkOrderAccessInfoUpdatedListener.php.html#25"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderAccessInfoUpdatedListener::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Action/WorkOrderReadyToScheduleListener.php.html#34"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderReadyToScheduleListener::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Trip/TripReScheduledListener.php.html#36"><abbr title="App\Listeners\WorkOrder\Trip\TripReScheduledListener::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderAddressUpdatedListener.php.html#25"><abbr title="App\Listeners\WorkOrder\WorkOrderAddressUpdatedListener::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderDescriptionUpdatedListener.php.html#26"><abbr title="App\Listeners\WorkOrder\WorkOrderDescriptionUpdatedListener::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderResidentUpdatedListener.php.html#26"><abbr title="App\Listeners\WorkOrder\WorkOrderResidentUpdatedListener::handle">handle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Note/WorkOrderNoteCreatedListener.php.html#28"><abbr title="App\Listeners\WorkOrder\Note\WorkOrderNoteCreatedListener::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Quote/QuoteApprovedListener.php.html#26"><abbr title="App\Listeners\WorkOrder\Quote\QuoteApprovedListener::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Trip/TripCanceledListener.php.html#50"><abbr title="App\Listeners\WorkOrder\Trip\TripCanceledListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Trip/TripEnRouteStartListener.php.html#32"><abbr title="App\Listeners\WorkOrder\Trip\TripEnRouteStartListener::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Trip/TripEndListener.php.html#51"><abbr title="App\Listeners\WorkOrder\Trip\TripEndListener::updateHealthScoreStatus">updateHealthScoreStatus</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Action/WorkOrderClaimPendingListener.php.html#32"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderClaimPendingListener::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Action/WorkOrderDueDateChangedListener.php.html#28"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderDueDateChangedListener::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Action/WorkOrderPriorityChangedListener.php.html#31"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderPriorityChangedListener::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Action/WorkOrderWorkInProgressListener.php.html#33"><abbr title="App\Listeners\WorkOrder\Action\WorkOrderWorkInProgressListener::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quote/QuoteCreatedListener.php.html#27"><abbr title="App\Listeners\WorkOrder\Quote\QuoteCreatedListener::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quote/QuoteRejectedListener.php.html#27"><abbr title="App\Listeners\WorkOrder\Quote\QuoteRejectedListener::handle">handle</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quote/QuoteSubmitForApprovalListener.php.html#27"><abbr title="App\Listeners\WorkOrder\Quote\QuoteSubmitForApprovalListener::handle">handle</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([39,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([81,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"Action\/WorkOrderAccessInfoUpdatedListener.php.html#12\">App\\Listeners\\WorkOrder\\Action\\WorkOrderAccessInfoUpdatedListener<\/a>"],[0,2,"<a href=\"Action\/WorkOrderAwaitingAvailabilityListener.php.html#14\">App\\Listeners\\WorkOrder\\Action\\WorkOrderAwaitingAvailabilityListener<\/a>"],[0,8,"<a href=\"Action\/WorkOrderCanceledListener.php.html#20\">App\\Listeners\\WorkOrder\\Action\\WorkOrderCanceledListener<\/a>"],[0,3,"<a href=\"Action\/WorkOrderClaimPendingListener.php.html#17\">App\\Listeners\\WorkOrder\\Action\\WorkOrderClaimPendingListener<\/a>"],[0,2,"<a href=\"Action\/WorkOrderCreatedListener.php.html#14\">App\\Listeners\\WorkOrder\\Action\\WorkOrderCreatedListener<\/a>"],[0,3,"<a href=\"Action\/WorkOrderDueDateChangedListener.php.html#13\">App\\Listeners\\WorkOrder\\Action\\WorkOrderDueDateChangedListener<\/a>"],[0,2,"<a href=\"Action\/WorkOrderNTESetListener.php.html#14\">App\\Listeners\\WorkOrder\\Action\\WorkOrderNTESetListener<\/a>"],[0,2,"<a href=\"Action\/WorkOrderNTEUpdatedListener.php.html#14\">App\\Listeners\\WorkOrder\\Action\\WorkOrderNTEUpdatedListener<\/a>"],[0,3,"<a href=\"Action\/WorkOrderPriorityChangedListener.php.html#16\">App\\Listeners\\WorkOrder\\Action\\WorkOrderPriorityChangedListener<\/a>"],[0,6,"<a href=\"Action\/WorkOrderQualityCheckListener.php.html#19\">App\\Listeners\\WorkOrder\\Action\\WorkOrderQualityCheckListener<\/a>"],[0,5,"<a href=\"Action\/WorkOrderReadyToScheduleListener.php.html#19\">App\\Listeners\\WorkOrder\\Action\\WorkOrderReadyToScheduleListener<\/a>"],[0,2,"<a href=\"Action\/WorkOrderScheduledListener.php.html#15\">App\\Listeners\\WorkOrder\\Action\\WorkOrderScheduledListener<\/a>"],[0,3,"<a href=\"Action\/WorkOrderWorkInProgressListener.php.html#18\">App\\Listeners\\WorkOrder\\Action\\WorkOrderWorkInProgressListener<\/a>"],[0,11,"<a href=\"ExternalWorkOrderUpdateListener.php.html#9\">App\\Listeners\\WorkOrder\\ExternalWorkOrderUpdateListener<\/a>"],[0,4,"<a href=\"Note\/WorkOrderNoteCreatedListener.php.html#13\">App\\Listeners\\WorkOrder\\Note\\WorkOrderNoteCreatedListener<\/a>"],[0,4,"<a href=\"Quote\/QuoteApprovedListener.php.html#13\">App\\Listeners\\WorkOrder\\Quote\\QuoteApprovedListener<\/a>"],[0,3,"<a href=\"Quote\/QuoteCreatedListener.php.html#14\">App\\Listeners\\WorkOrder\\Quote\\QuoteCreatedListener<\/a>"],[0,3,"<a href=\"Quote\/QuoteRejectedListener.php.html#14\">App\\Listeners\\WorkOrder\\Quote\\QuoteRejectedListener<\/a>"],[0,3,"<a href=\"Quote\/QuoteSubmitForApprovalListener.php.html#14\">App\\Listeners\\WorkOrder\\Quote\\QuoteSubmitForApprovalListener<\/a>"],[0,2,"<a href=\"ResidentAvailabilityDeleteListener.php.html#13\">App\\Listeners\\WorkOrder\\ResidentAvailabilityDeleteListener<\/a>"],[0,5,"<a href=\"Trip\/TripCanceledListener.php.html#16\">App\\Listeners\\WorkOrder\\Trip\\TripCanceledListener<\/a>"],[0,2,"<a href=\"Trip\/TripEnRoutePausedListener.php.html#14\">App\\Listeners\\WorkOrder\\Trip\\TripEnRoutePausedListener<\/a>"],[0,2,"<a href=\"Trip\/TripEnRouteResumeListener.php.html#14\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteResumeListener<\/a>"],[0,4,"<a href=\"Trip\/TripEnRouteStartListener.php.html#17\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteStartListener<\/a>"],[0,5,"<a href=\"Trip\/TripEndListener.php.html#17\">App\\Listeners\\WorkOrder\\Trip\\TripEndListener<\/a>"],[0,2,"<a href=\"Trip\/TripPausedListener.php.html#14\">App\\Listeners\\WorkOrder\\Trip\\TripPausedListener<\/a>"],[0,10,"<a href=\"Trip\/TripReScheduledListener.php.html#21\">App\\Listeners\\WorkOrder\\Trip\\TripReScheduledListener<\/a>"],[0,2,"<a href=\"Trip\/TripResumeListener.php.html#13\">App\\Listeners\\WorkOrder\\Trip\\TripResumeListener<\/a>"],[0,2,"<a href=\"Trip\/TripScheduleInProgressListener.php.html#13\">App\\Listeners\\WorkOrder\\Trip\\TripScheduleInProgressListener<\/a>"],[0,8,"<a href=\"Trip\/TripScheduledListener.php.html#19\">App\\Listeners\\WorkOrder\\Trip\\TripScheduledListener<\/a>"],[0,2,"<a href=\"Trip\/TripWorkingListener.php.html#14\">App\\Listeners\\WorkOrder\\Trip\\TripWorkingListener<\/a>"],[0,5,"<a href=\"WorkOrderAddressUpdatedListener.php.html#12\">App\\Listeners\\WorkOrder\\WorkOrderAddressUpdatedListener<\/a>"],[0,5,"<a href=\"WorkOrderDescriptionUpdatedListener.php.html#13\">App\\Listeners\\WorkOrder\\WorkOrderDescriptionUpdatedListener<\/a>"],[0,11,"<a href=\"WorkOrderPausedListener.php.html#21\">App\\Listeners\\WorkOrder\\WorkOrderPausedListener<\/a>"],[0,8,"<a href=\"WorkOrderReOpenListener.php.html#20\">App\\Listeners\\WorkOrder\\WorkOrderReOpenListener<\/a>"],[0,2,"<a href=\"WorkOrderReScheduledListener.php.html#14\">App\\Listeners\\WorkOrder\\WorkOrderReScheduledListener<\/a>"],[0,5,"<a href=\"WorkOrderResidentUpdatedListener.php.html#13\">App\\Listeners\\WorkOrder\\WorkOrderResidentUpdatedListener<\/a>"],[0,8,"<a href=\"WorkOrderResolveListener.php.html#20\">App\\Listeners\\WorkOrder\\WorkOrderResolveListener<\/a>"],[0,7,"<a href=\"WorkOrderSendToVendorListener.php.html#15\">App\\Listeners\\WorkOrder\\WorkOrderSendToVendorListener<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Action\/WorkOrderAccessInfoUpdatedListener.php.html#17\">App\\Listeners\\WorkOrder\\Action\\WorkOrderAccessInfoUpdatedListener::__construct<\/a>"],[0,4,"<a href=\"Action\/WorkOrderAccessInfoUpdatedListener.php.html#25\">App\\Listeners\\WorkOrder\\Action\\WorkOrderAccessInfoUpdatedListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderAwaitingAvailabilityListener.php.html#21\">App\\Listeners\\WorkOrder\\Action\\WorkOrderAwaitingAvailabilityListener::__construct<\/a>"],[0,1,"<a href=\"Action\/WorkOrderAwaitingAvailabilityListener.php.html#29\">App\\Listeners\\WorkOrder\\Action\\WorkOrderAwaitingAvailabilityListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderCanceledListener.php.html#27\">App\\Listeners\\WorkOrder\\Action\\WorkOrderCanceledListener::__construct<\/a>"],[0,7,"<a href=\"Action\/WorkOrderCanceledListener.php.html#35\">App\\Listeners\\WorkOrder\\Action\\WorkOrderCanceledListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderClaimPendingListener.php.html#24\">App\\Listeners\\WorkOrder\\Action\\WorkOrderClaimPendingListener::__construct<\/a>"],[0,2,"<a href=\"Action\/WorkOrderClaimPendingListener.php.html#32\">App\\Listeners\\WorkOrder\\Action\\WorkOrderClaimPendingListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderCreatedListener.php.html#19\">App\\Listeners\\WorkOrder\\Action\\WorkOrderCreatedListener::__construct<\/a>"],[0,1,"<a href=\"Action\/WorkOrderCreatedListener.php.html#27\">App\\Listeners\\WorkOrder\\Action\\WorkOrderCreatedListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderDueDateChangedListener.php.html#20\">App\\Listeners\\WorkOrder\\Action\\WorkOrderDueDateChangedListener::__construct<\/a>"],[0,2,"<a href=\"Action\/WorkOrderDueDateChangedListener.php.html#28\">App\\Listeners\\WorkOrder\\Action\\WorkOrderDueDateChangedListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderNTESetListener.php.html#21\">App\\Listeners\\WorkOrder\\Action\\WorkOrderNTESetListener::__construct<\/a>"],[0,1,"<a href=\"Action\/WorkOrderNTESetListener.php.html#29\">App\\Listeners\\WorkOrder\\Action\\WorkOrderNTESetListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderNTEUpdatedListener.php.html#21\">App\\Listeners\\WorkOrder\\Action\\WorkOrderNTEUpdatedListener::__construct<\/a>"],[0,1,"<a href=\"Action\/WorkOrderNTEUpdatedListener.php.html#29\">App\\Listeners\\WorkOrder\\Action\\WorkOrderNTEUpdatedListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderPriorityChangedListener.php.html#23\">App\\Listeners\\WorkOrder\\Action\\WorkOrderPriorityChangedListener::__construct<\/a>"],[0,2,"<a href=\"Action\/WorkOrderPriorityChangedListener.php.html#31\">App\\Listeners\\WorkOrder\\Action\\WorkOrderPriorityChangedListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderQualityCheckListener.php.html#26\">App\\Listeners\\WorkOrder\\Action\\WorkOrderQualityCheckListener::__construct<\/a>"],[0,5,"<a href=\"Action\/WorkOrderQualityCheckListener.php.html#34\">App\\Listeners\\WorkOrder\\Action\\WorkOrderQualityCheckListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderReadyToScheduleListener.php.html#26\">App\\Listeners\\WorkOrder\\Action\\WorkOrderReadyToScheduleListener::__construct<\/a>"],[0,4,"<a href=\"Action\/WorkOrderReadyToScheduleListener.php.html#34\">App\\Listeners\\WorkOrder\\Action\\WorkOrderReadyToScheduleListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderScheduledListener.php.html#22\">App\\Listeners\\WorkOrder\\Action\\WorkOrderScheduledListener::__construct<\/a>"],[0,1,"<a href=\"Action\/WorkOrderScheduledListener.php.html#30\">App\\Listeners\\WorkOrder\\Action\\WorkOrderScheduledListener::handle<\/a>"],[0,1,"<a href=\"Action\/WorkOrderWorkInProgressListener.php.html#25\">App\\Listeners\\WorkOrder\\Action\\WorkOrderWorkInProgressListener::__construct<\/a>"],[0,2,"<a href=\"Action\/WorkOrderWorkInProgressListener.php.html#33\">App\\Listeners\\WorkOrder\\Action\\WorkOrderWorkInProgressListener::handle<\/a>"],[0,1,"<a href=\"ExternalWorkOrderUpdateListener.php.html#14\">App\\Listeners\\WorkOrder\\ExternalWorkOrderUpdateListener::__construct<\/a>"],[0,10,"<a href=\"ExternalWorkOrderUpdateListener.php.html#22\">App\\Listeners\\WorkOrder\\ExternalWorkOrderUpdateListener::handle<\/a>"],[0,1,"<a href=\"Note\/WorkOrderNoteCreatedListener.php.html#20\">App\\Listeners\\WorkOrder\\Note\\WorkOrderNoteCreatedListener::__construct<\/a>"],[0,3,"<a href=\"Note\/WorkOrderNoteCreatedListener.php.html#28\">App\\Listeners\\WorkOrder\\Note\\WorkOrderNoteCreatedListener::handle<\/a>"],[0,1,"<a href=\"Quote\/QuoteApprovedListener.php.html#18\">App\\Listeners\\WorkOrder\\Quote\\QuoteApprovedListener::__construct<\/a>"],[0,3,"<a href=\"Quote\/QuoteApprovedListener.php.html#26\">App\\Listeners\\WorkOrder\\Quote\\QuoteApprovedListener::handle<\/a>"],[0,1,"<a href=\"Quote\/QuoteCreatedListener.php.html#19\">App\\Listeners\\WorkOrder\\Quote\\QuoteCreatedListener::__construct<\/a>"],[0,2,"<a href=\"Quote\/QuoteCreatedListener.php.html#27\">App\\Listeners\\WorkOrder\\Quote\\QuoteCreatedListener::handle<\/a>"],[0,1,"<a href=\"Quote\/QuoteRejectedListener.php.html#19\">App\\Listeners\\WorkOrder\\Quote\\QuoteRejectedListener::__construct<\/a>"],[0,2,"<a href=\"Quote\/QuoteRejectedListener.php.html#27\">App\\Listeners\\WorkOrder\\Quote\\QuoteRejectedListener::handle<\/a>"],[0,1,"<a href=\"Quote\/QuoteSubmitForApprovalListener.php.html#19\">App\\Listeners\\WorkOrder\\Quote\\QuoteSubmitForApprovalListener::__construct<\/a>"],[0,2,"<a href=\"Quote\/QuoteSubmitForApprovalListener.php.html#27\">App\\Listeners\\WorkOrder\\Quote\\QuoteSubmitForApprovalListener::handle<\/a>"],[0,1,"<a href=\"ResidentAvailabilityDeleteListener.php.html#20\">App\\Listeners\\WorkOrder\\ResidentAvailabilityDeleteListener::__construct<\/a>"],[0,1,"<a href=\"ResidentAvailabilityDeleteListener.php.html#28\">App\\Listeners\\WorkOrder\\ResidentAvailabilityDeleteListener::handle<\/a>"],[0,1,"<a href=\"Trip\/TripCanceledListener.php.html#23\">App\\Listeners\\WorkOrder\\Trip\\TripCanceledListener::__construct<\/a>"],[0,1,"<a href=\"Trip\/TripCanceledListener.php.html#31\">App\\Listeners\\WorkOrder\\Trip\\TripCanceledListener::handle<\/a>"],[0,3,"<a href=\"Trip\/TripCanceledListener.php.html#50\">App\\Listeners\\WorkOrder\\Trip\\TripCanceledListener::updateHealthScoreStatus<\/a>"],[0,1,"<a href=\"Trip\/TripEnRoutePausedListener.php.html#21\">App\\Listeners\\WorkOrder\\Trip\\TripEnRoutePausedListener::__construct<\/a>"],[0,1,"<a href=\"Trip\/TripEnRoutePausedListener.php.html#29\">App\\Listeners\\WorkOrder\\Trip\\TripEnRoutePausedListener::handle<\/a>"],[0,1,"<a href=\"Trip\/TripEnRouteResumeListener.php.html#21\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteResumeListener::__construct<\/a>"],[0,1,"<a href=\"Trip\/TripEnRouteResumeListener.php.html#29\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteResumeListener::handle<\/a>"],[0,1,"<a href=\"Trip\/TripEnRouteStartListener.php.html#24\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteStartListener::__construct<\/a>"],[0,3,"<a href=\"Trip\/TripEnRouteStartListener.php.html#32\">App\\Listeners\\WorkOrder\\Trip\\TripEnRouteStartListener::handle<\/a>"],[0,1,"<a href=\"Trip\/TripEndListener.php.html#24\">App\\Listeners\\WorkOrder\\Trip\\TripEndListener::__construct<\/a>"],[0,1,"<a href=\"Trip\/TripEndListener.php.html#32\">App\\Listeners\\WorkOrder\\Trip\\TripEndListener::handle<\/a>"],[0,3,"<a href=\"Trip\/TripEndListener.php.html#51\">App\\Listeners\\WorkOrder\\Trip\\TripEndListener::updateHealthScoreStatus<\/a>"],[0,1,"<a href=\"Trip\/TripPausedListener.php.html#21\">App\\Listeners\\WorkOrder\\Trip\\TripPausedListener::__construct<\/a>"],[0,1,"<a href=\"Trip\/TripPausedListener.php.html#29\">App\\Listeners\\WorkOrder\\Trip\\TripPausedListener::handle<\/a>"],[0,1,"<a href=\"Trip\/TripReScheduledListener.php.html#28\">App\\Listeners\\WorkOrder\\Trip\\TripReScheduledListener::__construct<\/a>"],[0,4,"<a href=\"Trip\/TripReScheduledListener.php.html#36\">App\\Listeners\\WorkOrder\\Trip\\TripReScheduledListener::handle<\/a>"],[0,5,"<a href=\"Trip\/TripReScheduledListener.php.html#95\">App\\Listeners\\WorkOrder\\Trip\\TripReScheduledListener::updateHealthScoreStatus<\/a>"],[0,1,"<a href=\"Trip\/TripResumeListener.php.html#18\">App\\Listeners\\WorkOrder\\Trip\\TripResumeListener::__construct<\/a>"],[0,1,"<a href=\"Trip\/TripResumeListener.php.html#26\">App\\Listeners\\WorkOrder\\Trip\\TripResumeListener::handle<\/a>"],[0,1,"<a href=\"Trip\/TripScheduleInProgressListener.php.html#18\">App\\Listeners\\WorkOrder\\Trip\\TripScheduleInProgressListener::__construct<\/a>"],[0,1,"<a href=\"Trip\/TripScheduleInProgressListener.php.html#26\">App\\Listeners\\WorkOrder\\Trip\\TripScheduleInProgressListener::handle<\/a>"],[0,1,"<a href=\"Trip\/TripScheduledListener.php.html#26\">App\\Listeners\\WorkOrder\\Trip\\TripScheduledListener::__construct<\/a>"],[0,7,"<a href=\"Trip\/TripScheduledListener.php.html#34\">App\\Listeners\\WorkOrder\\Trip\\TripScheduledListener::handle<\/a>"],[0,1,"<a href=\"Trip\/TripWorkingListener.php.html#21\">App\\Listeners\\WorkOrder\\Trip\\TripWorkingListener::__construct<\/a>"],[0,1,"<a href=\"Trip\/TripWorkingListener.php.html#29\">App\\Listeners\\WorkOrder\\Trip\\TripWorkingListener::handle<\/a>"],[0,1,"<a href=\"WorkOrderAddressUpdatedListener.php.html#17\">App\\Listeners\\WorkOrder\\WorkOrderAddressUpdatedListener::__construct<\/a>"],[0,4,"<a href=\"WorkOrderAddressUpdatedListener.php.html#25\">App\\Listeners\\WorkOrder\\WorkOrderAddressUpdatedListener::handle<\/a>"],[0,1,"<a href=\"WorkOrderDescriptionUpdatedListener.php.html#18\">App\\Listeners\\WorkOrder\\WorkOrderDescriptionUpdatedListener::__construct<\/a>"],[0,4,"<a href=\"WorkOrderDescriptionUpdatedListener.php.html#26\">App\\Listeners\\WorkOrder\\WorkOrderDescriptionUpdatedListener::handle<\/a>"],[0,1,"<a href=\"WorkOrderPausedListener.php.html#28\">App\\Listeners\\WorkOrder\\WorkOrderPausedListener::__construct<\/a>"],[0,10,"<a href=\"WorkOrderPausedListener.php.html#36\">App\\Listeners\\WorkOrder\\WorkOrderPausedListener::handle<\/a>"],[0,1,"<a href=\"WorkOrderReOpenListener.php.html#27\">App\\Listeners\\WorkOrder\\WorkOrderReOpenListener::__construct<\/a>"],[0,7,"<a href=\"WorkOrderReOpenListener.php.html#35\">App\\Listeners\\WorkOrder\\WorkOrderReOpenListener::handle<\/a>"],[0,1,"<a href=\"WorkOrderReScheduledListener.php.html#19\">App\\Listeners\\WorkOrder\\WorkOrderReScheduledListener::__construct<\/a>"],[0,1,"<a href=\"WorkOrderReScheduledListener.php.html#27\">App\\Listeners\\WorkOrder\\WorkOrderReScheduledListener::handle<\/a>"],[0,1,"<a href=\"WorkOrderResidentUpdatedListener.php.html#18\">App\\Listeners\\WorkOrder\\WorkOrderResidentUpdatedListener::__construct<\/a>"],[0,4,"<a href=\"WorkOrderResidentUpdatedListener.php.html#26\">App\\Listeners\\WorkOrder\\WorkOrderResidentUpdatedListener::handle<\/a>"],[0,1,"<a href=\"WorkOrderResolveListener.php.html#27\">App\\Listeners\\WorkOrder\\WorkOrderResolveListener::__construct<\/a>"],[0,7,"<a href=\"WorkOrderResolveListener.php.html#35\">App\\Listeners\\WorkOrder\\WorkOrderResolveListener::handle<\/a>"],[0,1,"<a href=\"WorkOrderSendToVendorListener.php.html#20\">App\\Listeners\\WorkOrder\\WorkOrderSendToVendorListener::__construct<\/a>"],[0,6,"<a href=\"WorkOrderSendToVendorListener.php.html#28\">App\\Listeners\\WorkOrder\\WorkOrderSendToVendorListener::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

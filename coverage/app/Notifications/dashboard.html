<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Notifications</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Notifications</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseNotification.php.html#10">App\Notifications\BaseNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeveloperAlertNotification.php.html#12">App\Notifications\DeveloperAlertNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/ServiceRequest/BaseServiceRequestNotification.php.html#8">App\Notifications\Resident\ServiceRequest\BaseServiceRequestNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/ServiceRequest/ResidentAvailabilityNotification.php.html#11">App\Notifications\Resident\ServiceRequest\ResidentAvailabilityNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/BaseWorkOrderNotification.php.html#9">App\Notifications\Resident\WorkOrder\BaseWorkOrderNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/EnRouteNotification.php.html#12">App\Notifications\Resident\WorkOrder\EnRouteNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/PausedNotification.php.html#11">App\Notifications\Resident\WorkOrder\PausedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/ResidentAvailabilityNotification.php.html#11">App\Notifications\Resident\WorkOrder\ResidentAvailabilityNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/ScheduledNotification.php.html#13">App\Notifications\Resident\WorkOrder\ScheduledNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseServiceRequestNotification.php.html#8">App\Notifications\ServiceRequest\BaseServiceRequestNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NoteCreatedNotification.php.html#14">App\Notifications\ServiceRequest\NoteCreatedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssignedNotification.php.html#12">App\Notifications\ServiceRequest\ServiceRequestAssignedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChangedNotification.php.html#14">App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#13">App\Notifications\WorkOrder\AssignedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/BaseWorkOrderNotification.php.html#8">App\Notifications\WorkOrder\BaseWorkOrderNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#13">App\Notifications\WorkOrder\DueDateChangedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#13">App\Notifications\WorkOrder\HealthScoreChangedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#13">App\Notifications\WorkOrder\NoteCreatedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#14">App\Notifications\WorkOrder\PriorityChangedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallAssignedNotification.php.html#13">App\Notifications\WorkOrder\ServiceCallAssignedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallRescheduledNotification.php.html#13">App\Notifications\WorkOrder\ServiceCallRescheduledNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#15">App\Notifications\WorkOrder\WorkOrderStateChangedNotification</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#15">App\Notifications\WorkOrder\WorkOrderStateChangedNotification</a></td><td class="text-right">506</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#13">App\Notifications\WorkOrder\AssignedNotification</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#13">App\Notifications\WorkOrder\NoteCreatedNotification</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#13">App\Notifications\WorkOrder\DueDateChangedNotification</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#14">App\Notifications\WorkOrder\PriorityChangedNotification</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#13">App\Notifications\WorkOrder\HealthScoreChangedNotification</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssignedNotification.php.html#12">App\Notifications\ServiceRequest\ServiceRequestAssignedNotification</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Resident/WorkOrder/ScheduledNotification.php.html#13">App\Notifications\Resident\WorkOrder\ScheduledNotification</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Resident/WorkOrder/EnRouteNotification.php.html#12">App\Notifications\Resident\WorkOrder\EnRouteNotification</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resident/WorkOrder/PausedNotification.php.html#11">App\Notifications\Resident\WorkOrder\PausedNotification</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseNotification.php.html#24"><abbr title="App\Notifications\BaseNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeveloperAlertNotification.php.html#24"><abbr title="App\Notifications\DeveloperAlertNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeveloperAlertNotification.php.html#32"><abbr title="App\Notifications\DeveloperAlertNotification::envelope">envelope</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeveloperAlertNotification.php.html#46"><abbr title="App\Notifications\DeveloperAlertNotification::content">content</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/ServiceRequest/BaseServiceRequestNotification.php.html#13"><abbr title="App\Notifications\Resident\ServiceRequest\BaseServiceRequestNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/ServiceRequest/ResidentAvailabilityNotification.php.html#13"><abbr title="App\Notifications\Resident\ServiceRequest\ResidentAvailabilityNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/ServiceRequest/ResidentAvailabilityNotification.php.html#25"><abbr title="App\Notifications\Resident\ServiceRequest\ResidentAvailabilityNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/ServiceRequest/ResidentAvailabilityNotification.php.html#30"><abbr title="App\Notifications\Resident\ServiceRequest\ResidentAvailabilityNotification::toTwilio">toTwilio</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/BaseWorkOrderNotification.php.html#14"><abbr title="App\Notifications\Resident\WorkOrder\BaseWorkOrderNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/EnRouteNotification.php.html#14"><abbr title="App\Notifications\Resident\WorkOrder\EnRouteNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/EnRouteNotification.php.html#21"><abbr title="App\Notifications\Resident\WorkOrder\EnRouteNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/EnRouteNotification.php.html#26"><abbr title="App\Notifications\Resident\WorkOrder\EnRouteNotification::toTwilio">toTwilio</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/PausedNotification.php.html#18"><abbr title="App\Notifications\Resident\WorkOrder\PausedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/PausedNotification.php.html#23"><abbr title="App\Notifications\Resident\WorkOrder\PausedNotification::toTwilio">toTwilio</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/ResidentAvailabilityNotification.php.html#13"><abbr title="App\Notifications\Resident\WorkOrder\ResidentAvailabilityNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/ResidentAvailabilityNotification.php.html#26"><abbr title="App\Notifications\Resident\WorkOrder\ResidentAvailabilityNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/ResidentAvailabilityNotification.php.html#31"><abbr title="App\Notifications\Resident\WorkOrder\ResidentAvailabilityNotification::toTwilio">toTwilio</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/ScheduledNotification.php.html#18"><abbr title="App\Notifications\Resident\WorkOrder\ScheduledNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/ScheduledNotification.php.html#25"><abbr title="App\Notifications\Resident\WorkOrder\ScheduledNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Resident/WorkOrder/ScheduledNotification.php.html#30"><abbr title="App\Notifications\Resident\WorkOrder\ScheduledNotification::toTwilio">toTwilio</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseServiceRequestNotification.php.html#17"><abbr title="App\Notifications\ServiceRequest\BaseServiceRequestNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseServiceRequestNotification.php.html#27"><abbr title="App\Notifications\ServiceRequest\BaseServiceRequestNotification::toMail">toMail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseServiceRequestNotification.php.html#39"><abbr title="App\Notifications\ServiceRequest\BaseServiceRequestNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NoteCreatedNotification.php.html#21"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NoteCreatedNotification.php.html#28"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NoteCreatedNotification.php.html#36"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NoteCreatedNotification.php.html#45"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NoteCreatedNotification.php.html#53"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NoteCreatedNotification.php.html#85"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NoteCreatedNotification.php.html#95"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NoteCreatedNotification.php.html#102"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssignedNotification.php.html#19"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssignedNotification.php.html#26"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssignedNotification.php.html#34"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssignedNotification.php.html#45"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssignedNotification.php.html#53"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssignedNotification.php.html#92"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssignedNotification.php.html#102"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssignedNotification.php.html#109"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChangedNotification.php.html#21"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChangedNotification.php.html#28"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChangedNotification.php.html#36"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChangedNotification.php.html#48"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChangedNotification.php.html#56"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChangedNotification.php.html#87"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChangedNotification.php.html#97"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChangedNotification.php.html#104"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#24"><abbr title="App\Notifications\WorkOrder\AssignedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#31"><abbr title="App\Notifications\WorkOrder\AssignedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#39"><abbr title="App\Notifications\WorkOrder\AssignedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#53"><abbr title="App\Notifications\WorkOrder\AssignedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#61"><abbr title="App\Notifications\WorkOrder\AssignedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#95"><abbr title="App\Notifications\WorkOrder\AssignedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#105"><abbr title="App\Notifications\WorkOrder\AssignedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#112"><abbr title="App\Notifications\WorkOrder\AssignedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#117"><abbr title="App\Notifications\WorkOrder\AssignedNotification::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#147"><abbr title="App\Notifications\WorkOrder\AssignedNotification::getPropertyAddress">getPropertyAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#159"><abbr title="App\Notifications\WorkOrder\AssignedNotification::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#176"><abbr title="App\Notifications\WorkOrder\AssignedNotification::getUser">getUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/BaseWorkOrderNotification.php.html#17"><abbr title="App\Notifications\WorkOrder\BaseWorkOrderNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/BaseWorkOrderNotification.php.html#27"><abbr title="App\Notifications\WorkOrder\BaseWorkOrderNotification::toMail">toMail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/BaseWorkOrderNotification.php.html#39"><abbr title="App\Notifications\WorkOrder\BaseWorkOrderNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#24"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#31"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#39"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#48"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#56"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#88"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#98"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#105"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#110"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#131"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::getPropertyAddress">getPropertyAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#143"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::getUser">getUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#26"><abbr title="App\Notifications\WorkOrder\HealthScoreChangedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#33"><abbr title="App\Notifications\WorkOrder\HealthScoreChangedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#41"><abbr title="App\Notifications\WorkOrder\HealthScoreChangedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#50"><abbr title="App\Notifications\WorkOrder\HealthScoreChangedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#58"><abbr title="App\Notifications\WorkOrder\HealthScoreChangedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#83"><abbr title="App\Notifications\WorkOrder\HealthScoreChangedNotification::getEventAttributes">getEventAttributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#103"><abbr title="App\Notifications\WorkOrder\HealthScoreChangedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#113"><abbr title="App\Notifications\WorkOrder\HealthScoreChangedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#120"><abbr title="App\Notifications\WorkOrder\HealthScoreChangedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#24"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#31"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#36"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::getWorkOrderNote">getWorkOrderNote</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#66"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::getUser">getUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#81"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::getPropertyAddress">getPropertyAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#94"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#115"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#128"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#136"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#169"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#179"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#186"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#25"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#32"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#40"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#55"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#63"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#95"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#105"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#112"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#117"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#137"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::getPropertyAddress">getPropertyAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#149"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::getUser">getUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallAssignedNotification.php.html#20"><abbr title="App\Notifications\WorkOrder\ServiceCallAssignedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallAssignedNotification.php.html#27"><abbr title="App\Notifications\WorkOrder\ServiceCallAssignedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallAssignedNotification.php.html#35"><abbr title="App\Notifications\WorkOrder\ServiceCallAssignedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallAssignedNotification.php.html#46"><abbr title="App\Notifications\WorkOrder\ServiceCallAssignedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallAssignedNotification.php.html#54"><abbr title="App\Notifications\WorkOrder\ServiceCallAssignedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallAssignedNotification.php.html#87"><abbr title="App\Notifications\WorkOrder\ServiceCallAssignedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallAssignedNotification.php.html#97"><abbr title="App\Notifications\WorkOrder\ServiceCallAssignedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallAssignedNotification.php.html#104"><abbr title="App\Notifications\WorkOrder\ServiceCallAssignedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallRescheduledNotification.php.html#20"><abbr title="App\Notifications\WorkOrder\ServiceCallRescheduledNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallRescheduledNotification.php.html#27"><abbr title="App\Notifications\WorkOrder\ServiceCallRescheduledNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallRescheduledNotification.php.html#35"><abbr title="App\Notifications\WorkOrder\ServiceCallRescheduledNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallRescheduledNotification.php.html#46"><abbr title="App\Notifications\WorkOrder\ServiceCallRescheduledNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallRescheduledNotification.php.html#54"><abbr title="App\Notifications\WorkOrder\ServiceCallRescheduledNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallRescheduledNotification.php.html#87"><abbr title="App\Notifications\WorkOrder\ServiceCallRescheduledNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallRescheduledNotification.php.html#97"><abbr title="App\Notifications\WorkOrder\ServiceCallRescheduledNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ServiceCallRescheduledNotification.php.html#104"><abbr title="App\Notifications\WorkOrder\ServiceCallRescheduledNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#33"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#40"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#48"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#60"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#68"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#100"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#110"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#117"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#127"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::getEventAttributes">getEventAttributes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#173"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#194"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::getPropertyAddress">getPropertyAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#206"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#127"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::getEventAttributes">getEventAttributes</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Resident/WorkOrder/ScheduledNotification.php.html#30"><abbr title="App\Notifications\Resident\WorkOrder\ScheduledNotification::toTwilio">toTwilio</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Resident/WorkOrder/EnRouteNotification.php.html#26"><abbr title="App\Notifications\Resident\WorkOrder\EnRouteNotification::toTwilio">toTwilio</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#147"><abbr title="App\Notifications\WorkOrder\AssignedNotification::getPropertyAddress">getPropertyAddress</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#159"><abbr title="App\Notifications\WorkOrder\AssignedNotification::getCategory">getCategory</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#131"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::getPropertyAddress">getPropertyAddress</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#81"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::getPropertyAddress">getPropertyAddress</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#94"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::getCategory">getCategory</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#137"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::getPropertyAddress">getPropertyAddress</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#194"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::getPropertyAddress">getPropertyAddress</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Resident/WorkOrder/PausedNotification.php.html#23"><abbr title="App\Notifications\Resident\WorkOrder\PausedNotification::toTwilio">toTwilio</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssignedNotification.php.html#53"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#117"><abbr title="App\Notifications\WorkOrder\AssignedNotification::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/AssignedNotification.php.html#176"><abbr title="App\Notifications\WorkOrder\AssignedNotification::getUser">getUser</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#110"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/DueDateChangedNotification.php.html#143"><abbr title="App\Notifications\WorkOrder\DueDateChangedNotification::getUser">getUser</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/HealthScoreChangedNotification.php.html#83"><abbr title="App\Notifications\WorkOrder\HealthScoreChangedNotification::getEventAttributes">getEventAttributes</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#36"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::getWorkOrderNote">getWorkOrderNote</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/NoteCreatedNotification.php.html#66"><abbr title="App\Notifications\WorkOrder\NoteCreatedNotification::getUser">getUser</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#117"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/PriorityChangedNotification.php.html#149"><abbr title="App\Notifications\WorkOrder\PriorityChangedNotification::getUser">getUser</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#173"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChangedNotification.php.html#206"><abbr title="App\Notifications\WorkOrder\WorkOrderStateChangedNotification::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Thu Jun 26 15:40:22 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([22,0,0,0,0,0,0,0,0,0,0,1], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([133,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BaseNotification.php.html#10\">App\\Notifications\\BaseNotification<\/a>"],[0,3,"<a href=\"DeveloperAlertNotification.php.html#12\">App\\Notifications\\DeveloperAlertNotification<\/a>"],[100,0,"<a href=\"Resident\/BaseResidentNotification.php.html#7\">App\\Notifications\\Resident\\BaseResidentNotification<\/a>"],[0,1,"<a href=\"Resident\/ServiceRequest\/BaseServiceRequestNotification.php.html#8\">App\\Notifications\\Resident\\ServiceRequest\\BaseServiceRequestNotification<\/a>"],[0,3,"<a href=\"Resident\/ServiceRequest\/ResidentAvailabilityNotification.php.html#11\">App\\Notifications\\Resident\\ServiceRequest\\ResidentAvailabilityNotification<\/a>"],[0,1,"<a href=\"Resident\/WorkOrder\/BaseWorkOrderNotification.php.html#9\">App\\Notifications\\Resident\\WorkOrder\\BaseWorkOrderNotification<\/a>"],[0,6,"<a href=\"Resident\/WorkOrder\/EnRouteNotification.php.html#12\">App\\Notifications\\Resident\\WorkOrder\\EnRouteNotification<\/a>"],[0,3,"<a href=\"Resident\/WorkOrder\/PausedNotification.php.html#11\">App\\Notifications\\Resident\\WorkOrder\\PausedNotification<\/a>"],[0,3,"<a href=\"Resident\/WorkOrder\/ResidentAvailabilityNotification.php.html#11\">App\\Notifications\\Resident\\WorkOrder\\ResidentAvailabilityNotification<\/a>"],[0,8,"<a href=\"Resident\/WorkOrder\/ScheduledNotification.php.html#13\">App\\Notifications\\Resident\\WorkOrder\\ScheduledNotification<\/a>"],[0,3,"<a href=\"ServiceRequest\/BaseServiceRequestNotification.php.html#8\">App\\Notifications\\ServiceRequest\\BaseServiceRequestNotification<\/a>"],[0,8,"<a href=\"ServiceRequest\/NoteCreatedNotification.php.html#14\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification<\/a>"],[0,9,"<a href=\"ServiceRequest\/ServiceRequestAssignedNotification.php.html#12\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification<\/a>"],[0,8,"<a href=\"ServiceRequest\/ServiceRequestPriorityChangedNotification.php.html#14\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification<\/a>"],[0,18,"<a href=\"WorkOrder\/AssignedNotification.php.html#13\">App\\Notifications\\WorkOrder\\AssignedNotification<\/a>"],[0,3,"<a href=\"WorkOrder\/BaseWorkOrderNotification.php.html#8\">App\\Notifications\\WorkOrder\\BaseWorkOrderNotification<\/a>"],[0,15,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#13\">App\\Notifications\\WorkOrder\\DueDateChangedNotification<\/a>"],[0,10,"<a href=\"WorkOrder\/HealthScoreChangedNotification.php.html#13\">App\\Notifications\\WorkOrder\\HealthScoreChangedNotification<\/a>"],[0,18,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#13\">App\\Notifications\\WorkOrder\\NoteCreatedNotification<\/a>"],[0,15,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#14\">App\\Notifications\\WorkOrder\\PriorityChangedNotification<\/a>"],[0,8,"<a href=\"WorkOrder\/ServiceCallAssignedNotification.php.html#13\">App\\Notifications\\WorkOrder\\ServiceCallAssignedNotification<\/a>"],[0,8,"<a href=\"WorkOrder\/ServiceCallRescheduledNotification.php.html#13\">App\\Notifications\\WorkOrder\\ServiceCallRescheduledNotification<\/a>"],[0,22,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#15\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BaseNotification.php.html#24\">App\\Notifications\\BaseNotification::toArray<\/a>"],[0,1,"<a href=\"DeveloperAlertNotification.php.html#24\">App\\Notifications\\DeveloperAlertNotification::__construct<\/a>"],[0,1,"<a href=\"DeveloperAlertNotification.php.html#32\">App\\Notifications\\DeveloperAlertNotification::envelope<\/a>"],[0,1,"<a href=\"DeveloperAlertNotification.php.html#46\">App\\Notifications\\DeveloperAlertNotification::content<\/a>"],[0,1,"<a href=\"Resident\/ServiceRequest\/BaseServiceRequestNotification.php.html#13\">App\\Notifications\\Resident\\ServiceRequest\\BaseServiceRequestNotification::__construct<\/a>"],[0,1,"<a href=\"Resident\/ServiceRequest\/ResidentAvailabilityNotification.php.html#13\">App\\Notifications\\Resident\\ServiceRequest\\ResidentAvailabilityNotification::__construct<\/a>"],[0,1,"<a href=\"Resident\/ServiceRequest\/ResidentAvailabilityNotification.php.html#25\">App\\Notifications\\Resident\\ServiceRequest\\ResidentAvailabilityNotification::via<\/a>"],[0,1,"<a href=\"Resident\/ServiceRequest\/ResidentAvailabilityNotification.php.html#30\">App\\Notifications\\Resident\\ServiceRequest\\ResidentAvailabilityNotification::toTwilio<\/a>"],[0,1,"<a href=\"Resident\/WorkOrder\/BaseWorkOrderNotification.php.html#14\">App\\Notifications\\Resident\\WorkOrder\\BaseWorkOrderNotification::__construct<\/a>"],[0,1,"<a href=\"Resident\/WorkOrder\/EnRouteNotification.php.html#14\">App\\Notifications\\Resident\\WorkOrder\\EnRouteNotification::__construct<\/a>"],[0,1,"<a href=\"Resident\/WorkOrder\/EnRouteNotification.php.html#21\">App\\Notifications\\Resident\\WorkOrder\\EnRouteNotification::via<\/a>"],[0,4,"<a href=\"Resident\/WorkOrder\/EnRouteNotification.php.html#26\">App\\Notifications\\Resident\\WorkOrder\\EnRouteNotification::toTwilio<\/a>"],[0,1,"<a href=\"Resident\/WorkOrder\/PausedNotification.php.html#18\">App\\Notifications\\Resident\\WorkOrder\\PausedNotification::via<\/a>"],[0,2,"<a href=\"Resident\/WorkOrder\/PausedNotification.php.html#23\">App\\Notifications\\Resident\\WorkOrder\\PausedNotification::toTwilio<\/a>"],[0,1,"<a href=\"Resident\/WorkOrder\/ResidentAvailabilityNotification.php.html#13\">App\\Notifications\\Resident\\WorkOrder\\ResidentAvailabilityNotification::__construct<\/a>"],[0,1,"<a href=\"Resident\/WorkOrder\/ResidentAvailabilityNotification.php.html#26\">App\\Notifications\\Resident\\WorkOrder\\ResidentAvailabilityNotification::via<\/a>"],[0,1,"<a href=\"Resident\/WorkOrder\/ResidentAvailabilityNotification.php.html#31\">App\\Notifications\\Resident\\WorkOrder\\ResidentAvailabilityNotification::toTwilio<\/a>"],[0,1,"<a href=\"Resident\/WorkOrder\/ScheduledNotification.php.html#18\">App\\Notifications\\Resident\\WorkOrder\\ScheduledNotification::__construct<\/a>"],[0,1,"<a href=\"Resident\/WorkOrder\/ScheduledNotification.php.html#25\">App\\Notifications\\Resident\\WorkOrder\\ScheduledNotification::via<\/a>"],[0,6,"<a href=\"Resident\/WorkOrder\/ScheduledNotification.php.html#30\">App\\Notifications\\Resident\\WorkOrder\\ScheduledNotification::toTwilio<\/a>"],[0,1,"<a href=\"ServiceRequest\/BaseServiceRequestNotification.php.html#17\">App\\Notifications\\ServiceRequest\\BaseServiceRequestNotification::via<\/a>"],[0,1,"<a href=\"ServiceRequest\/BaseServiceRequestNotification.php.html#27\">App\\Notifications\\ServiceRequest\\BaseServiceRequestNotification::toMail<\/a>"],[0,1,"<a href=\"ServiceRequest\/BaseServiceRequestNotification.php.html#39\">App\\Notifications\\ServiceRequest\\BaseServiceRequestNotification::toArray<\/a>"],[0,1,"<a href=\"ServiceRequest\/NoteCreatedNotification.php.html#21\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/NoteCreatedNotification.php.html#28\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::via<\/a>"],[0,1,"<a href=\"ServiceRequest\/NoteCreatedNotification.php.html#36\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::toDatabase<\/a>"],[0,1,"<a href=\"ServiceRequest\/NoteCreatedNotification.php.html#45\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::databaseType<\/a>"],[0,1,"<a href=\"ServiceRequest\/NoteCreatedNotification.php.html#53\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"ServiceRequest\/NoteCreatedNotification.php.html#85\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::broadcastType<\/a>"],[0,1,"<a href=\"ServiceRequest\/NoteCreatedNotification.php.html#95\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::toArray<\/a>"],[0,1,"<a href=\"ServiceRequest\/NoteCreatedNotification.php.html#102\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssignedNotification.php.html#19\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssignedNotification.php.html#26\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::via<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssignedNotification.php.html#34\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::toDatabase<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssignedNotification.php.html#45\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::databaseType<\/a>"],[0,2,"<a href=\"ServiceRequest\/ServiceRequestAssignedNotification.php.html#53\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssignedNotification.php.html#92\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::broadcastType<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssignedNotification.php.html#102\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::toArray<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssignedNotification.php.html#109\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestPriorityChangedNotification.php.html#21\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestPriorityChangedNotification.php.html#28\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::via<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestPriorityChangedNotification.php.html#36\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::toDatabase<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestPriorityChangedNotification.php.html#48\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::databaseType<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestPriorityChangedNotification.php.html#56\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestPriorityChangedNotification.php.html#87\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::broadcastType<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestPriorityChangedNotification.php.html#97\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::toArray<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestPriorityChangedNotification.php.html#104\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/AssignedNotification.php.html#24\">App\\Notifications\\WorkOrder\\AssignedNotification::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/AssignedNotification.php.html#31\">App\\Notifications\\WorkOrder\\AssignedNotification::via<\/a>"],[0,1,"<a href=\"WorkOrder\/AssignedNotification.php.html#39\">App\\Notifications\\WorkOrder\\AssignedNotification::toDatabase<\/a>"],[0,1,"<a href=\"WorkOrder\/AssignedNotification.php.html#53\">App\\Notifications\\WorkOrder\\AssignedNotification::databaseType<\/a>"],[0,1,"<a href=\"WorkOrder\/AssignedNotification.php.html#61\">App\\Notifications\\WorkOrder\\AssignedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"WorkOrder\/AssignedNotification.php.html#95\">App\\Notifications\\WorkOrder\\AssignedNotification::broadcastType<\/a>"],[0,1,"<a href=\"WorkOrder\/AssignedNotification.php.html#105\">App\\Notifications\\WorkOrder\\AssignedNotification::toArray<\/a>"],[0,1,"<a href=\"WorkOrder\/AssignedNotification.php.html#112\">App\\Notifications\\WorkOrder\\AssignedNotification::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/AssignedNotification.php.html#117\">App\\Notifications\\WorkOrder\\AssignedNotification::getWorkOrder<\/a>"],[0,3,"<a href=\"WorkOrder\/AssignedNotification.php.html#147\">App\\Notifications\\WorkOrder\\AssignedNotification::getPropertyAddress<\/a>"],[0,3,"<a href=\"WorkOrder\/AssignedNotification.php.html#159\">App\\Notifications\\WorkOrder\\AssignedNotification::getCategory<\/a>"],[0,2,"<a href=\"WorkOrder\/AssignedNotification.php.html#176\">App\\Notifications\\WorkOrder\\AssignedNotification::getUser<\/a>"],[0,1,"<a href=\"WorkOrder\/BaseWorkOrderNotification.php.html#17\">App\\Notifications\\WorkOrder\\BaseWorkOrderNotification::via<\/a>"],[0,1,"<a href=\"WorkOrder\/BaseWorkOrderNotification.php.html#27\">App\\Notifications\\WorkOrder\\BaseWorkOrderNotification::toMail<\/a>"],[0,1,"<a href=\"WorkOrder\/BaseWorkOrderNotification.php.html#39\">App\\Notifications\\WorkOrder\\BaseWorkOrderNotification::toArray<\/a>"],[0,1,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#24\">App\\Notifications\\WorkOrder\\DueDateChangedNotification::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#31\">App\\Notifications\\WorkOrder\\DueDateChangedNotification::via<\/a>"],[0,1,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#39\">App\\Notifications\\WorkOrder\\DueDateChangedNotification::toDatabase<\/a>"],[0,1,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#48\">App\\Notifications\\WorkOrder\\DueDateChangedNotification::databaseType<\/a>"],[0,1,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#56\">App\\Notifications\\WorkOrder\\DueDateChangedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#88\">App\\Notifications\\WorkOrder\\DueDateChangedNotification::broadcastType<\/a>"],[0,1,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#98\">App\\Notifications\\WorkOrder\\DueDateChangedNotification::toArray<\/a>"],[0,1,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#105\">App\\Notifications\\WorkOrder\\DueDateChangedNotification::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#110\">App\\Notifications\\WorkOrder\\DueDateChangedNotification::getWorkOrder<\/a>"],[0,3,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#131\">App\\Notifications\\WorkOrder\\DueDateChangedNotification::getPropertyAddress<\/a>"],[0,2,"<a href=\"WorkOrder\/DueDateChangedNotification.php.html#143\">App\\Notifications\\WorkOrder\\DueDateChangedNotification::getUser<\/a>"],[0,1,"<a href=\"WorkOrder\/HealthScoreChangedNotification.php.html#26\">App\\Notifications\\WorkOrder\\HealthScoreChangedNotification::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/HealthScoreChangedNotification.php.html#33\">App\\Notifications\\WorkOrder\\HealthScoreChangedNotification::via<\/a>"],[0,1,"<a href=\"WorkOrder\/HealthScoreChangedNotification.php.html#41\">App\\Notifications\\WorkOrder\\HealthScoreChangedNotification::toDatabase<\/a>"],[0,1,"<a href=\"WorkOrder\/HealthScoreChangedNotification.php.html#50\">App\\Notifications\\WorkOrder\\HealthScoreChangedNotification::databaseType<\/a>"],[0,1,"<a href=\"WorkOrder\/HealthScoreChangedNotification.php.html#58\">App\\Notifications\\WorkOrder\\HealthScoreChangedNotification::toBroadcast<\/a>"],[0,2,"<a href=\"WorkOrder\/HealthScoreChangedNotification.php.html#83\">App\\Notifications\\WorkOrder\\HealthScoreChangedNotification::getEventAttributes<\/a>"],[0,1,"<a href=\"WorkOrder\/HealthScoreChangedNotification.php.html#103\">App\\Notifications\\WorkOrder\\HealthScoreChangedNotification::broadcastType<\/a>"],[0,1,"<a href=\"WorkOrder\/HealthScoreChangedNotification.php.html#113\">App\\Notifications\\WorkOrder\\HealthScoreChangedNotification::toArray<\/a>"],[0,1,"<a href=\"WorkOrder\/HealthScoreChangedNotification.php.html#120\">App\\Notifications\\WorkOrder\\HealthScoreChangedNotification::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#24\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#31\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::via<\/a>"],[0,2,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#36\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::getWorkOrderNote<\/a>"],[0,2,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#66\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::getUser<\/a>"],[0,3,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#81\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::getPropertyAddress<\/a>"],[0,3,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#94\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::getCategory<\/a>"],[0,1,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#115\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::toDatabase<\/a>"],[0,1,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#128\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::databaseType<\/a>"],[0,1,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#136\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#169\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::broadcastType<\/a>"],[0,1,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#179\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::toArray<\/a>"],[0,1,"<a href=\"WorkOrder\/NoteCreatedNotification.php.html#186\">App\\Notifications\\WorkOrder\\NoteCreatedNotification::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#25\">App\\Notifications\\WorkOrder\\PriorityChangedNotification::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#32\">App\\Notifications\\WorkOrder\\PriorityChangedNotification::via<\/a>"],[0,1,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#40\">App\\Notifications\\WorkOrder\\PriorityChangedNotification::toDatabase<\/a>"],[0,1,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#55\">App\\Notifications\\WorkOrder\\PriorityChangedNotification::databaseType<\/a>"],[0,1,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#63\">App\\Notifications\\WorkOrder\\PriorityChangedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#95\">App\\Notifications\\WorkOrder\\PriorityChangedNotification::broadcastType<\/a>"],[0,1,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#105\">App\\Notifications\\WorkOrder\\PriorityChangedNotification::toArray<\/a>"],[0,1,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#112\">App\\Notifications\\WorkOrder\\PriorityChangedNotification::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#117\">App\\Notifications\\WorkOrder\\PriorityChangedNotification::getWorkOrder<\/a>"],[0,3,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#137\">App\\Notifications\\WorkOrder\\PriorityChangedNotification::getPropertyAddress<\/a>"],[0,2,"<a href=\"WorkOrder\/PriorityChangedNotification.php.html#149\">App\\Notifications\\WorkOrder\\PriorityChangedNotification::getUser<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallAssignedNotification.php.html#20\">App\\Notifications\\WorkOrder\\ServiceCallAssignedNotification::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallAssignedNotification.php.html#27\">App\\Notifications\\WorkOrder\\ServiceCallAssignedNotification::via<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallAssignedNotification.php.html#35\">App\\Notifications\\WorkOrder\\ServiceCallAssignedNotification::toDatabase<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallAssignedNotification.php.html#46\">App\\Notifications\\WorkOrder\\ServiceCallAssignedNotification::databaseType<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallAssignedNotification.php.html#54\">App\\Notifications\\WorkOrder\\ServiceCallAssignedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallAssignedNotification.php.html#87\">App\\Notifications\\WorkOrder\\ServiceCallAssignedNotification::broadcastType<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallAssignedNotification.php.html#97\">App\\Notifications\\WorkOrder\\ServiceCallAssignedNotification::toArray<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallAssignedNotification.php.html#104\">App\\Notifications\\WorkOrder\\ServiceCallAssignedNotification::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallRescheduledNotification.php.html#20\">App\\Notifications\\WorkOrder\\ServiceCallRescheduledNotification::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallRescheduledNotification.php.html#27\">App\\Notifications\\WorkOrder\\ServiceCallRescheduledNotification::via<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallRescheduledNotification.php.html#35\">App\\Notifications\\WorkOrder\\ServiceCallRescheduledNotification::toDatabase<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallRescheduledNotification.php.html#46\">App\\Notifications\\WorkOrder\\ServiceCallRescheduledNotification::databaseType<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallRescheduledNotification.php.html#54\">App\\Notifications\\WorkOrder\\ServiceCallRescheduledNotification::toBroadcast<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallRescheduledNotification.php.html#87\">App\\Notifications\\WorkOrder\\ServiceCallRescheduledNotification::broadcastType<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallRescheduledNotification.php.html#97\">App\\Notifications\\WorkOrder\\ServiceCallRescheduledNotification::toArray<\/a>"],[0,1,"<a href=\"WorkOrder\/ServiceCallRescheduledNotification.php.html#104\">App\\Notifications\\WorkOrder\\ServiceCallRescheduledNotification::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#33\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#40\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::via<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#48\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::toDatabase<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#60\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::databaseType<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#68\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#100\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::broadcastType<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#110\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::toArray<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#117\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::broadcastAs<\/a>"],[0,7,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#127\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::getEventAttributes<\/a>"],[0,2,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#173\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::getWorkOrder<\/a>"],[0,3,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#194\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::getPropertyAddress<\/a>"],[0,2,"<a href=\"WorkOrder\/WorkOrderStateChangedNotification.php.html#206\">App\\Notifications\\WorkOrder\\WorkOrderStateChangedNotification::getWorkOrderActivityLog<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

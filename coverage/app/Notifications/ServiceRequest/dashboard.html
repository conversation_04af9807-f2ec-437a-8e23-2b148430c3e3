<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Notifications/ServiceRequest</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Notifications</a></li>
         <li class="breadcrumb-item"><a href="index.html">ServiceRequest</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseServiceRequestNotification.php.html#8">App\Notifications\ServiceRequest\BaseServiceRequestNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NoteCreatedNotification.php.html#14">App\Notifications\ServiceRequest\NoteCreatedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignedNotification.php.html#12">App\Notifications\ServiceRequest\ServiceRequestAssignedNotification</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChangedNotification.php.html#14">App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ServiceRequestAssignedNotification.php.html#12">App\Notifications\ServiceRequest\ServiceRequestAssignedNotification</a></td><td class="text-right">90</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseServiceRequestNotification.php.html#17"><abbr title="App\Notifications\ServiceRequest\BaseServiceRequestNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseServiceRequestNotification.php.html#27"><abbr title="App\Notifications\ServiceRequest\BaseServiceRequestNotification::toMail">toMail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseServiceRequestNotification.php.html#39"><abbr title="App\Notifications\ServiceRequest\BaseServiceRequestNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NoteCreatedNotification.php.html#21"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NoteCreatedNotification.php.html#28"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NoteCreatedNotification.php.html#36"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NoteCreatedNotification.php.html#45"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NoteCreatedNotification.php.html#53"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NoteCreatedNotification.php.html#85"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NoteCreatedNotification.php.html#95"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NoteCreatedNotification.php.html#102"><abbr title="App\Notifications\ServiceRequest\NoteCreatedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignedNotification.php.html#19"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignedNotification.php.html#26"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignedNotification.php.html#34"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignedNotification.php.html#45"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignedNotification.php.html#53"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignedNotification.php.html#92"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignedNotification.php.html#102"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestAssignedNotification.php.html#109"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChangedNotification.php.html#21"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChangedNotification.php.html#28"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::via">via</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChangedNotification.php.html#36"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::toDatabase">toDatabase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChangedNotification.php.html#48"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::databaseType">databaseType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChangedNotification.php.html#56"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChangedNotification.php.html#87"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::broadcastType">broadcastType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChangedNotification.php.html#97"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestPriorityChangedNotification.php.html#104"><abbr title="App\Notifications\ServiceRequest\ServiceRequestPriorityChangedNotification::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ServiceRequestAssignedNotification.php.html#53"><abbr title="App\Notifications\ServiceRequest\ServiceRequestAssignedNotification::toBroadcast">toBroadcast</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([4,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([27,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,3,"<a href=\"BaseServiceRequestNotification.php.html#8\">App\\Notifications\\ServiceRequest\\BaseServiceRequestNotification<\/a>"],[0,8,"<a href=\"NoteCreatedNotification.php.html#14\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification<\/a>"],[0,9,"<a href=\"ServiceRequestAssignedNotification.php.html#12\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification<\/a>"],[0,8,"<a href=\"ServiceRequestPriorityChangedNotification.php.html#14\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BaseServiceRequestNotification.php.html#17\">App\\Notifications\\ServiceRequest\\BaseServiceRequestNotification::via<\/a>"],[0,1,"<a href=\"BaseServiceRequestNotification.php.html#27\">App\\Notifications\\ServiceRequest\\BaseServiceRequestNotification::toMail<\/a>"],[0,1,"<a href=\"BaseServiceRequestNotification.php.html#39\">App\\Notifications\\ServiceRequest\\BaseServiceRequestNotification::toArray<\/a>"],[0,1,"<a href=\"NoteCreatedNotification.php.html#21\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::__construct<\/a>"],[0,1,"<a href=\"NoteCreatedNotification.php.html#28\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::via<\/a>"],[0,1,"<a href=\"NoteCreatedNotification.php.html#36\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::toDatabase<\/a>"],[0,1,"<a href=\"NoteCreatedNotification.php.html#45\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::databaseType<\/a>"],[0,1,"<a href=\"NoteCreatedNotification.php.html#53\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"NoteCreatedNotification.php.html#85\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::broadcastType<\/a>"],[0,1,"<a href=\"NoteCreatedNotification.php.html#95\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::toArray<\/a>"],[0,1,"<a href=\"NoteCreatedNotification.php.html#102\">App\\Notifications\\ServiceRequest\\NoteCreatedNotification::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestAssignedNotification.php.html#19\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestAssignedNotification.php.html#26\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::via<\/a>"],[0,1,"<a href=\"ServiceRequestAssignedNotification.php.html#34\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::toDatabase<\/a>"],[0,1,"<a href=\"ServiceRequestAssignedNotification.php.html#45\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::databaseType<\/a>"],[0,2,"<a href=\"ServiceRequestAssignedNotification.php.html#53\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"ServiceRequestAssignedNotification.php.html#92\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::broadcastType<\/a>"],[0,1,"<a href=\"ServiceRequestAssignedNotification.php.html#102\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::toArray<\/a>"],[0,1,"<a href=\"ServiceRequestAssignedNotification.php.html#109\">App\\Notifications\\ServiceRequest\\ServiceRequestAssignedNotification::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestPriorityChangedNotification.php.html#21\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::__construct<\/a>"],[0,1,"<a href=\"ServiceRequestPriorityChangedNotification.php.html#28\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::via<\/a>"],[0,1,"<a href=\"ServiceRequestPriorityChangedNotification.php.html#36\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::toDatabase<\/a>"],[0,1,"<a href=\"ServiceRequestPriorityChangedNotification.php.html#48\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::databaseType<\/a>"],[0,1,"<a href=\"ServiceRequestPriorityChangedNotification.php.html#56\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::toBroadcast<\/a>"],[0,1,"<a href=\"ServiceRequestPriorityChangedNotification.php.html#87\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::broadcastType<\/a>"],[0,1,"<a href=\"ServiceRequestPriorityChangedNotification.php.html#97\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::toArray<\/a>"],[0,1,"<a href=\"ServiceRequestPriorityChangedNotification.php.html#104\">App\\Notifications\\ServiceRequest\\ServiceRequestPriorityChangedNotification::broadcastAs<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

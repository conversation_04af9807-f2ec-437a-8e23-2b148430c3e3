<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Events/Issue</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Events</a></li>
         <li class="breadcrumb-item"><a href="index.html">Issue</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseIssueEvent.php.html#13">App\Events\Issue\BaseIssueEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDeletedIssueRestored.php.html#14">App\Events\Issue\ServiceRequestDeletedIssueRestored</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueCanceled.php.html#14">App\Events\Issue\ServiceRequestIssueCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueCreated.php.html#14">App\Events\Issue\ServiceRequestIssueCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueDeleted.php.html#15">App\Events\Issue\ServiceRequestIssueDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueRestored.php.html#14">App\Events\Issue\ServiceRequestIssueRestored</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueUnassigned.php.html#14">App\Events\Issue\ServiceRequestIssueUnassigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueUpdated.php.html#14">App\Events\Issue\ServiceRequestIssueUpdated</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BaseIssueEvent.php.html#20"><abbr title="App\Events\Issue\BaseIssueEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseIssueEvent.php.html#25"><abbr title="App\Events\Issue\BaseIssueEvent::getIssueDetails">getIssueDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseIssueEvent.php.html#51"><abbr title="App\Events\Issue\BaseIssueEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseIssueEvent.php.html#61"><abbr title="App\Events\Issue\BaseIssueEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BaseIssueEvent.php.html#77"><abbr title="App\Events\Issue\BaseIssueEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestDeletedIssueRestored.php.html#21"><abbr title="App\Events\Issue\ServiceRequestDeletedIssueRestored::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueCanceled.php.html#21"><abbr title="App\Events\Issue\ServiceRequestIssueCanceled::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueCreated.php.html#21"><abbr title="App\Events\Issue\ServiceRequestIssueCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueDeleted.php.html#24"><abbr title="App\Events\Issue\ServiceRequestIssueDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueDeleted.php.html#34"><abbr title="App\Events\Issue\ServiceRequestIssueDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueDeleted.php.html#45"><abbr title="App\Events\Issue\ServiceRequestIssueDeleted::getIssueDetails">getIssueDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueRestored.php.html#21"><abbr title="App\Events\Issue\ServiceRequestIssueRestored::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueUnassigned.php.html#21"><abbr title="App\Events\Issue\ServiceRequestIssueUnassigned::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestIssueUpdated.php.html#21"><abbr title="App\Events\Issue\ServiceRequestIssueUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:22:08 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([8,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([14,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"BaseIssueEvent.php.html#13\">App\\Events\\Issue\\BaseIssueEvent<\/a>"],[0,1,"<a href=\"ServiceRequestDeletedIssueRestored.php.html#14\">App\\Events\\Issue\\ServiceRequestDeletedIssueRestored<\/a>"],[0,1,"<a href=\"ServiceRequestIssueCanceled.php.html#14\">App\\Events\\Issue\\ServiceRequestIssueCanceled<\/a>"],[0,1,"<a href=\"ServiceRequestIssueCreated.php.html#14\">App\\Events\\Issue\\ServiceRequestIssueCreated<\/a>"],[0,3,"<a href=\"ServiceRequestIssueDeleted.php.html#15\">App\\Events\\Issue\\ServiceRequestIssueDeleted<\/a>"],[0,1,"<a href=\"ServiceRequestIssueRestored.php.html#14\">App\\Events\\Issue\\ServiceRequestIssueRestored<\/a>"],[0,1,"<a href=\"ServiceRequestIssueUnassigned.php.html#14\">App\\Events\\Issue\\ServiceRequestIssueUnassigned<\/a>"],[0,1,"<a href=\"ServiceRequestIssueUpdated.php.html#14\">App\\Events\\Issue\\ServiceRequestIssueUpdated<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BaseIssueEvent.php.html#20\">App\\Events\\Issue\\BaseIssueEvent::__construct<\/a>"],[0,1,"<a href=\"BaseIssueEvent.php.html#25\">App\\Events\\Issue\\BaseIssueEvent::getIssueDetails<\/a>"],[0,1,"<a href=\"BaseIssueEvent.php.html#51\">App\\Events\\Issue\\BaseIssueEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"BaseIssueEvent.php.html#61\">App\\Events\\Issue\\BaseIssueEvent::broadcastOn<\/a>"],[0,1,"<a href=\"BaseIssueEvent.php.html#77\">App\\Events\\Issue\\BaseIssueEvent::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequestDeletedIssueRestored.php.html#21\">App\\Events\\Issue\\ServiceRequestDeletedIssueRestored::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestIssueCanceled.php.html#21\">App\\Events\\Issue\\ServiceRequestIssueCanceled::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestIssueCreated.php.html#21\">App\\Events\\Issue\\ServiceRequestIssueCreated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestIssueDeleted.php.html#24\">App\\Events\\Issue\\ServiceRequestIssueDeleted::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestIssueDeleted.php.html#34\">App\\Events\\Issue\\ServiceRequestIssueDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequestIssueDeleted.php.html#45\">App\\Events\\Issue\\ServiceRequestIssueDeleted::getIssueDetails<\/a>"],[0,1,"<a href=\"ServiceRequestIssueRestored.php.html#21\">App\\Events\\Issue\\ServiceRequestIssueRestored::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestIssueUnassigned.php.html#21\">App\\Events\\Issue\\ServiceRequestIssueUnassigned::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequestIssueUpdated.php.html#21\">App\\Events\\Issue\\ServiceRequestIssueUpdated::broadcastAs<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

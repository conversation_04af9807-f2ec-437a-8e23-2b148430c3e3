<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Events</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Events</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Invoice/BroadCast/InvoiceCreated.php.html#14">App\Events\Invoice\BroadCast\InvoiceCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceDeleted.php.html#14">App\Events\Invoice\BroadCast\InvoiceDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceUpdated.php.html#14">App\Events\Invoice\BroadCast\InvoiceUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/FullyPaid.php.html#13">App\Events\Invoice\FullyPaid</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceCreatedWebhookEvent.php.html#13">App\Events\Invoice\InvoiceCreatedWebhookEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceUpdatedWebhookEvent.php.html#13">App\Events\Invoice\InvoiceUpdatedWebhookEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceVoidedWebhookEvent.php.html#13">App\Events\Invoice\InvoiceVoidedWebhookEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/PartiallyPaid.php.html#13">App\Events\Invoice\PartiallyPaid</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/BaseIssueEvent.php.html#13">App\Events\Issue\BaseIssueEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestDeletedIssueRestored.php.html#14">App\Events\Issue\ServiceRequestDeletedIssueRestored</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueCanceled.php.html#14">App\Events\Issue\ServiceRequestIssueCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueCreated.php.html#14">App\Events\Issue\ServiceRequestIssueCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueDeleted.php.html#15">App\Events\Issue\ServiceRequestIssueDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueRestored.php.html#14">App\Events\Issue\ServiceRequestIssueRestored</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueUnassigned.php.html#14">App\Events\Issue\ServiceRequestIssueUnassigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueUpdated.php.html#14">App\Events\Issue\ServiceRequestIssueUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/BaseIssueEvent.php.html#11">App\Events\ServiceRequest\ActivityLog\BaseIssueEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/ServiceRequestActivityLogCreated.php.html#14">App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/ServiceRequestActivityLogDeleted.php.html#15">App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/ServiceRequestActivityLogUpdated.php.html#14">App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseServiceRequestEvent.php.html#11">App\Events\ServiceRequest\BaseServiceRequestEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/CreateServiceRequestActivityLog.php.html#13">App\Events\ServiceRequest\CreateServiceRequestActivityLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NewServiceRequestCreated.php.html#13">App\Events\ServiceRequest\NewServiceRequestCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaDeleteLulaWebhook.php.html#13">App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaDeleteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#16">App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityBroadcast.php.html#14">App\Events\ServiceRequest\ResidentAvailabilityBroadcast</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityDeleted.php.html#14">App\Events\ServiceRequest\ResidentAvailabilityDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityStore.php.html#14">App\Events\ServiceRequest\ResidentAvailabilityStore</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAccessInfoUpdated.php.html#10">App\Events\ServiceRequest\ServiceRequestAccessInfoUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAddressUpdated.php.html#13">App\Events\ServiceRequest\ServiceRequestAddressUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeAdded.php.html#16">App\Events\ServiceRequest\ServiceRequestAssigneeAdded</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeRemoved.php.html#13">App\Events\ServiceRequest\ServiceRequestAssigneeRemoved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeUpdated.php.html#16">App\Events\ServiceRequest\ServiceRequestAssigneeUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAwaitingAvailability.php.html#10">App\Events\ServiceRequest\ServiceRequestAwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestCreated.php.html#10">App\Events\ServiceRequest\ServiceRequestCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestDescriptionUpdated.php.html#18">App\Events\ServiceRequest\ServiceRequestDescriptionUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteCreated.php.html#22">App\Events\ServiceRequest\ServiceRequestNoteCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteDeleted.php.html#16">App\Events\ServiceRequest\ServiceRequestNoteDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteUpdated.php.html#17">App\Events\ServiceRequest\ServiceRequestNoteUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChanged.php.html#14">App\Events\ServiceRequest\ServiceRequestPriorityChanged</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestResidentUpdated.php.html#14">App\Events\ServiceRequest\ServiceRequestResidentUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestStateChange.php.html#17">App\Events\ServiceRequest\ServiceRequestStateChange</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#15">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderCreated.php.html#15">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderDeleted.php.html#13">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderIssueAssigned.php.html#14">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderIssueUpdated.php.html#14">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/SyncServiceRequestWorkOrder.php.html#12">App\Events\ServiceRequest\WorkOrder\SyncServiceRequestWorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/TagApplied.php.html#15">App\Events\Tag\TagApplied</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/DefaultPinnedQuotesView.php.html#11">App\Events\User\DefaultPinnedQuotesView</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderAccessInfoUpdated.php.html#15">App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderAwaitingAvailability.php.html#13">App\Events\WorkOrder\Actions\WorkOrderAwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCanceled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderClaimPending.php.html#14">App\Events\WorkOrder\Actions\WorkOrderClaimPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCreated.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderDueDateChanged.php.html#15">App\Events\WorkOrder\Actions\WorkOrderDueDateChanged</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTESet.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTESet</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTEUpdated.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTEUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderPriorityChanged.php.html#17">App\Events\WorkOrder\Actions\WorkOrderPriorityChanged</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderQualityCheck.php.html#14">App\Events\WorkOrder\Actions\WorkOrderQualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderReadyToSchedule.php.html#14">App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderScheduled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderWorkInProgress.php.html#14">App\Events\WorkOrder\Actions\WorkOrderWorkInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLog/BaseIssueEvent.php.html#12">App\Events\WorkOrder\ActivityLog\BaseIssueEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLog/WorkOrderActivityLogCreated.php.html#14">App\Events\WorkOrder\ActivityLog\WorkOrderActivityLogCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLogEntry.php.html#13">App\Events\WorkOrder\ActivityLogEntry</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ExternalWorkOrderUpdateEvent.php.html#10">App\Events\WorkOrder\ExternalWorkOrderUpdateEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Issue/WorkOrderIssueAssigned.php.html#14">App\Events\WorkOrder\Issue\WorkOrderIssueAssigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Issue/WorkOrderIssueBaseEvent.php.html#13">App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteBaseEvent.php.html#13">App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteCreated.php.html#14">App\Events\WorkOrder\Note\WorkOrderNoteCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteDeleted.php.html#15">App\Events\WorkOrder\Note\WorkOrderNoteDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/AwaitingAvailabilityLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\AwaitingAvailabilityLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#14">App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#17">App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteApproveLulaWebhook.php.html#19">App\Events\WorkOrder\PublicWebhook\Lula\QuoteApproveLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteCreateLulaWebhook.php.html#20">App\Events\WorkOrder\PublicWebhook\Lula\QuoteCreateLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteRejectLulaWebhook.php.html#20">App\Events\WorkOrder\PublicWebhook\Lula\QuoteRejectLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteSubmitForApprovalLulaWebhook.php.html#16">App\Events\WorkOrder\PublicWebhook\Lula\QuoteSubmitForApprovalLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskCreatedLulaWebhook.php.html#16">App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskCreatedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskDeletedLulaWebhook.php.html#16">App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskDeletedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskMediaCreatedLulaWebhook.php.html#14">App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskMediaCreatedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskUpdatedLulaWebhook.php.html#16">App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskUpdatedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripPausedLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripResumedLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#13">App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#13">App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripUpdatedLulaWebhook.php.html#17">App\Events\WorkOrder\PublicWebhook\Lula\TripUpdatedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#15">App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderCanceledLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCanceledLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderPausedLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderPausedLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderReOpenLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderReOpenLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderResolveLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderResolveLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderScopingLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderScopingLulaWebhook</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/QuoteStateChange.php.html#14">App\Events\WorkOrder\Quote\QuoteStateChange</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityBroadcast.php.html#14">App\Events\WorkOrder\ResidentAvailabilityBroadcast</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityDeleted.php.html#14">App\Events\WorkOrder\ResidentAvailabilityDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityDeletedEvent.php.html#14">App\Events\WorkOrder\ResidentAvailabilityDeletedEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentUpdated.php.html#18">App\Events\WorkOrder\ResidentUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripBaseEvent.php.html#10">App\Events\WorkOrder\Trip\TripBaseEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripCanceled.php.html#10">App\Events\WorkOrder\Trip\TripCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripEnRouteStart.php.html#11">App\Events\WorkOrder\Trip\TripEnRouteStart</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripReScheduled.php.html#10">App\Events\WorkOrder\Trip\TripReScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripScheduleInProgress.php.html#10">App\Events\WorkOrder\Trip\TripScheduleInProgress</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAddressUpdated.php.html#10">App\Events\WorkOrder\WorkOrderAddressUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeAdded.php.html#16">App\Events\WorkOrder\WorkOrderAssigneeAdded</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeRemoved.php.html#13">App\Events\WorkOrder\WorkOrderAssigneeRemoved</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeUpdated.php.html#16">App\Events\WorkOrder\WorkOrderAssigneeUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderBaseEvent.php.html#11">App\Events\WorkOrder\WorkOrderBaseEvent</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderDescriptionUpdated.php.html#10">App\Events\WorkOrder\WorkOrderDescriptionUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderDetailsUpdate.php.html#13">App\Events\WorkOrder\WorkOrderDetailsUpdate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderHealthScoreChange.php.html#22">App\Events\WorkOrder\WorkOrderHealthScoreChange</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderListDeleted.php.html#15">App\Events\WorkOrder\WorkOrderListDeleted</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderPaused.php.html#10">App\Events\WorkOrder\WorkOrderPaused</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderPropertyAddressUpdated.php.html#16">App\Events\WorkOrder\WorkOrderPropertyAddressUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderReOpen.php.html#10">App\Events\WorkOrder\WorkOrderReOpen</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderReScheduled.php.html#10">App\Events\WorkOrder\WorkOrderReScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderResidentUpdated.php.html#10">App\Events\WorkOrder\WorkOrderResidentUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderResolve.php.html#10">App\Events\WorkOrder\WorkOrderResolve</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderSendToVendor.php.html#11">App\Events\WorkOrder\WorkOrderSendToVendor</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderServiceCallAssigned.php.html#16">App\Events\WorkOrder\WorkOrderServiceCallAssigned</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderServiceCallRescheduled.php.html#16">App\Events\WorkOrder\WorkOrderServiceCallRescheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChange.php.html#16">App\Events\WorkOrder\WorkOrderStateChange</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderTagUpdated.php.html#16">App\Events\WorkOrder\WorkOrderTagUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderUpdate.php.html#13">App\Events\WorkOrder\WorkOrderUpdate</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteCreated.php.html#22">App\Events\ServiceRequest\ServiceRequestNoteCreated</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestStateChange.php.html#17">App\Events\ServiceRequest\ServiceRequestStateChange</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChange.php.html#16">App\Events\WorkOrder\WorkOrderStateChange</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestDescriptionUpdated.php.html#18">App\Events\ServiceRequest\ServiceRequestDescriptionUpdated</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#13">App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteUpdated.php.html#17">App\Events\ServiceRequest\ServiceRequestNoteUpdated</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#15">App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrder/ActivityLog/BaseIssueEvent.php.html#12">App\Events\WorkOrder\ActivityLog\BaseIssueEvent</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#17">App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="WorkOrder/Quote/QuoteStateChange.php.html#14">App\Events\WorkOrder\Quote\QuoteStateChange</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceCreated.php.html#14">App\Events\Invoice\BroadCast\InvoiceCreated</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceDeleted.php.html#14">App\Events\Invoice\BroadCast\InvoiceDeleted</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceUpdated.php.html#14">App\Events\Invoice\BroadCast\InvoiceUpdated</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Invoice/InvoiceCreatedWebhookEvent.php.html#13">App\Events\Invoice\InvoiceCreatedWebhookEvent</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Invoice/InvoiceUpdatedWebhookEvent.php.html#13">App\Events\Invoice\InvoiceUpdatedWebhookEvent</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#16">App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteBaseEvent.php.html#13">App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#13">App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripBaseEvent.php.html#10">App\Events\WorkOrder\Trip\TripBaseEvent</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderBaseEvent.php.html#11">App\Events\WorkOrder\WorkOrderBaseEvent</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeUpdated.php.html#16">App\Events\ServiceRequest\ServiceRequestAssigneeUpdated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderAccessInfoUpdated.php.html#15">App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#15">App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeUpdated.php.html#16">App\Events\WorkOrder\WorkOrderAssigneeUpdated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderHealthScoreChange.php.html#22">App\Events\WorkOrder\WorkOrderHealthScoreChange</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderTagUpdated.php.html#16">App\Events\WorkOrder\WorkOrderTagUpdated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderUpdate.php.html#13">App\Events\WorkOrder\WorkOrderUpdate</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequest/BaseServiceRequestEvent.php.html#11">App\Events\ServiceRequest\BaseServiceRequestEvent</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTESet.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTESet</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTEUpdated.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTEUpdated</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripPausedLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripResumedLulaWebhook.php.html#12">App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCanceled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCanceled</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderClaimPending.php.html#14">App\Events\WorkOrder\Actions\WorkOrderClaimPending</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCreated.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCreated</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderDueDateChanged.php.html#15">App\Events\WorkOrder\Actions\WorkOrderDueDateChanged</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderPriorityChanged.php.html#17">App\Events\WorkOrder\Actions\WorkOrderPriorityChanged</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderQualityCheck.php.html#14">App\Events\WorkOrder\Actions\WorkOrderQualityCheck</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderReadyToSchedule.php.html#14">App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderScheduled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderScheduled</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderWorkInProgress.php.html#14">App\Events\WorkOrder\Actions\WorkOrderWorkInProgress</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteDeleted.php.html#15">App\Events\WorkOrder\Note\WorkOrderNoteDeleted</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#14">App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Invoice/BroadCast/InvoiceCreated.php.html#24"><abbr title="App\Events\Invoice\BroadCast\InvoiceCreated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceCreated.php.html#32"><abbr title="App\Events\Invoice\BroadCast\InvoiceCreated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceCreated.php.html#42"><abbr title="App\Events\Invoice\BroadCast\InvoiceCreated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceCreated.php.html#54"><abbr title="App\Events\Invoice\BroadCast\InvoiceCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceCreated.php.html#64"><abbr title="App\Events\Invoice\BroadCast\InvoiceCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceCreated.php.html#77"><abbr title="App\Events\Invoice\BroadCast\InvoiceCreated::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceDeleted.php.html#24"><abbr title="App\Events\Invoice\BroadCast\InvoiceDeleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceDeleted.php.html#32"><abbr title="App\Events\Invoice\BroadCast\InvoiceDeleted::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceDeleted.php.html#42"><abbr title="App\Events\Invoice\BroadCast\InvoiceDeleted::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceDeleted.php.html#54"><abbr title="App\Events\Invoice\BroadCast\InvoiceDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceDeleted.php.html#64"><abbr title="App\Events\Invoice\BroadCast\InvoiceDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceDeleted.php.html#77"><abbr title="App\Events\Invoice\BroadCast\InvoiceDeleted::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceUpdated.php.html#24"><abbr title="App\Events\Invoice\BroadCast\InvoiceUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceUpdated.php.html#32"><abbr title="App\Events\Invoice\BroadCast\InvoiceUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceUpdated.php.html#42"><abbr title="App\Events\Invoice\BroadCast\InvoiceUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceUpdated.php.html#54"><abbr title="App\Events\Invoice\BroadCast\InvoiceUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceUpdated.php.html#64"><abbr title="App\Events\Invoice\BroadCast\InvoiceUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceUpdated.php.html#77"><abbr title="App\Events\Invoice\BroadCast\InvoiceUpdated::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/FullyPaid.php.html#20"><abbr title="App\Events\Invoice\FullyPaid::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/FullyPaid.php.html#25"><abbr title="App\Events\Invoice\FullyPaid::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/FullyPaid.php.html#33"><abbr title="App\Events\Invoice\FullyPaid::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceCreatedWebhookEvent.php.html#20"><abbr title="App\Events\Invoice\InvoiceCreatedWebhookEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceCreatedWebhookEvent.php.html#25"><abbr title="App\Events\Invoice\InvoiceCreatedWebhookEvent::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceCreatedWebhookEvent.php.html#33"><abbr title="App\Events\Invoice\InvoiceCreatedWebhookEvent::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceUpdatedWebhookEvent.php.html#20"><abbr title="App\Events\Invoice\InvoiceUpdatedWebhookEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceUpdatedWebhookEvent.php.html#25"><abbr title="App\Events\Invoice\InvoiceUpdatedWebhookEvent::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceUpdatedWebhookEvent.php.html#36"><abbr title="App\Events\Invoice\InvoiceUpdatedWebhookEvent::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceVoidedWebhookEvent.php.html#20"><abbr title="App\Events\Invoice\InvoiceVoidedWebhookEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceVoidedWebhookEvent.php.html#25"><abbr title="App\Events\Invoice\InvoiceVoidedWebhookEvent::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/InvoiceVoidedWebhookEvent.php.html#30"><abbr title="App\Events\Invoice\InvoiceVoidedWebhookEvent::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/PartiallyPaid.php.html#20"><abbr title="App\Events\Invoice\PartiallyPaid::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/PartiallyPaid.php.html#25"><abbr title="App\Events\Invoice\PartiallyPaid::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Invoice/PartiallyPaid.php.html#33"><abbr title="App\Events\Invoice\PartiallyPaid::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/BaseIssueEvent.php.html#20"><abbr title="App\Events\Issue\BaseIssueEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/BaseIssueEvent.php.html#25"><abbr title="App\Events\Issue\BaseIssueEvent::getIssueDetails">getIssueDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/BaseIssueEvent.php.html#51"><abbr title="App\Events\Issue\BaseIssueEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/BaseIssueEvent.php.html#61"><abbr title="App\Events\Issue\BaseIssueEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/BaseIssueEvent.php.html#77"><abbr title="App\Events\Issue\BaseIssueEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestDeletedIssueRestored.php.html#21"><abbr title="App\Events\Issue\ServiceRequestDeletedIssueRestored::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueCanceled.php.html#21"><abbr title="App\Events\Issue\ServiceRequestIssueCanceled::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueCreated.php.html#21"><abbr title="App\Events\Issue\ServiceRequestIssueCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueDeleted.php.html#24"><abbr title="App\Events\Issue\ServiceRequestIssueDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueDeleted.php.html#34"><abbr title="App\Events\Issue\ServiceRequestIssueDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueDeleted.php.html#45"><abbr title="App\Events\Issue\ServiceRequestIssueDeleted::getIssueDetails">getIssueDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueRestored.php.html#21"><abbr title="App\Events\Issue\ServiceRequestIssueRestored::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueUnassigned.php.html#21"><abbr title="App\Events\Issue\ServiceRequestIssueUnassigned::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Issue/ServiceRequestIssueUpdated.php.html#21"><abbr title="App\Events\Issue\ServiceRequestIssueUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/BaseIssueEvent.php.html#15"><abbr title="App\Events\ServiceRequest\ActivityLog\BaseIssueEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/BaseIssueEvent.php.html#17"><abbr title="App\Events\ServiceRequest\ActivityLog\BaseIssueEvent::getServiceRequestActivityLog">getServiceRequestActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/BaseIssueEvent.php.html#35"><abbr title="App\Events\ServiceRequest\ActivityLog\BaseIssueEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/BaseIssueEvent.php.html#45"><abbr title="App\Events\ServiceRequest\ActivityLog\BaseIssueEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/BaseIssueEvent.php.html#61"><abbr title="App\Events\ServiceRequest\ActivityLog\BaseIssueEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/ServiceRequestActivityLogCreated.php.html#21"><abbr title="App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/ServiceRequestActivityLogDeleted.php.html#22"><abbr title="App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/ServiceRequestActivityLogDeleted.php.html#32"><abbr title="App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/ServiceRequestActivityLogDeleted.php.html#48"><abbr title="App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleted::getServiceRequestActivityLog">getServiceRequestActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ActivityLog/ServiceRequestActivityLogUpdated.php.html#21"><abbr title="App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseServiceRequestEvent.php.html#20"><abbr title="App\Events\ServiceRequest\BaseServiceRequestEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseServiceRequestEvent.php.html#25"><abbr title="App\Events\ServiceRequest\BaseServiceRequestEvent::getServiceRequestDetails">getServiceRequestDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseServiceRequestEvent.php.html#53"><abbr title="App\Events\ServiceRequest\BaseServiceRequestEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/BaseServiceRequestEvent.php.html#63"><abbr title="App\Events\ServiceRequest\BaseServiceRequestEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/CreateServiceRequestActivityLog.php.html#31"><abbr title="App\Events\ServiceRequest\CreateServiceRequestActivityLog::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NewServiceRequestCreated.php.html#20"><abbr title="App\Events\ServiceRequest\NewServiceRequestCreated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NewServiceRequestCreated.php.html#29"><abbr title="App\Events\ServiceRequest\NewServiceRequestCreated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NewServiceRequestCreated.php.html#39"><abbr title="App\Events\ServiceRequest\NewServiceRequestCreated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NewServiceRequestCreated.php.html#49"><abbr title="App\Events\ServiceRequest\NewServiceRequestCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/NewServiceRequestCreated.php.html#59"><abbr title="App\Events\ServiceRequest\NewServiceRequestCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaDeleteLulaWebhook.php.html#20"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaDeleteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaDeleteLulaWebhook.php.html#25"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaDeleteLulaWebhook::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaDeleteLulaWebhook.php.html#30"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaDeleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#28"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#32"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::fetchMedia">fetchMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#51"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#62"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityBroadcast.php.html#18"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityBroadcast::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityBroadcast.php.html#29"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityBroadcast::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityBroadcast.php.html#39"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityBroadcast::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityBroadcast.php.html#49"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityBroadcast::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityDeleted.php.html#21"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityDeleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ResidentAvailabilityStore.php.html#21"><abbr title="App\Events\ServiceRequest\ResidentAvailabilityStore::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAccessInfoUpdated.php.html#14"><abbr title="App\Events\ServiceRequest\ServiceRequestAccessInfoUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAddressUpdated.php.html#22"><abbr title="App\Events\ServiceRequest\ServiceRequestAddressUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAddressUpdated.php.html#34"><abbr title="App\Events\ServiceRequest\ServiceRequestAddressUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAddressUpdated.php.html#44"><abbr title="App\Events\ServiceRequest\ServiceRequestAddressUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeAdded.php.html#23"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeAdded::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeRemoved.php.html#20"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeRemoved::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeUpdated.php.html#23"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeUpdated.php.html#32"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeUpdated.php.html#42"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeUpdated.php.html#52"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeUpdated.php.html#66"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAwaitingAvailability.php.html#17"><abbr title="App\Events\ServiceRequest\ServiceRequestAwaitingAvailability::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestCreated.php.html#18"><abbr title="App\Events\ServiceRequest\ServiceRequestCreated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestDescriptionUpdated.php.html#25"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestDescriptionUpdated.php.html#30"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestDescriptionUpdated.php.html#40"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestDescriptionUpdated.php.html#54"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestDescriptionUpdated.php.html#64"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestDescriptionUpdated.php.html#73"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::getServiceRequestDescription">getServiceRequestDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteCreated.php.html#32"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteCreated.php.html#66"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteCreated.php.html#76"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteCreated.php.html#93"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteCreated.php.html#103"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteDeleted.php.html#23"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteDeleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteDeleted.php.html#31"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteDeleted::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteDeleted.php.html#41"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteDeleted::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteDeleted.php.html#51"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteDeleted.php.html#61"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteUpdated.php.html#24"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteUpdated.php.html#32"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteUpdated.php.html#42"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteUpdated.php.html#52"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteUpdated.php.html#62"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChanged.php.html#22"><abbr title="App\Events\ServiceRequest\ServiceRequestPriorityChanged::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestPriorityChanged.php.html#32"><abbr title="App\Events\ServiceRequest\ServiceRequestPriorityChanged::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestResidentUpdated.php.html#18"><abbr title="App\Events\ServiceRequest\ServiceRequestResidentUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestResidentUpdated.php.html#28"><abbr title="App\Events\ServiceRequest\ServiceRequestResidentUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestResidentUpdated.php.html#38"><abbr title="App\Events\ServiceRequest\ServiceRequestResidentUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestStateChange.php.html#27"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestStateChange.php.html#38"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestStateChange.php.html#51"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestStateChange.php.html#61"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestStateChange.php.html#109"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestStateChange.php.html#123"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::getServiceRequestActivityLog">getServiceRequestActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#22"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#29"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#44"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#52"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#62"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#78"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#101"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::currentWorkOrderAbilities">currentWorkOrderAbilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderCreated.php.html#22"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderCreated.php.html#32"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderCreated.php.html#44"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderDeleted.php.html#20"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderDeleted.php.html#30"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderIssueAssigned.php.html#23"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssigned::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderIssueUpdated.php.html#23"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/SyncServiceRequestWorkOrder.php.html#16"><abbr title="App\Events\ServiceRequest\WorkOrder\SyncServiceRequestWorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/TagApplied.php.html#22"><abbr title="App\Events\Tag\TagApplied::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/TagApplied.php.html#30"><abbr title="App\Events\Tag\TagApplied::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/TagApplied.php.html#40"><abbr title="App\Events\Tag\TagApplied::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/TagApplied.php.html#50"><abbr title="App\Events\Tag\TagApplied::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Tag/TagApplied.php.html#60"><abbr title="App\Events\Tag\TagApplied::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/DefaultPinnedQuotesView.php.html#18"><abbr title="App\Events\User\DefaultPinnedQuotesView::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="User/DefaultPinnedQuotesView.php.html#28"><abbr title="App\Events\User\DefaultPinnedQuotesView::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderAccessInfoUpdated.php.html#22"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderAccessInfoUpdated.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderAccessInfoUpdated.php.html#54"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderAwaitingAvailability.php.html#20"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAwaitingAvailability::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderAwaitingAvailability.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAwaitingAvailability::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCanceled.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCanceled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCanceled.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderClaimPending.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderClaimPending.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderClaimPending.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCreated.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCreated.php.html#31"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCreated.php.html#50"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderDueDateChanged.php.html#22"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderDueDateChanged.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderDueDateChanged.php.html#49"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTESet.php.html#23"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTESet.php.html#33"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTESet.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTESet.php.html#64"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTEUpdated.php.html#23"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTEUpdated.php.html#33"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTEUpdated.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTEUpdated.php.html#64"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderPriorityChanged.php.html#24"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderPriorityChanged.php.html#32"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderPriorityChanged.php.html#51"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderQualityCheck.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderQualityCheck.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderQualityCheck.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderReadyToSchedule.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderReadyToSchedule.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderReadyToSchedule.php.html#49"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderScheduled.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderScheduled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderScheduled.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderWorkInProgress.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderWorkInProgress.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderWorkInProgress.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLog/BaseIssueEvent.php.html#16"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLog/BaseIssueEvent.php.html#18"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLog/BaseIssueEvent.php.html#37"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLog/BaseIssueEvent.php.html#47"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLog/BaseIssueEvent.php.html#63"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLog/WorkOrderActivityLogCreated.php.html#21"><abbr title="App\Events\WorkOrder\ActivityLog\WorkOrderActivityLogCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLogEntry.php.html#22"><abbr title="App\Events\WorkOrder\ActivityLogEntry::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLogEntry.php.html#33"><abbr title="App\Events\WorkOrder\ActivityLogEntry::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLogEntry.php.html#43"><abbr title="App\Events\WorkOrder\ActivityLogEntry::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ActivityLogEntry.php.html#53"><abbr title="App\Events\WorkOrder\ActivityLogEntry::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ExternalWorkOrderUpdateEvent.php.html#35"><abbr title="App\Events\WorkOrder\ExternalWorkOrderUpdateEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Issue/WorkOrderIssueAssigned.php.html#21"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueAssigned::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Issue/WorkOrderIssueBaseEvent.php.html#17"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Issue/WorkOrderIssueBaseEvent.php.html#22"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::getWorkOrderIssueDetails">getWorkOrderIssueDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Issue/WorkOrderIssueBaseEvent.php.html#50"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Issue/WorkOrderIssueBaseEvent.php.html#60"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Issue/WorkOrderIssueBaseEvent.php.html#74"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Issue/WorkOrderIssueBaseEvent.php.html#84"><abbr title="App\Events\WorkOrder\Issue\WorkOrderIssueBaseEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteBaseEvent.php.html#17"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteBaseEvent.php.html#22"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::getWorkOrderNoteDetails">getWorkOrderNoteDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteBaseEvent.php.html#55"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteBaseEvent.php.html#65"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteBaseEvent.php.html#79"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteBaseEvent.php.html#89"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteCreated.php.html#21"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteDeleted.php.html#22"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteDeleted.php.html#30"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteDeleted::getWorkOrderNoteDetails">getWorkOrderNoteDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteDeleted.php.html#56"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/AwaitingAvailabilityLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\AwaitingAvailabilityLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/AwaitingAvailabilityLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\AwaitingAvailabilityLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/AwaitingAvailabilityLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\AwaitingAvailabilityLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#21"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#26"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#29"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#33"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::fetchMedia">fetchMedia</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#53"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#64"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteApproveLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteApproveLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteApproveLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteApproveLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteApproveLulaWebhook.php.html#39"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteApproveLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteCreateLulaWebhook.php.html#29"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteCreateLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteCreateLulaWebhook.php.html#37"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteCreateLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteCreateLulaWebhook.php.html#45"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteCreateLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteRejectLulaWebhook.php.html#24"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteRejectLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteRejectLulaWebhook.php.html#32"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteRejectLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteRejectLulaWebhook.php.html#40"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteRejectLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteSubmitForApprovalLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteSubmitForApprovalLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteSubmitForApprovalLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteSubmitForApprovalLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteSubmitForApprovalLulaWebhook.php.html#36"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteSubmitForApprovalLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskCreatedLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskCreatedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskCreatedLulaWebhook.php.html#29"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskCreatedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskCreatedLulaWebhook.php.html#34"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskCreatedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskDeletedLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskDeletedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskDeletedLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskDeletedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskDeletedLulaWebhook.php.html#33"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskDeletedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskMediaCreatedLulaWebhook.php.html#21"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskMediaCreatedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskMediaCreatedLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskMediaCreatedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskMediaCreatedLulaWebhook.php.html#33"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskMediaCreatedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskUpdatedLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskUpdatedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskUpdatedLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskUpdatedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/QuoteTaskUpdatedLulaWebhook.php.html#33"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskUpdatedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripPausedLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripPausedLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripPausedLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripResumedLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripResumedLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripResumedLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#17"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#22"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#50"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#20"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#24"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#32"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripUpdatedLulaWebhook.php.html#24"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripUpdatedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripUpdatedLulaWebhook.php.html#30"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripUpdatedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripUpdatedLulaWebhook.php.html#35"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripUpdatedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#22"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#36"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderCanceledLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCanceledLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderCanceledLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCanceledLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderCanceledLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCanceledLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderPausedLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderPausedLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderPausedLulaWebhook.php.html#21"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderPausedLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderPausedLulaWebhook.php.html#29"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderPausedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderReOpenLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderReOpenLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderReOpenLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderReOpenLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderReOpenLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderReOpenLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderResolveLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderResolveLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderResolveLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderResolveLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderResolveLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderResolveLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderScopingLulaWebhook.php.html#19"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderScopingLulaWebhook::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderScopingLulaWebhook.php.html#23"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderScopingLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderScopingLulaWebhook.php.html#28"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderScopingLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/QuoteStateChange.php.html#27"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/QuoteStateChange.php.html#37"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/QuoteStateChange.php.html#50"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/QuoteStateChange.php.html#62"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/QuoteStateChange.php.html#85"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Quote/QuoteStateChange.php.html#122"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityBroadcast.php.html#18"><abbr title="App\Events\WorkOrder\ResidentAvailabilityBroadcast::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityBroadcast.php.html#29"><abbr title="App\Events\WorkOrder\ResidentAvailabilityBroadcast::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityBroadcast.php.html#39"><abbr title="App\Events\WorkOrder\ResidentAvailabilityBroadcast::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityBroadcast.php.html#49"><abbr title="App\Events\WorkOrder\ResidentAvailabilityBroadcast::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityDeleted.php.html#21"><abbr title="App\Events\WorkOrder\ResidentAvailabilityDeleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityDeletedEvent.php.html#18"><abbr title="App\Events\WorkOrder\ResidentAvailabilityDeletedEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityDeletedEvent.php.html#28"><abbr title="App\Events\WorkOrder\ResidentAvailabilityDeletedEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityDeletedEvent.php.html#38"><abbr title="App\Events\WorkOrder\ResidentAvailabilityDeletedEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentAvailabilityDeletedEvent.php.html#48"><abbr title="App\Events\WorkOrder\ResidentAvailabilityDeletedEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentUpdated.php.html#31"><abbr title="App\Events\WorkOrder\ResidentUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentUpdated.php.html#42"><abbr title="App\Events\WorkOrder\ResidentUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentUpdated.php.html#53"><abbr title="App\Events\WorkOrder\ResidentUpdated::getServiceRequestDetails">getServiceRequestDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentUpdated.php.html#69"><abbr title="App\Events\WorkOrder\ResidentUpdated::getResidentDetails">getResidentDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentUpdated.php.html#86"><abbr title="App\Events\WorkOrder\ResidentUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentUpdated.php.html#96"><abbr title="App\Events\WorkOrder\ResidentUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/ResidentUpdated.php.html#110"><abbr title="App\Events\WorkOrder\ResidentUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripBaseEvent.php.html#17"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripBaseEvent.php.html#22"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::getTrip">getTrip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripBaseEvent.php.html#40"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripBaseEvent.php.html#50"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripBaseEvent.php.html#62"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripBaseEvent.php.html#72"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripCanceled.php.html#17"><abbr title="App\Events\WorkOrder\Trip\TripCanceled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripEnRouteStart.php.html#18"><abbr title="App\Events\WorkOrder\Trip\TripEnRouteStart::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripReScheduled.php.html#17"><abbr title="App\Events\WorkOrder\Trip\TripReScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripScheduleInProgress.php.html#17"><abbr title="App\Events\WorkOrder\Trip\TripScheduleInProgress::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAddressUpdated.php.html#14"><abbr title="App\Events\WorkOrder\WorkOrderAddressUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeAdded.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeAdded::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeRemoved.php.html#20"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeRemoved::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeUpdated.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeUpdated.php.html#32"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeUpdated.php.html#42"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeUpdated.php.html#52"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeUpdated.php.html#66"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderBaseEvent.php.html#18"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderBaseEvent.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderBaseEvent.php.html#82"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderBaseEvent.php.html#92"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderBaseEvent.php.html#104"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderBaseEvent.php.html#114"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderDescriptionUpdated.php.html#14"><abbr title="App\Events\WorkOrder\WorkOrderDescriptionUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderDetailsUpdate.php.html#22"><abbr title="App\Events\WorkOrder\WorkOrderDetailsUpdate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderDetailsUpdate.php.html#27"><abbr title="App\Events\WorkOrder\WorkOrderDetailsUpdate::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderDetailsUpdate.php.html#37"><abbr title="App\Events\WorkOrder\WorkOrderDetailsUpdate::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderDetailsUpdate.php.html#53"><abbr title="App\Events\WorkOrder\WorkOrderDetailsUpdate::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderDetailsUpdate.php.html#63"><abbr title="App\Events\WorkOrder\WorkOrderDetailsUpdate::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderHealthScoreChange.php.html#29"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderHealthScoreChange.php.html#47"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderHealthScoreChange.php.html#57"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderHealthScoreChange.php.html#67"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderHealthScoreChange.php.html#77"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderListDeleted.php.html#24"><abbr title="App\Events\WorkOrder\WorkOrderListDeleted::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderListDeleted.php.html#31"><abbr title="App\Events\WorkOrder\WorkOrderListDeleted::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderListDeleted.php.html#41"><abbr title="App\Events\WorkOrder\WorkOrderListDeleted::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderListDeleted.php.html#56"><abbr title="App\Events\WorkOrder\WorkOrderListDeleted::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderListDeleted.php.html#66"><abbr title="App\Events\WorkOrder\WorkOrderListDeleted::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderPaused.php.html#17"><abbr title="App\Events\WorkOrder\WorkOrderPaused::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderPropertyAddressUpdated.php.html#26"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderPropertyAddressUpdated.php.html#31"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderPropertyAddressUpdated.php.html#39"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::getPropertyDetails">getPropertyDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderPropertyAddressUpdated.php.html#47"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderPropertyAddressUpdated.php.html#57"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderPropertyAddressUpdated.php.html#69"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderPropertyAddressUpdated.php.html#79"><abbr title="App\Events\WorkOrder\WorkOrderPropertyAddressUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderReOpen.php.html#17"><abbr title="App\Events\WorkOrder\WorkOrderReOpen::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderReScheduled.php.html#17"><abbr title="App\Events\WorkOrder\WorkOrderReScheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderResidentUpdated.php.html#14"><abbr title="App\Events\WorkOrder\WorkOrderResidentUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderResolve.php.html#17"><abbr title="App\Events\WorkOrder\WorkOrderResolve::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderSendToVendor.php.html#18"><abbr title="App\Events\WorkOrder\WorkOrderSendToVendor::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderSendToVendor.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderSendToVendor::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderSendToVendor.php.html#28"><abbr title="App\Events\WorkOrder\WorkOrderSendToVendor::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderServiceCallAssigned.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderServiceCallAssigned::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderServiceCallRescheduled.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderServiceCallRescheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChange.php.html#29"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChange.php.html#40"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChange.php.html#53"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChange.php.html#63"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChange.php.html#112"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChange.php.html#149"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderTagUpdated.php.html#25"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderTagUpdated.php.html#36"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderTagUpdated.php.html#46"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderTagUpdated.php.html#56"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderTagUpdated.php.html#66"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderUpdate.php.html#22"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderUpdate.php.html#30"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::broadcastQueue">broadcastQueue</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderUpdate.php.html#40"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderUpdate.php.html#55"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderUpdate.php.html#65"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripScheduledLulaWebhook.php.html#50"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripScheduledLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteCreated.php.html#32"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::__construct">__construct</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteCreated.php.html#103"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Invoice/InvoiceCreatedWebhookEvent.php.html#33"><abbr title="App\Events\Invoice\InvoiceCreatedWebhookEvent::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="Invoice/InvoiceUpdatedWebhookEvent.php.html#36"><abbr title="App\Events\Invoice\InvoiceUpdatedWebhookEvent::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripStoppedLulaWebhook.php.html#32"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripStoppedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestDescriptionUpdated.php.html#73"><abbr title="App\Events\ServiceRequest\ServiceRequestDescriptionUpdated::getServiceRequestDescription">getServiceRequestDescription</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteUpdated.php.html#62"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripEnRouteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripEnRouteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorKOrderReadyToInvoiceLulaWebhook.php.html#36"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderCompleteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderCompleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/WorkOrderInProgressLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\WorkOrderInProgressLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#32"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::fetchMedia">fetchMedia</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestNoteCreated.php.html#76"><abbr title="App\Events\ServiceRequest\ServiceRequestNoteCreated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestStateChange.php.html#61"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderAccessInfoUpdated.php.html#54"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/ActivityLog/BaseIssueEvent.php.html#63"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#33"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::fetchMedia">fetchMedia</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripPausedLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripPausedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/TripResumedLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\TripResumedLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChange.php.html#63"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceCreated.php.html#77"><abbr title="App\Events\Invoice\BroadCast\InvoiceCreated::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceDeleted.php.html#77"><abbr title="App\Events\Invoice\BroadCast\InvoiceDeleted::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Invoice/BroadCast/InvoiceUpdated.php.html#77"><abbr title="App\Events\Invoice\BroadCast\InvoiceUpdated::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/BaseServiceRequestEvent.php.html#25"><abbr title="App\Events\ServiceRequest\BaseServiceRequestEvent::getServiceRequestDetails">getServiceRequestDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/PublicWebhook/Lula/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#51"><abbr title="App\Events\ServiceRequest\PublicWebhook\Lula\ServiceRequestMediaUploadCompleteLulaWebhook::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestAssigneeUpdated.php.html#52"><abbr title="App\Events\ServiceRequest\ServiceRequestAssigneeUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestStateChange.php.html#109"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::getServiceRequest">getServiceRequest</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/ServiceRequestStateChange.php.html#123"><abbr title="App\Events\ServiceRequest\ServiceRequestStateChange::getServiceRequestActivityLog">getServiceRequestActivityLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceRequest/WorkOrder/ServiceRequestWorkOrderBaseEvent.php.html#101"><abbr title="App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent::currentWorkOrderAbilities">currentWorkOrderAbilities</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderAccessInfoUpdated.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCanceled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderClaimPending.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderCreated.php.html#50"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderDueDateChanged.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTESet.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderNTEUpdated.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderPriorityChanged.php.html#32"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderQualityCheck.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderReadyToSchedule.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderScheduled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Actions/WorkOrderWorkInProgress.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/ActivityLog/BaseIssueEvent.php.html#18"><abbr title="App\Events\WorkOrder\ActivityLog\BaseIssueEvent::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteBaseEvent.php.html#22"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteBaseEvent::getWorkOrderNoteDetails">getWorkOrderNoteDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Note/WorkOrderNoteDeleted.php.html#30"><abbr title="App\Events\WorkOrder\Note\WorkOrderNoteDeleted::getWorkOrderNoteDetails">getWorkOrderNoteDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaDeleteLulaWebhook.php.html#31"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#53"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/PublicWebhook/Lula/MediaUploadCompleteLulaWebhook.php.html#64"><abbr title="App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook::prepareWebhookPayload">prepareWebhookPayload</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Quote/QuoteStateChange.php.html#85"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Quote/QuoteStateChange.php.html#122"><abbr title="App\Events\WorkOrder\Quote\QuoteStateChange::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/Trip/TripBaseEvent.php.html#22"><abbr title="App\Events\WorkOrder\Trip\TripBaseEvent::getTrip">getTrip</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderAssigneeUpdated.php.html#52"><abbr title="App\Events\WorkOrder\WorkOrderAssigneeUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderBaseEvent.php.html#23"><abbr title="App\Events\WorkOrder\WorkOrderBaseEvent::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderHealthScoreChange.php.html#29"><abbr title="App\Events\WorkOrder\WorkOrderHealthScoreChange::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChange.php.html#112"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::getWorkOrder">getWorkOrder</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderStateChange.php.html#149"><abbr title="App\Events\WorkOrder\WorkOrderStateChange::getWorkOrderActivityLog">getWorkOrderActivityLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderTagUpdated.php.html#66"><abbr title="App\Events\WorkOrder\WorkOrderTagUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrder/WorkOrderUpdate.php.html#65"><abbr title="App\Events\WorkOrder\WorkOrderUpdate::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:22:08 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([128,0,0,0,0,0,0,0,0,0,0,15], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([386,0,0,0,0,0,0,0,0,0,0,1], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,7,"<a href=\"Invoice\/BroadCast\/InvoiceCreated.php.html#14\">App\\Events\\Invoice\\BroadCast\\InvoiceCreated<\/a>"],[0,7,"<a href=\"Invoice\/BroadCast\/InvoiceDeleted.php.html#14\">App\\Events\\Invoice\\BroadCast\\InvoiceDeleted<\/a>"],[0,7,"<a href=\"Invoice\/BroadCast\/InvoiceUpdated.php.html#14\">App\\Events\\Invoice\\BroadCast\\InvoiceUpdated<\/a>"],[0,3,"<a href=\"Invoice\/FullyPaid.php.html#13\">App\\Events\\Invoice\\FullyPaid<\/a>"],[0,7,"<a href=\"Invoice\/InvoiceCreatedWebhookEvent.php.html#13\">App\\Events\\Invoice\\InvoiceCreatedWebhookEvent<\/a>"],[0,7,"<a href=\"Invoice\/InvoiceUpdatedWebhookEvent.php.html#13\">App\\Events\\Invoice\\InvoiceUpdatedWebhookEvent<\/a>"],[0,3,"<a href=\"Invoice\/InvoiceVoidedWebhookEvent.php.html#13\">App\\Events\\Invoice\\InvoiceVoidedWebhookEvent<\/a>"],[0,3,"<a href=\"Invoice\/PartiallyPaid.php.html#13\">App\\Events\\Invoice\\PartiallyPaid<\/a>"],[0,5,"<a href=\"Issue\/BaseIssueEvent.php.html#13\">App\\Events\\Issue\\BaseIssueEvent<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestDeletedIssueRestored.php.html#14\">App\\Events\\Issue\\ServiceRequestDeletedIssueRestored<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueCanceled.php.html#14\">App\\Events\\Issue\\ServiceRequestIssueCanceled<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueCreated.php.html#14\">App\\Events\\Issue\\ServiceRequestIssueCreated<\/a>"],[0,3,"<a href=\"Issue\/ServiceRequestIssueDeleted.php.html#15\">App\\Events\\Issue\\ServiceRequestIssueDeleted<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueRestored.php.html#14\">App\\Events\\Issue\\ServiceRequestIssueRestored<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueUnassigned.php.html#14\">App\\Events\\Issue\\ServiceRequestIssueUnassigned<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueUpdated.php.html#14\">App\\Events\\Issue\\ServiceRequestIssueUpdated<\/a>"],[0,5,"<a href=\"ServiceRequest\/ActivityLog\/BaseIssueEvent.php.html#11\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/ServiceRequestActivityLogCreated.php.html#14\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogCreated<\/a>"],[0,3,"<a href=\"ServiceRequest\/ActivityLog\/ServiceRequestActivityLogDeleted.php.html#15\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogDeleted<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/ServiceRequestActivityLogUpdated.php.html#14\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogUpdated<\/a>"],[0,5,"<a href=\"ServiceRequest\/BaseServiceRequestEvent.php.html#11\">App\\Events\\ServiceRequest\\BaseServiceRequestEvent<\/a>"],[0,1,"<a href=\"ServiceRequest\/CreateServiceRequestActivityLog.php.html#13\">App\\Events\\ServiceRequest\\CreateServiceRequestActivityLog<\/a>"],[0,5,"<a href=\"ServiceRequest\/NewServiceRequestCreated.php.html#13\">App\\Events\\ServiceRequest\\NewServiceRequestCreated<\/a>"],[0,3,"<a href=\"ServiceRequest\/PublicWebhook\/Lula\/ServiceRequestMediaDeleteLulaWebhook.php.html#13\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaDeleteLulaWebhook<\/a>"],[0,7,"<a href=\"ServiceRequest\/PublicWebhook\/Lula\/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#16\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaUploadCompleteLulaWebhook<\/a>"],[0,4,"<a href=\"ServiceRequest\/ResidentAvailabilityBroadcast.php.html#14\">App\\Events\\ServiceRequest\\ResidentAvailabilityBroadcast<\/a>"],[0,1,"<a href=\"ServiceRequest\/ResidentAvailabilityDeleted.php.html#14\">App\\Events\\ServiceRequest\\ResidentAvailabilityDeleted<\/a>"],[0,1,"<a href=\"ServiceRequest\/ResidentAvailabilityStore.php.html#14\">App\\Events\\ServiceRequest\\ResidentAvailabilityStore<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAccessInfoUpdated.php.html#10\">App\\Events\\ServiceRequest\\ServiceRequestAccessInfoUpdated<\/a>"],[0,3,"<a href=\"ServiceRequest\/ServiceRequestAddressUpdated.php.html#13\">App\\Events\\ServiceRequest\\ServiceRequestAddressUpdated<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssigneeAdded.php.html#16\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeAdded<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssigneeRemoved.php.html#13\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeRemoved<\/a>"],[0,6,"<a href=\"ServiceRequest\/ServiceRequestAssigneeUpdated.php.html#16\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAwaitingAvailability.php.html#10\">App\\Events\\ServiceRequest\\ServiceRequestAwaitingAvailability<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestCreated.php.html#10\">App\\Events\\ServiceRequest\\ServiceRequestCreated<\/a>"],[0,9,"<a href=\"ServiceRequest\/ServiceRequestDescriptionUpdated.php.html#18\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated<\/a>"],[0,17,"<a href=\"ServiceRequest\/ServiceRequestNoteCreated.php.html#22\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated<\/a>"],[0,5,"<a href=\"ServiceRequest\/ServiceRequestNoteDeleted.php.html#16\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted<\/a>"],[0,8,"<a href=\"ServiceRequest\/ServiceRequestNoteUpdated.php.html#17\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated<\/a>"],[0,2,"<a href=\"ServiceRequest\/ServiceRequestPriorityChanged.php.html#14\">App\\Events\\ServiceRequest\\ServiceRequestPriorityChanged<\/a>"],[0,3,"<a href=\"ServiceRequest\/ServiceRequestResidentUpdated.php.html#14\">App\\Events\\ServiceRequest\\ServiceRequestResidentUpdated<\/a>"],[0,10,"<a href=\"ServiceRequest\/ServiceRequestStateChange.php.html#17\">App\\Events\\ServiceRequest\\ServiceRequestStateChange<\/a>"],[100,0,"<a href=\"ServiceRequest\/ServiceRequestStateChanged.php.html#7\">App\\Events\\ServiceRequest\\ServiceRequestStateChanged<\/a>"],[0,8,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#15\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent<\/a>"],[100,0,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderCanceled.php.html#13\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderCanceled<\/a>"],[0,3,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderCreated.php.html#15\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderCreated<\/a>"],[0,2,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderDeleted.php.html#13\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderDeleted<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderIssueAssigned.php.html#14\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderIssueAssigned<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderIssueUpdated.php.html#14\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderIssueUpdated<\/a>"],[100,0,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderUpdated.php.html#13\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderUpdated<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/SyncServiceRequestWorkOrder.php.html#12\">App\\Events\\ServiceRequest\\WorkOrder\\SyncServiceRequestWorkOrder<\/a>"],[0,5,"<a href=\"Tag\/TagApplied.php.html#15\">App\\Events\\Tag\\TagApplied<\/a>"],[0,2,"<a href=\"User\/DefaultPinnedQuotesView.php.html#11\">App\\Events\\User\\DefaultPinnedQuotesView<\/a>"],[100,1,"<a href=\"User\/UserInvited.php.html#11\">App\\Events\\User\\UserInvited<\/a>"],[0,6,"<a href=\"WorkOrder\/Actions\/WorkOrderAccessInfoUpdated.php.html#15\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderAwaitingAvailability.php.html#13\">App\\Events\\WorkOrder\\Actions\\WorkOrderAwaitingAvailability<\/a>"],[0,4,"<a href=\"WorkOrder\/Actions\/WorkOrderCanceled.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled<\/a>"],[0,4,"<a href=\"WorkOrder\/Actions\/WorkOrderClaimPending.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending<\/a>"],[0,4,"<a href=\"WorkOrder\/Actions\/WorkOrderCreated.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated<\/a>"],[0,4,"<a href=\"WorkOrder\/Actions\/WorkOrderDueDateChanged.php.html#15\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged<\/a>"],[0,5,"<a href=\"WorkOrder\/Actions\/WorkOrderNTESet.php.html#16\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet<\/a>"],[0,5,"<a href=\"WorkOrder\/Actions\/WorkOrderNTEUpdated.php.html#16\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated<\/a>"],[0,4,"<a href=\"WorkOrder\/Actions\/WorkOrderPriorityChanged.php.html#17\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged<\/a>"],[0,4,"<a href=\"WorkOrder\/Actions\/WorkOrderQualityCheck.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck<\/a>"],[0,4,"<a href=\"WorkOrder\/Actions\/WorkOrderReadyToSchedule.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule<\/a>"],[0,4,"<a href=\"WorkOrder\/Actions\/WorkOrderScheduled.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled<\/a>"],[0,4,"<a href=\"WorkOrder\/Actions\/WorkOrderWorkInProgress.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress<\/a>"],[0,8,"<a href=\"WorkOrder\/ActivityLog\/BaseIssueEvent.php.html#12\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent<\/a>"],[0,1,"<a href=\"WorkOrder\/ActivityLog\/WorkOrderActivityLogCreated.php.html#14\">App\\Events\\WorkOrder\\ActivityLog\\WorkOrderActivityLogCreated<\/a>"],[0,4,"<a href=\"WorkOrder\/ActivityLogEntry.php.html#13\">App\\Events\\WorkOrder\\ActivityLogEntry<\/a>"],[0,1,"<a href=\"WorkOrder\/ExternalWorkOrderUpdateEvent.php.html#10\">App\\Events\\WorkOrder\\ExternalWorkOrderUpdateEvent<\/a>"],[0,1,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueAssigned.php.html#14\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueAssigned<\/a>"],[0,6,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueBaseEvent.php.html#13\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent<\/a>"],[100,0,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueCanceled.php.html#14\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueCanceled<\/a>"],[100,0,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueDeclined.php.html#14\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueDeclined<\/a>"],[100,0,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueUpdated.php.html#14\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueUpdated<\/a>"],[0,7,"<a href=\"WorkOrder\/Note\/WorkOrderNoteBaseEvent.php.html#13\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/WorkOrderNoteCreated.php.html#14\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteCreated<\/a>"],[0,4,"<a href=\"WorkOrder\/Note\/WorkOrderNoteDeleted.php.html#15\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteDeleted<\/a>"],[100,0,"<a href=\"WorkOrder\/Note\/WorkOrderNoteUpdated.php.html#12\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteUpdated<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/AwaitingAvailabilityLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\AwaitingAvailabilityLulaWebhook<\/a>"],[0,4,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/MediaDeleteLulaWebhook.php.html#14\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaDeleteLulaWebhook<\/a>"],[0,8,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/MediaUploadCompleteLulaWebhook.php.html#17\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaUploadCompleteLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteApproveLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteApproveLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteCreateLulaWebhook.php.html#20\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteCreateLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteRejectLulaWebhook.php.html#20\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteRejectLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteSubmitForApprovalLulaWebhook.php.html#16\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteSubmitForApprovalLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskCreatedLulaWebhook.php.html#16\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskCreatedLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskDeletedLulaWebhook.php.html#16\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskDeletedLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskMediaCreatedLulaWebhook.php.html#14\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskMediaCreatedLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskUpdatedLulaWebhook.php.html#16\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskUpdatedLulaWebhook<\/a>"],[0,6,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripEnRouteLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripEnRouteLulaWebhook<\/a>"],[0,5,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripPausedLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripPausedLulaWebhook<\/a>"],[0,5,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripResumedLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripResumedLulaWebhook<\/a>"],[0,9,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripScheduledLulaWebhook.php.html#13\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripScheduledLulaWebhook<\/a>"],[0,7,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripStoppedLulaWebhook.php.html#13\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripStoppedLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripUpdatedLulaWebhook.php.html#17\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripUpdatedLulaWebhook<\/a>"],[0,6,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorKOrderReadyToInvoiceLulaWebhook.php.html#15\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorKOrderReadyToInvoiceLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderCanceledLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCanceledLulaWebhook<\/a>"],[0,6,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderCompleteLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCompleteLulaWebhook<\/a>"],[0,6,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderInProgressLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderInProgressLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderPausedLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderPausedLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderReOpenLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderReOpenLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderResolveLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderResolveLulaWebhook<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderScopingLulaWebhook.php.html#12\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderScopingLulaWebhook<\/a>"],[0,8,"<a href=\"WorkOrder\/Quote\/QuoteStateChange.php.html#14\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange<\/a>"],[0,4,"<a href=\"WorkOrder\/ResidentAvailabilityBroadcast.php.html#14\">App\\Events\\WorkOrder\\ResidentAvailabilityBroadcast<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentAvailabilityDeleted.php.html#14\">App\\Events\\WorkOrder\\ResidentAvailabilityDeleted<\/a>"],[0,4,"<a href=\"WorkOrder\/ResidentAvailabilityDeletedEvent.php.html#14\">App\\Events\\WorkOrder\\ResidentAvailabilityDeletedEvent<\/a>"],[0,7,"<a href=\"WorkOrder\/ResidentUpdated.php.html#18\">App\\Events\\WorkOrder\\ResidentUpdated<\/a>"],[0,7,"<a href=\"WorkOrder\/Trip\/TripBaseEvent.php.html#10\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripCanceled.php.html#10\">App\\Events\\WorkOrder\\Trip\\TripCanceled<\/a>"],[100,0,"<a href=\"WorkOrder\/Trip\/TripEnRoutePaused.php.html#12\">App\\Events\\WorkOrder\\Trip\\TripEnRoutePaused<\/a>"],[100,0,"<a href=\"WorkOrder\/Trip\/TripEnRouteResumed.php.html#12\">App\\Events\\WorkOrder\\Trip\\TripEnRouteResumed<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripEnRouteStart.php.html#11\">App\\Events\\WorkOrder\\Trip\\TripEnRouteStart<\/a>"],[100,0,"<a href=\"WorkOrder\/Trip\/TripEnd.php.html#11\">App\\Events\\WorkOrder\\Trip\\TripEnd<\/a>"],[100,0,"<a href=\"WorkOrder\/Trip\/TripPaused.php.html#12\">App\\Events\\WorkOrder\\Trip\\TripPaused<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripReScheduled.php.html#10\">App\\Events\\WorkOrder\\Trip\\TripReScheduled<\/a>"],[100,0,"<a href=\"WorkOrder\/Trip\/TripResume.php.html#12\">App\\Events\\WorkOrder\\Trip\\TripResume<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripScheduleInProgress.php.html#10\">App\\Events\\WorkOrder\\Trip\\TripScheduleInProgress<\/a>"],[100,0,"<a href=\"WorkOrder\/Trip\/TripScheduled.php.html#11\">App\\Events\\WorkOrder\\Trip\\TripScheduled<\/a>"],[100,0,"<a href=\"WorkOrder\/Trip\/TripWorking.php.html#12\">App\\Events\\WorkOrder\\Trip\\TripWorking<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderAddressUpdated.php.html#10\">App\\Events\\WorkOrder\\WorkOrderAddressUpdated<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderAssigneeAdded.php.html#16\">App\\Events\\WorkOrder\\WorkOrderAssigneeAdded<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderAssigneeRemoved.php.html#13\">App\\Events\\WorkOrder\\WorkOrderAssigneeRemoved<\/a>"],[0,6,"<a href=\"WorkOrder\/WorkOrderAssigneeUpdated.php.html#16\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated<\/a>"],[0,7,"<a href=\"WorkOrder\/WorkOrderBaseEvent.php.html#11\">App\\Events\\WorkOrder\\WorkOrderBaseEvent<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderDescriptionUpdated.php.html#10\">App\\Events\\WorkOrder\\WorkOrderDescriptionUpdated<\/a>"],[0,5,"<a href=\"WorkOrder\/WorkOrderDetailsUpdate.php.html#13\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate<\/a>"],[0,6,"<a href=\"WorkOrder\/WorkOrderHealthScoreChange.php.html#22\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange<\/a>"],[0,5,"<a href=\"WorkOrder\/WorkOrderListDeleted.php.html#15\">App\\Events\\WorkOrder\\WorkOrderListDeleted<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderPaused.php.html#10\">App\\Events\\WorkOrder\\WorkOrderPaused<\/a>"],[0,7,"<a href=\"WorkOrder\/WorkOrderPropertyAddressUpdated.php.html#16\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderReOpen.php.html#10\">App\\Events\\WorkOrder\\WorkOrderReOpen<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderReScheduled.php.html#10\">App\\Events\\WorkOrder\\WorkOrderReScheduled<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderResidentUpdated.php.html#10\">App\\Events\\WorkOrder\\WorkOrderResidentUpdated<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderResolve.php.html#10\">App\\Events\\WorkOrder\\WorkOrderResolve<\/a>"],[0,3,"<a href=\"WorkOrder\/WorkOrderSendToVendor.php.html#11\">App\\Events\\WorkOrder\\WorkOrderSendToVendor<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderServiceCallAssigned.php.html#16\">App\\Events\\WorkOrder\\WorkOrderServiceCallAssigned<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderServiceCallRescheduled.php.html#16\">App\\Events\\WorkOrder\\WorkOrderServiceCallRescheduled<\/a>"],[0,10,"<a href=\"WorkOrder\/WorkOrderStateChange.php.html#16\">App\\Events\\WorkOrder\\WorkOrderStateChange<\/a>"],[0,6,"<a href=\"WorkOrder\/WorkOrderTagUpdated.php.html#16\">App\\Events\\WorkOrder\\WorkOrderTagUpdated<\/a>"],[0,6,"<a href=\"WorkOrder\/WorkOrderUpdate.php.html#13\">App\\Events\\WorkOrder\\WorkOrderUpdate<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceCreated.php.html#24\">App\\Events\\Invoice\\BroadCast\\InvoiceCreated::__construct<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceCreated.php.html#32\">App\\Events\\Invoice\\BroadCast\\InvoiceCreated::broadcastQueue<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceCreated.php.html#42\">App\\Events\\Invoice\\BroadCast\\InvoiceCreated::broadcastOn<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceCreated.php.html#54\">App\\Events\\Invoice\\BroadCast\\InvoiceCreated::broadcastAs<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceCreated.php.html#64\">App\\Events\\Invoice\\BroadCast\\InvoiceCreated::broadcastWith<\/a>"],[0,2,"<a href=\"Invoice\/BroadCast\/InvoiceCreated.php.html#77\">App\\Events\\Invoice\\BroadCast\\InvoiceCreated::getWorkOrder<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceDeleted.php.html#24\">App\\Events\\Invoice\\BroadCast\\InvoiceDeleted::__construct<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceDeleted.php.html#32\">App\\Events\\Invoice\\BroadCast\\InvoiceDeleted::broadcastQueue<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceDeleted.php.html#42\">App\\Events\\Invoice\\BroadCast\\InvoiceDeleted::broadcastOn<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceDeleted.php.html#54\">App\\Events\\Invoice\\BroadCast\\InvoiceDeleted::broadcastAs<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceDeleted.php.html#64\">App\\Events\\Invoice\\BroadCast\\InvoiceDeleted::broadcastWith<\/a>"],[0,2,"<a href=\"Invoice\/BroadCast\/InvoiceDeleted.php.html#77\">App\\Events\\Invoice\\BroadCast\\InvoiceDeleted::getWorkOrder<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceUpdated.php.html#24\">App\\Events\\Invoice\\BroadCast\\InvoiceUpdated::__construct<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceUpdated.php.html#32\">App\\Events\\Invoice\\BroadCast\\InvoiceUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceUpdated.php.html#42\">App\\Events\\Invoice\\BroadCast\\InvoiceUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceUpdated.php.html#54\">App\\Events\\Invoice\\BroadCast\\InvoiceUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"Invoice\/BroadCast\/InvoiceUpdated.php.html#64\">App\\Events\\Invoice\\BroadCast\\InvoiceUpdated::broadcastWith<\/a>"],[0,2,"<a href=\"Invoice\/BroadCast\/InvoiceUpdated.php.html#77\">App\\Events\\Invoice\\BroadCast\\InvoiceUpdated::getWorkOrder<\/a>"],[0,1,"<a href=\"Invoice\/FullyPaid.php.html#20\">App\\Events\\Invoice\\FullyPaid::__construct<\/a>"],[0,1,"<a href=\"Invoice\/FullyPaid.php.html#25\">App\\Events\\Invoice\\FullyPaid::getWorkOrder<\/a>"],[0,1,"<a href=\"Invoice\/FullyPaid.php.html#33\">App\\Events\\Invoice\\FullyPaid::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"Invoice\/InvoiceCreatedWebhookEvent.php.html#20\">App\\Events\\Invoice\\InvoiceCreatedWebhookEvent::__construct<\/a>"],[0,1,"<a href=\"Invoice\/InvoiceCreatedWebhookEvent.php.html#25\">App\\Events\\Invoice\\InvoiceCreatedWebhookEvent::getWorkOrder<\/a>"],[0,5,"<a href=\"Invoice\/InvoiceCreatedWebhookEvent.php.html#33\">App\\Events\\Invoice\\InvoiceCreatedWebhookEvent::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"Invoice\/InvoiceUpdatedWebhookEvent.php.html#20\">App\\Events\\Invoice\\InvoiceUpdatedWebhookEvent::__construct<\/a>"],[0,1,"<a href=\"Invoice\/InvoiceUpdatedWebhookEvent.php.html#25\">App\\Events\\Invoice\\InvoiceUpdatedWebhookEvent::getWorkOrder<\/a>"],[0,5,"<a href=\"Invoice\/InvoiceUpdatedWebhookEvent.php.html#36\">App\\Events\\Invoice\\InvoiceUpdatedWebhookEvent::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"Invoice\/InvoiceVoidedWebhookEvent.php.html#20\">App\\Events\\Invoice\\InvoiceVoidedWebhookEvent::__construct<\/a>"],[0,1,"<a href=\"Invoice\/InvoiceVoidedWebhookEvent.php.html#25\">App\\Events\\Invoice\\InvoiceVoidedWebhookEvent::getWorkOrder<\/a>"],[0,1,"<a href=\"Invoice\/InvoiceVoidedWebhookEvent.php.html#30\">App\\Events\\Invoice\\InvoiceVoidedWebhookEvent::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"Invoice\/PartiallyPaid.php.html#20\">App\\Events\\Invoice\\PartiallyPaid::__construct<\/a>"],[0,1,"<a href=\"Invoice\/PartiallyPaid.php.html#25\">App\\Events\\Invoice\\PartiallyPaid::getWorkOrder<\/a>"],[0,1,"<a href=\"Invoice\/PartiallyPaid.php.html#33\">App\\Events\\Invoice\\PartiallyPaid::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"Issue\/BaseIssueEvent.php.html#20\">App\\Events\\Issue\\BaseIssueEvent::__construct<\/a>"],[0,1,"<a href=\"Issue\/BaseIssueEvent.php.html#25\">App\\Events\\Issue\\BaseIssueEvent::getIssueDetails<\/a>"],[0,1,"<a href=\"Issue\/BaseIssueEvent.php.html#51\">App\\Events\\Issue\\BaseIssueEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"Issue\/BaseIssueEvent.php.html#61\">App\\Events\\Issue\\BaseIssueEvent::broadcastOn<\/a>"],[0,1,"<a href=\"Issue\/BaseIssueEvent.php.html#77\">App\\Events\\Issue\\BaseIssueEvent::broadcastWith<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestDeletedIssueRestored.php.html#21\">App\\Events\\Issue\\ServiceRequestDeletedIssueRestored::broadcastAs<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueCanceled.php.html#21\">App\\Events\\Issue\\ServiceRequestIssueCanceled::broadcastAs<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueCreated.php.html#21\">App\\Events\\Issue\\ServiceRequestIssueCreated::broadcastAs<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueDeleted.php.html#24\">App\\Events\\Issue\\ServiceRequestIssueDeleted::broadcastAs<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueDeleted.php.html#34\">App\\Events\\Issue\\ServiceRequestIssueDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueDeleted.php.html#45\">App\\Events\\Issue\\ServiceRequestIssueDeleted::getIssueDetails<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueRestored.php.html#21\">App\\Events\\Issue\\ServiceRequestIssueRestored::broadcastAs<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueUnassigned.php.html#21\">App\\Events\\Issue\\ServiceRequestIssueUnassigned::broadcastAs<\/a>"],[0,1,"<a href=\"Issue\/ServiceRequestIssueUpdated.php.html#21\">App\\Events\\Issue\\ServiceRequestIssueUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/BaseIssueEvent.php.html#15\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/BaseIssueEvent.php.html#17\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent::getServiceRequestActivityLog<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/BaseIssueEvent.php.html#35\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/BaseIssueEvent.php.html#45\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/BaseIssueEvent.php.html#61\">App\\Events\\ServiceRequest\\ActivityLog\\BaseIssueEvent::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/ServiceRequestActivityLogCreated.php.html#21\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogCreated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/ServiceRequestActivityLogDeleted.php.html#22\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogDeleted::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/ServiceRequestActivityLogDeleted.php.html#32\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/ServiceRequestActivityLogDeleted.php.html#48\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogDeleted::getServiceRequestActivityLog<\/a>"],[0,1,"<a href=\"ServiceRequest\/ActivityLog\/ServiceRequestActivityLogUpdated.php.html#21\">App\\Events\\ServiceRequest\\ActivityLog\\ServiceRequestActivityLogUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/BaseServiceRequestEvent.php.html#20\">App\\Events\\ServiceRequest\\BaseServiceRequestEvent::__construct<\/a>"],[0,2,"<a href=\"ServiceRequest\/BaseServiceRequestEvent.php.html#25\">App\\Events\\ServiceRequest\\BaseServiceRequestEvent::getServiceRequestDetails<\/a>"],[0,1,"<a href=\"ServiceRequest\/BaseServiceRequestEvent.php.html#53\">App\\Events\\ServiceRequest\\BaseServiceRequestEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequest\/BaseServiceRequestEvent.php.html#63\">App\\Events\\ServiceRequest\\BaseServiceRequestEvent::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequest\/CreateServiceRequestActivityLog.php.html#31\">App\\Events\\ServiceRequest\\CreateServiceRequestActivityLog::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/NewServiceRequestCreated.php.html#20\">App\\Events\\ServiceRequest\\NewServiceRequestCreated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/NewServiceRequestCreated.php.html#29\">App\\Events\\ServiceRequest\\NewServiceRequestCreated::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequest\/NewServiceRequestCreated.php.html#39\">App\\Events\\ServiceRequest\\NewServiceRequestCreated::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequest\/NewServiceRequestCreated.php.html#49\">App\\Events\\ServiceRequest\\NewServiceRequestCreated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/NewServiceRequestCreated.php.html#59\">App\\Events\\ServiceRequest\\NewServiceRequestCreated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/PublicWebhook\/Lula\/ServiceRequestMediaDeleteLulaWebhook.php.html#20\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaDeleteLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/PublicWebhook\/Lula\/ServiceRequestMediaDeleteLulaWebhook.php.html#25\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaDeleteLulaWebhook::getServiceRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/PublicWebhook\/Lula\/ServiceRequestMediaDeleteLulaWebhook.php.html#30\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaDeleteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"ServiceRequest\/PublicWebhook\/Lula\/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#28\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaUploadCompleteLulaWebhook::__construct<\/a>"],[0,3,"<a href=\"ServiceRequest\/PublicWebhook\/Lula\/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#32\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaUploadCompleteLulaWebhook::fetchMedia<\/a>"],[0,2,"<a href=\"ServiceRequest\/PublicWebhook\/Lula\/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#51\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaUploadCompleteLulaWebhook::getServiceRequest<\/a>"],[0,1,"<a href=\"ServiceRequest\/PublicWebhook\/Lula\/ServiceRequestMediaUploadCompleteLulaWebhook.php.html#62\">App\\Events\\ServiceRequest\\PublicWebhook\\Lula\\ServiceRequestMediaUploadCompleteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"ServiceRequest\/ResidentAvailabilityBroadcast.php.html#18\">App\\Events\\ServiceRequest\\ResidentAvailabilityBroadcast::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ResidentAvailabilityBroadcast.php.html#29\">App\\Events\\ServiceRequest\\ResidentAvailabilityBroadcast::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequest\/ResidentAvailabilityBroadcast.php.html#39\">App\\Events\\ServiceRequest\\ResidentAvailabilityBroadcast::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ResidentAvailabilityBroadcast.php.html#49\">App\\Events\\ServiceRequest\\ResidentAvailabilityBroadcast::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/ResidentAvailabilityDeleted.php.html#21\">App\\Events\\ServiceRequest\\ResidentAvailabilityDeleted::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ResidentAvailabilityStore.php.html#21\">App\\Events\\ServiceRequest\\ResidentAvailabilityStore::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAccessInfoUpdated.php.html#14\">App\\Events\\ServiceRequest\\ServiceRequestAccessInfoUpdated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAddressUpdated.php.html#22\">App\\Events\\ServiceRequest\\ServiceRequestAddressUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAddressUpdated.php.html#34\">App\\Events\\ServiceRequest\\ServiceRequestAddressUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAddressUpdated.php.html#44\">App\\Events\\ServiceRequest\\ServiceRequestAddressUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssigneeAdded.php.html#23\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeAdded::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssigneeRemoved.php.html#20\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeRemoved::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssigneeUpdated.php.html#23\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssigneeUpdated.php.html#32\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssigneeUpdated.php.html#42\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated::broadcastOn<\/a>"],[0,2,"<a href=\"ServiceRequest\/ServiceRequestAssigneeUpdated.php.html#52\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAssigneeUpdated.php.html#66\">App\\Events\\ServiceRequest\\ServiceRequestAssigneeUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestAwaitingAvailability.php.html#17\">App\\Events\\ServiceRequest\\ServiceRequestAwaitingAvailability::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestCreated.php.html#18\">App\\Events\\ServiceRequest\\ServiceRequestCreated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestDescriptionUpdated.php.html#25\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestDescriptionUpdated.php.html#30\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestDescriptionUpdated.php.html#40\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestDescriptionUpdated.php.html#54\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestDescriptionUpdated.php.html#64\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::broadcastWith<\/a>"],[0,4,"<a href=\"ServiceRequest\/ServiceRequestDescriptionUpdated.php.html#73\">App\\Events\\ServiceRequest\\ServiceRequestDescriptionUpdated::getServiceRequestDescription<\/a>"],[0,6,"<a href=\"ServiceRequest\/ServiceRequestNoteCreated.php.html#32\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestNoteCreated.php.html#66\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated::broadcastQueue<\/a>"],[0,3,"<a href=\"ServiceRequest\/ServiceRequestNoteCreated.php.html#76\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestNoteCreated.php.html#93\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated::broadcastAs<\/a>"],[0,6,"<a href=\"ServiceRequest\/ServiceRequestNoteCreated.php.html#103\">App\\Events\\ServiceRequest\\ServiceRequestNoteCreated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestNoteDeleted.php.html#23\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestNoteDeleted.php.html#31\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestNoteDeleted.php.html#41\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestNoteDeleted.php.html#51\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestNoteDeleted.php.html#61\">App\\Events\\ServiceRequest\\ServiceRequestNoteDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestNoteUpdated.php.html#24\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestNoteUpdated.php.html#32\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestNoteUpdated.php.html#42\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestNoteUpdated.php.html#52\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated::broadcastAs<\/a>"],[0,4,"<a href=\"ServiceRequest\/ServiceRequestNoteUpdated.php.html#62\">App\\Events\\ServiceRequest\\ServiceRequestNoteUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestPriorityChanged.php.html#22\">App\\Events\\ServiceRequest\\ServiceRequestPriorityChanged::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestPriorityChanged.php.html#32\">App\\Events\\ServiceRequest\\ServiceRequestPriorityChanged::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestResidentUpdated.php.html#18\">App\\Events\\ServiceRequest\\ServiceRequestResidentUpdated::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestResidentUpdated.php.html#28\">App\\Events\\ServiceRequest\\ServiceRequestResidentUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestResidentUpdated.php.html#38\">App\\Events\\ServiceRequest\\ServiceRequestResidentUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestStateChange.php.html#27\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestStateChange.php.html#38\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequest\/ServiceRequestStateChange.php.html#51\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::broadcastAs<\/a>"],[0,3,"<a href=\"ServiceRequest\/ServiceRequestStateChange.php.html#61\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::broadcastWith<\/a>"],[0,2,"<a href=\"ServiceRequest\/ServiceRequestStateChange.php.html#109\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::getServiceRequest<\/a>"],[0,2,"<a href=\"ServiceRequest\/ServiceRequestStateChange.php.html#123\">App\\Events\\ServiceRequest\\ServiceRequestStateChange::getServiceRequestActivityLog<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#22\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::__construct<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#29\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::broadcastOn<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#44\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#52\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#62\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#78\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::getWorkOrderDetails<\/a>"],[0,2,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderBaseEvent.php.html#101\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderBaseEvent::currentWorkOrderAbilities<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderCreated.php.html#22\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderCreated::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderCreated.php.html#32\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderCreated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderCreated.php.html#44\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderCreated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderDeleted.php.html#20\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderDeleted::broadcastAs<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderDeleted.php.html#30\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderIssueAssigned.php.html#23\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderIssueAssigned::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/ServiceRequestWorkOrderIssueUpdated.php.html#23\">App\\Events\\ServiceRequest\\WorkOrder\\ServiceRequestWorkOrderIssueUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"ServiceRequest\/WorkOrder\/SyncServiceRequestWorkOrder.php.html#16\">App\\Events\\ServiceRequest\\WorkOrder\\SyncServiceRequestWorkOrder::__construct<\/a>"],[0,1,"<a href=\"Tag\/TagApplied.php.html#22\">App\\Events\\Tag\\TagApplied::__construct<\/a>"],[0,1,"<a href=\"Tag\/TagApplied.php.html#30\">App\\Events\\Tag\\TagApplied::broadcastQueue<\/a>"],[0,1,"<a href=\"Tag\/TagApplied.php.html#40\">App\\Events\\Tag\\TagApplied::broadcastOn<\/a>"],[0,1,"<a href=\"Tag\/TagApplied.php.html#50\">App\\Events\\Tag\\TagApplied::broadcastAs<\/a>"],[0,1,"<a href=\"Tag\/TagApplied.php.html#60\">App\\Events\\Tag\\TagApplied::broadcastWith<\/a>"],[0,1,"<a href=\"User\/DefaultPinnedQuotesView.php.html#18\">App\\Events\\User\\DefaultPinnedQuotesView::__construct<\/a>"],[0,1,"<a href=\"User\/DefaultPinnedQuotesView.php.html#28\">App\\Events\\User\\DefaultPinnedQuotesView::broadcastOn<\/a>"],[100,1,"<a href=\"User\/UserInvited.php.html#18\">App\\Events\\User\\UserInvited::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderAccessInfoUpdated.php.html#22\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderAccessInfoUpdated.php.html#30\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated::getWorkOrderDetails<\/a>"],[0,3,"<a href=\"WorkOrder\/Actions\/WorkOrderAccessInfoUpdated.php.html#54\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderAwaitingAvailability.php.html#20\">App\\Events\\WorkOrder\\Actions\\WorkOrderAwaitingAvailability::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderAwaitingAvailability.php.html#30\">App\\Events\\WorkOrder\\Actions\\WorkOrderAwaitingAvailability::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderCanceled.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderCanceled.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderCanceled.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderClaimPending.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderClaimPending.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderClaimPending.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderCreated.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderCreated.php.html#31\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated::broadcastWith<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderCreated.php.html#50\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderDueDateChanged.php.html#22\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderDueDateChanged.php.html#30\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderDueDateChanged.php.html#49\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderNTESet.php.html#23\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderNTESet.php.html#33\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::broadcastOn<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderNTESet.php.html#45\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderNTESet.php.html#64\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderNTEUpdated.php.html#23\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderNTEUpdated.php.html#33\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::broadcastOn<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderNTEUpdated.php.html#45\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderNTEUpdated.php.html#64\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderPriorityChanged.php.html#24\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderPriorityChanged.php.html#32\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderPriorityChanged.php.html#51\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderQualityCheck.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderQualityCheck.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderQualityCheck.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderReadyToSchedule.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderReadyToSchedule.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderReadyToSchedule.php.html#49\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderScheduled.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderScheduled.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderScheduled.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderWorkInProgress.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/Actions\/WorkOrderWorkInProgress.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Actions\/WorkOrderWorkInProgress.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/ActivityLog\/BaseIssueEvent.php.html#16\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent::__construct<\/a>"],[0,2,"<a href=\"WorkOrder\/ActivityLog\/BaseIssueEvent.php.html#18\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent::getWorkOrderActivityLog<\/a>"],[0,1,"<a href=\"WorkOrder\/ActivityLog\/BaseIssueEvent.php.html#37\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/ActivityLog\/BaseIssueEvent.php.html#47\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent::broadcastOn<\/a>"],[0,3,"<a href=\"WorkOrder\/ActivityLog\/BaseIssueEvent.php.html#63\">App\\Events\\WorkOrder\\ActivityLog\\BaseIssueEvent::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/ActivityLog\/WorkOrderActivityLogCreated.php.html#21\">App\\Events\\WorkOrder\\ActivityLog\\WorkOrderActivityLogCreated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/ActivityLogEntry.php.html#22\">App\\Events\\WorkOrder\\ActivityLogEntry::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/ActivityLogEntry.php.html#33\">App\\Events\\WorkOrder\\ActivityLogEntry::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/ActivityLogEntry.php.html#43\">App\\Events\\WorkOrder\\ActivityLogEntry::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/ActivityLogEntry.php.html#53\">App\\Events\\WorkOrder\\ActivityLogEntry::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/ExternalWorkOrderUpdateEvent.php.html#35\">App\\Events\\WorkOrder\\ExternalWorkOrderUpdateEvent::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueAssigned.php.html#21\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueAssigned::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueBaseEvent.php.html#17\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueBaseEvent.php.html#22\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::getWorkOrderIssueDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueBaseEvent.php.html#50\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueBaseEvent.php.html#60\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueBaseEvent.php.html#74\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/Issue\/WorkOrderIssueBaseEvent.php.html#84\">App\\Events\\WorkOrder\\Issue\\WorkOrderIssueBaseEvent::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/WorkOrderNoteBaseEvent.php.html#17\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::__construct<\/a>"],[0,2,"<a href=\"WorkOrder\/Note\/WorkOrderNoteBaseEvent.php.html#22\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::getWorkOrderNoteDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/WorkOrderNoteBaseEvent.php.html#55\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/WorkOrderNoteBaseEvent.php.html#65\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/WorkOrderNoteBaseEvent.php.html#79\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/WorkOrderNoteBaseEvent.php.html#89\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteBaseEvent::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/WorkOrderNoteCreated.php.html#21\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteCreated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/WorkOrderNoteDeleted.php.html#22\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteDeleted::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/Note\/WorkOrderNoteDeleted.php.html#30\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteDeleted::getWorkOrderNoteDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/Note\/WorkOrderNoteDeleted.php.html#56\">App\\Events\\WorkOrder\\Note\\WorkOrderNoteDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/AwaitingAvailabilityLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\AwaitingAvailabilityLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/AwaitingAvailabilityLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\AwaitingAvailabilityLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/AwaitingAvailabilityLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\AwaitingAvailabilityLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/MediaDeleteLulaWebhook.php.html#21\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaDeleteLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/MediaDeleteLulaWebhook.php.html#26\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaDeleteLulaWebhook::getWorkOrder<\/a>"],[0,2,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/MediaDeleteLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaDeleteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/MediaUploadCompleteLulaWebhook.php.html#29\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaUploadCompleteLulaWebhook::__construct<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/MediaUploadCompleteLulaWebhook.php.html#33\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaUploadCompleteLulaWebhook::fetchMedia<\/a>"],[0,2,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/MediaUploadCompleteLulaWebhook.php.html#53\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaUploadCompleteLulaWebhook::getWorkOrder<\/a>"],[0,2,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/MediaUploadCompleteLulaWebhook.php.html#64\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\MediaUploadCompleteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteApproveLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteApproveLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteApproveLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteApproveLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteApproveLulaWebhook.php.html#39\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteApproveLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteCreateLulaWebhook.php.html#29\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteCreateLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteCreateLulaWebhook.php.html#37\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteCreateLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteCreateLulaWebhook.php.html#45\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteCreateLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteRejectLulaWebhook.php.html#24\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteRejectLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteRejectLulaWebhook.php.html#32\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteRejectLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteRejectLulaWebhook.php.html#40\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteRejectLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteSubmitForApprovalLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteSubmitForApprovalLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteSubmitForApprovalLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteSubmitForApprovalLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteSubmitForApprovalLulaWebhook.php.html#36\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteSubmitForApprovalLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskCreatedLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskCreatedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskCreatedLulaWebhook.php.html#29\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskCreatedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskCreatedLulaWebhook.php.html#34\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskCreatedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskDeletedLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskDeletedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskDeletedLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskDeletedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskDeletedLulaWebhook.php.html#33\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskDeletedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskMediaCreatedLulaWebhook.php.html#21\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskMediaCreatedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskMediaCreatedLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskMediaCreatedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskMediaCreatedLulaWebhook.php.html#33\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskMediaCreatedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskUpdatedLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskUpdatedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskUpdatedLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskUpdatedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/QuoteTaskUpdatedLulaWebhook.php.html#33\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\QuoteTaskUpdatedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripEnRouteLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripEnRouteLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripEnRouteLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripEnRouteLulaWebhook::getWorkOrder<\/a>"],[0,4,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripEnRouteLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripEnRouteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripPausedLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripPausedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripPausedLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripPausedLulaWebhook::getWorkOrder<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripPausedLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripPausedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripResumedLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripResumedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripResumedLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripResumedLulaWebhook::getWorkOrder<\/a>"],[0,3,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripResumedLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripResumedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripScheduledLulaWebhook.php.html#17\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripScheduledLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripScheduledLulaWebhook.php.html#22\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripScheduledLulaWebhook::getWorkOrder<\/a>"],[0,7,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripScheduledLulaWebhook.php.html#50\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripScheduledLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripStoppedLulaWebhook.php.html#20\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripStoppedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripStoppedLulaWebhook.php.html#24\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripStoppedLulaWebhook::getWorkOrder<\/a>"],[0,5,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripStoppedLulaWebhook.php.html#32\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripStoppedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripUpdatedLulaWebhook.php.html#24\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripUpdatedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripUpdatedLulaWebhook.php.html#30\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripUpdatedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/TripUpdatedLulaWebhook.php.html#35\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\TripUpdatedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorKOrderReadyToInvoiceLulaWebhook.php.html#22\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorKOrderReadyToInvoiceLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorKOrderReadyToInvoiceLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorKOrderReadyToInvoiceLulaWebhook::getWorkOrder<\/a>"],[0,4,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorKOrderReadyToInvoiceLulaWebhook.php.html#36\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorKOrderReadyToInvoiceLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderCanceledLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCanceledLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderCanceledLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCanceledLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderCanceledLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCanceledLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderCompleteLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCompleteLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderCompleteLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCompleteLulaWebhook::getWorkOrder<\/a>"],[0,4,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderCompleteLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderCompleteLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderInProgressLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderInProgressLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderInProgressLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderInProgressLulaWebhook::getWorkOrder<\/a>"],[0,4,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderInProgressLulaWebhook.php.html#31\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderInProgressLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderPausedLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderPausedLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderPausedLulaWebhook.php.html#21\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderPausedLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderPausedLulaWebhook.php.html#29\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderPausedLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderReOpenLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderReOpenLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderReOpenLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderReOpenLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderReOpenLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderReOpenLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderResolveLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderResolveLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderResolveLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderResolveLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderResolveLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderResolveLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderScopingLulaWebhook.php.html#19\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderScopingLulaWebhook::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderScopingLulaWebhook.php.html#23\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderScopingLulaWebhook::getWorkOrder<\/a>"],[0,1,"<a href=\"WorkOrder\/PublicWebhook\/Lula\/WorkOrderScopingLulaWebhook.php.html#28\">App\\Events\\WorkOrder\\PublicWebhook\\Lula\\WorkOrderScopingLulaWebhook::prepareWebhookPayload<\/a>"],[0,1,"<a href=\"WorkOrder\/Quote\/QuoteStateChange.php.html#27\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/Quote\/QuoteStateChange.php.html#37\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/Quote\/QuoteStateChange.php.html#50\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/Quote\/QuoteStateChange.php.html#62\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::broadcastWith<\/a>"],[0,2,"<a href=\"WorkOrder\/Quote\/QuoteStateChange.php.html#85\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::getWorkOrder<\/a>"],[0,2,"<a href=\"WorkOrder\/Quote\/QuoteStateChange.php.html#122\">App\\Events\\WorkOrder\\Quote\\QuoteStateChange::getWorkOrderActivityLog<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentAvailabilityBroadcast.php.html#18\">App\\Events\\WorkOrder\\ResidentAvailabilityBroadcast::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentAvailabilityBroadcast.php.html#29\">App\\Events\\WorkOrder\\ResidentAvailabilityBroadcast::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentAvailabilityBroadcast.php.html#39\">App\\Events\\WorkOrder\\ResidentAvailabilityBroadcast::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentAvailabilityBroadcast.php.html#49\">App\\Events\\WorkOrder\\ResidentAvailabilityBroadcast::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentAvailabilityDeleted.php.html#21\">App\\Events\\WorkOrder\\ResidentAvailabilityDeleted::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentAvailabilityDeletedEvent.php.html#18\">App\\Events\\WorkOrder\\ResidentAvailabilityDeletedEvent::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentAvailabilityDeletedEvent.php.html#28\">App\\Events\\WorkOrder\\ResidentAvailabilityDeletedEvent::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentAvailabilityDeletedEvent.php.html#38\">App\\Events\\WorkOrder\\ResidentAvailabilityDeletedEvent::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentAvailabilityDeletedEvent.php.html#48\">App\\Events\\WorkOrder\\ResidentAvailabilityDeletedEvent::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentUpdated.php.html#31\">App\\Events\\WorkOrder\\ResidentUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentUpdated.php.html#42\">App\\Events\\WorkOrder\\ResidentUpdated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentUpdated.php.html#53\">App\\Events\\WorkOrder\\ResidentUpdated::getServiceRequestDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentUpdated.php.html#69\">App\\Events\\WorkOrder\\ResidentUpdated::getResidentDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentUpdated.php.html#86\">App\\Events\\WorkOrder\\ResidentUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentUpdated.php.html#96\">App\\Events\\WorkOrder\\ResidentUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/ResidentUpdated.php.html#110\">App\\Events\\WorkOrder\\ResidentUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripBaseEvent.php.html#17\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::__construct<\/a>"],[0,2,"<a href=\"WorkOrder\/Trip\/TripBaseEvent.php.html#22\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::getTrip<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripBaseEvent.php.html#40\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripBaseEvent.php.html#50\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripBaseEvent.php.html#62\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripBaseEvent.php.html#72\">App\\Events\\WorkOrder\\Trip\\TripBaseEvent::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripCanceled.php.html#17\">App\\Events\\WorkOrder\\Trip\\TripCanceled::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripEnRouteStart.php.html#18\">App\\Events\\WorkOrder\\Trip\\TripEnRouteStart::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripReScheduled.php.html#17\">App\\Events\\WorkOrder\\Trip\\TripReScheduled::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/Trip\/TripScheduleInProgress.php.html#17\">App\\Events\\WorkOrder\\Trip\\TripScheduleInProgress::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderAddressUpdated.php.html#14\">App\\Events\\WorkOrder\\WorkOrderAddressUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderAssigneeAdded.php.html#23\">App\\Events\\WorkOrder\\WorkOrderAssigneeAdded::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderAssigneeRemoved.php.html#20\">App\\Events\\WorkOrder\\WorkOrderAssigneeRemoved::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderAssigneeUpdated.php.html#23\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderAssigneeUpdated.php.html#32\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderAssigneeUpdated.php.html#42\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated::broadcastOn<\/a>"],[0,2,"<a href=\"WorkOrder\/WorkOrderAssigneeUpdated.php.html#52\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderAssigneeUpdated.php.html#66\">App\\Events\\WorkOrder\\WorkOrderAssigneeUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderBaseEvent.php.html#18\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::__construct<\/a>"],[0,2,"<a href=\"WorkOrder\/WorkOrderBaseEvent.php.html#23\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderBaseEvent.php.html#82\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderBaseEvent.php.html#92\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderBaseEvent.php.html#104\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderBaseEvent.php.html#114\">App\\Events\\WorkOrder\\WorkOrderBaseEvent::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderDescriptionUpdated.php.html#14\">App\\Events\\WorkOrder\\WorkOrderDescriptionUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderDetailsUpdate.php.html#22\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderDetailsUpdate.php.html#27\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderDetailsUpdate.php.html#37\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderDetailsUpdate.php.html#53\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderDetailsUpdate.php.html#63\">App\\Events\\WorkOrder\\WorkOrderDetailsUpdate::broadcastWith<\/a>"],[0,2,"<a href=\"WorkOrder\/WorkOrderHealthScoreChange.php.html#29\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderHealthScoreChange.php.html#47\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderHealthScoreChange.php.html#57\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderHealthScoreChange.php.html#67\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderHealthScoreChange.php.html#77\">App\\Events\\WorkOrder\\WorkOrderHealthScoreChange::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderListDeleted.php.html#24\">App\\Events\\WorkOrder\\WorkOrderListDeleted::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderListDeleted.php.html#31\">App\\Events\\WorkOrder\\WorkOrderListDeleted::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderListDeleted.php.html#41\">App\\Events\\WorkOrder\\WorkOrderListDeleted::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderListDeleted.php.html#56\">App\\Events\\WorkOrder\\WorkOrderListDeleted::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderListDeleted.php.html#66\">App\\Events\\WorkOrder\\WorkOrderListDeleted::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderPaused.php.html#17\">App\\Events\\WorkOrder\\WorkOrderPaused::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderPropertyAddressUpdated.php.html#26\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderPropertyAddressUpdated.php.html#31\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderPropertyAddressUpdated.php.html#39\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::getPropertyDetails<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderPropertyAddressUpdated.php.html#47\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderPropertyAddressUpdated.php.html#57\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderPropertyAddressUpdated.php.html#69\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderPropertyAddressUpdated.php.html#79\">App\\Events\\WorkOrder\\WorkOrderPropertyAddressUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderReOpen.php.html#17\">App\\Events\\WorkOrder\\WorkOrderReOpen::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderReScheduled.php.html#17\">App\\Events\\WorkOrder\\WorkOrderReScheduled::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderResidentUpdated.php.html#14\">App\\Events\\WorkOrder\\WorkOrderResidentUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderResolve.php.html#17\">App\\Events\\WorkOrder\\WorkOrderResolve::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderSendToVendor.php.html#18\">App\\Events\\WorkOrder\\WorkOrderSendToVendor::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderSendToVendor.php.html#23\">App\\Events\\WorkOrder\\WorkOrderSendToVendor::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderSendToVendor.php.html#28\">App\\Events\\WorkOrder\\WorkOrderSendToVendor::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderServiceCallAssigned.php.html#23\">App\\Events\\WorkOrder\\WorkOrderServiceCallAssigned::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderServiceCallRescheduled.php.html#23\">App\\Events\\WorkOrder\\WorkOrderServiceCallRescheduled::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderStateChange.php.html#29\">App\\Events\\WorkOrder\\WorkOrderStateChange::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderStateChange.php.html#40\">App\\Events\\WorkOrder\\WorkOrderStateChange::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderStateChange.php.html#53\">App\\Events\\WorkOrder\\WorkOrderStateChange::broadcastAs<\/a>"],[0,3,"<a href=\"WorkOrder\/WorkOrderStateChange.php.html#63\">App\\Events\\WorkOrder\\WorkOrderStateChange::broadcastWith<\/a>"],[0,2,"<a href=\"WorkOrder\/WorkOrderStateChange.php.html#112\">App\\Events\\WorkOrder\\WorkOrderStateChange::getWorkOrder<\/a>"],[0,2,"<a href=\"WorkOrder\/WorkOrderStateChange.php.html#149\">App\\Events\\WorkOrder\\WorkOrderStateChange::getWorkOrderActivityLog<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderTagUpdated.php.html#25\">App\\Events\\WorkOrder\\WorkOrderTagUpdated::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderTagUpdated.php.html#36\">App\\Events\\WorkOrder\\WorkOrderTagUpdated::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderTagUpdated.php.html#46\">App\\Events\\WorkOrder\\WorkOrderTagUpdated::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderTagUpdated.php.html#56\">App\\Events\\WorkOrder\\WorkOrderTagUpdated::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/WorkOrderTagUpdated.php.html#66\">App\\Events\\WorkOrder\\WorkOrderTagUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderUpdate.php.html#22\">App\\Events\\WorkOrder\\WorkOrderUpdate::__construct<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderUpdate.php.html#30\">App\\Events\\WorkOrder\\WorkOrderUpdate::broadcastQueue<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderUpdate.php.html#40\">App\\Events\\WorkOrder\\WorkOrderUpdate::broadcastOn<\/a>"],[0,1,"<a href=\"WorkOrder\/WorkOrderUpdate.php.html#55\">App\\Events\\WorkOrder\\WorkOrderUpdate::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrder\/WorkOrderUpdate.php.html#65\">App\\Events\\WorkOrder\\WorkOrderUpdate::broadcastWith<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Events/WorkOrder/Actions</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Events</a></li>
         <li class="breadcrumb-item"><a href="../index.html">WorkOrder</a></li>
         <li class="breadcrumb-item"><a href="index.html">Actions</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderAccessInfoUpdated.php.html#15">App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAwaitingAvailability.php.html#13">App\Events\WorkOrder\Actions\WorkOrderAwaitingAvailability</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderCanceled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCanceled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderClaimPending.php.html#14">App\Events\WorkOrder\Actions\WorkOrderClaimPending</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderCreated.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCreated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDueDateChanged.php.html#15">App\Events\WorkOrder\Actions\WorkOrderDueDateChanged</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNTESet.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTESet</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNTEUpdated.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTEUpdated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPriorityChanged.php.html#17">App\Events\WorkOrder\Actions\WorkOrderPriorityChanged</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderQualityCheck.php.html#14">App\Events\WorkOrder\Actions\WorkOrderQualityCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReadyToSchedule.php.html#14">App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderScheduled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderScheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderWorkInProgress.php.html#14">App\Events\WorkOrder\Actions\WorkOrderWorkInProgress</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderAccessInfoUpdated.php.html#15">App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="WorkOrderNTESet.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTESet</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderNTEUpdated.php.html#16">App\Events\WorkOrder\Actions\WorkOrderNTEUpdated</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="WorkOrderCanceled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCanceled</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderClaimPending.php.html#14">App\Events\WorkOrder\Actions\WorkOrderClaimPending</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderCreated.php.html#14">App\Events\WorkOrder\Actions\WorkOrderCreated</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderDueDateChanged.php.html#15">App\Events\WorkOrder\Actions\WorkOrderDueDateChanged</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderPriorityChanged.php.html#17">App\Events\WorkOrder\Actions\WorkOrderPriorityChanged</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderQualityCheck.php.html#14">App\Events\WorkOrder\Actions\WorkOrderQualityCheck</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderReadyToSchedule.php.html#14">App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderScheduled.php.html#14">App\Events\WorkOrder\Actions\WorkOrderScheduled</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderWorkInProgress.php.html#14">App\Events\WorkOrder\Actions\WorkOrderWorkInProgress</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderAccessInfoUpdated.php.html#22"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAccessInfoUpdated.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAccessInfoUpdated.php.html#54"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAwaitingAvailability.php.html#20"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAwaitingAvailability::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderAwaitingAvailability.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAwaitingAvailability::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderCanceled.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderCanceled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderCanceled.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderClaimPending.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderClaimPending.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderClaimPending.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderCreated.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderCreated.php.html#31"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderCreated.php.html#50"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDueDateChanged.php.html#22"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDueDateChanged.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderDueDateChanged.php.html#49"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNTESet.php.html#23"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNTESet.php.html#33"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNTESet.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNTESet.php.html#64"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNTEUpdated.php.html#23"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNTEUpdated.php.html#33"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::broadcastOn">broadcastOn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNTEUpdated.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderNTEUpdated.php.html#64"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPriorityChanged.php.html#24"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPriorityChanged.php.html#32"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderPriorityChanged.php.html#51"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderQualityCheck.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderQualityCheck.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderQualityCheck.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReadyToSchedule.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReadyToSchedule.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderReadyToSchedule.php.html#49"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderScheduled.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderScheduled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderScheduled.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderWorkInProgress.php.html#21"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::broadcastAs">broadcastAs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderWorkInProgress.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderWorkInProgress.php.html#48"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="WorkOrderAccessInfoUpdated.php.html#54"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::broadcastWith">broadcastWith</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderAccessInfoUpdated.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderAccessInfoUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderCanceled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCanceled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderClaimPending.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderClaimPending::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderCreated.php.html#50"><abbr title="App\Events\WorkOrder\Actions\WorkOrderCreated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderDueDateChanged.php.html#30"><abbr title="App\Events\WorkOrder\Actions\WorkOrderDueDateChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderNTESet.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTESet::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderNTEUpdated.php.html#45"><abbr title="App\Events\WorkOrder\Actions\WorkOrderNTEUpdated::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderPriorityChanged.php.html#32"><abbr title="App\Events\WorkOrder\Actions\WorkOrderPriorityChanged::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderQualityCheck.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderQualityCheck::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderReadyToSchedule.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderScheduled.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderScheduled::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WorkOrderWorkInProgress.php.html#29"><abbr title="App\Events\WorkOrder\Actions\WorkOrderWorkInProgress::getWorkOrderDetails">getWorkOrderDetails</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:22:08 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([13,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([40,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"WorkOrderAccessInfoUpdated.php.html#15\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated<\/a>"],[0,2,"<a href=\"WorkOrderAwaitingAvailability.php.html#13\">App\\Events\\WorkOrder\\Actions\\WorkOrderAwaitingAvailability<\/a>"],[0,4,"<a href=\"WorkOrderCanceled.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled<\/a>"],[0,4,"<a href=\"WorkOrderClaimPending.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending<\/a>"],[0,4,"<a href=\"WorkOrderCreated.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated<\/a>"],[0,4,"<a href=\"WorkOrderDueDateChanged.php.html#15\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged<\/a>"],[0,5,"<a href=\"WorkOrderNTESet.php.html#16\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet<\/a>"],[0,5,"<a href=\"WorkOrderNTEUpdated.php.html#16\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated<\/a>"],[0,4,"<a href=\"WorkOrderPriorityChanged.php.html#17\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged<\/a>"],[0,4,"<a href=\"WorkOrderQualityCheck.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck<\/a>"],[0,4,"<a href=\"WorkOrderReadyToSchedule.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule<\/a>"],[0,4,"<a href=\"WorkOrderScheduled.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled<\/a>"],[0,4,"<a href=\"WorkOrderWorkInProgress.php.html#14\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"WorkOrderAccessInfoUpdated.php.html#22\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrderAccessInfoUpdated.php.html#30\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated::getWorkOrderDetails<\/a>"],[0,3,"<a href=\"WorkOrderAccessInfoUpdated.php.html#54\">App\\Events\\WorkOrder\\Actions\\WorkOrderAccessInfoUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderAwaitingAvailability.php.html#20\">App\\Events\\WorkOrder\\Actions\\WorkOrderAwaitingAvailability::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrderAwaitingAvailability.php.html#30\">App\\Events\\WorkOrder\\Actions\\WorkOrderAwaitingAvailability::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderCanceled.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrderCanceled.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderCanceled.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderCanceled::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderClaimPending.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrderClaimPending.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderClaimPending.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderClaimPending::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderCreated.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrderCreated.php.html#31\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated::broadcastWith<\/a>"],[0,2,"<a href=\"WorkOrderCreated.php.html#50\">App\\Events\\WorkOrder\\Actions\\WorkOrderCreated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderDueDateChanged.php.html#22\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrderDueDateChanged.php.html#30\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderDueDateChanged.php.html#49\">App\\Events\\WorkOrder\\Actions\\WorkOrderDueDateChanged::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderNTESet.php.html#23\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrderNTESet.php.html#33\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::broadcastOn<\/a>"],[0,2,"<a href=\"WorkOrderNTESet.php.html#45\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderNTESet.php.html#64\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTESet::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderNTEUpdated.php.html#23\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::broadcastAs<\/a>"],[0,1,"<a href=\"WorkOrderNTEUpdated.php.html#33\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::broadcastOn<\/a>"],[0,2,"<a href=\"WorkOrderNTEUpdated.php.html#45\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderNTEUpdated.php.html#64\">App\\Events\\WorkOrder\\Actions\\WorkOrderNTEUpdated::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderPriorityChanged.php.html#24\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrderPriorityChanged.php.html#32\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderPriorityChanged.php.html#51\">App\\Events\\WorkOrder\\Actions\\WorkOrderPriorityChanged::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderQualityCheck.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrderQualityCheck.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderQualityCheck.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderQualityCheck::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderReadyToSchedule.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrderReadyToSchedule.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderReadyToSchedule.php.html#49\">App\\Events\\WorkOrder\\Actions\\WorkOrderReadyToSchedule::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderScheduled.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrderScheduled.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderScheduled.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderScheduled::broadcastWith<\/a>"],[0,1,"<a href=\"WorkOrderWorkInProgress.php.html#21\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress::broadcastAs<\/a>"],[0,2,"<a href=\"WorkOrderWorkInProgress.php.html#29\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress::getWorkOrderDetails<\/a>"],[0,1,"<a href=\"WorkOrderWorkInProgress.php.html#48\">App\\Events\\WorkOrder\\Actions\\WorkOrderWorkInProgress::broadcastWith<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

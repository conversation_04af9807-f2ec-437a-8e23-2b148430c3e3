<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Fixer/ClassNotation</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Fixer</a></li>
         <li class="breadcrumb-item"><a href="index.html">ClassNotation</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ControllerOrderFixer.php.html#11">App\Fixer\ClassNotation\ControllerOrderFixer</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#42">App\Fixer\ClassNotation\OrderedClassElementsFixer</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="OrderedClassElementsFixer.php.html#42">App\Fixer\ClassNotation\OrderedClassElementsFixer</a></td><td class="text-right">4830</td></tr>
       <tr><td><a href="ControllerOrderFixer.php.html#11">App\Fixer\ClassNotation\ControllerOrderFixer</a></td><td class="text-right">306</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ControllerOrderFixer.php.html#13"><abbr title="App\Fixer\ClassNotation\ControllerOrderFixer::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ControllerOrderFixer.php.html#18"><abbr title="App\Fixer\ClassNotation\ControllerOrderFixer::configure">configure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ControllerOrderFixer.php.html#57"><abbr title="App\Fixer\ClassNotation\ControllerOrderFixer::getPriority">getPriority</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ControllerOrderFixer.php.html#62"><abbr title="App\Fixer\ClassNotation\ControllerOrderFixer::isControllerClass">isControllerClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ControllerOrderFixer.php.html#106"><abbr title="App\Fixer\ClassNotation\ControllerOrderFixer::applyFix">applyFix</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#111"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::getConfigurationDefinition">getConfigurationDefinition</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#116"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#124"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::configure">configure</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#171"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::isCandidate">isCandidate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#179"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::getDefinition">getDefinition</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#249"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::getPriority">getPriority</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#257"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::applyFix">applyFix</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#285"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::createConfigurationDefinition">createConfigurationDefinition</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#344"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::getElements">getElements</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#422"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::detectElementType">detectElementType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#472"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::findElementEnd">findElementEnd</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#500"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::sortElements">sortElements</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#594"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::sortGroupElements">sortGroupElements</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#618"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::sortTokens">sortTokens</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="OrderedClassElementsFixer.php.html#344"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::getElements">getElements</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#500"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::sortElements">sortElements</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ControllerOrderFixer.php.html#62"><abbr title="App\Fixer\ClassNotation\ControllerOrderFixer::isControllerClass">isControllerClass</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#124"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::configure">configure</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#422"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::detectElementType">detectElementType</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#257"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::applyFix">applyFix</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#285"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::createConfigurationDefinition">createConfigurationDefinition</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#472"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::findElementEnd">findElementEnd</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ControllerOrderFixer.php.html#106"><abbr title="App\Fixer\ClassNotation\ControllerOrderFixer::applyFix">applyFix</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#618"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::sortTokens">sortTokens</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OrderedClassElementsFixer.php.html#594"><abbr title="App\Fixer\ClassNotation\OrderedClassElementsFixer::sortGroupElements">sortGroupElements</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 9:22:08 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([2,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([19,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,17,"<a href=\"ControllerOrderFixer.php.html#11\">App\\Fixer\\ClassNotation\\ControllerOrderFixer<\/a>"],[0,69,"<a href=\"OrderedClassElementsFixer.php.html#42\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ControllerOrderFixer.php.html#13\">App\\Fixer\\ClassNotation\\ControllerOrderFixer::getName<\/a>"],[0,1,"<a href=\"ControllerOrderFixer.php.html#18\">App\\Fixer\\ClassNotation\\ControllerOrderFixer::configure<\/a>"],[0,1,"<a href=\"ControllerOrderFixer.php.html#57\">App\\Fixer\\ClassNotation\\ControllerOrderFixer::getPriority<\/a>"],[0,10,"<a href=\"ControllerOrderFixer.php.html#62\">App\\Fixer\\ClassNotation\\ControllerOrderFixer::isControllerClass<\/a>"],[0,4,"<a href=\"ControllerOrderFixer.php.html#106\">App\\Fixer\\ClassNotation\\ControllerOrderFixer::applyFix<\/a>"],[0,1,"<a href=\"OrderedClassElementsFixer.php.html#111\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::getConfigurationDefinition<\/a>"],[0,1,"<a href=\"OrderedClassElementsFixer.php.html#116\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::getName<\/a>"],[0,10,"<a href=\"OrderedClassElementsFixer.php.html#124\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::configure<\/a>"],[0,1,"<a href=\"OrderedClassElementsFixer.php.html#171\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::isCandidate<\/a>"],[0,1,"<a href=\"OrderedClassElementsFixer.php.html#179\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::getDefinition<\/a>"],[0,1,"<a href=\"OrderedClassElementsFixer.php.html#249\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::getPriority<\/a>"],[0,5,"<a href=\"OrderedClassElementsFixer.php.html#257\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::applyFix<\/a>"],[0,5,"<a href=\"OrderedClassElementsFixer.php.html#285\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::createConfigurationDefinition<\/a>"],[0,13,"<a href=\"OrderedClassElementsFixer.php.html#344\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::getElements<\/a>"],[0,9,"<a href=\"OrderedClassElementsFixer.php.html#422\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::detectElementType<\/a>"],[0,5,"<a href=\"OrderedClassElementsFixer.php.html#472\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::findElementEnd<\/a>"],[0,12,"<a href=\"OrderedClassElementsFixer.php.html#500\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::sortElements<\/a>"],[0,2,"<a href=\"OrderedClassElementsFixer.php.html#594\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::sortGroupElements<\/a>"],[0,3,"<a href=\"OrderedClassElementsFixer.php.html#618\">App\\Fixer\\ClassNotation\\OrderedClassElementsFixer::sortTokens<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

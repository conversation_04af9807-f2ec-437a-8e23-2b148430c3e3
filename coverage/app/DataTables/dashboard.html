<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/DataTables</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform</a></li>
         <li class="breadcrumb-item"><a href="../index.html">app</a></li>
         <li class="breadcrumb-item"><a href="index.html">DataTables</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DeveloperAlertLogsDataTable.php.html#14">App\DataTables\DeveloperAlertLogsDataTable</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEventsDataTable.php.html#14">App\DataTables\PublicApiWorkOrderWebhookEventsDataTable</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#15">App\DataTables\RequestLogsDataTable</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPublicApiLogDataTable.php.html#14">App\DataTables\VendorPublicApiLogDataTable</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCallsDataTable.php.html#12">App\DataTables\WebhookCallsDataTable</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="RequestLogsDataTable.php.html#15">App\DataTables\RequestLogsDataTable</a></td><td class="text-right">870</td></tr>
       <tr><td><a href="VendorPublicApiLogDataTable.php.html#14">App\DataTables\VendorPublicApiLogDataTable</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="DeveloperAlertLogsDataTable.php.html#14">App\DataTables\DeveloperAlertLogsDataTable</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEventsDataTable.php.html#14">App\DataTables\PublicApiWorkOrderWebhookEventsDataTable</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="WebhookCallsDataTable.php.html#12">App\DataTables\WebhookCallsDataTable</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DeveloperAlertLogsDataTable.php.html#21"><abbr title="App\DataTables\DeveloperAlertLogsDataTable::dataTable">dataTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeveloperAlertLogsDataTable.php.html#81"><abbr title="App\DataTables\DeveloperAlertLogsDataTable::query">query</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeveloperAlertLogsDataTable.php.html#89"><abbr title="App\DataTables\DeveloperAlertLogsDataTable::html">html</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeveloperAlertLogsDataTable.php.html#160"><abbr title="App\DataTables\DeveloperAlertLogsDataTable::getColumns">getColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DeveloperAlertLogsDataTable.php.html#189"><abbr title="App\DataTables\DeveloperAlertLogsDataTable::filename">filename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEventsDataTable.php.html#21"><abbr title="App\DataTables\PublicApiWorkOrderWebhookEventsDataTable::dataTable">dataTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEventsDataTable.php.html#60"><abbr title="App\DataTables\PublicApiWorkOrderWebhookEventsDataTable::query">query</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEventsDataTable.php.html#68"><abbr title="App\DataTables\PublicApiWorkOrderWebhookEventsDataTable::html">html</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEventsDataTable.php.html#90"><abbr title="App\DataTables\PublicApiWorkOrderWebhookEventsDataTable::getColumns">getColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEventsDataTable.php.html#118"><abbr title="App\DataTables\PublicApiWorkOrderWebhookEventsDataTable::filename">filename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#22"><abbr title="App\DataTables\RequestLogsDataTable::dataTable">dataTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#144"><abbr title="App\DataTables\RequestLogsDataTable::query">query</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#152"><abbr title="App\DataTables\RequestLogsDataTable::html">html</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#223"><abbr title="App\DataTables\RequestLogsDataTable::getColumns">getColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#259"><abbr title="App\DataTables\RequestLogsDataTable::getStatusMessage">getStatusMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#273"><abbr title="App\DataTables\RequestLogsDataTable::getSource">getSource</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#286"><abbr title="App\DataTables\RequestLogsDataTable::mobileIcon">mobileIcon</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#295"><abbr title="App\DataTables\RequestLogsDataTable::getStatusCodeClass">getStatusCodeClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#316"><abbr title="App\DataTables\RequestLogsDataTable::getMethodClass">getMethodClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#327"><abbr title="App\DataTables\RequestLogsDataTable::getUserDetails">getUserDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#347"><abbr title="App\DataTables\RequestLogsDataTable::filename">filename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPublicApiLogDataTable.php.html#21"><abbr title="App\DataTables\VendorPublicApiLogDataTable::dataTable">dataTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPublicApiLogDataTable.php.html#67"><abbr title="App\DataTables\VendorPublicApiLogDataTable::query">query</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPublicApiLogDataTable.php.html#75"><abbr title="App\DataTables\VendorPublicApiLogDataTable::html">html</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPublicApiLogDataTable.php.html#95"><abbr title="App\DataTables\VendorPublicApiLogDataTable::getColumns">getColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPublicApiLogDataTable.php.html#118"><abbr title="App\DataTables\VendorPublicApiLogDataTable::filename">filename</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPublicApiLogDataTable.php.html#123"><abbr title="App\DataTables\VendorPublicApiLogDataTable::getMethodClass">getMethodClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="VendorPublicApiLogDataTable.php.html#134"><abbr title="App\DataTables\VendorPublicApiLogDataTable::getStatusCodeClass">getStatusCodeClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCallsDataTable.php.html#19"><abbr title="App\DataTables\WebhookCallsDataTable::dataTable">dataTable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCallsDataTable.php.html#48"><abbr title="App\DataTables\WebhookCallsDataTable::query">query</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCallsDataTable.php.html#56"><abbr title="App\DataTables\WebhookCallsDataTable::html">html</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCallsDataTable.php.html#76"><abbr title="App\DataTables\WebhookCallsDataTable::getColumns">getColumns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WebhookCallsDataTable.php.html#98"><abbr title="App\DataTables\WebhookCallsDataTable::filename">filename</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="RequestLogsDataTable.php.html#295"><abbr title="App\DataTables\RequestLogsDataTable::getStatusCodeClass">getStatusCodeClass</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="VendorPublicApiLogDataTable.php.html#134"><abbr title="App\DataTables\VendorPublicApiLogDataTable::getStatusCodeClass">getStatusCodeClass</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#22"><abbr title="App\DataTables\RequestLogsDataTable::dataTable">dataTable</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DeveloperAlertLogsDataTable.php.html#21"><abbr title="App\DataTables\DeveloperAlertLogsDataTable::dataTable">dataTable</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#259"><abbr title="App\DataTables\RequestLogsDataTable::getStatusMessage">getStatusMessage</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#327"><abbr title="App\DataTables\RequestLogsDataTable::getUserDetails">getUserDetails</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="PublicApiWorkOrderWebhookEventsDataTable.php.html#21"><abbr title="App\DataTables\PublicApiWorkOrderWebhookEventsDataTable::dataTable">dataTable</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="VendorPublicApiLogDataTable.php.html#21"><abbr title="App\DataTables\VendorPublicApiLogDataTable::dataTable">dataTable</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RequestLogsDataTable.php.html#273"><abbr title="App\DataTables\RequestLogsDataTable::getSource">getSource</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="WebhookCallsDataTable.php.html#19"><abbr title="App\DataTables\WebhookCallsDataTable::dataTable">dataTable</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Fri Jul 4 0:40:55 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([5,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([33,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,8,"<a href=\"DeveloperAlertLogsDataTable.php.html#14\">App\\DataTables\\DeveloperAlertLogsDataTable<\/a>"],[0,7,"<a href=\"PublicApiWorkOrderWebhookEventsDataTable.php.html#14\">App\\DataTables\\PublicApiWorkOrderWebhookEventsDataTable<\/a>"],[0,29,"<a href=\"RequestLogsDataTable.php.html#15\">App\\DataTables\\RequestLogsDataTable<\/a>"],[0,15,"<a href=\"VendorPublicApiLogDataTable.php.html#14\">App\\DataTables\\VendorPublicApiLogDataTable<\/a>"],[0,6,"<a href=\"WebhookCallsDataTable.php.html#12\">App\\DataTables\\WebhookCallsDataTable<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"DeveloperAlertLogsDataTable.php.html#21\">App\\DataTables\\DeveloperAlertLogsDataTable::dataTable<\/a>"],[0,1,"<a href=\"DeveloperAlertLogsDataTable.php.html#81\">App\\DataTables\\DeveloperAlertLogsDataTable::query<\/a>"],[0,1,"<a href=\"DeveloperAlertLogsDataTable.php.html#89\">App\\DataTables\\DeveloperAlertLogsDataTable::html<\/a>"],[0,1,"<a href=\"DeveloperAlertLogsDataTable.php.html#160\">App\\DataTables\\DeveloperAlertLogsDataTable::getColumns<\/a>"],[0,1,"<a href=\"DeveloperAlertLogsDataTable.php.html#189\">App\\DataTables\\DeveloperAlertLogsDataTable::filename<\/a>"],[0,3,"<a href=\"PublicApiWorkOrderWebhookEventsDataTable.php.html#21\">App\\DataTables\\PublicApiWorkOrderWebhookEventsDataTable::dataTable<\/a>"],[0,1,"<a href=\"PublicApiWorkOrderWebhookEventsDataTable.php.html#60\">App\\DataTables\\PublicApiWorkOrderWebhookEventsDataTable::query<\/a>"],[0,1,"<a href=\"PublicApiWorkOrderWebhookEventsDataTable.php.html#68\">App\\DataTables\\PublicApiWorkOrderWebhookEventsDataTable::html<\/a>"],[0,1,"<a href=\"PublicApiWorkOrderWebhookEventsDataTable.php.html#90\">App\\DataTables\\PublicApiWorkOrderWebhookEventsDataTable::getColumns<\/a>"],[0,1,"<a href=\"PublicApiWorkOrderWebhookEventsDataTable.php.html#118\">App\\DataTables\\PublicApiWorkOrderWebhookEventsDataTable::filename<\/a>"],[0,6,"<a href=\"RequestLogsDataTable.php.html#22\">App\\DataTables\\RequestLogsDataTable::dataTable<\/a>"],[0,1,"<a href=\"RequestLogsDataTable.php.html#144\">App\\DataTables\\RequestLogsDataTable::query<\/a>"],[0,1,"<a href=\"RequestLogsDataTable.php.html#152\">App\\DataTables\\RequestLogsDataTable::html<\/a>"],[0,1,"<a href=\"RequestLogsDataTable.php.html#223\">App\\DataTables\\RequestLogsDataTable::getColumns<\/a>"],[0,4,"<a href=\"RequestLogsDataTable.php.html#259\">App\\DataTables\\RequestLogsDataTable::getStatusMessage<\/a>"],[0,2,"<a href=\"RequestLogsDataTable.php.html#273\">App\\DataTables\\RequestLogsDataTable::getSource<\/a>"],[0,1,"<a href=\"RequestLogsDataTable.php.html#286\">App\\DataTables\\RequestLogsDataTable::mobileIcon<\/a>"],[0,7,"<a href=\"RequestLogsDataTable.php.html#295\">App\\DataTables\\RequestLogsDataTable::getStatusCodeClass<\/a>"],[0,1,"<a href=\"RequestLogsDataTable.php.html#316\">App\\DataTables\\RequestLogsDataTable::getMethodClass<\/a>"],[0,4,"<a href=\"RequestLogsDataTable.php.html#327\">App\\DataTables\\RequestLogsDataTable::getUserDetails<\/a>"],[0,1,"<a href=\"RequestLogsDataTable.php.html#347\">App\\DataTables\\RequestLogsDataTable::filename<\/a>"],[0,3,"<a href=\"VendorPublicApiLogDataTable.php.html#21\">App\\DataTables\\VendorPublicApiLogDataTable::dataTable<\/a>"],[0,1,"<a href=\"VendorPublicApiLogDataTable.php.html#67\">App\\DataTables\\VendorPublicApiLogDataTable::query<\/a>"],[0,1,"<a href=\"VendorPublicApiLogDataTable.php.html#75\">App\\DataTables\\VendorPublicApiLogDataTable::html<\/a>"],[0,1,"<a href=\"VendorPublicApiLogDataTable.php.html#95\">App\\DataTables\\VendorPublicApiLogDataTable::getColumns<\/a>"],[0,1,"<a href=\"VendorPublicApiLogDataTable.php.html#118\">App\\DataTables\\VendorPublicApiLogDataTable::filename<\/a>"],[0,1,"<a href=\"VendorPublicApiLogDataTable.php.html#123\">App\\DataTables\\VendorPublicApiLogDataTable::getMethodClass<\/a>"],[0,7,"<a href=\"VendorPublicApiLogDataTable.php.html#134\">App\\DataTables\\VendorPublicApiLogDataTable::getStatusCodeClass<\/a>"],[0,2,"<a href=\"WebhookCallsDataTable.php.html#19\">App\\DataTables\\WebhookCallsDataTable::dataTable<\/a>"],[0,1,"<a href=\"WebhookCallsDataTable.php.html#48\">App\\DataTables\\WebhookCallsDataTable::query<\/a>"],[0,1,"<a href=\"WebhookCallsDataTable.php.html#56\">App\\DataTables\\WebhookCallsDataTable::html<\/a>"],[0,1,"<a href=\"WebhookCallsDataTable.php.html#76\">App\\DataTables\\WebhookCallsDataTable::getColumns<\/a>"],[0,1,"<a href=\"WebhookCallsDataTable.php.html#98\">App\\DataTables\\WebhookCallsDataTable::filename<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

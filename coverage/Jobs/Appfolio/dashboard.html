<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Jobs/Appfolio</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Jobs</a></li>
         <li class="breadcrumb-item"><a href="index.html">Appfolio</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="IngestAppfolioWorkOrdersJob.php.html#14">App\Jobs\Appfolio\IngestAppfolioWorkOrdersJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IngestAppfolioWorkOrdersToServiceRequestJob.php.html#14">App\Jobs\Appfolio\IngestAppfolioWorkOrdersToServiceRequestJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProcessAppfolioNewWorkOrder.php.html#15">App\Jobs\Appfolio\ProcessAppfolioNewWorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProcessAppfolioNewWorkOrderToServiceRequest.php.html#15">App\Jobs\Appfolio\ProcessAppfolioNewWorkOrderToServiceRequest</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStateUpdateJob.php.html#15">App\Jobs\Appfolio\ServiceRequestStateUpdateJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncVendorsJob.php.html#24">App\Jobs\Appfolio\SyncVendorsJob</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateStatusInAppfolioWorkOrder.php.html#17">App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStateUpdateJob.php.html#15">App\Jobs\Appfolio\WorkOrderStateUpdateJob</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SyncVendorsJob.php.html#24">App\Jobs\Appfolio\SyncVendorsJob</a></td><td class="text-right">812</td></tr>
       <tr><td><a href="UpdateStatusInAppfolioWorkOrder.php.html#17">App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ServiceRequestStateUpdateJob.php.html#15">App\Jobs\Appfolio\ServiceRequestStateUpdateJob</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="WorkOrderStateUpdateJob.php.html#15">App\Jobs\Appfolio\WorkOrderStateUpdateJob</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="IngestAppfolioWorkOrdersJob.php.html#28"><abbr title="App\Jobs\Appfolio\IngestAppfolioWorkOrdersJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IngestAppfolioWorkOrdersJob.php.html#37"><abbr title="App\Jobs\Appfolio\IngestAppfolioWorkOrdersJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IngestAppfolioWorkOrdersToServiceRequestJob.php.html#28"><abbr title="App\Jobs\Appfolio\IngestAppfolioWorkOrdersToServiceRequestJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IngestAppfolioWorkOrdersToServiceRequestJob.php.html#37"><abbr title="App\Jobs\Appfolio\IngestAppfolioWorkOrdersToServiceRequestJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProcessAppfolioNewWorkOrder.php.html#40"><abbr title="App\Jobs\Appfolio\ProcessAppfolioNewWorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProcessAppfolioNewWorkOrder.php.html#51"><abbr title="App\Jobs\Appfolio\ProcessAppfolioNewWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProcessAppfolioNewWorkOrderToServiceRequest.php.html#40"><abbr title="App\Jobs\Appfolio\ProcessAppfolioNewWorkOrderToServiceRequest::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProcessAppfolioNewWorkOrderToServiceRequest.php.html#51"><abbr title="App\Jobs\Appfolio\ProcessAppfolioNewWorkOrderToServiceRequest::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStateUpdateJob.php.html#34"><abbr title="App\Jobs\Appfolio\ServiceRequestStateUpdateJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceRequestStateUpdateJob.php.html#44"><abbr title="App\Jobs\Appfolio\ServiceRequestStateUpdateJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncVendorsJob.php.html#33"><abbr title="App\Jobs\Appfolio\SyncVendorsJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncVendorsJob.php.html#43"><abbr title="App\Jobs\Appfolio\SyncVendorsJob::middleware">middleware</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SyncVendorsJob.php.html#57"><abbr title="App\Jobs\Appfolio\SyncVendorsJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateStatusInAppfolioWorkOrder.php.html#42"><abbr title="App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateStatusInAppfolioWorkOrder.php.html#53"><abbr title="App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UpdateStatusInAppfolioWorkOrder.php.html#111"><abbr title="App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder::toWorkOrderTimezone">toWorkOrderTimezone</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStateUpdateJob.php.html#34"><abbr title="App\Jobs\Appfolio\WorkOrderStateUpdateJob::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkOrderStateUpdateJob.php.html#44"><abbr title="App\Jobs\Appfolio\WorkOrderStateUpdateJob::handle">handle</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SyncVendorsJob.php.html#57"><abbr title="App\Jobs\Appfolio\SyncVendorsJob::handle">handle</abbr></a></td><td class="text-right">650</td></tr>
       <tr><td><a href="UpdateStatusInAppfolioWorkOrder.php.html#53"><abbr title="App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder::handle">handle</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ServiceRequestStateUpdateJob.php.html#44"><abbr title="App\Jobs\Appfolio\ServiceRequestStateUpdateJob::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="WorkOrderStateUpdateJob.php.html#44"><abbr title="App\Jobs\Appfolio\WorkOrderStateUpdateJob::handle">handle</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SyncVendorsJob.php.html#43"><abbr title="App\Jobs\Appfolio\SyncVendorsJob::middleware">middleware</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UpdateStatusInAppfolioWorkOrder.php.html#111"><abbr title="App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder::toWorkOrderTimezone">toWorkOrderTimezone</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Mon Jun 30 0:32:48 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([8,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([18,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"IngestAppfolioWorkOrdersJob.php.html#14\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersJob<\/a>"],[0,2,"<a href=\"IngestAppfolioWorkOrdersToServiceRequestJob.php.html#14\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersToServiceRequestJob<\/a>"],[0,2,"<a href=\"ProcessAppfolioNewWorkOrder.php.html#15\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrder<\/a>"],[0,2,"<a href=\"ProcessAppfolioNewWorkOrderToServiceRequest.php.html#15\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrderToServiceRequest<\/a>"],[0,4,"<a href=\"ServiceRequestStateUpdateJob.php.html#15\">App\\Jobs\\Appfolio\\ServiceRequestStateUpdateJob<\/a>"],[0,28,"<a href=\"SyncVendorsJob.php.html#24\">App\\Jobs\\Appfolio\\SyncVendorsJob<\/a>"],[0,11,"<a href=\"UpdateStatusInAppfolioWorkOrder.php.html#17\">App\\Jobs\\Appfolio\\UpdateStatusInAppfolioWorkOrder<\/a>"],[0,4,"<a href=\"WorkOrderStateUpdateJob.php.html#15\">App\\Jobs\\Appfolio\\WorkOrderStateUpdateJob<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"IngestAppfolioWorkOrdersJob.php.html#28\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersJob::__construct<\/a>"],[0,1,"<a href=\"IngestAppfolioWorkOrdersJob.php.html#37\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersJob::handle<\/a>"],[0,1,"<a href=\"IngestAppfolioWorkOrdersToServiceRequestJob.php.html#28\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersToServiceRequestJob::__construct<\/a>"],[0,1,"<a href=\"IngestAppfolioWorkOrdersToServiceRequestJob.php.html#37\">App\\Jobs\\Appfolio\\IngestAppfolioWorkOrdersToServiceRequestJob::handle<\/a>"],[0,1,"<a href=\"ProcessAppfolioNewWorkOrder.php.html#40\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrder::__construct<\/a>"],[0,1,"<a href=\"ProcessAppfolioNewWorkOrder.php.html#51\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrder::handle<\/a>"],[0,1,"<a href=\"ProcessAppfolioNewWorkOrderToServiceRequest.php.html#40\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrderToServiceRequest::__construct<\/a>"],[0,1,"<a href=\"ProcessAppfolioNewWorkOrderToServiceRequest.php.html#51\">App\\Jobs\\Appfolio\\ProcessAppfolioNewWorkOrderToServiceRequest::handle<\/a>"],[0,1,"<a href=\"ServiceRequestStateUpdateJob.php.html#34\">App\\Jobs\\Appfolio\\ServiceRequestStateUpdateJob::__construct<\/a>"],[0,3,"<a href=\"ServiceRequestStateUpdateJob.php.html#44\">App\\Jobs\\Appfolio\\ServiceRequestStateUpdateJob::handle<\/a>"],[0,1,"<a href=\"SyncVendorsJob.php.html#33\">App\\Jobs\\Appfolio\\SyncVendorsJob::__construct<\/a>"],[0,2,"<a href=\"SyncVendorsJob.php.html#43\">App\\Jobs\\Appfolio\\SyncVendorsJob::middleware<\/a>"],[0,25,"<a href=\"SyncVendorsJob.php.html#57\">App\\Jobs\\Appfolio\\SyncVendorsJob::handle<\/a>"],[0,1,"<a href=\"UpdateStatusInAppfolioWorkOrder.php.html#42\">App\\Jobs\\Appfolio\\UpdateStatusInAppfolioWorkOrder::__construct<\/a>"],[0,8,"<a href=\"UpdateStatusInAppfolioWorkOrder.php.html#53\">App\\Jobs\\Appfolio\\UpdateStatusInAppfolioWorkOrder::handle<\/a>"],[0,2,"<a href=\"UpdateStatusInAppfolioWorkOrder.php.html#111\">App\\Jobs\\Appfolio\\UpdateStatusInAppfolioWorkOrder::toWorkOrderTimezone<\/a>"],[0,1,"<a href=\"WorkOrderStateUpdateJob.php.html#34\">App\\Jobs\\Appfolio\\WorkOrderStateUpdateJob::__construct<\/a>"],[0,3,"<a href=\"WorkOrderStateUpdateJob.php.html#44\">App\\Jobs\\Appfolio\\WorkOrderStateUpdateJob::handle<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Actions/Vendors/SyncAllVendors.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">Vendors</a></li>
         <li class="breadcrumb-item active">SyncAllVendors.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="4.17" aria-valuemin="0" aria-valuemax="100" style="width: 4.17%">
           <span class="sr-only">4.17% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">4.17%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;24</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="25.00" aria-valuemin="0" aria-valuemax="100" style="width: 25.00%">
           <span class="sr-only">25.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">25.00%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;4</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><abbr title="App\Actions\Vendors\SyncAllVendors">SyncAllVendors</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="4.17" aria-valuemin="0" aria-valuemax="100" style="width: 4.17%">
           <span class="sr-only">4.17% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">4.17%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;24</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="25.00" aria-valuemin="0" aria-valuemax="100" style="width: 25.00%">
           <span class="sr-only">25.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">25.00%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;4</div></td>
       <td class="danger small">117.50</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#24"><abbr title="getSlug(): string">getSlug</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#29"><abbr title="handle(App\Models\Organization $organization): void">handle</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#37"><abbr title="asController(Illuminate\Http\Request $request): Illuminate\Http\JsonResponse">asController</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;21</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">56</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#77"><abbr title="authorize(Lorisleiva\Actions\ActionRequest $request): bool">authorize</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Actions\Vendors</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Enums\Boolean</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Exceptions\NotFoundException\OrganizationNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Exceptions\NotFoundException\UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Exceptions\VendorException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Helpers\Helper</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Jobs\Appfolio\SyncVendorsJob</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\Organization</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\Vendor</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Exception</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Http\JsonResponse</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Http\Request</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\Response</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Lorisleiva\Actions\ActionRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Lorisleiva\Actions\Concerns\AsAction</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">SyncAllVendors</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">BaseAction</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">AsAction</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">static</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getSlug</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="193 tests cover line 26" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\VendorOnboardingStatusesTest::__pest_evaluable_returns_correct_label_for_each_status&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\VendorOnboardingStatusesTest::__pest_evaluable_returns_default_label_for_invalid_status&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_return_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_return_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_Handles_missing_issue_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_retrieves_the_correct_issue_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Exception\SignedUrlExceptionTest::__pest_evaluable_signedUrlNotProvided_returns_correct_exception&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Exception\SignedUrlExceptionTest::__pest_evaluable_signedUrlExpired_returns_correct_exception&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Helpers\HelperTest::__pest_evaluable_Helper__resolvePhoneNumberForTwilio_returns_formatted_E164_phone_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Helpers\HelperTest::__pest_evaluable_developerAlert_logs_info_and_queues_mail&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Helpers\HelperTest::__pest_evaluable_Helper__exceptionLog_logs_an_exception_without_notify&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Helpers\HelperTest::__pest_evaluable_displayPhoneNumber_formats_number_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Helpers\HelperTest::__pest_evaluable_generateSecureLink_returns_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Helpers\HelperTest::__pest_evaluable_getLocationApiKey_returns_secret_string_from_AWS_SecretsManager&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Helpers\HelperTest::__pest_evaluable_buildStringUrl_reconstructs_verify_route_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Mail\PasswordChangedMailTest::__pest_evaluable_password_changed_mail_has_the_correct_view&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Mail\PasswordChangedMailTest::__pest_evaluable_password_changed_mail_is_queueable&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Mail\PasswordChangedMailTest::__pest_evaluable_password_changed_mail_renders_without_errors&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Mail\PasswordChangedMailTest::__pest_evaluable_password_changed_mail_uses_the_default_queue_connection&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Mail\PasswordChangedMailTest::__pest_evaluable_password_changed_mail_has_the_correct_priority&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Mail\PasswordChangedMailTest::__pest_evaluable_password_changed_mail_handles_custom_from_address&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Mail\PasswordChangedMailTest::__pest_evaluable_password_changed_mail_serializes_and_deserializes_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Mail\PasswordChangedMailTest::__pest_evaluable_password_changed_mail_can_be_queued&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Mail\PasswordChangedMailTest::__pest_evaluable_password_changed_mail_has_the_correct_traits&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Mail\PasswordResetMailTest::__pest_evaluable_password_reset_mail_has_correct_view_and_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Mail\PasswordResetMailTest::__pest_evaluable_password_reset_mail_renders_with_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable__Vendor_model_has_a_proper_relationship_with__→_VendorServices&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable__Vendor_model_has_a_proper_relationship_with__→_VendorServiceArea&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable__Vendor_model_has_a_proper_relationship_with__→_VendorUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable__Vendor_model_has_a_proper_relationship_with__→_VendorOnboardings&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable__Vendor_model_has_a_proper_relationship_with__→_Organizations_through_OrganizationVendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable_getAddress_method_returns_properly_formatted_address&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable_getName_method_returns_properly_formatted_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable_casts_dates_to_CarbonImmutable&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable_soft_deletes_are_enabled_on_the_model&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_match_database_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable_vendor__uuid_is_cast_to_UuidBinary&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorModelTest::__pest_evaluable_vendor_can_be_found_by_UUID&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingModelTest::__pest_evaluable__VendorOnboarding_model_has_a_proper_relationship_with__→_Vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingModelTest::__pest_evaluable__VendorOnboarding_model_has_a_proper_relationship_with__→_VendorOnboardingStatus&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingModelTest::__pest_evaluable__VendorOnboarding_model_has_a_proper_relationship_with__→_User&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingModelTest::__pest_evaluable__VendorOnboarding_model_has_a_proper_relationship_with__→_Organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingModelTest::__pest_evaluable_soft_deletes_are_enabled_on_the_model&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_match_database_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingModelTest::__pest_evaluable_vendor__onboarding__uuid_is_cast_to_UuidBinary&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingModelTest::__pest_evaluable_vendor_onboarding_can_be_found_by_UUID&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingModelTest::__pest_evaluable_uuidColumn_method_returns_the_correct_column_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingStatusModelTest::__pest_evaluable__VendorOnboardingStatus_model_has_a_proper_relationship_with__→_VendorOnboardings&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingStatusModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_match_database_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingStatusModelTest::__pest_evaluable_timestamps_are_disabled_on_the_model&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingStatusModelTest::__pest_evaluable_can_create_a_new_status_with_valid_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingStatusModelTest::__pest_evaluable_can_retrieve_a_status_by_its_slug&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingStatusModelTest::__pest_evaluable_can_update_an_existing_status&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorOnboardingStatusModelTest::__pest_evaluable_can_order_statuses_by_sort__order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceAreaModelTest::__pest_evaluable__VendorServiceArea_model_has_a_proper_relationship_with__→_Vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceAreaModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_match_database_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceAreaModelTest::__pest_evaluable_vendor__service__area__uuid_is_cast_to_UuidBinary&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceAreaModelTest::__pest_evaluable_vendor_service_area_can_be_found_by_UUID&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceAreaModelTest::__pest_evaluable_soft_deletes_are_enabled_on_the_model&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceAreaModelTest::__pest_evaluable_zip__codes_attribute_is_cast_to_json&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceAreaModelTest::__pest_evaluable_can_create_a_vendor_service_area_with_valid_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceAreaModelTest::__pest_evaluable_can_update_a_vendor_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceAreaModelTest::__pest_evaluable_can_find_vendor_service_area_by_vendor__id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceAreaModelTest::__pest_evaluable_can_handle_empty_zip__codes_array&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceAreaModelTest::__pest_evaluable_can_handle_null_zip__codes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceModelTest::__pest_evaluable__VendorService_model_has_a_proper_relationship_with__→_Vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceModelTest::__pest_evaluable__VendorService_model_has_a_proper_relationship_with__→_ProblemCategory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceModelTest::__pest_evaluable__VendorService_model_has_a_proper_relationship_with__→_ProblemSubCategory&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceModelTest::__pest_evaluable__VendorService_model_has_a_proper_relationship_with__→_ProblemDiagnosis&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceModelTest::__pest_evaluable__VendorService_model_has_a_proper_relationship_with__→_Organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_match_database_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceModelTest::__pest_evaluable_vendor__service__uuid_is_cast_to_UuidBinary&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceModelTest::__pest_evaluable_vendor_service_can_be_found_by_UUID&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceModelTest::__pest_evaluable_soft_deletes_are_enabled_on_the_model&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceModelTest::__pest_evaluable_can_create_a_vendor_service_with_all_relationships&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorServiceModelTest::__pest_evaluable_can_find_vendor_services_by_vendor__id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable__VendorUser_model_has_a_proper_relationship_with__→_Vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable__VendorUser_model_has_a_proper_relationship_with__→_User&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_match_database_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable_vendor__user__uuid_is_cast_to_UuidBinary&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable_vendor_user_can_be_found_by_UUID&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable_soft_deletes_are_enabled_on_the_model&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable_can_create_a_vendor_user_with_valid_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable_can_update_a_vendor_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable_can_find_vendor_users_by_vendor__id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable_can_find_vendor_users_by_user__id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable_can_handle_null_user__id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable_deleting_a_vendor_does_not_delete_the_vendor_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\VendorUserModelTest::__pest_evaluable_deleting_a_user_does_not_delete_the_vendor_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_Check_if_password_does_not_pass_on_based_on_requirements&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_Password_should_be_invalid_when_character_is_less_than_8&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_Check_if_blank_client__key_is_required&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_Check_if_signedUrl_is_valid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_it_fails_when_signedUrl_is_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_it_fails_when_signedUrl_has_invalid_userid_with_empty_ID&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_it_fails_when_organization_is_not_found_for_given_client__key&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordResetTest::__pest_evaluable_it_sends_a_password_reset_email_for_a_valid_user_email_in_vendors_portal_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordResetTest::__pest_evaluable_it_sends_a_password_reset_email_for_a_valid_user_email_without_client__key_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordResetTest::__pest_evaluable_it_fails_with_422_for_non_existent_email&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordResetTest::__pest_evaluable_it_fails_with_422_when_email_is_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordResetTest::__pest_evaluable_it_throws_ModelNotFoundException_for_valid_but_non_existent_email&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\VerifySignedUrlTest::__pest_evaluable_valid_signed_URL_passed_as_request_input_is_processed_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\VerifySignedUrlTest::__pest_evaluable_invalid_signature_results_in_unprocessable_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\VerifySignedUrlTest::__pest_evaluable_valid_signed_URL_but_expire_link&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Exception_during_service_request_priority_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_when_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Exception_during_property_address_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_true_for_a_user_with_the_correct_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_when_user_does_not_have_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Unauthorized_user_cannot_list_work_orders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_sheduled_and_work_in_progress_work_orders_when_filter_status_is_all__open_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_paused_work_orders_when_filter_status_is_all__paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Return_completed_and_cancelled_work_orders_when_filter_status_is_all__closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_scheduled_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_paused_date&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_Vendor_can_filter_work_orders_by_closed_date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_the_correct_resource_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderShowTest::__pest_evaluable_Return_not_found_on_work_order_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Generating_signed_url_with_invalid_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Generating_signed_url_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Set_service_areas_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Get_onboarding_details_with_invalid_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Get_onboarding_details_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Successfully_return_vendor_onboarding_inte_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Return_403_error_for_if_signed_url_is_expired&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Get_lookup_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Set_password_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Set_status_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Successfully_return_vendor_onboarding_status_with_valid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_forbidden_if_user_is_not_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_unauthorize_if_no_token_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingStatusTest::__pest_evaluable_Return_internal_server_error_if_onboarding_does_not_exist&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'syncAll'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handle</span><span class="keyword">(</span><span class="default">Organization</span><span class="default">&nbsp;</span><span class="default">$organization</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">dispatch</span><span class="keyword">(</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">SyncVendorsJob</span><span class="keyword">(</span><span class="default">$organization</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">1</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@throws&nbsp;Exception</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">asController</span><span class="keyword">(</span><span class="default">Request</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">JsonResponse</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$organization</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">organization</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$organization</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">OrganizationNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$organization</span><span class="default">-&gt;</span><span class="default">is_appfolio_enabled</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">Boolean</span><span class="default">::</span><span class="default">YES</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">VendorException</span><span class="default">::</span><span class="default">appfolioNotEnabled</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handle</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$organization</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">message</span><span class="keyword">(</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'sync&nbsp;in&nbsp;progress'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">UserNotFoundException</span><span class="keyword">|</span><span class="default">OrganizationNotFoundException</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Helper</span><span class="default">::</span><span class="default">exceptionLog</span><span class="keyword">(</span><span class="default">exception</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'Sync&nbsp;Vendors&nbsp;API&nbsp;Failed&nbsp;due&nbsp;to&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">get_class</span><span class="keyword">(</span><span class="default">$exception</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">notFound</span><span class="keyword">(</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">VendorException</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Helper</span><span class="default">::</span><span class="default">exceptionLog</span><span class="keyword">(</span><span class="default">exception</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'Sync&nbsp;Vendors&nbsp;API&nbsp;Failed&nbsp;due&nbsp;to&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">get_class</span><span class="keyword">(</span><span class="default">$exception</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">unprocessableEntity</span><span class="keyword">(</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Exception</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Helper</span><span class="default">::</span><span class="default">exceptionLog</span><span class="keyword">(</span><span class="default">exception</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'Sync&nbsp;Vendors&nbsp;API&nbsp;Failed'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">notify</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">internalServerError</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">authorize</span><span class="keyword">(</span><span class="default">ActionRequest</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">can</span><span class="keyword">(</span><span class="default">'sync'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">Vendor</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Mon Jun 30 0:32:48 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

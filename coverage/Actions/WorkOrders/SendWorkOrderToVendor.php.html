<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Actions/WorkOrders/SendWorkOrderToVendor.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Actions</a></li>
         <li class="breadcrumb-item"><a href="index.html">WorkOrders</a></li>
         <li class="breadcrumb-item active">SendWorkOrderToVendor.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.74" aria-valuemin="0" aria-valuemax="100" style="width: 0.74%">
           <span class="sr-only">0.74% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.74%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;135</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="25.00" aria-valuemin="0" aria-valuemax="100" style="width: 25.00%">
           <span class="sr-only">25.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">25.00%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;4</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><abbr title="App\Actions\WorkOrders\SendWorkOrderToVendor">SendWorkOrderToVendor</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.74" aria-valuemin="0" aria-valuemax="100" style="width: 0.74%">
           <span class="sr-only">0.74% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.74%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;135</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="25.00" aria-valuemin="0" aria-valuemax="100" style="width: 25.00%">
           <span class="sr-only">25.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">25.00%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;4</div></td>
       <td class="danger small">452.27</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#36"><abbr title="getSlug(): string">getSlug</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#44"><abbr title="handle(App\Models\WorkOrder $workOrder, App\Models\WorkOrderTask $workOrderTask, App\Models\User $user, array $payload): void">handle</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;54</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">110</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#124"><abbr title="asController(App\Http\Requests\WorkOrder\SendToVendorRequest $request, App\Models\WorkOrder $workOrder, App\Models\WorkOrderTask $workOrderTask): \Illuminate\Http\Resources\Json\JsonResource|\Illuminate\Http\JsonResponse">asController</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;79</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">72</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#225"><abbr title="authorize(Lorisleiva\Actions\ActionRequest $request): bool">authorize</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Actions\WorkOrders</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Events\WorkOrder\WorkOrderSendToVendor</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Exceptions\NotFoundException\UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Helpers\Helper</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Requests\WorkOrder\SendToVendorRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Http\Resources\WorkOrder\WorkOrderSendToVendorResource</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\Organization</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\User</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\Vendor</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\WorkOrder</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\WorkOrderTask</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Services\Vendor\Enum\Service</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Services\Vendor\Exceptions\ServiceException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Services\Vendor\Services\Lula\Exception\WorkOrderSendException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Services\Vendor\VendorService</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\ServiceCalls\Ended</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\States\WorkOrders\ClaimPending</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Exception</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Database\Eloquent\ModelNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Http\JsonResponse</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Http\Resources\Json\JsonResource</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\DB</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Support\Facades\Response</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">InvalidArgumentException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Lorisleiva\Actions\ActionRequest</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Lorisleiva\Actions\Concerns\AsAction</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Spatie\ModelStates\Exceptions\CouldNotPerformTransition</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">SendWorkOrderToVendor</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">BaseAction</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">AsAction</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">static</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getSlug</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2105 tests cover line 38" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Integration\FilterableSortableIntegrationTest::filterableAndSortableTraitsWorkTogether&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Integration\FilterableSortableIntegrationTest::filterableTraitWithComplexFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Integration\FilterableSortableIntegrationTest::sortableTraitWithMultipleSortFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Integration\FilterableSortableIntegrationTest::filterableTraitWithSearchAndEntityIdFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Integration\FilterableSortableIntegrationTest::sortableTraitWithGroupSorting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SearchServiceTest::registerSearchHandler&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SearchServiceTest::applySearch&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SearchServiceTest::emptySearchTermReturnsUnmodifiedQuery&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SearchServiceTest::defaultSearchHandlersAreRegistered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SearchServiceTest::applySearchWithNonExistentEntityTypeReturnsUnmodifiedQuery&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SearchServiceTest::getSearchHandlerReturnsNullForNonExistentEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SearchServiceTest::searchHandlersIncludeRelatedFieldsForSpecificEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SortServiceTest::registerSortFieldMappings&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SortServiceTest::getSortFieldMappings&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SortServiceTest::getDefaultSortField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SortServiceTest::applySorting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SortServiceTest::prepareSortValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SortServiceTest::prepareSortValueForWorkOrderWithGroup&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SortServiceTest::prepareSortValueForWorkOrderWithHealthScore&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SortServiceTest::prepareSortValueForWorkOrderWithHealthScoreGrouping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SortServiceTest::applySortingWithMultipleFields&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\SortServiceTest::applySortingWithNonExistentField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateFilterTypeTest::__pest_evaluable_DateFilterType_enum_has_correct_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateFilterTypeTest::__pest_evaluable_DateFilterType_getDescription_returns_correct_descriptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateFilterTypeTest::__pest_evaluable_DateFilterType_can_be_invoked_statically&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_enum_has_correct_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_getDisplayName_returns_correct_names&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_getDateRange_returns_correct_date_ranges&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_makeCollection_returns_all_slugs_except_specified_ones&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_futureDates_returns_only_future_dates&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateSlugTest::__pest_evaluable_DateSlug_can_be_invoked_statically&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestDeletedIssueRestoredTest::__pest_evaluable_Returns_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestDeletedIssueRestoredTest::__pest_evaluable_Implements_the_ShouldBroadcast_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestDeletedIssueRestoredTest::__pest_evaluable_Implements_the_ShouldDispatchAfterCommit_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestDeletedIssueRestoredTest::__pest_evaluable_Implements_the_ShouldQueue_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestDeletedIssueRestoredTest::__pest_evaluable_Extends_the_BaseIssueEvent_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_return_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_return_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_Handles_missing_issue_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_retrieves_the_correct_issue_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueRestoredTest::__pest_evaluable_Extends_the_BaseIssueEvent_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestActivityLogupdatedEventTest::__pest_evaluable_Event_has_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestActivityLogupdatedEventTest::__pest_evaluable_Event_returns_the_correct_broadcast_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestActivityLogupdatedEventTest::__pest_evaluable_Event_has_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestActivityLogupdatedEventTest::__pest_evaluable_Event_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestActivityLogupdatedEventTest::__pest_evaluable_Event_throws_an_exception_if_activity_log_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestDescriptionUpdatedEventTest::__pest_evaluable_broadcast_queue_return_correct_queue_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestDescriptionUpdatedEventTest::__pest_evaluable_broadcast_as_return_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestDescriptionUpdatedEventTest::__pest_evaluable_broadcast_on_return_correct_channel_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestDescriptionUpdatedEventTest::__pest_evaluable_broadcast_with_return_correct_response_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestDescriptionUpdatedEventTest::__pest_evaluable_get_service_request_description_method_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestDescriptionUpdatedEventTest::__pest_evaluable_broadcast_event_is_dispatched&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestStateChangeTest::__pest_evaluable_ServiceRequestStateChange_event_is_broadcasted_with_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestStateChangeTest::__pest_evaluable_ServiceRequestStateChange_event_broadcastAs_returns_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestStateChangeTest::__pest_evaluable_ServiceRequestStateChange_event_broadcastOn_returns_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestStateChangeTest::__pest_evaluable_ServiceRequestStateChange_event_broadcastWith_returns_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeletedEventTest::__pest_evaluable_Event_have_ServiceRequestWorkOrderBaseEvent_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeletedEventTest::__pest_evaluable_Event_have_ShouldBroadcast_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeletedEventTest::__pest_evaluable_Event_have_ShouldDispatchAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeletedEventTest::__pest_evaluable_Event_have_ShouldQueueAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeletedEventTest::__pest_evaluable_Event_have_proper_methods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeletedEventTest::__pest_evaluable_broadcasts_on_method_return_the_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeletedEventTest::__pest_evaluable_broadcastQueue_return_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeletedEventTest::__pest_evaluable_broadcastAs_returns_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeletedEventTest::__pest_evaluable_getWorkOrderDetails_returns_the_given_work_order_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeletedEventTest::__pest_evaluable_getWorkOrderDetails_throws_an_exception_when_work_order_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeletedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_Event_have_WorkOrderIssueBaseEvent_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_Event_have_ShouldBroadcast_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_Event_have_ShouldDispatchAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_Event_have_ShouldQueueAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_Event_have_proper_methods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcasts_on_method_return_the_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcastQueue_return_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcastAs_returns_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_getWorkOrderIssueDetails_returns_the_given_work_order_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_getWorkOrderIssueDetails_throws_an_exception_when_work_order_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueCanceledEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderListDeletedTest::__pest_evaluable_Event_have_ShouldBroadcast_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderListDeletedTest::__pest_evaluable_Event_have_ShouldDispatchAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderListDeletedTest::__pest_evaluable_Event_have_proper_methods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderListDeletedTest::__pest_evaluable_broadcasts_on_method_return_the_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderListDeletedTest::__pest_evaluable_broadcastQueue_return_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderListDeletedTest::__pest_evaluable_broadcastAs_returns_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderListDeletedTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestActivityLogListenerTest::__pest_evaluable_Creates_an_activity_log_and_broadcasts_an_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestActivityLogListenerTest::__pest_evaluable__Trow_exception_for_invalid_payload__→_empty_organization__id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestActivityLogListenerTest::__pest_evaluable__Trow_exception_for_invalid_payload__→_empty_type&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestActivityLogListenerTest::__pest_evaluable__Trow_exception_for_invalid_payload__→_empty_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestActivityLogListenerTest::__pest_evaluable__Trow_exception_for_invalid_payload__→_empty_event__attributes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestActivityLogListenerTest::__pest_evaluable__Trow_exception_for_invalid_payload__→_empty_service__request__id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestIssueRestoredListenerTest::__pest_evaluable_Throws_ModelNotFoundException_when_issue_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestIssueRestoredListenerTest::__pest_evaluable_Handles_event_with_action_restore__canceled_and_dispatches_CreateServiceRequestActivityLog&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestWorkOrderDeletedListenerTest::__pest_evaluable_Dispatches_CreateServiceRequestActivityLog_event_when_handling_ServiceRequestWorkOrderCreated&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestWorkOrderDeletedListenerTest::__pest_evaluable_Throws_ModelNotFoundException_when_work_order_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\ServiceRequestModelTest::__pest_evaluable__ServiceRequest_model_has_a_proper_relationship_with__→_Issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\ServiceRequestModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_include_the_appropriate_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\WorkOrderIssueStatusModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_include_the_appropriate_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\WorkOrderStatusModelTest::__pest_evaluable__WorkOrderStatus_model_has_a_proper_relationship_with__→_workOrders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\WorkOrderStatusModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_include_the_appropriate_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\WorkOrderIssue\AssignIssueRequestTest::__pest_evaluable_The_Validates_the_rules_are_added&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\WorkOrderIssue\AssignIssueRequestTest::__pest_evaluable_Passes_validation_with_valid_data#(['d63c26de-33cb-3972-bb1a-7092d69a6535'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\Issue\IssueResourceTest::__pest_evaluable_Issue_resource_includes_work__orders_when_state_is_Assigned_and_workOrderIssues_is_not_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueStatusResourceTest::__pest_evaluable_returns_the_correct_structure_in_toArray&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\WorkOrderServiceRequestResourceTest::__pest_evaluable_Work_order_response_resource_includes_issue_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\CanceledStateTest::__pest_evaluable_has_the_correct_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\CanceledStateTest::__pest_evaluable_has_the_correct_action_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\CanceledStateTest::__pest_evaluable_returns_the_correct_label&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\CanceledStateTest::__pest_evaluable_returns_the_correct_actions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\WorkOrderIssue\CanceledStateTest::__pest_evaluable_has_the_correct_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\WorkOrderIssue\CanceledStateTest::__pest_evaluable_has_the_correct_action_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\WorkOrderIssue\CanceledStateTest::__pest_evaluable_returns_the_correct_label&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\WorkOrderIssue\CanceledStateTest::__pest_evaluable_returns_the_correct_actions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiResponseTest::__pest_evaluable_successResponse_returns_correct_JSON_structure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiResponseTest::__pest_evaluable_errorResponse_returns_correct_JSON_structure_with_errors&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiResponseTest::__pest_evaluable_errorResponse_returns_default_error_response_when_errors_are_null&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'canceled', ['unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'claim_pending', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'created', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'paused', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'quality_check', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'resolved', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduled', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scoping', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'assign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'awaiting_availability', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'canceled', ['unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'claim_pending', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'created', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'paused', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'quality_check', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_schedule', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'ready_to_invoice', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'resolved', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduled', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scoping', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'scheduling_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Work_order_issue_ability_is_correct_for_status#('canceled', 'work_in_progress', ['edit', 'unassign'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\WorkOrderIssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_work_order_issue_which_status#('declined')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CreateWOAndCompleteTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_fully_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithSubmitQuoteOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Submit_a_quote____&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ConfigTest::__pest_evaluable_can_get_config_with_valid_domain&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ConfigTest::__pest_evaluable_doesnt_get_config_without_valid_domain&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_create_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_create_issue_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_create_issue_api_#('')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Exception_during_issue_creation_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_throws_an_exception_when_problem_diagnosis_is_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Authorize_method_returns_true_when_user_has_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Authorize_method_returns_false_when_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Authorize_method_returns_false_when_user_does_not_have_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_asController_throw_exception_if_the_service_request_is_closed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_getWorkOrderIfExists_method_throws_an_exception_when_work_order_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_getWorkOrderIfExists_method_return_null_if_the_uuid_is_null&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_getWorkOrderIfExists_method_return_work_order_if_the_uuid_correct&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_getWorkOrderIfExists_method_throw_exception_when_Work_Order_is_cancelled&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_createIssue_method_return_newly_created_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_getProblemDiagnosis_method_throws_an_exception_when_invalid_problem__diagnosis__id_given_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_getProblemDiagnosis_method_return_ProblemDiagnosis_if_the_uuid_correct&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_getResponse_method_returns_WorkOrderIssueResource_when_work_order_is_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_getResponse_method_returns_IssueResource_when_work_order_is_not_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CreateIssueTest::__pest_evaluable_Handle_method_creates_an_issue_and_work_order_issue_successfully_and_dispatch_events_when_a_work_order_is_passed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_unassign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_unassign_issue_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_unassign_issue_api_#('')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Exception_during_issue_unassign_from_WO_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Handle_method_unassign_an_issue_from_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Issue_not_matched_with_requested_service_request_this_throw_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Issue_states_not_mached_with_Assigned_this_throw_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_Work_Order_states_not_mached_with_ReadyToSchedule__Canceled_and_AwaitingAvailability_this_throw_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_and_Unassign_issue_if_no_other_WO_uses_the_same_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueUnassignedFromWorkOrderTest::__pest_evaluable_check_WO_has_no_other_issues_and_delete_WO_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachImageToServiceRequestJobTest::__pest_evaluable_Verify_the_job_implements_ShouldQueue_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachImageToServiceRequestJobTest::__pest_evaluable_Handles_invalid_url&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachImageToServiceRequestJobTest::__pest_evaluable_Handles_non_image_url&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachImageToServiceRequestJobTest::__pest_evaluable_Handles_successful_image_upload&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachImageToServiceRequestJobTest::__pest_evaluable_Exception_throws_if_s3_file_upload_failed&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachImageToServiceRequestJobTest::__pest_evaluable_Exception_throws_when_an_invalid_servicer_request_description_id_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachImageToServiceRequestJobTest::__pest_evaluable_Handles_image_upload_with_service_request_description&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachImageToServiceRequestJobTest::__pest_evaluable_Handles_image_upload_with_service_request_description_has_additional_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Migrations\AddServiceRequestIdToNotificationsTableTest::__pest_evaluable_it_rolls_back_migration_by_removing_service__request__id_column&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Onboarding\SearchAddressTest::__pest_evaluable_it_test_if_it_return_valid_response_when_searching_address&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Onboarding\SearchAddressTest::__pest_evaluable_test_if_it_return_valid_response_when_searching_invalid_address&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Onboarding\SearchAddressTest::__pest_evaluable_test_if_it_return_valid_response_when_searching_with_invalid_signed_url&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListStatusTest::__pest_evaluable_The_quote_list_API_response_for_web__Grouping_by_status__and_first_occurence_is_Pending_Review&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListStatusTest::__pest_evaluable_The_quote_list_API_response_for_web__Grouping_by_status__and_last__occurence_is_Rejected&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListStatusTest::__pest_evaluable_The_quote_list_API_response_for_web__Sorting__by_status_in_Ascending_Order__and_last__occurence_is_Rejected&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListStatusTest::__pest_evaluable_The_quote_list_API_response_for_web__Sorting_by_status_in_Ascending_Order__and_first_occurence_is_Pending_Review&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListStatusTest::__pest_evaluable_The_quote_list_API_response_for_web__Sorting_by_status_in_Descending_Order__and_first_occurence_is_Rejected&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListStatusTest::__pest_evaluable_The_quote_list_API_response_for_web__Sorting__by_status_in_Descending_Order__and_last__occurence_is_Pending_Review&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Rule\TimingRuleTest::__pest_evaluable_Validates_that_the_empty_timing_payload_request_show_the_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Rule\TimingRuleTest::__pest_evaluable_Validates_the_duplicate_values_present_in_the_timing_payload_request_and_it_shows_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Rule\TimingRuleTest::__pest_evaluable_Validates_invalid_timing_present_in_the_request_payload_and_it_shows_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Rule\TimingRuleTest::__pest_evaluable_The_valid_request_data_pass_the_validation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Rule\TimingRuleTest::__pest_evaluable_Validates_that_the_timing_payload_with_empty_string_show_the_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\RescheduleAppointmentTest::__pest_evaluable_Save_a_selected_service_window_as_a_task_appointment&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\RescheduleAppointmentTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_reschedule_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\RescheduleAppointmentTest::__pest_evaluable_User_without_a_token_cannot_access_reschedule_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\RescheduleAppointmentTest::__pest_evaluable_Unauthorized_organization_cannot_access_reschedule_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\RescheduleAppointmentTest::__pest_evaluable_Unauthorized_user_cannot_access_re_schedule_API__without_reschedule_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\RescheduleAppointmentTest::__pest_evaluable_User_cannot_save_a_selected_service_window_as_a_task_appointment_when_the_trip_state_is_invalid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeAddTest::__pest_evaluable_User_with_an_valid_token_can_delete_the_assignee_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeAddTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_service_request_assignee_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeAddTest::__pest_evaluable_User_without_a_token_cannot_access_service_request_assignee_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeAddTest::__pest_evaluable_The_request_with_an_invalid_service__request__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeAddTest::__pest_evaluable_The_request_with_an_invalid_user__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeAddTest::__pest_evaluable_The_request_from_another_organizations_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AwaitingAvailabilityTest::__pest_evaluable_throws_exception_when_request_user_is_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\CreateWorkOrderTest::__pest_evaluable_throws_exception_when_request_user_is_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\InProgressTest::__pest_evaluable_throws_exception_when_request_user_is_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\DeleteMediaTest::__pest_evaluable_The_user_with_a_valid_token_can_delete_the_service_request_media_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\DeleteMediaTest::__pest_evaluable_The_user_with_an_invalid_token_can_t_delete_the_service_request_media_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\DeleteMediaTest::__pest_evaluable_The_user_with_an_invalid_media_uuid_can_t_delete_the_service_request_media_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\DeleteMediaTest::__pest_evaluable_The_user_with_different_service__request__uuid_cannot_delete_the_service_request_media_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\UploadMediaTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_service_request_media_thumbnail_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\UploadMediaTest::__pest_evaluable_The_user_with_a_invalid_token_can_access_the_service_request_media_thumbnail_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\UploadMediaTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_service_request_original_media_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\UploadMediaTest::__pest_evaluable_The_user_with_invalid_payload_cannot_access_the_service_request_media_thumbnail_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\UploadMediaTest::__pest_evaluable_The_user_with_invalid_service_request_id_cannot_access_the_service_request_media_thumbnail_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\UploadMediaTest::__pest_evaluable_The_user_cannot_access_the_service_request_thumbnail_api_when_the_uploaded_file_is_not_an_image_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\UploadMediaTest::__pest_evaluable_The_user_with_invalid_media_type_cannot_upload_service_request_media_thumbnail_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\UploadMediaTest::__pest_evaluable_Validation_for_the_maximum_image_upload_count_for_service_request_media_thumbnails_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\UploadMediaTest::__pest_evaluable_Validation_for_the_maximum_image_upload_count_for_service_request_before_media_thumbnails_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\UploadMediaTest::__pest_evaluable_Validation_for_the_maximum_image_upload_count_for_service_request_after_media_thumbnails_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\Media\UploadMediaTest::__pest_evaluable_Validation_for_the_media_size_while_upload_media_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\DeleteResidentPastAvailabilityTest::__pest_evaluable_Delete_the_past_resident_availability_using_the_scheduler_artisan_command_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\DeleteResidentPastAvailabilityTest::__pest_evaluable_The_availability_date_is_in_the_past__so_the_day_passed_should_be_updated_to__yes__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\DeleteResidentPastAvailabilityTest::__pest_evaluable_The_availability_dates_of_a_service_request_are_in_the_past__so_the_day_passed_should_be_updated_to__yes__and_the_work_order_state_should_be_automatically_changed_to_awaiting_availability_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityShowTest::__pest_evaluable_User_without_already_scheduled_availability_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityShowTest::__pest_evaluable_User_with_already_scheduled_availability_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityShowTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityStoreTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityStoreTest::__pest_evaluable_The_request_with_an_invalid_service_request_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityStoreTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityStoreTest::__pest_evaluable_The_payload_with_date_earlier_than_today_is_invalid_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityStoreTest::__pest_evaluable_The_request_with_an_invalid_service_request_status_cannot_access_resident_availability_store_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityStoreTest::__pest_evaluable_Already_scheduled_user_cannot_schedule_again_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityStoreTest::__pest_evaluable_Payload_with_duplicate_date_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityStoreTest::__pest_evaluable_The_service_request_where_the_resident_adds_availability_when_the_admin_has_previously_added_availability_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreTest::__pest_evaluable_Create_a_service_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_are_added_with_correct_number_of_slots#(Carbon\CarbonImmutable, Carbon\CarbonImmutable, 1, …)&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_are_added_with_correct_number_of_slots#(Carbon\CarbonImmutable, Carbon\CarbonImmutable, 2, …)&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_are_added_with_correct_number_of_slots#(Carbon\CarbonImmutable, Carbon\CarbonImmutable, 16, …)&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_are_added_with_correct_number_of_slots#(Carbon\CarbonImmutable, Carbon\CarbonImmutable, 3, …) #1&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_are_added_with_correct_number_of_slots#(Carbon\CarbonImmutable, Carbon\CarbonImmutable, 8, …) #1&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_are_added_with_correct_number_of_slots#(Carbon\CarbonImmutable, Carbon\CarbonImmutable, 8, …) #2&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_are_added_with_correct_number_of_slots#(Carbon\CarbonImmutable, Carbon\CarbonImmutable, 3, …) #2&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_are_added_with_correct_number_of_slots#(Carbon\CarbonImmutable, Carbon\CarbonImmutable, 8, …) #3&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_All_free_slots_are_returned&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_throw_exception_when_invalid_input#(Carbon\CarbonImmutable, Carbon\CarbonImmutable) #1&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_throw_exception_when_invalid_input#(Carbon\CarbonImmutable, Carbon\CarbonImmutable) #2&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_throw_exception_when_slot_collision_occurs#(Carbon\CarbonImmutable, Carbon\CarbonImmutable) #1&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\DailyAgendaTest::__pest_evaluable_Appointments_throw_exception_when_slot_collision_occurs#(Carbon\CarbonImmutable, Carbon\CarbonImmutable) #2&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\TechnicianCalendarTest::__pest_evaluable_Calendar_returns_a_valid_set_of_open_service_windows_when_no_appointments&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\TechnicianCalendarTest::__pest_evaluable_Calendar_returns_no_windows_when_no_working_hours_defined&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\TechnicianCalendarTest::__pest_evaluable_Calendar_returns_a_valid_set_of_open_service_windows_when_appointments&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\TechnicianCalendarTest::__pest_evaluable_Calendar_returns_a_valid_set_of_open_service_windows_when_appointments_on_multiple_days&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\Scheduling\Domain\Entities\TechnicianCalendarTest::__pest_evaluable_Calendar_ignores_appointments_outside_configured_time_period&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\ServiceRequestRegister\ServiceRequestPhotosRegisterTest::__pest_evaluable_Throws_exception_when_service_request_is_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\ServiceRequestRegister\ServiceRequestPhotosRegisterTest::__pest_evaluable_Dispatches_photo_jobs_when_photos_exist_for_appfolio&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\ServiceRequestRegister\ServiceRequestPhotosRegisterTest::__pest_evaluable_Does_not_dispatch_photo_jobs_when_photos_do_not_exist&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Services\ServiceRequestRegister\ServiceRequestPhotosRegisterTest::__pest_evaluable_Upload_photos_successfully_and_update_addition_info_in_description_history_table&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\CreateTest::__pest_evaluable_The_valid_user_can_create_a_tag_using_a_valid_payload__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\CreateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_tag_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\CreateTest::__pest_evaluable_User_without_a_token_cannot_access_tag_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\CreateTest::__pest_evaluable_The_tag_can_t_create_using_with_an_invalid_payload__it_returns_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\CreateTest::__pest_evaluable_Unauthorized_organization_cannot_access_tag_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\CreateTest::__pest_evaluable_Unauthorized_user_cannot_access_tag_create_API__without_tag_create_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\DeleteTagTest::__pest_evaluable_User_with_a_valid_token_can_delete_tag_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\DeleteTagTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_tag_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\DeleteTagTest::__pest_evaluable_User_without_a_token_cannot_access_tag_delete_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\DeleteTagTest::__pest_evaluable_The_request_with_an_invalid_tag_uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\DeleteTagTest::__pest_evaluable_Unauthorized_organization_cannot_access_tag_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\DeleteTagTest::__pest_evaluable_Unauthorized_user_cannot_access_tag_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\DeleteTagTest::__pest_evaluable_The_tag_associated_with_one_organization_cannot_be_deleted_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\BlockOutAppointmentUpdateTest::__pest_evaluable_The_valid_user_can_update_block_out_API__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\BlockOutAppointmentUpdateTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_block_out_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\BlockOutAppointmentUpdateTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_block_out_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\BlockOutAppointmentUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\BlockOutAppointmentUpdateTest::__pest_evaluable_The_request_with_an_invalid_technician_uu__id_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\BlockOutAppointmentUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_block_out_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\BlockOutAppointmentUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_block_out_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\BlockOutAppointmentUpdateTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_get_the_block_out_data_of_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\BlockOutAppointmentUpdateTest::__pest_evaluable_User_cannot_update_regular_technician_appointment_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianAppointmentsTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_technician_appointments_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianAppointmentsTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_technician_block_out_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianAppointmentsTest::__pest_evaluable_Unauthorized_organization_cannot_access_technician_appointments_list_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianAppointmentsTest::__pest_evaluable_Unauthorized_user_cannot_access_technician_block_out_list_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianAppointmentsTest::__pest_evaluable_A_user_with_a_valid_token_can_access_technician_appointments_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianAppointmentsTest::__pest_evaluable_The_passed_date_not_present_in_the_list_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateTechnicianSkillsTest::__pest_evaluable_The_valid_user_can_update_the_technician_s_skills_update_API__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateTechnicianSkillsTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_technician_skills_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateTechnicianSkillsTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_technician_skills_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateTechnicianSkillsTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateTechnicianSkillsTest::__pest_evaluable_The_request_with_an_invalid_technician_uu__id_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateTechnicianSkillsTest::__pest_evaluable_Unauthorized_organization_cannot_access_technician_skills_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateTechnicianSkillsTest::__pest_evaluable_Unauthorized_user_cannot_access_technician_skills_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateTechnicianSkillsTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_get_the_technician_skills_data_of_user_in_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserAccountInfoUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_user_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserAccountInfoUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_user_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserAccountInfoUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_user_details_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserAccountInfoUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_details_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserAccountInfoUpdateTest::__pest_evaluable_The_user_associated_with_one_organization_cannot_update_other_organization_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserAccountInfoUpdateTest::__pest_evaluable_The_request_with_invalid_user__id_shows_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserAccountInfoUpdateTest::__pest_evaluable_Invalid_payload_request_show_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncVendorTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_sync_vendor_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncVendorTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_the_sync_vendor_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncVendorTest::__pest_evaluable_User_without_a_token_cannot_access_the_sync_vendor_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncVendorTest::__pest_evaluable_User_with_a_invalid_vendor_id_cannot_access_the_sync_vendor_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncVendorTest::__pest_evaluable_Unauthorized_organization_cannot_access_sync_vendor_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncVendorTest::__pest_evaluable_Unauthorized_user_cannot_access_sync_vendor_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncVendorTest::__pest_evaluable_If_the_app_folio_functionality_is_not_enabled__the_organization_will_be_unable_to_access_the_sync_vendor_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncVendorTest::__pest_evaluable_Properly_handles_job_queuing_and_behavior_of_syncing_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorShowTest::__pest_evaluable_User_with_a_valid_token_can_access_vendor_show_API_and_the_return_response_contain_correct_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorShowTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_vendor_show_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorShowTest::__pest_evaluable_User_with_a_valid_token_with_invalid_vendor_id_cannot_access_vendor_show_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorShowTest::__pest_evaluable_User_without_a_token_cannot_access_vendor_show_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorShowTest::__pest_evaluable_Unauthorized_user_cannot_access_vendor_show_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorShowTest::__pest_evaluable_Unauthorized_organization_cannot_access_vendor_show_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Successfully_return_vendor_onboarding_details_to_onboarding_owner_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Get_onboarding_details_with_invalid_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingDetailsTest::__pest_evaluable_Get_onboarding_details_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Successfully_set_status_for_valid_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_404_when_onboardingId_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Set_status_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Return_error_if_status_changed_from_higher_order_status_back_to_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_the_vendor_who_owns_the_onboarding_can_update_the_status__except_approve_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Only_allow_coordinator_with_correct_organization_can_approve_the_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetStatusTest::__pest_evaluable_Allow_coordinator_to_approve_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_The_valid_user_can_rename_his_own_work_order_view__and_it_will_return_the_proper_keys_after_the_rename&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_view_rename_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_view_rename_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_view_rename_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_view_rename_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_calendar_view_rename_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_view_rename_API__without_work_order_view_update_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_Unauthorized_user_cannot_access_user_view_rename_API__without_user_view_list_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_Unauthorized_user_cannot_access_user_view_rename_API__without_calendar_view_update_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_The_request_with_an_existing_view_name_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_The_request_with_a_predefined_view_name_in_another_view_can_be_recreated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_The_request_with_the_same_name_of_the_view_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_The_work_order_view_associated_with_one_user_cannot_be_renamed_by_another_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_The_work_order_view_associated_with_one_organization_cannot_be_renamed_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_The_user_view_associated_with_one_organization_cannot_be_renamed_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\RenameViewTest::__pest_evaluable_The_calendar_view_associated_with_one_organization_cannot_be_renamed_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\ViewListTest::__pest_evaluable_The_valid_user_can_view_the_work_order_view_list__and_it_contains_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\ViewListTest::__pest_evaluable_The_valid_user_can_view_the_user_view_list__and_it_contains_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\ViewListTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_view_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\ViewListTest::__pest_evaluable_User_without_a_token_cannot_access_view_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\ViewListTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_view_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\ViewListTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_view_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\ViewListTest::__pest_evaluable_Unauthorized_organization_cannot_access_calendar_view_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\ViewListTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_view_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\ViewListTest::__pest_evaluable_Unauthorized_user_cannot_access_user_view_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\ViewListTest::__pest_evaluable_Unauthorized_user_cannot_access_calendar_view_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\ViewListTest::__pest_evaluable_The_request_with_an_invalid_view_type_slug_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewCountTest::__pest_evaluable_The_valid_user_can_view_the_view_count__and_it_contains_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewCountTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_view_count_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewCountTest::__pest_evaluable_User_without_a_token_cannot_access_view_count_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewCountTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_view_count_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewCountTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_view_count_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewCountTest::__pest_evaluable_Unauthorized_organization_cannot_access_calendar_view_count_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewCountTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_view_count_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewCountTest::__pest_evaluable_Unauthorized_user_cannot_access_user_view_count_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewCountTest::__pest_evaluable_Unauthorized_user_cannot_access_calendar_view_count_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewCountTest::__pest_evaluable_User_with_an_invalid_view_type_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewCountTest::__pest_evaluable_Count_of_a_pinned_work_order_view_type_is_correct_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_User_with_a_valid_token_can_enroute_work_order_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_enroute_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_enroute_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_enroute_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_enroute_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_The_work_orders_associated_with_one_technician_cannot_be_updated_by_another_technician&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_The_work_orders_state_is_not_scheduled&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_If_user_is_technician_and_he_can_update_status_to_enroute_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_Already_enroute_work_order_cannot_enroute_again&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_User_with_a_valid_token_can_enroute_work_order_and_return_proper_response__For_mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\EnRouteTest::__pest_evaluable_The_service_call_with_a_working_state_can_t_be_en_route_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\DeleteInvoiceTest::__pest_evaluable_The_valid_user_can_access_the_delete_invoice_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\DeleteInvoiceTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_invoice_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\DeleteInvoiceTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_invoice_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\DeleteInvoiceTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\DeleteInvoiceTest::__pest_evaluable_The_request_with_an_invalid_invoice__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\DeleteInvoiceTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_and_invoice__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\DeleteInvoiceTest::__pest_evaluable_Unauthorized_organization_cannot_access_the_invoice_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\DeleteInvoiceTest::__pest_evaluable_Unauthorized_user_cannot_access_the_invoice_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\DeleteInvoiceTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_do_the_invoice_delete_action_for_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\DeleteInvoiceTest::__pest_evaluable_The_invoice_not_associated_with_the_given_work_order_shows_an_invalid_argument_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\DeleteInvoiceTest::__pest_evaluable_It_is_not_possible_to_delete_drafted_invoices_in_any_invoice_state_other_than_Draft&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ListInvoiceTest::__pest_evaluable_User_with_a_valid_token_can_access_invoice_list_API_and_the_return_response_contain_correct_keys__draft_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ListInvoiceTest::__pest_evaluable_User_with_a_valid_token_can_access_invoice_list_API_and_the_return_response_contain_correct_keys__payment_pending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ListInvoiceTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_invoice_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ListInvoiceTest::__pest_evaluable_User_without_a_token_cannot_access_invoice_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ListInvoiceTest::__pest_evaluable_Unauthorized_user_cannot_access_invoice_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ListInvoiceTest::__pest_evaluable_Unauthorized_organization_cannot_access_invoice_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Notification\ListNotificationTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_notification_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Notification\ListNotificationTest::__pest_evaluable_User_without_a_token_cannot_access_notification_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_User_with_a_valid_token_can_update_NTE_amount_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_NTE_amount_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_NTE_amount_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_The_request_with_an_invalid_work_order_task_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_and_work_order_task_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_NTE_amount_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_NTE_amount_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_The_work_order_associated_with_one_organization_are_not_update_to_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_The_payload_without_NTE_amount_shows_a_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_The_payload_with_a_non_numerical_NTE_amount_shows_a_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_The_payload_with_a_negative_or_less_than_zero_NTE_amount_shows_a_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\NteAmountUpdateTest::__pest_evaluable_The_payload_with_NTE_amount_greater_than_12_digits_shows_a_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_The_valid_user_can_create_the_work_order_quote_task__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_work_order_quote_task_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_work_order_quote_task_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_quote__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid__work__order__task__uuid__quote__uuid_and_quote__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_Unauthorized_user_no_permission_for_quote_update_and_task_create__cannot_access_the_work_order_quote_task_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_User_with_quote_update_permission_can_create_quote_task_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_User_with_quote_task_create_permission_can_create_quote_task_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_quote_task_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_The_work_order_quote_task_associated_with_one_organization_cannot_be_created_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_The_user_cannot_create_quote_with_an_invalid_media_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_The_user_can_create_a_quote_with_the_media_type_quote_task_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_The_user_cannot_create_quote_task_with_an_invalid_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_The_work_order_quote_status_should_be_quote__pending__review_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTaskTest::__pest_evaluable_The_proper_payload_creates_new_task_for_quote&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RestoreQuoteTest::__pest_evaluable_Restore_all_quote_task&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RestoreQuoteTest::__pest_evaluable_Work_order_status_is_not_equal_to_cancel_shows_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RestoreQuoteTest::__pest_evaluable_The_work_order_task_associated_with_another_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RestoreQuoteTest::__pest_evaluable_Sending_a_quote_not_related_to_the_work_order_is_throw_exception&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RestoreQuoteTest::__pest_evaluable_Cannot_restore_a_quote_with_Quote_status_other_than_rejected&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RestoreQuoteTest::__pest_evaluable_The_work_order_task_does_not_have_a_service_call_showing_a_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RestoreQuoteTest::__pest_evaluable_The_quote_service_call_does_not_match_the_work_order_service_call_and_shows_a_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RestoreQuoteTest::__pest_evaluable_Restore_all_quote_task__restore_to_pending_review_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_The_valid_user_can_update_the_work_order_quote_task__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_work_order_quote_task_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_work_order_quote_task_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_work_order_quote_task_update_with_empty_payload_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_quote__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_quote__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid__work__order__task__uuid__quote__uuid_and_quote__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_Unauthorized_user_no_permission_for_quote_update_and_task_update__cannot_access_the_work_order_quote_task_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_User_with_quote_update_permission_can_update_quote_task_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_User_with_quote_task_update_permission_can_update_quote_task_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_quote_task_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_The_work_order_quote_task_associated_with_one_organization_cannot_be_updated_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_A_Work_order_quote_task_cannot_be_updated_under_a_quote_where_status_is_not_Paused_or_WorkInPRogress_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_The_work_order_quote_status_should_be_quote__pending__review&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_Update_quote_task_without_adding_any_additional_material_s__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTaskTest::__pest_evaluable_Update_quote_task_with_new_materials_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ScopingTest::__pest_evaluable_Move_the_work_order_state_Created_to_Scoping&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\SendToInvoiceTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_ready_to_invoice&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\SendToInvoiceTest::__pest_evaluable_User_with_an_invalid_token_can_t_update_state_to_ready_to_invoice&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\SendToInvoiceTest::__pest_evaluable_User_without_a_token_can_t_update_state_to_ready_to_invoice&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\SendToInvoiceTest::__pest_evaluable_Unauthorized_user_cannot_update_state_to_ready_to_invoice&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\SendToInvoiceTest::__pest_evaluable_Unauthorized_organization_cannot_update_state_to_ready_to_invoice&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\SendToInvoiceTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_shows_the_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\SendToInvoiceTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\SendToInvoiceTest::__pest_evaluable_The_work_orders_not_in_proper_state_can_t_move_to_ready_to_invoice&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_update_trip_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_The_user_with_an_invalid_token_cannot_access_the_update_trip_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_update_trip_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_The_request_with_an_invalid_work__order__service__call__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid__work_order_task_uuid_and_service_call_uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_The_user_without_service_notes_cannot_update_the_trip_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_The_user_with_an_invalid_service_call_state_cannot_update_the_trip_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Unauthorized_organization_cannot_access_update_trip_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Unauthorized_user_cannot_access_update_trip_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_update_trip_API_with_materials_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_The_trip__end__with__type_is_required_when_trip__end__with_is_partially_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Checking_work_order_task_materials_count_before_and_after_updating_a_trip_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Work_order_status_will_be_automatically_changed_to__Paused__when_a__partially_completed__trip_is_updated_to__submit_quote__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Work_order_status_will_be_automatically_changed_to__Paused__when_a__partially_completed__trip_is_updated_to__await_for_quote_approval_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Work_order_status_will_be_automatically_changed_to__Ready_to_schedule__when_a__partially_completed__trip_is_updated_to__finish_with_another_day__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Work_order_status_will_be_automatically_changed_to__Paused__when_a__no_work__trip_is_updated_to__submit_quote__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Work_order_status_will_be_automatically_changed_to__Paused__when_a__no_work__trip_is_updated_to__await_for_quote_approval__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Work_order_status_will_be_automatically_changed_to__Ready_to_schedule__when_a__no_work__trip_is_updated_to__get_parts__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Work_order_status_will_be_automatically_changed_to__Ready_to_schedule__when_a__no_work__trip_is_updated_to__resident_did_not_show_up__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Work_order_status_will_be_automatically_changed_to__Ready_to_schedule__when_a__no_work__trip_is_updated_to__other__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Work_order_status_will_be_automatically_changed_to__Quality_check__when_trip_is_updated_to__work_complete__from__no_work__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\UpdateTripTest::__pest_evaluable_Work_order_status_will_be_automatically_changed_to__Quality_check__when_trip_is_updated_to__work_complete__from__partial_work__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ProblemCategoryUpdateTest::__pest_evaluable_User_with_an_valid_token_can_update_work_order_problem_category_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ProblemCategoryUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_problem_category_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ProblemCategoryUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_problem_category_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ProblemCategoryUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_problem_category_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ProblemCategoryUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_problem_category_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ProblemCategoryUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ProblemCategoryUpdateTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ProblemCategoryUpdateTest::__pest_evaluable_The_work_order_problem_category_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderHealthScoreTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_HealthScore_is_At_Risk&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderHealthScoreTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_HealthScore_is_Critical&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderHealthScoreTest::__pest_evaluable_The_work_order_HealthScore_will_be_updated_from_Critical_At_Risk_to_Healthy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesInvalidUuids&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesNonExistentUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyUnassignedFilterHandlesServiceRequestEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterIsCalledWithCorrectParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterCatchesExceptionWhenParsingInvalidUuid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Broadcasting\ServiceRequest\ServiceRequestChannelTest::__pest_evaluable_Allows_user_to_access_channel_if_authorized&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Broadcasting\ServiceRequest\ServiceRequestChannelTest::__pest_evaluable_prevents_user_to_access_channel_if_user_cannot_view_service_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Broadcasting\ServiceRequest\ServiceRequestChannelTest::__pest_evaluable_Prevents_user_to_access_channel_if_service_request_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Broadcasting\ServiceRequest\ServiceRequestChannelTest::__pest_evaluable_Prevents_user_to_access_channel_if_organization_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Broadcasting\ServiceRequest\ServiceRequestChannelTest::__pest_evaluable_Prevents_user_to_access_channel_if_user_s_and_service_request_organization_is_different&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\CreateServiceRequestActivityLogTest::__pest_evaluable_assigns_the_payload_correctly_in_the_constructor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCanceledTest::__pest_evaluable_Returns_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCanceledTest::__pest_evaluable_Implements_the_ShouldBroadcast_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCanceledTest::__pest_evaluable_Implements_the_ShouldDispatchAfterCommit_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCanceledTest::__pest_evaluable_Implements_the_ShouldQueue_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCanceledTest::__pest_evaluable_Extends_the_BaseIssueEvent_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueDeletedTest::__pest_evaluable_It_return_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueDeletedTest::__pest_evaluable_It_returns_the_correct_broadcast_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueDeletedTest::__pest_evaluable_It_return_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueDeletedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueDeletedTest::__pest_evaluable_Handles_missing_issue_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueDeletedTest::__pest_evaluable_retrieves_the_correct_issue_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_return_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_return_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_Handles_missing_issue_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueUpdatedTest::__pest_evaluable_retrieves_the_correct_issue_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestNoteDeletedTest::__pest_evaluable_ServiceRequestNoteDeleted_event_is_broadcasted_with_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestNoteDeletedTest::__pest_evaluable_ServiceRequestNoteDeleted_event_broadcastQueue_returns_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestNoteDeletedTest::__pest_evaluable_ServiceRequestNoteDeleted_event_broadcastAs_returns_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestNoteDeletedTest::__pest_evaluable_ServiceRequestNoteDeleted_event_broadcastOn_returns_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestNoteDeletedTest::__pest_evaluable_ServiceRequestNoteDeleted_event_broadcastWith_returns_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestResidentUpdatedEventTest::__pest_evaluable_ServiceRequestResidentUpdated_event_is_broadcasted_with_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestResidentUpdatedEventTest::__pest_evaluable_Implements_the_ShouldBroadcast_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestResidentUpdatedEventTest::__pest_evaluable_Implements_the_ShouldDispatchAfterCommit_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestResidentUpdatedEventTest::__pest_evaluable_broadcasts_NewMessageSent_event_with_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestResidentUpdatedEventTest::__pest_evaluable_broadcasts_ServiceRequestResidentUpdated_on_correct_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestResidentUpdatedEventTest::__pest_evaluable_it_returns_correct_broadcast_data_from_broadcastWith&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreatedEventTest::__pest_evaluable_Event_have_ServiceRequestWorkOrderBaseEvent_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreatedEventTest::__pest_evaluable_Event_have_ShouldBroadcast_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreatedEventTest::__pest_evaluable_Event_have_ShouldDispatchAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreatedEventTest::__pest_evaluable_Event_have_ShouldQueueAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreatedEventTest::__pest_evaluable_Event_have_proper_methods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreatedEventTest::__pest_evaluable_broadcasts_on_method_return_the_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreatedEventTest::__pest_evaluable_broadcastQueue_return_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreatedEventTest::__pest_evaluable_broadcastAs_returns_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreatedEventTest::__pest_evaluable_getWorkOrderDetails_returns_the_given_work_order_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreatedEventTest::__pest_evaluable_getWorkOrderDetails_throws_an_exception_when_work_order_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreatedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_Event_have_WorkOrderIssueBaseEvent_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_Event_have_ShouldBroadcast_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_Event_have_ShouldDispatchAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_Event_have_ShouldQueueAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_Event_have_proper_methods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcasts_on_method_return_the_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastQueue_return_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastAs_returns_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_getWorkOrderIssueDetails_returns_the_given_work_order_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_getWorkOrderIssueDetails_throws_an_exception_when_work_order_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderPropertyAddressUpdatedTest::__pest_evaluable_broadcast_queue_return_correct_queue_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderPropertyAddressUpdatedTest::__pest_evaluable_broadcast_as_return_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderPropertyAddressUpdatedTest::__pest_evaluable_broadcast_on_return_correct_channel_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderPropertyAddressUpdatedTest::__pest_evaluable_broadcast_with_return_correct_response_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\WorkOrderPropertyAddressUpdatedTest::__pest_evaluable_broadcast_event_is_dispatched&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestIssueCanceledListenerTest::__pest_evaluable_Dispatches_CreateServiceRequestActivityLog_event_when_handling_ServiceRequestIssueCanceledListener&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestIssueCanceledListenerTest::__pest_evaluable_Throws_ModelNotFoundException_when_issue_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestIssueUpdatedListenerTest::__pest_evaluable_Dispatches_CreateServiceRequestActivityLog_event_when_handling_ServiceRequestIssueUpdated&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestIssueUpdatedListenerTest::__pest_evaluable_Throws_ModelNotFoundException_when_issue_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\SyncServiceRequestWorkOrderListenerTest::__pest_evaluable_Dispatches_WorkOrderUpdate_event_when_handling_class_SyncServiceRequestWorkOrderListener_implements_ShouldQueue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\ServiceRequestDescriptionModelTest::__pest_evaluable__ServiceRequestDescription_model_has_a_proper_relationship_with__→_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\ServiceRequestDescriptionModelTest::__pest_evaluable__ServiceRequestDescription_model_has_a_proper_relationship_with__→_createdUser&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\ServiceRequestDescriptionModelTest::__pest_evaluable__ServiceRequestDescription_model_has_a_proper_relationship_with__→_serviceRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\WorkOrderModelTest::__pest_evaluable__WorkOrder_model_has_a_proper_relationship_with__→_workOrderIssues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\WorkOrderModelTest::__pest_evaluable__WorkOrder_model_has_a_proper_relationship_with__→_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\WorkOrderModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_include_the_appropriate_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\ServiceRequest\WorkOrder\WorkOrderIssueResourceTest::__pest_evaluable_Work_order_issue_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueListResourceTest::__pest_evaluable_WorkOrderIssueListResource_transforms_a_single_WorkOrderIssue_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\WorkOrderIssueResourceTest::__pest_evaluable_WorkOrderIssueResource_transforms_a_single_WorkOrderIssue_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\UnassignIssueResourceTest::__pest_evaluable_Unassign_Issue_from_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\UnassignedStateTest::__pest_evaluable_has_the_correct_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\UnassignedStateTest::__pest_evaluable_has_the_correct_action_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\UnassignedStateTest::__pest_evaluable_returns_the_correct_label&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\UnassignedStateTest::__pest_evaluable_returns_the_correct_actions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Support\TransactionHelperTest::__pest_evaluable_successful_transaction_runs_without_errors&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Support\TransactionHelperTest::__pest_evaluable_exception_is_rethrown_when_no_factory_is_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Support\TransactionHelperTest::__pest_evaluable_custom_exception_factory_wraps_original&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Support\TransactionHelperTest::__pest_evaluable_rollback_fails_gracefully_and_logs&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Support\TransactionHelperTest::__pest_evaluable_status_code_is_included_in_log_payload_when_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ArchitectureTest::__pest_evaluable_Does_not_use_debugging_functions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ArchitectureTest::__pest_evaluable_Uses_the_redirect_facade_for_redirecting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithPCWillSubmitAQuoteForMeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Project_coordinator_will_submit_a_quote_for_me__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_User_with_a_valid_token_can_delete_an_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_delete_issue_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_delete_issue_api_#('')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_The_request_with_an_invalid_uuid_return_error_response_#(404, 'invalid-uuid', 'invalid-uuid')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_The_request_with_an_invalid_uuid_return_error_response_#(Closure) #1&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_The_request_with_an_invalid_uuid_return_error_response_#(Closure) #2&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_Exception_during_issue_deletion_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_Issue_is_deleted_successfully_when_unassigned_and_not_linked_to_WorkOrderIssue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_Issue_deletion_fails_when_issue_has_related_WorkOrderIssue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_Issue_is_deleted_when_assigned_and_all_work_orders_are_ready__to__schedule&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_Issue_is_not_deleted_when_assigned_and_not_all_work_orders_are_ready__to__schedule&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_Authorize_method_returns_true_for_a_user_with_the_correct_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_Authorize_method_returns_false_when_user_does_not_have_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_Authorize_method_returns_false_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_Issue_cannot_be_deleted_if_it_does_not_belong_to_the_specified_service_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_Handel_method_dispatch_UpdateServiceRequestIssueCanceledActivityLogJob&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_WorkOrder_is_deleted_if_only_one_WorkOrderIssue_exists_for_it&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\DeleteIssueTest::__pest_evaluable_WorkOrder_is_not_deleted_when_it_has_multiple_associated_WorkOrderIssues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_issue_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_issue_update_API_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_issue_update_API_#('')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_The_request_with_an_invalid_uuid_return_error_response_#(404, 'invalid-uuid', 'invalid-uuid')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_The_request_with_an_invalid_uuid_return_error_response_#(Closure) #1&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_The_request_with_an_invalid_uuid_return_error_response_#(Closure) #2&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Event_is_not_dispatched_if_payload_data_and_issue_data_are_same_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_User_not_found_exception_is_handled_accurately&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Issue_not_match_with_service_request_throw_exception&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Exception_during_issue_updation_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_throws_an_exception_when_problem_diagnosis_is_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_an_issue_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Authorize_method_returns_true_for_a_user_with_the_correct_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Authorize_method_returns_false_when_user_does_not_have_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Authorize_method_returns_false_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_updates_issue_title__description__and_problem_diagnosis_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_does_not_update_issue_if_no_changes_are_made_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_Handle_method_trigger_events&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_getResponse_method_returns_WorkOrderIssueResource_when_work_order_is_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UpdateIssueTest::__pest_evaluable_getResponse_method_returns_IssueResource_when_work_order_is_not_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\WorkOrder\CreateTest::__pest_evaluable_The_valid_user_can_create_a_work_order_using_a_valid_payload__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\WorkOrder\CreateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\WorkOrder\CreateTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\WorkOrder\CreateTest::__pest_evaluable_The_work_order_cannot_be_created_using_an_invalid_payload_and_it_returns_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\WorkOrder\CreateTest::__pest_evaluable_The_work_order_can_t_be_created_using_empty_resident_payload__first_name__last__name__phone_number__email__and_primary_resident_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\WorkOrder\CreateTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\WorkOrder\CreateTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_create_API__without_work_order_create_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\WorkOrder\CreateTest::__pest_evaluable_The_work_order_cannot_create_using_wrong_problem_sub_category_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleListViewTest::__pest_evaluable_User_with_a_valid_token_can_access_role_list_API_and_the_return_response_contain_correct_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleListViewTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_role_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleListViewTest::__pest_evaluable_User_without_a_token_cannot_access_role_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleListViewTest::__pest_evaluable_Unauthorized_user_cannot_access_role_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleListViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_role_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleListViewTest::__pest_evaluable_User_can_access_role_list_API_with_sort&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleListViewTest::__pest_evaluable_User_can_access_role_list_API_with_search&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleUpdateTest::__pest_evaluable_The_valid_user_can_update_role__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_role_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_role_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleUpdateTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_role_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_role_update_API__without_role_view_update_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleUpdateTest::__pest_evaluable_The_role_associated_with_one_organization_cannot_be_updated_by_another_organization_s_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleUpdateTest::__pest_evaluable_It_is_possible_to_update_a_role_with_the_name_of_already_deleted_role_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AccessMethodUpdateTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_access_method_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AccessMethodUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_service_request_access_method_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AccessMethodUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_service_request_access_method_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AccessMethodUpdateTest::__pest_evaluable_The_request_with_an_invalid_service_request_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AccessMethodUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AccessMethodUpdateTest::__pest_evaluable_The_request_with_an_invalid_property_access_method_enum_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AccessMethodUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_service_request_access_method_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AccessMethodUpdateTest::__pest_evaluable_The_service__request_associated_with_one_organization_are_not_update_to_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_User_with_a_valid_token_can_update_service_request_description_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_service_request_description_update_API_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_service_request_description_update_API_#('')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_The_request_with_an_invalid_data_return_error_response_#(404, ['Vero eos sed repellendus. Con…autem.'], '546c9cea-2869-371b-845e-04a69e3f034e')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_The_request_with_an_invalid_data_return_error_response_#(422, ['aaaaaaaaaaaaaaaaaaaaaaaaaaaaa…aaaaaa'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_The_request_with_an_invalid_data_return_error_response_#(422, [null])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_The_description_is_being_stored_accurately_for_the_first_time_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_The_description_is_being_stored_accurately_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_The_description_is_being_stored_accurately_and_all_the_previous_service_request_descriptions_are_soft_deleted_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_Check_if_the_same_description_already_exists__new_record_is_not_created_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_Update_description_user_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\DescriptionUpdateTest::__pest_evaluable_Update_description_exception&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability__with_service_request_store_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_The_request_with_an_invalid_service_request_uuid_parameter_shows_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_Unauthorized_organization_cannot_access_store_resident_availability_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_Unauthorized_user_cannot_access_store_resident_availability_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_The_payload_with_date_earlier_than_today_is_invalid_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\AdminStoreResidentAvailabilityTest::__pest_evaluable_Payload_with_already_added_date_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentUpdateTest::__pest_evaluable_User_with_an_valid_token_can_update_service_request_resident_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentUpdateTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentUpdateTest::__pest_evaluable_cannot_update_service_request_when_request_payload_included_with_invalid_resident&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentUpdateTest::__pest_evaluable_Exception_during_service_request_resident_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_work_order_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_The_request_with_an_invalid_service__request__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestCreateWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_create_work_order_with_images&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_valid_token_can_delete_a_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_delete_work_order_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_delete_work_order_api_#('')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_The_request_with_an_invalid_uuid_return_error_response_#(404, 'invalid-uuid')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_without_delete_permission_cannot_access_work_order_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_Exception_during_work_order_delete_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_Unauthorized_user_cannot_access_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_Authorize_method_returns_false_when_user_does_not_have_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_Authorize_method_returns_false_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_User_without_permission_cannot_delete_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_Throws_CouldNotPerformTransition_when_work_order_state_is_invalid_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_deleteWorkOrderIssues_method_delete_the_work_order_issue_when_work_order_have_one_work_order_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_delete_WorkOrderIssues_method_not_throw_any_error_when_work_order_issue_is_empty_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_updateIssueStatus_method_sets_issue_state_to_unassigned_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_canIssueStatusBeUpdatedToUnassigned_method_returns_false_when_at_least_one_work_order_is_not_canceled_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_canIssueStatusBeUpdatedToUnassigned_method_returns_true_when_the_work_orders_collection_is_empty_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\WorkOrder\DeleteWorkOrderTest::__pest_evaluable_canIssueStatusBeUpdatedToUnassigned_method_returns_true_when_all_work_orders_are_canceled_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\TagViewTest::__pest_evaluable_User_with_a_valid_token_can_access_tag_details_API_and_the_return_response_contain_correct_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\TagViewTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_tag_details_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\TagViewTest::__pest_evaluable_User_without_a_token_cannot_access_tag_details_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\TagViewTest::__pest_evaluable_The_request_with_an_invalid_tag_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\TagViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_tag_details_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\TagViewTest::__pest_evaluable_Unauthorized_user_cannot_access_tag_details_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\TagViewTest::__pest_evaluable_The_tag_associated_with_one_organization_are_not_visible_to_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\CreateTechnicianBlockOutTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_technician_block_out_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\CreateTechnicianBlockOutTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_technician_block_out_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\CreateTechnicianBlockOutTest::__pest_evaluable_Unauthorized_organization_cannot_access_technician_block_outs_create_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\CreateTechnicianBlockOutTest::__pest_evaluable_Unauthorized_user_cannot_access_technician_block_out_create_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\CreateTechnicianBlockOutTest::__pest_evaluable_The_valid_user_can_create_block_out_API__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\CreateTechnicianBlockOutTest::__pest_evaluable_The_valid_user_with_invalid_payload_can_t_create_blockout__it_will_return_validation_message_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\CreateTechnicianBlockOutTest::__pest_evaluable_The_valid_user_can_add_blockout_on_re_opened_job_schedule_timings__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\CreateTechnicianBlockOutTest::__pest_evaluable_The_valid_user_can_add_blockout_on_active_service_call_exist_time__and_it_will_return_validation_message_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianWorkingHoursTest::__pest_evaluable_The_valid_user_can_import_the_working_hours__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianWorkingHoursTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_working_hours_import_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianWorkingHoursTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_working_hours_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianWorkingHoursTest::__pest_evaluable_Unauthorized_organization_cannot_access_working_hours_import_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianWorkingHoursTest::__pest_evaluable_Unauthorized_user_cannot_access_working_hours_import_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianWorkingHoursTest::__pest_evaluable_Non_technician_users_can_t_get_the_working_hours_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ListTechnicianWorkingHoursTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_get_the_working_hours_data_for_another_organization_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Technician\UpdateWorkingHoursTest::__pest_evaluable_The_valid_user_can_update_the_technician_s_working_hours_update_API__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateWorkingHoursTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_working_hours_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateWorkingHoursTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_working_hours_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateWorkingHoursTest::__pest_evaluable_Passing_the_user__uuid_to_the_update_API_without_the_technician_role_is_resulting_in_a_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateWorkingHoursTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Technician\UpdateWorkingHoursTest::__pest_evaluable_Validation_errors_occur_when_weekday_payload_is_given_without_lunch_break_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Technician\UpdateWorkingHoursTest::__pest_evaluable_Validation_errors_occur_when_the_weekday_payload_is_given_without_weekday_or_isEnabled_is_false_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\UpdateWorkingHoursTest::__pest_evaluable_The_request_with_an_invalid_user_uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Technician\UpdateWorkingHoursTest::__pest_evaluable_Unauthorized_organization_cannot_access_working_hours_update_API_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Technician\UpdateWorkingHoursTest::__pest_evaluable_Unauthorized_user_cannot_access_working_hours_update_API_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\Technician\UpdateWorkingHoursTest::__pest_evaluable_Validate_given_payload_is_saved_properly_in_DB_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ViewTechnicianCalendarTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_view_technician_calendar_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ViewTechnicianCalendarTest::__pest_evaluable_A_user_without_a_token_cannot_view_technician_calendar_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ViewTechnicianCalendarTest::__pest_evaluable_Unauthorized_organization_cannot_view_technician_calendar_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ViewTechnicianCalendarTest::__pest_evaluable_Unauthorized_user_cannot_view_technician_calendar_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ViewTechnicianCalendarTest::__pest_evaluable_The_request_without_form_data_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ViewTechnicianCalendarTest::__pest_evaluable_The_valid_user_can_view_technician_calendar__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncAllVendorsTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_sync_all_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncAllVendorsTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_the_sync_all_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncAllVendorsTest::__pest_evaluable_User_without_a_token_cannot_access_the_sync_all_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncAllVendorsTest::__pest_evaluable_Unauthorized_organization_cannot_access_sync_all_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncAllVendorsTest::__pest_evaluable_Unauthorized_user_cannot_access_sync_all_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncAllVendorsTest::__pest_evaluable_If_the_app_folio_functionality_is_not_enabled__the_organization_will_be_unable_to_access_the_sync_all_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\Actions\SyncAllVendorsTest::__pest_evaluable_Properly_handles_job_queuing_and_behavior_of_syncing_all_vendors&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorListTest::__pest_evaluable_User_with_a_valid_token_can_access_vendor_list_API_and_the_return_response_contain_correct_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorListTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_vendor_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorListTest::__pest_evaluable_User_without_a_token_cannot_access_vendor_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorListTest::__pest_evaluable_Unauthorized_user_cannot_access_vendor_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorListTest::__pest_evaluable_Unauthorized_organization_cannot_access_vendor_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Can_set_service_areas_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_area&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Set_service_areas_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnBoardingServiceAreasTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_both_problem_categories__vendor_onboarding_statuses_and_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_problem_categories&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_statuses&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Invalid_lookup_type_should_return_422&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Can_fetch_only_vendor_onboarding_states&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingLookupTest::__pest_evaluable_Get_lookup_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Can_set_services_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_service_offered&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetServicesTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_A_valid_user_can_use_the_work_order_view_pin_API__and_it_will_return_proper_data_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_A_valid_user_can_use_the_user_view_pin_API__and_it_will_return_proper_data_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_view_pin_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_User_without_a_token_cannot_access_view_pin_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_If_the_action_parameter_is_not_equal_to_pin_or_unpin__it_will_show_a_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_view_pin_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_view_pin_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_calendar_view_pin_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_view_pin_API__without_work_order_view_list_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_Unauthorized_user_cannot_access_user_view_pin_API__without_user_list_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_Unauthorized_user_cannot_access_calendar_view_pin_API__without_calendar_list_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_The_work_order_view_associated_with_one_organization_cannot_be_pinned_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_The_user_view_associated_with_one_organization_cannot_be_pinned_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_The_calendar_view_associated_with_one_organization_cannot_be_pinned_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_The_work_order_view_associated_with_one_user_cannot_be_pinned_by_another_user_s_view_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_The_user_view_associated_with_one_user_cannot_be_pinned_by_another_user_s_view_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\PinViewTest::__pest_evaluable_The_calendar_view_associated_with_one_user_cannot_be_pinned_by_another_user_s_view_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_valid_user_can_update_his_own_work_order_view__and_it_will_return_the_proper_keys_after_the_update_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_valid_user_can_update_his_own_user_view__and_it_will_return_the_proper_keys_after_the_update_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_view_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_view_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_request_with_an_empty_payload_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_view_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_view_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_calendar_view_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_view_update_API__without_work_order_view_update_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_Unauthorized_user_cannot_access_user_view_update_API__without_user_list_update_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_Unauthorized_user_cannot_access_calendar_view_update_API__without_user_list_update_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_user_can_t_update_the_predefined_work_order_view_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_user_can_t_update_the_predefined_user_view_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_user_can_t_update_the_predefined_calendar_view_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_work_order_view_associated_with_one_user_cannot_be_updated_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_user_view_associated_with_one_user_cannot_be_updated_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_calendar_view_associated_with_one_user_cannot_be_updated_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_work_order_view_associated_with_one_organization_cannot_be_updated_by_another_organization_s_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_user_view_associated_with_one_organization_cannot_be_updated_by_another_organization_s_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\UpdateViewTest::__pest_evaluable_The_calendar_view_associated_with_one_organization_cannot_be_updated_by_another_organization_s_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_complete_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_user_with_an_invalid_token_cannot_access_the_work_order_complete_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_work_order_complete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_and_work_order_task_uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_user_without_service_notes_cannot_complete_the_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_user_with_a_invalid_state_cannot_complete_the_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_work_orders_associated_with_one_technician_cannot_be_updated_by_another_technician&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_cancel_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_complete_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_user_without_work_type_cannot_access_the_work_order_complete_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_user_is_unable_to_access_the_work_order_completion_API_because_the_service_call_state_transition_is_not_permitted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_user_with_invalid_work_type_cannot_access_the_work_order_complete_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_complete_api_with_materials_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CompleteTest::__pest_evaluable_The_user_with_a_valid_token_with_invalid_quantity__type_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_fully_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_fully_paid_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_fully_paid_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_request_with_an_invalid_invoice__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_and_invoice__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_Unauthorized_organization_cannot_access_the_fully_paid_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_Unauthorized_user_cannot_access_the_fully_paid_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_do_the_fully_paid_action_for_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_The_invoice_not_associated_with_the_given_work_order_shows_an_invalid_argument_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_PaymentPending_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_QualityCheck__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_fully_paid_invoice_API_with_work_order_state_ReadyToInvoice__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\FullyPaidInvoiceTest::__pest_evaluable_It_is_not_possible_to_access_fully_paid_invoice_API_with_invoice_state_other_than_PaymentPending__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_voided_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_void_invoice_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_void_invoice_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_The_request_with_an_invalid_invoice__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_and_invoice__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_Unauthorized_organization_cannot_access_the_void_invoice_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_Unauthorized_user_cannot_access_the_void_invoice_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_do_the_void_invoice_action_for_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_The_invoice_not_associated_with_the_given_work_order_shows_an_invalid_argument_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_The_invoice_with_Payment_Pending_state_can_access_void_invoice_action&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_The_invoice_with_Partially_Paid_state_can_access_void_invoice_action_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_The_invoice_with_Paid_state_can_access_void_invoice_action&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\VoidedInvoiceTest::__pest_evaluable_It_is_not_possible_to_access_void_invoice_action_with_invoice_state_Draft&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_Accept_all_quote_task&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_Partially_approve_quote&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_Partially_approve_quote_with_some_tasks_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_Cannot_approve_quote_of_Work_order_having_state_other_than_paused_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_Sending_a_quote_that_is_not_related_to_the_work_order_throws_exception&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_Sending_a_quote_without_any_task_throws_exception&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_Sending_empty_task_to_approve_throw_exception&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_valid_user_can_accept_all_quote_tasks_using_approve_quote_API__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_approve_quote_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_approve_quote_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_request_with_an_invalid_quote__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid__work__order__task__uuid_and_quote__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_approve_quote_API_can_not_work_with_an_empty_payload_of_approve__quote__task__ids_and_returns_a_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_approve_quote_API_can_not_work_with_an_empty_payload_and_returns_a_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_approve_quote_API_can_not_work_with_an_empty_payload_of_invalid_approve__quote__task__ids_and_reject__quote__task__ids__returns_a_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_Unauthorized_organization_cannot_access_the_approve_quote_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_Unauthorized_user_cannot_access_the_approve_quote_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_quote_associated_with_one_organization_cannot_be_approved_by_another_organization_s_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_work_order_task_not_associated_with_the_given_work_order_shows_an_invalid_argument_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_quote_not_associated_with_the_given_work_order_shows_an_invalid_argument_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_Cannot_approve_work_order_with_Quote_status_other_than_pending_approval_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\ApproveQuoteTest::__pest_evaluable_The_number_of_quote_tasks_given_does_not_match_the_number_of_quote_tasks_currently_in_use__throw_quote_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\SubmitForApprovalTest::__pest_evaluable_Send_quote_for_approval&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\SubmitForApprovalTest::__pest_evaluable_The_user_with_valid_token_can_submit_quote_for_approval&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\SubmitForApprovalTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_submit_quote_approval_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\SubmitForApprovalTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\SubmitForApprovalTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\SubmitForApprovalTest::__pest_evaluable_The_request_with_an_invalid_quote__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\SubmitForApprovalTest::__pest_evaluable_Unauthorized_user_cannot_access_submit_quote_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\SubmitForApprovalTest::__pest_evaluable_The_work_order_quote_task_associated_with_one_organization_cannot_be_handle_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\SubmitForApprovalTest::__pest_evaluable_Unauthorized_organization_cannot_access_submit_quote_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_User_with_a_valid_token_can_reopen_work_order_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_reopen_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_reopen_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_reopen_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_reopen_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_The_work_orders_associated_with_one_technician_cannot_be_updated_by_another_technician&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_The_work_orders_not_in_proper_state_can_t_move_to_reopen&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_reopen_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_ReOpen_a_cancel_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_ReOpen_a_Resolve_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReOpenTest::__pest_evaluable_ReOpen_a_TripAvoidance_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResumeTripTest::__pest_evaluable_User_with_a_valid_token_can_resume_work_order_trip_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResumeTripTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_resume_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResumeTripTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_resume_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResumeTripTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_resume_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResumeTripTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_resume_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResumeTripTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResumeTripTest::__pest_evaluable_The_request_with_an_invalid_work_order_task_uuid_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResumeTripTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_resumed_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResumeTripTest::__pest_evaluable_The_work_orders_associated_with_one_technician_cannot_be_resumed_by_another_technician&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResumeTripTest::__pest_evaluable_The_work_orders_not_in_proper_state_can_t_move_to_resume&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResumeTripTest::__pest_evaluable_Cannot_resume_trip_if_the_service_call_is_not_in_proper_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\ActivityLogListTest::__pest_evaluable_The_valid_user_can_access_the_work_order_activity_log_list_API__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\ActivityLogListTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_work_order_activity_log_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\ActivityLogListTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_work_order_activity_log_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\ActivityLogListTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\ActivityLogListTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_and_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\ActivityLogListTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\ActivityLogListTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_activity_log_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\ActivityLogListTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_activity_log_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\ActivityLogListTest::__pest_evaluable_The_work_order_activity_log_list_associated_with_one_organization_cannot_be_accessed_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeDeleteTest::__pest_evaluable_User_with_an_valid_token_can_delete_the_assignee_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeDeleteTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_work_order_delete_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeDeleteTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_assignee_delete_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeDeleteTest::__pest_evaluable_Unauthorized_organization_cannot_access__work_order_assignee_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeDeleteTest::__pest_evaluable_The_request_with_an_invalid_work__order__note__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeDeleteTest::__pest_evaluable_The_request_with_an_invalid_user__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeDeleteTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_assignee_delete_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\DeleteMediaTest::__pest_evaluable_The_user_with_a_valid_token_can_delete_the_work_order_media_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\DeleteMediaTest::__pest_evaluable_The_user_with_an_invalid_token_can_t_delete_the_work_order_media_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\DeleteMediaTest::__pest_evaluable_The_user_with_an_invalid_media_uuid_can_t_delete_the_work_order_media_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\DeleteMediaTest::__pest_evaluable_The_user_with_different_work__order__uuid_cannot_delete_the_work_order_media_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_The_user_with_a_invalid_token_can_access_the_work_order_media_thumbnail_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_media_thumbnail_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_original_media_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_The_user_with_invalid_payload_cannot_access_the_work_order_media_thumbnail_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_The_user_with_invalid_work_order_id_cannot_access_the_work_order_media_thumbnail_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_The_user_cannot_access_the_work_order_thumbnail_api_when_the_uploaded_file_is_not_an_image_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_The_user_with_invalid_media_type_cannot_upload_work_order_media_thumbnail_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_Validation_for_the_maximum_image_upload_count_for_work_order_media_thumbnails_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_Validation_for_the_maximum_image_upload_count_for_work_order_before_media_thumbnails_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_Validation_for_the_maximum_image_upload_count_for_work_order_after_media_thumbnails_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_Validation_for_the_work_order_task_id_while_upload_media_for_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Media\UploadMediaTest::__pest_evaluable_Validation_for_the_media_size_while_upload_media_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteUpdateTest::__pest_evaluable_The_valid_user_can_update_the_work_order_note_update_API__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteUpdateTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_work_order_note_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteUpdateTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_work_order_note_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteUpdateTest::__pest_evaluable_The_request_with_an_invalid_work__order__note__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_note_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_note_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteUpdateTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_get_the_work_order_note_data_for_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateDeleteTest::__pest_evaluable_User_with_a_valid_token_can_delete_work_order_due_date_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateDeleteTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_due_date_delete_API#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateDeleteTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_due_date_delete_API#('')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateDeleteTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_due_date_delete_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateDeleteTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_due_date_delete_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateDeleteTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_shows_the_validation_error_#('fef91fa3-7f8d-3d77-9f91-8b98458916cc')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateDeleteTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_deleted_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateDeleteTest::__pest_evaluable_Delete_due_date_exception&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PropertyAddressUpdateTest::__pest_evaluable_User_with_an_valid_token_can_update_work_order_property_address_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PropertyAddressUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_property_address_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PropertyAddressUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_property_address_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PropertyAddressUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PropertyAddressUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_property_address_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PropertyAddressUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PropertyAddressUpdateTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PropertyAddressUpdateTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::applyDateFilterWithSingleDate&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::applyDateFilterWithDateBetween&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::applyDateFilterWithDateNotBetween&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::applyDateFilterWithOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::applyDateFilterWithInvalidOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::applyDateFilterWithInvalidDateOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::dateValuesForIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::dateValuesForIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::dateValuesForIsAfterOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::dateValuesForIsBeForeOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::dateOperationResponse&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::resolveWhereClauseOperator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::getDateRangeFromSlug&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::getDateRangeFromSlugThrowsExceptionForInvalidSlug&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::applyDateFilterWithInvalidGroupOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\DateFilterServiceTest::resolveOperatorSetForDateSlug&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Traits\FilterableTest::scopeFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Traits\FilterableTest::scopeApplyFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Traits\FilterableTest::scopeProcess&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Traits\FilterableTest::scopeProcessCallsFilterService&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Traits\SortableTest::scopeApplySorting&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Traits\SortableTest::scopePrepareSortValue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Action\Issue\BaseActionTest::__pest_evaluable_Throws_an_exception_when_getSlug_is_called&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Action\Issue\BaseActionTest::__pest_evaluable_Returns_the_correct_route_when_getRoute_is_called&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Action\Issue\BaseActionTest::__pest_evaluable_Returns_false_when_authorize_is_called_with_a_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateOperationTest::__pest_evaluable_DateOperation_enum_has_correct_cases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateOperationTest::__pest_evaluable_DateOperation_getDescription_returns_correct_descriptions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Enums\DateOperationTest::__pest_evaluable_DateOperation_can_be_invoked_statically&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\BaseIssueEventTest::__pest_evaluable_Retrieves_issue_details_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\BaseIssueEventTest::__pest_evaluable_Returns_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\BaseIssueEventTest::__pest_evaluable_Broadcasts_on_the_correct_channel&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\BaseIssueEventTest::__pest_evaluable_Broadcasts_with_the_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\BaseIssueEventTest::__pest_evaluable_Handles_missing_issue_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_return_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_return_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_Handles_missing_issue_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_retrieves_the_correct_issue_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\Issue\ServiceRequestIssueCreatedTest::__pest_evaluable_Extends_the_BaseIssueEvent_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogCreatedEventTest::__pest_evaluable_Event_has_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogCreatedEventTest::__pest_evaluable_Event_returns_the_correct_broadcast_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogCreatedEventTest::__pest_evaluable_Event_has_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogCreatedEventTest::__pest_evaluable_Event_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogCreatedEventTest::__pest_evaluable_Event_throws_an_exception_if_activity_log_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestAddressUpdatedTest::__pest_evaluable_broadcast_queue_return_correct_queue_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestAddressUpdatedTest::__pest_evaluable_broadcast_as_return_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestAddressUpdatedTest::__pest_evaluable_broadcast_on_return_correct_channel_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestAddressUpdatedTest::__pest_evaluable_broadcast_with_return_correct_response_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestAddressUpdatedTest::__pest_evaluable_broadcast_event_is_dispatched&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestPriorityUpdatedTest::__pest_evaluable_broadcast_queue_return_correct_queue_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestPriorityUpdatedTest::__pest_evaluable_broadcast_as_return_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestPriorityUpdatedTest::__pest_evaluable_broadcast_on_returns_correct_channel_names&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestPriorityUpdatedTest::__pest_evaluable_broadcast_with_returns_correct_response_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestPriorityUpdatedTest::__pest_evaluable_broadcast_event_is_dispatched&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestPriorityUpdatedTest::__pest_evaluable_getServiceRequestDetails_method_returns_proper_ServiceRequest_instance&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceledEventTest::__pest_evaluable_Event_have_ServiceRequestWorkOrderBaseEvent_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceledEventTest::__pest_evaluable_Event_have_ShouldBroadcast_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceledEventTest::__pest_evaluable_Event_have_ShouldDispatchAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceledEventTest::__pest_evaluable_Event_have_ShouldQueueAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceledEventTest::__pest_evaluable_Event_have_proper_methods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceledEventTest::__pest_evaluable_broadcasts_on_method_return_the_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceledEventTest::__pest_evaluable_broadcastQueue_return_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceledEventTest::__pest_evaluable_broadcastAs_returns_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceledEventTest::__pest_evaluable_getWorkOrderDetails_returns_the_given_work_order_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceledEventTest::__pest_evaluable_getWorkOrderDetails_throws_an_exception_when_work_order_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceledEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdatedEventTest::__pest_evaluable_Event_have_ServiceRequestWorkOrderBaseEvent_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdatedEventTest::__pest_evaluable_Event_have_ShouldBroadcast_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdatedEventTest::__pest_evaluable_Event_have_ShouldDispatchAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdatedEventTest::__pest_evaluable_Event_have_ShouldQueueAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdatedEventTest::__pest_evaluable_Event_have_proper_methods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcasts_on_method_return_the_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastQueue_return_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastAs_returns_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdatedEventTest::__pest_evaluable_getWorkOrderDetails_returns_the_given_work_order_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdatedEventTest::__pest_evaluable_getWorkOrderDetails_throws_an_exception_when_work_order_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\ResidentUpdatedTest::__pest_evaluable_ResidentUpdated_event_is_broadcasted_with_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\ResidentUpdatedTest::__pest_evaluable_Implements_the_ShouldBroadcast_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\ResidentUpdatedTest::__pest_evaluable_Implements_the_ShouldDispatchAfterCommit_interface&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\ResidentUpdatedTest::__pest_evaluable_broadcasts_ResidentUpdated_event_with_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\ResidentUpdatedTest::__pest_evaluable_broadcasts_ServiceRequestResidentUpdated_on_correct_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\ResidentUpdatedTest::__pest_evaluable_returns_correct_broadcast_data_from_broadcastWith&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestIssueCreatedListenerTest::__pest_evaluable_Dispatches_CreateServiceRequestActivityLog_event_when_handling_ServiceRequestIssueCreated&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestIssueCreatedListenerTest::__pest_evaluable_Throws_ModelNotFoundException_when_issue_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestWorkOrderCanceledListenerTest::__pest_evaluable_Dispatches_CreateServiceRequestActivityLog_when_handling_the_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestWorkOrderCanceledListenerTest::__pest_evaluable_Throws_an_exception_if_the_work_order_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\IssueStatusModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_include_the_appropriate_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\ProblemDiagnosisModelTest::__pest_evaluable__ProblemDiagnosis_model_has_a_proper_relationship_with__→_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\ProblemDiagnosisModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_include_the_appropriate_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\WorkOrderIssueModelTest::__pest_evaluable__WorkOrderIssue_model_has_a_proper_relationship_with__→_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\WorkOrderIssueModelTest::__pest_evaluable__WorkOrderIssue_model_has_a_proper_relationship_with__→_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\WorkOrderIssueModelTest::__pest_evaluable__WorkOrderIssue_model_has_a_proper_relationship_with__→_workOrder&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\WorkOrderIssueModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_include_the_appropriate_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\WorkOrderIssue\UnassignIssueRequestTest::__pest_evaluable_Passes_validation_with_valid_data#(['fdf53990-112c-3f44-b566-a867b72c4245'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\ServiceRequestDecriptionResourceTest::__pest_evaluable_Service_request_description_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\ServiceRequestResourceTest::__pest_evaluable_Service_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_standard_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\Issue\WorkOrderIssueResourceTest::__pest_evaluable_transforms_the_resource_correctly_for_device_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrder\WorkOrderResourceTest::__pest_evaluable_Work_order_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\AssignedStateTest::__pest_evaluable_has_the_correct_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\AssignedStateTest::__pest_evaluable_has_the_correct_action_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\AssignedStateTest::__pest_evaluable_returns_the_correct_label&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\AssignedStateTest::__pest_evaluable_returns_the_correct_actions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\WorkOrderIssue\AssignedStateTest::__pest_evaluable_has_the_correct_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\WorkOrderIssue\AssignedStateTest::__pest_evaluable_has_the_correct_action_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\WorkOrderIssue\AssignedStateTest::__pest_evaluable_returns_the_correct_label&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\WorkOrderIssue\AssignedStateTest::__pest_evaluable_returns_the_correct_actions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_UserNotFoundException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_WorkOrderNotFoundException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_OrganizationNotFound&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_NotFoundResourceException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_ForbiddenException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_ModelNotFoundException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_UnexpectedValueException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_InvalidArgumentException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_LogicException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_AuthenticationException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_AuthorizationException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_TypeError&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_CouldNotPerformTransition&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_SignedUrlException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_CognitoAPIFailedException&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\ApiExceptionHandlerTest::__pest_evaluable_handles_unknown_exception__default_case_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_Check_if_password_does_not_pass_on_based_on_requirements&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_Password_should_be_invalid_when_character_is_less_than_8&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_Check_if_blank_client__key_is_required&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_Check_if_signedUrl_is_valid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_it_fails_when_signedUrl_is_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordChangeTest::__pest_evaluable_it_fails_when_organization_is_not_found_for_given_client__key&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordResetTest::__pest_evaluable_it_sends_a_password_reset_email_for_a_valid_user_email_in_vendors_portal_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordResetTest::__pest_evaluable_it_sends_a_password_reset_email_for_a_valid_user_email_without_client__key_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordResetTest::__pest_evaluable_it_fails_with_422_for_non_existent_email&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordResetTest::__pest_evaluable_it_fails_with_422_when_email_is_missing&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\PasswordResetTest::__pest_evaluable_it_throws_ModelNotFoundException_for_valid_but_non_existent_email&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\VeriftySignedUrlTest::__pest_evaluable_valid_signed_URL_passed_as_request_input_is_processed_successfully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\VeriftySignedUrlTest::__pest_evaluable_invalid_signature_results_in_unprocessable_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Authentication\VeriftySignedUrlTest::__pest_evaluable_valid_signed_URL_but_expire_link&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\CompleteWOWithNoWorkOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__I_need_to_submit_a_quote_myself___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithAwaitForQuoteApprovalOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Await_for_quote_approval___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithResidentShowUpFeeOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Resident_didn’t_show_up___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Channels\ServiceRequestDatabaseChannelTest::__pest_evaluable_it_builds_the_database_payload_correctly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_cancel_issue_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_cancel_issue_api_#('')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_Handles_UserNotFoundException_correctly_in_asController_method_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_Cannot_cancel_issue_when_state_is_not_unassigned_or_assigned___validated_in_asController_method_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_Handles_exceptions_properly_during_asController_method_execution_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_Cannot_cancel_issue_associated_with_a_scheduled_work_order___validated_in_asController_method_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_Cannot_cancel_issue_not_linked_to_the_requested_service_request___validated_in_asController_method_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_authorize_method_returns_true_when_user_has_permission_to_cancel_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_authorize_method_returns_false_when_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_authorize_method_returns_false_when_user_does_not_have_permission_to_cancel_the_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_getAllAssociatedWorkOrders_returns_collection_of_work_orders_associated_with_the_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_getAllAssociatedWorkOrders_returns_collection_of_work_orders_with_issues_associated_with_the_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_getAllAssociatedWorkOrders_returns_empty_collection_when_no_work_orders_are_associated_with_the_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_getAllAssociatedWorkOrders_returns_empty_collection_when_associated_work_order_issues_are_deleted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_getAllAssociatedWorkOrders_returns_work_order_collection_when_some_associated_work_order_issues_are_deleted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_getWorkOrdersWantToCancel_returns_collection_of_work_orders_with_no_associated_issue_in_assigned_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_getWorkOrdersWantToCancel_returns_empty_collection_when_associated_issue_is_in_assigned_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_getWorkOrdersWantToCancel_returns_work_order_collection_when_a_work_order_has_3_issues_and_none_are_in_assigned_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_getWorkOrdersWantToCancel_returns_empty_collection_when_a_work_order_has_3_issues_and_all_are_in_assigned_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_getWorkOrdersWantToCancel_returns_empty_collection_when_a_work_order_has_3_issues_2_are_canceled_and_1_is_in_assigned_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrder_method_canceled_the_work_order_and_dispatches_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrder_does_not_trigger_event_for_already_canceled_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_updates_all_associated_work_order_issues_to_canceled_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_method_does_not_update_work_order_issues_for_unrelated_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_cancelWorkOrderIssues_does_not_throw_an_error_when_no_work_order_issues_are_associated_with_the_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_successfully_when_no_work_order_issues_are_associated_and_trigger_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\CancelIssueTest::__pest_evaluable_handle_method_cancels_the_issue_associated_work_order_issues_and_work_order_successfully_and_triggers_event_when_work_order_is_in_progress_and_has_one_assigned_issue_in_assigned_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_restore_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_restore_issue_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_restore_issue_api_#('')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_Cannot_restore_issue_when_service_request_is_in_closed_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_Issue_state_other_then_canceled_cannot_be_restored_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_Issue_not_matched_with_requested_service_request_this_throw_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_Exception_during_issue_restore_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_Handle_method_restore_the_issue_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_Handle_method_dispatch_UpdateServiceRequestIssueCanceledActivityLogJob_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_Authorize_method_returns_true_when_user_has_permission_to_restore_the_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_Authorize_method_returns_false_when_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\RestoreIssueTest::__pest_evaluable_Authorize_method_returns_false_when_user_does_not_have_permission_to_restore_the_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachMediaToWorkOrderJobTest::__pest_evaluable_Verify_the_job_implements_ShouldQueue_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachMediaToWorkOrderJobTest::__pest_evaluable_Handles_invalid_url&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachMediaToWorkOrderJobTest::__pest_evaluable_Handles_successful_image_upload&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachMediaToWorkOrderJobTest::__pest_evaluable_Handles_successful_video_upload&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachMediaToWorkOrderJobTest::__pest_evaluable_Handles_empty_response_from_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachMediaToWorkOrderJobTest::__pest_evaluable_Handles_successful_HEIF_image_upload&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Jobs\AttachMediaToWorkOrderJobTest::__pest_evaluable_Handles_failure_of_HEIF_image_upload_due_to_conversion_API_failure&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Profile\ProfileTest::__pest_evaluable_Authenticated_user_can_access_profile_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Profile\ProfileTest::__pest_evaluable_User_with_invalid_token_cannot_access_profile_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Profile\ProfileTest::__pest_evaluable_User_without_token_cannot_access_profile_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\Media\PublicMediaTest::__pest_evaluable_it_handles_exceptions_when_fetching_thumbnail_media&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\Media\PublicMediaTest::__pest_evaluable_it_handles_exceptions_when_fetching_original_media&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\Media\PublicMediaTest::__pest_evaluable_it_generates_a_signed_URL_for_the_thumbnail_media_file&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\Media\PublicMediaTest::__pest_evaluable_it_generates_a_signed_URL_for_the_original_media_file&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\Media\PublicMediaTest::__pest_evaluable_it_handles_exceptions_when_fetching_service_request_thumbnail_media&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\Media\PublicMediaTest::__pest_evaluable_it_handles_exceptions_when_fetching_service_request_original_media&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\Media\PublicMediaTest::__pest_evaluable_it_generates_a_signed_URL_for_the_service_request_thumnail_media_file&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\PublicAPI\Media\PublicMediaTest::__pest_evaluable_it_generates_a_signed_URL_for_the_service_request_original_media_file&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_the_web_contains_proper_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_status__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_status__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_category__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_category__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleShowTest::__pest_evaluable_User_with_a_valid_token_can_access_role_show_API_and_the_return_response_contain_correct_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleShowTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_role_show_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleShowTest::__pest_evaluable_User_without_a_token_cannot_access_role_show_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleShowTest::__pest_evaluable_Unauthorized_organization_cannot_access_role_show_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleShowTest::__pest_evaluable_Unauthorized_user_cannot_access_role_show_API__without_role_show_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleShowTest::__pest_evaluable_The_role_associated_with_one_organization_cannot_be_showed_to_another_organization_s_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\SchedulingControllerTest::__pest_evaluable_Get_work_order_task_scheduling_context&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\SchedulingControllerTest::__pest_evaluable_Get_work_order_ranked_appointments&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\SchedulingControllerTest::__pest_evaluable_Save_a_selected_service_window_as_a_task_appointment&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\SchedulingControllerTest::__pest_evaluable_Do_not_allow_saving_a_selected_service_window_as_a_task_appointment_with_an_invalid_state&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\SchedulingControllerTest::__pest_evaluable_The_scheduling_datetime_suggestions_don_t_include_any_technician_block_out_days&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Scheduling\SchedulingControllerTest::__pest_evaluable_The_technician_without_the_problem_diagnosis_will_not_be_displayed&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_User_with_an_valid_token_can_update_service_request_property_address_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_service_request_property_address_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_service_request_property_address_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_service_request_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_service_request_property_address_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_The_service_request_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_Address_edit_fails_when_the_Property_ID_does_not_match_the_Service_Request_Property_ID_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_Address_edit_fails_when_the_State_ID_invalid_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_Address_edit_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_Authorize_method_returns_false_when_user_does_not_have_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_Authorize_method_returns_false_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_Authorize_method_returns_true_for_a_user_with_the_correct_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PropertyAddressUpdateTest::__pest_evaluable_Exception_during_property_address_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_resident_availability_update_API_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityUpdateTest::__pest_evaluable_The_request_with_an_invalid_service_request_uuid_parameter_shows_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_update_resident_availability_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_update_resident_availability_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityUpdateTest::__pest_evaluable_The_request_with_an_invalid_service_request_status_cannot_access_resident_availability_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityUpdateTest::__pest_evaluable_The_service_request_without_resident_availability_cannot_access_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestListTest::__pest_evaluable_The_service_request_list_API_response_for_the_web_contains_proper_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestListTest::__pest_evaluable_The_service_request_list_API_response_for_web__Filter_by_status__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestListTest::__pest_evaluable_The_service_request_list_API_response_for_web__Filter_by_status__is__not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestListTest::__pest_evaluable_The_service_request_list_API_response_for_web__Filter_by_Imported_From__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestListTest::__pest_evaluable_The_service_request_list_API_response_for_web__Filter_by_Imported_From__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestListTest::__pest_evaluable_The_service_request_list_API_response_for_web__Filter_by_added__date__is_last__month__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ListTagTest::__pest_evaluable_The_tag_list_API_response_for_the_web_contains_proper_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ListTagTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_tag_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ListTagTest::__pest_evaluable_User_without_a_token_cannot_access_tag_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ListTagTest::__pest_evaluable_Unauthorized_organization_cannot_access_tag_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ListTagTest::__pest_evaluable_Unauthorized_user_cannot_access_tag_list_API__without_tag_list_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ListTagTest::__pest_evaluable_User_with__Attach_Detach_Work_Order_Tags__permission_can_view_work_order_list_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\DeleteBlockOutAppointmentTest::__pest_evaluable_The_valid_user_can_delete_block_out_appointment__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\DeleteBlockOutAppointmentTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_delete_block_out_appointment_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\DeleteBlockOutAppointmentTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_delete_block_out_appointment_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\DeleteBlockOutAppointmentTest::__pest_evaluable_The_request_with_an_invalid_technician__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\DeleteBlockOutAppointmentTest::__pest_evaluable_The_request_with_an_invalid_technician__appointment__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\DeleteBlockOutAppointmentTest::__pest_evaluable_Unauthorized_organization_cannot_access_the_delete_block_out_appointment_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\DeleteBlockOutAppointmentTest::__pest_evaluable_Unauthorized_user_cannot_access_the_delete_block_out_appointment_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\DeleteBlockOutAppointmentTest::__pest_evaluable_The_block_out_appointment_associated_with_one_technician_can_t_delete_the_block_out_appointment_of_another_technician_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\DeleteBlockOutAppointmentTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_delete_the_block_out_appointment_of_technician_in_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\DeleteBlockOutAppointmentTest::__pest_evaluable_The_user_can_t_delete_regular_block_out_appointment_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_Checking__Off_Day__calculating_from_technician_working_hours_function_is_working_properly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_The_time_slot_generating_function_working_properly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_The_time_slot_generating_function_is_working_properly_when_skip_days_are_included&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_The_time_slot_generating_function_work_properly_when_working_hours_of_a_technician_is_null&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_Filtering_time_slot_using_appointment_function_works_properly&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_Filtering_time_slot_using_appointment_function_work_properly_case_2__all_day_block_out&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_Filtering_time_slot_using_appointment_function_work_properly_case_3__partial_block_out&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_Filtering_time_slot_using_appointment_function_work_properly_case_4__past_days&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkDateTimeInsideTheWindow__works_properly_for_all_test_case_1__appointment_inside_the_window&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkDateTimeInsideTheWindow__works_properly_for_all_test_case_2__appointment_start_date_same_as_window_start&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkDateTimeInsideTheWindow__works_properly_for_all_test_case_3__appointment_end_date_same_as_window_end&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkDateTimeInsideTheWindow__works_properly_for_all_test_case_4__appointment_and_window_has_same_time&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkDateTimeInsideTheWindow__works_properly_for_all_test_case_5__appointment_start_out_side_the_window_and_end_inside_the_window&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkDateTimeInsideTheWindow__works_properly_for_all_test_case_6__appointment_start_inside_side_the_window_and_end_out_side_the_window&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkDateTimeInsideTheWindow__works_properly_for_all_test_case_7__appointment_start_and_end_out_side_the_window&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkDateTimeInsideTheWindow__works_properly_for_all_test_case_7__appointment_start_and_out_side_the_window_and_end_same_as_window_start&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkDateTimeInsideTheWindow__works_properly_for_all_test_case_9__appointment_end_and_out_side_the_window_and_start_same_as_window_end&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkDateTimeInsideTheWindow__works_properly_for_all_test_case_10__out_side_slot&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkIfItIsPassedWindow__works_properly_for_all_test_case_1__current_date_and_slot_start_date_is_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkIfItIsPassedWindow__works_properly_for_all_test_case_2__start_date_is_passed_one&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__checkIfItIsPassedWindow__works_properly_for_all_test_case_2__start_date_is_a_future_one&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__findWindowStartEndTime__works_properly_for_case_1__morning_afternoon_evening&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__findWindowStartEndTime__works_properly_for_case_2__morning__afternoon_and_evening&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__findWindowStartEndTime__works_properly_for_case_3__morning_and_evening&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__findWindowStartEndTime__works_properly_for_case_4__morning_and_afternoon&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable__findWindowStartEndTime__works_properly_for_case_5__afternoon_and_evening&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_ability_date_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_Unauthorized_organization_cannot_access_availability_date_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_Unauthorized_user_cannot_access_availability_date_API__without_reschedule_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_Filtering_time_slot_using_appointment_function_works_properly_when_any_working_hour_change&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_Filtering_time_slot_using_appointment_function_works_properly_when_two_appointments_are_present_in_a_window&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_The_time_slot_generating_function_works_properly_for_9am___6pm_WH_and_4hrs_durations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_Filtering_time_slot_using_appointment_function_works_properly_for_9am___6pm_WH_and_4hrs_durations&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAvailabilityTest::__pest_evaluable_Filtering_time_slot_using_appointment_function_works_properly_for_9am___6pm_WH__4hrs_durations_and_some_appointments&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserListTest::__pest_evaluable_User_with_a_valid_token_can_access_user_list_API_and_the_return_response_contain_correct_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserListTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_user_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserListTest::__pest_evaluable_User_without_a_token_cannot_access_user_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserListTest::__pest_evaluable_Unauthorized_user_cannot_access_user_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserListTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserListTest::__pest_evaluable_The_user_list_API_response_for_web__Filter_by_status__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserListTest::__pest_evaluable_The_user_list_API_response_for_web__Filter_by_status__is__not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserListTest::__pest_evaluable_The_user_list_API_response_for_web__Filter_by_role__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserListTest::__pest_evaluable_The_user_list_API_response_for_web__Filter_by_role__not__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Can_set_password_successfully_for_third_party_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Non_existent_onboardingId_returns_404&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_set_password&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Set_password_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetPasswordTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_valid_user_can_duplicate_his_own_work_order_view__and_it_will_return_the_proper_keys_after_the_duplication_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_work_order_view_duplicate_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_view_duplicate_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_view_duplicate_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_view_duplicate_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_calendar_view_duplicate_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_Unauthorized_user_cannot_access_view_duplicate_API__without_work_order_duplicate_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_Unauthorized_user_cannot_access_view_duplicate_API__without_user_list_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_Unauthorized_user_cannot_access_view_duplicate_API__without_calendar_duplicate_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_request_with_an_existing_work_order_view_name_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_request_with_an_invalid_view_uuid_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_work_order_view_associated_with_one_organization_cannot_be_duplicated_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_user_view_associated_with_one_organization_cannot_be_duplicated_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_calendar_view_associated_with_one_organization_cannot_be_duplicated_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_work_order_view_associated_with_one_user_cannot_be_duplicated_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_user_view_associated_with_one_user_cannot_be_duplicated_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_calendar_view_associated_with_one_user_cannot_be_duplicated_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DuplicateViewTest::__pest_evaluable_The_request_with_a_predefined_view_name_in_another_view_can_be_recreated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_A_valid_user_can_use_the_set_default_API__and_it_will_return_proper_data_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_view_set_default_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_A_user_without_a_token_cannot_access_view_set_default_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_view_set_default_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_view_set_default_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_calendar_view_set_default_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_Unauthorized_user_cannot_access_view_set_default_API__without_work_order_view_list_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_Unauthorized_user_cannot_access_view_set_default_API__without_user_view_list_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_Unauthorized_user_cannot_access_view_set_default_API__without_calendar_view_list_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_The_request_with_an_invalid_view_uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_The_work_order_view_associated_with_one_user_cannot_be_set_as_default_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_The_user_view_associated_with_one_user_cannot_be_set_as_default_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_The_calendar_view_associated_with_one_user_cannot_be_set_as_default_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_The_work_order_view_associated_with_one_organization_cannot_be_set_as_default_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_The_user_view_associated_with_one_organization_cannot_be_set_as_default_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\SetAsDefaultViewTest::__pest_evaluable_The_calendar_view_associated_with_one_organization_cannot_be_set_as_default_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_The_valid_user_can_cancel_the_work_order__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_work_order_cancel_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_work_order_cancel_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_and_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_cancel_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_cancel_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_The_work_order_associated_with_one_organization_cannot_be_cancelled_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Already_cancelled_or_completed_work_order_cannot_be_cancelled_again&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_cancel_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Cancel_a_Created_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Cancel_a_Paused_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Cancel_a_PaymentPending_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Cancel_a_QualityCheck_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Cancel_a_ReadyToInvoice_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Cancel_a_ReadyToSchedule_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Cancel_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Cancel_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Cancel_a_Scoping_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Cancel_a_TripAvoided_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_Cancel_a_WorkInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_canIssueStatusBeUpdatedToUnassigned_method_returns_true_when_all_work_order_is_canceled&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_canIssueStatusBeUpdatedToUnassigned_method_returns_false_at_least_one_work_order_is_not_canceled&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_canIssueStatusBeUpdatedToUnassigned_method_returns_true_when_the_work_orders_collection_is_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_updateIssueStatus_method_sets_issue_state_to_unassigned_when_work_orders_are_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_updateIssueStatus_method_sets_issue_state_to_unassigned_when_all_work_orders_are_canceled&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_updateIssueStatus_method_does_not_set_issue_state_to_unassigned_when_work_orders_are_not_all_canceled&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_one_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_not_throw_any_error_when_work_order_issue_is_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\CancelTest::__pest_evaluable_cancelWorkOrderIssues_method_cancel_the_work_order_issue_when_work_order_have_many_work_order_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_The_valid_user_can_use_the_partially_paid_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_partially_paid_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_partially_paid_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_The_request_with_an_invalid_invoice__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_and_invoice__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_Unauthorized_organization_cannot_access_the_partially_paid_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_Unauthorized_user_cannot_access_the_partially_paid_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_do_the_partially_paid_action_for_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_The_invoice_not_associated_with_the_given_work_order_shows_an_invalid_argument_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_It_is_possible_to_access_partially_paid_invoice_action_with_work_order_state_ReadyToInvoice_only_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_It_is_not_possible_to_access_partially_paid_invoice_action_with_work_order_state_other_than_ReadyToInvoice_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\PartiallyPaidInvoiceTest::__pest_evaluable_It_is_not_possible_to_access_partially_paid_invoice_action_with_invoice_state_other_than_PaymentPending__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Notification\ReadNotificationTest::__pest_evaluable_User_with_an_valid_token_can_read_notification_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Notification\ReadNotificationTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_read_notification_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Notification\ReadNotificationTest::__pest_evaluable_User_without_a_token_cannot_access_read_notification_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Notification\ReadNotificationTest::__pest_evaluable_The_request_with_an_invalid_notification__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Notification\ReadNotificationTest::__pest_evaluable_User_from_one_organization_cannot_read_notification_created_for_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Notification\ReadNotificationTest::__pest_evaluable_Unauthorized_user_cannot_read_notification_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_User_with_a_valid_token_can_access_the_work_order_pause_API_and_the_return_response_contains_the_correct_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_pause_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_pause_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_The_request_with_an_invalid_work_order_task_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_The_pause_API_can_t_work_with_an_empty_payload_And_returns_a_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_pause_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_pause_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_The_work_order_associated_with_one_organization_are_not_visible_to_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_The_work_order_task_associated_with_one_technician_are_not_visible_to_another_technician_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_Already_paused_work_order_cannot_be_paused_again&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_The_work_orders_not_in_proper_state_can_t_move_to_pause_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_pause_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_Pause_a_Created_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_Pause_a_Trip_Avoidance_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_Pause_a_ReadyToSchedule_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_Pause_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_Pause_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_Pause_a_Scoping_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_Pause_a_WorkInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTest::__pest_evaluable_Pause_a_Quality_check_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_The_valid_user_can_delete_the_work_order_quote_task__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_work_order_quote_task_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_work_order_quote_task_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_quote__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_quote__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid__work__order__task__uuid__quote__uuid_and_quote__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_Unauthorized_user_no_permission_for_quote_update_and_task_delete__cannot_access_the_work_order_quote_task_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_User_with_quote_update_permission_can_delete_quote_task&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_User_with_quote_task_delete_permission_can_delete_quote_task&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_The_work_order_quote_task_associated_with_one_organization_cannot_be_deleted_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_The_work_order_quote_must_have_at_least_one_work_order_quote_task_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_The_user_cannot_delete_quote_task_with_an_invalid_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_The_work_order_quote_with_status_quote__pending__review_can_be_deleted_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_Checking_quote_task_count_before_and_after_deleting_a_quote_task_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\DeleteQuoteTaskTest::__pest_evaluable_User_can_not_delete_another_organization_quote_task&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTest::__pest_evaluable_Update_quote_task_and_its_materials_with_out_adding_any_addition_task_material_s_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTest::__pest_evaluable_Update_quote_task_and_its_materials_with_two_new_additional_task&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTest::__pest_evaluable_Updating_the_quote_task_of_work_order_other_than_pause_state_show_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTest::__pest_evaluable_Updating_the_quote_task_with_giving_invalid_work_order_and_work_order_task&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTest::__pest_evaluable_Updating_the_quote_task_with_giving_invalid_work_order_and_quote&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTest::__pest_evaluable_Update_quote_task_with_empty_payload_show_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTest::__pest_evaluable_Update_quote_task_with_invalid_payload__quote_task_id__show_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTest::__pest_evaluable_Update_quote_task_with_only_new_items&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTest::__pest_evaluable_Update_quote_task_and_its_materials_with_new_additional_task_and_some_item_deleted&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\UpdateQuoteTest::__pest_evaluable_Updating_the_a_quote_is_not_in_pending_to_approve_show_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_an_invalid_token_can_t_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_User_with_out_token_can_t_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Unauthorized_user_cannot_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Unauthorized_organization_cannot_update_state_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_shows_the_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_request_with_an_invalid_work_order_task_uuid_shows_the_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_work_orders_not_in_proper_state_can_t_move_to_resolve&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_resolve_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_PaymentPending_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_QualityCheck_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ResolvedTest::__pest_evaluable_Resolve_a_ReadyToInvoice_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_User_with_a_valid_token_can_stop_trip_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_stop_trip_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_stop_trip_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_stop_trip_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_stop_trip_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_The_request_with_an_invalid_work_order_task_uuid_shows_a_resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_The_work_order_not_associated_with_the_given_task_shows_an_exception_that_has_not_been_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_The_work_order_with_the_WorkInProgress_state_can_be_stopped_trip_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_The_work_order_with_the_ReadyToSchedule_state_can_be_stopped_trip_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_The_work_order_with_the_SchedulingInProgress_state_can_be_stopped_trip_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StopTripTest::__pest_evaluable_The_work_orders_associated_with_one_technician_cannot_be_updated_by_another_technician&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeStoreTest::__pest_evaluable_User_with_an_valid_token_can_add_the_assignee_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeStoreTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_work_order_assignee_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeStoreTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_assignee_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeStoreTest::__pest_evaluable_Unauthorized_organization_cannot_access__work_order_assignee_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeStoreTest::__pest_evaluable_The_request_with_an_invalid_work__order__note__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeStoreTest::__pest_evaluable_The_request_with_an_invalid_user__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeStoreTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_assignee_add_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Assignee\WorkOrderAssigneeStoreTest::__pest_evaluable_The_request_from_another_organizations_user&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderDetailsTest::__pest_evaluable_User_with_a_valid_token_can_access_the_work_order_details_API_and_the_return_response_contains_the_correct_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderDetailsTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_details_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderDetailsTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_details_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderDetailsTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderDetailsTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_details_API_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderDetailsTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_details_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderDetailsTest::__pest_evaluable_The_work_order_associated_with_one_organization_are_not_visible_to_another_organization_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderDetailsTest::__pest_evaluable_The_work_order_with_material_in_payload_return_response_contains_the_correct_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteDeleteTest::__pest_evaluable_The_valid_user_can_delete_the_work_order_note_delete_API__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteDeleteTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_work_order_note_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteDeleteTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_work_order_note_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteDeleteTest::__pest_evaluable_The_request_with_an_invalid_work__order__note__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteDeleteTest::__pest_evaluable_Unauthorized_organization_cannot_access__work_order_note_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteDeleteTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_note_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteDeleteTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_delete_the_work_order_note_data_for_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteStoreTest::__pest_evaluable_The_work_order_notes_create_API_response_with_proper_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteStoreTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_work_order_notes_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteStoreTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_notes_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteStoreTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_notes_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteStoreTest::__pest_evaluable_User_with_empty_payload_cannot_access_work_order_notes_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteStoreTest::__pest_evaluable_User_with_invalid_payload_cannot_access_work_order_notes_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteStoreTest::__pest_evaluable_Another_organization_user_cannot_access_work_order_notes_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteStoreTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_note_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DescriptionUpdateTest::__pest_evaluable_User_with_a_valid_token_can_update_work_order_description_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DescriptionUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_description_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DescriptionUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_description_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DescriptionUpdateTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DescriptionUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DescriptionUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_description_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DescriptionUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_description_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DescriptionUpdateTest::__pest_evaluable_the_work_order_associated_with_one_organization_are_not_update_to_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_update_work_order_priority_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PriorityUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PriorityUpdateTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PriorityUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PriorityUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PriorityUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PriorityUpdateTest::__pest_evaluable_The_request_with_an_invalid_priority_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\PriorityUpdateTest::__pest_evaluable_The_work_order_associated_with_one_organization_are_not_update_to_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderDeleteTest::__pest_evaluable_User_with_a_valid_token_can_delete_the_work_roder_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderDeleteTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_work_order_delete_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderDeleteTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_delete_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderDeleteTest::__pest_evaluable_Unauthorized_organization_cannot_access__work_order_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderDeleteTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderDeleteTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_delete_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_the_web_contains_proper_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_status__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_status__is__not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_priority__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_priority__is__not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_category__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::registerFilterHandler&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::applyEntityIdFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::filterQuery&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::nonExistentFilterHandlerReturnsNull&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::getFilterTypeForField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::resolveWhereClauseOperator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::resolveWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::isDirectFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::isFilterGroup&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::hasFilters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::applySimpleFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::applyDateFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::applyDirectFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::applyFilterGroup&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::findFilterColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::registerDefaultFilterHandlers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::generateQuery&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::getModelForField&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::findWorkOrderFilterColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::findVendorWorkOrderFilterColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::findServiceRequestFilterColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::findUserFilterColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::findQuoteFilterColumn&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::applyUuidFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::getFilterTypeForFieldWithSpecialCases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::resolveWhereClauseOperatorWithSpecialCases&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::resolveWhereClauseOperatorWithInvalidOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::resolveWorkOrderWhereClauseOperator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::resolveVendorWorkOrderWhereClauseOperator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::resolveServiceRequestWhereClauseOperator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::resolveUserWhereClauseOperator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\FilterServiceTest::resolveQuoteWhereClauseOperator&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleteEventTest::__pest_evaluable_Event_has_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleteEventTest::__pest_evaluable_Event_returns_the_correct_broadcast_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleteEventTest::__pest_evaluable_Event_has_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleteEventTest::__pest_evaluable_Event_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleteEventTest::__pest_evaluable_Event_throws_an_exception_if_activity_log_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestNoteUpdatedTest::__pest_evaluable_ServiceRequestNoteUpdated_event_is_broadcasted_with_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestNoteUpdatedTest::__pest_evaluable_ServiceRequestNoteUpdated_event_broadcastQueue_returns_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestNoteUpdatedTest::__pest_evaluable_ServiceRequestNoteUpdated_event_broadcastAs_returns_correct_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestNoteUpdatedTest::__pest_evaluable_ServiceRequestNoteUpdated_event_broadcastOn_returns_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestNoteUpdatedTest::__pest_evaluable_ServiceRequestNoteUpdated_event_broadcastWith_returns_correct_data_for_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestNoteUpdatedTest::__pest_evaluable_ServiceRequestNoteUpdated_event_broadcastWith_returns_correct_data_for_vendor&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestWorkOrderDeletedTest::__pest_evaluable_It_return_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestWorkOrderDeletedTest::__pest_evaluable_It_returns_the_correct_broadcast_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestWorkOrderDeletedTest::__pest_evaluable_It_return_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestWorkOrderDeletedTest::__pest_evaluable_It_returns_the_correct_broadcast_data&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestWorkOrderDeletedTest::__pest_evaluable_Handles_missing_work_order_gracefully&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\ServiceRequestWorkOrderDeletedTest::__pest_evaluable_retrieves_the_correct_work_order_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssignedEventTest::__pest_evaluable_Event_have_ServiceRequestWorkOrderBaseEvent_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssignedEventTest::__pest_evaluable_Event_have_ShouldBroadcast_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssignedEventTest::__pest_evaluable_Event_have_ShouldDispatchAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssignedEventTest::__pest_evaluable_Event_have_ShouldQueueAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssignedEventTest::__pest_evaluable_Event_have_proper_methods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssignedEventTest::__pest_evaluable_broadcasts_on_method_return_the_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastQueue_return_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastAs_returns_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssignedEventTest::__pest_evaluable_getWorkOrderDetails_returns_the_given_work_order_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssignedEventTest::__pest_evaluable_getWorkOrderDetails_throws_an_exception_when_work_order_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssignedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_Event_have_WorkOrderIssueBaseEvent_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_Event_have_ShouldBroadcast_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_Event_have_ShouldDispatchAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_Event_have_ShouldQueueAfterCommit_class&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_Event_have_proper_methods&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcasts_on_method_return_the_correct_channels&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastQueue_return_the_correct_broadcast_queue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastAs_returns_the_correct_broadcast_name&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_getWorkOrderIssueDetails_returns_the_given_work_order_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_getWorkOrderIssueDetails_throws_an_exception_when_work_order_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Events\WorkOrder\Issue\WorkOrderIssueUpdatedEventTest::__pest_evaluable_broadcastWith_return_the_correct_pusher_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Exception\IssueExceptionTest::__pest_evaluable_IssueException__undefinedRouteSlug_returns_the_correct_exception_message&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Exception\IssueExceptionTest::__pest_evaluable_IssueException__actionNotAllowed_returns_the_correct_exception_message&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Exception\IssueExceptionTest::__pest_evaluable_IssueException__notFound_returns_the_correct_exception_message&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Http\Controllers\Developer\HealthCheckControllerTest::__pest_evaluable_basicCheck_returns_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Http\Controllers\Developer\HealthCheckControllerTest::__pest_evaluable_utility_returns_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Job\ServiceRequest\ActivityLog\UpdateServiceRequestIssueCanceledActivityLogJobTest::__pest_evaluable_Updates_activity_logs_and_broadcasts_event&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Job\ServiceRequest\ActivityLog\UpdateServiceRequestIssueCanceledActivityLogJobTest::__pest_evaluable_Does_nothing_if_no_activity_logs_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Job\ServiceRequest\ActivityLog\UpdateServiceRequestIssueCanceledActivityLogJobTest::__pest_evaluable_Updates_activity_logs_not_trigger_event_if_data_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestIssueDeletedListenerTest::__pest_evaluable_Dispatches_CreateServiceRequestActivityLog_event_when_handling_ServiceRequestIssueCreated&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestIssueDeletedListenerTest::__pest_evaluable_Throws_ModelNotFoundException_when_issue_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestPriorityChangedListenerTest::__pest_evaluable_Dispatches_CreateServiceRequestActivityLog_event_when_handling_ServiceRequestPriorityChanged&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Listener\ServiceRequestPriorityChangedListenerTest::__pest_evaluable_Throws_ModelNotFoundException_when_work_order_is_not_found&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\IssueModelTest::__pest_evaluable__Issue_model_has_a_proper_relationship_with__→_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\IssueModelTest::__pest_evaluable__Issue_model_has_a_proper_relationship_with__→_problemDiagnosis&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\IssueModelTest::__pest_evaluable__Issue_model_has_a_proper_relationship_with__→_serviceRequest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\IssueModelTest::__pest_evaluable__Issue_model_has_a_proper_relationship_with__→_workOrderIssues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\IssueModelTest::__pest_evaluable__Issue_model_has_a_proper_relationship_with__→_workOrders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Models\IssueModelTest::__pest_evaluable_The_model_has_fillable_attributes_that_include_the_appropriate_columns&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_The_Validates_the_rules_are_added&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_Passes_validation_with_valid_data#(['Placeat ratione et repellendu…et et.', 'Quo fugit quis facere praesen…re ad.'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_Fails_validation_with_invalid_data#(['', 'Tempora autem aut in quia omn…lorem.'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_Fails_validation_with_invalid_data#(['Dolores tempora quia fugit no…iquam.', ''])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_Fails_validation_with_invalid_data#(['', '', ''])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_Fails_validation_with_invalid_data#([null, 'Laboriosam quam aperiam aut a…i est.'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_Fails_validation_with_invalid_data#(['Aut ut quasi eligendi sequi f…acere.', null])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_Fails_validation_with_invalid_data#(['Non sit provident modi quisqu… quia.', null, null])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_Fails_validation_with_invalid_data#(['Quidem aspernatur accusamus i…animi.', null, '763f5c8c-fbea-3c64-8f88-94a183bbfd2f'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_Fails_validation_with_invalid_data#(['Saepe quia dolores dicta omni…cimus.', null, '2e510080-b8ed-3e07-b2eb-f3e95a7de9ca'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_Fails_validation_with_invalid_data#(['aaaaaaaaaaaaaaaaaaaaaaaaaaaaa…aaaaaa', 'Ex quam magni ut qui maiores …s eum.', '0fefc95a-5be7-3349-a6e7-27f204719209'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Request\Issue\CreateIssueRequestTest::__pest_evaluable_Fails_validation_with_invalid_data#(['Mollitia provident tenetur su…on et.', 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaa…aaaaaa', 'b4b32e57-58c7-3d28-be34-492c0dd451dd'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Resources\WorkOrderIssue\AssignIssueResourceTest::__pest_evaluable_Assign_Issue_to_Existing_WorkOrder_request_response_resource_returns_proper_key_values_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\DoneStateTest::__pest_evaluable_has_the_correct_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\DoneStateTest::__pest_evaluable_has_the_correct_action_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\DoneStateTest::__pest_evaluable_returns_the_correct_label&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\Issue\DoneStateTest::__pest_evaluable_returns_the_correct_actions&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\WorkOrderIssue\DeclainedStateTest::__pest_evaluable_has_the_correct_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\WorkOrderIssue\DeclainedStateTest::__pest_evaluable_has_the_correct_action_name_property&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\State\WorkOrderIssue\DeclainedStateTest::__pest_evaluable_returns_the_correct_label&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'awaiting_availability', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'canceled', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'claim_pending', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'created', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'paused', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'quality_check', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_schedule', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'ready_to_invoice', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'resolved', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduled', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scoping', ['edit', 'assign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'scheduling_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('unassigned', 'work_in_progress', ['edit', 'assign', 'cancel', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'awaiting_availability', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'canceled', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'claim_pending', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'created', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'paused', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'quality_check', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_schedule', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'ready_to_invoice', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'resolved', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduled', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scoping', ['edit', 'unassign', 'delete', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'scheduling_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('assigned', 'work_in_progress', ['edit', 'unassign', 'add_new_work_order', …])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'awaiting_availability', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'canceled', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'claim_pending', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'created', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'paused', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'quality_check', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_schedule', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'ready_to_invoice', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'resolved', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduled', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scoping', ['edit', 'delete', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'scheduling_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('canceled', 'work_in_progress', ['edit', 'restore'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'awaiting_availability', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'canceled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'claim_pending', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'created', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'paused', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'quality_check', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_schedule', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'ready_to_invoice', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'resolved', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduled', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scoping', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'scheduling_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Issue_ability_is_correct_for_issue_status#('done', 'work_in_progress', ['edit'])&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Empty_ability_for_deleted_issue_which_status#('done')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('unassigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('assigned')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('canceled')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Unit\Traits\IssueAbilitiesTest::__pest_evaluable_Only_edit_ability_for_service_request_closed_issue_which_status#('done')&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\PartiallyCompleteWOWithFinishAnotherDayOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_Partially_completed_with_trip__end__with__type_as__Finish_another_day___&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\BasicFlows\WOCompleteWithOtherOptionTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_all_the_necessary_state_until_it_is_No_work_with_trip__end__with__type_as__Other___&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\BasicFlows\WoPauseCancelAndReopenTest::__pest_evaluable_Create_a_new_work_order_and_progress_it_through_pause__cancel_and_reopen_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_a_valid_token_can_access_assign_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_assign_issue_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_assign_issue_api_#('')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Exception_during_issue_assign_to_existing_WO_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Handle_method_assign_an_issue_to_existing_WO_successfully_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Issue_not_matched_with_requested_service_request_this_throw_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Issue_states_not_mached_with_Assigned_and_UnAssigned_this_throw_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Show_validation_error_when_assign_a_same_issue_to_a_existing_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\IssueAssignedToWorkOrderTest::__pest_evaluable_Work_Order_states_not_mached_with_ReadyToSchedule__Canceled_and_AwaitingAvailability_this_throw_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_a_valid_token_can_access_cancel_issue_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_cancel_issue_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_cancel_issue_api_#('')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_Exception_during_issue_cancel_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_Issue_not_matched_with_requested_service_request_this_throw_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_Cannot_undo_issue_when_service_request_is_in_closed_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_Handle_method_undo_the_issue_successfully_when_no_activity_log_entry_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_Handle_method_undo_the_issue_successfully_when_activity_log_entry_exists_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_Authorize_method_returns_true_for_a_user_with_delete_permission_on_the_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_Authorize_method_returns_false_for_a_user_without_delete_permission_on_the_issue_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Issue\UndoIssueTest::__pest_evaluable_Authorize_method_returns_false_when_no_user_is_present_in_the_request_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\LookUp\LookUpControllerApiTest::__pest_evaluable_The_user_with_valid_token_can_access_state_lookup_API_and_it_return_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\LookUp\LookUpControllerApiTest::__pest_evaluable_The_user_with_valid_token_can_access_country_lookup_API_and_it_return_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\LookUp\LookUpControllerApiTest::__pest_evaluable_The_user_with_valid_token_can_access_problem_category_lookup_API_and_it_return_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\LookUp\LookUpControllerApiTest::__pest_evaluable_The_user_with_invalid_token_cannot_access_the_lookup_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\LookUp\LookUpControllerApiTest::__pest_evaluable_The_user_with_valid_token_can_access_technician_lookup_API_and_it_return_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\LookUp\LookUpControllerApiTest::__pest_evaluable_The_user_with_valid_token_can_access_workOrder_assignee_lookup_API_and_it_return_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\LookUp\LookUpControllerApiTest::__pest_evaluable_The_user_with_valid_token_can_access_open_filter_lookup_API_and_it_return_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\LookUp\LookUpControllerApiTest::__pest_evaluable_The_user_with_valid_token_can_access_pause_filter_lookup_API_and_it_return_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\LookUp\LookUpControllerApiTest::__pest_evaluable_The_user_with_valid_token_can_access_close_filter_lookup_API_and_it_return_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Notifcations\NoteCreatedNotificationTest::__pest_evaluable_it_returns_correct_channels_in_via_method&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Notifcations\NoteCreatedNotificationTest::__pest_evaluable_it_returns_correct_databaseType_value&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_view_permission_and_Service_Request_Management_feature_is_pass_the_issue_view_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_view_permission_and_with_out_Service_Request_Management_feature_is_fail_the_issue_view_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_out_ServiceRequestManagement_Issue_view_permission_and_Service_Request_Management_feature_is_fail_the_issue_view_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_create_permission_and_Service_Request_Management_feature_is_pass_the_issue_create_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_create_permission_and_with_out_Service_Request_Management_feature_is_fail_the_issue_create_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_out_ServiceRequestManagement_Issue_create_permission_and_Service_Request_Management_feature_is_fail_the_issue_create_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_edit_permission_and_Service_Request_Management_feature_is_pass_the_issue_edit_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_edit_permission_and_with_out_Service_Request_Management_feature_is_fail_the_issue_edit_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_out_ServiceRequestManagement_Issue_edit_permission_and_Service_Request_Management_feature_is_fail_the_issue_edit_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_edit_permission_and_Service_Request_Management_feature_but_issue_and_user_are_in_different_organization_is_fail_the_issue_edit_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_delete_permission_and_Service_Request_Management_feature_is_pass_the_issue_delete_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_delete_permission_and_with_out_Service_Request_Management_feature_is_fail_the_issue_delete_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_out_ServiceRequestManagement_Issue_delete_permission_and_Service_Request_Management_feature_is_fail_the_issue_delete_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_delete_permission_and_Service_Request_Management_feature_but_issue_and_user_are_in_different_organization_is_fail_the_issue_delete_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_cancel_permission_and_Service_Request_Management_feature_is_pass_the_issue_cancel_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_cancel_permission_and_with_out_Service_Request_Management_feature_is_fail_the_issue_cancel_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_out_ServiceRequestManagement_Issue_cancel_permission_and_Service_Request_Management_feature_is_fail_the_issue_cancel_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Policy\IssuePolicyTest::__pest_evaluable_User_with_ServiceRequestManagement_Issue_cancel_permission_and_Service_Request_Management_feature_but_issue_and_user_are_in_different_organization_is_fail_the_issue_cancel_policy&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\DeleteRoleTest::__pest_evaluable_The_valid_user_can_delete_role__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\DeleteRoleTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_role_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\DeleteRoleTest::__pest_evaluable_User_without_a_token_cannot_access_role_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\DeleteRoleTest::__pest_evaluable_The_role_cannot_be_deleted_as_the_uuid_provided_in_the_request_is_invalid_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\DeleteRoleTest::__pest_evaluable_Unauthorized_organization_cannot_access_role_delete_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\DeleteRoleTest::__pest_evaluable_Unauthorized_user_cannot_access_role_delete_API__without_role_delete_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\DeleteRoleTest::__pest_evaluable_The_request_with_an_already_deleted_role_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\DeleteRoleTest::__pest_evaluable_Trying_to_delete_role_which_is_assigned_to_an_existing_user_shows_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\DeleteRoleTest::__pest_evaluable_The_role_associated_with_one_organization_cannot_be_deleted_by_another_organization_s_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleStoreTest::__pest_evaluable_User_with_a_valid_token_can_create_role_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleStoreTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_role_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleStoreTest::__pest_evaluable_User_without_a_token_cannot_access_role_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleStoreTest::__pest_evaluable_Unauthorized_user_cannot_access_role_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleStoreTest::__pest_evaluable_Unauthorized_organization_cannot_access_role_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleStoreTest::__pest_evaluable_The_request_with_an_empty_payload_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleStoreTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleStoreTest::__pest_evaluable_The_request_with_a_payload_having_repeated_name_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Role\RoleStoreTest::__pest_evaluable_It_is_possible_to_create_a_new_role_with_the_name_of_already_deleted_role_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ActivityLogTest::__pest_evaluable_it_can_fetch_all_service_request_activity_logs&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ActivityLogTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_service_request_activity_log_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ActivityLogTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_service_request_activity_log_list_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ActivityLogTest::__pest_evaluable_The_request_with_an_invalid_service__request__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeDeleteTest::__pest_evaluable_User_with_an_valid_token_can_delete_the_assignee_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeDeleteTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_service_request_assignee_delete_create_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeDeleteTest::__pest_evaluable_User_without_a_token_cannot_access_service_request_assignee_delete_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeDeleteTest::__pest_evaluable_The_request_with_an_invalid_service__request__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeDeleteTest::__pest_evaluable_The_request_with_an_invalid_user__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\AssigneeDeleteTest::__pest_evaluable_The_request_from_another_organizations_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ClosedTest::__pest_evaluable_throws_exception_when_request_user_is_empty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\FilterListTest::__pest_evaluable_A_user_can_get_service_request_filter_values_for_status&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\FilterListTest::__pest_evaluable_A_user_can_get_service_request_filter_values_for_assignee&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\FilterListTest::__pest_evaluable_A_user_can_get_service_request_filter_values_for_imported__from&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\FilterListTest::__pest_evaluable_A_user_can_get_service_request_filter_values_for_added__date&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\FilterListTest::__pest_evaluable_A_user_receives_an_error_when_an_invalid_filter_type_is_provided&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_a_valid_token_can_access_update_priotity_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_update_service_request_api_#('Bearer invalid-token')&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_User_not_found_exception_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Exception_during_service_request_priority_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_when_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Does_not_update_when_priority_is_the_same&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Priority_update_success&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Exception_during_property_address_update_is_handled_correctly_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_true_for_a_user_with_the_correct_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_user_is_not_authenticated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\PriorityUpdateTest::__pest_evaluable_Authorize_method_returns_false_when_user_does_not_have_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_User_with_a_valid_token_can_access_the_request_resident_availability_API_and_the_return_response_contains_the_correct_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_The_user_with_an_invalid_token_cannot_access_the_request_resident_availability_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_request_resident_availability_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_and_work_order_task_uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_Unauthorized_organization_cannot_access_request_resident_availability_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_Unauthorized_user_cannot_access_request_resident_availability_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_The_service_request_associated_with_one_organization_cannot_be_access_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_The_service_request_with_invalid_status_cannot_be_access_other_than_in__progress__ready_to_schedule_and_awaiting_availability_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_The_work_order_with_empty_resident_phone_number_cannot_be_access&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ResidentAvailability\ResidentAvailabilityRequestSmsTest::__pest_evaluable_The_valid_user_can_access_the_request_resident_availability_action__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ServiceRequestShowTest::__pest_evaluable_User_with_a_valid_token_can_view_service_request_details&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\ShowTest::__pest_evaluable_User_with_a_valid_token_can_access_the_service_request_details_api&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Create_a_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Cannot_create_a_work_order_without_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Cannot_create_a_work_order_with_invalid_issues&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Cannot_create_a_work_order_with_issue_that_doesn_t_belong_to_this_service_request&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\ServiceRequest\StoreWorkOrderTest::__pest_evaluable_Cannot_create_a_work_order_with_a_canceled_issue&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ApplyWorkOrderTagTest::__pest_evaluable_The_valid_user_can_apply_the_tag_to_the_work_order_using_a_valid_payload__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ApplyWorkOrderTagTest::__pest_evaluable_The_valid_user_can_remove_the_applied_tags_to_the_work_order_using_a_valid_payload_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ApplyWorkOrderTagTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_apply_tag_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ApplyWorkOrderTagTest::__pest_evaluable_User_without_a_token_cannot_access_apply_tag_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ApplyWorkOrderTagTest::__pest_evaluable_Can_t_apply_tag_using_with_an_empty_payload_And_return_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ApplyWorkOrderTagTest::__pest_evaluable_Unauthorized_organization_cannot_access_apply_tag_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ApplyWorkOrderTagTest::__pest_evaluable_Unauthorized_user_cannot_access_apply_tag_API__without_change_tag_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\ApplyWorkOrderTagTest::__pest_evaluable_Cannot_apply_tag_to_work_order_belonging_to_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\UpdateTagTest::__pest_evaluable_User_with_a_valid_token_can_update_tag_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\UpdateTagTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_tag_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\UpdateTagTest::__pest_evaluable_User_without_a_token_cannot_access_tag_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\UpdateTagTest::__pest_evaluable_The_request_with_an_invalid_tag_uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\UpdateTagTest::__pest_evaluable_Unauthorized_organization_cannot_access_tag_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\UpdateTagTest::__pest_evaluable_Unauthorized_user_cannot_access_tag_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\UpdateTagTest::__pest_evaluable_The_tag_associated_with_one_organization_cannot_be_updated_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\UpdateTagTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Tag\UpdateTagTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ImportTechnicianSkillsTest::__pest_evaluable_The_valid_user_can_import_the_technician_skills__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ImportTechnicianSkillsTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_technician_skills_import_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ImportTechnicianSkillsTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_technician_skills_import_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ImportTechnicianSkillsTest::__pest_evaluable_The_request_with_an_invalid_technician_uu__id_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ImportTechnicianSkillsTest::__pest_evaluable_Unauthorized_organization_cannot_access_technician_skills_import_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ImportTechnicianSkillsTest::__pest_evaluable_Unauthorized_user_cannot_access_technician_skills_import_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\ImportTechnicianSkillsTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_get_the_technician_skills_data_of_another_organization_s_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAppointmentTest::__pest_evaluable_Technician_with_a_valid_token_can_access_schedule_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAppointmentTest::__pest_evaluable_Technician_cannot_schedule_if_he_has_blocked_out_full_day_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAppointmentTest::__pest_evaluable_The_request_with_time_beyond_technician_working_hours_cannot_be_scheduled_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAppointmentTest::__pest_evaluable_If_the_technician_already_has_an_appointment_in_the_time_given_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAppointmentTest::__pest_evaluable_Technician_with_an_invalid_token_cannot_access_schedule_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAppointmentTest::__pest_evaluable_Unauthorized_organization_cannot_access_schedule_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAppointmentTest::__pest_evaluable_Unauthorized_technician_cannot_access_schedule_API__without_reschedule_Trip_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAppointmentTest::__pest_evaluable_Work_order_with_state_other_than_ready__to__schedule_cannot_be_scheduled&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Technician\TechnicianScheduleAppointmentTest::__pest_evaluable_Work_order_service_call_state_other_than_ended_cannot_be_scheduled_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserDetailsTest::__pest_evaluable_User_with_a_valid_token_can_access_user_details_API_and_the_return_response_contain_correct_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserDetailsTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_user_details_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserDetailsTest::__pest_evaluable_User_without_a_token_cannot_access_user_details_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserDetailsTest::__pest_evaluable_Unauthorized_user_cannot_access_user_details_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserDetailsTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_details_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserDetailsTest::__pest_evaluable_The_user_associated_with_one_organization_is_not_visible_to_other_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserDetailsTest::__pest_evaluable_The_request_with_invalid_user__id_shows_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\User\UserDetailsTest::__pest_evaluable_The_technician_type_user_details_response_contain_additional_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\UpdateVendorTest::__pest_evaluable_User_with_a_valid_token_can_access_vendor_update_status_API_and_the_return_response_contain_correct_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\UpdateVendorTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_vendor_update_status_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\UpdateVendorTest::__pest_evaluable_User_with_a_valid_token_with_invalid_vendor_id_cannot_access_vendor_update_status_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\UpdateVendorTest::__pest_evaluable_User_without_a_token_cannot_access_vendor_update_status_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\UpdateVendorTest::__pest_evaluable_Unauthorized_user_cannot_access_vendor_update_status_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\UpdateVendorTest::__pest_evaluable_Unauthorized_organization_cannot_access_vendor_update_status_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_vendor_can_list_work_orders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_unauthorized_user_cannot_list_work_orders&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_vendor_can_filter_work_orders_by_status&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_vendor_can_filter_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_vendor_can_search_work_orders_by_service_category&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Vendor\VendorWorkOrderListTest::__pest_evaluable_vendor_can_search_work_orders_by_work_order_number&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_User_can_generate_signed_URL_for_third_party_vendor_onboarding&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Empty_vendor_ID_should_return_500_when_generating_signed_URL&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Generating_signed_url_with_invalid_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Generating_signed_url_without_token_should_return_unauthorize&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\GenerateSignedUrlTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_acount_user_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Successfully_return_vendor_onboarding_inte_with_valid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Return_404_when_vendor_onboarding_is_not_found_for_invalid_onboardingId&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable_Return_403_error_for_if_signed_url_is_expired&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingInviteTest::__pest_evaluable__Return_400_if_request_exceeded_5&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Can_set_basic_info_successfully_with_valid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_404_with_invalid_onboarding_id&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Throws_transition_exception_when_current_status_is_not_basic_info&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_unauthorize_with_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_unauthorize_without_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_a_third_party_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\VendorOnboarding\VendorOnboardingSetBasicInfoTest::__pest_evaluable_Return_403_on_valid_onboardingId_but_not_the_owner&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_valid_user_can_delete_his_own_view__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_work_order_view_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_view_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_work_order_view_cannot_be_deleted_as_the_uuid_provided_in_the_request_is_invalid_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_view_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_view_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_calendar_view_delete_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_Unauthorized_user_cannot_access_view_delete_API__without_work_order_view_delete_permission__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_Unauthorized_user_cannot_access_view_delete_API__without_user_delete_permission__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_Unauthorized_user_cannot_access_view_delete_API__without_calendar_delete_permission__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_user_can_t_delete_pre_defined_views_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_work_order_view_associated_with_one_user_cannot_be_deleted_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_user_view_associated_with_one_user_cannot_be_deleted_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_calendar_view_associated_with_one_user_cannot_be_deleted_by_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_work_order_view_associated_with_one_organization_cannot_be_deleted_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_user_view_associated_with_one_organization_cannot_be_deleted_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_calendar_view_associated_with_one_organization_cannot_be_deleted_by_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_request_with_deleted_view_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_user_can_delete_the_view_which_is_already_pinned_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_user_can_delete_the_view_which_is_a_default_for_the_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_user_cannot_delete_the_global_view_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\DeleteViewTest::__pest_evaluable_The_valid_user_can_delete_his_own_calendar_view__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\StoreViewTest::__pest_evaluable_The_valid_user_can_create_a_new_work_order_view__and_it_will_return_proper_data_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\StoreViewTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_work_order_view_store_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\StoreViewTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_view_store_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\StoreViewTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_view_store_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\StoreViewTest::__pest_evaluable_Unauthorized_organization_cannot_access_view_store_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\StoreViewTest::__pest_evaluable_The_request_with_an_empty_payload_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\StoreViewTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\StoreViewTest::__pest_evaluable_The_request_with_an_existing_view_name_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\StoreViewTest::__pest_evaluable_The_request_with_a_predefined_view_name_cannot_be_recreated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\StoreViewTest::__pest_evaluable_The_request_with_a_predefined_view_name_in_another_view_can_be_recreated_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\StoreViewTest::__pest_evaluable_The_request_with_an_invalid_view_type_shows_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_The_valid_user_can_view_the_work_order_view_configurations__and_it_contains_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_The_valid_user_can_view_the_user_view_configurations__and_it_contains_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_and_user_view_configurations_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_view_configurations_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_Invalid_view__uuid_can_not_access_the_config_data_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_view_configurations_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_Unauthorized_organization_cannot_access_user_view_configurations_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_Unauthorized_organization_cannot_access_calendar_view_configurations_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_view_configurations_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_Unauthorized_user_cannot_access_user_view_configurations_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_Unauthorized_user_cannot_access_calendar_view_configurations_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_The_work_order_view_associated_with_one_organization_cannot_be_viewed_in_the_configuration_of_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_The_user_view_associated_with_one_user_cannot_be_viewed_in_the_configuration_of_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_The_calendar_view_associated_with_one_user_cannot_be_viewed_in_the_configuration_of_another_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_The_request_without_uuid_return_the_default_view_configurations_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\View\WorkOrderViewConfigTest::__pest_evaluable_The_request_without_uuid_and_view__type_shows_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_Create_invoice_for_all_work_complete_trip&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_Create_invoice_for_quote_trip&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_Update_invoice&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_invoice_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_invoice_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_Unauthorized_organization_cannot_access_the_create_invoice_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_Unauthorized_user_cannot_access_the_invoice_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_The_user_associated_with_one_organization_can_t_do_the_invoice_create_action_for_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_Cannot_create_invoice_for_work_order_with_state_other_than_ready__to__invoice&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_Empty_subsidiaries_for_material_allow_to_create_invoice&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_Empty_subsidiaries_for_any_line_item_other_than_material_show_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\CreateInvoiceTest::__pest_evaluable_Create_invoice_with_invalid_markup__fee__type__value__hour_and_minute_show_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\InvoiceSummaryTest::__pest_evaluable_User_with_a_valid_token_can_access_invoice_summary_API_and_the_return_response_contain_correct_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\InvoiceSummaryTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_invoice_summary_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\InvoiceSummaryTest::__pest_evaluable_User_without_a_token_cannot_access_invoice_summary_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\InvoiceSummaryTest::__pest_evaluable_Unauthorized_organization_cannot_access_invoice_summary_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\InvoiceSummaryTest::__pest_evaluable_Unauthorized_user_cannot_access_invoice_summary_API__without_invoice_create_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\InvoiceSummaryTest::__pest_evaluable_The_work_order_associated_with_one_organization_cannot_be_viewed_by_another_organization_s_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\InvoiceSummaryTest::__pest_evaluable_Check_work_order_summary_return_expected_value&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\InvoiceSummaryTest::__pest_evaluable_The_invoice_work_order_id_does_not_match_with_work_order_id_given_shows_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ShowInvoiceTest::__pest_evaluable_User_with_a_valid_token_can_access_invoice_view_API_and_the_return_response_contain_correct_keys__all_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ShowInvoiceTest::__pest_evaluable_User_with_a_valid_token_can_access_invoice_view_API_and_the_return_response_contain_correct_keys_partially_completed_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ShowInvoiceTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_invoice_view_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ShowInvoiceTest::__pest_evaluable_User_without_a_token_cannot_access_invoice_view_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ShowInvoiceTest::__pest_evaluable_Unauthorized_organization_cannot_access_invoice_view_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ShowInvoiceTest::__pest_evaluable_Unauthorized_user_cannot_access_invoice_view_API__without_invoice_view_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ShowInvoiceTest::__pest_evaluable_The_invoice_associated_with_one_organization_cannot_be_viewed_by_another_organization_s_user_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Invoices\ShowInvoiceTest::__pest_evaluable_The_invoice_work_order_id_does_not_match_with_work_order_id_given__shows_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_User_with_a_valid_token_can_access_the_work_order_pause_API_and_the_return_response_contains_the_correct_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_pause_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_pause_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_The_request_with_an_invalid_work_order_task_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_pause_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_pause_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_The_work_order_task_associated_with_one_organization_are_not_visible_to_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_The_work_order_task_is_not_associated_with_the_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_Cannot_Pause_trip_with_Work_order_state_other_than_work__in__progress&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_Cannot_pause_trip_with_Work_order_service_call_state_other_than_working&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_The_work_order_associated_with_one_technician_are_not_visible_to_another_technician_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\PauseTripTest::__pest_evaluable_User_with_a_valid_token_can_access_the_work_order_pause_API_and_the_return_response_contains_the_correct_keys_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTest::__pest_evaluable_The_user_with_an_invalid_token_can_access_the_work_order_create_quote_api_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTest::__pest_evaluable_The_user_cannot_create_quote_with_invalid_payload_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTest::__pest_evaluable_The_user_can_create_quote_with_media_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTest::__pest_evaluable_The_user_cannot_create_quote_with_an_invalid_media_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTest::__pest_evaluable_The_user_cannot_create_quote_with_an_invalid_state_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTest::__pest_evaluable_Unauthorized_user_cannot_create_quote_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\CreateQuoteTest::__pest_evaluable_The_user_cannot_create_a_quote_if_there_is_already_an_existing_quote_associated_with_the_work_order_task__The_quote_status_is_not_equal_to_rejected_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_Reject_all_quote_task&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_The_work_order_task_associated_with_another_work_order_cannot_be_rejected_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_Cannot_reject_a_quote_having_Work_order_state_other_than_paused&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_Sending_a_quote_not_related_to_the_work_order_is_throw_exception&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_Sending_an_already_rejected_quote_throw_exception&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_The_valid_user_can_reject_all_quote_tasks_using_reject_quote_API__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_A_user_with_an_invalid_token_cannot_access_the_reject_quote_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_A_user_without_a_token_cannot_access_the_reject_quote_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_The_request_with_an_invalid_work__order__task__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_The_request_with_an_invalid_quote__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_The_request_with_an_invalid_work__order__uuid__work__order__task__uuid_and_quote__uuid_shows_the_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_Unauthorized_organization_cannot_access_the_reject_quote_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_Unauthorized_user_cannot_access_the_reject_quote_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_The_quote_associated_with_one_organization_cannot_be_rejected_by_another_organization_s_user&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_The_work_order_task_not_associated_with_the_given_work_order_shows_an_invalid_argument_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_The_quote_not_associated_with_the_given_work_order_shows_an_invalid_argument_exception_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\Quote\RejectQuoteTest::__pest_evaluable_Cannot_reject_a_quote_with_Quote_status_other_than_quote_pending_review&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_User_with_a_valid_token_can_update_state_to_ready_to_schedule&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_User_with_an_invalid_token_can_t_update_state_to_ready_to_schedule&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_User_with_out_token_can_t_update_state_to_ready_to_schedule&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_Unauthorized_user_cannot_update_state_to_ready_to_schedule&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_Unauthorized_organization_cannot_update_state_to_ready_to_schedule&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_shows_the_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_The_request_with_an_invalid_work_order_task_uuid_shows_the_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_The_work_orders_not_in_proper_state_can_t_move_to_ready_to_schedule&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_ready_to_schedule_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_TripAvoidance_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Paused_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_QualityCheck_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scheduled_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_SchedulingInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_Scoping_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\ReadyToScheduleTest::__pest_evaluable_ReadyToSchedule_a_WorkInProgress_State_Work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_User_with_a_valid_token_can_access_the_work_order_start_timer_API_and_the_return_response_contains_the_correct_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_start_timer_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_start_timer_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_The_request_with_an_invalid_work_order_task_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_start_timer_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_start_timer_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_The_work_order_task_associated_with_one_organization_are_not_visible_to_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_The_work_order_task_associated_with_one_technician_are_not_visible_to_another_technician_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_The_user_with_a_valid_token_can_access_the_work_order_start_timer_api_Mobile_Device__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_Cannot_start_timer_for_a_scheduled_state_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_Cannot_start_timer_for_a_paused_state_work_order&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_Cannot_start_timer_for_a_canceled_state_work_order_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_Cannot_start_timer_for_a_scheduled_state_work_order_service_Call_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Actions\StartTimerTest::__pest_evaluable_Cannot_start_timer_for_a_working_state_work_order_service_Call_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\CreateTest::__pest_evaluable_The_valid_user_can_create_a_work_order_using_a_valid_payload__and_it_will_return_the_proper_keys_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\CreateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\CreateTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\CreateTest::__pest_evaluable_The_work_order_can_t_create_using_with_an_invalid_payload_And_return_validation_error&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\CreateTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_create_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\CreateTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_create_API__without_work_order_create_permission_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\CreateTest::__pest_evaluable_The_token_generate_API_return_a_valid_token_for_the_valid_request_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\CreateTest::__pest_evaluable_The_token_generate_API_return_error_token_for_an_invalid_request_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\CreateTest::__pest_evaluable_m2m_user_can_t_create_work_order_with_an_invalid_token&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\CreateTest::__pest_evaluable_m2m_user_can_t_create_work_order_without_scop_workorder_create_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderListViewTest::__pest_evaluable_Work_order_list_API_returns_proper_response&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderListViewTest::__pest_evaluable_The_work_order_list_API_response_for_the_mobile_app__status__open__&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderListViewTest::__pest_evaluable_The_work_order_list_API_response_for_the_mobile_app__status__paused__&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderListViewTest::__pest_evaluable_The_work_order_list_API_response_for_the_mobile_app__status__closed__&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\MobileAppWorkOrderListViewTest::__pest_evaluable_After_technician__A__completes_the_work_order__it_is_re_opened_and_scheduled_to_technician__B___This_WO_not_list_is_Technician__A_s_open_list&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteListTest::__pest_evaluable_The_work_order_notes_list_API_response_with_proper_keys&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteListTest::__pest_evaluable_User_with_a_invalid_token_cannot_access_work_order_notes_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteListTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_notes_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteListTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_notes_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteListTest::__pest_evaluable_User_with_incorrect_work_order_task_id_cannot_access_work_order_notes_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteListTest::__pest_evaluable_Another_organization_user_cannot_access_work_order_notes_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Note\WorkOrderNoteListTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_note_list_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\AccessMethodUpdateTest::__pest_evaluable_User_with_a_valid_token_can_update_work_order_access_method_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\AccessMethodUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_access_method_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\AccessMethodUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_access_method_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\AccessMethodUpdateTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\AccessMethodUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\AccessMethodUpdateTest::__pest_evaluable_The_request_with_an_invalid_property_access_method_enum_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\AccessMethodUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_access_method_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\AccessMethodUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_access_method_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\AccessMethodUpdateTest::__pest_evaluable_The_work_order_associated_with_one_organization_are_not_update_to_another_organization_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateUpdateTest::__pest_evaluable_User_with_a_valid_token_can_update_work_order_due_date_and_return_proper_response&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_due_date_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_due_date_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_due_date_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_due_date_update_API&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;danger&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateUpdateTest::__pest_evaluable_The_request_with_an_invalid_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateUpdateTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\DueDateUpdateTest::__pest_evaluable_The_work_orders_associated_with_one_organization_cannot_be_updated_by_another_organization&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ResidentInfoUpdateTest::__pest_evaluable_User_with_a_valid_token_can_update_work_order_resident_info_and_return_proper_response_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ResidentInfoUpdateTest::__pest_evaluable_User_with_an_invalid_token_cannot_access_work_order_resident_info_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ResidentInfoUpdateTest::__pest_evaluable_User_without_a_token_cannot_access_work_order_resident_info_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ResidentInfoUpdateTest::__pest_evaluable_The_request_with_an_invalid_work_order_uuid_parameter_shows_a_Resource_not_found_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ResidentInfoUpdateTest::__pest_evaluable_The_request_with_an_empty_payload_shows_the_validation_error_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ResidentInfoUpdateTest::__pest_evaluable_Unauthorized_organization_cannot_access_work_order_resident_info_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ResidentInfoUpdateTest::__pest_evaluable_Unauthorized_user_cannot_access_work_order_resident_info_update_API_&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\Update\ResidentInfoUpdateTest::__pest_evaluable_The_work_order_associated_with_one_organization_are_not_update_to_another_organization_&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'sendWorkOrderToVendor'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;string,&nbsp;mixed&gt;&nbsp;&nbsp;$payload</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">handle</span><span class="keyword">(</span><span class="default">WorkOrder</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">WorkOrderTask</span><span class="default">&nbsp;</span><span class="default">$workOrderTask</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">User</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$payload</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">void</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendors</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">Vendor</span><span class="default">::</span><span class="default">whereUuid</span><span class="keyword">(</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'vendor_ids'</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'vendor_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'vendor_uuid'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'service'</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">get</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$vendors</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">ModelNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$organization</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">Organization</span><span class="default">::</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendorSettings'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$vendors</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'organization_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'vendor_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'client_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'client_secret_key'</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereIn</span><span class="keyword">(</span><span class="default">'vendor_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$vendors</span><span class="default">-&gt;</span><span class="default">pluck</span><span class="keyword">(</span><span class="default">'vendor_id'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">toArray</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">findOrFail</span><span class="keyword">(</span><span class="default">$user</span><span class="default">-&gt;</span><span class="default">organization_id</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'organization_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'organization_uuid'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$organization</span><span class="default">-&gt;</span><span class="default">vendorSettings</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="default">ServiceException</span><span class="default">::</span><span class="default">invalidVendor</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$workOrderTask</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">WorkOrderTask</span><span class="default">::</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'latestServiceCalls'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.work_order_service_call_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.created_at'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.state'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.work_order_service_call_number'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.technician_appointment_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.service_notes'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.trip_end_with'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.trip_end_with_type'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.trip_end_with_reason'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'appointment:technician_appointment_id,actual_start_time,actual_elapse_time_in_sec,adjusted_elapse_time_in_sec,actual_end_time,adjusted_travel_time_in_sec,technician_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">lockForUpdate</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'work_order_task_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_order_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'problem_diagnosis_id'</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">where</span><span class="keyword">(</span><span class="default">'work_order_task_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$workOrderTask</span><span class="default">-&gt;</span><span class="default">work_order_task_id</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">firstOrFail</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$workOrderTask</span><span class="default">-&gt;</span><span class="default">latestServiceCalls</span><span class="default">-&gt;</span><span class="default">isNotEmpty</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$latestServiceCall</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$workOrderTask</span><span class="default">-&gt;</span><span class="default">latestServiceCalls</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$latestServiceCall</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$latestServiceCall</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">equals</span><span class="keyword">(</span><span class="default">Ended</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$latestServiceCall</span><span class="default">-&gt;</span><span class="default">technician_appointment_id</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//End&nbsp;that&nbsp;trip</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$latestServiceCall</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">transitionTo</span><span class="keyword">(</span><span class="default">Ended</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$workOrderTask</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">else</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">CouldNotPerformTransition</span><span class="keyword">(</span><span class="default">__</span><span class="keyword">(</span><span class="default">'This&nbsp;transition&nbsp;not&nbsp;allowed&nbsp;to&nbsp;perform&nbsp;with&nbsp;the&nbsp;work&nbsp;order'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$service</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">Service</span><span class="default">::</span><span class="default">THIRD_PARTY_VENDOR</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">count</span><span class="keyword">(</span><span class="default">$vendors</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">1</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$vendors</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">service</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendors</span><span class="default">-&gt;</span><span class="default">first</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">service</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">Service</span><span class="default">::</span><span class="default">LULA</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$service</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">Service</span><span class="default">::</span><span class="default">LULA</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//TODO:&nbsp;Handling&nbsp;generating&nbsp;vendor&nbsp;service,&nbsp;for&nbsp;now&nbsp;making&nbsp;it&nbsp;as&nbsp;lula.</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorService</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">VendorService</span><span class="default">::</span><span class="default">make</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">vendorProvider</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">Service</span><span class="default">::</span><span class="default">getServiceProviderFrom</span><span class="keyword">(</span><span class="default">$service</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">organization</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$organization</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">event</span><span class="keyword">(</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">WorkOrderSendToVendor</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">work_order_id</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$vendors</span><span class="default">-&gt;</span><span class="default">pluck</span><span class="keyword">(</span><span class="default">'vendor_id'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">toArray</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$vendorService</span><span class="default">-&gt;</span><span class="default">sendWorkOrder</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$user</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$payload</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">asController</span><span class="keyword">(</span><span class="default">SendToVendorRequest</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">WorkOrder</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">WorkOrderTask</span><span class="default">&nbsp;</span><span class="default">$workOrderTask</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">JsonResource</span><span class="keyword">|</span><span class="default">JsonResponse</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">beginTransaction</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$user</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UserNotFoundException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">validateWorkOrder</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$workOrderTask</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">work_order_id</span><span class="default">&nbsp;</span><span class="default">!==</span><span class="default">&nbsp;</span><span class="default">$workOrderTask</span><span class="default">-&gt;</span><span class="default">work_order_id</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">InvalidArgumentException</span><span class="keyword">(</span><span class="default">__</span><span class="keyword">(</span><span class="default">'Work&nbsp;order&nbsp;is&nbsp;not&nbsp;matched&nbsp;with&nbsp;the&nbsp;work&nbsp;order&nbsp;task'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">state</span><span class="default">-&gt;</span><span class="default">canTransitionTo</span><span class="keyword">(</span><span class="default">ClaimPending</span><span class="default">::</span><span class="keyword">class</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">CouldNotPerformTransition</span><span class="keyword">(</span><span class="default">__</span><span class="keyword">(</span><span class="default">'This&nbsp;transition&nbsp;not&nbsp;allowed&nbsp;to&nbsp;perform&nbsp;with&nbsp;the&nbsp;work&nbsp;order'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">handle</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$workOrder</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$workOrderTask</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$user</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">all</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">commit</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">load</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'tasks:work_order_task_id,work_order_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'latestInvoices:invoice_id,work_order_id,state,created_at'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'tasks.latestServiceCalls'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.work_order_service_call_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.work_order_service_call_uuid'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.last_modified_at'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.state'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.is_active'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.status'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.work_to_perform'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.created_at'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.lula_appointment_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.vendor_appointment_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.technician_appointment_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.work_order_service_call_number'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.scheduled_start_time'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order_service_calls.scheduled_end_time'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendorAppointment:vendor_appointment_id,vendor_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendorAppointment.vendorAllocations:vendor_allocation_id,vendor_appointment_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'vendorAppointment.vendor:vendor_id,first_name,last_name,vendor_uuid'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'lulaAppointment'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'lula_appointment_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'cancellation_reason'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_order_reference_number'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'service_category_label'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'scheduled_start_time'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'scheduled_end_time'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'estimated_return_start_time'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'estimated_return_end_time'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'paused_reason'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'rescheduled_reason'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'elapse_time_in_sec'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'appointment'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'actual_start_time'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'actual_end_time'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'rescheduled_reason'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'adjusted_elapse_time_in_sec'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'enroute_at'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'adjusted_travel_time_in_sec'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'technician_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'technician_appointment_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">with</span><span class="keyword">(</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'technician:technician_id,technician_uuid,user_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'technician.user:user_id,first_name,middle_name,last_name,profile_pic'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">WorkOrderSendToVendorResource</span><span class="keyword">(</span><span class="default">$workOrder</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">UserNotFoundException</span><span class="keyword">|</span><span class="default">ModelNotFoundException</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollback</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Helper</span><span class="default">::</span><span class="default">exceptionLog</span><span class="keyword">(</span><span class="default">exception</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'Work&nbsp;Order&nbsp;Send&nbsp;To&nbsp;Vendor&nbsp;Failed.'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">notFound</span><span class="keyword">(</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">InvalidArgumentException</span><span class="keyword">|</span><span class="default">CouldNotPerformTransition</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollback</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Helper</span><span class="default">::</span><span class="default">exceptionLog</span><span class="keyword">(</span><span class="default">exception</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'Work&nbsp;Order&nbsp;Send&nbsp;To&nbsp;Vendor&nbsp;Failed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">internalServerError</span><span class="keyword">(</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">WorkOrderSendException</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollback</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Helper</span><span class="default">::</span><span class="default">exceptionLog</span><span class="keyword">(</span><span class="default">exception</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'Work&nbsp;Order&nbsp;Send&nbsp;To&nbsp;Vendor&nbsp;Failed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="213" href="#213">213</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="214" href="#214">214</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">internalServerError</span><span class="keyword">(</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="215" href="#215">215</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Exception</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="216" href="#216">216</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">rollback</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="217" href="#217">217</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Helper</span><span class="default">::</span><span class="default">exceptionLog</span><span class="keyword">(</span><span class="default">exception</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">$exception</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">additionalInfo</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="218" href="#218">218</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'workOrderId'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$workOrder</span><span class="default">-&gt;</span><span class="default">work_order_id</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="219" href="#219">219</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">message</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'Work&nbsp;Order&nbsp;Send&nbsp;To&nbsp;Vendor&nbsp;Failed'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="220" href="#220">220</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="221" href="#221">221</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">Response</span><span class="default">::</span><span class="default">internalServerError</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="222" href="#222">222</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="223" href="#223">223</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="224" href="#224">224</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="225" href="#225">225</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">authorize</span><span class="keyword">(</span><span class="default">ActionRequest</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">bool</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="226" href="#226">226</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="227" href="#227">227</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">user</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">can</span><span class="keyword">(</span><span class="default">'schedule'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$request</span><span class="default">-&gt;</span><span class="default">route</span><span class="keyword">(</span><span class="default">'workOrder'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="228" href="#228">228</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="229" href="#229">229</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.17</a> at Wed Jun 25 16:26:03 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

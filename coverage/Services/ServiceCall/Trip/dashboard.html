<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Services/ServiceCall/Trip</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="../index.html">ServiceCall</a></li>
         <li class="breadcrumb-item"><a href="index.html">Trip</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Domain/Exceptions/TripStateServiceException.php.html#7">App\Services\ServiceCall\Trip\Domain\Exceptions\TripStateServiceException</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EntityResolver.php.html#11">App\Services\ServiceCall\Trip\EntityResolver</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Lula/EnRouteHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Lula\EnRouteHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Technician/EnRouteHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EnRouteHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Technician/EnRoutePausedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EnRoutePausedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Technician/EndedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EndedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Technician/PausedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\PausedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Technician/WorkingHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\WorkingHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Vendor/EnRouteHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Vendor\EnRouteHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Vendor/EnRoutePausedHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Vendor\EnRoutePausedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Vendor/EndedHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Vendor\EndedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Vendor/PausedHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Vendor\PausedHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Vendor/WorkingHandler.php.html#7">App\Services\ServiceCall\Trip\Handlers\Vendor\WorkingHandler</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StateTransitionHandlerFactory.php.html#12">App\Services\ServiceCall\Trip\StateTransitionHandlerFactory</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripStateService.php.html#10">App\Services\ServiceCall\Trip\TripStateService</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripStateServiceProvider.php.html#7">App\Services\ServiceCall\Trip\TripStateServiceProvider</a></td><td class="text-right">50%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="EntityResolver.php.html#11">App\Services\ServiceCall\Trip\EntityResolver</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Handlers/Technician/EnRouteHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EnRouteHandler</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Handlers/Technician/EndedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EndedHandler</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Handlers/Technician/PausedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\PausedHandler</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Handlers/Technician/WorkingHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\WorkingHandler</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="StateTransitionHandlerFactory.php.html#12">App\Services\ServiceCall\Trip\StateTransitionHandlerFactory</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Handlers/Technician/EnRoutePausedHandler.php.html#10">App\Services\ServiceCall\Trip\Handlers\Technician\EnRoutePausedHandler</a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Domain/Exceptions/TripStateServiceException.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Domain\Exceptions\TripStateServiceException::invalidEntityResolver">invalidEntityResolver</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domain/Exceptions/TripStateServiceException.php.html#14"><abbr title="App\Services\ServiceCall\Trip\Domain\Exceptions\TripStateServiceException::invalidStateTransitionArgumentException">invalidStateTransitionArgumentException</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EntityResolver.php.html#20"><abbr title="App\Services\ServiceCall\Trip\EntityResolver::resolve">resolve</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Lula/EnRouteHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Lula\EnRouteHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Technician/EnRouteHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EnRouteHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Technician/EnRoutePausedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EnRoutePausedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Technician/EndedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EndedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Technician/PausedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\PausedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Technician/WorkingHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\WorkingHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Vendor/EnRouteHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Vendor\EnRouteHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Vendor/EnRoutePausedHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Vendor\EnRoutePausedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Vendor/EndedHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Vendor\EndedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Vendor/PausedHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Vendor\PausedHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Handlers/Vendor/WorkingHandler.php.html#9"><abbr title="App\Services\ServiceCall\Trip\Handlers\Vendor\WorkingHandler::apply">apply</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StateTransitionHandlerFactory.php.html#16"><abbr title="App\Services\ServiceCall\Trip\StateTransitionHandlerFactory::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StateTransitionHandlerFactory.php.html#27"><abbr title="App\Services\ServiceCall\Trip\StateTransitionHandlerFactory::make">make</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripStateService.php.html#15"><abbr title="App\Services\ServiceCall\Trip\TripStateService::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripStateService.php.html#24"><abbr title="App\Services\ServiceCall\Trip\TripStateService::updateTripState">updateTripState</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TripStateServiceProvider.php.html#9"><abbr title="App\Services\ServiceCall\Trip\TripStateServiceProvider::register">register</abbr></a></td><td class="text-right">50%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="EntityResolver.php.html#20"><abbr title="App\Services\ServiceCall\Trip\EntityResolver::resolve">resolve</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="Handlers/Technician/EnRouteHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EnRouteHandler::apply">apply</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Handlers/Technician/EndedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EndedHandler::apply">apply</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Handlers/Technician/PausedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\PausedHandler::apply">apply</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Handlers/Technician/WorkingHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\WorkingHandler::apply">apply</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Handlers/Technician/EnRoutePausedHandler.php.html#12"><abbr title="App\Services\ServiceCall\Trip\Handlers\Technician\EnRoutePausedHandler::apply">apply</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StateTransitionHandlerFactory.php.html#27"><abbr title="App\Services\ServiceCall\Trip\StateTransitionHandlerFactory::make">make</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.36</a> at Mon Jun 30 0:32:48 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([15,0,0,0,0,0,1,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([18,0,0,0,0,0,1,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,2,"<a href=\"Domain\/Exceptions\/TripStateServiceException.php.html#7\">App\\Services\\ServiceCall\\Trip\\Domain\\Exceptions\\TripStateServiceException<\/a>"],[0,4,"<a href=\"EntityResolver.php.html#11\">App\\Services\\ServiceCall\\Trip\\EntityResolver<\/a>"],[0,1,"<a href=\"Handlers\/Lula\/EnRouteHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Lula\\EnRouteHandler<\/a>"],[0,3,"<a href=\"Handlers\/Technician\/EnRouteHandler.php.html#10\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EnRouteHandler<\/a>"],[0,2,"<a href=\"Handlers\/Technician\/EnRoutePausedHandler.php.html#10\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EnRoutePausedHandler<\/a>"],[0,3,"<a href=\"Handlers\/Technician\/EndedHandler.php.html#10\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EndedHandler<\/a>"],[0,3,"<a href=\"Handlers\/Technician\/PausedHandler.php.html#10\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\PausedHandler<\/a>"],[0,3,"<a href=\"Handlers\/Technician\/WorkingHandler.php.html#10\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\WorkingHandler<\/a>"],[0,1,"<a href=\"Handlers\/Vendor\/EnRouteHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EnRouteHandler<\/a>"],[0,1,"<a href=\"Handlers\/Vendor\/EnRoutePausedHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EnRoutePausedHandler<\/a>"],[0,1,"<a href=\"Handlers\/Vendor\/EndedHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EndedHandler<\/a>"],[0,1,"<a href=\"Handlers\/Vendor\/PausedHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\PausedHandler<\/a>"],[0,1,"<a href=\"Handlers\/Vendor\/WorkingHandler.php.html#7\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\WorkingHandler<\/a>"],[0,3,"<a href=\"StateTransitionHandlerFactory.php.html#12\">App\\Services\\ServiceCall\\Trip\\StateTransitionHandlerFactory<\/a>"],[0,2,"<a href=\"TripStateService.php.html#10\">App\\Services\\ServiceCall\\Trip\\TripStateService<\/a>"],[50,1,"<a href=\"TripStateServiceProvider.php.html#7\">App\\Services\\ServiceCall\\Trip\\TripStateServiceProvider<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Domain\/Exceptions\/TripStateServiceException.php.html#9\">App\\Services\\ServiceCall\\Trip\\Domain\\Exceptions\\TripStateServiceException::invalidEntityResolver<\/a>"],[0,1,"<a href=\"Domain\/Exceptions\/TripStateServiceException.php.html#14\">App\\Services\\ServiceCall\\Trip\\Domain\\Exceptions\\TripStateServiceException::invalidStateTransitionArgumentException<\/a>"],[0,4,"<a href=\"EntityResolver.php.html#20\">App\\Services\\ServiceCall\\Trip\\EntityResolver::resolve<\/a>"],[0,1,"<a href=\"Handlers\/Lula\/EnRouteHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Lula\\EnRouteHandler::apply<\/a>"],[0,3,"<a href=\"Handlers\/Technician\/EnRouteHandler.php.html#12\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EnRouteHandler::apply<\/a>"],[0,2,"<a href=\"Handlers\/Technician\/EnRoutePausedHandler.php.html#12\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EnRoutePausedHandler::apply<\/a>"],[0,3,"<a href=\"Handlers\/Technician\/EndedHandler.php.html#12\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\EndedHandler::apply<\/a>"],[0,3,"<a href=\"Handlers\/Technician\/PausedHandler.php.html#12\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\PausedHandler::apply<\/a>"],[0,3,"<a href=\"Handlers\/Technician\/WorkingHandler.php.html#12\">App\\Services\\ServiceCall\\Trip\\Handlers\\Technician\\WorkingHandler::apply<\/a>"],[0,1,"<a href=\"Handlers\/Vendor\/EnRouteHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EnRouteHandler::apply<\/a>"],[0,1,"<a href=\"Handlers\/Vendor\/EnRoutePausedHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EnRoutePausedHandler::apply<\/a>"],[0,1,"<a href=\"Handlers\/Vendor\/EndedHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\EndedHandler::apply<\/a>"],[0,1,"<a href=\"Handlers\/Vendor\/PausedHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\PausedHandler::apply<\/a>"],[0,1,"<a href=\"Handlers\/Vendor\/WorkingHandler.php.html#9\">App\\Services\\ServiceCall\\Trip\\Handlers\\Vendor\\WorkingHandler::apply<\/a>"],[0,1,"<a href=\"StateTransitionHandlerFactory.php.html#16\">App\\Services\\ServiceCall\\Trip\\StateTransitionHandlerFactory::__construct<\/a>"],[0,2,"<a href=\"StateTransitionHandlerFactory.php.html#27\">App\\Services\\ServiceCall\\Trip\\StateTransitionHandlerFactory::make<\/a>"],[0,1,"<a href=\"TripStateService.php.html#15\">App\\Services\\ServiceCall\\Trip\\TripStateService::__construct<\/a>"],[0,1,"<a href=\"TripStateService.php.html#24\">App\\Services\\ServiceCall\\Trip\\TripStateService::updateTripState<\/a>"],[50,1,"<a href=\"TripStateServiceProvider.php.html#9\">App\\Services\\ServiceCall\\Trip\\TripStateServiceProvider::register<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

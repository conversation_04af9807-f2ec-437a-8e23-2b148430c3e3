<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/Documents/Code/lula/lula-saas-platform/app/Services/Filters/AssigneeFilterService.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/home/<USER>/Documents/Code/lula/lula-saas-platform/app</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Services</a></li>
         <li class="breadcrumb-item"><a href="index.html">Filters</a></li>
         <li class="breadcrumb-item active">AssigneeFilterService.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="warning">Total</td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="73.12" aria-valuemin="0" aria-valuemax="100" style="width: 73.12%">
           <span class="sr-only">73.12% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">73.12%</div></td>
       <td class="warning small"><div align="right">117&nbsp;/&nbsp;160</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="36.36" aria-valuemin="0" aria-valuemax="100" style="width: 36.36%">
           <span class="sr-only">36.36% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">36.36%</div></td>
       <td class="danger small"><div align="right">4&nbsp;/&nbsp;11</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><abbr title="App\Services\Filters\AssigneeFilterService">AssigneeFilterService</abbr></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="73.12" aria-valuemin="0" aria-valuemax="100" style="width: 73.12%">
           <span class="sr-only">73.12% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">73.12%</div></td>
       <td class="warning small"><div align="right">117&nbsp;/&nbsp;160</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="36.36" aria-valuemin="0" aria-valuemax="100" style="width: 36.36%">
           <span class="sr-only">36.36% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">36.36%</div></td>
       <td class="danger small"><div align="right">4&nbsp;/&nbsp;11</div></td>
       <td class="danger small">61.16</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#54"><abbr title="applyAssigneeFilter(Illuminate\Database\Eloquent\Builder $query, array $payload, string $operator, string $entityType): ?Illuminate\Database\Eloquent\Builder">applyAssigneeFilter</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">16&nbsp;/&nbsp;16</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">7</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#100"><abbr title="applyNotAssignedToFilter(Illuminate\Database\Eloquent\Builder $query, array $userUuids, string $entityType): Illuminate\Database\Eloquent\Builder">applyNotAssignedToFilter</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="90.91" aria-valuemin="0" aria-valuemax="100" style="width: 90.91%">
           <span class="sr-only">90.91% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">90.91%</div></td>
       <td class="success small"><div align="right">30&nbsp;/&nbsp;33</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6.03</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#173"><abbr title="applyAssignedToFilter(Illuminate\Database\Eloquent\Builder $query, array $userUuids, string $entityType, string $whereClause): Illuminate\Database\Eloquent\Builder">applyAssignedToFilter</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="83.33" aria-valuemin="0" aria-valuemax="100" style="width: 83.33%">
           <span class="sr-only">83.33% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">83.33%</div></td>
       <td class="warning small"><div align="right">5&nbsp;/&nbsp;6</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">1.00</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#193"><abbr title="applyUnassignedFilter(Illuminate\Database\Eloquent\Builder $query, string $entityType, string $groupOperation): Illuminate\Database\Eloquent\Builder">applyUnassignedFilter</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="69.57" aria-valuemin="0" aria-valuemax="100" style="width: 69.57%">
           <span class="sr-only">69.57% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">69.57%</div></td>
       <td class="warning small"><div align="right">16&nbsp;/&nbsp;23</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2.11</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#231"><abbr title="applyWorkOrderAssigneeFilter(Illuminate\Database\Eloquent\Builder $query, array $userUuids, string $whereClause): Illuminate\Database\Eloquent\Builder">applyWorkOrderAssigneeFilter</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="87.50" aria-valuemin="0" aria-valuemax="100" style="width: 87.50%">
           <span class="sr-only">87.50% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">87.50%</div></td>
       <td class="warning small"><div align="right">21&nbsp;/&nbsp;24</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">5.05</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="warning">&nbsp;<a href="#288"><abbr title="applyQuoteAssigneeFilter(Illuminate\Database\Eloquent\Builder $query, array $userUuids, string $whereClause): Illuminate\Database\Eloquent\Builder">applyQuoteAssigneeFilter</abbr></a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="87.50" aria-valuemin="0" aria-valuemax="100" style="width: 87.50%">
           <span class="sr-only">87.50% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">87.50%</div></td>
       <td class="warning small"><div align="right">21&nbsp;/&nbsp;24</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">5.05</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#342"><abbr title="applyServiceRequestAssigneeFilter(Illuminate\Database\Eloquent\Builder $query, array $userUuids, string $whereClause): Illuminate\Database\Eloquent\Builder">applyServiceRequestAssigneeFilter</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;23</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">30</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#391"><abbr title="getWorkOrderIdsWithAssignees(array $userIds): array">getWorkOrderIdsWithAssignees</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#404"><abbr title="getQuoteIdsWithAssignees(array $userIds): array">getQuoteIdsWithAssignees</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">4&nbsp;/&nbsp;4</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">1</td>
       <td class="success" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#418"><abbr title="getServiceRequestIdsWithAssignees(array $userIds): array">getServiceRequestIdsWithAssignees</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;3</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="success">&nbsp;<a href="#436"><abbr title="resolveWhereClause(string $groupOperation, string $operator): string">resolveWhereClause</abbr></a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success small">2</td>
       <td class="success" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">App\Services\Filters</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\User</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">App\Models\WorkOrderAssignee</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">DB</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Exception</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Database\Eloquent\Builder</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Illuminate\Database\Eloquent\Model</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Log</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">UnexpectedValueException</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;AssigneeFilterService&nbsp;handles&nbsp;all&nbsp;assignee&nbsp;filtering&nbsp;functionality&nbsp;for&nbsp;the&nbsp;application.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;This&nbsp;service&nbsp;centralizes&nbsp;assignee&nbsp;filtering&nbsp;logic&nbsp;across&nbsp;the&nbsp;application,&nbsp;providing</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;a&nbsp;consistent&nbsp;way&nbsp;to&nbsp;filter&nbsp;data&nbsp;based&nbsp;on&nbsp;assignees.&nbsp;It&nbsp;handles&nbsp;the&nbsp;complexities&nbsp;of</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;filtering&nbsp;by&nbsp;assignees&nbsp;across&nbsp;different&nbsp;entity&nbsp;types&nbsp;(work&nbsp;orders,&nbsp;quotes,&nbsp;service&nbsp;requests).</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;Key&nbsp;features:</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;-&nbsp;Centralized&nbsp;assignee&nbsp;filtering&nbsp;logic</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;-&nbsp;Support&nbsp;for&nbsp;'is'&nbsp;and&nbsp;'is_not'&nbsp;operations</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;-&nbsp;Support&nbsp;for&nbsp;'unassigned'&nbsp;special&nbsp;value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;-&nbsp;Support&nbsp;for&nbsp;multiple&nbsp;entity&nbsp;types&nbsp;(work_order,&nbsp;quote,&nbsp;service_request)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;-&nbsp;Integration&nbsp;with&nbsp;Laravel's&nbsp;query&nbsp;builder</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;Usage:</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;```php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;//&nbsp;In&nbsp;a&nbsp;controller&nbsp;or&nbsp;service</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;$query&nbsp;=&nbsp;WorkOrder::query();</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;$query&nbsp;=&nbsp;$this-&gt;assigneeFilterService-&gt;applyAssigneeFilter($query,&nbsp;$payload,&nbsp;$operator,&nbsp;'work_order');</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;//&nbsp;Or&nbsp;using&nbsp;the&nbsp;Filterable&nbsp;trait&nbsp;on&nbsp;a&nbsp;model</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;$query&nbsp;=&nbsp;WorkOrder::query()-&gt;filterAssignee(['user_uuid1',&nbsp;'user_uuid2'],&nbsp;'is',&nbsp;'work_order');</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*&nbsp;```</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">AssigneeFilterService</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Apply&nbsp;assignee&nbsp;filter&nbsp;to&nbsp;the&nbsp;query</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;This&nbsp;method&nbsp;applies&nbsp;assignee&nbsp;filtering&nbsp;to&nbsp;the&nbsp;query&nbsp;builder&nbsp;based&nbsp;on&nbsp;the&nbsp;provided</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;payload.&nbsp;It&nbsp;handles&nbsp;various&nbsp;operations&nbsp;and&nbsp;supports&nbsp;different&nbsp;entity&nbsp;types.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@template&nbsp;TModel&nbsp;of&nbsp;Model</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;Builder&lt;TModel&gt;&nbsp;&nbsp;$query&nbsp;&nbsp;The&nbsp;query&nbsp;builder&nbsp;to&nbsp;apply&nbsp;the&nbsp;filter&nbsp;to</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array{&quot;group_operation&quot;:&nbsp;string,&nbsp;&quot;column_operation&quot;:&nbsp;string,&nbsp;&quot;field&quot;:&nbsp;string,&nbsp;&quot;column_name&quot;:&nbsp;string,&nbsp;&quot;values&quot;:&nbsp;array&lt;string&gt;}&nbsp;&nbsp;$payload&nbsp;&nbsp;The&nbsp;filter&nbsp;payload</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$operator&nbsp;&nbsp;The&nbsp;resolved&nbsp;operator&nbsp;(uuid,&nbsp;not_uuid)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$entityType&nbsp;&nbsp;The&nbsp;entity&nbsp;type&nbsp;(work_order,&nbsp;quote,&nbsp;service_request)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;Builder&lt;TModel&gt;|null&nbsp;The&nbsp;query&nbsp;builder&nbsp;with&nbsp;the&nbsp;filter&nbsp;applied</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">applyAssigneeFilter</span><span class="keyword">(</span><span class="default">Builder</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$payload</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$operator</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$entityType</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">Builder</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 56" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterIsCalledWithCorrectParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyAssigneeFilter&nbsp;-&nbsp;Payload:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$payload</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 57" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterIsCalledWithCorrectParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyAssigneeFilter&nbsp;-&nbsp;Operator:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$operator</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 58" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterIsCalledWithCorrectParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyAssigneeFilter&nbsp;-&nbsp;Entity&nbsp;Type:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$entityType</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 60" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterIsCalledWithCorrectParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$whereClause</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">resolveWhereClause</span><span class="keyword">(</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'group_operation'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$operator</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 61" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterIsCalledWithCorrectParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyAssigneeFilter&nbsp;-&nbsp;Where&nbsp;Clause:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Handle&nbsp;special&nbsp;case&nbsp;for&nbsp;'unassigned'</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 64" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterIsCalledWithCorrectParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'values'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'values'</span><span class="keyword">]</span><span class="keyword">[</span><span class="default">0</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">'unassigned'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 65" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyAssigneeFilter&nbsp;-&nbsp;Applying&nbsp;unassigned&nbsp;filter'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 67" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">applyUnassignedFilter</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$entityType</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'group_operation'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Special&nbsp;handling&nbsp;for&nbsp;'is_not'&nbsp;operation</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="8 tests cover line 71" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterIsCalledWithCorrectParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'values'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'column_operation'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">'is_not'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 72" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterIsCalledWithCorrectParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyAssigneeFilter&nbsp;-&nbsp;Applying&nbsp;is_not&nbsp;filter&nbsp;with&nbsp;values:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'values'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 74" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterIsCalledWithCorrectParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">applyNotAssignedToFilter</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'values'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$entityType</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Standard&nbsp;handling&nbsp;for&nbsp;'is'&nbsp;operation</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 78" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">!</span><span class="default">&nbsp;</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'values'</span><span class="keyword">]</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="default">&amp;&amp;</span><span class="default">&nbsp;</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'column_operation'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">'is'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 79" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyAssigneeFilter&nbsp;-&nbsp;Applying&nbsp;is&nbsp;filter&nbsp;with&nbsp;values:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'values'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 81" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">applyAssignedToFilter</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$payload</span><span class="keyword">[</span><span class="default">'values'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$entityType</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Default&nbsp;case&nbsp;-&nbsp;just&nbsp;return&nbsp;the&nbsp;query</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 85" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyAssigneeFilter&nbsp;-&nbsp;No&nbsp;filter&nbsp;applied,&nbsp;returning&nbsp;original&nbsp;query'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 87" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Apply&nbsp;filter&nbsp;for&nbsp;entities&nbsp;not&nbsp;assigned&nbsp;to&nbsp;specified&nbsp;users</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@template&nbsp;TModel&nbsp;of&nbsp;Model</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;Builder&lt;TModel&gt;&nbsp;&nbsp;$query&nbsp;&nbsp;The&nbsp;query&nbsp;builder&nbsp;to&nbsp;apply&nbsp;the&nbsp;filter&nbsp;to</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;string&gt;&nbsp;&nbsp;$userUuids&nbsp;&nbsp;The&nbsp;UUIDs&nbsp;of&nbsp;users&nbsp;to&nbsp;exclude</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$entityType&nbsp;&nbsp;The&nbsp;entity&nbsp;type&nbsp;(work_order,&nbsp;quote,&nbsp;service_request)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;Builder&lt;TModel&gt;&nbsp;The&nbsp;query&nbsp;builder&nbsp;with&nbsp;the&nbsp;filter&nbsp;applied</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">applyNotAssignedToFilter</span><span class="keyword">(</span><span class="default">Builder</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$userUuids</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$entityType</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">Builder</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 102" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesInvalidUuids&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesNonExistentUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterCatchesExceptionWhenParsingInvalidUuid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyNotAssignedToFilter&nbsp;-&nbsp;User&nbsp;UUIDs:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$userUuids</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Convert&nbsp;string&nbsp;UUIDs&nbsp;to&nbsp;binary&nbsp;format&nbsp;for&nbsp;database&nbsp;query</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 105" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesInvalidUuids&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesNonExistentUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterCatchesExceptionWhenParsingInvalidUuid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$binaryUuids</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 106" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesInvalidUuids&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesNonExistentUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterCatchesExceptionWhenParsingInvalidUuid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">foreach</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$userUuids</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;process&nbsp;valid&nbsp;UUIDs</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 108" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesInvalidUuids&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesNonExistentUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterCatchesExceptionWhenParsingInvalidUuid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">preg_match</span><span class="keyword">(</span><span class="default">'/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="7 tests cover line 110" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesNonExistentUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$binaryUuids</span><span class="keyword">[</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">\Ramsey\Uuid\Uuid</span><span class="default">::</span><span class="default">fromString</span><span class="keyword">(</span><span class="default">$uuid</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">getBytes</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Exception</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">warning</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyNotAssignedToFilter&nbsp;-&nbsp;Invalid&nbsp;UUID:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'&nbsp;-&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">else</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 115" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesInvalidUuids&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterCatchesExceptionWhenParsingInvalidUuid&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">warning</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyNotAssignedToFilter&nbsp;-&nbsp;Invalid&nbsp;UUID&nbsp;format:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 119" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesInvalidUuids&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesNonExistentUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterCatchesExceptionWhenParsingInvalidUuid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$userIds</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">User</span><span class="default">::</span><span class="default">whereIn</span><span class="keyword">(</span><span class="default">'user_uuid'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$binaryUuids</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">pluck</span><span class="keyword">(</span><span class="default">'user_id'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">toArray</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 120" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesInvalidUuids&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesNonExistentUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterCatchesExceptionWhenParsingInvalidUuid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyNotAssignedToFilter&nbsp;-&nbsp;User&nbsp;IDs:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$userIds</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;If&nbsp;no&nbsp;valid&nbsp;users&nbsp;found,&nbsp;return&nbsp;all&nbsp;entities&nbsp;(since&nbsp;we&nbsp;can't&nbsp;exclude&nbsp;assignments&nbsp;for&nbsp;non-existent&nbsp;users)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="9 tests cover line 123" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesInvalidUuids&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesNonExistentUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterCatchesExceptionWhenParsingInvalidUuid&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$userIds</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 124" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesInvalidUuids&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesNonExistentUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterCatchesExceptionWhenParsingInvalidUuid&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyNotAssignedToFilter&nbsp;-&nbsp;No&nbsp;valid&nbsp;users&nbsp;found,&nbsp;returning&nbsp;all&nbsp;entities'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 126" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesInvalidUuids&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesNonExistentUsers&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterCatchesExceptionWhenParsingInvalidUuid&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Get&nbsp;entity&nbsp;IDs&nbsp;that&nbsp;have&nbsp;the&nbsp;specified&nbsp;assignees</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 130" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$entityIdsWithAssignees</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">match</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$entityType</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 131" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getWorkOrderIdsWithAssignees</span><span class="keyword">(</span><span class="default">$userIds</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 132" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'quote'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getQuoteIdsWithAssignees</span><span class="keyword">(</span><span class="default">$userIds</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 133" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'service_request'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getServiceRequestIdsWithAssignees</span><span class="keyword">(</span><span class="default">$userIds</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 134" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">default</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UnexpectedValueException</span><span class="keyword">(</span><span class="string">&quot;</span><span class="string">Unexpected&nbsp;entity&nbsp;type&nbsp;[</span><span class="string">{</span><span class="string">$entityType</span><span class="keyword">}</span><span class="string">]&nbsp;for&nbsp;assignee&nbsp;filter.</span><span class="string">&quot;</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="6 tests cover line 135" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 136" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyNotAssignedToFilter&nbsp;-&nbsp;Entity&nbsp;IDs&nbsp;with&nbsp;assignees:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$entityIdsWithAssignees</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Return&nbsp;entities&nbsp;that&nbsp;don't&nbsp;have&nbsp;the&nbsp;specified&nbsp;assignees</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 139" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$idColumn</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">match</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$entityType</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 140" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'work_orders.work_order_id'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 141" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'quote'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'quotes.quote_id'</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 142" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'service_request'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'service_requests.service_request_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">default</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UnexpectedValueException</span><span class="keyword">(</span><span class="string">&quot;</span><span class="string">Unexpected&nbsp;entity&nbsp;type&nbsp;[</span><span class="string">{</span><span class="string">$entityType</span><span class="keyword">}</span><span class="string">]&nbsp;for&nbsp;assignee&nbsp;filter.</span><span class="string">&quot;</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 144" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Enable&nbsp;query&nbsp;logging</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 147" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">enableQueryLog</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;If&nbsp;no&nbsp;entities&nbsp;have&nbsp;the&nbsp;specified&nbsp;assignees,&nbsp;return&nbsp;all&nbsp;entities</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 150" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">empty</span><span class="keyword">(</span><span class="default">$entityIdsWithAssignees</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 151" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyNotAssignedToFilter&nbsp;-&nbsp;No&nbsp;entities&nbsp;found&nbsp;with&nbsp;specified&nbsp;assignees,&nbsp;returning&nbsp;all&nbsp;entities'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="2 tests cover line 152" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$result</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">else</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 154" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$result</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">whereNotIn</span><span class="keyword">(</span><span class="default">$idColumn</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$entityIdsWithAssignees</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 157" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyNotAssignedToFilter&nbsp;-&nbsp;SQL:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">DB</span><span class="default">::</span><span class="default">getQueryLog</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 159" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesEmptyEntityIds&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesDifferentEntityTypes&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterHandlesWhereNotInClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$result</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Apply&nbsp;filter&nbsp;for&nbsp;entities&nbsp;assigned&nbsp;to&nbsp;specified&nbsp;users</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@template&nbsp;TModel&nbsp;of&nbsp;Model</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;Builder&lt;TModel&gt;&nbsp;&nbsp;$query&nbsp;&nbsp;The&nbsp;query&nbsp;builder&nbsp;to&nbsp;apply&nbsp;the&nbsp;filter&nbsp;to</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;string&gt;&nbsp;&nbsp;$userUuids&nbsp;&nbsp;The&nbsp;UUIDs&nbsp;of&nbsp;users&nbsp;to&nbsp;include</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$entityType&nbsp;&nbsp;The&nbsp;entity&nbsp;type&nbsp;(work_order,&nbsp;quote,&nbsp;service_request)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$whereClause&nbsp;&nbsp;The&nbsp;where&nbsp;clause&nbsp;to&nbsp;use&nbsp;(where,&nbsp;orWhere)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;Builder&lt;TModel&gt;&nbsp;The&nbsp;query&nbsp;builder&nbsp;with&nbsp;the&nbsp;filter&nbsp;applied</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">applyAssignedToFilter</span><span class="keyword">(</span><span class="default">Builder</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$userUuids</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$entityType</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">Builder</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 175" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">match</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$entityType</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 176" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">applyWorkOrderAssigneeFilter</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$userUuids</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 177" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'quote'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">applyQuoteAssigneeFilter</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$userUuids</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'service_request'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">applyServiceRequestAssigneeFilter</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$userUuids</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 179" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">default</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UnexpectedValueException</span><span class="keyword">(</span><span class="string">&quot;</span><span class="string">Unexpected&nbsp;entity&nbsp;type&nbsp;[</span><span class="string">{</span><span class="string">$entityType</span><span class="keyword">}</span><span class="string">]&nbsp;for&nbsp;assignee&nbsp;filter.</span><span class="string">&quot;</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="5 tests cover line 180" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Apply&nbsp;filter&nbsp;for&nbsp;unassigned&nbsp;entities</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@template&nbsp;TModel&nbsp;of&nbsp;Model</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;Builder&lt;TModel&gt;&nbsp;&nbsp;$query&nbsp;&nbsp;The&nbsp;query&nbsp;builder&nbsp;to&nbsp;apply&nbsp;the&nbsp;filter&nbsp;to</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$entityType&nbsp;&nbsp;The&nbsp;entity&nbsp;type&nbsp;(work_order,&nbsp;quote,&nbsp;service_request)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$groupOperation&nbsp;&nbsp;The&nbsp;group&nbsp;operation&nbsp;(and,&nbsp;or)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;Builder&lt;TModel&gt;&nbsp;The&nbsp;query&nbsp;builder&nbsp;with&nbsp;the&nbsp;filter&nbsp;applied</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">applyUnassignedFilter</span><span class="keyword">(</span><span class="default">Builder</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$entityType</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$groupOperation</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">Builder</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 195" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyUnassignedFilterHandlesServiceRequestEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$whereClause</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$groupOperation</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">'and'</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'whereNotExists'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'orWhereNotExists'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 197" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyUnassignedFilterHandlesServiceRequestEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">match</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$entityType</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 198" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'work_order'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">$whereClause</span><span class="keyword">(</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 199" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'work_order_assignee_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 200" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">from</span><span class="keyword">(</span><span class="default">'work_order_assignees'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 201" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereColumn</span><span class="keyword">(</span><span class="default">'work_order_assignees.work_order_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_orders.work_order_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 202" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereNull</span><span class="keyword">(</span><span class="default">'work_order_assignees.deleted_at'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 203" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'quote'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">$whereClause</span><span class="keyword">(</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'work_order_assignee_id'</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">from</span><span class="keyword">(</span><span class="default">'work_order_assignees'</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">join</span><span class="keyword">(</span><span class="default">'quotes'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'quotes.work_order_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'='</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_order_assignees.work_order_id'</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereColumn</span><span class="keyword">(</span><span class="default">'quotes.quote_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'quotes.quote_id'</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereNull</span><span class="keyword">(</span><span class="default">'work_order_assignees.deleted_at'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 211" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyUnassignedFilterHandlesServiceRequestEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'service_request'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">$whereClause</span><span class="keyword">(</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 212" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyUnassignedFilterHandlesServiceRequestEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'service_request_assignee_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 213" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyUnassignedFilterHandlesServiceRequestEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="213" href="#213">213</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">from</span><span class="keyword">(</span><span class="default">'service_request_assignees'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 214" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyUnassignedFilterHandlesServiceRequestEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="214" href="#214">214</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereColumn</span><span class="keyword">(</span><span class="default">'service_request_assignees.service_request_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'service_requests.service_request_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 215" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyUnassignedFilterHandlesServiceRequestEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="215" href="#215">215</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereNull</span><span class="keyword">(</span><span class="default">'service_request_assignees.deleted_at'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 216" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyUnassignedFilterHandlesServiceRequestEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="216" href="#216">216</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 217" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyUnassignedFilterHandlesServiceRequestEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="217" href="#217">217</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">default</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">throw</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">UnexpectedValueException</span><span class="keyword">(</span><span class="string">&quot;</span><span class="string">Unexpected&nbsp;entity&nbsp;type&nbsp;[</span><span class="string">{</span><span class="string">$entityType</span><span class="keyword">}</span><span class="string">]&nbsp;for&nbsp;assignee&nbsp;filter.</span><span class="string">&quot;</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 218" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyUnassignedFilterHandlesServiceRequestEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="218" href="#218">218</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="219" href="#219">219</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="220" href="#220">220</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="221" href="#221">221</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="222" href="#222">222</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Apply&nbsp;assignee&nbsp;filter&nbsp;for&nbsp;work&nbsp;orders</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="223" href="#223">223</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="224" href="#224">224</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@template&nbsp;TModel&nbsp;of&nbsp;Model</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="225" href="#225">225</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="226" href="#226">226</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;Builder&lt;TModel&gt;&nbsp;&nbsp;$query&nbsp;&nbsp;The&nbsp;query&nbsp;builder&nbsp;to&nbsp;apply&nbsp;the&nbsp;filter&nbsp;to</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="227" href="#227">227</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;string&gt;&nbsp;&nbsp;$userUuids&nbsp;&nbsp;The&nbsp;UUIDs&nbsp;of&nbsp;users&nbsp;to&nbsp;include</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="228" href="#228">228</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$whereClause&nbsp;&nbsp;The&nbsp;where&nbsp;clause&nbsp;to&nbsp;use&nbsp;(where,&nbsp;orWhere)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="229" href="#229">229</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;Builder&lt;TModel&gt;&nbsp;The&nbsp;query&nbsp;builder&nbsp;with&nbsp;the&nbsp;filter&nbsp;applied</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="230" href="#230">230</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="231" href="#231">231</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">applyWorkOrderAssigneeFilter</span><span class="keyword">(</span><span class="default">Builder</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$userUuids</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">Builder</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="232" href="#232">232</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 233" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="233" href="#233">233</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyWorkOrderAssigneeFilter&nbsp;-&nbsp;User&nbsp;UUIDs:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$userUuids</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 234" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="234" href="#234">234</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyWorkOrderAssigneeFilter&nbsp;-&nbsp;Where&nbsp;Clause:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="235" href="#235">235</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="236" href="#236">236</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Get&nbsp;user&nbsp;IDs&nbsp;from&nbsp;UUIDs</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="237" href="#237">237</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Convert&nbsp;string&nbsp;UUIDs&nbsp;to&nbsp;binary&nbsp;format&nbsp;for&nbsp;database&nbsp;query</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 238" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="238" href="#238">238</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$binaryUuids</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 239" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="239" href="#239">239</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">foreach</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$userUuids</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="240" href="#240">240</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;process&nbsp;valid&nbsp;UUIDs</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 241" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="241" href="#241">241</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">preg_match</span><span class="keyword">(</span><span class="default">'/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="242" href="#242">242</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 243" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="243" href="#243">243</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$binaryUuids</span><span class="keyword">[</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">\Ramsey\Uuid\Uuid</span><span class="default">::</span><span class="default">fromString</span><span class="keyword">(</span><span class="default">$uuid</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">getBytes</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="244" href="#244">244</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Exception</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="245" href="#245">245</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">warning</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyWorkOrderAssigneeFilter&nbsp;-&nbsp;Invalid&nbsp;UUID:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'&nbsp;-&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="246" href="#246">246</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="247" href="#247">247</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">else</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="248" href="#248">248</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">warning</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyWorkOrderAssigneeFilter&nbsp;-&nbsp;Invalid&nbsp;UUID&nbsp;format:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="249" href="#249">249</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="250" href="#250">250</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="251" href="#251">251</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 252" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="252" href="#252">252</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyWorkOrderAssigneeFilter&nbsp;-&nbsp;Binary&nbsp;UUIDs&nbsp;created'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="253" href="#253">253</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 254" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="254" href="#254">254</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$userIds</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">User</span><span class="default">::</span><span class="default">whereIn</span><span class="keyword">(</span><span class="default">'user_uuid'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$binaryUuids</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">pluck</span><span class="keyword">(</span><span class="default">'user_id'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">toArray</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 255" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="255" href="#255">255</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyWorkOrderAssigneeFilter&nbsp;-&nbsp;User&nbsp;IDs:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$userIds</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="256" href="#256">256</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="257" href="#257">257</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Enable&nbsp;query&nbsp;logging</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 258" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="258" href="#258">258</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">enableQueryLog</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="259" href="#259">259</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="260" href="#260">260</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Determine&nbsp;the&nbsp;correct&nbsp;exists&nbsp;method&nbsp;based&nbsp;on&nbsp;the&nbsp;where&nbsp;clause</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 261" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="261" href="#261">261</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$existsMethod</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">str_contains</span><span class="keyword">(</span><span class="default">$whereClause</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'orWhere'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'orWhereExists'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'whereExists'</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 262" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="262" href="#262">262</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyWorkOrderAssigneeFilter&nbsp;-&nbsp;Using&nbsp;exists&nbsp;method:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$existsMethod</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="263" href="#263">263</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 264" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="264" href="#264">264</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$result</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">$existsMethod</span><span class="keyword">(</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$userIds</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 265" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="265" href="#265">265</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'work_order_assignee_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 266" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="266" href="#266">266</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">from</span><span class="keyword">(</span><span class="default">'work_order_assignees'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 267" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="267" href="#267">267</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereColumn</span><span class="keyword">(</span><span class="default">'work_order_assignees.work_order_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_orders.work_order_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 268" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="268" href="#268">268</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereIn</span><span class="keyword">(</span><span class="default">'work_order_assignees.user_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$userIds</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 269" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="269" href="#269">269</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereNull</span><span class="keyword">(</span><span class="default">'work_order_assignees.deleted_at'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 270" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="270" href="#270">270</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="271" href="#271">271</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="272" href="#272">272</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Log&nbsp;the&nbsp;generated&nbsp;SQL&nbsp;query</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 273" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="273" href="#273">273</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyWorkOrderAssigneeFilter&nbsp;-&nbsp;SQL:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">DB</span><span class="default">::</span><span class="default">getQueryLog</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="274" href="#274">274</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="4 tests cover line 275" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesOrOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssignedToFilterHandlesAndOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="275" href="#275">275</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$result</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="276" href="#276">276</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="277" href="#277">277</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="278" href="#278">278</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="279" href="#279">279</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Apply&nbsp;assignee&nbsp;filter&nbsp;for&nbsp;quotes</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="280" href="#280">280</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="281" href="#281">281</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@template&nbsp;TModel&nbsp;of&nbsp;Model</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="282" href="#282">282</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="283" href="#283">283</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;Builder&lt;TModel&gt;&nbsp;&nbsp;$query&nbsp;&nbsp;The&nbsp;query&nbsp;builder&nbsp;to&nbsp;apply&nbsp;the&nbsp;filter&nbsp;to</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="284" href="#284">284</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;string&gt;&nbsp;&nbsp;$userUuids&nbsp;&nbsp;The&nbsp;UUIDs&nbsp;of&nbsp;users&nbsp;to&nbsp;include</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="285" href="#285">285</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$whereClause&nbsp;&nbsp;The&nbsp;where&nbsp;clause&nbsp;to&nbsp;use&nbsp;(where,&nbsp;orWhere)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="286" href="#286">286</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;Builder&lt;TModel&gt;&nbsp;The&nbsp;query&nbsp;builder&nbsp;with&nbsp;the&nbsp;filter&nbsp;applied</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="287" href="#287">287</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="288" href="#288">288</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">applyQuoteAssigneeFilter</span><span class="keyword">(</span><span class="default">Builder</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$userUuids</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">Builder</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="289" href="#289">289</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 290" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="290" href="#290">290</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyQuoteAssigneeFilter&nbsp;-&nbsp;User&nbsp;UUIDs:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$userUuids</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 291" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="291" href="#291">291</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyQuoteAssigneeFilter&nbsp;-&nbsp;Where&nbsp;Clause:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="292" href="#292">292</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="293" href="#293">293</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Convert&nbsp;string&nbsp;UUIDs&nbsp;to&nbsp;binary&nbsp;format&nbsp;for&nbsp;database&nbsp;query</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 294" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="294" href="#294">294</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$binaryUuids</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 295" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="295" href="#295">295</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">foreach</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$userUuids</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="296" href="#296">296</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;process&nbsp;valid&nbsp;UUIDs</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 297" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="297" href="#297">297</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">preg_match</span><span class="keyword">(</span><span class="default">'/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="298" href="#298">298</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 299" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="299" href="#299">299</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$binaryUuids</span><span class="keyword">[</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">\Ramsey\Uuid\Uuid</span><span class="default">::</span><span class="default">fromString</span><span class="keyword">(</span><span class="default">$uuid</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">getBytes</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="300" href="#300">300</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Exception</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="301" href="#301">301</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">warning</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyQuoteAssigneeFilter&nbsp;-&nbsp;Invalid&nbsp;UUID:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'&nbsp;-&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="302" href="#302">302</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="303" href="#303">303</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">else</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="304" href="#304">304</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">warning</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyQuoteAssigneeFilter&nbsp;-&nbsp;Invalid&nbsp;UUID&nbsp;format:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="305" href="#305">305</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="306" href="#306">306</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="307" href="#307">307</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 308" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="308" href="#308">308</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$userIds</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">User</span><span class="default">::</span><span class="default">whereIn</span><span class="keyword">(</span><span class="default">'user_uuid'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$binaryUuids</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">pluck</span><span class="keyword">(</span><span class="default">'user_id'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">toArray</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 309" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="309" href="#309">309</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyQuoteAssigneeFilter&nbsp;-&nbsp;User&nbsp;IDs:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$userIds</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="310" href="#310">310</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="311" href="#311">311</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Enable&nbsp;query&nbsp;logging</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 312" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="312" href="#312">312</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">enableQueryLog</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="313" href="#313">313</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="314" href="#314">314</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Determine&nbsp;the&nbsp;correct&nbsp;exists&nbsp;method&nbsp;based&nbsp;on&nbsp;the&nbsp;where&nbsp;clause</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 315" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="315" href="#315">315</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$existsMethod</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">str_contains</span><span class="keyword">(</span><span class="default">$whereClause</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'orWhere'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'orWhereExists'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'whereExists'</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 316" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="316" href="#316">316</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyQuoteAssigneeFilter&nbsp;-&nbsp;Using&nbsp;exists&nbsp;method:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$existsMethod</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="317" href="#317">317</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 318" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="318" href="#318">318</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$result</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">$existsMethod</span><span class="keyword">(</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$userIds</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 319" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="319" href="#319">319</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'work_order_assignee_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 320" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="320" href="#320">320</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">from</span><span class="keyword">(</span><span class="default">'work_order_assignees'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 321" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="321" href="#321">321</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">join</span><span class="keyword">(</span><span class="default">'quotes'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'quotes.work_order_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'='</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_order_assignees.work_order_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 322" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="322" href="#322">322</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereColumn</span><span class="keyword">(</span><span class="default">'quotes.quote_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'quotes.quote_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 323" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="323" href="#323">323</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereIn</span><span class="keyword">(</span><span class="default">'work_order_assignees.user_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$userIds</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 324" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="324" href="#324">324</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereNull</span><span class="keyword">(</span><span class="default">'work_order_assignees.deleted_at'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 325" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="325" href="#325">325</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="326" href="#326">326</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 327" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="327" href="#327">327</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyQuoteAssigneeFilter&nbsp;-&nbsp;SQL:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">DB</span><span class="default">::</span><span class="default">getQueryLog</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="328" href="#328">328</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="3 tests cover line 329" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterAddsCorrectWhereClause&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyQuoteAssigneeFilterBasicTest&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="329" href="#329">329</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$result</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="330" href="#330">330</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="331" href="#331">331</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="332" href="#332">332</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="333" href="#333">333</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Apply&nbsp;assignee&nbsp;filter&nbsp;for&nbsp;service&nbsp;requests</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="334" href="#334">334</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="335" href="#335">335</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@template&nbsp;TModel&nbsp;of&nbsp;Model</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="336" href="#336">336</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="337" href="#337">337</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;Builder&lt;TModel&gt;&nbsp;&nbsp;$query&nbsp;&nbsp;The&nbsp;query&nbsp;builder&nbsp;to&nbsp;apply&nbsp;the&nbsp;filter&nbsp;to</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="338" href="#338">338</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;string&gt;&nbsp;&nbsp;$userUuids&nbsp;&nbsp;The&nbsp;UUIDs&nbsp;of&nbsp;users&nbsp;to&nbsp;include</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="339" href="#339">339</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$whereClause&nbsp;&nbsp;The&nbsp;where&nbsp;clause&nbsp;to&nbsp;use&nbsp;(where,&nbsp;orWhere)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="340" href="#340">340</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;Builder&lt;TModel&gt;&nbsp;The&nbsp;query&nbsp;builder&nbsp;with&nbsp;the&nbsp;filter&nbsp;applied</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="341" href="#341">341</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="342" href="#342">342</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">applyServiceRequestAssigneeFilter</span><span class="keyword">(</span><span class="default">Builder</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$userUuids</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">Builder</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="343" href="#343">343</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="344" href="#344">344</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyServiceRequestAssigneeFilter&nbsp;-&nbsp;User&nbsp;UUIDs:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$userUuids</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="345" href="#345">345</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyServiceRequestAssigneeFilter&nbsp;-&nbsp;Where&nbsp;Clause:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$whereClause</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="346" href="#346">346</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="347" href="#347">347</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Convert&nbsp;string&nbsp;UUIDs&nbsp;to&nbsp;binary&nbsp;format&nbsp;for&nbsp;database&nbsp;query</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="348" href="#348">348</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$binaryUuids</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="349" href="#349">349</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">foreach</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$userUuids</span><span class="default">&nbsp;</span><span class="keyword">as</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="350" href="#350">350</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Only&nbsp;process&nbsp;valid&nbsp;UUIDs</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="351" href="#351">351</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">preg_match</span><span class="keyword">(</span><span class="default">'/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="352" href="#352">352</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">try</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="353" href="#353">353</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$binaryUuids</span><span class="keyword">[</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">\Ramsey\Uuid\Uuid</span><span class="default">::</span><span class="default">fromString</span><span class="keyword">(</span><span class="default">$uuid</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">getBytes</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="354" href="#354">354</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">catch</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">Exception</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="355" href="#355">355</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">warning</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyServiceRequestAssigneeFilter&nbsp;-&nbsp;Invalid&nbsp;UUID:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">'&nbsp;-&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$e</span><span class="default">-&gt;</span><span class="default">getMessage</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="356" href="#356">356</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="357" href="#357">357</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="default">&nbsp;</span><span class="keyword">else</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="358" href="#358">358</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">warning</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyServiceRequestAssigneeFilter&nbsp;-&nbsp;Invalid&nbsp;UUID&nbsp;format:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$uuid</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="359" href="#359">359</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="360" href="#360">360</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="361" href="#361">361</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="362" href="#362">362</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$userIds</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">User</span><span class="default">::</span><span class="default">whereIn</span><span class="keyword">(</span><span class="default">'user_uuid'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$binaryUuids</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">pluck</span><span class="keyword">(</span><span class="default">'user_id'</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">toArray</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="363" href="#363">363</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyServiceRequestAssigneeFilter&nbsp;-&nbsp;User&nbsp;IDs:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">$userIds</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="364" href="#364">364</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="365" href="#365">365</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Enable&nbsp;query&nbsp;logging</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="366" href="#366">366</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">DB</span><span class="default">::</span><span class="default">enableQueryLog</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="367" href="#367">367</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="368" href="#368">368</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;Determine&nbsp;the&nbsp;correct&nbsp;exists&nbsp;method&nbsp;based&nbsp;on&nbsp;the&nbsp;where&nbsp;clause</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="369" href="#369">369</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$existsMethod</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">str_contains</span><span class="keyword">(</span><span class="default">$whereClause</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'orWhere'</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'orWhereExists'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'whereExists'</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="370" href="#370">370</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyServiceRequestAssigneeFilter&nbsp;-&nbsp;Using&nbsp;exists&nbsp;method:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">$existsMethod</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="371" href="#371">371</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="372" href="#372">372</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$result</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">$existsMethod</span><span class="keyword">(</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$query</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">use</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="default">$userIds</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="373" href="#373">373</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$query</span><span class="default">-&gt;</span><span class="default">select</span><span class="keyword">(</span><span class="default">'service_request_assignee_id'</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="374" href="#374">374</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">from</span><span class="keyword">(</span><span class="default">'service_request_assignees'</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="375" href="#375">375</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereColumn</span><span class="keyword">(</span><span class="default">'service_request_assignees.service_request_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'service_requests.service_request_id'</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="376" href="#376">376</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereIn</span><span class="keyword">(</span><span class="default">'service_request_assignees.user_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$userIds</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="377" href="#377">377</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">whereNull</span><span class="keyword">(</span><span class="default">'service_request_assignees.deleted_at'</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="378" href="#378">378</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="379" href="#379">379</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="380" href="#380">380</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">Log</span><span class="default">::</span><span class="default">info</span><span class="keyword">(</span><span class="default">'AssigneeFilterService::applyServiceRequestAssigneeFilter&nbsp;-&nbsp;SQL:&nbsp;'</span><span class="default">&nbsp;</span><span class="keyword">.</span><span class="default">&nbsp;</span><span class="default">json_encode</span><span class="keyword">(</span><span class="default">DB</span><span class="default">::</span><span class="default">getQueryLog</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="381" href="#381">381</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="382" href="#382">382</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$result</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="383" href="#383">383</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="384" href="#384">384</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="385" href="#385">385</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="386" href="#386">386</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;work&nbsp;order&nbsp;IDs&nbsp;that&nbsp;have&nbsp;the&nbsp;specified&nbsp;assignees</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="387" href="#387">387</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="388" href="#388">388</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;int&gt;&nbsp;&nbsp;$userIds&nbsp;&nbsp;The&nbsp;IDs&nbsp;of&nbsp;users&nbsp;to&nbsp;check</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="389" href="#389">389</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&lt;int&gt;&nbsp;The&nbsp;work&nbsp;order&nbsp;IDs&nbsp;with&nbsp;the&nbsp;specified&nbsp;assignees</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="390" href="#390">390</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="391" href="#391">391</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getWorkOrderIdsWithAssignees</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$userIds</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="392" href="#392">392</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 393" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="393" href="#393">393</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">WorkOrderAssignee</span><span class="default">::</span><span class="default">whereIn</span><span class="keyword">(</span><span class="default">'user_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$userIds</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 394" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="394" href="#394">394</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">pluck</span><span class="keyword">(</span><span class="default">'work_order_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 395" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="395" href="#395">395</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">toArray</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="396" href="#396">396</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="397" href="#397">397</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="398" href="#398">398</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="399" href="#399">399</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;quote&nbsp;IDs&nbsp;that&nbsp;have&nbsp;the&nbsp;specified&nbsp;assignees</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="400" href="#400">400</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="401" href="#401">401</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;int&gt;&nbsp;&nbsp;$userIds&nbsp;&nbsp;The&nbsp;IDs&nbsp;of&nbsp;users&nbsp;to&nbsp;check</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="402" href="#402">402</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&lt;int&gt;&nbsp;The&nbsp;quote&nbsp;IDs&nbsp;with&nbsp;the&nbsp;specified&nbsp;assignees</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="403" href="#403">403</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="404" href="#404">404</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getQuoteIdsWithAssignees</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$userIds</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="405" href="#405">405</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 406" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="406" href="#406">406</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">WorkOrderAssignee</span><span class="default">::</span><span class="default">whereIn</span><span class="keyword">(</span><span class="default">'user_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$userIds</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 407" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="407" href="#407">407</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">join</span><span class="keyword">(</span><span class="default">'quotes'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'quotes.work_order_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'='</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'work_order_assignees.work_order_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 408" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="408" href="#408">408</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">pluck</span><span class="keyword">(</span><span class="default">'quotes.quote_id'</span><span class="keyword">)</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="1 test covers line 409" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="409" href="#409">409</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">toArray</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="410" href="#410">410</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="411" href="#411">411</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="412" href="#412">412</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="413" href="#413">413</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Get&nbsp;service&nbsp;request&nbsp;IDs&nbsp;that&nbsp;have&nbsp;the&nbsp;specified&nbsp;assignees</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="414" href="#414">414</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="415" href="#415">415</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;array&lt;int&gt;&nbsp;&nbsp;$userIds&nbsp;&nbsp;The&nbsp;IDs&nbsp;of&nbsp;users&nbsp;to&nbsp;check</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="416" href="#416">416</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;array&lt;int&gt;&nbsp;The&nbsp;service&nbsp;request&nbsp;IDs&nbsp;with&nbsp;the&nbsp;specified&nbsp;assignees</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="417" href="#417">417</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="418" href="#418">418</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getServiceRequestIdsWithAssignees</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$userIds</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="keyword">array</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="419" href="#419">419</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="420" href="#420">420</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">\App\Models\ServiceRequestAssignee</span><span class="default">::</span><span class="default">whereIn</span><span class="keyword">(</span><span class="default">'user_id'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$userIds</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="421" href="#421">421</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">pluck</span><span class="keyword">(</span><span class="default">'service_request_id'</span><span class="keyword">)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="422" href="#422">422</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">-&gt;</span><span class="default">toArray</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="423" href="#423">423</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="424" href="#424">424</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="425" href="#425">425</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">/**</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="426" href="#426">426</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;Resolve&nbsp;the&nbsp;where&nbsp;clause&nbsp;based&nbsp;on&nbsp;the&nbsp;group&nbsp;operation&nbsp;and&nbsp;operator</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="427" href="#427">427</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="428" href="#428">428</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;For&nbsp;assignee&nbsp;filters,&nbsp;we&nbsp;only&nbsp;need&nbsp;the&nbsp;base&nbsp;where&nbsp;clause&nbsp;(where/orWhere)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="429" href="#429">429</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;since&nbsp;we&nbsp;use&nbsp;whereExists/orWhereExists&nbsp;methods&nbsp;rather&nbsp;than&nbsp;specific</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="430" href="#430">430</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;UUID-based&nbsp;where&nbsp;clauses.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="431" href="#431">431</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="432" href="#432">432</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$groupOperation&nbsp;&nbsp;The&nbsp;group&nbsp;operation&nbsp;(and,&nbsp;or)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="433" href="#433">433</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@param&nbsp;&nbsp;string&nbsp;&nbsp;$operator&nbsp;&nbsp;The&nbsp;operator&nbsp;(uuid,&nbsp;not_uuid)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="434" href="#434">434</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*&nbsp;@return&nbsp;string&nbsp;The&nbsp;where&nbsp;clause&nbsp;method&nbsp;name</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="435" href="#435">435</a></td><td class="col-11 codeLine"><span class="comment">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="436" href="#436">436</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">resolveWhereClause</span><span class="keyword">(</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$groupOperation</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">string</span><span class="default">&nbsp;</span><span class="default">$operator</span><span class="keyword">)</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">string</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="437" href="#437">437</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="438" href="#438">438</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;For&nbsp;assignee&nbsp;filters,&nbsp;we&nbsp;only&nbsp;need&nbsp;the&nbsp;base&nbsp;where&nbsp;clause</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="439" href="#439">439</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;The&nbsp;specific&nbsp;exists&nbsp;method&nbsp;(whereExists/orWhereExists)&nbsp;is&nbsp;determined</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="440" href="#440">440</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;in&nbsp;the&nbsp;individual&nbsp;filter&nbsp;methods&nbsp;based&nbsp;on&nbsp;this&nbsp;clause</span></td></tr>
    <tr class="covered-by-large-tests popin d-flex"><td  data-title="10 tests cover line 441" data-content="&lt;ul&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterReturnsQueryWhenValuesEmpty&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesUnassignedFilter&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterThrowsExceptionForInvalidEntityType&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyAssigneeFilterHandlesIsNotOperation&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;Tests\Unit\Services\Filters\AssigneeFilterServiceTest::applyNotAssignedToFilterIsCalledWithCorrectParameters&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\Quote\QuoteListTest::__pest_evaluable_The_quote_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_operation__&lt;/li&gt;&lt;li class=&quot;covered-by-large-tests&quot;&gt;P\Tests\Feature\WorkOrder\WorkOrderListTest::__pest_evaluable_The_work_order_list_API_response_for_web__Filter_by_assignee__is_not_operation__&lt;/li&gt;&lt;/ul&gt;" data-placement="top" data-html="true" class="col-1 text-right"><a id="441" href="#441">441</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$groupOperation</span><span class="default">&nbsp;</span><span class="default">===</span><span class="default">&nbsp;</span><span class="default">'and'</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'where'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'orWhere'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="442" href="#442">442</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="443" href="#443">443</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.6</a> and <a href="https://phpunit.de/">PHPUnit 10.5.17</a> at Wed Jun 25 16:26:03 UTC 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

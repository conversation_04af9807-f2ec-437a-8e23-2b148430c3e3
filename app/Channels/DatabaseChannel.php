<?php

namespace App\Channels;

use App\Models\User;
use App\Models\WorkOrder;
use Illuminate\Notifications\Channels\DatabaseChannel as BaseDatabaseChannel;
use Illuminate\Notifications\Notification;

class DatabaseChannel extends BaseDatabaseChannel
{
    /**
     * Build an array payload for the DatabaseNotification Model.
     *
     * @return array <string, mixed>
     */
    protected function buildPayload($notifiable, Notification $notification): array
    {
        $data = $this->getData($notifiable, $notification);
        $workOrder = null;

        if (! empty($data['work_order_id'])) {
            $workOrder = WorkOrder::whereUuid($data['work_order_id'])->first();
        }

        if (! empty($data['action_done_by'])) {
            $user = User::whereUuid($data['action_done_by'])->first();
        }

        return [
            'reference_notification_id' => $notification->id,
            'notification_uuid' => $notification->id,
            'organization_id' => $notifiable->organization_id ?? null,
            'work_order_id' => $workOrder->work_order_id ?? null,
            'action_done_by' => $user->user_id ?? null,
            'type' => method_exists($notification, 'databaseType')
                ? $notification->databaseType($notifiable)
                : get_class($notification),
            'data' => $this->getData($notifiable, $notification),
            'read_at' => null,
            'cleared_at' => null,
        ];
    }
}

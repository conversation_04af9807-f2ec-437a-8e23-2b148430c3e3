<?php

namespace App\Traits;

use App\Enums\DateRanges;
use App\Models\User;
use App\Models\WorkOrderAssignee;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use LogicException;
use UnexpectedValueException;

trait ServiceRequestListFilterTrait
{
    /**
     * Find the column name for data filtering WRT given filed name.
     */
    protected function findFilterColumn(string $fieldName): ?string
    {
        return match ($fieldName) {
            'status' => 'service_request_statuses.slug',
            'assignee' => 'users.user_uuid',
            'imported_from' => 'service_request_sources.name',
            'added_date' => 'service_requests.created_at',
            default => throw new UnexpectedValueException(__("Unexpected filter column [{$fieldName}] provided.")),
        };
    }

    protected function resolveWhereClauseOperator(string $column, string $operator): string
    {
        return match (true) {
            in_array($column, ['imported_from', 'status']) && $operator === 'is' => 'in',
            in_array($column, ['imported_from', 'status']) && $operator === 'is_not' => 'not_in',
            in_array($column, ['assignee']) && $operator === 'is' => 'uuid',
            in_array($column, ['assignee']) && $operator === 'is_not' => 'not_uuid',
            in_array($column, ['added_date']) && $operator === 'is_between' => 'date_between',
            in_array($column, ['added_date']) && $operator !== 'is_between' => 'date',
            default => throw new UnexpectedValueException(__("Unexpected where clause column, operator [{$column}, {$operator}] provided.")),
        };
    }

    /**
     * Generate a where clause for given payload
     *
     *
     * @param array{
     *      "group_operation": string,
     *      "column_operation": string,
     *      "field" : string,
     *      "column_name" : string,
     *      "values" : array<string>
     * } $payload
     * @param  Builder<Model>  $query
     * @return Builder<Model>|null
     */
    protected function generateQuery(array $payload, Builder $query): ?Builder
    {
        // Finding the where clause operator for the field and operation from the request
        // ie: the field is 'category' and operation is 'is' then we return the operator 'in';
        $operator = $this->resolveWhereClauseOperator($payload['field'], $payload['column_operation']);

        // Here we generate the where clause by passing group operation('or'/'and') and the operator(we find it just above code section)
        switch ($payload['field']) {
            case 'imported_from':
            case 'status':
                $whereClause = $this->resolveWhereClause($payload['group_operation'], $operator);

                return $query->$whereClause($payload['column_name'], $payload['values']);
            case 'assignee':
                $whereClause = $this->resolveWhereClause($payload['group_operation'], $operator);
                if (! empty($payload['values']) && $payload['column_operation'] === 'is_not') {
                    $userUuids = $payload['values'];

                    $userIds = User::whereUuid($userUuids)->pluck('user_id')->toArray();
                    $workOrderAssigneeIds = WorkOrderAssignee::whereIn('user_id', $userIds)->pluck('work_order_id')->toArray();

                    return $query->where(function ($query) use ($payload, $whereClause, $workOrderAssigneeIds, $userUuids) {
                        $query->whereNotIn('service_requests.service_request_id', $workOrderAssigneeIds)
                            ->$whereClause($userUuids, $payload['column_name'], new User);
                    });
                }

                return $query->$whereClause($payload['values'], $payload['column_name'], new User);
            case 'added_date':
                // We are getting first element from the values
                $value = Arr::first($payload['values']);
                // Validating the date slug
                if (! in_array($value, collect(DateRanges::cases())->pluck('value')->toArray())) {
                    throw new UnexpectedValueException(__("Unexpected date slug[{$value}] provided."));
                }
                // We are determining the operation and value for date slug in order to generate the where clause operation.
                $queryOperationSet = $this->resolveOperatorSetForDateSlug($value, $payload['column_operation']);

                // Finding the where clause operator for the "$queryOperationSet['operation']"
                $whereClause = $this->resolveWhereClause($payload['group_operation'], $queryOperationSet['operation']);

                // Handling array value for the date operation, If the value is an array we use where between directly.
                if (isset($queryOperationSet['value']) && is_array($queryOperationSet['value'])) {
                    return $query->$whereClause($payload['column_name'], $queryOperationSet['value']);
                }

                // We've determined that the generated date value only comprises a single value. and apply the where clause with the operator.
                if (! empty($queryOperationSet['operator']) && ! empty($queryOperationSet['value'])) {
                    return $query->$whereClause($payload['column_name'], $queryOperationSet['operator'], $queryOperationSet['value']);
                }

                throw new LogicException(__('Unable to handle the date filter.'));
            default:
                throw new UnexpectedValueException("Unexpected filter field [{$payload['field']}] provided.");
        }
    }

    protected function resolveWhereClause(string $groupOperation, string $whereOperation): string
    {
        $whereClause = null;

        if ($groupOperation === 'and') {
            $whereClause = 'where';
        }

        if ($groupOperation === 'or') {
            $whereClause = 'orWhere';
        }

        $whereClauseOperator = match ($whereOperation) {
            'in' => 'In',
            'date' => 'Date',
            'not_in' => 'NotIn',
            'date_between' => 'Between',
            'uuid' => 'Uuid',
            'not_uuid' => 'NotUuid',
            'date_not_between' => 'NotBetween',
            default => throw new UnexpectedValueException(__("Unexpected where clause operation [{$whereOperation}] provided.")),
        };

        // Building the where clause
        $whereClause .= $whereClauseOperator;

        return $whereClause;
    }

    /**
     * Filter Query
     *
     * Here's where we'll generate multiple where clauses based on the filter expression and return a query builder.
     *
     * @template TModel of Model
     *
     * @param  array<string,mixed>  $filters
     * @param  Builder<TModel>  $queryBuilder
     * @return Builder<TModel>
     */
    protected function filterQuery(array $filters, Builder $queryBuilder): Builder
    {
        if (isset($filters['fl_group']) && is_array($filters['fl_group']) && count($filters['fl_group'])) {

            $queryBuilder->where(function (Builder $query) use ($filters) {
                foreach ($filters['fl_group'] as $filter) {
                    // Check if any data is missing for filter then skip that iteration.
                    if (! isset($filter['field'])) {
                        throw new UnexpectedValueException(__('The filter field should be present and valid.'));
                    }

                    if (! isset($filter['operation'])) {
                        throw new UnexpectedValueException(__('The filter operation should be present and valid.'));
                    }

                    if (empty($filter['values'])) {
                        throw new UnexpectedValueException(__('The filter value should be present and valid.'));
                    }

                    // Find the column name WRT given field slug.
                    $columnName = is_string($filter['field']) ? $this->findFilterColumn($filter['field']) : null;

                    // There is no predefined column name for give field name then skip it.
                    if (! $columnName) {
                        throw new UnexpectedValueException(__("The filter [{$filter['field']}] mapping could not be found."));
                    }

                    // Find the operator for the column.
                    $generateQueryPayload = [
                        'group_operation' => isset($filters['group_op']) && is_string($filters['group_op']) ? $filters['group_op'] : 'and',
                        'column_operation' => is_string($filter['operation']) ? $filter['operation'] : '',
                        'field' => is_string($filter['field']) ? $filter['field'] : '',
                        'column_name' => $columnName,
                        'values' => is_array($filter['values']) ? $filter['values'] : [],
                    ];

                    // Generate the where clause.
                    $appendQuery = $this->generateQuery($generateQueryPayload, $query);

                    if (! $appendQuery) {
                        throw new UnexpectedValueException(__('Filter query builder failed.'));
                    }

                    $query = $appendQuery;
                }
            });
        }

        return $queryBuilder;
    }

    /**
     * Return an array for where operation.
     * It contains 'operation'(date, date_between, date_not), 'operator'(<, >, <>, =, etc), 'value'(single date, or array of date)
     *
     * @return array<string,mixed>
     */
    protected function resolveOperatorSetForDateSlug(string $dateSlug, string $columnOperation): array
    {
        return match ($columnOperation) {
            'is' => $this->dateValuesForIsOperation($dateSlug),
            'is_not' => $this->dateValuesForIsNotOperation($dateSlug),
            'is_after' => $this->dateValuesForIsAfterOperation($dateSlug),
            'is_before' => $this->dateValuesForIsBeForeOperation($dateSlug),
            default => throw new UnexpectedValueException(__("We are facing an unexpected data slug [{$columnOperation}].")),
        };
    }

    /**
     * Generates date operation, operator and value(s) for where clause
     *
     * @return array<string,mixed>
     */
    protected function dateValuesForIsOperation(string $dateSlug): array
    {
        $timezone = 'UTC';

        return match ($dateSlug) {
            'overdue' => $this->dateOperationResponse('date', '<', Carbon::today($timezone)->format('Y-m-d')),
            'today' => $this->dateOperationResponse('date', '=', Carbon::today($timezone)->format('Y-m-d')),
            'tomorrow' => $this->dateOperationResponse('date', '=', Carbon::tomorrow($timezone)->format('Y-m-d')),
            'yesterday' => $this->dateOperationResponse('date', '=', Carbon::yesterday($timezone)->format('Y-m-d')),
            'this_week' => $this->dateOperationResponse('date_between', 'between',
                Carbon::now($timezone)->startOfWeek()->format('Y-m-d'),
                Carbon::now($timezone)->endOfWeek()->format('Y-m-d'),
            ),
            'next_week' => $this->dateOperationResponse('date_between', 'between',
                Carbon::now($timezone)->addWeek()->startOfWeek()->format('Y-m-d'),
                Carbon::now($timezone)->addWeek()->endOfWeek()->format('Y-m-d'),
            ),
            'last_week' => $this->dateOperationResponse('date_between', 'between',
                Carbon::now($timezone)->subWeek()->startOfWeek()->format('Y-m-d'),
                Carbon::now($timezone)->subWeek()->endOfWeek()->format('Y-m-d'),
            ),
            'this_month' => $this->dateOperationResponse('date_between', 'between',
                Carbon::now($timezone)->startOfMonth()->format('Y-m-d'),
                Carbon::now($timezone)->endOfMonth()->format('Y-m-d'),
            ),
            'next_month' => $this->dateOperationResponse('date_between', 'between',
                Carbon::now($timezone)->addMonth()->startOfMonth()->format('Y-m-d'),
                Carbon::now($timezone)->addMonth()->endOfMonth()->format('Y-m-d'),
            ),
            'last_month' => $this->dateOperationResponse('date_between', 'between',
                Carbon::now($timezone)->subMonth()->startOfMonth()->format('Y-m-d'),
                Carbon::now($timezone)->subMonth()->endOfMonth()->format('Y-m-d'),
            ),
            default => throw new UnexpectedValueException(__("Unexpected date slug [{$dateSlug}] Provided.")),
        };
    }

    /**
     * Generates date operation, operator and value(s) for where clause
     *
     * @return array<string,mixed>
     */
    protected function dateValuesForIsNotOperation(string $dateSlug): array
    {
        $timezone = 'UTC';

        return match ($dateSlug) {
            'overdue' => $this->dateOperationResponse('date', '>=', Carbon::today($timezone)->format('Y-m-d')),
            'today' => $this->dateOperationResponse('date', '<>', Carbon::today($timezone)->format('Y-m-d')),
            'tomorrow' => $this->dateOperationResponse('date', '<>', Carbon::tomorrow($timezone)->format('Y-m-d')),
            'yesterday' => $this->dateOperationResponse('date', '<>', Carbon::yesterday($timezone)->format('Y-m-d')),
            'this_week' => $this->dateOperationResponse('date_not_between', null,
                Carbon::now($timezone)->startOfWeek()->format('Y-m-d'),
                Carbon::now($timezone)->endOfWeek()->format('Y-m-d'),
            ),
            'next_week' => $this->dateOperationResponse('date_not_between', null,
                Carbon::now($timezone)->addWeek()->startOfWeek()->format('Y-m-d'),
                Carbon::now($timezone)->addWeek()->endOfWeek()->format('Y-m-d'),
            ),
            'last_week' => $this->dateOperationResponse('date_not_between', null,
                Carbon::now($timezone)->subWeek()->startOfWeek()->format('Y-m-d'),
                Carbon::now($timezone)->subWeek()->endOfWeek()->format('Y-m-d'),
            ),
            'this_month' => $this->dateOperationResponse('date_not_between', null,
                Carbon::now($timezone)->startOfMonth()->format('Y-m-d'),
                Carbon::now($timezone)->endOfMonth()->format('Y-m-d'),
            ),
            'next_month' => $this->dateOperationResponse('date_not_between', null,
                Carbon::now($timezone)->addMonth()->startOfMonth()->format('Y-m-d'),
                Carbon::now($timezone)->addMonth()->endOfMonth()->format('Y-m-d'),
            ),
            'last_month' => $this->dateOperationResponse('date_not_between', null,
                Carbon::now($timezone)->subMonth()->startOfMonth()->format('Y-m-d'),
                Carbon::now($timezone)->subMonth()->endOfMonth()->format('Y-m-d'),
            ),
            default => throw new UnexpectedValueException(__("Unexpected date slug [{$dateSlug}] Provided.")),
        };
    }

    /**
     * Generates date operation, operator and value(s) for where clause
     *
     * @return array<string,mixed>
     */
    protected function dateValuesForIsAfterOperation(string $dateSlug): array
    {
        $timezone = 'UTC';

        return match ($dateSlug) {
            'overdue', 'today' => $this->dateOperationResponse('date', '>', Carbon::today($timezone)->format('Y-m-d')),
            'tomorrow' => $this->dateOperationResponse('date', '>', Carbon::tomorrow($timezone)->format('Y-m-d')),
            'yesterday' => $this->dateOperationResponse('date', '>', Carbon::yesterday($timezone)->format('Y-m-d')),
            'this_week' => $this->dateOperationResponse('date', '>', Carbon::now($timezone)->endOfWeek()->format('Y-m-d')),
            'next_week' => $this->dateOperationResponse('date', '>', Carbon::now($timezone)->addWeek()->endOfWeek()->format('Y-m-d')),
            'last_week' => $this->dateOperationResponse('date', '>', Carbon::now($timezone)->subWeek()->endOfWeek()->format('Y-m-d')),
            'this_month' => $this->dateOperationResponse('date', '>', Carbon::now($timezone)->endOfMonth()->format('Y-m-d')),
            'next_month' => $this->dateOperationResponse('date', '>', Carbon::now($timezone)->addMonth()->endOfMonth()->format('Y-m-d')),
            'last_month' => $this->dateOperationResponse('date', '>', Carbon::now($timezone)->subMonth()->endOfMonth()->format('Y-m-d')),
            default => throw new UnexpectedValueException(__("Unexpected date slug [{$dateSlug}] Provided.")),
        };
    }

    /**
     * Generates date operation, operator and value(s) for where clause
     *
     * @return array<string,mixed>
     */
    protected function dateValuesForIsBeForeOperation(string $dateSlug): array
    {
        $timezone = 'UTC';

        return match ($dateSlug) {
            'overdue', 'today' => $this->dateOperationResponse('date', '<', Carbon::today($timezone)->format('Y-m-d')),
            'tomorrow' => $this->dateOperationResponse('date', '<', Carbon::tomorrow($timezone)->format('Y-m-d')),
            'yesterday' => $this->dateOperationResponse('date', '<', Carbon::yesterday($timezone)->format('Y-m-d')),
            'this_week' => $this->dateOperationResponse('date', '<', Carbon::now($timezone)->startOfWeek()->format('Y-m-d')),
            'next_week' => $this->dateOperationResponse('date', '<', Carbon::now($timezone)->addWeek()->startOfWeek()->format('Y-m-d')),
            'last_week' => $this->dateOperationResponse('date', '<', Carbon::now($timezone)->subWeek()->startOfWeek()->format('Y-m-d')),
            'this_month' => $this->dateOperationResponse('date', '<', Carbon::now($timezone)->startOfMonth()->format('Y-m-d')),
            'next_month' => $this->dateOperationResponse('date', '<', Carbon::now($timezone)->addMonth()->startOfMonth()->format('Y-m-d')),
            'last_month' => $this->dateOperationResponse('date', '<', Carbon::now($timezone)->subMonth()->startOfMonth()->format('Y-m-d')),
            default => throw new UnexpectedValueException(__("Unexpected date slug [{$dateSlug}] Provided.")),
        };
    }

    /**
     * Generates a response for the date operation
     *
     * @return array<string,mixed>
     */
    protected function dateOperationResponse(string $operation, ?string $operator, string $start_date, ?string $end_date = null): array
    {
        // If start and end date present, we return an array
        if ($start_date && $end_date) {
            return [
                'operation' => $operation,
                'operator' => $operator,
                'value' => [
                    $start_date,
                    $end_date,
                ],
            ];
        }

        // if only start date present, we return date as string
        return [
            'operation' => $operation,
            'operator' => $operator,
            'value' => $start_date,
        ];
    }
}

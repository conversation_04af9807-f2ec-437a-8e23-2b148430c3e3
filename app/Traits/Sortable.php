<?php

namespace App\Traits;

use App\Services\SortService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Sortable trait provides sorting functionality to Eloquent models.
 *
 * This trait adds scope methods to Eloquent models that allow for easy sorting
 * of query results. It uses the SortService to handle the actual sorting logic,
 * providing a consistent way to sort data across the application.
 *
 * Key features:
 * - Adds scope methods for sorting and preparing sort values
 * - Integrates with the SortService for centralized sorting logic
 * - Supports entity-specific sorting rules
 *
 * Usage:
 * ```php
 * // In a model
 * class User extends Model
 * {
 *     use Sortable;
 * }
 *
 * // In a controller
 * $sortValue = $request->sort ?? null;
 * $groupValue = $request->group ?? null;
 * $users = User::query()
 *     ->applySorting(
 *         User::prepareSortValue($sortValue, $groupValue, 'user'),
 *         'user'
 *     )
 *     ->get();
 * ```
 */
trait Sortable
{
    /**
     * Static method to prepare a sort value based on sort field, group field, and entity type.
     *
     * This static method provides a convenient way to prepare a sort value without
     * needing to create a query builder instance. It's useful in controllers when
     * you want to prepare a sort value before applying it to a query.
     *
     * Example:
     * ```php
     * $sortValue = User::prepareSortValue($request->sort, $request->group, 'user');
     * $users = User::query()
     *     ->applySorting($sortValue, 'user')
     *     ->get();
     * ```
     *
     * @param  string|null  $sortValue  The sort value (e.g., 'name,-status')
     * @param  string|null  $groupValue  The group value (e.g., 'status')
     * @param  string  $entityType  The entity type (work_order, quote, service_request, user, etc.)
     * @return string The prepared sort value
     */
    public static function prepareSortValue(?string $sortValue, ?string $groupValue, string $entityType): string
    {
        $sortService = app(SortService::class);

        return $sortService->prepareSortValue($sortValue, $groupValue, $entityType);
    }

    /**
     * Apply sorting to the query builder using the SortService.
     *
     * This scope method applies sorting to the query builder based on the provided
     * sort value and entity type. It delegates the actual sorting logic to the
     * SortService, which translates the sort value into appropriate ORDER BY clauses.
     *
     * Example:
     * ```php
     * $users = User::query()
     *     ->applySorting('name,-created_at', 'user')
     *     ->get();
     * ```
     *
     * @param  Builder<Model>  $builder  The query builder to apply sorting to
     * @param  string  $sortValue  The sort value (e.g., 'name,-status')
     * @param  string  $entityType  The entity type (work_order, quote, service_request, user, etc.)
     * @return Builder<Model> The query builder with sorting applied
     */
    public function scopeApplySorting(Builder $builder, string $sortValue, string $entityType): Builder
    {
        $sortService = app(SortService::class);

        return $sortService->applySorting($builder, $sortValue, $entityType);
    }

    /**
     * Prepare a sort value based on sort field, group field, and entity type.
     *
     * This scope method prepares a sort value that can be used with the applySorting
     * method. It handles complex logic like default sort fields, group-based sorting,
     * and special cases for specific entity types. The resulting sort value is a
     * comma-separated string of sort fields.
     *
     * Example:
     * ```php
     * $sortValue = User::prepareSortValue($request->sort, $request->group, 'user');
     * $users = User::query()
     *     ->applySorting($sortValue, 'user')
     *     ->get();
     * ```
     *
     * @param  Builder<Model>  $builder  The query builder (not used in this method, but required for scope methods)
     * @param  string|null  $sortValue  The sort value (e.g., 'name,-status')
     * @param  string|null  $groupValue  The group value (e.g., 'status')
     * @param  string  $entityType  The entity type (work_order, quote, service_request, user, etc.)
     * @return string The prepared sort value
     */
    public function scopePrepareSortValue(Builder $builder, ?string $sortValue, ?string $groupValue, string $entityType): string
    {
        $sortService = app(SortService::class);

        return $sortService->prepareSortValue($sortValue, $groupValue, $entityType);
    }
}

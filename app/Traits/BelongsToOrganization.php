<?php

namespace App\Traits;

use App\Models\Organization;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait BelongsToOrganization
{
    /**
     * Boot with organization scope.
     */
    protected static function bootBelongsToOrganization(): void
    {
        $table = self::getModel()->getTable();
        static::addGlobalScope('organization', function (Builder $query) use ($table) {
            $organizationId = request()->user()?->organization_id
                ?? auth()->user()?->organization_id;

            if ($organizationId) {
                $query->where("{$table}.organization_id", $organizationId);
            }
        });
    }

    /**
     * Get the organization to which this model belongs to.
     *
     * @return BelongsTo<Organization, $this>
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id', 'organization_id');
    }
}

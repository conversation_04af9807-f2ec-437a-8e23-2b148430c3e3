<?php

namespace App\Traits;

use App\Exceptions\AWS\Cognito\JwtValidationException;
use Carbon\Carbon;
use DomainException;
use Exception;
use Firebase\JWT\BeforeValidException;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\JWK;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\SignatureInvalidException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use InvalidArgumentException;
use stdClass;
use UnexpectedValueException;

trait ValidatesJWT
{
    /**
     * Validate a JWT and return the payload
     *
     * @throws JwtValidationException Failed to parse the provided JWT
     */
    protected function validateJWT(string $token): stdClass
    {
        if (empty($token)) {
            throw new JwtValidationException(__('Access denied.'));
        }

        try {
            return JWT::decode(
                $token,
                $this->getJsonWebKeys($token)
            );
        } catch (BeforeValidException|ExpiredException $e) {
            throw new JwtValidationException(__('Access denied due to invalid or expired token.'));
        } catch (SignatureInvalidException|UnexpectedValueException|InvalidArgumentException $e) {
            throw new JwtValidationException(__('Access denied due to invalid token'));
        } catch (DomainException|Exception $e) {
            throw new JwtValidationException(__('Access denied due to invalid token'));
        }
    }

    /**
     * Get json web key
     *
     * @return array<string, Key> An associative array of key IDs (kid) to Key objects
     */
    protected function getJsonWebKeys(string $token): array
    {
        $tokenSegments = explode('.', $token);

        if (count($tokenSegments) !== 3) {
            throw new UnexpectedValueException(__('Wrong number of segments'));
        }

        $tokenHeader = JWT::jsonDecode(JWT::urlsafeB64Decode($tokenSegments[0]));
        $payload = JWT::jsonDecode(JWT::urlsafeB64Decode($tokenSegments[1]));

        if (empty($tokenHeader) || empty($payload)) {
            throw new UnexpectedValueException(__('Invalid Token'));
        }

        $keys = (array) Cache::remember(
            $this->getCacheName($payload->iss, $tokenHeader->kid),
            $this->getCacheExpiry(),
            static function () use ($payload) {
                return Http::get("{$payload->iss}/.well-known/jwks.json")->json();
            }
        );

        return JWK::parseKeySet($keys);
    }

    /**
     * Get cache name for cognito
     */
    protected function getCacheName(string $iss, string $kid): string
    {
        $iss = Str::afterLast($iss, '/');

        return "cognito_{$iss}_{$kid}";
    }

    /**
     *  Get cache expiry
     */
    protected function getCacheExpiry(): Carbon
    {
        return Carbon::now()->addYear();
    }
}

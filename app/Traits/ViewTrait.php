<?php

namespace App\Traits;

use App\Models\User;
use App\Models\UserDefaultView;
use App\Models\UserPinnedView;
use App\Models\View;
use App\Models\ViewType;
use Exception;
use Illuminate\Database\Query\JoinClause;
use InvalidArgumentException;

trait ViewTrait
{
    /**
     *  Fetch view configuration values
     */
    public function viewConfig(ViewType $viewType, ?string $view_uuid, User $user): View
    {
        // Check if the request contains a view_uuid parameter then return view configuration corresponding to the uuid
        // Otherwise return the default view configuration for requested view type.
        if ($view_uuid) {
            $view = View::withoutGlobalScope('organization')
                ->whereUuid($view_uuid)
                ->where('view_type_id', $viewType->view_type_id)
                ->where(function ($query) use ($user) {
                    $query->where(function ($query) use ($user) {
                        $query->where('organization_id', $user->organization_id)
                            ->where('user_id', $user->user_id);
                    })
                        ->orWhere('scope', 'global');
                })
                ->select('view_id', 'view_uuid', 'payload', 'organization_id')
                ->firstOrFail();

            return $view;
        }

        $view = View::withoutGlobalScope('organization')
            ->where('view_type_id', $viewType->view_type_id)
            ->where('name', 'Default View')
            ->select('view_uuid', 'payload')
            ->firstOrFail();

        return $view;
    }

    /**
     * Create a new view.
     *
     * @param  array{
     *  "view_type_id": int,
     *  "name": string,
     *  "user_id": int|null,
     *  "organization_id": int|null,
     *  "payload": mixed,
     * }  $payload
     */
    protected function createView(array $payload): View
    {
        if (empty($payload['user_id'])) {
            throw new InvalidArgumentException(__("undefined user id {$payload['user_id']}"));
        }

        if (empty($payload['organization_id'])) {
            throw new InvalidArgumentException(__("undefined organization id {$payload['organization_id']}."));
        }

        $view = View::create([
            'view_type_id' => $payload['view_type_id'],
            'payload' => $payload['payload'],
            'name' => $payload['name'],
            'user_id' => $payload['user_id'],
            'organization_id' => $payload['organization_id'],
            'scope' => 'individual',
        ]);

        if (empty($view)) {
            throw new Exception(__('Failure to create view.'));
        }

        return $view;
    }

    /**
     *  Set a view is user default view.
     */
    protected function setAsDefaultView(View $view, int $userId): UserDefaultView
    {
        if (empty($view->view_id)) {
            throw new InvalidArgumentException(__('undefined view id'));
        }

        if (empty($view->view_type_id)) {
            throw new InvalidArgumentException(__('undefined view type id'));
        }

        if (empty($userId)) {
            throw new InvalidArgumentException(__("undefined user {$userId}"));
        }

        UserDefaultView::join('views', function (JoinClause $joinQuery) use ($view) {
            $joinQuery->on('user_default_views.view_id', 'views.view_id')
                ->where('views.view_type_id', $view->view_type_id);
        })
            ->where('user_default_views.user_id', $userId)
            ->delete();

        $userDefaultView = UserDefaultView::create([
            'view_id' => $view->view_id,
            'user_id' => $userId,
        ]);

        if (empty($userDefaultView)) {
            throw new Exception(__('Failed to add user default view.'));
        }

        return $userDefaultView;
    }

    /**
     * Add/Remove specified view to the default pinned view list
     */
    protected function pinOrUnpinView(View $view, string $action, ?int $userId): void
    {
        if (empty($view->view_id)) {
            throw new InvalidArgumentException(__('Undefined view id'));
        }

        if (empty($userId)) {
            throw new InvalidArgumentException(__("Undefined user {$userId}"));
        }

        if (! in_array($action, ['pin', 'unpin'])) {
            throw new InvalidArgumentException(__("Invalid action: {$action}"));
        }

        if ($action == 'pin') {
            $pinnedView = UserPinnedView::updateOrCreate([
                'user_id' => $userId,
                'view_id' => $view->view_id,
            ]);
        }

        if ($action == 'unpin') {
            $pinnedView = UserPinnedView::where('user_id', $userId)
                ->where('view_id', $view->view_id)
                ->first();

            if (! empty($pinnedView)) {
                $pinnedView->delete();
            }
        }
    }

    /**
     * Update specified view.
     *
     * @param  array<string, string|mixed>  $payload
     */
    protected function updateView(View $view, array $payload): View
    {
        if (! empty($payload['name'])) {
            $view->name = $payload['name'];
        }

        if (! empty($payload['payload'])) {
            $view->payload = $payload['payload'];
        }

        $view->save();

        return $view;
    }

    /**
     * Delete a specified view
     */
    protected function destroyView(View $view, ?int $userId): void
    {
        if (empty($userId)) {
            throw new InvalidArgumentException(__('Undefined user id'));
        }

        if (! empty($view->deleted_at)) {
            throw new InvalidArgumentException(__('View already deleted'));
        }

        // If this view is pinned then, delete it.
        UserPinnedView::where('user_id', $userId)
            ->where('view_id', $view->view_id)
            ->delete();

        // If this view is a default for a user then, delete it.
        UserDefaultView::where('user_id', $userId)
            ->where('view_id', $view->view_id)
            ->delete();

        //delete the view
        $view->delete();
    }
}

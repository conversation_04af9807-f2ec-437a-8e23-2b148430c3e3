<?php

namespace App\Traits;

use App\Models\Role;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use UnexpectedValueException;

trait UserListFilterTrait
{
    /**
     * Filter Query
     *
     * Here's where we'll generate multiple where clauses based on the filter expression and return a query builder.
     *
     * @template TModel of Model
     *
     * @param  array<string,mixed>  $filters
     * @param  Builder<TModel>  $queryBuilder
     * @return Builder<TModel>
     */
    protected function filterQuery(array $filters, Builder $queryBuilder): Builder
    {
        if (isset($filters['fl_group']) && is_array($filters['fl_group']) && count($filters['fl_group'])) {

            $queryBuilder->where(function (Builder $query) use ($filters) {
                foreach ($filters['fl_group'] as $filter) {

                    // Check if any data is missing for filter then skip that iteration.
                    if (! isset($filter['field'])) {
                        throw new UnexpectedValueException(__('The filter field should be present and valid.'));
                    }

                    if (! isset($filter['operation'])) {
                        throw new UnexpectedValueException(__('The filter operation should be present and valid.'));
                    }

                    if (empty($filter['values'])) {
                        throw new UnexpectedValueException(__('The filter value should be present and valid.'));
                    }

                    // Find the column name WRT given field slug.
                    $columnName = is_string($filter['field']) ? $this->findFilterColumn($filter['field']) : null;

                    // There is no predefined column name for give field name then skip it.
                    if (! $columnName) {
                        throw new UnexpectedValueException(__("The filter [{$filter['field']}] mapping could not be found."));
                    }
                    // Find the operator for the column.
                    $generateQueryPayload = [
                        'group_operation' => isset($filters['group_op']) && is_string($filters['group_op']) ? $filters['group_op'] : 'and',
                        'column_operation' => is_string($filter['operation']) ? $filter['operation'] : '',
                        'field' => is_string($filter['field']) ? $filter['field'] : '',
                        'column_name' => $columnName,
                        'values' => is_array($filter['values']) ? $filter['values'] : [],
                    ];

                    // Generate the where clause.
                    $appendQuery = $this->generateQuery($generateQueryPayload, $query);

                    if (! $appendQuery) {
                        throw new UnexpectedValueException(__('Filter query builder failed.'));
                    }

                    $query = $appendQuery;
                }
            });
        }

        return $queryBuilder;
    }

    /**
     * Find the column name for data filtering WRT given filed name.
     */
    protected function findFilterColumn(string $fieldName): ?string
    {
        return match ($fieldName) {
            'status' => 'users.status',
            'role' => 'roles.role_uuid',
            default => throw new UnexpectedValueException(__("Unexpected filter column [{$fieldName}] provided.")),
        };
    }

    /**
     * Generate a where clause for given payload
     *
     *
     * @param array{
     *      "group_operation": string,
     *      "column_operation": string,
     *      "field" : string,
     *      "column_name" : string,
     *      "values" : array<string>
     * } $payload
     * @param  Builder<Model>  $query
     * @return Builder<Model>|null
     */
    protected function generateQuery(array $payload, Builder $query): ?Builder
    {
        // Finding the where clause operator for the field and operation from the request
        // ie: the field is 'category' and operation is 'is' then we return the operator 'in';
        $operator = $this->resolveWhereClauseOperator($payload['field'], $payload['column_operation']);

        // Here we generate the where clause by passing group operation('or'/'and') and the operator(we find it just above code section)
        switch ($payload['field']) {
            case 'status':

                $whereClause = $this->resolveWhereClause($payload['group_operation'], $operator);

                return $query->$whereClause($payload['column_name'], $payload['values']);
            case 'role':

                $whereClause = $this->resolveWhereClause($payload['group_operation'], $operator);

                return $query->$whereClause($payload['values'], $payload['column_name'], new Role);
            default:
                throw new UnexpectedValueException("Unexpected filter field [{$payload['field']}] provided.");
        }
    }

    protected function resolveWhereClauseOperator(string $column, string $operator): string
    {
        return match (true) {
            in_array($column, ['status']) && $operator === 'is' => 'in',
            in_array($column, ['status']) && $operator === 'is_not' => 'not_in',
            in_array($column, ['role']) && $operator === 'is' => 'uuid',
            in_array($column, ['role']) && $operator === 'is_not' => 'not_uuid',

            default => throw new UnexpectedValueException(__("Unexpected where clause column, operator [{$column}, {$operator}] provided.")),
        };
    }

    /**
     * Return a where clause corresponding to the group operation and operation
     */
    protected function resolveWhereClause(string $groupOperation, string $whereOperation): string
    {
        $whereClause = null;

        if ($groupOperation === 'and') {
            $whereClause = 'where';
        }

        if ($groupOperation === 'or') {
            $whereClause = 'orWhere';
        }

        $whereClauseOperator = match ($whereOperation) {
            'in' => 'In',
            'not_in' => 'NotIn',
            'uuid' => 'Uuid',
            'not_uuid' => 'NotUuid',
            default => throw new UnexpectedValueException(__("Unexpected where clause operation [{$whereOperation}] provided.")),
        };

        // Building the where clause
        $whereClause .= $whereClauseOperator;

        return $whereClause;
    }
}

<?php

namespace App\Traits;

use App\Services\AssigneeFilterService;
use App\Services\DateFilterService;
use App\Services\FilterService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * Filterable trait provides filtering functionality to Eloquent models.
 *
 * This trait adds scope methods to Eloquent models that allow for easy filtering
 * of query results. It uses the FilterService to handle the actual filtering logic,
 * providing a consistent way to filter data across the application.
 *
 * Key features:
 * - Adds scope methods for filtering, date filtering, assignee filtering, and processing queries
 * - Integrates with the FilterService, DateFilterService, and AssigneeFilterService for centralized logic
 * - Supports entity-specific filtering, date filtering, and assignee filtering rules
 *
 * Usage:
 * ```php
 * // In a model
 * class User extends Model
 * {
 *     use Filterable;
 * }
 *
 * // In a controller
 * $users = User::query()
 *     ->process($request->all(), 'user')
 *     ->get();
 * ```
 *
 * Note: This trait should be used directly on models, not included within other traits.
 * For searching functionality, use the Searchable trait separately.
 * For sorting functionality, use the Sortable trait separately.
 *
 * Date filtering and assignee filtering are included in this trait and do not require separate traits.
 */
trait Filterable
{
    /**
     * Legacy filter scope method for backward compatibility.
     *
     * This scope method is maintained for backward compatibility.
     * New code should use the process method instead, which provides more functionality
     * and better integration with the FilterService.
     *
     * @param  Builder<Model>  $builder  The query builder to filter
     * @param  mixed  $filter  The filter object (optional, for backward compatibility)
     * @return Builder<Model> The filtered query builder
     */
    public function scopeFilter(Builder $builder, $filter = null): Builder
    {
        // Just return the builder, as filter classes have been removed
        return $builder;
    }

    /**
     * Apply filters to the query builder using the FilterService.
     *
     * This scope method applies filters to the query builder based on the provided
     * filter expression and entity type. It delegates the actual filtering logic to
     * the FilterService, which translates the filter expression into appropriate
     * WHERE clauses.
     *
     * Example:
     * ```php
     * $filters = [
     *     'fl_group' => [
     *         [
     *             'group_operation' => 'and',
     *             'field' => 'status',
     *             'column_operation' => 'is',
     *             'values' => ['active']
     *         ]
     *     ]
     * ];
     * $users = User::query()
     *     ->applyFilters($filters, 'user')
     *     ->get();
     * ```
     *
     * @param  Builder<Model>  $builder  The query builder to apply filters to
     * @param  array<string,mixed>  $filters  The filter expression
     * @param  string  $entityType  The entity type (work_order, quote, service_request, user, etc.)
     * @return Builder<Model> The filtered query builder
     */
    public function scopeApplyFilters(Builder $builder, array $filters, string $entityType): Builder
    {
        $filterService = app(FilterService::class);

        return $filterService->filterQuery($filters, $builder, $entityType);
    }

    /**
     * Process a query with filtering based on the provided parameters.
     *
     * This scope method combines multiple filtering operations into a
     * single method call, making it easy to apply all necessary filters to a query
     * builder. It delegates to the FilterService's processQuery method, which handles:
     * - Entity ID filtering
     * - Work order ID filtering
     * - Service request ID filtering
     * - Complex filter expressions
     * - Search term filtering (via SearchService)
     *
     * This method centralizes all filtering logic in the FilterService and delegates
     * search functionality to the SearchService.
     *
     * Example:
     * ```php
     * $users = User::query()
     *     ->process($request->all(), 'user')
     *     ->get();
     * ```
     *
     * Note: For searching, use the Searchable trait methods separately.
     * For sorting, use the Sortable trait methods separately.
     *
     * @param  Builder<Model>  $builder  The query builder to process
     * @param  array<string,mixed>  $params  The parameters to apply (search, entity_id, filter, etc.)
     * @param  string  $entityType  The entity type (work_order, quote, service_request, user, etc.)
     * @return Builder<Model> The processed query builder
     */
    public function scopeProcess(Builder $builder, array $params, string $entityType): Builder
    {
        $filterService = app(FilterService::class);

        return $filterService->processQuery($builder, $params, $entityType);
    }

    /**
     * Apply date filter to the query builder using the DateFilterService.
     *
     * This scope method applies date filtering to the query builder based on the provided
     * field, operation, and value. It delegates the actual date filtering logic to the
     * DateFilterService, which translates the date filter into appropriate WHERE clauses.
     *
     * Example:
     * ```php
     * $workOrders = WorkOrder::query()
     *     ->filterDate('due_date', 'is', 'today', 'work_order')
     *     ->get();
     * ```
     *
     * @param  Builder<Model>  $builder  The query builder to apply date filter to
     * @param  string  $field  The field to filter on (e.g., 'due_date', 'created_at')
     * @param  string  $operation  The operation to apply (e.g., 'is', 'is_not', 'is_after', 'is_before')
     * @param  string  $value  The value to filter by (e.g., 'today', 'tomorrow', 'this_week')
     * @param  string|null  $entityType  The entity type (work_order, quote, service_request, user, etc.)
     * @return Builder<Model> The query builder with date filter applied
     */
    public function scopeFilterDate(Builder $builder, string $field, string $operation, string $value, ?string $entityType = null): Builder
    {
        $dateFilterService = app(DateFilterService::class);

        // Create a payload similar to what FilterService expects
        $payload = [
            'group_operation' => 'and',
            'column_operation' => $operation,
            'field' => $field,
            'column_name' => $field,
            'values' => [$value],
        ];

        return $dateFilterService->applyDateFilter($builder, $payload, $entityType);
    }

    /**
     * Apply assignee filter to the query builder using the AssigneeFilterService.
     *
     * This scope method applies assignee filtering to the query builder based on the provided
     * values, operation, and entity type. It delegates the actual assignee filtering logic to the
     * AssigneeFilterService, which translates the assignee filter into appropriate WHERE clauses.
     *
     * Example:
     * ```php
     * $workOrders = WorkOrder::query()
     *     ->filterAssignee(['user_uuid1', 'user_uuid2'], 'is', 'work_order')
     *     ->get();
     * ```
     *
     * Special values:
     * - 'unassigned': Filter for entities with no assignees
     *
     * @param  Builder<Model>  $builder  The query builder to apply assignee filter to
     * @param  array<string>  $values  The values to filter by (user UUIDs or 'unassigned')
     * @param  string  $operation  The operation to apply (e.g., 'is', 'is_not')
     * @param  string  $entityType  The entity type (work_order, quote, service_request)
     * @return Builder<Model> The query builder with assignee filter applied
     */
    public function scopeFilterAssignee(Builder $builder, array $values, string $operation, string $entityType): Builder
    {
        $assigneeFilterService = app(AssigneeFilterService::class);

        // Create a payload similar to what FilterService expects
        $payload = [
            'group_operation' => 'and',
            'column_operation' => $operation,
            'field' => 'assignee',
            'column_name' => 'assignee',
            'values' => $values,
        ];

        return $assigneeFilterService->applyAssigneeFilter($builder, $payload, 'uuid', $entityType);
    }
}

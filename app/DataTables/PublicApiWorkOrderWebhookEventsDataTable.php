<?php

namespace App\DataTables;

use App\Models\PublicApiWorkOrderWebhookEvent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Illuminate\Support\Facades\DB;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class PublicApiWorkOrderWebhookEventsDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param  QueryBuilder<PublicApiWorkOrderWebhookEvent>  $query  Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->filter(function (Builder $query) {
                if ($search = $this->request()->input('search.value')) {
                    $query->where('public_api_work_order_webhook_events.status', $search)
                        ->orWhere('public_api_work_order_webhook_events.response_status_code', $search)
                        ->orWhere('public_api_work_order_webhook_events.public_api_work_order_webhook_event_uuid', $search)
                        ->orwhere(DB::raw("JSON_UNQUOTE(JSON_EXTRACT(public_api_work_order_webhook_events.payload, '$.data.workOrderId'))"), 'like', $search . '%')
                        ->orWhere(DB::raw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(public_api_work_order_webhook_events.payload, '$.eventType')))"), 'like', $search . '%');
                }

            })
            ->setRowId('public_api_work_order_webhook_event_id')
            ->setTransformer(function (PublicApiWorkOrderWebhookEvent $publicApiWorkOrderWebhookEvent) {
                return [
                    'public_api_work_order_webhook_event_id' => $publicApiWorkOrderWebhookEvent->public_api_work_order_webhook_event_id,
                    'workOrderId' => urldecode($publicApiWorkOrderWebhookEvent->payload['data']['workOrderId'] ?? null),
                    'eventType' => urldecode($publicApiWorkOrderWebhookEvent->payload['eventType'] ?? null),
                    'status' => $publicApiWorkOrderWebhookEvent->status,
                    'response_status_code' => $publicApiWorkOrderWebhookEvent->response_status_code,
                    'notified_at' => $publicApiWorkOrderWebhookEvent->notified_at,
                    'created_at' => $publicApiWorkOrderWebhookEvent->created_at ? ("<span title='" . $publicApiWorkOrderWebhookEvent->created_at->setTimezone('America/Chicago')->format('d-m-Y h:i A T') . "'>" . $publicApiWorkOrderWebhookEvent->created_at->setTimezone('UTC') . '</span>') : '',
                    'action' => "<a class='btn btn-sm btn-outline-primary' href=" . route('Debug::logs.webhook.outgoing.show', ['public_api_wo_webhook_event_uuid' => $publicApiWorkOrderWebhookEvent->public_api_work_order_webhook_event_uuid]) . " role='button' target='_blank'>view</a>",
                ];
            })
            ->orderColumn('workOrderId', false)
            ->orderColumn('eventType', false)
            ->orderColumn('created_at', function ($query, $order) {
                $query->orderBy('public_api_work_order_webhook_events.created_at', $order);
            })
            ->addColumn('action', 'Debug::logs.webhook.outgoing.show');
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<PublicApiWorkOrderWebhookEvent>
     */
    public function query(PublicApiWorkOrderWebhookEvent $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('public_api_work_order_webhook_event_id')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->orderBy(0)
            ->autoWidth()
            ->selectStyleSingle()
            ->parameters([
                'buttons' => [
                    'reload',
                    'export',
                ],
            ]);
    }

    /**
     * Get the dataTable columns definition.
     *
     * @return array<int, Column>
     */
    public function getColumns(): array
    {
        return [
            Column::make('public_api_work_order_webhook_event_id')->title('Id'),
            Column::make('workOrderId')
                ->searchable(false),
            Column::make('eventType')
                ->searchable(false),
            Column::make('status'),
            Column::make('response_status_code'),
            Column::make('notified_at'),
            Column::make('created_at')
                ->addClass('text-center')
                ->searchable(false)
                ->title('Created At UTC'),
            Column::computed('action')
                ->addClass('text-center')
                ->exportable(false)
                ->printable(false)
                ->width(60)
                ->searchable(false)
                ->addClass('text-center'),
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'PublicApiWorkOrderWebhookEventsLogs_' . date('YmdHis');
    }
}

<?php

namespace App\DataTables;

use App\Models\WebhookCall;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class WebhookCallsDataTable extends DataTable
{
    /**
     * Build the DataTable class.
     *
     * @param  QueryBuilder<WebhookCall>  $query  Results from query() method.
     */
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        $query->join('organizations', 'organizations.organization_id', '=', 'webhook_calls.organization_id')
            ->select(
                'webhook_calls.webhook_call_id', 'webhook_calls.webhook_call_uuid', 'webhook_calls.name', 'webhook_calls.created_at',
                'webhook_calls.payload', 'webhook_calls.url', 'webhook_calls.organization_id', 'organizations.name AS organization_name'
            );

        return (new EloquentDataTable($query))
            ->setRowId('webhook_call_id')
            ->setTransformer(function (WebhookCall $webhookCall) {
                return [
                    'webhook_call_id' => $webhookCall->webhook_call_id,
                    'url' => $webhookCall->url,
                    'name' => ucfirst($webhookCall->name),
                    'organization' => ucfirst($webhookCall->organization_name ?? ''),
                    'event_type' => $webhookCall->payload['eventType'] ?? null,
                    'created_at' => $webhookCall->created_at ? ("<span title='" . $webhookCall->created_at->setTimezone('America/Chicago')->format('d-m-Y h:i A T') . "'>" . $webhookCall->created_at->setTimezone('UTC') . '</span>') : '',
                    'action' => "<a class='btn btn-sm btn-outline-primary' href=" . route('Debug::logs.webhook.incoming.show', ['webhook_call' => $webhookCall->webhook_call_uuid]) . " role='button' target='_blank'>view</a>",
                ];
            })
            ->addColumn('action', 'Debug::logs.webhook.incoming.show');
    }

    /**
     * Get the query source of dataTable.
     *
     * @return QueryBuilder<WebhookCall>
     */
    public function query(WebhookCall $model): QueryBuilder
    {
        return $model->newQuery();
    }

    /**
     * Optional method if you want to use the html builder.
     */
    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('webhook_call_id')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->orderBy(0)
            ->autoWidth()
            ->selectStyleSingle()
            ->parameters([
                'buttons' => [
                    'reload',
                    'export',
                ],
            ]);
    }

    /**
     * @return array<int, Column>
     */
    public function getColumns(): array
    {
        return [
            Column::make('webhook_call_id')->title('Id'),
            Column::make('name'),
            Column::make('organization'),
            Column::make('event_type'),
            Column::make('url'),
            Column::make('created_at'),
            Column::computed('action')
                ->addClass('text-center')
                ->exportable(false)
                ->printable(false)
                ->width(60)
                ->searchable(false)
                ->addClass('text-center'),
        ];
    }

    /**
     * Get the filename for export.
     */
    protected function filename(): string
    {
        return 'WebhookCalls_' . date('YmdHis');
    }
}

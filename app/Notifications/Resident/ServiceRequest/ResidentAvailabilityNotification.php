<?php

namespace App\Notifications\Resident\ServiceRequest;

use App\Models\Resident;
use Illuminate\Support\Facades\Log;
use NotificationChannels\Twilio\TwilioChannel;
use NotificationChannels\Twilio\TwilioMessage;
use NotificationChannels\Twilio\TwilioSmsMessage;

class ResidentAvailabilityNotification extends BaseServiceRequestNotification
{
    public function __construct(
        public int $serviceRequestId,
        public string $message,
        public string $url,
        public ?int $userId = null
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(Resident $notifiable): array
    {
        return [TwilioChannel::class];
    }

    public function toTwilio(Resident $notifiable): TwilioSmsMessage|TwilioMessage
    {
        $this->data['message'] = $this->message . $this->url;

        Log::info('toTwilio', $this->data);

        return (new TwilioSmsMessage)
            ->content($this->data['message']);
    }
}

<?php

namespace App\Notifications\Resident\WorkOrder;

use App\Models\Resident;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use Illuminate\Support\Facades\Log;
use NotificationChannels\Twilio\TwilioChannel;
use NotificationChannels\Twilio\TwilioMessage;
use NotificationChannels\Twilio\TwilioSmsMessage;

class ScheduledNotification extends BaseWorkOrderNotification
{
    /**
     * Create a new notification instance.
     */
    public function __construct(public int $workOrderId) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return [TwilioChannel::class];
    }

    public function toTwilio(Resident $notifiable): TwilioSmsMessage|TwilioMessage
    {
        $day = '';
        $date = '';
        $scheduledTime = '';
        $category = '';
        $workOrder = WorkOrder::with([
            'timezone:timezone_id,name',
            'issues:problem_diagnosis_id',
            'issues.problemDiagnosis:problem_diagnosis_id,problem_sub_category_id',
            'issues.problemDiagnosis.subCategory:problem_sub_category_id,problem_category_id',
            'issues.problemDiagnosis.subCategory.problemCategory:problem_category_id,label',
            'latestTrips' => function ($query) {
                $query->select(
                    'work_order_service_call_id',
                    'work_order_id',
                    'technician_appointment_id',
                    'lula_appointment_id',
                    'scheduled_start_time',
                    'created_at',
                )->with([
                    'appointment:technician_appointment_id,rescheduled_reason',
                    'lulaAppointment:lula_appointment_id,rescheduled_reason',
                ]);
            },
        ])->select(
            'work_order_id',
            'timezone_id'
        )->findOrFail($this->workOrderId);

        if ($workOrder->issues->isNotEmpty()) {
            $category = implode(' / ', $workOrder->issues->pluck('problemDiagnosis.subCategory.problemCategory.label')->toArray());
        }
        /** @var WorkOrderServiceCall $serviceCall */
        $serviceCall = $workOrder->latestTrips->first();

        if ($serviceCall) {
            if (! empty($serviceCall->appointment)) {
                $appointment = $serviceCall->appointment;
            } elseif (! empty($serviceCall->lulaAppointment)) {
                $appointment = $serviceCall->lulaAppointment;
            }

            $scheduledStartDateTime = $serviceCall->scheduled_start_time?->setTimezone($workOrder->timezone->name ?? config('settings.default_timezone'));
            $day = $scheduledStartDateTime?->format('l');
            $date = $scheduledStartDateTime?->format('F d, Y');
            $scheduledTime = implode(' - ', [
                $scheduledStartDateTime?->format('ha'),
                $scheduledStartDateTime?->addHours(4)->format('ha'),
            ]);
        }

        if (! empty($appointment->rescheduled_reason)) {
            $message = "The appointment to take care of your {$category} maintenance request has been rescheduled to {$day}, {$date} with an arrival window of {$scheduledTime} You will receive a text message when the technician is on their way.";
        } else {

            $message = "We have a technician scheduled on {$day}, {$date} with an arrival window of {$scheduledTime} to take care of your {$category} maintenance request. You will receive a text message when the technician is on their way.";
        }

        $this->data['message'] = $message;

        Log::info('toTwilio', $this->data);

        return (new TwilioSmsMessage)
            ->content($this->data['message']);
    }
}

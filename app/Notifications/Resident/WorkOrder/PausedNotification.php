<?php

namespace App\Notifications\Resident\WorkOrder;

use App\Models\Resident;
use Illuminate\Support\Facades\Log;
use NotificationChannels\Twilio\TwilioChannel;
use NotificationChannels\Twilio\TwilioMessage;
use NotificationChannels\Twilio\TwilioSmsMessage;

class PausedNotification extends BaseWorkOrderNotification
{
    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return [TwilioChannel::class];
    }

    public function toTwilio(Resident $notifiable): TwilioSmsMessage|TwilioMessage
    {
        $workOrderTask = $this->workOrder->tasks->first();
        $workOrderCategory = '';

        if ($workOrderTask) {
            $workOrderCategory = $workOrderTask->problemDiagnosis->subCategory->problemCategory->label ?? '';
        }

        $this->data['message'] = "Your {$workOrderCategory} maintenance request has been paused. Your property manager is working on next steps to resolve your issue.";

        Log::info('toTwilio', $this->data);

        return (new TwilioSmsMessage)
            ->content($this->data['message']);
    }
}

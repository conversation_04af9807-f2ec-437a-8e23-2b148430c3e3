<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;

class BaseNotification extends Notification implements ShouldDispatchAfterCommit, ShouldQueue
{
    use Queueable;

    /**
     * @var array<string, mixed>
     */
    public array $data;

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'data' => $this->data,
            'notification' => self::class,
            'notifiable' => $notifiable,
        ];
    }
}

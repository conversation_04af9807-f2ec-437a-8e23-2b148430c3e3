<?php

declare(strict_types=1);

namespace App\States\WorkOrderIssue;

use App\Enums\WorkOrderIssueActions;

class Assigned extends WorkOrderIssueState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'assigned';

    public static string $actionName = 'assigned';

    public function label(): string
    {
        return 'Assigned';
    }

    public function labelForMobile(): string
    {
        return 'Pending';
    }

    public function colorClassForMobile(): string
    {
        return 'has-grey';
    }

    /**
     * @return array<int, string>
     */
    public function actions(): array
    {
        return [
            WorkOrderIssueActions::EDIT(),
            WorkOrderIssueActions::UNASSIGN(),
            WorkOrderIssueActions::ASSIGN(),
            WorkOrderIssueActions::MARK_AS_DONE(),
            WorkOrderIssueActions::DECLINE(),
        ];
    }
}

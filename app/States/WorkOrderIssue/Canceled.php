<?php

declare(strict_types=1);

namespace App\States\WorkOrderIssue;

use App\Enums\WorkOrderIssueActions;

class Canceled extends WorkOrderIssueState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'canceled';

    public static string $actionName = 'canceled';

    public function label(): string
    {
        return 'Canceled';
    }

    public function labelForMobile(): string
    {
        return 'Canceled';
    }

    public function colorClassForMobile(): string
    {
        return 'has-danger';
    }

    /**
     * @return array<int, string>
     */
    public function actions(): array
    {
        return [
            WorkOrderIssueActions::EDIT(),
            WorkOrderIssueActions::UNASSIGN(),
        ];
    }
}

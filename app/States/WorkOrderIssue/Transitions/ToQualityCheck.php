<?php

namespace App\States\WorkOrderIssue\Transitions;

use App\Models\WorkOrderIssue;
use App\States\WorkOrderIssue\QualityCheck;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToQualityCheck extends Transition
{
    public function __construct(protected WorkOrderIssue $workOrderIssue) {}

    public function handle(): WorkOrderIssue
    {
        $this->workOrderIssue->state = new QualityCheck($this->workOrderIssue);
        $this->workOrderIssue->state_updated_at = CarbonImmutable::now();
        $this->workOrderIssue->save();

        return $this->workOrderIssue;
    }
}

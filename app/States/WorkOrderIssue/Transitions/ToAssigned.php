<?php

namespace App\States\WorkOrderIssue\Transitions;

use App\Models\WorkOrderIssue;
use App\States\WorkOrderIssue\Assigned;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToAssigned extends Transition
{
    public function __construct(
        protected WorkOrderIssue $workOrderIssue
    ) {}

    public function handle(): WorkOrderIssue
    {
        $this->workOrderIssue->state = new Assigned($this->workOrderIssue);
        $this->workOrderIssue->state_updated_at = CarbonImmutable::now();
        $this->workOrderIssue->save();

        return $this->workOrderIssue;
    }
}

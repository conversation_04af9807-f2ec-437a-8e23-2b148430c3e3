<?php

namespace App\States\WorkOrderIssue\Transitions;

use App\Models\WorkOrderIssue;
use App\States\Issue\Done;
use App\States\WorkOrderIssue\Done as WorkOrderIssueDone;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToDone extends Transition
{
    /**
     * @param  array<string,mixed>  $workOrderIssueData
     */
    public function __construct(
        protected WorkOrderIssue $workOrderIssue,
        public ?array $workOrderIssueData = null
    ) {}

    public function handle(): WorkOrderIssue
    {
        $this->workOrderIssue->state = new WorkOrderIssueDone($this->workOrderIssue);
        $this->workOrderIssue->state_updated_at = CarbonImmutable::now();
        $this->workOrderIssue->service_notes = $this->workOrderIssueData['service_notes'] ?? null;
        $this->workOrderIssue->issue_caused_by_resident = $this->workOrderIssueData['issue_caused_by_resident'] ?? null;
        $this->workOrderIssue->issue_caused_details = $this->workOrderIssueData['issue_caused_details'] ?? null;
        $this->workOrderIssue->save();

        // Update issue
        $this->workOrderIssue->issue->state = new Done($this->workOrderIssue->issue);
        $this->workOrderIssue->issue->state_updated_at = CarbonImmutable::now();
        $this->workOrderIssue->issue->save();

        return $this->workOrderIssue;
    }
}

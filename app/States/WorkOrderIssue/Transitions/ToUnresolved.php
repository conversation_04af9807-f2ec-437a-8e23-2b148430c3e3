<?php

namespace App\States\WorkOrderIssue\Transitions;

use App\Models\User;
use App\Models\WorkOrderIssue;
use App\States\WorkOrderIssue\Unresolved;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToUnresolved extends Transition
{
    public function __construct(protected WorkOrderIssue $workOrderIssue, public ?string $declineReason = null, public ?User $user = null) {}

    public function handle(): WorkOrderIssue
    {
        $this->workOrderIssue->state = new Unresolved($this->workOrderIssue);
        $this->workOrderIssue->state_updated_at = CarbonImmutable::now();
        $this->workOrderIssue->decline_reason = $this->declineReason ?? null;
        $this->workOrderIssue->declined_user_id = $this->user->user_id ?? null;
        $this->workOrderIssue->save();

        return $this->workOrderIssue;
    }
}

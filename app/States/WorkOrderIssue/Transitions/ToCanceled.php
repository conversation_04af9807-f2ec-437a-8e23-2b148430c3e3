<?php

namespace App\States\WorkOrderIssue\Transitions;

use App\Events\WorkOrder\Issue\WorkOrderIssueCanceled;
use App\Models\WorkOrderIssue;
use App\States\WorkOrderIssue\Canceled;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToCanceled extends Transition
{
    public function __construct(protected WorkOrderIssue $workOrderIssue) {}

    public function handle(): WorkOrderIssue
    {
        $this->workOrderIssue->state = new Canceled($this->workOrderIssue);
        $this->workOrderIssue->state_updated_at = CarbonImmutable::now();
        $this->workOrderIssue->save();

        WorkOrderIssueCanceled::broadcast($this->workOrderIssue->work_order_issue_id)->toOthers();

        return $this->workOrderIssue;
    }
}

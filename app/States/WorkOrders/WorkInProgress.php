<?php

namespace App\States\WorkOrders;

use App\Enums\WorkOrderActions;

class WorkInProgress extends WorkOrderState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'work_in_progress';

    public static string $actionName = 'work_in_progress';

    public function color(): string
    {
        return '#aj981j';
    }

    public function label(): string
    {
        return 'Work In Progress';
    }

    public function colorClass(): string
    {
        return 'has-primary';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            // WorkOrderActions::PAUSE(),
            // WorkOrderActions::CANCEL(),
            WorkOrderActions::START_WORK(),
            WorkOrderActions::PAUSE_WORK(),
            WorkOrderActions::RESUME_WORK(),
            WorkOrderActions::PAUSE_EN_ROUTE(),
            WorkOrderActions::RESUME_EN_ROUTE(),
            WorkOrderActions::CLOSE_WORK_ORDER(),
        ];
    }
}

<?php

namespace App\States\WorkOrders;

use App\Enums\WorkOrderActions;

class QualityCheck extends WorkOrderState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'quality_check';

    public static string $actionName = 'quality_check';

    public function color(): string
    {
        return '#aj981j';
    }

    public function label(): string
    {
        return 'Quality Check';
    }

    public function colorClass(): string
    {
        return 'has-pink-dark';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            WorkOrderActions::READY_TO_INVOICE(),
            //WorkOrderActions::CANCEL(),
            //WorkOrderActions::PAUSE(),
        ];
    }
}

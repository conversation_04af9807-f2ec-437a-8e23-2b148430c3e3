<?php

namespace App\States\WorkOrders;

use App\Enums\WorkOrderActions;

class ReadyToSchedule extends WorkOrderState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'ready_to_schedule';

    public static string $actionName = 'ready_to_schedule';

    public function color(): string
    {
        return '#aj981j';
    }

    public function label(): string
    {
        return 'Ready To Schedule';
    }

    public function colorClass(): string
    {
        return 'has-success';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            WorkOrderActions::SCHEDULE(),
            //WorkOrderActions::PAUSE(),
            //WorkOrderActions::CANCEL(),
            WorkOrderActions::DELETE(),
            WorkOrderActions::CREATE_ISSUE(),
        ];
    }
}

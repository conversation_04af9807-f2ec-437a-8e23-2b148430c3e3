<?php

namespace App\States\WorkOrders;

use App\Enums\WorkOrderActions;

class ClaimPending extends WorkOrderState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'claim_pending';

    public static string $actionName = 'claim_pending';

    public function color(): string
    {
        return '#aj981j';
    }

    public function label(): string
    {
        return 'Claim Pending';
    }

    public function colorClass(): string
    {
        return 'has-grey';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            //WorkOrderActions::CANCEL(),
        ];
    }
}

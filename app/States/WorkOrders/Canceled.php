<?php

namespace App\States\WorkOrders;

use App\Enums\WorkOrderActions;

class Canceled extends WorkOrderState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'canceled';

    public static string $actionName = 'cancel';

    public function color(): string
    {
        return '#aj981j';
    }

    public function label(): string
    {
        return 'Canceled';
    }

    public function colorClass(): string
    {
        return 'has-danger';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            WorkOrderActions::CREATE(),
            WorkOrderActions::DELETE(),
        ];
    }
}

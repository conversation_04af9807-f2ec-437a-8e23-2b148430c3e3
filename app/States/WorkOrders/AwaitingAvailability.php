<?php

namespace App\States\WorkOrders;

use App\Enums\WorkOrderActions;

class AwaitingAvailability extends WorkOrderState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'awaiting_availability';

    public static string $actionName = 'awaiting_availability';

    public function color(): string
    {
        return '#F2F4F7';
    }

    public function label(): string
    {
        return 'Awaiting Availability';
    }

    public function colorClass(): string
    {
        return 'has-secondary';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            WorkOrderActions::READY_TO_SCHEDULE(),
            //WorkOrderActions::CANCEL(),
            WorkOrderActions::DELETE(),
            WorkOrderActions::CREATE_ISSUE(),
        ];
    }
}

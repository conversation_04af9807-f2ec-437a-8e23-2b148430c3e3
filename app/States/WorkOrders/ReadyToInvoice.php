<?php

namespace App\States\WorkOrders;

use App\Enums\WorkOrderActions;

class ReadyToInvoice extends WorkOrderState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'ready_to_invoice';

    public static string $actionName = 'ready_to_invoice';

    public function color(): string
    {
        return '#aj981j';
    }

    public function label(): string
    {
        return 'Invoicing';
    }

    public function colorClass(): string
    {
        return 'has-warning-text-white';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            WorkOrderActions::RESOLVE(),
            //WorkOrderActions::CANCEL(),
            //WorkOrderActions::PAUSE(),
        ];
    }
}

<?php

namespace App\States\WorkOrders\Transitions;

use App\Enums\WorkOrderStatus as WorkOrderStatusEnum;
use App\Events\WorkOrder\Actions\WorkOrderClaimPending;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderStatus;
use App\States\WorkOrders\ClaimPending;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToClaimPending extends Transition
{
    // Use the "notes" key to add notes to the activity log. Then make a corresponding change to "LogStatusChangeEvents" listener
    public function __construct(protected WorkOrder $workOrder, public ?User $user = null) {}

    public function handle(): WorkOrder
    {
        // Change status
        $oldState = $this->workOrder->state;
        $workOrderCancelledStatus = WorkOrderStatus::where('slug', WorkOrderStatusEnum::CLAIM_PENDING())
            ->firstOrFail('work_order_status_id');
        $this->workOrder->work_order_status_id = $workOrderCancelledStatus->work_order_status_id;
        $this->workOrder->state = new ClaimPending($this->workOrder);
        $this->workOrder->state_updated_at = CarbonImmutable::now();
        $this->workOrder->save();

        // trigger event
        $activityLogEventAttributes = [
            'from' => $oldState->label(),
            'from_color_class' => $oldState->colorClass(),
            'to' => $this->workOrder->state->label(),
            'to_color_class' => $this->workOrder->state->colorClass(),
            'slug_of_to' => $this->workOrder->state->getValue(),
        ];

        event(new WorkOrderClaimPending(
            $this->workOrder->work_order_id,
            $activityLogEventAttributes,
            $this->user?->user_id,
        ));

        return $this->workOrder;
    }
}

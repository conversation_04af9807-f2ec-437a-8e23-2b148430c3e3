<?php

namespace App\States\WorkOrders\Transitions;

use App\Enums\WorkOrderStatus as WorkOrderStatusEnum;
use App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderUpdated;
use App\Events\WorkOrder\Actions\WorkOrderQualityCheck;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderStatus;
use App\States\WorkOrders\QualityCheck;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToQualityCheck extends Transition
{
    // Use the "notes" key to add notes to the activity log. Then make a corresponding change to "LogStatusChangeEvents" listener

    /**
     * @param  array<string,mixed>  $payload
     */
    public function __construct(
        protected WorkOrder $workOrder,
        public ?User $user = null,
    ) {}

    public function handle(): WorkOrder
    {
        $oldState = $this->workOrder->state;
        $this->workOrder->state = new QualityCheck($this->workOrder);

        $workOrderStatus = WorkOrderStatus::where('slug', WorkOrderStatusEnum::QUALITY_CHECK())->firstOrFail();
        $this->workOrder->work_order_status_id = $workOrderStatus->work_order_status_id;
        $this->workOrder->work_completed_at = CarbonImmutable::now();
        $this->workOrder->state_updated_at = CarbonImmutable::now();
        $this->workOrder->save();

        $activityLogEventAttributes = [
            'from' => $oldState->label(),
            'from_color_class' => $oldState->colorClass(),
            'to' => $this->workOrder->state->label(),
            'to_color_class' => $this->workOrder->state->colorClass(),
            'slug_of_to' => $this->workOrder->state->getValue(),
        ];

        event(new WorkOrderQualityCheck(
            $this->workOrder->work_order_id,
            $activityLogEventAttributes,
            $this->user?->user_id
        ));

        ServiceRequestWorkOrderUpdated::broadcast($this->workOrder->work_order_id);

        return $this->workOrder;
    }
}

<?php

namespace App\States\WorkOrders\Transitions;

use App\Enums\Boolean;
use App\Enums\WorkOrderStatus as WorkOrderStatusEnum;
use App\Events\WorkOrder\WorkOrderReOpen;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderStatus;
use App\Models\WorkOrderTask;
use App\States\ServiceCalls\Canceled;
use App\States\ServiceCalls\Ended;
use App\States\WorkOrders\Created;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToCreated extends Transition
{
    public function __construct(protected WorkOrder $workOrder, public ?User $user = null, public ?WorkOrderTask $workOrderTask = null) {}

    public function handle(): WorkOrder
    {

        $latestServiceCall = $this->workOrderTask?->latestServiceCalls->first() ?? null;

        $oldState = $this->workOrder->state;

        if (! empty($latestServiceCall)) {
            $latestServiceCall->is_active = Boolean::NO();
            $latestServiceCall->save();

            if (! $latestServiceCall->state->equals(Ended::class, Canceled::class)) {
                $latestServiceCall->state->transitionTo(Ended::class, [], $this->user);
            }
        }

        // Change work status and other data
        $workOrderStatus = WorkOrderStatus::where('slug', WorkOrderStatusEnum::NEW())->firstOrFail();
        $this->workOrder->work_order_status_id = $workOrderStatus->work_order_status_id;
        $this->workOrder->paused_reason = null;
        $this->workOrder->canceled_reason = null;
        $this->workOrder->paused_at = null;
        $this->workOrder->work_completed_at = null;
        $this->workOrder->resolved_at = null;
        $this->workOrder->canceled_at = null;
        $this->workOrder->vendor_id = null;
        $this->workOrder->vendor_work_order_id = null;

        // Change state
        $this->workOrder->state = new Created($this->workOrder);
        $this->workOrder->state_updated_at = CarbonImmutable::now();
        $this->workOrder->save();

        if (! empty($this->workOrder) && ! empty($this->workOrderTask)) {
            $activityLogEventAttributes = [
                'from' => $oldState->label(),
                'from_color_class' => $oldState->colorClass(),
                'to' => $this->workOrder->state->label(),
                'to_color_class' => $this->workOrder->state->colorClass(),
                'slug_of_to' => $this->workOrder->state->getValue(),
            ];

            // trigger event
            event(new WorkOrderReOpen(
                $this->workOrder->work_order_id,
                $this->workOrderTask->work_order_task_id,
                $activityLogEventAttributes,
                $this->user?->user_id
            ));
        }

        return $this->workOrder;
    }
}

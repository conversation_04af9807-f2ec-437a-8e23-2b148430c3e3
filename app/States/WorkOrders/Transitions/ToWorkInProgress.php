<?php

namespace App\States\WorkOrders\Transitions;

use App\Enums\WorkOrderStatus as WorkOrderStatusEnum;
use App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderUpdated;
use App\Events\WorkOrder\Actions\WorkOrderWorkInProgress;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderStatus;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToWorkInProgress extends Transition
{
    public function __construct(protected WorkOrder $workOrder, public ?User $user = null) {}

    public function handle(): WorkOrder
    {
        $workOrderInProgressStatus = WorkOrderStatus::where('slug', WorkOrderStatusEnum::WORK_IN_PROGRESS())
            ->select('work_order_status_id')
            ->firstOrFail();

        $oldState = $this->workOrder->state;
        $this->workOrder->work_order_status_id = $workOrderInProgressStatus->work_order_status_id;
        $this->workOrder->state = new WorkInProgress($this->workOrder);
        $this->workOrder->state_updated_at = CarbonImmutable::now();
        $this->workOrder->save();

        // trigger event
        $activityLogEventAttributes = [
            'from' => $oldState->label(),
            'from_color_class' => $oldState->colorClass(),
            'to' => $this->workOrder->state->label(),
            'to_color_class' => $this->workOrder->state->colorClass(),
            'slug_of_to' => $this->workOrder->state->getValue(),
        ];

        event(new WorkOrderWorkInProgress(
            $this->workOrder->work_order_id,
            $activityLogEventAttributes,
            $this->user?->user_id
        ));

        ServiceRequestWorkOrderUpdated::broadcast($this->workOrder->work_order_id);

        return $this->workOrder;
    }
}

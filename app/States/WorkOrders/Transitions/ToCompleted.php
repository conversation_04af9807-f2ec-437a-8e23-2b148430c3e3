<?php

namespace App\States\WorkOrders\Transitions;

use App\Enums\WorkOrderStatus as WorkOrderStatusEnum;
use App\Events\WorkOrder\WorkOrderResolve;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderStatus;
use App\Models\WorkOrderTask;
use App\States\WorkOrders\Completed;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToCompleted extends Transition
{
    public function __construct(protected WorkOrder $workOrder, public ?User $user = null, public ?WorkOrderTask $workOrderTask = null) {}

    public function handle(): WorkOrder
    {
        $oldState = $this->workOrder->state;
        // Change status
        $workOrderStatus = WorkOrderStatus::where('slug', WorkOrderStatusEnum::COMPLETED())->firstOrFail();
        $this->workOrder->work_order_status_id = $workOrderStatus->work_order_status_id;

        $this->workOrder->state = new Completed($this->workOrder);
        $this->workOrder->state_updated_at = CarbonImmutable::now();
        $this->workOrder->resolved_at = CarbonImmutable::now();

        $this->workOrder->save();

        if (! empty($this->workOrder) && ! empty($this->workOrderTask)) {
            // trigger event
            $activityLogEventAttributes = [
                'from' => $oldState->label(),
                'from_color_class' => $oldState->colorClass(),
                'to' => $this->workOrder->state->label(),
                'to_color_class' => $this->workOrder->state->colorClass(),
                'slug_of_to' => $this->workOrder->state->getValue(),
            ];

            event(new WorkOrderResolve(
                $this->workOrder->work_order_id,
                $this->workOrderTask->work_order_task_id,
                $activityLogEventAttributes,
                $this->user?->user_id
            ));
        }

        return $this->workOrder;
    }
}

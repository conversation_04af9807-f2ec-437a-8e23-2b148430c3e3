<?php

namespace App\States\WorkOrders\Transitions;

use App\Enums\WorkOrderStatus as WorkOrderStatusEnum;
use App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderUpdated;
use App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderStatus;
use App\States\WorkOrders\ReadyToSchedule;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToReadyToSchedule extends Transition
{
    public function __construct(
        protected WorkOrder $workOrder,
        public ?User $user = null,
    ) {}

    public function handle(): WorkOrder
    {
        $oldWorkOrderState = $this->workOrder->state;
        // Change status
        $workOrderStatus = WorkOrderStatus::select('work_order_status_id')
            ->where('slug', WorkOrderStatusEnum::READY_TO_SCHEDULE())
            ->firstOrFail();
        $this->workOrder->work_order_status_id = $workOrderStatus->work_order_status_id;

        // Change state
        $this->workOrder->state = new ReadyToSchedule($this->workOrder);
        $this->workOrder->state_updated_at = CarbonImmutable::now();
        $this->workOrder->save();

        $activityLogEventAttribute = [
            'from' => $oldWorkOrderState->label(),
            'from_color_class' => $oldWorkOrderState->colorClass(),
            'to' => $this->workOrder->state->label(),
            'to_color_class' => $this->workOrder->state->colorClass(),
            'slug_of_to' => $this->workOrder->state->getValue(),
        ];

        WorkOrderReadyToSchedule::broadcast($this->workOrder->work_order_id, $activityLogEventAttribute, $this->user?->user_id)->toOthers();
        ServiceRequestWorkOrderUpdated::broadcast($this->workOrder->work_order_id);

        return $this->workOrder;
    }
}

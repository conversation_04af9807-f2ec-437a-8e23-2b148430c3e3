<?php

namespace App\States\WorkOrders\Transitions;

use App\Enums\WorkOrderStatus as WorkOrderStatusEnum;
use App\Events\WorkOrder\PublicWebhook\Lula\WorKOrderReadyToInvoiceLulaWebhook;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderStatus;
use App\Models\WorkOrderTask;
use App\States\WorkOrders\ReadyToInvoice;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToReadyToInvoice extends Transition
{
    public function __construct(
        protected WorkOrder $workOrder,
        public ?User $user = null,
        public ?WorkOrderTask $workOrderTask = null
    ) {}

    public function handle(): WorkOrder
    {
        $this->workOrder->state = new ReadyToInvoice($this->workOrder);
        $workOrderStatus = WorkOrderStatus::where('slug', WorkOrderStatusEnum::READY_TO_INVOICE())->firstOrFail();
        $this->workOrder->work_order_status_id = $workOrderStatus->work_order_status_id;
        $this->workOrder->state_updated_at = CarbonImmutable::now();
        $this->workOrder->save();

        event(new WorKOrderReadyToInvoiceLulaWebhook($this->workOrder, $this->user, $this->workOrderTask));

        return $this->workOrder;
    }
}

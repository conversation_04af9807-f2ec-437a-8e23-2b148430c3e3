<?php

namespace App\States\ServiceRequests;

class AwaitingAvailability extends ServiceRequestState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'awaiting_availability';

    public static string $actionName = 'awaiting_availability';

    public function color(): string
    {
        return '#344054';
    }

    public function label(): string
    {
        return 'Awaiting Availability';
    }

    public function colorClass(): string
    {
        return 'has-gray';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            CreateWorkOrder::$actionName,
            Closed::$actionName,
        ];
    }
}

<?php

namespace App\States\ServiceRequests;

class Scoping extends ServiceRequestState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'scoping';

    public static string $actionName = 'scoping';

    public function color(): string
    {
        return '#FF6200';
    }

    public function label(): string
    {
        return 'Scoping';
    }

    public function colorClass(): string
    {
        return 'has-orange';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            CreateWorkOrder::$actionName,
            Closed::$actionName,
        ];
    }
}

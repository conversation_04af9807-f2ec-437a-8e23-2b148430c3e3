<?php

namespace App\States\ServiceRequests;

use App\Events\ServiceRequest\ServiceRequestStateChanged;
use App\Models\ServiceRequest;
use App\States\ServiceRequests\Transitions\ToAwaitingAvailability;
use App\States\ServiceRequests\Transitions\ToClosed;
use App\States\ServiceRequests\Transitions\ToCreateWorkOrder;
use App\States\ServiceRequests\Transitions\ToInProgress;
use App\States\ServiceRequests\Transitions\ToScoping;
use Spatie\ModelStates\Exceptions\InvalidConfig;
use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

/**
 * @extends State<ServiceRequest>
 */
abstract class ServiceRequestState extends State
{
    /**
     * @throws InvalidConfig
     */
    public static function config(): StateConfig
    {
        return parent::config()
            ->default(Created::class)
            ->allowTransition(
                from : [Created::class, Scoping::class, AwaitingAvailability::class, CreateWorkOrder::class, InProgress::class],
                to: Closed::class,
                transition: ToClosed::class,
            )->allowTransition(
                from: [Created::class],
                to: Scoping::class,
                transition: ToScoping::class,
            )
            ->allowTransition(
                from: [Scoping::class, InProgress::class, CreateWorkOrder::class],
                to: AwaitingAvailability::class,
                transition: ToAwaitingAvailability::class,
            )
            ->allowTransition(
                from: [Scoping::class, AwaitingAvailability::class],
                to: CreateWorkOrder::class,
                transition: ToCreateWorkOrder::class
            )->allowTransition(
                from: [CreateWorkOrder::class, AwaitingAvailability::class, Closed::class],
                to: InProgress::class,
                transition: ToInProgress::class
            )->stateChangedEvent(ServiceRequestStateChanged::class);
    }

    abstract public function color(): string;

    abstract public function colorClass(): string;

    abstract public function label(): string;

    /**
     * @return array<int, string|null>
     */
    abstract public function actions(): array;
}

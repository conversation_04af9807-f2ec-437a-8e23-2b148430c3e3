<?php

namespace App\States\ServiceRequests\Transitions;

use App\Enums\ServiceRequestStatusTypes;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestStatus;
use App\Models\User;
use App\States\ServiceRequests\InProgress;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToInProgress extends Transition
{
    public function __construct(
        protected ServiceRequest $serviceRequest,
        public ?User $user = null,
        public ?string $notes = null,
    ) {}

    public function handle(): ServiceRequest
    {
        // Change status
        $serviceRequestStatus = ServiceRequestStatus::select(['service_request_status_id'])
            ->where('slug', ServiceRequestStatusTypes::IN_PROGRESS())
            ->firstOrFail();
        $this->serviceRequest->service_request_status_id = $serviceRequestStatus->service_request_status_id;

        // Change state
        $this->serviceRequest->state = new InProgress($this->serviceRequest);
        $this->serviceRequest->state_updated_at = CarbonImmutable::now();
        $this->serviceRequest->save();

        return $this->serviceRequest;
    }
}

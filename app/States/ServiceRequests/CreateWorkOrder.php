<?php

namespace App\States\ServiceRequests;

class CreateWorkOrder extends ServiceRequestState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'create_work_order';

    public static string $actionName = 'create_work_order';

    public function color(): string
    {
        return '#FF6200';
    }

    public function label(): string
    {
        return 'Create Work Order';
    }

    public function colorClass(): string
    {
        return 'has-orange';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            InProgress::$actionName,
            Closed::$actionName,
        ];
    }
}

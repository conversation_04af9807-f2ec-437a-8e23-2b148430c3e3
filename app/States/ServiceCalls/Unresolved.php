<?php

declare(strict_types=1);

namespace App\States\ServiceCalls;

class Unresolved extends ServiceCallState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'unresolved';

    public static string $actionName = 'unresolved';

    public function label(): string
    {
        return 'Unresolved';
    }

    public function labelForMobile(): string
    {
        return 'Unresolved';
    }

    public function colorClass(): string
    {
        return 'has-success-light';
    }
}

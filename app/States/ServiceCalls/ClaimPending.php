<?php

namespace App\States\ServiceCalls;

class ClaimPending extends ServiceCallState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'claim_pending';

    public static string $actionName = 'claim_pending';

    public function label(): string
    {
        return 'Claim Pending';
    }

    public function labelForMobile(): string
    {
        return 'Claim Pending';
    }

    public function colorClass(): string
    {
        return 'has-grey';
    }
}

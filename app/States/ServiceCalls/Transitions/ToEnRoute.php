<?php

namespace App\States\ServiceCalls\Transitions;

use App\Events\WorkOrder\Trip\TripEnRouteResumed;
use App\Events\WorkOrder\Trip\TripEnRouteStart;
use App\Exceptions\WorkOrderException;
use App\Models\User;
use App\Models\WorkOrderServiceCall;
use App\Services\ServiceCall\Trip\TripStateService;
use App\States\ServiceCalls\EnRoute;
use App\States\ServiceCalls\EnRoutePaused;
use Carbon\CarbonImmutable;
use Exception;
use Spatie\ModelStates\Transition;

class ToEnRoute extends Transition
{
    protected TripStateService $tripStateService;

    public function __construct(
        protected WorkOrderServiceCall $trip,
        public ?User $user = null,
        public bool $sendResidentNotification = false,
    ) {
        /** @var TripStateService $this tripStateService */
        $this->tripStateService = app(TripStateService::class);
    }

    /**
     * @throws WorkOrderException
     * @throws Exception
     */
    public function handle(): WorkOrderServiceCall
    {
        $oldState = $this->trip->state;

        $this->trip->state = new EnRoute($this->trip);
        $this->trip->state_updated_at = CarbonImmutable::now();
        $this->trip->last_modified_user = $this->user?->user_id;
        $this->trip->last_modified_at = CarbonImmutable::now();

        $eventAttribute = [
            'from' => $oldState->label(),
            'from_color_class' => $oldState->colorClass(),
            'to' => $this->trip->state->label(),
            'to_color_class' => $this->trip->state->colorClass(),
            'slug_of_to' => $this->trip->state->getValue(),
            'trip_number' => $this->trip->work_order_service_call_number,
            'trip_id' => $this->trip->work_order_service_call_uuid,
        ];

        // if the trip state en-route is paused, then we need to resume the drive time. otherwise, start en-route.
        if ($oldState->equals(EnRoutePaused::class)) {
            $this->trip->en_route_timer_resumed_at = CarbonImmutable::now();

            event(new TripEnRouteResumed(
                $this->trip->work_order_service_call_id,
                $this->user?->user_id,
                $eventAttribute,
            ));
        } else {
            $this->trip->en_route_at = CarbonImmutable::now();

            event(new TripEnRouteStart(
                $this->trip->work_order_service_call_id,
                $this->user?->user_id,
                $eventAttribute,
                $this->sendResidentNotification,
            ));
        }

        $this->trip->save();

        $this->tripStateService->updateTripState(
            serviceCallId: $this->trip->work_order_service_call_id,
            newState: $this->trip->state,
            additionalData: []
        );

        return $this->trip;
    }
}

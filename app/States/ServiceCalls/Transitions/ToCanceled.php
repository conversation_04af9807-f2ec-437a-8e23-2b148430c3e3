<?php

namespace App\States\ServiceCalls\Transitions;

use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderTask;
use App\States\ServiceCalls\Canceled;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToCanceled extends Transition
{
    public function __construct(
        protected WorkOrderServiceCall $workOrderServiceCall,
        public ?WorkOrder $workOrder = null,
        public ?WorkOrderTask $workOrderTask = null,
        public ?User $user = null,
    ) {}

    public function handle(): WorkOrderServiceCall
    {
        $this->workOrderServiceCall->state = new Canceled($this->workOrderServiceCall);
        $this->workOrderServiceCall->state_updated_at = CarbonImmutable::now();
        $this->workOrderServiceCall->last_modified_user = $this->user?->user_id;
        $this->workOrderServiceCall->last_modified_at = CarbonImmutable::now();
        $this->workOrderServiceCall->save();

        return $this->workOrderServiceCall;
    }
}

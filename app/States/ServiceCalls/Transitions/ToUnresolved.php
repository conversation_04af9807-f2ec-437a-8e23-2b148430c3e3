<?php

namespace App\States\ServiceCalls\Transitions;

use App\Models\User;
use App\Models\WorkOrderServiceCall;
use App\States\ServiceCalls\Unresolved;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToUnresolved extends Transition
{
    public function __construct(
        protected WorkOrderServiceCall $trip,
        public ?User $user = null,

    ) {}

    public function handle(): WorkOrderServiceCall
    {
        // Update the trip state to Unresolved
        $this->trip->state = new Unresolved($this->trip);
        $this->trip->last_modified_user = $this->user?->user_id;
        $this->trip->last_modified_at = CarbonImmutable::now();
        $this->trip->save();

        return $this->trip;
    }
}

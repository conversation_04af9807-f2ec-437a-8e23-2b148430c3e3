<?php

namespace App\States\ServiceCalls\Transitions;

use App\Enums\WorkToPerformTypes;
use App\Events\WorkOrder\Trip\TripReScheduled;
use App\Events\WorkOrder\WorkOrderServiceCallRescheduled;
use App\Models\Quote;
use App\Models\Technician as TechnicianModel;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\Services\Scheduling\Domain\Entities\Technician;
use App\States\ServiceCalls\ReScheduled;
use Carbon\CarbonImmutable;
use Exception;
use Spatie\ModelStates\Transition;

class ToReScheduled extends Transition
{
    public function __construct(
        protected WorkOrderServiceCall $workOrderServiceCall,
        public ?WorkOrder $workOrder = null,
        public ?User $user = null,
        public ?Technician $technician = null,
        public ?CarbonImmutable $scheduled_start_time = null,
        public ?CarbonImmutable $scheduled_end_time = null,
        protected ?string $mode = null,
        protected ?string $method = null,
        public ?string $notes = null,
        protected ?string $workToPerform = null,
        protected ?string $quoteId = null,
        protected ?string $nteAmount = null,
    ) {}

    public function handle(): WorkOrderServiceCall
    {
        $appointment = $this->workOrderServiceCall->appointment;

        if (! $appointment) {
            throw new Exception('Failed to register service call');
        }

        // Update the service call(Trip) and technician appointment when rescheduling.
        if (! empty($this->technician->id)) {
            $appointment->technician_id = $this->technician->id;
        }
        if ($this->scheduled_start_time) {
            $appointment->scheduled_start_time = $this->scheduled_start_time;
        }
        if ($this->scheduled_end_time) {
            $appointment->scheduled_end_time = $this->scheduled_end_time;
        }

        $appointment->rescheduled_reason = $this->notes ? trim($this->notes) : null;
        $appointment->save();

        $oldState = $this->workOrderServiceCall->state;
        $this->workOrderServiceCall->state = new ReScheduled($this->workOrderServiceCall);
        $this->workOrderServiceCall->scheduled_start_time = $appointment->scheduled_start_time;
        $this->workOrderServiceCall->scheduled_end_time = $appointment->scheduled_end_time;
        $this->workOrderServiceCall->last_modified_user = $this->user?->user_id;
        $this->workOrderServiceCall->last_modified_at = CarbonImmutable::now();
        $this->workOrderServiceCall->duration_minutes = $appointment->scheduled_start_time->diffInMinutes($appointment->scheduled_end_time);
        $this->workOrderServiceCall->mode = $this->mode;
        $this->workOrderServiceCall->method = $this->method;

        if (! empty($this->quoteId) && $this->workToPerform === WorkToPerformTypes::QUOTE_TASK()) {
            $quote = Quote::select('quote_id', 'last_assigned_service_call_id')
                ->whereUuid($this->quoteId)
                ->where('organization_id', $this->workOrder?->organization_id)
                ->first();

            if (empty($quote)) {
                throw new Exception(__('Link quote not found'));
            }

            $quote->last_assigned_service_call_id = $this->workOrderServiceCall->work_order_service_call_id;
            $quote->save();

            $this->workOrderServiceCall->quote_id = $quote->quote_id;
            $this->workOrderServiceCall->work_to_perform = WorkToPerformTypes::QUOTE_TASK();
        }

        if ($this->workToPerform === WorkToPerformTypes::HOURLY_TASK()) {
            $this->workOrderServiceCall->work_to_perform = WorkToPerformTypes::HOURLY_TASK();
            $this->workOrderServiceCall->quote_id = null;
        }

        $this->workOrderServiceCall->save();

        //update work order
        if (! empty($this->workOrder)) {
            $this->workOrder->state_updated_at = CarbonImmutable::now();
            $this->workOrder->nte_amount_in_cents = $this->nteAmount;
            $this->workOrder->save();
        }

        // Create service call activity log for this action
        $eventAttributes = [
            'from' => $oldState->label(),
            'from_color_class' => $oldState->colorClass(),
            'to' => $this->workOrderServiceCall->state->label(),
            'to_color_class' => $this->workOrderServiceCall->state->colorClass(),
            'slug_of_to' => $this->workOrderServiceCall->state->getValue(),
            'trip_number' => $this->workOrderServiceCall->work_order_service_call_number,
            'trip_id' => $this->workOrderServiceCall->work_order_service_call_uuid,
            'is_rescheduled' => true,
            'rescheduled_reason' => $appointment->rescheduled_reason,
            'appointment_detail' => [
                'technician_id' => $this->technician?->uuid,
                'technician' => $this->technician?->fullName,
                'technician_profile_pic' => $this->technician?->profilePic,
                'schedule_start_date' => $this->workOrderServiceCall->scheduled_start_time->toIso8601String(),
                'schedule_end_date' => $this->workOrderServiceCall->scheduled_end_time->toIso8601String(),
            ],
        ];

        if (! empty($this->workOrder) && ! empty($this->workOrderTask)) {
            event(new TripReScheduled(
                $this->workOrder->work_order_id,
                $eventAttributes,
                $this->user?->user_id
            ));
        }

        // Create technician notification
        $technicianUser = TechnicianModel::with('user')
            ->find($this->technician?->id);

        WorkOrderServiceCallRescheduled::dispatch($this->workOrder, $technicianUser->user, $this->user);

        return $this->workOrderServiceCall;
    }
}

<?php

namespace App\States\ServiceCalls\Transitions;

use App\Enums\ScheduleTypes;
use App\Enums\ServiceCallStatus;
use App\Enums\Trip;
use App\Events\WorkOrder\Trip\TripEnd;
use App\Exceptions\WorkOrderException;
use App\Models\User;
use App\Models\WorkOrderServiceCall;
use App\Services\ServiceCall\Trip\TripStateService;
use App\States\ServiceCalls\Ended;
use Carbon\CarbonImmutable;
use Exception;
use Spatie\ModelStates\Transition;

class ToEnded extends Transition
{
    protected TripStateService $tripServiceCall;

    /**
     * @param  array<string,string|int>  $requestPayload
     */
    public function __construct(
        protected WorkOrderServiceCall $trip,
        public ?array $requestPayload = [],
        public ?User $user = null,
    ) {
        $this->tripServiceCall = app(TripStateService::class);
    }

    /**
     * @throws WorkOrderException
     * @throws Exception
     */
    public function handle(): WorkOrderServiceCall
    {
        $oldState = $this->trip->state;

        //  State change
        $this->trip->state = new Ended($this->trip);
        $this->trip->state_updated_at = CarbonImmutable::now();
        $this->trip->last_modified_user = $this->user?->user_id;
        $this->trip->last_modified_at = CarbonImmutable::now();

        $this->trip->additional_notes = $this->requestPayload['additional_notes'] ?? null;
        $this->trip->status = ServiceCallStatus::COMPLETED();
        $this->trip->work_completed_at = CarbonImmutable::now();

        $currentElapseTimeInSec = $this->calculateWorkTime($this->trip);
        $this->trip->labor_time_in_sec = $currentElapseTimeInSec;
        $this->trip->adjusted_labor_time_in_sec = $this->requestPayload['total_work_time_in_sec'] ?? $currentElapseTimeInSec;

        if (isset($this->requestPayload['total_travel_time_in_sec'])) {
            $this->trip->adjusted_drive_time_in_sec = $this->requestPayload['total_travel_time_in_sec'];
        }

        $this->trip->timer_adjusted_reason = $this->requestPayload['time_adjusted_reason'] ?? null;
        $this->trip->save();

        $this->tripServiceCall->updateTripState(
            serviceCallId: $this->trip->work_order_service_call_id,
            newState: $this->trip->state,
            additionalData: []
        );

        $eventAttributes = [];
        if (! empty($this->trip->lula_appointment_id)) {
            $this->trip->load('lulaAppointment:lula_appointment_id,work_order_reference_number');
            // Trigger Trip End Event
            $eventAttributes = [
                'to' => $this->trip->state->label(),
                'to_color_class' => $this->trip->state->colorClass(),
                'slug_of_to' => $this->trip->state->getValue(),
                'trip_id' => $this->trip->work_order_service_call_uuid,
                'trip_type' => ScheduleTypes::LULA_PRO(),
                'wo_reference_number' => $this->trip->lulaAppointment->work_order_reference_number ?? null,
            ];
        } else {
            $eventAttributes = [
                'from' => $oldState->label(),
                'from_color_class' => $oldState->colorClass(),
                'to' => $this->trip->state->label(),
                'to_color_class' => $this->trip->state->colorClass(),
                'slug_of_to' => $this->trip->state->getValue(),
                'trip_number' => $this->trip->work_order_service_call_number,
                'trip_id' => $this->trip->work_order_service_call_uuid,
                'additional_notes' => $this->trip->additional_notes,
            ];

            // TO DO:add media and materials
        }

        event(new TripEnd(
            $this->trip->work_order_service_call_id,
            $this->user->user_id ?? null,
            $eventAttributes,
        ));

        return $this->trip;
    }

    public function calculateWorkTime(WorkOrderServiceCall $serviceCall): int
    {
        $startTime = $serviceCall->work_timer_resumed_at ?? $serviceCall->work_started_at;

        if (empty($startTime)) {
            throw WorkOrderException::missingStartTime();
        }

        $currentWorkTimeInSec = $startTime->diffInSeconds($serviceCall->work_completed_at);

        $previousWorkTime = $serviceCall->labor_time_in_sec ?? 0;

        return $previousWorkTime + $currentWorkTimeInSec;
    }
}

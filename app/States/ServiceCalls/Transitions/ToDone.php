<?php

namespace App\States\ServiceCalls\Transitions;

use App\Models\User;
use App\Models\WorkOrderServiceCall;
use App\States\ServiceCalls\Done;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToDone extends Transition
{
    public function __construct(
        protected WorkOrderServiceCall $trip,
        public ?User $user = null,

    ) {}

    public function handle(): WorkOrderServiceCall
    {
        // Update the trip state to Done
        $this->trip->state = new Done($this->trip);
        $this->trip->state_updated_at = CarbonImmutable::now();
        $this->trip->last_modified_user = $this->user?->user_id;
        $this->trip->last_modified_at = CarbonImmutable::now();
        $this->trip->save();

        return $this->trip;
    }
}

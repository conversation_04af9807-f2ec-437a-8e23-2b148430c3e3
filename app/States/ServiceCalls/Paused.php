<?php

namespace App\States\ServiceCalls;

class Paused extends ServiceCallState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'paused';

    public static string $actionName = 'pause_trip';

    public function label(): string
    {
        return 'Timer Paused';
    }

    public function labelForMobile(): string
    {
        return 'Timer Stopped';
    }

    public function colorClass(): string
    {
        return 'has-light-grey';
    }
}

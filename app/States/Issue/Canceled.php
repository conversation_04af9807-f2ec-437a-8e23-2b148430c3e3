<?php

declare(strict_types=1);

namespace App\States\Issue;

use App\Enums\IssueActions;

class Canceled extends IssueState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'canceled';

    public static string $actionName = 'canceled';

    public function label(): string
    {
        return 'Canceled';
    }

    /**
     * @return array<int, string>
     */
    public function actions(): array
    {
        return [
            IssueActions::EDIT(),
            IssueActions::DELETE(),
            IssueActions::RESTORE(),
        ];
    }
}

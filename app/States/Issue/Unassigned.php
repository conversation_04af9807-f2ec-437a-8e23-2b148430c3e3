<?php

declare(strict_types=1);

namespace App\States\Issue;

use App\Enums\IssueActions;

class Unassigned extends IssueState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'unassigned';

    public static string $actionName = 'unassigned';

    public function label(): string
    {
        return 'Unassigned';
    }

    /**
     * @return array<int, string>
     */
    public function actions(): array
    {
        return [
            IssueActions::EDIT(),
            IssueActions::ASSIGN(),
            IssueActions::DELETE(),
            IssueActions::CANCEL(),
            IssueActions::ADD_NEW_WORK_ORDER(),
        ];
    }
}

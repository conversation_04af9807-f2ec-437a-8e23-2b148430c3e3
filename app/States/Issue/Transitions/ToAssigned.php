<?php

namespace App\States\Issue\Transitions;

use App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueAssigned;
use App\Events\WorkOrder\Issue\WorkOrderIssueAssigned;
use App\Models\Issue;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\States\Issue\Assigned;
use App\States\WorkOrderIssue\Assigned as WorkOrderIssuesAssigned;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToAssigned extends Transition
{
    public function __construct(
        protected Issue $issue,
        public ?WorkOrder $workOrder = null,
        public ?User $user = null,
    ) {}

    public function handle(): Issue
    {
        $this->issue->state = new Assigned($this->issue);
        $this->issue->state_updated_at = CarbonImmutable::now();
        $this->issue->save();

        $workOrderIssue = WorkOrderIssue::create([
            'state' => WorkOrderIssuesAssigned::class,
            'work_order_id' => $this->workOrder->work_order_id,
            'issue_id' => $this->issue->issue_id,
            'organization_id' => $this->issue->organization_id,
            'state_updated_at' => CarbonImmutable::now(),
        ]);

        ServiceRequestWorkOrderIssueAssigned::broadcast(
            $workOrderIssue->work_order_id,
            $this->user?->user_id,
            [
                'title' => $this->issue->title,
                'work_order_number' => $this->workOrder?->work_order_number,
            ]
        )->toOthers();
        WorkOrderIssueAssigned::broadcast($workOrderIssue->work_order_issue_id)->toOthers();

        return $this->issue;
    }
}

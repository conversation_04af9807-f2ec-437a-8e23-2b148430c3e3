<?php

namespace App\States\Issue\Transitions;

use App\Models\Issue;
use App\States\Issue\Unresolved;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToUnresolved extends Transition
{
    public function __construct(
        public Issue $issue
    ) {}

    public function handle(): Issue
    {
        $this->issue->state = new Unresolved($this->issue);
        $this->issue->state_updated_at = CarbonImmutable::now();
        $this->issue->save();

        return $this->issue;
    }
}

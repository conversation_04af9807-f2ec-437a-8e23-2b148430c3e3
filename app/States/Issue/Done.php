<?php

declare(strict_types=1);

namespace App\States\Issue;

use App\Enums\IssueActions;

class Done extends IssueState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'done';

    public static string $actionName = 'done';

    public function label(): string
    {
        return 'Done';
    }

    /**
     * @return array<int, string>
     */
    public function actions(): array
    {
        return [
            IssueActions::EDIT(),
        ];
    }
}

<?php

namespace App\States\Invoices;

use App\Models\Invoice;
use App\States\Invoices\Transitions\ToPaid;
use App\States\Invoices\Transitions\ToPartiallyPaid;
use App\States\Invoices\Transitions\ToPaymentPending;
use App\States\Invoices\Transitions\ToVoided;
use Spatie\ModelStates\Exceptions\InvalidConfig;
use Spatie\ModelStates\State;
use Spatie\ModelStates\StateConfig;

/**
 * @extends State<Invoice>
 */
abstract class InvoiceState extends State
{
    /**
     * @throws InvalidConfig
     */
    public static function config(): StateConfig
    {
        return parent::config()
            ->default(Draft::class)
            ->allowTransition(
                from: [Draft::class],
                to: PaymentPending::class,
                transition: ToPaymentPending::class,
            )->allowTransition(
                from: [PaymentPending::class, PartiallyPaid::class],
                to: Paid::class,
                transition: ToPaid::class,
            )
            ->allowTransition(
                from: [PaymentPending::class],
                to: PartiallyPaid::class,
                transition: ToPartiallyPaid::class,
            )
            ->allowTransition(
                from: [PaymentPending::class, PartiallyPaid::class, Paid::class],
                to: Voided::class,
                transition: ToVoided::class,
            );
    }

    abstract public function color(): string;

    abstract public function colorClass(): string;

    abstract public function label(): string;

    /**
     * @return array<int, string|null>
     */
    abstract public function actions(): array;
}

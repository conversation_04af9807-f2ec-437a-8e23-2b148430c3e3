<?php

namespace App\States\Invoices;

class PartiallyPaid extends InvoiceState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'partially_paid';

    public static string $actionName = 'partially_paid';

    public function color(): string
    {
        return '#aj981j';
    }

    public function label(): string
    {
        return 'Partially Paid';
    }

    public function colorClass(): string
    {
        return 'partially-paid';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            Paid::$actionName,
        ];
    }
}

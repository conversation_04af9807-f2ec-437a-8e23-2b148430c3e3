<?php

namespace App\States\Invoices\Transitions;

use App\Models\Invoice;
use App\Models\User;
use App\Models\WorkOrder;
use App\States\Invoices\PartiallyPaid;
use Carbon\CarbonImmutable;
use Spatie\ModelStates\Transition;

class ToPartiallyPaid extends Transition
{
    public function __construct(protected Invoice $invoice, public ?WorkOrder $workOrder = null, public ?User $user = null) {}

    public function handle(): Invoice
    {
        // Change state
        $this->invoice->state = new PartiallyPaid($this->invoice);
        $this->invoice->state_updated_at = CarbonImmutable::now();
        $this->invoice->save();

        return $this->invoice;
    }
}

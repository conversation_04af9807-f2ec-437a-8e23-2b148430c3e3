<?php

namespace App\States\Invoices;

class PaymentPending extends InvoiceState
{
    // SPATIE: Do not use a hyphen(-) as this will conflict with internal naming conventions.
    public static string $name = 'payment_pending';

    public static string $actionName = 'payment_pending';

    public function color(): string
    {
        return '#aj981j';
    }

    public function label(): string
    {
        return 'Pending Payment';
    }

    public function colorClass(): string
    {
        return 'pending';
    }

    /**
     * @return array<int, string|null>
     */
    public function actions(): array
    {
        return [
            Voided::$actionName,
            Paid::$actionName,
            PartiallyPaid::$actionName,
        ];
    }
}

<?php

namespace App\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Ramsey\Uuid\Uuid;
use <PERSON>\Uuid\UuidInterface;

/**
 * @implements CastsAttributes<string, UuidInterface>
 */
class UuidBinary implements CastsAttributes
{
    /**
     * Transform the attribute from the underlying model values.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @param  mixed  $value
     * @param  array<mixed>  $attributes
     */
    public function get($model, string $key, $value, array $attributes): ?string
    {
        if (blank($value)) {
            return null;
        }

        return Uuid::fromBytes($value)->toString();
    }

    /**
     * Transform the attribute to its underlying model values.
     *
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @param  mixed  $value
     * @param  array<mixed>  $attributes
     * @return array<mixed>
     */
    public function set($model, string $key, $value, array $attributes): ?array
    {
        if (blank($value)) {
            return null;
        }

        return [
            $key => Uuid::fromString(strtolower($value))->getBytes(),
        ];
    }
}

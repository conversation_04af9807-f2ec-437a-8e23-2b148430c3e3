<?php

namespace App\Listeners;

use App\Events\ServiceRequest\ResidentAvailabilityBroadcast;
use App\Events\WorkOrder\ResidentAvailabilityBroadcast as WorkOrderBroadCast;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestActivityLog;
use App\Models\WorkOrder;
use App\Models\WorkOrderActivityLog;
use App\Notifications\Resident\ServiceRequest\ResidentAvailabilityNotification;
use App\Services\ServiceRequestActivity\Enums\ActivityLogEventTypes;
use App\Services\WorkOrderActivity\Enums\ActivityLogEventTypes as WorkOrderActivityLogEventTypes;
use Illuminate\Notifications\Events\NotificationSent;

class NotificationSentListener
{
    /**
     * Handle the event.
     */
    public function handle(NotificationSent $event): void
    {
        if ($event->notification instanceof ResidentAvailabilityNotification) {

            if (! empty($event->notification->serviceRequestId)) {
                /** @var ServiceRequest|null $serviceRequest */
                $serviceRequest = ServiceRequest::select(['service_request_id', 'service_request_uuid', 'organization_id'])
                    ->with('organization:organization_id,organization_uuid')
                    ->find($event->notification->serviceRequestId);

                if ($serviceRequest) {
                    $serviceRequestActivityLog = ServiceRequestActivityLog::create([
                        'service_request_id' => $serviceRequest->service_request_id,
                        'organization_id' => $serviceRequest->organization_id,
                        'triggered_by' => $event->notification->userId ?? null,
                        'event' => ActivityLogEventTypes::SERVICE_REQUEST_AVAILABILITY_REQUESTED(),
                        'event_attributes' => [
                            'message' => $event->notification->message ?? null,
                            'url' => $event->notification->url ?? null,
                        ],
                    ]);
                    $workOrders = WorkOrder::where('service_request_id', $serviceRequest->service_request_id)->get();

                    foreach ($workOrders as $workOrder) {
                        $workOrder->load('organization:organization_id,organization_uuid');
                        $activityLog = WorkOrderActivityLog::create([
                            'work_order_id' => $workOrder->work_order_id,
                            'organization_id' => $workOrder->organization_id,
                            'triggered_by' => $event->notification->userId ?? null,
                            'event' => WorkOrderActivityLogEventTypes::WORK_ORDER_AVAILABILITY_REQUESTED(),
                            'event_attributes' => [
                                'message' => $event->notification->message ?? null,
                                'url' => $event->notification->url ?? null,
                            ],
                        ]);
                        event(new WorkOrderBroadCast($workOrder, $activityLog->work_order_activity_log_id, WorkOrderActivityLogEventTypes::WORK_ORDER_AVAILABILITY_REQUESTED()));

                    }
                    event(new ResidentAvailabilityBroadcast($serviceRequest, $serviceRequestActivityLog->service_request_activity_log_id, ActivityLogEventTypes::SERVICE_REQUEST_AVAILABILITY_ADDED()));
                }
            }
        }
    }
}

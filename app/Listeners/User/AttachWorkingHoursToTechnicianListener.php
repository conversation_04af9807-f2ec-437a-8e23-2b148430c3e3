<?php

namespace App\Listeners\User;

use App\Enums\UserTypes;
use App\Events\User\UserInvited;
use App\Models\Technician;
use App\Models\TechnicianWorkingHour;
use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class AttachWorkingHoursToTechnicianListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserInvited $event): void
    {
        Log::info('Working Hours listener');
        $user = $event->user;

        /** @var Technician|null $technician */
        $technician = $user->user_type === UserTypes::TECHNICIAN() ? $user->technician : null;
        Log::info('Working Hours listener started', [
            'user_id' => $user->user_id,
            'technician' => $technician?->technician_id,
        ]);
        if (! empty($technician)) {
            /** @var array<string, mixed> $defaultWorkingHours */
            $defaultWorkingHours = config('technicians.workingHours');
            /** @var Collection<string, mixed> $defaultWorkingHourCollections */
            $defaultWorkingHourCollections = collect($defaultWorkingHours);

            // week day working hours
            $defaultWeekDayWorkingHours = $defaultWorkingHourCollections->except('lunch_time_break');

            // Lunch time break
            $defaultLunchBreak = $defaultWorkingHourCollections->only('lunch_time_break');

            //Check if already working hours exists.
            $technicianWorkingHours = TechnicianWorkingHour::where('technician_id', $technician->technician_id)
                ->where('organization_id', $technician->organization_id)
                ->count();

            if (! $technicianWorkingHours) {
                //If no add default working hours
                foreach ($defaultWeekDayWorkingHours as $key => $defaultWorkingHour) {

                    $userWorkingHour = new TechnicianWorkingHour;

                    $userWorkingHour->technician_id = $technician->technician_id;
                    $userWorkingHour->organization_id = $technician->organization_id;
                    $userWorkingHour->weekday = $key;

                    // Convert to 24-hour format
                    $workStartTime = CarbonImmutable::parse($defaultWorkingHour['from']);
                    $workEndTime = CarbonImmutable::parse($defaultWorkingHour['to']);

                    $lunchStartTime = CarbonImmutable::parse($defaultLunchBreak['lunch_time_break']['from']);
                    $lunchEndTime = CarbonImmutable::parse($defaultLunchBreak['lunch_time_break']['to']);
                    $lunchDuration = $defaultLunchBreak['lunch_time_break']['duration'];

                    $userWorkingHour->work_start_at = $workStartTime;
                    $userWorkingHour->work_end_at = $workEndTime;
                    $userWorkingHour->lunch_break_slot_start = $lunchStartTime;
                    $userWorkingHour->lunch_break_slot_end = $lunchEndTime;
                    $userWorkingHour->lunch_break_duration_in_minutes = $lunchDuration;
                    $userWorkingHour->is_enabled = empty($defaultWorkingHour['isEnabled']) ? 'no' : 'yes';

                    $userWorkingHour->save();
                }
            }
        }
    }
}

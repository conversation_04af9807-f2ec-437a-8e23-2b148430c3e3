<?php

namespace App\Listeners\User;

use App\Enums\QuoteStatus;
use App\Enums\ViewTypes;
use App\Events\User\DefaultPinnedQuotesView;
use App\Models\UserPinnedView;
use App\Models\View;
use App\Models\ViewType;

class PinnedQuotesViewListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(DefaultPinnedQuotesView $event): void
    {
        $user = $event->user;

        $viewType = ViewType::where('slug', ViewTypes::QUOTES)->first();

        if (! empty($viewType)) {
            $quoteSubMenus = [
                [
                    'scope' => 'individual',
                    'payload' => json_decode('{"columns": [{"label": "Category", "value": "category", "selected": true, "isEditable": false, "sub_fields": [{"label": "Property Address", "value": "property_address", "selected": true, "isEditable": true}], "isStaticColumn": true}, {"label": "Parent WO ID", "value": "work_order_number", "selected": true, "isEditable": false, "isStaticColumn": true}, {"label": "Status", "value": "status", "selected": true, "isEditable": false, "isStaticColumn": true}, {"label": "Total", "value": "total", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Approved Total", "value": "total_approved", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Submitted By", "value": "submitted_by", "selected": true, "isEditable": true, "sub_fields": [{"label": "Submitted Date", "value": "submitted_date", "selected": true, "isEditable": true}], "isStaticColumn": false}, {"label": "Assignee", "value": "assignee", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Approved / Rejected By", "value": "approved_by", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "WO Tags", "value": "tag", "selected": true, "isEditable": true, "isStaticColumn": false}], "filters": {"fields": [{"ops": ["is", "is_not"], "label": "Status", "value": "status", "values_type": {"is": "multi-select", "is_not": "multi-select"}}, {"ops": ["is"], "label": "Category", "value": "category", "values_type": {"is": "multi-select"}}, {"ops": ["is"], "label": "Assignee", "value": "assignee", "values_type": {"is": "multi-select"}}, {"ops": ["is", "is_not"], "label": "Submitted By", "value": "submitted_by", "values_type": {"is": "multi-select"}}, {"ops": ["is", "is_not"], "label": "Submitted Date", "value": "submitted_date", "values_type": {"is": "multi-select"}}, {"ops": ["is"], "label": "Tags", "value": "tag", "values_type": {"is": "multi-select"}}], "applied": {"fl_group": [{"field": {"label": "Status", "value": "status"}, "value": [{"label": "Pending Approval", "value": "pending-approval"}], "operation": {"label": "is", "value": "is"}}], "group_op": null}, "operators": {"is": {"label": "is", "value": "is"}, "is_not": {"label": "Is not", "value": "is_not"}, "is_after": {"label": "Is after", "value": "is_after"}, "is_before": {"label": "Is before", "value": "is_before"}, "is_between": {"label": "Is between", "value": "is_between"}}}, "grouping": {"g_by": {"label": "None", "value": "none"}, "default": "none", "g_options": [{"label": "None", "value": "none"}, {"label": "WO", "value": "work_order_number"}, {"label": "Status", "value": "status"}, {"label": "Category", "value": "category"}, {"label": "Assignee", "value": "assignee"}, {"label": "Submitted By", "value": "submitted_by"}, {"label": "Tags", "value": "tag"}]}}'),
                    'name' => QuoteStatus::label(QuoteStatus::PENDING_APPROVAL(), 'web'),
                    'view_type_id' => $viewType->view_type_id,
                    'user_id' => $user->user_id,
                    'organization_id' => $user->organization_id,
                ],
                [
                    'scope' => 'individual',
                    'payload' => json_decode('{"columns": [{"label": "Category", "value": "category", "selected": true, "isEditable": false, "sub_fields": [{"label": "Property Address", "value": "property_address", "selected": true, "isEditable": true}], "isStaticColumn": true}, {"label": "Parent WO ID", "value": "work_order_number", "selected": true, "isEditable": false, "isStaticColumn": true}, {"label": "Status", "value": "status", "selected": true, "isEditable": false, "isStaticColumn": true}, {"label": "Total", "value": "total", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Approved Total", "value": "total_approved", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Submitted By", "value": "submitted_by", "selected": true, "isEditable": true, "sub_fields": [{"label": "Submitted Date", "value": "submitted_date", "selected": true, "isEditable": true}], "isStaticColumn": false}, {"label": "Assignee", "value": "assignee", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Approved / Rejected By", "value": "approved_by", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "WO Tags", "value": "tag", "selected": true, "isEditable": true, "isStaticColumn": false}], "filters": {"fields": [{"ops": ["is", "is_not"], "label": "Status", "value": "status", "values_type": {"is": "multi-select", "is_not": "multi-select"}}, {"ops": ["is"], "label": "Category", "value": "category", "values_type": {"is": "multi-select"}}, {"ops": ["is"], "label": "Assignee", "value": "assignee", "values_type": {"is": "multi-select"}}, {"ops": ["is", "is_not"], "label": "Submitted By", "value": "submitted_by", "values_type": {"is": "multi-select"}}, {"ops": ["is", "is_not"], "label": "Submitted Date", "value": "submitted_date", "values_type": {"is": "multi-select"}}, {"ops": ["is"], "label": "Tags", "value": "tag", "values_type": {"is": "multi-select"}}], "applied": {"fl_group": [{"field": {"label": "Status", "value": "status"}, "value": [{"label": "Pending Review", "value": "quote-pending-review"}], "operation": {"label": "is", "value": "is"}}], "group_op": null}, "operators": {"is": {"label": "is", "value": "is"}, "is_not": {"label": "Is not", "value": "is_not"}, "is_after": {"label": "Is after", "value": "is_after"}, "is_before": {"label": "Is before", "value": "is_before"}, "is_between": {"label": "Is between", "value": "is_between"}}}, "grouping": {"g_by": {"label": "None", "value": "none"}, "default": "none", "g_options": [{"label": "None", "value": "none"}, {"label": "WO", "value": "work_order_number"}, {"label": "Status", "value": "status"}, {"label": "Category", "value": "category"}, {"label": "Assignee", "value": "assignee"}, {"label": "Submitted By", "value": "submitted_by"}, {"label": "Tags", "value": "tag"}]}}'),
                    'name' => QuoteStatus::label(QuoteStatus::QUOTE_PENDING_REVIEW(), 'web'),
                    'view_type_id' => $viewType->view_type_id,
                    'user_id' => $user->user_id,
                    'organization_id' => $user->organization_id,
                ],
                [
                    'scope' => 'individual',
                    'payload' => json_decode('{"columns": [{"label": "Category", "value": "category", "selected": true, "isEditable": false, "sub_fields": [{"label": "Property Address", "value": "property_address", "selected": true, "isEditable": true}], "isStaticColumn": true}, {"label": "Parent WO ID", "value": "work_order_number", "selected": true, "isEditable": false, "isStaticColumn": true}, {"label": "Status", "value": "status", "selected": true, "isEditable": false, "isStaticColumn": true}, {"label": "Total", "value": "total", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Approved Total", "value": "total_approved", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Submitted By", "value": "submitted_by", "selected": true, "isEditable": true, "sub_fields": [{"label": "Submitted Date", "value": "submitted_date", "selected": true, "isEditable": true}], "isStaticColumn": false}, {"label": "Assignee", "value": "assignee", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Approved / Rejected By", "value": "approved_by", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "WO Tags", "value": "tag", "selected": true, "isEditable": true, "isStaticColumn": false}], "filters": {"fields": [{"ops": ["is", "is_not"], "label": "Status", "value": "status", "values_type": {"is": "multi-select", "is_not": "multi-select"}}, {"ops": ["is"], "label": "Category", "value": "category", "values_type": {"is": "multi-select"}}, {"ops": ["is"], "label": "Assignee", "value": "assignee", "values_type": {"is": "multi-select"}}, {"ops": ["is", "is_not"], "label": "Submitted By", "value": "submitted_by", "values_type": {"is": "multi-select"}}, {"ops": ["is", "is_not"], "label": "Submitted Date", "value": "submitted_date", "values_type": {"is": "multi-select"}}, {"ops": ["is"], "label": "Tags", "value": "tag", "values_type": {"is": "multi-select"}}], "applied": {"fl_group": [{"field": {"label": "Status", "value": "status"}, "value": [{"label": "Approved", "value": "approved"}], "operation": {"label": "is", "value": "is"}}], "group_op": null}, "operators": {"is": {"label": "is", "value": "is"}, "is_not": {"label": "Is not", "value": "is_not"}, "is_after": {"label": "Is after", "value": "is_after"}, "is_before": {"label": "Is before", "value": "is_before"}, "is_between": {"label": "Is between", "value": "is_between"}}}, "grouping": {"g_by": {"label": "None", "value": "none"}, "default": "none", "g_options": [{"label": "None", "value": "none"}, {"label": "WO", "value": "work_order_number"}, {"label": "Status", "value": "status"}, {"label": "Category", "value": "category"}, {"label": "Assignee", "value": "assignee"}, {"label": "Submitted By", "value": "submitted_by"}, {"label": "Tags", "value": "tag"}]}}'),
                    'name' => QuoteStatus::label(QuoteStatus::APPROVED(), 'web'),
                    'view_type_id' => $viewType->view_type_id,
                    'user_id' => $user->user_id,
                    'organization_id' => $user->organization_id,
                ],
                [
                    'scope' => 'individual',
                    'payload' => json_decode('{"columns": [{"label": "Category", "value": "category", "selected": true, "isEditable": false, "sub_fields": [{"label": "Property Address", "value": "property_address", "selected": true, "isEditable": true}], "isStaticColumn": true}, {"label": "Parent WO ID", "value": "work_order_number", "selected": true, "isEditable": false, "isStaticColumn": true}, {"label": "Status", "value": "status", "selected": true, "isEditable": false, "isStaticColumn": true}, {"label": "Total", "value": "total", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Approved Total", "value": "total_approved", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Submitted By", "value": "submitted_by", "selected": true, "isEditable": true, "sub_fields": [{"label": "Submitted Date", "value": "submitted_date", "selected": true, "isEditable": true}], "isStaticColumn": false}, {"label": "Assignee", "value": "assignee", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "Approved / Rejected By", "value": "approved_by", "selected": true, "isEditable": true, "isStaticColumn": false}, {"label": "WO Tags", "value": "tag", "selected": true, "isEditable": true, "isStaticColumn": false}], "filters": {"fields": [{"ops": ["is", "is_not"], "label": "Status", "value": "status", "values_type": {"is": "multi-select", "is_not": "multi-select"}}, {"ops": ["is"], "label": "Category", "value": "category", "values_type": {"is": "multi-select"}}, {"ops": ["is"], "label": "Assignee", "value": "assignee", "values_type": {"is": "multi-select"}}, {"ops": ["is", "is_not"], "label": "Submitted By", "value": "submitted_by", "values_type": {"is": "multi-select"}}, {"ops": ["is", "is_not"], "label": "Submitted Date", "value": "submitted_date", "values_type": {"is": "multi-select"}}, {"ops": ["is"], "label": "Tags", "value": "tag", "values_type": {"is": "multi-select"}}], "applied": {"fl_group": [{"field": {"label": "Status", "value": "status"}, "value": [{"label": "Rejected", "value": "rejected"}], "operation": {"label": "is", "value": "is"}}], "group_op": null}, "operators": {"is": {"label": "is", "value": "is"}, "is_not": {"label": "Is not", "value": "is_not"}, "is_after": {"label": "Is after", "value": "is_after"}, "is_before": {"label": "Is before", "value": "is_before"}, "is_between": {"label": "Is between", "value": "is_between"}}}, "grouping": {"g_by": {"label": "None", "value": "none"}, "default": "none", "g_options": [{"label": "None", "value": "none"}, {"label": "WO", "value": "work_order_number"}, {"label": "Status", "value": "status"}, {"label": "Category", "value": "category"}, {"label": "Assignee", "value": "assignee"}, {"label": "Submitted By", "value": "submitted_by"}, {"label": "Tags", "value": "tag"}]}}'),
                    'name' => QuoteStatus::label(QuoteStatus::REJECTED(), 'web'),
                    'view_type_id' => $viewType->view_type_id,
                    'user_id' => $user->user_id,
                    'organization_id' => $user->organization_id,
                ],
            ];

            foreach ($quoteSubMenus as $quoteSubMenu) {
                $view = View::updateOrCreate([
                    'name' => $quoteSubMenu['name'],
                    'user_id' => $user->user_id,
                    'organization_id' => $user->organization_id,
                ], $quoteSubMenu);

                UserPinnedView::updateOrCreate([
                    'user_id' => $user->user_id,
                    'view_id' => $view->view_id,
                ]);
            }
        }
    }
}

<?php

namespace App\Listeners\WorkOrder\Trip;

use App\Events\WorkOrder\ActivityLog\WorkOrderActivityLogCreated;
use App\Events\WorkOrder\Trip\TripEnRouteStart;
use App\Models\WorkOrder;
use App\Models\WorkOrderActivityLog;
use App\Models\WorkOrderServiceCall;
use App\Notifications\Resident\WorkOrder\EnRouteNotification;
use App\Services\WorkOrderActivity\Enums\ActivityLogEventTypes;
use App\Traits\BasicNotificationTrait;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Notification;

class TripEnRouteStartListener implements ShouldHandleEventsAfterCommit, ShouldQueue
{
    use BasicNotificationTrait;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TripEnRouteStart $event): void
    {
        $trip = WorkOrderServiceCall::select('work_order_service_call_id', 'work_order_id', 'organization_id')
            ->where('work_order_service_call_id', $event->tripId)
            ->first();

        $workOrderActivityLog = WorkOrderActivityLog::create([
            'work_order_id' => $trip->work_order_id,
            'organization_id' => $trip->organization_id,
            'triggered_by' => $event->userId ?? null,
            'event' => ActivityLogEventTypes::WORK_ORDER_SERVICE_CALL_EN_ROUTE_INITIATED(),
            'event_attributes' => $event->eventAttributes,
        ]);

        WorkOrderActivityLogCreated::broadcast($workOrderActivityLog->work_order_activity_log_id);

        if (! empty($event->sendResidentNotification)) {
            $workOrder = WorkOrder::with([
                'serviceRequest:service_request_id,requesting_resident_id',
                'serviceRequest.resident:resident_id,resident_uuid,organization_id,first_name,last_name,email,phone_number',
            ])
                ->where('work_order_id', $trip->work_order_id)
                ->select('work_order_id', 'service_request_id')
                ->first();

            // Work order schedules resident notification
            if (! empty($workOrder->serviceRequest->resident->phone_number)) {
                $workOrder->serviceRequest->resident->notify(new EnRouteNotification($workOrder->work_order_id));
            }
        }
    }
}

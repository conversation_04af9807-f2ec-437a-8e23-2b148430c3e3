<?php

namespace App\Listeners\WorkOrder;

use App\Events\WorkOrder\WorkOrderResidentUpdated;
use App\Events\WorkOrder\WorkOrderUpdate;
use App\Jobs\UpdateExternalWorkOrderDetails;
use App\Models\WorkOrder;
use App\States\WorkOrders\ClaimPending;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;

class WorkOrderResidentUpdatedListener implements ShouldHandleEventsAfterCommit, ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(WorkOrderResidentUpdated $event): void
    {
        $workOrder = WorkOrder::with([
            'organization' => function ($query) {
                return $query->select(
                    'organization_id', 'organization_uuid'
                );
            },
        ])
            ->select(
                'work_order_id', 'work_order_uuid', 'organization_id', 'vendor_id', 'state',
                'vendor_work_order_id'
            )
            ->where('work_order_id', $event->workOrderId)
            ->firstOrFail();

        WorkOrderUpdate::dispatch($workOrder->work_order_uuid);

        // if the work order send to vendor then cancel it
        if (
            ! empty($workOrder->vendor_id) &&
            ! empty($workOrder->vendor_work_order_id) &&
            $workOrder->state->equals(ClaimPending::class)
        ) {
            UpdateExternalWorkOrderDetails::dispatch($workOrder->work_order_uuid, 'resident_info');
        }
    }
}

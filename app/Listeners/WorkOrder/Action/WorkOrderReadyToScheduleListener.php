<?php

namespace App\Listeners\WorkOrder\Action;

use App\Enums\WorkOrderSourceTypes;
use App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule;
use App\Events\WorkOrder\ActivityLog\WorkOrderActivityLogCreated;
use App\Events\WorkOrder\ExternalWorkOrderUpdateEvent;
use App\Models\WorkOrder;
use App\Models\WorkOrderActivityLog;
use App\Models\WorkOrderStatusLog;
use App\Notifications\WorkOrder\WorkOrderStateChangedNotification;
use App\Services\WorkOrderActivity\Enums\ActivityLogEventTypes;
use App\Traits\BasicNotificationTrait;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Notification;

class WorkOrderReadyToScheduleListener implements ShouldHandleEventsAfterCommit, ShouldQueue
{
    use BasicNotificationTrait;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(WorkOrderReadyToSchedule $event): void
    {
        $workOrder = WorkOrder::with([
            'resident:resident_id,resident_uuid,property_id,first_name,last_name,email,phone_number,company_name',
            'workOrderSource:work_order_source_id,slug',
            'status:work_order_status_id,label,slug',
            'organization' => function ($query) {
                return $query->select([
                    'organization_id', 'organization_uuid', 'is_appfolio_enabled', 'appfolio_vendor_id',
                    'name', 'appfolio_client_id', 'appfolio_client_secret', 'appfolio_customer_id', 'domain',
                    'appfolio_integrated_at', 'webhook_enabled', 'webhook_api_url', 'webhook_secret_key',
                ]);
            },
        ])
            ->select([
                'work_order_id', 'work_order_uuid', 'organization_id', 'vendor_id', 'state',
                'work_order_status_id', 'property_id', 'timezone_id', 'work_order_source_id',
                'canceled_at', 'canceled_reason', 'work_order_reference_id', 'work_order_reference_number',
                'requesting_resident_id', 'work_order_number',
            ])
            ->where('work_order_id', $event->workOrderId)
            ->firstOrFail();

        WorkOrderStatusLog::create([
            'work_order_id' => $workOrder->work_order_id,
            'updated_by_user_id' => $event->userId ?? null,
            'work_order_status_id' => $workOrder->work_order_status_id,
        ]);

        $workOrderActivityLog = WorkOrderActivityLog::create([
            'work_order_id' => $event->workOrderId,
            'organization_id' => $workOrder->organization_id,
            'triggered_by' => $event->userId ?? null,
            'event' => ActivityLogEventTypes::WORK_ORDER_STATUS_CHANGED(),
            'event_attributes' => $event->activityLogEventAttributes,
        ]);

        WorkOrderActivityLogCreated::broadcast($workOrderActivityLog->work_order_activity_log_id);

        // Trigger work order state change notification
        $assignees = $this->getAssigneesToNotify($workOrder, $event->userId);
        if (! empty($assignees)) {
            Notification::send($assignees, new WorkOrderStateChangedNotification($workOrder->work_order_id, $workOrderActivityLog->work_order_activity_log_id));
        }

        // Check the work order source and trigger nessasery event
        if (! empty($workOrder->workOrderSource->slug)) {
            if ($workOrder->workOrderSource->slug === WorkOrderSourceTypes::APPFOLIO()) {
                event(new ExternalWorkOrderUpdateEvent($workOrder, 'Assigned'));
            }
        }

    }
}

<?php

namespace App\Listeners\WorkOrder;

use App\Enums\WorkOrderSourceTypes;
use App\Events\WorkOrder\ExternalWorkOrderUpdateEvent;
use App\Jobs\Appfolio\UpdateStatusInAppfolioWorkOrder;

class ExternalWorkOrderUpdateListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ExternalWorkOrderUpdateEvent $event): void
    {
        if (empty($event->status)) {
            return;
        }

        $workOrder = $event->workOrder;
        $workOrder->loadMissing(['workOrderSource:work_order_source_id,slug', 'organization']);
        $additionalData = $event->additionalData;

        if (! empty($workOrder->workOrderSource) && $workOrder->workOrderSource->slug === WorkOrderSourceTypes::APPFOLIO()) {
            $payload = $additionalData = [];
            if ($event->status == 'Assigned') {
                $payload = [
                    'Status' => 'Assigned',
                    'VendorId' => $workOrder->organization->appfolio_vendor_id,
                ];
            } elseif ($event->status == 'Scheduled') {
                $payload = [
                    'Status' => 'Scheduled',
                    'ScheduledStart' => $event->additionalData['ScheduledStart'] ?? null,
                    'ScheduledEnd' => $event->additionalData['ScheduledEnd'] ?? null,
                ];
            } elseif ($event->status == 'Waiting') {
                $payload = [
                    'Status' => 'Waiting',
                ];
                $additionalData = [
                    'pausedReason' => $workOrder->paused_reason ?? '',
                ];
            } elseif ($event->status == 'Work Completed') {
                $payload = [
                    'Status' => 'Work Completed',
                    'WorkCompletedOn' => $workOrder->work_completed_at,
                ];
            } elseif ($event->status == 'Completed') {
                $payload = [
                    'Status' => 'Completed',
                    'CompletedOn' => $workOrder->resolved_at,
                ];
            } elseif ($event->status == 'Canceled') {
                $payload = [
                    'Status' => 'Canceled',
                    'CanceledOn' => $workOrder->canceled_at,
                ];
            }
            dispatch(new UpdateStatusInAppfolioWorkOrder($workOrder, $payload, $additionalData));
        }
    }
}

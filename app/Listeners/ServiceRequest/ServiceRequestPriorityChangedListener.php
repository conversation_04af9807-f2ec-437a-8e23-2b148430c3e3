<?php

declare(strict_types=1);

namespace App\Listeners\ServiceRequest;

use App\Enums\Priority;
use App\Events\ServiceRequest\CreateServiceRequestActivityLog;
use App\Events\ServiceRequest\ServiceRequestPriorityChanged;
use App\Models\ServiceRequest;
use App\Services\ServiceRequestActivity\Enums\ActivityLogEventTypes;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Str;

class ServiceRequestPriorityChangedListener implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ServiceRequestPriorityChanged $event): void
    {
        $serviceRequest = ServiceRequest::select(
            'service_request_id', 'service_request_uuid', 'organization_id', 'priority'
        )
            ->where('service_request_id', $event->serviceRequestId)
            ->firstOrFail();

        // create activity log for priority change
        $activityLogPayload = [
            'organization_id' => $serviceRequest->organization_id,
            'type' => 'service-request',
            'event' => ActivityLogEventTypes::SERVICE_REQUEST_PRIORITY_CHANGED(),
            'service_request_id' => $serviceRequest->service_request_id,
            'triggered_by_user_id' => $event->userId,
            'event_attributes' => [
                'service_request_id' => $serviceRequest->service_request_uuid,
                'priority' => [
                    'label' => Str::title(Priority::from($serviceRequest->priority)->name),
                    'value' => Priority::from($serviceRequest->priority)->value,
                ],
            ],
        ];

        CreateServiceRequestActivityLog::dispatch($activityLogPayload);
    }
}

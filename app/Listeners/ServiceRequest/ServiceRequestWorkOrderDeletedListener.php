<?php

namespace App\Listeners\ServiceRequest;

use App\Events\ServiceRequest\CreateServiceRequestActivityLog;
use App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderDeleted;
use App\Models\WorkOrder;
use App\Services\ServiceRequestActivity\Enums\ActivityLogEventTypes;
use Illuminate\Contracts\Queue\ShouldQueue;

class ServiceRequestWorkOrderDeletedListener implements ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ServiceRequestWorkOrderDeleted $event): void
    {
        $workOrder = WorkOrder::withTrashed()->select(
            'work_order_id', 'work_order_uuid', 'service_request_id', 'organization_id',
            'work_order_number', 'user_id'
        )
            ->where('work_order_id', $event->workOrderId)
            ->firstOrFail();

        // create activity log for work order delete
        $activityLogPayload = [
            'organization_id' => $workOrder->organization_id,
            'type' => 'service-request',
            'event' => ActivityLogEventTypes::SERVICE_REQUEST_WORK_ORDER_DELETED(),
            'service_request_id' => $workOrder->service_request_id,
            'triggered_by_user_id' => $workOrder->user_id,
            'event_attributes' => [
                'work_order_id' => $workOrder->work_order_uuid,
                'work_order_number' => $workOrder->work_order_number,
            ],
        ];

        CreateServiceRequestActivityLog::dispatch($activityLogPayload);
    }
}

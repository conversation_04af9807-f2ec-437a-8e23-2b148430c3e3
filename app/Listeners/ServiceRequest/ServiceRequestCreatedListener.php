<?php

namespace App\Listeners\ServiceRequest;

use App\Events\ServiceRequest\NewServiceRequestCreated;
use App\Events\ServiceRequest\ServiceRequestCreated;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestActivityLog;
use App\Models\ServiceRequestStatusLog;
use App\Services\ServiceRequestActivity\Enums\ActivityLogEventTypes;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;

class ServiceRequestCreatedListener implements ShouldHandleEventsAfterCommit, ShouldQueue
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ServiceRequestCreated $event): void
    {
        $serviceRequest = ServiceRequest::select([
            'service_request_id',
            'service_request_uuid',
            'service_request_status_id',
            'organization_id',
            'state',
        ])
            ->with('organization:organization_id,organization_uuid')
            ->find($event->serviceRequestId);

        if (! empty($serviceRequest)) {

            // Create service request status log
            ServiceRequestStatusLog::create([
                'service_request_id' => $serviceRequest->service_request_id,
                'service_request_status_id' => $serviceRequest->service_request_status_id,
            ]);

            // Create activity logs for changed to status new
            ServiceRequestActivityLog::create([
                'service_request_id' => $serviceRequest->service_request_id,
                'organization_id' => $serviceRequest->organization_id,
                'triggered_by' => $event->userId ?? null,
                'event' => ActivityLogEventTypes::SERVICE_REQUEST_STATUS_CHANGED(),
                'event_attributes' => [
                    'from' => 'New',
                    'from_color_class' => 'has-info',
                    'slug_of_to' => 'new',
                    'to' => 'New',
                    'to_color_class' => 'has-info',
                ],
            ]);

            // Create activity logs for imported from appfolio
            ServiceRequestActivityLog::create([
                'service_request_id' => $serviceRequest->service_request_id,
                'organization_id' => $serviceRequest->organization_id,
                'triggered_by' => $event->userId ?? null,
                'event' => ActivityLogEventTypes::SERVICE_REQUEST_CREATED(),
                'event_attributes' => $event->activityLogEventAttributes,
            ]);

            NewServiceRequestCreated::dispatch($serviceRequest);

        }
    }
}

<?php

namespace App\Listeners\Webhook;

use App\Enums\Boolean;
use App\Enums\Feature;
use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Exceptions\WorkOrderMediaException;
use App\Models\Organization;
use App\Models\PublicApiWorkOrderWebhookEvent;
use App\Models\WorkOrder;
use App\Packages\OrganizationRolePermission\Exceptions\OrganizationNotFound;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Str;
use RuntimeException;
use Spatie\WebhookServer\WebhookCall;

class DispatchWorkOrderWebhookListener implements ShouldQueue
{
    /**
     * Handle the event.
     *
     * @throws Exception
     */
    public function handle(DispatchWorkOrderWebhook $event): void
    {
        // In this phase we don't send work order update to lula
        // rewrite this if this feature coming
        // try {
        //     if (empty($event->preventDispatchWebhook)) {
        //         $this->dispatch($event->getWorkOrder(), $event->prepareWebhookPayload());
        //     }
        // } catch (WorkOrderMediaException $exception) {
        //     return;
        // } catch (Exception $exception) {
        //     throw new RuntimeException($exception->getMessage());
        // }
    }

    /**
     * @param  array<string, mixed>  $payload
     *
     * @throws Exception
     */
    protected function dispatch(WorkOrder $workOrder, array $payload): void
    {
        $workOrder->loadMissing('organization:organization_id,organization_uuid,webhook_enabled,webhook_api_url,webhook_secret_key');

        $organization = $workOrder->organization;

        if (is_null($organization)) {
            throw new OrganizationNotFound;
        }

        $isWebhookEnabled = $organization->webhook_enabled;
        $webhookApiUrl = $organization->webhook_api_url;
        $webhookSecretKey = $organization->webhook_secret_key;

        if ($isWebhookEnabled === Boolean::YES()) {

            if (empty($webhookApiUrl)) {
                throw new RuntimeException('Empty webhook api url');
            }

            if (empty($webhookSecretKey)) {
                throw new RuntimeException('Empty webhook secret key');
            }

            $publicApiWorkOrderWebHookEvent = $this->createWebhookLog($workOrder);

            $payload['eventId'] = $publicApiWorkOrderWebHookEvent['public_api_work_order_webhook_event_uuid'];
            $payload['version'] = $publicApiWorkOrderWebHookEvent['version'];
            $payload['organizationId'] = $organization->organization_uuid;
            $payload['eventTimeUTC'] = Carbon::now()->toIso8601String();
            $publicApiWorkOrderWebHookEvent->payload = collect($payload)->sortKeys()->toArray();
            $publicApiWorkOrderWebHookEvent->save();

            if (empty($payload['eventType'])) {
                throw new Exception(__('Event type can\'t be empty'));
            }

            if (! $this->checkEventIsPermittedForOrganization($payload['eventType'], $organization)) {
                throw new Exception(__('The feature is not enabled for the organization'));
            }

            WebhookCall::create()
                ->url($webhookApiUrl)
                ->withHeaders(['Accept' => 'application/json'])
                ->uuid($payload['eventId'])
                ->payload($payload)
                ->useSecret($webhookSecretKey)
                ->dispatch();
        }
    }

    protected function resolveFeatureFromEventType(string $event): ?string
    {
        $feature = null;
        if (Str::contains($event, 'foresight:workorder')) {
            $feature = Feature::WORK_ORDER_MANAGEMENT();
        }

        if (Str::contains($event, 'foresight:workorder.quote')) {
            $feature = Feature::QUOTE_MANAGEMENT();
        }

        if (Str::contains($event, 'foresight:workorder.ready-to-invoice') || Str::contains($event, 'foresight:invoice')) {
            $feature = Feature::INVOICE_MANAGEMENT();
        }

        return $feature;
    }

    protected function checkEventIsPermittedForOrganization(string $event, Organization $organization): bool
    {
        $features = $organization->features->pluck('name')->toArray();

        $feature = $this->resolveFeatureFromEventType($event);

        return $feature && in_array($feature, $features, true);
    }

    protected function createWebhookLog(WorkOrder $workOrder): PublicApiWorkOrderWebhookEvent
    {

        return PublicApiWorkOrderWebhookEvent::create([
            'work_order_id' => $workOrder->work_order_id,
            'work_order_source_id' => $workOrder->work_order_source_id,
        ]);
    }
}

<?php

namespace App\Listeners\Webhook;

use App\Models\PublicApiWorkOrderWebhookEvent;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Log;
use Spatie\WebhookServer\Events\WebhookCallSucceededEvent;

class WebhookSucceededListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(WebhookCallSucceededEvent $event): void
    {
        Log::channel('webhook')->info('Webhook[WebhookCallSucceededEvent]', [
            'url' => $event->webhookUrl ?? null,
            'payload' => $event->payload ?? null,
            'headers' => $event->headers ?? null,
            'response' => $event->response ?? null,
        ]);

        $webhookLog = PublicApiWorkOrderWebhookEvent::whereUuid($event->uuid)->firstOrFail();

        /** @var Response $response */
        $response = $event->response;

        if ($response) {
            $webhookLog->response = json_decode($response->getBody()->getContents(), true);
            $webhookLog->response_status_code = $response->getStatusCode();
        }
        $webhookLog->attempt = $event->attempt;
        $webhookLog->status = 'delivered';

        $webhookLog->save();
    }
}

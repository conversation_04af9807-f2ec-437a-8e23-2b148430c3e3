<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

enum DateRanges: string
{
    use InvokableCases;

    case OVERDUE = 'overdue';
    case TODAY = 'today';
    case TOMORROW = 'tomorrow';
    case YESTERDAY = 'yesterday';
    case THIS_WEEK = 'this_week';
    case NEXT_WEEK = 'next_week';
    case LAST_WEEK = 'last_week';
    case THIS_MONTH = 'this_month';
    case NEXT_MONTH = 'next_month';
    case LAST_MONTH = 'last_month';

    /**
     * Returns the slug and value list
     *
     * @param  string|array<int, string>  $except
     * @return Collection<int, self>
     */
    public static function makeCollection(string|array $except = []): Collection
    {
        return collect(self::cases())->whereNotIn('value', Arr::wrap($except));
    }

    /**
     * Returns the slug and value list
     *
     * @return Collection<int, string>
     */
    public static function excludeFutureDates(): Collection
    {
        return collect([DateRanges::TOMORROW(), DateRanges::NEXT_MONTH(), DateRanges::NEXT_WEEK()]);
    }
}

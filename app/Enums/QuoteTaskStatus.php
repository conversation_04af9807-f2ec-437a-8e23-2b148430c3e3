<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use Exception;

enum QuoteTaskStatus: string
{
    use InvokableCases;

    case QUOTE_PENDING_REVIEW = 'quote-pending-review';
    case PENDING_DECISION = 'pending-decision';
    case APPROVED = 'approved';
    case REJECTED = 'rejected';

    public static function label(string $status, string $source = 'mobile'): string
    {
        if ($source === 'web') {
            return match ($status) {
                self::APPROVED() => 'Approved',
                self::REJECTED() => 'Rejected',
                self::QUOTE_PENDING_REVIEW() => 'In Review',
                self::PENDING_DECISION() => 'Pending Decision',
                default => throw new Exception("Invalid status: [{$status}]")
            };
        }

        return match ($status) {
            self::APPROVED() => 'Approved',
            self::REJECTED() => 'Rejected',
            self::QUOTE_PENDING_REVIEW() => 'In Review',
            self::PENDING_DECISION() => 'Pending Decision',
            default => throw new Exception("Invalid status: [{$status}]")
        };
    }
}

<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use Exception;

enum QuoteStatus: string
{
    use InvokableCases;

    case QUOTE_PENDING_REVIEW = 'quote-pending-review';
    case PENDING_APPROVAL = 'pending-approval';
    case PARTIALLY_APPROVED = 'partially-approved';
    case APPROVED = 'approved';
    case REJECTED = 'rejected';

    public static function label(string $status, string $source = 'mobile'): string
    {
        if ($source === 'web') {
            return match ($status) {
                self::APPROVED() => 'Approved',
                self::REJECTED() => 'Rejected',
                self::QUOTE_PENDING_REVIEW() => 'Pending Review',
                self::PARTIALLY_APPROVED() => 'Partially Approved',
                self::PENDING_APPROVAL() => 'Pending Approval',
                default => throw new Exception("Invalid status: [{$status}]")
            };
        }

        return match ($status) {
            self::APPROVED() => 'Quote Fully Approved',
            self::REJECTED() => 'Quote Rejected',
            self::QUOTE_PENDING_REVIEW() => 'Quote Pending Review',
            self::PARTIALLY_APPROVED() => 'Quote Partially Approved',
            self::PENDING_APPROVAL() => 'Quote Pending Approval',
            default => throw new Exception("Invalid status: [{$status}]")
        };
    }
}

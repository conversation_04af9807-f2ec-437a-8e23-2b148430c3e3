<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

enum Feature: string
{
    use InvokableCases, Values;

    case BILLING = 'Billing';
    case WORK_ORDER_MANAGEMENT = 'Work Order Management';
    case USER_MANAGEMENT = 'User Management';
    case TECHNICIAN_MANAGEMENT = 'Technician Management';
    case ROLE_MANAGEMENT = 'Role Management';
    case ORGANIZATION_SETTINGS = 'Organization Settings';
    case CALENDAR_MANAGEMENT = 'Calendar Management';
    case QUOTE_MANAGEMENT = 'Quote Management';
    case INVOICE_MANAGEMENT = 'Invoice Management';
    case TAG_MANAGEMENT = 'Tag Management';
    case APPLICATION_SETTINGS = 'Application Settings';
    case VENDOR_MANAGEMENT = 'Vendor Management';
    case VENDOR_PORTAL_MANAGEMENT = 'Vendor Portal Management';
    case SERVICE_REQUEST_MANAGEMENT = 'Service Request Management';
}

<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

enum InvoiceSubsidiaryTypes: string
{
    use InvokableCases;
    use Values;

    case HOURLY_LABOR = 'hourly-labor';

    case FEE = 'fee';

    case LABOR = 'labor';

    case MATERIAL = 'material';

    case NOTES = 'notes';

    case DRIVE_RATE = 'drive-rate';

    public static function isLaborCostType(string $type): bool
    {
        if (in_array($type, [self::HOURLY_LABOR(), self::DRIVE_RATE()])) {
            return true;
        } else {
            return false;
        }
    }
}

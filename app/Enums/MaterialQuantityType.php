<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

enum MaterialQuantityType: string
{
    use InvokableCases;
    use Values;

    case UNITS = 'units';
    case SQUARE_FEET = 'square-ft';
    case LINEAR_FEET = 'linear-ft';
    case POUNDS = 'pounds';

    /**
     * Returns quantity
     *
     * @return array<int, array<string,string>>
     */
    public static function allTypes(): array
    {
        return [
            [
                'label' => 'Linear ft',
                'value' => self::LINEAR_FEET(),
            ],
            [
                'label' => 'Pounds',
                'value' => self::POUNDS(),
            ],
            [
                'label' => 'Square ft',
                'value' => self::SQUARE_FEET(),
            ],
            [
                'label' => 'Units',
                'value' => self::UNITS(),
            ],
        ];
    }
}

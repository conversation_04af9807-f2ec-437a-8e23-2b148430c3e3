<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;

enum ScheduleTypes: string
{
    use InvokableCases;

    case IN_HOUSE = 'in-house';

    case LULA_PRO = 'lula';

    case THIRD_PARTY_VENDOR = 'third-party-vendor';

    case NOT_ASSIGNED = 'not-assigned';

    public function getLabel(): string
    {
        return match ($this) {
            self::IN_HOUSE => 'In House',
            self::LULA_PRO => 'Lula',
            self::THIRD_PARTY_VENDOR => 'Third Party Vendor',
            self::NOT_ASSIGNED => 'Not Assigned',
        };
    }
}

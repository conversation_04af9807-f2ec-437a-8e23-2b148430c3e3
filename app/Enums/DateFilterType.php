<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

enum DateFilterType: string
{
    use InvokableCases;
    use Values;

    case DATE = 'date';
    case DATE_BETWEEN = 'date_between';
    case DATE_NOT_BETWEEN = 'date_not_between';

    /**
     * Get the description of the filter type
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::DATE => 'Single date comparison',
            self::DATE_BETWEEN => 'Date range (between)',
            self::DATE_NOT_BETWEEN => 'Date range (not between)',
        };
    }
}

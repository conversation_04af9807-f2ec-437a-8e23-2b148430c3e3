<?php

declare(strict_types=1);

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

enum IssueActions: string
{
    use InvokableCases, Values;

    case EDIT = 'edit';

    case ASSIGN = 'assign';

    case UNASSIGN = 'unassign';

    case DELETE = 'delete';

    case CANCEL = 'cancel';

    case RESTORE = 'restore';

    case ADD_NEW_WORK_ORDER = 'add_new_work_order';
}

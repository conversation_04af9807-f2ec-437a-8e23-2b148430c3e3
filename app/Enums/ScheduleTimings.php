<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;
use Exception;

enum ScheduleTimings: string
{
    use InvokableCases;
    use Values;

    case MORNING = 'morning';
    case AFTERNOON = 'afternoon';
    case EVENING = 'evening';
    case ANYTIME = 'anytime';

    /**
     * Returns the start_time and end_time list
     *
     * @return array<string, string>
     */
    public static function timeRange(string $slug): array
    {
        return match ($slug) {
            self::MORNING() => [
                'start_time' => '08:00:00',
                'end_time' => '12:00:00',
            ],
            self::AFTERNOON() => [
                'start_time' => '12:00:00',
                'end_time' => '16:00:00',
            ],
            self::EVENING() => [
                'start_time' => '16:00:00',
                'end_time' => '20:00:00',
            ],
            self::ANYTIME() => [
                'start_time' => '08:00:00',
                'end_time' => '20:00:00',
            ],
            default => throw new Exception("Invalid slug: [{$slug}]")
        };
    }

    public static function label(string $slug): string
    {
        return match ($slug) {
            self::MORNING() => 'Morning',
            self::AFTERNOON() => 'Afternoon',
            self::EVENING() => 'Evening',
            self::ANYTIME() => 'Anytime',
            default => throw new Exception("Invalid slug: [{$slug}]")
        };
    }

    //FE response
    /**
     * Returns the slug and value list
     *
     * @return array<int, array<string, array<string, string>|string>>
     */
    public static function all(): array
    {
        return [
            [
                'label' => self::label(self::MORNING()),
                'value' => self::MORNING(),
                'time_range' => '8am-12pm',
            ],
            [
                'label' => self::label(self::AFTERNOON()),
                'value' => self::AFTERNOON(),
                'time_range' => '12pm-4pm',
            ],
            [
                'label' => self::label(self::EVENING()),
                'value' => self::EVENING(),
                'time_range' => '4pm-8pm',
            ],
            [
                'label' => self::label(self::ANYTIME()),
                'value' => self::ANYTIME(),
                'time_range' => '8am-8pm',
            ],
        ];
    }

    /**
     * @return array<int,string>
     */
    public static function scheduleTimingsFromApp(): array
    {
        $preDefinedTimings = self::values();
        unset($preDefinedTimings[array_search(self::ANYTIME(), $preDefinedTimings, true)]);

        return $preDefinedTimings;
    }

    /**
     * Return timings in order
     *
     * @return array<string>
     */
    public static function displaySortOrder(): array
    {
        return [
            self::MORNING(),
            self::AFTERNOON(),
            self::EVENING(),
        ];
    }
}

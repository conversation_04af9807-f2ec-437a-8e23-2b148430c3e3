<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

enum PropertyAccessMethods: string
{
    use InvokableCases,Values;

    case ADULT_WILL_LET_YOU_IN = 'adult-will-let-you-in';
    case DIGITAL_KEY_CODE = 'digital-key-code';
    case HIDDEN_KEY = 'hidden-key';
    case KEY_WITH_PROPERTY_MANAGEMENT = 'key-with-property-management';
    case NO_ACCESS_NEEDED = 'no-access-needed';
    case OTHER = 'other';

    /**
     *  Format enum value for api response.
     *
     * @return array<int,array<string,string|null>>
     */
    public static function apiResponseFormat(): array
    {
        return [
            [
                'label' => self::label(self::ADULT_WILL_LET_YOU_IN()),
                'value' => self::ADULT_WILL_LET_YOU_IN(),
            ],
            [
                'label' => self::label(self::DIGITAL_KEY_CODE()),
                'value' => self::DIGITAL_KEY_CODE(),
            ],
            [
                'label' => self::label(self::HIDDEN_KEY()),
                'value' => self::HIDDEN_KEY(),
            ],
            [
                'label' => self::label(self::KEY_WITH_PROPERTY_MANAGEMENT()),
                'value' => self::KEY_WITH_PROPERTY_MANAGEMENT(),
            ],
            [
                'label' => self::label(self::NO_ACCESS_NEEDED()),
                'value' => self::NO_ACCESS_NEEDED(),
            ],
            [
                'label' => self::label(self::OTHER()),
                'value' => self::OTHER(),
            ],
        ];
    }

    /**
     *  Value for slug.
     */
    public static function label(string $slug): ?string
    {
        return match ($slug) {
            self::ADULT_WILL_LET_YOU_IN() => 'An adult will be home to let you in',
            self::DIGITAL_KEY_CODE() => 'Digital Key or Lockbox',
            self::HIDDEN_KEY() => 'Hidden key',
            self::KEY_WITH_PROPERTY_MANAGEMENT() => 'Key with property management office',
            self::NO_ACCESS_NEEDED() => 'No Access Needed /Exterior',
            self::OTHER() => 'Other',
            default => null,
        };
    }
}

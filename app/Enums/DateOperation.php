<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

enum DateOperation: string
{
    use InvokableCases;
    use Values;

    case IS = 'is';
    case IS_NOT = 'is_not';
    case IS_AFTER = 'is_after';
    case IS_BEFORE = 'is_before';

    /**
     * Get the description of the operation
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::IS => 'Is equal to',
            self::IS_NOT => 'Is not equal to',
            self::IS_AFTER => 'Is after',
            self::IS_BEFORE => 'Is before',
        };
    }
}

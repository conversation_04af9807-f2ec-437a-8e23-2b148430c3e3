<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use Exception;

enum UserStatus: string
{
    use InvokableCases;

    case ACTIVE = 'active';
    case INACTIVE = 'inactive';

    /**
     * @throws Exception
     */
    public static function find(string $status): string
    {
        return match ($status) {
            'active' => self::ACTIVE->value,
            'inactive' => self::INACTIVE->value,
            default => throw new Exception("Invalid status: [{$status}]")
        };
    }

    /**
     * Returns the label and value format for frontend API
     *
     * @return array<int, array<string, string>>
     */
    public static function statusValueLabelFormat(): array
    {
        $data = [];
        foreach (self::cases() as $status) {
            array_push($data, [
                'label' => $status->name,
                'value' => $status->value,
            ]);
        }

        return $data;
    }
}

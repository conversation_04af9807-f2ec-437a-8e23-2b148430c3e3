<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

enum DateSlug: string
{
    use InvokableCases;
    use Values;

    case TODAY = 'today';
    case TOMORROW = 'tomorrow';
    case YESTERDAY = 'yesterday';
    case OVERDUE = 'overdue';
    case THIS_WEEK = 'this_week';
    case NEXT_WEEK = 'next_week';
    case LAST_WEEK = 'last_week';
    case THIS_MONTH = 'this_month';
    case NEXT_MONTH = 'next_month';
    case LAST_MONTH = 'last_month';

    /**
     * Returns all date slugs as a collection
     *
     * @param  string|array<int, string>  $except
     * @return Collection<int, self>
     */
    public static function makeCollection(string|array $except = []): Collection
    {
        $exceptValues = Arr::wrap($except);

        return collect(self::cases())->filter(function ($case) use ($exceptValues) {
            return ! in_array($case->value, $exceptValues);
        });
    }

    /**
     * Returns future date slugs
     *
     * @return Collection<int, string>
     */
    public static function futureDates(): Collection
    {
        return collect([self::TOMORROW(), self::NEXT_WEEK(), self::NEXT_MONTH()]);
    }

    /**
     * Get the display name for the date slug
     */
    public function getDisplayName(): string
    {
        return match ($this) {
            self::TODAY => 'Today',
            self::TOMORROW => 'Tomorrow',
            self::YESTERDAY => 'Yesterday',
            self::OVERDUE => 'Overdue',
            self::THIS_WEEK => 'This Week',
            self::NEXT_WEEK => 'Next Week',
            self::LAST_WEEK => 'Last Week',
            self::THIS_MONTH => 'This Month',
            self::NEXT_MONTH => 'Next Month',
            self::LAST_MONTH => 'Last Month',
        };
    }

    /**
     * Get the date range for this slug
     *
     * @return array{0: 'single'|'range', 1: Carbon, 2?: Carbon|null}
     */
    public function getDateRange(): array
    {
        $now = Carbon::now();

        // Ensure Carbon uses Sunday as the first day of the week
        Carbon::setWeekStartsAt(Carbon::SUNDAY);
        Carbon::setWeekEndsAt(Carbon::SATURDAY);

        return match ($this) {
            self::TODAY => ['single', $now->copy()->startOfDay(), null],
            self::TOMORROW => ['single', $now->copy()->addDay()->startOfDay(), null],
            self::YESTERDAY => ['single', $now->copy()->subDay()->startOfDay(), null],
            self::OVERDUE => ['single', $now->copy()->subDay()->endOfDay(), null],
            self::THIS_WEEK => ['range', $now->copy()->startOfWeek(), $now->copy()->endOfWeek()],
            self::NEXT_WEEK => ['range', $now->copy()->addWeek()->startOfWeek(), $now->copy()->addWeek()->endOfWeek()],
            self::LAST_WEEK => ['range', $now->copy()->subWeek()->startOfWeek(), $now->copy()->subWeek()->endOfWeek()],
            self::THIS_MONTH => ['range', $now->copy()->startOfMonth(), $now->copy()->endOfMonth()],
            self::NEXT_MONTH => ['range', $now->copy()->addMonth()->startOfMonth(), $now->copy()->addMonth()->endOfMonth()],
            self::LAST_MONTH => ['range', $now->copy()->subMonth()->startOfMonth(), $now->copy()->subMonth()->endOfMonth()],
        };
    }
}

<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;
use Exception;

enum VendorOnBoardingStatus: string
{
    use InvokableCases;
    use Values;

    case PENDING = 'pending';
    case COMPLETE = 'complete';

    public static function label(string $status): string
    {
        return match ($status) {
            self::PENDING() => 'Pending',
            self::COMPLETE() => 'Complete',
            default => throw new Exception("Invalid status: [{$status}]")
        };
    }
}

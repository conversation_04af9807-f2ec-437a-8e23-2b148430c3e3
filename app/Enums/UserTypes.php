<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;

enum UserTypes: string
{
    use InvokableCases;

    case ACCOUNT_USER = 'account_user';
    case TECHNICIAN = 'technician';
    case VENDOR = 'vendor';
    case RESIDENT = 'resident';
    case ASSET_OWNER = 'asset_owner';
    case LULA_PRO_NETWORK = 'lula_pro_network';

    /**
     * @return UserTypes[]
     */
    public static function tripActionAllowedType(): array
    {
        return [
            self::ACCOUNT_USER(),
            self::TECHNICIAN(),
            self::VENDOR(),
            self::LULA_PRO_NETWORK(),
        ];
    }
}

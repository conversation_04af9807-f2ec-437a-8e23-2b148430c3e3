<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;
use Illuminate\Support\Collection;

enum InvoiceLineItemTypes: string
{
    use InvokableCases;
    use Values;

    case HOURLY_LABOR = 'hourly-labor';

    case QUOTE_TASK = 'quote-task';

    case MATERIAL = 'material';

    case LABOR = 'labor';

    case NO_SHOW_FEE = 'no-show-fee';

    case TRIP_FEE = 'trip-fee';

    case SERVICE_NOTE = 'service-notes';

    /**
     * @return Collection<int, string>
     */
    public static function hourlyTypes(): Collection
    {
        return collect([
            InvoiceLineItemTypes::HOURLY_LABOR(), InvoiceLineItemTypes::NO_SHOW_FEE(),
            InvoiceLineItemTypes::TRIP_FEE(), self::LABOR(),
        ]);
    }
}

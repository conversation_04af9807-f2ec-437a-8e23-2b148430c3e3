<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

enum Priority: string
{
    use InvokableCases, Values;

    case URGENT = 'urgent';
    case HIGH = 'high';
    case MEDIUM = 'medium';
    case LOW = 'low';

    /**
     * Returns priorities
     *
     * @param  string|array<int, string>  $except
     * @return Collection<int, self>
     */
    public static function makeCollection(string|array $except = []): Collection
    {
        return collect(self::cases())->whereNotIn('value', Arr::wrap($except));
    }

    /**
     * Returns the slug and value list
     *
     * @return array<int, array<string, string>>
     */
    public static function priorityValueLabelFormat(): array
    {
        $data = [];
        foreach (self::cases() as $priority) {
            array_push($data, [
                'label' => Str::title($priority->name),
                'value' => $priority->value,
            ]);
        }

        return $data;
    }
}

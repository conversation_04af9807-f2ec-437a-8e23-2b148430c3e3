<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

enum ServiceCallActionTypes: string
{
    use InvokableCases;
    use Values;

    case SCHEDULED = 'scheduled';

    case RE_SCHEDULED = 're-scheduled';

    case COMPLETED = 'completed';

    case EN_ROUTE = 'en-route';

    case WORKING = 'working';

    case PAUSED = 'paused';

    case UPDATED = 'updated';

    case SCHEDULE_IN_PENDING = 'schedule-in-pending';

    case SCHEDULE_IN_PROGRESS = 'schedule-in-progress';

    case CANCELED = 'canceled';

    case CLAIM_PENDING = 'claim-pending';

    case PAUSE_ENROUTE = 'pause-en-route';
}

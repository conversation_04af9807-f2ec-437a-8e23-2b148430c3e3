<?php

namespace App\Enums;

use ArchTech\Enums\InvokableCases;
use Illuminate\Support\Collection;

enum WorkOrderStatus: string
{
    use InvokableCases;

    case NEW = 'created';
    case SCOPING = 'scoping';
    case DE_ESCALATING = 'de_escalating';
    case READY_TO_SCHEDULE = 'ready_to_schedule';
    case SCHEDULING_IN_PROGRESS = 'scheduling_in_progress';
    case SCHEDULED = 'scheduled';
    case WORK_IN_PROGRESS = 'work_in_progress';
    case QUOTE_PENDING_REVIEW = 'quote_pending_review';
    case QUOTE_PENDING_APPROVAL = 'quote_pending_approval';
    case QUALITY_CHECK = 'quality_check';
    case READY_TO_INVOICE = 'ready_to_invoice';
    case PENDING_INVOICE_PAYMENT = 'pending_invoice_payment';
    case PAUSED = 'paused';
    case CANCELED = 'canceled';
    case TRIP_AVOIDED = 'trip_avoided';
    case COMPLETED = 'completed';
    case CLAIM_PENDING = 'claim_pending';
    case AWAITING_AVAILABILITY = 'awaiting_availability';

    /**
     * @return Collection<int, self>
     */
    public static function newStatuses(): Collection
    {
        return collect([WorkOrderStatus::NEW]);
    }

    /**
     * @return Collection<int, self>
     */
    public static function triagingStatuses(): Collection
    {
        return collect([
            WorkOrderStatus::SCOPING,
            WorkOrderStatus::DE_ESCALATING,
        ]);
    }

    /**
     * @return Collection<int, self>
     */
    public static function schedulingStatuses(): Collection
    {
        return collect([
            WorkOrderStatus::READY_TO_SCHEDULE,
            WorkOrderStatus::SCHEDULING_IN_PROGRESS,
        ]);
    }

    /**
     * @return Collection<int, self>
     */
    public static function scheduledStatuses(): Collection
    {
        return collect([
            WorkOrderStatus::SCHEDULED,
        ]);
    }

    /**
     * @return Collection<int, self>
     */
    public static function workInProgressStatuses(): Collection
    {
        return collect([
            WorkOrderStatus::WORK_IN_PROGRESS,
        ]);
    }

    /**
     * @return Collection<int, self>
     */
    public static function quotingStatuses(): Collection
    {
        return collect([
            WorkOrderStatus::QUOTE_PENDING_REVIEW,
            WorkOrderStatus::QUOTE_PENDING_APPROVAL,
        ]);
    }

    /**
     * @return Collection<int, self>
     */
    public static function pausedStatuses(): Collection
    {
        return collect([
            WorkOrderStatus::PAUSED,
        ]);
    }

    /**
     * @return Collection<int, self>
     */
    public static function qualityCheckStatuses(): Collection
    {
        return collect([
            WorkOrderStatus::QUALITY_CHECK,
        ]);
    }

    /**
     * @return Collection<int, self>
     */
    public static function invoiceStatuses(): Collection
    {
        return collect([
            WorkOrderStatus::READY_TO_INVOICE,
            WorkOrderStatus::PENDING_INVOICE_PAYMENT,
        ]);
    }

    /**
     * @return Collection<int, self>
     */
    public static function closedStatuses(): Collection
    {
        return collect([
            WorkOrderStatus::COMPLETED,
            WorkOrderStatus::QUALITY_CHECK,
            WorkOrderStatus::CANCELED,
            WorkOrderStatus::READY_TO_INVOICE,
        ]);
    }

    /**
     * @return Collection<int, self>
     */
    public static function openWorkOrderStates(): Collection
    {
        return collect(
            array_merge(
                self::scheduledStatuses()->toArray(),
                self::workInProgressStatuses()->toArray(),
            )
        );
    }

    /**
     * @return Collection<int, self>
     */
    public static function healthScoreStates(): Collection
    {
        return collect([
            WorkOrderStatus::CLAIM_PENDING,
            WorkOrderStatus::SCHEDULING_IN_PROGRESS,
            WorkOrderStatus::SCHEDULED,
            WorkOrderStatus::WORK_IN_PROGRESS,
        ]);
    }

    /**
     * Returns the label and value format for frontend API
     *
     * @return array<int, array<string, string>>
     */
    public static function valueLabelForMobileApp(string $type = 'all'): array
    {
        $data = [];

        $type = match ($type) {
            'open' => 'openWorkOrderStates',
            'closed' => 'closedStatuses',
            'paused' => 'pausedStatuses',
            default => 'cases'
        };

        foreach (self::{$type}() as $status) {
            array_push($data, [
                'label' => self::labelForMobile($status->value),
                'value' => $status->value,
            ]);
        }

        return $data;
    }

    public static function labelForMobile(string $slug): string
    {
        return match ($slug) {
            self::NEW() => 'New',
            self::SCOPING() => 'Scoping',
            self::DE_ESCALATING() => 'De-escalating',
            self::READY_TO_SCHEDULE() => 'Ready to Schedule',
            self::SCHEDULING_IN_PROGRESS() => 'Scheduling in Progress',
            self::SCHEDULED() => 'Scheduled',
            self::WORK_IN_PROGRESS() => 'Work in Progress',
            self::QUOTE_PENDING_REVIEW() => 'Quote Pending Review',
            self::QUOTE_PENDING_APPROVAL() => 'Quote Pending Approval',
            self::QUALITY_CHECK() => 'Quality Check',
            self::READY_TO_INVOICE() => 'Ready to Invoice',
            self::PENDING_INVOICE_PAYMENT() => 'Pending Invoice Payment',
            self::PAUSED() => 'Paused',
            self::CANCELED() => 'Canceled',
            self::TRIP_AVOIDED() => 'Trip Avoided',
            self::COMPLETED() => 'Completed',
            self::CLAIM_PENDING() => 'Claim Pending',
            self::AWAITING_AVAILABILITY() => 'Awaiting Availability',
            default => ''
        };
    }
}

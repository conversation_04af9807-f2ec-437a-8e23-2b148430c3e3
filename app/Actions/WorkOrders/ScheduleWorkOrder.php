<?php

namespace App\Actions\WorkOrders;

use App\Helpers\Helper;
use App\Http\Requests\Schedule\StoreAppointmentRequest;
use App\Http\Resources\WorkOrder\WorkOrderScheduledResource;
use App\Models\Organization;
use App\Models\WorkOrder;
use App\Services\Scheduling\SchedulingService;
use App\States\WorkOrders\Scheduled;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;
use Throwable;

class ScheduleWorkOrder extends BaseAction
{
    use AsAction;

    public function __construct(protected SchedulingService $scheduleService) {}

    public static function getSlug(): string
    {
        return 'scheduleWorkOrder';
    }

    public static function getRoute(): string
    {
        return 'api/work-orders/{workOrder}/actions/';
    }

    public function handle(
        Organization $organization,
        WorkOrder $workOrder,
        string $serviceWindowReferenceId,
        string $mode,
        string $method,
        string $workToPerform,
        ?string $quoteId = null,
        ?string $nteAmount = null,
    ): WorkOrder {
        $workOrder = DB::transaction(function () use ($organization, $workOrder, $serviceWindowReferenceId, $mode, $method, $workToPerform, $quoteId, $nteAmount) {
            return $this->scheduleService->registerServiceCall(
                organizationId: $organization->organization_id,
                workOrder: $workOrder,
                serviceWindowReference: $serviceWindowReferenceId,
                mode: $mode,
                method: $method,
                workToPerform: $workToPerform,
                quoteId: $quoteId,
                nteAmount: $nteAmount,
            );
        });

        return $workOrder;
    }

    public function asController(StoreAppointmentRequest $request, WorkOrder $workOrder): WorkOrderScheduledResource|JsonResponse
    {
        $organization = $request->user()?->organization;

        if (! $organization) {
            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }

        try {
            DB::beginTransaction();

            $lockedWorkOrder = WorkOrder::lockForUpdate()
                ->with([
                    'issues:problem_diagnosis_id', 'timezone',
                ])
                ->where('work_order_id', $workOrder->work_order_id)
                ->select(
                    'work_order_id',
                    'service_request_id',
                    'work_order_uuid',
                    'state',
                    'organization_id',
                    'timezone_id',
                    'work_order_status_id',
                    'state_updated_at',
                    'vendor_id',
                    'vendor_work_order_id',
                    'nte_amount_in_cents',
                )
                ->first();

            if (! $lockedWorkOrder->state->canTransitionTo(Scheduled::class)) {
                throw new CouldNotPerformTransition(__('This transition not allowed to perform with the work order'));
            }

            $lockedWorkOrder = $this->handle(
                organization: $organization,
                workOrder: $lockedWorkOrder,
                serviceWindowReferenceId: $request->input('service_window_reference_id'),
                mode: $request->input('mode'),
                method: $request->input('method'),
                workToPerform: $request->input('work_to_perform') ?? 'hourly-task',
                quoteId: $request->input('quote_id') ?? null,
                nteAmount: $request->input('nte_amount_in_cents'),
            );

            DB::commit();

            $lockedWorkOrder->load([
                'latestTrips' => function ($query) {
                    $query->select(
                        'work_order_service_calls.work_order_service_call_id',
                        'work_order_service_calls.work_order_service_call_uuid',
                        'work_order_service_calls.work_order_service_call_number',
                        'work_order_service_calls.work_order_id',
                        'work_order_service_calls.technician_appointment_id',
                        'work_order_service_calls.vendor_appointment_id',
                        'work_order_service_calls.lula_appointment_id',
                        'work_order_service_calls.scheduled_start_time',
                        'work_order_service_calls.scheduled_end_time',
                        'work_order_service_calls.state',
                        'work_order_service_calls.state_updated_at',
                        'work_order_service_calls.additional_notes',
                        'work_order_service_calls.status',
                        'work_order_service_calls.is_active',
                        'work_order_service_calls.last_modified_user',
                        'work_order_service_calls.last_modified_at',
                    )
                        ->with([
                            'technicianAppointment:technician_appointment_id,technician_id',
                            'technicianAppointment.technician:technician_id,technician_uuid,user_id',
                            'technicianAppointment.technician.user:user_id,first_name,last_name,middle_name,profile_pic',
                        ]);
                },
            ]);

            return new WorkOrderScheduledResource($lockedWorkOrder);
        } catch (CouldNotPerformTransition $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'This transition[schedule] not allowed to perform with the work order.');

            return Response::unprocessableEntity(message: __('This transition not allowed to perform with the work order.'));
        } catch (InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Word Order Note store api failed due to InvalidArgumentException');

            return Response::unprocessableEntity(message: __('Invalid argument passed to the request. Work order and task are not matching.'));
        } catch (Exception|Throwable $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Schedule failed[Exception]', notify: true);

            return Response::internalServerError(message: $exception->getMessage());
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('schedule', $request->route('workOrder'));
    }
}

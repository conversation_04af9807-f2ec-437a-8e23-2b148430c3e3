<?php

namespace App\Actions\WorkOrders;

use App\Enums\CostTypes;
use App\Enums\QuoteStatus;
use App\Enums\ServiceCallActionTypes;
use App\Enums\Trip;
use App\Events\WorkOrder\PublicWebhook\Lula\TripUpdatedLulaWebhook;
use App\Events\WorkOrder\WorkOrderUpdate;
use App\Exceptions\TripException;
use App\Helpers\Helper;
use App\Http\Requests\WorkOrder\UpdateTripRequest;
use App\Http\Resources\WorkOrder\TripResource;
use App\Models\TechnicianAppointment;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderServiceCallLog;
use App\Models\WorkOrderTask;
use App\Models\WorkOrderTaskMaterial;
use App\States\ServiceCalls\Ended;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\QualityCheck;
use App\States\WorkOrders\ReadyToSchedule;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;

class UpdateWorkOrderTrip extends BaseAction
{
    use AsAction;

    protected static string $method = 'put';

    public static function getSlug(): string
    {
        return 'UpdateWorkOrderTrip';
    }

    public static function getRoute(): string
    {
        return 'api/work-orders/{workOrder}/tasks/{workOrderTask}/trips/{workOrderServiceCall}/actions/';
    }

    /**
     * @param  array<string,mixed>  $payload
     */
    public function handle(array $payload, WorkOrderTask $workOrderTask, WorkOrder $workOrder, User $user, WorkOrderServiceCall $trip, TechnicianAppointment $technicianAppointment): void
    {
        DB::transaction(function () use ($payload, $workOrderTask, $workOrder, $user, $trip, $technicianAppointment) {

            // **********  Update service details *****************
            $trip->service_notes = $payload['service_notes'] ?? $trip->service_notes;
            $trip->trip_end_with = $payload['trip_end_with'] ?? $trip->trip_end_with;
            $trip->trip_end_with_type = $payload['trip_end_with_type'] ?? $trip->trip_end_with_type;
            $trip->trip_end_with_reason = $payload['reason'] ?? $trip->trip_end_with_reason;
            $trip->last_modified_user = $user->user_id ?? null;
            $trip->last_modified_at = CarbonImmutable::now();

            // Trip end reason is only available for "FINISH_WITH_ANOTHER_DAY" and "OTHER" options. Otherwise, set it as null.
            if ($payload['trip_end_with'] === Trip::ALL_COMPLETED() || isset($payload['trip_end_with_type']) && (
                ! in_array($payload['trip_end_with_type'], [Trip::FINISH_WITH_ANOTHER_DAY(), Trip::OTHER()]))) {
                $trip->trip_end_with_reason = null;
            }

            /**
             * #Click-up https://app.clickup.com/t/86b0ba1gj?comment=90140035254237&threadedComment=90140038209460
             * According to the above comment, we can delete all materials, media and service notes related to the trip
             * if the trip end with type updated to "No Work". Other wise update the materials
             */
            if ($payload['trip_end_with'] === Trip::NO_WORK()) {
                $trip->service_notes = null;

                $trip->workOrderTaskMaterials->each->delete();
                $trip->media->each->delete();
            } else {
                $materials = $payload['materials'] ?? [];
                $existingMaterials = $trip->workOrderTaskMaterials;
                $materialIds = [];

                if (! empty($materials)) {
                    foreach ($materials as $material) {
                        $unit_price = $material['unit_price_in_cents'] ?? 0;
                        $quantity = $material['quantity'] ?? 1;
                        $workOrderTaskMaterial = null;

                        if (! empty($material['material_id'])) {
                            $workOrderTaskMaterial = $existingMaterials->where('work_order_task_material_uuid', $material['material_id'])->first();
                        }

                        if ($workOrderTaskMaterial) {
                            $workOrderTaskMaterial->update([
                                'label' => $material['label'],
                                'cost_in_cents' => $material['cost_in_cents'] ?? $unit_price * $quantity,
                                'cost_type' => ! empty($material['unit_price_in_cents']) ? CostTypes::PER_UNIT() : CostTypes::TOTAL(),
                                'quantity' => $quantity,
                                'quantity_type' => $material['quantity_type'],
                            ]);

                            $materialIds[] = $material['material_id'];
                        } else {
                            $workOrderTaskMaterial = WorkOrderTaskMaterial::create([
                                'organization_id' => $workOrderTask->organization_id,
                                'work_order_task_id' => $workOrderTask->work_order_task_id,
                                'label' => $material['label'],
                                'created_user_id' => $user->user_id,
                                'cost_in_cents' => $material['cost_in_cents'] ?? $unit_price * $quantity,
                                'cost_type' => ! empty($material['unit_price_in_cents']) ? CostTypes::PER_UNIT() : CostTypes::TOTAL(),
                                'quantity' => $quantity,
                                'quantity_type' => $material['quantity_type'],
                                'work_order_service_call_id' => $trip->work_order_service_call_id,
                            ]);
                        }
                    }
                }
                // check if any material(s) are missing from the request
                // then we can delete those material(s)
                $deletedMaterials = $existingMaterials->whereNotIn('work_order_task_material_uuid', $materialIds);

                if ($deletedMaterials->isNotEmpty()) {
                    $deletedMaterials->each->delete();
                }
            }

            // ******************** Update appointment details ***********************
            if (! empty($technicianAppointment->actual_start_time) && ! empty($payload['completed_time_in_sec'])) {
                $technicianAppointment->adjusted_elapse_time_in_sec = $payload['completed_time_in_sec'];
                $technicianAppointment->save();
            }

            if (! empty($payload['travel_time_in_sec'])) {
                $technicianAppointment->adjusted_travel_time_in_sec = $payload['travel_time_in_sec'];
                $technicianAppointment->save();
            }

            /**
             * We want to update work order status as WRT trip edit option for the updating trip if it is the last one
             */
            $latestServiceCall = $workOrderTask->latestServiceCalls->firstOrFail();
            if ($latestServiceCall->work_order_service_call_id === $trip->work_order_service_call_id) {

                if ($payload['trip_end_with'] === Trip::ALL_COMPLETED()) {
                    $trip->trip_end_with_type = null;
                }

                $existingQuote = $trip->createdQuote;
                $pausedReason = ! empty($existingQuote) ? 'Awaiting Quote Approval' : 'Awaiting Quote to be submitted';

                // If there is a quote without approval or rejected status, move the work order to Pause.
                if (! empty($existingQuote) && ! in_array($existingQuote->status, [QuoteStatus::APPROVED(), QuoteStatus::REJECTED()])) {
                    // Update work order state if it is not in Paused
                    if (! $workOrder->state->equals(Paused::class)) {
                        PauseWorkOrder::run($workOrder, $workOrderTask, $pausedReason, $user);
                    }
                } else {
                    if ($payload['trip_end_with'] === Trip::ALL_COMPLETED()) {
                        // Update work order state if it is not in QualityCheck
                        if (! $workOrder->state->equals(QualityCheck::class)) {

                            // Note: As per work order state mechanism we can only move to Quality check from WorkInProgress state.
                            // So we temporally set work order state as WorkInProgress.
                            $workOrder->state = new WorkInProgress($workOrder);
                            $workOrder->state->transitionTo(QualityCheck::class, $user);
                        }
                    }

                    if ($payload['trip_end_with'] === Trip::PARTIALLY_COMPLETED()) {
                        if (in_array($payload['trip_end_with_type'], [Trip::SUBMIT_QUOTE(), Trip::AWAIT_FOR_QUOTE_APPROVAL()])) {
                            // Update work order state if it is not in Paused
                            if (! $workOrder->state->equals(Paused::class)) {
                                PauseWorkOrder::run($workOrder, $workOrderTask, $pausedReason, $user);
                            }

                        } elseif (in_array($payload['trip_end_with_type'], [Trip::FINISH_WITH_ANOTHER_DAY()])) {
                            // Update work order state if it is not in ReadyToSchedule
                            if (! $workOrder->state->equals(ReadyToSchedule::class)) {
                                ReadyToScheduleWorkOrder::run($workOrder, $user);
                            }
                        }
                    }

                    if ($payload['trip_end_with'] === Trip::NO_WORK()) {
                        if (in_array($payload['trip_end_with_type'], [Trip::SUBMIT_QUOTE(), Trip::AWAIT_FOR_QUOTE_APPROVAL()])) {
                            // Update work order state if it is not in Paused
                            if (! $workOrder->state->equals(Paused::class)) {
                                PauseWorkOrder::run($workOrder, $workOrderTask, $pausedReason, $user);
                            }

                        } elseif (in_array($payload['trip_end_with_type'], [Trip::GET_PARTS(), Trip::RESIDENT_DID_NOT_SHOW_UP(), Trip::OTHER()])) {
                            // Update work order state if it is not in ReadyToSchedule
                            if (! $workOrder->state->equals(ReadyToSchedule::class)) {
                                ReadyToScheduleWorkOrder::run($workOrder, $user);
                            }
                        }
                    }
                }
            }

            $trip->save();

            WorkOrderServiceCallLog::create([
                'work_order_service_call_id' => $trip->work_order_service_call_id,
                'organization_id' => $workOrder->organization_id,
                'work_order_id' => $workOrder->work_order_id,
                'work_order_task_id' => $workOrderTask->work_order_task_id,
                'action' => ServiceCallActionTypes::UPDATED(),
                'notes' => $payload['service_notes'] ?? null,
                'action_start_by_user_id' => $user->user_id,
                'action_started_at' => CarbonImmutable::now(),
                'trip_end_with' => $payload['trip_end_with'],
                'trip_end_with_type' => $payload['trip_end_with_type'] ?? null,
                'trip_end_with_reason' => $trip->trip_end_with_reason,
            ]);

            event(new TripUpdatedLulaWebhook($workOrder, $workOrderTask, $trip));
            WorkOrderUpdate::dispatch($workOrder->work_order_uuid);
        });
    }

    /**
     * @throws Exception
     */
    public function asController(WorkOrder $workOrder, WorkOrderTask $workOrderTask, WorkOrderServiceCall $workOrderServiceCall, UpdateTripRequest $request): TripResource|JsonResponse
    {
        try {
            $user = $request->user();

            if (empty($user)) {
                throw TripException::userNotFound();
            }

            if ($workOrder->work_order_id !== $workOrderTask->work_order_id) {
                throw TripException::invalidWorkOrderTask();
            }
            $workOrderServiceCall->load('appointment', 'workOrderTaskMaterials', 'createdQuote', 'media', 'media.workOrder', 'media.workOrder.organization');
            $technicianAppointment = $workOrderServiceCall->appointment;

            if (empty($technicianAppointment)) {
                throw TripException::appointmentNotFound();
            }

            if ($workOrder->work_order_id !== $technicianAppointment->work_order_id) {
                throw TripException::invalidTrip();
            }

            if (! $workOrderServiceCall->state->equals(Ended::class)) {
                throw new CouldNotPerformTransition(__('This transition not allowed to perform with the service call'));
            }

            $workOrder->load('organization');
            $workOrderTask->load('latestServiceCalls');

            $this->handle(
                $request->all(),
                $workOrderTask,
                $workOrder,
                $user,
                $workOrderServiceCall,
                $technicianAppointment
            );
            $workOrderServiceCall->load([
                'workOrderTaskMaterials', 'media.workOrder.organization',
                'lulaAppointment:lula_appointment_id,vendor_instructions,work_order_reference_number,service_category_label',
                'vendorAppointment' => function ($query) {
                    $query->select(
                        'vendor_appointment_id', 'vendor_appointment_uuid', 'organization_id', 'vendor_id', 'vendor_instructions',
                    )
                        ->with([
                            'vendorAllocations:vendor_allocation_id,vendor_appointment_id',
                            'vendor:vendor_id,first_name,last_name,vendor_uuid',
                        ]);
                },
            ]);

            return new TripResource($workOrderServiceCall);

        } catch (TripException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Update Work Order Trip API Failed[TripException]');

            return Response::unprocessableEntity(__($exception->getMessage()));
        } catch (CouldNotPerformTransition $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Update Work Order Trip API Failed[CouldNotPerformTransition]');

            return Response::unprocessableEntity(message: __('This transition not allowed to perform with the work order.'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Update Work Order Trip API Failed[Exception]', additionalInfo: [
                'workOrderId' => $workOrder->work_order_id,
                'workOrderTaskId' => $workOrderTask->work_order_task_id,
                'WorkOrderServiceCallId' => $workOrderServiceCall->work_order_service_call_id,
            ], notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('updateTrip', $request->route('workOrder'));
    }
}

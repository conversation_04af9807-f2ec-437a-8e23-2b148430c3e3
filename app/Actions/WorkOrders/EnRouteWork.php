<?php

namespace App\Actions\WorkOrders;

use App\Enums\ServiceCallActionTypes;
use App\Exceptions\ForbiddenException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Exceptions\WorkOrderException;
use App\Http\Resources\WorkOrder\EnRouteResource;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderServiceCallLog;
use App\States\ServiceCalls\EnRoute;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EnRouteWork extends BaseAction
{
    use AsAction;

    protected static string $method = 'patch';

    public static function getRoute(): string
    {
        return 'api/work-orders/{workOrder}/actions/';
    }

    public static function getSlug(): string
    {
        return 'enRoute';
    }

    public function handle(WorkOrder $workOrder, User $user, WorkOrderServiceCall $latestTrip, bool $sendResidentNotification): WorkOrder
    {
        return DB::transaction(function () use ($workOrder, $user, $latestTrip, $sendResidentNotification): WorkOrder {
            $workOrder->state->transitionTo(WorkInProgress::class, $user);
            $latestTrip->state->transitionTo(EnRoute::class, $user, $sendResidentNotification);

            WorkOrderServiceCallLog::create([
                'work_order_service_call_id' => $latestTrip->work_order_service_call_id,
                'organization_id' => $workOrder->organization_id,
                'work_order_id' => $workOrder->work_order_id,
                'action' => ServiceCallActionTypes::EN_ROUTE(),
                'action_start_by_user_id' => $user->user_id,
                'action_started_at' => CarbonImmutable::now(),
            ]);

            return $workOrder;
        });
    }

    /**
     * @throws Exception|Throwable
     */
    public function asController(Request $request, WorkOrder $workOrder): EnRouteResource|JsonResponse
    {
        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if (! $workOrder->state->canTransitionTo(WorkInProgress::class)) {
                throw WorkOrderException::couldNotPerformTransition();
            }

            $workOrder->load(['latestTrips:work_order_service_call_id,work_order_id,technician_appointment_id,vendor_appointment_id,lula_appointment_id']);

            $latestTrip = $workOrder->latestTrips?->first();

            if (empty($latestTrip)) {
                throw WorkOrderException::activeTripNotFound();
            }

            // Check if the user type can perform this action
            if (! $this->canPerformTripActionByUser($user, $latestTrip)) {
                throw ForbiddenException::AccessDenied();
            }

            DB::beginTransaction();

            $lockedTrip = WorkOrderServiceCall::lockForUpdate()
                ->select(
                    'work_order_service_call_id',
                    'work_order_service_call_uuid',
                    'work_order_service_call_number',
                    'work_order_id',
                    'technician_appointment_id',
                    'vendor_appointment_id',
                    'lula_appointment_id',
                    'state',
                    'state_updated_at',
                    'last_modified_user',
                    'last_modified_at',
                )
                ->findOrFail($latestTrip->work_order_service_call_id);

            if (! $lockedTrip->state->canTransitionTo(EnRoute::class)) {
                throw WorkOrderException::couldNotPerformTransition();
            }

            $sendResidentNotification = $request->notify_resident ?? false;

            $workOrder = $this->handle(
                $workOrder,
                $user,
                $lockedTrip,
                $sendResidentNotification
            );

            DB::commit();

            $workOrder->load([
                'latestTrips:work_order_service_call_id,work_order_id,work_order_service_call_uuid,state,technician_appointment_id,vendor_appointment_id,lula_appointment_id,en_route_at',
            ]);

            return new EnRouteResource($workOrder);
        } catch (Exception $exception) {
            DB::rollBack();

            return $this->handleException($exception, 'En-route');
        }

    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('enroute', $request->route('workOrder'));
    }
}

<?php

namespace App\Actions\WorkOrders;

use App\Enums\ServiceCallActionTypes;
use App\Exceptions\ForbiddenException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Exceptions\WorkOrderException;
use App\Http\Resources\WorkOrder\StartWorkResource;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderServiceCallLog;
use App\States\ServiceCalls\Working;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class StartWork extends BaseAction
{
    use AsAction;

    public static function getRoute(): string
    {
        return 'api/work-orders/{workOrder}/actions/';
    }

    public static function getSlug(): string
    {
        return 'startWork';
    }

    public function handle(WorkOrder $workOrder, User $user, WorkOrderServiceCall $latestTrip): void
    {
        DB::transaction(function () use ($workOrder, $user, $latestTrip) {

            $latestTrip->state->transitionTo(Working::class, $user);

            $latestWorkOrderServiceCallLog = $latestTrip->latestTrip();
            // Check if the latest enroute log then update it.
            if (
                ! empty($latestWorkOrderServiceCallLog) &&
                (
                    $latestWorkOrderServiceCallLog->action === ServiceCallActionTypes::EN_ROUTE() ||
                    $latestWorkOrderServiceCallLog->action === ServiceCallActionTypes::PAUSED()
                )
            ) {
                $latestWorkOrderServiceCallLog->action_ended_at = CarbonImmutable::now();
                $latestWorkOrderServiceCallLog->action_end_by_user_id = $user->user_id;
                $latestWorkOrderServiceCallLog->save();
            }

            WorkOrderServiceCallLog::create([
                'work_order_service_call_id' => $latestTrip->work_order_service_call_id,
                'organization_id' => $workOrder->organization_id,
                'work_order_id' => $workOrder->work_order_id,
                'action' => ServiceCallActionTypes::WORKING(),
                'action_start_by_user_id' => $user->user_id,
                'action_started_at' => CarbonImmutable::now(),
            ]);
        });
    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, WorkOrder $workOrder): StartWorkResource|JsonResponse
    {

        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if (! $workOrder->state->equals(WorkInProgress::class)) {
                throw WorkOrderException::couldNotPerformTransition();
            }

            $workOrder->load(['latestTrips:work_order_service_call_id,work_order_id,technician_appointment_id,vendor_appointment_id,lula_appointment_id']);

            $latestTrip = $workOrder->latestTrips?->first();

            if (empty($latestTrip)) {
                throw WorkOrderException::activeTripNotFound();
            }

            // Check if the user type can perform this action
            if (! $this->canPerformTripActionByUser($user, $latestTrip)) {
                throw ForbiddenException::AccessDenied();
            }

            DB::beginTransaction();

            $lockedServiceCall = WorkOrderServiceCall::lockForUpdate()
                ->with([
                    'latestTripLogs:work_order_service_call_log_id,work_order_service_call_id,action,action_ended_at,action_end_by_user_id',
                ])
                ->select(
                    'work_order_service_call_id',
                    'work_order_service_call_uuid',
                    'work_order_service_call_number',
                    'work_order_id',
                    'technician_appointment_id',
                    'vendor_appointment_id',
                    'lula_appointment_id',
                    'state',
                    'state_updated_at',
                    'is_active',
                    'work_started_at',
                    'en_route_at',
                    'en_route_timer_resumed_at',
                    'drive_time_in_sec',
                    'adjusted_drive_time_in_sec',
                )
                ->findOrFail($latestTrip->work_order_service_call_id);

            if (! $lockedServiceCall->state->canTransitionTo(Working::class)) {
                throw WorkOrderException::activeTripNotFound();
            }

            $this->handle(
                $workOrder,
                $user,
                $lockedServiceCall,
            );

            DB::commit();

            $workOrder->load([
                'latestTrips:work_order_service_call_id,work_order_service_call_uuid,work_order_service_call_number,work_order_id,technician_appointment_id,vendor_appointment_id,lula_appointment_id,work_started_at,state,adjusted_drive_time_in_sec',
            ]);

            return new StartWorkResource($workOrder);
        } catch (Exception $exception) {
            DB::rollBack();

            return $this->handleException($exception, 'Start work');
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('startTimer', $request->route('workOrder'));
    }
}

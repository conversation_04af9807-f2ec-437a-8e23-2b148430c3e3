<?php

namespace App\Actions\WorkOrders;

use App\Enums\QuoteStatus;
use App\Exceptions\CouldNotPerformAction;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Resources\WorkOrder\AwaitingAvailabilityResources;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderTask;
use App\States\WorkOrders\AwaitingAvailability;
use App\States\WorkOrders\Paused;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;

class AwaitingAvailabilityWorkOrder extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'awaitingAvailabilityWorkOrder';
    }

    public function handle(WorkOrder $workOrder, ?User $user = null, ?string $notes = null): WorkOrder
    {
        return DB::transaction(function () use ($workOrder, $user, $notes) {
            $workOrder->state->transitionTo(AwaitingAvailability::class, $user, $notes);

            return $workOrder;
        });

    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, WorkOrder $workOrder, WorkOrderTask $workOrderTask): AwaitingAvailabilityResources|JsonResponse
    {
        $user = $request->user();

        try {
            DB::beginTransaction();

            $this->validateWorkOrder($workOrder, $workOrderTask);

            if ($workOrder->work_order_id !== $workOrderTask->work_order_id) {
                throw new InvalidArgumentException(__('Work order is not matched with the work order task'));
            }

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if (! $workOrder->state->canTransitionTo(AwaitingAvailability::class)) {
                throw new CouldNotPerformTransition(__('This transition[awaiting availability] not allowed to perform with the work order.'));
            }

            if ($workOrder->state->equals(Paused::class)) {
                $workOrderTask->load('latestQuotes:quote_id,work_order_task_id,status');
                $latestQuote = $workOrderTask->latestQuotes->first();

                // Do not allow ReadyToSchedule work order acton if the quote is still open/active
                if (
                    ! empty($latestQuote) && in_array($latestQuote->status, [
                        QuoteStatus::QUOTE_PENDING_REVIEW(),
                        QuoteStatus::PENDING_APPROVAL(),
                    ])
                ) {
                    throw new CouldNotPerformAction(__('This action is not allowed to be performed because there is a pending submitted quote.'));
                }
            }

            $workOrder = WorkOrder::with([
                'organization:organization_id,organization_uuid',
                'latestInvoices:invoices.invoice_id,invoices.work_order_id,invoices.state,invoices.created_at',
                'property:property_id,street_address,unit_number,city,state_id,postal_zip_code',
                'property.state:state_id,state_code',
                'tasks' => function ($query) {
                    return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                        ->with([
                            'latestServiceCalls' => function ($query) {
                                return $query->select(
                                    'work_order_service_calls.work_order_service_call_id',
                                    'work_order_service_calls.technician_appointment_id',
                                    'work_order_service_calls.lula_appointment_id',
                                    'work_order_service_calls.state',
                                    'work_order_service_calls.work_to_perform',
                                    'work_order_service_calls.trip_end_with',
                                    'work_order_service_calls.trip_end_with_type',
                                    'work_order_service_calls.created_at',
                                )->with([
                                    'createdQuote:quote_id,work_order_service_call_id,status',
                                    'appointment' => function ($query) {
                                        $query->select('technician_id', 'technician_appointment_id');
                                    },
                                ]);
                            },
                        ]);
                },
            ])
                ->select(
                    'work_order_id', 'organization_id', 'state', 'work_order_uuid', 'work_order_status_id', 'state_updated_at',
                    'property_id', 'work_order_status_id', 'work_order_reference_number', 'work_order_number', 'timezone_id'
                )
                ->where('work_order_id', $workOrder->work_order_id)
                ->lockForUpdate()
                ->firstOrFail();

            $workOrder = $this->handle(
                $workOrder,
                $user
            );

            DB::commit();

            return new AwaitingAvailabilityResources($workOrder);
        } catch (CouldNotPerformTransition $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage());

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (CouldNotPerformAction $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage());

            return Response::unprocessableEntity(message: __($exception->getMessage()));
        } catch (UserNotFoundException|ModelNotFoundException|InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work order Awaiting Availability API failed due to ' . get_class($exception));

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work order Awaiting Availability API failed[Exception]', additionalInfo: [
                'workOrderId' => $workOrder->work_order_id,
                'workOrderTaskId' => $workOrderTask->work_order_task_id,
            ], notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        // TODO: create a new permission for this action
        return $request->user() && $request->user()->can('readyToSchedule', $request->route('workOrder'));
    }
}

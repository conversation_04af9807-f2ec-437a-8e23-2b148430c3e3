<?php

namespace App\Actions\WorkOrders;

use App\Enums\ServiceCallActionTypes;
use App\Exceptions\ForbiddenException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Exceptions\WorkOrderException;
use App\Http\Resources\WorkOrder\PauseEnRouteResource;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderServiceCallLog;
use App\States\ServiceCalls\EnRoutePaused;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class PauseEnRoute extends BaseAction
{
    use AsAction;

    protected static string $method = 'post';

    public static function getRoute(): string
    {
        return 'api/work-orders/{workOrder}/actions/';
    }

    public static function getSlug(): string
    {
        return 'pauseEnRoute';
    }

    /**
     * @throws Throwable
     */
    public function handle(WorkOrder $workOrder, User $user, WorkOrderServiceCall $latestTrip): WorkOrderServiceCall
    {
        return DB::transaction(function () use ($workOrder, $user, $latestTrip) {

            $latestTrip->state->transitionTo(EnRoutePaused::class, $user);

            $latestTripLog = $latestTrip->latestTripLogs?->first();

            if (! empty($latestTripLog) && $latestTripLog->action = ServiceCallActionTypes::EN_ROUTE()) {
                $latestTripLog->action_ended_at = CarbonImmutable::now();
                $latestTripLog->action_end_by_user_id = $user->user_id;
                $latestTripLog->save();
            }

            WorkOrderServiceCallLog::create([
                'work_order_service_call_id' => $latestTrip->work_order_service_call_id,
                'organization_id' => $workOrder->organization_id,
                'work_order_id' => $workOrder->work_order_id,
                'action' => ServiceCallActionTypes::PAUSE_ENROUTE(),
                'action_start_by_user_id' => $user->user_id,
                'action_started_at' => CarbonImmutable::now(),
            ]);

            return $latestTrip;
        });
    }

    /**
     * @throws Exception|Throwable
     */
    public function asController(Request $request, WorkOrder $workOrder): PauseEnRouteResource|JsonResponse
    {
        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if (! $workOrder->state->equals(WorkInProgress::class)) {
                throw WorkOrderException::couldNotPerformTransition();
            }

            $workOrder->load(['latestTrips:work_order_service_call_id,work_order_id,technician_appointment_id,vendor_appointment_id,lula_appointment_id']);

            $latestTrip = $workOrder->latestTrips?->first();

            if (empty($latestTrip)) {
                throw WorkOrderException::activeTripNotFound();
            }

            // Check if the user type can perform this action
            if (! $this->canPerformTripActionByUser($user, $latestTrip)) {
                throw ForbiddenException::AccessDenied();
            }

            DB::beginTransaction();

            $lockedTrip = WorkOrderServiceCall::lockForUpdate()
                ->with([
                    'latestTripLogs:work_order_service_call_log_id,work_order_service_call_id,action,action_ended_at,action_end_by_user_id',
                ])
                ->select(
                    'work_order_service_call_id',
                    'work_order_service_call_uuid',
                    'work_order_service_call_number',
                    'work_order_id',
                    'technician_appointment_id',
                    'vendor_appointment_id',
                    'lula_appointment_id',
                    'state',
                    'state_updated_at',
                    'last_modified_user',
                    'last_modified_at',
                    'en_route_at',
                    'en_route_timer_paused_at',
                    'en_route_timer_resumed_at',
                    'drive_time_in_sec',
                    'adjusted_drive_time_in_sec',
                )
                ->findOrFail($latestTrip->work_order_service_call_id);

            if (! $lockedTrip->state->canTransitionTo(EnRoutePaused::class)) {
                throw WorkOrderException::couldNotPerformTransition();
            }

            $this->handle(
                $workOrder,
                $user,
                $lockedTrip,
            );

            DB::commit();

            $workOrder->load([
                'latestTrips:work_order_service_call_id,work_order_id,work_order_service_call_uuid,state,technician_appointment_id,en_route_at,en_route_timer_paused_at,adjusted_drive_time_in_sec',
            ]);

            return new PauseEnRouteResource($workOrder);

        } catch (Exception $exception) {
            DB::rollBack();

            return $this->handleException($exception, 'Pause En-route');
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('enroute', $request->route('workOrder'));
    }
}

<?php

namespace App\Actions\WorkOrders;

use App\Enums\UserTypes;
use App\Exceptions\ForbiddenException;
use App\Exceptions\IssueException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Exceptions\WorkOrderException;
use App\Helpers\Helper;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderTask;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;
use Lorisleiva\Actions\ActionRequest;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;

abstract class BaseAction
{
    protected static string $method = 'post';

    public static function getSlug(): string
    {
        throw new Exception('Undefined slug');
    }

    public static function getRoute(): string
    {
        return 'api/work-orders/{workOrder}/tasks/{workOrderTask}/actions/';
    }

    public static function routes(Router $router): void
    {
        $router->{static::$method}(static::getRoute() . static::getSlug(), static::class)
            ->middleware(['api', 'auth.cognito', 'tenant'])->name('workOrder.action.' . static::getSlug());
    }

    /**
     * @throws Exception
     */
    public function authorize(ActionRequest $request): bool
    {
        return false;
    }

    /**
     * @throws InvalidArgumentException
     */
    public function validateWorkOrder(WorkOrder $workOrder, ?WorkOrderTask $workOrderTask = null): void
    {
        if (empty($workOrder->priority)) {
            throw new InvalidArgumentException(__('Work order has empty priority'));
        }

        if ($workOrderTask !== null && empty($workOrderTask->problem_diagnosis_id)) {
            throw new InvalidArgumentException(__('Work order task has empty problem diagnosis'));
        }
    }

    /**
     * Handle exceptions for the index method.
     */
    public function handleException(Exception $exception, string $action): JsonResponse
    {
        Helper::exceptionLog(exception: $exception, message: 'Work order ' . $action . ' API failed');

        return match (true) {
            $exception instanceof CouldNotPerformTransition
            || $exception instanceof UserNotFoundException
            || $exception instanceof WorkOrderException
            || $exception instanceof IssueException
            || $exception instanceof ModelNotFoundException => Response::unprocessableEntity(
                message: $exception->getMessage()
            ),

            $exception instanceof ForbiddenException => Response::forbidden(
                message: $exception->getMessage()
            ),

            default => Response::internalServerError(),
        };
    }

    protected function canPerformTripActionByUser(User $user, WorkOrderServiceCall $serviceCall): bool
    {
        $userTypeAllowed = in_array($user->user_type, UserTypes::tripActionAllowedType(), true);

        if (! $userTypeAllowed) {
            return false;
        }

        $validate = match ($user->user_type) {
            UserTypes::TECHNICIAN() => static function () use ($user, $serviceCall): bool {
                $serviceCall->load('technicianAppointment:technician_appointment_id,technician_id');
                $technicianAppointment = $serviceCall->technicianAppointment;
                if (! $technicianAppointment) {
                    return false;
                }
                $user->load('technician:technician_id,user_id');

                return $user->technician?->technician_id === $technicianAppointment->technician_id;
            },
            UserTypes::VENDOR() => static function (): bool {
                // TODO:: Validate request for vendor.
                return false;
            },
            UserTypes::ACCOUNT_USER() => static function () use ($serviceCall): bool {
                if (! array_filter([$serviceCall->technician_appointment_id, $serviceCall->vendor_appointment_id, $serviceCall->lula_appointment_id])) {
                    // No appointment found on the service call.
                    return false;
                }

                return true;
            },
            default => static function (): bool {
                return false;
            }
        };

        return $validate();

    }
}

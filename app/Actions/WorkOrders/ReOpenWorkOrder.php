<?php

namespace App\Actions\WorkOrders;

use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Resources\WorkOrder\ReOpenResource;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderTask;
use App\States\WorkOrders\AwaitingAvailability;
use App\States\WorkOrders\Created;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;
use Spatie\ModelStates\Exceptions\TransitionNotFound;

class ReOpenWorkOrder extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'reOpenWorkOrder';
    }

    public function handle(WorkOrder $workOrder, WorkOrderTask $workOrderTask, User $user): void
    {
        DB::transaction(function () use ($workOrder, $user) {
            // TODO prepare work order to be in a valid re-opened state

            $workOrder->state->transitionTo(AwaitingAvailability::class, $user);

        });
    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, WorkOrder $workOrder, WorkOrderTask $workOrderTask): ReOpenResource|JsonResponse
    {
        $user = $request->user();

        try {
            DB::beginTransaction();

            $workOrder = WorkOrder::lockForUpdate()->findOrFail($workOrder->work_order_id);

            if ($workOrder->work_order_id !== $workOrderTask->work_order_id) {
                throw new InvalidArgumentException(__('Work order is not matched with the work order task'));
            }

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if (! $workOrder->state->canTransitionTo(Created::class)) {
                throw new CouldNotPerformTransition(__('This transition not allowed to perform with the work order'));
            }

            $workOrderTask->load('latestServiceCalls.appointment');

            $this->handle(
                $workOrder,
                $workOrderTask,
                $user
            );

            DB::commit();

            return new ReOpenResource($workOrder);
        } catch (CouldNotPerformTransition|TransitionNotFound $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'This transition[re-open] not allowed to perform with the work order.');

            return Response::unprocessableEntity(message: __('This transition not allowed to perform with the work order.'));
        } catch (UserNotFoundException|InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work order Re-open API failed due to ' . get_class($exception));

            return Response::notFound(message: $exception->getMessage());

        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work order Re-open API failed[Exception]', additionalInfo: [
                'workOrderId' => $workOrder->work_order_id,
                'workOrderTaskId' => $workOrderTask->work_order_task_id,
            ], notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('reOpen', $request->route('workOrder'));
    }
}

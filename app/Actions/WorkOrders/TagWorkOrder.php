<?php

namespace App\Actions\WorkOrders;

use App\Events\WorkOrder\WorkOrderTagUpdated;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Requests\WorkOrder\TagRequest;
use App\Http\Resources\Tag\TagResource;
use App\Models\Tag;
use App\Models\User;
use App\Models\WorkOrder;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class TagWorkOrder extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'applyTags';
    }

    public static function routes(Router $router): void
    {
        $router->{static::$method}('api/work-orders/{workOrder}/actions/' . static::getSlug(), static::class)
            ->middleware(['api', 'auth.cognito', 'tenant'])->name('workOrder.action.' . static::getSlug());
    }

    /**
     * @param  array<string, mixed>  $payload
     */
    public function handle(WorkOrder $workOrder, User $user, array $payload): void
    {

        $workOrderTags = Tag::whereUuid($payload['tags'])->get();

        $workOrderTags = $workOrderTags->sortBy(function ($tag) use ($payload) {
            return array_search($tag['tag_uuid'], $payload['tags']);
        });

        if ($payload['action'] == 'attach') {
            $workOrder->tags()->sync($workOrderTags->pluck('tag_id')->toArray());
        } else {
            $workOrder->tags()->detach($workOrderTags->pluck('tag_id')->toArray());
        }

        $workOrder->load(['tags' => function ($query) {
            $query->withCount('workOrders');
        }]);

        WorkOrderTagUpdated::broadcast($workOrder, $payload['action'], $workOrderTags)->toOthers();
    }

    /**
     * @throws Exception
     */
    public function asController(TagRequest $request, WorkOrder $workOrder): AnonymousResourceCollection|JsonResponse
    {
        $user = $request->user();

        try {
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $this->handle(
                $workOrder,
                $user,
                $request->all()
            );

            return TagResource::collection($workOrder->tags);
        } catch (UserNotFoundException|ModelNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order Tags API Failed.');

            return Response::notFound(message: $exception->getMessage());
        } catch (QueryException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order Tags api failed');

            return Response::internalServerError(message: __('The request can\'t be processed due to QueryException.'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, additionalInfo: [
                'workOrderId' => $workOrder->work_order_id,
            ], message: 'Work Order Tags API Failed');

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('changeTags', $request->route('workOrder'));
    }
}

<?php

namespace App\Actions\WorkOrders;

use App\Enums\Boolean;
use App\Enums\CostTypes;
use App\Enums\MediaType;
use App\Enums\ServiceCallActionTypes;
use App\Enums\Trip;
use App\Enums\UserTypes;
use App\Exceptions\ForbiddenException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Exceptions\WorkOrderException;
use App\Helpers\Helper;
use App\Http\Requests\WorkOrder\CompleteRequest;
use App\Http\Resources\WorkOrder\CompletedResource;
use App\Models\TechnicianAppointment;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderServiceCallLog;
use App\Models\WorkOrderTask;
use App\Models\WorkOrderTaskMaterial;
use App\Packages\OrganizationRolePermission\Exceptions\OrganizationNotFound;
use App\States\ServiceCalls\Ended;
use App\States\ServiceCalls\Paused;
use App\States\ServiceCalls\Working;
use App\States\WorkOrders\QualityCheck;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;
use Throwable;

class CompleteWorkOrder extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'completeWorkOrder';
    }

    /**
     * @param  array<string,mixed>  $payload
     */
    public function handle(
        array $payload,
        WorkOrder $workOrder,
        WorkOrderTask $workOrderTask,
        WorkOrderServiceCall $serviceCall,
        TechnicianAppointment $technicianAppointment,
        User $user,
    ): void {
        DB::transaction(function () use ($payload, $workOrder, $workOrderTask, $serviceCall, $user) {
            $materials = $payload['materials'] ?? [];
            //save material details in to WorkOrderTaskMaterial table
            if (! empty($materials)) {
                $materialPayload = [];
                foreach ($materials as $material) {
                    $unit_price = $material['unit_price_in_cents'] ?? 0;
                    $quantity = $material['quantity'] ?? 1;
                    $materialPayload[] = [
                        'organization_id' => $workOrder->organization_id,
                        'label' => $material['label'],
                        'created_user_id' => $user->user_id,
                        'cost_in_cents' => $material['cost_in_cents'] ?? $unit_price * $quantity,
                        'cost_type' => ! empty($material['unit_price_in_cents']) ? CostTypes::PER_UNIT() : CostTypes::TOTAL(),
                        'quantity' => $quantity,
                        'quantity_type' => $material['quantity_type'],
                        'work_order_service_call_id' => $serviceCall->work_order_service_call_id,
                    ];
                }

                $workOrderTask->materials()->createMany($materialPayload);
            }

            // Service call update (Trip update)
            $serviceCall->state->transitionTo(Ended::class, $payload, $user);
            $serviceCallLogNote = null;
            $existingQuote = $serviceCall->createdQuote;

            // if there is a quote with pending decision submitted, move the WO to Pause.
            if (in_array($payload['trip_end_with'], [Trip::PARTIALLY_COMPLETED(), Trip::NO_WORK()])) {
                if (in_array($payload['trip_end_with_type'], [Trip::SUBMIT_QUOTE(), Trip::AWAIT_FOR_QUOTE_APPROVAL()])) {
                    $pausedReason = ! empty($existingQuote) ? 'Awaiting Quote Approval' : 'Awaiting Quote to be submitted';
                    PauseWorkOrder::run($workOrder, $workOrderTask, $pausedReason, $user);
                    $serviceCallLogNote = 'Work order paused';
                } elseif (! empty($existingQuote)) {
                    $pausedReason = 'Awaiting Quote Approval';
                    PauseWorkOrder::run($workOrder, $workOrderTask, $pausedReason, $user);
                    $serviceCallLogNote = 'Work order paused';
                } else {
                    ReadyToScheduleWorkOrder::run($workOrder, $user);
                }
            } elseif (in_array($payload['trip_end_with'], [Trip::ALL_COMPLETED()]) && ! empty($existingQuote)) {
                $pausedReason = 'Awaiting Quote Approval';
                PauseWorkOrder::run($workOrder, $workOrderTask, $pausedReason, $user);
                $serviceCallLogNote = 'Work order paused';
            } else {
                $workOrder->state->transitionTo(QualityCheck::class, $user);
            }

            $latestWorkOrderServiceCallLog = $serviceCall->latestServiceCallLogs->first();
            if (
                ! empty($latestWorkOrderServiceCallLog) &&
                $latestWorkOrderServiceCallLog->action === ServiceCallActionTypes::WORKING()
            ) {
                $latestWorkOrderServiceCallLog->action_ended_at = CarbonImmutable::now();
                $latestWorkOrderServiceCallLog->action_end_by_user_id = $user->user_id;
                $latestWorkOrderServiceCallLog->save();
            }

            WorkOrderServiceCallLog::create([
                'work_order_service_call_id' => $serviceCall->work_order_service_call_id,
                'organization_id' => $workOrder->organization_id,
                'work_order_id' => $workOrder->work_order_id,
                'work_order_task_id' => $workOrderTask->work_order_task_id,
                'action' => ServiceCallActionTypes::COMPLETED(),
                'notes' => $serviceCallLogNote,
                'action_start_by_user_id' => $user->user_id,
                'action_started_at' => CarbonImmutable::now(),
                'trip_end_with' => $payload['trip_end_with'],
                'trip_end_with_type' => $payload['trip_end_with_type'] ?? null,
                'trip_end_with_reason' => $payload['reason'] ?? null,
            ]);
        });
    }

    public function asController(CompleteRequest $request, WorkOrder $workOrder, WorkOrderTask $workOrderTask): CompletedResource|JsonResponse
    {
        try {
            DB::beginTransaction();
            if ($workOrder->work_order_id !== $workOrderTask->work_order_id) {
                throw WorkOrderException::invalidWorkOrder();
            }

            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFound;
            }

            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if (! $workOrder->state->canTransitionTo(QualityCheck::class)) {
                throw new CouldNotPerformTransition(__('This transition not allowed to perform with the work order'));
            }

            $workOrderTask->load('latestServiceCalls.appointment', 'latestServiceCalls.latestServiceCallLogs', 'latestServiceCalls.createdQuote');

            $serviceCall = $workOrderTask->latestServiceCalls->isNotEmpty() ? $workOrderTask->latestServiceCalls->first() : null;

            if (empty($serviceCall)) {
                throw new ModelNotFoundException(__('Existing service call not found.'));
            }

            $lockedServiceCall = WorkOrderServiceCall::lockForUpdate()
                ->findOrFail($serviceCall->work_order_service_call_id);

            if (! $lockedServiceCall->state->equals(Working::class, Paused::class)) {
                throw new CouldNotPerformTransition(__('This transition not allowed to perform with the work order'));
            }

            $technicianAppointment = $serviceCall->appointment;

            if (empty($technicianAppointment)) {
                throw new ModelNotFoundException(__('Existing appointment not found.'));
            }

            if ($user->user_type === UserTypes::TECHNICIAN()) {
                if ($user->technician?->technician_id != $technicianAppointment->technician_id) {
                    throw ForbiddenException::accessDenied();
                }
            }

            $this->handle(
                $request->all(),
                $workOrder,
                $workOrderTask,
                $serviceCall,
                $technicianAppointment,
                $user,
            );

            DB::commit();

            $workOrderTask->load([
                'latestServiceCalls' => function ($query) {
                    return $query->with([
                        'media' => function ($query) {
                            return $query->with([
                                'workOrder:work_orders.work_order_id,work_orders.work_order_uuid,work_orders.organization_id',
                                'workOrder.organization:organizations.organization_id,organizations.domain',
                            ])
                                ->wherePivot('has_thumbnail', Boolean::YES())
                                ->wherePivotIn('media_type', [MediaType::AFTER_MEDIA(), MediaType::BEFORE_MEDIA()]);
                        },
                        'workOrderTaskMaterials',
                        'createdQuote', 'assignedQuote:quote_id,quote_number',
                        'appointment' => function ($query) {
                            $query->select(
                                'actual_start_time', 'actual_end_time', 'rescheduled_reason', 'adjusted_elapse_time_in_sec', 'enroute_at',
                                'adjusted_travel_time_in_sec', 'technician_id', 'technician_appointment_id',
                            )
                                ->with([
                                    'technician:technician_id,technician_uuid,user_id',
                                    'technician.user:user_id,first_name,middle_name,last_name,profile_pic',
                                ]);
                        },
                        'lulaAppointment' => function ($query) {
                            $query->select(
                                'lula_appointment_id', 'vendor_instructions', 'work_order_reference_number', 'service_category_label',
                                'scheduled_start_time', 'scheduled_end_time', 'estimated_return_start_time', 'estimated_return_end_time', 'paused_reason', 'rescheduled_reason'
                            );
                        },
                        'vendorAppointment' => function ($query) {
                            $query->select(
                                'vendor_appointment_id', 'vendor_appointment_uuid', 'organization_id', 'vendor_id', 'vendor_instructions',
                            )
                                ->with([
                                    'vendorAllocations:vendor_allocation_id,vendor_appointment_id',
                                    'vendor:vendor_id,first_name,last_name,vendor_uuid',
                                ]);
                        },
                    ]);
                },
            ]);
            $workOrder->setRelation('tasks', collect([$workOrderTask]));

            return new CompletedResource($workOrder);
        } catch (CouldNotPerformTransition $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'This transition[quality check] not allowed to perform with the work order');

            return Response::unprocessableEntity(message: __('This transition not allowed to perform with the work order.'));
        } catch (OrganizationNotFound|UserNotFoundException|WorkOrderException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order complete api failed due to ' . get_class($exception));

            return Response::unprocessableEntity(message: __($exception->getMessage()));
        } catch (QueryException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order complete api failed[QueryException]', notify: true);

            return Response::internalServerError(message: __('The request can\'t be processed due to QueryException.'));
        } catch (ForbiddenException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order complete api Failed[ForbiddenException]');

            return Response::forbidden(message: __($exception->getMessage()));
        } catch (Exception|Throwable $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order complete api failed due to ' . get_class($exception), notify: true);

            return Response::internalServerError(message: $exception->getMessage());
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('complete', $request->route('workOrder'));
    }
}

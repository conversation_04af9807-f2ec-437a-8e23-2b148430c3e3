<?php

namespace App\Actions\Quotes;

use App\Enums\Boolean;
use App\Enums\MediaType;
use App\Enums\QuoteStatus;
use App\Enums\QuoteTaskStatus;
use App\Events\WorkOrder\PublicWebhook\Lula\QuoteRejectLulaWebhook;
use App\Events\WorkOrder\WorkOrderUpdate;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Exceptions\QuoteException;
use App\Helpers\Helper;
use App\Http\Resources\WorkOrder\QuoteRejectResource;
use App\Models\Quote;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderTask;
use App\States\WorkOrders\Paused;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisle<PERSON>\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class RejectQuote extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'rejectQuote';
    }

    public static function getRoute(): string
    {
        return 'api/work-orders/{workOrder}/tasks/{workOrderTask}/quotes/{quote}/actions/';
    }

    public function handle(WorkOrder $workOrder, WorkOrderTask $workOrderTask, Quote $quote, bool $preventDispatchWebhook = false, ?User $user = null): void
    {
        DB::transaction(function () use ($workOrder, $workOrderTask, $quote, $preventDispatchWebhook, $user) {

            if ($workOrder->work_order_id !== $workOrderTask->work_order_id) {
                throw QuoteException::invalidWorkOrderTask();
            }

            if ($quote->work_order_id !== $workOrder->work_order_id) {
                throw QuoteException::invalidQuote();
            }

            if (! in_array($quote->status, [QuoteStatus::QUOTE_PENDING_REVIEW(), QuoteStatus::PENDING_APPROVAL()])) {
                throw QuoteException::invalidQuoteStatus($quote->status);
            }

            $quote->status = QuoteStatus::REJECTED();
            $quote->rejected_user_id = $user->user_id ?? null;
            $quote->rejected_at = CarbonImmutable::now();
            $quote->save();

            //Rearrange quote task number and update status
            $quote->load(['quoteTasks' => function ($query) {
                $query->select('quote_task_id', 'quote_id', 'quote_task_number', 'status')
                    ->orderBy('created_at', 'asc');
            }]);

            $quoteTasks = $quote->quoteTasks;
            if ($quoteTasks->isNotEmpty()) {
                $taskNumber = 1;
                foreach ($quoteTasks as $quoteTask) {
                    $quoteTask->quote_task_number = $taskNumber;
                    $quoteTask->status = QuoteTaskStatus::REJECTED();
                    $quoteTask->save();
                    $taskNumber = $taskNumber + 1;
                }
            }

            $workOrder->load('organization:organization_id,webhook_secret_key,webhook_enabled,webhook_api_url,webhook_secret_key,organization_uuid');

            QuoteRejectLulaWebhook::dispatch($workOrder, $workOrderTask, $quote, $user, $preventDispatchWebhook);
            WorkOrderUpdate::dispatch($workOrder->work_order_uuid);
        });
    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, WorkOrder $workOrder, WorkOrderTask $workOrderTask, Quote $quote): QuoteRejectResource|JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if (! ($workOrder->state->equals(Paused::class))) {
                throw QuoteException::invalidWorkOrderStatus($workOrder->state->label());
            }

            $this->validateRouteParameterRelation($workOrder, $workOrderTask, $quote);

            $lockedQuote = Quote::where('quote_id', $quote->quote_id)
                ->lockForUpdate()
                ->firstOrFail();

            $this->handle(
                $workOrder,
                $workOrderTask,
                $lockedQuote,
                false,
                $user
            );

            DB::commit();

            $lockedQuote->load([
                'workOrder.tasks.latestServiceCalls.createdQuote',
                'submittedUser:user_id,user_uuid,first_name,last_name,middle_name',
                'reviewedUser:user_id,user_uuid,first_name,last_name,middle_name',
                'approvedUser:user_id,user_uuid,first_name,last_name,middle_name',
                'rejectedUser:user_id,user_uuid,first_name,last_name,middle_name',
                'quoteTasks' => function ($query) {
                    return $query->with([
                        'media' => function ($query) {
                            return $query->with([
                                'workOrder:work_orders.work_order_id,work_orders.organization_id,work_orders.work_order_uuid',
                                'workOrder.organization:organizations.organization_id,organizations.domain',
                            ])
                                ->wherePivot('has_thumbnail', Boolean::YES())
                                ->wherePivot('media_type', MediaType::QUOTE_TASK());
                        },
                        'quoteTaskMaterials',
                    ])
                        ->orderBy('created_at');
                },
            ]);

            return new QuoteRejectResource($lockedQuote);
        } catch (UserNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Reject quote API failed[UserNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (QuoteException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Reject quote API failed[QuoteException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Reject quote API failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('reject', $request->route('quote'));
    }
}

<?php

namespace App\Actions\Quotes;

use App\Enums\Boolean;
use App\Enums\CostTypes;
use App\Enums\MarkUpFeeTypes;
use App\Enums\MediaType;
use App\Enums\QuoteStatus;
use App\Enums\QuoteTaskStatus;
use App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskCreatedLulaWebhook;
use App\Events\WorkOrder\WorkOrderUpdate;
use App\Exceptions\NotFoundException;
use App\Exceptions\QuoteException;
use App\Helpers\Helper;
use App\Http\Requests\WorkOrder\Quote\TaskCreateRequest;
use App\Http\Resources\WorkOrder\QuoteTaskResource;
use App\Models\Media;
use App\Models\Quote;
use App\Models\QuoteTask;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderMedia;
use App\Models\WorkOrderTask;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateQuoteTask extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'createQuoteTask';
    }

    public static function getRoute(): string
    {
        return 'api/work-orders/{workOrder}/tasks/{workOrderTask}/quotes/{quote}/quote-tasks/actions/';
    }

    /**
     * @param  array<string, mixed>  $payload
     */
    public function handle(WorkOrder $workOrder, WorkOrderTask $workOrderTask, Quote $quote, array $payload, User $user): QuoteTask
    {
        $quote->load('quoteTasks:quote_task_id,quote_id');

        return DB::transaction(function () use ($workOrder, $quote, $payload, $user) {
            $laborCostInCents = $payload['labor_cost_in_cents'] ?? $payload['cost_in_cents'] ?? 0;
            $markupFeeTypeValue = $payload['markup_fee_type_value'] ?? 0;
            $laborMarkupFeeInCents = (! empty($payload['markup_fee_type']) && $payload['markup_fee_type'] == MarkUpFeeTypes::FIXED())
                ? ($markupFeeTypeValue * 100)
                : (int) round($laborCostInCents * ($markupFeeTypeValue / 100));

            $quoteTask = QuoteTask::create([
                'organization_id' => $quote->organization_id,
                'work_order_task_id' => $quote->work_order_task_id,
                'user_id' => $user->user_id,
                'quote_task_number' => count($quote->quoteTasks) + 1,
                'markup_fee_type' => $payload['markup_fee_type'] ?? null,
                'markup_fee_type_value' => $markupFeeTypeValue,
                'markup_fee_in_cents' => $laborMarkupFeeInCents,
                'cost_in_cents' => $laborCostInCents,
                'total_cost_in_cents' => $laborCostInCents + $laborMarkupFeeInCents,
                'estimated_time' => $payload['estimated_time'] ?? null,
                'description' => $payload['description'],
                'status' => QuoteTaskStatus::QUOTE_PENDING_REVIEW(),
                'quote_id' => $quote->quote_id,
            ]);

            if (! empty($payload['media_ids'])) {
                $taskMediaIds = Media::join('work_order_media', 'work_order_media.media_id', 'media.media_id')
                    ->select(['media.media_uuid', 'media.media_id', 'work_order_media.work_order_media_id', 'work_order_media.media_type'])
                    ->where('work_order_media.media_type', MediaType::QUOTE_TASK())
                    ->whereUuid($payload['media_ids'])
                    ->pluck('work_order_media_id')
                    ->toArray();

                if (! count($taskMediaIds)) {
                    throw QuoteException::mediaNotMatched();
                }

                WorkOrderMedia::whereIn('work_order_media_id', $taskMediaIds)
                    ->update([
                        'quote_task_id' => $quoteTask->quote_task_id,
                    ]);
            }

            // Insert Quote task material
            if (! empty($payload['materials'])) {
                $materialPayload = [];

                foreach ($payload['materials'] as $material) {
                    $costType = ! empty($material['unit_price_in_cents']) ? CostTypes::PER_UNIT() : CostTypes::TOTAL();
                    $materialCostInCents = $material['material_cost_in_cents'] ?? $material['cost_in_cents'] ?? 0;
                    $markupFeeTypeValue = $material['markup_fee_type_value'] ?? config('workorder.quote.markup_fee.default_values.material');
                    $markupFeeType = $material['markup_fee_type'] ?? config('workorder.quote.markup_fee.default_type');

                    if ($costType == CostTypes::PER_UNIT()) {
                        $unitPrice = $material['unit_price_in_cents'] ?? 0;
                        $quantity = $material['quantity'] ?? 1;
                        $materialCostInCents = $unitPrice * $quantity;
                    }

                    $materialMarkupFeeInCents = ($markupFeeType == MarkUpFeeTypes::FIXED())
                        ? ($markupFeeTypeValue * 100)
                        : (int) round($materialCostInCents * ($markupFeeTypeValue / 100));

                    $materialPayload[] = [
                        'organization_id' => $quote->organization_id,
                        'label' => $material['label'],
                        'markup_fee_type' => $markupFeeType,
                        'markup_fee_type_value' => $markupFeeTypeValue,
                        'markup_fee_in_cents' => $materialMarkupFeeInCents,
                        'cost_in_cents' => $materialCostInCents,
                        'total_cost_in_cents' => $materialCostInCents + $materialMarkupFeeInCents,
                        'cost_type' => ! empty($material['unit_price_in_cents']) ? CostTypes::PER_UNIT() : CostTypes::TOTAL(),
                        'quantity' => $material['quantity'] ?? 1,
                        'quantity_type' => $material['quantity_type'],
                    ];
                }

                $quoteTask->quoteTaskMaterials()->createMany($materialPayload);
            }

            //update quote modified details
            $quote->last_modified_user_id = $user->user_id;
            $quote->last_modified_at = CarbonImmutable::now();
            $quote->save();

            QuoteTaskCreatedLulaWebhook::dispatch($workOrder, $quoteTask);
            WorkOrderUpdate::dispatch($workOrder->work_order_uuid);

            return $quoteTask;
        });
    }

    /**
     * @throws Exception
     */
    public function asController(TaskCreateRequest $request, WorkOrder $workOrder, WorkOrderTask $workOrderTask, Quote $quote): QuoteTaskResource|JsonResponse
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw NotFoundException::resourceNotFound('User');
            }

            $this->validateRouteParameterRelation($workOrder, $workOrderTask, $quote);

            if (! ($workOrder->state->equals(Paused::class, WorkInProgress::class))) {
                throw QuoteException::invalidWorkOrderStatus($workOrder->state->label());
            }

            if ($quote->status !== QuoteStatus::QUOTE_PENDING_REVIEW()) {
                throw QuoteException::invalidQuoteStatus($quote->status);
            }

            $payload = $request->all();
            $workOrder->load('organization:organization_id,webhook_secret_key,webhook_enabled,webhook_api_url,webhook_secret_key,organization_uuid');

            $quoteTask = $this->handle(
                $workOrder,
                $workOrderTask,
                $quote,
                $payload['quote_task'],
                $user
            );

            $quoteTask->load([
                'media' => function ($query) {
                    return $query->with([
                        'workOrder:work_orders.work_order_id,work_orders.organization_id,work_orders.work_order_uuid',
                        'workOrder.organization:organizations.organization_id,organizations.domain',
                    ])
                        ->wherePivot('has_thumbnail', Boolean::YES())
                        ->wherePivot('media_type', MediaType::QUOTE_TASK());
                },
                'quoteTaskMaterials',
            ]);

            return new QuoteTaskResource($quoteTask);
        } catch (QuoteException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Create Quote Task API failed [QuoteException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (NotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Create Quote Task API Failed [NotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Create Quote Task API Failed[Exception]', additionalInfo: [
                'workOrderId' => $workOrder->work_order_id,
                'workOrderTaskId' => $workOrderTask->work_order_task_id,
                'quoteId' => $quote->quote_id,
            ], notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('createQuoteTask', $request->route('quote'));
    }
}

<?php

namespace App\Actions\Quotes;

use App\Enums\QuoteStatus;
use App\Events\WorkOrder\PublicWebhook\Lula\QuoteTaskDeletedLulaWebhook;
use App\Events\WorkOrder\WorkOrderUpdate;
use App\Exceptions\NotFoundException;
use App\Exceptions\QuoteException;
use App\Helpers\Helper;
use App\Http\Resources\WorkOrder\Quote\TaskDeleteResource;
use App\Models\Quote;
use App\Models\QuoteTask;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderTask;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisle<PERSON>\Actions\Concerns\AsAction;

class DeleteQuoteTask extends BaseAction
{
    use AsAction;

    protected static string $method = 'delete';

    public static function getSlug(): string
    {
        return 'deleteQuoteTask';
    }

    public function handle(WorkOrder $workOrder, Quote $quote, QuoteTask $quoteTask, User $user): void
    {
        DB::transaction(function () use ($workOrder, $quote, $quoteTask, $user) {
            //update quote modified details
            $quote->last_modified_user_id = $user->user_id;
            $quote->last_modified_at = CarbonImmutable::now();
            $quote->save();

            $quoteTask->quote_task_number = null;
            $quoteTask->save();

            $quoteTask->delete();

            //Rearrange quote task number
            $quote->load(['quoteTasks' => function ($query) {
                $query->select('quote_task_id', 'quote_id', 'quote_task_number')
                    ->orderBy('created_at', 'asc');
            }]);

            $quoteTasks = $quote->quoteTasks;
            if ($quoteTasks->isNotEmpty()) {
                $taskNumber = 1;
                foreach ($quoteTasks as $qTask) {
                    $qTask->quote_task_number = $taskNumber;
                    $qTask->save();
                    $taskNumber = $taskNumber + 1;
                }
            }
            QuoteTaskDeletedLulaWebhook::dispatch($workOrder, $quoteTask);
            WorkOrderUpdate::dispatch($workOrder->work_order_uuid);
        });
    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, WorkOrder $workOrder, WorkOrderTask $workOrderTask, Quote $quote, QuoteTask $quoteTask): TaskDeleteResource|JsonResponse
    {
        $user = $request->user();

        try {
            if (empty($user)) {
                throw NotFoundException::resourceNotFound('User');
            }

            $this->validateRouteParameterRelation($workOrder, $workOrderTask, $quote, $quoteTask);

            if (! ($workOrder->state->equals(Paused::class, WorkInProgress::class))) {
                throw QuoteException::invalidWorkOrderStatus($workOrder->state->label());
            }

            if ($quote->status !== QuoteStatus::QUOTE_PENDING_REVIEW()) {
                throw QuoteException::invalidQuoteStatus($quote->status);
            }

            //Check at least one quote task exists for the quote
            if ($quote->quoteTasks->count() <= 1) {
                throw QuoteException::atLeastOneQuoteTask();
            }

            $workOrder->load('organization:organization_id,webhook_secret_key,webhook_enabled,webhook_api_url,webhook_secret_key,organization_uuid');

            $this->handle(
                $workOrder,
                $quote,
                $quoteTask,
                $user
            );

            return new TaskDeleteResource($quoteTask);

        } catch (NotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Delete Quote Task API Failed [NotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (QuoteException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Delete Quote Task API failed[QuoteException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Delete Quote Task API Failed[Exception]', additionalInfo: [
                'workOrderId' => $workOrder->work_order_id,
                'workOrderTaskId' => $workOrderTask->work_order_task_id,
                'quoteId' => $quote->quote_id,
                'quoteTaskId' => $quoteTask->quote_task_id,
            ], notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('deleteQuoteTask', [$request->route('quote'), $request->route('quoteTask')]);
    }
}

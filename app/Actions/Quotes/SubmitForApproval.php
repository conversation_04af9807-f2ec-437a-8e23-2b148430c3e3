<?php

namespace App\Actions\Quotes;

use App\Enums\Boolean;
use App\Enums\MediaType;
use App\Enums\QuoteStatus;
use App\Enums\QuoteTaskStatus;
use App\Events\WorkOrder\PublicWebhook\Lula\QuoteSubmitForApprovalLulaWebhook;
use App\Events\WorkOrder\WorkOrderUpdate;
use App\Exceptions\NotFoundException;
use App\Exceptions\QuoteException;
use App\Helpers\Helper;
use App\Http\Resources\WorkOrder\QuoteResource;
use App\Models\Quote;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderTask;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class SubmitForApproval extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'submitForApproval';
    }

    public static function getRoute(): string
    {
        return 'api/work-orders/{workOrder}/tasks/{workOrderTask}/quotes/{quote}/actions/';
    }

    public function handle(WorkOrder $workOrder, WorkOrderTask $workOrderTask, Quote $quote, bool $preventDispatchWebhook = false, ?User $user = null): void
    {
        DB::transaction(function () use ($workOrder, $workOrderTask, $quote, $preventDispatchWebhook, $user) {
            $quote->status = QuoteStatus::PENDING_APPROVAL();
            $quote->reviewed_user_id = $user->user_id ?? null;
            $quote->reviewed_at = CarbonImmutable::now();
            $quote->save();

            $quote->quoteTasks()->update(['status' => QuoteTaskStatus::PENDING_DECISION()]);

            QuoteSubmitForApprovalLulaWebhook::dispatch($workOrder, $workOrderTask, $quote, $user, $preventDispatchWebhook);
            WorkOrderUpdate::dispatch($workOrder->work_order_uuid);
        });
    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, WorkOrder $workOrder, WorkOrderTask $workOrderTask, Quote $quote): QuoteResource|JsonResponse
    {
        $user = $request->user();

        try {
            DB::beginTransaction();

            $lockedQuote = Quote::where('quote_id', $quote->quote_id)
                ->lockForUpdate()
                ->firstOrFail();

            $this->validateRouteParameterRelation($workOrder, $workOrderTask, $lockedQuote);

            if (empty($user)) {
                throw NotFoundException::userNotFound();
            }

            if ($workOrder->state->equals(WorkInProgress::class)) {
                throw QuoteException::quoteActionFailed();
            }

            if ($lockedQuote->status !== QuoteStatus::QUOTE_PENDING_REVIEW()) {
                throw QuoteException::invalidQuoteStatus($lockedQuote->status);
            }

            if (! ($workOrder->state->equals(Paused::class))) {
                throw QuoteException::invalidWorkOrderStatus($workOrder->state->label());
            }

            $workOrder->load('organization:organization_id,webhook_secret_key,webhook_enabled,webhook_api_url,webhook_secret_key,organization_uuid');

            $this->handle(
                $workOrder,
                $workOrderTask,
                $lockedQuote,
                false,
                $user
            );

            DB::commit();

            $lockedQuote->load([
                'submittedUser:user_id,user_uuid,first_name,last_name,middle_name',
                'reviewedUser:user_id,user_uuid,first_name,last_name,middle_name',
                'approvedUser:user_id,user_uuid,first_name,last_name,middle_name',
                'rejectedUser:user_id,user_uuid,first_name,last_name,middle_name',
                'quoteTasks' => function ($query) {
                    return $query->with([
                        'media' => function ($query) {
                            return $query->with([
                                'workOrder:work_orders.work_order_id,work_orders.organization_id,work_orders.work_order_uuid',
                                'workOrder.organization:organizations.organization_id,organizations.domain',
                            ])
                                ->wherePivot('has_thumbnail', Boolean::YES())
                                ->wherePivot('media_type', MediaType::QUOTE_TASK());
                        },
                        'quoteTaskMaterials',
                    ])
                        ->orderBy('created_at');
                },
            ]);

            return new QuoteResource($lockedQuote);
        } catch (NotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Submit For Approval Quote Task API Failed [NotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (QuoteException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Submit For Approval Quote Task API Failed [QuoteException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Submit For Approval Quote Task API Failed[Exception]', additionalInfo: [
                'workOrderId' => $workOrder->work_order_id,
                'workOrderTaskId' => $workOrderTask->work_order_task_id,
                'quoteId' => $quote->quote_id,
            ], notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('submitForApproval', $request->route('quote'));
    }
}

<?php

namespace App\Actions\Quotes;

use App\Enums\Boolean;
use App\Enums\CostTypes;
use App\Enums\MarkUpFeeTypes;
use App\Enums\MediaType;
use App\Enums\QuoteStatus;
use App\Enums\QuoteTaskStatus;
use App\Enums\UserTypes;
use App\Events\WorkOrder\PublicWebhook\Lula\QuoteCreateLulaWebhook;
use App\Exceptions\NotFoundException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Exceptions\QuoteException;
use App\Exceptions\WorkOrderMediaException;
use App\Helpers\Helper;
use App\Http\Requests\WorkOrder\CreateQuoteRequest;
use App\Http\Resources\WorkOrder\CreateQuoteResource;
use App\Models\Media;
use App\Models\Quote;
use App\Models\QuoteTask;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderMedia;
use App\Models\WorkOrderTask;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;
use Spatie\ModelStates\Exceptions\TransitionNotFound;

class CreateQuote extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'createQuote';
    }

    public static function getRoute(): string
    {
        return 'api/work-orders/{workOrder}/tasks/{workOrderTask}/actions/';
    }

    /**
     * @param  array<string,array<int,mixed>>  $payload
     */
    public function handle(WorkOrder $workOrder, WorkOrderTask $workOrderTask, User $user, string $reason, array $payload): Quote
    {
        return DB::transaction(function () use ($workOrder, $workOrderTask, $user, $reason, $payload) {

            if ($workOrder->state->equals(Paused::class)) {
                $workOrder->paused_reason = $reason;
                $workOrder->save();
            }

            $mediaIds = [];
            $quoteTasks = $payload['quote_tasks'] ?? null;

            if (empty($quoteTasks)) {
                throw NotFoundException::resourceNotFound('quote task');
            }

            foreach ($quoteTasks as $quoteTask) {
                if (! empty($quoteTask['media_ids'])) {
                    $mediaIds = array_merge($mediaIds, $quoteTask['media_ids']);
                }
            }

            $mediaCollection = collect([]);

            if (count($mediaIds)) {
                $mediaCollection = Media::join('work_order_media', 'work_order_media.media_id', 'media.media_id')
                    ->select(['media.media_uuid', 'media.media_id', 'work_order_media.work_order_media_id', 'work_order_media.media_type'])
                    ->where('work_order_media.media_type', MediaType::QUOTE_TASK())
                    ->whereUuid($mediaIds)
                    ->get();

                if ($mediaCollection->isEmpty()) {
                    throw new WorkOrderMediaException(__('Invalid media.'));
                }
            }

            // Create quote
            $quote = Quote::create([
                'organization_id' => $workOrder->organization_id,
                'work_order_id' => $workOrder->work_order_id,
                'work_order_task_id' => $workOrderTask->work_order_task_id,
                'work_order_service_call_id' => $workOrderTask->latestServiceCalls->first()->work_order_service_call_id ?? null,
                'status' => QuoteStatus::QUOTE_PENDING_REVIEW(),
                'submitted_user_id' => $user->user_id,
                'submitted_at' => CarbonImmutable::now(),
            ]);

            $newlyCreatedQuoteTaskIds = [];

            $quoteTaskNumber = 1;
            foreach ($quoteTasks as $quoteTaskEntry) {

                Log::info($quoteTaskEntry);
                // Insert Quote task
                $laborCostInCents = $quoteTaskEntry['labor_cost_in_cents'] ?? $quoteTaskEntry['cost_in_cents'] ?? 0;
                $markupFeeTypeValue = $quoteTaskEntry['markup_fee_type_value'] ?? config('workorder.quote.markup_fee.default_values.labor');
                $markupFeeType = $quoteTaskEntry['markup_fee_type'] ?? config('workorder.quote.markup_fee.default_type');
                $laborMarkupFeeInCents = ($markupFeeType == MarkUpFeeTypes::FIXED())
                    ? ($markupFeeTypeValue * 100)
                    : (int) round($laborCostInCents * ($markupFeeTypeValue / 100));

                $newQuoteTask = QuoteTask::create([
                    'organization_id' => $quote->organization_id,
                    'work_order_task_id' => $quote->work_order_task_id,
                    'user_id' => $user->user_id,
                    'quote_task_number' => $quoteTaskNumber,
                    'markup_fee_type' => $markupFeeType,
                    'markup_fee_type_value' => $markupFeeTypeValue,
                    'markup_fee_in_cents' => $laborMarkupFeeInCents,
                    'cost_in_cents' => $laborCostInCents,
                    'total_cost_in_cents' => $laborCostInCents + $laborMarkupFeeInCents,
                    'estimated_time' => $quoteTaskEntry['estimated_time'] ?? null,
                    'description' => $quoteTaskEntry['description'],
                    'status' => QuoteTaskStatus::QUOTE_PENDING_REVIEW(),
                    'quote_id' => $quote->quote_id,
                ]);

                $quoteTaskNumber = $quoteTaskNumber + 1;

                // Insert Quote task media
                if (! empty($quoteTaskEntry['media_ids'])) {
                    $workOrderMediaIds = $mediaCollection
                        ->whereIn('media_uuid', $quoteTaskEntry['media_ids'])
                        ->pluck('work_order_media_id')->toArray();

                    WorkOrderMedia::whereIn('work_order_media_id', $workOrderMediaIds)
                        ->update([
                            'quote_task_id' => $newQuoteTask->quote_task_id,
                        ]);
                }

                $newlyCreatedQuoteTaskIds[] = $newQuoteTask->quote_task_id;

                // Insert Quote task material
                if (! empty($quoteTaskEntry['materials'])) {
                    $materialPayload = [];
                    foreach ($quoteTaskEntry['materials'] as $material) {
                        $costType = ! empty($material['unit_price_in_cents']) ? CostTypes::PER_UNIT() : CostTypes::TOTAL();
                        $materialCostInCents = $material['material_cost_in_cents'] ?? $material['cost_in_cents'] ?? 0;
                        $markupFeeTypeValue = $material['markup_fee_type_value'] ?? config('workorder.quote.markup_fee.default_values.material');
                        $markupFeeType = $material['markup_fee_type'] ?? config('workorder.quote.markup_fee.default_type');

                        if ($costType == CostTypes::PER_UNIT()) {
                            $unitPrice = $material['unit_price_in_cents'] ?? 0;
                            $quantity = $material['quantity'] ?? 1;
                            $materialCostInCents = $unitPrice * $quantity;
                        }

                        $materialMarkupFeeInCents = ($markupFeeType == MarkUpFeeTypes::FIXED())
                            ? ($markupFeeTypeValue * 100)
                            : (int) round($materialCostInCents * ($markupFeeTypeValue / 100));

                        $materialPayload[] = [
                            'organization_id' => $quote->organization_id,
                            'label' => $material['label'],
                            'markup_fee_type' => $markupFeeType,
                            'markup_fee_type_value' => $markupFeeTypeValue,
                            'markup_fee_in_cents' => $materialMarkupFeeInCents,
                            'cost_in_cents' => $materialCostInCents,
                            'total_cost_in_cents' => $materialCostInCents + $materialMarkupFeeInCents,
                            'cost_type' => $costType,
                            'quantity' => $material['quantity'] ?? 1,
                            'quantity_type' => $material['quantity_type'],
                        ];
                    }
                    $newQuoteTask->quoteTaskMaterials()->createMany($materialPayload);
                }
            }

            QuoteCreateLulaWebhook::dispatch($workOrder, $workOrderTask, $quote, $user, $newlyCreatedQuoteTaskIds);

            return $quote;
        });
    }

    /**
     * @throws Exception
     */
    public function asController(CreateQuoteRequest $request, WorkOrder $workOrder, WorkOrderTask $workOrderTask): CreateQuoteResource|JsonResponse
    {
        $user = $request->user();

        try {
            $this->validateRouteParameterRelation($workOrder, $workOrderTask);

            if (empty($user)) {
                throw NotFoundException::userNotFound();
            }

            if (! $workOrder->state->equals(Paused::class, WorkInProgress::class)) {
                throw new CouldNotPerformTransition(__('This transition not allowed to perform with the work order'));
            }

            $workOrderTask->load(['latestServiceCalls.createdQuote' => function ($query) {
                return $query->where('status', '<>', QuoteStatus::REJECTED());
            }, 'latestServiceCalls.appointment']);

            $latestServiceCall = $workOrderTask->latestServiceCalls->first();

            if (empty($latestServiceCall)) {
                throw new ModelNotFoundException(__('Service Call not found.'));
            }

            $latestQuote = $latestServiceCall->createdQuote;

            if (! empty($latestQuote)) {
                throw QuoteException::existingQuote();
            }

            if ($user->user_type === UserTypes::TECHNICIAN()) {
                $technicianAppointment = $latestServiceCall->appointment;

                if (empty($technicianAppointment)) {
                    throw new ModelNotFoundException(__('Existing technician appointment not Found.'));
                }

                if ($user->technician?->technician_id != $technicianAppointment->technician_id) {
                    throw new ModelNotFoundException(__('You are not authorized to access this resource.'));
                }
            }

            // As for now we create a static reason for quote creation.
            $reason = 'Awaiting Quote Approval';

            $quote = $this->handle(
                $workOrder,
                $workOrderTask,
                $user,
                $reason,
                $request->all()
            );
            // manually set a relationship already loaded related data
            $workOrderTask->load(['latestServiceCalls.createdQuote', 'latestServiceCalls.appointment']);
            $workOrder->setRelation('tasks', collect([$workOrderTask]));
            $quote->setRelation('workOrder', $workOrder);
            $quote->load(['submittedUser', 'quoteTasks' => function ($query) {
                return $query->with([
                    'media' => function ($query) {
                        return $query->with('workOrder.organization')
                            ->wherePivot('has_thumbnail', Boolean::YES())
                            ->wherePivot('media_type', MediaType::QUOTE_TASK());
                    },
                    'quoteTaskMaterials',
                ])
                    ->orderBy('created_at');
            }]);

            return new CreateQuoteResource($quote);
        } catch (UserNotFoundException|ModelNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Submit Quote API Failed due to ' . get_class($exception));

            return Response::notFound(message: $exception->getMessage());
        } catch (CouldNotPerformTransition|TransitionNotFound $exception) {
            Helper::exceptionLog(exception: $exception, message: 'This transition[create quote] not allowed to perform with the work order.');

            return Response::unprocessableEntity(message: 'This transition not allowed to perform with the work order.');
        } catch (WorkOrderMediaException|QuoteException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Invalid media request.');

            return Response::unprocessableEntity(__($exception->getMessage()));
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Invalid request data.');

            return Response::unprocessableEntity(__('Invalid request data.'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Submit Quote API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('submitQuote', $request->route('workOrder'));
    }
}

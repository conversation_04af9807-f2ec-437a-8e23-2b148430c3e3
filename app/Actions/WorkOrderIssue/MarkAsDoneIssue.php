<?php

namespace App\Actions\WorkOrderIssue;

use App\Enums\CostTypes;
use App\Enums\MediaType;
use App\Enums\ServiceCallIssueStatus;
use App\Enums\UserTypes;
use App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceled;
use App\Exceptions\ForbiddenException;
use App\Exceptions\IssueException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Requests\WorkOrderIssue\MarkAsDoneIssueRequest;
use App\Http\Resources\WorkOrderIssue\WorkOrderIssueDetailsResource;
use App\Models\Material;
use App\Models\Media;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\Models\WorkOrderMedia;
use App\Models\WorkOrderServiceCallIssue;
use App\States\WorkOrderIssue\Canceled;
use App\States\WorkOrderIssue\Done as WorkOrderIssueDone;
use App\States\WorkOrders\Canceled as WorkOrdersCanceled;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class MarkAsDoneIssue extends BaseAction
{
    use AsAction;

    protected static string $method = 'post';

    public static function getSlug(): string
    {
        return 'markAsDone';
    }

    /**
     * @param  array<string,mixed>  $payloads
     */
    public function handle(array $payloads, WorkOrderIssue $workOrderIssue, int $tripId, User $user): WorkOrderIssue
    {
        return DB::transaction(function () use ($workOrderIssue, $payloads, $tripId, $user) {
            $causedByResident = $payloads['issue_caused_by_resident'] ?? null;
            $workOrderIssueData = [
                'service_notes' => $payloads['service_notes'],
                'issue_caused_by_resident' => $causedByResident,
                'issue_caused_details' => $causedByResident ? ($payloads['issue_caused_details'] ?? null) : null,
            ];

            $workOrderIssue->state->transitionTo(WorkOrderIssueDone::class, $workOrderIssueData);

            WorkOrderServiceCallIssue::where('work_order_service_call_id', $tripId)
                ->where('work_order_issue_id', $workOrderIssue->work_order_issue_id)
                ->update([
                    'status' => ServiceCallIssueStatus::DONE(),
                ]);

            $this->cancelIssuesAndRelatedWorkOrders($workOrderIssue, $user);
            // Handle issue media
            $this->handleIssueMedia($workOrderIssue, $tripId, $payloads);

            // Store material details
            $materialIds = [];
            $inputMaterials = $payloads['materials'] ?? [];
            foreach ($inputMaterials as $inputMaterial) {
                $quantity = $inputMaterial['quantity'];
                $costType = $inputMaterial['cost_type'];
                $unitPriceInCents = $inputMaterial['unit_price_in_cents'] ?? 0;
                $costInCents = $inputMaterial['cost_in_cents'] ?? 0;
                // Calculate total cost in cents
                $totalCostInCents = ($costType == CostTypes::PER_UNIT()) ? $quantity * $unitPriceInCents : $costInCents;

                $material = Material::create([
                    'label' => $inputMaterial['label'],
                    'quantity' => $quantity,
                    'quantity_type' => $inputMaterial['quantity_type'],
                    'cost_in_cents' => $costInCents,
                    'cost_type' => $costType,
                    'unit_price_in_cents' => $unitPriceInCents,
                    'total_cost_in_cents' => $totalCostInCents,
                ]);
                $materialIds[] = $material->material_id;
            }
            $workOrderIssue->materials()->attach($materialIds);

            return $workOrderIssue;
        });
    }

    /**
     * @throws Exception
     */
    public function asController(MarkAsDoneIssueRequest $request, WorkOrder $workOrder, WorkOrderIssue $workOrderIssue): WorkOrderIssueDetailsResource|JsonResponse
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if ($workOrder->work_order_id !== $workOrderIssue->work_order_id) {
                throw IssueException::invalidWorkOrderIssue();
            }

            if (! $workOrderIssue->state->canTransitionTo(WorkOrderIssueDone::class)) {
                throw IssueException::actionNotAllowed();
            }

            $workOrder->load([
                'latestTrips:work_order_service_call_id,work_order_service_call_uuid,work_order_id,technician_appointment_id,state,is_active' => [
                    'appointment:technician_appointment_id,work_order_id,technician_id',
                ],
            ]);

            $activeTrip = $workOrder->activeTrip();

            if (empty($activeTrip)) {
                throw new ModelNotFoundException(__('Trip not found.'));
            }

            $technicianAppointment = $activeTrip->appointment;

            if (empty($technicianAppointment)) {
                throw new ModelNotFoundException(__('Existing technician appointment not Found.'));
            }

            if (
                $user->user_type === UserTypes::TECHNICIAN() &&
                $user->technician?->technician_id != $technicianAppointment->technician_id
            ) {
                throw ForbiddenException::AccessDenied();
            }

            $this->validateMediaLimit($workOrderIssue, $request->only('before_media_ids', 'after_media_ids'));

            $workOrderIssue = $this->handle($request->all(), $workOrderIssue, $activeTrip->work_order_service_call_id, $user);

            $workOrderIssue->load(
                [
                    'materials' => function ($query) {
                        $query->select([
                            'material_uuid',
                            'label',
                            'quantity',
                            'quantity_type',
                            'cost_type',
                            'cost_in_cents',
                            'unit_price_in_cents',
                            'total_cost_in_cents',
                        ]);
                    },
                    'issue' => function ($query) {
                        $query->select([
                            'issue_id',
                            'title',
                            'description',
                        ]);
                    },
                ],
            );

            return new WorkOrderIssueDetailsResource($workOrderIssue);
        } catch (UserNotFoundException|ModelNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order issue Mark As Done API failed');

            $response = Response::notFound(message: $exception->getMessage());
        } catch (IssueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order issue Mark As Done API failed[IssueException]');

            $response = Response::unprocessableEntity(message: $exception->getMessage());
        } catch (ForbiddenException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order issue Mark As Done API Failed[ForbiddenException]');

            $response = Response::forbidden(message: __($exception->getMessage()));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order issue Mark As Done API failed', notify: true);

            $response = Response::internalServerError();
        }

        return $response;
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('complete', $request->route('workOrder'));
    }

    /**
     * Handle work order issue media
     *
     * @param  array<string,mixed>  $payloads
     */
    public function handleIssueMedia(WorkOrderIssue $workOrderIssue, int $tripId, array $payloads): void
    {
        $beforeMediaIds = $payloads['before_media_ids'] ?? [];
        $afterMediaIds = $payloads['after_media_ids'] ?? [];
        $inputMediaIds = array_merge($beforeMediaIds, $afterMediaIds);
        // Media mapping with work order issue
        if (! empty($inputMediaIds)) {
            $existingMediaIds = Media::with('workOrderMedia')
                ->whereUuid($inputMediaIds)
                ->where('organization_id', $workOrderIssue->organization_id)
                ->pluck('media_id')->toArray();

            WorkOrderMedia::whereIn('media_id', $existingMediaIds)
                ->update([
                    'work_order_issue_id' => $workOrderIssue->work_order_issue_id,
                    'work_order_service_call_id' => $tripId,
                ]);
        }
    }

    /**
     * Cancel a work order issues and related work order.
     */
    public function cancelIssuesAndRelatedWorkOrders(WorkOrderIssue $workOrderIssue, User $user): void
    {
        $workOrderIssues = WorkOrderIssue::where('issue_id', $workOrderIssue->issue_id)
            ->where('work_order_issue_id', '<>', $workOrderIssue->work_order_issue_id)
            ->get();

        if ($workOrderIssues->isNotEmpty()) {
            foreach ($workOrderIssues as $workOrderIssue) {
                if (! $workOrderIssue->state->equals(Canceled::class)) {
                    $workOrderIssue->state->transitionTo(Canceled::class);
                }
            }

            // Load work order with related issues
            $workOrderIssues->load([
                'workOrder' => function ($query) {
                    $query->select([
                        'work_order_id',
                        'state',
                        'canceled_at',
                        'state_updated_at',
                        'work_order_status_id',

                    ])
                        ->with('workOrderIssues:work_order_issue_id,work_order_id,issue_id,state');
                },
            ]);

            // Cancel work orders
            $workOrdersToCancel = $workOrderIssues->pluck('workOrder')
                ->filter()
                ->filter(function (WorkOrder $workOrder) {
                    return $workOrder->workOrderIssues->every(function ($workOrderIssue) {
                        return $workOrderIssue->state->equals(Canceled::class);
                    });
                });

            foreach ($workOrdersToCancel as $workOrderToCancel) {
                if (! $workOrderToCancel->state->equals(WorkOrdersCanceled::class)) {
                    $workOrderToCancel->state->transitionTo(WorkOrdersCanceled::class, null, $user, null);
                    ServiceRequestWorkOrderCanceled::broadcast($workOrderToCancel->work_order_id, $user->user_id);
                }
            }
        }
    }

    /**
     * Upload media limit validation
     */
    public function validateMediaLimit(WorkOrderIssue $workOrderIssue, array $payload): void
    {
        $beforeMediaType = MediaType::BEFORE_MEDIA();
        $afterMediaType = MediaType::AFTER_MEDIA();

        $media = WorkOrderMedia::where('work_order_issue_id', $workOrderIssue->work_order_issue_id)
            ->whereIn('media_type', [$beforeMediaType, $afterMediaType])
            ->get(['work_order_media_id', 'media_type']);

        $beforeMediaCount = $media->where('media_type', $beforeMediaType)->count();
        $afterMediaCount = $media->where('media_type', $afterMediaType)->count();

        $beforeMediaIds = $payload['before_media_ids'] ?? [];
        $afterMediaIds = $payload['after_media_ids'] ?? [];

        $totalBeforeMediaCount = $beforeMediaCount + count($beforeMediaIds);
        $totalAfterMediaCount = $afterMediaCount + count($afterMediaIds);

        $limit = config('media.work_order_media_limit.work_order');

        if ($totalBeforeMediaCount > $limit) {
            throw IssueException::mediaValidation($beforeMediaType, $limit);
        }

        if ($totalAfterMediaCount > $limit) {
            throw IssueException::mediaValidation($afterMediaType, $limit);
        }
    }
}

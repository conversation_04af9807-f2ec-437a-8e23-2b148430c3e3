<?php

namespace App\Actions\WorkOrderIssue;

use App\Actions\Issue\UnassignIssue as UnassignServiceRequestIssue;
use App\Exceptions\IssueException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Resources\WorkOrderIssue\WorkOrderIssueResource;
use App\Models\Issue;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\States\Issue\Unassigned;
use App\States\WorkOrders\AwaitingAvailability;
use App\States\WorkOrders\Canceled;
use App\States\WorkOrders\Created;
use App\States\WorkOrders\ReadyToSchedule;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class UnassignIssue extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'unassign';
    }

    public function handle(Issue $issue, WorkOrder $workOrder, User $user): void
    {
        (new UnassignServiceRequestIssue)->handle($issue, $workOrder, $user);
    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, WorkOrder $workOrder, WorkOrderIssue $workOrderIssue): WorkOrderIssueResource|JsonResponse
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if (! $workOrder->state->equals(Created::class, ReadyToSchedule::class, AwaitingAvailability::class)) {
                throw IssueException::actionNotAllowed();
            }

            $workOrderIssue->load([
                'issue:issue_id,issue_uuid,service_request_id,problem_diagnosis_id,organization_id,user_id,title,description,state,state_updated_at',
            ]);

            $issue = $workOrderIssue->issue;

            if (empty($issue)) {
                throw IssueException::notFound();
            }

            if (! $issue->state->canTransitionTo(Unassigned::class)) {
                throw IssueException::actionNotAllowed();
            }

            $this->handle(
                $issue,
                $workOrder,
                $user
            );

            $workOrderIssue->load([
                'workOrder:work_order_id,state',
                'issue:issue_id,issue_uuid,title,description,problem_diagnosis_id',
                'issue.workOrders' => function ($query) {
                    $query->select('work_orders.work_order_id', 'work_order_uuid', 'work_order_number', 'work_orders.state')
                        ->withCount('issues')
                        ->whereNotState('work_orders.state', Canceled::class);
                },
                'issue.problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label',
                'issue.problemDiagnosis.subCategory:problem_sub_category_id,problem_sub_category_uuid,label,problem_category_id',
                'issue.problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
            ]);

            return new WorkOrderIssueResource($workOrderIssue);
        } catch (UserNotFoundException|IssueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order issue unassigned failed');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order issue unassigned failed', notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('create', WorkOrder::class);
    }
}

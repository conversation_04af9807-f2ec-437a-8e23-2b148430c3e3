<?php

namespace App\Actions\WorkOrderIssue;

use App\Actions\Issue\UpdateIssue as updateServiceRequestIssue;
use App\Exceptions\IssueException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Requests\Issue\CreateIssueRequest;
use App\Http\Resources\WorkOrderIssue\WorkOrderIssueResource;
use App\Models\Issue;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\States\WorkOrders\Canceled;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdateIssueBasicDetails extends BaseAction
{
    use AsAction;

    protected static string $method = 'put';

    public static function getSlug(): string
    {
        return 'updateBasicDetails';
    }

    /**
     * @param array{
     *     title :string,
     *     description : string
     *     problem_diagnosis_id: string,
     * } $payload
     */
    public function handle(array $payload, Issue $issue, User $user): void
    {
        (new updateServiceRequestIssue)->handle($payload, $issue, $user->user_id);
    }

    /**
     * @throws Exception
     */
    public function asController(CreateIssueRequest $request, WorkOrder $workOrder, WorkOrderIssue $workOrderIssue): WorkOrderIssueResource|JsonResponse
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $workOrderIssue->load([
                'issue:issue_id,issue_uuid,service_request_id,problem_diagnosis_id,organization_id,user_id,title,description,state,state_updated_at',
                'issue.serviceRequest:service_request_id,service_request_uuid,state',
                'issue.workOrders:work_order_id,work_order_uuid,work_order_number,state',
                'issue.problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label',
                'issue.problemDiagnosis.subCategory:problem_sub_category_id,problem_category_id,problem_sub_category_uuid,label,problem_category_id',
                'issue.problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
            ]);

            $issue = $workOrderIssue->issue;

            if (empty($issue)) {
                throw IssueException::notFound();
            }

            $this->handle(
                $request->all(),
                $issue,
                $user,
            );

            $workOrderIssue->load([
                'issue:issue_id,issue_uuid,title,description,problem_diagnosis_id',
                'issue.workOrders' => function ($query) {
                    $query->select('work_orders.work_order_id', 'work_order_uuid', 'work_order_number', 'work_orders.state')
                        ->withCount('issues')
                        ->whereNotState('work_orders.state', Canceled::class);
                },
                'issue.problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label',
                'issue.problemDiagnosis.subCategory:problem_sub_category_id,problem_sub_category_uuid,label,problem_category_id',
                'issue.problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
            ]);

            $workOrderIssue->setRelation('workOrder', $workOrder);

            return new WorkOrderIssueResource($workOrderIssue);
        } catch (UserNotFoundException|IssueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order issue update failed');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order issue update failed', notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('create', WorkOrder::class);
    }
}

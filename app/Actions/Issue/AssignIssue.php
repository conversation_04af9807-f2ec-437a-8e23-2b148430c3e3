<?php

namespace App\Actions\Issue;

use App\Events\Issue\ServiceRequestIssueUpdated;
use App\Events\ServiceRequest\WorkOrder\SyncServiceRequestWorkOrder;
use App\Exceptions\IssueException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Requests\Issue\AssignRequest;
use App\Http\Resources\Issue\AssignIssueResource;
use App\Models\Issue;
use App\Models\ServiceRequest;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\States\Issue\Assigned;
use App\States\WorkOrders\AwaitingAvailability;
use App\States\WorkOrders\Canceled;
use App\States\WorkOrders\Created;
use App\States\WorkOrders\ReadyToSchedule;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class AssignIssue extends BaseAction
{
    use AsAction;

    protected static string $method = 'post';

    public static function getRoute(): string
    {
        return 'api/service-requests/{serviceRequest}/issues/{issue}/action/';
    }

    public static function getSlug(): string
    {
        return 'assignIssue';
    }

    /**
     * @throws Throwable
     */
    public function handle(Issue $issue, WorkOrder $workOrder, User $user): Issue
    {
        return DB::transaction(function () use ($issue, $workOrder, $user) {

            $issue = $issue->state->transitionTo(Assigned::class, $workOrder, $user);

            ServiceRequestIssueUpdated::broadcast($issue->issue_id)->toOthers();

            SyncServiceRequestWorkOrder::dispatch($issue->service_request_id);

            return $issue;
        });
    }

    /**
     * @throws Exception
     */
    public function asController(AssignRequest $request, ServiceRequest $serviceRequest, Issue $issue): AssignIssueResource|JsonResponse
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if ($serviceRequest->service_request_id !== $issue->service_request_id) {
                throw IssueException::notFound();
            }

            if (! $issue->state->canTransitionTo(Assigned::class)) {
                throw IssueException::actionNotAllowed();
            }

            $workOrder = WorkOrder::select('work_order_id', 'work_order_uuid', 'work_order_number', 'service_request_id')
                ->whereState('state', [Created::class, ReadyToSchedule::class, AwaitingAvailability::class])
                ->whereUuid($request->get('work_order_id'))->first();

            if (empty($workOrder)) {
                throw IssueException::actionNotAllowed();
            }

            if ($this->isIssueAlreadyAssigned($workOrder, $issue)) {
                throw IssueException::issueAlreadyAssigned();
            }

            $issue = $this->handle(
                $issue,
                $workOrder,
                $user
            );
            $issue->setRelation('workOrder', $workOrder);

            $issue->load([
                'serviceRequest:service_request_id,state',
                'workOrders' => function (BelongsToMany $query): BelongsToMany {
                    return $query->select('work_orders.work_order_id', 'work_orders.state', 'work_orders.work_order_uuid', 'work_orders.work_order_number')
                        ->withCount('issues')
                        ->whereNotState('work_orders.state', Canceled::class);
                },
            ]);

            return new AssignIssueResource($issue);
        } catch (UserNotFoundException|IssueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order issue assigned failed');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Throwable $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order issue assigned failed', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Check if the given issue is already assigned to the specified work order.
     */
    public function isIssueAlreadyAssigned(WorkOrder $workOrder, Issue $issue): bool
    {
        return WorkOrderIssue::select('work_order_issue_id')
            ->where('work_order_id', $workOrder->work_order_id)
            ->where('issue_id', $issue->issue_id)
            ->exists();
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('create', WorkOrder::class);
    }
}

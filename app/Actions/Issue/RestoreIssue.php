<?php

declare(strict_types=1);

namespace App\Actions\Issue;

use App\Events\Issue\ServiceRequestIssueRestored;
use App\Exceptions\IssueException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Resources\Issue\IssueResource;
use App\Jobs\ServiceRequest\ActivityLog\UpdateServiceRequestIssueCanceledActivityLogJob;
use App\Models\Issue;
use App\Models\ServiceRequest;
use App\States\Issue\Canceled;
use App\States\Issue\Unassigned;
use App\States\ServiceRequests\Closed;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;

class RestoreIssue extends BaseAction
{
    use AsAction;

    protected static string $method = 'put';

    public static function getSlug(): string
    {
        return 'restoreIssue';
    }

    public static function getRoute(): string
    {
        return 'api/service-requests/{serviceRequest}/issues/{issue}/action/';
    }

    public function handle(Issue $issue, int $userId): Issue
    {
        return DB::transaction(function () use ($issue, $userId) {
            $issue->state->transitionTo(Unassigned::class);

            ServiceRequestIssueRestored::broadcast($issue->issue_id, $userId)->toOthers();

            dispatch(new UpdateServiceRequestIssueCanceledActivityLogJob($issue->issue_id))->afterCommit();

            return $issue;
        });
    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, ServiceRequest $serviceRequest, Issue $issue): IssueResource|JsonResponse
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if ($serviceRequest->service_request_id !== $issue->service_request_id) {
                throw IssueException::notFound();
            }

            // Cannot restore issue if service request is closed
            if ($serviceRequest->state->equals(Closed::class)) {
                throw IssueException::actionNotAllowed('Cannot perform restore action on closed service request');
            }

            if (! $issue->state->equals(Canceled::class)) {
                throw new CouldNotPerformTransition('This transition is not allowed to perform with the issue');
            }

            $issue = $this->handle($issue, $user->user_id);

            $issue->load([
                'serviceRequest:service_request_id,state',
                'workOrders:work_order_id,work_order_uuid,work_order_number,state',
            ]);

            return new IssueResource($issue);
        } catch (UserNotFoundException|ModelNotFoundException|IssueException|CouldNotPerformTransition $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service request issue creation failed');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service request issue creation failed');

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('cancel', $request->route('issue'));
    }
}

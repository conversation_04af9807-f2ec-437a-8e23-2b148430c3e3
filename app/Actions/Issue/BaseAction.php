<?php

namespace App\Actions\Issue;

use App\Exceptions\IssueException;
use Exception;
use Illuminate\Routing\Router;
use Lorisleiva\Actions\ActionRequest;

abstract class BaseAction
{
    protected static string $method = 'post';

    public static function getSlug(): string
    {
        throw IssueException::undefinedRouteSlug();
    }

    public static function getRoute(): string
    {
        return 'api/service-requests/{serviceRequest}/issues/action/';
    }

    public static function routes(Router $router): void
    {
        $router->{static::$method}(static::getRoute() . static::getSlug(), static::class)
            ->middleware(['api', 'auth.cognito', 'tenant'])->name('serviceRequest.issue.action.' . static::getSlug());
    }

    /**
     * @throws Exception
     */
    public function authorize(ActionRequest $request): bool
    {
        return false;
    }
}

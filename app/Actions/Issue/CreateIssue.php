<?php

declare(strict_types=1);

namespace App\Actions\Issue;

use App\Events\Issue\ServiceRequestIssueCreated;
use App\Events\ServiceRequest\WorkOrder\SyncServiceRequestWorkOrder;
use App\Exceptions\IssueException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Requests\Issue\CreateIssueRequest;
use App\Http\Resources\Issue\IssueResource;
use App\Models\Issue;
use App\Models\ProblemDiagnosis;
use App\Models\ServiceRequest;
use App\Models\User;
use App\States\Issue\Unassigned;
use App\States\ServiceRequests\Closed;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class CreateIssue extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'createIssue';
    }

    /**
     * Handle the creation of an issue.
     *
     * @param  array<string, string>  $payload
     */
    public function handle(array $payload, ServiceRequest $serviceRequest, User $user): Issue
    {
        return DB::transaction(function () use ($payload, $serviceRequest, $user) {
            $problemDiagnosis = $this->getProblemDiagnosis($payload['problem_diagnosis_id']);

            $issue = $this->createIssue($payload, $serviceRequest, $user, $problemDiagnosis);

            ServiceRequestIssueCreated::broadcast($issue->issue_id, $user->user_id)->toOthers();
            SyncServiceRequestWorkOrder::broadcast($serviceRequest->service_request_id);

            return $issue;
        });
    }

    /**
     * Get the problem diagnosis by UUID.
     */
    public function getProblemDiagnosis(string $problemDiagnosisUuid): ProblemDiagnosis
    {
        return ProblemDiagnosis::select('problem_diagnosis_id', 'problem_diagnosis_uuid', 'problem_sub_category_id', 'label')
            ->with([
                'subCategory:problem_sub_category_id,problem_sub_category_uuid,label,problem_category_id',
                'subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
            ])
            ->whereUuid($problemDiagnosisUuid)
            ->firstOrFail();
    }

    /**
     * Create a new issue.
     *
     * @param  array<string, string>  $payload
     */
    public function createIssue(array $payload, ServiceRequest $serviceRequest, User $user, ProblemDiagnosis $problemDiagnosis): Issue
    {
        $issue = Issue::create([
            'service_request_id' => $serviceRequest->service_request_id,
            'problem_diagnosis_id' => $problemDiagnosis->problem_diagnosis_id,
            'organization_id' => $serviceRequest->organization_id,
            'user_id' => $user->user_id,
            'title' => trim($payload['title']),
            'description' => trim($payload['description']),
            'state' => Unassigned::class,
            'state_updated_at' => CarbonImmutable::now(),
        ]);

        $issue->setRelation('problemDiagnosis', $problemDiagnosis);

        return $issue;
    }

    /**
     * @throws Exception
     */
    public function asController(CreateIssueRequest $request, ServiceRequest $serviceRequest): IssueResource|JsonResponse
    {
        try {
            $user = $request->user();
            if (! $user) {
                throw new UserNotFoundException;
            }

            if ($serviceRequest->state->equals(Closed::class)) {
                throw IssueException::actionNotAllowed();
            }

            $issue = $this->handle(
                $request->all(),
                $serviceRequest,
                $user
            );

            $issue->load([
                'serviceRequest:service_request_id,state',
                'workOrders:work_order_id,state',
            ]);

            return new IssueResource($issue);
        } catch (UserNotFoundException|IssueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service request issue creation failed');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service request issue creation failed', notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('create', Issue::class);
    }
}

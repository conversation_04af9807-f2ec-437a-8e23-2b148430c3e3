<?php

declare(strict_types=1);

namespace App\Actions\Issue;

use App\Events\Issue\ServiceRequestDeletedIssueRestored;
use App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogDeleted;
use App\Exceptions\IssueException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Resources\Issue\IssueResource;
use App\Models\Issue;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestActivityLog;
use App\Models\User;
use App\Services\ServiceRequestActivity\Enums\ActivityLogEventTypes;
use App\States\Issue\Unassigned;
use App\States\ServiceRequests\Closed;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lori<PERSON><PERSON>\Actions\Concerns\AsAction;

class UndoIssue extends BaseAction
{
    use AsAction;

    protected static string $method = 'put';

    public static function getSlug(): string
    {
        return 'undoIssue';
    }

    public static function getRoute(): string
    {
        return 'api/service-requests/{serviceRequest}/issues/{issue}/action/';
    }

    public function handle(Issue $issue, User $user): Issue
    {
        return DB::transaction(function () use ($issue, $user): Issue {
            $this->restoreIssue($issue, $user);
            $this->deleteActivityLogs($issue);

            ServiceRequestDeletedIssueRestored::broadcast($issue->issue_id, $user->user_id)->toOthers();

            return $issue;
        });
    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, ServiceRequest $serviceRequest, Issue $issue): IssueResource|JsonResponse
    {
        try {
            $user = $request->user();

            if (! $user) {
                throw new UserNotFoundException;
            }

            if ($serviceRequest->service_request_id !== $issue->service_request_id) {
                throw IssueException::notFound();
            }

            // Cannot undo if service request is closed
            if ($serviceRequest->state->equals(Closed::class)) {
                throw IssueException::actionNotAllowed('Cannot perform undo action on closed service request');
            }

            $issue = $this->handle($issue, $user);

            $issue->load([
                'serviceRequest:service_request_id,state',
                'workOrders:work_order_id,work_order_uuid,work_order_number,state',
                'problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label',
                'problemDiagnosis.subCategory:problem_sub_category_id,problem_category_id,problem_sub_category_uuid,label',
                'problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
            ]);

            return new IssueResource($issue);
        } catch (UserNotFoundException|IssueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service request issue undo API failed due to ' . get_class($exception));

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service request issue undo API failed[Exception]', additionalInfo: [
                'serviceRequestId' => $serviceRequest->service_request_id,
                'issueId' => $issue->issue_id ?? null,
            ]);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('delete', $request->route('issue'));
    }

    protected function restoreIssue(Issue $issue, User $user): void
    {
        $issue->update([
            'deleted_at' => null,
            'state' => Unassigned::class,
            'state_updated_at' => CarbonImmutable::now(),
            'user_id' => $user->user_id,
        ]);
    }

    protected function deleteActivityLogs(Issue $issue): void
    {
        $activityLogs = ServiceRequestActivityLog::select(
            'service_request_activity_log_id',
            'service_request_id',
            'organization_id',
            'type',
            'event',
            'event_attributes',
            'deleted_at'
        )
            ->where('service_request_id', $issue->service_request_id)
            ->where('organization_id', $issue->organization_id)
            ->where('event', ActivityLogEventTypes::SERVICE_REQUEST_ISSUE_DELETED())
            ->where('type', 'service-request')
            ->whereJsonContains('event_attributes->issue_id', $issue->issue_uuid)
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($activityLogs as $log) {
            $log->delete();
            ServiceRequestActivityLogDeleted::broadcast($log->service_request_activity_log_id);
        }
    }
}

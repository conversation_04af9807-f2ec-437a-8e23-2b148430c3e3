<?php

declare(strict_types=1);

namespace App\Actions\Issue;

use App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCanceled;
use App\Events\ServiceRequest\WorkOrder\SyncServiceRequestWorkOrder;
use App\Exceptions\IssueException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Resources\Issue\IssueResource;
use App\Models\Issue;
use App\Models\ServiceRequest;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\States\Issue\Assigned;
use App\States\Issue\Canceled;
use App\States\WorkOrderIssue\Canceled as WorkOrderIssueCanceled;
use App\States\WorkOrders\AwaitingAvailability;
use App\States\WorkOrders\Canceled as WorkOrdersCanceled;
use App\States\WorkOrders\ReadyToSchedule;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class CancelIssue extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'cancelIssue';
    }

    public static function getRoute(): string
    {
        return 'api/service-requests/{serviceRequest}/issues/{issue}/action/';
    }

    public function handle(Issue $issue, User $user): Issue
    {
        return DB::transaction(function () use ($issue, $user) {
            $issue->state->transitionTo(Canceled::class, $user);

            $issueMappedWorkOrders = $this->getAllAssociatedWorkOrders($issue);

            if ($issueMappedWorkOrders->isNotEmpty()) {
                $workOrders = $this->getWorkOrdersWantToCancel($issueMappedWorkOrders);

                foreach ($workOrders as $workOrder) {
                    $this->cancelWorkOrder($workOrder, $user);
                }
            }

            $this->cancelWorkOrderIssues($issue);
            SyncServiceRequestWorkOrder::dispatch($issue->service_request_id);

            return $issue;
        });
    }

    /**
     * Retrieve all work orders associated with a given issue.
     *
     * @return Collection<int, WorkOrder>
     */
    public function getAllAssociatedWorkOrders(Issue $issue): Collection
    {
        return WorkOrder::join('work_order_issues', function (JoinClause $join) use ($issue) {
            $join->on('work_orders.work_order_id', '=', 'work_order_issues.work_order_id')
                ->where('work_order_issues.issue_id', $issue->issue_id)
                ->whereNull('work_order_issues.deleted_at');
        })
            ->with([
                'issues:issue_id,state',
            ])
            ->select('work_orders.work_order_id', 'work_orders.state')
            ->get();
    }

    /**
     * Determines if there are any scheduled or in-progress work orders associated with the given issue.
     *
     * This method checks for the existence of work orders linked to the specified issue
     * that are not in the states of "ReadyToSchedule", "WorkOrdersCanceled", or "AwaitingAvailability".
     * It excludes soft-deleted records from the check.
     */
    public function hasAnyScheduledOrInprogressWorkOrderExists(Issue $issue): bool
    {
        return WorkOrder::join('work_order_issues', function (JoinClause $join) use ($issue) {
            $join->on('work_orders.work_order_id', '=', 'work_order_issues.work_order_id')
                ->where('work_order_issues.issue_id', $issue->issue_id)
                ->whereNull('work_order_issues.deleted_at');
        })
            ->whereNotState('work_orders.state', [ReadyToSchedule::class, WorkOrdersCanceled::class, AwaitingAvailability::class])
            ->select('work_orders.work_order_id')
            ->exists();
    }

    /**
     * Filters a collection of work orders to identify those that can be canceled.
     *
     * A work order is considered eligible for cancellation if it has no issues
     * in the "Assigned" state.
     *
     * @param  Collection<int, WorkOrder>  $workOrders
     * @return Collection<int, WorkOrder> A collection of work orders that can be canceled.
     */
    public function getWorkOrdersWantToCancel(Collection $workOrders): Collection
    {
        return $workOrders->filter(function (WorkOrder $workOrder) {
            $assignedIssues = $workOrder->issues->filter(fn (Issue $issue) => $issue->state->equals(Assigned::class));

            return $assignedIssues->isEmpty();
        });
    }

    /**
     * Cancel a work order.
     */
    public function cancelWorkOrder(WorkOrder $workOrder, User $user): void
    {
        if (! $workOrder->state->equals(WorkOrdersCanceled::class)) {
            $workOrder->state->transitionTo(WorkOrdersCanceled::class, null, $user, null);
            ServiceRequestWorkOrderCanceled::broadcast($workOrder->work_order_id, $user->user_id);
        }
    }

    /**
     * Cancel a work order issues.
     */
    public function cancelWorkOrderIssues(Issue $issue): void
    {
        // cancel all work order issues
        $workOrderIssues = WorkOrderIssue::select('work_order_issue_id', 'state')
            ->where('issue_id', $issue->issue_id)
            ->whereNotState('state', WorkOrderIssueCanceled::class)
            ->get();

        foreach ($workOrderIssues as $workOrderIssue) {
            $workOrderIssue->state->transitionTo(WorkOrderIssueCanceled::class);
        }
    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, ServiceRequest $serviceRequest, Issue $issue): IssueResource|JsonResponse
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if ($serviceRequest->service_request_id !== $issue->service_request_id) {
                throw IssueException::notFound();
            }

            if (! $issue->state->canTransitionTo(Canceled::class)) {
                throw IssueException::actionNotAllowed();
            }

            // If the issue is in the assigned status and is not currently linked to a work order that is scheduled or in progress, it can be canceled.
            if ($issue->state->equals(Assigned::class) && $this->hasAnyScheduledOrInprogressWorkOrderExists($issue)) {
                throw IssueException::actionNotAllowed();
            }

            $issue = $this->handle(
                $issue,
                $user
            );

            $issue->load([
                'serviceRequest:service_request_id,state',
                'workOrders:work_order_id,work_order_uuid,work_order_number,state',
                'problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label',
                'problemDiagnosis.subCategory:problem_sub_category_id,problem_sub_category_uuid,label,problem_category_id',
                'problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
            ]);

            return new IssueResource($issue);
        } catch (UserNotFoundException|IssueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service request issue cancellation failed');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service request issue cancellation failed', notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('cancel', $request->route('issue'));
    }
}

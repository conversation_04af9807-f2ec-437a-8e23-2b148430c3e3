<?php

namespace App\Actions\ServiceRequest;

use App\Models\ServiceRequest;
use Exception;
use Illuminate\Routing\Router;
use InvalidArgumentException;
use Lorisleiva\Actions\ActionRequest;

abstract class BaseAction
{
    protected static string $method = 'post';

    public static function getSlug(): string
    {
        throw new Exception('Undefined slug');
    }

    public static function getRoute(): string
    {
        return 'api/service-requests/{serviceRequest}/actions/';
    }

    public static function routes(Router $router): void
    {
        $router->{static::$method}(static::getRoute() . static::getSlug(), static::class)
            ->middleware(['api', 'auth.cognito', 'tenant'])->name('serviceRequest.action.' . static::getSlug());
    }

    /**
     * @throws Exception
     */
    public function authorize(ActionRequest $request): bool
    {
        return false;
    }

    /**
     * @throws InvalidArgumentException
     */
    public function validateServiceRequest(ServiceRequest $serviceRequest): void
    {
        if (empty($serviceRequest->priority)) {
            throw new InvalidArgumentException(__('Service request has empty priority'));
        }

        if (empty($serviceRequest->latestDescription->description)) {
            throw new InvalidArgumentException(__('Service request has empty description'));
        }

        if (empty($serviceRequest->categories)) {
            throw new InvalidArgumentException(__('Service request has empty problem diagnosis'));
        }
    }
}

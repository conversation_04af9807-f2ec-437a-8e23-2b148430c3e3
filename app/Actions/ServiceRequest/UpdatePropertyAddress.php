<?php

namespace App\Actions\ServiceRequest;

use App\Events\ServiceRequest\ServiceRequestAddressUpdated;
use App\Events\WorkOrder\WorkOrderPropertyAddressUpdated;
use App\Helpers\Helper;
use App\Http\Requests\ServiceRequest\PropertyAddressUpdateRequest;
use App\Http\Resources\ServiceRequest\PropertyAddressResource;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\State;
use App\Models\WorkOrder;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class UpdatePropertyAddress extends BaseAction
{
    use AsAction;

    protected static string $method = 'patch';

    /**
     * The unique identifier for this action used in routing.
     */
    public static function getSlug(): string
    {
        return 'updatePropertyAddress';
    }

    /**
     * Handle the actual property update operation.
     *
     * @param  array  $payload  The validated request data.
     * @param  Property  $property  The property model instance.
     * @param  State  $state  The state model instance.
     * @return Property The updated property.
     */
    public function handle(array $payload, Property $property, State $state): Property
    {
        $fullAddress = $payload['street_address'] . ', ' . $payload['city'] . ', ' . $state->state_code . ', ' .
            $payload['postal_zip_code'] . ', ' . $property->country->alpha2_code;

        $property->street_address = $payload['street_address'];
        $property->city = $payload['city'];
        $property->state_id = $state->state_id;
        $property->postal_zip_code = $payload['postal_zip_code'];
        $property->unit_number = $payload['unit_number'];
        $property->full_address = $fullAddress;
        $property->save();

        return $property;
    }

    /**
     * Execute the controller logic for updating the property address.
     *
     * @param  PropertyAddressUpdateRequest  $request  The validated request instance.
     * @param  ServiceRequest  $serviceRequest  The related service request model.
     * @return PropertyAddressResource|JsonResponse The API response.
     *
     * @throws Exception
     */
    public function asController(PropertyAddressUpdateRequest $request, ServiceRequest $serviceRequest): PropertyAddressResource|JsonResponse
    {
        try {
            $property = Property::with([
                'country:country_id,alpha2_code',
                'state:state_id,state_uuid,name,state_code',
            ])
                ->where('property_id', $serviceRequest->property_id)
                ->whereUuid($request->property_id)
                ->select('property_id', 'property_uuid', 'country_id', 'street_address', 'city', 'state_id', 'postal_zip_code', 'full_address')
                ->first();

            if (empty($property)) {
                throw new ModelNotFoundException(__('Property not found.'));
            }

            $state = State::select('state_id', 'state_code')
                ->whereUuid($request->state_id)
                ->first();

            $property = $this->handle($request->all(), $property, $state);

            $workOrders = WorkOrder::select('work_order_id')
                ->where('service_request_id', $serviceRequest->service_request_id)
                ->get();

            ServiceRequestAddressUpdated::dispatch($serviceRequest->service_request_id);

            foreach ($workOrders as $workOrder) {
                WorkOrderPropertyAddressUpdated::dispatch($workOrder->work_order_id, $property->property_id);
            }

            return new PropertyAddressResource($property);
        } catch (ModelNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Model not found[ModelNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request Property Address Update api Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Authorize the request using policy.
     */
    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('update', $request->route('serviceRequest'));
    }
}

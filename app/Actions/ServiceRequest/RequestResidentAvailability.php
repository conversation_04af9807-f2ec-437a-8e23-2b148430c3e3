<?php

namespace App\Actions\ServiceRequest;

use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Exceptions\ResidentAvailabilityException;
use App\Helpers\Helper;
use App\Models\Resident;
use App\Models\ServiceRequest;
use App\Models\User;
use App\Notifications\Resident\ServiceRequest\ResidentAvailabilityNotification;
use App\States\ServiceRequests\AwaitingAvailability;
use App\States\ServiceRequests\CreateWorkOrder;
use App\States\ServiceRequests\InProgress;
use App\States\ServiceRequests\Scoping;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class RequestResidentAvailability extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'requestResidentAvailability';
    }

    public function handle(ServiceRequest $serviceRequest, User $user, Resident $resident): void
    {
        DB::transaction(function () use ($serviceRequest, $user, $resident) {
            $serviceRequest->load('organization:organization_id,organization_uuid,name');
            $residentName = $resident->getName();

            if (! $serviceRequest->state->equals(AwaitingAvailability::class, CreateWorkOrder::class)) {
                AwaitingAvailabilityServiceRequest::run($serviceRequest, $user);
            }

            $availabilityURL = config('workorder.public.resident_availability.domain') . $serviceRequest->service_request_uuid;
            $message = "Hello {$residentName}, this is {$serviceRequest->organization->name}. Please use this link to provide your availability for the next days:";
            $resident->notify((new ResidentAvailabilityNotification($serviceRequest->service_request_id, $message, $availabilityURL, $user->user_id)));

            $serviceRequest->availability_requested_user_id = $user->user_id;
            $serviceRequest->availability_requested_at = CarbonImmutable::now();
            $serviceRequest->availability_viewed_user_id = null;
            $serviceRequest->save();

        });
    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, ServiceRequest $serviceRequest): JsonResponse
    {
        try {
            DB::beginTransaction();

            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if (! $serviceRequest->state->equals(Scoping::class, AwaitingAvailability::class, InProgress::class, CreateWorkOrder::class)) {
                throw ResidentAvailabilityException::invalidServiceRequestStatus($serviceRequest->state->label());
            }

            $serviceRequest->load('resident:resident_id,first_name,last_name,phone_number');
            $resident = $serviceRequest->resident;

            if (empty($resident)) {
                throw ResidentAvailabilityException::residentNotFound();
            }

            if (empty($resident->phone_number)) {
                throw ResidentAvailabilityException::residentPhoneNotFound();
            }

            $this->handle(
                $serviceRequest,
                $user,
                $resident
            );
            $serviceRequest->refresh();
            DB::commit();

            return response()->json([
                'availability_requested_at' => $serviceRequest->availability_requested_at,
            ]);
        } catch (UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Request Resident Availability API failed[UserNotFound]');

            return Response::notFound(message: $exception->getMessage());
        } catch (ResidentAvailabilityException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Request Resident Availability API failed[ResidentAvailabilityException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Request Resident Availability API failed[Exception]', additionalInfo: [
                'workOrderId' => $serviceRequest->service_request_id,

            ], notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('requestResidentAvailability', $request->route('serviceRequest'));
    }
}

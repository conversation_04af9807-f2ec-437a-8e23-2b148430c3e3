<?php

namespace App\Actions\Invoices;

use App\Events\Invoice\PartiallyPaid as PartiallyPaidEvent;
use App\Exceptions\InvoiceException;
use App\Exceptions\NotFoundException;
use App\Helpers\Helper;
use App\Http\Resources\WorkOrder\Invoice\PartiallyPaidResource;
use App\Jobs\Invoice\CreateInvoiceLogJob;
use App\Models\Invoice;
use App\Models\User;
use App\Models\WorkOrder;
use App\States\Invoices\PartiallyPaid;
use App\States\WorkOrders\ReadyToInvoice;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;
use Spatie\ModelStates\Exceptions\TransitionNotFound;

class PartiallyPaidInvoice extends BaseAction
{
    use AsAction;
    protected static string $method = 'put';

    public static function getSlug(): string
    {
        return 'partiallyPaidInvoice';
    }

    public function handle(Invoice $invoice, WorkOrder $workOrder, User $user): void
    {
        DB::transaction(function () use ($invoice, $workOrder, $user) {

            $invoice->state->transitionTo(PartiallyPaid::class, $workOrder, $user);

            dispatch(new CreateInvoiceLogJob($invoice, 'status_change', $user))->afterCommit();
            event(new PartiallyPaidEvent($workOrder, $invoice));

        });
    }

    public function asController(Request $request, WorkOrder $workOrder, Invoice $invoice): JsonResponse|PartiallyPaidResource
    {
        try {
            DB::beginTransaction();
            $user = $request->user();

            if (empty($user)) {
                throw NotFoundException::userNotFound();
            }

            $this->validateRouteParameterRelation($workOrder, $invoice);

            if (! ($workOrder->state->equals(ReadyToInvoice::class))) {
                throw InvoiceException::invalidWorkOrderStatus($workOrder->state->label());
            }

            $invoice = Invoice::lockForUpdate()
                ->findOrFail($invoice->invoice_id);

            if (! $invoice->state->canTransitionTo(PartiallyPaid::class)) {
                throw InvoiceException::invalidInvoiceStatus($invoice->state->label());
            }

            $this->handle($invoice, $workOrder, $user);
            DB::commit();
            $invoice->load('workOrder.latestInvoices');

            return new PartiallyPaidResource($invoice);

        } catch (InvoiceException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage());

            return Response::unprocessableEntity(message: __($exception->getMessage()));
        } catch (NotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Partially paid API failed due to ' . get_class($exception));

            return Response::notFound(message: $exception->getMessage());
        } catch (CouldNotPerformTransition|TransitionNotFound $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'This transition[partially paid] not allowed to perform with the work order.');

            return Response::unprocessableEntity(message: 'This transition not allowed to perform with the work order.');
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Partially paid API failed', notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('partiallyPaid', $request->route('invoice'));
    }
}

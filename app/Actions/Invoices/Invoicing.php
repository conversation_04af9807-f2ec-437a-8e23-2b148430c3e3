<?php

namespace App\Actions\Invoices;

use App\Enums\Boolean;
use App\Enums\MediaType;
use App\Exceptions\InvoiceException;
use App\Exceptions\NotFoundException;
use App\Helpers\Helper;
use App\Http\Requests\Invoice\InvoiceRequest;
use App\Http\Resources\Invoice\InvoiceResource;
use App\Models\Invoice;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderTask;
use App\Services\InvoiceRegister\InvoiceRegisterClient;
use App\States\Invoices\Voided;
use App\States\WorkOrders\ReadyToInvoice;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;
use Spatie\ModelStates\Exceptions\TransitionNotFound;

class Invoicing extends BaseAction
{
    use AsAction;

    protected static string $method = 'post';

    public static function getSlug(): string
    {
        return 'invoicing';
    }

    public static function getRoute(): string
    {
        return 'api/work-orders/{workOrder}/invoices/actions/';
    }

    public function handle(InvoiceRequest $request, WorkOrder $workOrder, WorkOrderTask $workOrderTask, User $user): Invoice
    {
        return DB::transaction(function () use ($request, $workOrder, $workOrderTask, $user) {
            $invoiceRegisterClient = new InvoiceRegisterClient;

            return $invoiceRegisterClient->register($request, $workOrder, $workOrderTask, $user);
        });
    }

    public function asController(InvoiceRequest $request, WorkOrder $workOrder): InvoiceResource|JsonResponse
    {
        try {
            $user = $request->user();

            if (empty($user)) {
                throw NotFoundException::userNotFound();
            }

            if (! $workOrder->state->equals(ReadyToInvoice::class)) {
                throw new CouldNotPerformTransition(__('This transition not allowed to perform with the work order'));
            }

            $workOrder->load('tasks', 'latestInvoices');

            if (empty($request->invoice_id)) {
                if ($workOrder->latestInvoices->isNotEmpty()) {
                    $latestInvoice = $workOrder->latestInvoices->first();

                    if (! $latestInvoice->state->equals(Voided::class)) {
                        throw new CouldNotPerformTransition(__('This transition not allowed to perform with the work order: an active invoice already exists'));
                    }
                }
            }

            if (empty($workOrder->tasks->first())) {
                throw NotFoundException::resourceNotFound('Work order task');
            }

            $workOrderTask = $workOrder->tasks->first();
            $invoice = $this->handle($request, $workOrder, $workOrderTask, $user);

            $invoice->load([
                'workOrder.latestInvoices',
                'lineItems.quote:quote_id,quote_uuid',
                'lineItems.quoteTask:quote_task_id,quote_task_uuid,quote_task_number',
                'lineItems.serviceCall.media.workOrder.organization',
                'lineItems.serviceCall.lulaAppointment:lula_appointment_id,work_order_reference_number',
                'lineItems.subsidiaries.quoteTaskMaterial:quote_task_material_id,quote_task_material_uuid',
                'lineItems.subsidiaries.workOrderTaskMaterial:work_order_task_material_id,work_order_task_material_uuid',
                'lineItems.subsidiaries.quoteTask:quote_task_id,quote_task_uuid',
                'lineItems.quote.serviceCalls.media' => function ($query) {
                    return $query->with([
                        'workOrder:work_orders.work_order_id,work_orders.organization_id,work_orders.work_order_uuid',
                        'workOrder.organization:organizations.organization_id,organizations.domain',
                    ])->wherePivot('has_thumbnail', Boolean::YES())
                        ->whereIn('media_type', [MediaType::AFTER_MEDIA, MediaType::BEFORE_MEDIA]);
                },
            ]);

            return new InvoiceResource($invoice);

        } catch (InvoiceException $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage());

            return Response::unprocessableEntity(message: __($exception->getMessage()));
        } catch (NotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Invoice creation failed due to ' . get_class($exception));

            return Response::notFound(message: $exception->getMessage());
        } catch (CouldNotPerformTransition|TransitionNotFound $exception) {
            Helper::exceptionLog(exception: $exception, message: 'This transition[create invoice] not allowed to perform with the work order.');

            return Response::unprocessableEntity(message: 'This transition not allowed to perform with the work order.');
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Invoice creation failed', notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('create', [Invoice::class, $request->route('workOrder')]);
    }
}

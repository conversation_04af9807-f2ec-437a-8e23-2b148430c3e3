<?php

namespace App\Actions\Invoices;

use App\Events\Invoice\InvoiceVoidedWebhookEvent;
use App\Exceptions\InvoiceException;
use App\Exceptions\NotFoundException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Resources\Invoice\VoidInvoiceResource;
use App\Jobs\Invoice\CreateInvoiceLogJob;
use App\Models\Invoice;
use App\Models\User;
use App\Models\WorkOrder;
use App\States\Invoices\Voided;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class VoidInvoice extends BaseAction
{
    use AsAction;
    protected static string $method = 'put';

    public static function getSlug(): string
    {
        return 'voidInvoice';
    }

    public function handle(WorkOrder $workOrder, Invoice $invoice, User $user): void
    {
        DB::transaction(function () use ($invoice, $workOrder, $user) {

            $invoice->state->transitionTo(Voided::class, $workOrder, $user);

            dispatch(new CreateInvoiceLogJob($invoice, 'status_change', $user))->afterCommit();

            InvoiceVoidedWebhookEvent::dispatch($workOrder, $invoice);
        });
    }

    public function asController(Request $request, WorkOrder $workOrder, Invoice $invoice): VoidInvoiceResource|JsonResponse
    {
        try {
            DB::beginTransaction();
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $this->validateRouteParameterRelation($workOrder, $invoice);

            $invoice = Invoice::lockForUpdate()
                ->findOrFail($invoice->invoice_id);

            if (! $invoice->state->canTransitionTo(Voided::class)) {
                throw InvoiceException::invalidInvoiceStatus($invoice->state->label());
            }

            $this->handle(
                $workOrder,
                $invoice,
                $user
            );

            DB::commit();

            $invoice->load('workOrder.latestInvoices');

            return new VoidInvoiceResource($invoice);

        } catch (InvoiceException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Void Invoice API failed[InvoiceException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (NotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Void Invoice API Failed [NotFoundException]');

            return Response::notFound($exception->getMessage());
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Void Invoice API Failed[Exception]', additionalInfo: [
                'workOrderId' => $workOrder->work_order_id,
                'InvoiceId' => $invoice->invoice_id,
            ], notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('voided', $request->route('invoice'));
    }
}

<?php

namespace App\Actions\Notifications;

use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Models\DatabaseNotification;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class ClearNotification extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'clearNotification';
    }

    public function handle(DatabaseNotification $notification): void
    {
        DB::transaction(function () use ($notification) {

            $notification->cleared_at = CarbonImmutable::now();
            $notification->save();

        });
    }

    public function asController(Request $request, DatabaseNotification $notification): JsonResponse
    {
        $user = $request->user();
        try {

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            //check if cleared_at_at is null
            if (empty($notification->cleared_at)) {
                $this->handle(
                    $notification
                );
            }

            return Response::message(message: __('Notification cleared successfully'));

        } catch (UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Clear Notification API Failed[UserNotFound]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Clear Notification API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('clear', $request->route('notification'));
    }
}

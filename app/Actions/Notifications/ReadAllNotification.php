<?php

namespace App\Actions\Notifications;

use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class ReadAllNotification extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'readAllNotification';
    }

    public static function getRoute(): string
    {
        return 'api/notifications/actions/';
    }

    public function handle(User $user): void
    {
        DB::transaction(function () use ($user) {

            $user->unreadNotifications()->update(['read_at' => now()]);

        });
    }

    /**
     * @throws Throwable
     */
    public function asController(Request $request): JsonResponse
    {
        $user = $request->user();
        try {

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $message = __('All notifications are mark as read');

            if ($user->unreadNotifications()->count()) {
                $this->handle($user);
            } else {
                $message = __('No Notification Found');
            }

            return Response::message(message: $message);

        } catch (UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Mark all as Read Notification API Failed[UserNotFound]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Marl all as Read Notification API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return (bool) $request->user();
    }
}

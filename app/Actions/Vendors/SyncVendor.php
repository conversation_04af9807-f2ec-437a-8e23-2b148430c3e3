<?php

namespace App\Actions\Vendors;

use App\Enums\Boolean;
use App\Exceptions\NotFoundException\OrganizationNotFoundException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Exceptions\VendorException;
use App\Helpers\Helper;
use App\Jobs\Appfolio\SyncVendorsJob;
use App\Models\Organization;
use App\Models\Vendor;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class SyncVendor extends BaseAction
{
    use AsAction;

    public static function getSlug(): string
    {
        return 'sync';
    }

    public static function getRoute(): string
    {
        return 'api/vendors/{vendor}/actions/';
    }

    public function handle(Organization $organization, Vendor $vendor): void
    {
        dispatch(new SyncVendorsJob($organization, 1, $vendor));
    }

    /**
     * @throws Exception
     */
    public function asController(Request $request, Vendor $vendor): JsonResponse
    {
        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $organization = $user->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            if ($organization->is_appfolio_enabled !== Boolean::YES()) {
                throw VendorException::appfolioNotEnabled();
            }

            $this->handle(
                $organization,
                $vendor
            );

            return Response::message(message: 'Sync in progress');
        } catch (UserNotFoundException|OrganizationNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Sync Vendor API Failed due to ' . get_class($exception));

            return Response::notFound(message: $exception->getMessage());
        } catch (VendorException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Sync Vendor API Failed due to ' . get_class($exception));

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Sync Vendor API Failed', notify: true);

            return Response::internalServerError();
        }
    }

    public function authorize(ActionRequest $request): bool
    {
        return $request->user() && $request->user()->can('sync', $request->route('vendor'));

    }
}

<?php

namespace App\Observers;

use App\Enums\HealthViolationTypes;
use App\Enums\Priority;
use App\Enums\WorkOrderStatus as WorkOrderStatusEnum;
use App\Events\WorkOrder\WorkOrderHealthScoreChange;
use App\Models\Organization;
use App\Models\WorkOrder;
use App\Models\WorkOrderActivityLog;
use App\Models\WorkOrderHealthLog;
use App\Models\WorkOrderHealthTracker;
use App\Services\WorkOrderActivity\Enums\ActivityLogEventTypes;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WorkOrderObserver
{
    /**
     * Handle the WorkOrder "created" event.
     */
    public function created(WorkOrder $workOrder): void
    {
        //
    }

    public function updated(WorkOrder $workOrder): void
    {

        $originalState = $workOrder->getOriginal('state') ?? '';
        $originalPriority = $workOrder->getOriginal('priority') ?? '';

        $workOrderStates = $this->getWorkOrderStates($workOrder, $originalState, $originalPriority);

        foreach ($workOrderStates as $state) {
            if ($state['condition']) {
                try {
                    if ($state['healthy'] == true) {
                        $healthTrackers = $this->getHealthTrackers($state);
                        DB::transaction(function () use ($workOrder, $healthTrackers) {
                            $this->updateHealthLogs($workOrder, $healthTrackers);
                        });

                    } elseif ($state['healthy'] == false) {
                        $healthTrackers = $this->getHealthTrackers($state);
                        DB::transaction(function () use ($workOrder, $healthTrackers) {
                            $this->resetHealthLogs($workOrder, $healthTrackers);
                        });
                        break;
                    }
                } catch (Exception $e) {
                    Log::error($e->getMessage());
                }
            }
        }
    }

    /**
     * @param  WorkOrder  $workOrder  The WorkOrder instance.
     * @param  array{work_order_health_tracker_uuid: string, classification: string, label: string, slug: string}  $healthTracker
     */
    public function createUnhealthyWorkOrderActivityLog(WorkOrder $workOrder, array $healthTracker, string $eventType, string $condition): WorkOrderActivityLog
    {

        $workOrderActivityLog = WorkOrderActivityLog::updateOrCreate([
            'work_order_id' => $workOrder->work_order_id,
            'event' => $eventType,
        ], [
            'work_order_id' => $workOrder->work_order_id,
            'work_order_task_id' => $workOrder->tasks->first()->work_order_task_id ?? null,
            'organization_id' => $workOrder->organization_id,
            'triggered_by' => null,
            'event' => $eventType,
            'event_attributes' => [
                'from' => 'healthy',
                'to' => $healthTracker['classification'],
                'violation' => $healthTracker['label'],
                'violation_slug' => $healthTracker['slug'],
                'condition' => $condition,
                'health_tracker_uuid' => $healthTracker['work_order_health_tracker_uuid'],
            ],
        ]);
        $workOrderActivityLog->created_at = Carbon::now()->addSecond();
        $workOrderActivityLog->save();

        if (! empty($workOrder->work_order_id)) {
            $organization = Organization::find($workOrder->organization_id);
            $work_order = WorkOrder::select([
                'work_order_id', 'work_order_uuid', 'work_order_status_id', 'organization_id', 'state',
            ])->find($workOrder->work_order_id);

            if ($organization instanceof Organization && $work_order instanceof WorkOrder) {
                WorkOrderHealthScoreChange::dispatch($organization, $work_order, $workOrderActivityLog);
            }
        }

        return $workOrderActivityLog;
    }

    /**
     * @param  WorkOrder  $workOrder  The WorkOrder instance.
     * @param  array{work_order_health_tracker_uuid: string, classification: string}  $healthTracker
     */
    public function createWorkOrderActivityLog(WorkOrder $workOrder, array $healthTracker, int $i): WorkOrderActivityLog
    {
        $workOrderActivityLog = WorkOrderActivityLog::updateOrCreate([
            'work_order_id' => $workOrder->work_order_id,
            'event' => ActivityLogEventTypes::WORK_ORDER_HEALTH_HEALTHY(),
        ], [
            'work_order_id' => $workOrder->work_order_id,
            'work_order_task_id' => $workOrder->tasks->first()->work_order_task_id ?? null,
            'organization_id' => $workOrder->organization_id,
            'triggered_by' => null,
            'event' => ActivityLogEventTypes::WORK_ORDER_HEALTH_HEALTHY(),
            'event_attributes' => [
                'from' => $healthTracker['classification'],
                'to' => 'healthy',
                'violation' => 'Healthy',
                'violation_slug' => 'Healthy',
                'condition' => '',
                'health_tracker_uuid' => $healthTracker['work_order_health_tracker_uuid'],
            ],
        ]);
        $workOrderActivityLog->created_at = $workOrder->state != WorkOrderStatusEnum::QUALITY_CHECK() ? Carbon::now()->addSecond() : Carbon::now();
        $workOrderActivityLog->save();

        if (! empty($workOrder->work_order_id) && $i == 1) {
            $organization = Organization::find($workOrder->organization_id);
            $work_order = WorkOrder::select([
                'work_order_id', 'work_order_uuid', 'work_order_status_id', 'organization_id', 'state',
            ])->find($workOrder->work_order_id);

            if ($organization instanceof Organization && $work_order instanceof WorkOrder) {

                WorkOrderHealthScoreChange::dispatch($organization, $work_order, $workOrderActivityLog);

            }
        }

        return $workOrderActivityLog;
    }

    /**
     * Handle the WorkOrder "deleted" event.
     */
    public function deleted(WorkOrder $workOrder): void
    {
        //
    }

    /**
     * Handle the WorkOrder "restored" event.
     */
    public function restored(WorkOrder $workOrder): void
    {
        //
    }

    /**
     * Handle the WorkOrder "force deleted" event.
     */
    public function forceDeleted(WorkOrder $workOrder): void
    {
        //
    }

    /**
     * Generate work order states and conditions.
     *
     * @return array<int, array<string, mixed>>
     */
    private function getWorkOrderStates(WorkOrder $workOrder, string $originalState, string $originalPriority): array
    {
        $priority = [Priority::HIGH(), Priority::URGENT()];

        return [
            [
                'condition' => $workOrder->isDirty('state') &&
                    ($originalState == WorkOrderStatusEnum::CLAIM_PENDING() ||
                        $originalState == WorkOrderStatusEnum::SCHEDULING_IN_PROGRESS()) &&
                        $workOrder->state != WorkOrderStatusEnum::CANCELED() &&
                   in_array($originalPriority, $priority),
                'slug' => [HealthViolationTypes::UNCLAIMED_TOO_LONG()],
                'healthy' => true,
            ],
            [
                'condition' => $workOrder->isDirty('state') &&
                    $originalState == WorkOrderStatusEnum::SCHEDULED() && $workOrder->state != WorkOrderStatusEnum::CANCELED() &&
                    in_array($originalPriority, $priority),
                'slug' => [HealthViolationTypes::EMERGENCY_MISSED_ETA()],
                'healthy' => true,
            ],
            [
                'condition' => $workOrder->isDirty('state') &&
                    $originalState == WorkOrderStatusEnum::WORK_IN_PROGRESS() && $workOrder->state != WorkOrderStatusEnum::QUALITY_CHECK() && $workOrder->state != WorkOrderStatusEnum::CANCELED(),
                'slug' => [HealthViolationTypes::RUNNING_CLOCK()],
                'healthy' => true,
            ],
            [
                'condition' => $workOrder->isDirty('priority') &&
                in_array($originalPriority, $priority) && ! in_array($workOrder->priority, $priority),
                'slug' => [HealthViolationTypes::UNCLAIMED_TOO_LONG(), HealthViolationTypes::EMERGENCY_MISSED_ETA(), HealthViolationTypes::RUNNING_CLOCK()],
                'healthy' => true,
            ],
            [
                'condition' => $workOrder->isDirty('priority') && ! in_array($originalPriority, $priority) && in_array($workOrder->priority, $priority),
                'slug' => [HealthViolationTypes::UNCLAIMED_TOO_LONG(), HealthViolationTypes::EMERGENCY_MISSED_ETA(), HealthViolationTypes::RUNNING_CLOCK()],
                'healthy' => false,
            ],
        ];
    }

    /**
     * Retrieve health trackers by state slug.
     *
     * @param  array<string, mixed>  $state
     * @return array<int, array<string, mixed>>
     *
     * @throws Exception
     */
    private function getHealthTrackers(array $state): array
    {
        $healthTrackers = WorkOrderHealthTracker::getTrackerIdsBySlug($state['slug']);
        if (! is_array($healthTrackers)) {
            throw new Exception('Health trackers must be an array.');
        }

        return $healthTrackers;
    }

    /**
     * Update health logs for a work order.
     *
     * @param  array<int, array<string, mixed>>  $healthTrackers
     */
    private function updateHealthLogs(WorkOrder $workOrder, array $healthTrackers): void
    {
        $i = 1;
        foreach ($healthTrackers as $healthTracker) {
            if (isset($healthTracker['work_order_health_tracker_id'])) {

                $affectedRows = WorkOrderHealthLog::where('work_order_id', $workOrder->work_order_id)
                    ->where('work_order_health_tracker_id', $healthTracker['work_order_health_tracker_id'])
                    ->whereNull('resolved_at')
                    ->update(['resolved_at' => CarbonImmutable::now()]);

                if ($affectedRows > 0) {
                    $wo = WorkOrder::with(['tasks.latestServiceCalls'])->find($workOrder->work_order_id);
                    if ($wo) {
                        $filteredHealthTracker = [
                            'work_order_health_tracker_uuid' => $healthTracker['work_order_health_tracker_uuid'],
                            'classification' => $healthTracker['classification'],
                        ];
                        $this->createWorkOrderActivityLog($wo, $filteredHealthTracker, $i);
                        $i++;
                    }

                }
            }

        }
    }

    /**
     * Update health logs for a work order.
     *
     * @param  array<int, array<string, mixed>>  $healthTrackers
     */
    private function resetHealthLogs(WorkOrder $workOrder, array $healthTrackers): void
    {

        foreach ($healthTrackers as $healthTracker) {

            if (isset($healthTracker['work_order_health_tracker_id']) && $workOrder->state != WorkOrderStatusEnum::QUALITY_CHECK()) {

                $affectedRows = WorkOrderHealthLog::where('work_order_id', $workOrder->work_order_id)
                    ->where('work_order_health_tracker_id', $healthTracker['work_order_health_tracker_id'])
                    ->update(['resolved_at' => null]);

                if ($affectedRows > 0) {
                    $wo = WorkOrder::with(['tasks.latestServiceCalls'])->find($workOrder->work_order_id);
                    if ($wo) {
                        $filteredHealthTracker = [
                            'work_order_health_tracker_uuid' => $healthTracker['work_order_health_tracker_uuid'],
                            'classification' => $healthTracker['classification'],
                            'label' => $healthTracker['label'],
                            'slug' => $healthTracker['slug'],
                        ];
                        $eventType = $healthTracker['weight'] == 2 ? ActivityLogEventTypes::WORK_ORDER_HEALTH_CRITICAL() : ActivityLogEventTypes::WORK_ORDER_HEALTH_AT_RISK();
                        $condition = $healthTracker['weight'] == 2 ? $this->getIsCriticalMessage($healthTracker['slug']) : $this->getAtRiskMessage($healthTracker['slug']);
                        $this->createUnhealthyWorkOrderActivityLog($wo, $filteredHealthTracker, $eventType, $condition);

                    }

                }
            }

        }
    }

    private function getAtRiskMessage(string $violationSlug): string
    {
        return match ($violationSlug) {
            HealthViolationTypes::UNCLAIMED_TOO_LONG() => 'Claim pending for more than 2 hours',
            HealthViolationTypes::RUNNING_CLOCK() => 'Trip has been in progress for more than 2 hours',
            HealthViolationTypes::EMERGENCY_MISSED_ETA() => 'Trip arrival window is 1 hour past the ETA',
            default => '',
        };
    }

    private function getIsCriticalMessage(string $violationSlug): string
    {
        return match ($violationSlug) {
            HealthViolationTypes::UNCLAIMED_TOO_LONG() => 'Claim pending for more than 4 hours',
            HealthViolationTypes::RUNNING_CLOCK() => 'Trip has been in progress for more than 4 hours',
            HealthViolationTypes::EMERGENCY_MISSED_ETA() => 'Trip arrival window is 2 hours past the ETA',
            default => '',
        };
    }
}

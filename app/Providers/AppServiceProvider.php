<?php

namespace App\Providers;

use App\Channels\DatabaseChannel;
use App\Models\DatabaseNotification;
use App\Models\User;
use App\Models\WorkOrder;
use App\Observers\WorkOrderObserver;
use Aws\SecretsManager\SecretsManagerClient;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }

        Model::preventLazyLoading(! app()->isProduction());

        // Register secret manager
        $this->app->bind(SecretsManagerClient::class, function () {
            return new SecretsManagerClient([
                'version' => 'latest',
                'region' => config('services.cognito.region'),
            ]);
        });

        // TODO: This can be enable this once the Work Order Action Service is implemented.
        //Model::shouldBeStrict(! app()->isProduction());
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->app->instance(\Illuminate\Notifications\Channels\DatabaseChannel::class, new DatabaseChannel);
        $this->app->instance(\Illuminate\Notifications\DatabaseNotification::class, new DatabaseNotification);
        WorkOrder::observe(WorkOrderObserver::class);
        Relation::$morphMap = [
            'user' => User::class,
        ];
    }
}

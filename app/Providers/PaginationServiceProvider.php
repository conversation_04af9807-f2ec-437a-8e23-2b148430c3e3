<?php

namespace App\Providers;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\ServiceProvider;

class PaginationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        JsonResource::withoutWrapping();

        JsonResource::macro('paginationInformation', function ($request, $paginated, array $default) {
            $metaData = [
                'prev' => $default['links']['prev'] ?? null,
                'next' => $default['links']['next'] ?? null,
                'last' => $paginated['last_page_url'] ?? null,
                'total' => $paginated['total'] ?? null,
                'per_page' => $paginated['per_page'] ?? null,
                'current_page' => $paginated['current_page'] ?? null,
                'from' => $default['meta']['from'] ?? null,
                'to' => $default['meta']['to'] ?? null,
            ];

            if (! empty($default['meta']['next_cursor']) || ! empty($default['meta']['prev_cursor'])) {
                $metaData['next_cursor'] = $default['meta']['next_cursor'] ?? null;
                $metaData['prev_cursor'] = $default['meta']['prev_cursor'] ?? null;
            }

            return [
                'meta' => $metaData,
            ];
        });
    }
}

<?php

namespace App\Providers;

use Illuminate\Http\Request;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Str;

class RequestServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        if (! app()->runningInConsole()) {
            request()->attributes->add(['request_uuid' => (string) Str::orderedUuid()]);
        }

        Request::macro('expectsDevice', function (string $device = 'Mobile') {
            /** @var Request $this */
            return Str::lower($this->header('Device-Type', 'Web')) === Str::lower($device);
        });

        Request::macro('requestSource', function (string $device = 'Mobile') {
            /** @var Request $this */
            return Str::lower($this->header('Device-Type', 'Web')) === Str::lower($device) ? 'mobile' : 'web';
        });
    }
}

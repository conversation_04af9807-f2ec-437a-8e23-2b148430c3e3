<?php

namespace App\Rules;

use App\Enums\ScheduleTimings;
use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class TimingRule implements DataAwareRule, ValidationRule
{
    /**
     * All of the data under validation.
     *
     * @var array<string, mixed>
     */
    protected $data = [];

    /**
     * Set the data under validation.
     *
     * @param  array<string, mixed>  $data
     */
    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }

    /**
     * Run the validation rule.
     *
     * @param  Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $availableTimings = ScheduleTimings::values();

        if (count($value)) {

            // Check if timing array contains values other than specified
            $invalidTimings = array_diff($value, $availableTimings);

            if (! empty($invalidTimings)) {
                $fail('The :attribute contains invalid values.');
            }
            // Check if timing array contains duplicate values
            if ($this->hasDuplicates($value)) {
                $fail('The :attribute contains invalid values: contains duplicate values.');
            }

            // Check if "anytime" is combined with other slug
            if (
                count($value) > 1 &&
                in_array('anytime', $value)
            ) {
                $fail('The :attribute contains invalid values.');
            }
        } else {
            $fail('The :attribute field is required.');
        }
    }

    /**
     * @param  array<string, mixed>  $array
     */
    public function hasDuplicates(array $array): bool
    {
        $counts = array_count_values($array);

        foreach ($counts as $count) {
            if ($count > 1) {
                return true;
            }
        }

        return false;
    }
}

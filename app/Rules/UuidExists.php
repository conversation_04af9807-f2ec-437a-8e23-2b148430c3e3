<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Ramsey\Uuid\Uuid;

class UuidExists implements ValidationRule
{
    protected Model $model;

    protected string $column;

    protected bool $withTrashed;

    /**
     * Initialize model and column for validation
     *
     * @param  class-string<Model>  $model
     */
    public function __construct(string $model, string $column = 'uuid', bool $withTrashed = false)
    {
        $this->model = new $model;

        $this->column = $column;

        $this->withTrashed = $withTrashed;
    }

    /**
     * Run the validation rule.
     *
     * @param  Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        /** @var string $field * */
        $field = Str::replace(['_id', '_uuid'], '', $attribute);
        if (is_array($value)) {
            foreach ($value as $uuid) {
                if (! Uuid::isValid($uuid)) {
                    $fail(__("Invalid {$field}."));
                }
            }
        } else {
            if (! Uuid::isValid($value)) {
                $fail(__("Invalid {$field}."));
            }
        }

        if ($this->withTrashed) {
            if (! DB::table($this->model->getTable())->whereRaw("{$this->column} = UUID_TO_BIN(?)", [$value])->exists()) {
                $fail(__("Invalid {$field}."));
            }
        } else {
            if (! $this->model->whereUuid($value)->exists()) {
                $fail(__("Invalid {$field}."));
            }
        }

    }
}

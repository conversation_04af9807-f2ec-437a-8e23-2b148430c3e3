<?php

namespace App\Rules;

use Carbon\CarbonImmutable;
use Closure;
use Exception;
use Illuminate\Contracts\Validation\ValidationRule;

class DateAfterRule implements ValidationRule
{
    /**
     * All of the data under validation.
     */
    public function __construct(protected string $timezone = 'UTC')
    {
        $this->timezone = $timezone;
    }

    /**
     * Run the validation rule.
     *
     * @param  Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        try {
            $date = CarbonImmutable::parse($value, $this->timezone)->startOfDay();
            $today = CarbonImmutable::now($this->timezone)->startOfDay();

            if ($date->lt($today)) {
                $fail("The selected date [{$value}] is already passed.");
            }
        } catch (Exception $e) {
            $fail("The selected date [{$value}] is not a valid date.");
        }
    }
}

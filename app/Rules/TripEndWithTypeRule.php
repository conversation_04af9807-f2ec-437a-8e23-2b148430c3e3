<?php

namespace App\Rules;

use App\Enums\Trip;
use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class TripEndWithTypeRule implements DataAwareRule, ValidationRule
{
    /**
     * All of the data under validation.
     *
     * @var array<string, mixed>
     */
    protected $data = [];

    /**
     * Set the data under validation.
     *
     * @param  array<string, mixed>  $data
     */
    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }

    /**
     * Run the validation rule.
     *
     * @param  Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (isset($this->data['trip_end_with'])) {

            if (in_array($this->data['trip_end_with'], [Trip::PARTIALLY_COMPLETED()])) {
                if (isset($this->data['trip_end_with_type'])) {
                    if (! in_array($this->data['trip_end_with_type'], [Trip::SUBMIT_QUOTE(), Trip::FINISH_WITH_ANOTHER_DAY(), Trip::AWAIT_FOR_QUOTE_APPROVAL()])) {
                        $definedValues = collect(Trip::partialCompleteOptions())->pluck('value')->toArray();
                        $definedValues = implode(', ', $definedValues);
                        $fail("The :attribute must be in [{$definedValues}].");
                    }
                } else {
                    $fail('The :attribute field is required.');
                }
            }

            if (in_array($this->data['trip_end_with'], [Trip::NO_WORK()])) {
                if (isset($this->data['trip_end_with_type'])) {
                    if (! in_array($this->data['trip_end_with_type'], [
                        Trip::SUBMIT_QUOTE(), Trip::GET_PARTS(), Trip::AWAIT_FOR_QUOTE_APPROVAL(),
                        Trip::RESIDENT_DID_NOT_SHOW_UP(), Trip::OTHER(),
                    ])) {
                        $definedValues = collect(Trip::noWorkOptions())->pluck('value')->toArray();
                        $definedValues = implode(', ', $definedValues);
                        $fail("The :attribute must be in [{$definedValues}].");
                    }
                } else {
                    $fail('The :attribute field is required.');
                }
            }

            if (in_array($this->data['trip_end_with'], [Trip::ALL_COMPLETED()])) {
                if (isset($this->data['trip_end_with_type'])) {
                    $fail('The :attribute is invalid');
                }
            }
        }
    }
}

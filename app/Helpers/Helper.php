<?php

namespace App\Helpers;

use App\Mail\ExceptionOccurred;
use App\Models\Invoice;
use App\Models\WorkOrder;
use App\Notifications\DeveloperAlertNotification;
use Aws\SecretsManager\SecretsManagerClient;
use Carbon\Carbon;
use Exception;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Propaganistas\LaravelPhone\PhoneNumber;
use Sentry\Laravel\Integration;
use Symfony\Component\ErrorHandler\ErrorRenderer\HtmlErrorRenderer;
use Symfony\Component\ErrorHandler\Exception\FlattenException;
use Throwable;

/**
 * Helper
 */
class Helper
{
    /**
     * Generate Random AlphaNumber:
     * Unique random alphanumeric for given table.
     */
    public static function generateRandomAlphaNumber(string $table, string $column, int $length = 8): string
    {
        do {
            $data = [];

            $randomNumber = strtoupper(substr(md5(uniqid('', true)), rand(0, 25), $length));

            if ($table === 'work_orders') {
                $data = WorkOrder::select($column)
                    ->where($column, $randomNumber)
                    ->first();
            } elseif ($table === 'invoices') {
                $data = Invoice::select($column)
                    ->where($column, $randomNumber)
                    ->first();
            }
        } while (! empty($data));

        return $randomNumber;
    }

    public static function resolvePhoneNumberForTwilio(string $phoneNumber, string $country = 'US'): string
    {
        if (config('twilio-notification-channel.debug_mode', false)) {

            if (! in_array(substr($phoneNumber, -10), array_map(
                fn ($number) => substr($number, -10),
                array_filter(
                    explode(',', config('twilio-notification-channel.debug_whitelisted_numbers'))
                )
            ), true)) {
                $phoneNumber = config('twilio-notification-channel.debug_to_number');
            }
        }

        return (new PhoneNumber($phoneNumber, $country))->formatE164();
    }

    /**
     * @param  array<string,mixed>  $data
     */
    public static function developerAlert(array $data, string $message): void
    {
        $data['message'] = $message;
        $data['time'] = Carbon::now()->setTimezone(config('settings.default_timezone'))->format('d-m-Y h:s A - T');
        $ignoreInputKeys = config('settings.developer_alert.exception_hide_values');
        $data = Arr::except($data, $ignoreInputKeys);

        info($message, [
            'data' => $data,
            'message' => $message,
        ]);

        try {
            $developerEmails = explode(',', config('settings.developer_alert.notify_emails'));
            if (! empty(array_filter($developerEmails)) && ! app()->isLocal()) {
                Mail::to(array_map(function ($email) {
                    return trim($email);
                }, $developerEmails))
                    ->queue(new DeveloperAlertNotification($data));
            }
        } catch (Exception $exception) {
            info($message, [
                'data' => $data,
                'message' => $message,
                'exception' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'line' => $exception->getLine(),
                'file' => $exception->getFile(),
            ]);
        }
    }

    /**
     * Log Exceptions.
     *
     * @param  array<string, mixed>  $additionalInfo
     */
    public static function exceptionLog(Exception|Throwable $exception, array $additionalInfo = [], string $message = '', bool $notify = false): void
    {
        try {
            $request = request();
            $sensitive_data = ['password', 'password_confirmation'];

            if (! $message) {
                $message = $exception->getMessage();
            }

            $requestData = json_encode($request->except($sensitive_data), JSON_THROW_ON_ERROR);

            $logChannelLevel = $notify ? 'error' : 'debug';
            $logChannel = Log::channel($notify ? 'developer_alert' : config('logging.default'));
            $logChannel->$logChannelLevel($message, [
                'url' => $request->fullUrl(),
                'method' => $request->getMethod(),
                'exception_class' => get_class($exception),
                'exception' => $exception->getMessage(),
                'code' => $exception->getCode(),
                'line' => $exception->getLine(),
                'file' => $exception->getFile(),
                'request' => $requestData,
                'request_id' => $request->request_uuid ?? null,
                'additional_info' => count($additionalInfo) ? json_encode($additionalInfo) : null,
            ]);

            $additionalInfo['request_id'] = $request->attributes->get('request_uuid');

            if ($notify && Config::get('settings.debug.mail.enabled') && ! app()->isLocal()) {
                $addresses = collect(explode(',', Config::get('settings.debug.mail.addresses')))->filter()->all();
                $flattenException = FlattenException::createFromThrowable($exception);
                $handler = new HtmlErrorRenderer(true);
                $css = $handler->getStylesheet();
                $content = $handler->getBody($flattenException);

                // Rate limit the emails
                $throttle = Limit::perMinutes(15, 5)->by($flattenException->getMessage());
                $rateLimit = RateLimiter::attempt(
                    with(Str::slug($throttle->key), fn ($key) => md5($key)),
                    $throttle->maxAttempts,
                    fn () => Mail::to($addresses)
                        ->queue(new ExceptionOccurred(exception: [
                            'css' => $css,
                            'content' => $content,
                            'message' => $flattenException->getMessage(),
                            'additional_info' => $additionalInfo,
                        ], sub: $message)),
                    $throttle->decaySeconds
                );

                Log::info('Developer log trigger with rate limit', [
                    'rateLimit' => $rateLimit,
                ]);

                Integration::captureUnhandledException($exception);
            }

            if (! empty($additionalInfo['request_id'])) {
                Session::flash('exception_log', [
                    'exception_class' => get_class($exception),
                    'message' => $exception->getMessage(),
                    'code' => $exception->getCode(),
                    'line' => $exception->getLine(),
                    'file' => $exception->getFile(),
                ]);
            }
        } catch (Exception $exception) {
            Log::emergency('Error in logging', [
                'message' => $exception->getMessage(),
            ]);
        }
    }

    public static function displayPhoneNumber(string $phone): string
    {
        // add logic to correctly format number here
        // a more robust ways would be to use a regular expression
        return '(' . substr($phone, 0, 3) . ') ' . substr($phone, 3, 3) . '-' . substr($phone, 6);
    }

    public static function generateSecureLink(string $userId, int $expiry = 5): string
    {
        return URL::temporarySignedRoute(
            'verify',
            Carbon::now()->addMinutes($expiry),
            ['id' => $userId]
        );
    }

    public static function getLocationApiKey()
    {
        $client = App::make(SecretsManagerClient::class);

        $secretName = 'foresight/' . config('services.cognito.location_key') . '/locationkey';
        try {
            $result = $client->getSecretValue([
                'SecretId' => $secretName,
            ]);

            if (isset($result['SecretString'])) {
                $secret = $result['SecretString'];
                $data = json_decode($secret, true);
                $index = 'foresight-' . config('services.cognito.location_key') . '-location-key';

                return $data[$index];
            } else {
                return base64_decode($result['SecretBinary']);
            }
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Location API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    public static function buildStringUrl($url)
    {

        $signedUrl = '';

        if (strpos($url, 'verify')) {
            $parsedUrl = parse_url($url);
            $path = $parsedUrl['path'] ?? '';
            preg_match('/\/verify\/([a-f0-9-]{36})/', $path, $uuidMatches);
            $uuid = $uuidMatches[1] ?? null;
            $query = $parsedUrl['query'] ?? '';
            parse_str($query, $queryParams);

            return Request::create(url("/api/verify/{$uuid}") . "?expires={$queryParams['expires']}&signature={$queryParams['signature']}");
        }

        return $signedUrl;
    }
}

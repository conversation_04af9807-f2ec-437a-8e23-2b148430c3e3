<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class OnboardingMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $onboardingUrl;
    public $expiration;
    public $organizationName;

    public function __construct($onboardingUrl, $expiration, $organizationName)
    {
        $this->onboardingUrl = $onboardingUrl;
        $this->expiration = $expiration;
        $this->organizationName = $organizationName;

    }

    public function build()
    {
        return $this->subject("You've Been Invited to Join Foresight")
            ->view('emails.onboarding-link');
    }
}

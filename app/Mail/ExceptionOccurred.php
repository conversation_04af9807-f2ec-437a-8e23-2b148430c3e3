<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ExceptionOccurred extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * @param  array<string, mixed>  $exception
     */
    public function __construct(protected array $exception, protected string $sub = 'Exception Occurred') {}

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $environment = ucwords(app()->environment());

        return new Envelope(
            subject: "Exception::Alert::{$environment} - {$this->sub}"
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'developer.exception.notification',
            with: [
                'css' => $this->exception['css'] ?? null,
                'content' => $this->exception['content'] ?? null,
                'message' => $this->exception['message'] ?? null,
                'additional_info' => $this->exception['additional_info'] ?? null,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}

<?php

namespace App\Broadcasting;

use App\Models\Organization;
use App\Models\User;
use App\Models\WorkOrder;

class WorkOrderChannel
{
    /**
     * Authenticate the user's access to the channel.
     */
    public function join(User $user, string $organizationUuid, string $workOrderUuid): bool
    {
        $workOrder = WorkOrder::whereUuid($workOrderUuid)->first();

        if ($user->cant('view', $workOrder)) {
            return false;
        }

        $organization = Organization::whereUuid($organizationUuid)->first();

        if (empty($workOrder) || empty($organization)) {
            return false;
        }

        return ($user->organization_id === $workOrder->organization_id)
            && ($workOrder->organization_id === $organization->organization_id);
    }
}

<?php

namespace App\Broadcasting;

use App\Models\Organization;
use App\Models\User;
use App\Models\WorkOrder;

class WorkOrderListChannel
{
    /**
     * Authenticate the user's access to the channel.
     */
    public function join(User $user, string $organizationUuid): bool
    {
        if ($user->cant('viewAny', WorkOrder::class)) {
            return false;
        }

        $organization = Organization::whereUuid($organizationUuid)->first();

        if (empty($organization)) {
            return false;
        }

        return $user->organization_id === $organization->organization_id;
    }
}

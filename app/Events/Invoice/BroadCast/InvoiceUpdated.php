<?php

namespace App\Events\Invoice\BroadCast;

use App\Models\Invoice;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class InvoiceUpdated implements ShouldBroadcast, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /** @var WorkOrder|null */
    public $workOrder;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public int $workOrderId,
        public int $invoiceId,
    ) {}

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue()
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $workOrder = $this->getWorkOrder();

        return [
            new PrivateChannel("organization.{$workOrder->organization->organization_uuid}.work-orders.{$workOrder->work_order_uuid}"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'invoice.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith()
    {
        $invoice = Invoice::select('invoice_uuid')->where('invoice_id', $this->invoiceId)->first();
        $workOrder = $this->getWorkOrder();

        return [
            'data' => [
                'work_order_id' => $workOrder->work_order_uuid,
                'invoice_id' => $invoice?->invoice_uuid,
            ],
        ];
    }

    public function getWorkOrder(): WorkOrder
    {
        if (empty($this->workOrder)) {
            $this->workOrder = WorkOrder::with([
                'organization:organization_id,organization_uuid',
            ])
                ->select('work_order_id', 'organization_id', 'state', 'work_order_uuid')
                ->where('work_order_id', $this->workOrderId)
                ->firstOrFail();
        }

        return $this->workOrder;
    }
}

<?php

namespace App\Events\Invoice;

use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\Invoice;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class InvoiceVoidedWebhookEvent implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
        public Invoice $invoice
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();

        return [
            'data' => [

                'status' => [
                    'title' => $workOrder->state->label(),
                    'slug' => $workOrder->state->getValue(),
                ],
                'workOrderId' => $workOrder->work_order_uuid,

                'invoiceDetails' => [
                    'invoiceId' => $this->invoice->invoice_uuid,
                    'status' => [
                        'title' => $this->invoice->state->label(),
                        'slug' => $this->invoice->state->getValue(),
                    ],
                ],
            ],
            'eventType' => 'foresight:invoice.voided',
        ];
    }
}

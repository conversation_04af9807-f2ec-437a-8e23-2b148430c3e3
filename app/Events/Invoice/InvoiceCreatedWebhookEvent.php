<?php

namespace App\Events\Invoice;

use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\Invoice;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class InvoiceCreatedWebhookEvent implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
        public Invoice $invoice,
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    /**
     * @return array<string, mixed>
     */
    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();
        $invoice = $this->invoice;

        $invoiceDetails = [
            'invoiceId' => $invoice->invoice_uuid,
            'invoiceNumber' => $invoice->invoice_number,
            'description' => $invoice->description,
            'totalMarkupFeeInCents' => $invoice->total_markup_fee_in_cents,
            'costInCents' => $invoice->cost_in_cents,
            'totalCostInCents' => $invoice->total_cost_in_cents,
            'amountPaidInCents' => $invoice->amount_paid_in_cents,
            'status' => [
                'slug' => $invoice->state->getValue(),
                'title' => $invoice->state->label(),
            ],
            'invoiceLineItems' => [],
        ];

        if ($invoice->lineItems->isNotEmpty()) {
            $lineItems = [];
            foreach ($invoice->lineItems as $lineItem) {
                $item = [
                    'invoiceLineItemId' => $lineItem->invoice_line_item_uuid,
                    'description' => $lineItem->invoice_line_item_uuid,
                    'invoiceLineItemType' => $lineItem->invoice_line_item_uuid,
                    'totalMarkupFeeInCents' => $lineItem->invoice_line_item_uuid,
                    'costInCents' => $lineItem->invoice_line_item_uuid,
                    'totalCostInCents' => $lineItem->invoice_line_item_uuid,
                    'subsidiaries' => [],
                ];

                if ($lineItem->subsidiaries->isNotEmpty()) {
                    $subsidiaries = [];
                    foreach ($lineItem->subsidiaries as $subsidiary) {
                        array_push($subsidiaries, [
                            'subsidiaryId' => $subsidiary->invoice_line_item_subsidiary_uuid,
                            'subsidiaryType' => $subsidiary->subsidiary_type,
                            'description' => $subsidiary->description,
                            'hourlyRateInCents' => $subsidiary->hourly_rate_in_cents,
                            'durationInSeconds' => $subsidiary->duration_in_seconds,
                            'quantity' => $subsidiary->quantity,
                            'quantityType' => $subsidiary->quantity_type,
                            'costType' => $subsidiary->cost_type,
                            'markupFeeType' => $subsidiary->markup_fee_type,
                            'markupFeeTypeValue' => $subsidiary->markup_fee_type_value,
                            'markupFeeInCents' => $subsidiary->markup_fee_in_cents,
                            'costInCents' => $subsidiary->cost_in_cents,
                            'totalCostInCents' => $subsidiary->total_cost_in_cents,
                        ]);
                    }
                    $item['subsidiaries'] = $subsidiaries;
                }

                array_push($lineItems, $item);
            }
            $invoiceDetails['invoiceLineItems'] = $lineItems;
        }

        return [
            'data' => [
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],
                'workOrderId' => $workOrder->work_order_uuid,
                'invoiceDetails' => $invoiceDetails,
            ],
            'eventType' => 'foresight:invoice.created',
        ];
    }
}

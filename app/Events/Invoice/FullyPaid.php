<?php

namespace App\Events\Invoice;

use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\Invoice;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class FullyPaid implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
        public Invoice $invoice,
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    /**
     * @return array<string, mixed>
     */
    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();
        $invoice = $this->invoice;

        $invoiceDetails = [
            'invoiceId' => $invoice->invoice_uuid,
            'invoiceNumber' => $invoice->invoice_number,
            'status' => [
                'slug' => $invoice->state->getValue(),
                'title' => $invoice->state->label(),
            ],
        ];

        return [
            'data' => [
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],
                'workOrderId' => $workOrder->work_order_uuid,
                'invoiceDetails' => $invoiceDetails,
            ],
            'eventType' => 'foresight:invoice.paid',
        ];
    }
}

<?php

namespace App\Events\ServiceRequest;

use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ResidentAvailabilityStore implements ShouldDispatchAfterCommit, ShouldQueue, ShouldQueueAfterCommit
{
    use Dispatchable, InteractsWithSockets, Queueable, SerializesModels;

    /**
     * @param  array<int,mixed>  $residentAvailability
     */
    public function __construct(
        public int $serviceRequestId,
        public array $residentAvailability,
        public ?User $user = null,
    ) {}
}

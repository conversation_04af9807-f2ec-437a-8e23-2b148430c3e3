<?php

declare(strict_types=1);

namespace App\Events\ServiceRequest\ActivityLog;

use App\Models\ServiceRequestActivityLog;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestActivityLogDeleted extends BaseIssueEvent implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'serviceRequest.activityLog.deleted';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->serviceRequestActivityLog = $this->serviceRequestActivityLog ?? $this->getServiceRequestActivityLog();

        return [
            'data' => [
                'service_request_id' => $this->serviceRequestActivityLog->serviceRequest?->service_request_uuid,
                'activity_log' => [
                    'activity_uuid' => $this->serviceRequestActivityLog->service_request_activity_log_uuid,
                    'event' => $this->serviceRequestActivityLog->event,
                    'created_at' => $this->serviceRequestActivityLog->created_at->toIso8601String(),
                ],
            ],
        ];
    }

    public function getServiceRequestActivityLog(): ServiceRequestActivityLog
    {
        return ServiceRequestActivityLog::withTrashed()
            ->select(
                'service_request_activity_log_id', 'service_request_activity_log_uuid', 'event',
                'organization_id', 'service_request_id', 'created_at'
            )
            ->with([
                'organization:organization_id,organization_uuid',
                'serviceRequest:service_request_id,service_request_uuid',
            ])
            ->where('service_request_activity_log_id', $this->serviceRequestActivityLogId)
            ->firstOrFail();
    }
}

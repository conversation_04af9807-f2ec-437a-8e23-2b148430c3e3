<?php

namespace App\Events\ServiceRequest;

use App\Enums\Priority;
use App\Models\ServiceRequest;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class ServiceRequestPriorityChanged extends BaseServiceRequestEvent implements ShouldBroadcast, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public ?ServiceRequest $serviceRequest = null;

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'serviceRequest.priority.changed';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        return [
            'data' => [
                'service_request_id' => $this->serviceRequest->service_request_uuid,
                'priority' => [
                    'label' => Str::title(Priority::from($this->serviceRequest->priority)->name),
                    'value' => Priority::from($this->serviceRequest->priority)->value,
                ],
            ],
        ];
    }
}

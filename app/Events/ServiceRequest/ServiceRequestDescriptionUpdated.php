<?php

namespace App\Events\ServiceRequest;

use App\Enums\Boolean;
use App\Http\Resources\ServiceRequest\MediaResource;
use App\Http\Resources\ServiceRequest\ServiceRequestDescriptionResource;
use App\Models\ServiceRequestDescription;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestDescriptionUpdated implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /** @var ServiceRequestDescription|null */
    public $serviceRequestDescription;

    public function __construct(public int $serviceRequestDescriptionId) {}

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue(): string
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        $this->serviceRequestDescription = $this->serviceRequestDescription ?? $this->getServiceRequestDescription();
        $organizationUuid = $this->serviceRequestDescription->organization?->organization_uuid;
        $serviceRequestUuid = $this->serviceRequestDescription->serviceRequest?->service_request_uuid;

        return [
            new PrivateChannel("organization.{$organizationUuid}.service-requests.{$serviceRequestUuid}"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'serviceRequest.description.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->serviceRequestDescription = $this->serviceRequestDescription ?? $this->getServiceRequestDescription();

        return [
            'data' => new ServiceRequestDescriptionResource($this->serviceRequestDescription),
        ];
    }

    public function getServiceRequestDescription(): ServiceRequestDescription
    {
        $serviceRequestDescription = ServiceRequestDescription::select(
            'service_request_description_uuid', 'service_request_id', 'description', 'additional_info', 'user_id',
            'created_at', 'deleted_at', 'organization_id'
        )
            ->with([
                'createdUser:user_id,first_name,middle_name,last_name',
                'organization:organization_id,organization_uuid',
                'serviceRequest' => function ($query) {
                    return $query->with([
                        'media' => function ($query) {
                            return $query->select(
                                'media.media_id',
                                'media.media_uuid',
                                'media.file_name',
                                'media.mime_type',
                                'media.thumbnail_file_name',
                                'media.optimized_file_name',
                                'media.deleted_at',
                            )
                                ->wherePivot('has_upload_completed', Boolean::YES())
                                ->withTrashed();
                        },
                    ])
                        ->select('service_request_id', 'service_request_uuid');
                },
            ])
            ->where('service_request_description_id', $this->serviceRequestDescriptionId)
            ->firstOrFail();

        if (
            ! empty($serviceRequestDescription->additional_info['media_ids']) &&
            ! empty($serviceRequestDescription->serviceRequest) &&
            $serviceRequestDescription->serviceRequest->media->isNotEmpty()
        ) {
            $medias = MediaResource::collection($serviceRequestDescription->serviceRequest->media->whereIn('media_id', $serviceRequestDescription->additional_info['media_ids']) ?? collect([]));
            $serviceRequestDescription->setRelation('medias', $medias);
        }

        return $serviceRequestDescription;
    }
}

<?php

namespace App\Events\ServiceRequest;

use App\Http\Resources\ServiceRequest\MediaResource;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestActivityLog;
use App\States\ServiceRequests\ServiceRequestState;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestStateChange implements ShouldBroadcastNow, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /** @var ServiceRequest|null */
    public $serviceRequest;

    /** @var ServiceRequestActivityLog|null */
    public $serviceRequestActivityLog;

    public function __construct(
        public int $serviceRequestId,
        public int $serviceRequestActivityLogId,
        public ServiceRequestState $state,
    ) {}

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        $serviceRequest = $this->getServiceRequest();

        return [
            new PrivateChannel("organization.{$serviceRequest->organization->organization_uuid}.service-requests"),
            new PrivateChannel("organization.{$serviceRequest->organization->organization_uuid}.service-requests.{$serviceRequest->service_request_uuid}"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'servicerequest.' . $this->state->getValue();
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $serviceRequest = $this->getServiceRequest();
        $serviceRequestActivityLog = $this->getServiceRequestActivityLog();

        /** @var array<int|string, mixed>>|null */
        $eventAttributes = $serviceRequestActivityLog->event_attributes;

        if (! empty($eventAttributes['medias'])) {
            $mediaIds = $eventAttributes['medias'];

            $serviceRequest->load(['media' => function ($query) use ($mediaIds) {
                return $query->whereUuid($mediaIds)
                    ->select(
                        'media.media_id', 'media.media_uuid', 'media.file_name', 'media.mime_type', 'media.thumbnail_file_name', 'media.optimized_file_name'
                    )
                    ->withTrashed();
            }]);

            $media = $serviceRequest->media;

            if ($media->isNotEmpty()) {
                $eventAttributes['medias'] = MediaResource::collection($media);
            }
        }

        return [
            'data' => [
                'service_request_id' => $serviceRequest->service_request_uuid,
                'service_request_reference_number' => $serviceRequest->service_request_reference_number,
                'service_request_reference_url' => $serviceRequest->service_request_reference_url,
                'status' => [
                    'label' => $this->state->label(),
                    'value' => $this->state->getValue(),
                    'color_class' => $this->state->colorClass(),
                ],
                'activity_log' => [
                    'activity_uuid' => $serviceRequestActivityLog->service_request_activity_log_uuid,
                    'event' => $serviceRequestActivityLog->event,
                    'attributes' => $eventAttributes,
                    'created_at' => $serviceRequestActivityLog->created_at->toIso8601String(),
                    'triggered_by' => $serviceRequestActivityLog->triggeredBy?->getName(),
                ],
                'abilities' => $serviceRequest->resolveStateAbilities(),
            ],
        ];
    }

    public function getServiceRequest(): ServiceRequest
    {
        if (empty($this->serviceRequest)) {
            $this->serviceRequest = ServiceRequest::with([
                'organization:organization_id,organization_uuid',
            ])
                ->select('service_request_id', 'organization_id', 'state', 'service_request_uuid', 'service_request_reference_number', 'service_request_reference_url')
                ->where('service_request_id', $this->serviceRequestId)
                ->firstOrFail();
        }

        return $this->serviceRequest;
    }

    public function getServiceRequestActivityLog(): ServiceRequestActivityLog
    {
        if (empty($this->serviceRequestActivityLog)) {
            $this->serviceRequestActivityLog = ServiceRequestActivityLog::with([
                'triggeredBy:user_id,first_name,middle_name,last_name',
            ])
                ->where('service_request_activity_log_id', $this->serviceRequestActivityLogId)
                ->select('service_request_activity_log_uuid', 'event', 'event_attributes', 'created_at', 'triggered_by')
                ->firstOrFail();
        }

        return $this->serviceRequestActivityLog;
    }
}

<?php

namespace App\Events\ServiceRequest;

use App\Http\Resources\ServiceRequest\ResidentResource;
use App\Models\Resident;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestResidentUpdated extends BaseServiceRequestEvent implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public int $serviceRequestId,
        public array $residentIds
    ) {
        parent::__construct($serviceRequestId);
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'serviceRequest.resident.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $residents = Resident::select([
            'resident_id',
            'resident_uuid',
            'first_name',
            'last_name',
            'email',
            'phone_number',
        ])
            ->whereIn('resident_id', $this->residentIds)
            ->get();
        $requestingResidentId = $this->serviceRequest?->requesting_resident_id;

        $primaryResident = $residents->where('resident_id', $requestingResidentId)->first();
        $primaryResidentId = $primaryResident?->resident_uuid;

        return [
            'data' => [
                'residents' => ResidentResource::collection($residents),
                'primary_resident_id' => $primaryResidentId,
            ],
        ];
    }
}

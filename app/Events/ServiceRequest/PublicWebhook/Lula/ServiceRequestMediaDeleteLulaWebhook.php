<?php

namespace App\Events\ServiceRequest\PublicWebhook\Lula;

use App\Events\ServiceRequest\PublicWebhook\DispatchServiceRequestWebhook;
use App\Models\Media;
use App\Models\ServiceRequest;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestMediaDeleteLulaWebhook implements DispatchServiceRequestWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public Media $media,
        public ServiceRequest $serviceRequest
    ) {}

    public function getServiceRequest(): ServiceRequest
    {
        return $this->serviceRequest;
    }

    public function prepareWebhookPayload(): array
    {
        $serviceRequest = $this->getServiceRequest();
        $serviceRequestMedia = $this->media->serviceRequestMedia;

        return [
            'data' => [
                'serviceRequestId' => $serviceRequest->service_request_uuid,
                'mediaDetails' => [
                    'id' => $this->media->media_uuid,
                    'type' => $serviceRequestMedia?->media_type,
                ],
            ],
            'eventType' => 'foresight:servicerequest.media-deleted',
        ];
    }
}

<?php

namespace App\Events\ServiceRequest;

use App\Models\ServiceRequest;
use App\Models\ServiceRequestActivityLog;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ResidentAvailabilityBroadcast implements ShouldBroadcastNow, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public ServiceRequest $serviceRequest,
        public int $serviceRequestActivityLogId,
        public string $eventType
    ) {}

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("organization.{$this->serviceRequest->organization->organization_uuid}.service-requests.{$this->serviceRequest->service_request_uuid}"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return $this->eventType;
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $serviceRequestActivityLog = ServiceRequestActivityLog::with([
            'triggeredBy:user_id,first_name,last_name,middle_name',
        ])
            ->select('service_request_activity_log_uuid', 'event', 'event_attributes', 'created_at', 'triggered_by')
            ->where('service_request_activity_log_id', $this->serviceRequestActivityLogId)
            ->firstOrFail();

        return [
            'data' => [
                'service_request_id' => $this->serviceRequest->service_request_uuid,
                'activity_log' => [
                    'activity_uuid' => $serviceRequestActivityLog->service_request_activity_log_uuid,
                    'event' => $serviceRequestActivityLog->event,
                    'attributes' => $serviceRequestActivityLog->event_attributes,
                    'created_at' => $serviceRequestActivityLog->created_at->toIso8601String(),
                    'triggered_by' => $serviceRequestActivityLog->triggeredBy?->getName(),
                ],
            ],
        ];
    }
}

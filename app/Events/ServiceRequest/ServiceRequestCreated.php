<?php

namespace App\Events\ServiceRequest;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestCreated implements ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @param  array<int>  $serviceRequestCategoryIds
     * @param  array<int|string,mixed>  $activityLogEventAttributes
     */
    public function __construct(
        public int $serviceRequestId,
        public array $serviceRequestCategoryIds = [],
        public array $activityLogEventAttributes = [],
        public ?int $userId = null,
    ) {}
}

<?php

namespace App\Events\ServiceRequest;

use App\Models\ServiceRequest;
use App\Models\ServiceRequestNote;
use App\Models\User;
use App\Notifications\ServiceRequest\NoteCreatedNotification;
use App\Services\ServiceRequestActivity\Enums\ActivityLogEventTypes;
use App\Services\Vendor\Enum\Service;
use App\Traits\BasicNotificationTrait;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Notification;
use InvalidArgumentException;

class ServiceRequestNoteCreated implements ShouldBroadcast, ShouldDispatchAfterCommit
{
    use BasicNotificationTrait, Dispatchable, InteractsWithSockets, SerializesModels;

    public ServiceRequest $serviceRequest;
    public ServiceRequestNote $serviceRequestNote;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public int $serviceRequestId,
        public int $serviceRequestNoteId,
        public ?User $user = null,
    ) {
        if ($serviceRequestId <= 0 || $serviceRequestNoteId <= 0) {
            throw new InvalidArgumentException('ServiceRequest ID and ServiceRequestNote ID must be positive integers.');
        }

        $serviceRequest = ServiceRequest::find($serviceRequestId);
        $serviceRequestNote = ServiceRequestNote::find($serviceRequestNoteId);

        if ($serviceRequest === null) {
            throw new InvalidArgumentException("ServiceRequest not found with ID: {$serviceRequestId}");
        }

        if ($serviceRequestNote === null) {
            throw new InvalidArgumentException("ServiceRequestNote not found with ID: {$serviceRequestNoteId}");
        }

        $this->serviceRequest = $serviceRequest;
        $this->serviceRequestNote = $serviceRequestNote;

        $assignees = $this->getServiceRequestAssigneesToNotify($this->serviceRequest, $user?->user_id);

        // Send notifications
        if (! empty($assignees)) {
            Notification::send($assignees, new NoteCreatedNotification($this->serviceRequest, $this->serviceRequestNote, $user));
        }
    }

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue(): string
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        $organizationUuid = $this->serviceRequest->organization->organization_uuid;
        $serviceRequestUuid = $this->serviceRequest->service_request_uuid;

        if (! $organizationUuid || ! $serviceRequestUuid) {
            throw new InvalidArgumentException('Invalid organization or service request UUID for broadcasting.');
        }

        return [
            new PrivateChannel("organization.{$organizationUuid}.service-requests.{$serviceRequestUuid}"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return ActivityLogEventTypes::SERVICE_REQUEST_NOTE_CREATED();
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->serviceRequestNote->loadMissing([
            'user:user_id,user_uuid,first_name,last_name,middle_name,profile_pic',
            'vendor:vendor_id,vendor_uuid,company_name,log_file_name,service',
        ]);

        $serviceRequestUuid = $this->serviceRequest->service_request_uuid;
        $noteUuid = $this->serviceRequestNote->service_request_note_uuid;
        $noteText = $this->serviceRequestNote->note;
        $createdAt = $this->serviceRequestNote->created_at?->toIso8601String();
        $isEditable = empty($this->serviceRequestNote->vendor_id);

        if (! $serviceRequestUuid || ! $noteUuid) {
            throw new InvalidArgumentException('Invalid service request or note UUID.');
        }

        $data = [
            'data' => [
                'service_request_id' => $serviceRequestUuid,
                'service_request_note' => [
                    'note_id' => $noteUuid,
                    'note' => $noteText,
                    'is_editable' => $isEditable,
                    'created_at' => $createdAt,
                    'created_user' => [],
                ],
            ],
        ];

        if (! empty($this->serviceRequestNote->user)) {
            $user = $this->serviceRequestNote->user;
            $data['data']['service_request_note']['created_user'] = [
                'user_id' => $user->user_uuid,
                'name' => $user->getName(),
                'profile_pic' => $user->profile_pic,
            ];
        } elseif (! empty($this->serviceRequestNote->vendor)) {
            $vendor = $this->serviceRequestNote->vendor;
            $name = $vendor->company_name;

            if ($vendor->service === Service::LULA()) {
                $name = 'Lula Support';
            }

            $data['data']['service_request_note']['created_user'] = [
                'user_id' => $vendor->vendor_uuid,
                'name' => $name,
                'profile_pic' => $vendor->log_file_name,
            ];
        }

        return $data;
    }
}

<?php

declare(strict_types=1);

namespace App\Events\ServiceRequest\WorkOrder;

use App\Http\Resources\WorkOrder\WorkOrderStatusResource;
use App\Models\WorkOrder;
use App\States\WorkOrders\AwaitingAvailability;
use App\States\WorkOrders\Canceled;
use App\States\WorkOrders\Created;
use App\States\WorkOrders\ReadyToSchedule;
use Illuminate\Broadcasting\PrivateChannel;

abstract class ServiceRequestWorkOrderBaseEvent
{
    public ?WorkOrder $workOrder;

    /**
     * @param  array<string, array<string,string>|string>  $eventAttributes  An associative array of event attributes.
     */
    public function __construct(public int $workOrderId, public ?int $userId = null, public array $eventAttributes = [], public bool $createActivityLog = true) {}

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $this->workOrder ??= $this->getWorkOrderDetails();

        $serviceRequestUuid = $this->workOrder->serviceRequest?->service_request_uuid;
        $organizationUuid = $this->workOrder->organization->organization_uuid ?? null;

        return [
            new PrivateChannel("organization.{$organizationUuid}.service-requests.{$serviceRequestUuid}"),
        ];
    }

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue(): string
    {
        return 'broadcasts';
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'serviceRequest.workOrder.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->workOrder ??= $this->getWorkOrderDetails();

        return [
            'data' => [
                'work_order_id' => $this->workOrder->work_order_uuid,
                'status' => new WorkOrderStatusResource($this->workOrder->state),
                'abilities' => $this->currentWorkOrderAbilities(),
            ],
        ];
    }

    /**
     * Get work order details with the required relationships.
     */
    public function getWorkOrderDetails(): WorkOrder
    {
        return WorkOrder::select([
            'work_order_id',
            'work_order_uuid',
            'work_order_number',
            'service_request_id',
            'organization_id',
            'state',
            'created_at',
        ])
            ->with([
                'organization:organization_id,organization_uuid',
                'serviceRequest:service_request_id,service_request_uuid',
            ])
            ->where('work_order_id', $this->workOrderId)
            ->withTrashed()
            ->firstOrFail();
    }

    /**
     * Based on the current work order state, return the abilities that can be performed on the work order.
     */
    public function currentWorkOrderAbilities(): array
    {
        return $this->workOrder->state->equals(AwaitingAvailability::class, Canceled::class, Created::class, ReadyToSchedule::class) ? ['delete'] : [];
    }
}

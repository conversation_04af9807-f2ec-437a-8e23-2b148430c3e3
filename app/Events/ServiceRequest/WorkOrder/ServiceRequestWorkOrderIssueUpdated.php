<?php

namespace App\Events\ServiceRequest\WorkOrder;

use App\Http\Resources\ServiceRequest\WorkOrder\WorkOrderStoreIssueResource;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestWorkOrderIssueUpdated extends ServiceRequestWorkOrderBaseEvent implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueueAfterCommit
{
    use Dispatchable, InteractsWithSockets, Queueable, SerializesModels;

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->workOrder ??= $this->getWorkOrderDetails();

        $this->workOrder->load([
            'workOrderIssues' => function ($query) {
                $query->with('issue:issue_id,issue_uuid,title,state')
                    ->select('work_order_id', 'issue_id', 'state');
            },
        ]);

        return [
            'data' => [
                'work_order_id' => $this->workOrder->work_order_uuid,
                'issues' => WorkOrderStoreIssueResource::collection($this->workOrder->workOrderIssues),
                'abilities' => $this->currentWorkOrderAbilities(),
            ],
        ];
    }
}

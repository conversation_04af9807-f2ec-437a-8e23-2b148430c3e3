<?php

namespace App\Events\ServiceRequest\WorkOrder;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestWorkOrderUpdated extends ServiceRequestWorkOrderBaseEvent implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueueAfterCommit
{
    use Dispatchable, InteractsWithSockets, Queueable, SerializesModels;
}

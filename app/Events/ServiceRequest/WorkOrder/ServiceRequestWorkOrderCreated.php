<?php

namespace App\Events\ServiceRequest\WorkOrder;

use App\Http\Resources\ServiceRequest\WorkOrderStoreResource;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestWorkOrderCreated extends ServiceRequestWorkOrderBaseEvent implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueueAfterCommit
{
    use Dispatchable, InteractsWithSockets, Queueable, SerializesModels;

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'serviceRequest.workOrder.created';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->workOrder ??= $this->getWorkOrderDetails();

        return [
            'data' => new WorkOrderStoreResource($this->workOrder),
        ];
    }

    /**
     * Get work order details with the required relationships.
     */
    public function getWorkOrderDetails(): WorkOrder
    {
        return WorkOrder::select([
            'work_order_id',
            'work_order_uuid',
            'work_order_number',
            'service_request_id',
            'organization_id',
            'state',
            'created_at',
        ])
            ->with([
                'organization:organization_id,organization_uuid',
                'serviceRequest:service_request_id,service_request_uuid',
                'workOrderIssues' => function ($query) {
                    $query->with('issue:issue_id,issue_uuid,title,state')
                        ->select('work_order_id', 'issue_id', 'state');
                },
            ])
            ->where('work_order_id', $this->workOrderId)
            ->firstOrFail();
    }
}

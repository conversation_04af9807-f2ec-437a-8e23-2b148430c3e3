<?php

declare(strict_types=1);

namespace App\Events\ServiceRequest;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CreateServiceRequestActivityLog implements ShouldDispatchAfterCommit, ShouldQueueAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @param array{
     *     organization_id: int,
     *     type: string,
     *     event: string,
     *     event_attributes: array<string, mixed>,
     *     service_request_id: int,
     *     work_order_id?: int,
     *     work_order_task_id?: int,
     *     triggered_by_user_id: int|null
     * } $activityLogPayload
     */
    public function __construct(public array $activityLogPayload) {}
}

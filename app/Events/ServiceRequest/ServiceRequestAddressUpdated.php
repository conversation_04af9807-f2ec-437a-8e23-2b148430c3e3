<?php

namespace App\Events\ServiceRequest;

use App\Models\Property;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestAddressUpdated extends BaseServiceRequestEvent implements ShouldBroadcast, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $this->getServiceRequestDetails();

        return [
            new PrivateChannel("organization.{$this->organizationUuid}.service-requests"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'serviceRequest.address.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $property = Property::select('property_id', 'property_uuid', 'street_address', 'unit_number', 'city', 'state_id', 'postal_zip_code')->findOrFail($this->serviceRequest->property_id);

        return [
            'data' => [
                'service_request_id' => $this->serviceRequestUuid,
                'property_id' => $property->property_uuid,
                'address' => $property->getAddress(),
                'state' => [
                    'value' => $property->state->state_uuid,
                    'label' => $property->state->name,
                ],
            ],
        ];
    }
}

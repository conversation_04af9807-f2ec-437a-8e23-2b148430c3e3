<?php

namespace App\Events\ServiceRequest;

use App\Models\ServiceRequest;
use App\Models\ServiceRequestNote;
use App\Services\ServiceRequestActivity\Enums\ActivityLogEventTypes;
use App\Services\Vendor\Enum\Service;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestNoteUpdated implements ShouldBroadcast, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public ServiceRequest $serviceRequest,
        public ServiceRequestNote $serviceRequestNote
    ) {}

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue(): string
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("organization.{$this->serviceRequest->organization->organization_uuid}.service-requests.{$this->serviceRequest->service_request_uuid}"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return ActivityLogEventTypes::SERVICE_REQUEST_NOTE_UPDATED();
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->serviceRequestNote->loadMissing([
            'user:user_id,user_uuid,first_name,last_name,middle_name,profile_pic',
            'vendor:vendor_id,vendor_uuid,company_name,log_file_name,service',
            'lastModifiedUser:user_id,user_uuid,first_name,last_name,middle_name',
        ]);

        $data = [
            'data' => [
                'service_request_id' => $this->serviceRequest->service_request_uuid,
                'service_request_note' => [
                    'note_id' => $this->serviceRequestNote->service_request_note_uuid,
                    'note' => $this->serviceRequestNote->note,
                    'created_at' => $this->serviceRequestNote->created_at?->toIso8601String(),
                    'is_editable' => empty($this->serviceRequestNote->vendor_id),
                    'modified_user' => [
                        'user_id' => $this->serviceRequestNote->lastModifiedUser?->user_uuid,
                        'name' => $this->serviceRequestNote->lastModifiedUser?->getName(),
                        'modified_at' => $this->serviceRequestNote->last_modified_at?->toIso8601String() ?? null,
                    ],
                ],
            ],
        ];

        if (! empty($this->serviceRequestNote->user)) {
            $data['data']['service_request_note']['created_user'] = [
                'user_id' => $this->serviceRequestNote->user->user_uuid,
                'name' => $this->serviceRequestNote->user->getName(),
                'profile_pic' => $this->serviceRequestNote->user->profile_pic,
            ];
        } elseif (! empty($this->serviceRequestNote->vendor)) {
            $name = $this->serviceRequestNote->vendor->company_name;
            if ($this->serviceRequestNote->vendor->service === Service::LULA()) {
                $name = 'Lula Support';
            }
            $data['data']['service_request_note']['created_user'] = [
                'user_id' => $this->serviceRequestNote->vendor->vendor_uuid,
                'name' => $name,
                'profile_pic' => $this->serviceRequestNote->vendor->log_file_name,
            ];
        }

        return $data;
    }
}

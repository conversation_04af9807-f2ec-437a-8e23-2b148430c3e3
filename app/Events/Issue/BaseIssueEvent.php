<?php

declare(strict_types=1);

namespace App\Events\Issue;

use App\Http\Resources\Issue\IssueResource;
use App\Models\Issue;
use App\States\WorkOrders\Canceled;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\PrivateChannel;

abstract class BaseIssueEvent
{
    public ?Issue $issue;

    /**
     * @param  array<string, mixed>  $eventAttributes  An associative array of event attributes.
     */
    public function __construct(public int $issueId, public ?int $userId = null, public array $eventAttributes = []) {}

    /**
     * Get issue details with the required relationships.
     */
    public function getIssueDetails(): Issue
    {
        return Issue::select(
            'issue_id', 'issue_uuid', 'service_request_id', 'problem_diagnosis_id', 'organization_id',
            'title', 'description', 'issues.state', 'created_at'
        )
            ->with([
                'organization:organization_id,organization_uuid',
                'serviceRequest:service_request_id,service_request_uuid,service_requests.state',
                'workOrderIssues:work_order_issues.work_order_id,issue_id',
                'workOrderIssues.workOrder:work_order_id,work_order_number',
                'problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label',
                'problemDiagnosis.subCategory:problem_sub_category_id,problem_sub_category_uuid,label,problem_category_id',
                'problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
                'workOrders' => function ($query) {
                    return $query->select('work_orders.work_order_id', 'work_order_uuid', 'work_order_number', 'work_orders.state')
                        ->whereNotState('work_orders.state', Canceled::class);
                },
            ])
            ->where('issue_id', $this->issueId)
            ->firstOrFail();
    }

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue(): string
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        $this->issue ??= $this->getIssueDetails();
        $organizationUuid = $this->issue->organization?->organization_uuid;
        $serviceRequestUuid = $this->issue->serviceRequest?->service_request_uuid;

        return [
            new PrivateChannel("organization.{$organizationUuid}.service-requests.{$serviceRequestUuid}"),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->issue ??= $this->getIssueDetails();

        return [
            'data' => new IssueResource($this->issue),
        ];
    }
}

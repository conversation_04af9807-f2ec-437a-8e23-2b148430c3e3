<?php

declare(strict_types=1);

namespace App\Events\Issue;

use App\Models\Issue;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ServiceRequestIssueDeleted extends BaseIssueEvent implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public ?Issue $issue;

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'serviceRequest.issue.deleted';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->issue = $this->issue ?? $this->getIssueDetails();

        return [
            'data' => [
                'issue_id' => $this->issue->issue_uuid,
            ],
        ];
    }

    public function getIssueDetails(): Issue
    {
        return Issue::withTrashed()->select(
            'issue_id',
            'issue_uuid',
            'service_request_id',
            'organization_id',
        )
            ->with([
                'organization:organization_id,organization_uuid',
                'serviceRequest:service_request_id,service_request_uuid',
            ])
            ->where('issue_id', $this->issueId)
            ->firstOrFail();
    }
}

<?php

namespace App\Events\WorkOrder;

use App\Models\Organization;
use App\Models\WorkOrder;
use App\Models\WorkOrderActivityLog;
use App\Notifications\WorkOrder\HealthScoreChangedNotification;
use App\Services\WorkOrderActivity\Enums\ActivityLogEventTypes;
use App\Traits\BasicNotificationTrait;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Notification;

class WorkOrderHealthScoreChange implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueue
{
    use BasicNotificationTrait, Dispatchable, InteractsWithSockets, Queueable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public Organization $organization,
        public WorkOrder $workOrder,
        public WorkOrderActivityLog $workOrderActivityLog
    ) {

        $assignees = $this->getAssigneesToNotify($workOrder, 0);

        if (! empty($assignees)) {

            Notification::send($assignees, new HealthScoreChangedNotification($workOrder, $workOrderActivityLog));
        }

    }

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue(): string
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("organization.{$this->organization->organization_uuid}.work-orders"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'workorder.health.healthy';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        return [
            'data' => [
                'work_order_id' => $this->workOrder->work_order_uuid,
                'activity' => [
                    'activity_uuid' => $this->workOrderActivityLog->work_order_activity_log_uuid,
                    'event' => ActivityLogEventTypes::WORK_ORDER_HEALTH_HEALTHY(),
                    'attributes' => $this->workOrderActivityLog->event_attributes,
                    'created_at' => Carbon::parse($this->workOrderActivityLog->created_at)->toIso8601String(),
                    'triggered_by' => null,
                ],
            ],
        ];
    }
}

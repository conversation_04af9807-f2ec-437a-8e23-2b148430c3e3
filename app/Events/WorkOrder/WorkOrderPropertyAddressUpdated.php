<?php

namespace App\Events\WorkOrder;

use App\Models\Property;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderPropertyAddressUpdated implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithSockets, Queueable, SerializesModels;

    public Property $property;
    public WorkOrder $workOrder;

    /**
     * Create a new event instance.
     */
    public function __construct(public int $workOrderId, public int $propertyId) {}

    /**
     * Get work order details
     */
    public function getWorkOrderDetails(): WorkOrder
    {
        return $this->workOrder ??= WorkOrder::select('organization_id', 'work_order_uuid')->findOrFail($this->workOrderId);
    }

    /**
     * Get property details
     */
    public function getPropertyDetails(): Property
    {
        return $this->property ??= Property::select('property_id', 'property_uuid', 'street_address', 'unit_number', 'city', 'state_id', 'postal_zip_code')->findOrFail($this->propertyId);
    }

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue(): string
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $this->workOrder = $this->getWorkOrderDetails();

        return [
            new PrivateChannel("organization.{$this->workOrder->organization->organization_uuid}.work-orders"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'workOrder.address.changed';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->property = $this->getPropertyDetails();
        $this->workOrder = $this->getWorkOrderDetails();

        return [
            'data' => [
                'work_order_id' => $this->workOrder->work_order_uuid,
                'property_id' => $this->property->property_uuid,
                'address' => $this->property->getAddress(),
                'state' => [
                    'value' => $this->property->state->state_uuid,
                    'label' => $this->property->state->name,
                ],
            ],
        ];
    }
}

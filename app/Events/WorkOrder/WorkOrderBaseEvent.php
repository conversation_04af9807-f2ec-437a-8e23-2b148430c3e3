<?php

declare(strict_types=1);

namespace App\Events\WorkOrder;

use App\Http\Resources\WorkOrder\WorkOrderTripResource;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\PrivateChannel;

abstract class WorkOrderBaseEvent
{
    public ?WorkOrder $workOrder;

    /**
     * @param  array<int|string,string|array<int,string>>  $activityLogEventAttributes
     */
    public function __construct(public int $workOrderId, public array $activityLogEventAttributes = [], public ?int $userId = null) {}

    /**
     * Get WorkOrder Issue details with the required relationships.
     */
    public function getWorkOrderDetails(): WorkOrder
    {
        if (empty($this->workOrder)) {
            $this->workOrder = WorkOrder::with([
                'organization:organization_id,organization_uuid',
                'latestTrips' => function ($query) {
                    $query->select(
                        'work_order_service_call_id',
                        'work_order_service_call_uuid',
                        'work_order_service_call_number',
                        'organization_id',
                        'work_order_id',
                        'technician_appointment_id',
                        'vendor_appointment_id',
                        'lula_appointment_id',
                        'scheduled_start_time',
                        'scheduled_end_time',
                        'state',
                        'state_updated_at',
                        'additional_notes',
                        'note_to_provider',
                        'status',
                        'is_active',
                        'last_modified_at',
                        'en_route_at',
                        'en_route_timer_paused_at',
                        'en_route_timer_resumed_at',
                        'drive_time_in_sec',
                        'adjusted_drive_time_in_sec',
                        'work_started_at',
                        'work_timer_paused_at',
                        'work_timer_resumed_at',
                        'work_completed_at',
                        'labor_time_in_sec',
                        'adjusted_labor_time_in_sec',
                        'created_at'
                    )
                        ->with([
                            'technicianAppointment:technician_appointment_id,technician_appointment_uuid,technician_id',
                            'technicianAppointment.technician:technician_id,technician_uuid,user_id',
                            'technicianAppointment.technician.user:user_id,first_name,last_name,middle_name,profile_pic',
                            'vendorAppointment:vendor_appointment_id,vendor_id,vendor_appointment_uuid',
                            'vendorAppointment.vendorAllocations:vendor_allocation_id,vendor_appointment_id',
                            'vendorAppointment.vendorAllocations.vendor:vendor_id,first_name,last_name,vendor_uuid',
                            'lulaAppointment:lula_appointment_id,lula_appointment_uuid',
                        ]);
                },
            ])
                ->select('work_order_id', 'organization_id', 'state', 'work_order_uuid', 'work_order_reference_number', 'work_order_reference_url')
                ->where('work_order_id', $this->workOrderId)
                ->firstOrFail();
        }

        return $this->workOrder;
    }

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue(): string
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $workOrder = $this->getWorkOrderDetails();

        return [
            new PrivateChannel("organization.{$workOrder->organization->organization_uuid}.work-orders"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'serviceRequest.workOrder.statusChanged';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $workOrder = $this->getWorkOrderDetails();

        return [
            'data' => [
                'work_order_id' => $workOrder->work_order_uuid,
                'status' => [
                    'label' => $workOrder->state->label(),
                    'value' => $workOrder->state->getValue(),
                    'color_class' => $workOrder->state->colorClass(),
                ],
                'abilities' => $workOrder->abilities(),
                'trips' => WorkOrderTripResource::collection($workOrder->latestTrips),
            ],
        ];
    }
}

<?php

namespace App\Events\WorkOrder;

use App\Http\Resources\ServiceRequest\ResidentResource;
use App\Models\Resident;
use App\Models\ServiceRequest;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ResidentUpdated implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public WorkOrder $workOrder;
    public ServiceRequest $serviceRequest;

    /** @var Collection<int, Resident> */
    public Collection $residents;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public int $serviceRequestId,
        public int $workOrderId,
        public array $residentIds
    ) {
        //
    }

    /**
     * Get work order details
     */
    public function getWorkOrderDetails(): WorkOrder
    {
        return $this->workOrder ??= WorkOrder::with([
            'organization:organization_id,organization_uuid',
        ])
            ->findOrFail($this->workOrderId);
    }

    /**
     * Get service request details
     */
    public function getServiceRequestDetails(): ServiceRequest
    {
        return $this->serviceRequest ??= ServiceRequest::select([
            'service_request_id',
            'service_request_uuid',
            'requesting_resident_id',
        ])
            ->where('service_request_id', $this->serviceRequestId)
            ->firstOrFail();
    }

    /**
     * Get resident details
     *
     * @return \Illuminate\Database\Eloquent\Collection<int, Resident>
     */
    public function getResidentDetails(): Collection
    {
        return $this->residents ??= Resident::select([
            'resident_id',
            'resident_uuid',
            'first_name',
            'last_name',
            'email',
            'phone_number',
        ])
            ->whereIn('resident_id', $this->residentIds)
            ->get();
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'workOrder.resident.updated';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $this->workOrder = $this->getWorkOrderDetails();

        return [
            new PrivateChannel("organization.{$this->workOrder->organization->organization_uuid}.work-orders.{$this->workOrder->work_order_uuid}"),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->serviceRequest = $this->getServiceRequestDetails();
        $this->residents = $this->getResidentDetails();
        $requestingResidentId = $this->serviceRequest->requesting_resident_id;

        $primaryResident = $this->residents->where('resident_id', $requestingResidentId)->first();
        $primaryResidentId = $primaryResident?->resident_uuid;

        return [
            'data' => [
                'residents' => ResidentResource::collection($this->residents),
                'primary_resident_id' => $primaryResidentId,
            ],
        ];
    }
}

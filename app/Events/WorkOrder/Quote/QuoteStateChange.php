<?php

namespace App\Events\WorkOrder\Quote;

use App\Models\WorkOrder;
use App\Models\WorkOrderActivityLog;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class QuoteStateChange implements ShouldBroadcastNow, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /** @var WorkOrder|null */
    public $workOrder;

    /** @var WorkOrderActivityLog|null */
    public $workOrderActivityLog;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public int $workOrderId,
        public int $workOrderActivityLogId
    ) {}

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $workOrder = $this->getWorkOrder();

        return [
            new PrivateChannel("organization.{$workOrder->organization->organization_uuid}.work-orders"),
            new PrivateChannel("organization.{$workOrder->organization->organization_uuid}.work-orders.{$workOrder->work_order_uuid}"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        $workOrder = $this->getWorkOrder();

        return 'workorder.task.' . $workOrder->state->getValue();
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $workOrder = $this->getWorkOrder();
        $workOrderActivityLog = $this->getWorkOrderActivityLog();

        /** @var array<int|string, mixed>>|null */
        $eventAttributes = $workOrderActivityLog->event_attributes;

        return [
            'data' => [
                'work_order_id' => $workOrder->work_order_uuid,
                'activity_log' => [
                    'activity_uuid' => $workOrderActivityLog->work_order_activity_log_uuid,
                    'event' => $workOrderActivityLog->event,
                    'attributes' => $eventAttributes,
                    'created_at' => $workOrderActivityLog->created_at?->toIso8601String(),
                    'triggered_by' => $workOrderActivityLog->triggeredBy?->getName(),
                ],
                'abilities' => $workOrder->resolveStateAbilities(),
            ],
        ];
    }

    public function getWorkOrder(): WorkOrder
    {
        if (empty($this->workOrder)) {
            $this->workOrder = WorkOrder::with([
                'organization:organization_id,organization_uuid',
                'latestInvoices:invoices.invoice_id,invoices.work_order_id,invoices.state,invoices.created_at',
                'tasks' => function ($query) {
                    return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                        ->with([
                            'latestServiceCalls' => function ($query) {
                                return $query->select(
                                    'work_order_service_calls.work_order_service_call_id',
                                    'work_order_service_calls.technician_appointment_id',
                                    'work_order_service_calls.lula_appointment_id',
                                    'work_order_service_calls.state',
                                    'work_order_service_calls.work_to_perform',
                                    'work_order_service_calls.trip_end_with',
                                    'work_order_service_calls.trip_end_with_type',
                                    'work_order_service_calls.created_at',
                                )->with([
                                    'createdQuote:quote_id,work_order_service_call_id,status',
                                    'appointment' => function ($query) {
                                        $query->select('technician_id', 'technician_appointment_id');
                                    },
                                ]);
                            },
                        ]);
                },
            ])
                ->select('work_order_id', 'organization_id', 'state', 'work_order_uuid')
                ->where('work_order_id', $this->workOrderId)
                ->firstOrFail();
        }

        return $this->workOrder;
    }

    public function getWorkOrderActivityLog(): WorkOrderActivityLog
    {
        if (empty($this->workOrderActivityLog)) {
            $this->workOrderActivityLog = WorkOrderActivityLog::with([
                'triggeredBy:user_id,first_name,middle_name,last_name',
            ])
                ->where('work_order_activity_log_id', $this->workOrderActivityLogId)
                ->select('work_order_activity_log_uuid', 'event', 'event_attributes', 'created_at', 'triggered_by')
                ->firstOrFail();
        }

        return $this->workOrderActivityLog;
    }
}

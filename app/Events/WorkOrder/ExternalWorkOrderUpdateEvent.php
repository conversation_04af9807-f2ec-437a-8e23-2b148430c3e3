<?php

namespace App\Events\WorkOrder;

use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ExternalWorkOrderUpdateEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @var WorkOrder
     */
    public $workOrder;

    /**
     * @var string
     */
    public $status;

    /**
     * @var array<string,mixed>
     */
    public $additionalData;

    /**
     * Create a new event instance.
     *
     * @param  string  $status
     * @param  array<string,mixed>  $additionalData
     */
    public function __construct(WorkOrder $workOrder, $status, $additionalData = [])
    {
        $this->workOrder = $workOrder;
        $this->status = $status;
        $this->additionalData = $additionalData;
    }
}

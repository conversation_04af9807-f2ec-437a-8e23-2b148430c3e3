<?php

namespace App\Events\WorkOrder;

use App\Models\WorkOrder;
use App\Models\WorkOrderActivityLog;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ResidentAvailabilityBroadcast implements ShouldBroadcastNow, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public WorkOrder $workOrder,
        public int $workOrderActivityLogId,
        public string $eventType
    ) {}

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("organization.{$this->workOrder->organization->organization_uuid}.work-orders.{$this->workOrder->work_order_uuid}"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return $this->eventType;
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $workOrderActivityLog = WorkOrderActivityLog::with([
            'triggeredBy:user_id,first_name,last_name,middle_name',
        ])
            ->select('work_order_activity_log_uuid', 'event', 'event_attributes', 'created_at', 'triggered_by')
            ->where('work_order_activity_log_id', $this->workOrderActivityLogId)
            ->firstOrFail();

        return [
            'data' => [
                'work_order_id' => $this->workOrder->work_order_uuid,
                'activity_log' => [
                    'activity_uuid' => $workOrderActivityLog->work_order_activity_log_uuid,
                    'event' => $workOrderActivityLog->event,
                    'attributes' => $workOrderActivityLog->event_attributes,
                    'created_at' => $workOrderActivityLog->created_at?->toIso8601String(),
                    'triggered_by' => $workOrderActivityLog->triggeredBy?->getName(),
                ],
            ],
        ];
    }
}

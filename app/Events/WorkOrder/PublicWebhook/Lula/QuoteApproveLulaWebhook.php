<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Enums\ImageConversionType;
use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\Media;
use App\Models\Quote;
use App\Models\QuoteTask;
use App\Models\QuoteTaskMaterial;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderTask;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class QuoteApproveLulaWebhook implements DispatchWorkOrderWebhook
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public WorkOrder $workOrder,
        public WorkOrderTask $workOrderTask,
        public Quote $quote,
        public ?User $user,
        public bool $preventDispatchWebhook
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    /**
     * @return array<string, mixed>
     */
    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();
        $this->quote->loadMissing(
            'quoteTasks', 'quoteTasks.media', 'quoteTasks.quoteTaskMaterials',
            'approvedUser:user_id,user_uuid,first_name,last_name,middle_name'
        );

        $quoteDetails = [
            'approverName' => $this->quote->approvedUser?->getName(),
            'status' => [
                'slug' => $this->quote->status,
                'title' => Str::title($this->quote->status),
            ],
        ];

        $quoteTasks = $this->quote->quoteTasks->map(function (QuoteTask $quoteTask) {
            return [
                'quoteTaskId' => $quoteTask->quote_task_uuid,
                'description' => $quoteTask->description,
                'costInCents' => $quoteTask->total_cost_in_cents,
                'estimatedTime' => $quoteTask->estimated_time,
                'status' => $quoteTask->status,
                'mediaDetails' => $quoteTask->media->map(function (Media $media) {
                    return [
                        'id' => $media->media_uuid,
                        'type' => $media->pivot->media_type ?? null,
                        'mimeType' => $media->mime_type,
                        'uri' => $media->getTemporaryMediaUrl(type: ImageConversionType::ORIGINAL()),
                        'thumbnailUri' => $media->getTemporaryMediaUrl(type: ImageConversionType::THUMBNAIL()),
                    ];
                })->all(),
                'quoteTaskMaterials' => collect($quoteTask->quoteTaskMaterials)->map(function (QuoteTaskMaterial $quoteTaskMaterial) {
                    return [
                        'label' => $quoteTaskMaterial->label,
                        'quantity' => $quoteTaskMaterial->quantity,
                        'quantityType' => $quoteTaskMaterial->quantity_type,
                        'costInCents' => $quoteTaskMaterial->total_cost_in_cents,
                        'unitPriceInCents' => $quoteTaskMaterial->unitPrice(),
                    ];
                })->toArray(),
            ];
        })->all();

        return [
            'data' => [
                'workOrderId' => $workOrder->work_order_uuid,
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],
                'quoteId' => $this->quote->quote_uuid,
                'quoteDetails' => $quoteDetails,
                'quoteTasks' => $quoteTasks,
            ],
            'eventType' => 'foresight:workorder.quote.approved',
        ];
    }
}

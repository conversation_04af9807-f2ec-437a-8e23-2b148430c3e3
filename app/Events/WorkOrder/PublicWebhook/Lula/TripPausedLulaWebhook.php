<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TripPausedLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    /**
     * @return array<string, mixed>
     */
    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();
        $task = $workOrder->tasks->first();
        $serviceCall = null;
        $pausedDetails = [];
        if (! empty($task)) {
            $serviceCall = $task->latestServiceCalls->first();
            if (! empty($serviceCall)) {
                $pausedDetails = [
                    // TODO: update timer_paused_at with work_timer_paused_at from technician_appointments table
                    'pausedAtUTC' => $serviceCall->timer_paused_at?->toIso8601String(),
                    'pausedReason' => $serviceCall->timer_paused_reason,
                    'status' => [
                        'label' => $serviceCall->state->label(),
                        'value' => $serviceCall->state->getValue(),
                    ],
                ];
            }
        }

        return [
            'data' => [
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],
                'workOrderId' => $workOrder->work_order_uuid,
                'pausedDetails' => $pausedDetails,
            ],
            'eventType' => 'foresight:workorder.trip.paused',
        ];
    }
}

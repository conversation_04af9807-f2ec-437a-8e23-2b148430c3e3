<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderCompleteLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    /**
     * @return array<string, mixed>
     */
    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();
        $task = $workOrder->tasks->first();
        $completedDetails = [];
        if (! empty($task)) {
            $serviceCall = $task->latestServiceCalls->first();
            if (! empty($serviceCall)) {
                $completedDetails = [
                    'workCompletedAtUTC' => $workOrder->work_completed_at?->toIso8601String(),
                    'serviceNotes' => $serviceCall->service_notes,
                ];

                $workOrderTaskMaterials = $serviceCall->workOrderTaskMaterials;

                $materials = [];
                if (! empty($workOrderTaskMaterials)) {

                    $workOrderTaskMaterials = $workOrderTaskMaterials->map(function ($workOrderTaskMaterial) {
                        return [
                            'materialId' => $workOrderTaskMaterial->work_order_task_material_uuid,
                            'quantity' => $workOrderTaskMaterial->quantity,
                            'quantityType' => $workOrderTaskMaterial->quantity_type,
                            'label' => $workOrderTaskMaterial->label,
                            'costInCents' => $workOrderTaskMaterial->cost_in_cents ?? null,
                            'unitPriceInCents' => $workOrderTaskMaterial->unitPrice(),
                        ];
                    });

                    $materials = $workOrderTaskMaterials->toArray();
                }
                $completedDetails['materials'] = $materials;
            }
        }

        return [
            'data' => [
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],
                'workOrderId' => $workOrder->work_order_uuid,
                'workOrderTaskId' => $task->work_order_task_uuid ?? null,
                'completedDetails' => $completedDetails,
            ],
            'eventType' => 'foresight:workorder.completed',
        ];
    }
}

<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Enums\ServiceCallActionTypes;
use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TripStoppedLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    /**
     * @return array<string, mixed>
     */
    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();

        $task = $workOrder->tasks->first();
        if (! empty($task)) {
            $serviceCall = $task->latestServiceCalls->first();
            if (! empty($serviceCall)) {
                $serviceCallLog = $serviceCall->latestServiceCallLogs->first();
                if (! empty($serviceCallLog) && $serviceCallLog->action === ServiceCallActionTypes::EN_ROUTE()) {
                    $tripStopAtUTC = $serviceCallLog->action_ended_at?->toIso8601String();
                }
            }
        }

        return [
            'data' => [
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],
                'workOrderId' => $workOrder->work_order_uuid,
                'tripStopDetails' => [
                    'tripStopAtUTC' => $tripStopAtUTC ?? null,
                ],
            ],
            'eventType' => 'foresight:workorder.stop-trip',
        ];
    }
}

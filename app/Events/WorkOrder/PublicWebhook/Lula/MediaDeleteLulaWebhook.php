<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Enums\MediaType;
use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\Media;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MediaDeleteLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public Media $media,
        public WorkOrder $workOrder
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();
        $workOrderMedia = $this->media->workOrderMedia;
        $quoteTaskId = null;

        if ($workOrderMedia?->media_type == MediaType::QUOTE_TASK()) {
            $quoteTaskId = $workOrderMedia?->quoteTask?->quote_task_uuid;
        }

        return [
            'data' => [
                'workOrderId' => $workOrder->work_order_uuid,
                'mediaDetails' => [
                    'id' => $this->media->media_uuid,
                    'type' => $workOrderMedia?->media_type,
                    'quoteTaskId' => $quoteTaskId,
                ],
            ],
            'eventType' => 'foresight:workorder.media-deleted',
        ];
    }
}

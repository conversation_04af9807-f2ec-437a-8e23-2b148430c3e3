<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderInProgressLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    /**
     * @return array<string, mixed>
     */
    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();
        $task = $workOrder->tasks->first();
        $technicianAppointment = null;
        if (! empty($task)) {
            $serviceCall = $task->latestServiceCalls->first();
            if (! empty($serviceCall)) {
                $technicianAppointment = $serviceCall->appointment;
            }
        }

        return [
            'data' => [
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],
                'workOrderId' => $workOrder->work_order_uuid,
                'workOrderTaskId' => $task?->work_order_task_uuid,
                'inProgressDetails' => [
                    'startAtUTC' => ! empty($technicianAppointment->actual_start_time) ? $technicianAppointment->actual_start_time->toIso8601String() : null,
                ],
            ],
            'eventType' => 'foresight:workorder.inprogress',
        ];
    }
}

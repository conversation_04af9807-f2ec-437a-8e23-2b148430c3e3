<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Enums\QuoteStatus;
use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\Quote;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderTask;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class QuoteSubmitForApprovalLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
        public WorkOrderTask $workOrderTask,
        public Quote $quote,
        public ?User $user,
        public bool $preventDispatchWebhook,
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();

        return [
            'data' => [
                'workOrderId' => $workOrder->work_order_uuid,
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],
                'quoteId' => $this->quote->quote_uuid,
                'quoteDetails' => [
                    'status' => [
                        'slug' => QuoteStatus::label($this->quote->status, 'web'),
                        'title' => $this->quote->status,
                    ],
                ],
            ],
            'eventType' => 'foresight:workorder.quote.submit-for-approval',
        ];
    }
}

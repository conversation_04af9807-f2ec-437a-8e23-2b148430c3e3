<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\Media;
use App\Models\WorkOrder;
use App\Models\WorkOrderMedia;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class QuoteTaskMediaCreatedLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
        public Media $media,
        public WorkOrderMedia $workOrderMedia,
        public string $referenceId
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();

        return [
            'data' => [
                'workOrderId' => $workOrder->work_order_uuid,
                'quoteMedia' => [
                    'mediaId' => $this->media->media_uuid,
                    'referenceId' => $this->referenceId,
                    'mediaType' => $this->workOrderMedia->media_type,
                ],
            ],
            'eventType' => 'foresight:workorder.quote.media.created',
        ];
    }
}

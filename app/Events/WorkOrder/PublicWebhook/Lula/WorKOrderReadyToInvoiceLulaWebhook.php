<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderTask;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Translation\Exception\NotFoundResourceException;

class WorKOrderReadyToInvoiceLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
        public ?User $user,
        public ?WorkOrderTask $workOrderTask = null
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    /**
     * @return array<string, mixed>
     */
    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();

        $this->workOrderTask?->load('latestServiceCalls');
        $tripDetails = $this->workOrderTask?->latestServiceCalls;

        if (empty($tripDetails)) {
            throw new NotFoundResourceException(__('Workorder service call resource not found.'));
        }
        $allTripsInfo = [];

        foreach ($tripDetails as $tripDetail) {
            $tripDetail->load('workOrderTaskMaterials');
            $workOrderTaskMaterials = $tripDetail->workOrderTaskMaterials;
            $materials = [];
            if (! empty($workOrderTaskMaterials)) {
                $workOrderTaskMaterials = $workOrderTaskMaterials->map(function ($workOrderTaskMaterial) {
                    return [
                        'materialId' => $workOrderTaskMaterial->work_order_task_material_uuid,
                        'quantity' => $workOrderTaskMaterial->quantity,
                        'quantityType' => $workOrderTaskMaterial->quantity_type,
                        'label' => $workOrderTaskMaterial->label,
                        'costInCents' => $workOrderTaskMaterial->cost_in_cents ?? null,
                        'unitPriceInCents' => $workOrderTaskMaterial->unitPrice(),
                    ];
                });

                $materials = $workOrderTaskMaterials->toArray();
            }

            $allTripsInfo[] = [
                'service_notes' => $tripDetail->service_notes,
                'status' => $tripDetail->status,
                'workOrderTaskMaterials' => $materials,
            ];
        }

        return [
            'data' => [
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],
                'tripDetails' => $allTripsInfo,
                'workOrderId' => $workOrder->work_order_uuid,
                'workOrderTaskId' => $this->workOrderTask->work_order_task_uuid ?? null,

            ],
            'eventType' => 'foresight:workorder.ready-to-invoice',
        ];
    }
}

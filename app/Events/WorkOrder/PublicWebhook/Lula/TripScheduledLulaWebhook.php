<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

//TO DO: to remove if not using
class TripScheduledLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public int $workOrderId,
        public bool $isRescheduled = false,
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        $workOrder = WorkOrder::with([
            'tasks:work_order_task_id,work_order_id',
            'organization:organization_id,organization_uuid,webhook_enabled,webhook_api_url,webhook_secret_key',
            'tasks.latestServiceCalls' => function ($query) {
                return $query->select(
                    'work_order_service_calls.work_order_service_call_id',
                    'work_order_service_calls.technician_appointment_id',
                    'work_order_service_calls.duration_minutes',
                    'work_order_service_calls.scheduled_start_time',
                    'work_order_service_calls.scheduled_end_time',
                    'work_order_service_calls.created_at',
                )->with([
                    'appointment:technician_appointment_id,rescheduled_reason,technician_appointment_uuid,technician_id',
                    'appointment.technician:technician_id,technician_uuid,user_id',
                    'appointment.technician.user:user_id,first_name,last_name,middle_name',
                ]);
            },
        ])
            ->select(
                'work_order_id', 'work_order_uuid', 'state', 'organization_id'
            )
            ->findOrFail($this->workOrderId);

        return $workOrder;
    }

    public function prepareWebhookPayload(): array
    {
        $payload = [];
        $workOrder = $this->getWorkOrder();
        $task = $workOrder->tasks->first();

        if (! empty($task)) {
            $serviceCall = $task->latestServiceCalls->first();
            if (! empty($serviceCall)) {

                $technician = ! empty($serviceCall->technician_appointment_id) ? $serviceCall->appointment?->technician : null;

                $payload = [
                    'data' => [
                        'status' => [
                            'slug' => $workOrder->state->getValue(),
                            'title' => $workOrder->state->label(),
                        ],
                        'workOrderId' => $workOrder->work_order_uuid,
                        'scheduleDetails' => [
                            'appointmentId' => $serviceCall->appointment?->technician_appointment_uuid,
                            'estimateDuration' => $serviceCall->duration_minutes,
                            'startTimeAtUTC' => $serviceCall->scheduled_start_time?->toIso8601String(),
                            'endTimeAtUTC' => $serviceCall->scheduled_end_time?->toIso8601String(),
                            'durationMinutes' => $serviceCall->duration_minutes ?? 0,
                            'technician' => ! empty($technician) ? [
                                'technicianId' => $technician->technician_uuid,
                                'name' => $technician->user?->getName(),
                            ] : null,
                        ],
                    ],
                    'eventType' => 'foresight:workorder.scheduled',
                ];

                if (! empty($serviceCall->appointment->rescheduled_reason) && $this->isRescheduled) {
                    $payload['data']['isRescheduled'] = true;
                    $payload['data']['reScheduledReason'] = $serviceCall->appointment->rescheduled_reason;
                }
            }
        }

        return $payload;
    }
}

<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderCanceledLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();

        return [
            'data' => [
                'status' => [
                    'title' => $workOrder->state->label(),
                    'slug' => $workOrder->state->getValue(),
                ],
                'workOrderId' => $workOrder->work_order_uuid,
                'cancelDetails' => [
                    'reason' => $workOrder->canceled_reason,
                    'canceledAtUTC' => $workOrder->canceled_at?->toIso8601String(),
                ],
            ],
            'eventType' => 'foresight:workorder.canceled',
        ];
    }
}

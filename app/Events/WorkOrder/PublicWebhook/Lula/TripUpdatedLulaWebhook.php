<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Enums\ImageConversionType;
use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\Media;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderTask;
use App\Models\WorkOrderTaskMaterial;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TripUpdatedLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
        public WorkOrderTask $workOrderTask,
        public WorkOrderServiceCall $workOrderServiceCall
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();

        return [
            'data' => [
                'workOrderId' => $workOrder->work_order_uuid,
                'workOrderTaskId' => $this->workOrderTask->work_order_task_uuid,
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],

                'tripDetails' => [
                    'tripId' => $this->workOrderServiceCall->work_order_service_call_uuid,
                    'serviceNotes' => $this->workOrderServiceCall->service_notes ?? null,
                    'mediaDetails' => $this->workOrderServiceCall->media->map(function (Media $media) {
                        return [
                            'id' => $media->media_uuid,
                            'type' => $media->pivot->media_type ?? null,
                            'mimeType' => $media->mime_type,
                            'uri' => $media->getTemporaryMediaUrl(type: ImageConversionType::ORIGINAL()),
                            'thumbnailUri' => $media->getTemporaryMediaUrl(type: ImageConversionType::THUMBNAIL()),
                        ];
                    })->toArray(),
                    'workOrderTaskMaterials' => $this->workOrderServiceCall->workOrderTaskMaterials->map(function (WorkOrderTaskMaterial $workOrderTaskMaterial) {
                        return [
                            'label' => $workOrderTaskMaterial->label,
                            'quantity' => $workOrderTaskMaterial->quantity,
                            'quantityType' => $workOrderTaskMaterial->quantity_type,
                            'costInCents' => $workOrderTaskMaterial->cost_in_cents,
                            'unitPriceInCents' => $workOrderTaskMaterial->unitPrice(),
                        ];
                    })->toArray(),
                ],
            ],
            'eventType' => 'foresight:workorder.trip-updated',
        ];
    }
}

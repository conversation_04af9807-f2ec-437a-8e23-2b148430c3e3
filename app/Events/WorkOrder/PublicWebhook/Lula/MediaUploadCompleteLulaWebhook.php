<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Enums\ImageConversionType;
use App\Enums\MediaType;
use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Exceptions\WorkOrderMediaException;
use App\Models\Media;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use RuntimeException;

class MediaUploadCompleteLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @var Media|null
     */
    public $media = null;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public int $mediaId
    ) {}

    public function fetchMedia(): Media
    {
        if (empty($this->media)) {
            $this->media = Media::with([
                'workOrderMedia:work_order_media_id,media_id,work_order_id,media_type,quote_task_id',
                'workOrderMedia.workOrder:work_order_id,work_order_uuid,work_order_source_id,organization_id',
                'workOrderMedia.quoteTask:quote_task_id,quote_task_uuid',
            ])
                ->select('media_id', 'media_uuid', 'mime_type', 'file_name', 'thumbnail_file_name')
                ->where('media_id', $this->mediaId)
                ->first();

            if (empty($this->media)) {
                throw new WorkOrderMediaException('Media not found');
            }
        }

        return $this->media;
    }

    public function getWorkOrder(): WorkOrder
    {
        $media = $this->fetchMedia();

        if (empty($media->workOrderMedia->workOrder)) {
            throw new RuntimeException('Empty workOrder');
        }

        return $media->workOrderMedia->workOrder;
    }

    public function prepareWebhookPayload(): array
    {
        $media = $this->fetchMedia();
        $workOrder = $this->getWorkOrder();

        $mediaDetails = [
            'id' => $media->media_uuid,
            'type' => $media->workOrderMedia?->media_type,
            'mimeType' => $media->mime_type,
            'uri' => $media->getTemporaryMediaUrl(type: ImageConversionType::ORIGINAL()),
            'thumbnailUri' => $media->getTemporaryMediaUrl(type: ImageConversionType::THUMBNAIL()),
        ];

        if ($media->workOrderMedia?->media_type == MediaType::QUOTE_TASK()) {
            $mediaDetails['quoteTaskId'] = $media->workOrderMedia?->quoteTask?->quote_task_uuid;
        }

        return [
            'data' => [
                'workOrderId' => $workOrder->work_order_uuid,
                'mediaDetails' => $mediaDetails,
            ],
            'eventType' => 'foresight:workorder.media-uploaded',
        ];
    }
}

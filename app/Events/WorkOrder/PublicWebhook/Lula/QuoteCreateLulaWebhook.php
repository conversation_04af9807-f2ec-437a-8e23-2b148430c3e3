<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Enums\Boolean;
use App\Enums\ImageConversionType;
use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\Media;
use App\Models\Quote;
use App\Models\QuoteTask;
use App\Models\QuoteTaskMaterial;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderTask;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class QuoteCreateLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @param  array<int,int>  $newlyCreatedQuoteTaskIds
     */
    public function __construct(
        public WorkOrder $workOrder,
        public WorkOrderTask $workOrderTask,
        public Quote $quote,
        public User $user,
        public array $newlyCreatedQuoteTaskIds
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    /**
     * @return array<string, mixed>
     */
    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();

        $quoteTasks = QuoteTask::with([
            'quoteTaskMaterials:quote_task_id,label,total_cost_in_cents,quantity,quantity_type,cost_type,markup_fee_in_cents,cost_in_cents',
            'media' => function ($query) {
                $query->where('has_upload_completed', Boolean::YES);
            },
        ])
            ->whereIn('quote_task_id', $this->newlyCreatedQuoteTaskIds)
            ->select('quote_task_id', 'quote_task_uuid', 'description', 'total_cost_in_cents', 'estimated_time', 'markup_fee_in_cents', 'cost_in_cents')
            ->get();

        $quoteTasks = $quoteTasks->map(function (QuoteTask $quoteTask) {
            return [
                'quoteTaskId' => $quoteTask->quote_task_uuid,
                'description' => $quoteTask->description,
                'costInCents' => $quoteTask->total_cost_in_cents,
                'estimatedTime' => $quoteTask->estimated_time,
                'laborCostInCents' => $quoteTask->cost_in_cents,
                'markupFeeInCents' => $quoteTask->markup_fee_in_cents,
                'mediaDetails' => $quoteTask->media->map(function (Media $media) {
                    return [
                        'id' => $media->media_uuid,
                        'type' => $media->pivot->media_type ?? null,
                        'mimeType' => $media->mime_type,
                        'uri' => $media->getTemporaryMediaUrl(type: ImageConversionType::ORIGINAL()),
                        'thumbnailUri' => $media->getTemporaryMediaUrl(type: ImageConversionType::THUMBNAIL()),
                    ];
                })->all(),
                'quoteTaskMaterials' => collect($quoteTask->quoteTaskMaterials)->map(function (QuoteTaskMaterial $quoteTaskMaterial) {
                    return [
                        'label' => $quoteTaskMaterial->label,
                        'quantity' => $quoteTaskMaterial->quantity,
                        'quantityType' => $quoteTaskMaterial->quantity_type,
                        'costInCents' => $quoteTaskMaterial->total_cost_in_cents,
                        'unitPriceInCents' => $quoteTaskMaterial->unitPrice(),
                        'markupFeeInCents' => $quoteTaskMaterial->markup_fee_in_cents,
                        'materialCostInCents' => $quoteTaskMaterial->cost_in_cents,
                    ];
                })->toArray(),
            ];
        })->all();

        return [
            'data' => [
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],
                'workOrderId' => $workOrder->work_order_uuid,
                'quoteId' => $this->quote->quote_uuid,
                'workOrderTaskId' => $this->workOrderTask->work_order_task_uuid ?? null,
                'quoteTasks' => $quoteTasks,
            ],
            'eventType' => 'foresight:workorder.quote.created',
        ];
    }
}

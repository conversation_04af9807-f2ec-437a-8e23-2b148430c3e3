<?php

namespace App\Events\WorkOrder\PublicWebhook\Lula;

use App\Enums\ImageConversionType;
use App\Events\WorkOrder\PublicWebhook\DispatchWorkOrderWebhook;
use App\Models\Media;
use App\Models\QuoteTask;
use App\Models\QuoteTaskMaterial;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class QuoteTaskCreatedLulaWebhook implements DispatchWorkOrderWebhook, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public WorkOrder $workOrder,
        public QuoteTask $quoteTask,
        public ?string $referenceId = null
    ) {}

    public function getWorkOrder(): WorkOrder
    {
        return $this->workOrder;
    }

    public function prepareWebhookPayload(): array
    {
        $workOrder = $this->getWorkOrder();

        $this->quoteTask->load(['media', 'quoteTaskMaterials', 'quote']);

        return [
            'data' => [
                'workOrderId' => $workOrder->work_order_uuid,
                'status' => [
                    'slug' => $workOrder->state->getValue(),
                    'title' => $workOrder->state->label(),
                ],
                'quoteId' => $this->quoteTask->quote?->quote_uuid,
                'quoteTask' => [
                    'quoteTaskId' => $this->quoteTask->quote_task_uuid,
                    'referenceId' => $this->referenceId,
                    'description' => $this->quoteTask->description,
                    'costInCents' => $this->quoteTask->total_cost_in_cents,
                    'estimatedTime' => $this->quoteTask->estimated_time,
                    'laborCostInCents' => $this->quoteTask->cost_in_cents,
                    'markupFeeInCents' => $this->quoteTask->markup_fee_in_cents,
                    'mediaDetails' => $this->quoteTask->media->map(function (Media $media) {
                        return [
                            'id' => $media->media_uuid,
                            'type' => $media->pivot->media_type ?? null,
                            'mimeType' => $media->mime_type,
                            'uri' => $media->getTemporaryMediaUrl(type: ImageConversionType::ORIGINAL()),
                            'thumbnailUri' => $media->getTemporaryMediaUrl(type: ImageConversionType::THUMBNAIL()),
                        ];
                    })->all(),
                    'quoteTaskMaterials' => collect($this->quoteTask->quoteTaskMaterials)->map(function (QuoteTaskMaterial $quoteTaskMaterial) {
                        return [
                            'label' => $quoteTaskMaterial->label,
                            'quantity' => $quoteTaskMaterial->quantity,
                            'quantityType' => $quoteTaskMaterial->quantity_type,
                            'costInCents' => $quoteTaskMaterial->total_cost_in_cents,
                            'unitPriceInCents' => $quoteTaskMaterial->unitPrice(),
                            'markupFeeInCents' => $quoteTaskMaterial->markup_fee_in_cents,
                            'materialCostInCents' => $quoteTaskMaterial->cost_in_cents,
                        ];
                    }),
                ],
            ],
            'eventType' => 'foresight:workorder.quote.task.created',
        ];
    }
}

<?php

namespace App\Events\WorkOrder\Actions;

use App\Events\WorkOrder\WorkOrderBaseEvent;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderAwaitingAvailability extends WorkOrderBaseEvent implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueueAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'workOrder.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $workOrder = $this->getWorkOrderDetails();

        return [
            'data' => [
                'work_order_id' => $workOrder->work_order_uuid,
                'status' => [
                    'label' => $workOrder->state->label(),
                    'value' => $workOrder->state->getValue(),
                    'color_class' => $workOrder->state->colorClass(),
                ],
                'abilities' => $workOrder->abilities(),
            ],
        ];
    }
}

<?php

namespace App\Events\WorkOrder\Actions;

use App\Events\WorkOrder\WorkOrderBaseEvent;
use App\Http\Resources\WorkOrder\PropertyResource;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderAccessInfoUpdated extends WorkOrderBaseEvent implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueueAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'workOrder.updated';
    }

    /**
     * Get WorkOrder Issue details with the required relationships.
     */
    public function getWorkOrderDetails(): WorkOrder
    {
        if (empty($this->workOrder)) {
            $this->workOrder = WorkOrder::with([
                'organization:organization_id,organization_uuid',
                'serviceRequest:service_request_id,property_access_method,property_access_code,property_access_note,property_id',
                'serviceRequest.property:property_id,property_uuid,property_name,full_address,latitude,longitude,state_id,street_address,city,unit_number,postal_zip_code',
                'serviceRequest.property.state:state_id,state_uuid,name,state_code',
                'serviceRequest.resident:resident_id,resident_uuid,first_name,last_name,phone_number',
                'serviceRequest.property.residents:resident_id,resident_uuid,property_id,first_name,last_name,phone_number,email',
            ])
                ->select('work_order_id', 'work_order_uuid', 'organization_id', 'service_request_id')
                ->where('work_order_id', $this->workOrderId)
                ->firstOrFail();
        }

        return $this->workOrder;
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $workOrder = $this->getWorkOrderDetails();

        $propertyAccessInfo = [
            'access_method' => $workOrder->serviceRequest->property_access_method ?? null,
            'access_code' => $workOrder->serviceRequest->property_access_code ?? null,
            'access_note' => $workOrder->serviceRequest->property_access_note ?? null,
        ];

        return [
            'data' => [
                'work_order_id' => $workOrder->work_order_uuid,
                'property' => $workOrder->serviceRequest && $workOrder->serviceRequest->property
                    ? new PropertyResource($workOrder->serviceRequest->property, $propertyAccessInfo)
                : null,
            ],
        ];
    }
}

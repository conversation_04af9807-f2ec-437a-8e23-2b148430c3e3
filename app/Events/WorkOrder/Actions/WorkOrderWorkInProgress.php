<?php

namespace App\Events\WorkOrder\Actions;

use App\Events\WorkOrder\WorkOrderBaseEvent;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderWorkInProgress extends WorkOrderBaseEvent implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueueAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'workOrder.updated';
    }

    /**
     * Get WorkOrder Issue details with the required relationships.
     */
    public function getWorkOrderDetails(): WorkOrder
    {
        if (empty($this->workOrder)) {
            $this->workOrder = WorkOrder::with([
                'organization:organization_id,organization_uuid',
            ])
                ->select('work_order_id', 'organization_id', 'nte_amount_in_cents', 'work_order_uuid')
                ->where('work_order_id', $this->workOrderId)
                ->firstOrFail();
        }

        return $this->workOrder;
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $workOrder = $this->getWorkOrderDetails();

        return [
            'data' => [
                'work_order_id' => $workOrder->work_order_uuid,
                'should_fetch' => true,
            ],
        ];
    }
}

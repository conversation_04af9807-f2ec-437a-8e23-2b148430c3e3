<?php

namespace App\Events\WorkOrder;

use App\Http\Resources\Tag\TagResource;
use App\Models\Tag;
use App\Models\WorkOrder;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderTagUpdated implements ShouldBroadcast, ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @param  Collection<int,Tag>  $tags
     */
    public function __construct(
        public WorkOrder $workOrder,
        public string $action,
        public Collection $tags
    ) {}

    /**
     * The name of the queue on which to place the broadcasting job.
     *
     * @return string
     */
    public function broadcastQueue()
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("organization.{$this->workOrder->organization->organization_uuid}.work-orders"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'workorder.tags.applied';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->workOrder->load(['tags' => function ($query) {
            $query->withCount('workOrders');
        }]);

        $data = [
            'work_order_id' => $this->workOrder->work_order_uuid,
            'tags' => TagResource::collection($this->workOrder->tags),
            'action' => $this->action,
        ];

        if ($this->action == 'detach') {
            $this->tags->loadCount('workOrders');

            $data['detached_tags'] = TagResource::collection($this->tags);
        }

        return ['data' => $data];
    }
}

<?php

namespace App\Events\WorkOrder;

use App\Models\User;
use App\Models\WorkOrder;
use App\Notifications\WorkOrder\ServiceCallAssignedNotification;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderServiceCallAssigned implements ShouldDispatchAfterCommit, ShouldQueue, ShouldQueueAfterCommit
{
    use Dispatchable, InteractsWithSockets, Queueable, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(public WorkOrder $workOrder, public User $user, public User $assignedByUser)
    {
        $this->user->notify(new ServiceCallAssignedNotification($this->workOrder, $this->assignedByUser));
    }
}

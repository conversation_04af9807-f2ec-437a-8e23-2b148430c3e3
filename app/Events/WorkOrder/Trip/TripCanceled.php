<?php

namespace App\Events\WorkOrder\Trip;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TripCanceled implements ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @param  array<int|string,mixed>  $activityLogEventAttributes
     */
    public function __construct(
        public int $workOrderId,
        public int $taskId,
        public array $activityLogEventAttributes = [],
        public bool $triggerWOUpdateEvent = true,
        public ?int $userId = null,
    ) {}
}

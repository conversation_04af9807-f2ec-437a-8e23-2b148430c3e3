<?php

namespace App\Events\WorkOrder\Trip;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TripEnRouteStart extends TripBaseEvent implements ShouldDispatchAfterCommit, ShouldQueueAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @param  array<int|string, array<int|string>>  $eventAttributes
     */
    public function __construct(public int $tripId, public ?int $userId = null, public array $eventAttributes = [], public bool $sendResidentNotification = false) {}
}

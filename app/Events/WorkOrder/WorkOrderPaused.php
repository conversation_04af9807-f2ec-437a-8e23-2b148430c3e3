<?php

namespace App\Events\WorkOrder;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderPaused implements ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @param  array<int|string,mixed>  $activityLogEventAttributes
     */
    public function __construct(
        public int $workOrderId,
        public int $taskId,
        public array $activityLogEventAttributes = [],
        public ?int $userId = null,
    ) {}
}

<?php

namespace App\Events\WorkOrder\Note;

use App\Services\WorkOrderActivity\Enums\ActivityLogEventTypes;
use App\Traits\BasicNotificationTrait;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderNoteCreated extends WorkOrderNoteBaseEvent implements ShouldBroadcast, ShouldDispatchAfterCommit, ShouldQueue
{
    use BasicNotificationTrait, Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return ActivityLogEventTypes::WORK_ORDER_NOTE_CREATED();
    }
}

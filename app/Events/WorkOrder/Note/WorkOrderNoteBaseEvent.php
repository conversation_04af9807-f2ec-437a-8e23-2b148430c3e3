<?php

declare(strict_types=1);

namespace App\Events\WorkOrder\Note;

use App\Http\Resources\WorkOrder\Note\NoteListResource;
use App\Models\WorkOrderNote;
use App\Services\WorkOrderActivity\Enums\ActivityLogEventTypes;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\PrivateChannel;

abstract class WorkOrderNoteBaseEvent
{
    public ?WorkOrderNote $workOrderNote;

    public function __construct(public int $workOrderNoteId, public ?int $userId = null) {}

    /**
     * Get WorkOrder Issue details with the required relationships.
     */
    public function getWorkOrderNoteDetails(): WorkOrderNote
    {
        if (empty($this->workOrderNote)) {
            $this->workOrderNote = WorkOrderNote::select(
                'work_order_note_id',
                'work_order_note_uuid',
                'organization_id',
                'work_order_id',
                'user_id',
                'vendor_id',
                'note_type',
                'note',
                'last_modified_at',
                'last_modified_user_id',
                'created_at',
                'updated_at',
            )->with([
                'workOrder:work_order_id,work_order_uuid',
                'organization:organization_id,organization_uuid',
                'user:user_id,user_uuid,first_name,last_name,middle_name,profile_pic',
                'vendor:vendor_id,vendor_uuid,company_name,log_file_name,service',
                'lastModifiedUser:user_id,user_uuid,first_name,last_name,middle_name',
            ])
                ->where('work_order_note_id', $this->workOrderNoteId)
                ->firstOrFail();
        }

        return $this->workOrderNote;
    }

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue(): string
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        $workOrderNote = $this->getWorkOrderNoteDetails();
        $workOrder = $workOrderNote->workOrder;
        $organization = $workOrderNote->organization;

        return [
            new PrivateChannel("organization.{$organization?->organization_uuid}.work-orders.{$workOrder?->work_order_uuid}"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return ActivityLogEventTypes::WORK_ORDER_NOTE_UPDATED();
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $workOrderNote = $this->getWorkOrderNoteDetails();

        return [
            'data' => [
                'work_order_id' => $workOrderNote->workOrder?->work_order_uuid,
                'work_order_note' => new NoteListResource($workOrderNote),
            ],
        ];
    }
}

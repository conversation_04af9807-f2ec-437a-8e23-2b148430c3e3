<?php

declare(strict_types=1);

namespace App\Events\WorkOrder\ActivityLog;

use App\Http\Resources\WorkOrder\MediaResource;
use App\Models\WorkOrderActivityLog;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\PrivateChannel;

abstract class BaseIssueEvent
{
    public ?WorkOrderActivityLog $workOrderActivityLog;

    public function __construct(public int $workOrderActivityLogId) {}

    public function getWorkOrderActivityLog(): WorkOrderActivityLog
    {
        if (empty($this->workOrderActivityLog)) {
            $this->workOrderActivityLog = WorkOrderActivityLog::with([
                'triggeredBy:user_id,first_name,middle_name,last_name',
                'organization:organization_id,organization_uuid',
                'workOrder:work_order_id,work_order_uuid',
            ])
                ->where('work_order_activity_log_id', $this->workOrderActivityLogId)
                ->select('work_order_activity_log_id', 'work_order_activity_log_uuid', 'event', 'event_attributes', 'created_at', 'triggered_by', 'organization_id', 'work_order_id')
                ->firstOrFail();
        }

        return $this->workOrderActivityLog;
    }

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue(): string
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        $workOrderActivityLog = $this->getWorkOrderActivityLog();
        $organizationUuid = $workOrderActivityLog->organization->organization_uuid;
        $workOrderUuid = $workOrderActivityLog->workOrder?->work_order_uuid;

        return [
            new PrivateChannel("organization.{$organizationUuid}.work-orders.{$workOrderUuid}"),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $workOrderActivityLog = $this->getWorkOrderActivityLog();
        $workOrder = $workOrderActivityLog->workOrder ?? null;

        /** @var array<int|string, mixed>>|null */
        $eventAttributes = $workOrderActivityLog->event_attributes;

        if (! empty($eventAttributes['medias'])) {
            $mediaIds = $eventAttributes['medias'];

            $workOrder->load(['media' => function ($query) use ($mediaIds) {
                return $query->whereUuid($mediaIds)
                    ->select(
                        'media.media_id', 'media.media_uuid', 'media.file_name', 'media.mime_type', 'media.thumbnail_file_name', 'media.optimized_file_name'
                    )
                    ->withTrashed();
            }]);
            // TODO Set work order relation with media instead of loading work order.
            $media = $workOrder->media;

            if ($media->isNotEmpty()) {
                // Replace media ids to media urls
                $eventAttributes['medias'] = MediaResource::collection($media);
            }
        }

        return [
            'data' => [
                'work_order_id' => $workOrder?->work_order_uuid,
                'activity_log' => [
                    'activity_uuid' => $workOrderActivityLog->work_order_activity_log_uuid,
                    'event' => $workOrderActivityLog->event,
                    'attributes' => $eventAttributes,
                    'created_at' => $workOrderActivityLog->created_at?->toIso8601String(),
                    'triggered_by' => $workOrderActivityLog->triggeredBy?->getName(),
                ],
            ],
        ];
    }
}

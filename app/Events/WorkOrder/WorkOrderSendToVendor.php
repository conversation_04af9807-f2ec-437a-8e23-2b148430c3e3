<?php

namespace App\Events\WorkOrder;

use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkOrderSendToVendor implements ShouldDispatchAfterCommit
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * @param  array<int,int>  $vendorIds
     */
    public function __construct(public int $workOrderId, public array $vendorIds, public User $user) {}

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): void {}

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): void {}
}

<?php

declare(strict_types=1);

namespace App\Events\WorkOrder\Issue;

use App\Http\Resources\WorkOrderIssue\WorkOrderIssueResource;
use App\Models\WorkOrderIssue;
use App\States\WorkOrders\Canceled;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\PrivateChannel;

abstract class WorkOrderIssueBaseEvent
{
    public ?WorkOrderIssue $workOrderIssue;

    public function __construct(public int $workOrderIssueId, public bool $createActivityLog = false) {}

    /**
     * Get WorkOrder Issue details with the required relationships.
     */
    public function getWorkOrderIssueDetails(): WorkOrderIssue
    {
        return WorkOrderIssue::select('work_order_issue_id', 'work_order_issue_uuid', 'work_order_id', 'issue_id', 'organization_id', 'state')
            ->with([
                'issue:issue_id,issue_uuid,title,description,problem_diagnosis_id,state',
                'issue.problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,label,problem_sub_category_id',
                'issue.problemDiagnosis.subCategory:problem_sub_category_id,problem_sub_category_uuid,label,problem_category_id',
                'issue.problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
                'issue.workOrders' => function ($query) {
                    $query->with('latestTrips:work_order_service_call_id,work_order_service_call_uuid,work_order_service_call_number,work_order_id,state')
                        ->select('work_orders.work_order_id', 'work_order_uuid', 'work_order_number', 'work_orders.state')
                        ->withCount('issues')
                        ->whereNotState('work_orders.state', Canceled::class);
                },
                'organization:organization_id,organization_uuid',
                'workOrder' => function ($query) {
                    $query->select('work_order_id', 'work_order_uuid', 'state')
                        ->withTrashed();
                },
            ])
            ->withTrashed()
            ->where('work_order_issue_id', $this->workOrderIssueId)
            ->firstOrFail();
    }

    /**
     * The name of the queue on which to place the broadcasting job.
     */
    public function broadcastQueue(): string
    {
        return 'broadcasts';
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, Channel>
     */
    public function broadcastOn(): array
    {
        $this->workOrderIssue ??= $this->getWorkOrderIssueDetails();
        $organizationUuid = $this->workOrderIssue->organization->organization_uuid;
        $workOrderUuid = $this->workOrderIssue->workOrder->work_order_uuid;

        return [
            new PrivateChannel("organization.{$organizationUuid}.work-orders.{$workOrderUuid}"),
        ];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'workOrder.issue.updated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array<string, mixed>
     */
    public function broadcastWith(): array
    {
        $this->workOrderIssue ??= $this->getWorkOrderIssueDetails();

        return [
            'data' => new WorkOrderIssueResource($this->workOrderIssue),
        ];
    }
}

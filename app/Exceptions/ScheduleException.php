<?php

namespace App\Exceptions;

use Exception;

class ScheduleException extends Exception
{
    public static function problemDiagnosisNotMatch(): ScheduleException
    {
        return new self(__('Invalid technician: problem diagnosis not matched'));
    }

    public static function windowNotFound(string $message = 'The chosen time slot is unavailable for scheduling. Please select a different time slot'): ScheduleException
    {
        return new self(__($message));
    }

    public static function noSkill(string $message = 'Technician is not capable for this task: no skill found'): ScheduleException
    {
        return new self(__($message));
    }

    public static function noWorkingHours(string $message = 'The technician is unavailable during this day'): ScheduleException
    {
        return new self(__($message));
    }
}

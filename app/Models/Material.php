<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class Material
 *
 * @property int $material_id
 * @property string $material_uuid
 * @property string $label
 * @property int $quantity
 * @property enum-string $quantity_type
 * @property int|null $cost_in_cents
 * @property enum-string|null $cost_type
 * @property int|null $unit_price_in_cents
 * @property int|null $total_cost_in_cents
 * @property int|null $cost_in_cents
 * @property enum-string|null $markup_fee_type
 * @property int|null $markup_fee_type_value
 * @property int|null $markup_fee_in_cents
 * @property \Carbon\CarbonImmutable $created_at
 * @property \Carbon\CarbonImmutable $updated_at
 * @property \Carbon\CarbonImmutable|null $deleted_at
 *
 * @mixin \Eloquent
 */
class Material extends Model
{
    use BindsOnUuid, GeneratesUuid, HasFactory, SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'material_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'material_id',
        'material_uuid',
        'label',
        'quantity',
        'quantity_type',
        'cost_in_cents',
        'cost_type',
        'unit_price_in_cents',
        'total_cost_in_cents',
        'cost_in_cents',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'material_uuid' => UuidBinary::class,
    ];

    /**
     * Get the issues associated to work order
     *
     * @return BelongsToMany<Issue>
     */
    public function workOrderIssues()
    {
        return $this->belongsToMany(WorkOrderIssueMaterial::class, 'work_order_issue_material_id', 'material_id', 'work_order_issue_id')->wherePivotNull('deleted_at');
    }
}

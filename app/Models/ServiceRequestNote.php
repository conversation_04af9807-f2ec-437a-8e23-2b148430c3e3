<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * App\Models\ServiceRequestNote
 *
 * @property int $service_request_note_id
 * @property string $service_request_note_uuid
 * @property int $service_request_status_id
 * @property int|null $user_id
 * @property int|null $vendor_id
 * @property string $note_type
 * @property string $note
 * @property \Carbon\Carbon|null $last_modified_at
 * @property int|null $last_modified_user_id
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property string|null $delete
 * @property-read User $user
 * @property Carbon|null $deleted_at
 *
 * @method static Builder|ServiceRequestNote newModelQuery()
 * @method static Builder|ServiceRequestNote newQuery()
 * @method static Builder|ServiceRequestNote onlyTrashed()
 * @method static Builder|ServiceRequestNote orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static Builder|ServiceRequestNote orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static Builder|ServiceRequestNote query()
 * @method static Builder|ServiceRequestNote whereCreatedAt($value)
 * @method static Builder|ServiceRequestNote whereDeletedAt($value)
 * @method static Builder|ServiceRequestNote whereLastModifiedAt($value)
 * @method static Builder|ServiceRequestNote whereLastModifiedUserId($value)
 * @method static Builder|ServiceRequestNote whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static Builder|ServiceRequestNote whereNote($value)
 * @method static Builder|ServiceRequestNote whereNoteType($value)
 * @method static Builder|ServiceRequestNote whereOrganizationId($value)
 * @method static Builder|ServiceRequestNote whereUpdatedAt($value)
 * @method static Builder|ServiceRequestNote whereUserId($value)
 * @method static Builder|ServiceRequestNote whereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static Builder|ServiceRequestNote whereWorkOrderId($value)
 * @method static Builder|ServiceRequestNote whereServiceRequestNoteId($value)
 * @method static Builder|ServiceRequestNote whereServiceRequestNoteUuid($value)
 * @method static Builder|ServiceRequestNote whereWorkOrderStatusId($value)
 * @method static Builder|ServiceRequestNote whereWorkOrderTaskId($value)
 * @method static Builder|ServiceRequestNote withTrashed()
 * @method static Builder|ServiceRequestNote withoutTrashed()
 *
 * @mixin \Eloquent
 */
class ServiceRequestNote extends Model
{
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'service_request_note_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'organization_id',
        'service_request_id',
        'service_request_status_id',
        'user_id',
        'vendor_id',
        'note_type',
        'note',
        'last_modified_at',
        'last_modified_user_id',
    ];

    protected $casts = [
        'service_request_note_uuid' => UuidBinary::class,
        'last_modified_at' => 'datetime',
    ];

    /**
     * @return BelongsTo<ServiceRequest, self>
     */
    public function serviceRequest(): BelongsTo
    {
        return $this->belongsTo(ServiceRequest::class, 'service_request_id');
    }

    /**
     * @return BelongsTo<User, self>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id')->withTrashed();
    }

    /**
     * @return BelongsTo<Vendor, self>
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class, 'vendor_id', 'vendor_id')->withTrashed();
    }

    /**
     * @return BelongsTo<User, self>
     */
    public function lastModifiedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'last_modified_user_id', 'user_id')->withTrashed();
    }
}

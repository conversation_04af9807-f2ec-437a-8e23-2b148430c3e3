<?php

namespace App\Models;

use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\PropertyOwner
 *
 * @property int $property_owner_id
 * @property int $organization_id
 * @property int $property_id
 * @property int $asset_owner_id
 * @property string $ownership_start_date
 * @property string|null $ownership_end_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner query()
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner whereAssetOwnerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner whereOwnershipEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner whereOwnershipStartDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner wherePropertyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner wherePropertyOwnerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner whereUuid($uuid, $uuidColumn = null)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner withoutTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|PropertyOwner whereNotUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @mixin \Eloquent
 */
class PropertyOwner extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'property_owner_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'organization_id',
        'property_id',
        'asset_owner_id',
        'ownership_start_date',
        'ownership_end_date',
    ];
}

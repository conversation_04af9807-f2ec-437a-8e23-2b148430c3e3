<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Helpers\Helper;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Database\Factories\ResidentFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;

/**
 * App\Models\Resident
 *
 * @property int $resident_id
 * @property string|null $resident_uuid
 * @property int $organization_id
 * @property string $first_name
 * @property string $last_name
 * @property string $email
 * @property string $phone_number
 * @property string|null $company_name
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read string $name
 * @property-read Organization $organization
 *
 * @method static \Database\Factories\ResidentFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Resident newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Resident newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Resident onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Resident query()
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident wherePhoneNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereResidentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereResidentUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereUuid($uuid, $uuidColumn = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Resident withoutTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Resident orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Resident whereNotUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @property int $property_id
 * @property-read Property $property
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Resident wherePropertyId($value)
 *
 * @mixin \Eloquent
 */
class Resident extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;

    /** @use HasFactory<ResidentFactory> */
    use HasFactory;
    use Notifiable;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'resident_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'resident_uuid',
        'organization_id',
        'property_id',
        'first_name',
        'last_name',
        'email',
        'phone_number',
        'company_name',
    ];

    protected $casts = [
        'resident_uuid' => UuidBinary::class,
    ];

    public function getNameAttribute(): string
    {
        return $this->getName();
    }

    public function getName(): string
    {
        return trim("{$this->first_name} {$this->last_name}");
    }

    /**
     * @return BelongsTo<Property, $this>
     */
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class, 'property_id');
    }

    public function routeNotificationForTwilio(): string
    {
        return Helper::resolvePhoneNumberForTwilio($this->phone_number ?? '');
    }
}

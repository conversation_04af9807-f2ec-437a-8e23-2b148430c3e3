<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Enums\Boolean;
use App\Enums\QuoteStatus;
use App\Enums\Trip;
use App\Enums\WorkToPerformTypes;
use App\Helpers\Helper;
use App\States\Invoices\Paid;
use App\States\Invoices\PartiallyPaid as InvoicesPartiallyPaid;
use App\States\Invoices\PaymentPending;
use App\States\Invoices\Voided;
use App\States\ServiceCalls\Ended;
use App\States\ServiceCalls\EnRoute;
use App\States\ServiceCalls\Paused as PauseTrip;
use App\States\ServiceCalls\Working;
use App\States\WorkOrders\AwaitingAvailability;
use App\States\WorkOrders\Canceled;
use App\States\WorkOrders\ClaimPending;
use App\States\WorkOrders\Completed;
use App\States\WorkOrders\Created;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\QualityCheck;
use App\States\WorkOrders\ReadyToInvoice;
use App\States\WorkOrders\ReadyToSchedule;
use App\States\WorkOrders\Scheduled;
use App\States\WorkOrders\WorkInProgress;
use App\States\WorkOrders\WorkOrderState;
use App\Traits\AbilityResolverTrait;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\Filterable;
use App\Traits\GeneratesUuid;
use Carbon\CarbonImmutable;
use Database\Factories\WorkOrderFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\ModelStates\HasStates;

/**
 * App\Models\WorkOrder
 *
 * @property int $work_order_id
 * @property string $work_order_uuid
 * @property int $organization_id
 * @property int $property_id
 * @property int $user_id
 * @property int|null $requesting_resident_id
 * @property int|null $property_manager_id
 * @property int|null $vendor_id
 * @property string|null $vendor_work_order_id
 * @property int $work_order_status_id
 * @property int $service_request_id
 * @property int $timezone_id
 * @property string $description
 * @property string|null $paused_reason
 * @property string|null $canceled_reason
 * @property string $priority
 * @property WorkOrderState $state
 * @property string|null $property_access_method
 * @property string|null $property_access_code
 * @property string|null $property_access_note
 * @property CarbonImmutable|null $due_date
 * @property CarbonImmutable|null $work_completed_at
 * @property CarbonImmutable|null $resolved_at
 * @property CarbonImmutable|null $canceled_at
 * @property CarbonImmutable|null $paused_at
 * @property CarbonImmutable|null $state_updated_at
 * @property string $work_order_number
 * @property int $nte_amount_in_cents
 * @property int|null $availability_requested_user_id
 * @property int|null $availability_viewed_user_id
 * @property CarbonImmutable|null $availability_requested_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read Organization $organization
 * @property-read Property $property
 * @property-read Resident|null $resident
 * @property-read ServiceCategory $serviceCategory
 * @property-read WorkOrderStatus|null $status

 * @property-read Vendor|null $vendor
 *
 * @method static \Database\Factories\WorkOrderFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder filter(\App\Http\Filters\QueryFilter $filter)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereCanceledAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder wherePriority($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder wherePropertyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder wherePropertyManagerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereRequestingResidentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereScheduledEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereScheduledStart($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereServiceCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereUuid($uuid, $uuidColumn = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereVendorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereWoStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereWorkCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereWorkOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereWorkOrderUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder withoutTrashed()
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Media> $media
 * @property-read int|null $media_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereDueDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereWorkOrderNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereWorkOrderStatusId($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\WorkOrderTask> $tasks
 * @property-read int|null $tasks_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder wherePropertyAccessCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder wherePropertyAccessMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder wherePropertyAccessNote($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\WorkOrderHealthLogs> $healthLogs
 * @property int|null $work_order_source_id
 * @property-read Timezone $timezone
 * @property-read WorkOrderSource|null $workOrderSource
 * @property-read ServiceRequest|null $serviceRequest
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder orWhereNotState(string $column, $states)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder orWhereState(string $column, $states)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereNotState(string $column, $states)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereTimezoneId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrder whereWorkOrderSourceId($value)
 *
 * @mixin \Eloquent
 */
class WorkOrder extends Model
{
    use AbilityResolverTrait;
    use BelongsToOrganization;
    use BindsOnUuid;
    use Filterable;
    use GeneratesUuid;

    /** @use HasFactory<WorkOrderFactory> */
    use HasFactory;
    use HasStates;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'work_order_uuid',
        'organization_id',
        'property_id',
        'user_id',
        'requesting_resident_id',
        'property_manager_id',
        'work_order_status_id',
        'state',
        'description',
        'priority',
        'property_access_method',
        'property_access_code',
        'property_access_note',
        'due_date',
        'work_completed_at',
        'resolved_at',
        'canceled_at',
        'work_order_number',
        'timezone_id',
        'service_request_id',
        'work_order_source_id',
        'work_order_reference_id',
        'work_order_reference_url',
        'work_order_reference_number',
        'work_order_reference_created_at',
        'paused_reason',
        'canceled_reason',
        'paused_at',
        'vendor_id',
        'vendor_work_order_id',
        'nte_amount_in_cents',
    ];

    protected $casts = [
        'work_order_uuid' => UuidBinary::class,
        'state' => WorkOrderState::class,
        'work_completed_at' => 'immutable_datetime',
        'work_order_reference_created_at' => 'immutable_datetime',
        'resolved_at' => 'immutable_datetime',
        'canceled_at' => 'immutable_datetime',
        'paused_at' => 'immutable_datetime',
        'state_updated_at' => 'immutable_datetime',
        'due_date' => 'immutable_datetime',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::created(function (WorkOrder $workOrder) {
            if (! $workOrder->work_order_number) {
                $workOrder->work_order_number = Helper::generateRandomAlphaNumber('work_orders', 'work_order_number');
            }
            $workOrder->save();
        });
    }

    public function uuidColumn(): string
    {
        return 'work_order_uuid';
    }

    /**
     * @return BelongsTo<Organization, $this>
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    /**
     * @return BelongsTo<WorkOrderStatus, $this>
     */
    public function status(): BelongsTo
    {
        return $this->belongsTo(WorkOrderStatus::class, 'work_order_status_id');
    }

    /**
     * @return BelongsTo<Property, $this>
     */
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class, 'property_id');
    }

    /**
     * @return BelongsTo<Resident, $this>
     */
    public function resident(): BelongsTo
    {
        return $this->belongsTo(Resident::class, 'requesting_resident_id');
    }

    /**
     * @return BelongsToMany<Media, $this>
     */
    public function media(): BelongsToMany
    {
        return $this->belongsToMany(Media::class, 'work_order_media', 'work_order_id', 'media_id')
            ->withPivot('media_type', 'has_thumbnail', 'has_upload_completed');
    }

    /**
     * @return HasMany<WorkOrderTask, $this>
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(WorkOrderTask::class, 'work_order_id', 'work_order_id');
    }

    /**
     * @return HasMany<WorkOrderHealthLog, $this>
     */
    public function healthLogs(): HasMany
    {
        return $this->hasMany(WorkOrderHealthLog::class, 'work_order_id', 'work_order_id');
    }

    /**
     * @return BelongsTo<WorkOrderSource, $this>
     */
    public function workOrderSource(): BelongsTo
    {
        return $this->belongsTo(WorkOrderSource::class, 'work_order_source_id');
    }

    /**
     * @return BelongsTo<ServiceRequest, self>
     */
    public function serviceRequest(): BelongsTo
    {
        return $this->belongsTo(ServiceRequest::class, 'service_request_id');
    }

    /**
     * @return BelongsTo<Timezone, self>
     */
    public function timezone(): BelongsTo
    {
        return $this->belongsTo(Timezone::class, 'timezone_id');
    }

    /**
     * @return HasMany<WorkOrderAssignee, $this>
     */
    public function assignees(): HasMany
    {
        return $this->hasMany(WorkOrderAssignee::class, 'work_order_id', 'work_order_id');
    }

    /**
     * @return BelongsToMany<Tag, $this>
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'work_order_tags', 'work_order_id', 'tag_id')->orderByPivot('work_order_tag_id');
    }

    /**
     * @return HasMany<Invoice, $this>
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'work_order_id', 'work_order_id');
    }

    /**
     * @return HasMany<Invoice, $this>
     */
    public function latestInvoices(): HasMany
    {
        return $this->invoices()->latest();
    }

    /**
     * @return BelongsTo<Vendor, $this>
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }

    /**
     * @return BelongsTo<User,self>
     */
    public function createdUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the work order status that owns the work order.
     *
     * @return BelongsTo<WorkOrderStatus, self>
     */
    public function workOrderStatus(): BelongsTo
    {
        return $this->belongsTo(WorkOrderStatus::class, 'work_order_status_id');
    }

    /**
     * Get all work order issues related to this work order.
     *
     * @return HasMany<WorkOrderIssue>
     */
    public function workOrderIssues(): HasMany
    {
        return $this->hasMany(WorkOrderIssue::class, 'work_order_id', 'work_order_id');
    }

    /**
     * Get the issues associated to work order
     *
     * @return BelongsToMany<Issue>
     */
    public function issues()
    {
        return $this->belongsToMany(Issue::class, 'work_order_issues', 'work_order_id', 'issue_id')->wherePivotNull('deleted_at');
    }

    /**
     * @return HasMany<WorkOrderServiceCall>
     */
    public function trips(): HasMany
    {
        return $this->hasMany(WorkOrderServiceCall::class, 'work_order_id', 'work_order_id')->orderBy('created_at', 'DESC');
    }

    public function activeTrip(): ?WorkOrderServiceCall
    {
        return $this->latestTrips->where('is_active', Boolean::YES())?->first();
    }

    /**
     * @return HasMany<WorkOrderServiceCall>
     */
    public function latestTrips(): HasMany
    {
        return $this->hasMany(WorkOrderServiceCall::class, 'work_order_id', 'work_order_id')->latest();
    }

    /**
     * @return array<int, string|null>
     */
    public function resolveStateAbilities(string $fromSource = 'web'): array
    {

        $workOrder = $this;
        $abilities = $workOrder->state->actions();
        if (empty($workOrder->vendor_id)) {
            $workOrderTask = $workOrder->tasks->first();
            $mobileAllowedAbilities = collect([
                EnRoute::$actionName,
                Working::$actionName,
                Ended::$actionName,
                'stop_trip',
            ])->filter(fn ($ability) => in_array($ability, $abilities, true))->values()->all();

            if ($this->state->equals(WorkInProgress::class, Paused::class)) {
                /** @var WorkOrderServiceCall $serviceCall */
                $serviceCall = $workOrderTask?->latestServiceCalls?->first();
                if ($serviceCall && ! $serviceCall->state->equals(EnRoute::class)) {
                    $mobileAllowedAbilities[] = 'before_media_upload';
                }
            }

            if ($this->state->equals(Paused::class)) {
                $serviceCall = $workOrderTask?->latestServiceCalls?->first();
                if (
                    ! empty($serviceCall) &&
                    in_array($serviceCall->trip_end_with_type, [Trip::SUBMIT_QUOTE(), Trip::AWAIT_FOR_QUOTE_APPROVAL()])
                ) {
                    if (empty($serviceCall->createdQuote)) {
                        $abilities[] = 'submit_quote';
                        if (in_array($serviceCall->trip_end_with_type, [Trip::SUBMIT_QUOTE()])) {
                            $mobileAllowedAbilities[] = 'submit_quote';
                        }

                        if (array_search(ReadyToSchedule::$actionName, $abilities, true) !== false) {
                            unset($abilities[array_search(ReadyToSchedule::$actionName, $abilities, true)]);
                        }
                    }
                }

                if (
                    ! empty($serviceCall->createdQuote) &&
                    in_array($serviceCall->createdQuote->status, [QuoteStatus::QUOTE_PENDING_REVIEW(), QuoteStatus::PENDING_APPROVAL()])
                ) {
                    if (array_search(ReadyToSchedule::$actionName, $abilities, true) !== false) {
                        unset($abilities[array_search(ReadyToSchedule::$actionName, $abilities, true)]);
                    }
                }
            }

            if ($this->state->equals(WorkInProgress::class)) {
                $serviceCall = $workOrderTask?->latestServiceCalls?->first();
                if ($serviceCall?->state->equals(Working::class)) {
                    // Unable to pause a work order with an active trip
                    if (array_search(Paused::$actionName, $abilities, true) !== false) {
                        unset($abilities[array_search(Paused::$actionName, $abilities, true)]);
                    }

                    $abilities[] = PauseTrip::$actionName;
                    $mobileAllowedAbilities[] = PauseTrip::$actionName;
                }

                if ($serviceCall?->state->equals(PauseTrip::class)) {
                    $abilities[] = 'resume';
                    $mobileAllowedAbilities[] = 'resume';
                    //Remove start_timer and stop_trip ability from both
                    if (array_search(Working::$actionName, $abilities, true) !== false) {
                        unset($abilities[array_search(Working::$actionName, $abilities, true)]);
                    }

                    if (array_search('stop_trip', $abilities, true) !== false) {
                        unset($abilities[array_search('stop_trip', $abilities, true)]);
                    }

                    if (array_search(Working::$actionName, $mobileAllowedAbilities, true) !== false) {
                        unset($mobileAllowedAbilities[array_search(Working::$actionName, $mobileAllowedAbilities, true)]);
                    }

                    if (array_search('stop_trip', $mobileAllowedAbilities, true) !== false) {
                        unset($mobileAllowedAbilities[array_search('stop_trip', $mobileAllowedAbilities, true)]);
                    }
                }

                if ($serviceCall?->state->equals(PauseTrip::class, Working::class)) {
                    if (empty($serviceCall->createdQuote)) {
                        // End trip submit quote option ability.
                        // Allow to create a quote while the trip has started.
                        $abilities = array_merge($abilities, ['submit_quote', 'can_submit_quote']);
                        $mobileAllowedAbilities[] = 'can_submit_quote';
                    }

                    //As per new requirement(https://app.clickup.com/t/86b08ycew)
                    // Quote task have no ability to add additional materials
                    if ($serviceCall->work_to_perform === WorkToPerformTypes::HOURLY_TASK()) {
                        $abilities[] = 'add_materials';
                        $mobileAllowedAbilities[] = 'add_materials';
                    }
                }

                if ($serviceCall?->state->equals(EnRoute::class)) {
                    // Remove stop_timer ability from both.
                    if (array_search(Ended::$actionName, $mobileAllowedAbilities, true) !== false) {
                        unset($mobileAllowedAbilities[array_search(Ended::$actionName, $mobileAllowedAbilities, true)]);
                    }

                    if (array_search(Ended::$actionName, $abilities, true) !== false) {
                        unset($abilities[array_search(Ended::$actionName, $abilities, true)]);
                    }
                }

                if ($serviceCall?->state->equals(Working::class)) {
                    // Remove start_timer and stop_trip ability from both.
                    if (array_search(Working::$actionName, $mobileAllowedAbilities, true) !== false) {
                        unset($mobileAllowedAbilities[array_search(Working::$actionName, $mobileAllowedAbilities, true)]);
                    }

                    if (array_search('stop_trip', $mobileAllowedAbilities, true) !== false) {
                        unset($mobileAllowedAbilities[array_search('stop_trip', $mobileAllowedAbilities, true)]);
                    }

                    if (array_search(Working::$actionName, $abilities, true) !== false) {
                        unset($abilities[array_search(Working::$actionName, $abilities, true)]);
                    }

                    if (array_search('stop_trip', $abilities, true) !== false) {
                        unset($abilities[array_search('stop_trip', $abilities, true)]);
                    }
                }
            }

            if ($fromSource === 'mobile') {
                /** @var WorkOrderServiceCall|null $serviceCall */
                $serviceCall = $workOrderTask?->latestServiceCalls?->first();

                /** @var TechnicianAppointment|null $appointment */
                $appointment = $serviceCall?->appointment;

                if ($this->state->equals(ReadyToSchedule::class)) {
                    if (request()->user() && request()->user()->can('scheduleTrip', [User::class])) {
                        if (
                            ! empty($serviceCall) &&
                            in_array($serviceCall->trip_end_with_type, [
                                Trip::FINISH_WITH_ANOTHER_DAY(),
                                Trip::GET_PARTS(),
                                Trip::RESIDENT_DID_NOT_SHOW_UP(),
                                Trip::OTHER(),
                            ])
                        ) {
                            $mobileAllowedAbilities[] = Scheduled::$actionName;
                        }
                    }
                }

                if ($appointment && $appointment->technician_id !== request()->user()?->technician?->technician_id) {
                    $mobileAllowedAbilities = [];
                }
            }

            if ($this->state->equals(ReadyToInvoice::class)) {
                $latestInvoices = $workOrder->latestInvoices->first() ?? null;

                if (empty($latestInvoices)) {
                    $abilities[] = 'create_invoice';
                } else {
                    if ($latestInvoices->state->equals(PaymentPending::class)) {
                        $abilities[] = Voided::$actionName;
                        $abilities[] = InvoicesPartiallyPaid::$actionName;
                        $abilities[] = Paid::$actionName;
                    }

                    if ($latestInvoices->state->equals(InvoicesPartiallyPaid::class)) {
                        $abilities[] = Voided::$actionName;
                        $abilities[] = Paid::$actionName;
                    }

                    if ($latestInvoices->state->equals(Voided::class)) {
                        $abilities[] = 'create_invoice';
                    }
                }
            }
        } else {
            $abilities = $workOrder->state->actions();

            // remove the ability of cancel the work order (after schedule)
            if ($workOrder->state->equals(ClaimPending::class)) {
                $abilities = [Canceled::$actionName];
            } elseif ($workOrder->state->equals(ReadyToSchedule::class)) {
                if (array_search(Paused::$actionName, $abilities, true) !== false) {
                    unset($abilities[array_search(Paused::$actionName, $abilities, true)]);
                }
            } elseif (! $workOrder->state->equals(Canceled::class)) {
                $abilities = [];
            }

            if ($workOrder->state->equals(QualityCheck::class, Completed::class, ReadyToInvoice::class)) {
                $abilities = $workOrder->state->actions();

                if (array_search(Canceled::$actionName, $abilities, true) !== false) {
                    unset($abilities[array_search(Canceled::$actionName, $abilities, true)]);
                }

                if (array_search(Paused::$actionName, $abilities, true) !== false) {
                    unset($abilities[array_search(Paused::$actionName, $abilities, true)]);
                }

                if ($this->state->equals(ReadyToInvoice::class)) {
                    $latestInvoices = $workOrder->latestInvoices->first() ?? null;

                    if (empty($latestInvoices)) {
                        $abilities[] = 'create_invoice';
                    } else {
                        if ($latestInvoices->state->equals(PaymentPending::class)) {
                            $abilities[] = Voided::$actionName;
                            $abilities[] = InvoicesPartiallyPaid::$actionName;
                            $abilities[] = Paid::$actionName;
                        }

                        if ($latestInvoices->state->equals(InvoicesPartiallyPaid::class)) {
                            $abilities[] = Voided::$actionName;
                            $abilities[] = Paid::$actionName;
                        }

                        if ($latestInvoices->state->equals(Voided::class)) {
                            $abilities[] = 'create_invoice';
                        }
                    }
                }
            }
            $mobileAllowedAbilities = [];
        }

        if ($workOrder->state->equals(Created::class, AwaitingAvailability::class, ReadyToSchedule::class)) {
            $abilities[] = 'create_issue';
        }

        return match (strtolower($fromSource)) {
            'mobile' => collect($mobileAllowedAbilities)->values()->all(),
            default => collect($abilities)->values()->all()
        };
    }
}

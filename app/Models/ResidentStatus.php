<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ResidentStatus
 *
 * @property int $resident_status_id
 * @property string $status
 * @property string $description
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ResidentStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResidentStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ResidentStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|ResidentStatus whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResidentStatus whereResidentStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ResidentStatus whereStatus($value)
 *
 * @mixin \Eloquent
 */
class ResidentStatus extends Model
{
    public $timestamps = false;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'resident_status_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'status',
        'description',
    ];
}

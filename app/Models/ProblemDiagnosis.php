<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\ProblemDiagnosis
 *
 * @property int $problem_diagnosis_id
 * @property string $problem_diagnosis_uuid
 * @property string $label
 * @property int $problem_sub_category_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 *
 * @method static \Database\Factories\ProblemDiagnosisFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis whereProblemDiagnosisId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis whereProblemSubCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis withoutTrashed()
 *
 * @property string $slug
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis whereSlug($value)
 *
 * @property-read ProblemSubCategory $subCategory
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis whereProblemDiagnosisUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemDiagnosis whereUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @mixin \Eloquent
 */
class ProblemDiagnosis extends Model
{
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'problem_diagnosis_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'problem_diagnosis_uuid',
        'label',
        'slug',
        'problem_sub_category_id',

    ];

    protected $casts = [
        'problem_diagnosis_uuid' => UuidBinary::class,
    ];

    /**
     * @return BelongsTo<ProblemSubCategory, $this>
     */
    public function subCategory(): BelongsTo
    {
        return $this->belongsTo(ProblemSubCategory::class, 'problem_sub_category_id', 'problem_sub_category_id');
    }

    /**
     * Get all issues related to this problem diagnosis.
     *
     * @return HasMany<Issue>
     */
    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class, 'problem_diagnosis_id', 'problem_diagnosis_id');
    }
}

<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServiceRequestCategory extends Model
{
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'service_request_category_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'service_request_category_uuid',
        'service_request_id',
        'problem_category_id',
        'problem_sub_category_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'service_request_category_uuid' => UuidBinary::class,
    ];

    /**
     * @return BelongsTo<ServiceRequest, self>
     */
    public function serviceRequest(): BelongsTo
    {
        return $this->belongsTo(ServiceRequest::class, 'service_request_id', 'service_request_id');
    }

    /**
     * @return BelongsTo<ProblemCategory, self>
     */
    public function problemCategory(): BelongsTo
    {
        return $this->belongsTo(ProblemCategory::class, 'problem_category_id', 'problem_category_id');
    }

    /**
     * @return BelongsTo<ProblemSubCategory, self>
     */
    public function problemSubCategory(): BelongsTo
    {
        return $this->belongsTo(ProblemSubCategory::class, 'problem_sub_category_id', 'problem_sub_category_id');
    }
}

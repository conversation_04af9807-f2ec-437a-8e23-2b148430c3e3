<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\WorkOrderTask
 *
 * @property int $work_order_task_id
 * @property int $organization_id
 * @property mixed $work_order_task_uuid
 * @property int $work_order_id
 * @property int $problem_diagnosis_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask whereProblemDiagnosisId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask whereWorkOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask whereWorkOrderTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask whereWorkOrderTaskUuid($value)
 *
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask whereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask withoutTrashed()
 *
 * @property-read WorkOrder $workOrder
 * @property int|null $estimated_duration
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTask whereEstimatedDuration($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\WorkOrderServiceCall> $completedServiceCalls
 * @property-read int|null $completed_service_calls_count
 * @property-read ProblemDiagnosis $diagnosis
 * @property-read ProblemDiagnosis $problemDiagnosis
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\WorkOrderServiceCall> $serviceCalls
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\WorkOrderServiceCall> $latestServiceCalls
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\WorkOrderTaskMaterial> $materials
 * @property-read int|null $service_calls_count
 *
 * @mixin \Eloquent
 */
class WorkOrderTask extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use SoftDeletes;

    protected $primaryKey = 'work_order_task_id';

    protected $fillable = [
        'work_order_task_uuid',
        'organization_id',
        'work_order_id',
        'estimated_duration',
        'problem_diagnosis_id',
    ];

    protected $casts = [
        'work_order_task_uuid' => UuidBinary::class,
    ];

    public function uuidColumn(): string
    {
        return 'work_order_task_uuid';
    }

    /**
     * @return BelongsTo<Organization, $this>
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    /**
     * @return BelongsTo<ProblemDiagnosis, $this>
     */
    public function diagnosis(): BelongsTo
    {
        return $this->belongsTo(ProblemDiagnosis::class, 'problem_diagnosis_id');
    }

    /**
     * @return BelongsTo<WorkOrder, $this>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id');
    }

    /**
     * @return HasManyThrough<WorkOrderServiceCall, WorkOrderServiceCallTask, $this>
     */
    public function serviceCalls(): HasManyThrough
    {
        return $this->hasManyThrough(
            WorkOrderServiceCall::class,
            WorkOrderServiceCallTask::class,
            'work_order_task_id',
            'work_order_service_call_id',
            'work_order_task_id',
            'work_order_service_call_id',
        )->where('status', 'active');
    }

    /**
     * @return HasManyThrough<WorkOrderServiceCall, WorkOrderServiceCallTask, $this>
     */
    public function completedServiceCalls(): HasManyThrough
    {
        return $this->hasManyThrough(
            WorkOrderServiceCall::class,
            WorkOrderServiceCallTask::class,
            'work_order_task_id',
            'work_order_service_call_id',
            'work_order_task_id',
            'work_order_service_call_id',
        )->where('status', 'completed');
    }

    /**
     * @return HasManyThrough<WorkOrderServiceCall, WorkOrderServiceCallTask, $this>
     */
    public function allServiceCalls(): HasManyThrough
    {
        return $this->hasManyThrough(
            WorkOrderServiceCall::class,
            WorkOrderServiceCallTask::class,
            'work_order_task_id',
            'work_order_service_call_id',
            'work_order_task_id',
            'work_order_service_call_id',
        );
    }

    /**
     * @return HasManyThrough<WorkOrderServiceCall, WorkOrderServiceCallTask, $this>
     */
    public function latestServiceCalls(): HasManyThrough
    {
        return $this->allServiceCalls()->latest();
    }

    /**
     * @return BelongsTo<ProblemDiagnosis, $this>
     */
    public function problemDiagnosis(): BelongsTo
    {
        return $this->belongsTo(ProblemDiagnosis::class, 'problem_diagnosis_id', 'problem_diagnosis_id');
    }

    /**
     * Get all the materials associated with the quote item.
     *
     * @return HasMany<QuoteTask, $this>
     */
    public function quoteTasks(): HasMany
    {
        return $this->hasMany(QuoteTask::class, 'work_order_task_id', 'work_order_task_id');
    }

    /**
     * Get the quotes associated with the task.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany<Quote, $this>
     */
    public function quotes(): HasMany
    {
        return $this->HasMany(Quote::class, 'work_order_task_id', 'work_order_task_id');
    }

    /**
     * Get latest quote.
     *
     * @return HasMany<Quote, $this>
     */
    public function latestQuotes(): HasMany
    {
        return $this->quotes()->latest();
    }

    /**
     * Get all the materials associated with the work order task.
     *
     * @return HasMany<WorkOrderTaskMaterial, $this>
     */
    public function materials(): HasMany
    {
        return $this->hasMany(WorkOrderTaskMaterial::class, 'work_order_task_id', 'work_order_task_id');
    }
}

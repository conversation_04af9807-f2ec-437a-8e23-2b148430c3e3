<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Database\Factories\LulaAppointmentFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\LulaAppointment
 *
 * @property int $lula_appointment_id
 * @property string $lula_appointment_uuid
 * @property int $organization_id
 * @property string $work_order_reference_number
 * @property string $service_category_label
 * @property string|null $rescheduled_reason
 * @property string|null $paused_reason
 * @property string|null $cancellation_reason
 * @property string|null $external_appointment_reference_id
 * @property \Carbon\CarbonImmutable|null $scheduled_start_time
 * @property \Carbon\CarbonImmutable|null $scheduled_end_time
 * @property \Carbon\CarbonImmutable|null $estimated_return_end_time
 * @property \Carbon\CarbonImmutable|null $estimated_return_start_time
 * @property \Carbon\CarbonImmutable|null $actual_start_time
 * @property \Carbon\CarbonImmutable|null $actual_end_time
 * @property int|null $elapse_time_in_sec
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 *
 * @method static \Database\Factories\LulaAppointmentFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment query()
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment whereLulaAppointmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment whereUpdatedAt($value)
 *
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment whereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment withoutTrashed()
 *
 * @property mixed $lula_appointment_uuid
 * @property int $organization_id
 *
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment whereLulaAppointmentUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LulaAppointment whereOrganizationId($value)
 *
 * @mixin \Eloquent
 */
class LulaAppointment extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;

    /** @use HasFactory<LulaAppointmentFactory> */
    use HasFactory;
    use SoftDeletes;

    protected $primaryKey = 'lula_appointment_id';

    protected $fillable = [
        'lula_appointment_uuid',
        'organization_id',
        'vendor_instructions',
        'work_order_reference_number',
        'service_category_label',
        'scheduled_start_time',
        'scheduled_end_time',
        'actual_start_time',
        'actual_end_time',
        'paused_reason',
        'cancellation_reason',
        'estimated_return_start_time',
        'estimated_return_end_time',
        'elapse_time_in_sec',
        'rescheduled_reason',
        'external_appointment_reference_id',
    ];

    protected $casts = [
        'lula_appointment_uuid' => UuidBinary::class,
        'scheduled_start_time' => 'immutable_datetime',
        'scheduled_end_time' => 'immutable_datetime',
        'estimated_return_start_time' => 'immutable_datetime',
        'estimated_return_end_time' => 'immutable_datetime',
        'actual_start_time' => 'immutable_datetime',
        'actual_end_time' => 'immutable_datetime',
    ];
}

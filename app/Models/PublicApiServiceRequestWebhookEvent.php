<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\PublicApiServiceRequestWebhookEvent
 *
 * @property int $public_api_service_request_webhook_event_id
 * @property string|null $public_api_service_request_webhook_event_uuid
 * @property int $service_request_id
 * @property int $service_request_source_id
 * @property array|null $payload
 * @property int $version
 * @property string $status
 * @property string |null $next_attempt_at
 * @property int $attempt
 * @property string|array $response
 * @property int|null $response_status_code
 * @property string $notified_at
 * @property int|null $parent_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @mixin \Eloquent
 */
class PublicApiServiceRequestWebhookEvent extends Model
{
    use BindsOnUuid;
    use GeneratesUuid;
    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'public_api_service_request_webhook_event_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'service_request_id',
        'service_request_source_id',
        'payload',
        'version',
        'status',
        'next_attempt_at',
        'attempt',
        'response',
        'response_status_code',
        'notified_at',
        'parent_id',
    ];

    protected $casts = [
        'public_api_service_request_webhook_event_uuid' => UuidBinary::class,
        'payload' => 'array',
        'response' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->version = $model->version ?? 1;
        });
    }
}

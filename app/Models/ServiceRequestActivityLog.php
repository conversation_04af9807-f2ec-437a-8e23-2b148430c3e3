<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\ServiceRequestActivityLog
 *
 * @property string|null $service_request_activity_log_uuid
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog withoutTrashed()
 *
 * @property int $service_request_activity_log_id
 * @property int $service_request_id
 * @property int|null $work_order_id
 * @property int $work_order_task_id
 * @property int $organization_id
 * @property int|null $triggered_by
 * @property string $type
 * @property string $event
 * @property array $event_attributes
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereEvent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereEventAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereTriggeredBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereServiceRequestActivityLogId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereServiceRequestActivityLogUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereServiceRequestId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequestActivityLog whereServiceRequestTaskId($value)
 *
 * @property-read User|null $triggeredBy
 *
 * @mixin \Eloquent
 */
class ServiceRequestActivityLog extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'service_request_activity_log_id';

    protected $dateFormat = 'Y-m-d H:i:s.u';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'service_request_activity_log_uuid',
        'service_request_id',
        'work_order_id',
        'work_order_task_id',
        'organization_id',
        'triggered_by',
        'type',
        'event',
        'event_attributes',
    ];

    protected $casts = [
        'service_request_activity_log_uuid' => UuidBinary::class,
        'event_attributes' => 'array',
    ];

    /**
     * @return BelongsTo<ServiceRequest, self>
     */
    public function serviceRequest(): BelongsTo
    {
        return $this->belongsTo(ServiceRequest::class, 'service_request_id', 'service_request_id');
    }

    /**
     * @return BelongsTo<WorkOrder, self>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id', 'work_order_id');
    }

    /**
     * @return BelongsTo<WorkOrder, self>
     */
    public function workOrderTask(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id', 'work_order_id');
    }

    /**
     * @return BelongsTo<User, self>
     */
    public function triggeredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'triggered_by', 'user_id');
    }
}

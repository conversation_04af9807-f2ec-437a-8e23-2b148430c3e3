<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\WorkOrderAssignee
 *
 * @property string|null $work_order_assignee_uuid
 * @property int $work_order_assignee_id
 * @property int $work_order_id
 * @property int $user_id
 * @property int|null $action_by_user_id
 * @property int|null $removed_by_user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 *  * @mixin \Eloquent
 */
class WorkOrderAssignee extends Model
{
    use BindsOnUuid;
    use GeneratesUuid;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_assignee_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'work_order_assignee_uuid',
        'work_order_id',
        'user_id',
        'action_by_user_id',
        'removed_by_user_id',
    ];

    protected $casts = [
        'work_order_assignee_uuid' => UuidBinary::class,
    ];

    /**
     * @return BelongsTo<WorkOrder, $this>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id');
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
}

<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\WorkOrderSource
 *
 * @property int $work_order_source_id
 * @property string $name
 * @property string $slug
 * @property string|null $work_order_source_uuid
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource whereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource whereWorkOrderSourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderSource whereWorkOrderSourceUuid($value)
 *
 * @mixin \Eloquent
 */
class WorkOrderSource extends Model
{
    use BindsOnUuid;
    use GeneratesUuid;
    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_source_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'name',
        'slug',
    ];

    protected $casts = [
        'work_order_source_uuid' => UuidBinary::class,
    ];
}

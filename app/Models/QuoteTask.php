<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Database\Factories\QuoteTaskFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\QuoteTask
 *
 * @property int $quote_task_id
 * @property string $quote_task_uuid
 * @property int $organization_id
 * @property int $work_order_task_id
 * @property int|null $user_id
 * @property float $cost
 * @property int $estimated_time
 * @property int|null $markup_fee_type_value
 * @property int|null $markup_fee_in_cents
 * @property int|null $cost_in_cents
 * @property int|null $total_cost_in_cents
 * @property int|null $quote_task_number
 * @property string $description
 * @property string $status
 * @property string $markup_fee_type
 * @property string $cost_type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @mixin \Eloquent
 */
class QuoteTask extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;

    /** @use HasFactory<QuoteTaskFactory> */
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'quote_task_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'organization_id',
        'work_order_task_id',
        'user_id',
        'markup_fee_type',
        'markup_fee_type_value',
        'markup_fee_in_cents',
        'cost_in_cents',
        'total_cost_in_cents',
        'estimated_time',
        'description',
        'status',
        'quote_id',
        'cost_type',
        'quote_task_number',
    ];

    protected $casts = [
        'quote_task_uuid' => UuidBinary::class,
    ];

    public function uuidColumn(): string
    {
        return 'quote_task_uuid';
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return BelongsTo<WorkOrderTask, $this>
     */
    public function task(): belongsTo
    {
        return $this->belongsTo(WorkOrderTask::class, 'work_order_task_id', 'work_order_task_id');
    }

    /**
     * @return BelongsTo<Quote, $this>
     */
    public function quote(): belongsTo
    {
        return $this->belongsTo(Quote::class, 'quote_id', 'quote_id');
    }

    /**
     * Get all the materials associated with the quote item.
     *
     * @return HasMany<QuoteTaskMaterial, $this>
     */
    public function quoteTaskMaterials(): HasMany
    {
        return $this->hasMany(QuoteTaskMaterial::class, 'quote_task_id', 'quote_task_id');
    }

    /**
     * Get all the media associated with the quote item.
     *
     * @return BelongsToMany<Media, $this>
     */
    public function media(): BelongsToMany
    {
        return $this->belongsToMany(Media::class, 'work_order_media', 'quote_task_id', 'media_id')
            ->withPivot('media_type', 'has_thumbnail', 'has_upload_completed', 'has_optimized');
    }
}

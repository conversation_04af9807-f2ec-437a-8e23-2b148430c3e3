<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\AsArrayObject;
use Illuminate\Database\Eloquent\MassPrunable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeveloperAlert extends Model
{
    use MassPrunable;

    protected $table = 'developer_alerts';

    protected $primaryKey = 'developer_alert_id';

    protected $fillable = [
        'level_name',
        'level',
        'message',
        'logged_at',
        'context',
        'extra',
        'organization_id',
        'occurrence',
    ];

    protected $casts = [
        'context' => AsArrayObject::class,
        'extra' => AsArrayObject::class,
        'logged_at' => 'immutable_datetime',
    ];

    /**
     * @return BelongsTo<Organization, $this>
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id', 'organization_id');
    }
}

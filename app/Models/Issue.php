<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\States\Issue\IssueState;
use App\Traits\AbilityResolverTrait;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\ModelStates\HasStates;

/**
 * Class Issue
 *
 * @property int $issue_id
 * @property string $issue_uuid
 * @property int $service_request_id
 * @property int $problem_diagnosis_id
 * @property int $organization_id
 * @property int|null $user_id
 * @property string $title
 * @property string $description
 * @property IssueState $state
 * @property \Carbon\CarbonImmutable|null $state_updated_at
 * @property \Carbon\CarbonImmutable $created_at
 * @property \Carbon\CarbonImmutable $updated_at
 * @property \Carbon\CarbonImmutable|null $deleted_at
 *
 * @mixin \Eloquent
 */
class Issue extends Model
{
    use AbilityResolverTrait;
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use HasStates;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'issue_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'issue_uuid',
        'service_request_id',
        'problem_diagnosis_id',
        'organization_id',
        'user_id',
        'title',
        'description',
        'state',
        'state_updated_at',
        'deleted_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'state' => IssueState::class,
        'issue_uuid' => UuidBinary::class,
        'created_at' => 'immutable_datetime',
        'updated_at' => 'immutable_datetime',
        'deleted_at' => 'immutable_datetime',
        'state_updated_at' => 'immutable_datetime',
    ];

    /**
     * Get the service request associated with the issue.
     *
     * @return BelongsTo<ServiceRequest, self>
     */
    public function serviceRequest(): BelongsTo
    {
        return $this->belongsTo(ServiceRequest::class, 'service_request_id');
    }

    /**
     * Get the problem diagnosis associated with the issue.
     *
     * @return BelongsTo<ProblemDiagnosis, self>
     */
    public function problemDiagnosis(): BelongsTo
    {
        return $this->belongsTo(ProblemDiagnosis::class, 'problem_diagnosis_id');
    }

    /**
     * Get all work order issues related to this issue.
     *
     * @return HasMany<WorkOrderIssue>
     */
    public function workOrderIssues(): HasMany
    {
        return $this->hasMany(WorkOrderIssue::class, 'issue_id', 'issue_id');
    }

    /**
     * Get the issues associated to work order
     *
     * @return BelongsToMany<WorkOrder>
     */
    public function workOrders(): BelongsToMany
    {
        return $this->belongsToMany(WorkOrder::class, 'work_order_issues', 'issue_id', 'work_order_id')
            ->wherePivotNull('deleted_at');
    }

    /**
     * Get the user associated with the issue.
     *
     * @return BelongsTo<User, self>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}

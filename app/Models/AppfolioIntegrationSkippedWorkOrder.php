<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AppfolioIntegrationSkippedWorkOrder extends Model
{
    use HasFactory;

    /**
     * App\Models\AppfolioIntegrationSkippedWorkOrder
     *
     * @property int $appfolio_integration_skipped_work_order_id
     * @property int $organization_id
     * @property string $appfolio_work_order_uuid
     * @property string $appfolio_entity_type
     * @property string $display_number
     * @property string $work_order_url
     * @property string $work_order_status
     * @property string $vendor_id
     * @property \Illuminate\Support\Carbon|null $work_order_created_at
     * @property \Illuminate\Support\Carbon|null $notified_at
     * @property \Illuminate\Support\Carbon|null $created_at
     *
     * @mixin \Eloquent
     */

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'appfolio_integration_skipped_work_order_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'organization_id',
        'work_order_id',
        'appfolio_work_order_uuid',
        'display_number',
        'work_order_url',
        'work_order_status',
        'vendor_id',
        'work_order_created_at',
        'notified_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'notified_at' => 'date',
        'work_order_created_at' => 'date',
    ];
}

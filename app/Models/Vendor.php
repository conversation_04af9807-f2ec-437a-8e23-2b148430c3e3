<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\Filterable;
use App\Traits\GeneratesUuid;
use Database\Factories\VendorFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;

/**
 * App\Models\Vendor
 *
 * @property int $vendor_id
 * @property string|null $vendor_uuid
 * @property string|null $company_name
 * @property string|null $email
 * @property string|null $log_file_name
 * @property string|null $first_name
 * @property string|null $last_name
 * @property string|null $phone_number
 * @property string $service
 * @property string $external_reference_id
 * @property string $external_reference_link
 * @property string|null $address1
 * @property string|null $address2
 * @property string|null $city
 * @property string|null $state_province
 * @property string|null $postal_zip_code
 * @property int|null $country_id
 * @property string $is_active
 * @property string $on_boarding_status
 * @property string $is_company
 * @property \Carbon\CarbonImmutable|null $gl_insurance_expire_at
 * @property \Carbon\CarbonImmutable $last_sync_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read Organization $organization
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\VendorService> $vendorServices
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\VendorUser> $vendorUsers
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\VendorOnboarding> $vendorOnboardings
 *
 * @method static \Database\Factories\VendorFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor query()
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereAddress1($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereAddress2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereIsCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor wherePostalZipCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereStateProvince($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereUuid($uuid, $uuidColumn = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereVendorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereVendorUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor withoutTrashed()
 *
 * @property-read User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Vendor whereNotUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @mixin \Eloquent
 */
class Vendor extends Model
{
    use BindsOnUuid;
    use Filterable;
    use GeneratesUuid;

    /** @use HasFactory<VendorFactory> */
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'vendor_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'vendor_uuid',
        'company_name',
        'email',
        'log_file_name',
        'first_name',
        'last_name',
        'phone_number',
        'service',
        'external_reference_id',
        'external_reference_link',
        'address1',
        'address2',
        'city',
        'state_province',
        'postal_zip_code',
        'country_id',
        'is_active',
        'on_boarding_status',
        'is_company',
        'gl_insurance_expire_at',
        'last_sync_at',

    ];

    protected $casts = [
        'vendor_uuid' => UuidBinary::class,
        'gl_insurance_expire_at' => 'immutable_datetime',
        'last_sync_at' => 'immutable_datetime',
    ];

    /**
     * Get all the vendor services of the feature
     *
     * @return HasMany<VendorService>
     */
    public function vendorServices()
    {
        return $this->hasMany(VendorService::class, 'vendor_id');
    }

    /**
     * Get vendor service area
     *
     * @return HasOne<VendorServiceArea>
     */
    public function vendorServiceArea()
    {
        return $this->hasOne(VendorServiceArea::class, 'vendor_id');
    }

    /**
     * Get all the vendor users
     *
     * @return HasMany<VendorUser>
     */
    public function vendorUsers()
    {
        return $this->hasMany(VendorUser::class, 'vendor_id');
    }

    /**
     * Get all the vendor users
     *
     * @return HasMany<VendorOnboarding>
     */
    public function vendorOnboardings()
    {
        return $this->hasMany(VendorOnboarding::class, 'vendor_id');
    }

    /**
     * Address info
     */
    public function getAddress(): string
    {
        $addressComponent = [];
        $addressComponent['address_1'] = $this->address1 ?? null;
        $addressComponent['address_2'] = $this->address2 ?? null;

        $addressComponent['city'] = $this->city ?? null;
        $addressComponent['state'] = $this->state_province ?? null;
        $addressComponent['postal_zip_code'] = $this->postal_zip_code ?? null;

        return trim(
            implode(', ', array_filter([
                implode(', ', array_filter(Arr::only($addressComponent, ['address_1', 'address_2', 'city']))),
                implode(' ', array_filter(Arr::only($addressComponent, ['state', 'postal_zip_code']))),
            ]))
        );
    }

    public function getName(): string
    {
        return trim(implode(' ', array_filter([$this->first_name, $this->last_name])));
    }

    /**
     * @return BelongsToMany<Organization, $this>
     */
    public function organizations(): BelongsToMany
    {
        return $this->belongsToMany(Organization::class, 'organization_vendors', 'vendor_id', 'organization_id');
    }
}

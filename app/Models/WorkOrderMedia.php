<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\WorkOrderMedia
 *
 * @property int $work_order_media_id
 * @property string|null $work_order_media_uuid
 * @property int $media_id
 * @property int $organization_id
 * @property int $work_order_id
 * @property int $work_order_issue_id
 * @property int $work_order_task_id
 * @property int $quote_task_id
 * @property int $user_id
 * @property string $media_type
 * @property string $has_thumbnail
 * @property string $has_optimized
 * @property string $has_upload_completed
 * @property int $work_order_service_call_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereHasOptimized($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereHasThumbnail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereHasUploadCompleted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereMediaId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereMediaType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereWorkOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereWorkOrderMediaId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderMedia whereWorkOrderMediaUuid($value)
 *
 * @property-read Media $media
 * @property-read WorkOrder $workOrder
 *
 * @mixin \Eloquent
 */
class WorkOrderMedia extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_media_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'work_order_media_uuid',
        'media_id',
        'organization_id',
        'work_order_id',
        'work_order_issue_id',
        'user_id',
        'work_order_task_id',
        'quote_task_id',
        'media_type',
        'has_thumbnail',
        'has_optimized',
        'has_upload_completed',
        'work_order_service_call_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'work_order_media_uuid' => UuidBinary::class,
    ];

    /**
     * @return BelongsTo<WorkOrder, $this>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id', 'work_order_id');
    }

    /**
     * @return BelongsTo<QuoteTask, $this>
     */
    public function quoteTask(): BelongsTo
    {
        return $this->belongsTo(QuoteTask::class, 'quote_task_id', 'quote_task_id');
    }

    /**
     * @return BelongsTo<Media, $this>
     */
    public function media(): BelongsTo
    {
        return $this->belongsTo(Media::class, 'media_id', 'media_id');
    }
}

<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\WorkOrderNoteLog
 *
 * @property int $work_order_note_log_id
 * @property string $work_order_note_log_uuid
 * @property int $organization_id
 * @property int $work_order_note_id
 * @property int|null $user_id
 * @property int|null $vendor_id
 * @property string $note
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $delete
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog whereNote($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog whereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog whereWorkOrderNoteId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog whereWorkOrderNoteLogId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog whereWorkOrderNoteLogUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderNoteLog withoutTrashed()
 *
 * @mixin \Eloquent
 */
class WorkOrderNoteLog extends Model
{
    use BindsOnUuid;
    use GeneratesUuid;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_note_log_id';

    protected $dateFormat = 'Y-m-d H:i:s.u';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'organization_id',
        'work_order_note_id',
        'user_id',
        'vendor_id',
        'note',
    ];

    protected $casts = [
        'work_order_note_log_uuid' => UuidBinary::class,
    ];
}

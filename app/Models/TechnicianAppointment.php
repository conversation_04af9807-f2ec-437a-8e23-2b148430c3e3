<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Database\Factories\TechnicianAppointmentFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\TechnicianAppointment
 *
 * @property int $technician_appointment_id
 * @property mixed $technician_appointment_uuid
 * @property int $organization_id
 * @property string|null $rescheduled_reason
 * @property \Carbon\CarbonImmutable|null $enroute_at
 * @property \Carbon\CarbonImmutable|null $actual_start_time
 * @property \Carbon\CarbonImmutable|null $actual_end_time
 * @property CarbonImmutable|null $en_route_timer_paused_at
 * @property CarbonImmutable|null $en_route_timer_resumed_at
 * @property CarbonImmutable|null $work_timer_paused_at
 * @property CarbonImmutable|null $work_timer_resumed_at
 * @property string|null $canceled_at
 * @property string|null $canceled_details
 * @property int|null $canceled_by_user
 * @property int|null $canceled_by_resident
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property int|null $actual_elapse_time_in_sec
 * @property int|null $adjusted_elapse_time_in_sec
 * @property int|null $travel_time_in_sec
 * @property int|null $adjusted_travel_time_in_sec
 *
 * @method static \Database\Factories\TechnicianAppointmentFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment query()
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereActualEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereActualStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereCanceledAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereCanceledByResident($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereCanceledByUser($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereCanceledDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereEnrouteAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereTechnicianAppointmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereTechnicianAppointmentUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereUpdatedAt($value)
 *
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment withoutTrashed()
 *
 * @property int $technician_id
 * @property int|null $work_order_id
 * @property int|null $work_order_task_id
 * @property int|null $is_block_out_all_day
 * @property string $appointment_type
 * @property string|null $note
 * @property string|null $time_adjusted_reason
 * @property string|null $paused_reason
 *
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereTechnicianId($value)
 *
 * @property-read Technician $technician
 * @property int|null $locked_by
 * @property string|null $lock_expires_at
 * @property int|null $rescheduled_from
 *
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereLockExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereLockedBy($value)
 *
 * @property \Carbon\CarbonImmutable $scheduled_start_time
 * @property \Carbon\CarbonImmutable $scheduled_end_time
 * @property-read WorkOrderServiceCall|null $serviceCall
 *
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereScheduledEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereScheduledStartTime($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\TechnicianAppointmentLog> $appointmentLogs
 * @property-read int|null $appointment_logs_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereRescheduledFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TechnicianAppointment whereRescheduledReason($value)
 *
 * @mixin \Eloquent
 */
class TechnicianAppointment extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;

    /** @use HasFactory<TechnicianAppointmentFactory> */
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'technician_appointment_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'organization_id',
        'work_order_id',
        'work_order_task_id',
        'technician_id',
        'appointment_type',
        'is_block_out_all_day',
        'note',
        'paused_reason',
        'scheduled_start_time',
        'scheduled_end_time',
        'work_timer_paused_at',
        'work_timer_resumed_at',
        'enroute_at',
        'actual_start_time',
        'actual_end_time',
        'actual_elapse_time_in_sec',
        'adjusted_elapse_time_in_sec',
        'lock_expires_at',
        'rescheduled_reason',
        'rescheduled_from',
        'travel_time_in_sec',
        'adjusted_travel_time_in_sec',
        'en_route_timer_paused_at',
        'en_route_timer_resumed_at',
        'time_adjusted_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'technician_appointment_uuid' => UuidBinary::class,
        'scheduled_start_time' => 'immutable_datetime',
        'scheduled_end_time' => 'immutable_datetime',
        'enroute_at' => 'immutable_datetime',
        'actual_start_time' => 'immutable_datetime',
        'actual_end_time' => 'immutable_datetime',
        'canceled_at' => 'immutable_datetime',
        'lock_expires_at' => 'immutable_datetime',
        'en_route_timer_paused_at' => 'immutable_datetime',
        'en_route_timer_resumed_at' => 'immutable_datetime',
        'work_timer_paused_at' => 'immutable_datetime',
        'work_timer_resumed_at' => 'immutable_datetime',
    ];

    public function uuidColumn(): string
    {
        return 'technician_appointment_uuid';
    }

    /**
     * @return BelongsTo<Technician, $this>
     */
    public function technician(): BelongsTo
    {
        return $this->belongsTo(Technician::class, 'technician_id');
    }

    /**
     * @return HasOne<WorkOrderServiceCall, $this>
     */
    public function serviceCall(): HasOne
    {
        return $this->hasOne(WorkOrderServiceCall::class, 'technician_appointment_id');
    }

    /**
     * @return BelongsTo<WorkOrder, $this>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id');
    }

    /**
     * @return BelongsTo<Property, $this>
     */
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class, 'property_id');
    }

    /**
     * Get the technician logs related to the technician appointment.
     *
     * @return HasMany<\App\Models\TechnicianAppointmentLog, $this>
     */
    public function appointmentLogs(): HasMany
    {
        return $this->hasMany(TechnicianAppointmentLog::class, 'technician_appointment_id', 'technician_appointment_id')
            ->latest();
    }
}

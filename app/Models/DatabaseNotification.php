<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Carbon\CarbonImmutable;
use Database\Factories\DatabaseNotificationFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Notifications\DatabaseNotification as BaseDatabaseNotification;

/**
 * App\Models\DatabaseNotification
 *
 * @property int $notification_id
 * @property string $notification_uuid
 * @property string $reference_notification_id
 * @property int $organization_id
 * @property string $type
 * @property int $notifiable_id
 * @property int $work_order_id
 * @property int $service_request_id
 * @property int|null $action_done_by
 * @property string $notifiable_type
 * @property string $data
 * @property CarbonImmutable|null $read_at
 * @property CarbonImmutable|null $cleared_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @mixin \Eloquent
 */
class DatabaseNotification extends BaseDatabaseNotification
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;

    /** @use HasFactory<DatabaseNotificationFactory> */
    use HasFactory;

    protected $table = 'notifications';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'notification_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'notification_uuid',
        'reference_notification_id',
        'organization_id',
        'work_order_id',
        'service_request_id',
        'action_done_by',
        'type',
        'notifiable_type',
        'notifiable_id',
        'data',
        'read_at',
        'cleared_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'notification_uuid' => UuidBinary::class,
        'data' => 'array',
        'read_at' => 'immutable_datetime',
        'cleared_at' => 'immutable_datetime',
    ];

    public function uuidColumn(): string
    {
        return 'notification_uuid';
    }

    /**
     * Scope a query to only include cleared notifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder<DatabaseNotification>  $query
     * @return \Illuminate\Database\Eloquent\Builder<DatabaseNotification>
     */
    public function scopeCleared(Builder $query)
    {
        return $query->whereNotNull('cleared_at');
    }

    /**
     * @return BelongsTo<WorkOrder, $this>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id');
    }

    /**
     * @return BelongsTo<WorkOrder, self>
     */
    public function serviceRequest(): BelongsTo
    {
        return $this->belongsTo(ServiceRequest::class, 'service_request_id');
    }

    /**
     * @return BelongsTo<User, self>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'action_done_by', 'user_id');
    }
}

<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\VendorService
 *
 * @property int $vendor_service_id
 * @property string $vendor_service_uuid
 * @property int $vendor_id
 * @property int|null $problem_category_id
 * @property int|null $problem_sub_category_id
 * @property int|null $problem_diagnosis_id
 * @property int $organization_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read Vendor $vendor
 * @property-read ProblemCategory $problemCategory
 * @property-read ProblemSubCategory $problemSubCategory
 * @property-read ProblemDiagnosis $problemDiagnosis
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|VendorService orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorService whereMediaUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorService whereUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @mixin \Eloquent
 */
class VendorService extends Model
{
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'vendor_services';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'vendor_service_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'vendor_service_uuid',
        'vendor_id',
        'problem_category_id',
        'problem_sub_category_id',
        'problem_diagnosis_id',
        'organization_id',
    ];

    protected $casts = [
        'vendor_service_uuid' => UuidBinary::class,
    ];

    /**
     * @return BelongsTo<Vendor, self>
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }

    /**
     * @return BelongsTo<ProblemCategory, self>
     */
    public function problemCategory(): BelongsTo
    {
        return $this->belongsTo(ProblemCategory::class, 'problem_category_id');
    }

    /**
     * @return BelongsTo<ProblemSubCategory, self>
     */
    public function problemSubCategory(): BelongsTo
    {
        return $this->belongsTo(ProblemSubCategory::class, 'problem_sub_category_id');
    }

    /**
     * @return BelongsTo<ProblemDiagnosis, self>
     */
    public function problemDiagnosis(): BelongsTo
    {
        return $this->belongsTo(ProblemDiagnosis::class, 'problem_diagnosis_id');
    }

    /**
     * @return BelongsTo<Organization, self>
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }
}

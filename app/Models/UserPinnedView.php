<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\UserPinnedView
 *
 * @property int $user_pinned_view_id
 * @property int|null $user_id
 * @property int|null $view_id
 *
 * @method static \Illuminate\Database\Eloquent\Builder|UserPinnedView newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserPinnedView newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserPinnedView query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserPinnedView whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserPinnedView whereUserPinnedViewId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserPinnedView whereViewId($value)
 *
 * @mixin \Eloquent
 */
class UserPinnedView extends Model
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'user_pinned_view_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'user_id',
        'view_id',
    ];
}

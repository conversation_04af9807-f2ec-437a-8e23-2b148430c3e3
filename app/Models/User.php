<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Casts\UuidBinary;
use App\Enums\UserStatus;
use App\Events\User\UserInvited;
use App\Helpers\Helper;
use App\Packages\OrganizationRolePermission\OrganizationUserManager;
use App\Packages\OrganizationRolePermission\Traits\HasPermission;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\Filterable;
use App\Traits\GeneratesUuid;
use Carbon\CarbonImmutable;
use Database\Factories\UserFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use InvalidArgumentException;
use Laravel\Sanctum\HasApiTokens;

/**
 * App\Models\User
 *
 * @property int $user_id
 * @property mixed $user_uuid
 * @property string|null $cognito_user_id
 * @property int|null $organization_id
 * @property string $name
 * @property string $email
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read Organization|null $organization
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Portfolio> $portfolios
 * @property-read int|null $portfolios_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 *
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|User onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|User query()
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCognitoUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|User withoutTrashed()
 *
 * @property string $first_name
 * @property string|null $middle_name
 * @property string|null $last_name
 * @property string|null $phone_number
 * @property string|null $profile_pic
 * @property string|null $street_address
 * @property string|null $apt_suite_unit
 * @property string|null $city
 * @property int|null $state_id
 * @property int|null $country_id
 * @property string|null $zip_code
 * @property string|null $enable_email_notification
 * @property string|null $enable_sms_notification
 * @property string|null $enable_push_notification
 * @property int|null $last_password_updated_user_id
 * @property CarbonImmutable $last_activity_at
 * @property int $timezone_id
 * @property string $user_type
 * @property-read Country|null $country
 * @property-read State|null $state
 *
 * @method static \Illuminate\Database\Eloquent\Builder|User filter(\App\Http\Filters\QueryFilter $filter)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEnableEmailNotification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEnablePushNotification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereEnableSmsNotification($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User wherePhoneNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereProfilePic($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereStateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereStreetAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereStreetAddressTwo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUserType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUserUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereUuid($uuid, $uuidColumn = null)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereZipCode($value)
 *
 * @property string|null $last_password_updated_at
 * @property-read User|null $lastPasswordUpdatedUser
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\TechnicianWorkingHour> $userWorkingHours
 * @property-read int|null $user_working_hours_count
 * @property-read Vendor|null $vendor
 *
 * @method static \Illuminate\Database\Eloquent\Builder|User orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|User orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereAptSuiteUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereLastPasswordUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereLastPasswordUpdatedUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereMiddleName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|User whereNotUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @property-read Technician|null $technician
 * @property string|null $cognito_user_name
 *
 * @method static \Illuminate\Database\Eloquent\Builder|User whereCognitoUserName($value)
 *
 * @property-read Timezone|null $timezone
 *
 * @mixin \Eloquent
 */
class User extends Authenticatable
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use Filterable;
    use GeneratesUuid;
    use HasApiTokens;

    /** @use HasFactory<UserFactory> */
    use HasFactory;
    use HasPermission;
    use Notifiable;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'user_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'email',
        'cognito_user_id',
        'cognito_user_name',
        'organization_id',
        'first_name',
        'middle_name',
        'last_name',
        'phone_number',
        'profile_pic',
        'street_address',
        'apt_suite_unit',
        'city',
        'state',
        'zip_code',
        'enable_email_notification',
        'enable_sms_notification',
        'enable_push_notification',
        'status',
        'user_type',
        'country_id',
        'timezone_id',
        'last_activity_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'user_uuid' => UuidBinary::class,
        'last_activity_at' => 'immutable_datetime',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::created(function (User $user) {
            UserInvited::dispatch($user);
        });

        static::creating(function (User $user) {
            if (empty($user->timezone_id)) {
                /** @var Timezone $timezone */
                $timezone = Timezone::defaultTimzone()->first();
                if ($timezone) {
                    $user->timezone_id = $timezone->timezone_id;
                }
            }
        });
    }

    public function getName(): string
    {
        return trim(implode(' ', array_filter([$this->first_name, $this->middle_name, $this->last_name])));
    }

    /**
     * The portfolios that belong to the user.
     *
     * @return BelongsToMany<Portfolio, $this>
     */
    public function portfolios(): BelongsToMany
    {
        return $this->belongsToMany(Portfolio::class, 'portfolio_user', 'user_id', 'portfolio_id');
    }

    /**
     * @return BelongsToMany<Role, $this>
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(Role::class, 'user_role', 'user_id', 'role_id');
    }

    /**
     * @return Collection<int, Permission>
     */
    public function getCachedPermissions(): Collection
    {
        return app(OrganizationUserManager::class)
            ->setUser($this)
            ->getPermissions();
    }

    /**
     * @return Collection<int, Role>
     */
    public function getCachedRoles(): Collection
    {
        return app(OrganizationUserManager::class)
            ->setUser($this)
            ->getRoles();
    }

    /**
     * @param  array<string|int>|int|Role|Collection<int, Role>  $roles
     */
    public function assignRole(mixed $roles): void
    {
        if (is_int($roles)) {
            $roles = [$roles];
        } elseif ($roles instanceof Role) {
            $roles = [$roles->role_id];
        } elseif ($roles instanceof Collection) {
            $roles = $roles->pluck('role_id');
        } elseif (! is_iterable($roles)) {
            throw new InvalidArgumentException('Called assignRole with invalid parameter.');
        }

        $this->roles()->sync($roles, false);

        $this->clearPermissionCache();
    }

    /**
     * @param  mixed|array<int,string>  $roles
     */
    public function syncRoles(mixed $roles): void
    {
        $this->roles()->sync($roles);

        $this->clearPermissionCache();
    }

    /**
     * Fetch the user's abilities.
     *
     * @return array<string>|Collection<string|int,mixed>
     */
    public function permissions(): array|Collection
    {
        return $this->getCachedPermissions()->pluck('name')->unique();
    }

    public function hasRole(mixed $role): bool
    {
        return (bool) $this->roles()->where('name', $role)
            ->when(is_int($role), function ($query) use ($role) {
                return $query->orWhere('role_id', $role);
            })
            ->when($role instanceof Role, function ($query) use ($role) {
                return $query->orWhere('role_id', $role->role_id);
            })->count();
    }

    public function clearPermissionCache(): bool
    {
        return app(OrganizationUserManager::class)
            ->setUser($this)
            ->forgetCachedPermissions();
    }

    /**
     * @return BelongsTo<State, $this>
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    /**
     * @return BelongsTo<Country, $this>
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    /**
     * Get the vendor associated with the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne<\App\Models\Vendor, $this>
     */
    public function vendor(): HasOne
    {
        return $this->hasOne(Vendor::class, 'user_id', 'user_id');
    }

    /**
     * Get the last password updated user
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne<\App\Models\User, $this>
     */
    public function lastPasswordUpdatedUser(): HasOne
    {
        return $this->hasOne(User::class, 'user_id', 'last_password_updated_user_id');
    }

    /**
     * Get the technician associated with the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne<Technician, $this>
     */
    public function technician(): HasOne
    {
        return $this->hasOne(Technician::class, 'user_id', 'user_id');
    }

    /**
     * @return BelongsTo<Timezone, $this>
     */
    public function timezone(): BelongsTo
    {
        return $this->belongsTo(Timezone::class, 'timezone_id');
    }

    public function getInlineFullAddressFormat(): string
    {
        $fullAddress = [];

        $addressPartOne = [];
        $addressPartTwo = [];
        $addressPartOne[] = $this->street_address;

        if ($this->apt_suite_unit) {
            $addressPartOne[] = "Unit #{$this->apt_suite_unit}";
        }
        $fullAddress[] = implode(' ', $addressPartOne);

        $addressPartTwo[] = $this->city;
        if (! empty($this->state->state_code)) {
            $addressPartTwo[] = $this->state->state_code;
        }
        $addressPartTwo[] = $this->zip_code;
        $fullAddress[] = implode(' ', array_filter($addressPartTwo));

        return implode(', ', $fullAddress);

    }

    /**
     * @return array<string, mixed>
     */
    public function getAddress(): array
    {
        return [
            'street_address' => $this->street_address,
            'apt_suite_unit' => $this->apt_suite_unit,
            'city' => $this->city,
            'state_code' => $this->state?->state_code,
            'zip_code' => $this->zip_code,
        ];
    }

    /**
     * @param  Builder<self>  $query
     */
    public function scopeActive(Builder $query): void
    {
        $query->whereStatus(UserStatus::ACTIVE());
    }

    public function routeNotificationForTwilio(): string
    {
        return Helper::resolvePhoneNumberForTwilio($this->phone_number ?? '');
    }

    /**
     * The channels the user receives notification broadcasts on.
     */
    public function receivesBroadcastNotificationsOn(): string
    {
        return 'organization.' . $this->organization?->organization_uuid . '.users.' . $this->user_uuid;
    }

    /**
     * Get the entity's notifications.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany<DatabaseNotification, $this>
     */
    public function notifications()
    {
        return $this->morphMany(DatabaseNotification::class, 'notifiable')->latest();
    }

    /**
     * Get the entity's unread notifications.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany<DatabaseNotification, $this>
     */
    public function clearedNotifications()
    {
        return $this->notifications()->cleared();
    }

    /**
     * Get the entity's uncleared unread notifications.
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphMany<DatabaseNotification, $this>
     */
    public function unClearedUnreadNotifications()
    {
        return $this->notifications()->unread()->whereNull('cleared_at');
    }
}

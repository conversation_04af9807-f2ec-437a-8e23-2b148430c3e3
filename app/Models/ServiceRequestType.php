<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ServiceRequestType extends Model
{
    use GeneratesUuid;
    use HasFactory;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'service_request_type_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'label',
        'name',
        'slug',
    ];

    protected $casts = [
        'service_request_type_uuid' => UuidBinary::class,
    ];
}

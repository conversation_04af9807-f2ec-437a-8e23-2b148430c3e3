<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Database\Factories\PropertyFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Property
 *
 * @property int $property_id
 * @property string|null $property_uuid
 * @property int $organization_id
 * @property int|null $property_type_id
 * @property string|null $external_source_id
 * @property string|null $property_name
 * @property string $full_address
 * @property string $street_address
 * @property string|null $unit_type
 * @property string|null $unit_number
 * @property string|null $bed_unit
 * @property string $city
 * @property int $state_id
 * @property string $postal_zip_code
 * @property int $country_id
 * @property string|null $latitude
 * @property string|null $longitude
 * @property string|null $navigation_address
 * @property string|null $google_place_id
 * @property json|null $google_geocode_response
 * @property int $timezone_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read Organization $organization
 *
 * @method static \Database\Factories\PropertyFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|Property newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Property newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Property onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Property query()
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereBedUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereExternalSourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereFullAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property wherePostalZipCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property wherePropertyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property wherePropertyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property wherePropertyTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property wherePropertyUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereStateProvince($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereStreetAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereUnitNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereUnitType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereUuid($uuid, $uuidColumn = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Property withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Property withoutTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Property orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Property orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereNotUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Resident> $residents
 * @property-read int|null $residents_count
 * @property-read Country $country
 * @property-read State $state
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Property whereStateId($value)
 *
 * @property-read Timezone|null $timezone
 *
 * @mixin \Eloquent
 */
class Property extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;

    /** @use HasFactory<PropertyFactory> */
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'property_id';

    protected $casts = [
        'property_uuid' => UuidBinary::class,
        'google_geocode_response' => 'json',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'property_uuid',
        'organization_id',
        'property_type_id',
        'external_source_id',
        'property_name',
        'full_address',
        'street_address',
        'unit_type',
        'unit_number',
        'bed_unit',
        'city',
        'state_province',
        'postal_zip_code',
        'country_id',
        'latitude',
        'longitude',
        'navigation_address',
        'google_place_id',
        'google_geocode_response',
        'state_id',
        'timezone_id',
    ];

    /**
     * @return BelongsTo<State, $this>
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id', 'state_id');
    }

    /**
     * @return BelongsTo<Country, $this>
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id', 'country_id');
    }

    public function getInlineFullAddressFormat(): string
    {
        $fullAddress = [];

        $addressPartOne = [];
        $addressPartTwo = [];
        $addressPartOne[] = $this->street_address;

        if ($this->unit_number) {
            $addressPartOne[] = "Unit #{$this->unit_number}";
        }
        $fullAddress[] = implode(' ', $addressPartOne);

        $addressPartTwo[] = $this->city;
        if ($this->state->state_code) {
            $addressPartTwo[] = $this->state->state_code . ' ' . $this->postal_zip_code;
        } else {
            $addressPartTwo[] = $this->postal_zip_code;
        }

        $fullAddress[] = implode(' ', $addressPartTwo);

        return implode(', ', $fullAddress);
    }

    /**
     * @return array<string,string|int>
     */
    public function getAddress(): array
    {
        return [
            'street_address' => $this->street_address,
            'apt_suite_unit' => $this->unit_number,
            'city' => $this->city,
            'state_code' => $this->state->state_code ?? '',
            'zip_code' => $this->postal_zip_code,
        ];
    }

    /**
     * @return BelongsTo<Timezone, $this>
     */
    public function timezone(): BelongsTo
    {
        return $this->belongsTo(Timezone::class, 'timezone_id');
    }

    /**
     * @return BelongsToMany<Resident, $this>
     */
    public function residents(): BelongsToMany
    {
        return $this->belongsToMany(Resident::class, 'property_residents', 'property_id', 'resident_id');
    }
}

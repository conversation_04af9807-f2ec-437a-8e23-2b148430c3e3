<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\ProblemCategory
 *
 * @property int $problem_category_id
 * @property string $problem_category_uuid
 * @property string $label
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 *
 * @method static \Database\Factories\ProblemCategoryFactory factory($count = null, $state = [])
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProblemSubCategory> $problemSubCategories
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory whereProblemCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory withoutTrashed()
 *
 * @property string $slug
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory whereProblemCategoryUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProblemCategory whereUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @property-read int|null $problem_sub_categories_count
 *
 * @mixin \Eloquent
 */
class ProblemCategory extends Model
{
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'problem_category_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'label',
        'slug',
    ];

    protected $casts = [
        'problem_category_uuid' => UuidBinary::class,
    ];

    /**
     * Get all the problem sub category of the problem category
     *
     * @return HasMany<ProblemSubCategory, $this>
     */
    public function problemSubCategories(): HasMany
    {
        return $this->hasMany(ProblemSubCategory::class, 'problem_category_id', 'problem_category_id');
    }
}

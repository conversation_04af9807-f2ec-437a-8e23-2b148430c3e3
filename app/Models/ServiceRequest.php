<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Enums\WorkOrderStatus as WorkOrderStatusEnum;
use App\Helpers\Helper;
use App\States\ServiceRequests\AwaitingAvailability;
use App\States\ServiceRequests\Closed;
use App\States\ServiceRequests\CreateWorkOrder;
use App\States\ServiceRequests\InProgress;
use App\States\ServiceRequests\ServiceRequestState;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\Filterable;
use App\Traits\GeneratesUuid;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\ModelStates\HasStates;

/**
 * App\Models\ServiceRequest
 *
 * @property int $service_request_id
 * @property string $service_request_uuid
 * @property string $service_request_number
 * @property int $organization_id
 * @property int $property_id
 * @property int|null $requesting_resident_id
 * @property int|null $property_manager_id
 * @property int $service_request_status_id
 * @property int $timezone_id
 * @property int|null $service_request_type_id
 * @property ServiceRequestState $state
 * @property string $description
 * @property string $priority
 * @property string|null $property_access_method
 * @property string|null $property_access_code
 * @property string|null $property_access_note
 * @property int|null $service_request_source_id
 * @property int|null $description_last_edited_user_id
 * @property string|null $service_request_reference_id
 * @property string|null $service_request_reference_url
 * @property CarbonImmutable|null $service_request_reference_created_at
 * @property string|null $service_request_reference_number
 * @property CarbonImmutable|null $state_updated_at
 * @property CarbonImmutable|null $resolved_at
 * @property CarbonImmutable|null $canceled_at
 * @property CarbonImmutable|null $availability_requested_at
 * @property int|null $availability_requested_user_id
 * @property int|null $availability_viewed_user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $description_last_edited_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequest whereUuid($uuid, $uuidColumn = null)
 * @method static \Illuminate\Database\Eloquent\Builder|ServiceRequest orWhereUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @property-read Organization $organization
 * @property-read Property $property
 * @property-read Resident|null $resident
 * @property-read ServiceRequestStatus|null $status
 * @property-read Timezone $timezone
 * @property-read ServiceRequestType|null $type
 * @property-read ServiceRequestSource|null $source
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ServiceRequestCategory> $categories
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Media> $media
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\WorkOrder> $workOrders
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ResidentAvailability> $residentAvailabilities
 * @property-read int|null $media_count
 *
 * @mixin \Eloquent
 */
class ServiceRequest extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use Filterable;
    use GeneratesUuid;
    use HasFactory;
    use HasStates;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'service_request_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'service_request_uuid',
        'service_request_number',
        'organization_id',
        'property_id',
        'requesting_resident_id',
        'property_manager_id',
        'service_request_status_id',
        'timezone_id',
        'service_request_type_id',
        'state',
        'description',
        'priority',
        'property_access_method',
        'property_access_code',
        'property_access_note',
        'service_request_source_id',
        'service_request_reference_id',
        'service_request_reference_url',
        'service_request_reference_created_at',
        'service_request_reference_number',
        'state_updated_at',
        'resolved_at',
        'canceled_at',
        'description_last_edited_user_id',
        'description_last_edited_at',
        'availability_requested_at',
        'availability_requested_user_id',
        'availability_viewed_user_id',
    ];

    protected $casts = [
        'service_request_uuid' => UuidBinary::class,
        'state' => ServiceRequestState::class,
        'resolved_at' => 'immutable_datetime',
        'canceled_at' => 'immutable_datetime',
        'description_last_edited_at' => 'datetime',
        'availability_requested_at' => 'immutable_datetime',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::created(function (ServiceRequest $serviceRequest) {
            $serviceRequest->service_request_number = Helper::generateRandomAlphaNumber('service_requests', 'service_request_number');
            $serviceRequest->save();
        });
    }

    public function uuidColumn(): string
    {
        return 'service_request_uuid';
    }

    /**
     * @return BelongsTo<Organization, self>
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    /**
     * @return BelongsTo<Property, self>
     */
    public function property(): BelongsTo
    {
        return $this->belongsTo(Property::class, 'property_id');
    }

    /**
     * @return BelongsTo<Resident, self>
     */
    public function resident(): BelongsTo
    {
        return $this->belongsTo(Resident::class, 'requesting_resident_id');
    }

    /**
     * @return BelongsTo<ServiceRequestStatus, self>
     */
    public function status(): BelongsTo
    {
        return $this->belongsTo(ServiceRequestStatus::class, 'service_request_status_id');
    }

    /**
     * @return BelongsTo<Timezone, self>
     */
    public function timezone(): BelongsTo
    {
        return $this->belongsTo(Timezone::class, 'timezone_id');
    }

    /**
     * @return BelongsTo<ServiceRequestType, self>
     */
    public function type(): BelongsTo
    {
        return $this->belongsTo(ServiceRequestType::class, 'service_request_type_id');
    }

    /**
     * @return BelongsTo<ServiceRequestSource, self>
     */
    public function source(): BelongsTo
    {
        return $this->belongsTo(ServiceRequestSource::class, 'service_request_source_id');
    }

    /**
     * @return BelongsTo<User,self>
     */
    public function updatedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'description_last_edited_user_id');
    }

    /**
     * @return HasMany<ServiceRequestCategory>
     */
    public function categories(): HasMany
    {
        return $this->hasMany(ServiceRequestCategory::class, 'service_request_id', 'service_request_id');
    }

    /**
     * @return HasMany<ServiceRequestAssignee>
     */
    public function assignees(): HasMany
    {
        return $this->hasMany(ServiceRequestAssignee::class, 'service_request_id', 'service_request_id');
    }

    /**
     * @return BelongsToMany<Media>
     */
    public function media(): BelongsToMany
    {
        return $this->belongsToMany(Media::class, 'service_request_media', 'service_request_id', 'media_id')
            ->withPivot('media_type', 'has_thumbnail', 'has_upload_completed');
    }

    /**
     * Get the availabilities of resident.
     *
     * @return HasMany<ResidentAvailability>
     */
    public function residentAvailabilities(): HasMany
    {
        return $this->hasMany(ResidentAvailability::class, 'service_request_id', 'service_request_id');
    }

    /**
     * @return HasMany<WorkOrder>
     */
    public function workOrders(): HasMany
    {
        return $this->hasMany(WorkOrder::class, 'service_request_id', 'service_request_id');
    }

    /**
     * @return HasMany<ServiceRequestDescription>
     */
    public function serviceRequestDescriptions(): HasMany
    {
        return $this->hasMany(ServiceRequestDescription::class, 'service_request_id', 'service_request_id');
    }

    /**
     * @return HasOne<ServiceRequestDescription>
     */
    public function latestDescription(): HasOne
    {
        return $this->hasOne(ServiceRequestDescription::class, 'service_request_id')->latestOfMany('created_at');
    }

    /**
     * Get all issues related to this service request.
     *
     * @return HasMany<Issue>
     */
    public function issues(): HasMany
    {
        return $this->hasMany(Issue::class, 'service_request_id', 'service_request_id');
    }

    /**
     * @return array<int, string|null>
     */
    public function resolveStateAbilities(string $fromSource = 'web'): array
    {
        $abilities = $this->state->actions();
        $mobileAllowedAbilities = [];

        $abilities[] = 'request_availability';
        $abilities[] = 'go_to_work_order';

        if (! $this->state->equals(Closed::class)) {
            $abilities[] = 'create_work_order';
            $abilities[] = 'create_issue';
        }
        if ($this->state->equals(AwaitingAvailability::class)) {
            $abilities[] = 'add_availability';
        }
        if ($this->state->equals(InProgress::class)) {
            $workOrders = $this->workOrders->load('status');
            // Check if all work orders are either Resolved or Canceled
            $allResolvedOrCanceled = $workOrders->every(fn ($workOrder) => in_array($workOrder->status->slug, [WorkOrderStatusEnum::COMPLETED(), WorkOrderStatusEnum::CANCELED()])

            );

            // Remove the 'Closed' action if not all work orders are in terminal status Resolved or Canceled
            if (! $allResolvedOrCanceled) {
                $abilities = array_filter($abilities, fn ($action) => $action !== Closed::$actionName);
            }
        }
        if ($this->state->equals(CreateWorkOrder::class)) {

            $abilities[] = 'request_availability';
            $abilities[] = 'edit_work_order';
            $abilities[] = 'create_work_order';
        }

        return strtolower($fromSource) === 'mobile'
            ? collect($mobileAllowedAbilities)->values()->all()
            : collect($abilities)->values()->all();
    }
}

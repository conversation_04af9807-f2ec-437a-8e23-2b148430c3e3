<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\AppfolioApiLog
 *
 * @property int $appfolio_api_log_id
 * @property string|null $appfolio_work_order_uuid
 * @property string $log_type
 * @property string $appfolio_entity_type
 * @property string|null $appfolio_entity_uuid
 * @property string $api_url
 * @property string $method
 * @property json|null|mixed $payload
 * @property json|null|mixed $response
 * @property int $status_code
 * @property \Illuminate\Support\Carbon|null $last_updated_at
 * @property \Illuminate\Support\Carbon|null $created_at
 */
class AppfolioApiLog extends Model
{
    /**
     * The name of the "updated at" column.
     *
     * @var string|null
     */
    const UPDATED_AT = null;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'appfolio_api_log_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'appfolio_work_order_uuid',
        'log_type',
        'appfolio_entity_type',
        'appfolio_entity_uuid',
        'api_url',
        'method',
        'payload',
        'response',
        'status_code',
        'last_updated_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'payload' => 'json',
        'response' => 'json',
        'last_updated_at' => 'date',
    ];
}

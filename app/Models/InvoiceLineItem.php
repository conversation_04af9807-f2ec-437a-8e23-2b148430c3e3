<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\InvoiceLineItem
 *
 * @property int $invoice_line_item_id
 * @property string $invoice_line_item_uuid
 * @property int $invoice_id
 * @property int $work_order_service_call_id
 * @property string|null $description
 * @property string|null $external_invoice_url
 * @property string $invoice_line_item_type
 * @property int|string $quote_task_id
 * @property int|string $quote_id
 * @property int $total_markup_fee_in_cents
 * @property int $total_cost_in_cents
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @mixin \Eloquent
 */
class InvoiceLineItem extends Model
{
    use GeneratesUuid;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'invoice_line_item_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'invoice_line_item_uuid', 'invoice_id', 'work_order_service_call_id', 'description', 'invoice_line_item_type',
        'quote_task_id', 'quote_id', 'total_markup_fee_in_cents', 'cost_in_cents', 'total_cost_in_cents', 'external_invoice_url',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'invoice_line_item_uuid' => UuidBinary::class,
    ];

    public function uuidColumn(): string
    {
        return 'invoice_line_item_uuid';
    }

    /**
     * @return BelongsTo<Invoice, $this>
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, 'invoice_id');
    }

    /**
     * @return BelongsTo<WorkOrderServiceCall, $this>
     */
    public function serviceCall(): BelongsTo
    {
        return $this->belongsTo(WorkOrderServiceCall::class, 'work_order_service_call_id');
    }

    /**
     * @return BelongsTo<QuoteTask, $this>
     */
    public function quoteTask(): BelongsTo
    {
        return $this->belongsTo(QuoteTask::class, 'quote_task_id');
    }

    /**
     * @return BelongsTo<Quote, $this>
     */
    public function quote(): BelongsTo
    {
        return $this->belongsTo(Quote::class, 'quote_id');
    }

    /**
     * @return HasMany<InvoiceLineItemSubsidiary, $this>
     */
    public function subsidiaries(): HasMany
    {
        return $this->hasMany(InvoiceLineItemSubsidiary::class, 'invoice_line_item_id');
    }
}

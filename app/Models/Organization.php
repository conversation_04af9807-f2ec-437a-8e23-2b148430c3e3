<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Packages\OrganizationRolePermission\OrganizationManager;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Spatie\Multitenancy\Models\Tenant;

/**
 * App\Models\Organization
 *
 * @property int $organization_id
 * @property mixed $organization_uuid
 * @property string $name
 * @property string $domain_type
 * @property string $domain
 * @property string|null $user_pool_id
 * @property string|null $user_pool_app_client_id
 * @property string|null $user_pool_api_client_id
 * @property string|null $user_pool_api_client_secret
 * @property string|null $template
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Account> $accounts
 * @property-read int|null $accounts_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Feature> $features
 * @property-read int|null $features_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User> $users
 * @property-read int|null $users_count
 *
 * @method static \Spatie\Multitenancy\TenantCollection<int, static> all($columns = ['*'])
 * @method static \Spatie\Multitenancy\TenantCollection<int, static> get($columns = ['*'])
 * @method static \Illuminate\Database\Eloquent\Builder|Organization newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Organization newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Organization query()
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereDomain($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereDomainType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereTemplate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUserPoolApiClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUserPoolApiClientSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUserPoolAppClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUserPoolId($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Role> $roles
 * @property-read int|null $roles_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereOrganizationUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUuid($uuid, $uuidColumn = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereNotUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @property string $public_api_enabled
 * @property string|null $user_pool_public_api_client_id
 * @property string|null $user_pool_public_api_client_secret
 * @property string|null $user_pool_public_api_client_endpoint
 * @property string|null $webhook_secret_key
 * @property string $webhook_enabled
 * @property string|null $webhook_api_url
 * @property string|null $appfolio_client_id
 * @property string|null $appfolio_client_secret
 * @property string|null $appfolio_vendor_id
 * @property string|null $appfolio_customer_id
 * @property string $is_appfolio_enabled
 * @property OrganizationVendor $vendorSettings
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Organization wherePublicApiEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUserPoolPublicApiClientEndpoint($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUserPoolPublicApiClientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUserPoolPublicApiClientSecret($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereWebhookApiUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereWebhookEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereWebhookSecretKey($value)
 *
 * @mixin \Eloquent
 */
class Organization extends Tenant
{
    use BindsOnUuid;
    use GeneratesUuid;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'organization_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'name',
        'domain_type',
        'domain',
        'user_pool_id',
        'user_pool_app_client_id',
        'user_pool_api_client_id',
        'user_pool_api_client_secret',
        'user_pool_domain',
        'public_api_enabled',
        'user_pool_public_api_client_id',
        'user_pool_public_api_client_secret',
        'user_pool_public_api_client_endpoint',
        'webhook_secret_key',
        'webhook_enabled',
        'webhook_api_url',
        'template',
        'appfolio_client_id',
        'appfolio_client_secret',
        'appfolio_vendor_id',
        'appfolio_integrated_at',
        'logo_file_name',
        'street_address',
        'city',
        'zip_code',
        'state_id',
        'country_id',
        'phone_number',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'organization_uuid' => UuidBinary::class,
        'appfolio_client_id' => 'encrypted',
        'appfolio_client_secret' => 'encrypted',
        'appfolio_integrated_at' => 'immutable_datetime',

        'user_pool_api_client_secret' => 'encrypted',
        'user_pool_public_api_client_secret' => 'encrypted',
        'webhook_secret_key' => 'encrypted',
    ];

    /**
     * Get all accounts of the organization.
     *
     * @return HasMany<Account, $this>
     */
    public function accounts(): HasMany
    {
        return $this->hasMany(Account::class, 'organization_id', 'organization_id');
    }

    /**
     * Get all accounts of the organization.
     *
     * @return HasMany<User, $this>
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'organization_id', 'organization_id');
    }

    /**
     * @return HasMany<Role, $this>
     */
    public function roles(): HasMany
    {
        return $this->hasMany(Role::class, 'organization_id', 'organization_id');
    }

    /**
     * Get the features associated to organization
     *
     * @return BelongsToMany<Feature, $this>
     */
    public function features(): BelongsToMany
    {
        return $this->belongsToMany(Feature::class, 'organization_feature', 'organization_id', 'feature_id');
    }

    /**
     * @return HasMany<OrganizationIdentityProvider, $this>
     */
    public function identityProviders(): HasMany
    {
        return $this->hasMany(OrganizationIdentityProvider::class, 'organization_id', 'organization_id');
    }

    /**
     * @return Collection<int, Feature>
     */
    public function getCachedFeatures(): Collection
    {
        if (app()->runningInConsole()) {
            app(OrganizationManager::class)->forgetCachedPermissions();
        }

        return app(OrganizationManager::class)
            ->setOrganization($this)
            ->getFeatures();
    }

    /**
     * @param  array<string>  $params
     * @return Collection<int, Permission>
     */
    public function getCachedPermissions(array $params = []): Collection
    {
        return app(OrganizationManager::class)
            ->setOrganization($this)
            ->getPermissions($params);
    }

    /**
     * @param  array<string>  $params
     * @return Collection<int, Role>
     */
    public function getCachedRoles(array $params = []): Collection
    {
        return app(OrganizationManager::class)
            ->setOrganization($this)
            ->getRoles($params);
    }

    public function getMediaPathPrefix(): string
    {
        return "organizations/{$this->domain}";
    }

    /**
     * @return BelongsTo<State, $this>
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    /**
     * @return BelongsTo<Country, $this>
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function logoUrl(): string
    {
        $expirationTime = config('media.cache.temporary_file_cache_expire_in_seconds');

        return Cache::remember("{$this->organization_uuid}_logo", $expirationTime, function () use ($expirationTime) {
            return Storage::temporaryUrl(
                ($this->getMediaPathPrefix() . "/{$this->logo_file_name}"),
                now()->seconds($expirationTime)
            );
        });

    }

    /**
     * @return HasMany<OrganizationVendor, $this>
     */
    public function vendorSettings(): HasMany
    {
        return $this->hasMany(OrganizationVendor::class, 'organization_id', 'organization_id');
    }

    /**
     * @return BelongsToMany<Vendor, $this>
     */
    public function vendors(): BelongsToMany
    {
        return $this->belongsToMany(Vendor::class, 'organization_vendors', 'organization_id', 'vendor_id');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AppfolioIntegrationSkippedServiceRequest extends Model
{
    use HasFactory;

    /**
     * App\Models\AppfolioIntegrationSkippedServiceRequest
     *
     * @property int $appfolio_integration_skipped_service_request_id
     * @property int $organization_id
     * @property int $service_request_id
     * @property string $appfolio_work_order_uuid
     * @property string $appfolio_work_order_number
     * @property string $appfolio_work_order_status
     * @property string $appfolio_vendor_id
     * @property \Illuminate\Support\Carbon|null $foresight_service_request_created_at
     * @property \Illuminate\Support\Carbon|null $notified_at
     * @property \Illuminate\Support\Carbon|null $created_at
     *
     * @mixin \Eloquent
     */

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'appfolio_integration_skipped_service_request_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'organization_id',
        'service_request_id',
        'appfolio_work_order_uuid',
        'appfolio_work_order_number',
        'appfolio_work_order_status',
        'appfolio_vendor_id',
        'foresight_service_request_created_at',
        'notified_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'notified_at' => 'date',
        'foresight_service_request_created_at' => 'date',
    ];
}

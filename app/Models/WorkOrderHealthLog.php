<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\WorkOrderHealthLog
 *
 * @property int $work_order_health_status_id
 * @property int $work_order_health_logs_id
 * @property int $work_order_id
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthLog whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthLog whereSlug($value)
 *
 * @mixin \Eloquent
 */
class WorkOrderHealthLog extends Model
{
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_health_logs_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'work_order_id',
        'work_order_health_tracker_id',
        'resolved_at',
    ];

    /**
     * @return BelongsTo<WorkOrderHealthTracker, $this>
     */
    public function healthTracker(): BelongsTo
    {
        return $this->belongsTo(WorkOrderHealthTracker::class, 'work_order_health_tracker_id');
    }

    /**
     * @return BelongsTo<WorkOrder, $this>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id');
    }
}

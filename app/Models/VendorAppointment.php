<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Database\Factories\VendorAppointmentFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\VendorAppointment
 *
 * @property int $vendor_appointment_id
 * @property string $vendor_appointment_uuid
 * @property int $organization_id
 * @property int|null $vendor_id
 * @property string|null $vendor_instructions
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @method static \Database\Factories\VendorAppointmentFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment query()
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment whereVendorAppointmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment whereVendorId($value)
 *
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment whereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment withoutTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorAppointment whereVendorAppointmentUuid($value)
 *
 * @mixin \Eloquent
 */
class VendorAppointment extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;

    /** @use HasFactory<VendorAppointmentFactory> */
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'vendor_appointment_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'organization_id',
        'vendor_id',
        'vendor_instructions',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'vendor_appointment_uuid' => UuidBinary::class,
    ];

    /**
     * @return HasMany<VendorAllocation, $this>
     */
    public function vendorAllocations(): HasMany
    {
        return $this->hasMany(VendorAllocation::class, 'vendor_appointment_id', 'vendor_appointment_id');
    }

    /**
     * @return BelongsTo<Vendor, $this>
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }
}

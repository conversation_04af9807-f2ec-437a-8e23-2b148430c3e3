<?php

namespace App\Models;

use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\WorkOrderLog
 *
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderLog onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderLog whereUuid($uuid, $uuidColumn = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderLog withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderLog withoutTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderLog orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderLog orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderLog whereNotUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @mixin \Eloquent
 */
class WorkOrderLog extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_log_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'updated_by_user_id',
        'status_id',
        'sub_status_id',
        'description',
    ];
}

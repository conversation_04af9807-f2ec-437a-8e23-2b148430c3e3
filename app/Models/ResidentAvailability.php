<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\ResidentAvailability
 *
 * @property int $resident_availability_id
 * @property int $work_order_id
 * @property int $service_request_id
 * @property int $work_order_task_id
 * @property int $organization_id
 * @property int $user_id
 * @property string $timing
 * @property CarbonImmutable $availability_date
 * @property string $day_passed
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read WorkOrder $workOrder
 * @property-read ServiceRequest $serviceRequest
 *
 *  @mixin \Eloquent
 */
class ResidentAvailability extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'resident_availability_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'work_order_id',
        'work_order_task_id',
        'service_request_id',
        'organization_id',
        'user_id',
        'timing',
        'availability_date',
        'day_passed',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'resident_availability_uuid' => UuidBinary::class,
        'availability_date' => 'immutable_datetime',
    ];

    /**
     * @return BelongsTo<WorkOrder, self>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id');
    }

    /**
     * @return BelongsTo<ServiceRequest, self>
     */
    public function serviceRequest(): BelongsTo
    {
        return $this->belongsTo(ServiceRequest::class, 'service_request_id');
    }
}

<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\Filterable;
use App\Traits\GeneratesUuid;
use Carbon\CarbonImmutable;
use Database\Factories\QuoteFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Quote
 *
 * @property int $quote_id
 * @property string $quote_uuid
 * @property int $organization_id
 * @property int $work_order_id
 * @property int $work_order_task_id
 * @property int|null $work_order_service_call_id
 * @property string $status
 * @property int|null $submitted_user_id
 * @property int|null $approved_user_id
 * @property int|null $rejected_user_id
 * @property int|null $reviewed_user_id
 * @property int|null $last_modified_user_id
 * @property CarbonImmutable|null $submitted_at
 * @property CarbonImmutable|null $approved_at
 * @property CarbonImmutable|null $rejected_at
 * @property CarbonImmutable|null $reviewed_at
 * @property CarbonImmutable|null $last_modified_at
 * @property CarbonImmutable $created_at
 * @property CarbonImmutable $updated_at
 * @property CarbonImmutable|null $deleted_at
 * @property int $quote_number
 * @property int|null $last_assigned_service_call_id
 *
 * @mixin \Eloquent
 */
class Quote extends Model
{
    use BindsOnUuid;
    use Filterable;
    use GeneratesUuid;

    /** @use HasFactory<QuoteFactory> */
    use HasFactory;
    use SoftDeletes;
    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'quote_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'organization_id',
        'work_order_id',
        'work_order_task_id',
        'work_order_service_call_id',
        'status',
        'submitted_user_id',
        'approved_user_id',
        'rejected_user_id',
        'reviewed_user_id',
        'last_modified_user_id',
        'submitted_at',
        'approved_at',
        'rejected_at',
        'reviewed_at',
        'last_modified_at',
        'last_assigned_service_call_id',
        'quote_number',

    ];

    protected $casts = [
        'quote_uuid' => UuidBinary::class,
        'created_at' => 'immutable_datetime',
        'updated_at' => 'immutable_datetime',
        'submitted_at' => 'immutable_datetime',
        'approved_at' => 'immutable_datetime',
        'rejected_at' => 'immutable_datetime',
        'reviewed_at' => 'immutable_datetime',
        'last_modified_at' => 'immutable_datetime',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::created(function (Quote $quote) {
            $count = Quote::where('work_order_id', $quote->work_order_id)->count();
            $quote->quote_number = $count;
            $quote->save();
        });
    }

    public function uuidColumn(): string
    {
        return 'quote_uuid';
    }

    /**
     * Get all the quote task associated with the quote.
     *
     * @return HasMany<QuoteTask, $this>
     */
    public function quoteTasks(): HasMany
    {
        return $this->hasMany(QuoteTask::class, 'quote_id', 'quote_id');
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function submittedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'submitted_user_id', 'user_id');
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function approvedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_user_id', 'user_id');
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function rejectedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'rejected_user_id', 'user_id');
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function reviewedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_user_id', 'user_id');
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function lastModifiedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'last_modified_user_id', 'user_id');
    }

    /**
     * Get all service calls.
     *
     * @return HasMany<WorkOrderServiceCall, $this>
     */
    public function serviceCalls(): HasMany
    {
        return $this->hasMany(WorkOrderServiceCall::class, 'quote_id', 'quote_id');
    }

    /**
     * @return BelongsTo<WorkOrderServiceCall, $this>
     */
    public function serviceCall(): BelongsTo
    {
        return $this->belongsTo(WorkOrderServiceCall::class, 'work_order_service_call_id', 'work_order_service_call_id');
    }

    /**
     * @return BelongsTo<WorkOrder, $this>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id', 'work_order_id');
    }

    /**
     * @return BelongsTo<WorkOrderTask, $this>
     */
    public function workOrderTask(): BelongsTo
    {
        return $this->belongsTo(WorkOrderTask::class, 'work_order_task_id', 'work_order_task_id');
    }

    /**
     * Get all the quote task associated with the quote.
     *
     * @return HasMany<WorkOrderServiceCall, $this>
     */
    public function assignedServiceCalls(): HasMany
    {
        return $this->hasMany(WorkOrderServiceCall::class, 'quote_id', 'quote_id');
    }

    /**
     * @return BelongsTo<WorkOrderServiceCall, $this>
     */
    public function latestAssignedServiceCall(): belongsTo
    {
        return $this->belongsTo(WorkOrderServiceCall::class, 'last_assigned_service_call_id', 'work_order_service_call_id');
    }
}

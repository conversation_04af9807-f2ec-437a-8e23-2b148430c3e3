<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Helpers\Helper;
use App\States\Invoices\InvoiceState;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\ModelStates\HasStates;

/**
 * App\Models\Invoice
 *
 * @property int $invoice_id
 * @property string $invoice_uuid
 * @property string $invoice_number
 * @property int $work_order_id
 * @property int $organization_id
 * @property int|null $created_by_user_id
 * @property string $description
 * @property int $cost_in_cents
 * @property int $total_markup_fee_in_cents
 * @property int $total_cost_in_cents
 * @property int $amount_paid_in_cents
 * @property InvoiceState $state
 * @property int|null $drafted_by_user_id
 * @property CarbonImmutable|null $drafted_at
 * @property CarbonImmutable|null $state_updated_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read Organization $organization
 * @property-read WorkOrder $workOrder
 *
 * @mixin \Eloquent
 */
class Invoice extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use HasStates;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'invoice_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'invoice_uuid', 'work_order_id', 'organization_id', 'invoice_number', 'description', 'total_markup_fee_in_cents',
        'total_cost_in_cents', 'amount_paid_in_cents', 'state', 'state_updated_at', 'created_by_user_id', 'drafted_at', 'drafted_by_user_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'invoice_uuid' => UuidBinary::class,
        'state' => InvoiceState::class,
        'state_updated_at' => 'immutable_datetime',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        static::creating(function (Invoice $invoice) {
            $invoice->invoice_number = Helper::generateRandomAlphaNumber('invoices', 'invoice_number');
        });
    }

    public function uuidColumn(): string
    {
        return 'invoice_uuid';
    }

    /**
     * @return BelongsTo<Organization, $this>
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }

    /**
     * @return BelongsTo<WorkOrder, $this>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id');
    }

    /**
     * @return HasMany<InvoiceLineItem, $this>
     */
    public function lineItems(): HasMany
    {
        return $this->hasMany(InvoiceLineItem::class, 'invoice_id');
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function createdByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by_user_id');
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function draftedByUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'drafted_by_user_id');
    }

    /**
     * Total markup fee in dollar
     */
    protected function getTotalMarkupFeeAttribute(): string
    {
        $totalMarkupFee = ! empty($this->total_markup_fee_in_cents) ? ($this->total_markup_fee_in_cents / 100) : 0;

        return number_format($totalMarkupFee, 2, '.', '');
    }

    /**
     * Total cost in dollar
     */
    protected function getTotalCostAttribute(): string
    {
        $totalCost = ! empty($this->total_cost_in_cents) ? (($this->total_cost_in_cents - $this->total_markup_fee_in_cents) / 100) : 0;

        return number_format($totalCost, 2, '.', '');
    }

    /**
     * Cost in dollar
     */
    protected function getCostAttribute(): string
    {
        $cost = ! empty($this->total_cost_in_cents) ? (($this->total_cost_in_cents - $this->total_markup_fee_in_cents) / 100) : 0;

        return number_format($cost, 2, '.', '');
    }
}

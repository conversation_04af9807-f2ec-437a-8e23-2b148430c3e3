<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\UserWorkOrderBookmark
 *
 * @property int $user_work_order_bookmark_id
 * @property int|null $user_id
 * @property int|null $work_order_id
 *
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkOrderBookmark newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkOrderBookmark newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkOrderBookmark query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkOrderBookmark whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkOrderBookmark whereUserWorkOrderBookmarkId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserWorkOrderBookmark whereWorkOrderId($value)
 *
 * @mixin \Eloquent
 */
class UserWorkOrderBookmark extends Model
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'user_work_order_bookmark_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'user_id',
        'work_order_id',
    ];
}

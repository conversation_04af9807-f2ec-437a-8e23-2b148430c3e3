<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\WorkOrderStatusLog
 *
 * @property int $work_order_status_log_id
 * @property int $work_order_id
 * @property int $updated_by_user_id
 * @property int $work_order_status_id
 * @property int|null $sub_status_id
 * @property string|null $description
 * @property string|null $created_at
 * @property-read WorkOrder $workOrder
 * @property-read WorkOrderStatus $workOrderStatus
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatusLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatusLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatusLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatusLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatusLog whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatusLog whereSubStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatusLog whereUpdatedByUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatusLog whereWorkOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatusLog whereWorkOrderStatusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatusLog whereWorkOrderStatusLogId($value)
 *
 * @mixin \Eloquent
 */
class WorkOrderStatusLog extends Model
{
    public $timestamps = false;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_status_log_id';

    protected $dateFormat = 'Y-m-d H:i:s.u';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'work_order_id',
        'updated_by_user_id',
        'work_order_status_id',
        'sub_status_id',
        'description',
    ];

    /**
     * @return BelongsTo<WorkOrder, self>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id');
    }

    /**
     * @return BelongsTo<WorkOrderStatus, self>
     */
    public function workOrderStatus(): BelongsTo
    {
        return $this->belongsTo(WorkOrderStatus::class, 'work_order_status_id');
    }
}

<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\VendorUser
 *
 * @property int $vendor_user_id
 * @property string $vendor_user_uuid
 * @property int $vendor_id
 * @property int|null $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read Vendor $vendor
 * @property-read User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder|VendoUser orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|VendoUser whereMediaUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|VendoUser whereUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @mixin \Eloquent
 */
class VendorUser extends Model
{
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'vendor_users';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'vendor_user_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'vendor_user_uuid',
        'vendor_id',
        'user_id',
    ];

    protected $casts = [
        'vendor_user_uuid' => UuidBinary::class,
    ];

    /**
     * @return BelongsTo<Vendor, self>
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }

    /**
     * @return BelongsTo<User, self>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}

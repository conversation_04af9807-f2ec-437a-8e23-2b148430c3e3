<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class QuoteStatus extends Model
{
    public $timestamps = false;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'quote_status_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'label',
        'slug',
        'status_color',
    ];
}

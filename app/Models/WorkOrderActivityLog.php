<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\WorkOrderActivityLog
 *
 * @property string|null $work_order_activity_log_uuid
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog withoutTrashed()
 *
 * @property int $work_order_activity_log_id
 * @property int $work_order_id
 * @property int $work_order_task_id
 * @property int $organization_id
 * @property int|null $triggered_by
 * @property string $event
 * @property array $event_attributes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereEvent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereEventAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereTriggeredBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereWorkOrderActivityLogId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereWorkOrderActivityLogUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereWorkOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderActivityLog whereWorkOrderTaskId($value)
 *
 * @property-read User|null $triggeredBy
 *
 * @mixin \Eloquent
 */
class WorkOrderActivityLog extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_activity_log_id';

    protected $dateFormat = 'Y-m-d H:i:s.u';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'work_order_id',
        'work_order_task_id',
        'organization_id',
        'triggered_by',
        'event',
        'event_attributes',
    ];

    protected $casts = [
        'work_order_activity_log_uuid' => UuidBinary::class,
        'event_attributes' => 'array',
    ];

    /**
     * @return BelongsTo<User, $this>
     */
    public function triggeredBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'triggered_by', 'user_id');
    }

    /**
     * @return BelongsTo<WorkOrder, self>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id', 'work_order_id');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\WorkOrderStatus
 *
 * @property int $work_order_status_id
 * @property string $label
 * @property string $slug
 * @property string|null $status_color
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatus whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatus whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatus whereStatusColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatus whereWorkOrderStatusId($value)
 *
 * @property int $sort_order
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderStatus whereSortOrder($value)
 *
 * @mixin \Eloquent
 */
class WorkOrderStatus extends Model
{
    use HasFactory;

    public $timestamps = false;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_status_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'label',
        'slug',
        'status_color',
    ];

    public function workOrders()
    {
        return $this->hasMany(WorkOrder::class, 'work_order_status_id');
    }
}

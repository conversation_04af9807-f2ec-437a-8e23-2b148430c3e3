<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Technician
 *
 * @property int $technician_id
 * @property int|null $user_id
 * @property string|null $technician_uuid
 * @property int $organization_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read Organization|null $organization
 * @property-read User|null $user
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\TechnicianWorkingHour> $workingHours
 * @property-read int|null $working_hours_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Technician newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Technician newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Technician onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Technician orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician query()
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereTechnicianId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereTechnicianUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Technician withoutTrashed()
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\TechnicianSkill> $skills
 * @property-read int|null $skills_count
 * @property string|null $latitude
 * @property string|null $longitude
 * @property int|null $max_travel_distance
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Technician whereMaxTravelDistance($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProblemDiagnosis> $problemDiagnoses
 * @property-read int|null $problem_diagnoses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\TechnicianAppointment> $appointments
 * @property-read int|null $appointments_count
 *
 * @mixin \Eloquent
 */
class Technician extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'technician_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'organization_id',
        'user_id',
        'latitude',
        'longitude',
        'max_travel_distance',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'technician_uuid' => UuidBinary::class,
    ];

    public function uuidColumn(): string
    {
        return 'technician_uuid';
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the working hours related to the technician.
     *
     * @return HasMany<\App\Models\TechnicianWorkingHour, $this>
     */
    public function workingHours(): HasMany
    {
        return $this->hasMany(TechnicianWorkingHour::class, 'technician_id', 'technician_id');
    }

    /**
     * Get all skills of the technician.
     *
     * @return HasMany<TechnicianSkill, $this>
     */
    public function skills(): HasMany
    {
        return $this->hasMany(TechnicianSkill::class, 'technician_id', 'technician_id');
    }

    /**
     * Get all skills of the technician.
     *
     * @return HasMany<TechnicianAppointment, $this>
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(TechnicianAppointment::class, 'technician_id', 'technician_id');
    }

    /**
     * @return BelongsToMany<ProblemDiagnosis, $this>
     */
    public function problemDiagnoses(): BelongsToMany
    {
        return $this->belongsToMany(ProblemDiagnosis::class, 'technician_skills', 'technician_id', 'problem_diagnosis_id');
    }
}

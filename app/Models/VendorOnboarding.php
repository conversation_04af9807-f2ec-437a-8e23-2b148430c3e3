<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\VendorOnboarding
 *
 * @property int $vendor_onboarding_id
 * @property string $vendor_onboarding_uuid
 * @property string $type
 * @property int $vendor_onboarding_status_id
 * @property int $vendor_id
 * @property int|null $user_id
 * @property int $organization_id
 * @property int $access_count
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read VendorOnboardingStatus $status
 * @property-read Vendor $vendor
 * @property-read User $user
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|VendorOnboarding orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorOnboarding whereMediaUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|VendorOnboarding whereUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @mixin \Eloquent
 */
class VendorOnboarding extends Model
{
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    protected $table = 'vendor_onboardings';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'vendor_onboarding_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'vendor_onboarding_uuid',
        'type',
        'vendor_onboarding_status_id',
        'vendor_id',
        'user_id',
        'organization_id',
        'access_count',
    ];

    protected $casts = [
        'vendor_onboarding_uuid' => UuidBinary::class,
    ];

    public function uuidColumn(): string
    {
        return 'vendor_onboarding_uuid';
    }

    /**
     * @return BelongsTo<VendorOnboardingStatus, self>
     */
    public function status(): BelongsTo
    {
        return $this->belongsTo(VendorOnboardingStatus::class, 'vendor_onboarding_status_id');
    }

    /**
     * @return BelongsTo<Vendor, self>
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class, 'vendor_id');
    }

    /**
     * @return BelongsTo<User, self>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * @return BelongsTo<Organization, self>
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }
}

<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\WorkOrderMedia
 *
 * @property int $service_request_media_id
 * @property string|null $service_request_media_uuid
 * @property int $media_id
 * @property int $organization_id
 * @property int $service_request_id
 * @property int $user_id
 * @property string $media_type
 * @property string $has_thumbnail
 * @property string $has_optimized
 * @property string $has_upload_completed
 * @property int $work_order_service_call_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read Organization $organization
 * @property-read Media $media
 * @property-read ServiceRequest $serviceRequest
 *
 * @mixin \Eloquent
 */
class ServiceRequestMedia extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'service_request_media_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'service_request_media_uuid',
        'media_id',
        'organization_id',
        'service_request_id',
        'user_id',
        'media_type',
        'has_thumbnail',
        'has_optimized',
        'has_upload_completed',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'service_request_media_uuid' => UuidBinary::class,
    ];

    /**
     * @return BelongsTo<Media, self>
     */
    public function media(): BelongsTo
    {
        return $this->belongsTo(Media::class, 'media_id', 'media_id');
    }

    /**
     * @return BelongsTo<ServiceRequest, self>
     */
    public function serviceRequest(): BelongsTo
    {
        return $this->belongsTo(ServiceRequest::class, 'service_request_id', 'service_request_id');
    }
}

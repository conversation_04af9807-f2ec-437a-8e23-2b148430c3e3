<?php

namespace App\Models;

use App\Traits\BelongsToOrganization;
use Database\Factories\WorkOrderTaskLogFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\WorkOrderTaskLog
 *
 * @property int $work_order_task_log_id
 * @property int $organization_id
 * @property int $work_order_task_id
 * @property mixed $event
 * @property string|null $deleted_at
 *
 * @method static \Database\Factories\WorkOrderTaskLogFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTaskLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTaskLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTaskLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTaskLog whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTaskLog whereEvent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTaskLog whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTaskLog whereWorkOrderTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTaskLog whereWorkOrderTaskLogId($value)
 *
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTaskLog onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTaskLog withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderTaskLog withoutTrashed()
 *
 * @mixin \Eloquent
 */
class WorkOrderTaskLog extends Model
{
    use BelongsToOrganization;

    /** @use HasFactory<WorkOrderTaskLogFactory> */
    use HasFactory;
    use SoftDeletes;

    public $timestamps = false;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_status_log_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'work_order_task_log_id',
        'organization_id',
        'work_order_task_id',
        'event',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'event' => 'object',
    ];
}

<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\InvoiceLineItemSubsidiary
 *
 * @property int $invoice_line_item_subsidiary_id
 * @property string $invoice_line_item_subsidiary_uuid
 * @property int $invoice_line_item_id
 * @property int $work_order_service_call_id
 * @property int|null $work_order_task_material_id
 * @property int|null $quote_task_material_id
 * @property int|null $quote_task_id
 * @property string $subsidiary_type
 * @property string|null $description
 * @property int|null $hourly_rate_in_cents
 * @property int|null $duration_in_seconds
 * @property int|null $quantity
 * @property string $quantity_type
 * @property string $markup_fee_type
 * @property int|null $markup_fee_type_value
 * @property int|null $markup_fee_in_cents
 * @property int|null $cost_in_cents
 * @property int|null $total_cost_in_cents
 * @property string $cost_type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @mixin \Eloquent
 */
class InvoiceLineItemSubsidiary extends Model
{
    use GeneratesUuid;
    use SoftDeletes;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'invoice_line_item_subsidiaries';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'invoice_line_item_subsidiary_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'invoice_line_item_subsidiary_uuid', 'invoice_line_item_id', 'work_order_task_material_id',
        'quote_task_material_id', 'subsidiary_type', 'description', 'hourly_rate_in_cents', 'duration_in_seconds',
        'markup_fee_type', 'markup_fee_type_value', 'markup_fee_in_cents', 'cost_in_cents', 'total_cost_in_cents',
        'quantity', 'quantity_type', 'quote_task_id', 'cost_type',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'invoice_line_item_subsidiary_uuid' => UuidBinary::class,
    ];

    public function uuidColumn(): string
    {
        return 'invoice_line_item_subsidiary_uuid';
    }

    /**
     * @return BelongsTo<InvoiceLineItem, $this>
     */
    public function invoiceLineItem(): BelongsTo
    {
        return $this->belongsTo(InvoiceLineItem::class, 'invoice_line_item_id');
    }

    /**
     * @return BelongsTo<WorkOrderTaskMaterial, $this>
     */
    public function workOrderTaskMaterial(): BelongsTo
    {
        return $this->belongsTo(WorkOrderTaskMaterial::class, 'work_order_task_material_id');
    }

    /**
     * @return BelongsTo<QuoteTaskMaterial, $this>
     */
    public function quoteTaskMaterial(): BelongsTo
    {
        return $this->belongsTo(QuoteTaskMaterial::class, 'quote_task_material_id');
    }

    /**
     * @return BelongsTo<QuoteTask, $this>
     */
    public function quoteTask(): BelongsTo
    {
        return $this->belongsTo(QuoteTask::class, 'quote_task_id');
    }
}

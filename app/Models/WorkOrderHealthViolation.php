<?php

namespace App\Models;

use App\Casts\UuidBinary;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\WorkOrderHealthViolation
 *
 * @property int $work_order_health_violation_id
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthViolation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthViolation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthViolation query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthViolation whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthViolation whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthViolation whereStatusColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderHealthViolation whereWorkOrderStatusId($value)
 *
 * @mixin \Eloquent
 */
class WorkOrderHealthViolation extends Model
{
    use SoftDeletes;
    protected $table = 'work_order_health_violations';
    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_health_violation_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'label',
        'slug',
        'created_by',
        'work_order_health_violation_id',

    ];
    protected $casts = [
        'work_order_health_uuid' => UuidBinary::class,
    ];

    public function uuidColumn(): string
    {
        return 'work_order_violation_uuid';
    }

    /**
     * @return HasMany<WorkOrderHealthTracker, $this>
     */
    public function healthTrackers(): HasMany
    {
        return $this->hasMany(WorkOrderHealthTracker::class, 'work_order_health_violation_id');
    }
}

<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\States\WorkOrderIssue\WorkOrderIssueState;
use App\Traits\AbilityResolverTrait;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\ModelStates\HasStates;

/**
 * Class WorkOrderIssue
 *
 * @property int $work_order_issue_id
 * @property string $work_order_issue_uuid
 * @property int $work_order_id
 * @property int $issue_id
 * @property int $organization_id
 * @property int|null $declined_user_id
 * @property string|null $service_notes
 * @property string|null $decline_reason
 * @property bool|null $issue_caused_by_resident
 * @property string|null $issue_caused_details
 * @property WorkOrderIssueState $state
 * @property \Carbon\CarbonImmutable|null $state_updated_at
 * @property \Carbon\CarbonImmutable $created_at
 * @property \Carbon\CarbonImmutable $updated_at
 * @property \Carbon\CarbonImmutable|null $deleted_at
 * @property-read Issue $issue
 *
 * @mixin \Eloquent
 */
class WorkOrderIssue extends Model
{
    use AbilityResolverTrait;
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use HasStates;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_issue_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'work_order_issue_uuid',
        'work_order_id',
        'issue_id',
        'organization_id',
        'state',
        'state_updated_at',
        'service_notes',
        'issue_caused_by_resident',
        'issue_caused_details',
        'decline_reason',
        'declined_user_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'work_order_issue_uuid' => UuidBinary::class,
        'state' => WorkOrderIssueState::class,
        'state_updated_at' => 'immutable_datetime',
    ];

    /**
     * Get the work order associated with the work order issue.
     *
     * @return BelongsTo<WorkOrder, self>
     */
    public function workOrder(): BelongsTo
    {
        return $this->belongsTo(WorkOrder::class, 'work_order_id');
    }

    /**
     * Get the issue associated with the work order issue.
     *
     * @return BelongsTo<Issue, self>
     */
    public function issue(): BelongsTo
    {
        return $this->belongsTo(Issue::class, 'issue_id');
    }

    /**
     * Get the work oder issue status associated with the work order issue.
     */
    public function workOrderIssueStatus()
    {
        return $this->belongsTo(WorkOrderIssueStatus::class, 'state', 'slug');
    }

    /**
     * Get the issues associated to work order
     *
     * @return BelongsToMany<Material>
     */
    public function materials()
    {
        return $this->belongsToMany(
            Material::class,
            'work_order_issue_materials',
            'work_order_issue_id',
            'material_id'
        );
    }

    /**
     * @return BelongsToMany<Media>
     */
    public function media(): BelongsToMany
    {
        return $this->belongsToMany(Media::class, 'work_order_media', 'work_order_issue_id', 'media_id')
            ->withPivot('media_type', 'has_thumbnail', 'has_upload_completed');
    }
}

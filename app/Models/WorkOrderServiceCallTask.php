<?php

namespace App\Models;

use App\Traits\BelongsToOrganization;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\WorkOrderServiceCallTask
 *
 * @property int $work_order_service_call_task_id
 * @property int $organization_id
 * @property int|null $work_order_service_call_id
 * @property int|null $work_order_task_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read Organization $organization
 *
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask query()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask whereWorkOrderServiceCallId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask whereWorkOrderServiceCallTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask whereWorkOrderTaskId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|WorkOrderServiceCallTask withoutTrashed()
 *
 * @property-read WorkOrderServiceCall|null $serviceCall
 * @property-read WorkOrderTask|null $task
 *
 * @mixin \Eloquent
 */
class WorkOrderServiceCallTask extends Model
{
    use BelongsToOrganization;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_service_call_task_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'organization_id',
        'work_order_service_call_id',
        'work_order_task_id',
    ];

    /**
     * @return BelongsTo<WorkOrderTask, $this>
     */
    public function task(): belongsTo
    {
        return $this->belongsTo(WorkOrderTask::class, 'work_order_task_id', 'work_order_task_id');
    }

    /**
     * @return BelongsTo<WorkOrderServiceCall, $this>
     */
    public function serviceCall(): belongsTo
    {
        return $this->belongsTo(WorkOrderServiceCall::class, 'work_order_service_call_id', 'work_order_service_call_id');
    }
}

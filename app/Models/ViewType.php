<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\ViewType
 *
 * @property int $view_type_id
 * @property string $label
 * @property string $slug
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ViewType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ViewType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ViewType query()
 * @method static \Illuminate\Database\Eloquent\Builder|ViewType whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ViewType whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ViewType whereViewTypeId($value)
 *
 * @mixin \Eloquent
 */
class ViewType extends Model
{
    /**
     * @var bool
     */
    public $timestamps = false;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'view_type_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'label',
        'slug',
    ];
}

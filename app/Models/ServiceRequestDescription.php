<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\ServiceRequestDescription
 *
 * @property int $service_request_description_id
 * @property string $service_request_description_uuid
 * @property string $description
 * @property int $service_request_id
 * @property int|null $user_id
 * @property int $organization_id
 * @property \Illuminate\Support\Collection<(int|string), mixed>|array $additional_info
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 *
 * @method static \Database\Factories\ServiceRequestDescriptionFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class ServiceRequestDescription extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'service_request_description_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'service_request_description_uuid',
        'service_request_description_id',
        'service_request_id',
        'description',
        'organization_id',
        'user_id',
        'additional_info',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'service_request_description_uuid' => UuidBinary::class,
        'additional_info' => 'array',
    ];

    /**
     * @return BelongsTo<User,self>
     */
    public function createdUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the service request to which this model belongs to.
     *
     * @return BelongsTo<ServiceRequest, self>
     */
    public function serviceRequest(): BelongsTo
    {
        return $this->belongsTo(ServiceRequest::class, 'service_request_id', 'service_request_id');
    }
}

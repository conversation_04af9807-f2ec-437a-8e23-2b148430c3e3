<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Http\Request;
use Spatie\WebhookClient\Models\WebhookCall as WebhookCallModel;
use Spatie\WebhookClient\WebhookConfig;

class WebhookCall extends WebhookCallModel
{
    use BelongsToOrganization,
        BindsOnUuid,
        GeneratesUuid;

    protected $primaryKey = 'webhook_call_id';

    protected $fillable = [
        'webhook_call_uuid',
        'organization_id',
        'name',
        'url',
        'headers',
        'payload',
        'exception',
    ];

    protected $casts = [
        'headers' => 'array',
        'payload' => 'array',
        'exception' => 'array',
        'webhook_call_uuid' => UuidBinary::class,
    ];

    public static function storeWebhook(WebhookConfig $config, Request $request): \Spatie\WebhookClient\Models\WebhookCall
    {
        $headers = self::headersToStore($config, $request);

        return self::create([
            'name' => $config->name,
            'url' => $request->fullUrl(),
            'headers' => $headers,
            'payload' => $request->input(),
            'exception' => null,
            'organization_id' => $request->get('organization_id', $request->attributes->get('organization_id')),
        ]);
    }

    /**
     * @return BelongsTo<Organization, $this>
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class, 'organization_id');
    }
}

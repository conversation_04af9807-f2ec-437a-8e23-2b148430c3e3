<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class WorkOrderTags extends Model
{
    public $timestamps = false;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'work_order_tags';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'work_order_tag_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tag_id',
        'work_order_id',
    ];
}

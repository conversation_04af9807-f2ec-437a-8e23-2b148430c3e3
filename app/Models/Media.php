<?php

namespace App\Models;

use App\Casts\UuidBinary;
use App\Enums\ImageConversionType;
use App\Traits\BelongsToOrganization;
use App\Traits\BindsOnUuid;
use App\Traits\GeneratesUuid;
use Database\Factories\MediaFactory;
use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\URL;

/**
 * App\Models\Media
 *
 * @property int $media_id
 * @property string|null $media_uuid
 * @property int $organization_id
 * @property string $file_name
 * @property string|null $original_file_name
 * @property string|null $mime_type
 * @property string|null $size
 * @property string $extension
 * @property string|null $thumbnail_file_name
 * @property string|null $original_thumbnail_file_name
 * @property string|null $thumbnail_extension
 * @property string|null $optimized_file_name
 * @property string|null $external_media_reference_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $deleted_at
 * @property-read Organization $organization
 * @property-read WorkOrderMedia|null $workOrderMedia
 * @property-read ServiceRequestMedia|null $serviceRequestMedia
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Media newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Media newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Media orWhereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Media orWhereUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Media query()
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereExtension($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereMediaId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereMediaUuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereMimeType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereNotUuid($uuid, $uuidColumn = null, $model = null)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereOriginalFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereOriginalThumbnailFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereThumbnailExtension($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereThumbnailFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Media whereUuid($uuid, $uuidColumn = null, $model = null)
 *
 * @mixin \Eloquent
 */
class Media extends Model
{
    use BelongsToOrganization;
    use BindsOnUuid;
    use GeneratesUuid;

    /** @use HasFactory<MediaFactory> */
    use HasFactory;
    use SoftDeletes;

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'media_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int,string>
     */
    protected $fillable = [
        'media_uuid', 'organization_id', 'original_file_name', 'file_name', 'mime_type', 'size', 'extension',
        'original_thumbnail_file_name', 'thumbnail_file_name', 'thumbnail_extension', 'optimized_file_name', 'external_media_reference_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string,string>
     */
    protected $casts = [
        'media_uuid' => UuidBinary::class,
    ];

    /**
     * @return BelongsTo<WorkOrderMedia, $this>
     */
    public function workOrderMedia(): BelongsTo
    {
        return $this->belongsTo(WorkOrderMedia::class, 'media_id', 'media_id');
    }

    /**
     * @return HasOneThrough<WorkOrder, WorkOrderMedia, $this>
     */
    public function workOrder(): HasOneThrough
    {
        // TODO: this lint issues seems like a bug for the L11, will fix it once we find the exact reason.
        return $this->HasOneThrough( // @phpstan-ignore-line
            WorkOrder::class,
            WorkOrderMedia::class,
            'media_id',
            'work_order_id',
            'media_id',
            'work_order_id',
        );
    }

    /**
     * @return BelongsTo<ServiceRequestMedia, self>
     */
    public function serviceRequestMedia(): BelongsTo
    {
        return $this->belongsTo(ServiceRequestMedia::class, 'media_id', 'media_id');
    }

    /**
     * @return HasOneThrough<ServiceRequest>
     */
    public function serviceRequest(): HasOneThrough
    {
        return $this->HasOneThrough(
            ServiceRequest::class,
            ServiceRequestMedia::class,
            'media_id',
            'service_request_id',
            'media_id',
            'service_request_id',
        );
    }

    public function getBasePath(Organization $organization, string $workOrderUuid): string
    {
        return $organization->getMediaPathPrefix() . "/work-orders/{$workOrderUuid}";
    }

    public function getServiceRequestBasePath(Organization $organization, string $serviceRequestUuid): string
    {
        return $organization->getMediaPathPrefix() . "/service-requests/{$serviceRequestUuid}";
    }

    /**
     * @throws Exception
     */
    public function getTemporaryMediaUrl(string $type = 'thumbnail'): string
    {
        $media = $this;
        $expirationTime = now()->addSeconds(config('media.cache.temporary_file_cache_expire_in_seconds'));

        return match ($type) {
            ImageConversionType::THUMBNAIL() => URL::temporarySignedRoute('public.media', $expirationTime, [
                'media' => $media->media_uuid,
                'type' => ImageConversionType::THUMBNAIL(),
                'filename' => $this->thumbnail_file_name,
            ]),
            ImageConversionType::OPTIMIZED() => URL::temporarySignedRoute('public.media', $expirationTime, [
                'media' => $media->media_uuid,
                'type' => ImageConversionType::OPTIMIZED(),
                'filename' => $this->optimized_file_name,
            ]),
            default => URL::temporarySignedRoute('public.media', $expirationTime, [
                'media' => $media->media_uuid,
                'type' => ImageConversionType::ORIGINAL(),
                'filename' => $this->file_name,
            ])
        };
    }

    /**
     * @throws Exception
     */
    public function getServiceRequestTemporaryMediaUrl(string $type = 'thumbnail'): string
    {
        $media = $this;
        $expirationTime = now()->addSeconds(3600);

        return match ($type) {
            ImageConversionType::THUMBNAIL() => URL::temporarySignedRoute('public.media.service_request', $expirationTime, [
                'media' => $media->media_uuid,
                'type' => ImageConversionType::THUMBNAIL(),
                'filename' => $this->thumbnail_file_name,
            ]),
            ImageConversionType::OPTIMIZED() => URL::temporarySignedRoute('public.media.service_request', $expirationTime, [
                'media' => $media->media_uuid,
                'type' => ImageConversionType::OPTIMIZED(),
                'filename' => $this->optimized_file_name,
            ]),
            default => URL::temporarySignedRoute('public.media.service_request', $expirationTime, [
                'media' => $media->media_uuid,
                'type' => ImageConversionType::ORIGINAL(),
                'filename' => $this->file_name,
            ])
        };
    }
}

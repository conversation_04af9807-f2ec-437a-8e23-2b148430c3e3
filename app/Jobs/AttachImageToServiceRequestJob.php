<?php

namespace App\Jobs;

use App\Enums\Boolean;
use App\Enums\ImageResizeTypes;
use App\Exceptions\MediaUploadException;
use App\Helpers\Helper;
use App\Models\Media;
use App\Models\Organization;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestDescription;
use App\Models\ServiceRequestMedia;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Spatie\Multitenancy\Jobs\NotTenantAware;
use Symfony\Component\Mime\MimeTypes;

class AttachImageToServiceRequestJob implements NotTenantAware, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var string
     */
    protected $url;

    /**
     * @var Organization
     */
    protected $organization;

    /**
     * @var ServiceRequest
     */
    protected $serviceRequest;

    /**
     * @var int|null
     */
    protected $serviceRequestDescriptionId;

    public function __construct(string $url, Organization $organization, ServiceRequest $serviceRequest, ?int $serviceRequestDescriptionId = null)
    {
        $this->url = $url;
        $this->organization = $organization;
        $this->serviceRequest = $serviceRequest;
        $this->serviceRequestDescriptionId = $serviceRequestDescriptionId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        try {
            if ($this->url) {
                $fileResponse = Http::get($this->url);

                // Check the response is success
                if ($fileResponse->failed()) {
                    throw new FileNotFoundException('File fetching failed: invalid url provided');
                }

                // check response is a valid image
                if (! str_starts_with($fileResponse->header('Content-Type'), 'image/')) {
                    throw new FileNotFoundException('File fetching failed: provided url is not an image');
                }

                $fileResponseHeader = $fileResponse->headers();
                $actualFileContent = $fileResponse->body();

                $basename = pathinfo($this->url, PATHINFO_BASENAME);
                $filterableName = explode('?', $basename);
                $fileName = $filterableName[0] ?? $basename;

                $extension = pathinfo($fileName, PATHINFO_EXTENSION);

                if (empty($extension)) {
                    $extension = (new MimeTypes)->getExtensions($fileResponseHeader['Content-Type'][0])[0];
                }

                Log::info('Media info1', [
                    'fileResponseHeader' => $fileResponseHeader,
                    'url' => $this->url,
                    'fileName' => $fileName,
                    'extension' => $extension,
                    'filterableName' => $filterableName,
                ]);

                if (in_array($extension, ['heic', 'heif'])) {
                    Log::info('Before HEIC image upload', [
                        'domain' => $this->organization->domain,
                        'service_request_uuid' => $this->serviceRequest->service_request_uuid,
                        'url' => $this->url,
                    ]);

                    $tempPath = $this->heicHeifImageConversion();

                    Log::info('After HEIC image upload', [
                        'domain' => $this->organization->domain,
                        'service_request_uuid' => $this->serviceRequest->service_request_uuid,
                        'url' => $this->url,
                        'tempPath' => $tempPath,
                    ]);

                    if (Storage::exists($tempPath)) {
                        $actualFileContent = Storage::get($tempPath);
                        $fileName = File::name($tempPath);
                        $extension = File::extension($tempPath);
                    } else {
                        throw new FileNotFoundException('Heic/Heif converted file content fetching failed.');
                    }
                }

                if (empty($actualFileContent)) {
                    throw new FileNotFoundException('File content fetching failed.');
                }

                Log::info('Uploading image');

                $serviceRequestDirectory = $this->organization->getMediaPathPrefix() . "/service-requests/{$this->serviceRequest->service_request_uuid}";

                // Generating a unique name for the original file.
                $uniqueFileName = Str::uuid();

                $actualFileName = $fileName;
                $actualExtension = $extension;

                $uniqueFilename = "{$uniqueFileName}.{$actualExtension}";
                $destinationFilePathOriginal = $serviceRequestDirectory . '/' . $uniqueFilename;

                $uploaded = Storage::put($destinationFilePathOriginal, $actualFileContent);

                if (! $uploaded) {
                    throw new Exception('Original image uploading failed.');
                }

                $mimeType = Storage::mimeType($destinationFilePathOriginal);
                $sizeInBytes = Storage::size($destinationFilePathOriginal);

                Log::info('Media info', [
                    'mimeType' => $mimeType,
                    'extension' => $extension,
                ]);

                $media = Media::create([
                    'organization_id' => $this->organization->organization_id,
                    'original_file_name' => $actualFileName ?? null,
                    'file_name' => $uniqueFilename,
                    'mime_type' => $mimeType,
                    'size' => (string) $sizeInBytes,
                    'extension' => $extension,
                ]);

                ServiceRequestMedia::create([
                    'media_id' => $media->media_id,
                    'organization_id' => $this->organization->organization_id,
                    'service_request_id' => $this->serviceRequest->service_request_id,
                    'user_id' => null,
                    'media_type' => null,
                    'has_thumbnail' => Boolean::NO(),
                    'has_upload_completed' => Boolean::YES(),
                ]);

                // save media to the ServiceRequestDescription table
                if (! empty($this->serviceRequestDescriptionId)) {
                    $serviceRequestDescription = ServiceRequestDescription::withTrashed()
                        ->select('service_request_description_id', 'additional_info')
                        ->find($this->serviceRequestDescriptionId);

                    if (empty($serviceRequestDescription)) {
                        throw new ModelNotFoundException('Service request description not found');
                    }

                    $additionalInfo = $serviceRequestDescription->additional_info ?? [];

                    $mediaIds = $additionalInfo['media_ids'] ?? [];
                    $mediaIds[] = $media->media_id;

                    $additionalInfo['media_ids'] = array_unique($mediaIds);

                    $serviceRequestDescription->additional_info = $additionalInfo;
                    $serviceRequestDescription->save();
                }

                if (! empty($tempPath) && Storage::exists($tempPath)) {
                    Storage::delete($tempPath);
                }

                Log::info('Uploaded media', [
                    'media_id' => $media->media_id,
                ]);

                DB::commit();
                dispatch(new ServiceRequestImageResizeJob($media, [ImageResizeTypes::THUMBNAIL(), ImageResizeTypes::OPTIMIZED()]))
                    ->afterCommit()
                    ->onQueue('attachment');
            }
        } catch (Exception|FileNotFoundException $exception) {
            DB::rollback();
            Log::error('Transfer Image from Core Job Failed.!!', [
                'service_request_uuid' => $this->serviceRequest->service_request_uuid,
                'exception' => $exception->getMessage(),
                'getCode' => $exception->getCode(),
                'getTrace' => $exception->getTrace(),
                'getLine' => $exception->getLine(),
                'url' => $this->url,
            ]);
            Helper::exceptionLog(
                exception: $exception,
                additionalInfo: [
                    'service_request_id' => $this->serviceRequest->service_request_id ?? null,
                    'organization_id' => $this->organization->organization_id ?? null,
                    'url' => $this->url,
                ],
                message: 'Transfer Image from Core Job Failed due to ' . get_class($exception),
                notify: true
            );
            throw new MediaUploadException('Media upload failed');
        }
    }

    /**
     * @throws FileNotFoundException
     */
    protected function heicHeifImageConversion(): string
    {
        try {
            Log::info('heicHeifImageConversion', [
                'url' => $this->url,
                'service_request_uuid' => $this->serviceRequest->service_request_uuid,
                'domain' => $this->organization->domain,
            ]);

            $heicImageConversionApi = config('settings.heic_image_conversion_api');

            $response = Http::withHeaders([
                'accept' => 'application/json',
                'content-type' => 'application/json',
            ])->post($heicImageConversionApi, [
                'url' => $this->url,
                'service_request_uuid' => $this->serviceRequest->service_request_uuid,
                'domain' => $this->organization->domain,
            ]);

            if ($response->successful() && ! empty($response->json('path'))) {
                return $response->json('path');
            }

            Log::info('Failed to convert Heic/Heif images due to error response!', [
                'service_request_uuid' => $this->serviceRequest->service_request_uuid,
                'domain' => $this->organization->domain,
                'response' => $response->json(),
                'statusCode' => $response->status(),
                'url' => $this->url,
            ]);

            throw new FileNotFoundException('Heic/Heif image conversion failed due to invalid response!');
        } catch (FileNotFoundException $e) {
            throw $e;
        } catch (Exception $e) {
            Log::error('Failed to convert Heic/Heif images due to exception', [
                'service_request_uuid' => $this->serviceRequest->service_request_uuid,
                'domain' => $this->organization->domain,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'url' => $this->url,
            ]);
            Helper::exceptionLog($e, [
                'domain' => $this->organization->domain,
                'service_request_uuid' => $this->serviceRequest->service_request_uuid,
                'url' => $this->url,
            ], 'Heic/Heif image conversion Exception!');
        }

        throw new FileNotFoundException('Heic/Heif image conversion file does not found!');
    }
}

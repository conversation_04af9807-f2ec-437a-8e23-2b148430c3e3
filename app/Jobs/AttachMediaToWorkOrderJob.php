<?php

namespace App\Jobs;

use App\Enums\Boolean;
use App\Enums\ImageResizeTypes;
use App\Exceptions\MediaUploadException;
use App\Exceptions\StorageException;
use App\Helpers\Helper;
use App\Models\Media;
use App\Models\Organization;
use App\Models\WorkOrder;
use App\Models\WorkOrderMedia;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Spatie\Multitenancy\Jobs\NotTenantAware;
use Symfony\Component\Mime\MimeTypes;

class AttachMediaToWorkOrderJob implements NotTenantAware, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var string
     */
    protected $url;

    /**
     * @var string
     */
    protected $thumbnailUrl;

    /**
     * @var Organization
     */
    protected $organization;

    /**
     * @var WorkOrder
     */
    protected $workOrder;

    public function __construct(string $url, string $thumbnailUrl, Organization $organization, WorkOrder $workOrder)
    {
        $this->url = $url;
        $this->thumbnailUrl = $thumbnailUrl;
        $this->organization = $organization;
        $this->workOrder = $workOrder;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::beginTransaction();

        try {
            if ($this->url) {
                $reqOriginalFileResponse = $this->fetchMedia($this->url);

                if ($reqOriginalFileResponse->failed()) {
                    throw new FileNotFoundException('File fetching failed: invalid url provided');
                }

                // Handle original image or video
                $mediaDetails = $this->processOriginalFile($reqOriginalFileResponse);
                $originalFileContent = $mediaDetails['content'];
                $originalFileName = $mediaDetails['fileName'];
                $originalExtension = $mediaDetails['extension'];

                // Validate if the image blob is successfully fetched
                if (empty($originalFileContent)) {
                    throw new FileNotFoundException('File content fetching failed.');
                }

                // Handle image (if applicable)
                if (str_starts_with($reqOriginalFileResponse->header('Content-Type'), 'image/')) {
                    $this->handleImageUpload($originalFileContent, $originalFileName, $originalExtension);
                }

                // Handle video (if applicable)
                if (str_starts_with($reqOriginalFileResponse->header('Content-Type'), 'video/')) {
                    $this->handleVideoUpload($originalFileName, $originalExtension);
                }

                DB::commit();
            }
        } catch (Exception|FileNotFoundException $exception) {
            DB::rollback();
            $this->logErrorDetails('Transfer Image from Core Job Failed.!!', $exception);
            throw new MediaUploadException('Media upload failed');
        }
    }

    /**
     * @throws FileNotFoundException
     */
    protected function heicHeifImageConversion(): string
    {
        try {
            Log::info('heicHeifImageConversion', [
                'url' => $this->url,
                'work_order_uuid' => $this->workOrder->work_order_uuid,
                'domain' => $this->organization->domain,
            ]);

            $heicImageConversionApi = config('settings.heic_image_conversion_api');

            $response = Http::withHeaders([
                'accept' => 'application/json',
                'content-type' => 'application/json',
            ])->post($heicImageConversionApi, [
                'url' => $this->url,
                'work_order_uuid' => $this->workOrder->work_order_uuid,
                'domain' => $this->organization->domain,
            ]);

            if ($response->successful() && ! empty($response->json('path'))) {
                return $response->json('path');
            }

            Log::info('Failed to convert Heic/Heif images due to error response!', [
                'work_order_uuid' => $this->workOrder->work_order_uuid,
                'domain' => $this->organization->domain,
                'response' => $response->json(),
                'statusCode' => $response->status(),
                'url' => $this->url,
            ]);

            throw new FileNotFoundException('Heic/Heif image conversion failed due to invalid response!');
        } catch (FileNotFoundException $e) {
            throw $e;
        } catch (Exception $e) {
            $this->logErrorDetails('Failed to convert Heic/Heif images due to exception', $e);
        }

        throw new FileNotFoundException('Heic/Heif image conversion file does not found!');
    }

    private function fetchMedia(string $url)
    {
        return Http::get($url);
    }

    private function handleImageUpload($fileContent, $fileName, $extension): void
    {
        Log::info('Uploading original image');

        // Generate unique file name
        $uniqueFileName = Str::uuid();
        $workOrderDirectory = $this->organization->getMediaPathPrefix() . "/work-orders/{$this->workOrder->work_order_uuid}";
        $originalUniqueFilename = "{$uniqueFileName}.{$extension}";
        $originalDestinationFilePath = $workOrderDirectory . '/' . $originalUniqueFilename;

        // Upload the original image
        $originalUploaded = Storage::put($originalDestinationFilePath, $fileContent);

        if (! $originalUploaded) {
            throw StorageException::uploadFailed();
        }

        $uploadedMimeType = Storage::mimeType($originalDestinationFilePath);
        $uploadedSize = Storage::size($originalDestinationFilePath);

        Log::info('Original image uploaded', [
            'mimeType' => $uploadedMimeType,
            'size' => $uploadedSize,
            'fileName' => $fileName,
        ]);

        $media = Media::create([
            'organization_id' => $this->organization->organization_id,
            'original_file_name' => $fileName ?? null,
            'file_name' => $originalUniqueFilename,
            'mime_type' => $uploadedMimeType,
            'size' => (string) $uploadedSize,
            'extension' => $extension,
        ]);

        WorkOrderMedia::create([
            'media_id' => $media->media_id,
            'organization_id' => $this->organization->organization_id,
            'work_order_id' => $this->workOrder->work_order_id,
            'has_thumbnail' => Boolean::NO(),
            'has_upload_completed' => Boolean::YES(),
        ]);

        // Create optimize and thumbnail images if necessary
        dispatch_sync(new ImageResizeJob($media, [ImageResizeTypes::THUMBNAIL(), ImageResizeTypes::OPTIMIZED()]));
    }

    private function handleVideoUpload($fileName, $extension): void
    {
        if ($this->thumbnailUrl) {
            $reqThumbnailFileResponse = $this->fetchMedia($this->thumbnailUrl);

            if ($reqThumbnailFileResponse->failed()) {
                throw new FileNotFoundException('Thumbnail fetching failed.');
            }

            // Process thumbnail
            $thumbnailDetails = $this->processThumbnail($reqThumbnailFileResponse);
            $thumbnailContent = $thumbnailDetails['content'];
            $thumbnailFileName = $thumbnailDetails['fileName'];
            $thumbnailExtension = $thumbnailDetails['extension'];

            // Handle thumbnail upload
            $this->uploadVideoThumbnail($thumbnailContent, $thumbnailFileName, $thumbnailExtension, $fileName, $extension);
        }
    }

    private function uploadVideoThumbnail($thumbnailContent, $thumbnailFileName, $thumbnailExtension, $originalFileName, $originalExtension): void
    {
        $uniqueFileName = Str::uuid();
        $workOrderDirectory = $this->organization->getMediaPathPrefix() . "/work-orders/{$this->workOrder->work_order_uuid}";
        $thumbnailUniqueFilename = "{$uniqueFileName}_thumbnail.{$thumbnailExtension}";
        $thumbnailDestinationFilePath = $workOrderDirectory . '/' . $thumbnailUniqueFilename;

        $thumbnailUploaded = Storage::put($thumbnailDestinationFilePath, $thumbnailContent);

        if (! $thumbnailUploaded) {
            throw StorageException::uploadFailed();
        }

        $uploadedMimeType = Storage::mimeType($thumbnailDestinationFilePath);
        $uploadedSize = Storage::size($thumbnailDestinationFilePath);

        Log::info('Uploaded thumbnail media', [
            'mimeType' => $uploadedMimeType,
            'size' => $uploadedSize,
            'fileName' => $thumbnailFileName,
        ]);

        $videoThumbnailMedia = Media::create([
            'organization_id' => $this->organization->organization_id,
            'original_file_name' => $originalFileName ?? null,
            'original_thumbnail_file_name' => $thumbnailFileName ?? null,
            'file_name' => $originalFileName,
            'thumbnail_file_name' => $thumbnailUniqueFilename,
            'mime_type' => $uploadedMimeType,
            'size' => (string) $uploadedSize,
            'extension' => $originalExtension,
            'thumbnail_extension' => $thumbnailExtension,
        ]);

        WorkOrderMedia::create([
            'media_id' => $videoThumbnailMedia->media_id,
            'organization_id' => $this->organization->organization_id,
            'work_order_id' => $this->workOrder->work_order_id,
            'has_thumbnail' => Boolean::YES(),
            'has_upload_completed' => Boolean::YES(),
        ]);
    }

    private function processOriginalFile($reqOriginalFileResponse): array
    {
        $reqOriginalFileResponseHeader = $reqOriginalFileResponse->headers();
        $reqOriginalFileContent = $reqOriginalFileResponse->body();
        $reqOriginalBasename = pathinfo($this->url, PATHINFO_BASENAME);
        $reqOriginalFilterableName = explode('?', $reqOriginalBasename);
        $reqOriginalFileName = $reqOriginalFilterableName[0] ?? $reqOriginalBasename;
        $reqOriginalExtension = pathinfo($reqOriginalFileName, PATHINFO_EXTENSION);

        if (empty($reqOriginalExtension)) {
            $reqOriginalExtension = (new MimeTypes)->getExtensions($reqOriginalFileResponseHeader['Content-Type'][0])[0];
        }

        // Convert HEIC/HEIF to PNG if necessary
        if (in_array($reqOriginalExtension, ['heic', 'heif'])) {
            $reqOriginalFileContent = $this->heicHeifImageConversion();
            $reqOriginalFileName = File::name($reqOriginalFileContent);
            $reqOriginalExtension = File::extension($reqOriginalFileContent);
        }

        return [
            'content' => $reqOriginalFileContent,
            'fileName' => $reqOriginalFileName,
            'extension' => $reqOriginalExtension,
        ];
    }

    private function processThumbnail($reqThumbnailFileResponse): array
    {
        $reqThumbnailFileResponseHeader = $reqThumbnailFileResponse->headers();
        $reqThumbnailFileContent = $reqThumbnailFileResponse->body();
        $reqThumbnailBasename = pathinfo($this->thumbnailUrl, PATHINFO_BASENAME);
        $reqThumbnailFileName = explode('?', $reqThumbnailBasename)[0];
        $reqThumbnailExtension = pathinfo($reqThumbnailFileName, PATHINFO_EXTENSION);

        if (empty($reqThumbnailExtension)) {
            $reqThumbnailExtension = (new MimeTypes)->getExtensions($reqThumbnailFileResponseHeader['Content-Type'][0])[0];
        }

        return [
            'content' => $reqThumbnailFileContent,
            'fileName' => $reqThumbnailFileName,
            'extension' => $reqThumbnailExtension,
        ];
    }

    /**
     * Logs the error and exceptions consistently.
     */
    private function logErrorDetails($message, $exception): void
    {
        Log::error($message, [
            'work_order_uuid' => $this->workOrder->work_order_uuid,
            'exception' => $exception->getMessage(),
            'getCode' => $exception->getCode(),
            'getTrace' => $exception->getTrace(),
            'getLine' => $exception->getLine(),
            'url' => $this->url,
            'domain' => $this->organization->domain,
        ]);

        Helper::exceptionLog(
            exception: $exception,
            additionalInfo: [
                'work_order_id' => $this->workOrder->work_order_id ?? null,
                'organization_id' => $this->organization->organization_id ?? null,
                'url' => $this->url,
                'domain' => $this->organization->domain,
            ],
            message: $message,
            notify: true
        );
    }
}

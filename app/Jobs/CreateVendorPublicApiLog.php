<?php

namespace App\Jobs;

use App\Models\VendorPublicApiLog;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Spatie\Multitenancy\Jobs\NotTenantAware;

class CreateVendorPublicApiLog implements NotTenantAware, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @param  array<string,mixed>  $inputData
     */
    public function __construct(
        public array $inputData
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            VendorPublicApiLog::create($this->inputData);
        } catch (Exception $exception) {
            Log::error('VendorPublicApiLog Creation Failed', $this->inputData);
            throw new Exception($exception->getMessage());
        }
    }
}

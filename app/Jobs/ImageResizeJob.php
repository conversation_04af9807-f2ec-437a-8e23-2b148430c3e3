<?php

namespace App\Jobs;

use App\Enums\Boolean;
use App\Enums\ImageResizeTypes;
use App\Exceptions\NotFoundException;
use App\Helpers\Helper;
use App\Models\Media;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Encoders\JpegEncoder;
use Intervention\Image\ImageManager;
use Spatie\Multitenancy\Jobs\NotTenantAware;

class ImageResizeJob implements NotTenantAware, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * Create a new job instance.
     */

    /**
     * @param  array<int,string>  $type
     */
    public function __construct(public Media $media, public array $type)
    {
        $this->media = $media;
        $this->type = $type;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array<int, int>
     */
    public function backoff(): array
    {
        return [3, 10, 15];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $this->media->load([
                'workOrderMedia' => function ($query) {
                    $query->withTrashed();
                },
                'workOrderMedia.workOrder:work_order_id,work_order_uuid,organization_id',
                'workOrderMedia.workOrder.organization:organization_id,domain',
            ]);

            if (! empty($this->media->mime_type) && str_starts_with($this->media->mime_type, 'image/')) {

                $workOrderMedia = $this->media->workOrderMedia;

                if (empty($workOrderMedia)) {
                    throw NotFoundException::resourceNotFound('workOrderMedia');
                }

                $workOrder = $workOrderMedia->workOrder;

                if (empty($workOrder)) {
                    throw NotFoundException::resourceNotFound('workOrder');
                }

                $organization = $workOrder->organization;

                if (empty($organization)) {
                    throw NotFoundException::resourceNotFound('organization');
                }

                $workOrderDirectory = $organization->getMediaPathPrefix() . "/work-orders/{$workOrder->work_order_uuid}";
                $mediaPath = $workOrderDirectory . "/{$this->media->file_name}";

                if (Storage::exists($mediaPath)) {

                    $filename = $this->media->file_name;
                    $parts = explode('.' . $this->media->extension, $filename);

                    $uniqueFilename = $parts[0];

                    $manager = ImageManager::imagick();
                    $linkedImageFile = Storage::get($mediaPath);

                    if (in_array(ImageResizeTypes::THUMBNAIL(), $this->type)) {
                        $image = $manager->read($linkedImageFile);
                        $thumbnailExtension = config('settings.image.thumbnail.extension');
                        $thumbnailFileName = "{$uniqueFilename}_thumbnail.{$thumbnailExtension}";

                        $image->scaleDown(config('settings.image.thumbnail.width'), config('settings.image.thumbnail.height'));

                        $uploaded = Storage::put($workOrderDirectory . "/{$thumbnailFileName}", $image->encode(new JpegEncoder));

                        if (! $uploaded) {
                            throw new Exception(__("Thumbnail uploading failed: media {$this->media->media_uuid}"));
                        }

                        $this->media->thumbnail_file_name = $thumbnailFileName;
                        $this->media->thumbnail_extension = $thumbnailExtension;
                        $this->media->original_thumbnail_file_name = $thumbnailFileName;
                        $this->media->save();

                        //update has_thumbnail
                        $workOrderMedia->has_thumbnail = Boolean::YES();
                        $workOrderMedia->save();

                        // clean memory
                        $image = null;
                    }

                    if (in_array(ImageResizeTypes::OPTIMIZED(), $this->type)) {
                        $image = $manager->read($linkedImageFile);

                        $optimizedFileName = "{$uniqueFilename}_optimized.{$this->media->extension}";

                        $image->scaleDown(width: config('settings.image.optimized.width'));
                        $uploaded = Storage::put($workOrderDirectory . "/{$optimizedFileName}", $image->encode(new JpegEncoder));

                        if (! $uploaded) {
                            throw new Exception(__("Optimized image uploading failed: media {$this->media->media_uuid}"));
                        }

                        $this->media->optimized_file_name = $optimizedFileName;
                        $this->media->save();

                        //update has_optimized
                        $workOrderMedia->has_optimized = Boolean::YES();
                        $workOrderMedia->save();
                    }
                } else {
                    throw NotFoundException::mediaNotExists($mediaPath);
                }
            } else {
                Log::info('Other than image file requested to resize', [
                    'media_id' => $this->media->media_uuid,
                ]);
            }

        } catch (Exception|NotFoundException $exception) {
            Log::error('Image resize Failed.!!', [
                'media_id' => $this->media->media_uuid,
                'exception' => $exception->getMessage(),
                'getCode' => $exception->getCode(),
                'getTrace' => $exception->getTrace(),
                'getLine' => $exception->getLine(),
            ]);
            Helper::exceptionLog(
                exception: $exception,
                additionalInfo: [
                    'media_id' => $this->media->media_uuid,
                    'type' => $this->type,
                ],
                message: 'Image resize job failed ' . get_class($exception),
                notify: true
            );

            throw new Exception('Media resize job failed.');
        }
    }
}

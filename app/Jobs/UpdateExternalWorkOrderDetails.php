<?php

namespace App\Jobs;

use App\Helpers\Helper;
use App\Models\Resident;
use App\Models\WorkOrder;
use App\Services\Vendor\Enum\Service;
use App\Services\Vendor\VendorService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Spatie\Multitenancy\Jobs\NotTenantAware;

class UpdateExternalWorkOrderDetails implements NotTenantAware, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public string $workOrderId,
        public string $type
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $updateDetails = [];
        try {
            $workOrder = WorkOrder::with([
                'vendor:vendor_id,service',
                'organization:organization_id,organization_uuid',
            ])
                ->whereUuid($this->workOrderId)
                ->select(
                    'work_order_id', 'vendor_id', 'vendor_work_order_id', 'organization_id',
                    'property_access_method', 'property_access_code', 'property_access_note',
                    'requesting_resident_id', 'property_id', 'description'
                )
                ->firstOrFail();

            $updateDetails = match ($this->type) {
                'cancel' => $this->workOrderCancelPayload(),
                'description' => $this->workOrderDescriptionPayload($workOrder),
                'access_info' => $this->workOrderAccessInfoPayload($workOrder),
                'resident_info' => $this->workOrderResidentInfoPayload($workOrder),
                'address' => $this->workOrderAddressPayload($workOrder),
                default => []
            };

            if (! empty($workOrder->vendor)) {
                if ($workOrder->vendor->service === Service::LULA()) {
                    $vendorService = VendorService::make(
                        vendorProvider: Service::getServiceProviderFrom($workOrder->vendor->service),
                        organization: $workOrder->organization
                    );

                    if (! empty($workOrder->vendor_work_order_id)) {
                        $vendorService->updateWorkOrder($workOrder, $updateDetails);
                    }
                }
            }
        } catch (Exception $exception) {
            Log::error('Job details updating failed.!!', [
                'updateDetails' => $updateDetails,
                'exception' => $exception->getMessage(),
            ]);
            Helper::exceptionLog(
                exception: $exception,
                additionalInfo: [
                    'updateDetails' => $updateDetails,
                ],
                message: 'Job details updating failed',
                notify: true
            );
            throw new Exception('Job details updating failed');
        }
    }

    /**
     * @return array<string,int>
     */
    public function workOrderCancelPayload(): array
    {
        return [
            'canceledByCustomer' => 1,
        ];
    }

    /**
     * @return array<string,string>
     */
    public function workOrderDescriptionPayload(WorkOrder $workOrder): array
    {
        return [
            'description' => $workOrder->description,
        ];
    }

    /**
     * @return array<string,mixed>
     */
    public function workOrderAccessInfoPayload(WorkOrder $workOrder): array
    {
        return [
            'accessInstructions' => [
                'type' => $workOrder->property_access_method,
                'accessCode' => $workOrder->property_access_code,
                'additionalInstructions' => $workOrder->property_access_note,
            ],
        ];
    }

    /**
     * @return array<string,mixed>
     */
    public function workOrderResidentInfoPayload(WorkOrder $workOrder): array
    {
        $workOrder->loadMissing(
            [
                'property:property_id,street_address,unit_number,city,postal_zip_code,state_id',
                'property.state:state_id,state_code',
                'property.residents:residents.resident_id,residents.first_name,residents.last_name,residents.email,residents.phone_number,residents.property_id',
            ]
        );

        $payload = [
            'residents' => [],
        ];

        if (! empty($workOrder->property->residents)) {
            $payload['residents'] = $workOrder->property->residents->map(function (Resident $resident) use ($workOrder) {
                return [
                    'email' => $resident->email,
                    'phone' => $resident->phone_number,
                    'fullName' => $resident->getName(),
                    'isPrimary' => $workOrder->requesting_resident_id === $resident->resident_id,
                ];
            })->toArray();
        }

        return $payload;
    }

    /**
     * @return array<string,mixed>
     */
    public function workOrderAddressPayload(WorkOrder $workOrder): array
    {
        $workOrder->loadMissing(
            [
                'property:property_id,street_address,unit_number,city,postal_zip_code,state_id',
                'property.state:state_id,state_code',
            ]
        );

        $payload = [
            'propertyAddress' => Arr::mapWithKeys($workOrder->property->getAddress(), function ($value, $key) {
                $key = match ($key) {
                    'street_address' => 'streetAddress',
                    'state_code' => 'stateAbbr',
                    'zip_code' => 'zipcode',
                    'apt_suite_unit' => 'unit',
                    default => $key
                };

                return [$key => $value];
            }),
        ];

        return $payload;
    }
}

<?php

namespace App\Jobs\Webhook;

use App\Enums\Boolean;
use App\Enums\MediaType;
use App\Enums\ScheduleTypes;
use App\Enums\ServiceCallActionTypes;
use App\Enums\ServiceCallStatus;
use App\Enums\Trip;
use App\Enums\WorkOrderNoteTypes;
use App\Enums\WorkOrderStatus as WorkOrderStatusEnum;
use App\Enums\WorkToPerformTypes;
use App\Events\WorkOrder\Actions\WorkOrderClaimPending;
use App\Events\WorkOrder\Actions\WorkOrderQualityCheck;
use App\Events\WorkOrder\Actions\WorkOrderReadyToSchedule;
use App\Events\WorkOrder\Actions\WorkOrderScheduled;
use App\Events\WorkOrder\Actions\WorkOrderWorkInProgress;
use App\Events\WorkOrder\Note\WorkOrderNoteCreated;
use App\Events\WorkOrder\Trip\TripCanceled;
use App\Events\WorkOrder\Trip\TripEnd;
use App\Events\WorkOrder\Trip\TripPaused;
use App\Events\WorkOrder\Trip\TripReScheduled;
use App\Events\WorkOrder\Trip\TripScheduled;
use App\Events\WorkOrder\Trip\TripScheduleInProgress;
use App\Events\WorkOrder\Trip\TripWorking;
use App\Events\WorkOrder\WorkOrderPaused;
use App\Events\WorkOrder\WorkOrderReScheduled;
use App\Events\WorkOrder\WorkOrderResolve;
use App\Events\WorkOrder\WorkOrderUpdate;
use App\Exceptions\LulaWebhookException;
use App\Helpers\Helper;
use App\Jobs\AttachImageToWorkOrderTrip;
use App\Jobs\SyncWorkOrderDetailsWithExternalJob;
use App\Models\LulaAppointment;
use App\Models\Media;
use App\Models\Vendor;
use App\Models\VendorExternalInvoice;
use App\Models\WorkOrder;
use App\Models\WorkOrderNote;
use App\Models\WorkOrderNoteLog;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderServiceCallLog;
use App\Models\WorkOrderServiceCallTask;
use App\Models\WorkOrderStatus;
use App\Models\WorkOrderTask;
use App\Services\Scheduling\Domain\Enums\SchedulingMethod;
use App\Services\Scheduling\Domain\Enums\SchedulingMode;
use App\Services\Vendor\Enum\Service;
use App\States\ServiceCalls\Canceled;
use App\States\ServiceCalls\Ended;
use App\States\ServiceCalls\EnRoute;
use App\States\ServiceCalls\Paused as ServiceCallsPaused;
use App\States\ServiceCalls\ReScheduled;
use App\States\ServiceCalls\Scheduled;
use App\States\ServiceCalls\ScheduleInProgress;
use App\States\ServiceCalls\Working;
use App\States\WorkOrders\Canceled as WorkOrderCanceledState;
use App\States\WorkOrders\ClaimPending;
use App\States\WorkOrders\Completed;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\QualityCheck;
use App\States\WorkOrders\ReadyToSchedule;
use App\States\WorkOrders\Scheduled as WorkOrdersScheduled;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;
use Spatie\WebhookClient\Jobs\ProcessWebhookJob as ProcessWebhookParentJob;

class ProcessLulaWebhookJob extends ProcessWebhookParentJob
{
    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('webhook process job', [
            $this->webhookCall,
        ]);

        $workOrder = null;
        try {

            $requestData = $this->webhookCall->payload;

            if (empty($requestData)) {
                throw LulaWebhookException::emptyPayload();
            }

            if (! isset($requestData['data'])) {
                throw LulaWebhookException::workOrderDetailsNotFound();
            }

            $workOrderDetails = collect((array) $requestData['data'])->first();

            if (empty($workOrderDetails)) {
                throw LulaWebhookException::workOrderDetailsNotFound();
            }

            if (empty($this->webhookCall->payload['eventType'])) {
                throw LulaWebhookException::emptyEvent();
            }

            DB::transaction(
                fn () => match ($this->webhookCall->payload['eventType']) {
                    'core:workorder.scheduling-in-progress' => $this->scheduleInProgress($workOrderDetails),
                    'core:workorder.scheduled' => $this->scheduleWorkOrder($workOrderDetails),
                    'core:workorder.work-in-progress' => $this->startWorkOrder($workOrderDetails),
                    'core:workorder.paused' => $this->workPaused($workOrderDetails),
                    'core:workorder.quality-check' => $this->qualityCheck($workOrderDetails),
                    'core:workorder.work-completed' => $this->workComplete($workOrderDetails),
                    'core:workorder.canceled' => $this->cancelWorkOrder($workOrderDetails),
                    'core:workorder.note.added' => $this->noteAdded($workOrderDetails),
                    'core:workorder.invoice-ready' => $this->createInvoice($workOrderDetails),
                    default => true
                }
            );
        } catch (LulaWebhookException|CouldNotPerformTransition $exception) {
            $this->saveExceptionDetails($exception, $workOrder);
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);
            throw LulaWebhookException::webhookFailed($this->webhookCall->payload['eventType'] ?? '', $exception->getMessage());
        } catch (Exception $exception) {
            $this->saveExceptionDetails($exception, $workOrder);
            Helper::exceptionLog(exception: $exception, message: 'Exception occurred while handling webhook', notify: true);
            throw new Exception(__($exception->getMessage()));
        }
    }

    public function saveExceptionDetails(Exception $exception, ?WorkOrder $workOrder = null): void
    {
        $exceptionDetails = [
            'exception_class' => get_class($exception),
            'exception' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'line' => $exception->getLine(),
            'file' => $exception->getFile(),
            'work_order_id' => $workOrder->work_order_uuid ?? null,
        ];

        $this->webhookCall->exception = $exceptionDetails;
        $this->webhookCall->save();
    }

    /**
     * Change work order trip (lula trip) status.
     *
     * @param  array<int|string,mixed>  $workOrderDetails
     */
    private function scheduleInProgress(array $workOrderDetails): void
    {
        $workOrder = WorkOrder::with([
            'organization:organization_id,organization_uuid',
            'tasks' => function ($query) {
                return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                    ->with([
                        'latestServiceCalls' => function ($query) {
                            return $query->select(
                                'work_order_service_calls.work_order_service_call_id',
                                'work_order_service_calls.work_order_service_call_uuid',
                                'work_order_service_calls.work_order_service_call_number',
                                'work_order_service_calls.lula_appointment_id',
                                'work_order_service_calls.scheduled_start_time',
                                'work_order_service_calls.scheduled_end_time',
                                'work_order_service_calls.state',
                                'work_order_service_calls.status',
                                'work_order_service_calls.last_modified_at',
                                'work_order_service_calls.created_at',
                            )->with([
                                'lulaAppointment' => function ($query) {
                                    return $query->select(
                                        'lula_appointment_id',
                                        'lula_appointment_uuid',
                                        'work_order_reference_number',
                                        'service_category_label',
                                        'scheduled_start_time',
                                        'scheduled_end_time',
                                    );
                                },
                            ]);
                        },
                    ]);
            },
        ])
            ->where('vendor_work_order_id', $workOrderDetails['workOrderId'])
            ->select(
                'work_order_id', 'work_order_uuid', 'state', 'organization_id', 'vendor_work_order_id', 'state_updated_at', 'work_order_status_id'
            )
            ->firstOrFail();

        $task = $workOrder->tasks->firstOrFail();
        $serviceCall = $task->latestServiceCalls->firstOrFail();

        if (empty($serviceCall->lulaAppointment)) {
            throw LulaWebhookException::lulaAppointmentNotExists();
        }

        $lulaAppointment = $serviceCall->lulaAppointment;

        if (! $workOrder->state->equals(ClaimPending::class)) {
            $this->toWorkOrderClaimPending($workOrder, $task);
        }

        // If the trip is end then create a new trip
        if ($serviceCall->state->equals(Ended::class, Canceled::class)) {
            $this->createNewLulaTrip($workOrder, $task, $serviceCall, $lulaAppointment);
        } elseif (! $serviceCall->state->equals(ScheduleInProgress::class)) {
            $this->toTripScheduleInProgress($workOrder, $task, $serviceCall, $lulaAppointment);
        }

        if (empty($lulaAppointment->service_category_label)) {
            SyncWorkOrderDetailsWithExternalJob::dispatch($workOrder->work_order_uuid)->afterCommit()->delay(now()->addSeconds(10));
        }

        // Trigger event for work order update
        event(new WorkOrderUpdate($workOrder->work_order_uuid));
    }

    private function toTripScheduleInProgress(WorkOrder &$workOrder, WorkOrderTask &$task, WorkOrderServiceCall &$serviceCall, LulaAppointment &$lulaAppointment): void
    {
        // Step 1 : Update service call status to 'Schedule In Progress'
        $scheduleInProgressState = new ScheduleInProgress($serviceCall);
        $serviceCall->state = $scheduleInProgressState;
        $serviceCall->last_modified_at = CarbonImmutable::now();
        $serviceCall->save();

        // Step 2 : Create activity log for this action
        $activityLogEventAttributes = [
            'to' => $scheduleInProgressState->label(),
            'to_color_class' => $scheduleInProgressState->colorClass(),
            'slug_of_to' => $scheduleInProgressState->getValue(),
            'trip_id' => $serviceCall->work_order_service_call_uuid,
            'trip_type' => ScheduleTypes::LULA_PRO(),
            'wo_reference_number' => $lulaAppointment->work_order_reference_number,
        ];

        event(new TripScheduleInProgress(
            $workOrder->work_order_id,
            $task->work_order_task_id,
            $activityLogEventAttributes,
        ));

        // Step 3 : Create service log for this action
        $additionalData = [
            'action' => ServiceCallActionTypes::SCHEDULE_IN_PROGRESS(),
            'type' => 'start',
        ];

        $this->createServiceLog(
            $serviceCall,
            $workOrder,
            $task,
            $additionalData
        );
    }

    /**
     * @param  array<string,mixed>  $additionalData
     */
    private function createServiceLog(WorkOrderServiceCall $serviceCall, WorkOrder $workOrder, WorkOrderTask $task, array $additionalData): void
    {
        WorkOrderServiceCallLog::create([
            'work_order_service_call_id' => $serviceCall->work_order_service_call_id,
            'organization_id' => $workOrder->organization_id,
            'work_order_id' => $workOrder->work_order_id,
            'work_order_task_id' => $task->work_order_task_id,
            'notes' => $additionalData['note'] ?? null,
            'action' => $additionalData['action'] ?? null,
            'action_start_by_user_id' => $additionalData['action_start_by_user_id'] ?? null,
            'action_end_by_user_id' => $additionalData['action_end_by_user_id'] ?? null,
            'action_started_at' => $additionalData['type'] === 'start' ? CarbonImmutable::now() : null,
            'action_ended_at' => $additionalData['type'] === 'end' ? CarbonImmutable::now() : null,
            'trip_end_with' => $additionalData['trip_end_with'] ?? null,
            'trip_end_with_type' => $additionalData['trip_end_with_type'] ?? null,
            'trip_end_with_reason' => $additionalData['trip_end_with_reason'] ?? null,
        ]);
    }

    /**
     * Schedule work order
     *
     * @param  array<int|string,mixed>  $workOrderDetails
     */
    private function scheduleWorkOrder(array $workOrderDetails): void
    {
        $workOrder = WorkOrder::with([
            'organization:organization_id,organization_uuid',
            'tasks' => function ($query) {
                return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                    ->with([
                        'latestServiceCalls' => function ($query) {
                            return $query->select(
                                'work_order_service_calls.work_order_service_call_id',
                                'work_order_service_calls.work_order_service_call_uuid',
                                'work_order_service_calls.work_order_service_call_number',
                                'work_order_service_calls.lula_appointment_id',
                                'work_order_service_calls.scheduled_start_time',
                                'work_order_service_calls.scheduled_end_time',
                                'work_order_service_calls.state',
                                'work_order_service_calls.service_notes',
                                'work_order_service_calls.status',
                                'work_order_service_calls.trip_end_with',
                                'work_order_service_calls.trip_end_with_type',
                                'work_order_service_calls.trip_end_with_reason',
                                'work_order_service_calls.last_modified_at',
                                'work_order_service_calls.timer_paused_at',
                                'work_order_service_calls.timer_resumed_at',
                                'work_order_service_calls.timer_paused_reason',
                                'work_order_service_calls.created_at',
                            )->with([
                                'lulaAppointment' => function ($query) {
                                    return $query->select(
                                        'lula_appointment_id',
                                        'lula_appointment_uuid',
                                        'work_order_reference_number',
                                        'service_category_label',
                                        'scheduled_start_time',
                                        'scheduled_end_time',
                                        'actual_start_time',
                                        'actual_end_time',
                                        'paused_reason',
                                        'rescheduled_reason',
                                        'cancellation_reason',
                                        'estimated_return_start_time',
                                        'estimated_return_end_time',
                                        'elapse_time_in_sec',
                                        'external_appointment_reference_id',
                                    );
                                },
                            ]);
                        },
                    ]);
            },
        ])
            ->where('vendor_work_order_id', $workOrderDetails['workOrderId'])
            ->select(
                'work_order_id', 'work_order_uuid', 'state', 'organization_id', 'vendor_work_order_id', 'state_updated_at', 'work_order_status_id'
            )
            ->firstOrFail();

        $task = $workOrder->tasks->firstOrFail();
        $serviceCall = $task->latestServiceCalls->firstOrFail();

        if (empty($serviceCall->lulaAppointment)) {
            throw LulaWebhookException::lulaAppointmentNotExists();
        }

        $lulaAppointment = $serviceCall->lulaAppointment;

        $scheduleDetails = $workOrderDetails['serviceSchedule'] ?? [];
        if (empty($scheduleDetails['serviceWindow']['startTimeUTC'])) {
            throw LulaWebhookException::scheduleDateMissing(__('Work order schedule start date missing'));
        }

        if (empty($scheduleDetails['serviceWindow']['endTimeUTC'])) {
            throw LulaWebhookException::scheduleDateMissing(__('Work order schedule end date missing'));
        }

        $startTime = CarbonImmutable::parse($scheduleDetails['serviceWindow']['startTimeUTC']);
        $endTime = CarbonImmutable::parse($scheduleDetails['serviceWindow']['endTimeUTC']);
        $externalScheduledReferenceId = $scheduleDetails['serviceScheduleId'] ?? null;

        $rescheduledDetails = $scheduleDetails['rescheduleDetails'] ?? [];

        if ($workOrder->state->equals(WorkOrdersScheduled::class)) {
            if (! empty($rescheduledDetails)) {
                $this->toWorkOrderReschedule($workOrder, $task);
            }
        } else {
            if (empty($rescheduledDetails)) {
                $this->toWorkOrderSchedule($workOrder, $task);
            } else {
                $this->toWorkOrderReschedule($workOrder, $task);
            }
        }

        if (empty($rescheduledDetails)) {
            // Schedule work order
            $this->toTripSchedule(
                $workOrder,
                $task,
                $serviceCall,
                $lulaAppointment,
                $startTime,
                $endTime,
                $externalScheduledReferenceId
            );
        } else {
            // Reschedule workorder
            $reScheduleReason = $rescheduledDetails['details'];
            $this->toTripReSchedule(
                $workOrder,
                $task,
                $serviceCall,
                $lulaAppointment,
                $startTime,
                $endTime,
                $reScheduleReason,
                $externalScheduledReferenceId
            );
        }

        if (empty($lulaAppointment->service_category_label)) {
            SyncWorkOrderDetailsWithExternalJob::dispatch($workOrder->work_order_uuid)->afterCommit()->delay(now()->addSeconds(10));
        }

        // Trigger event for work order update
        event(new WorkOrderUpdate($workOrder->work_order_uuid));
    }

    private function toWorkOrderSchedule(WorkOrder &$workOrder, WorkOrderTask &$task): void
    {
        // Step 1: Update the work order state to schedule.
        $oldWorkOrderState = $workOrder->state;
        $workOrderStatus = WorkOrderStatus::select('work_order_status_id')->where('slug', WorkOrderStatusEnum::SCHEDULED())->firstOrFail();

        $workOrder->state = new WorkOrdersScheduled($workOrder);
        $workOrder->work_order_status_id = $workOrderStatus->work_order_status_id;
        $workOrder->state_updated_at = CarbonImmutable::now();
        $workOrder->save();

        // Step 2: Trigger work order scheduled event.
        $activityLogEventAttribute = [
            'from' => $oldWorkOrderState->label(),
            'from_color_class' => $oldWorkOrderState->colorClass(),
            'to' => $workOrder->state->label(),
            'to_color_class' => $workOrder->state->colorClass(),
            'slug_of_to' => $workOrder->state->getValue(),
        ];

        event(new WorkOrderScheduled(
            $workOrder->work_order_id,
            $activityLogEventAttribute
        ));
    }

    private function toWorkOrderReschedule(WorkOrder &$workOrder, WorkOrderTask &$task): void
    {
        $oldWorkOrderState = $workOrder->state;
        $workOrderStatus = WorkOrderStatus::select('work_order_status_id')->where('slug', WorkOrderStatusEnum::SCHEDULED())->firstOrFail();

        $workOrder->state = new WorkOrdersScheduled($workOrder);
        $workOrder->work_order_status_id = $workOrderStatus->work_order_status_id;
        $workOrder->state_updated_at = CarbonImmutable::now();
        $workOrder->save();

        $eventAttributes = [
            'from' => $oldWorkOrderState->label(),
            'from_color_class' => $oldWorkOrderState->colorClass(),
            'to' => $workOrder->state->label(),
            'to_color_class' => $workOrder->state->colorClass(),
            'slug_of_to' => $workOrder->state->getValue(),
            'is_rescheduled' => true,
        ];

        event(new WorkOrderReScheduled(
            $workOrder->work_order_id,
            $task->work_order_task_id,
            $eventAttributes
        ));
    }

    private function toTripSchedule(WorkOrder &$workOrder, WorkOrderTask &$task, WorkOrderServiceCall &$serviceCall, LulaAppointment &$lulaAppointment, CarbonImmutable $startTime, CarbonImmutable $endTime, ?string $externalScheduledReferenceId = null): void
    {
        // Step 3: Update service call status to schedule
        $newState = new Scheduled($serviceCall);
        $serviceCall->state = $newState;
        $serviceCall->scheduled_start_time = $startTime;
        $serviceCall->scheduled_end_time = $endTime;
        $serviceCall->last_modified_at = CarbonImmutable::now();
        $serviceCall->save();

        // Step 4: Update lula appointment
        $lulaAppointment->scheduled_start_time = $startTime;
        $lulaAppointment->scheduled_end_time = $endTime;
        $lulaAppointment->external_appointment_reference_id = $externalScheduledReferenceId;
        $lulaAppointment->save();

        // Step 5: Trigger Trip Scheduled Event
        $eventAttributes = [
            'to' => $newState->label(),
            'to_color_class' => $newState->colorClass(),
            'slug_of_to' => $newState->getValue(),
            'trip_id' => $serviceCall->work_order_service_call_uuid,
            'trip_type' => ScheduleTypes::LULA_PRO(),
            'wo_reference_number' => $lulaAppointment->work_order_reference_number,
            'appointment_detail' => [
                'schedule_start_date' => $serviceCall->scheduled_start_time->toIso8601String(),
                'schedule_end_date' => $serviceCall->scheduled_end_time->toIso8601String(),
            ],
        ];

        event(new TripScheduled(
            $workOrder->work_order_id,
            null,
            $eventAttributes,
        ));

        // Step 6: Create Service call log
        $additionalData = [
            'action' => ServiceCallActionTypes::SCHEDULED(),
            'type' => 'start',
        ];
        $this->createServiceLog(
            $serviceCall,
            $workOrder,
            $task,
            $additionalData
        );
    }

    private function toTripReSchedule(WorkOrder &$workOrder, WorkOrderTask &$task, WorkOrderServiceCall &$serviceCall, LulaAppointment &$lulaAppointment, CarbonImmutable $startTime, CarbonImmutable $endTime, ?string $reason = null, ?string $externalScheduledReferenceId = null): void
    {
        $serviceCall->state = new ReScheduled($serviceCall);
        $serviceCall->scheduled_start_time = $startTime;
        $serviceCall->scheduled_end_time = $endTime;
        $serviceCall->last_modified_at = CarbonImmutable::now();
        $serviceCall->save();

        //Update lula appointment
        $lulaAppointment->rescheduled_reason = $reason;
        $lulaAppointment->scheduled_start_time = $startTime;
        $lulaAppointment->scheduled_end_time = $endTime;
        $lulaAppointment->external_appointment_reference_id = $externalScheduledReferenceId;
        $lulaAppointment->save();

        // Create service call activity log for this action
        $eventAttributes = [
            'to' => $serviceCall->state->label(),
            'to_color_class' => $serviceCall->state->colorClass(),
            'slug_of_to' => $serviceCall->state->getValue(),
            'trip_id' => $serviceCall->work_order_service_call_uuid,
            'trip_type' => ScheduleTypes::LULA_PRO(),
            'wo_reference_number' => $lulaAppointment->work_order_reference_number,
            'is_rescheduled' => true,
            'rescheduled_reason' => $reason,
            'appointment_detail' => [
                'schedule_start_date' => $serviceCall->scheduled_start_time->toIso8601String(),
                'schedule_end_date' => $serviceCall->scheduled_end_time->toIso8601String(),
            ],
        ];

        event(new TripReScheduled(
            $workOrder->work_order_id,
            $eventAttributes,
        ));

        $additionalData = [
            'action' => ServiceCallActionTypes::RE_SCHEDULED(),
            'type' => 'start',
        ];

        $this->createServiceLog(
            $serviceCall,
            $workOrder,
            $task,
            $additionalData
        );
    }

    /**
     * Start work order
     *
     * @param  array<int|string,mixed>  $workOrderDetails
     */
    private function startWorkOrder(array $workOrderDetails): void
    {
        $workOrder = WorkOrder::with([
            'organization:organization_id,organization_uuid',
            'tasks' => function ($query) {
                return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                    ->with([
                        'latestServiceCalls' => function ($query) {
                            return $query->select(
                                'work_order_service_calls.work_order_service_call_id',
                                'work_order_service_calls.work_order_service_call_uuid',
                                'work_order_service_calls.work_order_service_call_number',
                                'work_order_service_calls.lula_appointment_id',
                                'work_order_service_calls.state',
                                'work_order_service_calls.status',
                                'work_order_service_calls.last_modified_at',
                                'work_order_service_calls.created_at',
                            )->with([
                                'lulaAppointment' => function ($query) {
                                    return $query->select(
                                        'lula_appointment_id',
                                        'lula_appointment_uuid',
                                        'work_order_reference_number',
                                        'actual_start_time',
                                        'service_category_label',
                                    );
                                },
                            ]);
                        },
                    ]);
            },
        ])
            ->where('vendor_work_order_id', $workOrderDetails['workOrderId'])
            ->select(
                'work_order_id', 'work_order_uuid', 'state', 'organization_id', 'vendor_work_order_id', 'state_updated_at', 'work_order_status_id'
            )
            ->firstOrFail();

        $task = $workOrder->tasks->firstOrFail();
        $serviceCall = $task->latestServiceCalls->firstOrFail();

        if (empty($serviceCall->lulaAppointment)) {
            throw LulaWebhookException::lulaAppointmentNotExists();
        }

        $lulaAppointment = $serviceCall->lulaAppointment;

        if (! $workOrder->state->equals(WorkInProgress::class)) {
            $this->toWorkOrderWorkInProgress($workOrder, $task);
        }

        if (! $serviceCall->state->equals(Working::class)) {
            $this->toTripWorking($workOrder, $task, $serviceCall, $lulaAppointment);
        }

        $updatedDataForPusher = [
            'work_order_id' => $workOrder->work_order_uuid,
            'type' => 'trip-updated',
            'appointment' => [
                'appointment_id' => $serviceCall->work_order_service_call_uuid,
                'status' => [
                    'label' => $serviceCall->state->label(),
                    'value' => $serviceCall->state->getValue(),
                    'color_class' => $serviceCall->state->colorClass(),
                ],
                'last_modified_at' => $serviceCall->last_modified_at?->toIso8601String(),
                'category' => $lulaAppointment->service_category_label,
            ],
            'work_order' => [
                'status' => [
                    'label' => $workOrder->state->label(),
                    'value' => $workOrder->state->getValue(),
                    'color_class' => $workOrder->state->colorClass(),
                ],
            ],
        ];

        // Trigger event for work order update
        event(new WorkOrderUpdate($workOrder->work_order_uuid, $updatedDataForPusher));
    }

    private function toWorkOrderWorkInProgress(WorkOrder &$workOrder, WorkOrderTask &$task): void
    {
        // Update work order status
        $oldState = $workOrder->state;
        $workOrderInProgressStatus = WorkOrderStatus::where('slug', WorkOrderStatusEnum::WORK_IN_PROGRESS())
            ->select('work_order_status_id')
            ->firstOrFail();

        $workOrder->work_order_status_id = $workOrderInProgressStatus->work_order_status_id;
        $workOrder->state = new WorkInProgress($workOrder);
        $workOrder->state_updated_at = CarbonImmutable::now();
        $workOrder->save();

        // trigger event
        $activityLogEventAttributes = [
            'from' => $oldState->label(),
            'from_color_class' => $oldState->colorClass(),
            'to' => $workOrder->state->label(),
            'to_color_class' => $workOrder->state->colorClass(),
            'slug_of_to' => $workOrder->state->getValue(),
        ];

        event(new WorkOrderWorkInProgress(
            $workOrder->work_order_id,
            $activityLogEventAttributes,
        ));
    }

    private function toTripWorking(WorkOrder &$workOrder, WorkOrderTask &$task, WorkOrderServiceCall &$serviceCall, LulaAppointment &$lulaAppointment): void
    {

        // Update service call status
        $serviceCall->state = new Working($serviceCall);
        $serviceCall->last_modified_at = CarbonImmutable::now();
        $serviceCall->save();

        $lulaAppointment->actual_start_time = CarbonImmutable::now();
        $lulaAppointment->save();

        // Trigger Event
        $eventAttributes = [
            'to' => $serviceCall->state->label(),
            'to_color_class' => $serviceCall->state->colorClass(),
            'slug_of_to' => $serviceCall->state->getValue(),
            'trip_id' => $serviceCall->work_order_service_call_uuid,
            'trip_type' => ScheduleTypes::LULA_PRO(),
            'wo_reference_number' => $lulaAppointment->work_order_reference_number,
        ];

        event(new TripWorking(
            $serviceCall->work_order_service_call_id,
            null,
            $eventAttributes,
        ));

        // Create a service call log
        $additionalData = [
            'action' => ServiceCallActionTypes::WORKING(),
            'type' => 'start',
        ];

        $this->createServiceLog(
            $serviceCall,
            $workOrder,
            $task,
            $additionalData
        );
    }

    /**
     * End the current trip
     *
     * @param  array<int|string,mixed>  $workOrderDetails
     */
    private function qualityCheck(array $workOrderDetails): void
    {
        $workOrder = WorkOrder::with([
            'organization:organization_id,organization_uuid',
            'tasks' => function ($query) {
                return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                    ->with([
                        'latestServiceCalls' => function ($query) {
                            return $query->select(
                                'work_order_service_calls.work_order_service_call_id',
                                'work_order_service_calls.work_order_service_call_uuid',
                                'work_order_service_calls.work_order_service_call_number',
                                'work_order_service_calls.lula_appointment_id',
                                'work_order_service_calls.scheduled_start_time',
                                'work_order_service_calls.scheduled_end_time',
                                'work_order_service_calls.state',
                                'work_order_service_calls.service_notes',
                                'work_order_service_calls.status',
                                'work_order_service_calls.trip_end_with',
                                'work_order_service_calls.trip_end_with_type',
                                'work_order_service_calls.trip_end_with_reason',
                                'work_order_service_calls.last_modified_at',
                                'work_order_service_calls.created_at',
                            )->with([
                                'lulaAppointment' => function ($query) {
                                    return $query->select(
                                        'lula_appointment_id',
                                        'lula_appointment_uuid',
                                        'work_order_reference_number',
                                        'service_category_label',
                                        'actual_start_time',
                                        'actual_end_time',
                                        'elapse_time_in_sec',
                                    );
                                },
                            ]);
                        },
                    ]);
            },
        ])
            ->where('vendor_work_order_id', $workOrderDetails['workOrderId'])
            ->select(
                'work_order_id', 'work_order_uuid', 'state', 'organization_id', 'vendor_work_order_id', 'state_updated_at', 'work_order_status_id'
            )
            ->firstOrFail();

        $task = $workOrder->tasks->firstOrFail();
        $serviceCall = $task->latestServiceCalls->firstOrFail();

        if (empty($serviceCall->lulaAppointment)) {
            throw LulaWebhookException::lulaAppointmentNotExists();
        }

        $lulaAppointment = $serviceCall->lulaAppointment;

        if (! $workOrder->state->equals(WorkInProgress::class)) {
            $this->toWorkOrderWorkInProgress($workOrder, $task);
        }

        $this->toTripComplete($workOrder, $task, $serviceCall, $lulaAppointment);

        $updatedDataForPusher = [
            'work_order_id' => $workOrder->work_order_uuid,
            'type' => 'trip-updated',
            'appointment' => [
                'appointment_id' => $serviceCall->work_order_service_call_uuid,
                'status' => [
                    'label' => $serviceCall->state->label(),
                    'value' => $serviceCall->state->getValue(),
                    'color_class' => $serviceCall->state->colorClass(),
                ],
                'last_modified_at' => $serviceCall->last_modified_at?->toIso8601String(),
            ],
            'work_order' => [
                'status' => [
                    'label' => $workOrder->state->label(),
                    'value' => $workOrder->state->getValue(),
                    'color_class' => $workOrder->state->colorClass(),
                ],
            ],
        ];

        // Trigger event for work order update
        event(new WorkOrderUpdate($workOrder->work_order_uuid, $updatedDataForPusher));
    }

    private function toTripComplete(WorkOrder &$workOrder, WorkOrderTask &$task, WorkOrderServiceCall &$serviceCall, LulaAppointment &$lulaAppointment): void
    {
        // Update service call status to end
        $newState = new Ended($serviceCall);
        $serviceCall->state = $newState;
        $serviceCall->last_modified_at = CarbonImmutable::now();
        $serviceCall->status = ServiceCallStatus::COMPLETED();
        $serviceCall->trip_end_with = Trip::ALL_COMPLETED();
        $serviceCall->save();

        // Update lula appointment work end time
        $lulaAppointment->actual_end_time = CarbonImmutable::now();
        $lulaAppointment->elapse_time_in_sec = $lulaAppointment->actual_end_time->diffInSeconds($lulaAppointment->actual_end_time);
        $lulaAppointment->save();

        // Trigger Trip End Event
        $activityLogEventAttributes = [
            'to' => $newState->label(),
            'to_color_class' => $newState->colorClass(),
            'slug_of_to' => $newState->getValue(),
            'trip_id' => $serviceCall->work_order_service_call_uuid,
            'trip_type' => ScheduleTypes::LULA_PRO(),
            'wo_reference_number' => $lulaAppointment->work_order_reference_number,
            'vendor_qa_note' => 'Undergoing Quality Assurance by Lula',
        ];

        event(new TripEnd(
            $serviceCall->work_order_service_call_id,
            null,
            $activityLogEventAttributes,
        ));

        // Mark as end the last service call log is working
        $serviceCall->load('latestServiceCallLogs:work_order_service_call_log_id,action,action_ended_at,work_order_service_call_id');
        if (
            ! empty($serviceCall->latestServiceCallLogs->first()) &&
            $serviceCall->latestServiceCallLogs->first()->action === ServiceCallActionTypes::WORKING()
        ) {
            $latestServiceCallLog = $serviceCall->latestServiceCallLogs->first();
            $latestServiceCallLog->action_ended_at = CarbonImmutable::now();
            $latestServiceCallLog->save();
        }

        // Create a new service call log fro this action
        $additionalData = [
            'action' => ServiceCallActionTypes::COMPLETED(),
            'type' => 'start',
            'trip_end_with' => Trip::ALL_COMPLETED(),
        ];

        $this->createServiceLog(
            $serviceCall,
            $workOrder,
            $task,
            $additionalData
        );
    }

    /**
     * Complete the work order
     *
     * @param  array<int|string,mixed>  $workOrderDetails
     */
    private function workComplete(array $workOrderDetails): void
    {
        $workOrder = WorkOrder::with([
            'organization:organization_id,organization_uuid',
            'tasks' => function ($query) {
                return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                    ->with([
                        'latestServiceCalls' => function ($query) {
                            return $query->select(
                                'work_order_service_calls.work_order_service_call_id',
                                'work_order_service_calls.work_order_service_call_uuid',
                                'work_order_service_calls.work_order_service_call_number',
                                'work_order_service_calls.lula_appointment_id',
                                'work_order_service_calls.state',
                                'work_order_service_calls.service_notes',
                                'work_order_service_calls.status',
                                'work_order_service_calls.trip_end_with',
                                'work_order_service_calls.trip_end_with_type',
                                'work_order_service_calls.trip_end_with_reason',
                                'work_order_service_calls.last_modified_at',
                                'work_order_service_calls.created_at',
                            )->with([
                                'lulaAppointment' => function ($query) {
                                    return $query->select(
                                        'lula_appointment_id',
                                        'lula_appointment_uuid',
                                        'work_order_reference_number',
                                        'service_category_label',
                                        'actual_start_time',
                                        'actual_end_time',
                                        'cancellation_reason',
                                        'elapse_time_in_sec',
                                    );
                                },
                            ]);
                        },
                    ]);
            },
        ])
            ->where('vendor_work_order_id', $workOrderDetails['workOrderId'])
            ->select(
                'work_order_id', 'work_order_uuid', 'state', 'organization_id', 'vendor_work_order_id', 'state_updated_at', 'work_order_status_id', 'canceled_at',
                'resolved_at'
            )
            ->firstOrFail();

        $task = $workOrder->tasks->firstOrFail();
        $serviceCall = $task->latestServiceCalls->firstOrFail();

        if (empty($serviceCall->lulaAppointment)) {
            throw LulaWebhookException::lulaAppointmentNotExists();
        }

        $lulaAppointment = $serviceCall->lulaAppointment;
        $completedDetails = $workOrderDetails['completedDetails'];

        if (! empty($completedDetails['type']) && $completedDetails['type'] === 'de-escalated') {
            $deEscalatedReason = $completedDetails['details'] ?? null;
            if (! $workOrder->state->equals(Completed::class)) {
                $this->toResolved($workOrder, $task);
            }

            if ($serviceCall->state->equals(Working::class, ServiceCallsPaused::class, EnRoute::class)) {
                $this->toTripEnd($workOrder, $task, $serviceCall, $lulaAppointment, $deEscalatedReason);
            } elseif (! $serviceCall->state->equals(Canceled::class, Ended::class)) {
                $this->toTripCancel($workOrder, $task, $serviceCall, $lulaAppointment, $deEscalatedReason);
            }
        } else {
            $serviceNote = $completedDetails['serviceNotes'][0]['notes'] ?? null;
            $serviceCall->service_notes = ! empty($serviceNote) ? trim($serviceNote) : null;
            $serviceCall->save();

            if (! empty($completedDetails['photos'])) {
                foreach ($completedDetails['photos'] as $photo) {
                    if (! empty($photo['uri']) && ! empty($photo['type'])) {
                        if (! empty($photo['photoId'])) {
                            if (Media::select('media_id')->where('external_media_reference_id', $photo['photoId'])->count()) {
                                continue;
                            }
                        }
                        $mediaType = null;
                        if ($photo['type'] === 'BeforePhoto') {
                            $mediaType = MediaType::BEFORE_MEDIA();
                        }
                        if ($photo['type'] === 'AfterPhoto') {
                            $mediaType = MediaType::AFTER_MEDIA();
                        }

                        if (! empty($mediaType)) {
                            AttachImageToWorkOrderTrip::dispatch(
                                $photo['uri'],
                                $workOrder->organization,
                                $workOrder->work_order_id,
                                $workOrder->work_order_uuid,
                                $task->work_order_task_id,
                                $serviceCall->work_order_service_call_id,
                                $mediaType,
                                $photo['photoId'],
                            )->afterCommit();
                        }
                    }
                }
            }

            if (! $serviceCall->state->equals(Ended::class)) {
                $this->toTripComplete($workOrder, $task, $serviceCall, $lulaAppointment);
            }
        }

        // Trigger event for work order update
        event(new WorkOrderUpdate($workOrder->work_order_uuid));
    }

    private function toResolved(WorkOrder &$workOrder, WorkOrderTask &$task): void
    {
        $oldState = $workOrder->state;
        // Change status
        $workOrderStatus = WorkOrderStatus::select('work_order_status_id')
            ->where('slug', WorkOrderStatusEnum::COMPLETED())->firstOrFail();

        $workOrder->work_order_status_id = $workOrderStatus->work_order_status_id;
        $workOrder->state = new Completed($workOrder);
        $workOrder->state_updated_at = CarbonImmutable::now();
        $workOrder->resolved_at = CarbonImmutable::now();
        $workOrder->save();

        // trigger event
        $activityLogEventAttributes = [
            'from' => $oldState->label(),
            'from_color_class' => $oldState->colorClass(),
            'to' => $workOrder->state->label(),
            'to_color_class' => $workOrder->state->colorClass(),
            'slug_of_to' => $workOrder->state->getValue(),
        ];

        event(new WorkOrderResolve(
            $workOrder->work_order_id,
            $task->work_order_task_id,
            $activityLogEventAttributes,
        ));
    }

    /**
     * Cancel work order
     *
     * @param  array<int|string,mixed>  $workOrderDetails
     */
    private function cancelWorkOrder(array $workOrderDetails): void
    {
        $workOrder = WorkOrder::with([
            'organization:organization_id,organization_uuid',
            'tasks' => function ($query) {
                return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                    ->with([
                        'latestServiceCalls' => function ($query) {
                            return $query->select(
                                'work_order_service_calls.work_order_service_call_id',
                                'work_order_service_calls.work_order_service_call_uuid',
                                'work_order_service_calls.work_order_service_call_number',
                                'work_order_service_calls.lula_appointment_id',
                                'work_order_service_calls.state',
                                'work_order_service_calls.status',
                                'work_order_service_calls.last_modified_at',
                                'work_order_service_calls.created_at',
                            )->with([
                                'lulaAppointment' => function ($query) {
                                    return $query->select(
                                        'lula_appointment_id',
                                        'lula_appointment_uuid',
                                        'work_order_reference_number',
                                        'service_category_label',
                                        'cancellation_reason',
                                    );
                                },
                            ]);
                        },
                    ]);
            },
        ])
            ->where('vendor_work_order_id', $workOrderDetails['workOrderId'])
            ->select(
                'work_order_id', 'work_order_uuid', 'state', 'organization_id', 'vendor_work_order_id', 'state_updated_at', 'work_order_status_id', 'canceled_at',
                'canceled_reason', 'vendor_id', 'vendor_work_order_id'
            )
            ->firstOrFail();

        $task = $workOrder->tasks->firstOrFail();
        $serviceCall = $task->latestServiceCalls->firstOrFail();

        if (empty($serviceCall->lulaAppointment)) {
            throw LulaWebhookException::lulaAppointmentNotExists();
        }
        $lulaAppointment = $serviceCall->lulaAppointment;
        $cancelReason = $workOrderDetails['cancelDetails']['details'] ?? 'Lula canceled the work order';

        if (! $workOrder->state->equals(WorkOrderCanceledState::class)) {
            if (! $workOrder->state->equals(ReadyToSchedule::class)) {
                $this->toReadyToSchedule($workOrder, $task);
            }

            if (! $serviceCall->state->equals(Canceled::class)) {
                $this->toTripCancel($workOrder, $task, $serviceCall, $lulaAppointment, $cancelReason);
            }

            // Trigger event for work order update
            event(new WorkOrderUpdate($workOrder->work_order_uuid));
        }
    }

    private function toReadyToSchedule(WorkOrder &$workOrder, WorkOrderTask &$task): void
    {
        $oldWorkOrderState = $workOrder->state;
        // Change status
        $workOrderStatus = WorkOrderStatus::select('work_order_status_id')
            ->where('slug', WorkOrderStatusEnum::READY_TO_SCHEDULE())
            ->firstOrFail();

        $workOrder->work_order_status_id = $workOrderStatus->work_order_status_id;
        $workOrder->state = new ReadyToSchedule($workOrder);
        $workOrder->state_updated_at = CarbonImmutable::now();
        $workOrder->save();

        $activityLogEventAttribute = [
            'from' => $oldWorkOrderState->label(),
            'from_color_class' => $oldWorkOrderState->colorClass(),
            'to' => $workOrder->state->label(),
            'to_color_class' => $workOrder->state->colorClass(),
            'slug_of_to' => $workOrder->state->getValue(),
        ];

        event(new WorkOrderReadyToSchedule(
            $workOrder->work_order_id,
            $activityLogEventAttribute,
        ));
    }

    // private function toWorkOrderCancel(WorkOrder &$workOrder, WorkOrderTask &$task, string $canceledReason): void
    // {
    //     $oldState = $workOrder->state;
    //     $workOrder->canceled_at = CarbonImmutable::now();
    //     $workOrder->canceled_reason = $canceledReason;

    //     // Change status
    //     $workOrderCancelledStatus = WorkOrderStatus::where('slug', WorkOrderStatusEnum::CANCELED())
    //         ->select('work_order_status_id')
    //         ->firstOrFail();

    //     $workOrder->work_order_status_id = $workOrderCancelledStatus->work_order_status_id;
    //     $workOrder->state = new WorkOrderCanceledState($workOrder);
    //     $workOrder->state_updated_at = CarbonImmutable::now();
    //     $workOrder->save();

    //     // trigger event
    //     $activityLogEventAttributes = [
    //         'from' => $oldState->label(),
    //         'from_color_class' => $oldState->colorClass(),
    //         'to' => $workOrder->state->label(),
    //         'to_color_class' => $workOrder->state->colorClass(),
    //         'slug_of_to' => $workOrder->state->getValue(),
    //         'canceled_reason' => $canceledReason,
    //     ];

    //     event(new WorkOrderCanceled(
    //         $workOrder->work_order_id,
    //         $task->work_order_task_id,
    //         $activityLogEventAttributes,
    //     ));
    // }

    private function toTripCancel(WorkOrder &$workOrder, WorkOrderTask &$task, WorkOrderServiceCall &$serviceCall, LulaAppointment &$lulaAppointment, ?string $canceledReason = null): void
    {
        // Update service call status to end
        $serviceCall->state = new Canceled($serviceCall);
        $serviceCall->last_modified_at = CarbonImmutable::now();
        $serviceCall->status = ServiceCallStatus::CANCELED();
        $serviceCall->save();

        $lulaAppointment->cancellation_reason = $canceledReason;
        $lulaAppointment->save();

        // Trigger Trip End Event
        $activityLogEventAttributes = [
            'to' => $serviceCall->state->label(),
            'to_color_class' => $serviceCall->state->colorClass(),
            'slug_of_to' => $serviceCall->state->getValue(),
            'trip_id' => $serviceCall->work_order_service_call_uuid,
            'trip_type' => ScheduleTypes::LULA_PRO(),
            'wo_reference_number' => $lulaAppointment->work_order_reference_number,
        ];

        if (! empty($canceledReason)) {
            $activityLogEventAttributes['cancel_reason'] = $canceledReason;
        }

        event(new TripCanceled(
            $workOrder->work_order_id,
            $task->work_order_task_id,
            $activityLogEventAttributes,
        ));

        // Create a new service call log fro this action
        $additionalData = [
            'action' => ServiceCallActionTypes::CANCELED(),
            'type' => 'start',
        ];

        $this->createServiceLog(
            $serviceCall,
            $workOrder,
            $task,
            $additionalData
        );
    }

    /**
     * Change work order trip (lula trip) status.
     *
     * @param  array<int|string,mixed>  $workOrderDetails
     */
    private function workPaused(array $workOrderDetails): void
    {
        $workOrder = WorkOrder::with([
            'organization:organization_id,organization_uuid',
            'timezone:timezone_id,name',
            'tasks' => function ($query) {
                return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                    ->with([
                        'latestServiceCalls' => function ($query) {
                            return $query->select(
                                'work_order_service_calls.work_order_service_call_id',
                                'work_order_service_calls.work_order_service_call_uuid',
                                'work_order_service_calls.work_order_service_call_number',
                                'work_order_service_calls.lula_appointment_id',
                                'work_order_service_calls.scheduled_start_time',
                                'work_order_service_calls.scheduled_end_time',
                                'work_order_service_calls.state',
                                'work_order_service_calls.service_notes',
                                'work_order_service_calls.status',
                                'work_order_service_calls.trip_end_with',
                                'work_order_service_calls.trip_end_with_type',
                                'work_order_service_calls.trip_end_with_reason',
                                'work_order_service_calls.last_modified_at',
                                'work_order_service_calls.timer_paused_at',
                                'work_order_service_calls.timer_resumed_at',
                                'work_order_service_calls.timer_paused_reason',
                                'work_order_service_calls.created_at',
                            )->with([
                                'lulaAppointment' => function ($query) {
                                    return $query->select(
                                        'lula_appointment_id',
                                        'lula_appointment_uuid',
                                        'work_order_reference_number',
                                        'service_category_label',
                                        'scheduled_start_time',
                                        'scheduled_end_time',
                                        'actual_start_time',
                                        'actual_end_time',
                                        'paused_reason',
                                        'rescheduled_reason',
                                        'cancellation_reason',
                                        'estimated_return_start_time',
                                        'estimated_return_end_time',
                                        'elapse_time_in_sec',
                                    );
                                },
                            ]);
                        },
                    ]);
            },
        ])
            ->where('vendor_work_order_id', $workOrderDetails['workOrderId'])
            ->select(
                'work_order_id', 'work_order_uuid', 'state', 'organization_id', 'vendor_work_order_id', 'state_updated_at', 'work_order_status_id',
                'timezone_id'
            )
            ->firstOrFail();

        $task = $workOrder->tasks->firstOrFail();
        $serviceCall = $task->latestServiceCalls->firstOrFail();
        $pauseDetails = $workOrderDetails['pauseDetails'] ?? null;

        if (empty($serviceCall->lulaAppointment)) {
            throw LulaWebhookException::lulaAppointmentNotExists();
        }

        $lulaAppointment = $serviceCall->lulaAppointment;

        $pausedReason = $pauseDetails['reason'] ?? null;

        if (! empty($pausedReason) && ! empty($pauseDetails['details'])) {
            $pausedReason = trim($pausedReason . ': ' . $pauseDetails['details']);
        }

        if (empty($pauseDetails) || empty($pauseDetails['estimatedReturnDateUTC'])) {
            $this->toWorkOrderPause($workOrder, $task, $pausedReason);
            $this->toTripPause($workOrder, $task, $serviceCall, $lulaAppointment, $pausedReason);
        } else {
            $timezone = $workOrder->timezone->name ?? config('settings.default_timezone');
            $returnStartDate = CarbonImmutable::parse($pauseDetails['estimatedReturnDateUTC'])->setTimezone($timezone);
            $returnEndDate = CarbonImmutable::parse($pauseDetails['estimatedReturnDateUTC'])->setTimezone($timezone)->addHours(4);

            if ($returnEndDate->isStartOfDay()) {
                $returnEndDate = CarbonImmutable::parse($pauseDetails['estimatedReturnDateUTC'])->setTimezone($timezone)
                    ->endOfDay();
            }

            $this->toTripReScheduleDueToPause(
                $workOrder,
                $task,
                $serviceCall,
                $lulaAppointment,
                $returnStartDate->setTimezone('UTC'),
                $returnEndDate->setTimezone('UTC'),
                $pausedReason
            );
        }

        $lulaAppointment->paused_reason = $pausedReason;
        $lulaAppointment->save();

        // Trigger event for work order update
        event(new WorkOrderUpdate($workOrder->work_order_uuid));
    }

    private function toWorkOrderPause(WorkOrder &$workOrder, WorkOrderTask &$task, ?string $pausedReason = null): void
    {
        $oldState = $workOrder->state;
        $workOrderPausedStatus = WorkOrderStatus::where('slug', WorkOrderStatusEnum::PAUSED())
            ->select('work_order_status_id')
            ->firstOrFail();

        $workOrder->state = new Paused($workOrder);
        $workOrder->work_order_status_id = $workOrderPausedStatus->work_order_status_id;
        $workOrder->paused_reason = $pausedReason;
        $workOrder->paused_at = CarbonImmutable::now();
        $workOrder->state_updated_at = CarbonImmutable::now();
        $workOrder->save();

        // trigger event
        $activityLogEventAttributes = [
            'from' => $oldState->label(),
            'from_color_class' => $oldState->colorClass(),
            'to' => $workOrder->state->label(),
            'to_color_class' => $workOrder->state->colorClass(),
            'slug_of_to' => $workOrder->state->getValue(),
            'paused_reason' => $pausedReason,
        ];

        event(new WorkOrderPaused(
            $workOrder->work_order_id,
            $task->work_order_task_id,
            $activityLogEventAttributes,
        ));
    }

    private function toTripPause(WorkOrder &$workOrder, WorkOrderTask &$task, WorkOrderServiceCall &$serviceCall, LulaAppointment &$lulaAppointment, ?string $pausedReason = null): void
    {
        $serviceCall->state = new ServiceCallsPaused($serviceCall);
        $serviceCall->last_modified_at = CarbonImmutable::now();
        $serviceCall->timer_paused_at = CarbonImmutable::now();
        $serviceCall->timer_paused_reason = ! empty($pausedReason) ? trim($pausedReason) : null;
        $serviceCall->save();

        // Trigger Event
        $eventAttributes = [
            'to' => $serviceCall->state->label(),
            'to_color_class' => $serviceCall->state->colorClass(),
            'slug_of_to' => $serviceCall->state->getValue(),
            'trip_type' => ScheduleTypes::LULA_PRO(),
            'trip_id' => $serviceCall->work_order_service_call_uuid,
            'wo_reference_number' => $lulaAppointment->work_order_reference_number,
        ];

        event(new TripPaused(
            $serviceCall->work_order_service_call_id,
            null,
            $eventAttributes,
        ));
    }

    /**
     * Add note
     *
     * @param  array<int|string,mixed>  $workOrderDetails
     */
    private function noteAdded(array $workOrderDetails): void
    {
        $workOrder = WorkOrder::with([
            'organization:organization_id,organization_uuid',
            'tasks' => function ($query) {
                return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                    ->with([
                        'diagnosis:problem_diagnosis_id,problem_sub_category_id',
                        'diagnosis.subCategory:problem_sub_category_id,problem_category_id',
                        'diagnosis.subCategory.problemCategory:problem_category_id,label',
                    ]);
            },
            'property:property_id,street_address,unit_number,city,postal_zip_code,country_id,state_id',
            'property.state:name,state_id',
        ])
            ->where('vendor_work_order_id', $workOrderDetails['workOrderId'])
            ->select(
                'work_order_id', 'work_order_uuid', 'state', 'organization_id', 'work_order_status_id', 'property_id',
                'work_order_number'
            )
            ->firstOrFail();

        $task = $workOrder->tasks->firstOrFail();

        $vendor = Vendor::select('vendor_id')
            ->where('service', Service::LULA())
            ->firstOrFail();

        $workOrderNote = WorkOrderNote::create([
            'organization_id' => $workOrder->organization_id,
            'work_order_id' => $workOrder->work_order_id,
            'work_order_status' => $workOrder->state->getValue(),
            'vendor_id' => $vendor->vendor_id,
            'note_type' => WorkOrderNoteTypes::VENDOR_NOTE(),
            'note' => trim($workOrderDetails['note']['note']),
        ]);

        WorkOrderNoteLog::create([
            'organization_id' => $workOrder->organization_id,
            'work_order_note_id' => $workOrderNote->work_order_note_id,
            'vendor_id' => $vendor->vendor_id,
            'note' => trim($workOrderDetails['note']['note']),
        ]);

        WorkOrderNoteCreated::dispatch($workOrderNote->work_order_note_id);
    }

    /**
     * Add note
     *
     * @param  array<int|string,mixed>  $workOrderDetails
     */
    private function createInvoice(array $workOrderDetails): void
    {
        $workOrder = WorkOrder::with([
            'organization:organization_id,organization_uuid',
            'tasks' => function ($query) {
                return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                    ->with([
                        'latestServiceCalls' => function ($query) {
                            return $query->select(
                                'work_order_service_calls.work_order_service_call_id',
                                'work_order_service_calls.work_order_service_call_uuid',
                                'work_order_service_calls.work_order_id',
                                'work_order_service_calls.lula_appointment_id',
                                'work_order_service_calls.technician_appointment_id',
                                'work_order_service_calls.service_notes',
                                'work_order_service_calls.state',
                                'work_order_service_calls.last_modified_user',
                                'work_order_service_calls.last_modified_at',
                                'work_order_service_calls.created_at',
                            )->with([
                                'lulaAppointment:lula_appointment_id,work_order_reference_number,service_category_label',
                                'createdQuote:quote_id,quote_uuid,work_order_service_call_id,status',
                                'appointment:technician_appointment_id',
                            ]);
                        },
                    ]);
            },
        ])
            ->where('vendor_work_order_id', $workOrderDetails['workOrderId'])
            ->select(
                'work_order_id', 'work_order_uuid', 'state', 'organization_id',
                'canceled_at', 'canceled_reason', 'work_order_status_id', 'state_updated_at', 'vendor_id'
            )
            ->firstOrFail();

        $task = $workOrder->tasks->firstOrFail();
        $serviceCall = $task->latestServiceCalls->firstOrFail();

        if (empty($serviceCall->lulaAppointment)) {
            throw LulaWebhookException::lulaAppointmentNotExists();
        }

        $lulaAppointment = $serviceCall->lulaAppointment;

        $invoiceDetails = $workOrderDetails['invoices'] ?? [];

        if (count($invoiceDetails)) {
            $invoiceItems = $invoiceDetails['invoices'] ?? [];
            if (count($invoiceItems)) {
                foreach ($invoiceItems as $invoiceItem) {
                    $lineItems = [];
                    $lineItems['laborCost'] = $invoiceItem['totalLaborAmountInCents'] ?? 0;
                    $lineItems['materialCost'] = $invoiceItem['totalPartsAmountInCents'] ?? 0;

                    VendorExternalInvoice::updateOrCreate([
                        'vendor_external_invoice_reference_id' => $invoiceItem['invoiceId'] ?? null,
                        'organization_id' => $workOrder->organization_id,
                        'work_order_id' => $workOrder->work_order_id,
                        'work_order_task_id' => $task->work_order_task_id,
                    ], [
                        'vendor_external_invoice_url' => $invoiceItem['uri'] ?? null,
                        'work_order_service_call_id' => $serviceCall->work_order_service_call_id,
                        'vendor_id' => $workOrder->vendor_id,
                        'description' => $invoiceItem['invoiceNotes'] ?? $serviceCall->service_notes,
                        'amount_in_cents' => $invoiceItem['amountCents'] ?? null,
                        'amount_remaining_cents' => $invoiceItem['amountRemainingCents'] ?? null,
                        'extras' => $lineItems,
                    ]);

                    if (! empty($invoiceItem['photos'])) {
                        foreach ($invoiceItem['photos'] as $photo) {
                            if (! empty($photo['photoId'])) {
                                if (Media::select('media_id')->where('external_media_reference_id', $photo['photoId'])->count()) {
                                    continue;
                                }
                            }
                            if (! empty($photo['uri']) && ! empty($photo['type'])) {
                                $mediaType = null;
                                if ($photo['type'] === 'BeforePhoto') {
                                    $mediaType = MediaType::BEFORE_MEDIA();
                                }
                                if ($photo['type'] === 'AfterPhoto') {
                                    $mediaType = MediaType::AFTER_MEDIA();
                                }

                                if (! empty($mediaType)) {
                                    AttachImageToWorkOrderTrip::dispatch(
                                        $photo['uri'],
                                        $workOrder->organization,
                                        $workOrder->work_order_id,
                                        $workOrder->work_order_uuid,
                                        $task->work_order_task_id,
                                        $serviceCall->work_order_service_call_id,
                                        $mediaType,
                                        $photo['photoId'],
                                    )->afterCommit();
                                }
                            }
                        }
                    }
                }
            }

            // Change Work Order Status to Quality Check
            if (! $workOrder->state->equals(QualityCheck::class)) {
                $this->toWorkOrderQualityCheck($workOrder, $task);
            }

            if (! $serviceCall->state->equals(Ended::class)) {
                $this->toTripComplete($workOrder, $task, $serviceCall, $lulaAppointment);
            }

            $updatedDataForPusher = [
                'work_order_id' => $workOrder->work_order_uuid,
                'type' => 'trip-updated',
                'appointment' => [
                    'appointment_id' => $serviceCall->work_order_service_call_uuid,
                    'status' => [
                        'label' => $serviceCall->state->label(),
                        'value' => $serviceCall->state->getValue(),
                        'color_class' => $serviceCall->state->colorClass(),
                    ],
                    'last_modified_at' => $serviceCall->last_modified_at?->toIso8601String(),
                ],
                'work_order' => [
                    'status' => [
                        'label' => $workOrder->state->label(),
                        'value' => $workOrder->state->getValue(),
                        'color_class' => $workOrder->state->colorClass(),
                    ],
                ],
            ];

            // Trigger event for work order update
            event(new WorkOrderUpdate($workOrder->work_order_uuid, $updatedDataForPusher));
        }
    }

    private function toWorkOrderQualityCheck(WorkOrder &$workOrder, WorkOrderTask &$task): void
    {
        $oldState = $workOrder->state;
        $workOrderPausedStatus = WorkOrderStatus::where('slug', WorkOrderStatusEnum::QUALITY_CHECK())
            ->select('work_order_status_id')
            ->firstOrFail();

        $workOrder->state = new QualityCheck($workOrder);
        $workOrder->work_order_status_id = $workOrderPausedStatus->work_order_status_id;
        $workOrder->work_completed_at = CarbonImmutable::now();
        $workOrder->state_updated_at = CarbonImmutable::now();
        $workOrder->save();

        // trigger event
        $activityLogEventAttributes = [
            'from' => $oldState->label(),
            'from_color_class' => $oldState->colorClass(),
            'to' => $workOrder->state->label(),
            'to_color_class' => $workOrder->state->colorClass(),
            'slug_of_to' => $workOrder->state->getValue(),
        ];

        event(new WorkOrderQualityCheck(
            $workOrder->work_order_id,
            $activityLogEventAttributes,
        ));
    }

    private function toWorkOrderClaimPending(WorkOrder &$workOrder, WorkOrderTask &$task): void
    {
        // Change status
        $oldState = $workOrder->state;
        $workOrderCancelledStatus = WorkOrderStatus::where('slug', WorkOrderStatusEnum::CLAIM_PENDING())
            ->firstOrFail('work_order_status_id');
        $workOrder->work_order_status_id = $workOrderCancelledStatus->work_order_status_id;
        $workOrder->state = new ClaimPending($workOrder);
        $workOrder->state_updated_at = CarbonImmutable::now();
        $workOrder->save();

        // trigger event
        $activityLogEventAttributes = [
            'from' => $oldState->label(),
            'from_color_class' => $oldState->colorClass(),
            'to' => $workOrder->state->label(),
            'to_color_class' => $workOrder->state->colorClass(),
            'slug_of_to' => $workOrder->state->getValue(),
        ];

        event(new WorkOrderClaimPending(
            $workOrder->work_order_id,
            $activityLogEventAttributes,
        ));
    }

    private function toTripReScheduleDueToPause(WorkOrder &$workOrder, WorkOrderTask &$task, WorkOrderServiceCall &$serviceCall, LulaAppointment &$lulaAppointment, CarbonImmutable $startDate, CarbonImmutable $endDate, ?string $reason = null): void
    {
        if (! $workOrder->state->equals(WorkOrdersScheduled::class)) {
            /**----------------------------- End current trip ------------------------------**/
            // Update service call status to end
            $serviceCall->state = new Ended($serviceCall);
            $serviceCall->last_modified_at = CarbonImmutable::now();
            $serviceCall->status = ServiceCallStatus::COMPLETED();
            $serviceCall->trip_end_with = Trip::NO_WORK();
            $serviceCall->trip_end_with_type = Trip::OTHER();
            $serviceCall->trip_end_with_reason = $reason;
            $serviceCall->save();

            // Update lula appointment work end time
            if (! empty($lulaAppointment->actual_start_time)) {
                $lulaAppointment->actual_end_time = CarbonImmutable::now();
                $lulaAppointment->elapse_time_in_sec = $lulaAppointment->actual_end_time->diffInSeconds($lulaAppointment->actual_end_time);
                $lulaAppointment->save();
            }

            // Trigger Trip End Event
            $activityLogEventAttributes = [
                'to' => $serviceCall->state->label(),
                'to_color_class' => $serviceCall->state->colorClass(),
                'slug_of_to' => $serviceCall->state->getValue(),
                'trip_id' => $serviceCall->work_order_service_call_uuid,
                'trip_type' => ScheduleTypes::LULA_PRO(),
                'wo_reference_number' => $lulaAppointment->work_order_reference_number,
            ];

            event(new TripEnd(
                $serviceCall->work_order_service_call_id,
                null,
                $activityLogEventAttributes,
            ));

            // Mark as end the last service call log is working
            $serviceCall->load('latestServiceCallLogs:work_order_service_call_log_id,action,action_ended_at,work_order_service_call_id');
            if (
                ! empty($serviceCall->latestServiceCallLogs->first()) &&
                $serviceCall->latestServiceCallLogs->first()->action === ServiceCallActionTypes::WORKING()
            ) {
                $latestServiceCall = $serviceCall->latestServiceCallLogs->first();
                $latestServiceCall->action_ended_at = CarbonImmutable::now();
                $latestServiceCall->save();
            }

            // Create a new service call log fro this action
            $additionalData = [
                'action' => ServiceCallActionTypes::COMPLETED(),
                'type' => 'start',
                'trip_end_with' => Trip::NO_WORK(),
                'trip_end_with_type' => Trip::OTHER(),
                'trip_end_with_reason' => $reason,
            ];

            $this->createServiceLog(
                $serviceCall,
                $workOrder,
                $task,
                $additionalData
            );

            /**----------------------------- Change Work order status ------------------------------**/
            $oldWorkOrderState = $workOrder->state;
            $workOrderStatus = WorkOrderStatus::select('work_order_status_id')->where('slug', WorkOrderStatusEnum::SCHEDULED())->firstOrFail();

            $workOrder->state = new WorkOrdersScheduled($workOrder);
            $workOrder->work_order_status_id = $workOrderStatus->work_order_status_id;
            $workOrder->state_updated_at = CarbonImmutable::now();
            $workOrder->save();

            $eventAttributes = [
                'from' => $oldWorkOrderState->label(),
                'from_color_class' => $oldWorkOrderState->colorClass(),
                'to' => $workOrder->state->label(),
                'to_color_class' => $workOrder->state->colorClass(),
                'slug_of_to' => $workOrder->state->getValue(),
                'is_rescheduled' => true,
            ];

            event(new WorkOrderReScheduled(
                $workOrder->work_order_id,
                $task->work_order_task_id,
                $eventAttributes
            ));

            /**----------------------------- Create new lula trip ------------------------------**/
            $newLulaAppointment = LulaAppointment::create([
                'organization_id' => $workOrder->organization_id,
                'work_order_reference_number' => $lulaAppointment->work_order_reference_number,
                'service_category_label' => $lulaAppointment->service_category_label,
                'scheduled_start_time' => $startDate,
                'scheduled_end_time' => $endDate,
            ]);

            if (empty($newLulaAppointment)) {
                throw LulaWebhookException::rescheduleFailed(__('Lula appointment creation failed'));
            }

            // Update all other service calls as inactive.
            $task->allServiceCalls()->update(['is_active' => Boolean::NO()]);

            // Create a new service call and service call task.
            $newServiceCall = $task->serviceCalls()->create([
                'organization_id' => $workOrder->organization_id,
                'work_order_id' => $workOrder->work_order_id,
                'work_order_service_call_number' => $task->latestServiceCalls()->count() + 1,
                'lula_appointment_id' => $newLulaAppointment->lula_appointment_id,
                'scheduled_start_time' => $startDate,
                'scheduled_end_time' => $endDate,
                'duration_minutes' => 240,
                'mode' => SchedulingMode::MANUAL(),
                'method' => SchedulingMethod::EFFICIENT(),
                'status' => ServiceCallStatus::ACTIVE(),
                'state' => Scheduled::$name,
                'is_active' => Boolean::YES(),
                'work_to_perform' => WorkToPerformTypes::HOURLY_TASK(),
                'quote_id' => null,
                'last_modified_user' => null,
                'last_modified_at' => CarbonImmutable::now(),
            ]);

            if (! $newServiceCall) {
                throw LulaWebhookException::rescheduleFailed(__('Reschedule failed: error occurs when service call create'));
            }

            WorkOrderServiceCallTask::create([
                'organization_id' => $workOrder->organization_id,
                'work_order_service_call_id' => $newServiceCall->work_order_service_call_id,
                'work_order_task_id' => $task->work_order_task_id,
            ]);

            // Create service call activity log for this action
            $eventAttributes = [
                'to' => $newServiceCall->state->label(),
                'to_color_class' => $newServiceCall->state->colorClass(),
                'slug_of_to' => $newServiceCall->state->getValue(),
                'trip_id' => $newServiceCall->work_order_service_call_uuid,
                'trip_type' => ScheduleTypes::LULA_PRO(),
                'wo_reference_number' => $newLulaAppointment->work_order_reference_number,
                'is_rescheduled' => true,
                'rescheduled_reason' => $reason,
                'appointment_detail' => [
                    'schedule_start_date' => $newServiceCall->scheduled_start_time?->toIso8601String(),
                    'schedule_end_date' => $newServiceCall->scheduled_end_time?->toIso8601String(),
                ],
            ];

            event(new TripReScheduled(
                $workOrder->work_order_id,
                $eventAttributes,
            ));

            $additionalData = [
                'action' => ServiceCallActionTypes::RE_SCHEDULED(),
                'type' => 'start',
            ];

            $this->createServiceLog(
                $serviceCall,
                $workOrder,
                $task,
                $additionalData
            );
        } else {
            $serviceCall->scheduled_start_time = $startDate;
            $serviceCall->scheduled_end_time = $endDate;
            $serviceCall->save();

            $lulaAppointment->scheduled_start_time = $startDate;
            $lulaAppointment->scheduled_end_time = $endDate;
            $lulaAppointment->save();

            // Create service call activity log for this action
            $eventAttributes = [
                'to' => $serviceCall->state->label(),
                'to_color_class' => $serviceCall->state->colorClass(),
                'slug_of_to' => $serviceCall->state->getValue(),
                'trip_id' => $serviceCall->work_order_service_call_uuid,
                'trip_type' => ScheduleTypes::LULA_PRO(),
                'wo_reference_number' => $lulaAppointment->work_order_reference_number,
                'is_rescheduled' => true,
                'rescheduled_reason' => $reason,
                'appointment_detail' => [
                    'schedule_start_date' => $serviceCall->scheduled_start_time->toIso8601String(),
                    'schedule_end_date' => $serviceCall->scheduled_end_time->toIso8601String(),
                ],
            ];

            event(new TripReScheduled(
                $workOrder->work_order_id,
                $eventAttributes,
            ));

            $additionalData = [
                'action' => ServiceCallActionTypes::RE_SCHEDULED(),
                'type' => 'start',
            ];

            $this->createServiceLog(
                $serviceCall,
                $workOrder,
                $task,
                $additionalData
            );
        }
    }

    private function createNewLulaTrip(WorkOrder &$workOrder, WorkOrderTask &$task, WorkOrderServiceCall &$serviceCall, LulaAppointment &$lulaAppointment): void
    {
        // Create new lula appointment
        $newLulaAppointment = LulaAppointment::create([
            'organization_id' => $workOrder->organization_id,
            'work_order_reference_number' => $lulaAppointment->work_order_reference_number,
            'service_category_label' => $lulaAppointment->service_category_label,
            'scheduled_start_time' => $lulaAppointment->scheduled_start_time ?? null,
            'scheduled_end_time' => $lulaAppointment->scheduled_end_time ?? null,
        ]);

        if (empty($newLulaAppointment)) {
            throw LulaWebhookException::rescheduleFailed(__('Lula appointment creation failed'));
        }

        // Update all other service calls as inactive.
        $task->allServiceCalls()->update(['is_active' => Boolean::NO()]);

        // Create a new service call and service call task.
        $newServiceCall = $task->serviceCalls()->create([
            'organization_id' => $workOrder->organization_id,
            'work_order_id' => $workOrder->work_order_id,
            'work_order_service_call_number' => $task->latestServiceCalls()->count() + 1,
            'lula_appointment_id' => $newLulaAppointment->lula_appointment_id,
            'scheduled_start_time' => $serviceCall->scheduled_start_time ?? null,
            'scheduled_end_time' => $serviceCall->scheduled_end_time ?? null,
            'duration_minutes' => 240,
            'mode' => SchedulingMode::MANUAL(),
            'method' => SchedulingMethod::EFFICIENT(),
            'status' => ServiceCallStatus::ACTIVE(),
            'state' => ScheduleInProgress::$name,
            'is_active' => Boolean::YES(),
            'work_to_perform' => WorkToPerformTypes::HOURLY_TASK(),
            'quote_id' => null,
            'last_modified_user' => null,
            'last_modified_at' => CarbonImmutable::now(),
        ]);

        if (! $newServiceCall) {
            throw LulaWebhookException::rescheduleFailed(__('Reschedule failed: error occurs when service call create'));
        }

        WorkOrderServiceCallTask::create([
            'organization_id' => $workOrder->organization_id,
            'work_order_service_call_id' => $newServiceCall->work_order_service_call_id,
            'work_order_task_id' => $task->work_order_task_id,
        ]);

        // Step 2 : Create activity log for this action
        $activityLogEventAttributes = [
            'to' => $newServiceCall->state->label(),
            'to_color_class' => $newServiceCall->state->colorClass(),
            'slug_of_to' => $newServiceCall->state->getValue(),
            'trip_id' => $newServiceCall->work_order_service_call_uuid,
            'trip_type' => ScheduleTypes::LULA_PRO(),
            'wo_reference_number' => $newLulaAppointment->work_order_reference_number,
        ];

        event(new TripScheduleInProgress(
            $workOrder->work_order_id,
            $task->work_order_task_id,
            $activityLogEventAttributes,
        ));

        // Step 3 : Create service log for this action
        $additionalData = [
            'action' => ServiceCallActionTypes::SCHEDULE_IN_PROGRESS(),
            'type' => 'start',
        ];

        $this->createServiceLog(
            $newServiceCall,
            $workOrder,
            $task,
            $additionalData
        );
    }

    private function toTripEnd(WorkOrder &$workOrder, WorkOrderTask &$task, WorkOrderServiceCall &$serviceCall, LulaAppointment &$lulaAppointment, ?string $reason = null): void
    {
        /**----------------------------- End current trip ------------------------------**/
        // Update service call status to end
        $serviceCall->state = new Ended($serviceCall);
        $serviceCall->last_modified_at = CarbonImmutable::now();
        $serviceCall->status = ServiceCallStatus::COMPLETED();
        $serviceCall->trip_end_with = Trip::NO_WORK();
        $serviceCall->trip_end_with_type = Trip::OTHER();
        $serviceCall->trip_end_with_reason = $reason;
        $serviceCall->save();

        // Update lula appointment work end time
        if (! empty($lulaAppointment->actual_start_time)) {
            $lulaAppointment->actual_end_time = CarbonImmutable::now();
            $lulaAppointment->elapse_time_in_sec = $lulaAppointment->actual_end_time->diffInSeconds($lulaAppointment->actual_end_time);
            $lulaAppointment->save();
        }

        // Trigger Trip End Event
        $activityLogEventAttributes = [
            'to' => $serviceCall->state->label(),
            'to_color_class' => $serviceCall->state->colorClass(),
            'slug_of_to' => $serviceCall->state->getValue(),
            'trip_id' => $serviceCall->work_order_service_call_uuid,
            'trip_type' => ScheduleTypes::LULA_PRO(),
            'wo_reference_number' => $lulaAppointment->work_order_reference_number,
        ];

        event(new TripEnd(
            $serviceCall->work_order_service_call_id,
            null,
            $activityLogEventAttributes,
        ));

        // Mark as end the last service call log is working
        $serviceCall->load('latestServiceCallLogs:work_order_service_call_log_id,action,action_ended_at,work_order_service_call_id');
        if (
            ! empty($serviceCall->latestServiceCallLogs->first()) &&
            $serviceCall->latestServiceCallLogs->first()->action === ServiceCallActionTypes::WORKING()
        ) {
            $latestServiceCall = $serviceCall->latestServiceCallLogs->first();
            $latestServiceCall->action_ended_at = CarbonImmutable::now();
            $latestServiceCall->save();
        }

        // Create a new service call log fro this action
        $additionalData = [
            'action' => ServiceCallActionTypes::COMPLETED(),
            'type' => 'start',
            'trip_end_with' => Trip::NO_WORK(),
            'trip_end_with_type' => Trip::OTHER(),
            'trip_end_with_reason' => $reason,
        ];

        $this->createServiceLog(
            $serviceCall,
            $workOrder,
            $task,
            $additionalData
        );
    }
}

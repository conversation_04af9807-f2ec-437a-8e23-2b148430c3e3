<?php

namespace App\Jobs\Webhook;

use App\Actions\Quotes\ApproveQuote;
use App\Actions\Quotes\RejectQuote;
use App\Actions\Quotes\RestoreQuote;
use App\Actions\Quotes\SubmitForApproval;
use App\Actions\Quotes\UpdateQuote;
use App\Actions\WorkOrders\CancelWorkOrder;
use App\Enums\MarkUpFeeTypes;
use App\Enums\QuoteStatus;
use App\Exceptions\NotFoundException;
use App\Exceptions\QuoteException;
use App\Exceptions\WebhookException;
use App\Helpers\Helper;
use App\Models\WorkOrder;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\WebhookClient\Jobs\ProcessWebhookJob as ProcessWebhookParentJob;

class ProcessWebhookJob extends ProcessWebhookParentJob
{
    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('webhook process job', [
            $this->webhookCall,
        ]);

        $workOrder = null;
        try {
            if (! isset($this->webhookCall->payload['data'])) {
                throw WebhookException::invalidEventPayload('data');
            }

            $workOrder = WorkOrder::with([
                'organization:organization_id,webhook_secret_key,webhook_enabled,webhook_api_url,webhook_secret_key,organization_uuid',
                'tasks.latestServiceCalls.appointment',
                'tasks.latestServiceCalls.createdQuote.quoteTasks.quoteTaskMaterials',
            ])
                ->whereUuid($this->webhookCall->payload['data']['workOrderId'])
                ->firstOrFail();

            if (! $workOrder) {
                throw NotFoundException::resourceNotFound('work order');
            }

            DB::transaction(
                fn () => match ($this->webhookCall->payload['eventType']) {
                    'core:workorder.quote.approved' => $this->approveQuote($workOrder),
                    'core:workorder.quote.rejected', 'core:workorder.quote.expired' => $this->declineQuote($workOrder),
                    'core:workorder.quote.restored' => $this->restoreQuote($workOrder),
                    'core:workorder.quote.updated', 'core:workorder.quoting' => $this->updateQuoteDetails($workOrder),
                    'core:workorder.quote.created' => $this->sendForApproval($workOrder),
                    default => WebhookException::invalidEventRecieved($this->webhookCall->payload['eventType'])
                }
            );
        } catch (NotFoundException|WebhookException|ModelNotFoundException|QuoteException $exception) {
            $this->saveExceptionDetails($exception, $workOrder);
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);
            throw QuoteException::webhookFailed($this->webhookCall->payload['eventType'] ?? '', $exception->getMessage());
        } catch (Exception $exception) {
            $this->saveExceptionDetails($exception, $workOrder);
            Helper::exceptionLog(exception: $exception, message: 'Exception occurred while handling webhook', notify: true);
            throw new Exception(__($exception->getMessage()));
        }
    }

    public function updateQuoteDetails(WorkOrder $workOrder): void
    {
        $workOrderTask = $workOrder->tasks->first();

        if (empty($workOrderTask)) {
            throw NotFoundException::resourceNotFound('work order task');
        }

        $quote = $workOrderTask->latestServiceCalls->first()?->createdQuote;

        if (empty($quote)) {
            Log::error('Quote not found for this work order task', [
                'workOrder' => $workOrder->work_order_id,
                'workOrderTask' => $workOrderTask->work_order_task_id,
            ]);
            throw NotFoundException::resourceNotFound('quote');
        }

        $quoteDetails = $this->webhookCall->payload['data']['quote'] ?? null;

        if (empty($quoteDetails)) {
            throw QuoteException::invalidPayload(__('Empty quote details'));
        }

        $payLoad = [];

        foreach ($quoteDetails['quoteTasks'] as $task) {
            $laborCostInCents = $task['laborCostInCents'] ?? $task['costInCents'] ?? 0;
            $markupFeeType = $task['markupFeeType'] ?? MarkUpFeeTypes::FIXED();
            $markupFeeTypeValue = $task['markupFeeInCents'] ?? 0;

            $taskDetails = [
                'quote_task_id' => $task['quoteTaskId'],
                'reference_id' => $task['referenceId'] ?? null,
                'description' => $task['description'],
                'markup_fee_type' => $markupFeeType,
                'markup_fee_type_value' => $markupFeeTypeValue,
                'labor_cost_in_cents' => $laborCostInCents,
            ];
            $materialDetails = [];
            if (! empty($task['quoteTaskMaterials'])) {
                foreach ($task['quoteTaskMaterials'] as $material) {
                    $materialDetails[] = [
                        'label' => $material['label'],
                        'markup_fee_type' => $material['markupFeeType'] ?? MarkUpFeeTypes::FIXED(),
                        'markup_fee_type_value' => $material['markupFeeInCents'] ?? 0,
                        'material_cost_in_cents' => $material['materialCostInCents'] ?? $material['costInCents'] ?? 0,
                        'quantity' => $material['quantity'],
                        'quantity_type' => $material['quantityType'],
                    ];
                }
            }
            $taskDetails['materials'] = $materialDetails;

            $media = [];
            if (! empty($task['media'])) {
                foreach ($task['media'] as $requestedMedia) {
                    $media[] = [
                        'uri' => $requestedMedia['uri'],
                        'media_id' => $requestedMedia['mediaUuid'] ?? null,
                        'reference_id' => $requestedMedia['referenceId'] ?? null,
                    ];
                }
            }
            $taskDetails['media'] = $media;
            $payLoad[] = $taskDetails;
        }

        UpdateQuote::make()->handle(
            $workOrder,
            $workOrderTask,
            $quote,
            $payLoad
        );
    }

    public function saveExceptionDetails(Exception $exception, ?WorkOrder $workOrder = null): void
    {
        $exceptionDetails = [
            'exception_class' => get_class($exception),
            'exception' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'line' => $exception->getLine(),
            'file' => $exception->getFile(),
            'work_order_id' => $workOrder->work_order_uuid ?? null,
        ];

        $this->webhookCall->exception = $exceptionDetails;
        $this->webhookCall->save();
    }

    private function declineQuote(WorkOrder $workOrder): void
    {
        $workOrderTask = $workOrder->tasks->first();

        if (empty($workOrderTask)) {
            throw NotFoundException::resourceNotFound('work order task');
        }

        $quote = $workOrderTask->latestServiceCalls->first()?->createdQuote;

        if (empty($quote)) {
            Log::error('Quote not found for this work order task', [
                'workOrder' => $workOrder->work_order_id,
                'workOrderTask' => $workOrderTask->work_order_task_id,
            ]);
            throw NotFoundException::resourceNotFound('quote');
        }

        if (empty($this->webhookCall->payload['eventType'])) {
            throw QuoteException::invalidPayload(__('Empty event type'));
        }
        $note = $this->webhookCall->payload['eventType'] == 'core:workorder.quote.rejected' ? 'Quote Rejected' : 'Quote Expired';

        RejectQuote::make()->handle($workOrder, $workOrderTask, $quote, true);

        // Refresh quote after quote status update.
        $workOrderTask->latestServiceCalls->first()?->createdQuote?->refresh();

        CancelWorkOrder::run($workOrder, $workOrderTask, null, $note);
    }

    private function restoreQuote(WorkOrder $workOrder): void
    {
        $workOrderTask = $workOrder->tasks->first();

        if (empty($workOrderTask)) {
            throw NotFoundException::resourceNotFound('work order task');
        }

        $quote = $workOrderTask->latestServiceCalls->first()?->createdQuote;

        if (empty($quote)) {
            Log::error('Quote not found for this work order task', [
                'workOrder' => $workOrder->work_order_id,
                'workOrderTask' => $workOrderTask->work_order_task_id,
            ]);
            throw NotFoundException::resourceNotFound('quote');
        }

        $quoteStatus = QuoteStatus::QUOTE_PENDING_REVIEW();
        if (
            ! empty($this->webhookCall->payload['data']['quote']['status']['slug']) &&
            $this->webhookCall->payload['data']['quote']['status']['slug'] === 'pending-approval'
        ) {
            $quoteStatus = QuoteStatus::PENDING_APPROVAL();
        }

        RestoreQuote::make()->handle($workOrder, $workOrderTask, $quote, $quoteStatus);
    }

    private function approveQuote(WorkOrder $workOrder): void
    {
        $workOrderTask = $workOrder->tasks->first();

        if (empty($workOrderTask)) {
            throw NotFoundException::resourceNotFound('work order task');
        }

        if (empty($this->webhookCall->payload['data']['quote']['quoteTasks'])) {
            throw QuoteException::invalidPayload(__('Quote task details not in the payload'));
        }

        $taskDetails = $this->webhookCall->payload['data']['quote']['quoteTasks'];
        $quote = $workOrder->tasks->first()?->latestServiceCalls->first()?->createdQuote;

        if (empty($quote)) {
            Log::error('Quote not found for this work order task', [
                'workOrder' => $workOrder->work_order_id,
                'workOrderTask' => $workOrder->tasks->first()?->work_order_task_id,
            ]);

            throw NotFoundException::resourceNotFound('quote');
        }

        $approvedTaskIds = $rejectedTaskIds = [];
        foreach ($taskDetails as $quoteTask) {
            if (! in_array($quoteTask['quoteTaskId'], $quote->quoteTasks->pluck('quote_task_uuid')->toArray())) {
                Log::error('Invalid quoteTaskId', [
                    'workOrder' => $workOrder->work_order_id,
                    'workOrderTask' => $workOrder->tasks->first()?->work_order_task_id,
                    'quoteTaskId' => $quoteTask['quoteTaskId'],

                ]);

                throw QuoteException::invalidQuoteTask();
            }

            if ($quoteTask['approved']) {
                $approvedTaskIds[] = $quoteTask['quoteTaskId'];
            } else {
                $rejectedTaskIds[] = $quoteTask['quoteTaskId'];
            }
        }

        ApproveQuote::make()->handle($workOrder, $workOrderTask, $quote, $approvedTaskIds, $rejectedTaskIds, true);
    }

    private function sendForApproval(WorkOrder $workOrder): void
    {
        $workOrderTask = $workOrder->tasks->first();

        if (empty($workOrderTask)) {
            throw NotFoundException::resourceNotFound('work order task');
        }

        $quote = $workOrderTask->latestServiceCalls->first()?->createdQuote;

        if (empty($quote)) {
            Log::error('Quote not found for this work order task', [
                'workOrder' => $workOrder->work_order_id,
                'workOrderTask' => $workOrderTask->work_order_task_id,
            ]);
            throw NotFoundException::resourceNotFound('quote');
        }

        SubmitForApproval::make()->handle($workOrder, $workOrderTask, $quote, true);
    }
}

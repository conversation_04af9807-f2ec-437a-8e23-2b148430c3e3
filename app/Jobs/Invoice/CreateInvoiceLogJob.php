<?php

namespace App\Jobs\Invoice;

use App\Enums\InvoiceLogActions;
use App\Helpers\Helper;
use App\Models\Invoice;
use App\Models\InvoiceLog;
use App\Models\User;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CreateInvoiceLogJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public Invoice $invoice, public string $action, public User $user)
    {
        $this->invoice = $invoice;
        $this->action = $action;
        $this->user = $user;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Started CreateInvoiceLogJob', [
                'invoice' => $this->invoice->invoice_id,
                'action' => $this->action,
            ]);

            if ($this->action == 'status_change') {
                InvoiceLog::create([
                    'invoice_id' => $this->invoice->invoice_id,
                    'updated_by_user_id' => $this->user->user_id,
                    'action' => InvoiceLogActions::UPDATE(),
                    'state' => $this->invoice->state->getValue(),
                    'description' => $this->invoice->description,
                    'amount_paid_in_cents' => $this->invoice->amount_paid_in_cents,
                ]);
            }
            if ($this->action == InvoiceLogActions::DELETE()) {

                InvoiceLog::create([
                    'invoice_id' => $this->invoice->invoice_id,
                    'updated_by_user_id' => $this->user->user_id,
                    'action' => $this->action,
                    'state' => $this->invoice->state->getValue(),
                    'description' => $this->invoice->description,
                    'amount_paid_in_cents' => $this->invoice->amount_paid_in_cents,
                ]);
            }

            if (in_array($this->action, [InvoiceLogActions::CREATE(), InvoiceLogActions::UPDATE()])) {

                $this->invoice->loadMissing('lineItems.subsidiaries');

                InvoiceLog::create([
                    'invoice_id' => $this->invoice->invoice_id,
                    'updated_by_user_id' => $this->user->user_id,
                    'action' => $this->action,
                    'state' => $this->invoice->state->getValue(),
                    'description' => $this->invoice->description,
                    'amount_paid_in_cents' => $this->invoice->amount_paid_in_cents,
                ]);

                foreach ($this->invoice->lineItems as $lineItem) {

                    InvoiceLog::create([
                        'invoice_line_item_id' => $lineItem->invoice_line_item_id,
                        'invoice_id' => $this->invoice->invoice_id,
                        'updated_by_user_id' => $this->user->user_id,
                        'action' => $this->action,
                        'state' => $this->invoice->state->getValue(),
                        'description' => $lineItem->description,
                        'cost_in_cents' => $lineItem->cost_in_cents,
                        'total_cost_in_cents' => $lineItem->total_cost_in_cents,
                    ]);
                    foreach ($lineItem->subsidiaries as $subsidiary) {
                        InvoiceLog::create([
                            'invoice_line_item_subsidiary_id' => $subsidiary->invoice_line_item_subsidiary_id,
                            'invoice_id' => $this->invoice->invoice_id,
                            'updated_by_user_id' => $this->user->user_id,
                            'action' => $this->action,
                            'state' => $this->invoice->state->getValue(),
                            'description' => $subsidiary->description,
                            'hourly_rate_in_cents' => $subsidiary->hourly_rate_in_cents,
                            'duration_in_seconds' => $subsidiary->duration_in_seconds,
                            'markup_fee_type' => $subsidiary->markup_fee_type,
                            'markup_fee_type_value' => $subsidiary->markup_fee_type_value,
                            'markup_fee_in_cents' => $subsidiary->markup_fee_in_cents,
                            'cost_in_cents' => $subsidiary->cost_in_cents,
                            'total_cost_in_cents' => $subsidiary->total_cost_in_cents,
                        ]);
                    }
                }
            }

        } catch (Exception $exception) {
            Helper::exceptionLog(
                exception: $exception,
                additionalInfo: [
                    'invoice_id' => $this->invoice->invoice_id,
                    'work_order_id' => $this->invoice->work_order_id,

                ],
                message: 'Create Invoice Log Failed due to ' . get_class($exception),
                notify: true
            );

            throw new Exception($exception->getMessage());
        }
    }
}

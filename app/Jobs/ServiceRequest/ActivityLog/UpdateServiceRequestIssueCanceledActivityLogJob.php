<?php

namespace App\Jobs\ServiceRequest\ActivityLog;

use App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogUpdated;
use App\Models\Issue;
use App\Models\ServiceRequestActivityLog;
use App\Services\ServiceRequestActivity\Enums\ActivityLogEventTypes;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Spatie\Multitenancy\Jobs\NotTenantAware;

class UpdateServiceRequestIssueCanceledActivityLogJob implements NotTenantAware, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public int $issueId) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $issue = Issue::withTrashed()->select('issue_id', 'issue_uuid', 'service_request_id', 'organization_id')
            ->where('issue_id', $this->issueId)
            ->firstOrFail();

        /* Check for recent canceled activity log*/
        $issueCanceledActivityLogs = ServiceRequestActivityLog::where('service_request_id', $issue->service_request_id)
            ->where('organization_id', $issue->organization_id)
            ->where('type', 'service-request')
            ->where('event', ActivityLogEventTypes::SERVICE_REQUEST_ISSUE_CANCELED())
            ->whereJsonContains('event_attributes->issue_id', $issue->issue_uuid)
            ->whereJsonContains('event_attributes->can_restore', true)
            ->orderBy('created_at', 'desc')
            ->get();

        if ($issueCanceledActivityLogs->isNotEmpty()) {
            foreach ($issueCanceledActivityLogs as $issueCanceledActivityLog) {
                // Update can_restore to false
                $eventAttributes = $issueCanceledActivityLog->event_attributes ?? [];
                $eventAttributes['can_restore'] = false;
                $issueCanceledActivityLog->event_attributes = $eventAttributes;
                $issueCanceledActivityLog->save();

                ServiceRequestActivityLogUpdated::broadcast($issueCanceledActivityLog->service_request_activity_log_id);
            }
        }
    }
}

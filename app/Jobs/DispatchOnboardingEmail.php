<?php

namespace App\Jobs;

use App\Enums\VendorOnboardingStatuses;
use App\Helpers\OnboardingHelper;
use App\Mail\OnboardingMail;
use App\Models\User;
use App\Models\Vendor;
use App\Models\VendorOnboarding;
use App\Models\VendorOnboardingStatus;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use InvalidArgumentException;

class DispatchOnboardingEmail implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $uuids;
    protected $subject;
    protected $organizationId;
    protected $organizationName;
    protected $type;
    protected $hostname;

    /**
     * Create a new job instance.
     */
    public function __construct($uuids, $organizationId, $organization_name, $type)
    {
        $this->uuids = $uuids;
        $this->organizationId = $organizationId;
        $this->organizationName = $organization_name;
        $this->type = $type;
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        $newStatus = VendorOnboardingStatus::where('slug', VendorOnboardingStatuses::INITIAL())->firstOrFail();

        foreach ($this->uuids as $uuid) {
            try {
                $userOrVendor = match ($this->type) {
                    'in-house' => User::whereUuid($uuid)->firstOrFail(),
                    'third-party' => Vendor::whereUuid($uuid)->firstOrFail(),
                    default => throw new InvalidArgumentException("Invalid type: {$this->type}"),
                };

                $vendorOnboardingData = [
                    'organization_id' => $this->organizationId,
                    'type' => $this->type,
                    'vendor_onboarding_status_id' => $newStatus->vendor_onboarding_status_id,
                ];
                if ($this->type === 'in-house') {
                    $vendorOnboardingData['user_id'] = $userOrVendor->user_id;
                } else {
                    $vendorOnboardingData['vendor_id'] = $userOrVendor->vendor_id;
                }
                $vendorOnboarding = VendorOnboarding::create($vendorOnboardingData);

                $signedUrl = OnboardingHelper::generateSignedUrl(
                    'vendor_onboarding.details',
                    $vendorOnboarding->vendor_onboarding_uuid,
                    $this->organizationName,
                    config('services.cognito.provider.onboarding_link_expiration')
                );
                $url = parse_url($signedUrl);
                $queryParams = [];
                if (isset($url['query'])) {
                    parse_str($url['query'], $queryParams);
                }

                $query = '/onboarding';
                if (isset($url['query']) && ! empty($url['query'])) {
                    $query .= '?' . $url['query'];
                }

                $frontendUrl = config('services.cognito.provider.vendor_domain') . $query;
                Mail::to($userOrVendor->email)
                    ->queue(new OnboardingMail(
                        $frontendUrl,
                        $this->minutesToDaysSimple(config('services.cognito.provider.onboarding_link_expiration')),
                        $this->organizationName,
                        config('services.cognito.provider.foresight_logo_dark'),
                        config('services.cognito.provider.foresight_logo_light'),
                        config('services.cognito.provider.email_platform_image'),
                    ));
            } catch (Exception $e) {
                // Log failure but continue loop
                Log::error("Failed onboarding for UUID {$uuid}: {$e->getMessage()}");

                continue;
            }
        }
    }

    private function minutesToDaysSimple($minutes)
    {
        return $minutes / (60 * 24) . ' days';
    }
}

<?php

namespace App\Jobs;

use App\Enums\Boolean;
use App\Enums\ImageResizeTypes;
use App\Exceptions\MediaUploadException;
use App\Helpers\Helper;
use App\Models\Media;
use App\Models\Organization;
use App\Models\WorkOrderMedia;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Spatie\Multitenancy\Jobs\NotTenantAware;
use Symfony\Component\Mime\MimeTypes;

class AttachImageToWorkOrderTrip implements NotTenantAware, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public string $url,
        public Organization $organization,
        public int $workOrderId,
        public string $workOrderUUID,
        public int $taskId,
        public int $serviceCallId,
        public string $mediaType,
        public ?string $externalReferenceId = null,
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        try {
            if ($this->url) {
                $fileResponse = Http::get($this->url);

                // Check the response is success
                if ($fileResponse->failed()) {
                    throw new FileNotFoundException('File fetching failed: invalid url provided');
                }

                // check response is a valid image
                if (! str_starts_with($fileResponse->header('Content-Type'), 'image/')) {
                    throw new FileNotFoundException('File fetching failed: provided url is not an image');
                }

                $fileResponseHeader = $fileResponse->headers();
                $actualFileContent = $fileResponse->body();

                if (empty($actualFileContent)) {
                    throw new FileNotFoundException('File content fetching failed.');
                }

                $basename = pathinfo($this->url, PATHINFO_BASENAME);
                $filterableName = explode('?', $basename);
                $fileName = $filterableName[0] ?? $basename;

                $extension = pathinfo($fileName, PATHINFO_EXTENSION);

                if (empty($extension)) {
                    $extension = (new MimeTypes)->getExtensions($fileResponseHeader['Content-Type'][0])[0];
                }

                Log::info('Media info1', [
                    'fileResponseHeader' => $fileResponseHeader,
                    'url' => $this->url,
                    'fileName' => $fileName,
                    'extension' => $extension,
                    'filterableName' => $filterableName,
                ]);

                Log::info('Uploading image');

                $basePath = $this->organization->getMediaPathPrefix() . "/work-orders/{$this->workOrderUUID}";

                // Generating a unique name for the original file.
                $uniqueFileName = Str::uuid();

                $uniqueFilename = "{$uniqueFileName}.{$extension}";
                $originalFIlePath = $basePath . '/' . $uniqueFilename;

                $uploaded = Storage::put($originalFIlePath, $actualFileContent);

                if (! $uploaded) {
                    throw new Exception('Original image uploading failed.');
                }

                $mimeType = Storage::mimeType($originalFIlePath);
                $sizeInBytes = Storage::size($originalFIlePath);

                $media = Media::create([
                    'organization_id' => $this->organization->organization_id,
                    'original_file_name' => $fileName,
                    'file_name' => $uniqueFilename,
                    'mime_type' => $mimeType,
                    'size' => $sizeInBytes,
                    'extension' => $extension,
                    'external_media_reference_id' => $this->externalReferenceId,
                ]);

                WorkOrderMedia::create([
                    'media_id' => $media->media_id,
                    'organization_id' => $this->organization->organization_id,
                    'work_order_id' => $this->workOrderId,
                    'work_order_task_id' => $this->taskId,
                    'media_type' => $this->mediaType,
                    'has_thumbnail' => Boolean::NO(),
                    'has_upload_completed' => Boolean::YES(),
                    'work_order_service_call_id' => $this->serviceCallId,
                ]);

                Log::info('Uploaded media', [
                    'media_id' => $media->media_id,
                ]);

                DB::commit();
                dispatch(new ImageResizeJob($media, [ImageResizeTypes::THUMBNAIL(), ImageResizeTypes::OPTIMIZED()]))
                    ->afterCommit()
                    ->onQueue('attachment');
            }
        } catch (Exception|FileNotFoundException $exception) {
            DB::rollback();
            Log::error('Transfer Image from Core Job Failed(Trip Complete).!!', [
                'work_order_uuid' => $this->workOrderUUID,
                'exception' => $exception->getMessage(),
                'getCode' => $exception->getCode(),
                'getTrace' => $exception->getTrace(),
                'getLine' => $exception->getLine(),
                'url' => $this->url,
            ]);
            Helper::exceptionLog(
                exception: $exception,
                additionalInfo: [
                    'work_order_id' => $this->workOrderUUID ?? null,
                    'organization_id' => $this->organization->organization_id ?? null,
                    'url' => $this->url,
                ],
                message: 'Transfer Image from Core Job Failed(Trip Complete) due to ' . get_class($exception),
                notify: true
            );
            throw new MediaUploadException('Media upload failed');
        }
    }
}

<?php

namespace App\Jobs\HealthScore;

use App\Enums\HealthViolationTypes;
use App\Enums\Priority;
use App\Enums\WorkOrderStatus;
use App\Models\Organization;
use App\Models\WorkOrder;
use App\Models\WorkOrderActivityLog;
use App\Models\WorkOrderHealthLog;
use App\Models\WorkOrderHealthTracker;
use App\Models\WorkOrderHealthViolation;
use App\Services\WorkOrderActivity\Enums\ActivityLogEventTypes;
use Carbon\CarbonImmutable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Spatie\Multitenancy\Jobs\NotTenantAware;

class UpdateWorkOrderHealthScoreJob implements NotTenantAware, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Organization
     */
    protected $organization;

    protected float $duration;

    protected ?CarbonImmutable $startDate;

    protected ?CarbonImmutable $endDate;

    /**
     * Create a new job instance.
     */
    public function __construct(Organization $organization)
    {
        $this->organization = $organization;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $workOrders = $this->fetchWorkOrders();
        $healthViolations = WorkOrderHealthViolation::with('healthTrackers.violation')->get();

        foreach ($healthViolations as $healthViolation) {
            $workOrderGroup = $this->filterWorkOrdersByViolation($workOrders, $healthViolation->slug);

            foreach ($workOrderGroup as $workOrder) {
                $this->duration = $duration = $this->calculateDuration($workOrder, $healthViolation->slug);

                if ($duration > 0) {
                    foreach ($healthViolation->healthTrackers as $healthTracker) {
                        $this->processHealthViolation($workOrder, $healthTracker, $duration, $healthViolation->slug);
                    }
                }
            }
        }
    }

    public function createWorkOrderActivityLog(WorkOrder $workOrder, WorkOrderHealthTracker $healthTracker, string $event, string $condition): WorkOrderActivityLog
    {
        return WorkOrderActivityLog::create([
            'work_order_id' => $workOrder->work_order_id,
            'work_order_task_id' => $workOrder->tasks->first()->work_order_task_id ?? null,
            'organization_id' => $workOrder->organization_id,
            'triggered_by' => null,
            'event' => $event,
            'event_attributes' => [
                'from' => 'healthy',
                'to' => $healthTracker->classification,
                'violation' => $healthTracker->violation?->label,
                'violation_slug' => $healthTracker->violation?->slug,
                'condition' => $condition,
                'health_tracker_uuid' => $healthTracker->work_order_health_tracker_uuid,
                'duration' => $this->duration,
                'startDate' => $this->startDate,
                'endDate' => $this->endDate,
            ],
        ]);
    }

    public function createWorkOrderHealthActivityLog(WorkOrder $workOrder, WorkOrderHealthTracker $healthTracker): WorkOrderHealthLog
    {
        return WorkOrderHealthLog::updateOrCreate([
            'work_order_id' => $workOrder->work_order_id,
            'work_order_health_tracker_id' => $healthTracker->work_order_health_tracker_id,
            'resolved_at' => null,
        ], [
            'work_order_id' => $workOrder->work_order_id,
            'work_order_health_tracker_id' => $healthTracker->work_order_health_tracker_id,
        ]);
    }

    /**
     * @return Collection<int, WorkOrder>
     */
    private function fetchWorkOrders(): Collection
    {
        return WorkOrder::with(['tasks.latestServiceCalls'])
            ->whereIn('priority', [Priority::HIGH, Priority::URGENT])
            ->where('organization_id', $this->organization->organization_id)
            ->whereIn('state', WorkOrderStatus::healthScoreStates()->pluck('value')->toArray())
            ->get();
    }

    /**
     * @param  Collection<int, WorkOrder>  $workOrders
     * @return Collection<int, WorkOrder>
     */
    private function filterWorkOrdersByViolation(Collection $workOrders, string $violationSlug): Collection
    {
        return match ($violationSlug) {
            HealthViolationTypes::UNCLAIMED_TOO_LONG() => $workOrders->filter(fn ($wo) => in_array($wo->state, [
                WorkOrderStatus::SCHEDULING_IN_PROGRESS(),
                WorkOrderStatus::CLAIM_PENDING(),
            ])),
            HealthViolationTypes::EMERGENCY_MISSED_ETA() => $workOrders->filter(fn ($wo) => $wo->state == WorkOrderStatus::SCHEDULED()),
            HealthViolationTypes::RUNNING_CLOCK() => $workOrders->filter(fn ($wo) => $wo->state == WorkOrderStatus::WORK_IN_PROGRESS()),
            default => collect(),
        };
    }

    private function calculateDuration(WorkOrder $workOrder, string $violationSlug): float
    {
        $latestServiceCall = $workOrder->tasks->first()?->latestServiceCalls->first();

        $this->startDate = $start = match ($violationSlug) {
            HealthViolationTypes::UNCLAIMED_TOO_LONG(), HealthViolationTypes::RUNNING_CLOCK() => $workOrder->state_updated_at,
            HealthViolationTypes::EMERGENCY_MISSED_ETA() => $latestServiceCall?->scheduled_end_time,
            default => null,
        };

        $this->endDate = CarbonImmutable::now();

        return $start ? $start->floatDiffInHours(CarbonImmutable::now(), false) : 0;
    }

    private function processHealthViolation(WorkOrder $workOrder, WorkOrderHealthTracker $healthTracker, float $duration, string $violationSlug): void
    {
        if ($this->isAtRisk($duration, $violationSlug)) {
            $message = $this->getAtRiskMessage($violationSlug);
            $this->logWorkOrderActivity(
                $workOrder,
                $healthTracker,
                ActivityLogEventTypes::WORK_ORDER_HEALTH_AT_RISK(),
                $message);
        }

        if ($this->isCritical($duration, $violationSlug)) {
            $message = $this->getIsCriticalMessage($violationSlug);
            $this->logWorkOrderActivity(
                $workOrder, $healthTracker,
                ActivityLogEventTypes::WORK_ORDER_HEALTH_CRITICAL(),
                $message);
        }
    }

    private function isAtRisk(float $duration, string $violationSlug): bool
    {
        return match ($violationSlug) {
            HealthViolationTypes::UNCLAIMED_TOO_LONG(), HealthViolationTypes::RUNNING_CLOCK() => ($duration >= 2 && $duration <= 4),
            HealthViolationTypes::EMERGENCY_MISSED_ETA() => ($duration >= 1 && $duration <= 2),
            default => false,
        };
    }

    private function getAtRiskMessage(string $violationSlug): string
    {
        return match ($violationSlug) {
            HealthViolationTypes::UNCLAIMED_TOO_LONG() => 'Claim pending for more than 2 hours',
            HealthViolationTypes::RUNNING_CLOCK() => 'Trip has been in progress for more than 2 hours',
            HealthViolationTypes::EMERGENCY_MISSED_ETA() => 'Trip arrival window is 1 hour past the ETA',
            default => '',
        };
    }

    private function isCritical(float $duration, string $violationSlug): bool
    {
        return match ($violationSlug) {
            HealthViolationTypes::UNCLAIMED_TOO_LONG(), HealthViolationTypes::RUNNING_CLOCK() => $duration > 4,
            HealthViolationTypes::EMERGENCY_MISSED_ETA() => $duration > 2,
            default => false,
        };
    }

    private function getIsCriticalMessage(string $violationSlug): string
    {
        return match ($violationSlug) {
            HealthViolationTypes::UNCLAIMED_TOO_LONG() => 'Claim pending for more than 4 hours',
            HealthViolationTypes::RUNNING_CLOCK() => 'Trip has been in progress for more than 4 hours',
            HealthViolationTypes::EMERGENCY_MISSED_ETA() => 'Trip arrival window is 2 hours past the ETA',
            default => '',
        };
    }

    private function extractHealthTrackerClassification(string $string): string
    {
        return Str::afterLast($string, '.');
    }

    private function logWorkOrderActivity(WorkOrder $workOrder, WorkOrderHealthTracker $healthTracker, string $event, string $message): void
    {
        if ($this->extractHealthTrackerClassification($event) === $healthTracker->classification) {
            $workOrderHealthLog = $this->createWorkOrderHealthActivityLog($workOrder, $healthTracker);

            if ($workOrderHealthLog->wasRecentlyCreated) {
                $this->createWorkOrderActivityLog($workOrder, $healthTracker, $event, $message);
            }
        }
    }
}

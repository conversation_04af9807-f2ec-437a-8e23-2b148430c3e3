<?php

namespace App\Jobs;

use App\Helpers\Helper;
use App\Models\WorkOrder;
use App\Services\Vendor\Enum\Service;
use App\Services\Vendor\VendorService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\Multitenancy\Jobs\NotTenantAware;

class SyncWorkOrderDetailsWithExternalJob implements NotTenantAware, ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public string $workOrderId,
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        $data = [];
        try {
            $workOrder = WorkOrder::with([
                'vendor:vendor_id,service',
                'organization:organization_id,organization_uuid',
                'tasks' => function ($query) {
                    return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id')
                        ->with([
                            'latestServiceCalls' => function ($query) {
                                return $query->select(
                                    'work_order_service_calls.work_order_service_call_id',
                                    'work_order_service_calls.lula_appointment_id',
                                    'work_order_service_calls.created_at',
                                )->with([
                                    'lulaAppointment:lula_appointment_id,work_order_reference_number,service_category_label',
                                ]);
                            },
                        ]);
                },
            ])
                ->whereUuid($this->workOrderId)
                ->select(
                    'work_order_id', 'vendor_id', 'vendor_work_order_id', 'organization_id'
                )
                ->firstOrFail();

            $task = $workOrder->tasks->firstOrFail();
            $serviceCall = $task->latestServiceCalls->firstOrFail();

            if (! empty($serviceCall->lulaAppointment)) {
                $lulaAppointment = $serviceCall->lulaAppointment;
                if (! empty($workOrder->vendor)) {
                    if ($workOrder->vendor->service === Service::LULA()) {
                        $vendorService = VendorService::make(
                            vendorProvider: Service::getServiceProviderFrom($workOrder->vendor->service),
                            organization: $workOrder->organization
                        );

                        if (! empty($workOrder->vendor_work_order_id)) {
                            $data = $vendorService->getWorkOrder($workOrder);
                            $lulaAppointment->service_category_label = $data['serviceCategory'][0]['category']['label'] ?? null;
                            $lulaAppointment->save();
                        }
                    }
                }
            }
            DB::commit();
        } catch (Exception $exception) {
            DB::rollback();
            Log::error('Job details fetching failed.!!', [
                'data' => $data,
                'exception' => $exception->getMessage(),
            ]);
            Helper::exceptionLog(
                exception: $exception,
                additionalInfo: [
                    'response' => $data,
                ],
                message: 'Job details fetching failed',
                notify: true
            );
            throw new Exception('Job fetching failed');
        }
    }
}

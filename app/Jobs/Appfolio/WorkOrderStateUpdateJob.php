<?php

namespace App\Jobs\Appfolio;

use App\Actions\WorkOrders\CancelWorkOrder;
use App\Models\WorkOrder;
use App\States\WorkOrders\Canceled;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class WorkOrderStateUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var WorkOrder
     */
    public $workOrder;

    /**
     * @var array<string,mixed>
     */
    public $appfolioWorkOrderReference;

    /**
     * Create a new job instance.
     *
     * @param  array<string,mixed>  $appfolioWorkOrderReference
     */
    public function __construct(WorkOrder $workOrder, array $appfolioWorkOrderReference)
    {
        $this->workOrder = $workOrder;
        $this->appfolioWorkOrderReference = $appfolioWorkOrderReference;
        $this->onQueue('appfolio');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::channel('appfolio')->info('Started WorkOrderStateUpdateJob', [
            'workOrder' => $this->workOrder->work_order_id,
            'state' => $this->workOrder->state,
            'appfolioWorkOrderReference' => $this->appfolioWorkOrderReference,
        ]);

        Log::info('Started WorkOrderStateUpdateJob', [
            'workOrder' => $this->workOrder->work_order_id,
            'state' => $this->workOrder->state,
            'appfolioWorkOrderReference' => $this->appfolioWorkOrderReference,
        ]);

        $workOrder = WorkOrder::with(['tasks.latestServiceCalls.createdQuote', 'tasks.latestServiceCalls.appointment'])
            ->find($this->workOrder->work_order_id);

        if ((! ($workOrder?->state->equals(Canceled::class))) && $this->appfolioWorkOrderReference['Status'] == 'Canceled') {

            $workOrder?->tasks->each(function ($workOrderTask) use ($workOrder) {
                CancelWorkOrder::run($workOrder, $workOrderTask, null, __('The resident/property manager canceled the work order.'));
            });
        }
    }
}

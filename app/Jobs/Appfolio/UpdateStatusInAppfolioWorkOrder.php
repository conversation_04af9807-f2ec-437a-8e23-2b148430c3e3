<?php

namespace App\Jobs\Appfolio;

use App\Enums\WorkOrderSourceTypes;
use App\Models\WorkOrder;
use App\Services\Appfolio\AppfolioService;
use Carbon\Carbon;
use DateTimeInterface;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateStatusInAppfolioWorkOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var WorkOrder
     */
    public $workOrder;

    /**
     * @var array<string,mixed>
     */
    public $payload;

    /**
     * @var array<string,mixed>
     */
    public $additionalData;

    /**
     * Create a new job instance.
     *
     * @param  array<string,mixed>  $payload
     * @param  array<string,mixed>  $additionalData
     */
    public function __construct(WorkOrder $workOrder, array $payload, array $additionalData = [])
    {
        $this->workOrder = $workOrder;
        $this->payload = $payload;
        $this->additionalData = $additionalData;
        $this->onQueue('appfolio');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::channel('appfolio')->info('Started UpdateStatusInAppfolioWorkOrder.');

        $this->workOrder->loadMissing(['organization', 'timezone', 'workOrderSource']);

        if (
            ! empty($this->workOrder->work_order_reference_id) &&
            ! empty($this->workOrder->workOrderSource) &&
            $this->workOrder->workOrderSource->slug == WorkOrderSourceTypes::APPFOLIO()
        ) {
            // Update status in appfolio.
            $appfolio = new AppfolioService($this->workOrder->organization);
            $appfolio->updateWorkOrder($this->workOrder->work_order_reference_id, $this->payload);

            if ($this->payload['Status'] == 'Scheduled') {
                $timezone = $this->workOrder->timezone->name;

                $scheduledStartTime = $this->toWorkOrderTimezone($this->payload['ScheduledStart'], $timezone, 'h:i A');
                $scheduledEndTime = $this->toWorkOrderTimezone($this->payload['ScheduledEnd'], $timezone, 'h:i A');
                $scheduleDate = $this->toWorkOrderTimezone($this->payload['ScheduledStart'], $timezone, 'm/d/Y T');
                $note = trans('appfolio.notes.claimed', [
                    'work_order_no' => $this->workOrder->work_order_reference_number ?? '',
                    'scheduled_start_time' => $scheduledStartTime,
                    'scheduled_end_time' => $scheduledEndTime,
                    'scheduled_date' => $scheduleDate,
                ]);
                Log::channel('appfolio')->info('Scheduled', [
                    'workOrderId' => $this->workOrder->work_order_uuid,
                    'note' => $note,
                ]);
                $appfolio->createNotes($this->workOrder->work_order_reference_id, $note);
            } elseif ($this->payload['Status'] == 'Waiting') {
                $pausedReason = '';
                if (! empty($this->additionalData) && ! empty($this->additionalData['pausedReason'])) {
                    $pausedReason = $this->additionalData['pausedReason'] ?? '';
                }

                $note = trans('appfolio.notes.paused.default', [
                    'work_order_no' => $this->workOrder->work_order_reference_number,
                    'reason' => $pausedReason,
                ]);
                Log::channel('appfolio')->info('Waiting', [
                    'workOrderId' => $this->workOrder->work_order_uuid,
                    'note' => $note,
                ]);
                $appfolio->createNotes($this->workOrder->work_order_reference_id, $note);
            }
        }

        Log::channel('appfolio')->info("{$this->workOrder->organization->name}'s UpdateStatusInAppfolioWorkOrder Completed.");
    }

    /**
     * To user timezone
     *
     * @param  DateTimeInterface|string  $time
     */
    protected function toWorkOrderTimezone($time, string $timezone, string $format): string
    {
        if (! $time instanceof Carbon) {
            $time = new Carbon($time);
        }

        $time->setTimeZone($timezone);

        return $time->format($format);
    }
}

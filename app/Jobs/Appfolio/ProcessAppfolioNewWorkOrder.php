<?php

namespace App\Jobs\Appfolio;

use App\Models\Organization;
use App\Services\Appfolio\AppfolioService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class ProcessAppfolioNewWorkOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Organization
     */
    public $organization;

    /**
     * @var array<string,mixed>
     */
    public $workOrder;

    /**
     * @var array<string,mixed>
     */
    public $additionalData;

    /**
     * Create a new job instance.
     *
     * @param  array<string,mixed>  $workOrder
     * @param  array<string,mixed>  $additionalData
     */
    public function __construct(Organization $organization, array $workOrder, array $additionalData = [])
    {
        $this->organization = $organization;
        $this->workOrder = $workOrder;
        $this->additionalData = $additionalData;
        $this->onQueue('appfolio');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::channel('appfolio')->info('Started ProcessAppfolioNewWorkOrder');
        Redis::throttle((string) $this->organization->appfolio_client_id)->block(0)
            ->allow(config('appfolio.redis.no_of_requests'))
            ->every(config('appfolio.redis.process_time_in_seconds'))
            ->then(function () {
                Log::channel('appfolio')->info("{$this->organization->name}'s ProcessAppfolioNewWorkOrder Started");
                $appfolio = new AppfolioService($this->organization);
                $appfolio->processNewWorkOrder($this->workOrder, $this->additionalData);
            }, function () {
                // Could not obtain lock.
                $this->release(config('appfolio.redis.process_time_in_seconds'));
            });

        Log::channel('appfolio')->info("{$this->organization->name}'s ProcessAppfolioNewWorkOrder Completed");
    }
}

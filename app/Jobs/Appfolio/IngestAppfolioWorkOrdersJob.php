<?php

namespace App\Jobs\Appfolio;

use App\Models\Organization;
use App\Services\Appfolio\AppfolioService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class IngestAppfolioWorkOrdersJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var Organization
     */
    public $organization;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Organization $organization)
    {
        $this->organization = $organization;
        $this->onQueue('appfolio');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::channel('appfolio')->info("{$this->organization->name}'s IngestAppfolioWorkOrdersJob Started");

        $appfolio = new AppfolioService($this->organization);
        $appfolio->ingestNewWorkOrders();

        Log::channel('appfolio')->info("{$this->organization->name}'s IngestAppfolioWorkOrdersJob Completed");
    }
}

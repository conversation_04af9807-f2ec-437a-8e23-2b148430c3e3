<?php

namespace App\Jobs\Appfolio;

use App\Actions\ServiceRequest\ClosedServiceRequest;
use App\Models\ServiceRequest;
use App\States\ServiceRequests\Closed;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ServiceRequestStateUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @var ServiceRequest
     */
    public $serviceRequest;

    /**
     * @var array<string,mixed>
     */
    public $appfolioWorkOrderReference;

    /**
     * Create a new job instance.
     *
     * @param  array<string,mixed>  $appfolioWorkOrderReference
     */
    public function __construct(ServiceRequest $serviceRequest, array $appfolioWorkOrderReference)
    {
        $this->serviceRequest = $serviceRequest;
        $this->appfolioWorkOrderReference = $appfolioWorkOrderReference;
        $this->onQueue('appfolio');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::channel('appfolio')->info('Started ServiceRequestStateUpdateJob', [
            'serviceRequest' => $this->serviceRequest->service_request_id,
            'state' => $this->serviceRequest->state,
            'appfolioWorkOrderReference' => $this->appfolioWorkOrderReference,
        ]);

        Log::info('Started ServiceRequestStateUpdateJob', [
            'serviceRequest' => $this->serviceRequest->service_request_id,
            'state' => $this->serviceRequest->state,
            'appfolioWorkOrderReference' => $this->appfolioWorkOrderReference,
        ]);

        $serviceRequest = ServiceRequest::find($this->serviceRequest->service_request_id);

        if ((! ($serviceRequest?->state->equals(Closed::class))) && $this->appfolioWorkOrderReference['Status'] == 'Canceled') {
            ClosedServiceRequest::run($serviceRequest, null, __('The resident/property manager canceled the service request.'));
        }
    }
}

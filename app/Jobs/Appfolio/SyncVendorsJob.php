<?php

namespace App\Jobs\Appfolio;

use App\Enums\Boolean;
use App\Helpers\Helper;
use App\Models\Country;
use App\Models\Organization;
use App\Models\OrganizationVendor;
use App\Models\Vendor;
use App\Services\Appfolio\AppfolioService;
use App\Services\Vendor\Enum\Service;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class SyncVendorsJob implements ShouldDispatchAfterCommit, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public Organization $organization, public int $offSet = 1, public ?Vendor $vendor = null)
    {
        $this->onQueue('vendor');
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array<int, object>
     */
    public function middleware(): array
    {
        $uniqueId = $this->organization->organization_uuid;

        if (! empty($this->vendor->vendor_uuid)) {
            $uniqueId = $this->vendor->vendor_uuid;
        }

        return [new WithoutOverlapping($uniqueId)];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        DB::beginTransaction();
        $params = [];
        try {
            $pageSize = 500;
            $appfolioService = new AppfolioService($this->organization);

            if (! empty($this->vendor)) {
                $params = [
                    'filters' => ['Id' => $this->vendor->external_reference_id],
                ];

                $vendors = $appfolioService->getVendors($params);

                $appfolioVendor = $vendors['data'][0] ?? null;

                if (empty($appfolioVendor)) {
                    throw new Exception(__('Vendor details missing.'));
                }

                $country = Country::select('country_id', 'name', 'alpha2_code', 'alpha3_code')
                    ->where('alpha2_code', $appfolioVendor['CountryCode'])->first();

                $this->vendor->address1 = $appfolioVendor['Address1'];
                $this->vendor->address2 = $appfolioVendor['Address2'];
                $this->vendor->gl_insurance_expire_at = null;
                $this->vendor->city = $appfolioVendor['City'];
                $this->vendor->company_name = $appfolioVendor['CompanyName'];
                $this->vendor->country_id = $country?->country_id;
                $this->vendor->email = $appfolioVendor['Email'];
                $this->vendor->first_name = $appfolioVendor['FirstName'];
                $this->vendor->is_company = $appfolioVendor['IsCompany'] === true ? Boolean::YES() : Boolean::NO();
                $this->vendor->last_name = $appfolioVendor['LastName'];
                $this->vendor->external_reference_link = $appfolioVendor['Link'];
                $this->vendor->phone_number = preg_replace('/\D/', '', $appfolioVendor['PhoneNumber']);
                $this->vendor->state_province = $appfolioVendor['State'];
                $this->vendor->postal_zip_code = $appfolioVendor['Zip'];
                $this->vendor->last_sync_at = CarbonImmutable::now();
                $this->vendor->save();

            } else {
                $params = [
                    'page' => [
                        'size' => $pageSize,
                        'number' => $this->offSet,
                    ],
                ];
                $countries = Country::select('country_id', 'name', 'alpha2_code', 'alpha3_code')->get();
                $vendors = $appfolioService->getVendors($params);

                $appfolioVendors = collect((array) $vendors['data']) ?? null;

                if (empty($appfolioVendors)) {
                    throw new Exception(__('Vendors list missing.'));
                }

                $appfolioVendorIds = $appfolioVendors->pluck('Id')->toArray();

                $existingVendors = Vendor::withTrashed()
                    ->select(
                        'vendor_id', 'company_name', 'email', 'first_name', 'last_name', 'phone_number', 'service', 'external_reference_id',
                        'external_reference_link', 'address1', 'address2', 'city', 'state_province', 'postal_zip_code', 'country_id', 'is_active',
                        'on_boarding_status', 'is_company', 'gl_insurance_expire_at', 'last_sync_at'
                    )
                    ->whereIn('external_reference_id', $appfolioVendorIds)
                    ->get();

                foreach ($appfolioVendors as $appfolioVendor) {
                    $existingVendor = $existingVendors->where('external_reference_id', $appfolioVendor['Id'])->first();
                    $country = $countries->where('alpha2_code', $appfolioVendor['CountryCode'])->first();

                    // Create a new vendor if there is no value existing for appfolio vendor id
                    if (empty($existingVendor)) {
                        $newVendor = Vendor::create([
                            'external_reference_id' => $appfolioVendor['Id'],
                            'address1' => $appfolioVendor['Address1'],
                            'address2' => $appfolioVendor['Address2'],
                            'gl_insurance_expire_at' => null,
                            'city' => $appfolioVendor['City'],
                            'company_name' => $appfolioVendor['CompanyName'],
                            'country_id' => $country?->country_id,
                            'email' => $appfolioVendor['Email'],
                            'first_name' => $appfolioVendor['FirstName'],
                            'is_company' => $appfolioVendor['IsCompany'] === true ? Boolean::YES() : Boolean::NO(),
                            'last_name' => $appfolioVendor['LastName'],
                            'external_reference_link' => $appfolioVendor['Link'],
                            'phone_number' => preg_replace('/\D/', '', $appfolioVendor['PhoneNumber']),
                            'state_province' => $appfolioVendor['State'],
                            'postal_zip_code' => $appfolioVendor['Zip'],
                            'last_sync_at' => CarbonImmutable::now(),
                            'service' => Service::THIRD_PARTY_VENDOR(),
                        ]);

                        OrganizationVendor::create([
                            'organization_id' => $this->organization->organization_id,
                            'vendor_id' => $newVendor->vendor_id,
                        ]);
                    } else {
                        // update the details if any change occurs
                        $dataUpdated = false;

                        // Check Address1 has any change
                        if ($appfolioVendor['Address1'] !== $existingVendor->address1) {
                            $existingVendor->address1 = $appfolioVendor['Address1'];
                            $dataUpdated = true;
                        }

                        // Check Address2 has any change
                        if ($appfolioVendor['Address2'] !== $existingVendor->address2) {
                            $existingVendor->address2 = $appfolioVendor['Address2'];
                            $dataUpdated = true;
                        }

                        // Check City has any change
                        if ($appfolioVendor['City'] !== $existingVendor->city) {
                            $existingVendor->city = $appfolioVendor['City'];
                            $dataUpdated = true;
                        }

                        // Check CompanyName has any change
                        if ($appfolioVendor['CompanyName'] !== $existingVendor->company_name) {
                            $existingVendor->company_name = $appfolioVendor['CompanyName'];
                            $dataUpdated = true;
                        }

                        // Check CountryCode has any change
                        if ($country?->country_id !== $existingVendor->country_id) {
                            $existingVendor->country_id = $country?->country_id;
                            $dataUpdated = true;
                        }

                        // Check Email has any change
                        if ($appfolioVendor['Email'] !== $existingVendor->email) {
                            $existingVendor->email = $appfolioVendor['Email'];
                            $dataUpdated = true;
                        }

                        // Check FirstName has any change
                        if ($appfolioVendor['FirstName'] !== $existingVendor->first_name) {
                            $existingVendor->first_name = $appfolioVendor['FirstName'];
                            $dataUpdated = true;
                        }

                        // Check LastName has any change
                        if ($appfolioVendor['LastName'] !== $existingVendor->last_name) {
                            $existingVendor->last_name = $appfolioVendor['LastName'];
                            $dataUpdated = true;
                        }

                        // Check IsCompany has any change
                        $isCompany = $appfolioVendor['IsCompany'] === true ? Boolean::YES() : Boolean::NO();
                        if ($isCompany !== $existingVendor->is_company) {
                            $existingVendor->is_company = $isCompany;
                            $dataUpdated = true;
                        }

                        // Check Link has any change
                        if ($appfolioVendor['Link'] !== $existingVendor->external_reference_link) {
                            $existingVendor->external_reference_link = $appfolioVendor['Link'];
                            $dataUpdated = true;
                        }

                        // Check State has any change
                        if ($appfolioVendor['State'] !== $existingVendor->state_province) {
                            $existingVendor->state_province = $appfolioVendor['State'];
                            $dataUpdated = true;
                        }

                        // Check State has any change
                        if ($appfolioVendor['Zip'] !== $existingVendor->postal_zip_code) {
                            $existingVendor->postal_zip_code = $appfolioVendor['Zip'];
                            $dataUpdated = true;
                        }

                        // Check State has any change
                        if (preg_replace('/\D/', '', $appfolioVendor['PhoneNumber']) !== $existingVendor->phone_number) {
                            $existingVendor->phone_number = preg_replace('/\D/', '', $appfolioVendor['PhoneNumber']);
                            $dataUpdated = true;
                        }

                        if ($dataUpdated) {
                            $existingVendor->last_sync_at = CarbonImmutable::now();
                            $existingVendor->save();
                        }
                    }
                }

                if (! empty($vendors['next_page_path'])) {
                    dispatch(new SyncVendorsJob($this->organization, $this->offSet + 1))->afterCommit();
                }
            }

            DB::commit();
        } catch (Exception $exception) {
            DB::rollBack();

            Helper::exceptionLog(
                exception: $exception,
                additionalInfo: $params,
                message: 'Exception in sync vendors job',
                notify: true
            );

            throw new Exception($exception->getMessage());
        }

    }
}

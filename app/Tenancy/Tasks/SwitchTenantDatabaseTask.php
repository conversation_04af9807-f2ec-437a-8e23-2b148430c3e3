<?php

namespace App\Tenancy\Tasks;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Spatie\Multitenancy\Models\Tenant;
use Spatie\Multitenancy\Tasks\SwitchTenantTask;

class SwitchTenantDatabaseTask implements SwitchTenantTask
{
    public function makeCurrent(Tenant $tenant): void
    {
        $databaseStoragePath = 'tenancy/database';
        $directory = Storage::disk('local')->makeDirectory($databaseStoragePath);
        $database = [
            'host' => config('database.connections.tenancy.host'),
            'port' => config('database.connections.tenancy.port'),
            'database' => config('database.connections.tenancy.database'),
            'username' => config('database.connections.tenancy.username'),
            'password' => encrypt(config('database.connections.tenancy.password')),
            'read' => [
                'host' => config('database.connections.tenancy.read.host'),
                'username' => config('database.connections.tenancy.read.username'),
                'password' => encrypt(config('database.connections.tenancy.read.password')),
            ],
            'write' => [
                'host' => config('database.connections.tenancy.write.host'),
            ],
        ];

        if (! Storage::exists("{$databaseStoragePath}/tenancy_{$tenant->getKey()}_database.json")) {
            Storage::put("{$databaseStoragePath}/tenancy_{$tenant->getKey()}_database.json", json_encode($database, JSON_THROW_ON_ERROR));
        }

        $organizationDatabaseConfig = Storage::get("{$databaseStoragePath}/tenancy_{$tenant->getKey()}_database.json");
        if ($organizationDatabaseConfig) {
            $database = json_decode($organizationDatabaseConfig, true, 512, JSON_THROW_ON_ERROR);
            $database['password'] = decrypt($database['password']);

            $this->setTenantConnectionDatabase($database);
        }
    }

    public function forgetCurrent(): void
    {
        $this->setTenantConnectionDatabase([], 'mysql');
    }

    /**
     * @param  array<string>  $database
     */
    protected function setTenantConnectionDatabase(array $database, string $tenantConnectionName = 'tenancy'): void
    {
        app('db')->extend($tenantConnectionName, function ($config, $name) use ($database) {
            $config = [...$config, ...$database];

            return app('db.factory')->make($config, $name);
        });

        DB::purge($tenantConnectionName);

        // Octane will have an old `db` instance in the Model::$resolver.
        Model::setConnectionResolver(app('db'));

        config([
            'database.default' => $tenantConnectionName,
        ]);
    }
}

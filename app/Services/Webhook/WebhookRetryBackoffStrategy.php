<?php

namespace App\Services\Webhook;

use Spa<PERSON>\WebhookServer\BackoffStrategy\BackoffStrategy;

class WebhookRetryBackoffStrategy implements BackoffStrategy
{
    public function waitInSecondsAfterAttempt(int $attempt): int
    {
        return match ($attempt) {
            1 => 10,
            2 => 100,
            3 => 1000,
            4 => 60 * 60,
            5 => 120 * 60,
            default => 180 * 60,
        };
    }
}

<?php

namespace App\Services\Webhook;

use Exception;
use <PERSON><PERSON>\WebhookClient\Models\WebhookCall;
use Spatie\WebhookClient\WebhookProcessor;

class WebhookClientProcessor extends WebhookProcessor
{
    protected function processWebhook(WebhookCall $webhookCall): void
    {
        try {
            $job = new $this->config->processWebhookJobClass($webhookCall);

            $webhookCall->clearException();

            dispatch_sync($job);
        } catch (Exception $exception) {
            $webhookCall->saveException($exception);

            throw $exception;
        }
    }
}

<?php

namespace App\Services\InvoiceRegister\DTO;

use App\Http\Requests\Invoice\InvoiceRequest;
use App\Models\Invoice;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderTask;

class InvoiceRegisterDataObject
{
    public function __construct(
        protected InvoiceRequest $request,
        protected WorkOrder $workOrder,
        protected WorkOrderTask $workOrderTask,
        protected User $user,
        protected ?Invoice $invoice = null,
    ) {}

    public function getRequest(): InvoiceRequest
    {
        return $this->request;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getWorkOrder(): workOrder
    {
        return $this->workOrder;
    }

    public function getWorkOrderTask(): WorkOrderTask
    {
        return $this->workOrderTask;
    }

    public function setInvoice(Invoice $invoice): void
    {
        $this->invoice = $invoice;
    }

    public function getInvoice(): ?Invoice
    {
        return $this->invoice;
    }
}

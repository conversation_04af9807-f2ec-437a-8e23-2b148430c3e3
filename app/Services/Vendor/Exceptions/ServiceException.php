<?php

namespace App\Services\Vendor\Exceptions;

use RuntimeException;

class ServiceException extends RuntimeException
{
    public static function invalidService(string $message = 'Invalid vendor adapter'): self
    {
        return new self(__($message));
    }

    public static function tokenExpired(string $message = 'Token has expired.'): self
    {
        return new self(__($message));
    }

    public static function invalidToken(string $message = 'Invalid token.'): self
    {
        return new self(__($message));
    }

    public static function invalidCacheKey(string $message = 'Invalid cache key.'): self
    {
        return new self(__($message));
    }

    public static function invalidKey(string $key, string $message = 'Invalid cache :key.'): self
    {
        return new self(__(trans($message, ['key' => $key])));
    }

    public static function invalidVendor(): ServiceException
    {
        return new self(__('The provided vendor is not configured for the organization'));
    }
}

<?php

namespace App\Services\Vendor\Services\Lula;

use Exception;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;

class LulaClient extends PendingRequest
{
    public static function getInstance(): LulaClient
    {
        return new LulaClient;
    }

    /**
     * @param  array<string, mixed>  $options
     *
     * @throws Exception
     */
    public function send(string $method, string $url, array $options = []): PromiseInterface|Response
    {
        $this->baseUrl(Config::getInstance()->baseUrl());

        return parent::send($method, $url, $options);
    }

    /**
     * Handle dynamic static method calls into the model.
     *
     * @param  string  $method
     * @param  array<int|string, mixed>  $parameters
     * @return mixed
     */
    public static function __callStatic($method, $parameters)
    {
        return (new self)->$method(...$parameters);
    }
}

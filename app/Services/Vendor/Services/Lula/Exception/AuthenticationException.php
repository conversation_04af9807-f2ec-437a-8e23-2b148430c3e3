<?php

namespace App\Services\Vendor\Services\Lula\Exception;

use App\Services\Vendor\Exceptions\ServiceException;

class AuthenticationException extends ServiceException
{
    public static function invalidClient(): AuthenticationException
    {
        return new self(__('Authentication failed, due to missing credentials.'));
    }

    public static function invalidClientKey(): AuthenticationException
    {
        return new self(__('Authentication failed, due to missing credentials.'));
    }

    public static function invalidRequest(): AuthenticationException
    {
        return new self(__('Authentication failed, due to invalid request.'));
    }

    public static function accessTokenMissing(): AuthenticationException
    {
        return new self(__('Authentication failed, due to missing access token.'));
    }
}

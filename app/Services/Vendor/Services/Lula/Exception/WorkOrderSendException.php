<?php

namespace App\Services\Vendor\Services\Lula\Exception;

use App\Services\Vendor\Exceptions\ServiceException;

class WorkOrderSendException extends ServiceException
{
    public static function emptyWorkOrderTask(): WorkOrderSendException
    {
        return new self(__('Task not found, failed to fetch task from work order'));
    }

    public static function serviceCallRegisterFailed(): WorkOrderSendException
    {
        return new self(__('Failed to register service call'));
    }

    public static function failedResponse(string $message = 'Work order sent to vendor failed'): WorkOrderSendException
    {
        return new self(__($message));
    }

    public static function lulaAppointmentRegisterFailed(): WorkOrderSendException
    {
        return new self(__('Failed to register lula appointment'));
    }

    public static function lulaWorkOrder(string $type = ''): WorkOrderSendException
    {
        return new self(__("Work order sent to vendor failed, This work order created from {$type}"));
    }

    public static function unexpectedResponseData(string $message = 'Invalid response data'): WorkOrderSendException
    {
        return new self(__($message));
    }

    public static function emptyVendor(): WorkOrderSendException
    {
        return new self(__('Failed to fetch vendor list'));
    }

    public static function vendorAppointmentRegisterFailed(): WorkOrderSendException
    {
        return new self(__('Failed to register vendor appointment'));
    }
}

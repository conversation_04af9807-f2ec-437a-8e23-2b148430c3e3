<?php

namespace App\Services\Vendor\Enum;

use App\Services\Vendor\Exceptions\ServiceException;
use ArchTech\Enums\InvokableCases;
use ArchTech\Enums\Values;

enum Service: string
{
    use InvokableCases, Values;

    case LULA = 'lula';

    case THIRD_PARTY_VENDOR = 'third-party-vendor';

    public static function getServiceProviderFrom(string $type): Service
    {
        return match ($type) {
            'lula' => self::LULA,
            'third-party-vendor' => self::THIRD_PARTY_VENDOR,
            default => throw ServiceException::invalidService()
        };
    }
}

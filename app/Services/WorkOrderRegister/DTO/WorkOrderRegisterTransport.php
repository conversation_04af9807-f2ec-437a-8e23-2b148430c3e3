<?php

namespace App\Services\WorkOrderRegister\DTO;

use App\Models\Organization;
use App\Models\Property;
use App\Models\Resident;
use App\Models\ServiceRequest;
use App\Models\Timezone;
use App\Models\User;
use App\Models\WorkOrder;
use Illuminate\Http\Request;

class WorkOrderRegisterTransport
{
    public function __construct(
        protected Request $request,
        protected Organization $organization,
        protected ?User $user = null,
        protected ?Timezone $timezone = null,
        protected ?Property $property = null,
        protected ?Resident $resident = null,
        protected ?WorkOrder $workOrder = null,
        protected ?ServiceRequest $serviceRequest = null,
    ) {}

    public function setRequest(Request $request): void
    {
        $this->request = $request;
    }

    public function getRequest(): Request
    {
        return $this->request;
    }

    public function setOrganization(Organization $organization): void
    {
        $this->organization = $organization;
    }

    public function getOrganization(): Organization
    {
        return $this->organization;
    }

    public function setUser(User $user): void
    {
        $this->user = $user;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setTimezone(Timezone $timezone): void
    {
        $this->timezone = $timezone;
    }

    public function getTimezone(): ?Timezone
    {
        return $this->timezone;
    }

    public function setProperty(Property $property): void
    {
        $this->property = $property;
    }

    public function getProperty(): ?Property
    {
        return $this->property;
    }

    public function setResident(Resident $resident): void
    {
        $this->resident = $resident;
    }

    public function getResident(): ?Resident
    {
        return $this->resident;
    }

    public function setWorkOrder(WorkOrder $workOrder): void
    {
        $this->workOrder = $workOrder;
    }

    public function getWorkOrder(): ?WorkOrder
    {
        return $this->workOrder;
    }

    public function setServiceRequest(ServiceRequest $serviceRequest): void
    {
        $this->serviceRequest = $serviceRequest;
    }

    public function getServiceRequest(): ?ServiceRequest
    {
        return $this->serviceRequest;
    }
}

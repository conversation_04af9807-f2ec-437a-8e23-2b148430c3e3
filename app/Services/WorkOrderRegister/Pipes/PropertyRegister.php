<?php

namespace App\Services\WorkOrderRegister\Pipes;

use App\Models\Country;
use App\Models\Property;
use App\Models\State;
use App\Models\Timezone;
use App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport;
use Closure;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;

class PropertyRegister
{
    //    public function __construct(protected Property $model )
    //    {
    //    }

    public function handle(WorkOrderRegisterTransport $transport, Closure $next): void
    {
        $request = $transport->getRequest();
        $organization = $transport->getOrganization();

        // Collect the property details from the request payload.
        $requestedPropertyDetails = $request->get('property');

        $state = State::select('state_id')
            ->where('state_code', $requestedPropertyDetails['state_id'])
            ->firstOrFail();

        $country = Country::select('country_id')
            ->where('alpha2_code', $requestedPropertyDetails['country_id'])
            ->firstOrFail();

        $requestedTimezone = $request->timezone ?? config('settings.default_timezone');
        $timezone = Timezone::where('name', $requestedTimezone)->first();

        if (empty($timezone)) {
            throw new ModelNotFoundException('Timezone not found');
        }

        // Create a property associated with this work order.
        $property = Property::create([
            'organization_id' => $organization->organization_id,
            'property_name' => $requestedPropertyDetails['property_name'] ?? null,
            'full_address' => $requestedPropertyDetails['full_address'],
            'street_address' => $requestedPropertyDetails['street_address'],
            'unit_number' => $requestedPropertyDetails['unit_number'] ?? null,
            'bed_unit' => $requestedPropertyDetails['bed_unit'] ?? null,
            'city' => $requestedPropertyDetails['city'] ?? null,
            'postal_zip_code' => $requestedPropertyDetails['postal_zip_code'],
            'state_id' => $state->state_id,
            'country_id' => $country->country_id,
            'latitude' => $requestedPropertyDetails['latitude'] ?? null,
            'longitude' => $requestedPropertyDetails['longitude'] ?? null,
            'navigation_address' => $requestedPropertyDetails['navigation_address'] ?? null,
            'google_place_id' => $requestedPropertyDetails['google_place_id'] ?? null,
            'google_geocode_response' => $requestedPropertyDetails['google_geocode_response'] ?? null,
            'timezone_id' => $timezone->timezone_id,
        ]);

        $transport->setProperty($property);

        $next($transport);
    }
}

<?php

namespace App\Services\WorkOrderRegister\Pipes;

use App\Models\PropertyResident;
use App\Models\Resident;
use App\Models\ResidentStatus;
use App\Services\WorkOrderRegister\DTO\WorkOrderRegisterTransport;
use Closure;
use Illuminate\Http\Request;

class ResidentRegister
{
    public function handle(WorkOrderRegisterTransport $transport, Closure $next): void
    {
        $request = $transport->getRequest();
        $organization = $transport->getOrganization();
        $property = $transport->getProperty();

        $primaryResident = null;
        $resident = null;

        if (! empty($request->residents) && count($request->residents)) {
            // Collect the residents details from the request.
            $requestedResidentDetails = $request->residents;

            // Find resident status
            // TODO:: Discuss about resident status.
            // Now we assign 'current' status for all residents.
            $residentStatus = ResidentStatus::select('resident_status_id')
                ->where('status', 'Current')
                ->firstOrFail();

            foreach ($requestedResidentDetails as $residentDetails) {
                if (
                    empty($residentDetails['first_name']) &&
                    empty($residentDetails['last_name']) &&
                    empty($residentDetails['email']) &&
                    empty($residentDetails['phone_number'])
                ) {
                    $fistName = 'No Resident';
                } else {
                    $fistName = $residentDetails['first_name'] ?? null;
                }
                // Create resident
                $resident = Resident::create([
                    'organization_id' => $organization->organization_id,
                    'property_id' => $property?->property_id,
                    'first_name' => $fistName,
                    'last_name' => $residentDetails['last_name'] ?? null,
                    'email' => $residentDetails['email'] ?? null,
                    'phone_number' => $residentDetails['phone_number'] ?? null,
                ]);

                // Add a entry to the property resident table.
                $propertyResident = PropertyResident::create([
                    'organization_id' => $organization->organization_id,
                    'property_id' => $property?->property_id,
                    'resident_id' => $resident->resident_id,
                    'resident_status_id' => $residentStatus->resident_status_id,
                    'is_primary_resident' => $residentDetails['is_primary_resident'],
                ]);

                // Save the primary resident for further use.
                if ($residentDetails['is_primary_resident']) {
                    $primaryResident = $resident;
                }
            }
        }

        if (! empty($primaryResident)) {
            $transport->setResident($primaryResident);
        } elseif (! empty($resident)) {
            $transport->setResident($resident);
        }

        $next($transport);
    }
}

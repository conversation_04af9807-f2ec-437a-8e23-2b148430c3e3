<?php

namespace App\Services;

use App\Models\ServiceRequestAssignee;
use App\Models\User;
use App\Models\WorkOrderAssignee;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use UnexpectedValueException;

/**
 * AssigneeFilterService handles all assignee filtering functionality for the application.
 *
 * This service centralizes assignee filtering logic across the application, providing
 * a consistent way to filter data based on assignees. It handles the complexities of
 * filtering by assignees across different entity types (work orders, quotes, service requests).
 *
 * Key features:
 * - Centralized assignee filtering logic
 * - Support for 'is' and 'is_not' operations
 * - Support for 'unassigned' special value
 * - Support for multiple entity types (work_order, quote, service_request)
 * - Integration with <PERSON><PERSON>'s query builder
 *
 * Usage:
 * ```php
 * // In a controller or service
 * $query = WorkOrder::query();
 * $query = $this->assigneeFilterService->applyAssigneeFilter($query, $payload, $operator, 'work_order');
 *
 * // Or using the Filterable trait on a model
 * $query = WorkOrder::query()->filterAssignee(['user_uuid1', 'user_uuid2'], 'is', 'work_order');
 * ```
 */
class AssigneeFilterService
{
    /**
     * Apply assignee filter to the query
     *
     * This method applies assignee filtering to the query builder based on the provided
     * payload. It handles various operations and supports different entity types.
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder to apply the filter to
     * @param  array{"group_operation": string, "column_operation": string, "field": string, "column_name": string, "values": array<string>}  $payload  The filter payload
     * @param  string  $operator  The resolved operator (uuid, not_uuid)
     * @param  string  $entityType  The entity type (work_order, quote, service_request)
     * @return Builder<TModel>|null The query builder with the filter applied
     */
    public function applyAssigneeFilter(Builder $query, array $payload, string $operator, string $entityType): ?Builder
    {
        Log::info('AssigneeFilterService::applyAssigneeFilter - Payload: ' . json_encode($payload));
        Log::info('AssigneeFilterService::applyAssigneeFilter - Operator: ' . $operator);
        Log::info('AssigneeFilterService::applyAssigneeFilter - Entity Type: ' . $entityType);

        $whereClause = $this->resolveWhereClause($payload['group_operation'], $operator);
        Log::info('AssigneeFilterService::applyAssigneeFilter - Where Clause: ' . $whereClause);

        // Handle special case for 'unassigned'
        if (! empty($payload['values']) && $payload['values'][0] === 'unassigned') {
            Log::info('AssigneeFilterService::applyAssigneeFilter - Applying unassigned filter');

            return $this->applyUnassignedFilter($query, $entityType, $payload['group_operation']);
        }

        // Special handling for 'is_not' operation
        if (! empty($payload['values']) && $payload['column_operation'] === 'is_not') {
            Log::info('AssigneeFilterService::applyAssigneeFilter - Applying is_not filter with values: ' . json_encode($payload['values']));

            return $this->applyNotAssignedToFilter($query, $payload['values'], $entityType);
        }

        // Standard handling for 'is' operation
        if (! empty($payload['values']) && $payload['column_operation'] === 'is') {
            Log::info('AssigneeFilterService::applyAssigneeFilter - Applying is filter with values: ' . json_encode($payload['values']));

            return $this->applyAssignedToFilter($query, $payload['values'], $entityType, $whereClause);
        }

        // Default case - just return the query
        Log::info('AssigneeFilterService::applyAssigneeFilter - No filter applied, returning original query');

        return $query;
    }

    /**
     * Apply filter for entities not assigned to specified users
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder to apply the filter to
     * @param  array<string>  $userUuids  The UUIDs of users to exclude
     * @param  string  $entityType  The entity type (work_order, quote, service_request)
     * @return Builder<TModel> The query builder with the filter applied
     */
    protected function applyNotAssignedToFilter(Builder $query, array $userUuids, string $entityType): Builder
    {
        Log::info('AssigneeFilterService::applyNotAssignedToFilter - User UUIDs: ' . json_encode($userUuids));

        // Convert string UUIDs to binary format for database query
        $binaryUuids = [];
        foreach ($userUuids as $uuid) {
            // Only process valid UUIDs
            if (preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $uuid)) {
                try {
                    $binaryUuids[] = Uuid::fromString($uuid)->getBytes();
                } catch (Exception $e) {
                    Log::warning('AssigneeFilterService::applyNotAssignedToFilter - Invalid UUID: ' . $uuid . ' - ' . $e->getMessage());
                }
            } else {
                Log::warning('AssigneeFilterService::applyNotAssignedToFilter - Invalid UUID format: ' . $uuid);
            }
        }

        $userIds = User::whereIn('user_uuid', $binaryUuids)->pluck('user_id')->toArray();
        Log::info('AssigneeFilterService::applyNotAssignedToFilter - User IDs: ' . json_encode($userIds));

        // If no valid users found, return all entities (since we can't exclude assignments for non-existent users)
        if (empty($userIds)) {
            Log::info('AssigneeFilterService::applyNotAssignedToFilter - No valid users found, returning all entities');

            return $query;
        }

        // Get entity IDs that have the specified assignees
        $entityIdsWithAssignees = match ($entityType) {
            'work_order' => $this->getWorkOrderIdsWithAssignees($userIds),
            'quote' => $this->getQuoteIdsWithAssignees($userIds),
            'service_request' => $this->getServiceRequestIdsWithAssignees($userIds),
            default => throw new UnexpectedValueException("Unexpected entity type [{$entityType}] for assignee filter."),
        };
        Log::info('AssigneeFilterService::applyNotAssignedToFilter - Entity IDs with assignees: ' . json_encode($entityIdsWithAssignees));

        // Return entities that don't have the specified assignees
        $idColumn = match ($entityType) {
            'work_order' => 'work_orders.work_order_id',
            'quote' => 'quotes.quote_id',
            'service_request' => 'service_requests.service_request_id',
            default => throw new UnexpectedValueException("Unexpected entity type [{$entityType}] for assignee filter."),
        };

        // Enable query logging
        DB::enableQueryLog();

        // If no entities have the specified assignees, return all entities
        if (empty($entityIdsWithAssignees)) {
            Log::info('AssigneeFilterService::applyNotAssignedToFilter - No entities found with specified assignees, returning all entities');
            $result = $query;
        } else {
            $result = $query->whereNotIn($idColumn, $entityIdsWithAssignees);
        }

        Log::info('AssigneeFilterService::applyNotAssignedToFilter - SQL: ' . json_encode(DB::getQueryLog()));

        return $result;
    }

    /**
     * Apply filter for entities assigned to specified users
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder to apply the filter to
     * @param  array<string>  $userUuids  The UUIDs of users to include
     * @param  string  $entityType  The entity type (work_order, quote, service_request)
     * @param  string  $whereClause  The where clause to use (where, orWhere)
     * @return Builder<TModel> The query builder with the filter applied
     */
    protected function applyAssignedToFilter(Builder $query, array $userUuids, string $entityType, string $whereClause): Builder
    {
        return match ($entityType) {
            'work_order' => $this->applyWorkOrderAssigneeFilter($query, $userUuids, $whereClause),
            'quote' => $this->applyQuoteAssigneeFilter($query, $userUuids, $whereClause),
            'service_request' => $this->applyServiceRequestAssigneeFilter($query, $userUuids, $whereClause),
            default => throw new UnexpectedValueException("Unexpected entity type [{$entityType}] for assignee filter."),
        };
    }

    /**
     * Apply filter for unassigned entities
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder to apply the filter to
     * @param  string  $entityType  The entity type (work_order, quote, service_request)
     * @param  string  $groupOperation  The group operation (and, or)
     * @return Builder<TModel> The query builder with the filter applied
     */
    protected function applyUnassignedFilter(Builder $query, string $entityType, string $groupOperation): Builder
    {
        $whereClause = $groupOperation === 'and' ? 'whereNotExists' : 'orWhereNotExists';

        return match ($entityType) {
            'work_order' => $query->$whereClause(function ($query) {
                $query->select('work_order_assignee_id')
                    ->from('work_order_assignees')
                    ->whereColumn('work_order_assignees.work_order_id', 'work_orders.work_order_id')
                    ->whereNull('work_order_assignees.deleted_at');
            }),
            'quote' => $query->$whereClause(function ($query) {
                $query->select('work_order_assignee_id')
                    ->from('work_order_assignees')
                    ->join('quotes', 'quotes.work_order_id', '=', 'work_order_assignees.work_order_id')
                    ->whereColumn('quotes.quote_id', 'quotes.quote_id')
                    ->whereNull('work_order_assignees.deleted_at');
            }),
            'service_request' => $query->$whereClause(function ($query) {
                $query->select('service_request_assignee_id')
                    ->from('service_request_assignees')
                    ->whereColumn('service_request_assignees.service_request_id', 'service_requests.service_request_id')
                    ->whereNull('service_request_assignees.deleted_at');
            }),
            default => throw new UnexpectedValueException("Unexpected entity type [{$entityType}] for assignee filter."),
        };
    }

    /**
     * Apply assignee filter for work orders
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder to apply the filter to
     * @param  array<string>  $userUuids  The UUIDs of users to include
     * @param  string  $whereClause  The where clause to use (where, orWhere)
     * @return Builder<TModel> The query builder with the filter applied
     */
    protected function applyWorkOrderAssigneeFilter(Builder $query, array $userUuids, string $whereClause): Builder
    {
        Log::info('AssigneeFilterService::applyWorkOrderAssigneeFilter - User UUIDs: ' . json_encode($userUuids));
        Log::info('AssigneeFilterService::applyWorkOrderAssigneeFilter - Where Clause: ' . $whereClause);

        // Get user IDs from UUIDs
        // Convert string UUIDs to binary format for database query
        $binaryUuids = [];
        foreach ($userUuids as $uuid) {
            // Only process valid UUIDs
            if (preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $uuid)) {
                try {
                    $binaryUuids[] = Uuid::fromString($uuid)->getBytes();
                } catch (Exception $e) {
                    Log::warning('AssigneeFilterService::applyWorkOrderAssigneeFilter - Invalid UUID: ' . $uuid . ' - ' . $e->getMessage());
                }
            } else {
                Log::warning('AssigneeFilterService::applyWorkOrderAssigneeFilter - Invalid UUID format: ' . $uuid);
            }
        }

        Log::info('AssigneeFilterService::applyWorkOrderAssigneeFilter - Binary UUIDs created');

        $userIds = User::whereIn('user_uuid', $binaryUuids)->pluck('user_id')->toArray();
        Log::info('AssigneeFilterService::applyWorkOrderAssigneeFilter - User IDs: ' . json_encode($userIds));

        // Enable query logging
        DB::enableQueryLog();

        // Determine the correct exists method based on the where clause
        $existsMethod = str_contains($whereClause, 'orWhere') ? 'orWhereExists' : 'whereExists';
        Log::info('AssigneeFilterService::applyWorkOrderAssigneeFilter - Using exists method: ' . $existsMethod);

        $result = $query->$existsMethod(function ($query) use ($userIds) {
            $query->select('work_order_assignee_id')
                ->from('work_order_assignees')
                ->whereColumn('work_order_assignees.work_order_id', 'work_orders.work_order_id')
                ->whereIn('work_order_assignees.user_id', $userIds)
                ->whereNull('work_order_assignees.deleted_at');
        });

        // Log the generated SQL query
        Log::info('AssigneeFilterService::applyWorkOrderAssigneeFilter - SQL: ' . json_encode(DB::getQueryLog()));

        return $result;
    }

    /**
     * Apply assignee filter for quotes
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder to apply the filter to
     * @param  array<string>  $userUuids  The UUIDs of users to include
     * @param  string  $whereClause  The where clause to use (where, orWhere)
     * @return Builder<TModel> The query builder with the filter applied
     */
    protected function applyQuoteAssigneeFilter(Builder $query, array $userUuids, string $whereClause): Builder
    {
        Log::info('AssigneeFilterService::applyQuoteAssigneeFilter - User UUIDs: ' . json_encode($userUuids));
        Log::info('AssigneeFilterService::applyQuoteAssigneeFilter - Where Clause: ' . $whereClause);

        // Convert string UUIDs to binary format for database query
        $binaryUuids = [];
        foreach ($userUuids as $uuid) {
            // Only process valid UUIDs
            if (preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $uuid)) {
                try {
                    $binaryUuids[] = Uuid::fromString($uuid)->getBytes();
                } catch (Exception $e) {
                    Log::warning('AssigneeFilterService::applyQuoteAssigneeFilter - Invalid UUID: ' . $uuid . ' - ' . $e->getMessage());
                }
            } else {
                Log::warning('AssigneeFilterService::applyQuoteAssigneeFilter - Invalid UUID format: ' . $uuid);
            }
        }

        $userIds = User::whereIn('user_uuid', $binaryUuids)->pluck('user_id')->toArray();
        Log::info('AssigneeFilterService::applyQuoteAssigneeFilter - User IDs: ' . json_encode($userIds));

        // Enable query logging
        DB::enableQueryLog();

        // Determine the correct exists method based on the where clause
        $existsMethod = str_contains($whereClause, 'orWhere') ? 'orWhereExists' : 'whereExists';
        Log::info('AssigneeFilterService::applyQuoteAssigneeFilter - Using exists method: ' . $existsMethod);

        $result = $query->$existsMethod(function ($query) use ($userIds) {
            $query->select('work_order_assignee_id')
                ->from('work_order_assignees')
                ->join('quotes', 'quotes.work_order_id', '=', 'work_order_assignees.work_order_id')
                ->whereColumn('quotes.quote_id', 'quotes.quote_id')
                ->whereIn('work_order_assignees.user_id', $userIds)
                ->whereNull('work_order_assignees.deleted_at');
        });

        Log::info('AssigneeFilterService::applyQuoteAssigneeFilter - SQL: ' . json_encode(DB::getQueryLog()));

        return $result;
    }

    /**
     * Apply assignee filter for service requests
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder to apply the filter to
     * @param  array<string>  $userUuids  The UUIDs of users to include
     * @param  string  $whereClause  The where clause to use (where, orWhere)
     * @return Builder<TModel> The query builder with the filter applied
     */
    protected function applyServiceRequestAssigneeFilter(Builder $query, array $userUuids, string $whereClause): Builder
    {
        Log::info('AssigneeFilterService::applyServiceRequestAssigneeFilter - User UUIDs: ' . json_encode($userUuids));
        Log::info('AssigneeFilterService::applyServiceRequestAssigneeFilter - Where Clause: ' . $whereClause);

        // Convert string UUIDs to binary format for database query
        $binaryUuids = [];
        foreach ($userUuids as $uuid) {
            // Only process valid UUIDs
            if (preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $uuid)) {
                try {
                    $binaryUuids[] = Uuid::fromString($uuid)->getBytes();
                } catch (Exception $e) {
                    Log::warning('AssigneeFilterService::applyServiceRequestAssigneeFilter - Invalid UUID: ' . $uuid . ' - ' . $e->getMessage());
                }
            } else {
                Log::warning('AssigneeFilterService::applyServiceRequestAssigneeFilter - Invalid UUID format: ' . $uuid);
            }
        }

        $userIds = User::whereIn('user_uuid', $binaryUuids)->pluck('user_id')->toArray();
        Log::info('AssigneeFilterService::applyServiceRequestAssigneeFilter - User IDs: ' . json_encode($userIds));

        // Enable query logging
        DB::enableQueryLog();

        // Determine the correct exists method based on the where clause
        $existsMethod = str_contains($whereClause, 'orWhere') ? 'orWhereExists' : 'whereExists';
        Log::info('AssigneeFilterService::applyServiceRequestAssigneeFilter - Using exists method: ' . $existsMethod);

        $result = $query->$existsMethod(function ($query) use ($userIds) {
            $query->select('service_request_assignee_id')
                ->from('service_request_assignees')
                ->whereColumn('service_request_assignees.service_request_id', 'service_requests.service_request_id')
                ->whereIn('service_request_assignees.user_id', $userIds)
                ->whereNull('service_request_assignees.deleted_at');
        });

        Log::info('AssigneeFilterService::applyServiceRequestAssigneeFilter - SQL: ' . json_encode(DB::getQueryLog()));

        return $result;
    }

    /**
     * Get work order IDs that have the specified assignees
     *
     * @param  array<int>  $userIds  The IDs of users to check
     * @return array<int> The work order IDs with the specified assignees
     */
    protected function getWorkOrderIdsWithAssignees(array $userIds): array
    {
        return WorkOrderAssignee::whereIn('user_id', $userIds)
            ->pluck('work_order_id')
            ->toArray();
    }

    /**
     * Get quote IDs that have the specified assignees
     *
     * @param  array<int>  $userIds  The IDs of users to check
     * @return array<int> The quote IDs with the specified assignees
     */
    protected function getQuoteIdsWithAssignees(array $userIds): array
    {
        return WorkOrderAssignee::whereIn('user_id', $userIds)
            ->join('quotes', 'quotes.work_order_id', '=', 'work_order_assignees.work_order_id')
            ->pluck('quotes.quote_id')
            ->toArray();
    }

    /**
     * Get service request IDs that have the specified assignees
     *
     * @param  array<int>  $userIds  The IDs of users to check
     * @return array<int> The service request IDs with the specified assignees
     */
    protected function getServiceRequestIdsWithAssignees(array $userIds): array
    {
        return ServiceRequestAssignee::whereIn('user_id', $userIds)
            ->pluck('service_request_id')
            ->toArray();
    }

    /**
     * Resolve the where clause based on the group operation and operator
     *
     * For assignee filters, we only need the base where clause (where/orWhere)
     * since we use whereExists/orWhereExists methods rather than specific
     * UUID-based where clauses.
     *
     * @param  string  $groupOperation  The group operation (and, or)
     * @param  string  $operator  The operator (uuid, not_uuid)
     * @return string The where clause method name
     */
    protected function resolveWhereClause(string $groupOperation, string $operator): string
    {
        // For assignee filters, we only need the base where clause
        // The specific exists method (whereExists/orWhereExists) is determined
        // in the individual filter methods based on this clause
        return $groupOperation === 'and' ? 'where' : 'orWhere';
    }
}

<?php

namespace App\Services\WorkOrderActivity\Leaf;

use App\Http\Resources\WorkOrder\MediaResource;
use App\Models\WorkOrderActivityLog;
use App\Services\WorkOrderActivity\ActivityComponent;
use Illuminate\Support\Collection;

class StatusChangeLeaf implements ActivityComponent
{
    public function __construct(private readonly WorkOrderActivityLog $workOrderActivityLog) {}

    /**
     * @return array<int|string, mixed>
     */
    public function getActivities(): array
    {
        /** @var Collection<int, array<int|string, mixed>>|null */
        $eventAttributes = $this->workOrderActivityLog->event_attributes;

        $workOrder = $this->workOrderActivityLog->workOrder ?? null;
        if (! empty($eventAttributes['medias'])) {
            $mediaIds = $eventAttributes['medias'];
            if (! empty($workOrder)) {
                $media = $workOrder->media->whereIn('media_uuid', $mediaIds);

                if ($media->isNotEmpty()) {
                    // Replace media ids to media urls
                    $eventAttributes['medias'] = MediaResource::collection($media);
                }
            }
        }

        $eventAttributes['work_order_id'] = $workOrder?->work_order_uuid;
        $eventAttributes['work_order_number'] = $workOrder?->work_order_number;

        return [
            'activity_uuid' => $this->workOrderActivityLog->work_order_activity_log_uuid,
            'event' => $this->workOrderActivityLog->event,
            'attributes' => $eventAttributes,
            'created_at' => $this->workOrderActivityLog->created_at,
            'triggered_by' => $this->workOrderActivityLog->triggeredBy?->getName(),
        ];
    }
}

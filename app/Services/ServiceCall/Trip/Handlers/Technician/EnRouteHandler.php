<?php

namespace App\Services\ServiceCall\Trip\Handlers\Technician;

use App\Exceptions\WorkOrderException;
use App\Models\WorkOrderServiceCall;
use App\Services\ServiceCall\Trip\Domain\Contracts\StateTransitionHandlerInterface;
use Illuminate\Support\Facades\Log;

class EnRouteHandler implements StateTransitionHandlerInterface
{
    public function apply(int $entityId, int $serviceCallId, array $additionalData = []): void
    {
        Log::info("Technician EnRouteHandler: Applying for technician {$entityId} on service call {$serviceCallId}");

        $serviceCall = WorkOrderServiceCall::with('technicianAppointment:technician_appointment_id')
            ->findOrFail($serviceCallId, ['work_order_service_call_id', 'technician_appointment_id']);

        if ($serviceCall->technician_appointment_id === null) {
            throw WorkOrderException::technicianAppointmentNotFound();
        }

        $technicianAppointment = $serviceCall->technicianAppointment;

        if ($technicianAppointment === null) {
            throw WorkOrderException::technicianAppointmentNotFound();
        }

        Log::info("Technician EnRouteHandler: Successfully applied for technician {$entityId}");
    }
}

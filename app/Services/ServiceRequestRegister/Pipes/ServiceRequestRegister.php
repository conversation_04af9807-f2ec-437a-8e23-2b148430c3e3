<?php

namespace App\Services\ServiceRequestRegister\Pipes;

use App\Enums\ServiceRequestSourceTypes;
use App\Enums\ServiceRequestStatusTypes as EnumsServiceRequestStatus;
use App\Enums\ServiceRequestTypeTypes;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestDescription;
use App\Models\ServiceRequestSource;
use App\Models\ServiceRequestStatus;
use App\Models\ServiceRequestType;
use App\Services\ServiceRequestRegister\Dto\ServiceRequestRegisterTransport;
use Carbon\Carbon;
use Closure;

class ServiceRequestRegister
{
    public function handle(ServiceRequestRegisterTransport $transport, Closure $next): void
    {
        $request = $transport->getRequest();
        $organization = $transport->getOrganization();
        $user = $transport->getUser();
        $property = $transport->getProperty();
        $resident = $transport->getResident();
        $timezone = $transport->getTimezone();
        // Service request status new
        $serviceRequestStatus = ServiceRequestStatus::select('service_request_status_id')
            ->where('slug', EnumsServiceRequestStatus::NEW())
            ->firstOrFail();

        // Service request type
        $serviceRequestSourceType = ServiceRequestType::select('service_request_type_id')
            ->where('slug', ServiceRequestTypeTypes::MAINTENANCE_REQUEST())
            ->firstOrFail();

        // Service request source
        $serviceRequestSource = ServiceRequestSource::select('service_request_source_id')
            ->when($user && $user->user_type === 'm2m', function ($query) {
                return $query->where('slug', ServiceRequestSourceTypes::LULA());
            })->when($user && $user->user_type === 'appfolio', function ($query) {
                return $query->where('slug', ServiceRequestSourceTypes::APPFOLIO());
            })
            ->firstOrFail();

        $requestedPropertyDetails = $request->get('property');

        // Collect the property access info from the request payload.
        $propertyAccessInfo = ! empty($requestedPropertyDetails['access_info']) ? $requestedPropertyDetails['access_info'] : [];

        // Service request attributes
        $attributes = [
            'organization_id' => $organization->organization_id,
            'property_id' => $property?->property_id,
            'requesting_resident_id' => $resident?->resident_id,
            'service_request_status_id' => $serviceRequestStatus->service_request_status_id,
            'priority' => $request->priority,
            'property_access_method' => $propertyAccessInfo['method'] ?? null,
            'property_access_code' => $propertyAccessInfo['code'] ?? null,
            'property_access_note' => $propertyAccessInfo['note'] ?? null,
            'service_request_source_id' => $serviceRequestSource->service_request_source_id,
            'service_request_type_id' => $serviceRequestSourceType->service_request_type_id,
            'timezone_id' => $timezone?->timezone_id,
        ];

        if (! empty($request->service_request_reference_id)) {
            $attributes['service_request_reference_id'] = $request->service_request_reference_id;
        }

        if (! empty($request->service_request_reference_url)) {
            $attributes['service_request_reference_url'] = $request->service_request_reference_url;
        }

        if (! empty($request->service_request_reference_number)) {
            $attributes['service_request_reference_number'] = $request->service_request_reference_number;
        }

        if (! empty($request->service_request_reference_created_at)) {
            $attributes['service_request_reference_created_at'] = Carbon::parse($request->service_request_reference_created_at)
                ->format('Y-m-d H:i:s');
        }

        // Create a service request
        $serviceRequest = ServiceRequest::create($attributes);

        $description = $request->input('description');

        if (isset($description) && trim($description) !== '') {
            //Create Service Request Description
            $serviceRequestDescription = ServiceRequestDescription::create([
                'service_request_id' => $serviceRequest->service_request_id,
                'user_id' => $user?->user_id,
                'description' => $request->description,
                'organization_id' => $serviceRequest->organization_id,
            ]);

            $serviceRequest->setRelation('serviceRequestDescription', $serviceRequestDescription);
        }

        $transport->setServiceRequest($serviceRequest);

        $next($transport);
    }
}

<?php

namespace App\Services\Appfolio;

use App\Enums\Priority;
use App\Exceptions\Appfolio\AppfolioApiException;
use App\Exceptions\Appfolio\AppfolioApiResponseException;
use App\Helpers\Helper;
use App\Jobs\Appfolio\ProcessAppfolioNewWorkOrder;
use App\Jobs\Appfolio\WorkOrderStateUpdateJob;
use App\Models\AppfolioApiLog;
use App\Models\AppfolioIntegrationSkippedWorkOrder;
use App\Models\Organization;
use App\Models\Timezone;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderSource;
use App\Packages\GoogleMap\Enum\GoogleService;
use App\Packages\GoogleMap\GoogleApiServiceFactory;
use App\Packages\GoogleMap\Services\Geocode;
use App\Services\WorkOrderRegister\WorkOrderRegisterClient;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class AppfolioService
{
    /**
     * Foresight Organization
     *
     * @var \App\Models\Organization
     */
    public $organization;

    /**
     * Appfolio partner id
     *
     * @var string|null
     */
    protected $appfolioDeveloperId;

    /**
     * Appfolio client Id
     *
     * @var string|null
     */
    protected $appfolioClientId;

    /**
     * Appfolio secret key
     *
     * @var string|null
     */
    protected $appfolioClientSecret;

    /**
     * Appfolio vendor id
     *
     * @var string|null
     */
    protected $appfolioVendorId;

    /**
     * Appfolio API URL
     *
     * @var string|null
     */
    protected $appfolioApiBaseUrl;

    /**
     * Appfolio API Last updated at
     *
     * @var string|null
     */
    protected $lastUpdatedAtFrom;

    /**
     * Create a new Appfolio service instance.
     *
     * @return void
     */
    public function __construct(Organization $organization)
    {
        $this->organization = $organization;
        $this->lastUpdatedAtFrom = config('appfolio.last_updated_at_from');
        $this->appfolioApiBaseUrl = config('appfolio.api_base_url');
        $this->appfolioVendorId = $organization->appfolio_vendor_id;
        $this->appfolioClientId = $organization->appfolio_client_id;
        $this->appfolioClientSecret = $organization->appfolio_client_secret;
        $this->appfolioDeveloperId = config('appfolio.appfolio_developer_id');
    }

    /**
     * @param  array<string,mixed>  $params
     * @return Collection<int,array<string,mixed>>
     */
    public function get(string $api, array $params = [], ?string $workOrderId = null, ?bool $skipData = false): Collection
    {
        if (empty($params['filters']['Id']) && empty($params['filters']['LastUpdatedAtFrom'])) {
            $lastUpdatedAt = Carbon::now()->subMinutes(config('appfolio.sync_interval'))->toDateTimeString();
            $params['filters']['LastUpdatedAtFrom'] = config('appfolio.last_updated_at_from') ?? $lastUpdatedAt;
        }

        $response = $this->connect()->get($this->appfolioApiBaseUrl . $api, $params);

        Log::channel('appfolio')->info('get() response', [
            'api' => $api,
            'params' => $params,
            'workOrderId' => $workOrderId,
        ]);

        $this->logApiResponse($api, $response, $params, 'GET', $workOrderId);

        return $this->responseFormatter($response, $skipData);
    }

    /**
     * @param  array<string,mixed>  $body
     * @return Collection<int,array<string,mixed>>
     */
    public function post(string $api, array $body = [], ?string $workOrderId = null): Collection
    {
        $response = $this->connect()->post($this->appfolioApiBaseUrl . $api, $body);

        Log::channel('appfolio')->info('post() response', [
            'response' => $response->json() ?? $response->body(),
            'api' => $api,
            'body' => $body,
            'workOrderId' => $workOrderId,
        ]);

        $this->logApiResponse($api, $response, $body, 'POST', $workOrderId);

        return $this->responseFormatter($response);
    }

    /**
     * @param  array<string,mixed>  $body
     * @return Collection<int,array<string,mixed>>
     */
    public function patch(string $api, array $body = [], ?string $workOrderId = null): Collection
    {
        $response = $this->connect()->patch($this->appfolioApiBaseUrl . $api, $body);

        Log::channel('appfolio')->info('patch() response', [
            'response' => $response->json() ?? $response->body(),
            'api' => $api,
            'body' => $body,
            'workOrderId' => $workOrderId,
        ]);

        $this->logApiResponse($api, $response, $body, 'PATCH', $workOrderId);

        return $this->responseFormatter($response);
    }

    /**
     * @return Collection<int,array<string,mixed>>
     */
    public function postAttachment(string $api, string $path, ?string $fileName, ?string $workOrderId = null): Collection
    {
        if (! Storage::exists($path)) {
            throw new AppfolioApiException(__('File not found.'));
        }
        $response = $this->connect()->attach('File', File::get($path), $fileName)
            ->post($this->appfolioApiBaseUrl . $api);

        Log::channel('appfolio')->info('postAttachment response', [
            'response' => $response,
            '$path' => $path,
            '$fileName' => $fileName,
            '$api' => $api,
        ]);

        $this->logApiResponse($api, $response, [$fileName], 'POST', $workOrderId);

        return $this->responseFormatter($response);
    }

    public function createNotes(string $workOrderId, string $notes): void
    {
        $payload = [
            'Body' => $notes,
        ];
        try {
            $response = $this->post("work_orders/{$workOrderId}/notes", $payload, $workOrderId)->first();

            Log::channel('appfolio')->info('createNotes response', [
                'response' => $response,
                'note' => $notes,
                'workOrderId' => $workOrderId,
            ]);
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Log::channel('appfolio')->error('createNotes Exception!', [
                'organization' => $this->organization->name,
                'notes' => $notes,
                'workOrderId' => $workOrderId,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }
    }

    /**
     * @return array<string,mixed>|null
     */
    public function propertyDetails(string $propertyId, ?string $workOrderId = null): ?array
    {
        try {
            $params = [];
            $params['filters']['Id'] = $propertyId;
            $params['filters']['IncludeHidden'] = 'true';

            return $this->get('properties', $params, $workOrderId)->first();
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Log::channel('appfolio')->error('propertyDetails Exception!', [
                'organization' => $this->organization->name,
                'propertyId' => $propertyId,
                'workOrderId' => $workOrderId,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }

        return null;
    }

    /**
     * @return array<string,mixed>|null
     */
    public function unitDetails(string $unitId, ?string $workOrderId = null): ?array
    {
        try {
            $params = [];
            $params['filters']['Id'] = $unitId;
            $params['filters']['IncludeHidden'] = 'true';

            return $this->get('units', $params, $workOrderId)->first();
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Log::channel('appfolio')->error('unitDetails Exception!', [
                'organization' => $this->organization->name,
                'unitId' => $unitId,
                'workOrderId' => $workOrderId,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }

        return null;
    }

    /**
     * @param  array<string,mixed>  $workOrder
     * @return Collection<int,array<string,mixed>>
     */
    public function getTenants(array $workOrder): Collection
    {
        try {
            $params['filters']['LastUpdatedAtFrom'] = config('appfolio.last_updated_at_from');
            $params['filters']['Status'] = 'Current,Notice';

            if (! empty($workOrder['UnitId'])) {
                $params['filters']['UnitId'] = $workOrder['UnitId'];
            }

            if (! empty($workOrder['PropertyId'])) {
                $params['filters']['PropertyId'] = $workOrder['PropertyId'];
            }

            return $this->get('tenants', $params, $workOrder['Id']);
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Log::channel('appfolio')->error('getTenants Exception!', [
                'organization' => $this->organization->name,
                'workOrder' => $workOrder,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }

        return collect([]);
    }

    /**
     * @return Collection<int,array<string,mixed>>
     */
    public function tenantDetails(string $tenantId, ?string $workOrderId = null): Collection
    {
        try {
            $params = [];
            $params['filters']['Id'] = $tenantId;
            $params['filters']['IncludeHidden'] = 'true';

            return $this->get('tenants', $params, $workOrderId);
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Log::channel('appfolio')->error('tenantDetails Exception!', [
                'organization' => $this->organization->name,
                'tenantId' => $tenantId,
                'workOrderId' => $workOrderId,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }

        return collect([]);
    }

    public function ingestNewWorkOrders(): void
    {
        Log::channel('appfolio')->info('Started to Sync Work Orders of ' . ($this->organization->name));

        $organizationWorkOrders = $this->getWorkOrders();

        $newWorkOrders = $organizationWorkOrders->where('VendorId', $this->appfolioVendorId)
            ->whereIn('Status', ['Assigned', 'Scheduled', 'Estimate Requested']);

        $canceledWorkOrders = $organizationWorkOrders->where('VendorId', $this->appfolioVendorId)
            ->whereIn('Status', ['Canceled']);

        if ($canceledWorkOrders->isNotEmpty()) {
            $canceledAppfolioExistingWorkOrders = WorkOrder::join('work_order_sources', 'work_order_sources.work_order_source_id', '=', 'work_orders.work_order_source_id')
                ->select('work_orders.*')
                ->where('work_order_sources.slug', 'appfolio')
                ->whereIn('work_orders.work_order_reference_id', $canceledWorkOrders->pluck('Id'))
                ->where('work_orders.organization_id', $this->organization->organization_id)
                ->where('work_orders.state', '!=', 'canceled')
                ->get();

            if ($canceledAppfolioExistingWorkOrders->isNotEmpty()) {
                $canceledAppfolioExistingWorkOrders->each(function ($canceledAppfolioExistingWorkOrder) use ($canceledWorkOrders) {
                    $appfolioWorkOrderReference = $canceledWorkOrders->where('Id', $canceledAppfolioExistingWorkOrder->work_order_reference_id)->first();

                    Log::channel('appfolio')->info('Before processing WorkOrderStateUpdateJob', [
                        'appfolioWorkOrderReference' => $appfolioWorkOrderReference,
                        'work_order_id' => $canceledAppfolioExistingWorkOrder->work_order_id,
                    ]);

                    if (! empty($appfolioWorkOrderReference)) {
                        dispatch(new WorkOrderStateUpdateJob($canceledAppfolioExistingWorkOrder, $appfolioWorkOrderReference));
                    }
                });
            }
        }

        Log::channel('appfolio')->info("Fetched Work Orders for {$this->organization->name}", [
            'Uuids' => $newWorkOrders->pluck('Id')->all(),
            'WorkOrderNumbers' => $newWorkOrders->pluck('WorkOrderNumber')->all(),
        ]);

        // Find similar work orders already created in Foresight
        $appfolioExistingWorkOrderUuids = WorkOrder::join('work_order_sources', 'work_order_sources.work_order_source_id', '=', 'work_orders.work_order_source_id')
            ->where('work_order_sources.slug', 'appfolio')
            ->whereIn('work_orders.work_order_reference_id', $newWorkOrders->pluck('Id'))
            ->where('work_orders.organization_id', $this->organization->organization_id)
            ->pluck('work_orders.work_order_reference_id')
            ->all();

        $newWorkOrders = $newWorkOrders->whereNotIn('Id', $appfolioExistingWorkOrderUuids)->unique('Id');

        // Skip existing work orders those are created before integration
        $skipExistingWorkOrders = AppfolioIntegrationSkippedWorkOrder::join('work_orders', function ($query) {
            $query->on('work_orders.work_order_reference_id', '=', 'appfolio_integration_skipped_work_orders.appfolio_work_order_uuid');
        })
            ->join('work_order_sources', 'work_order_sources.work_order_source_id', '=', 'work_orders.work_order_source_id')
            ->where('work_order_sources.slug', 'appfolio')
            ->where('appfolio_integration_skipped_work_orders.organization_id', $this->organization->organization_id)
            ->whereIn('appfolio_integration_skipped_work_orders.appfolio_work_order_uuid', $newWorkOrders->pluck('Id'))
            ->whereNotIn('work_order_status', ['New'])
            ->where('appfolio_integration_skipped_work_orders.vendor_id', $this->appfolioVendorId)
            ->get();

        if ($skipExistingWorkOrders->isNotEmpty()) {
            $skipExistingWorkOrderIds = $skipExistingWorkOrders->whereNotIn('work_order_status', ['Assigned', 'Scheduled', 'Estimate Requested'])
                ->pluck('reference_uuid')
                ->all();

            Log::channel('appfolio')->info("Skipped Work Orders for {$this->organization->name}", [
                'Uuids' => $skipExistingWorkOrderIds,
            ]);
            $newWorkOrders = $newWorkOrders->whereNotIn('Id', $skipExistingWorkOrderIds)->unique('Id');
        }

        Log::channel('appfolio')->info('Start processNewWorkOrders');

        $this->processNewWorkOrders($newWorkOrders, $skipExistingWorkOrders);

        // TO DO
        //$this->syncExistingWorkOrders($organizationWorkOrders);
        Log::channel('appfolio')->info('Completed Work Order Sync of ' . ($this->organization->name));
    }

    /**
     * Process new work order
     *
     * @param  array<string,mixed>  $workOrder
     * @param  array<string,mixed>  $additionalData
     */
    public function processNewWorkOrder(array $workOrder, ?array $additionalData): void
    {
        try {
            $data = [];
            $needDeveloperAlert = false;
            $developerAlerts = [];
            Log::channel('appfolio')->info('processNewWOrkOrder started for ' . ($this->organization->name));

            $this->addAppfolioApiWorkOrderLog($workOrder);

            if (! empty($additionalData['property'])) {
                Log::channel('appfolio')->info('Property details from initial data', $additionalData['property']);
                $data['property_details'] = $additionalData['property'];
            } elseif (! empty($workOrder['PropertyId'])) {
                $data['property_details'] = $this->propertyDetails($workOrder['PropertyId'], $workOrder['Id']);

                if (! empty($data['property_details'])) {
                    $needDeveloperAlert = true;
                    $developerAlerts['property_api_empty_result_for_id'] = $workOrder['PropertyId'];
                }
            }

            if (! empty($additionalData['unit'])) {
                Log::channel('appfolio')->info('Unit details from initial data', $additionalData['unit']);
                $data['unit_details'] = $additionalData['unit'];
            } elseif (! empty($workOrder['UnitId'])) {
                $data['unit_details'] = $this->unitDetails($workOrder['UnitId'], $workOrder['Id']);

                if (! empty($data['unit_details'])) {
                    $needDeveloperAlert = true;
                    $developerAlerts['unit_api_empty_result_for_id'] = $workOrder['UnitId'];
                }
            }

            $this->validatePropertyLocation($data);

            if (! empty($additionalData['tenant'])) {
                Log::channel('appfolio')->info('Tenant details from initial data', $additionalData['tenant']);
                $data['tenant_details'][] = $additionalData['tenant'];
            } elseif (! empty($workOrder['RequestingTenantId'])) {
                $data['tenant_details'] = $this->tenantDetails($workOrder['RequestingTenantId'], $workOrder['Id']);

                if ($data['tenant_details']->isEmpty()) {
                    $needDeveloperAlert = true;
                    $developerAlerts['tenant_api_empty_result_for_id'] = $workOrder['RequestingTenantId'];
                }
            } else {
                $data['tenant_details'] = $this->getTenants($workOrder);

                if ($data['tenant_details']->isEmpty()) {
                    $needDeveloperAlert = true;
                    $developerAlerts['tenant_api_empty_result_for_id'] = $workOrder['RequestingTenantId'];
                }
            }

            $scrapperData = $this->fetchAdditionalDataFromScrapper($workOrder);

            $payload = $this->formatWorkOrderPayload($workOrder, $data, $scrapperData);

            if ($needDeveloperAlert) {
                $developerAlerts['work_order_no'] = $workOrder['WorkOrderNumber'] ?? '-';
                $developerAlerts['company_name'] = $this->organization->name;
                $developerAlerts['work_order_details'] = $workOrder;
                $developerAlerts['payload'] = $payload;
            }

            if (! empty($payload['property'])) {
                $existingWorkOrders = WorkOrder::join('work_order_sources', 'work_order_sources.work_order_source_id', '=', 'work_orders.work_order_source_id')
                    ->where('work_order_sources.slug', 'appfolio')
                    ->where('work_orders.organization_id', $this->organization->organization_id)
                    ->where('work_orders.work_order_reference_id', $workOrder['Id'])
                    ->whereNotNull('work_orders.work_order_reference_id')
                    ->count();

                if ($existingWorkOrders > 0) {
                    Log::channel('appfolio')->info("{$this->organization->name}'s Appfoliowork order is already created in Foresight", [
                        'payload' => $payload,
                    ]);

                    exit;
                }

                $foresightWorkOrder = $this->requestNewWorkOrder($payload);

                Log::channel('appfolio')->info('Requested service from appfolio', [
                    'workOrder' => $foresightWorkOrder,
                    'payload' => $payload,
                ]);

                // Create notes when Foresight accept the work order
                if ($foresightWorkOrder instanceof WorkOrder) {

                    $protocol = app()->environment('local') ? 'http' : 'https';

                    // $this->createNotes($workOrder['Id'],
                    //     __('appfolio.notes.job_created', [
                    //         'work_order_reference_number' => $workOrder['WorkOrderNumber'],
                    //         'work_order_number' => $foresightWorkOrder->work_order_number,
                    //         'work_order_url' => "{$protocol}://{$this->organization->domain}/work-orders/v/l/default?id={$foresightWorkOrder->work_order_uuid}",
                    //     ])
                    // );
                } else {
                    $supportAlert['work_order_no'] = $workOrder['WorkOrderNumber'] ?? '-';
                    $supportAlert['company_name'] = $this->organization->name;
                    $supportAlert['response'] = $workOrder;

                    $uniqueId = $workOrder['Id'] . '-request-service-response-failed';

                    if (! Cache::has($uniqueId)) {
                        $ttl = config('appfolio.sync_interval') * 60;
                        Cache::remember($uniqueId, $ttl, function () {
                            return 1;
                        });
                        //TO DO Developer notification
                    }

                    $supportAlert['response'] = $foresightWorkOrder;
                    $supportAlert['payload'] = $payload;

                    Log::channel('appfolio')->info("{$this->organization->name}'s Appfolio job is not created in Foresight", $supportAlert);

                    Helper::developerAlert($supportAlert, 'Failed to add appfolio jobs to Foresight!');
                }
            } else {
                $supportNotificationData['work_order_no'] = $workOrder['WorkOrderNumber'] ?? '-';
                $supportNotificationData['company_name'] = $this->organization->name;
                $supportNotificationData['reason'] = 'Property information not found in appfolio API';

                Log::channel('appfolio')->info("{$this->organization->name}'s  Appfolio job failed due to invalid property_details", $supportNotificationData);

                $uniqueId = $workOrder['Id'] . '-invalid-property-address';

                if (! Cache::has($uniqueId)) {
                    $ttl = config('appfolio.sync_interval') * 60;
                    Cache::remember($uniqueId, $ttl, function () {
                        return 1;
                    });
                    // TO DO send notification
                }
            }
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Helper::exceptionLog($e, $workOrder, "{$this->organization->name}'s ScrapeAppfolioNewWorkOrder Exception.");
            Log::channel('appfolio')->error("{$this->organization->name}'s ScrapeAppfolioNewWorkOrder Exception.", [
                'work_order' => $workOrder,
                'organization' => $this->organization,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }
    }

    /**
     * @param  array<string,mixed>  $workOrder
     * @return array<string,mixed>
     */
    public function fetchAdditionalDataFromScrapper(array $workOrder): array
    {
        try {
            Log::channel('appfolio')->info("{$this->organization->name}'s Started fetchAdditionalDataFromScrapper");

            $scrapperApiUrl = config('appfolio.scraper_api_url');

            if (empty($this->organization->appfolio_customer_id) || empty($workOrder['WorkOrderNumber'])) {
                return [];
            }

            $response = Http::withHeaders([
                'accept' => 'application/json',
                'content-type' => 'application/json',
            ])->get($scrapperApiUrl, [
                'appfolio_customer_id' => $this->organization->appfolio_customer_id,
                'numberForDisplay' => $workOrder['WorkOrderNumber'],
            ]);

            Log::channel('appfolio')->info("{$this->organization->name}'s fetchAdditionalDataFromScrapper Log!!", [
                'scrapper_api_url' => $scrapperApiUrl,
                'appfolio_customer_id' => $this->organization->appfolio_customer_id,
                'numberForDisplay' => $workOrder['WorkOrderNumber'],
                'status' => $response->status(),
                'json' => $response->json(),
            ]);

            if ($response->successful()) {
                return $response->json('result');
            }

            $data = [
                'work_order' => $workOrder,
                'partner_id' => $this->organization->organization_id,
                'label' => $this->organization->name,
                'response' => $response->json() ?? $response->body(),
                'status' => $response->status(),
                'scrapper_api_url' => $scrapperApiUrl,
            ];

            Log::channel('appfolio')->info("{$this->organization->name}'s Scrapper API for additional data has failed!!", $data);
        } catch (Exception $e) {
            Log::channel('appfolio')->error("Scrapper API Exception! - {$this->organization->name}", [
                'organization' => $this->organization->name,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
            Helper::exceptionLog($e, $workOrder, "{$this->organization->name}'s Scrapper API Exception!", true);
        }

        return [];
    }

    /**
     * Format work order data for creating new work orders in Foresight
     *
     * @param  array<string,mixed>  $workOrder
     * @param  array<string,mixed>  $data
     * @param  array<string,mixed>  $scrappedData
     * @return array<string,mixed>
     */
    public function formatWorkOrderPayload(array $workOrder, array $data, array $scrappedData = []): array
    {

        $needQuote = ! empty($scrappedData['is_quote']) && $scrappedData['is_quote'] == 'Yes' ? 'yes' : ($workOrder['Status'] == 'Estimate Requested' ? 'yes' : 'no');

        $workOrderSource = Cache::remember('appfolio_work_order_source', config('cache.system_variable_ttl'), function () {
            return WorkOrderSource::where('slug', 'appfolio')->first();
        });

        $priority = $workOrder['Priority'] ?? 'low';

        switch ($priority) {
            case 'Urgent':
                $priority = Priority::URGENT();
                break;
            case 'Normal':
                $priority = Priority::MEDIUM();
                break;
            case 'Low':
                $priority = Priority::LOW();
                break;
            default:
                $priority = Priority::MEDIUM();
                break;
        }

        $payload = [
            'organization_uuid' => $this->organization->organization_uuid ?? null,
            'work_order_source_id' => $workOrderSource->work_order_source_id ?? null,
            'work_order_reference_id' => $workOrder['Id'] ?? null,
            'work_order_reference_number' => $workOrder['WorkOrderNumber'] ?? null,
            'work_order_reference_url' => $scrappedData['work_order_reference_url'] ?? null,
            'work_order_reference_created_at' => $workOrder['CreatedAt'] ?? null,
            'description' => $workOrder['JobDescription'] ?? null,
            'priority' => $priority,
            'need_quote' => $needQuote,
            'nte_amount' => $scrappedData['maintenance_limit'] ?? null,
            'special_instructions' => $scrappedData['specialInstructions'] ?? null,
            'vendor_instructions' => $workOrder['VendorInstructions'] ?? null,
            'timezone' => 'America/Chicago',
            'property' => $this->formatPropertyInfo($workOrder, $data),
            'residents' => $this->formatTenantData($data),
            'photos' => $this->formatPhotos($scrappedData),
        ];

        Log::channel('appfolio')->info("{$this->organization->name}'s  New work order payload:", $payload);

        return $payload;
    }

    public function addSkippedWorkOrders(): void
    {
        try {
            $this->addUnAssignedWorkOrders();
            $this->addExistingWorkOrders();
        } catch (Exception $e) {
            Log::channel('appfolio')->error('Appfolio exception addSkippedWorkOrders()', [
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
            Helper::exceptionLog($e, ['organization' => $this->organization->domain], 'addSkippedWorkOrders exception!');
        }
    }

    /**
     * @param  array<mixed, mixed>  $params
     * @return Collection<int, array<string, mixed>|mixed>
     */
    public function getPaginatedWorkOrders(string $api, array $params = []): Collection
    {
        try {
            if (empty($params['filters']['Id']) && empty($params['filters']['LastUpdatedAtFrom'])) {
                $lastUpdatedAt = Carbon::now()->subMinutes(config('appfolio.sync_interval'))->toDateTimeString();
                $params['filters']['LastUpdatedAtFrom'] = config('appfolio.last_updated_at_from') ?? $lastUpdatedAt;
            }

            $response = $this->connect()->get($this->appfolioApiBaseUrl . $api, $params);

            $this->logApiResponse($api, $response, $params, 'GET');

            return $this->responseFormatter($response, true);
        } catch (Exception $e) {
            Log::channel('appfolio')->error("{$this->organization->name}'s getPaginatedWorkOrders() exception", [
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
            Helper::exceptionLog($e, ['api' => $api, 'params' => $params], 'Appfolio exception getPaginatedWorkOrders()');
        }

        return collect([]);
    }

    /**
     * Update Work Order Details.
     *
     * @param  array<string,mixed>  $payload
     * @return Collection<int, array<string, mixed>|mixed>
     */
    public function updateWorkOrder(string $workOrderId, array $payload)
    {
        return $this->patch("work_orders/{$workOrderId}", $payload);
    }

    /**
     * Fetch appfolio lula vendor
     *
     * @param  array<string,mixed>  $params
     * @return Collection<int, array<string, mixed>|mixed>
     */
    public function getVendors(array $params, ?bool $includeData = true): Collection
    {
        try {
            $this->checkIntegration();

            if (empty($params['filters']['LastUpdatedAtFrom'])) {
                // Setting a default date to fetch last updated at
                $params['filters']['LastUpdatedAtFrom'] = $this->lastUpdatedAtFrom;
            }

            return $this->get('vendors', $params, null, $includeData);
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Helper::exceptionLog($e, $params, 'Exception in fetchAppfolioVendors');
            Log::debug('fetchAppfolioVendors Exception', [
                'error' => $e->getMessage(),
                'input' => $params,
            ]);

            throw $e;
        }
    }

    /**
     * Check appfolio integration is active
     *
     * @return void
     */
    protected function checkIntegration()
    {
        if (empty($this->appfolioClientId) || empty($this->appfolioClientSecret)) {
            throw new AppfolioApiException(__('Appfolio integration is not enabled!'));
        }
    }

    /**
     * @param  array<string,string>  $headers
     */
    protected function connect(array $headers = []): PendingRequest
    {
        $encodedString = base64_encode("{$this->appfolioClientId}:{$this->appfolioClientSecret}");

        $headers = array_merge($headers, [
            'X-AppFolio-Developer-ID' => $this->appfolioDeveloperId,
            'Authorization' => "Basic {$encodedString}",
        ]);

        return Http::withHeaders($headers);
    }

    /**
     * @return Collection<int,array<string,mixed>>
     *
     * @throws AppfolioApiResponseException
     */
    protected function responseFormatter(Response $response, ?bool $skipData = false): Collection
    {
        if ($response->failed()) {
            Log::channel('appfolio')->info("{$this->organization->name}'s responseFormatter", ['response' => $response->body()]);

            if ($response->status() == 401) {
                throw new AppfolioApiResponseException(__('Failed to connect Appfolio, Either Appfolio integration is disabled or invalid client credentials!'));
            }

            throw new AppfolioApiResponseException($response->body());
        }

        if (! $skipData && ! empty($response->json()['data'])) {
            return new Collection($response->json()['data']);
        }

        return new Collection($response->json());
    }

    /**
     * @param  array<mixed>  $payload
     */
    protected function logApiResponse(string $api, Response $response, ?array $payload = [], string $method = 'GET', ?string $workOrderId = null): void
    {
        try {
            Log::channel('appfolio')->info("{$this->organization->name}'s logApiResponse", ['payload' => $payload, 'method' => $method, 'api' => $api]);

            $appfolioApiLog = new AppfolioApiLog;

            if ($api == 'work_orders') {
                $appfolioApiLog->appfolio_entity_type = 'WorkOrder';
            } elseif ($api == 'properties') {
                $appfolioApiLog->appfolio_entity_type = 'Property';
            } elseif ($api == 'units') {
                $appfolioApiLog->appfolio_entity_type = 'Unit';
            } elseif ($api == 'tenants') {
                $appfolioApiLog->appfolio_entity_type = 'Tenant';
            } elseif (str_contains($api, 'notes')) {
                $appfolioApiLog->appfolio_entity_type = 'Note';
            } elseif (str_contains($api, 'attachments')) {
                $appfolioApiLog->appfolio_entity_type = 'Attachment';
            }

            $appfolioApiLog->appfolio_work_order_uuid = $workOrderId;

            $appfolioApiLog->method = strtoupper($method);
            $appfolioApiLog->payload = $payload;
            $appfolioApiLog->api_url = $this->appfolioApiBaseUrl . $api;
            $appfolioApiLog->status_code = $response->status();
            $appfolioApiLog->response = $response->json() ?? $response->body();

            $notificationData = [
                'organization' => $this->organization->name,
                'response' => $response->json() ?? $response->body(),
                'statusCode' => $response->status(),
            ];

            if ($response->successful()) {
                if (
                    isset($response->json()['data']) && count($response->json()['data']) == 1 &&
                    ! empty($payload['filters']['Id']) && ! is_numeric(strpos($payload['filters']['Id'], ','))
                ) {
                    $entity = Arr::first((array) $response->json()['data']);
                    $appfolioApiLog->appfolio_entity_uuid = $entity['Id'] ?? null;
                }
            }

            if (empty($appfolioApiLog->appfolio_entity_uuid) && ! empty($payload['filters']['Id']) && ! is_numeric(strpos($payload['filters']['Id'], ','))) {
                $appfolioApiLog->appfolio_entity_uuid = $payload['filters']['Id'];
            }

            if (! empty($payload['filters']['LastUpdatedAtFrom'])) {
                $appfolioApiLog->last_updated_at = $payload['filters']['LastUpdatedAtFrom'];
            }

            $appfolioApiLog->save();

            if (! $response->successful()) {
                $notificationData['organization_id'] = $this->organization->organization_id;
                $notificationData['domain'] = $this->organization->domain;
                $notificationData['payload'] = $payload;
                $notificationData['method'] = $method;
                $notificationData['api_url'] = $this->appfolioApiBaseUrl . $api;
                $notificationData['statusCode'] = $response->status();
                Log::channel('appfolio')->info("{$this->organization->name}'s Appfolio API integration logApiResponse failed!", $notificationData);

                //TO DO Send developer email
            }
        } catch (Exception $e) {
            Helper::exceptionLog(
                $e,
                ['api' => $api, 'response' => $response, 'payload' => $payload],
                'Appfolio exception in logApiResponse()'
            );
            Log::channel('appfolio')->error("{$this->organization->name}'s Appfolio exception logApiResponse()", [
                'exception' => $e->getMessage(),
                'api' => $api,
                'response' => $response,
                'payload' => $payload,
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }
    }

    /**
     * @param  array<string,mixed>  $params
     * @return Collection<int,array<string,mixed>>
     */
    protected function getWorkOrders(array $params = []): Collection
    {
        $params['filters']['LastUpdatedAtFrom'] = Carbon::now()->subMinutes(config('appfolio.sync_interval'))->toDateTimeString();
        $params['filters']['VendorId'] = $this->appfolioVendorId;

        // This field is using for filter work orders that are created from the integrated date.
        if (empty($params['filters']['CreatedAtFrom']) && ! empty($this->organization->appfolio_integrated_at)) {
            $params['filters']['CreatedAtFrom'] = $this->organization->appfolio_integrated_at->subMonths(2)->toDateTimeString();
        }

        return $this->get('work_orders', $params);
    }

    /**
     * @param  Collection<int,array<string,mixed>>  $newWorkOrders
     * @param  Collection<int,AppfolioIntegrationSkippedWorkOrder>  $skippedExistingWorkOrders
     */
    protected function processNewWorkOrders(Collection $newWorkOrders, Collection $skippedExistingWorkOrders): void
    {
        Log::channel('appfolio')->info("{$this->organization->name}'s Started processNewWorkOrders " . ($this->organization->name), [
            'WorkOrderNumbers' => $newWorkOrders->pluck('WorkOrderNumber')->all(),
            'WorkOrderUuids' => $newWorkOrders->pluck('Id')->all(),
            'SkippedWorkOrderNumbers' => $skippedExistingWorkOrders->pluck('WorkOrderNumber')->all(),
            'SkippedWorkOrderUuids' => $skippedExistingWorkOrders->pluck('Id')->all(),
        ]);

        $propertyUuids = $newWorkOrders->pluck('PropertyId')->filter()->implode(',');
        $unitUuids = $newWorkOrders->pluck('UnitId')->filter()->implode(',');
        $tenantUuids = $newWorkOrders->pluck('RequestingTenantId')->filter()->implode(',');

        $properties = $this->getEntityListByUuid($propertyUuids, 'properties');
        $units = $this->getEntityListByUuid($unitUuids, 'units');
        $tenants = $this->getEntityListByUuid($tenantUuids, 'tenants');

        foreach ($newWorkOrders as $newWorkOrder) {
            $data = [];
            $developerNotification = [];
            try {
                Log::channel('appfolio')->info("{$this->organization->name}'s processNewWorkOrders started", $newWorkOrder);
                // Send notification if the work order is in skipped work order list
                if (
                    $skippedExistingWorkOrders->isNotEmpty() &&
                    ! empty($this->organization->appfolio_integrated_at) &&
                    $skippedExistingWorkOrders->where('appfolio_work_order_uuid', $newWorkOrder['Id'])->isNotEmpty()
                ) {
                    $skippedWorkOrder = $skippedExistingWorkOrders->where('appfolio_work_order_uuid', $newWorkOrder['Id'])->first();

                    if (empty($skippedWorkOrder->notified_at)) {
                        $skippedWorkOrder->notified_at = Carbon::now();
                        $skippedWorkOrder->save();

                        Log::channel('appfolio')->warning("{$this->organization->name}'s Appfolio Work Order skipped !!.", [
                            'organization' => $this->organization->name,
                            'WorkOrderNumber' => $newWorkOrder['WorkOrderNumber'],
                            'WO Details' => $newWorkOrder,
                        ]);
                    } else {
                        Log::channel('appfolio')->warning("{$this->organization->name}'s Skipped already notified Appfolio Work Order !!.", [
                            'organization' => $this->organization->name,
                            'WorkOrderNumber' => $newWorkOrder['WorkOrderNumber'],
                            'WO Details' => $newWorkOrder,
                        ]);
                    }

                    continue;
                }

                if ($properties->isNotEmpty() && ! empty($newWorkOrder['PropertyId'])) {
                    $data['property'] = $properties->where('Id', $newWorkOrder['PropertyId'])->first();
                    $developerNotification['property'] = $data['property'];
                }

                if ($units->isNotEmpty() && ! empty($newWorkOrder['UnitId'])) {
                    $data['unit'] = $units->where('Id', $newWorkOrder['UnitId'])->first();
                    $developerNotification['unit'] = $data['unit'];
                }

                if ($tenants->isNotEmpty() && ! empty($newWorkOrder['RequestingTenantId'])) {
                    $data['tenants'] = $tenants->where('Id', $newWorkOrder['RequestingTenantId'])->first();
                    $developerNotification['tenants'] = $data['tenants'];
                }
            } catch (Exception $e) {
                $developerNotification['errors'] = $e->getMessage();
                $developerNotification['new_work_order'] = $newWorkOrder;
                Log::channel('appfolio')->error("{$this->organization->name}'s Appfolio API initial data mapping failed - Normal !!", [
                    'organization' => $this->organization->organization_id,
                    'exception' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'line' => $e->getLine(),
                    'file' => $e->getFile(),
                    'data' => $developerNotification,
                ]);

                //TO DO - Developer notification
            }

            Log::channel('appfolio')->info("{$this->organization->name}'s processNewWorkOrders started!!.", [
                'organization' => $this->organization->name,
                'WorkOrder' => $newWorkOrder,
            ]);

            dispatch(new ProcessAppfolioNewWorkOrder($this->organization, $newWorkOrder, $data))->delay(config('appfolio.redis.queue_delay'));
        }
    }

    /**
     * @return Collection<int,array<string,mixed>>
     */
    protected function getEntityListByUuid(string $entityUuids, string $url): Collection
    {
        try {
            if (! empty($entityUuids)) {
                $params = [];
                $params['filters']['Id'] = $entityUuids;

                return $this->get($url, $params);
            }
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Log::channel('appfolio')->info("{$this->organization->name}'s Exception in getEntityListByUuid!!.", [
                'error' => $e->getMessage(),
                'entityUuids' => $entityUuids,
                'url' => $url,
            ]);
        }

        return collect([]);
    }

    /**
     * @param  Collection<int,array<string,mixed>>  $organizationWorkOrders
     */
    protected function syncExistingWorkOrders(Collection $organizationWorkOrders): void
    {
        try {

            Log::channel('appfolio')->info("{$this->organization->name}'s syncExistingWorkOrders started!!.", [
                'organization' => $this->organization->organization_id,
                'WorkOrders' => $organizationWorkOrders->pluck('WorkOrderNumber'),
            ]);

            $foresightWorkOrders = $organizationWorkOrders->where('VendorId', $this->appfolioVendorId);

            $appfolioOwnedExistingWorkOders = WorkOrder::join('work_order_sources', 'work_order_sources.work_order_source_id', '=', 'work_orders.work_order_source_id')
                ->where('work_order_sources.slug', 'appfolio')
                ->whereIn('work_orders.work_order_reference_id', $foresightWorkOrders->pluck('Id'))
                ->where('work_orders.organization_id', $this->organization->organization_id)
                ->get();

            // TO DO - appfolio jobs update to Foresight
            $appfolioOwnedExistingWorkOders->each(function ($workOrder) use ($foresightWorkOrders) {
                $appfolioWorkOrder = $foresightWorkOrders->where('Id', $workOrder->work_order_reference_id)->first();

                if (! empty($appfolioWorkOrder)) {
                    $this->validateAndUpdateJobs($workOrder, $appfolioWorkOrder);
                }
            });
        } catch (Exception $e) {
            Log::channel('appfolio')->error("{$this->organization->name}'s syncExistingWorkOrders - Appfolio Exception", [
                'organization' => $this->organization->name,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }

        Log::channel('appfolio')->info("{$this->organization->name}'s syncExistingWorkOrders completed!!.", [
            'organization' => $this->organization->organization_id,
        ]);
    }

    /**
     * @param  array<string,mixed>  $appfolioWorkOrder
     */
    protected function validateAndUpdateJobs(WorkOrder $workOrder, array $appfolioWorkOrder): void
    {
        //TO DO Notify support users when foresight work order's status is not matching with the appfolio work order status
    }

    /**
     * Validate property location
     *
     * @param  array<string,mixed>  $data
     */
    protected function validatePropertyLocation(array $data): bool
    {
        $isValid = true;

        //TO DO - Allow all location for now
        return true;
    }

    /**
     * @param  array<string,mixed>  $payload
     *
     * @throws Exception
     */
    protected function requestNewWorkOrder(array $payload): ?WorkOrder
    {
        try {
            $request = app(Request::class);
            $request->initialize($payload);

            //TO DO - Need authorization, this is a temp fix
            $user = User::newModelInstance([
                'first_name' => 'Public',
                'last_name' => 'API',
                'organization_id' => $this->organization->organization_id,
                'user_type' => 'appfolio',
            ]);

            if (method_exists($user, 'setRelation')) {
                $user->setRelation('organization', $this->organization);
            }

            $request->setUserResolver(function () use ($user) {
                return $user;
            });

            Log::channel('appfolio')->debug("{$this->organization}'s requestNewWorkOrder validator", [
                'validated' => $request,
            ]);

            if ($request) {
                DB::beginTransaction();

                $workOrderRegisterClient = app(WorkOrderRegisterClient::class);
                $timezone = Timezone::where('name', $payload['timezone'])->firstOrFail();
                $workOrder = $workOrderRegisterClient->register($request, $this->organization, $timezone);

                DB::commit();

                return $workOrder;
            }

            Log::channel('appfolio')->debug("{$this->organization}'s requestNewWorkOrder payload is invalid", [
                'payload' => $payload,
                'errors' => $request,
            ]);

            throw new Exception("{$this->organization->name}'s new work order payload is invalid");
        } catch (Exception $e) {
            Log::channel('appfolio')->error("{$this->organization->name}'s Appfolio requestNewWorkOrder Exception", ['error' => $e->getMessage()]);
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * @param  array<string,mixed>  $workOrder
     */
    protected function addAppfolioApiWorkOrderLog(array $workOrder): void
    {
        try {
            $appfolioApiLog = new appfolioApiLog;
            $appfolioApiLog->appfolio_entity_type = 'WorkOrder';
            $appfolioApiLog->appfolio_entity_uuid = $workOrder['Id'];
            $appfolioApiLog->appfolio_work_order_uuid = $workOrder['Id'];
            $appfolioApiLog->log_type = 'system';
            $appfolioApiLog->response = $workOrder;
            $appfolioApiLog->last_updated_at = $workOrder['LastUpdatedAt'];

            $appfolioApiLog->save();
        } catch (Exception $e) {
            Log::channel('appfolio')->error("Appfolio exception logWorkOrderData() - {$this->organization->name}", [
                'work_order' => $workOrder,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }
    }

    /**
     * @param  array<string,mixed>  $workOrder
     */
    protected function findServiceCategory(array $workOrder): string
    {
        $vendorTrades = config('appfolio.vendor_trades');

        $slug = 'unknown';

        if (! empty($workOrder['VendorTrade']) && ! empty($vendorTrades[$workOrder['VendorTrade']])) {
            $slug = $vendorTrades[$workOrder['VendorTrade']];
        }

        return $slug;
    }

    /**
     * @param  array<string,mixed>  $workOrder
     * @param  array<string,mixed>  $data
     * @return array<string,mixed>
     */
    protected function formatPropertyInfo(array $workOrder, array $data): array
    {

        if (empty($data['property_details']) && empty($data['unit_details'])) {
            return [];
        }

        $propertyName = ! empty($data['property_details']['Name']) ? $data['property_details']['Name'] . ', ' : '';
        $unitNumber = ! empty($data['property_details']['Address2']) ? $data['property_details']['Address2'] : null;
        $unitName = ! empty($data['property_details']['Name']) ? $data['property_details']['Name'] : null;
        $street = ! empty($data['property_details']['Address1']) ? $data['property_details']['Address1'] : null;
        $city = ! empty($data['property_details']['City']) ? $data['property_details']['City'] : null;
        $stateAbbreviation = ! empty($data['property_details']['State']) ? $data['property_details']['State'] : null;
        $zipCode = ! empty($data['property_details']['Zip']) ? $data['property_details']['Zip'] : null;

        //Consider priority for unit address
        if (! empty($data['unit_details'])) {
            $propertyName = ! empty($data['unit_details']['Name']) ? $data['unit_details']['Name'] . ', ' : $propertyName;
            $unitNumber = ! empty($data['unit_details']['Address2']) ? $data['unit_details']['Address2'] : $unitNumber;
            $unitName = ! empty($data['unit_details']['Name']) ? $data['unit_details']['Name'] : $unitName;
            $street = ! empty($data['unit_details']['Address1']) ? $data['unit_details']['Address1'] : $street;
            $city = ! empty($data['unit_details']['City']) ? $data['unit_details']['City'] : $city;
            $stateAbbreviation = ! empty($data['unit_details']['State']) ? $data['unit_details']['State'] : $stateAbbreviation;
            $zipCode = ! empty($data['unit_details']['Zip']) ? $data['unit_details']['Zip'] : $zipCode;
        }

        $fullAddress = "{$street}, {$city}, {$stateAbbreviation} {$zipCode}, US";
        /** @var Geocode $googleMapGeocodeClient */
        $googleMapGeocodeClient = GoogleApiServiceFactory::create(GoogleService::GEOCODE);
        $locationResponse = $googleMapGeocodeClient->getGeocodeFromAddress("{$fullAddress}");

        return [
            'property_name' => null,
            'full_address' => $locationResponse->get('address'),
            'street_address' => $street,
            'unit_number' => $unitNumber ?? $unitName,
            'bed_unit' => null,
            'city' => $city,
            'postal_zip_code' => $zipCode,
            'state' => $stateAbbreviation,
            'country' => 'US',
            'state_id' => $stateAbbreviation,
            'country_id' => 'US',
            'latitude' => $locationResponse->get('latitude'),
            'longitude' => $locationResponse->get('longitude'),
            'google_place_id' => $locationResponse->get('place_id'),
            'google_geocode_response' => $locationResponse->all(),
            'navigation_address' => $fullAddress,
            'access_info' => [
                'method' => 'adult-will-let-you-in',
                'code' => '',
                'note' => $workOrder['VendorInstructions'] ?? null,
            ],
        ];
    }

    /**
     * @param  array<string,mixed>  $data
     * @return array<int,mixed>
     */
    protected function formatTenantData(array $data): array
    {
        $tenants = [];

        if (! empty($data['tenant_details']) && $data['tenant_details'] instanceof Collection && $data['tenant_details']->isNotEmpty()) {
            foreach ($data['tenant_details']->sortBy('PrimaryTenant') as $tenantDetails) {
                $tenants[] = $this->formatTenantInput($tenantDetails);
            }
        }

        return $tenants;
    }

    /**
     * @param  array<string,mixed>  $scrappedData
     * @return array<int,array<string,string>>
     */
    protected function formatPhotos(array $scrappedData)
    {
        $photos = [];

        if (! empty($scrappedData['attachments'])) {
            foreach ($scrappedData['attachments'] as $attachmentUri) {
                $photos[]['uri'] = $attachmentUri;
            }
        }

        return $photos;
    }

    /**
     * Format tenant input
     *
     * @param  array<string,mixed>  $tenantDetails
     * @return array<string,mixed>
     */
    protected function formatTenantInput(?array $tenantDetails): array
    {
        return [
            'first_name' => ! empty($tenantDetails['FirstName']) ? trim($tenantDetails['FirstName']) : 'No Tenant',
            'last_name' => ! empty($tenantDetails['LastName']) ? trim($tenantDetails['LastName']) : null,
            'email' => $tenantDetails['Email'] ?? null,
            'phone_number' => (string) preg_replace('/[^0-9]/', '', ($tenantDetails['PhoneNumber'] ?? null)),
            'is_primary_resident' => true,
        ];
    }

    protected function addUnAssignedWorkOrders(string $api = 'work_orders', bool $includeParameters = true): void
    {
        if ($includeParameters) {
            $params['filters']['LastUpdatedAtFrom'] = Carbon::now()->subYears(2)->toDateTimeString();
            $params['filters']['Status'] = 'New';

            // This field is using for filter work orders that are created from the integrated date.
            if (! empty($this->organization->appfolio_integrated_at)) {
                $params['filters']['CreatedAtTo'] = $this->organization->appfolio_integrated_at;
            } else {
                $params['filters']['CreatedAtTo'] = Carbon::now()->toDateTimeString();
            }
        } else {
            $params = [];
            $parsedUrl = parse_url($api);

            if (! empty($parsedUrl['query'])) {
                parse_str($parsedUrl['query'], $params);
                $api = 'work_orders';
            }
        }

        $organizationWorkOrders = $this->getPaginatedWorkOrders($api, $params);

        if (! empty($organizationWorkOrders['data'])) {
            $this->addAppfolioIntegrationSkippedWorkOrders($organizationWorkOrders['data']);
        }

        if (! empty($organizationWorkOrders['next_page_path'])) {
            $this->addUnAssignedWorkOrders($organizationWorkOrders['next_page_path'], false);
        }
    }

    /**
     * Add existing work order data in skipped logs
     */
    protected function addExistingWorkOrders(string $api = 'work_orders', bool $includeParameters = true): void
    {
        if ($includeParameters) {
            $params['filters']['LastUpdatedAtFrom'] = Carbon::now()->subYears(2)->toDateTimeString();
            $params['filters']['VendorId'] = $this->appfolioVendorId;

            // This field is using for filter work orders that are created from the integrated date.
            if (! empty($this->organization->appfolio_integrated_at)) {
                $params['filters']['CreatedAtTo'] = $this->organization->appfolio_integrated_at;
            } else {
                $params['filters']['CreatedAtTo'] = Carbon::now()->toDateTimeString();
            }
        } else {
            $params = [];
            $parsedUrl = parse_url($api);

            if (! empty($parsedUrl['query'])) {
                parse_str($parsedUrl['query'], $params);
                $api = 'work_orders';
            }
        }

        $organizationWorkOrder = $this->getPaginatedWorkOrders($api, $params);

        if (! empty($organizationWorkOrder['data'])) {
            $this->addAppfolioIntegrationSkippedWorkOrders($organizationWorkOrder['data']);
        }

        if (! empty($organizationWorkOrder['next_page_path'])) {
            $this->addExistingWorkOrders($organizationWorkOrder['next_page_path'], false);
        }
    }

    /**
     * @param  array<int,array<string,mixed>>  $data
     */
    protected function addAppfolioIntegrationSkippedWorkOrders($data): void
    {
        collect($data)->each(function ($workOrder) {
            AppfolioIntegrationSkippedWorkOrder::updateOrCreate([
                'appfolio_work_order_uuid' => $workOrder['Id'],
                'organization_id' => $this->organization->organization_id,
            ], [
                'organization_id' => $this->organization->organization_id,
                'appfolio_work_order_uuid' => $workOrder['Id'],
                'display_number' => $workOrder['WorkOrderNumber'],
                'work_order_id' => null,
                'vendor_id' => $workOrder['VendorId'],
                'work_order_status' => $workOrder['Status'],
                'work_order_created_at' => Carbon::parse($workOrder['CreatedAt']),
            ]);
        });
    }
}

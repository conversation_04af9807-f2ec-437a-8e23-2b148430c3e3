<?php

namespace App\Services\Appfolio;

use App\Enums\Priority;
use App\Enums\ServiceRequestTypeTypes;
use App\Exceptions\Appfolio\AppfolioApiException;
use App\Exceptions\Appfolio\AppfolioApiResponseException;
use App\Helpers\Helper;
use App\Jobs\Appfolio\ProcessAppfolioNewWorkOrderToServiceRequest;
use App\Jobs\Appfolio\ServiceRequestStateUpdateJob;
use App\Models\AppfolioApiLog;
use App\Models\AppfolioIntegrationSkippedServiceRequest;
use App\Models\Organization;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestSource;
use App\Models\Timezone;
use App\Models\User;
use App\Packages\GoogleMap\Enum\GoogleService;
use App\Packages\GoogleMap\GoogleApiServiceFactory;
use App\Packages\GoogleMap\Services\Geocode;
use App\Services\ServiceRequestRegister\ServiceRequestRegisterClient;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class AppfolioForServiceRequestService
{
    /**
     * Foresight Organization
     *
     * @var \App\Models\Organization
     */
    public $organization;

    /**
     * Appfolio partner id
     *
     * @var string|null
     */
    protected $appfolioDeveloperId;

    /**
     * Appfolio client Id
     *
     * @var string|null
     */
    protected $appfolioClientId;

    /**
     * Appfolio secret key
     *
     * @var string|null
     */
    protected $appfolioClientSecret;

    /**
     * Appfolio vendor id
     *
     * @var string|null
     */
    protected $appfolioVendorId;

    /**
     * Appfolio API URL
     *
     * @var string|null
     */
    protected $appfolioApiBaseUrl;

    /**
     * Appfolio API Last updated at
     *
     * @var string|null
     */
    protected $lastUpdatedAtFrom;

    /**
     * Create a new Appfolio service instance.
     *
     * @return void
     */
    public function __construct(Organization $organization)
    {
        $this->organization = $organization;
        $this->lastUpdatedAtFrom = config('appfolio.last_updated_at_from');
        $this->appfolioApiBaseUrl = config('appfolio.api_base_url');
        $this->appfolioVendorId = $organization->appfolio_vendor_id;
        $this->appfolioClientId = $organization->appfolio_client_id;
        $this->appfolioClientSecret = $organization->appfolio_client_secret;
        $this->appfolioDeveloperId = config('appfolio.appfolio_developer_id');
    }

    /**
     * @param  array<string,mixed>  $params
     * @return Collection<int,array<string,mixed>>
     */
    public function get(string $api, array $params = [], ?string $workOrderId = null, ?bool $skipData = false): Collection
    {
        if (empty($params['filters']['Id']) && empty($params['filters']['LastUpdatedAtFrom'])) {
            $lastUpdatedAt = Carbon::now()->subMinutes(config('appfolio.sync_interval'))->toDateTimeString();
            $params['filters']['LastUpdatedAtFrom'] = config('appfolio.last_updated_at_from') ?? $lastUpdatedAt;
        }

        $response = $this->connect()->get($this->appfolioApiBaseUrl . $api, $params);

        Log::channel('appfolio')->info('get() response', [
            'api' => $api,
            'params' => $params,
            'workOrderId' => $workOrderId,
        ]);

        $this->logApiResponse($api, $response, $params, 'GET', $workOrderId);

        return $this->responseFormatter($response, $skipData);
    }

    /**
     * @param  array<string,mixed>  $body
     * @return Collection<int,array<string,mixed>>
     */
    public function post(string $api, array $body = [], ?string $workOrderId = null): Collection
    {
        $response = $this->connect()->post($this->appfolioApiBaseUrl . $api, $body);

        Log::channel('appfolio')->info('post() response', [
            'response' => $response->json() ?? $response->body(),
            'api' => $api,
            'body' => $body,
            'workOrderId' => $workOrderId,
        ]);

        $this->logApiResponse($api, $response, $body, 'POST', $workOrderId);

        return $this->responseFormatter($response);
    }

    /**
     * @param  array<string,mixed>  $body
     * @return Collection<int,array<string,mixed>>
     */
    public function patch(string $api, array $body = [], ?string $workOrderId = null): Collection
    {
        $response = $this->connect()->patch($this->appfolioApiBaseUrl . $api, $body);

        Log::channel('appfolio')->info('patch() response', [
            'response' => $response->json() ?? $response->body(),
            'api' => $api,
            'body' => $body,
            'workOrderId' => $workOrderId,
        ]);

        $this->logApiResponse($api, $response, $body, 'PATCH', $workOrderId);

        return $this->responseFormatter($response);
    }

    /**
     * @return Collection<int,array<string,mixed>>
     */
    public function postAttachment(string $api, string $path, ?string $fileName, ?string $workOrderId = null): Collection
    {
        if (! Storage::exists($path)) {
            throw new AppfolioApiException(__('File not found.'));
        }
        $response = $this->connect()->attach('File', File::get($path), $fileName)
            ->post($this->appfolioApiBaseUrl . $api);

        Log::channel('appfolio')->info('postAttachment response', [
            'response' => $response,
            '$path' => $path,
            '$fileName' => $fileName,
            '$api' => $api,
        ]);

        $this->logApiResponse($api, $response, [$fileName], 'POST', $workOrderId);

        return $this->responseFormatter($response);
    }

    /** GET APPFOLIO DETAILS API */

    /**
     * @return array<string,mixed>|null
     */
    public function propertyDetails(string $propertyId, ?string $workOrderId = null): ?array
    {
        try {
            $params = [];
            $params['filters']['Id'] = $propertyId;
            $params['filters']['IncludeHidden'] = 'true';

            return $this->get('properties', $params, $workOrderId)->first();
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Log::channel('appfolio')->error('propertyDetails Exception!', [
                'organization' => $this->organization->name,
                'propertyId' => $propertyId,
                'workOrderId' => $workOrderId,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }

        return null;
    }

    /**
     * @return array<string,mixed>|null
     */
    public function unitDetails(string $unitId, ?string $workOrderId = null): ?array
    {
        try {
            $params = [];
            $params['filters']['Id'] = $unitId;
            $params['filters']['IncludeHidden'] = 'true';

            return $this->get('units', $params, $workOrderId)->first();
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Log::channel('appfolio')->error('unitDetails Exception!', [
                'organization' => $this->organization->name,
                'unitId' => $unitId,
                'workOrderId' => $workOrderId,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }

        return null;
    }

    /**
     * @param  array<string,mixed>  $workOrder
     * @return Collection<int,array<string,mixed>>
     */
    public function getTenants(array $workOrder): Collection
    {
        try {
            $params['filters']['LastUpdatedAtFrom'] = config('appfolio.last_updated_at_from');
            $params['filters']['Status'] = 'Current,Notice';

            if (! empty($workOrder['UnitId'])) {
                $params['filters']['UnitId'] = $workOrder['UnitId'];
            }

            if (! empty($workOrder['PropertyId'])) {
                $params['filters']['PropertyId'] = $workOrder['PropertyId'];
            }

            return $this->get('tenants', $params, $workOrder['Id']);
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Log::channel('appfolio')->error('getTenants Exception!', [
                'organization' => $this->organization->name,
                'workOrder' => $workOrder,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }

        return collect([]);
    }

    /**
     * @return Collection<int,array<string,mixed>>
     */
    public function tenantDetails(string $tenantId, ?string $workOrderId = null): Collection
    {
        try {
            $params = [];
            $params['filters']['Id'] = $tenantId;
            $params['filters']['IncludeHidden'] = 'true';

            return $this->get('tenants', $params, $workOrderId);
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Log::channel('appfolio')->error('tenantDetails Exception!', [
                'organization' => $this->organization->name,
                'tenantId' => $tenantId,
                'workOrderId' => $workOrderId,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }

        return collect([]);
    }

    /** INGEST NEW WORK ORDERS */
    public function ingestNewWorkOrdersToServiceRequests(): void
    {
        Log::channel('appfolio')->info('Started to Sync Appfolio Work Orders to Service Request of ' . ($this->organization->name));

        // Get work orders from appfolio
        $appfolioOrganizationWorkOrders = $this->getAppfolioWorkOrders();

        // Filter work orders with status Assigned, Scheduled and Estimate Requested
        $appfolioNewWorkOrders = $appfolioOrganizationWorkOrders->where('VendorId', $this->appfolioVendorId)
            ->whereIn('Status', ['Assigned', 'Scheduled', 'Estimate Requested']);

        // Filter work orders with status Cancelled
        $appfolioCanceledWorkOrders = $appfolioOrganizationWorkOrders->where('VendorId', $this->appfolioVendorId)
            ->whereIn('Status', values: ['Canceled']);

        // Set service request to closed if appfolio work order is cancelled
        if ($appfolioCanceledWorkOrders->isNotEmpty()) {
            // Get closed service request from foresight
            $foresightCanceledServiceRequests = ServiceRequest::join('service_request_sources', 'service_request_sources.service_request_source_id', '=', 'service_requests.service_request_source_id')
                ->select('service_requests.*')
                ->where('service_request_sources.slug', 'appfolio')
                ->whereIn('service_requests.service_request_reference_id', $appfolioCanceledWorkOrders->pluck('Id'))
                ->where('service_requests.organization_id', $this->organization->organization_id)
                ->where('service_requests.state', '!=', 'closed')
                ->get();

            if ($foresightCanceledServiceRequests->isNotEmpty()) {
                $foresightCanceledServiceRequests->each(function ($canceledAppfolioExistingServiceRequest) use ($appfolioCanceledWorkOrders) {
                    /** @var ServiceRequest $canceledAppfolioExistingServiceRequest */
                    $appfolioWorkOrderReference = $appfolioCanceledWorkOrders->where('Id', $canceledAppfolioExistingServiceRequest->service_request_reference_id)->first();

                    Log::channel('appfolio')->info('Before processing ServiceRequestStateUpdateJob', [
                        'appfolioWorkOrderReference' => $appfolioWorkOrderReference,
                        'service_request_id' => $canceledAppfolioExistingServiceRequest->service_request_id,
                    ]);

                    if (! empty($appfolioWorkOrderReference)) {
                        dispatch(new ServiceRequestStateUpdateJob($canceledAppfolioExistingServiceRequest, $appfolioWorkOrderReference));
                    }
                });
            }
        }

        Log::channel('appfolio')->info("Fetched Work Orders for {$this->organization->name}", [
            'Uuids' => $appfolioNewWorkOrders->pluck('Id')->all(),
            'WorkOrderNumbers' => $appfolioNewWorkOrders->pluck('WorkOrderNumber')->all(),
        ]);

        // Get service requests references that is already created in foresight filtered by new work orders to be created
        $foresightExistingServiceRequestUuids = ServiceRequest::join('service_request_sources', 'service_request_sources.service_request_source_id', '=', 'service_requests.service_request_source_id')
            ->where('service_request_sources.slug', 'appfolio')
            ->whereIn('service_requests.service_request_reference_id', $appfolioNewWorkOrders->pluck('Id'))
            ->where('service_requests.organization_id', $this->organization->organization_id)
            ->pluck('service_requests.service_request_reference_id')
            ->all();

        // Filter new work orders that is not currently existing in foresight
        $appfolioNewWorkOrders = $appfolioNewWorkOrders->whereNotIn('Id', $foresightExistingServiceRequestUuids)->unique('Id');

        // Skip existing work orders those are created before integration
        // these work orders is created during the initial integration with lula
        // look for AppfolioIntegrationPreAction at Kernel.php
        $foresightSkippedExistingServiceRequests = AppfolioIntegrationSkippedServiceRequest::join('service_requests', function ($query) {
            $query->on('service_requests.service_request_reference_id', '=', 'appfolio_integration_skipped_service_requests.appfolio_work_order_uuid');
        })
            ->join('service_request_sources', 'service_request_sources.service_request_source_id', '=', 'service_requests.service_request_source_id')
            ->where('service_request_sources.slug', 'appfolio')
            ->where('appfolio_integration_skipped_service_requests.organization_id', $this->organization->organization_id)
            ->whereIn('appfolio_integration_skipped_service_requests.appfolio_work_order_uuid', $appfolioNewWorkOrders->pluck('Id'))
            ->whereNotIn('appfolio_integration_skipped_service_requests.appfolio_work_order_status', ['New'])
            ->where('appfolio_integration_skipped_service_requests.appfolio_vendor_id', $this->appfolioVendorId)
            ->get();

        // Removed from new work orders all already existing work order
        if ($foresightSkippedExistingServiceRequests->isNotEmpty()) {
            $skipExistingWorkOrderIds = $foresightSkippedExistingServiceRequests->whereNotIn('appfolio_work_order_status', ['Assigned', 'Scheduled', 'Estimate Requested'])
                ->pluck('appfolio_work_order_uuid')
                ->all();

            Log::channel('appfolio')->info("Skipped Work Orders for {$this->organization->name}", [
                'Uuids' => $skipExistingWorkOrderIds,
            ]);
            $appfolioNewWorkOrders = $appfolioNewWorkOrders->whereNotIn('Id', $skipExistingWorkOrderIds)->unique('Id');
        }

        Log::channel('appfolio')->info('Start processNewWorkOrders');

        $this->processNewWorkOrders($appfolioNewWorkOrders, $foresightSkippedExistingServiceRequests);

        Log::channel('appfolio')->info('Completed Work Order Sync of ' . ($this->organization->name));
    }

    /**
     * Process new work order
     *
     * @param  array<string,mixed>  $appfolioWorkOrder
     * @param  array<string,mixed>  $additionalData
     */
    public function processNewWorkOrder(array $appfolioWorkOrder, ?array $additionalData): void
    {
        try {
            // appfolioWorkOrder - original data from appfolio work orders
            // additionalData - additional data from appfolio properties, units, tenants
            $data = [];
            Log::channel('appfolio')->info('processNewWOrkOrder started for ' . ($this->organization->name));

            // Save appfolio logs to DB - appfolio_api_logs table
            $this->addAppfolioApiWorkOrderLog($appfolioWorkOrder);

            // Get property details if property does not exist
            if (! empty($additionalData['property'])) {
                Log::channel('appfolio')->info('Property details from initial data', $additionalData['property']);
                $data['property_details'] = $additionalData['property'];
            } elseif (! empty($appfolioWorkOrder['PropertyId'])) {
                $data['property_details'] = $this->propertyDetails($appfolioWorkOrder['PropertyId'], $appfolioWorkOrder['Id']);
            }

            // Get unit details if unit does not exist
            if (! empty($additionalData['unit'])) {
                Log::channel('appfolio')->info('Unit details from initial data', $additionalData['unit']);
                $data['unit_details'] = $additionalData['unit'];
            } elseif (! empty($appfolioWorkOrder['UnitId'])) {
                $data['unit_details'] = $this->unitDetails($appfolioWorkOrder['UnitId'], $appfolioWorkOrder['Id']);
            }

            // Get tenant details if tenant does not exist
            if (! empty($additionalData['tenant'])) {
                Log::channel('appfolio')->info('Tenant details from initial data', $additionalData['tenant']);
                $data['tenant_details'][] = $additionalData['tenant'];
            } elseif (! empty($appfolioWorkOrder['RequestingTenantId'])) {
                $data['tenant_details'] = $this->tenantDetails($appfolioWorkOrder['RequestingTenantId'], $appfolioWorkOrder['Id']);
            } else {
                $data['tenant_details'] = $this->getTenants($appfolioWorkOrder);
            }

            // THIS IS NOT WORKING
            $scrapperData = $this->fetchAdditionalDataFromScrapper($appfolioWorkOrder);

            // Format service request payload
            $payload = $this->formatServiceRequestPayload($appfolioWorkOrder, $data, $scrapperData);

            if (! empty($payload['property'])) {
                $foresightExistingServiceRequests = ServiceRequest::join('service_request_sources', 'service_request_sources.service_request_source_id', '=', 'service_requests.service_request_source_id')
                    ->where('service_request_sources.slug', 'appfolio')
                    ->where('service_requests.organization_id', $this->organization->organization_id)
                    ->where('service_requests.service_request_reference_id', $appfolioWorkOrder['Id'])
                    ->whereNotNull('service_requests.service_request_reference_id')
                    ->count();

                if ($foresightExistingServiceRequests > 0) {
                    Log::channel('appfolio')->info("{$this->organization->name}'s Appfoliowork order is already created in Foresight", [
                        'payload' => $payload,
                    ]);

                    exit;
                }

                // CREATE SERVICE REQUEST on foresight using the class
                // app/Services/WorkOrderRegister/WorkOrderRegisterClient.php
                $createdForesightServiceRequest = $this->createNewServiceRequest($payload);

                Log::channel('appfolio')->info('Requested service from appfolio', [
                    'servicveRequest' => $createdForesightServiceRequest,
                    'payload' => $payload,
                ]);

            } else {
                $supportNotificationData['work_order_no'] = $appfolioWorkOrder['WorkOrderNumber'] ?? '-';
                $supportNotificationData['company_name'] = $this->organization->name;
                $supportNotificationData['reason'] = 'Property information not found in appfolio API';

                Log::channel('appfolio')->info("{$this->organization->name}'s  Appfolio job failed due to invalid property_details", $supportNotificationData);

                $uniqueId = $appfolioWorkOrder['Id'] . '-invalid-property-address';

                if (! Cache::has($uniqueId)) {
                    $ttl = config('appfolio.sync_interval') * 60;
                    Cache::remember($uniqueId, $ttl, function () {
                        return 1;
                    });
                    // TO DO send notification
                }
            }
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Helper::exceptionLog($e, $appfolioWorkOrder, "{$this->organization->name}'s ScrapeAppfolioNewWorkOrder Exception.");
            Log::channel('appfolio')->error("{$this->organization->name}'s ScrapeAppfolioNewWorkOrder Exception.", [
                'work_order' => $appfolioWorkOrder,
                'organization' => $this->organization,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }
    }

    /**
     * @param  array<string,mixed>  $appfolioWorkOrder
     * @return array<string,mixed>
     */
    public function fetchAdditionalDataFromScrapper(array $appfolioWorkOrder): array
    {
        try {
            Log::channel('appfolio')->info("{$this->organization->name}'s Started fetchAdditionalDataFromScrapper");

            $scrapperApiUrl = config('appfolio.scraper_api_url');

            if (empty($this->organization->appfolio_customer_id) || empty($appfolioWorkOrder['WorkOrderNumber'])) {
                return [];
            }

            $response = Http::withHeaders([
                'accept' => 'application/json',
                'content-type' => 'application/json',
            ])->get($scrapperApiUrl, [
                'appfolio_customer_id' => $this->organization->appfolio_customer_id,
                'numberForDisplay' => $appfolioWorkOrder['WorkOrderNumber'],
            ]);

            Log::channel('appfolio')->info("{$this->organization->name}'s fetchAdditionalDataFromScrapper Log!!", [
                'scrapper_api_url' => $scrapperApiUrl,
                'appfolio_customer_id' => $this->organization->appfolio_customer_id,
                'numberForDisplay' => $appfolioWorkOrder['WorkOrderNumber'],
                'status' => $response->status(),
                'json' => $response->json(),
            ]);

            if ($response->successful()) {
                return $response->json('result');
            }

            $data = [
                'work_order' => $appfolioWorkOrder,
                'partner_id' => $this->organization->organization_id,
                'label' => $this->organization->name,
                'response' => $response->json() ?? $response->body(),
                'status' => $response->status(),
                'scrapper_api_url' => $scrapperApiUrl,
            ];

            Log::channel('appfolio')->info("{$this->organization->name}'s Scrapper API for additional data has failed!!", $data);
        } catch (Exception $e) {
            Log::channel('appfolio')->error("Scrapper API Exception! - {$this->organization->name}", [
                'organization' => $this->organization->name,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
            Helper::exceptionLog($e, $appfolioWorkOrder, "{$this->organization->name}'s Scrapper API Exception!", true);
        }

        return [];
    }

    /**
     * Format work order data for creating new work orders in Foresight
     *
     * @param  array<string,mixed>  $appfolioWorkOrder
     * @param  array<string,mixed>  $data
     * @param  array<string,mixed>  $scrappedData
     * @return array<string,mixed>
     */
    public function formatServiceRequestPayload(array $appfolioWorkOrder, array $data, array $scrappedData = []): array
    {
        $serviceRequestSource = Cache::remember('appfolio_service_request_source', config('cache.system_variable_ttl'), function () {
            return ServiceRequestSource::where('slug', 'appfolio')->first();
        });

        $priority = $appfolioWorkOrder['Priority'] ?? 'low';

        switch ($priority) {
            case 'Urgent':
                $priority = Priority::URGENT();
                break;
            case 'Normal':
                $priority = Priority::MEDIUM();
                break;
            case 'Low':
                $priority = Priority::LOW();
                break;
            default:
                $priority = Priority::MEDIUM();
                break;
        }

        $payload = [
            'request_type' => ServiceRequestTypeTypes::MAINTENANCE_REQUEST(),
            'priority' => $priority,
            'description' => $appfolioWorkOrder['JobDescription'] ?? null,
            'property' => $this->formatPropertyInfo($appfolioWorkOrder, $data),
            'residents' => $this->formatTenantData($data), // Resident = tenant
            'timezone' => 'America/Chicago',
            'categories' => [],
            'photos' => $this->formatPhotos($scrappedData),
            // Organization
            'organization_uuid' => $this->organization->organization_uuid ?? null,
            // Appfolio reference
            'service_request_source_id' => $serviceRequestSource->service_request_source_id ?? null,
            'service_request_reference_id' => $appfolioWorkOrder['Id'] ?? null,
            'service_request_reference_number' => $appfolioWorkOrder['WorkOrderNumber'] ?? null,
            'service_request_reference_url' => $scrappedData['work_order_reference_url'] ?? null,
            'service_request_reference_created_at' => $appfolioWorkOrder['CreatedAt'] ?? null,
            // Other information
            'nte_amount' => $scrappedData['maintenance_limit'] ?? null,
            'special_instructions' => $scrappedData['specialInstructions'] ?? null,
            'vendor_instructions' => $appfolioWorkOrder['VendorInstructions'] ?? null,
        ];

        Log::channel('appfolio')->info("{$this->organization->name}'s  New work order payload:", $payload);

        return $payload;
    }

    public function addSkippedWorkOrders(): void
    {
        try {
            $this->addUnAssignedWorkOrders();
            $this->addExistingWorkOrders();
        } catch (Exception $e) {
            Log::channel('appfolio')->error('Appfolio exception addSkippedWorkOrders()', [
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
            Helper::exceptionLog($e, ['organization' => $this->organization->domain], 'addSkippedWorkOrders exception!');
        }
    }

    /**
     * @param  array<mixed, mixed>  $params
     * @return Collection<int, array<string, mixed>|mixed>
     */
    public function getPaginatedWorkOrders(string $api, array $params = []): Collection
    {
        try {
            if (empty($params['filters']['Id']) && empty($params['filters']['LastUpdatedAtFrom'])) {
                $lastUpdatedAt = Carbon::now()->subMinutes(config('appfolio.sync_interval'))->toDateTimeString();
                $params['filters']['LastUpdatedAtFrom'] = config('appfolio.last_updated_at_from') ?? $lastUpdatedAt;
            }

            $response = $this->connect()->get($this->appfolioApiBaseUrl . $api, $params);

            $this->logApiResponse($api, $response, $params, 'GET');

            return $this->responseFormatter($response, true);
        } catch (Exception $e) {
            Log::channel('appfolio')->error("{$this->organization->name}'s getPaginatedWorkOrders() exception", [
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
            Helper::exceptionLog($e, ['api' => $api, 'params' => $params], 'Appfolio exception getPaginatedWorkOrders()');
        }

        return collect([]);
    }

    /**
     * Update Work Order Details.
     *
     * @param  array<string,mixed>  $payload
     * @return Collection<int, array<string, mixed>|mixed>
     */
    public function updateWorkOrder(string $workOrderId, array $payload)
    {
        return $this->patch("work_orders/{$workOrderId}", $payload);
    }

    /**
     * Fetch appfolio lula vendor
     *
     * @param  array<string,mixed>  $params
     * @return Collection<int, array<string, mixed>|mixed>
     */
    public function getVendors(array $params, ?bool $includeData = true): Collection
    {
        try {
            $this->checkIntegration();

            if (empty($params['filters']['LastUpdatedAtFrom'])) {
                // Setting a default date to fetch last updated at
                $params['filters']['LastUpdatedAtFrom'] = $this->lastUpdatedAtFrom;
            }

            return $this->get('vendors', $params, null, $includeData);
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Helper::exceptionLog($e, $params, 'Exception in fetchAppfolioVendors');
            Log::debug('fetchAppfolioVendors Exception', [
                'error' => $e->getMessage(),
                'input' => $params,
            ]);

            throw $e;
        }
    }

    /** PROCESS NEW WORK ORDERS */

    /**
     * @param  Collection<int,array<string,mixed>>  $appfolioNewWorkOrders
     * @param  Collection<int,AppfolioIntegrationSkippedServiceRequest>  $foresightSkippedExistingServiceRequests
     */
    protected function processNewWorkOrders(Collection $appfolioNewWorkOrders, Collection $foresightSkippedExistingServiceRequests): void
    {
        Log::channel('appfolio')->info("{$this->organization->name}'s Started processNewWorkOrders " . ($this->organization->name), [
            'WorkOrderNumbers' => $appfolioNewWorkOrders->pluck('WorkOrderNumber')->all(),
            'WorkOrderUuids' => $appfolioNewWorkOrders->pluck('Id')->all(),
            'SkippedWorkOrderNumbers' => $foresightSkippedExistingServiceRequests->pluck('WorkOrderNumber')->all(),
            'SkippedWorkOrderUuids' => $foresightSkippedExistingServiceRequests->pluck('Id')->all(),
        ]);

        // Get ids of propertyId, unitId and tenant Id
        // Formated on this example: 01346625-18f9-11ed-acc0-021d20f8a929,87eb23d0-4bff-11ed-bc6e-06927b267943
        $propertyUuids = $appfolioNewWorkOrders->pluck('PropertyId')->filter()->implode(',');
        $unitUuids = $appfolioNewWorkOrders->pluck('UnitId')->filter()->implode(',');
        $tenantUuids = $appfolioNewWorkOrders->pluck('RequestingTenantId')->filter()->implode(',');

        // Get the details from appfolio api filtered by the ids above
        $properties = $this->getEntityListByUuid($propertyUuids, 'properties');
        $units = $this->getEntityListByUuid($unitUuids, 'units');
        $tenants = $this->getEntityListByUuid($tenantUuids, 'tenants');

        foreach ($appfolioNewWorkOrders as $appfolioNewWorkOrder) {
            $data = [];
            $developerNotification = [];
            try {
                Log::channel('appfolio')->info("{$this->organization->name}'s processNewWorkOrders started", $appfolioNewWorkOrder);
                // Send notification if the work order is in skipped work order list
                if (
                    $foresightSkippedExistingServiceRequests->isNotEmpty() &&
                    ! empty($this->organization->appfolio_integrated_at) &&
                    $foresightSkippedExistingServiceRequests->where('appfolio_work_order_uuid', $appfolioNewWorkOrder['Id'])->isNotEmpty()
                ) {
                    // Get current foresight skipped service request
                    $foresightSkippedServiceRequest = $foresightSkippedExistingServiceRequests->where('appfolio_work_order_uuid', $appfolioNewWorkOrder['Id'])->first();

                    if (! empty($foresightSkippedServiceRequest) && empty($foresightSkippedServiceRequest->notified_at)) {
                        $foresightSkippedServiceRequest->notified_at = Carbon::now();
                        $foresightSkippedServiceRequest->save();

                        Log::channel('appfolio')->warning("{$this->organization->name}'s Appfolio Work Order skipped !!.", [
                            'organization' => $this->organization->name,
                            'WorkOrderNumber' => $appfolioNewWorkOrder['WorkOrderNumber'],
                            'WO Details' => $appfolioNewWorkOrder,
                        ]);
                    } else {
                        Log::channel('appfolio')->warning("{$this->organization->name}'s Skipped already notified Appfolio Work Order !!.", [
                            'organization' => $this->organization->name,
                            'WorkOrderNumber' => $appfolioNewWorkOrder['WorkOrderNumber'],
                            'WO Details' => $appfolioNewWorkOrder,
                        ]);
                    }

                    continue;
                }

                if ($properties->isNotEmpty() && ! empty($appfolioNewWorkOrder['PropertyId'])) {
                    $data['property'] = $properties->where('Id', $appfolioNewWorkOrder['PropertyId'])->first();
                    $developerNotification['property'] = $data['property'];
                }

                if ($units->isNotEmpty() && ! empty($appfolioNewWorkOrder['UnitId'])) {
                    $data['unit'] = $units->where('Id', $appfolioNewWorkOrder['UnitId'])->first();
                    $developerNotification['unit'] = $data['unit'];
                }

                if ($tenants->isNotEmpty() && ! empty($appfolioNewWorkOrder['RequestingTenantId'])) {
                    $data['tenants'] = $tenants->where('Id', $appfolioNewWorkOrder['RequestingTenantId'])->first();
                    $developerNotification['tenants'] = $data['tenants'];
                }
            } catch (Exception $e) {
                $developerNotification['errors'] = $e->getMessage();
                $developerNotification['new_work_order'] = $appfolioNewWorkOrder;
                Log::channel('appfolio')->error("{$this->organization->name}'s Appfolio API initial data mapping failed - Normal !!", [
                    'organization' => $this->organization->organization_id,
                    'exception' => $e->getMessage(),
                    'code' => $e->getCode(),
                    'line' => $e->getLine(),
                    'file' => $e->getFile(),
                    'data' => $developerNotification,
                ]);

                //TO DO - Developer notification
            }

            Log::channel('appfolio')->info("{$this->organization->name}'s processNewWorkOrders started!!.", [
                'organization' => $this->organization->name,
                'WorkOrder' => $appfolioNewWorkOrder,
            ]);

            // $this->processNewWorkOrder($appfolioNewWorkOrder, $data);
            dispatch(new ProcessAppfolioNewWorkOrderToServiceRequest($this->organization, $appfolioNewWorkOrder, $data))->delay(config('appfolio.redis.queue_delay'));
        }
    }

    /**
     * Check appfolio integration is active
     *
     * @return void
     */
    protected function checkIntegration()
    {
        if (empty($this->appfolioClientId) || empty($this->appfolioClientSecret)) {
            throw new AppfolioApiException(__('Appfolio integration is not enabled!'));
        }
    }

    /**
     * @param  array<string,string>  $headers
     */
    protected function connect(array $headers = []): PendingRequest
    {
        $encodedString = base64_encode("{$this->appfolioClientId}:{$this->appfolioClientSecret}");

        $headers = array_merge($headers, [
            'X-AppFolio-Developer-ID' => $this->appfolioDeveloperId,
            'Authorization' => "Basic {$encodedString}",
        ]);

        return Http::withHeaders($headers);
    }

    /**
     * @return Collection<int,array<string,mixed>>
     *
     * @throws AppfolioApiResponseException
     */
    protected function responseFormatter(Response $response, ?bool $skipData = false): Collection
    {
        if ($response->failed()) {
            Log::channel('appfolio')->info("{$this->organization->name}'s responseFormatter", ['response' => $response->body()]);

            if ($response->status() == 401) {
                throw new AppfolioApiResponseException(__('Failed to connect Appfolio, Either Appfolio integration is disabled or invalid client credentials!'));
            }

            throw new AppfolioApiResponseException($response->body());
        }

        if (! $skipData && ! empty($response->json()['data'])) {
            return new Collection($response->json()['data']);
        }

        return new Collection($response->json());
    }

    /**
     * @param  array<mixed>  $payload
     */
    protected function logApiResponse(string $api, Response $response, ?array $payload = [], string $method = 'GET', ?string $workOrderId = null): void
    {
        try {
            Log::channel('appfolio')->info("{$this->organization->name}'s logApiResponse", ['payload' => $payload, 'method' => $method, 'api' => $api]);

            $appfolioApiLog = new AppfolioApiLog;

            if ($api == 'work_orders') {
                $appfolioApiLog->appfolio_entity_type = 'WorkOrder';
            } elseif ($api == 'properties') {
                $appfolioApiLog->appfolio_entity_type = 'Property';
            } elseif ($api == 'units') {
                $appfolioApiLog->appfolio_entity_type = 'Unit';
            } elseif ($api == 'tenants') {
                $appfolioApiLog->appfolio_entity_type = 'Tenant';
            } elseif (str_contains($api, 'notes')) {
                $appfolioApiLog->appfolio_entity_type = 'Note';
            } elseif (str_contains($api, 'attachments')) {
                $appfolioApiLog->appfolio_entity_type = 'Attachment';
            }

            $appfolioApiLog->appfolio_work_order_uuid = $workOrderId;

            $appfolioApiLog->method = strtoupper($method);
            $appfolioApiLog->payload = $payload;
            $appfolioApiLog->api_url = $this->appfolioApiBaseUrl . $api;
            $appfolioApiLog->status_code = $response->status();
            $appfolioApiLog->response = $response->json() ?? $response->body();

            $notificationData = [
                'organization' => $this->organization->name,
                'response' => $response->json() ?? $response->body(),
                'statusCode' => $response->status(),
            ];

            if ($response->successful()) {
                if (
                    isset($response->json()['data']) && count($response->json()['data']) == 1 &&
                    ! empty($payload['filters']['Id']) && ! is_numeric(strpos($payload['filters']['Id'], ','))
                ) {
                    $entity = Arr::first($response->json()['data']);
                    $appfolioApiLog->appfolio_entity_uuid = $entity['Id'] ?? null;
                }
            }

            if (empty($appfolioApiLog->appfolio_entity_uuid) && ! empty($payload['filters']['Id']) && ! is_numeric(strpos($payload['filters']['Id'], ','))) {
                $appfolioApiLog->appfolio_entity_uuid = $payload['filters']['Id'];
            }

            if (! empty($payload['filters']['LastUpdatedAtFrom'])) {
                $appfolioApiLog->last_updated_at = $payload['filters']['LastUpdatedAtFrom'];
            }

            $appfolioApiLog->save();

            if (! $response->successful()) {
                $notificationData['organization_id'] = $this->organization->organization_id;
                $notificationData['domain'] = $this->organization->domain;
                $notificationData['payload'] = $payload;
                $notificationData['method'] = $method;
                $notificationData['api_url'] = $this->appfolioApiBaseUrl . $api;
                $notificationData['statusCode'] = $response->status();
                Log::channel('appfolio')->info("{$this->organization->name}'s Appfolio API integration logApiResponse failed!", $notificationData);

                //TO DO Send developer email
            }
        } catch (Exception $e) {
            Helper::exceptionLog(
                $e,
                ['api' => $api, 'response' => $response, 'payload' => $payload],
                'Appfolio exception in logApiResponse()'
            );
            Log::channel('appfolio')->error("{$this->organization->name}'s Appfolio exception logApiResponse()", [
                'exception' => $e->getMessage(),
                'api' => $api,
                'response' => $response,
                'payload' => $payload,
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }
    }

    /**
     * @param  array<string,mixed>  $params
     * @return Collection<int,array<string,mixed>>
     */
    protected function getAppfolioWorkOrders(array $params = []): Collection
    {
        //$params['filters']['LastUpdatedAtFrom'] = "2024-05-02";
        $params['filters']['LastUpdatedAtFrom'] = Carbon::now()->subMinutes(config('appfolio.sync_interval'))->toDateTimeString();
        $params['filters']['VendorId'] = $this->appfolioVendorId;

        // This field is using for filter work orders that are created from the integrated date.
        if (empty($params['filters']['CreatedAtFrom']) && ! empty($this->organization->appfolio_integrated_at)) {
            $params['filters']['CreatedAtFrom'] = $this->organization->appfolio_integrated_at->subMonths(2)->toDateTimeString();
        }

        return $this->get('work_orders', $params);
    }

    /**
     * @return Collection<int,array<string,mixed>>
     */
    protected function getEntityListByUuid(string $entityUuids, string $url): Collection
    {
        try {
            if (! empty($entityUuids)) {
                $params = [];
                $params['filters']['Id'] = $entityUuids;

                return $this->get($url, $params);
            }
        } catch (Exception|AppfolioApiException|AppfolioApiResponseException $e) {
            Log::channel('appfolio')->info("{$this->organization->name}'s Exception in getEntityListByUuid!!.", [
                'error' => $e->getMessage(),
                'entityUuids' => $entityUuids,
                'url' => $url,
            ]);
        }

        return collect([]);
    }

    /**
     * @param  array<string,mixed>  $payload
     *
     * @throws Exception
     */
    protected function createNewServiceRequest(array $payload): ?ServiceRequest
    {
        try {
            $request = app(Request::class);
            $request->initialize($payload);

            //TO DO - Need authorization, this is a temp fix
            $user = User::newModelInstance([
                'first_name' => 'Public',
                'last_name' => 'API',
                'organization_id' => $this->organization->organization_id,
                'user_type' => 'appfolio',
            ]);

            if (method_exists($user, 'setRelation')) {
                $user->setRelation('organization', $this->organization);
            }

            $request->setUserResolver(function () use ($user) {
                return $user;
            });

            Log::channel('appfolio')->debug("{$this->organization}'s requestNewWorkOrder validator", [
                'validated' => $request,
            ]);

            if ($request) {
                DB::beginTransaction();

                $serviceRequestRegisterClient = app(ServiceRequestRegisterClient::class);
                $timezone = Timezone::where('name', $payload['timezone'])->firstOrFail();
                $serviceRequest = $serviceRequestRegisterClient->register($request, $this->organization, $timezone);

                DB::commit();

                return $serviceRequest;
            }

            Log::channel('appfolio')->debug("{$this->organization}'s requestNewWorkOrder payload is invalid", [
                'payload' => $payload,
                'errors' => $request,
            ]);

            throw new Exception("{$this->organization->name}'s new work order payload is invalid");
        } catch (Exception $e) {
            Log::channel('appfolio')->error("{$this->organization->name}'s Appfolio requestNewWorkOrder Exception", ['error' => $e->getMessage()]);
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * @param  array<string,mixed>  $appfolioWorkOrder
     */
    protected function addAppfolioApiWorkOrderLog(array $appfolioWorkOrder): void
    {
        try {
            $appfolioApiLog = new appfolioApiLog;
            $appfolioApiLog->appfolio_entity_type = 'WorkOrder';
            $appfolioApiLog->appfolio_entity_uuid = $appfolioWorkOrder['Id'];
            $appfolioApiLog->appfolio_work_order_uuid = $appfolioWorkOrder['Id'];
            $appfolioApiLog->log_type = 'system';
            $appfolioApiLog->response = $appfolioWorkOrder;
            $appfolioApiLog->last_updated_at = $appfolioWorkOrder['LastUpdatedAt'];

            $appfolioApiLog->save();
        } catch (Exception $e) {
            Log::channel('appfolio')->error("Appfolio exception logWorkOrderData() - {$this->organization->name}", [
                'work_order' => $appfolioWorkOrder,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
        }
    }

    /**
     * @param  array<string,mixed>  $workOrder
     * @param  array<string,mixed>  $data
     * @return array<string,mixed>
     */
    protected function formatPropertyInfo(array $workOrder, array $data): array
    {

        if (empty($data['property_details']) && empty($data['unit_details'])) {
            return [];
        }

        $propertyName = ! empty($data['property_details']['Name']) ? $data['property_details']['Name'] . ', ' : '';
        $unitNumber = ! empty($data['property_details']['Address2']) ? $data['property_details']['Address2'] : null;
        $unitName = ! empty($data['property_details']['Name']) ? $data['property_details']['Name'] : null;
        $street = ! empty($data['property_details']['Address1']) ? $data['property_details']['Address1'] : null;
        $city = ! empty($data['property_details']['City']) ? $data['property_details']['City'] : null;
        $stateAbbreviation = ! empty($data['property_details']['State']) ? $data['property_details']['State'] : null;
        $zipCode = ! empty($data['property_details']['Zip']) ? $data['property_details']['Zip'] : null;

        //Consider priority for unit address
        if (! empty($data['unit_details'])) {
            $propertyName = ! empty($data['unit_details']['Name']) ? $data['unit_details']['Name'] . ', ' : $propertyName;
            $unitNumber = ! empty($data['unit_details']['Address2']) ? $data['unit_details']['Address2'] : $unitNumber;
            $unitName = ! empty($data['unit_details']['Name']) ? $data['unit_details']['Name'] : $unitName;
            $street = ! empty($data['unit_details']['Address1']) ? $data['unit_details']['Address1'] : $street;
            $city = ! empty($data['unit_details']['City']) ? $data['unit_details']['City'] : $city;
            $stateAbbreviation = ! empty($data['unit_details']['State']) ? $data['unit_details']['State'] : $stateAbbreviation;
            $zipCode = ! empty($data['unit_details']['Zip']) ? $data['unit_details']['Zip'] : $zipCode;
        }

        $fullAddress = "{$street}, {$city}, {$stateAbbreviation} {$zipCode}, US";
        /** @var Geocode $googleMapGeocodeClient */
        $googleMapGeocodeClient = GoogleApiServiceFactory::create(GoogleService::GEOCODE);
        $locationResponse = $googleMapGeocodeClient->getGeocodeFromAddress("{$fullAddress}");

        return [
            'property_name' => 'Jordane Hane Sr.',
            'full_address' => $locationResponse->get('address'),
            'street_address' => $street,
            'unit_number' => $unitNumber ?? $unitName,
            'bed_unit' => '',
            'city' => $city,
            'postal_zip_code' => $zipCode,
            'state' => $stateAbbreviation,
            'country' => 'US',
            'state_id' => $stateAbbreviation,
            'country_id' => 'US',
            'latitude' => $locationResponse->get('latitude'),
            'longitude' => $locationResponse->get('longitude'),
            'google_place_id' => $locationResponse->get('place_id'),
            'google_geocode_response' => $locationResponse->all(),
            'navigation_address' => $fullAddress,
            'access_info' => [
                'method' => 'adult-will-let-you-in',
                'code' => '',
                'note' => $workOrder['VendorInstructions'] ?? null,
            ],
        ];
    }

    /**
     * @param  array<string,mixed>  $data
     * @return array<int,mixed>
     */
    protected function formatTenantData(array $data): array
    {
        $tenants = [];

        if (! empty($data['tenant_details']) && $data['tenant_details'] instanceof Collection && $data['tenant_details']->isNotEmpty()) {
            foreach ($data['tenant_details']->sortBy('PrimaryTenant') as $tenantDetails) {
                $tenants[] = $this->formatTenantInput($tenantDetails);
            }
        }

        return $tenants;
    }

    /**
     * @param  array<string,mixed>  $scrappedData
     * @return array<int,array<string,string>>
     */
    protected function formatPhotos(array $scrappedData)
    {
        $photos = [];

        if (! empty($scrappedData['attachments'])) {
            foreach ($scrappedData['attachments'] as $attachmentUri) {
                $photos[]['uri'] = $attachmentUri;
            }
        }

        return $photos;
    }

    /**
     * Format tenant input
     *
     * @param  array<string,mixed>  $tenantDetails
     * @return array<string,mixed>
     */
    protected function formatTenantInput(?array $tenantDetails): array
    {
        return [
            'first_name' => ! empty($tenantDetails['FirstName']) ? trim($tenantDetails['FirstName']) : 'No Tenant',
            'last_name' => ! empty($tenantDetails['LastName']) ? trim($tenantDetails['LastName']) : null,
            'email' => $tenantDetails['Email'] ?? null,
            'phone_number' => (string) preg_replace('/[^0-9]/', '', ($tenantDetails['PhoneNumber'] ?? null)),
            'is_primary_resident' => true,
        ];
    }

    protected function addUnAssignedWorkOrders(string $api = 'work_orders', bool $includeParameters = true): void
    {
        if ($includeParameters) {
            $params['filters']['LastUpdatedAtFrom'] = Carbon::now()->subYears(2)->toDateTimeString();
            $params['filters']['Status'] = 'New';

            // This field is using for filter work orders that are created from the integrated date.
            if (! empty($this->organization->appfolio_integrated_at)) {
                $params['filters']['CreatedAtTo'] = $this->organization->appfolio_integrated_at;
            } else {
                $params['filters']['CreatedAtTo'] = Carbon::now()->toDateTimeString();
            }
        } else {
            $params = [];
            $parsedUrl = parse_url($api);

            if (! empty($parsedUrl['query'])) {
                parse_str($parsedUrl['query'], $params);
                $api = 'work_orders';
            }
        }

        $organizationWorkOrders = $this->getPaginatedWorkOrders($api, $params);

        if (! empty($organizationWorkOrders['data'])) {
            $this->addAppfolioIntegrationSkippedWorkOrders($organizationWorkOrders['data']);
        }

        if (! empty($organizationWorkOrders['next_page_path'])) {
            $this->addUnAssignedWorkOrders($organizationWorkOrders['next_page_path'], false);
        }
    }

    /**
     * Add existing work order data in skipped logs
     */
    protected function addExistingWorkOrders(string $api = 'work_orders', bool $includeParameters = true): void
    {
        if ($includeParameters) {
            $params['filters']['LastUpdatedAtFrom'] = Carbon::now()->subYears(2)->toDateTimeString();
            $params['filters']['VendorId'] = $this->appfolioVendorId;

            // This field is using for filter work orders that are created from the integrated date.
            if (! empty($this->organization->appfolio_integrated_at)) {
                $params['filters']['CreatedAtTo'] = $this->organization->appfolio_integrated_at;
            } else {
                $params['filters']['CreatedAtTo'] = Carbon::now()->toDateTimeString();
            }
        } else {
            $params = [];
            $parsedUrl = parse_url($api);

            if (! empty($parsedUrl['query'])) {
                parse_str($parsedUrl['query'], $params);
                $api = 'work_orders';
            }
        }

        $organizationWorkOrder = $this->getPaginatedWorkOrders($api, $params);

        if (! empty($organizationWorkOrder['data'])) {
            $this->addAppfolioIntegrationSkippedWorkOrders($organizationWorkOrder['data']);
        }

        if (! empty($organizationWorkOrder['next_page_path'])) {
            $this->addExistingWorkOrders($organizationWorkOrder['next_page_path'], false);
        }
    }

    /**
     * @param  array<int,array<string,mixed>>  $data
     */
    protected function addAppfolioIntegrationSkippedWorkOrders($data): void
    {
        collect($data)->each(function ($workOrder) {
            AppfolioIntegrationSkippedServiceRequest::updateOrCreate([
                'appfolio_work_order_uuid' => $workOrder['Id'],
                'organization_id' => $this->organization->organization_id,
            ], [
                'organization_id' => $this->organization->organization_id,
                'work_order_id' => null,
                'appfolio_work_order_uuid' => $workOrder['Id'],
                'appfolio_work_order_number' => $workOrder['WorkOrderNumber'],
                'appfolio_work_order_status' => $workOrder['Status'],
                'appfolio_vendor_id' => $workOrder['VendorId'],
                'foresight_service_request_created_at' => Carbon::parse($workOrder['CreatedAt']),
            ]);
        });
    }
}

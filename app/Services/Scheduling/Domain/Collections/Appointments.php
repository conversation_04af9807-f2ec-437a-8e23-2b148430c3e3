<?php

namespace App\Services\Scheduling\Domain\Collections;

use App\Models\Technician;
use App\Models\TechnicianAppointment;
use App\Services\Scheduling\Domain\Entities\Appointment;
use Illuminate\Support\Collection;

/**
 * @extends Collection<int, mixed>
 *
 * @method Appointment first(callable $callback = null, $default = null)
 */
class Appointments extends Collection
{
    /**
     * @param  array<int, mixed>  $appointments
     */
    public function __construct(array $appointments = [])
    {
        parent::__construct($appointments);
    }

    public static function from(Technician $technician): Appointments
    {
        return new self(
            $technician->appointments->map(function (TechnicianAppointment $appointment) {
                return Appointment::from($appointment);
            })->toArray()
        );
    }
}

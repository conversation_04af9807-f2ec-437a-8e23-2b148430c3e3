<?php

namespace App\Services\Scheduling\Domain\Traits;

use App\Services\Scheduling\Domain\Entities\Location;

trait DistanceHelperTrait
{
    protected function calculateStraightLineDistance(Location $a, Location $b, string $unit = 'M'): float
    {
        if (($a->lat == $b->lat) && ($a->long == $b->long)) {
            return 0;
        } else {
            $theta = $a->long - $b->long;
            $dist = sin(deg2rad($a->lat)) * sin(deg2rad($b->lat)) + cos(deg2rad($a->lat)) * cos(deg2rad($b->lat)) * cos(deg2rad($theta));
            $dist = acos($dist);
            $dist = rad2deg($dist);
            $miles = $dist * 60 * 1.1515;
            $unit = strtoupper($unit);

            if ($unit == 'K') {
                return $miles * 1.609344;
            } elseif ($unit == 'N') {
                return $miles * 0.8684;
            } else {
                return $miles;
            }
        }
    }
}

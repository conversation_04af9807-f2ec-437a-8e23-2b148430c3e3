<?php

namespace App\Services\Scheduling\Domain\Entities;

use App\Models\TechnicianWorkingHour;
use App\Services\Scheduling\Domain\Enums\CarbonDayOfWeek;
use Carbon\CarbonImmutable;
use Carbon\CarbonInterface;
use Exception;

/** @phpstan-consistent-constructor */
class WorkingDay
{
    public function __construct(public readonly CarbonDayOfWeek $workDay, public readonly CarbonImmutable $workStartsAt, public readonly CarbonImmutable $workEndsAt) {}

    /**
     * @throws Exception
     */
    public static function from(TechnicianWorkingHour $workingHour): WorkingDay
    {
        return new static(
            CarbonDayOfWeek::fromString($workingHour->weekday),
            $workingHour->work_start_at,
            $workingHour->work_end_at
        );
    }

    public function is(CarbonInterface $date): bool
    {
        return $this->workDay->value == $date->dayOfWeek;
    }
}

<?php

namespace App\Services\Scheduling\Domain\Entities;

use App\Services\Scheduling\Domain\Traits\DistanceHelperTrait;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Support\Facades\Log;

class ServiceWindow
{
    use DistanceHelperTrait;

    public ?Location $destination;
    public ?float $travelDistance;

    // TODO - this object is getting too unpredictable, refactor to multiple types with less null
    public function __construct(
        public readonly string $technicianUUID,
        public readonly CarbonImmutable $startTime,
        public ?CarbonImmutable $endTime = null,
        public readonly ?Location $previousLocation = null,
        public readonly ?bool $onHours = null,
        protected int $beforeBuffer = 60,
        protected int $afterBuffer = 60
    ) {}

    public static function fromReference(string $reference): ServiceWindow
    {
        $decoded = explode('.', base64_decode($reference));

        try {
            return new self(
                technicianUUID: $decoded[0],
                startTime: CarbonImmutable::parse($decoded[1]),
                endTime: CarbonImmutable::parse($decoded[2])
            );
        } catch (Exception $e) {
            Log::error('service window reference not matching expected schema', [$e->getMessage()]);
            // TODO throw a more interesting error.
            throw $e;
        }
    }

    public function getResidentStartTime(): CarbonImmutable
    {
        return $this->startTime->subMinutes($this->beforeBuffer);
    }

    public function getResidentEndTime(): ?CarbonImmutable
    {
        return $this->endTime?->addMinutes($this->afterBuffer);
    }

    public function getProviderStartTime(): CarbonImmutable
    {
        return $this->startTime;
    }

    public function getProviderEndTime(): ?CarbonImmutable
    {
        return $this->endTime;
    }

    public function setEndTime(CarbonImmutable $endTime): void
    {
        $this->endTime = $endTime;
    }

    public function setDestination(Location $destination): void
    {
        $this->destination = $destination;
        if ($this->previousLocation) {
            $this->travelDistance = $this->calculateStraightLineDistance(
                $destination,
                $this->previousLocation
            );
        } else {
            Log::warning('Unable to set travelDistance because previousLocation not found');
        }
    }

    public function encodedReference(): string
    {
        return base64_encode(implode('.', [
            $this->technicianUUID,
            $this->startTime->toIso8601String(),
            $this->endTime?->toIso8601String(),
        ]));
    }
}

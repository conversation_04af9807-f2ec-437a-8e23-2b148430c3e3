<?php

namespace App\Services\Scheduling;

use App\Clients\AiClient;
use App\Enums\Boolean;
use App\Enums\Priority;
use App\Enums\ScheduleTimings;
use App\Models\Organization;
use App\Models\ResidentAvailability;
use App\Models\Vendor;
use App\Models\WorkOrder as WorkOrderModel;
use App\Services\Scheduling\Domain\DTOs\AvailableProviders;
use App\Services\Scheduling\Domain\DTOs\TaskSchedulingOptions;
use App\Services\Scheduling\Domain\DTOs\TechnicianList;
use App\Services\Scheduling\Domain\DTOs\TechnicianSchedule;
use App\Services\Scheduling\Domain\Entities\RankedServiceWindow;
use App\Services\Scheduling\Domain\Entities\ServiceWindow;
use App\Services\Scheduling\Domain\Entities\Technician;
use App\Services\Scheduling\Domain\Entities\TechnicianCalendar;
use App\Services\Scheduling\Domain\Entities\WorkOrder;
use App\Services\Scheduling\Domain\Enums\SchedulingMethod;
use App\Services\Scheduling\Domain\Enums\SchedulingMode;
use App\Services\Scheduling\Domain\Traits\DistanceHelperTrait;
use App\Services\Scheduling\Strategies\EarliestStrategy;
use App\Services\Scheduling\Strategies\EfficientStrategy;
use App\Services\Scheduling\Strategies\EmergencyStrategy;
use App\Services\Scheduling\Strategies\SchedulingStrategyInterface;
use App\Services\Vendor\Enum\Service;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Throwable;

class SchedulingService
{
    use DistanceHelperTrait;

    public function __construct(protected SchedulingRepository $repository, protected AiClient $aiClient) {}

    public function getTaskSchedulingOptions(int $organizationId, int $workOrderId): TaskSchedulingOptions
    {
        $workOrder = $this->repository->getWorkOrder($workOrderId, $organizationId);
        $technicians = $this->getQualifiedTechnicians($organizationId, $workOrder);
        $duration = $this->getEstimatedDuration($workOrder);
        $currentDuration = $this->getCurrentDuration($workOrder);
        $currentMode = $this->getCurrentMode($workOrder);
        $currentMethod = $this->getCurrentMethod($workOrder);
        $workToPerform = $this->getCurrentWorkPerform($workOrder);
        $quote = $this->getCurrentLinkedQuote($workOrder);

        $vendors = $this->getVendors($organizationId);

        $residentAvailabilities = ResidentAvailability::select('timing', 'availability_date')
            ->where('service_request_id', $workOrder->serviceRequestId)
            ->where('day_passed', 'no')
            ->get();

        $timezone = $workOrder->timezone;

        $calendars = $technicians->map(function (Technician $technician) use ($residentAvailabilities, $timezone) {
            return new TechnicianCalendar(
                $technician,
                $residentAvailabilities,
                config('schedule.technician.calendar.slot_granularity'),
                false,
                $timezone
            );
        });

        $residentAvailabilityDateTime = $residentAvailabilities->map(function (ResidentAvailability $residentAvailability) use ($timezone) {
            $timingRange = ScheduleTimings::timeRange($residentAvailability->timing);

            return [
                'start_date' => CarbonImmutable::parse($residentAvailability->availability_date->format('Y-m-d') . $timingRange['start_time'], $timezone)->setTimezone('UTC'),
                'end_date' => CarbonImmutable::parse($residentAvailability->availability_date->format('Y-m-d') . $timingRange['end_time'], $timezone)->setTimezone('UTC'),
                'date' => CarbonImmutable::parse($residentAvailability->availability_date->format('Y-m-d') . $timingRange['start_time'], $timezone)->setTimezone('UTC')->format('Y-m-d'),
            ];
        })->toArray();

        $maxAvailabilityDate = $residentAvailabilities
            ->max(function ($availability) use ($timezone) {
                return CarbonImmutable::parse($availability->availability_date, $timezone)->startOfDay()->setTimezone('UTC')->addDay();
            });

        $rankedWindows = $this->getScheduler(SchedulingMethod::EFFICIENT)->getPreferredWindows($calendars, $workOrder->property, $duration, $maxAvailabilityDate);

        $filteredRankedWindows = $this->filterWindowsByResidentAvailability($rankedWindows, $residentAvailabilityDateTime);

        /** @var bool $inHouseTechAvailable */
        $inHouseTechAvailable = ! $filteredRankedWindows->isEmpty();
        $recommendedMethod = ($workOrder->workOrderPriority === Priority::URGENT() || $workOrder->serviceRequestPriority === Priority::URGENT()) ? SchedulingMethod::EARLIEST : SchedulingMethod::EFFICIENT;

        return new TaskSchedulingOptions(new AvailableProviders(
            $technicians,
            $vendors->where('service', Service::THIRD_PARTY_VENDOR())->where('is_active', Boolean::YES()), // TODO add vendors
            $vendors->where('service', Service::LULA())->first(), // TODO add Lula Appointments
        ), $duration, $currentDuration, $currentMode, $currentMethod, $workToPerform, $quote, $inHouseTechAvailable, SchedulingMode::MANUAL, $recommendedMethod);
    }

    // TODO only return service windows which can accommodate a 30 min buffer ahead of time for driving
    public function getTechnicianList(int $organizationId, WorkOrderModel $workOrder, SchedulingMethod $method, int $duration, bool $skipCollisionCheck = false): TechnicianList
    {
        $residentAvailabilities = ResidentAvailability::select('timing', 'availability_date')
            ->where('service_request_id', $workOrder->service_request_id)
            ->where('day_passed', 'no')
            ->get();

        if ($residentAvailabilities->isEmpty()) {
            throw ValidationException::withMessages([
                'resident_availability' => ['Please provide resident availability to proceed with scheduling.'],
            ]);
        }

        $workOrderEntity = $this->repository->getWorkOrder($workOrder->work_order_id, $organizationId);
        $technicians = $this->getQualifiedTechnicians($organizationId, $workOrderEntity);
        $timezone = $workOrderEntity->timezone ?: config('settings.default_timezone');

        $calendars = $technicians->map(function (Technician $technician) use ($skipCollisionCheck, $residentAvailabilities, $timezone) {
            return new TechnicianCalendar(
                $technician,
                $residentAvailabilities,
                config('schedule.technician.calendar.slot_granularity'),
                $skipCollisionCheck,
                $timezone,
            );
        });

        $residentAvailabilityDateTime = $residentAvailabilities->map(function (ResidentAvailability $residentAvailability) use ($timezone) {
            $timingRange = ScheduleTimings::timeRange($residentAvailability->timing);

            return [
                'start_date' => CarbonImmutable::parse($residentAvailability->availability_date->format('Y-m-d') . $timingRange['start_time'], $timezone)->setTimezone('UTC'),
                'end_date' => CarbonImmutable::parse($residentAvailability->availability_date->format('Y-m-d') . $timingRange['end_time'], $timezone)->setTimezone('UTC'),
                'date' => CarbonImmutable::parse($residentAvailability->availability_date->format('Y-m-d') . $timingRange['start_time'], $timezone)->setTimezone('UTC')->format('Y-m-d'),
            ];
        })->toArray();

        $maxAvailabilityDate = $residentAvailabilities
            ->max(function ($availability) use ($timezone) {
                return CarbonImmutable::parse($availability->availability_date, $timezone)->startOfDay()->setTimezone('UTC')->addDay();
            });

        $rankedWindows = $this->getScheduler($method)->getPreferredWindows($calendars, $workOrderEntity->property, $duration, $maxAvailabilityDate);

        $filteredRankedWindows = $this->filterWindowsByResidentAvailability($rankedWindows, $residentAvailabilityDateTime);

        $availableTechnicianUUIDs = $filteredRankedWindows
            ->pluck('serviceWindow.technicianUUID')
            ->unique();

        $filteredTechnicians = $technicians->filter(function (Technician $technician) use ($availableTechnicianUUIDs) {
            return $availableTechnicianUUIDs->contains($technician->uuid);
        })->values();

        /**
         * @var Collection<int,RankedServiceWindow>
         */
        $recommendedWindows = $filteredRankedWindows
            ->filter(fn (RankedServiceWindow $ranked) => $ranked->serviceWindow->onHours === true && $ranked->serviceWindow->startTime->isFuture())
            ->groupBy(fn (RankedServiceWindow $ranked) => $ranked->serviceWindow->technicianUUID)
            ->sortBy(fn (Collection $filteredRankedWindows) => $filteredRankedWindows->min(fn (RankedServiceWindow $ranked) => $ranked->rank))
            ->take(5)
            ->map(fn (Collection $filteredRankedWindows) => $filteredRankedWindows->first())
            ->values();

        return new TechnicianList($filteredTechnicians, $filteredRankedWindows, $recommendedWindows);
    }

    // TODO only return service windows which can accommodate a 30 min buffer ahead of time for driving
    public function getTechnicianSchedules(int $organizationId, WorkOrderModel $workOrder, SchedulingMethod $method, int $duration, ?string $technicianUUID = null): TechnicianSchedule
    {
        $residentAvailabilities = ResidentAvailability::select('timing', 'availability_date')
            ->where('service_request_id', $workOrder->service_request_id)
            ->where('day_passed', 'no')
            ->get();

        if ($residentAvailabilities->isEmpty()) {
            throw ValidationException::withMessages([
                'resident_availability' => ['Please provide resident availability to proceed with scheduling.'],
            ]);
        }

        $workOrderEntity = $this->repository->getWorkOrder($workOrder->work_order_id, $organizationId);
        $technicians = $this->getQualifiedTechnicians($organizationId, $workOrderEntity, $technicianUUID);
        $timezone = $workOrderEntity->timezone;

        $calendars = $technicians->map(function (Technician $technician) use ($residentAvailabilities, $timezone) {
            return new TechnicianCalendar(
                $technician,
                $residentAvailabilities,
                config('schedule.technician.calendar.slot_granularity'),
                false,
                $timezone
            );
        });

        $residentAvailabilityDateTime = $residentAvailabilities->map(function (ResidentAvailability $residentAvailability) use ($timezone) {
            $timingRange = ScheduleTimings::timeRange($residentAvailability->timing);

            return [
                'start_date' => CarbonImmutable::parse($residentAvailability->availability_date->format('Y-m-d') . $timingRange['start_time'], $timezone)->setTimezone('UTC'),
                'end_date' => CarbonImmutable::parse($residentAvailability->availability_date->format('Y-m-d') . $timingRange['end_time'], $timezone)->setTimezone('UTC'),
                'date' => CarbonImmutable::parse($residentAvailability->availability_date->format('Y-m-d') . $timingRange['start_time'], $timezone)->setTimezone('UTC')->format('Y-m-d'),
            ];
        })->toArray();

        $maxAvailabilityDate = $residentAvailabilities
            ->max(function ($availability) use ($timezone) {
                return CarbonImmutable::parse($availability->availability_date, $timezone)->startOfDay()->setTimezone('UTC')->addDay();
            });

        $rankedWindows = $this->getScheduler($method)->getPreferredWindows($calendars, $workOrderEntity->property, $duration, $maxAvailabilityDate);

        $filteredRankedWindows = $this->filterWindowsByResidentAvailability($rankedWindows, $residentAvailabilityDateTime);

        return new TechnicianSchedule($technicians, $filteredRankedWindows);
    }

    /**
     * @throws Throwable
     */
    public function registerServiceCall(int $organizationId, WorkOrderModel $workOrder, string $serviceWindowReference, string $mode, string $method, string $workToPerform, ?string $quoteId = null, ?string $reschedulingReason = null, ?string $nteAmount = null): WorkOrderModel
    {
        $problemDiagnosisIds = $workOrder->issues->pluck('problem_diagnosis_id')->toArray();
        $serviceWindow = ServiceWindow::fromReference($serviceWindowReference);
        $technician = $this->repository->getTechnicianByUUID($organizationId, $serviceWindow->technicianUUID, $problemDiagnosisIds);
        $residentAvailabilities = ResidentAvailability::select('timing', 'availability_date')
            ->where('service_request_id', $workOrder->service_request_id)
            ->where('day_passed', 'no')
            ->get();

        if (! $technician) {
            throw new Exception('Technician lacks the necessary skill set to address these issues.');
        }

        $timezone = $workOrder->timezone->name ?? config('settings.default_timezone');

        // TODO - get rid of all these hard coded periods
        $calendar = new TechnicianCalendar(
            $technician,
            $residentAvailabilities,
            30,
            false,
            $timezone
        );

        if (! $calendar->isAvailableServiceWindow($serviceWindow)) {
            throw new Exception('Service window is no longer available');
        }

        $workOrder = $this->repository->registerServiceCall(
            workOrder: $workOrder,
            technician: $technician,
            window: $serviceWindow,
            mode: $mode,
            method: $method,
            workToPerform: $workToPerform,
            quoteId: $quoteId,
            reason: $reschedulingReason,
            nteAmount: $nteAmount,
        );

        return $workOrder;
    }

    /**
     * Merge continuous time ranges where end_date equals the next start_date.
     *
     * @param  array<int, array{start_date: CarbonImmutable, end_date: CarbonImmutable}>  $ranges
     * @return array<int, array{start_date: CarbonImmutable, end_date: CarbonImmutable}>
     */
    public function findStartAndEndTimes(array $ranges): array
    {
        if (count($ranges) <= 1) {
            return $ranges;
        }

        usort($ranges, function ($a, $b) {
            return $a['start_date']->lessThan($b['start_date']) ? -1 : 1;
        });

        $mergedRanges = [];
        $currentRange = $ranges[0];

        foreach (array_slice($ranges, 1) as $range) {
            if ($currentRange['end_date']->equalTo($range['start_date'])) {
                $currentRange['end_date'] = $range['end_date'];
            } else {
                $mergedRanges[] = $currentRange;
                $currentRange = $range;
            }
        }

        $mergedRanges[] = $currentRange;

        return $mergedRanges;
    }

    /**
     * @return Collection<int, Vendor>
     */
    protected function getVendors(int $organizationId): Collection
    {
        $organization = Organization::with('vendors:vendor_uuid,company_name,service,is_active')
            ->findOrFail($organizationId, ['organization_id']);

        return $organization->vendors->map(fn (Vendor $vendor) => $vendor);
    }

    protected function getScheduler(SchedulingMethod $method): SchedulingStrategyInterface
    {
        return match ($method) {
            SchedulingMethod::EARLIEST => new EarliestStrategy,
            SchedulingMethod::EFFICIENT => new EfficientStrategy,
            SchedulingMethod::EMERGENCY => new EmergencyStrategy
        };
    }

    protected function getEstimatedDuration(WorkOrder $workOrder): int
    {
        return $workOrder->estimated_duration ?? $this->aiClient->getEstimatedDuration($workOrder);
    }

    protected function getCurrentDuration(WorkOrder $workOrder): ?int
    {
        return $workOrder->durationMinutes ?? null;
    }

    protected function getCurrentMode(WorkOrder $workOrder): ?string
    {
        return $workOrder->mode ?? null;
    }

    protected function getCurrentMethod(WorkOrder $workOrder): ?string
    {
        return $workOrder->method ?? null;
    }

    protected function getCurrentWorkPerform(WorkOrder $workOrder): ?string
    {
        return $workOrder->workToPerform ?? null;
    }

    /**
     * @return array<string,string>
     */
    protected function getCurrentLinkedQuote(WorkOrder $workOrder): array
    {
        if (! empty($workOrder->quote)) {
            return [
                'value' => $workOrder->quote->quoteId,
                'label' => "Related to Quote #{$workOrder->quote->quoteNumber}",
            ];
        }

        return [];
    }

    /**
     * @return Collection<int,Technician>
     */
    protected function getQualifiedTechnicians(int $organizationId, WorkOrder $workOrder, ?string $technicianUUID = null): Collection
    {
        $problemDiagnosisIds = $workOrder->diagnosisIds;
        $skilledTechs = $this->repository->getTechniciansForTask($organizationId, $problemDiagnosisIds, $technicianUUID);

        Log::info('Technicians qualified by skill set', ['total' => $skilledTechs->count(), 'technician_ids' => $skilledTechs->pluck('id')->toArray()]);

        $qualifiedTechs = $skilledTechs->filter(function (Technician $technician) use ($workOrder) {
            $distanceToWorkOrder = round($this->calculateStraightLineDistance(
                $technician->location,
                $workOrder->property
            ), 2);

            return $technician->maxTravelDistance >= $distanceToWorkOrder;
        });

        Log::info('Technicians qualified by skill set and within max distance', ['total' => $qualifiedTechs->count(), 'technician_ids' => $qualifiedTechs->pluck('id')->toArray()]);

        return $qualifiedTechs;
    }

    /**
     * Filter service windows that match resident availability timings.
     *
     * @param array<int|string,array<string,CarbonImmutable|string>
     */
    private function filterWindowsByResidentAvailability(Collection $rankedWindows, array $residentAvailabilityDateTime): Collection
    {
        return $rankedWindows->filter(function ($rankedWindow) use ($residentAvailabilityDateTime) {
            $startTime = $rankedWindow->serviceWindow->startTime;
            $endTime = $rankedWindow->serviceWindow->endTime;
            $availabilities = collect($residentAvailabilityDateTime)->where('date', $startTime->format('Y-m-d'));

            if ($rankedWindow->serviceWindow->onHours === false || ! $rankedWindow->serviceWindow->startTime->isFuture()) {
                return false;
            }

            if ($availabilities->isEmpty()) {
                return false;
            }

            $newAvailabilities = $this->findStartAndEndTimes($availabilities->toArray());
            foreach ($newAvailabilities as $availability) {
                if ($startTime->gte($availability['start_date']) && $endTime->lte($availability['end_date'])) {
                    return true;
                }
            }

            return false;
        })->values();
    }
}

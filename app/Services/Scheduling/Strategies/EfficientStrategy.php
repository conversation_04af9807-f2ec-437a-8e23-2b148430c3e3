<?php

namespace App\Services\Scheduling\Strategies;

use App\Services\Scheduling\Domain\Entities\Location;
use App\Services\Scheduling\Domain\Entities\RankedServiceWindow;
use App\Services\Scheduling\Domain\Entities\ServiceWindow;
use App\Services\Scheduling\Domain\Entities\TechnicianCalendar;
use Carbon\CarbonImmutable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class EfficientStrategy implements SchedulingStrategyInterface
{
    /**
     * @param  Collection<int,TechnicianCalendar>  $calendars
     * @return Collection<int,RankedServiceWindow>
     */
    public function getPreferredWindows(Collection $calendars, Location $location, int $duration, CarbonImmutable $maxDate): Collection
    {
        Log::info('Getting preferred windows using Efficient Strategy');
        $windows = $calendars
            ->flatMap(function (TechnicianCalendar $calendar) use ($duration, $maxDate, $location) {
                // Convert Calendar to Open Service Windows
                $windows = $calendar->getOpenServiceWindows($duration, $maxDate);
                $windows->map(function (ServiceWindow $window) use ($location) {
                    $window->setDestination($location);
                });

                return $windows;
            });

        // group by onHours, then distance, than earliest time.
        // TODO - if off hours are suggested, we should return the off hours nearest on hours as higher rank
        return $windows->groupBy(function (ServiceWindow $window) {
            return (int) $window->onHours;
        })
            ->sortKeys()
            // TODO: need tp resolve this code type issue, for now.
            ->flatMap(function (Collection $collection) {
                return $collection
                    ->groupBy(function (ServiceWindow $window) {
                        // Group Service Windows distance from origin location to new work order location - bucket distances in 5 mile increments
                        return (int) ceil($window->travelDistance / 5);
                    })
                    ->sortKeys() // sort buckets so that the closest bucket is at top of list
                    ->flatMap(function (Collection $collection) {
                        // Within each bucket sort service windows by start time.
                        return $collection->sortBy(function (ServiceWindow $window) {
                            return $window->startTime->timestamp;
                        });
                    })
                    ->values()
                    ->map(function (ServiceWindow $window, $index) {
                        return new RankedServiceWindow($index, $window);
                    });
            });
    }
}

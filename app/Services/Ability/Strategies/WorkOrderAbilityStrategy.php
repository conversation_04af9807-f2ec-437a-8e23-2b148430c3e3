<?php

namespace App\Services\Ability\Strategies;

use App\Enums\WorkOrderActions;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\States\ServiceCalls\EnRoute;
use App\States\ServiceCalls\EnRoutePaused;
use App\States\ServiceCalls\Paused;
use App\States\ServiceCalls\Working;
use App\States\WorkOrders\Scheduled;
use App\States\WorkOrders\WorkInProgress;
use Illuminate\Database\Eloquent\Model;

class WorkOrderAbilityStrategy implements AbilityStrategy
{
    public function getAbilities(Model $workOrder, string $sourceType = 'web'): array
    {
        /** @var WorkOrder $workOrder */
        $possibleActions = $workOrder->state->actions();

        /** @var WorkOrderServiceCall $activeTrip */
        $activeTrip = $workOrder->latestTrips->first();

        if ($sourceType !== 'web') {
            $possibleActions = $this->filterMobileAbilities($workOrder, $possibleActions);
        }

        if (! empty($activeTrip) && $workOrder->state->equals(WorkInProgress::class)) {
            $possibleActions = $this->abilitiesForWorkInProgressStatus($activeTrip, $possibleActions);
        }

        return $possibleActions;
    }

    /**
     * Filter out the abilities when work order is in work in progress
     *
     * @param  array<int|string>  $actions
     * @return array<int|string>
     */
    public function abilitiesForWorkInProgressStatus(WorkOrderServiceCall $trip, array $actions): array
    {
        $abilitiesToRemove = match ($trip->state?->getValue()) {
            EnRoute::$name => [WorkOrderActions::RESUME_EN_ROUTE(), WorkOrderActions::CLOSE_WORK_ORDER(), WorkOrderActions::RESUME_WORK(), WorkOrderActions::PAUSE_WORK()],
            EnRoutePaused::$name => [WorkOrderActions::START_WORK(), WorkOrderActions::PAUSE_EN_ROUTE(), WorkOrderActions::CLOSE_WORK_ORDER(), WorkOrderActions::RESUME_WORK(), WorkOrderActions::PAUSE_WORK()],
            Working::$name => [WorkOrderActions::RESUME_WORK(), WorkOrderActions::PAUSE_EN_ROUTE(), WorkOrderActions::RESUME_EN_ROUTE(), WorkOrderActions::START_WORK()],
            Paused::$name => [WorkOrderActions::PAUSE_EN_ROUTE(), WorkOrderActions::RESUME_EN_ROUTE(), WorkOrderActions::START_WORK(), WorkOrderActions::PAUSE_WORK()],
            default => []
        };

        return $this->removeActions($actions, $abilitiesToRemove);
    }

    /**
     * Filter abilities for mobile
     *
     * @param  array<int|string>  $actions
     * @return array<int|string>
     */
    private function filterMobileAbilities(WorkOrder $workOrder, array $actions): array
    {
        $actions = $this->removeActions($actions, [WorkOrderActions::CREATE_ISSUE()]);

        if ($workOrder->state->equals(Scheduled::class)) {
            $actions = $this->removeActions($actions, [
                WorkOrderActions::PAUSE(),
                WorkOrderActions::CANCEL(),
                WorkOrderActions::RE_SCHEDULE(),
            ]);
        }

        if ($workOrder->state->equals(WorkInProgress::class)) {
            $actions = $this->removeActions($actions, [
                WorkOrderActions::PAUSE(),
                WorkOrderActions::CANCEL(),
            ]);
        }

        return $actions;
    }

    /**
     * Utility to remove a list of actions from the actions array.
     *
     * @param  array<int|string>  $actions
     * @param  array<int|string>  $actionsToRemove
     * @return array<int|string>
     */
    private function removeActions(array $actions, array $actionsToRemove): array
    {
        foreach ($actionsToRemove as $actionToRemove) {
            if (in_array($actionToRemove, $actions)) {
                unset($actions[array_search($actionToRemove, $actions, true)]);
            }
        }

        return $actions;
    }
}

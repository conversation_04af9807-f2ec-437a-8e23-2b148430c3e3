<?php

namespace App\Services\Ability\Strategies;

use App\Enums\WorkOrderIssueActions;
use App\Models\WorkOrderIssue;
use App\States\WorkOrderIssue\Done;
use App\States\WorkOrderIssue\Unresolved;
use App\States\WorkOrders\Canceled as WorkOrderCanceled;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\Scheduled;
use App\States\WorkOrders\WorkInProgress;
use Illuminate\Database\Eloquent\Model;

class WorkOrderIssueAbilityStrategy implements AbilityStrategy
{
    public function getAbilities(Model $workOrderIssue, string $sourceType = 'web'): array
    {
        /** @var WorkOrderIssue $workOrderIssue */
        $possibleActions = $workOrderIssue->state->actions();

        if ($sourceType !== 'web') {
            $possibleActions = $this->filterUnusedAbilitiesForMobile($workOrderIssue, $possibleActions);
        }

        if ($this->isDeleted($workOrderIssue)) {
            return [];
        }

        $possibleActions = $this->filterEditAction($workOrderIssue, $possibleActions);

        return $possibleActions;
    }

    /**
     * Check if the issue is deleted.
     */
    private function isDeleted(WorkOrderIssue $workOrderIssue): bool
    {
        return ! empty($workOrderIssue->deleted_at);
    }

    /**
     * Filter out the EDIT action if the work order issue if mapped work order is canceled.
     *
     * @param  array<int|string>  $actions
     * @return array<int|string>
     */
    private function filterEditAction(WorkOrderIssue $workOrderIssue, array $actions): array
    {
        if (in_array(WorkOrderIssueActions::EDIT(), $actions) && $this->isMappedToClosedWorkOrder($workOrderIssue)) {
            unset($actions[array_search(WorkOrderIssueActions::EDIT(), $actions, true)]);
        }

        return $actions;
    }

    /**
     * Check if the work order associated with the work order issue is canceled.
     */
    private function isMappedToClosedWorkOrder(WorkOrderIssue $workOrderIssue): bool
    {
        if (empty($workOrderIssue->workOrder)) {
            return false;
        }

        return $workOrderIssue->workOrder->state?->equals(WorkOrderCanceled::class) ?? false;
    }

    /**
     * Filter un used abilities for mobile
     *
     * @param  array<int|string>  $actions
     * @return array<int|string>
     */
    private function filterUnusedAbilitiesForMobile(WorkOrderIssue $workOrderIssue, array $actions): array
    {
        if (! $workOrderIssue->workOrder->state->equals(Scheduled::class, WorkInProgress::class, Paused::class)) {
            return [];
        }

        if (in_array(WorkOrderIssueActions::EDIT(), $actions) && ! $workOrderIssue->state->equals(Done::class, Unresolved::class)) {
            unset($actions[array_search(WorkOrderIssueActions::EDIT(), $actions, true)]);
        }

        if (in_array(WorkOrderIssueActions::ASSIGN(), $actions)) {
            unset($actions[array_search(WorkOrderIssueActions::ASSIGN(), $actions, true)]);
        }

        if (in_array(WorkOrderIssueActions::UNASSIGN(), $actions)) {
            unset($actions[array_search(WorkOrderIssueActions::UNASSIGN(), $actions, true)]);
        }

        return $actions;
    }
}

<?php

namespace App\Services;

use App\Enums\DateFilterType;
use App\Enums\DateOperation;
use App\Enums\DateSlug;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use UnexpectedValueException;
use ValueError;

/**
 * DateFilterService handles all date filtering functionality for the application.
 *
 * This service centralizes date filtering logic across the application, providing
 * a consistent way to filter data based on dates. It handles the complexities of
 * translating date slugs (like 'today', 'this_week') into appropriate SQL queries.
 *
 * Key features:
 * - Support for date slugs (today, tomorrow, yesterday, this_week, etc.)
 * - Support for different operations (is, is_not, is_after, is_before)
 * - Support for both single dates and date ranges
 * - Integration with Lara<PERSON>'s query builder
 */
class DateFilterService
{
    /**
     * Apply date filter to the query
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder to apply the filter to
     * @param  array{"group_operation": string, "column_operation": string, "field": string, "column_name": string, "values": array<string>}  $payload  The filter payload
     * @param  string|null  $entityType  The entity type to use for resolving the where clause operator
     * @return Builder<TModel> The query builder with date filter applied
     */
    public function applyDateFilter(Builder $query, array $payload, ?string $entityType = null): Builder
    {
        $whereClause = $this->resolveWhereClauseOperator($payload['group_operation']);
        $dateSlug = $payload['values'][0];
        $operation = DateOperation::tryFrom($payload['column_operation'])
            ?? throw new UnexpectedValueException("Invalid date operation: {$payload['column_operation']}");

        $operatorSet = $this->resolveOperatorSetForDateSlug($dateSlug, $operation);

        // Handle different operation types with appropriate Laravel query builder methods
        return match ($operatorSet['operation']) {
            DateFilterType::DATE_BETWEEN->value => $this->applyBetweenFilter($query, $whereClause, $payload['column_name'], $operatorSet['value']),
            DateFilterType::DATE_NOT_BETWEEN->value => $this->applyNotBetweenFilter($query, $whereClause, $payload['column_name'], $operatorSet['value']),
            DateFilterType::DATE->value => $this->applySingleDateFilter($query, $whereClause, $payload['column_name'], $operatorSet['operator'], $operatorSet['value']),
            default => throw new UnexpectedValueException("Unexpected date operation [{$operatorSet['operation']}] provided."),
        };
    }

    /**
     * Resolve the operator set for a date slug and operation
     *
     * @param  string  $dateSlug  The date slug (today, tomorrow, this_week, etc.)
     * @param  DateOperation  $operation  The operation (is, is_not, is_after, is_before)
     * @return array{"operation": string, "operator": string, "value": string|array<string>}
     */
    public function resolveOperatorSetForDateSlug(string $dateSlug, DateOperation $operation): array
    {
        return match ($operation) {
            DateOperation::IS => $this->dateValuesForIsOperation($dateSlug),
            DateOperation::IS_NOT => $this->dateValuesForIsNotOperation($dateSlug),
            DateOperation::IS_AFTER => $this->dateValuesForIsAfterOperation($dateSlug),
            DateOperation::IS_BEFORE => $this->dateValuesForIsBeForeOperation($dateSlug),
        };
    }

    /**
     * Get date values for 'is' operation
     *
     * @param  string  $dateSlug  The date slug
     * @return array{"operation": string, "operator": string, "value": string|array<string>}
     */
    public function dateValuesForIsOperation(string $dateSlug): array
    {
        $dateRange = $this->getDateRangeFromSlug($dateSlug);
        [$type, $startDate, $endDate] = $dateRange;

        if ($type === 'single') {
            return $this->dateOperationResponse(DateFilterType::DATE, '=', $startDate->format('Y-m-d'));
        }

        return $this->dateOperationResponse(DateFilterType::DATE_BETWEEN, 'between', $startDate->format('Y-m-d'), $endDate->format('Y-m-d'));
    }

    /**
     * Get date values for 'is_not' operation
     *
     * @param  string  $dateSlug  The date slug
     * @return array{"operation": string, "operator": string, "value": string|array<string>}
     */
    public function dateValuesForIsNotOperation(string $dateSlug): array
    {
        $dateRange = $this->getDateRangeFromSlug($dateSlug);
        [$type, $startDate, $endDate] = $dateRange;

        if ($type === 'single') {
            return $this->dateOperationResponse(DateFilterType::DATE, '!=', $startDate->format('Y-m-d'));
        }

        return $this->dateOperationResponse(DateFilterType::DATE_NOT_BETWEEN, 'not between', $startDate->format('Y-m-d'), $endDate->format('Y-m-d'));
    }

    /**
     * Get date values for 'is_after' operation
     *
     * @param  string  $dateSlug  The date slug
     * @return array{"operation": string, "operator": string, "value": string}
     */
    public function dateValuesForIsAfterOperation(string $dateSlug): array
    {
        $dateRange = $this->getDateRangeFromSlug($dateSlug);
        [$type, $startDate, $endDate] = $dateRange;

        $date = $type === 'single' ? $startDate : $endDate;

        return $this->dateOperationResponse(DateFilterType::DATE, '>', $date->format('Y-m-d'));
    }

    /**
     * Get date values for 'is_before' operation
     *
     * @param  string  $dateSlug  The date slug
     * @return array{"operation": string, "operator": string, "value": string}
     */
    public function dateValuesForIsBeForeOperation(string $dateSlug): array
    {
        $dateRange = $this->getDateRangeFromSlug($dateSlug);
        [$type, $startDate] = $dateRange;

        return $this->dateOperationResponse(DateFilterType::DATE, '<', $startDate->format('Y-m-d'));
    }

    /**
     * Create a date operation response array
     *
     * @param  DateFilterType  $operation  The operation type
     * @param  string  $operator  The SQL operator
     * @param  string  $value1  The first value
     * @param  string|null  $value2  The second value (for ranges)
     * @return array{"operation": string, "operator": string, "value": string|array<string>}
     */
    public function dateOperationResponse(DateFilterType $operation, string $operator, string $value1, ?string $value2 = null): array
    {
        $response = [
            'operation' => $operation->value,
            'operator' => $operator,
            'value' => $value1,
        ];

        if ($value2 !== null) {
            $response['value'] = [$value1, $value2];
        }

        return $response;
    }

    /**
     * Get date range from slug
     *
     * @return array{0: 'single'|'range', 1: Carbon, 2?: Carbon|null}
     */
    protected function getDateRangeFromSlug(string $dateSlug): array
    {
        try {
            $dateEnum = DateSlug::from($dateSlug);

            return $dateEnum->getDateRange();
        } catch (ValueError $e) {
            throw new UnexpectedValueException("Unexpected date slug [{$dateSlug}] provided.");
        }
    }

    /**
     * Apply between filter to the query
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder
     * @param  string  $whereClause  The where clause method prefix ('where' or 'orWhere')
     * @param  string  $columnName  The column name to filter on
     * @param  array<string>  $values  Array containing start and end dates
     * @return Builder<TModel> The query builder with between filter applied
     */
    protected function applyBetweenFilter(Builder $query, string $whereClause, string $columnName, array $values): Builder
    {
        $method = $whereClause . 'Between';

        return $query->$method($columnName, $values);
    }

    /**
     * Apply not between filter to the query
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder
     * @param  string  $whereClause  The where clause method prefix ('where' or 'orWhere')
     * @param  string  $columnName  The column name to filter on
     * @param  array<string>  $values  Array containing start and end dates
     * @return Builder<TModel> The query builder with not between filter applied
     */
    protected function applyNotBetweenFilter(Builder $query, string $whereClause, string $columnName, array $values): Builder
    {
        $method = $whereClause . 'NotBetween';

        return $query->$method($columnName, $values);
    }

    /**
     * Apply single date filter to the query
     *
     * @template TModel of Model
     *
     * @param  Builder<TModel>  $query  The query builder
     * @param  string  $whereClause  The where clause method prefix ('where' or 'orWhere')
     * @param  string  $columnName  The column name to filter on
     * @param  string  $operator  The comparison operator ('=', '!=', '>', '<')
     * @param  string  $value  The date value to compare against
     * @return Builder<TModel> The query builder with date filter applied
     */
    protected function applySingleDateFilter(Builder $query, string $whereClause, string $columnName, string $operator, string $value): Builder
    {
        $method = $whereClause . 'Date';

        return $query->$method($columnName, $operator, $value);
    }

    /**
     * Resolve the where clause operator based on group operation
     *
     * @param  string  $groupOperation  The group operation ('and' or 'or')
     * @return string The where clause method name
     */
    protected function resolveWhereClauseOperator(string $groupOperation): string
    {
        return match ($groupOperation) {
            'and' => 'where',
            'or' => 'orWhere',
            default => throw new UnexpectedValueException("Unexpected group operation [{$groupOperation}] provided."),
        };
    }
}

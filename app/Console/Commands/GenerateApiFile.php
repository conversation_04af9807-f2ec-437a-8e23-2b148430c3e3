<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;

class GenerateApiFile extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-api-file {--url=} {--method=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate API file';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        if (App::isLocal()) {
            $url = $this->option('url');
            $method = $this->option('method');

            if (empty($url)) {
                $url = $this->ask('Enter API Url');
            }

            if (empty($method)) {
                $method = $this->ask('Enter the method');
            }

            $method = strtoupper($method);
            $stagingVariable = '$' . '{stageVariables.ecs_endpoint}';
            $resources = $this->convertUrlToResourceString($url);
            $spiltUrl = explode('/', trim($url, '/'));

            $apiText = '';
            foreach ($resources as $key => $path) {
                if ($path === 'Resource') {
                    continue;
                }

                $resourceArray = array_slice($resources, 0, $key + 1);
                $resourcesParentPath = $key === 1 ? 'ResourceApiId' : implode('', array_slice($resources, 0, $key));
                $resourcesPath = implode('', $resourceArray);
                $methodPath = 'Method' . implode('', array_slice($resources, 1, $key));
                $uri = implode('/', array_slice($spiltUrl, 1, $key));

                $text = "#/api/{$uri}
    {$resourcesPath}:
        DeletionPolicy: \"Delete\"
        Type: \"AWS::ApiGateway::Resource\"
        Properties:
            RestApiId: !Ref ApiGatewayID
            PathPart: \"{$spiltUrl[$key]}\"
            ParentId: !Ref {$resourcesParentPath}
    {$methodPath}01:
        DeletionPolicy: \"Delete\"
        Type: \"AWS::ApiGateway::Method\"
        Properties:
            RestApiId: !Ref ApiGatewayID
            ResourceId: !Ref {$resourcesPath}
            HttpMethod: \"OPTIONS\"
            AuthorizationType: \"NONE\"
            ApiKeyRequired: false
            RequestParameters: {}
            MethodResponses:
                -
                    ResponseModels:
                        \"application/json\": \"Empty\"
                    ResponseParameters:
                        \"method.response.header.Access-Control-Allow-Headers\": false
                        \"method.response.header.Access-Control-Allow-Methods\": false
                        \"method.response.header.Access-Control-Allow-Origin\": false
                    StatusCode: \"200\"
            Integration:
                CacheNamespace: !Ref {$resourcesPath}
                IntegrationResponses:
                    -
                        ResponseParameters:
                            \"method.response.header.Access-Control-Allow-Headers\": \"'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'\"
                            \"method.response.header.Access-Control-Allow-Methods\": \"'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'\"
                            \"method.response.header.Access-Control-Allow-Origin\": \"'*'\"
                        StatusCode: \"200\"
                PassthroughBehavior: \"WHEN_NO_MATCH\"
                RequestTemplates:
                    \"application/json\": \"{\\\"statusCode\\\": 200}\"
                TimeoutInMillis: 29000
                Type: \"MOCK\"";
                if ($key === count($resources) - 1) {
                    $text = $text . "
    {$methodPath}02:
        DeletionPolicy: \"Delete\"
        Type: \"AWS::ApiGateway::Method\"
        Properties:
            RestApiId: !Ref ApiGatewayID
            ResourceId: !Ref {$resourcesPath}
            HttpMethod: \"{$method}\"
            AuthorizationType: \"CUSTOM\"
            AuthorizerId: !Ref ApiGatewayAuthorizer
            ApiKeyRequired: false
            MethodResponses:
                -
                    ResponseModels:
                        \"application/json\": \"Empty\"
                    StatusCode: \"200\"
            Integration:
                CacheNamespace: !Ref {$resourcesPath}
                ConnectionType: \"INTERNET\"
                IntegrationHttpMethod: \"{$method}\"
                IntegrationResponses:
                    -
                        ResponseTemplates: {}
                        StatusCode: \"200\"
                PassthroughBehavior: \"WHEN_NO_MATCH\"
                TimeoutInMillis: 29000
                Type: \"HTTP_PROXY\"
                Uri: \"https://{$stagingVariable}/{$uri}\"";
                }

                $apiText = $apiText . "\n\n" . $text;
            }

            $fileName = $resources[count($resources) - 1] . '-' . $method . '.txt';

            // Write content to the file
            Storage::disk('local')->put($fileName, $apiText);
        } else {
            $this->info('This command only executes in the local environment.');
        }
    }

    /**
     * @return array<int, string>
     */
    public function convertUrlToResourceString(string $url): array
    {
        // Remove slashes
        $splicedUrl = explode('/', trim($url, '/'));

        foreach ($splicedUrl as $key => $slice) {
            $slice = str_replace('-', '', $slice);

            if ($slice === 'api') {
                $splicedUrl[$key] = 'Resource';

                continue;
            }

            // Check if the string starts with '{' and ends with '}'
            if (strpos($slice, '{') === 0 && strrpos($slice, '}') === strlen($slice) - 1) {
                $splicedUrl[$key] = ucfirst(str_replace('_', '', substr($slice, 1, -1)));

                continue;
            }

            $splicedUrl[$key] = ucfirst(strtolower($slice));
        }

        return $splicedUrl;
    }
}

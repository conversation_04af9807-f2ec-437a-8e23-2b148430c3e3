<?php

namespace App\Console\Commands\Appfolio;

use App\Enums\WorkOrderStatus;
use App\Helpers\Helper;
use App\Jobs\AttachMediaToWorkOrderJob;
use App\Models\WorkOrder;
use App\Models\WorkOrderActivityLog;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AppfolioAdditionalDataSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:appfolio-additional-data-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Appfolio additional data to the work orders';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $appfolioActiveWorkOrders = WorkOrder::join('work_order_sources', 'work_order_sources.work_order_source_id', '=', 'work_orders.work_order_source_id')
            ->join('work_order_statuses', 'work_order_statuses.work_order_status_id', '=', 'work_orders.work_order_status_id')
            ->whereIn('work_order_statuses.slug', [
                WorkOrderStatus::NEW,
                WorkOrderStatus::SCOPING,
                WorkOrderStatus::READY_TO_SCHEDULE,
            ])
            ->where('work_order_sources.slug', 'appfolio')
            ->where(function ($query) {
                return $query->whereNull('work_orders.work_order_reference_url')
                    ->orDoesntHave('media');
            })
            ->where('work_orders.created_at', '>', Carbon::now()->subHours(8))
            ->with('organization', 'media')
            ->orderBy('work_orders.created_at', 'desc')
            ->get();

        foreach ($appfolioActiveWorkOrders as $workOrder) {
            $this->syncAdditionalInfo($workOrder);
        }
    }

    /**
     * Sync additional details from appfolio
     */
    protected function syncAdditionalInfo(WorkOrder $workOrder): void
    {
        try {
            Log::channel('appfolio')->info("Appfolio work order additional data sync started.. work_order_id => {$workOrder->work_order_id}");
            $workOrder->loadMissing('tasks', 'tasks.serviceCalls');

            $scrapperApiUrl = config('appfolio.scraper_api_url');

            if (empty($workOrder->organization->appfolio_customer_id) || empty($workOrder->work_order_reference_number)) {
                Log::channel('appfolio')->info("Appfolio work order additional data sync skiped.. work_order_id => {$workOrder->work_order_id}");
            } else {

                $response = Http::withHeaders([
                    'accept' => 'application/json',
                    'content-type' => 'application/json',
                ])->get($scrapperApiUrl, [
                    'appfolio_customer_id' => $workOrder->organization->appfolio_customer_id,
                    'numberForDisplay' => $workOrder->work_order_reference_number,
                ]);

                if ($response->successful()) {
                    $additionalData = $response->json('result');

                    if (! empty($additionalData) && ! empty($additionalData['work_order_reference_url'])) {
                        $this->updateJobDetails($workOrder, $additionalData);
                        Log::channel('appfolio')->info("Appfolio work order additional data sync successfully completed.. work_order_id => {$workOrder->work_order_id}");

                    } else {
                        $additionalData['work_order_uuid'] = $workOrder->work_order_uuid;
                        $additionalData['work_order_id'] = $workOrder->work_order_id;
                        $additionalData['organization'] = $workOrder->organization->name;

                        Helper::developerAlert($additionalData, 'Additional data is not available.');

                        Log::channel('appfolio')->info("Appfolio work order additional data sync failed.. work_order_id => {$workOrder->work_order_id}", [
                            'additional_data' => $additionalData,
                            'work_order_id' => $workOrder->work_order_id,
                        ]);
                    }
                }
            }
        } catch (Exception $e) {
            Log::channel('appfolio')->error('Appfolio work order additional data sync failed on syncAdditionalInfo!', [
                'work_order_id' => $workOrder->work_order_id,
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
            Helper::exceptionLog($e, ['work_order_id' => $workOrder->work_order_id], 'Appfolio work order additional data sync failed on syncAdditionalInfo!');
        }
        Log::channel('appfolio')->info("Appfolio work order additional data sync finished.. work_order_id => {$workOrder->work_order_id}");
    }

    /**
     * @param  array<string,mixed>  $additionalData
     */
    protected function updateJobDetails(WorkOrder $workOrder, array $additionalData): void
    {
        try {

            // if (empty($workOrder->work_order_reference_url) && ! empty($additionalData['specialInstructions'])) {
            //     $workOrder->description .= PHP_EOL . strip_tags($additionalData['specialInstructions']);
            // }

            if (empty($workOrder->work_order_reference_url) && ! empty($additionalData['work_order_reference_url'])) {
                $workOrder->work_order_reference_url = $additionalData['work_order_reference_url'];
                $workOrderActivityLog = WorkOrderActivityLog::where('work_order_id', $workOrder->work_order_id)
                    ->where('event_attributes', 'like', '%imported_from%')
                    ->orderBy('work_order_activity_log_id', 'asc')
                    ->first();

                if (! empty($workOrderActivityLog)) {
                    $eventAttributes = $workOrderActivityLog->event_attributes;
                    $eventAttributes['work_order_reference_url'] = $workOrder->work_order_reference_url;

                    $workOrderActivityLog->event_attributes = $eventAttributes;
                    $workOrderActivityLog->save();
                }

            }

            if ($workOrder->isDirty(['work_order_reference_url'])) {
                $workOrder->save();
            }

            if (! Cache::has("appfolio_data_sync_{$workOrder->work_order_uuid}")) {
                if (count($workOrder->media) == 0 && ! empty($additionalData['attachments'])) {
                    $this->uploadWorkOrderImages($workOrder, $additionalData['attachments']);
                    Cache::set("appfolio_data_sync_{$workOrder->work_order_uuid}", 1, 300);
                }
            }

        } catch (Exception $e) {
            Log::channel('appfolio')->error('Scrapper API additional data updateJobDetails failed to sync!', [
                'exception' => $e->getMessage(),
                'code' => $e->getCode(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
            ]);
            Helper::exceptionLog($e, [
                'work_order_id' => $workOrder->work_order_id,
                'additionalData' => $additionalData,
            ], 'Scrapper API additional data updateJobDetails failed to sync!');
        }
    }

    /**
     * @param  array<int,string>  $attachments
     */
    protected function uploadWorkOrderImages(WorkOrder $workOrder, array $attachments): void
    {
        foreach ($attachments as $attachmentUrl) {
            dispatch(new AttachMediaToWorkOrderJob($attachmentUrl, $attachmentUrl, $workOrder->organization, $workOrder))->onQueue('attachment');
        }
    }
}

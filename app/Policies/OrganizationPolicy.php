<?php

namespace App\Policies;

use App\Enums\Feature;
use App\Models\Organization;
use App\Models\User;
use App\Traits\HandleFeatureAuthorization;

class OrganizationPolicy
{
    use HandleFeatureAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): void
    {
        //
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Organization $organization): void
    {
        //
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): void
    {
        //
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Organization $organization): void
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Organization $organization): void
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Organization $organization): void
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Organization $organization): void
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function templateView(User $user): bool
    {
        if ($this->doesNotHaveFeature($user, Feature::ORGANIZATION_SETTINGS)) {
            return false;
        }

        if ($user->cant('Organization.Template.update')) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function templateUpdate(User $user): bool
    {
        if ($this->doesNotHaveFeature($user, Feature::ORGANIZATION_SETTINGS)) {
            return false;
        }

        if ($user->cant('Organization.Template.update')) {
            return false;
        }

        return true;
    }
}

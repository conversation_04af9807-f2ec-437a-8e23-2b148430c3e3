<?php

namespace App\Policies;

use App\Enums\Feature;
use App\Models\Invoice;
use App\Models\User;
use App\Models\WorkOrder;
use App\Traits\HandleFeatureAuthorization;

class InvoicePolicy
{
    use HandleFeatureAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        if ($this->doesNotHaveFeature($user, Feature::INVOICE_MANAGEMENT)) {
            return false;
        }

        if ($user->cant('InvoiceManagement.Invoice.list')) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Invoice $invoice): bool
    {
        if ($this->doesNotHaveFeature($user, Feature::INVOICE_MANAGEMENT)) {
            return false;
        }

        if ($user->organization_id !== $invoice->organization_id) {
            return false;
        }

        if ($user->cant('InvoiceManagement.Invoice.view')) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user, WorkOrder $workOrder): bool
    {
        if ($this->doesNotHaveFeature($user, Feature::INVOICE_MANAGEMENT)) {
            return false;
        }

        if ($user->cant('InvoiceManagement.Invoice.create')) {
            return false;
        }

        if ($user->organization_id != $workOrder->organization_id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Invoice $invoice): void
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Invoice $invoice): bool
    {

        if ($this->doesNotHaveFeature($user, Feature::INVOICE_MANAGEMENT)) {
            return false;
        }

        if ($user->cant('InvoiceManagement.Invoice.delete')) {
            return false;
        }

        if ($user->organization_id !== $invoice->organization_id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Invoice $invoice): void
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Invoice $invoice): void
    {
        //
    }

    /**
     * Determine whether the user can use fully paid action.
     */
    public function fullyPaid(User $user, Invoice $invoice): bool
    {
        if ($this->doesNotHaveFeature($user, Feature::INVOICE_MANAGEMENT)) {
            return false;
        }

        if ($user->cant('InvoiceManagement.Invoice.processPayment')) {
            return false;
        }

        if ($user->organization_id !== $invoice->organization_id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can use partially paid action.
     */
    public function partiallyPaid(User $user, Invoice $invoice): bool
    {
        if ($this->doesNotHaveFeature($user, Feature::INVOICE_MANAGEMENT)) {
            return false;
        }

        if ($user->cant('InvoiceManagement.Invoice.processPayment')) {
            return false;
        }

        if ($user->organization_id !== $invoice->organization_id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can use voided action.
     */
    public function voided(User $user, Invoice $invoice): bool
    {
        if ($this->doesNotHaveFeature($user, Feature::INVOICE_MANAGEMENT)) {
            return false;
        }

        if ($user->cant('InvoiceManagement.Invoice.void')) {
            return false;
        }

        if ($user->organization_id !== $invoice->organization_id) {
            return false;
        }

        return true;
    }
}

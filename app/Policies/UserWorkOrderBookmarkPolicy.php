<?php

namespace App\Policies;

use App\Enums\Feature;
use App\Models\User;
use App\Models\UserWorkOrderBookmark;
use App\Traits\HandleFeatureAuthorization;

class UserWorkOrderBookmarkPolicy
{
    use HandleFeatureAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        if ($this->doesNotHaveFeature($user, Feature::WORK_ORDER_MANAGEMENT)) {
            return false;
        }

        if ($user->cant('WorkOrderManagement.WorkOrderBookmark.list')) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, UserWorkOrderBookmark $userWorkOrderBookmark): void
    {
        //
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        if ($this->doesNotHaveFeature($user, Feature::WORK_ORDER_MANAGEMENT)) {
            return false;
        }

        if ($user->cant('WorkOrderManagement.WorkOrderBookmark.create')) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, UserWorkOrderBookmark $userWorkOrderBookmark): void
    {
        //
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, UserWorkOrderBookmark $userWorkOrderBookmark): void
    {
        //
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, UserWorkOrderBookmark $userWorkOrderBookmark): void
    {
        //
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, UserWorkOrderBookmark $userWorkOrderBookmark): void
    {
        //
    }
}

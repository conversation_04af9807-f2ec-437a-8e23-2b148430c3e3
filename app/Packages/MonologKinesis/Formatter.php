<?php

namespace App\Packages\MonologKinesis;

use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;
use Monolog\Formatter\NormalizerFormatter;
use Monolog\LogRecord;

class Formatter extends NormalizerFormatter
{
    use ApplicationAwareFormatter;

    public const SIMPLE_DATE = 'Y-m-d\TH:i:s.uP';

    /**
     * Formats a log record.
     *
     * @param  LogRecord  $record  A record to format
     * @return mixed The formatted record
     */
    public function format(LogRecord $record)
    {
        $record = parent::format($record);

        return [
            'Data' => $this->toJson([
                'timestamp' => $record['datetime'] ?? gmdate('c'),
                'request_id' => Request::get('request_uuid'),
                'host' => gethostname(),
                'project' => $this->name,
                'env' => $this->environment,
                'message' => $record['message'],
                'channel' => $record['channel'],
                'level' => $record['level_name'],
                'extra' => $record['extra'],
                'context' => $record['context'],
            ]),
            'PartitionKey' => Str::of($this->name)
                ->append('_' . $this->environment)
                ->lower()
                ->snake(),
        ];
    }

    /**
     * Formats a set of log records.
     *
     * @param  array<mixed>  $records  A set of records to format
     * @return mixed The formatted set of records
     */
    public function formatBatch(array $records)
    {
        return [
            'Records' => collect($records)->map([$this, 'format'])->toArray(),
        ];
    }
}

<?php

namespace App\Packages\OrganizationRolePermission;

use App\Models\Organization;
use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use App\Packages\OrganizationRolePermission\Exceptions\OrganizationNotFound;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Access\Authorizable;
use Illuminate\Contracts\Auth\Access\Gate;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class OrganizationUserManager
{
    /**
     * @var Collection<int, Permission>|null
     */
    public ?Collection $permissions = null;

    /**
     * @var Collection<int, Role>|null
     */
    public ?Collection $roles = null;

    protected ?Organization $organization;

    protected ?User $user = null;

    /**
     * @var array<int|string, array<int|string>>
     */
    protected array $preparePermissions = [];

    protected mixed $cachedData = [];

    /**
     * @var string[]|int[]
     */
    private array $alias = [];

    /**
     * @var array|string[]
     */
    private array $except = ['created_at', 'updated_at', 'deleted_at'];

    public function getUser(): ?User
    {
        return $this->user;
    }

    /**
     * @return $this
     */
    public function setUser(User $user): static
    {
        $this->user = $user;

        if (! $user->organization) {
            throw new OrganizationNotFound('Organization not found');
        }
        $this->setOrganization($user->organization);

        return $this;
    }

    /**
     * Register the permission check method on the gate.
     * We resolve the Gate fresh here, for benefit of long-running instances.
     */
    public function registerPermissions(Gate $gate): bool
    {
        $gate->before(function (Authorizable $user, string $ability) {
            if (method_exists($user, 'checkPermissionTo')) {
                return $user->checkPermissionTo($ability) ?: null;
            }
        });

        return true;
    }

    public function loadOrganizationUserRolePermissions(): void
    {
        if ($this->permissions) {
            return;
        }

        $this->cachedData = Cache::remember($this->getCacheKey(), $this->getCacheKeyExpiry(), function () {
            $user = $this->getUser();

            if (! $user) {
                return [];
            }

            // Check if organization has cached roles and permissions
            if ($this->organization) {
                // Try to use the OrganizationManager's tryLoadFromCache method
                $organizationManager = app(OrganizationManager::class);
                $organizationManager->setOrganization($this->organization);

                if ($organizationManager->tryLoadFromCache()) {
                    // If organization data is cached, use its alias
                    $this->alias = $organizationManager->getAlias();
                }
            }

            $user->loadMissing('roles.permissions');

            $roles = $user->roles->map(function (Role $role) {
                if (! $this->alias) {
                    $this->aliasModelFields($role);
                }

                return $this->aliasedArray($role) + $this->getSerializedRoleRelation($role);

            })->all();

            $permissions = array_values($this->preparePermissions);
            $this->preparePermissions = [];

            return ['alias' => array_flip($this->alias)] + ['permissions' => $permissions, 'roles' => $roles];
        });

        $this->alias = ! empty($this->cachedData['alias']) ? $this->cachedData['alias'] : [];

        $this->hydratePermissionCache();

        $this->roles = $this->getHydratedRolePermissionCollection();

        $this->checkOrganizationEnabledRolePermission();

        $this->alias = $this->except = [];
    }

    /**
     * Get the permissions based on the passed params.
     *
     * @param  array<int|string>  $params
     * @return Collection<int, Permission>
     */
    public function getPermissions(array $params = []): Collection
    {
        $this->loadOrganizationUserRolePermissions();

        return $this->filterPermissions($params);
    }

    /**
     * Get the permissions based on the passed params.
     *
     * @param  array<string|int>  $params
     * @return Collection<int, Role>
     */
    public function getRoles(array $params = []): Collection
    {
        $this->loadOrganizationUserRolePermissions();

        return $this->filterRoles($params);
    }

    public function getCacheKey(): string
    {
        $user = $this->getUser();

        return $this->getCachePrefix((string) $user?->user_id);
    }

    public function getCachePrefix(string $append = ''): string
    {
        return "organization_user_role_permission:{$append}";
    }

    /**
     * @param  \Illuminate\Database\Eloquent\Collection<int, User>  $users
     */
    public function forgotCacheForUsers(\Illuminate\Database\Eloquent\Collection $users): void
    {
        foreach ($users as $user) {
            $this->forgotCacheByKey($this->getCachePrefix((string) $user->user_id));
        }
    }

    public function getCacheKeyExpiry(): Carbon
    {
        return Carbon::now()->addDay();
    }

    /**
     * @return $this
     */
    public function setOrganization(Organization $organization): OrganizationUserManager
    {
        $this->organization = $organization;

        $this->clearCacheCollection();

        $this->loadOrganizationUserRolePermissions();

        return $this;
    }

    /**
     * Clear already loaded permissions' collection.
     * This is only intended to be called by the PermissionServiceProvider on boot,
     * so that long-running instances like Swoole don't keep old data in memory.
     */
    public function clearCacheCollection(): void
    {
        $this->permissions = null;
        $this->roles = null;
    }

    /**
     * Flush the cache.
     */
    public function forgetCachedPermissions(): bool
    {
        $this->permissions = null;
        $this->roles = null;

        return $this->forgotCacheByKey($this->getCacheKey());
    }

    protected function forgotCacheByKey(string $key): bool
    {
        return Cache::forget($key);
    }

    protected function checkOrganizationEnabledRolePermission(): void
    {
        if (! $this->organization) {
            return;
        }

        // Get organization roles and permissions
        $organizationRoles = $this->organization->getCachedRoles();
        $organizationPermissions = $this->organization->getCachedPermissions();

        if ($this->permissions) {
            $this->permissions = $this->permissions->filter(function (Permission $permission) use ($organizationPermissions) {
                return $organizationPermissions->contains($permission->getKeyName(), $permission->getKey());
            });
        }

        if ($this->roles) {
            $this->roles = $this->roles->filter(function (Role $role) use ($organizationRoles) {
                return $organizationRoles->contains($role->getKeyName(), $role->getKey());
            });
        }
    }

    /**
     * @param  array<int|string>  $params
     * @return Collection<int, Permission>
     */
    protected function filterPermissions(array $params): Collection
    {
        $this->loadOrganizationUserRolePermissions();

        if (! $this->permissions) {
            return collect();
        }

        return $this->permissions->filter(static function ($permission) use ($params) {
            foreach ($params as $attr => $value) {
                if ($permission->getAttribute($attr) != $value) {
                    return false;
                }
            }

            return true;
        });
    }

    /**
     * @param  array<int|string>  $params
     * @return Collection<int, Role>
     */
    protected function filterRoles(array $params): Collection
    {
        $this->loadOrganizationUserRolePermissions();

        if (! $this->roles) {
            return collect();
        }

        return $this->roles->filter(static function ($role) use ($params) {
            foreach ($params as $attr => $value) {
                if ($role->getAttribute($attr) != $value) {
                    return false;
                }
            }

            return true;
        });
    }

    /**
     * Array for cache alias
     */
    private function aliasModelFields(mixed $newKeys = []): void
    {
        $i = 0;
        $alphas = ! count($this->alias) ? range('a', 'h') : range('j', 'o');

        foreach (array_keys($newKeys->getAttributes()) as $value) {
            if (! isset($this->alias[$value])) {
                $this->alias[$value] = $alphas[$i++] ?? $value;
            }
        }

        $this->alias = array_diff_key($this->alias, array_flip($this->except));
    }

    /**
     * Changes array keys with alias
     *
     * @param  Model|array<int|string>  $model
     * @return array<int>
     */
    private function aliasedArray(Model|array $model): array
    {
        return collect(is_array($model) ? $model : $model->getAttributes())->except($this->except)
            ->keyBy(fn ($value, $key) => $this->alias[$key] ?? $key)
            ->all();
    }

    /**
     * @return array<string|array<int, mixed>>
     */
    private function getSerializedRoleRelation(Role $role): array
    {
        if (! $role->permissions->count()) {
            return [];
        }

        if (! isset($this->alias['permissions'])) {
            $this->alias['permissions'] = 'p';
            $this->aliasModelFields($role->permissions[0]);
        }

        return [
            'p' => $role->permissions->map(function ($permission) {
                if (! isset($this->preparePermissions[$permission->getKey()])) {
                    $this->preparePermissions[$permission->getKey()] = $this->aliasedArray($permission);
                }

                return $permission->getKey();
            })->all(),
        ];
    }

    /**
     * @return Collection<int, Role>
     */
    private function getHydratedRolePermissionCollection(): Collection
    {
        return Collection::make(array_map(function ($item) {
            $roleInstance = new Role;

            return $roleInstance->newFromBuilder($this->aliasedArray(Arr::except($item, 'p')))
                ->setRelation('permissions', $this->getHydratedPermissionCollection($item['p'] ?? []));
        }, $this->cachedData['roles'] ?? []));

    }

    /**
     * @param  array<int>  $permissions
     * @return Collection<int, Permission>
     */
    private function getHydratedPermissionCollection(array $permissions): Collection
    {
        return $this->permissions ? $this->permissions->whereIn('permission_id', $permissions) : collect();
    }

    private function hydratePermissionCache(): void
    {
        $permissionInstance = new Permission;

        if (! isset($this->cachedData['permissions'])) {
            return;
        }

        $permissions = [];

        array_map(function ($item) use ($permissionInstance, &$permissions) {
            $permission = $permissionInstance->newFromBuilder($this->aliasedArray($item));
            $permissions[(int) $permission->getKey()] = $permission;
        }, $this->cachedData['permissions']);

        $this->cachedData['permissions'] = [];

        $this->permissions = Collection::make($permissions);
    }
}

<?php

namespace App\Packages\GoogleMap\Services;

use App\Helpers\Helper;
use App\Packages\GoogleMap\Exceptions\GeocodeException;
use App\Packages\GoogleMap\GoogleApiServiceInterface;
use Carbon\CarbonImmutable;
use Exception;
use GoogleMaps\WebService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class Geocode implements GoogleApiServiceInterface
{
    public function __construct(public WebService $client) {}

    /**
     * @return Collection<string, mixed>
     */
    public function getGeocodeFromAddress(string $address): Collection
    {
        try {
            return Cache::remember(Str::slug($address), CarbonImmutable::now()->addMinutes(10), function () use ($address) {
                $geocodingResponse = $this->client
                    ->setParamByKey('address', $address)
                    ->get();

                if (! is_string($geocodingResponse)) {
                    throw GeocodeException::invalidGeocodeResponse();
                }

                $geocoding_response = json_decode($geocodingResponse, false, 512, JSON_THROW_ON_ERROR);

                $location = $geocoding_response->results[0];

                return collect($this->formatLocationDetails($location));
            });

        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);
        }

        return collect();
    }

    /**
     * @return string[]
     */
    protected function formatLocationDetails(object $location): array
    {
        $locationData = [
            'street_number' => '',
            'route' => '',
            'locality' => '',
            'state_short' => '',
            'zip_code' => '',
            'country_short' => '',
            'latitude' => '',
            'longitude' => '',
        ];

        if (empty($location->address_components)) {
            return $locationData;
        }

        foreach ($location->address_components as $component) {
            $type = $component->types[0];

            switch ($type) {
                case 'street_number':
                    $locationData['street_number'] = $component->short_name;
                    break;
                case 'route':
                    $locationData['route'] = $component->short_name;
                    break;
                case 'locality':
                    $locationData['locality'] = $component->long_name;
                    break;
                case 'administrative_area_level_1':
                    $locationData['state'] = $component->long_name;
                    $locationData['state_short'] = $component->short_name;
                    break;
                case 'country':
                    $locationData['country'] = $component->long_name;
                    $locationData['country_short'] = $component->short_name;
                    break;
                case 'postal_code':
                    $locationData['zip_code'] = $component->long_name;
                    break;
            }
        }

        // 6266 West 157th Street, Overland Park, KS 66223, US
        $formattedAddress[] = trim($locationData['street_number'] . ' ' . $locationData['route']);
        foreach (['locality', 'state_short', 'zip_code', 'country_short'] as $keyword) {
            if (! empty($locationData[$keyword])) {
                $formattedAddress[] = $locationData[$keyword];
            }
        }

        $locationData['address'] = implode(', ', array_filter($formattedAddress));

        if (! empty($location->geometry) && ! empty($location->geometry->location)) {
            $locationData['latitude'] = $location->geometry->location->lat;
            $locationData['longitude'] = $location->geometry->location->lng;
        }

        if (! empty($location->place_id)) {
            $locationData['place_id'] = $location->place_id;
        }

        $locationData['full_response'] = $location;

        return $locationData;
    }
}

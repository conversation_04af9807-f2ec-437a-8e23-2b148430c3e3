<?php

namespace App\Packages\DBLogger;

use App\Models\DeveloperAlert;
use Exception;
use Illuminate\Support\Facades\Log;
use Monolog\Handler\AbstractProcessingHandler;
use Monolog\LogRecord;
use Throwable;

class DatabaseHandler extends AbstractProcessingHandler
{
    /**
     * {@inheritDoc}
     */
    protected function write(LogRecord $record): void
    {
        if (! empty($record['context']) && is_array($record['context']) && ! empty($record['context']['exception'])) {
            $exception = $record['context']['exception'];

            if ($exception instanceof Throwable) {
                $record['context']['exception'] = (string) $exception;
            }
        }

        try {
            $developerAlert = DeveloperAlert::updateOrCreate([
                'message' => $record['message'],
                'level' => $record['level'],
                'organization_id' => request()->user()?->organization_id,
            ], [
                'level' => $record['level'],
                'level_name' => $record['level_name'],
                'message' => $record['message'],
                'logged_at' => $record['datetime'],
                'context' => $record['context'],
                'extra' => $record['extra'],
            ]);

            if ($developerAlert->wasRecentlyCreated === false) {
                $developerAlert->increment('occurrence');
            }
        } catch (Exception $e) {
            Log::debug('Could not log to the database.', [
                'exception' => $e,
            ]);
        }
    }
}

<?php

namespace App\Http\Resources\Notification;

use App\Http\Resources\WorkOrder\PriorityResource;
use App\Models\DatabaseNotification;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin DatabaseNotification
 */
class NotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return ! is_null($this->work_order_id)
            ? $this->buildData($this->getWorkOrderDetails())
            : $this->buildData($this->getServiceRequestDetails());
    }

    /**
     * Build the base structure of the notification data.
     *
     * @return array<string, mixed>
     */
    private function buildData(array $objectDetails): array
    {
        return [
            'notification_id' => $this->notification_uuid,
            'user' => $this->getUserData(),
            'data' => $this->data,
            'type' => $this->type,
            'read_at' => $this->read_at?->toIso8601String(),
            'cleared_at' => $this->cleared_at?->toIso8601String(),
            'created_at' => $this->created_at?->toIso8601String(),
        ] + $objectDetails;
    }

    /**
     * Get details related to Work Order.
     *
     * @return array<string, mixed>
     */
    private function getWorkOrderDetails(): array
    {
        $task = $this->workOrder?->tasks->first();

        return [
            'work_order' => [
                'work_order_id' => $this->workOrder?->work_order_uuid,
                'address' => $this->workOrder?->property?->getAddress(),
                'priority' => new PriorityResource($this->workOrder?->priority),
                'work_order_number' => $this->workOrder?->work_order_number,
                'problem_category' => $task?->problemDiagnosis->subCategory->problemCategory->label ?? null,
                'timezone' => $this->workOrder->timezone->name ?? '',
            ],
        ];
    }

    /**
     * Get details related to Service Request.
     *
     * @return array<string, mixed>
     */
    private function getServiceRequestDetails(): array
    {
        $categories = is_array($this->serviceRequest?->categories)
            ? collect($this->serviceRequest->categories)
            : $this->serviceRequest->categories;

        $category = $categories?->first();

        return [
            'service_request' => [
                'service_request_id' => $this->serviceRequest?->service_request_uuid,
                'address' => $this->serviceRequest?->property?->getAddress(),
                'priority' => new PriorityResource($this->serviceRequest?->priority),
                'service_request_number' => $this->serviceRequest?->service_request_number,
                'problem_category' => $category?->problemCategory?->label ?? null,
                'timezone' => $this->serviceRequest->timezone->name ?? '',
            ],
        ];
    }

    /**
     * Get user-related data.
     *
     * @return array<string, mixed>
     */
    private function getUserData(): array
    {
        return [
            'user_id' => $this->user?->user_uuid,
            'name' => $this->user?->getName(),
        ];
    }
}

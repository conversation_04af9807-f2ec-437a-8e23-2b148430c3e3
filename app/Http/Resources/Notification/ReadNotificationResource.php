<?php

namespace App\Http\Resources\Notification;

use App\Models\DatabaseNotification;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin DatabaseNotification
 */
class ReadNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'notification_id' => $this->notification_uuid,
            'read_at' => $this->read_at?->toIso8601String(),
        ];
    }
}

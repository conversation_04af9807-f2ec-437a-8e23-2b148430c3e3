<?php

declare(strict_types=1);

namespace App\Http\Resources\WorkOrderIssue;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\WorkOrderIssue
 */
class WorkOrderIssueListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $response = [
            'issue_id' => $this->work_order_issue_uuid,
        ];

        return $response;
    }
}

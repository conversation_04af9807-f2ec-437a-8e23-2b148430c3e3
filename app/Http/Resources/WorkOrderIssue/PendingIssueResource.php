<?php

namespace App\Http\Resources\WorkOrderIssue;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\WorkOrderIssue
 */
class PendingIssueResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'issue_id' => $this->work_order_issue_uuid,
            'status' => new WorkOrderIssueStatusResource($this->state),
            'abilities' => $this->abilities($request->requestSource()),
        ];
    }
}

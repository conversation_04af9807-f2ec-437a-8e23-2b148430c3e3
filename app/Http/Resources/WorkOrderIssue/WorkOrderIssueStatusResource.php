<?php

declare(strict_types=1);

namespace App\Http\Resources\WorkOrderIssue;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\States\WorkOrderIssue\WorkOrderIssueState
 */
class WorkOrderIssueStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $responseData = [
            'value' => $this->getValue(),
            'label' => ($request->expectsDevice()) ? $this->labelForMobile() : $this->label(),
        ];

        if ($request->expectsDevice()) {
            $responseData['color_class'] = $this->colorClassForMobile();
        }

        return $responseData;
    }
}

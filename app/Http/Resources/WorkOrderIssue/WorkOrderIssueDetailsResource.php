<?php

namespace App\Http\Resources\WorkOrderIssue;

use App\Http\Resources\WorkOrder\MediaResource;
use App\States\WorkOrderIssue\Canceled;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\WorkOrderIssue
 */
class WorkOrderIssueDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'issue_id' => $this->work_order_issue_uuid,
            'title' => $this->issue->title,
            'description' => $this->issue->description,
            'decline_reason' => $this->decline_reason,
            'status' => new WorkOrderIssueStatusResource($this->state),
            'service_notes' => $this->service_notes,
            $this->mergeWhen($this->materials->isNotEmpty(), [
                'materials' => MaterialResource::collection($this->materials),
            ]),
            'issue_caused_by_resident' => $this->issue_caused_by_resident ? true : false,
            $this->mergeWhen(! empty($this->issue_caused_by_resident), [
                'issue_caused_details' => $this->issue_caused_details,
            ]),
            'media' => MediaResource::collection($this->media),
            'abilities' => $this->abilities($request->requestSource()),
            'is_editable' => ! $this->state->equals(Canceled::class),
        ];
    }
}

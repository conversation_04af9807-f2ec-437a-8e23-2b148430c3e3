<?php

namespace App\Http\Resources\WorkOrderIssue;

use App\Enums\CostTypes;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Material
 */
class MaterialResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'material_id' => $this->material_uuid,
            'label' => $this->label,
            'quantity' => $this->quantity,
            'quantity_type' => $this->quantity_type,
            'cost_type' => $this->cost_type,
            $this->mergeWhen($this->cost_type == CostTypes::PER_UNIT(), [
                'unit_price_in_cents' => $this->unit_price_in_cents,
            ]),
            $this->mergeWhen($this->cost_type == CostTypes::TOTAL(), [
                'cost_in_cents' => $this->cost_in_cents,
            ]),
            'total_cost_in_cents' => $this->total_cost_in_cents,
        ];
    }
}

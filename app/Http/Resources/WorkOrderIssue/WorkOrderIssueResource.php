<?php

declare(strict_types=1);

namespace App\Http\Resources\WorkOrderIssue;

use App\Http\Resources\WorkOrder\MediaResource;
use App\Http\Resources\WorkOrder\TripStatusResource;
use App\Models\WorkOrder;
use App\States\Issue\Done;
use App\States\ServiceCalls\MissingInfo;
use App\States\WorkOrderIssue\Unresolved;
use App\States\WorkOrders\QualityCheck;
use App\States\WorkOrders\ReadyToInvoice;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\WorkOrderIssue
 */
class WorkOrderIssueResource extends JsonResource
{
    public static function detailCollection($collection): \Illuminate\Support\Collection
    {
        return $collection->map(function ($issue) {
            $data = (new static($issue))->toArray(request());
            unset($data['abilities'], $data['work_orders']);

            return $data;
        });
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $response = [
            'issue_id' => $this->work_order_issue_uuid,
            'title' => $this->issue->title,
            'status' => new WorkOrderIssueStatusResource($this->state),
            'description' => $this->issue->description,
            'service_notes' => $this->service_notes,
            'issue_caused_details' => $this->issue_caused_details,
            'decline_reason' => $this->decline_reason,
            'state_updated_at' => $this->state_updated_at,
            'created_at' => $this->created_at,
            'abilities' => $this->abilities($request->expectsDevice() ? 'mobile' : 'web'),
            'media' => $this->when($this->relationLoaded('media'),
                fn () => MediaResource::collection($this->media)
            ),
            'updated_at' => $this->issue->updated_at,
        ];

        if (! $request->expectsDevice()) {
            $response = array_merge($response, [
                'work_orders' => $this->issue->workOrders->map(function ($workOrder) {
                    return [
                        'work_order_id' => $workOrder->work_order_uuid,
                        'work_order_number' => $workOrder->work_order_number,
                        'issues_count' => $workOrder->issues_count,
                    ];
                }),
                'problem' => [
                    'category' => [
                        'category_id' => $this->issue->problemDiagnosis->subCategory->problemCategory->problem_category_uuid ?? null,
                        'label' => $this->issue->problemDiagnosis->subCategory->problemCategory->label ?? null,
                    ],
                    'sub_category' => [
                        'sub_category_id' => $this->issue->problemDiagnosis->subCategory->problem_sub_category_uuid ?? null,
                        'label' => $this->issue->problemDiagnosis->subCategory->label ?? null,
                    ],
                    'diagnosis' => [
                        'diagnosis_id' => $this->issue->problemDiagnosis->problem_diagnosis_uuid ?? null,
                        'label' => $this->issue->problemDiagnosis->label ?? null,
                    ],
                ],
            ]);

            // The work order trips list must be displayed when the work order status is quality_check and invoicing only,
            // and when the issue status is quality_check, done, missing_info, or unresolved.
            // TODO: Issue status can also be quoted, but this is not yet implemented.
            // $currentWorkOrder = WorkOrder::where('work_order_id', $this->work_order_id)->first();
            // if (
            //     $currentWorkOrder
            //     && $currentWorkOrder->state->equals(QualityCheck::class, ReadyToInvoice::class)
            //     && $currentWorkOrder->latestTrips->isNotEmpty()
            //     && $this->issue->state->equals(QualityCheck::class, Done::class, MissingInfo::class, Unresolved::class)
            // ) {
            //     // Fetch the trips from the current work order having the same issue
            //     $response['trips'] = $this->mapTrips(
            //         $this->issue->workOrders->where('work_order_id', $this->work_order_id)
            //     );
            // }

            // // Fetch the trips from other work orders having the same issue
            // $otherWorkOrders = $this->issue->workOrders->where('work_order_id', '!=', $this->work_order_id);
            // if ($otherWorkOrders->isNotEmpty()) {
            //     $response['other_work_order_trips'] = $this->mapTrips(
            //         $this->issue->workOrders->where('work_order_id', '!=', $this->work_order_id)
            //     );
            // }
        } else {
            if ($this->state->equals(Unresolved::class)) {
                $response['decline_reason'] = $this->decline_reason;
            }

            $response['materials'] = MaterialResource::collection($this->materials);
        }

        return $response;
    }

    // private function mapTrips($workOrders)
    // {
    //     return $workOrders->flatMap(function ($workOrder) {
    //         return $workOrder->latestTrips->map(function ($trip) use ($workOrder) {
    //             return [
    //                 'work_order_number' => $workOrder->work_order_number,
    //                 'trip_id' => $trip->work_order_service_call_uuid,
    //                 'trip_number' => $trip->work_order_service_call_number,
    //                 'status' => new TripStatusResource($trip->state),
    //             ];
    //         });
    //     });
    // }
}

<?php

declare(strict_types=1);

namespace App\Http\Resources\WorkOrderIssue;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\WorkOrderIssue
 */
class IssueDeclinedResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'issue_id' => $this->work_order_issue_uuid,
            'decline_reason' => $this->decline_reason,
            'status' => new WorkOrderIssueStatusResource($this->state),
            'abilities' => $this->abilities(),
        ];
    }
}

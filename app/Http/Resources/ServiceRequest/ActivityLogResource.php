<?php

namespace App\Http\Resources\ServiceRequest;

use App\Models\ServiceRequestActivityLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin ServiceRequestActivityLog */
class ActivityLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'activity_uuid' => $this['activity_uuid'] ?? null,
            'event' => $this['event'] ?? null,
            'attributes' => $this['attributes'] ?? [],
            'created_at' => ! empty($this['created_at']) ? Carbon::parse($this['created_at'])->toIso8601String() : null,
            'triggered_by' => $this['triggered_by'] ?? null,
        ];
    }
}

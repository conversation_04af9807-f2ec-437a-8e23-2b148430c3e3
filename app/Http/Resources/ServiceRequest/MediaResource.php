<?php

namespace App\Http\Resources\ServiceRequest;

use App\Enums\Boolean;
use App\Enums\ImageConversionType;
use App\Models\Media;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Media
 */
class MediaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [

            'media_uuid' => $this->media_uuid,
            'media_type' => $this->pivot->media_type ?? null,
            'mime_type' => $this->mime_type,
            'thumbnail' => ! empty($this->pivot->has_thumbnail) && $this->pivot->has_thumbnail == Boolean::YES() ?
                $this->getServiceRequestTemporaryMediaUrl(type: ImageConversionType::THUMBNAIL()) : null,
            'original' => ! empty($this->pivot->has_upload_completed) && $this->pivot->has_upload_completed == Boolean::YES() ?
                $this->getServiceRequestTemporaryMediaUrl(type: ImageConversionType::ORIGINAL()) : null,
        ];
    }
}

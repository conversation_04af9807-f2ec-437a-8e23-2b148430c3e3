<?php

namespace App\Http\Resources\ServiceRequest\Group;

use App\Enums\Priority;
use App\Models\WorkOrder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin WorkOrder
 */
class PriorityBasedGroupDataResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this->priority ? Str::title(Priority::from($this->priority)->name) : null,
            'total' => $this->total ?? 0,
            'order' => $this->order ?? null,
            'slug' => $this->priority ?? null,
        ];
    }
}

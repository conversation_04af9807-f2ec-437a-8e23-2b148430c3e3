<?php

namespace App\Http\Resources\ServiceRequest\Media;

use App\Enums\Boolean;
use App\Enums\ImageConversionType;
use App\Models\Media;
use App\Models\ServiceRequestMedia;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 *@mixin ServiceRequestMedia
 *@mixin Media
 */
class OriginalMediaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @throws Exception
     */
    public function toArray(Request $request): array
    {
        return [
            'media_uuid' => $this->media->media_uuid,
            'thumbnail_url' => $this->serviceRequest && $this->has_thumbnail == Boolean::YES() ?
                $this->media->getServiceRequestTemporaryMediaUrl(type: ImageConversionType::OPTIMIZED()) : null,
            'original' => $this->serviceRequest && $this->has_upload_completed == Boolean::YES() ?
                $this->media->getServiceRequestTemporaryMediaUrl(type: ImageConversionType::ORIGINAL()) : null,
        ];
    }
}

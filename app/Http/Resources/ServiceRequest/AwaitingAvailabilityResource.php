<?php

namespace App\Http\Resources\ServiceRequest;

use App\Models\ServiceRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceRequest
 */
class AwaitingAvailabilityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'service_request_id' => $this->service_request_uuid,
            'status' => new ServiceRequestStatusResource($this->state),
            'abilities' => $this->when(! $request->expectsDevice(), $this->resolveStateAbilities()),
        ];
    }
}

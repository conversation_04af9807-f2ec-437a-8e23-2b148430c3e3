<?php

namespace App\Http\Resources\ServiceRequest;

use App\Enums\ServiceCallStatus;
use App\Enums\WorkOrderSourceTypes;
use App\Helpers\DataFormatHelper;
use App\Http\Resources\ServiceRequest\WorkOrder\WorkOrderIssuesResource;
use App\Http\Resources\WorkOrder\MediaResource;
use App\Http\Resources\WorkOrder\ProblemCategoryResource;
use App\Http\Resources\WorkOrder\TaskResource;
use App\Http\Resources\WorkOrder\WorkOrderAssigneeResource;
use App\Http\Resources\WorkOrder\WorkOrderStatusResource;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\States\WorkOrders\Canceled;
use App\States\WorkOrders\ClaimPending;
use App\States\WorkOrders\Created;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\ReadyToSchedule;
use App\States\WorkOrders\Scheduled;
use App\States\WorkOrders\WorkInProgress;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class WorkOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @throws Exception
     */
    public function toArray(Request $request): array
    {
        $propertyAccessInfo = [
            'access_method' => $this->property_access_method ?? null,
            'access_code' => $this->property_access_code ?? null,
            'access_note' => $this->property_access_note ?? null,
        ];

        $responseData = [
            'can_assign' => ! $this->state->equals(Canceled::class),
            'work_order_id' => $this->work_order_uuid,
            'work_order_number' => $this->work_order_number,
            'description' => $this->description,
            'nte_amount_in_cents' => $this->nte_amount_in_cents,
            'media' => MediaResource::collection($this->media),
            'work_order_tasks' => TaskResource::collection($this->tasks),
            'category' => ProblemCategoryResource::collection($this->workOrderIssues),
            'issues' => WorkOrderIssuesResource::collection($this->workOrderIssues),
            'nte_amount' => $this->nte_amount_in_cents,
            'abilities' => $this->resolveStateAbilities($request->expectsDevice() ? 'mobile' : 'web'),
        ];

        if ($this->state->equals(Paused::class)) {
            $responseData['paused_at'] = $this->paused_at?->toIso8601String();
            $responseData['paused_reason'] = $this->paused_reason;
        }

        if ($request->expectsDevice()) {
            $responseData = $this->generateMobileResponse($responseData);
        } else {
            $responseData['status'] = new WorkOrderStatusResource($this->state);
            $responseData['resolved_at'] = $this->resolved_at?->toIso8601String();
            $responseData['created_at'] = $this->created_at?->toIso8601String();
            $responseData['updated_at'] = $this->updated_at?->toIso8601String();

            $responseData['due_date'] = $this->due_date ? DataFormatHelper::dateFormat($this->due_date) : null;
            $responseData['assignees'] = WorkOrderAssigneeResource::collection($this->assignees);

            if ($this->state->equals(Canceled::class)) {
                $responseData['canceled_at'] = $this->canceled_at?->toIso8601String();
                $responseData['canceled_reason'] = $this->canceled_reason;
            }

            $responseData['show_schedule_details'] = ! $this->state->equals(
                Canceled::class, ClaimPending::class, Created::class,
                ReadyToSchedule::class
            );

        }

        return $responseData;
    }

    protected function isOpenWorkOrderOfAuthenticatedTechnician(?WorkOrderServiceCall $latestServiceCall): bool
    {
        return $latestServiceCall?->status === ServiceCallStatus::ACTIVE()
            && $this->state->equals(Scheduled::class, WorkInProgress::class)
            && request()->user()?->is($latestServiceCall->appointment?->technician->user);
    }

    protected function isLatestWorkOrderTripOfAuthenticatedTechnician(?WorkOrderServiceCall $latestServiceCall): bool
    {
        return (bool) request()->user()?->is($latestServiceCall?->appointment?->technician->user);
    }

    /**
     * Generate response data for mobile
     *
     * @param  array<string, mixed>  $responseData
     * @return array<string, mixed>
     */
    protected function generateMobileResponse(array &$responseData): array
    {
        // For appfolio job add job reference number
        if (! empty($this->workOrderSource)) {
            if ($this->workOrderSource->slug === WorkOrderSourceTypes::APPFOLIO()) {
                $responseData['external_source'] = [
                    'reference_number' => $this->work_order_reference_number,
                ];
            }
        }

        return $responseData;
    }
}

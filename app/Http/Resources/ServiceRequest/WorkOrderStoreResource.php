<?php

namespace App\Http\Resources\ServiceRequest;

use App\Http\Resources\ServiceRequest\WorkOrder\WorkOrderStoreIssueResource;
use App\Http\Resources\WorkOrder\WorkOrderStatusResource;
use App\Models\WorkOrder;
use App\States\WorkOrders\Canceled;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class WorkOrderStoreResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'can_assign' => ! $this->state->equals(Canceled::class),
            'service_request_id' => $this->serviceRequest ? $this->serviceRequest->service_request_uuid : null,
            'work_order_id' => $this->work_order_uuid,
            'work_order_number' => $this->work_order_number,
            'created_at' => ! empty($this->created_at) ? $this->created_at->toIso8601String() : null,
            'status' => new WorkOrderStatusResource($this->state),
            'issues' => WorkOrderStoreIssueResource::collection($this->workOrderIssues),
            'abilities' => $this->resolveStateAbilities(),
        ];
    }
}

<?php

namespace App\Http\Resources\ServiceRequest\WorkOrder;

use App\Models\Issue;
use App\Models\WorkOrderIssue;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderIssue
 * @mixin Issue
 */
class WorkOrderIssuesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'issue_id' => $this->issue?->issue_uuid,
            'title' => $this->issue?->title,
        ];
    }
}

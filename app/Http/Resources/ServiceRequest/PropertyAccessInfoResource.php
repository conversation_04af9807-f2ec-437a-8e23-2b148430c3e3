<?php

namespace App\Http\Resources\ServiceRequest;

use App\Enums\PropertyAccessMethods;
use App\Models\ServiceRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceRequest
 */
class PropertyAccessInfoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'access_method' => [
                'label' => $this->property_access_method ? PropertyAccessMethods::label($this->property_access_method) : null,
                'value' => $this->property_access_method ? PropertyAccessMethods::from($this->property_access_method)->value : null,
            ],
            'access_code' => $this->property_access_code ?? null,
            'access_note' => $this->property_access_note ?? null,
        ];
    }
}

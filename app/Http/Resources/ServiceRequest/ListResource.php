<?php

namespace App\Http\Resources\ServiceRequest;

use App\Http\Resources\WorkOrder\List\ListPropertyResource;
use App\Models\ServiceRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin ServiceRequest
 */
class ListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'service_request_id' => $this->service_request_uuid,
            'service_request_number' => $this->service_request_number ?? null,
            'description' => $this->latestDescription?->description,
            'property' => new ListPropertyResource($this->property),
            'type' => new ServiceRequestTypeResource($this->type),
            'assignees' => AssigneeResource::collection($this->assignees),
            'status' => new ServiceRequestStatusResource($this->state),
            'imported_from' => new ServiceRequestSourceResource($this->source),
            'priority' => new PriorityResource($this->priority),
            'timezone' => $this->timezone->name ?? '',
            'categories' => ProblemCategoryResource::collection($this->categories),
            'group' => ! empty($request->group) ? $this->findGroupSlug($request->group, $this) : null,
            'added_date' => $this->created_at?->toIso8601String(),
        ];
    }

    private function findGroupSlug(string $group, ListResource $serviceRequest): string
    {
        return match ($group) {
            'status' => $serviceRequest->state ?? '',
            'assignee' => ! empty($serviceRequest->assignee_ids_for_group) ? Str::slug($serviceRequest->assignee_ids_for_group) : '',
            'imported_from' => ! empty($serviceRequest->imported_from) ? Str::slug($serviceRequest->imported_from) : '',
            default => '',
        };
    }
}

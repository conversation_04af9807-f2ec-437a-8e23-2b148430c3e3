<?php

namespace App\Http\Resources\ServiceRequest;

use App\Models\ServiceRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceRequest
 */
class DescriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'service_request_id' => $this->service_request_uuid,
            'description' => $this->description,
            'description_updated_at' => $this->description_last_edited_at,
            'description_updated_by' => $this->updatedByUser?->first_name . ' ' . $this->updatedByUser?->last_name,

        ];
    }
}

<?php

namespace App\Http\Resources\ServiceRequest;

use App\Models\ServiceRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceRequest
 */
class StoreResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'service_request_id' => $this->service_request_uuid,
            'service_request_number' => $this->service_request_number,
            'created_at' => ! empty($this->created_at) ? $this->created_at->toIso8601String() : null,
        ];
    }
}

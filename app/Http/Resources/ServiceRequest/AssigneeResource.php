<?php

namespace App\Http\Resources\ServiceRequest;

use App\Models\ServiceRequestAssignee;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin ServiceRequestAssignee */
class AssigneeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'user_id' => $this->user->user_uuid ?? null,
            'name' => $this->user ? $this->user->getName() : null,
            'profile_pic' => $this->user->profile_pic ?? null,
        ];
    }
}

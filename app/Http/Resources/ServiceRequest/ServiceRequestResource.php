<?php

namespace App\Http\Resources\ServiceRequest;

use App\Enums\ServiceCallStatus;
use App\Http\Resources\Issue\IssueResource;
use App\Http\Resources\ResidentAvailability\ResidentAvailabilityResource;
use App\Models\ServiceRequest;
use App\Models\User;
use App\Models\WorkOrderServiceCall;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\Scheduled;
use App\States\WorkOrders\WorkInProgress;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceRequest
 */
class ServiceRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @throws Exception
     */
    public function toArray(Request $request): array
    {
        $propertyAccessInfo = [
            'access_method' => $this->property_access_method ?? null,
            'access_code' => $this->property_access_code ?? null,
            'access_note' => $this->property_access_note ?? null,
        ];

        $this->serviceRequestDescriptions->each(function ($description, $key) {
            if (! empty($description->additional_info)) {
                $medias = $this->media->whereIn('media_id', $description->additional_info['media_ids']);
                $description->setRelation('medias', $medias);
            }
            // For service requests from Appfolio, user_id will be empty
            if (($key + 1) === $this->serviceRequestDescriptions->count() && empty($description->createdUser)) {
                // Create a user instance with name as 'Appfolio'
                $user = new User([
                    'first_name' => $this->source->name,
                    'middle_name' => null,
                    'last_name' => null,
                ]);
                $description->setRelation('createdUser', $user);
            }
        });

        $responseData = [
            'service_request_id' => $this->service_request_uuid,
            'service_request_number' => $this->service_request_number,
            'description_history' => ServiceRequestDescriptionResource::collection($this->serviceRequestDescriptions),
            'date_added' => $this->created_at?->toIso8601String(),
            'type' => new ServiceRequestTypeResource($this->type),
            'priority' => new PriorityResource($this->priority),
            'property' => new PropertyResource($this->property, $propertyAccessInfo),
            'media' => MediaResource::collection($this->media->whereNull('deleted_at')),
            'timezone' => $this->timezone->name ?? '',

            'work_orders' => WorkOrderResource::collection($this->workOrders),
            'issues' => IssueResource::collection($this->issues),
            'abilities' => $this->resolveStateAbilities(),
            'work_order_count' => $this->work_orders_count,
            'imported_from' => new ServiceRequestSourceResource($this->source),
        ];

        // if ($this->state->equals(Paused::class)) {
        //     $responseData['paused_at'] = $this->paused_at?->toIso8601String();
        //     $responseData['paused_reason'] = $this->paused_reason;
        // }

        // if ($request->expectsDevice()) {
        //     $responseData = $this->generateMobileResponse($responseData);
        // } else {
        // For Web
        $responseData['status'] = new ServiceRequestStatusResource($this->state);
        $responseData['assignees'] = AssigneeResource::collection($this->assignees);
        $resource = new ResidentAvailabilityResource($this);
        $data = collect($resource->resolve());
        $residentRequestAvailabilityCount = count($this->residentAvailabilities->whereNull('user_id')->groupBy('availability_date')->keys()->toArray());
        $responseData['resident_availabilities'] = [
            'availability' => $data->get('resident_availability'),
            'resident_added_availability_count' => $residentRequestAvailabilityCount,
            'requested_at' => $this->availability_requested_at,
            'availability_created_at' => $this->residentAvailabilities->whereNull('user_id')->sortByDesc('created_at')->first()?->created_at?->toIso8601String() ?? null,
            'display_availability_added_alert' => empty($this->availability_viewed_user_id) && $residentRequestAvailabilityCount ? true : false,
        ];
        $responseData['resident_id'] = $this->resident->resident_uuid ?? null;

        return $responseData;
    }

    protected function isOpenWorkOrderOfAuthenticatedTechnician(?WorkOrderServiceCall $latestServiceCall): bool
    {
        return $latestServiceCall?->status === ServiceCallStatus::ACTIVE()
            && $this->state->equals(Scheduled::class, WorkInProgress::class)
            && request()->user()?->is($latestServiceCall->appointment?->technician->user);
    }

    protected function isLatestWorkOrderTripOfAuthenticatedTechnician(?WorkOrderServiceCall $latestServiceCall): bool
    {
        return (bool) request()->user()?->is($latestServiceCall?->appointment?->technician->user);
    }
}

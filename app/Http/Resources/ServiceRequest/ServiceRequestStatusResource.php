<?php

namespace App\Http\Resources\ServiceRequest;

use App\States\ServiceRequests\ServiceRequestState;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceRequestState
 */
class ServiceRequestStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this->label(),
            'value' => $this->getValue(),
            'color_class' => $this->colorClass(),
        ];
    }
}

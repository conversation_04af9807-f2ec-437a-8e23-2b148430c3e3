<?php

namespace App\Http\Resources\ServiceRequest;

use App\Models\ServiceRequestSource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin ServiceRequestSource
 */
class ServiceRequestSourceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this->name ? Str::title($this->name) : null,
            'value' => $this->slug ?? null,
        ];
    }
}

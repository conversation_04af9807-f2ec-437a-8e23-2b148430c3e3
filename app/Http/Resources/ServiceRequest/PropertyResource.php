<?php

namespace App\Http\Resources\ServiceRequest;

use App\Enums\PropertyAccessMethods;
use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Property
 */
class PropertyResource extends JsonResource
{
    /**
     * @var array<string,string|null>
     */
    protected $propertyAccessInfo;

    /**
     * Create a new resource instance.
     *
     * @param  mixed  $resource
     * @param  array<string,string|null>  $propertyAccessInfo
     * @return void
     */
    public function __construct($resource, array $propertyAccessInfo)
    {
        // Ensure you call the parent constructor
        parent::__construct($resource);
        $this->resource = $resource;

        $this->propertyAccessInfo = $propertyAccessInfo;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $responseData = [
            'address' => $request->expectsDevice() ? $this->getInlineFullAddressFormat() : $this->getAddress(),
            'name' => $this->property_name,
            'property_id' => $this->property_uuid,
            'location' => [
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
            ],
            'access_information' => $this->when(! empty($this->propertyAccessInfo['access_method']), [
                'access_method' => [
                    'label' => $this->propertyAccessInfo['access_method'] ? PropertyAccessMethods::label($this->propertyAccessInfo['access_method']) : null,
                    'value' => $this->propertyAccessInfo['access_method'] ? PropertyAccessMethods::from($this->propertyAccessInfo['access_method'])->value : null,
                ],
                'access_code' => $this->propertyAccessInfo['access_code'] ?? null,
                'access_note' => $this->propertyAccessInfo['access_note'] ?? null,
            ]),
        ];

        if (! $request->expectsDevice()) {
            // For web
            $responseData['residents'] = ResidentResource::collection($this->residents);
            $responseData['state'] = [
                'value' => $this->state->state_uuid ?? null,
                'label' => $this->state->name ?? null,
            ];
        }

        return $responseData;
    }
}

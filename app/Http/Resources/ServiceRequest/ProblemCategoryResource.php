<?php

namespace App\Http\Resources\ServiceRequest;

use App\Models\ServiceRequestCategory;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceRequestCategory
 */
class ProblemCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            'label' => $this->problemCategory->label ?? null,
            'slug' => $this->problemCategory->slug ?? null,
        ];
    }
}

<?php

namespace App\Http\Resources\ServiceRequest\Filter;

use App\Models\ServiceRequestSource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceRequestSource
 */
class ImportedFromFilterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this->name,
            'value' => $this->slug,
        ];
    }
}

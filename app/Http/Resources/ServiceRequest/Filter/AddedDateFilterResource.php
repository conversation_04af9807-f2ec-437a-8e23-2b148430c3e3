<?php

namespace App\Http\Resources\ServiceRequest\Filter;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class AddedDateFilterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string,string>
     */
    public function toArray(Request $request): array
    {
        $label = '';
        if (isset($this->resource->name)) {
            $label = Str::replace('_', ' ', (string) $this->resource->name);
        }

        return [
            'label' => is_string($label) ? Str::title($label) : $label,
            'value' => $this->resource->value ?? null,
        ];
    }
}

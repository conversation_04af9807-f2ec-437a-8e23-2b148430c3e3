<?php

namespace App\Http\Resources\ServiceRequest;

use App\Models\Property;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Property
 */
class PropertyAddressResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'property_id' => $this->property_uuid,
            'state' => [
                'value' => $this->state->state_uuid,
                'label' => $this->state->name,
            ],
            'address' => $this->getAddress(),
        ];
    }
}

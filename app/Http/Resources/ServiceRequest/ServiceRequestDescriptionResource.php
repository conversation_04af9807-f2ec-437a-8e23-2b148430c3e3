<?php

namespace App\Http\Resources\ServiceRequest;

use App\Models\ServiceRequestDescription;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceRequestDescription
 */
class ServiceRequestDescriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'service_request_description_id' => $this->service_request_description_uuid,
            'description' => $this->description,
            'created_by' => $this->createdUser?->getName(),
            'created_at' => $this->created_at->toIso8601String(),
            'medias' => ! empty($this->medias) ? MediaResource::collection($this->medias) : [],
            'is_active' => empty($this->deleted_at),
        ];
    }
}

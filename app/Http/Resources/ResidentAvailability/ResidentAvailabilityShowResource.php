<?php

namespace App\Http\Resources\ResidentAvailability;

use App\Models\ServiceRequest;
use App\States\ServiceRequests\AwaitingAvailability;
use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceRequest
 */
class ResidentAvailabilityShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $timezone = $this->timezone->name ?? config('settings.default_timezone');

        $resource = new ResidentAvailabilityResource($this);
        $data = collect($resource->resolve());

        $residentAvailability = $data->get('resident_availability') ?? [];

        $residentAvailability = collect((array) $residentAvailability)->map(function ($availability) {
            return [
                'date' => CarbonImmutable::parse($availability['date'])->format('m-d-Y'),
                'timings' => $availability['timings'],
                'day_passed' => $availability['day_passed'],
            ];
        });

        return [
            'timezone' => $timezone,
            'resident_availability' => $residentAvailability,
            'logo_uri' => $this->organization->logoUrl(),
            'is_editable' => ($this->state->equals(AwaitingAvailability::class) && ! empty($this->availability_requested_at)),
        ];
    }
}

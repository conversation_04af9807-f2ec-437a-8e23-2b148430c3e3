<?php

namespace App\Http\Resources\Vendor;

use App\Enums\Feature as FeatureEnum;
use App\Enums\UserTypes;
use App\Models\Feature;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin User
 */
class ProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        $permissions = Feature::with(['permissions:name,feature_id'])->where('name', FeatureEnum::VENDOR_PORTAL_MANAGEMENT())->select('feature_id')->first();
        $permissionNames = $permissions->permissions->pluck('name');

        return [
            'user_id' => $this->user_uuid,
            'name' => $this->getName(),
            'email' => $this->email,
            'permissions' => $permissionNames,
            'timezone' => $this->timezone?->name,
            'technician_id' => $this->when($this->user_type === UserTypes::TECHNICIAN(), $this->technician?->technician_uuid),
        ];
    }
}

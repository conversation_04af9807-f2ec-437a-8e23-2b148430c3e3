<?php

namespace App\Http\Resources\Vendor;

use App\Enums\Boolean;
use App\Http\Resources\WorkOrder\List\ActiveTripResource;
use App\Http\Resources\WorkOrder\List\ListPropertyResource;
use App\Http\Resources\WorkOrder\PriorityResource;
use App\Http\Resources\WorkOrder\WorkOrderStatusResource;
use App\Http\Resources\WorkOrderIssue\WorkOrderIssueResource;
use App\Models\WorkOrder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class ListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'work_order_id' => $this->work_order_uuid,
            'work_order_number' => $this->work_order_number ?? null,
            'work_order_issues' => WorkOrderIssueResource::detailCollection($this->workOrderIssues),
            'priority' => new PriorityResource($this->priority),
            'property' => new ListPropertyResource($this->serviceRequest->property),
            'trips' => $this->latestTrips->where('is_active', Boolean::YES())->isNotEmpty() ? ActiveTripResource::collection($this->latestTrips->where('is_active', Boolean::YES())) : [],
            'status' => new WorkOrderStatusResource($this->state),
            'has_missing_data' => $this->workOrderIssues->isEmpty() || empty($this->priority),
            'timezone' => $this->timezone->name ?? '',
            'due_date' => $this->due_date?->toIso8601String(),
            'paused_at' => $this->when(! empty($this->paused_at), $this->paused_at?->toIso8601String()),
            'resolved_at' => $this->when(! empty($this->resolved_at), $this->resolved_at?->toIso8601String()),
            'completed_at' => $this->when(! empty($this->work_completed_at), $this->work_completed_at?->toIso8601String()),
            'canceled_at' => $this->when(! empty($this->canceled_at), $this->canceled_at?->toIso8601String()),
            'abilities' => $this->abilities($request->requestSource()),
            'created_at' => $this->created_at?->toIso8601String(),
        ];
    }
}

<?php

namespace App\Http\Resources\Vendor;

use App\Http\Resources\StateResource;
use App\Models\State;
use Illuminate\Http\Resources\Json\JsonResource;

class BasicInfoResource extends JsonResource
{
    public function toArray($request)
    {
        $basicInfo = null;

        $basicInfo = [
            'vendor_uuid' => $this->vendor->vendor_uuid,
            'email' => $this->vendor->email,
            'first_name' => $this->vendor->first_name,
            'last_name' => $this->vendor->last_name,
            'phone_number' => $this->vendor->phone_number,
            'company_name' => $this->vendor->company_name,
            'service' => $this->vendor->service,
            'address1' => $this->vendor->address1,
            'address2' => $this->vendor->address2,
            'city' => $this->vendor->city,
            'state' => StateResource::collection(State::where('state_code', $this->vendor->state_province)->select('name', 'state_uuid')->get()),
            'state_province' => $this->vendor->state_province,
            'postal_zip_code' => $this->vendor->postal_zip_code,
            'gl_insurance_expire_at' => $this->vendor->gl_insurance_expire_at,
        ];

        return [
            'vendor_onboarding_uuid' => $this->vendor_onboarding_uuid,
            'basic-info' => $basicInfo,
            'service-offered' => null,
            'service-area' => null,
            'type' => $this->type,
            'status' => [
                'slug' => $this->status->slug,
                'label' => $this->status->label,
            ],
        ];
    }
}

<?php

namespace App\Http\Resources\Vendor;

use App\Enums\Boolean;
use App\Models\Vendor;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Vendor
 */
class UpdateVendorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request)
    {
        return [
            'vendor_id' => $this->vendor_uuid,
            'is_active' => ($this->is_active === Boolean::YES()),
        ];

    }
}

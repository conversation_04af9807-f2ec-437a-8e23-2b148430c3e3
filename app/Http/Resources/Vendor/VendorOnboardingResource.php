<?php

namespace App\Http\Resources\Vendor;

use App\Helpers\Helper;
use App\Http\Resources\StateResource;
use App\Models\State;
use Illuminate\Http\Resources\Json\JsonResource;

class VendorOnboardingResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'vendor_onboarding_uuid' => $this->vendor_onboarding_uuid,
            'basic-info' => $this->getBasicInfo(),
            'service-offered' => $this->getServiceOffered(),
            'service-area' => null,
            'type' => $this->type,
            'location-api-key' => Helper::getLocationApiKey() ?: '',
            'status' => [
                'slug' => $this->status->slug,
                'label' => $this->status->label,
            ],
        ];
    }

    protected function getBasicInfo(): array
    {
        return [
            'vendor_id' => $this->vendor->vendor_uuid,
            'email' => $this->vendor->email,
            'first_name' => $this->vendor->first_name,
            'last_name' => $this->vendor->last_name,
            'phone_number' => $this->vendor->phone_number,
            'company_name' => $this->vendor->company_name,
            'service' => $this->vendor->service,
            'address1' => $this->vendor->address1,
            'address2' => $this->vendor->address2,
            'city' => $this->vendor->city,
            'state' => StateResource::collection(
                State::where('state_code', $this->vendor->state_province)
                    ->select('name', 'state_uuid')
                    ->get()
            ),
            'postal_zip_code' => $this->vendor->postal_zip_code,
        ];
    }

    protected function getServiceOffered(): array
    {
        $selectedSkills = [
            'selected_diagnosis_ids' => [],
            'selected_sub_category_ids' => [],
            'selected_problem_category_ids' => [],
        ];

        if (! $this->vendor->vendorServices) {
            return ['selected_skills' => $selectedSkills];
        }

        foreach ($this->vendor->vendorServices as $service) {
            $this->processService($service, $selectedSkills);
        }

        return ['selected_skills' => $selectedSkills];
    }

    protected function processService($service, array &$selectedSkills): void
    {
        $diagnosis = $service->problemDiagnosis;

        if (! $this->isValidDiagnosisChain($diagnosis)) {
            return;
        }

        $this->addUniqueIds($diagnosis, $selectedSkills);
    }

    protected function isValidDiagnosisChain($diagnosis): bool
    {
        return $diagnosis
            && $diagnosis->subCategory
            && $diagnosis->subCategory->problemCategory;
    }

    protected function addUniqueIds($diagnosis, array &$selectedSkills): void
    {
        $diagnosisId = $diagnosis->problem_diagnosis_uuid;
        $subCategoryId = $diagnosis->subCategory->problem_sub_category_uuid;
        $categoryId = $diagnosis->subCategory->problemCategory->problem_category_uuid;

        $this->addIfNotPresent($diagnosisId, $selectedSkills['selected_diagnosis_ids']);
        $this->addIfNotPresent($subCategoryId, $selectedSkills['selected_sub_category_ids']);
        $this->addIfNotPresent($categoryId, $selectedSkills['selected_problem_category_ids']);
    }

    protected function addIfNotPresent(string $id, array &$array): void
    {
        if (! in_array($id, $array)) {
            $array[] = $id;
        }
    }
}

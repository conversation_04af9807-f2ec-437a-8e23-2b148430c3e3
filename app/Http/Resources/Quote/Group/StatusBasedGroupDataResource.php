<?php

namespace App\Http\Resources\Quote\Group;

use App\Enums\QuoteStatus;
use App\Models\Quote;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Quote
 */
class StatusBasedGroupDataResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @throws Exception
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => QuoteStatus::label($this->status, 'web'),
            'total' => $this->total ?? 0,
            'order' => $this->sort_order ?? null,
            'slug' => $this->status ?? null,
            'status_color' => $this->status_color ?? null,
        ];
    }
}

<?php

namespace App\Http\Resources\Quote;

use App\Enums\QuoteStatus;
use App\Http\Resources\Tag\TagResource;
use App\Http\Resources\WorkOrder\List\ListPropertyResource;
use App\Http\Resources\WorkOrder\WorkOrderAssigneeResource;
use App\Models\Quote;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin Quote
 */
class ListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'quote_id' => $this->quote_uuid,
            'quote_number' => $this->quote_number ?? null,
            'work_order_id' => $this->workOrder?->work_order_uuid,
            'work_order_number' => $this->workOrder?->work_order_number,
            'work_order_task_id' => $this->workOrderTask?->work_order_task_uuid,
            'work_order_service_call_id' => $this->serviceCall?->work_order_service_call_uuid,
            'work_description' => $this->workOrder?->description,
            'status' => collect(QuoteStatus::cases())->map(fn ($status) => [
                'value' => $status->value,
                'label' => QuoteStatus::label($status->value, 'web'),
            ])->first(fn ($status) => $status['value'] === $this->status),

            'quote_submitted_details' => [
                'user_id' => $this->submittedUser?->user_uuid,
                'name' => ! empty($this->submittedUser) ? $this->getFullName($this->submittedUser) : '',
                'profile_pic' => $this->submittedUser->profile_pic ?? null,
                'submitted_at' => $this->when(! empty($this->submitted_at), $this->submitted_at?->toIso8601String()),
            ],
            'quote_approved_details' => [
                'user_id' => $this->approvedUser?->user_uuid ?? '',
                'name' => ! empty($this->approvedUser) ? $this->getFullName($this->approvedUser) : '',
                'profile_pic' => $this->approvedUser->profile_pic ?? null,
                'approved_at' => $this->when(! empty($this->approved_at), $this->approved_at?->toIso8601String()),
            ],
            'quote_rejected_details' => [
                'user_id' => $this->rejectedUser?->user_uuid ?? '',
                'name' => ! empty($this->rejectedUser) ? $this->getFullName($this->rejectedUser) : '',
                'profile_pic' => $this->rejectedUser->profile_pic ?? null,
                'rejected_at' => $this->when(! empty($this->rejected_at), $this->rejected_at?->toIso8601String()),
            ],
            'assignees' => WorkOrderAssigneeResource::collection(collect($this->workOrder?->assignees)),
            'group' => ! empty($request->group) ? $this->findGroupSlug($request->group, $this) : null,
            'timezone' => $this->workOrder->timezone->name ?? '',
            'category' => ListTaskResource::collection($this->workOrder?->tasks),
            'property' => new ListPropertyResource($this->workOrder?->property),
            'tags' => TagResource::collection($this->workOrder?->tags),
            'total' => ($this->quoteTasks->sum('total_cost_in_cents') / 100) + ($this->quoteTasks->pluck('quoteTaskMaterials')->collapse()->flatten()->sum('total_cost_in_cents') / 100),
            'approved_total' => ($this->quoteTasks->where('status', QuoteStatus::APPROVED())->sum('total_cost_in_cents') / 100) + ($this->quoteTasks->where('status', QuoteStatus::APPROVED())->pluck('quoteTaskMaterials')->collapse()->flatten()->sum('total_cost_in_cents') / 100),
        ];
    }

    public function getFullName(User $user): string
    {
        return trim("{$user->first_name} {$user->middle_name} {$user->last_name}");
    }

    private function findGroupSlug(string $group, ListResource $quote): string
    {
        switch ($group) {
            case 'work_order_number':
                return $quote->work_order_number ?? '';
            case 'status':
                return $quote->status ?? '';
            case 'category':
                return ! empty($quote->problem_categories_label) ? Str::slug($quote->problem_categories_label) : '';
            case 'assignee':
                return ! empty($quote->assignee_ids_for_group) ? Str::slug($quote->assignee_ids_for_group) : '';
            case 'submitted_by':
                return ! empty($quote->submittedUser->user_uuid) ? Str::slug($quote->submittedUser->user_uuid) : '';
            case 'tag':
                return ! empty($quote->tag_ids_for_group) ? Str::slug($quote->tag_ids_for_group) : '';
            default:
                return '';
        }
    }
}

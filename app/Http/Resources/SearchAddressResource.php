<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SearchAddressResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'label' => $this['Place']['Label'] ?? null,
            'geometry' => [
                'point' => $this['Place']['Geometry']['Point'] ?? null,
            ],
            'municipality' => $this['Place']['Municipality'] ?? null,
            'sub_region' => $this['Place']['SubRegion'] ?? null,
            'state' => $this['Place']['Region'] ?? null,
            'country' => $this['Place']['Country'] ?? null,
            'postal_code' => $this['Place']['PostalCode'] ?? null,
        ];
    }
}

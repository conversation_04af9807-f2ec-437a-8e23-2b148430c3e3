<?php

namespace App\Http\Resources\Scheduling;

use App\Enums\ScheduleTimings;
use App\Services\Scheduling\Domain\DTOs\TechnicianSchedule;
use App\Services\Scheduling\Domain\Entities\RankedServiceWindow;
use Carbon\CarbonImmutable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin TechnicianSchedule
 */
class GetTechnicianSchedulesResources extends JsonResource
{
    protected $timezone;

    public function __construct($resource, $timezone)
    {
        // Ensure parent constructor is called
        parent::__construct($resource);

        $this->timezone = $timezone;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $allSlots = $this->rankedWindows->map(function (RankedServiceWindow $window) {
            $start = $window->serviceWindow->getProviderStartTime();
            $end = $window->serviceWindow->getProviderEndTime();

            $labelSlug = $this->getTimeLabel($start);
            if (! $labelSlug) {
                return null;
            }

            return [
                'service_window_reference_id' => $window->serviceWindow->encodedReference(),
                'date' => $start->toDateString(),
                'label' => ScheduleTimings::label($labelSlug),
                'start_time' => $start?->toIso8601String(),
                'end_time' => $end?->toIso8601String(),
            ];
        })->filter();

        return $allSlots
            ->groupBy('date')
            ->map(function ($slots, $date) {
                $unique = $slots->unique(function ($slot) {
                    return $slot['start_time'] . '|' . $slot['end_time'];
                });

                return [
                    'date' => $date,
                    'available_slots' => $unique->map(function ($slot) {
                        return [
                            'service_window_reference_id' => $slot['service_window_reference_id'],
                            'label' => $slot['label'],
                            'start_time' => $slot['start_time'],
                            'end_time' => $slot['end_time'],
                        ];
                    })->sortBy('start_time')->values(),
                ];
            })
            ->sortBy('date')
            ->values()
            ->toArray();
    }

    protected function getTimeLabel(CarbonImmutable $start): ?string
    {
        foreach (ScheduleTimings::scheduleTimingsFromApp() as $slug) {
            $range = ScheduleTimings::timeRange($slug);

            if ($start->setTimezone($this->timezone)->format('H:i:s') >= $range['start_time'] && $start->setTimezone($this->timezone)->format('H:i:s') < $range['end_time']) {
                return $slug;
            }
        }

        return null;
    }
}

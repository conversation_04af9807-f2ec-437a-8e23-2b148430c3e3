<?php

namespace App\Http\Resources\Scheduling;

use App\Enums\ScheduleTimings;
use Carbon\CarbonImmutable;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;

class GetVendorAvailabilityResources extends JsonResource
{
    protected Collection $availabilityMap;
    protected string $timezone;

    public function __construct(Collection $availabilityMap, string $timezone)
    {
        parent::__construct($availabilityMap);
        $this->availabilityMap = $availabilityMap;
        $this->timezone = $timezone;
    }

    public function toArray($request): array
    {
        $result = [];

        $displaySortOrder = ScheduleTimings::displaySortOrder();

        $sortedAvailabilityMap = $this->availabilityMap->sortKeys();

        foreach ($sortedAvailabilityMap as $date => $entries) {
            $slots = [];

            $uniqueTimings = collect($entries)->pluck('timing')->unique();

            foreach ($uniqueTimings as $timing) {
                $range = ScheduleTimings::timeRange($timing);
                $start = CarbonImmutable::parse("{$date} {$range['start_time']}", $this->timezone)->setTimezone('UTC');
                $end = CarbonImmutable::parse("{$date} {$range['end_time']}", $this->timezone)->setTimezone('UTC');

                $slots[] = [
                    'service_window_reference_id' => base64_encode(implode('.', [
                        $start->toIso8601String(),
                        $end->toIso8601String(),
                    ])),
                    'label' => ScheduleTimings::label($timing),
                    'start_time' => $start->toIso8601String(),
                    'end_time' => $end->toIso8601String(),
                ];
            }

            // Sort by display order
            usort(
                $slots,
                fn ($slotA, $slotB) => array_search(strtolower($slotA['label']), $displaySortOrder)
                    <=> array_search(strtolower($slotB['label']), $displaySortOrder)
            );

            $result[] = [
                'date' => $date,
                'available_slots' => $slots,
            ];
        }

        return $result;
    }
}

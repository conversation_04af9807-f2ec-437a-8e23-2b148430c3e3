<?php

namespace App\Http\Resources\Scheduling;

use App\Enums\AppointmentProviderTypes;
use App\Enums\WorkOrderSourceTypes;
use App\Enums\WorkToPerformTypes;
use App\Models\Quote;
use App\Services\Scheduling\Domain\DTOs\TaskSchedulingOptions;
use App\Services\Scheduling\Domain\Enums\SchedulingMethod;
use App\Services\Scheduling\Domain\Enums\SchedulingMode;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin TaskSchedulingOptions
 */
class ScheduleContextResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'recommended_provider_type' => match (true) {
                ! empty($this->inHouseTechAvailable) => AppointmentProviderTypes::TECHNICIAN,
                ! empty($this->providers->lula) => AppointmentProviderTypes::LULA,
                ! empty($this->providers->vendors->count()) => AppointmentProviderTypes::VENDOR,
                default => AppointmentProviderTypes::TECHNICIAN
            },
            'provider_settings' => [
                AppointmentProviderTypes::TECHNICIAN() => [
                    'recommended_mode' => $this->recommendedMode,
                    'recommended_method' => $this->recommendedMethod,
                    'current_method' => $this->currentMethod,
                    'current_mode' => $this->currentMode,
                    'recommended_duration' => $this->estimatedDuration,
                    'current_schedule_duration' => $this->currentDuration ?? null,
                    'available_modes' => collect(SchedulingMode::Names())->map(function (string $mode) {
                        return [
                            'value' => SchedulingMode::{$mode}(),
                            'label' => Str::title($mode),
                        ];
                    }),
                    'available_methods' => collect(SchedulingMethod::Names())->map(function (string $method) {
                        return [
                            'value' => SchedulingMethod::{$method}(),
                            'label' => Str::title($method),
                        ];
                    }),
                    'available_durations' => $this->getAvailableDurations(),
                    'qualified_technicians' => $this->providers->technicians->count(),
                    'work_to_perform' => $this->workToPerform ?? null,
                    'quote' => $this->quote,
                    'is_enabled' => ! empty($this->providers->technicians->count()) && ! empty($this->inHouseTechAvailable),
                ],
                AppointmentProviderTypes::VENDOR() => [
                    'is_enabled' => ! in_array(app()->environment(), ['local', 'development']) ? false : $this->providers->vendors->isNotEmpty(),
                    'vendor_count' => $this->providers->vendors->count(),
                ],
                AppointmentProviderTypes::LULA() => [
                    'estimated_cost' => [
                        'lowerCents' => 8000,
                        'upperCents' => 9500,
                    ],
                    'vendor' => $this->providers->lula,
                    'is_enabled' => ! empty($this->providers->lula) && $this->workOrderSourceSlug !== WorkOrderSourceTypes::LULA() && (! $this->workOrderIsAlreadySendToLula),
                ],
            ],

            'schedule_options' => $this->scheduleOption($this->approvedQuotes ?? null),
        ];
    }

    /**
     * @param  Collection<int, Quote>  $quotes
     * @return array<int, mixed>
     */
    public function scheduleOption(?Collection $quotes): array
    {
        $data = [
            [
                'value' => WorkToPerformTypes::HOURLY_TASK(),
                'label' => 'Hourly Rate Tasks',
            ],
        ];
        if (! empty($quotes)) {
            foreach ($quotes as $quote) {
                $data[] = [
                    'value' => WorkToPerformTypes::QUOTE_TASK(),
                    'label' => "Related to Quote #{$quote->quote_number}",
                    'quote_id' => $quote->quote_uuid,
                ];
            }
        }

        return $data;
    }

    /**
     * Get available durations in 30-minute intervals up to the specified hours.
     *
     * @return array<int,int>
     */
    public function getAvailableDurations(int $hours = 8): array
    {
        if ($hours <= 0) {
            return [];
        }

        $maxMinutes = $hours * 60;

        return range(30, $maxMinutes, 30);
    }
}

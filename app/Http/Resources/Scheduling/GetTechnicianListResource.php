<?php

namespace App\Http\Resources\Scheduling;

use App\Services\Scheduling\Domain\DTOs\TechnicianList;
use App\Services\Scheduling\Domain\Entities\RankedServiceWindow;
use App\Services\Scheduling\Domain\Entities\Technician;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin TechnicianList
 */
class GetTechnicianListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        $recommendedWindows = $this->recommendedWindows->map(function (RankedServiceWindow $window) {
            return $this->toWindowArray($window, $this->technicians->firstOrFail(function (Technician $technician) use ($window) {
                return $technician->uuid === $window->serviceWindow->technicianUUID;
            }));
        });

        return [
            'technicians' => $this->technicians->map(function (Technician $technician) {
                return [
                    'technician_uuid' => $technician->uuid,
                    'full_name' => $technician->fullName,
                ];
            })->toArray(),
            // TODO: Avoiding ranked_appointments for now and Need to implement some performance tweaks.
            //            'ranked_appointments' => $technicianWindows->collapse()->toArray(),
            'ranked_appointments' => [],
            'recommended_appointments' => $recommendedWindows->toArray(),
        ];
    }

    /**
     * @return array<string,int|mixed>
     */
    protected function toWindowArray(RankedServiceWindow $window, Technician $technician): array
    {
        // TODO better behavior - onHours should not be null but our types allow it and we should fix that
        return [
            'service_window_reference_id' => $window->serviceWindow->encodedReference(),
            'rank' => $window->rank,
            'on_hours' => $window->serviceWindow->onHours ?? true,
            'technician_uuid' => $technician->uuid,
            'provider_start_time' => $window->serviceWindow->getProviderStartTime()->toIso8601String(),
            'provider_end_time' => $window->serviceWindow->getProviderEndTime()?->toIso8601String(),
            'resident_start_time' => $window->serviceWindow->getResidentStartTime()->toIso8601String(),
            'resident_end_time' => $window->serviceWindow->getResidentEndTime()?->toIso8601String(),
        ];
    }
}

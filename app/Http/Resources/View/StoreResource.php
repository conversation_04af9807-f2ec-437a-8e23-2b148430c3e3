<?php

namespace App\Http\Resources\View;

use App\Models\View;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin View
 */
class StoreResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'view' => [
                'label' => $this->name,
                'value' => $this->view_uuid,
            ],
            'is_default' => false,
            'is_pinned' => false,
            'is_editable' => true,
            'is_deletable' => true,
            'is_able_to_pin' => true,
        ];
    }
}

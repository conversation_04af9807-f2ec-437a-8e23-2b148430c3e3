<?php

namespace App\Http\Resources\View;

use App\Enums\ViewTypes;
use App\Models\View;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin View
 */
class ConfigurationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray($request)
    {
        if ($request->view_type === ViewTypes::CALENDAR()) {
            return [
                'calendarFilter' => [
                    'technician' => $this->calendarFilter->technician ?? null,
                    'calendarMode' => $this->calendarFilter->calendarMode ?? null,
                    'settings' => $this->calendarFilter->settings ?? null,
                ],
            ];
        }

        return [
            'columns' => $this->columns ?? null,
            'filters' => $this->filters ?? null,
            'grouping' => $this->grouping ?? null,
        ];

    }
}

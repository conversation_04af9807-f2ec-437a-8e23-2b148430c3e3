<?php

namespace App\Http\Resources\View;

use App\Models\View;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin View
 */
class ViewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string|int, bool|array<string,string>>
     */
    public function toArray(Request $request): array
    {
        return [
            'view' => [
                'label' => $this->name,
                'value' => $this->view_uuid,
            ],
            'is_default' => $request->default_view_id ? $request->default_view_id == $this->view_id :
                ($this->name === 'Default View' ? true : false),
            'is_pinned' => $this->name === 'Default View' ? true :
                ($this->userPinnedViews->where('user_id', $request->user()?->user_id)->count() ? true : false),
            'is_editable' => $this->scope === 'global' ? false : true,
            'is_deletable' => $this->scope === 'global' ? false : true,
            'is_able_to_pin' => $this->name === 'Default View' ? false : true,
            $this->mergeWhen($this->name === 'Default View', [
                'is_primary' => true,
            ]),
        ];
    }
}

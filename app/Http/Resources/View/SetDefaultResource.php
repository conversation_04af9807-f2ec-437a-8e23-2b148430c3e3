<?php

namespace App\Http\Resources\View;

use App\Models\View;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin View
 */
class SetDefaultResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, array<string, string|null>|bool>
     */
    public function toArray(Request $request): array
    {
        return [
            'view' => [
                'label' => $this->name,
                'value' => $this->view_uuid,
            ],
            'is_default' => true,
        ];

    }
}

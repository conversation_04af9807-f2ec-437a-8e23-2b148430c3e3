<?php

namespace App\Http\Resources\View;

use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Models\View;
use App\Traits\WorkOrderListFilterTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin View
 */
class UpdatedResource extends JsonResource
{
    use WorkOrderListFilterTrait;

    /**
     * Transform the resource into an array.
     *
     * @return array<string,bool|array<string,string|null>>
     */
    public function toArray(Request $request): array
    {
        $user = $request->user();

        if (empty($user)) {
            throw new UserNotFoundException;
        }

        return [
            'view' => [
                'label' => $this->name,
                'value' => $this->view_uuid,
            ],
            'is_default' => $this->userDefaultViews->where('user_id', $user->user_id)->count() ? true : false,
            'is_pinned' => $this->userPinnedViews->where('user_id', $user->user_id)->count() ? true : false,
            'is_editable' => true,
            'is_deletable' => true,
            'is_able_to_pin' => true,
        ];
    }
}

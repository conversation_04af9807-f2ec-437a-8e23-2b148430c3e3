<?php

namespace App\Http\Resources\View;

use App\Models\View;
use App\Services\FilterService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 *@mixin View
 */
class PinResource extends JsonResource
{
    /**
     * The filter service instance.
     */
    protected FilterService $filterService;

    /**
     * Create a new resource instance.
     *
     * @param  mixed  $resource
     */
    public function __construct($resource)
    {
        parent::__construct($resource);
        $this->filterService = app(FilterService::class);
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string,string|null|bool>
     */
    public function toArray(Request $request): array
    {
        return [
            'name' => $this->name,
            'view_uuid' => $this->view_uuid,
            'is_pinned' => $request->input('action') === 'pin',
        ];

    }
}

<?php

namespace App\Http\Resources\View;

use App\Models\View;
use App\Traits\WorkOrderListFilterTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 *@mixin View
 */
class PinResource extends JsonResource
{
    use WorkOrderListFilterTrait;

    /**
     * Transform the resource into an array.
     *
     * @return array<string,string|null|bool>
     */
    public function toArray(Request $request): array
    {
        return [
            'name' => $this->name,
            'view_uuid' => $this->view_uuid,
            'is_pinned' => $request->input('action') === 'pin',
        ];

    }
}

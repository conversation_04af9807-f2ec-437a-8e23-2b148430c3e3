<?php

namespace App\Http\Resources;

use App\Enums\UserStatus;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Vendor
 */
class VendorUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'user_id' => $this->vendor_uuid,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->user->email,
            'is_active' => $this->user->status === UserStatus::ACTIVE(),
            'since_from' => $this->created_at?->toIso8601String(),
            'last_activity' => $this->updated_at?->toIso8601String(),
        ];
    }
}

<?php

namespace App\Http\Resources;

use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Role
 */
class RoleResource extends JsonResource
{
    public static $wrap = null;

    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'role_id' => $this->role_uuid,
            'name' => $this->name,
            'parent_role_id' => $this->parent_role_id,
            'permissions' => PermissionResource::collection($this->whenLoaded('permissions')),
            'can_modify' => ! in_array($this->name, ['Owner', 'Super Admin']),
            'description' => $this->description,
            'users_count' => $this->when(
                isset($this->users_count),
                $this->users_count ?? 0
            ),
        ];
    }
}

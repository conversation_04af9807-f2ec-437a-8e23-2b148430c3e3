<?php

namespace App\Http\Resources\Invoice;

use App\Models\Invoice;
use App\States\Invoices\Draft;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\URL;

/**
 * @mixin Invoice
 */
class InvoiceListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        $data = [
            'invoice_id' => $this->invoice_uuid,
            'invoice_number' => $this->invoice_number,
            'total_cost_in_cents' => $this->total_cost_in_cents,
            'status' => new InvoiceStateResource($this->state),
            'pdf_link' => URL::temporarySignedRoute('pdf.invoice', now()->addMinutes(config('pdf.expiration_minutes')), ['invoice' => $this->invoice_uuid]),
        ];

        if ($this->state->equals(Draft::class)) {
            $data['drafted_at'] = $this->drafted_at;
            $data['drafted_by_user'] = $this->draftedByUser?->getName();
        }

        $data['created_at'] = $this->created_at;
        $data['created_by_user'] = $this->createdByUser?->getName();

        return $data;
    }
}

<?php

namespace App\Http\Resources\Invoice;

use App\Enums\InvoiceSubsidiaryTypes;
use App\Models\InvoiceLineItemSubsidiary;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin InvoiceLineItemSubsidiary
 */
class InvoiceLineItemSubsidiariesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $hours = 0;
        $minutes = 0;
        if (InvoiceSubsidiaryTypes::isLaborCostType($this->subsidiary_type)) {
            $totalWorkTime = $this->duration_in_seconds ?? 0;
            $hours = Carbon::now()->addSeconds($totalWorkTime)->diffInRealHours(Carbon::now());

            $totalWorkTime = $totalWorkTime - ($hours * 3600);
            $minutes = Carbon::now()->addSeconds($totalWorkTime)->diffInRealMinutes(Carbon::now());
        }

        return [
            'invoice_item_subsidiary_id' => $this->invoice_line_item_subsidiary_uuid,
            'subsidiary_type' => $this->subsidiary_type,
            $this->mergeWhen(InvoiceSubsidiaryTypes::isLaborCostType($this->subsidiary_type), [
                'hourly_rate_in_cents' => $this->hourly_rate_in_cents,
                'hours' => $hours,
                'minutes' => $minutes,
            ]),
            $this->mergeWhen(! empty($this->quoteTaskMaterial->quote_task_material_uuid), [
                'quote_task_material_id' => $this->quoteTaskMaterial?->quote_task_material_uuid,
            ]),
            $this->mergeWhen(! empty($this->workOrderTaskMaterial->work_order_task_material_uuid), [
                'work_order_task_material_id' => $this->workOrderTaskMaterial?->work_order_task_material_uuid,
            ]),
            $this->mergeWhen($this->subsidiary_type === InvoiceSubsidiaryTypes::MATERIAL(), [
                'quantity' => $this->quantity,
                'quantity_type' => $this->quantity_type,
                'cost_type' => $this->cost_type,
            ]),
            $this->mergeWhen($this->subsidiary_type === InvoiceSubsidiaryTypes::LABOR(), [
                'quote_task_id' => $this->quoteTask->quote_task_uuid ?? null,
            ]),
            $this->mergeWhen(! in_array($this->subsidiary_type, [InvoiceSubsidiaryTypes::NOTES(), InvoiceSubsidiaryTypes::FEE()]), [
                'description' => $this->description,
                'markup_fee_type' => $this->markup_fee_type,
                'markup_fee_type_value' => $this->markup_fee_type_value,
            ]),
            'markup_fee_in_cents' => $this->markup_fee_in_cents,
            'cost_in_cents' => $this->cost_in_cents,
            'total_cost_in_cents' => $this->total_cost_in_cents,
        ];
    }
}

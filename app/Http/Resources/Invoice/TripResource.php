<?php

namespace App\Http\Resources\Invoice;

use App\Enums\ScheduleTypes;
use App\Enums\WorkToPerformTypes;
use App\Http\Resources\WorkOrder\MediaResource;
use App\Models\InvoiceLineItem;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin InvoiceLineItem
 */
class TripResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $serviceCall = $this->first()?->serviceCall;
        $media = collect([]);

        if ($serviceCall?->work_to_perform === WorkToPerformTypes::QUOTE_TASK()) {
            $tripStatus = 'quote-work';

            if (! empty($this->whereNotNull('quote_id')->first()->quote->serviceCalls)) {
                $serviceCalls = $this->whereNotNull('quote_id')->first()->quote->serviceCalls;
                foreach ($serviceCalls as $trip) {
                    $media = $media->merge(MediaResource::collection($trip->media));
                }
            }
        } else {
            $tripStatus = $serviceCall->trip_end_with ?? '';
            $media = MediaResource::collection($serviceCall?->media);
        }

        $tripType = '';
        if (! empty($serviceCall->technician_appointment_id)) {
            $tripType = ScheduleTypes::IN_HOUSE();
        }
        if (! empty($serviceCall->lula_appointment_id)) {
            $tripType = ScheduleTypes::LULA_PRO();
            $jobReferenceNumber = $serviceCall->lulaAppointment?->work_order_reference_number;
        }

        return [
            'work_order_service_call_id' => $serviceCall?->work_order_service_call_uuid,
            'trip_status' => [
                'label' => $this->tripStatusLabel($tripStatus),
                'value' => $tripStatus,
            ],
            'trip_number' => $serviceCall?->work_order_service_call_number,
            'scheduled_start_time' => $serviceCall?->scheduled_start_time?->toIso8601String(),
            'scheduled_end_time' => $serviceCall?->scheduled_end_time?->toIso8601String(),
            'total_markup_fee_in_cents' => $this->sum('total_markup_fee_in_cents'),
            'cost_in_cents' => $this->sum('cost_in_cents'),
            'total_cost_in_cents' => $this->sum('total_cost_in_cents'),
            'invoice_line_items' => InvoiceLineItemsResource::collection($this),
            'media_details' => $media,
            'trip_type' => $tripType,
            'wo_reference_number' => $jobReferenceNumber ?? null,
        ];
    }

    protected function tripStatusLabel(string $tripStatus = ''): string
    {
        if ($tripStatus == 'no-work') {
            return 'No Work';
        } elseif ($tripStatus == 'all-completed') {
            return 'Completed';
        } elseif ($tripStatus == 'partially-completed') {
            return 'Partially Work';
        } elseif ($tripStatus == 'quote-work') {
            return 'Quote Work';
        }

        return Str::title(str_replace('-', ' ', $tripStatus));
    }
}

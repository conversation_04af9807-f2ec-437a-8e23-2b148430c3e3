<?php

namespace App\Http\Resources\Invoice;

use App\Enums\InvoiceLineItemTypes;
use App\Enums\InvoiceSubsidiaryTypes;
use App\Models\InvoiceLineItem;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin InvoiceLineItem
 */
class InvoiceLineItemsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $itemType = $this->invoice_line_item_type;

        return [
            'invoice_line_item_id' => $this->invoice_line_item_uuid,
            'item_type' => $itemType,
            $this->mergeWhen(! in_array($itemType, [InvoiceLineItemTypes::MATERIAL()]), [
                'description' => $this->description,
            ]),
            $this->mergeWhen(in_array($itemType, [InvoiceLineItemTypes::NO_SHOW_FEE(), InvoiceLineItemTypes::TRIP_FEE()]), [
                'is_enabled' => ! empty($this->total_cost_in_cents),
            ]),
            $this->mergeWhen(in_array($itemType, [InvoiceLineItemTypes::SERVICE_NOTE()]), [
                'quote_id' => $this->quote->quote_uuid ?? null,
            ]),
            $this->mergeWhen($itemType === InvoiceLineItemTypes::QUOTE_TASK(), [
                'quote_task_id' => $this->quoteTask?->quote_task_uuid,
                'total_material_cost_in_cents' => $this->subsidiaries->where('subsidiary_type', InvoiceSubsidiaryTypes::MATERIAL())->sum('total_cost_in_cents'),
                'total_labor_cost_in_cents' => $this->subsidiaries->where('subsidiary_type', InvoiceSubsidiaryTypes::LABOR())->sum('total_cost_in_cents'),
                'task_number' => $this->quoteTask?->quote_task_number,
            ]),
            'total_markup_fee_in_cents' => $this->total_markup_fee_in_cents ?? 0,
            'total_cost_in_cents' => $this->total_cost_in_cents ?? 0,
            'cost_in_cents' => $this->cost_in_cents ?? 0,
            'subsidiaries' => InvoiceLineItemSubsidiariesResource::collection($this->subsidiaries),
        ];
    }
}

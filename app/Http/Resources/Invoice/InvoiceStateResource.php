<?php

namespace App\Http\Resources\Invoice;

use App\States\Invoices\InvoiceState;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin InvoiceState
 */
class InvoiceStateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            'label' => $this->label(),
            'value' => $this->getValue(),
            'color_class' => $this->colorClass(),
        ];
    }
}

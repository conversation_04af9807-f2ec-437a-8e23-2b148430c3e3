<?php

namespace App\Http\Resources;

use App\Models\ProblemDiagnosis;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ProblemDiagnosis
 */
class ProblemDiagnosisResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this->label,
            'value' => $this->problem_diagnosis_uuid,
            'slug' => $this->slug,
        ];
    }
}

<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin \App\Models\Feature
 */
class FeatureResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'feature_id' => $this->feature_uuid,
            'name' => $this->name,
            'slug' => Str::slug($this->name),
            'permissions' => PermissionResource::collection($this->permissions),
        ];
    }
}

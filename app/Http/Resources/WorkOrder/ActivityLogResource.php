<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\WorkOrderActivityLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin WorkOrderActivityLog */
class ActivityLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'activity_uuid' => $this['activity_uuid'] ?? null,
            'event' => $this['event'] ?? null,
            'attributes' => $this['attributes'] ?? [],
            'created_at' => ! empty($this['created_at']) ? Carbon::parse($this['created_at'])->toIso8601String() : null,
            'triggered_by' => $this['triggered_by'] ?? null,
        ];
    }
}

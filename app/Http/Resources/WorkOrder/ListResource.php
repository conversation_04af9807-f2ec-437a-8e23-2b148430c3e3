<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\Boolean;
use App\Enums\ServiceCallStatus;
use App\Http\Resources\Tag\TagResource;
use App\Http\Resources\WorkOrder\HealthScore\HealthScoreResource;
use App\Http\Resources\WorkOrder\List\ActiveTripResource;
use App\Http\Resources\WorkOrder\List\ListPropertyResource;
use App\Http\Resources\WorkOrderIssue\WorkOrderIssueListResource;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\States\ServiceCalls\EnRoute;
use App\States\ServiceCalls\EnRoutePaused;
use App\States\WorkOrders\Canceled;
use App\States\WorkOrders\Completed;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\QualityCheck;
use App\States\WorkOrders\ReadyToInvoice;
use App\States\WorkOrders\ReadyToSchedule;
use App\States\WorkOrders\Scheduled;
use App\States\WorkOrders\WorkInProgress;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin WorkOrder
 */
class ListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {

        if ($request->expectsDevice()) {
            return $this->transformResponseForDevice($request);
        }
        $healthLogs = $this->healthLogs()
            ->whereNull('resolved_at')
            ->with('healthTracker')->get()
            ->sortByDesc(function ($healthLog) {
                return (int) ($healthLog->healthTracker->weight ?? 0);
            });

        return [
            'abilities' => $this->abilities($request->requestSource()),
            'trips' => $this->latestTrips->where('is_active', Boolean::YES())->isNotEmpty() ? ActiveTripResource::collection($this->latestTrips->where('is_active', Boolean::YES())) : [],
            'assignees' => WorkOrderAssigneeResource::collection($this->assignees),
            'category' => ProblemCategoryResource::collection($this->workOrderIssues),
            'completed_at' => $this->when(! empty($this->work_completed_at), $this->work_completed_at?->toIso8601String()),
            'created_at' => $this->created_at?->toIso8601String(),
            'due_date' => $this->due_date?->toIso8601String(),
            'group' => ! empty($request->group) ? $this->findGroupSlug($request->group, $this) : null,
            'has_missing_data' => $this->workOrderIssues->isEmpty() || empty($this->priority),
            'health_score' => HealthScoreResource::collection($healthLogs)->collection->first() ?? ['classification' => 'healthy', 'created_at' => ''],
            'nte_amount_in_cents' => $this->nte_amount_in_cents,
            'priority' => new PriorityResource($this->priority),
            'property' => new ListPropertyResource($this->serviceRequest->property),
            'provider' => [],
            'resolved_at' => $this->when(! empty($this->resolved_at), $this->resolved_at?->toIso8601String()),
            'status' => new WorkOrderStatusResource($this->state),
            'tags' => TagResource::collection($this->tags),
            'timezone' => $this->timezone->name ?? '',
            'work_order_id' => $this->work_order_uuid,
            'work_order_number' => $this->work_order_number ?? null,
        ];
    }

    /**
     * @return array<string|int, mixed>
     */
    protected function transformResponseForDevice(Request $request): array
    {
        return [
            'trips' => $this->latestTrips->where('is_active', Boolean::YES())->isNotEmpty() ? ActiveTripResource::collection($this->latestTrips->where('is_active', Boolean::YES())) : [],
            'canceled_at' => $this->when(! empty($this->canceled_at) && $request->input('group_type') === 'closed', $this->canceled_at?->toIso8601String()),
            'completed_at' => $this->when(! empty($this->work_completed_at) && $request->input('group_type') === 'closed', $this->work_completed_at?->toIso8601String()),
            'category' => ProblemCategoryResource::collection($this->workOrderIssues),
            'nte_amount_in_cents' => $this->nte_amount_in_cents,
            'paused_at' => $this->when($this->state->equals(Paused::class) && $request->input('group_type') === 'paused', $this->paused_at?->toIso8601String()),
            'priority' => new PriorityResource($this->priority),
            'property' => [
                'address' => $this->serviceRequest?->property->getInlineFullAddressFormat(),
                'name' => $this->serviceRequest->property->property_name ?? null,
            ],
            'resolved_at' => $this->when(! empty($this->resolved_at) && $request->input('group_type') === 'closed', $this->resolved_at?->toIso8601String()),
            'status' => $this->getStatusForApp(),
            'timezone' => $this->timezone->name ?? '',
            'work_order_id' => $this->work_order_uuid,
            'work_order_issues' => WorkOrderIssueListResource::collection($this->workOrderIssues),
            'work_order_number' => $this->work_order_number ?? null,
        ];
    }

    protected function isOpenWorkOrderOfAuthenticatedTechnician(?WorkOrderServiceCall $latestTrip): bool
    {
        return $latestTrip?->status === ServiceCallStatus::ACTIVE()
            && $this->state->equals(Scheduled::class, WorkInProgress::class)
            && request()->user()?->is($latestTrip->technicianAppointment?->technician->user);
    }

    protected function isLatestWorkOrderTripOfAuthenticatedTechnician(?WorkOrderServiceCall $latestTrip): bool
    {
        return (bool) request()->user()?->is($latestTrip?->technicianAppointment?->technician->user);
    }

    protected function getStatusForApp(): TripStatusResource|WorkOrderStatusResource
    {
        /** @var WorkOrderServiceCall $latestTrip */
        $latestTrip = $this->latestTrips->first();

        if ($this->isOpenWorkOrderOfAuthenticatedTechnician($latestTrip)) {
            return match ($latestTrip->state->getValue()) {
                EnRoute::$name, EnRoutePaused::$name => new TripStatusResource($latestTrip->state),
                default => new WorkOrderStatusResource($this->state),
            };
        } elseif ($this->isLatestWorkOrderTripOfAuthenticatedTechnician($latestTrip)) {
            $responseData['status'] = match ($this->state->getValue()) {
                ReadyToSchedule::$name, Paused::$name, Canceled::$name, QualityCheck::$name, Completed::$name => new WorkOrderStatusResource($this->state),
                ReadyToInvoice::$name => null,
                default => new TripStatusResource($latestTrip->state),
            };
        }

        return new WorkOrderStatusResource($this->state);
    }

    private function findGroupSlug(string $group, ListResource $workOrder): string
    {
        switch ($group) {
            case 'priority':
                return Str::slug($workOrder->priority);
            case 'status':
                return $workOrder->status_slug ?? '';
            case 'category':
                return ! empty($workOrder->problem_categories_label) ? Str::slug($workOrder->problem_categories_label) : '';
            case 'assignee':
                return ! empty($workOrder->assignee_ids_for_group) ? Str::slug($workOrder->assignee_ids_for_group) : '';
            case 'technician':
                return ! empty($workOrder->technicians_ids_for_group) ? Str::slug($workOrder->technicians_ids_for_group) : '';
            case 'tag':
                return ! empty($workOrder->tag_ids_for_group) ? Str::slug($workOrder->tag_ids_for_group) : '';
            case 'health_score':
                return ! empty($workOrder->health_score) ? $workOrder->health_score : '';
            default:
                return '';
        }
    }
}

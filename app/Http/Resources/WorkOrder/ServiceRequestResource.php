<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\ServiceCallStatus;
use App\Http\Resources\ServiceRequest\ServiceRequestTypeResource;
use App\Models\ServiceRequest;
use App\Models\WorkOrderServiceCall;
use App\States\WorkOrders\Scheduled;
use App\States\WorkOrders\WorkInProgress;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceRequest
 */
class ServiceRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @throws Exception
     */
    public function toArray(Request $request): array
    {
        $propertyAccessInfo = [
            'access_method' => $this->property_access_method ?? null,
            'access_code' => $this->property_access_code ?? null,
            'access_note' => $this->property_access_note ?? null,
        ];

        $responseData = [
            'service_request_id' => $this->service_request_uuid,
            'service_request_number' => $this->service_request_number,
            'description_updated_by' => $this->latestDescription?->createdUser?->getName(),
            'description_updated_at' => $this->latestDescription?->created_at->toIso8601String(),
            'description' => $this->latestDescription?->description,
            'date_added' => $this->created_at?->toIso8601String(),
            'type' => new ServiceRequestTypeResource($this->type),
            'priority' => new PriorityResource($this->priority),
            'property' => new PropertyResource($this->property, $propertyAccessInfo),
            'media' => MediaResource::collection($this->media),
            'timezone' => $this->timezone->name ?? '',

            'work_orders' => $this->workOrders ? WorkOrderSubResource::collection($this->workOrders) : '',

        ];

        return $responseData;
    }

    protected function isOpenWorkOrderOfAuthenticatedTechnician(?WorkOrderServiceCall $latestServiceCall): bool
    {
        return $latestServiceCall?->status === ServiceCallStatus::ACTIVE()
            && $this->state->equals(Scheduled::class, WorkInProgress::class)
            && request()->user()?->is($latestServiceCall->appointment?->technician->user);
    }

    protected function isLatestWorkOrderTripOfAuthenticatedTechnician(?WorkOrderServiceCall $latestServiceCall): bool
    {
        return (bool) request()->user()?->is($latestServiceCall?->appointment?->technician->user);
    }
}

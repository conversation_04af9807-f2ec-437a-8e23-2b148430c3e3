<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\WorkOrderTask;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderTask
 */
class ProblemCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            'label' => $this->issue->problemDiagnosis->subCategory->problemCategory->label ?? null,
        ];
    }
}

<?php

namespace App\Http\Resources\WorkOrder\List;

use App\Enums\Boolean;
use App\Enums\ScheduleTypes;
use App\Http\Resources\WorkOrder\TripStatusResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\WorkOrderServiceCall
 */
class ActiveTripResource extends JsonResource
{
    /**
     * Transform the resource into an array based on the request source.
     *
     * @param  Request  $request  The incoming HTTP request.
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Determine which set of trip details to return based on the request source.
        return match ($request->requestSource()) {
            'mobile' => $this->mobileDeviceTripDetails(),
            default => $this->webTripDetails(),
        };
    }

    /**
     * Prepare trip details specifically for mobile devices.
     *
     * @return array<int|string, mixed>
     */
    public function mobileDeviceTripDetails(): array
    {
        $response = [
            'trip_id' => $this->work_order_service_call_uuid,
            'is_active' => $this->is_active === Boolean::YES(),
            'status' => new TripStatusResource($this->state),
        ];

        $appointmentDetails = [
            'schedule_start_time' => $this->scheduled_start_time?->toIso8601String(),
            'schedule_end_time' => $this->scheduled_end_time?->toIso8601String(),
            'total_work_time_in_sec' => $this->adjusted_labor_time_in_sec ?? 0,
            'total_travel_time_in_sec' => $this->adjusted_drive_time_in_sec ?? 0,
        ];

        // Conditionally add time-related fields if they exist.
        if ($this->en_route_at) {
            $appointmentDetails['enroute_started_at'] = $this->en_route_at->toIso8601String();
        }
        if ($this->en_route_timer_paused_at) {
            $appointmentDetails['enroute_paused_at'] = $this->en_route_timer_paused_at->toIso8601String();
        }
        if ($this->en_route_timer_resumed_at) {
            $appointmentDetails['enroute_resumed_at'] = $this->en_route_timer_resumed_at->toIso8601String();
        }
        if ($this->work_started_at) {
            $appointmentDetails['work_started_at'] = $this->work_started_at->toIso8601String();
        }
        if ($this->work_timer_paused_at) {
            $appointmentDetails['work_paused_at'] = $this->work_timer_paused_at->toIso8601String();
        }
        if ($this->work_timer_resumed_at) {
            $appointmentDetails['work_resumed_at'] = $this->work_timer_resumed_at->toIso8601String();
        }

        $response['appointment'] = $appointmentDetails;

        return $response;
    }

    /**
     * Prepare trip details specifically for web applications.
     *
     * @return array<int|string, mixed>
     */
    public function webTripDetails(): array
    {
        return [
            'trip_id' => $this->work_order_service_call_uuid,
            'trip_number' => $this->work_order_service_call_number,
            'is_active' => $this->is_active === Boolean::YES(),
            'status' => new TripStatusResource($this->state),
            'appointment' => $this->getAppointmentDetails(),
            'schedule_start_time' => $this->scheduled_start_time?->toIso8601String(),
            'schedule_end_time' => $this->scheduled_end_time?->toIso8601String(),
            'formatted_schedule_date' => $this->getFormattedScheduleDate(),
            'total_drive_time_in_sec' => $this->adjusted_drive_time_in_sec ?? 0,
            'total_labor_time_in_sec' => $this->adjusted_labor_time_in_sec ?? 0,
            $this->mergeWhen($this->en_route_at, function () {
                return [
                    'en_route_at' => $this->en_route_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->en_route_timer_paused_at, function () {
                return [
                    'en_route_paused_at' => $this->en_route_timer_paused_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->en_route_timer_resumed_at, function () {
                return [
                    'en_route_resumed_at' => $this->en_route_timer_resumed_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->work_started_at, function () {
                return [
                    'work_started_at' => $this->work_started_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->work_timer_paused_at, function () {
                return [
                    'work_paused_at' => $this->work_timer_paused_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->work_timer_resumed_at, function () {
                return [
                    'work_resumed_at' => $this->work_timer_resumed_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->work_completed_at, function () {
                return [
                    'work_completed_at' => $this->work_completed_at->toIso8601String(),
                ];
            }),
        ];
    }

    /**
     * Get details of the associated appointment (technician, vendor, or Lula).
     *
     * @return array<int|string, mixed>
     */
    public function getAppointmentDetails(): array
    {
        if (! empty($this->technicianAppointment)) {
            $appointment = [
                'type' => ScheduleTypes::IN_HOUSE(),
                'name' => $this->technicianAppointment->technician?->user?->getName(),
            ];
        }

        if (! empty($this->vendorAppointment)) {
            $appointment = [
                'type' => ScheduleTypes::THIRD_PARTY_VENDOR(),
                'name' => $this->vendorAppointment->vendor?->getName(),
            ];
        }

        if (! empty($this->lulaAppointment)) {
            $appointment = [
                'type' => ScheduleTypes::LULA_PRO(),
                'name' => 'Lula Provider',
            ];
        }

        return $appointment ?? [];
    }

    /**
     * Format the scheduled date range.
     */
    private function getFormattedScheduleDate(): ?string
    {
        if (empty($this->scheduled_start_time) || empty($this->scheduled_end_time)) {
            return null;
        }

        $formattedStart = Carbon::parse($this->scheduled_start_time)->format('M j, Y g A');
        $formattedEnd = Carbon::parse($this->scheduled_end_time)->format('g A');

        return $formattedStart . ' - ' . $formattedEnd;
    }
}

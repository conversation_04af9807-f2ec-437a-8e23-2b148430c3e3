<?php

namespace App\Http\Resources\WorkOrder\List;

use App\Enums\Boolean;
use App\Enums\ScheduleTypes;
use App\Enums\ServiceCallStatus;
use App\Enums\Trip;
use App\Http\Resources\WorkOrder\TaskMaterialResource;
use App\Http\Resources\WorkOrder\TripStatusResource;
use App\Models\LulaAppointment;
use App\Models\TechnicianAppointment;
use App\Models\WorkOrderServiceCall;
use App\States\ServiceCalls\ReScheduled;
use App\States\ServiceCalls\ScheduleInPending;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderServiceCall
 */
class ListTaskAppointmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var TechnicianAppointment $appointment */
        $appointment = $this->appointment ?? null;

        /** @var LulaAppointment $lulaAppointment */
        $lulaAppointment = $this->lulaAppointment ?? null;

        $tripType = '';

        if (! empty($appointment)) {
            $tripType = ScheduleTypes::IN_HOUSE();
        } elseif (! empty($lulaAppointment)) {
            $tripType = ScheduleTypes::LULA_PRO();
        }

        $response = [
            'appointment_id' => $this->work_order_service_call_uuid,
            'is_active' => $this->is_active === Boolean::YES(),
            'status' => new TripStatusResource($this->state),
            'is_canceled' => $this->status === ServiceCallStatus::CANCELED(),
        ];

        return match ($tripType) {
            ScheduleTypes::IN_HOUSE() => $this->inHouserTechnicianTripData($response),
            ScheduleTypes::LULA_PRO() => $this->lulaNetWorkProTripData($response),
            default => $response
        };
    }

    /**
     * Create in house trip response data
     *
     * @param  array<string, mixed>  $response
     * @return array<string, mixed>
     */
    public function inHouserTechnicianTripData(&$response): array
    {
        $response = array_merge($response, [
            'start_time' => $this->scheduled_start_time?->toIso8601String(),
            'end_time' => $this->scheduled_end_time?->toIso8601String(),
            'service_notes' => $this->service_notes,
            'trip_end_with' => collect(Trip::workTypeOptions())
                ->where('value', $this->trip_end_with)
                ->first(),
            'materials' => ! empty($this->workOrderTaskMaterials) ? TaskMaterialResource::collection($this->workOrderTaskMaterials) : [],
        ]);

        if (! empty($this->appointment)) {
            $response['started_at'] = $this->when(! empty($this->appointment->actual_start_time), $this->appointment->actual_start_time?->toIso8601String());
            $response['ended_at'] = $this->when(! empty($this->appointment->actual_end_time), $this->appointment->actual_end_time?->toIso8601String());

            $response['technician'] = [
                'technician_id' => $this->appointment->technician->technician_uuid,
                'name' => $this->appointment->technician->user?->getName(),
                'profile_pic' => null,
            ];

            $response['is_rescheduled'] = $this->state->equals(ReScheduled::class);
        }

        return $response;
    }

    /**
     * Create lula pro network response data
     *
     * @param  array<string, mixed>  $response
     * @return array<string, mixed>
     */
    public function lulaNetWorkProTripData(&$response): array
    {
        if ($this->state->equals(ScheduleInPending::class)) {
            $response['status'] = null;
        }

        $response['is_rescheduled'] = $this->state->equals(ReScheduled::class);

        return $response;
    }
}

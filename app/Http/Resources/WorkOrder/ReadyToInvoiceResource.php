<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\WorkOrder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class ReadyToInvoiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'work_order_id' => $this->work_order_uuid,
            'status' => new WorkOrderStatusResource($this->state),
            'abilities' => $this->when(! $request->expectsDevice(), $this->resolveStateAbilities()),
        ];
    }
}

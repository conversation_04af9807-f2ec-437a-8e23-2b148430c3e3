<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\ScheduleTypes;
use App\Models\LulaAppointment;
use App\Models\TechnicianAppointment;
use App\Models\VendorAppointment;
use App\Models\WorkOrderTask;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderTask
 */
class ProviderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var TechnicianAppointment $appointment */
        $appointment = $this->appointment ?? null;
        $tripType = '';
        /** @var LulaAppointment $lulaAppointment */
        $lulaAppointment = $this->lulaAppointment ?? null;
        /** @var VendorAppointment $vendorAppointment */
        $vendorAppointment = $this->vendorAppointment ?? null;
        if (! empty($appointment)) {
            return ['provider' => ScheduleTypes::IN_HOUSE()];
        } elseif (! empty($lulaAppointment)) {
            return ['provider' => ScheduleTypes::LULA_PRO()];
        } elseif (! empty($vendorAppointment)) {
            return ['provider' => 'vendor', 'name' => $this->vendorAppointment->vendor?->company_name ?? ''];
        } else {
            return ['name' => ''];
        }

    }
}

<?php

namespace App\Http\Resources\WorkOrder;

use App\States\ServiceCalls\ServiceCallState;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ServiceCallState
 */
class TripStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => ($request->expectsDevice()) ? $this->labelForMobile() : $this->label(),
            'value' => $this->getValue(),
            'color_class' => $this->colorClass(),
        ];
    }
}

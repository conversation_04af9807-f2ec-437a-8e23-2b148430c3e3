<?php

namespace App\Http\Resources\WorkOrder\Trip;

use App\Http\Resources\WorkOrder\MediaResource;
use App\Http\Resources\WorkOrder\TripStatusResource;
use App\Http\Resources\WorkOrderIssue\MaterialResource;
use App\States\ServiceCalls\Done;
use App\States\ServiceCalls\MissingInfo;
use App\States\ServiceCalls\Unresolved;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin WorkOrderServiceCall */
class TripDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $responseData = [
            'trip_id' => $this->work_order_service_call_uuid,
            'trip_number' => $this->work_order_service_call_number,
            'work_order_number' => $this->workOrder->work_order_number,
            'start_time' => $this->scheduled_start_time->toIso8601String(),
            'end_time' => $this->scheduled_end_time->toIso8601String(),
            'status' => new TripStatusResource($this->state),
            'modified_user' => [
                'user_id' => $this->lastModifiedUser->user_uuid ?? null,
                'name' => $this->lastModifiedUser?->getName() ?? null,
                'modified_at' => $this->last_modified_at?->toIso8601String(),
            ],
        ];

        if ($this->state->equals(Done::class, Unresolved::class)) {
            $responseData['additional_notes'] = $this->additional_notes;
            $responseData['service_notes'] = $this->service_notes;
            $responseData['appointment'] = [
                'technician_appointment_id' => $this->appointment->technician_appointment_uuid,
                'technician' => [
                    'technician_id' => $this->appointment->technician->technician_uuid,
                    'name' => $this->appointment->technician->user->first_name . ' ' . $this->appointment->technician->user->middle_name . ' ' . $this->appointment->technician->user->last_name,
                ],
            ];
            $responseData['media'] = MediaResource::collection($this->tripIssues[0]->workOrderIssue->media);
            $responseData['materials'] = MaterialResource::collection($this->tripIssues[0]->workOrderIssue->materials);
        }

        if ($this->state->equals(MissingInfo::class)) {
            $responseData['note_to_provider'] = $this->note_to_provider;
        }

        return $responseData;
    }
}

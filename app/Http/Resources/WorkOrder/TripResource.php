<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\Boolean;
use App\Enums\ScheduleTypes;
use App\Enums\Trip;
use App\Enums\WorkToPerformTypes;
use App\Models\LulaAppointment;
use App\Models\TechnicianAppointment;
use App\Models\VendorAppointment;
use App\Models\WorkOrderServiceCall;
use App\States\ServiceCalls\Paused;
use App\States\ServiceCalls\ReScheduled;
use App\States\ServiceCalls\Scheduled;
use App\States\ServiceCalls\ScheduleInPending;
use App\States\ServiceCalls\ScheduleInProgress;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderServiceCall
 */
class TripResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var TechnicianAppointment $appointment */
        $appointment = $this->appointment ?? null;

        /** @var LulaAppointment $lulaAppointment */
        $lulaAppointment = $this->lulaAppointment ?? null;

        /** @var VendorAppointment $vendorAppointment */
        $vendorAppointment = $this->vendorAppointment ?? null;

        $tripType = '';

        if (! empty($appointment)) {
            $tripType = ScheduleTypes::IN_HOUSE();
        } elseif (! empty($lulaAppointment)) {
            $tripType = ScheduleTypes::LULA_PRO();
        } elseif (! empty($vendorAppointment)) {
            $tripType = ScheduleTypes::THIRD_PARTY_VENDOR();
        }

        $responseData = [
            'trip_id' => $this->work_order_service_call_uuid,
            'trip_number' => $this->work_order_service_call_number,
            'is_active' => $this->is_active === Boolean::YES(),
            'status' => new TripStatusResource($this->state),
            'last_modified_at' => $this->last_modified_at?->toIso8601String(),
        ];

        return match ($tripType) {
            ScheduleTypes::IN_HOUSE() => $this->inHouserTechnicianTripData($responseData),
            ScheduleTypes::LULA_PRO() => $this->lulaNetWorkProTripData($responseData),
            ScheduleTypes::THIRD_PARTY_VENDOR() => $this->vendorTripData($responseData),
            default => $responseData
        };
    }

    /**
     * Create in house trip response data
     *
     * @param  array<string, mixed>  $responseData
     * @return array<string, mixed>
     */
    public function inHouserTechnicianTripData(&$responseData): array
    {
        $tripEndWithType = null;

        if ($this->trip_end_with === Trip::PARTIALLY_COMPLETED()) {
            $tripEndWithType = collect(Trip::partialCompleteOptions())
                ->where('value', $this->trip_end_with_type)
                ->first();
        } elseif ($this->trip_end_with === Trip::NO_WORK()) {
            $tripEndWithType = collect(Trip::noWorkOptions())
                ->where('value', $this->trip_end_with_type)
                ->first();
        }

        $responseData = array_merge($responseData, [
            'start_time' => $this->scheduled_start_time?->toIso8601String(),
            'end_time' => $this->scheduled_end_time?->toIso8601String(),
            'materials' => ! empty($this->workOrderTaskMaterials) ? TaskMaterialResource::collection($this->workOrderTaskMaterials) : [],
            'quote_id' => $this->createdQuote->quote_uuid ?? null,
            'media' => MediaResource::collection($this->media),
            'trip_end_with' => collect(Trip::workTypeOptions())
                ->where('value', $this->trip_end_with)
                ->first(),
            'trip_end_with_type' => $tripEndWithType,
            'reason' => $this->trip_end_with_reason,
            'is_rescheduled' => $this->state->equals(ReScheduled::class),
            'service_notes' => $this->service_notes,
            'disable_materials' => $this->work_to_perform === WorkToPerformTypes::QUOTE_TASK(),
            'resumed_at' => $this->when(! empty($this->appointment->work_timer_resumed_at), $this->appointment->work_timer_resumed_at?->toIso8601String()),
            'trip_type' => ScheduleTypes::IN_HOUSE(),
            'scheduled_notes' => $this->when(! empty($this->scheduled_notes), $this->scheduled_notes),
        ]);

        if (! empty($this->appointment)) {
            $responseData['technician'] = [
                'name' => $this->appointment->technician->user?->getName(),
                'technician_id' => $this->appointment->technician->technician_uuid,
                'profile_pic' => $this->appointment->technician->user?->profile_pic,
            ];

            if (! empty($this->appointment->actual_start_time)) {
                $responseData['started_at'] = $this->appointment->actual_start_time->toIso8601String();
            }

            if (! empty($this->appointment->actual_end_time)) {
                $responseData['ended_at'] = $this->appointment->actual_end_time->toIso8601String();
            }

            $responseData['rescheduled_reason'] = $this->appointment->rescheduled_reason;
            $responseData['elapse_time_in_sec'] = $this->appointment->adjusted_elapse_time_in_sec;
            $responseData['en_route_at'] = ! empty($this->appointment->enroute_at) ? $this->appointment->enroute_at->toIso8601String() : null;
            $responseData['travel_time_in_sec'] = $this->appointment->adjusted_travel_time_in_sec ?? 0;
        }

        // If the trip is paused status, add these details
        if ($this->state->equals(Paused::class)) {
            $responseData['paused_at'] = $this->appointment->work_timer_paused_at?->toIso8601String();
        }

        return $responseData;
    }

    /**
     * Create lula pro network response data
     *
     * @param  array<string, mixed>  $responseData
     * @return array<string, mixed>
     */
    public function lulaNetWorkProTripData(&$responseData): array
    {
        /** @var LulaAppointment $lulaAppointment */
        $lulaAppointment = $this->lulaAppointment ?? null;

        $responseData = array_merge($responseData, [
            'trip_type' => ScheduleTypes::LULA_PRO(),
        ]);

        if ($this->state->equals(ScheduleInPending::class)) {
            $responseData['status'] = null;

            return $responseData;
        }

        if (! empty($lulaAppointment)) {
            $responseData['wo_reference_number'] = $lulaAppointment->work_order_reference_number;
            $responseData['category'] = $lulaAppointment->service_category_label;

            if ($this->state->equals(ScheduleInProgress::class)) {
                return $responseData;
            }

            if ($this->state->equals(Scheduled::class, ReScheduled::class)) {
                if (! empty($lulaAppointment->rescheduled_reason)) {
                    $responseData['is_rescheduled'] = true;
                }
            }

            $responseData['start_time'] = $this->scheduled_start_time?->toIso8601String();
            $responseData['end_time'] = $this->scheduled_end_time?->toIso8601String();

            if (! empty($lulaAppointment->estimated_return_start_time)) {
                $responseData['estimated_return_start_time'] = $lulaAppointment->estimated_return_start_time->toIso8601String();
            }

            if (! empty($lulaAppointment->estimated_return_end_time)) {
                $responseData['estimated_return_end_time'] = $lulaAppointment->estimated_return_end_time->toIso8601String();
            }

            if (! empty($lulaAppointment->paused_reason)) {
                $responseData['paused_reason'] = $lulaAppointment->paused_reason;
            }

            if (! empty($lulaAppointment->cancellation_reason)) {
                $responseData['cancellation_reason'] = $lulaAppointment->cancellation_reason;
            }

            if (! empty($lulaAppointment->elapse_time_in_sec)) {
                $responseData['elapse_time_in_sec'] = $lulaAppointment->elapse_time_in_sec;
            }
        }

        return $responseData;
    }

    /**
     * Create lula pro network response data
     *
     * @param  array<string, mixed>  $responseData
     * @return array<string, mixed>
     */
    public function vendorTripData(&$responseData): array
    {
        /** @var VendorAppointment $vendorAppointment */
        $vendorAppointment = $this->vendorAppointment ?? null;

        $responseData = array_merge($responseData, [
            'type' => ScheduleTypes::THIRD_PARTY_VENDOR(),
        ]);

        if (! empty($vendorAppointment->vendorAllocations)) {
            $responseData['notified_vendor_count'] = count($vendorAppointment->vendorAllocations);

            if (count($vendorAppointment->vendorAllocations) === 1) {
                $responseData['appointment']['vendor_details'] = [
                    'name' => $vendorAppointment->vendor?->getName() ?? null,
                    'vendor_id' => $vendorAppointment->vendor->vendor_uuid ?? null,
                    'vendor_instructions' => $vendorAppointment->vendor_instructions ?? null,
                ];
            }
        }

        if (! empty($this->scheduled_start_time) && ! empty($this->scheduled_end_time)) {
            $responseData['appointment']['schedule_start_time'] = $this->scheduled_start_time->toIso8601String();
            $responseData['appointment']['schedule_end_time'] = $this->scheduled_end_time->toIso8601String();

            $responseData['technician'] = [
                'name' => $vendorAppointment->vendor?->getName() ?? null,
            ];
        }

        return $responseData;
    }
}

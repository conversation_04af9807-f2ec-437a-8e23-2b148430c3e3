<?php

namespace App\Http\Resources\WorkOrder;

use App\Http\Resources\WorkOrderIssue\WorkOrderIssueResource;
use App\Models\WorkOrder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class CancelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'work_order_id' => $this->work_order_uuid,
            'status' => new WorkOrderStatusResource($this->state),
            'canceled_at' => $this->canceled_at?->toIso8601String(),
            'canceled_reason' => $this->canceled_reason,
            'work_order_issues' => WorkOrderIssueResource::collection($this->workOrderIssues),
            'abilities' => $this->when(! $request->expectsDevice(), $this->resolveStateAbilities()),
        ];
    }
}

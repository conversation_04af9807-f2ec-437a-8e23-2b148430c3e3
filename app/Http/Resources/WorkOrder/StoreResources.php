<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\WorkOrder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class StoreResources extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'work_order_id' => $this->work_order_uuid,
            'work_order_number' => $this->work_order_number,
            'created_at' => ! empty($this->created_at) ? $this->created_at->toIso8601String() : null,
        ];
    }
}

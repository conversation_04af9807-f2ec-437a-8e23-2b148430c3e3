<?php

namespace App\Http\Resources\WorkOrder;

use App\States\WorkOrders\Paused;
use App\States\WorkOrders\ReadyToSchedule;
use App\States\WorkOrders\WorkOrderState;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderState
 */
class WorkOrderStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $label = $this->label();
        if ($request->expectsDevice()) {
            if ($this->getValue() === ReadyToSchedule::$name) {
                $label = 'Awaiting New Trip';
            }
            if ($this->getValue() === Paused::$name) {
                $label = 'Work Order Paused';
            }
        }

        return [
            'label' => $label,
            'value' => $this->getValue(),
            'color_class' => $this->colorClass(),
        ];
    }
}

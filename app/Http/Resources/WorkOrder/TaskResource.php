<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\Boolean;
use App\Enums\ScheduleTypes;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderTask;
use App\States\ServiceCalls\ReScheduled;
use App\States\ServiceCalls\Scheduled;
use App\States\ServiceCalls\ScheduleInProgress;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderTask
 */
class TaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $responseData = [
            'task_id' => $this->work_order_task_uuid,
            'estimate_duration' => $this->latestServiceCalls->first()->duration_minutes ?? null,
        ];

        if ($request->expectsDevice()) {
            // For Mobile Device
            $responseData['problem'] = [
                'category' => $this->problemDiagnosis->subCategory->problemCategory->label ?? null,
            ];
            $responseData['appointment'] = ! empty($this->latestServiceCalls->first()) ? new TripResource($this->latestServiceCalls->first()) : null;

            if (! empty($this->latestServiceCalls)) {
                $currentTechnicianLatestServiceCall = $this->latestServiceCalls->first(function ($serviceCall) use ($request) {
                    return ! empty($serviceCall->appointment->technician->user) && $request->user() &&
                        $request->user()->is($serviceCall->appointment->technician->user);
                });

                $trips = [];
                foreach ($this->latestServiceCalls as $serviceCall) {
                    if (! empty($serviceCall->appointment->technician->user) && $request->user() && $request->user()->is($serviceCall->appointment->technician->user)) {
                        $isTechLatestTrip = false;
                        if (
                            $currentTechnicianLatestServiceCall &&
                            $currentTechnicianLatestServiceCall->work_order_service_call_uuid === $serviceCall->work_order_service_call_uuid
                        ) {
                            $isTechLatestTrip = true;
                        }
                        $trips[] = new TechnicianAppointmentResource($serviceCall, $isTechLatestTrip);
                    }
                }
                $responseData['trips'] = $trips;
            }

            $responseData['quotes'] = ! empty($this->latestQuotes->first()) ? new QuoteResource($this->latestQuotes->first()) : null;
        } else {
            // For Web
            $responseData['problem'] = [
                'category' => [
                    'category_id' => $this->problemDiagnosis->subCategory->problemCategory->problem_category_uuid ?? null,
                    'label' => $this->problemDiagnosis->subCategory->problemCategory->label ?? null,
                ],
                'sub_category' => [
                    'sub_category_id' => $this->problemDiagnosis->subCategory->problem_sub_category_uuid ?? null,
                    'label' => $this->problemDiagnosis->subCategory->label ?? null,
                ],
                'diagnosis' => [
                    'diagnosis_id' => $this->problemDiagnosis->problem_diagnosis_uuid ?? null,
                    'label' => $this->problemDiagnosis->label ?? null,
                ],
            ];
            $inHouseTrip = [];

            if ($this->latestServiceCalls->whereNotNull('technician_appointment_id')->isNotEmpty()) {

                $inHouseTrip = TripResource::collection($this->latestServiceCalls->whereNotNull('technician_appointment_id'))->resolve();
            }

            $lulaTrips = $this->latestServiceCalls->whereNotNull('lula_appointment_id');

            if ($lulaTrips->isNotEmpty()) {
                /** @var WorkOrderServiceCall $lastLulaTrip */
                $lastLulaTrip = $lulaTrips->first();
                $lulaAppointment = [
                    'appointment_id' => $lastLulaTrip->work_order_service_call_uuid ?? null,
                    'status' => ! empty($lastLulaTrip->state) ? new TripStatusResource($lastLulaTrip->state) : null,
                    'last_modified_at' => ! empty($lastLulaTrip->last_modified_at) ? $lastLulaTrip->last_modified_at->toIso8601String() : null,
                    'is_active' => ! empty($lastLulaTrip->is_active) ? $lastLulaTrip->is_active === Boolean::YES() : null,
                    'trip_type' => ScheduleTypes::LULA_PRO(),
                    'trip_number' => $lastLulaTrip->work_order_service_call_number,
                    'start_time' => ! empty($lastLulaTrip->scheduled_start_time) ? $lastLulaTrip->scheduled_start_time->toIso8601String() : null,
                    'end_time' => ! empty($lastLulaTrip->scheduled_end_time) ? $lastLulaTrip->scheduled_end_time->toIso8601String() : null,
                    'trips' => TripResource::collection($lulaTrips),
                    'invoice_urls' => $lastLulaTrip->vendorExternalInvoices->pluck('vendor_external_invoice_url')->toArray(),
                    'total_job_cost' => $lastLulaTrip->vendorExternalInvoices->sum('amount_in_cents'),
                ];

                if ($lastLulaTrip->state->equals(ScheduleInProgress::class)) {
                    $lulaAppointment['start_time'] = null;
                    $lulaAppointment['end_time'] = null;
                }

                if ($lastLulaTrip->state->equals(Scheduled::class, ReScheduled::class)) {
                    if (! empty($lastLulaTrip->lulaAppointment->rescheduled_reason)) {
                        $lulaAppointment['is_rescheduled'] = true;
                    }
                }

                array_push($inHouseTrip, $lulaAppointment);
            }

            $vendorTrips = $this->latestServiceCalls->whereNotNull('vendor_appointment_id');

            if ($vendorTrips->isNotEmpty()) {
                $vendorTrips = TripResource::collection($vendorTrips)->resolve();

                if (count($vendorTrips)) {
                    foreach ($vendorTrips as $vendorTrip) {
                        array_push($inHouseTrip, $vendorTrip);
                    }
                }
            }

            $responseData['appointments'] = collect((array) $inHouseTrip)->sortBy('trip_number', descending: true)->toArray();

            $quotes = $this->latestQuotes;

            if ($quotes->isNotEmpty()) {
                $latestQuoteId = $quotes->first()->quote_uuid;
                $quotes = $quotes->map(function ($quote) use ($latestQuoteId) {
                    // Check your condition here
                    if ($quote->quote_uuid === $latestQuoteId) {
                        $quote->setAttribute('is_latest', true);
                    } else {
                        $quote->setAttribute('is_latest', false);
                    }

                    return $quote;
                });
            }
            $responseData['quotes'] = $quotes->isNotEmpty() ? QuoteResource::collection($quotes) : null;
        }

        return $responseData;
    }
}

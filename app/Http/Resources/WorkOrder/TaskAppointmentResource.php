<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\TechnicianAppointment;
use App\Models\WorkOrderServiceCall;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderServiceCall
 */
class TaskAppointmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var TechnicianAppointment $appointment */
        $appointment = $this->appointment;

        //TODO remove the paused_at and reason from this resource from new app build.
        $responseData = [
            'appointment_id' => $this->work_order_service_call_uuid,
            'start_time' => $this->scheduled_start_time?->toIso8601String(),
            'end_time' => $this->scheduled_end_time?->toIso8601String(),
            'duration_minutes' => $this->duration_minutes,
            'service_notes' => $this->when(! empty($this->service_notes), $this->service_notes ?? null),
            'enroute_at' => $this->when(! empty($appointment->enroute_at), $appointment->enroute_at?->toIso8601String()),
            'started_at' => $this->when(! empty($appointment->actual_start_time), $appointment->actual_start_time?->toIso8601String()),
        ];

        if (! $request->expectsDevice()) {
            // For Web
            $responseData['technician'] = [
                'name' => $this->appointment?->technician->user?->getName(),
                'technician_id' => $this->appointment?->technician->technician_uuid,
                'profile_pic' => null,
            ];

            if (! empty($this->appointment->rescheduled_reason)) {
                $responseData['rescheduled_reason'] = $this->appointment->rescheduled_reason;
                $responseData['is_rescheduled'] = (bool) $this->appointment->rescheduled_from;
            }
        }

        return $responseData;
    }
}

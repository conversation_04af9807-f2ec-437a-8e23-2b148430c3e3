<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\QuoteTaskStatus;
use App\Http\Resources\WorkOrder\Quote\QuoteMaterialResource;
use App\Models\QuoteTask;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin QuoteTask
 */
class QuoteTaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            'task_id' => $this->quote_task_uuid,
            'description' => $this->description,
            'status' => [
                'label' => QuoteTaskStatus::label($this->status),
                'value' => $this->status,
            ],
            'quote_task_number' => $this->quote_task_number,
            'markup_fee_type' => $this->when(! $request->expectsDevice(), $this->markup_fee_type),
            'markup_fee_type_value' => $this->when(! $request->expectsDevice(), $this->markup_fee_type_value),
            'markup_fee_in_cents' => $this->when(! $request->expectsDevice(), $this->markup_fee_in_cents),
            'labor_cost_in_cents' => $this->when(! $request->expectsDevice(), $this->cost_in_cents),
            'cost_in_cents' => $request->expectsDevice() ? $this->cost_in_cents : $this->total_cost_in_cents,
            'estimated_time' => $this->estimated_time,
            'materials' => QuoteMaterialResource::collection($this->quoteTaskMaterials),
            'media' => MediaResource::collection($this->media),
        ];
    }
}

<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\WorkOrderStatus;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderStatus
 */
class WorkOrderServiceRequestStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            'label' => $this->label,
            'value' => $this->slug,
            'color_class' => $this->status_color ?? '',
        ];

    }
}

<?php

namespace App\Http\Resources\WorkOrder;

use App\Exceptions\WorkOrderException;
use App\Models\WorkOrderServiceCall;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class StartWorkResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var WorkOrderServiceCall $latestTrip */
        $latestTrip = $this->latestTrips?->first();

        if ($latestTrip === null) {
            throw WorkOrderException::activeTripNotFound();
        }

        $responseData = [
            'work_order_id' => $this->work_order_uuid,
            'status' => new WorkOrderStatusResource($this->state),
            'abilities' => $this->abilities($request->requestSource()),
        ];

        // TO DO: change this trips array to trip
        if ($request->expectsDevice()) {
            $responseData['trips'] = [
                [
                    'trip_id' => $latestTrip->work_order_service_call_uuid,
                    'appointment' => [
                        'work_started_at' => $latestTrip->work_started_at->toIso8601String(),
                        'total_travel_time_in_sec' => $latestTrip->adjusted_drive_time_in_sec ?? null,
                    ],
                    'status' => new TripStatusResource($latestTrip->state),
                ],
            ];
        } else {
            $responseData['trip'] = $this->getTripDetails($latestTrip);
        }

        return $responseData;
    }

    /**
     * @param  WorkOrderServiceCall  $serviceCall
     * @return array<string, mixed>
     */
    protected function getTripDetails(WorkOrderServiceCall $trip): array
    {
        return [
            'trip_id' => $trip->work_order_service_call_uuid,
            'work_started_at' => $trip->work_started_at?->toIso8601String(),
            'total_drive_time_in_sec' => $trip->adjusted_drive_time_in_sec ?? null,
            'status' => new TripStatusResource($trip->state),
        ];
    }
}

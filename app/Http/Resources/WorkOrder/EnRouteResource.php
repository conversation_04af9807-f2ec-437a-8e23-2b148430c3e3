<?php

namespace App\Http\Resources\WorkOrder;

use App\Exceptions\WorkOrderException;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\States\ServiceCalls\EnRoute;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class EnRouteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @throws WorkOrderException
     */
    public function toArray(Request $request): array
    {
        /** @var WorkOrderServiceCall $latestTrip */
        $latestTrip = $this->latestTrips?->first();

        if ($latestTrip === null) {
            throw WorkOrderException::activeTripNotFound();
        }

        $responseData = [
            'work_order_id' => $this->work_order_uuid,
            'status' => $this->getWorkOrderStatus($request, $latestTrip),
            'abilities' => $this->abilities($request->requestSource()),
        ];

        // TO DO: change this trips array to trip
        if ($request->expectsDevice()) {
            $responseData['trips'] = [
                [
                    'trip_id' => $latestTrip->work_order_service_call_uuid,
                    'appointment' => [
                        'enroute_started_at' => $latestTrip->en_route_at?->toIso8601String(),
                    ],
                    'status' => new TripStatusResource($latestTrip->state),
                ],
            ];
        } else {
            $responseData['trip'] = $this->getTripDetails($latestTrip);
        }

        return $responseData;
    }

    /**
     * @param  WorkOrderServiceCall  $serviceCall
     */
    protected function getWorkOrderStatus(Request $request, WorkOrderServiceCall $trip): TripStatusResource|WorkOrderStatusResource
    {
        if (! $request->expectsDevice()) {
            return new WorkOrderStatusResource($this->state);
        }

        return match ($trip->state->getValue()) {
            EnRoute::$name => new TripStatusResource($trip->state),
            default => new WorkOrderStatusResource($this->state),
        };
    }

    /**
     * @param  WorkOrderServiceCall  $serviceCall
     * @return array<string, mixed>
     */
    protected function getTripDetails(WorkOrderServiceCall $trip): array
    {
        return [
            'trip_id' => $trip->work_order_service_call_uuid,
            'en_route_at' => $trip->en_route_at?->toIso8601String(),
            'status' => new TripStatusResource($trip->state),
        ];
    }
}

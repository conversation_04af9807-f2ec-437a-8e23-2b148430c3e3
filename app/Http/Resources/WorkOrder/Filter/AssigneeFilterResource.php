<?php

namespace App\Http\Resources\WorkOrder\Filter;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin User
 */
class AssigneeFilterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => trim($this->first_name . ' ' . $this->last_name),
            'value' => $this->user_uuid,
        ];
    }
}

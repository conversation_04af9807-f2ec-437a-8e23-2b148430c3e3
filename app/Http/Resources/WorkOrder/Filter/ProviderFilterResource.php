<?php

namespace App\Http\Resources\WorkOrder\Filter;

use App\Models\WorkOrderStatus;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderStatus
 */
class ProviderFilterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this['label'],
            'value' => $this['slug'],
        ];
    }
}

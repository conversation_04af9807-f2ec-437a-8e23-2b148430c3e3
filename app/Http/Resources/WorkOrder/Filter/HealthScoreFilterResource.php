<?php

namespace App\Http\Resources\WorkOrder\Filter;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class HealthScoreFilterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => ! empty($this->classification) ? Str::title(Str::replace('_', ' ', (string) $this->classification)) : 'unknown',
            'value' => $this->classification ?? 'unknown',
        ];
    }
}

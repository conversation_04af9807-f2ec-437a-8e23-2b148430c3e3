<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\Technician;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Technician */
class AppointmentTechnicianResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'technician_id' => $this->technician_uuid,
            'name' => $this->user?->getName(),
            'profile_pic' => $this->user?->profile_pic,
        ];
    }
}

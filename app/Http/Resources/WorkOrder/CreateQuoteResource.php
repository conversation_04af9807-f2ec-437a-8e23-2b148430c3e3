<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\QuoteStatus;
use App\Models\Quote;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Quote
 */
class CreateQuoteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'work_order_id' => ! empty($this->workOrder) ? $this->workOrder->work_order_uuid : null,
            'status' => ! empty($this->workOrder) ? new WorkOrderStatusResource($this->workOrder->state) : null,
            'abilities' => ! $request->expectsDevice() && ! empty($this->workOrder) ? $this->workOrder->resolveStateAbilities() : null,
            $this->mergeWhen(! $request->expectsDevice(), [
                'quotes' => [
                    'quote_id' => $this->quote_uuid,
                    'quote_number' => $this->quote_number,
                    'status' => [
                        'label' => QuoteStatus::label($this->status, $request->expectsDevice() ? 'mobile' : 'web'),
                        'value' => $this->status,
                    ],
                    'action_details' => [
                        'submitted_at' => $this->submitted_at?->toIso8601String(),
                        'submitted_by' => $this->submittedUser?->getName(),
                    ],
                    'quote_tasks' => QuoteTaskResource::collection($this->quoteTasks),
                ],
            ]),
        ];
    }
}

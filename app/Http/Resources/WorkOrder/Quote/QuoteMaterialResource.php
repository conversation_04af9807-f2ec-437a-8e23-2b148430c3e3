<?php

namespace App\Http\Resources\WorkOrder\Quote;

use App\Models\QuoteTaskMaterial;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin QuoteTaskMaterial
 */
class QuoteMaterialResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'material_id' => $this->quote_task_material_uuid,
            'label' => $this->label,
            'unit_price_in_cents' => $this->when(! empty($this->unitPriceWithoutMarkUp()), $this->unitPriceWithoutMarkUp()),
            'markup_fee_type' => $this->when(! $request->expectsDevice(), $this->markup_fee_type),
            'markup_fee_type_value' => $this->when(! $request->expectsDevice(), $this->markup_fee_type_value),
            'markup_fee_in_cents' => $this->when(! $request->expectsDevice(), $this->markup_fee_in_cents),
            'material_cost_in_cents' => $this->when(! $request->expectsDevice(), $this->cost_in_cents),
            'cost_in_cents' => $this->when(! is_null($this->total_cost_in_cents), $request->expectsDevice() ? $this->cost_in_cents : $this->total_cost_in_cents),
            'quantity' => $this->quantity,
            'quantity_type' => $this->quantity_type,
            'cost_type' => $this->cost_type,
        ];
    }
}

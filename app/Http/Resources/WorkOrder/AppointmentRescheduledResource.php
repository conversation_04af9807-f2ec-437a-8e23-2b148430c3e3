<?php

namespace App\Http\Resources\WorkOrder;

use App\Helpers\DataFormatHelper;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderTask;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderTask
 */
class AppointmentRescheduledResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var WorkOrderServiceCall $workOrderServiceCall */
        $workOrderServiceCall = $this->serviceCalls->first();

        return [
            'appointment_id' => $workOrderServiceCall->work_order_service_call_uuid,
            'start_time' => ! empty($workOrderServiceCall->scheduled_start_time) ? DataFormatHelper::dateFormat($workOrderServiceCall->scheduled_start_time) : null,
            'end_time' => ! empty($workOrderServiceCall->scheduled_end_time) ? DataFormatHelper::dateFormat($workOrderServiceCall->scheduled_end_time) : null,
            'duration_minutes' => $workOrderServiceCall->duration_minutes ?? 0,
            'status' => ! empty($this->workOrder) ? new WorkOrderStatusResource($this->workOrder->state) : [],
            'technicians' => ! empty($workOrderServiceCall->appointment) ? [new AppointmentTechnicianResource($workOrderServiceCall->appointment->technician)] : [],
            'reason' => $workOrderServiceCall->appointment->rescheduled_reason ?? null,
        ];
    }
}

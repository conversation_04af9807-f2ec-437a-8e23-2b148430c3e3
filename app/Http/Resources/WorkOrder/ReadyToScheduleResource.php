<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\WorkOrder;
use App\States\WorkOrders\Scheduled;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class ReadyToScheduleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'work_order_id' => $this->work_order_uuid,
            'status' => new WorkOrderStatusResource($this->state),
            // TODO: Need to implement abilities here, for now we are enabling schedule action
            //            'abilities' => $this->when(! $request->expectsDevice(), $this->resolveStateAbilities()),
            'abilities' => $this->when(! $request->expectsDevice(), [Scheduled::$actionName]),
        ];
    }
}

<?php

namespace App\Http\Resources\WorkOrder\Group;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TechnicianBasedGroupDataResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this['label'] ?? null,
            'total' => $this['total'] ?? 0,
            'order' => $this['order'] ?? null,
            'slug' => $this['slug'] ?? null,
        ];
    }
}

<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\Priority;
use App\Models\WorkOrder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin WorkOrder
 */
class PriorityUpdateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'work_order_id' => $this->work_order_uuid,
            'priority' => [
                'label' => $this->priority ? Str::title(Priority::from($this->priority)->name) : null,
                'value' => $this->priority ?? null,
            ],
        ];
    }
}

<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\QuoteStatus;
use App\Models\Quote;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\URL;

/**
 * @mixin Quote
 */
class QuoteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $responseData = [
            'quote_id' => $this->quote_uuid,
            'status' => [
                'label' => QuoteStatus::label($this->status, $request->expectsDevice() ? 'mobile' : 'web'),
                'value' => $this->status,
            ],
            'quote_tasks' => QuoteTaskResource::collection($this->quoteTasks),
            'pdf_link' => URL::temporarySignedRoute('pdf.quote', now()->addMinutes(240), ['quote' => $this->quote_uuid]),
        ];

        if (! $request->expectsDevice()) {
            $responseData['quote_number'] = $this->quote_number;
            $responseData['action_details'] = [
                'submitted_at' => $this->submitted_at?->toIso8601String(),
                'submitted_by' => $this->submittedUser?->getName(),
            ];
            if ($this->status === QuoteStatus::PENDING_APPROVAL()) {
                $responseData['action_details'] = array_merge($responseData['action_details'], [
                    'decided_at' => $this->reviewed_at?->toIso8601String(),
                    'decided_by' => $this->reviewedUser?->getName(),
                ]);
            }
            if ($this->status === QuoteStatus::APPROVED() || $this->status === QuoteStatus::PARTIALLY_APPROVED()) {
                $responseData['action_details'] = array_merge($responseData['action_details'], [
                    'decided_at' => $this->approved_at?->toIso8601String(),
                    'decided_by' => $this->approvedUser?->getName(),
                ]);
            }
            if ($this->status === QuoteStatus::REJECTED()) {
                $responseData['action_details'] = array_merge($responseData['action_details'], [
                    'decided_at' => $this->rejected_at?->toIso8601String(),
                    'decided_by' => $this->rejectedUser?->getName(),
                ]);
            }
            $responseData['is_latest'] = $this->is_latest ?? null;
        }

        return $responseData;
    }
}

<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin User */
class AssigneeListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'user_id' => $this->user_uuid,
            'name' => $this->getName(),
            'profile_pic' => $this->profile_pic,
        ];
    }
}

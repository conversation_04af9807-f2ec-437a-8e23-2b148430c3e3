<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\Boolean;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderTask;
use App\States\ServiceCalls\ReScheduled;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderTask
 */
class TaskScheduledAppointmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var WorkOrderServiceCall $workOrderServiceCall */
        $workOrderServiceCall = $this->latestServiceCalls->first();

        return [
            'appointment_id' => $workOrderServiceCall->work_order_service_call_uuid,
            'trip_number' => $workOrderServiceCall->work_order_service_call_number,
            'trip_status' => new TripStatusResource($workOrderServiceCall->state),
            'is_active' => $workOrderServiceCall->is_active === Boolean::YES(),
            'start_time' => $workOrderServiceCall->scheduled_start_time?->toIso8601String(),
            'end_time' => $workOrderServiceCall->scheduled_end_time?->toIso8601String(),
            'duration_minutes' => $workOrderServiceCall->duration_minutes ?? 0,
            'status' => $this->when(! empty($this->workOrder), new WorkOrderStatusResource($this->workOrder->state)),
            'technician' => $this->when(! empty($workOrderServiceCall->appointment), new AppointmentTechnicianResource($workOrderServiceCall->appointment?->technician)),
            'rescheduled_reason' => $workOrderServiceCall->appointment->rescheduled_reason ?? null,
            'is_rescheduled' => $workOrderServiceCall->state->equals(ReScheduled::class),
            'abilities' => $this->workOrder->resolveStateAbilities(),
            'last_modified_at' => $workOrderServiceCall->last_modified_at?->toIso8601String(),
            'work_performed' => $workOrderServiceCall->workPerformed(),
        ];
    }
}

<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\QuoteStatus;
use App\Models\Quote;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Quote
 */
class QuoteApproveResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'quotes' => [
                'quote_id' => $this->quote_uuid,
                'status' => [
                    'label' => QuoteStatus::label($this->status, $request->expectsDevice() ? 'mobile' : 'web'),
                    'value' => $this->status,
                ],
                'quote_tasks' => QuoteTaskResource::collection($this->quoteTasks),
                'quote_number' => $this->quote_number,
                'action_details' => [
                    'submitted_at' => $this->submitted_at?->toIso8601String(),
                    'submitted_by' => $this->submittedUser?->getName(),
                    'decided_at' => $this->approved_at?->toIso8601String(),
                    'decided_by' => $this->approvedUser?->getName(),
                ],
            ],
            'abilities' => $this->workOrder?->resolveStateAbilities(),
            'status' => new WorkOrderStatusResource($this->workOrder?->state),
        ];
    }
}

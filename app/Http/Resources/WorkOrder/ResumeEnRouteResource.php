<?php

namespace App\Http\Resources\WorkOrder;

use App\Exceptions\WorkOrderException;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\States\ServiceCalls\EnRoute;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class ResumeEnRouteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var WorkOrderServiceCall $latestTrip */
        $latestTrip = $this->latestTrips?->first();

        if ($latestTrip === null) {
            throw WorkOrderException::activeTripNotFound();
        }

        $responseData = [
            'work_order_id' => $this->work_order_uuid,
            'abilities' => $this->abilities($request->requestSource()),
            'status' => $this->getWorkOrderStatus($request, $latestTrip),
        ];

        // TO DO: change this trips array to trip
        if ($request->expectsDevice()) {
            $responseData['trips'] = [
                [
                    'trip_id' => $latestTrip->work_order_service_call_uuid,
                    'appointment' => [
                        'enroute_resumed_at' => $latestTrip->en_route_timer_resumed_at?->toIso8601String(),
                        'total_travel_time_in_sec' => $latestTrip->adjusted_drive_time_in_sec ?? null,
                    ],
                    'status' => new TripStatusResource($latestTrip->state),
                ],
            ];
        } else {
            $responseData['trip'] = $this->getTripDetails($latestTrip);
        }

        return $responseData;
    }

    /**
     * @param  WorkOrderServiceCall  $serviceCall
     */
    protected function getWorkOrderStatus(Request $request, WorkOrderServiceCall $trip): TripStatusResource|WorkOrderStatusResource
    {
        if (! $request->expectsDevice()) {
            return new WorkOrderStatusResource($this->state);
        }

        return match ($trip->state->getValue()) {
            EnRoute::$name => new TripStatusResource($trip->state),
            default => new WorkOrderStatusResource($this->state),
        };
    }

    /**
     * @param  WorkOrderServiceCall  $serviceCall
     * @return array<string, mixed>
     */
    protected function getTripDetails(WorkOrderServiceCall $trip): array
    {
        return [
            'trip_id' => $trip->work_order_service_call_uuid,
            'en_route_resumed_at' => $trip->en_route_timer_resumed_at?->toIso8601String(),
            'total_drive_time_in_sec' => $trip->adjusted_drive_time_in_sec ?? 0,
            'status' => new TripStatusResource($trip->state),
        ];
    }
}

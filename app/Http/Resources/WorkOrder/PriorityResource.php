<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\Priority;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class PriorityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this->resource ? Str::title(Priority::from($this->resource)->name) : null,
            'value' => $this->resource ?? null,
        ];
    }
}

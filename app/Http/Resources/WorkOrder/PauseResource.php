<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\WorkOrder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class PauseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'work_order_id' => $this->work_order_uuid,
            'work_order_task_id' => $this->tasks->first()?->work_order_task_uuid,
            'paused_reason' => $this->paused_reason,
            'paused_at' => $this->paused_at?->toIso8601String(),
            'status' => new WorkOrderStatusResource($this->state),
            'abilities' => $this->when(! $request->expectsDevice(), $this->resolveStateAbilities()),
        ];
    }
}

<?php

namespace App\Http\Resources\WorkOrder;

use App\Exceptions\WorkOrderException;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class PauseWorkResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var WorkOrderServiceCall $latestTrip */
        $latestTrip = $this->latestTrips?->first();

        if ($latestTrip === null) {
            throw WorkOrderException::activeTripNotFound();
        }

        $responseData = [
            'work_order_id' => $this->work_order_uuid,
            'abilities' => $this->abilities($request->requestSource()),
        ];

        if ($request->expectsDevice()) {
            $responseData['trips'] = [
                [
                    'trip_id' => $latestTrip->work_order_service_call_uuid,
                    'appointment' => [
                        'work_paused_at' => $latestTrip->work_timer_paused_at?->toIso8601String(),
                        'total_work_time_in_sec' => $latestTrip->adjusted_labor_time_in_sec ?? null,
                    ],
                    'status' => new TripStatusResource($latestTrip->state),
                ],
            ];
        } else {
            $responseData['trip'] = $this->getTripDetails($latestTrip);
        }

        return $responseData;
    }

    /**
     * @param  WorkOrderServiceCall  $serviceCall
     * @return array<string, mixed>
     */
    protected function getTripDetails(WorkOrderServiceCall $trip): array
    {
        return [
            'trip_id' => $trip->work_order_service_call_uuid,
            'work_paused_at' => $trip->work_timer_paused_at?->toIso8601String(),
            'total_labor_time_in_sec' => $trip->adjusted_labor_time_in_sec ?? null,
            'status' => new TripStatusResource($trip->state),
        ];
    }
}

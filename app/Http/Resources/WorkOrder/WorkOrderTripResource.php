<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\Boolean;
use App\Enums\ScheduleTypes;
use App\Http\Resources\WorkOrderIssue\MaterialResource;
use App\Http\Resources\WorkOrderIssue\WorkOrderIssueStatusResource;
use App\States\WorkOrderIssue\Unresolved;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\WorkOrderServiceCall
 */
class WorkOrderTripResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        if ($this->technician_appointment_id) {
            $type = ScheduleTypes::IN_HOUSE();
        } elseif ($this->vendor_appointment_id) {
            $type = ScheduleTypes::THIRD_PARTY_VENDOR();
        } elseif ($this->lula_appointment_id) {
            $type = ScheduleTypes::LULA_PRO();
        } else {
            $type = '';
        }

        // Response for Mobile
        if ($request->expectsDevice()) {
            $response = [
                'trip_id' => $this->work_order_service_call_uuid,
                'trip_number' => $this->work_order_service_call_number,
                'is_active' => $this->is_active === Boolean::YES(),
                'status' => new TripStatusResource($this->state),
                'last_modified_at' => $this->state_updated_at,
            ];

            $response['appointment']['schedule_start_time'] = $this->scheduled_start_time?->toIso8601String();
            $response['appointment']['schedule_end_time'] = $this->scheduled_end_time?->toIso8601String();

            // en-route start time
            if (! empty($this->en_route_at)) {
                $response['appointment']['enroute_started_at'] = $this->en_route_at->toIso8601String();
            }

            // En-route paused time
            if (! empty($this->en_route_timer_paused_at)) {
                $response['appointment']['enroute_paused_at'] = $this->en_route_timer_paused_at->toIso8601String();
            }

            // En-route resumed time
            if (! empty($this->en_route_timer_resumed_at)) {
                $response['appointment']['enroute_resumed_at'] = $this->en_route_timer_resumed_at->toIso8601String();
            }

            //  total en-route time
            if (! empty($this->adjusted_drive_time_in_sec)) {
                $response['appointment']['total_travel_time_in_sec'] = $this->adjusted_drive_time_in_sec ?? null;
            }

            // work start time
            if (! empty($this->work_started_at)) {
                $response['appointment']['work_started_at'] = $this->work_started_at->toIso8601String();
            }

            // work pause time
            if (! empty($this->work_timer_paused_at)) {
                $response['appointment']['work_paused_at'] = $this->work_timer_paused_at->toIso8601String();
            }

            // work resume time
            if (! empty($this->work_timer_resumed_at)) {
                $response['appointment']['work_resumed_at'] = $this->work_timer_resumed_at->toIso8601String();
            }

            // total work time
            $response['appointment']['total_work_time_in_sec'] = $this->adjusted_labor_time_in_sec ?? 0;

            //trip additional note
            if (! empty($this->additional_notes)) {
                $response['additional_notes'] = $this->additional_notes;
            }

            // Trip issue details
            if ($this->workOrderIssues->isNotEmpty()) {
                $response['issues'] = $this->issueResponse($this->workOrderIssues);
            }

            return $response;
        }

        return [
            'trip_id' => $this->work_order_service_call_uuid,
            'trip_number' => $this->work_order_service_call_number,
            'is_active' => $this->is_active === Boolean::YES(),
            'status' => new TripStatusResource($this->state),
            'appointment' => $this->getAppointmentDetails($type),
            $this->mergeWhen($type === 'third-party-vendor', function () {
                return [
                    'notified_vendor_count' => count($this->vendorAppointment->vendorAllocations),
                ];
            }),
            'schedule_start_time' => $this->scheduled_start_time?->toIso8601String(),
            'schedule_end_time' => $this->scheduled_end_time?->toIso8601String(),
            'total_drive_time_in_sec' => $this->adjusted_drive_time_in_sec ?? 0,
            'total_labor_time_in_sec' => $this->adjusted_labor_time_in_sec ?? 0,
            $this->mergeWhen($this->en_route_at, function () {
                return [
                    'en_route_at' => $this->en_route_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->en_route_timer_paused_at, function () {
                return [
                    'en_route_paused_at' => $this->en_route_timer_paused_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->en_route_timer_resumed_at, function () {
                return [
                    'en_route_resumed_at' => $this->en_route_timer_resumed_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->work_started_at, function () {
                return [
                    'work_started_at' => $this->work_started_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->work_timer_paused_at, function () {
                return [
                    'work_paused_at' => $this->work_timer_paused_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->work_timer_resumed_at, function () {
                return [
                    'work_resumed_at' => $this->work_timer_resumed_at->toIso8601String(),
                ];
            }),
            $this->mergeWhen($this->work_completed_at, function () {
                return [
                    'work_completed_at' => $this->work_completed_at->toIso8601String(),
                ];
            }),
            'additional_notes' => $this->additional_notes,
            'last_modified_at' => $this->state_updated_at,
        ];
    }

    /**
     * Generates a response for the given collection of work order issues.
     *
     * @param  Collection<int,WorkOrderIssue>  $workOrderIssues
     * @return array<string,mixed>
     */
    public function issueResponse(Collection $workOrderIssues): array
    {
        $response = [];
        foreach ($workOrderIssues as $workOrderIssue) {
            $issueResponse = [
                'issue_id' => $workOrderIssue->work_order_issue_uuid,
                'title' => $workOrderIssue->issue?->title,
                'status' => new WorkOrderIssueStatusResource($workOrderIssue->state),
            ];

            if ($workOrderIssue->state->equals(Unresolved::class)) {
                $issueResponse['decline_reason'] = $workOrderIssue->decline_reason;
            }

            if (! $workOrderIssue->state->equals(Unresolved::class)) {
                $issueResponse['materials'] = MaterialResource::collection($workOrderIssue->materials);
            }

            $response[] = $issueResponse;
        }

        return $response;
    }

    /**
     * get appointment details.
     */
    private function getAppointmentDetails($type): array
    {
        $appointmentData = [];
        if ($type === ScheduleTypes::IN_HOUSE() && $this->technicianAppointment) {
            $appointmentData['appointment_id'] = $this->technicianAppointment->technician_appointment_uuid;
            $appointmentData['name'] = $this->technicianAppointment->technician?->user?->getName();
            $appointmentData['type'] = $type;
            $appointmentData['profile_pic'] = null;
        } elseif ($type === ScheduleTypes::THIRD_PARTY_VENDOR() && $this->vendorAppointment) {
            $appointmentData['appointment_id'] = $this->vendorAppointment->vendor_appointment_uuid;
            $appointmentData['name'] = $this->vendorAppointment->vendor?->getName();
            $appointmentData['type'] = $type;
            $appointmentData['vendor_instructions'] = $this->vendorAppointment->vendor_instructions;
            $appointmentData['profile_pic'] = null;
        } elseif ($type === ScheduleTypes::LULA_PRO()) {
            $appointmentData['appointment_id'] = $this->lulaAppointment->lula_appointment_uuid;
            $appointmentData['name'] = 'Lula Provider';
            $appointmentData['type'] = $type;
            $appointmentData['profile_pic'] = null;
        }

        return $appointmentData;
    }
}

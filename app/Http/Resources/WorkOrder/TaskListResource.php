<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\WorkOrderTask;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderTask
 */
class TaskListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $latestServiceCall = $this->latestServiceCalls->first();

        return [
            'problem' => [
                'category' => $this->problemDiagnosis->subCategory->problemCategory->label ?? null,
            ],
            $this->mergeWhen(! empty($latestServiceCall), function () use ($latestServiceCall) {
                return [
                    'appointment' => ! empty($latestServiceCall) ? [
                        'start_time' => $latestServiceCall->scheduled_start_time?->toIso8601String(),
                        'end_time' => $latestServiceCall->scheduled_end_time?->toIso8601String(),
                        'started_at' => $this->when(
                            ! empty($latestServiceCall->appointment->actual_start_time),
                            $latestServiceCall->appointment?->actual_start_time?->toIso8601String(),
                        ),
                        'paused_at' => $this->when(! empty($latestServiceCall->timer_paused_at), $latestServiceCall->timer_paused_at?->toIso8601String()),
                        'resumed_at' => $this->when(! empty($latestServiceCall->timer_resumed_at), $latestServiceCall->timer_resumed_at?->toIso8601String()),
                        'elapse_time_in_sec' => $latestServiceCall->appointment->adjusted_elapse_time_in_sec ?? 0,
                        'status' => new TripStatusResource($latestServiceCall->state),
                    ] : [],

                ];
            }),
        ];
    }
}

<?php

namespace App\Http\Resources\WorkOrder;

use App\Models\WorkOrderAssignee;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin WorkOrderAssignee */
class WorkOrderAssigneeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'user_id' => $this->user->user_uuid ?? null,
            'name' => $this->user?->getName(),
            'profile_pic' => $this->user->profile_pic ?? null,
        ];
    }
}

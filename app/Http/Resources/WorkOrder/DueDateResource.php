<?php

namespace App\Http\Resources\WorkOrder;

use App\Helpers\DataFormatHelper;
use App\Models\WorkOrder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class DueDateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'work_order_id' => $this->work_order_uuid,
            'due_date' => $this->due_date ? DataFormatHelper::dateFormat($this->due_date) : null,
        ];
    }
}

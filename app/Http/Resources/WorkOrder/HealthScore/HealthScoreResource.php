<?php

namespace App\Http\Resources\WorkOrder\HealthScore;

use App\Models\WorkOrderHealthLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderHealthLog
 */
class HealthScoreResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $created_at = $this->created_at ?? null;

        return [
            'classification' => $this->healthTracker?->classification,
            'weight' => $this->healthTracker?->weight,
            'elapse_time' => $created_at ? $this->elapseTime(Carbon::parse($created_at), Carbon::now()) : null,
            'slug' => $this->healthTracker?->classification,
            'elapse_time_in_sec' => $created_at ? Carbon::parse($created_at) : 0,
        ];
    }

    public function elapseTime(Carbon $startDate, Carbon $endDate): string
    {
        // Calculate time differences
        $days = $endDate->diffInDays($startDate);
        $hours = $endDate->diffInHours($startDate) % 24; // Remaining hours after calculating days
        $minutes = $endDate->diffInMinutes($startDate) % 60; // Remaining minutes after hours

        if ($days > 0) {
            $timeDifference = sprintf('%dd %dh %dm', $days, $hours, $minutes);
        } else {
            $timeDifference = sprintf('%dh %dm', $hours, $minutes);
        }

        return $timeDifference;
    }
}

<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\ServiceCallStatus;
use App\Enums\WorkOrderSourceTypes;
use App\Helpers\DataFormatHelper;
use App\Http\Resources\Invoice\InvoiceListResource;
use App\Http\Resources\ResidentAvailability\ResidentAvailabilityResource;
use App\Http\Resources\ServiceRequest\WorkOrder\WorkOrderServiceRequestResource;
use App\Http\Resources\Tag\TagResource;
use App\Http\Resources\WorkOrder\HealthScore\HealthScoreResource;
use App\Http\Resources\WorkOrderIssue\WorkOrderIssueResource;
use App\Models\ResidentAvailability;
use App\Models\WorkOrder;
use App\Models\WorkOrderServiceCall;
use App\Services\Vendor\Enum\Service;
use App\States\ServiceCalls\EnRoute;
use App\States\ServiceCalls\EnRoutePaused;
use App\States\WorkOrders\Canceled;
use App\States\WorkOrders\ClaimPending;
use App\States\WorkOrders\Completed;
use App\States\WorkOrders\Created;
use App\States\WorkOrders\Paused;
use App\States\WorkOrders\QualityCheck;
use App\States\WorkOrders\ReadyToInvoice;
use App\States\WorkOrders\ReadyToSchedule;
use App\States\WorkOrders\Scheduled;
use App\States\WorkOrders\WorkInProgress;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class WorkOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @throws Exception
     */
    public function toArray(Request $request): array
    {
        $propertyAccessInfo = [
            'access_method' => $this->serviceRequest->property_access_method ?? null,
            'access_code' => $this->serviceRequest->property_access_code ?? null,
            'access_note' => $this->serviceRequest->property_access_note ?? null,
        ];

        $responseData = [
            'work_order_id' => $this->work_order_uuid,
            'work_order_number' => $this->work_order_number,
            'category' => ProblemCategoryResource::collection($this->workOrderIssues),
            'work_order_issues' => WorkOrderIssueResource::collection($this->workOrderIssues),
            'priority' => new PriorityResource($this->priority),
            'property' => $this->serviceRequest && $this->serviceRequest->property
                ? new PropertyResource($this->serviceRequest->property, $propertyAccessInfo)
                : null,
            'media' => MediaResource::collection($this->media),
            'timezone' => $this->timezone->name ?? '',
            'abilities' => $this->abilities($request->requestSource()),
            'nte_amount_in_cents' => $this->nte_amount_in_cents,
            'trips' => WorkOrderTripResource::collection($this->latestTrips),
        ];

        if ($this->state->equals(Paused::class)) {
            $responseData['paused_at'] = $this->paused_at?->toIso8601String();
            $responseData['paused_reason'] = $this->paused_reason;
        }

        if ($request->expectsDevice()) {
            $responseData = $this->generateMobileResponse($responseData);
        } else {
            // For Web
            $healthLogs = $this->healthLogs()
                ->whereNull('resolved_at')
                ->with('healthTracker')->get()
                ->sortByDesc(function ($healthLog) {
                    return (int) ($healthLog->healthTracker->weight ?? 0);
                });
            $responseData['description'] = $this->description;
            $responseData['health_score'] = HealthScoreResource::collection($healthLogs)->collection->first() ?? ['classification' => 'healthy', 'created_at' => ''];
            $responseData['service_request'] = new WorkOrderServiceRequestResource($this->serviceRequest);
            $responseData['status'] = new WorkOrderStatusResource($this->state);
            $responseData['resident_id'] = $this->serviceRequest->resident->resident_uuid ?? null;
            $responseData['resolved_at'] = $this->resolved_at?->toIso8601String();
            $responseData['created_at'] = $this->created_at?->toIso8601String();
            $responseData['updated_at'] = $this->updated_at?->toIso8601String();
            $responseData['created_by'] = $this->createdUser?->getName();
            $responseData['tags'] = TagResource::collection($this->tags);
            $responseData['due_date'] = $this->due_date ? DataFormatHelper::dateFormat($this->due_date) : null;
            $responseData['assignees'] = WorkOrderAssigneeResource::collection($this->assignees);

            $responseData['has_missing_data'] = $this->hasMissingData();

            if ($this->state->equals(Canceled::class)) {
                $responseData['canceled_at'] = $this->canceled_at?->toIso8601String();
                $responseData['canceled_reason'] = $this->canceled_reason;
            }

            if (! empty($this->vendor)) {
                $responseData['provider_name'] = $this->vendor->service === Service::LULA() ? $this->vendor->company_name : $this->vendor->getName();
            }

            if (! empty($this->latestInvoices)) {
                $responseData['invoices'] = InvoiceListResource::collection($this->latestInvoices);
            }

            $responseData['show_schedule_details'] = ! $this->state->equals(
                Canceled::class,
                ClaimPending::class,
                Created::class,
                ReadyToSchedule::class,
            );

            $resource = new ResidentAvailabilityResource($this->serviceRequest);
            $data = collect($resource->resolve());

            $residentRequestAvailabilityCount = $this->serviceRequest
                ? count($this->serviceRequest->residentAvailabilities->whereNull('user_id')->groupBy('availability_date')->keys()->toArray())
                : null;
            $responseData['resident_availabilities'] = [
                'availability' => $data->get('resident_availability'),
                'resident_added_availability_count' => $residentRequestAvailabilityCount,
                'requested_at' => $this->serviceRequest?->availability_requested_at?->toIso8601String(),
                'availability_created_at' => $this->serviceRequest?->residentAvailabilities->whereNull('user_id')->sortByDesc('created_at')->first()?->created_at?->toIso8601String() ?? null,
                'display_availability_added_alert' => empty($this->availability_viewed_user_id) && $residentRequestAvailabilityCount ? true : false,
            ];
        }

        return $responseData;
    }

    protected function isOpenWorkOrderOfAuthenticatedTechnician(?WorkOrderServiceCall $latestTrip): bool
    {
        return $latestTrip?->status === ServiceCallStatus::ACTIVE()
            && $this->state->equals(Scheduled::class, WorkInProgress::class)
            && request()->user()?->is($latestTrip->technicianAppointment?->technician->user);
    }

    protected function isLatestWorkOrderTripOfAuthenticatedTechnician(?WorkOrderServiceCall $latestTrip): bool
    {
        return (bool) request()->user()?->is($latestTrip?->technicianAppointment?->technician->user);
    }

    /**
     * Generate response data for mobile
     *
     * @param  array<string, mixed>  $responseData
     * @return array<string, mixed>
     */
    protected function generateMobileResponse(array &$responseData): array
    {
        // For appfolio job add job reference number
        if (! empty($this->workOrderSource)) {
            if ($this->workOrderSource->slug === WorkOrderSourceTypes::APPFOLIO()) {
                $responseData['external_source'] = [
                    'reference_number' => $this->work_order_reference_number,
                ];
            }
        }

        /** @var WorkOrderServiceCall $latestTrip */
        $latestTrip = $this->latestTrips->first();

        if ($this->isOpenWorkOrderOfAuthenticatedTechnician($latestTrip)) {
            $responseData['status'] = match ($latestTrip->state->getValue()) {
                EnRoute::$name, EnRoutePaused::$name => new TripStatusResource($latestTrip->state),
                default => new WorkOrderStatusResource($this->state),
            };
        } elseif ($this->isLatestWorkOrderTripOfAuthenticatedTechnician($latestTrip)) {
            $responseData['status'] = match ($this->state->getValue()) {
                ReadyToSchedule::$name, Paused::$name, Canceled::$name, QualityCheck::$name, Completed::$name => new WorkOrderStatusResource($this->state),
                ReadyToInvoice::$name => null,
                default => new TripStatusResource($latestTrip->state),
            };
        }

        $responseData['resident'] = [
            'resident_id' => $this->serviceRequest->resident->resident_uuid ?? null,
            'first_name' => $this->serviceRequest->resident->first_name ?? null,
            'last_name' => $this->serviceRequest->resident->last_name ?? null,
            'phone_number' => $this->serviceRequest->resident->phone_number ?? null,
        ];

        return $responseData;
    }

    /**
     * Check missing data.
     */
    protected function hasMissingData(): bool
    {
        if (empty($this->workOrderIssues) || empty($this->priority)) {
            return true;
        }

        return ! ResidentAvailability::where('service_request_id', $this->service_request_id)
            ->where('day_passed', 'no')
            ->exists();
    }
}

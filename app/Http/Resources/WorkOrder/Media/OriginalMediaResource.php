<?php

namespace App\Http\Resources\WorkOrder\Media;

use App\Enums\Boolean;
use App\Enums\ImageConversionType;
use App\Models\Media;
use App\Models\WorkOrderMedia;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 *@mixin WorkOrderMedia
 *@mixin Media
 */
class OriginalMediaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     * @throws Exception
     */
    public function toArray(Request $request): array
    {
        return [
            'media_uuid' => $this->media->media_uuid,
            'thumbnail_url' => $this->workOrder && $this->has_thumbnail == Boolean::YES() ?
                $this->media->getTemporaryMediaUrl(type: ImageConversionType::OPTIMIZED()) : null,
            'original' => $this->workOrder && $this->has_upload_completed == Boolean::YES() ?
                $this->media->getTemporaryMediaUrl(type: ImageConversionType::ORIGINAL()) : null,
        ];
    }
}

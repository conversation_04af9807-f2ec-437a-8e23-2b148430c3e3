<?php

namespace App\Http\Resources\WorkOrder\Media;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ThumbnailMediaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'media_uuid' => $this['media_uuid'] ?? null,
            'is_uploaded' => $this['is_uploaded'] ?? null,
            'stored_media_uuid' => $this['stored_media_uuid'] ?? null,
            'thumbnail_url' => $this['thumbnail_url'] ?? null,
            'media_type' => $this['media_type'] ?? null,
        ];
    }
}

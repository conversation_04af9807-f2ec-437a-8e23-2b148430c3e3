<?php

namespace App\Http\Resources\WorkOrder;

use App\Enums\Boolean;
use App\Enums\Trip;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderTaskMaterial;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin WorkOrderServiceCall */
class TechnicianAppointmentResource extends JsonResource
{
    public function __construct($resource, protected bool $isTechnicianLatestServiceCall = false)
    {
        // Ensure you call the parent constructor
        parent::__construct($resource);
        $this->isTechnicianLatestServiceCall = $isTechnicianLatestServiceCall;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'trip_id' => $this->work_order_service_call_uuid,
            'start_time' => $this->scheduled_start_time?->toIso8601String(),
            'end_time' => $this->scheduled_end_time?->toIso8601String(),
            'media' => MediaResource::collection($this->media),
            'materials' => ! empty($this->workOrderTaskMaterials) ? collect(clone $this->workOrderTaskMaterials)->map(function (WorkOrderTaskMaterial $material) {
                return [
                    'material_id' => $material->work_order_task_material_uuid,
                    'label' => $material->label,
                    'cost_in_cents' => $material->cost_in_cents,
                    'quantity' => $material->quantity,
                ];
            })->values() : [],
            'service_notes' => $this->when(! empty($this->service_notes), $this->service_notes ?? null),
            'is_active' => $this->is_active === Boolean::YES(),
            'is_last_trip' => $this->isTechnicianLatestServiceCall,
            'trip_end_with' => collect(Trip::workTypeOptions())
                ->where('value', $this->trip_end_with)
                ->first(),
            'trip_end_with_reason' => $this->trip_end_with_reason,
            'status' => new TripStatusResource($this->state),
        ];

        if ($this->isTechnicianLatestServiceCall) {
            $data['elapse_time_in_sec'] = $this->appointment?->adjusted_elapse_time_in_sec;
            $data['resumed_at'] = $this->timer_resumed_at?->toIso8601String();
            $data['enroute_at'] = $this->appointment?->enroute_at?->toIso8601String();
            $data['started_at'] = $this->appointment?->actual_start_time?->toIso8601String();
            $data['quote_id'] = $this->createdQuote->quote_uuid ?? null;
        }

        return $data;
    }
}

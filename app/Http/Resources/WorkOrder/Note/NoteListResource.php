<?php

namespace App\Http\Resources\WorkOrder\Note;

use App\Models\WorkOrderNote;
use App\Services\Vendor\Enum\Service;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderNote
 */
class NoteListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [
            'note_id' => $this->work_order_note_uuid,
            'note' => $this->note,
            'created_at' => $this->created_at?->toIso8601String(),
            'created_user' => [],
            'is_editable' => empty($this->vendor_id),
            $this->mergeWhen(! empty($this->lastModifiedUser), [
                'modified_user' => [
                    'user_id' => $this->lastModifiedUser->user_uuid ?? null,
                    'name' => $this->lastModifiedUser?->getName() ?? null,
                    'modified_at' => $this->last_modified_at?->toIso8601String(),
                ]]),
        ];

        if (! empty($this->user)) {
            $data['created_user'] = [
                'user_id' => $this->user->user_uuid,
                'name' => $this->user->getName(),
                'profile_pic' => $this->user->profile_pic,
            ];
        } elseif (! empty($this->vendor)) {
            $name = $this->vendor->company_name;
            if ($this->vendor->service === Service::LULA()) {
                $name = 'Lula Support';
            }

            $data['created_user'] = [
                'user_id' => $this->vendor->vendor_uuid,
                'name' => $name,
                'profile_pic' => $this->vendor->log_file_name,
            ];
        }

        return $data;
    }
}

<?php

namespace App\Http\Resources\WorkOrder\Invoice;

use App\Models\WorkOrder;
use App\Services\InvoiceRegister\InvoiceRegisterClient;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrder
 */
class InvoiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $tripDetails = [];
        $invoiceRegisterClient = new InvoiceRegisterClient;

        $trips = $this->tasks->first()?->allServiceCalls;

        if (! empty($trips)) {
            $tripDetails = $invoiceRegisterClient->generateInvoiceDetailsFromWorkOrderServiceCalls($trips);
        }

        return [
            'work_order_info' => [
                'description' => $this->description,
            ],
            'trips' => $tripDetails,
        ];
    }
}

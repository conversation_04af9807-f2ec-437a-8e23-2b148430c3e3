<?php

namespace App\Http\Resources\WorkOrder\Invoice;

use App\Http\Resources\Invoice\InvoiceStateResource;
use App\Http\Resources\WorkOrder\WorkOrderStatusResource;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 *  @mixin Invoice
 */
class FullyPaidResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'work_order_id' => $this->workOrder->work_order_uuid,
            'status' => new WorkOrderStatusResource($this->workOrder->state),
            'invoice' => [
                'invoice_id' => $this->invoice_uuid,
                'invoice_number' => $this->invoice_number,
                'status' => new InvoiceStateResource($this->state),
            ],
            'abilities' => $this->workOrder->resolveStateAbilities(),
        ];
    }
}

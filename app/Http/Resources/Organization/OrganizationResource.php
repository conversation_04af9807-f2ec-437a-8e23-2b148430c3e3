<?php

namespace App\Http\Resources\Organization;

use App\Models\Organization;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Organization
 */
class OrganizationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'name' => $this->name,
            'logo_uri' => $this->logoUrl(),
            'address' => [
                'address' => $this->street_address,
                'city' => $this->city,
                'state' => $this->state->state_code ?? '',
                'county' => $this->country->alpha2_code ?? '',
                'zip_code' => $this->zip_code,
            ],
            'phone_number' => $this->phone_number,
            'template' => $this->template,
        ];
    }
}

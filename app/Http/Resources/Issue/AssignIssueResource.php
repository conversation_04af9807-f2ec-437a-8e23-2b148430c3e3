<?php

declare(strict_types=1);

namespace App\Http\Resources\Issue;

use App\Models\WorkOrder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\WorkOrderIssue
 */
class AssignIssueResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'issue_id' => $this->issue_uuid,
            'work_order_id' => $this->workOrder?->work_order_uuid,
            'work_order_number' => $this->workOrder?->work_order_number,
            'status' => new IssueStatusResource($this->state),
            'title' => $this->title,
            'abilities' => $this->abilities(),
            'work_orders' => $this->workOrders?->map(function (WorkOrder $issueWorkOrder): array {
                return [
                    'work_order_id' => $issueWorkOrder->work_order_uuid,
                    'work_order_number' => $issueWorkOrder->work_order_number,
                    'issues_count' => $issueWorkOrder->issues_count,
                ];
            }),
        ];
    }
}

<?php

namespace App\Http\Resources;

use App\Models\ProblemSubCategory;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin ProblemSubCategory
 */
class ProblemSubCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => $this->label,
            'value' => $this->problem_sub_category_uuid,
            'slug' => $this->slug,
            'diagnosis' => ! empty($this->problemDiagnoses) ? ProblemDiagnosisResource::collection($this->problemDiagnoses) : [],
        ];
    }
}

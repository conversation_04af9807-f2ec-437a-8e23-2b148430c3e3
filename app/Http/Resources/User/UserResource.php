<?php

namespace App\Http\Resources\User;

use App\Enums\UserStatus;
use App\Helpers\DataFormatHelper;
use App\Http\Resources\Technician\SelectedSkillsResource;
use App\Models\Role;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin User
 */
class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Fetch all roles related to the organization.
        // Note:  we exclude the 'Technician'  when fetching roles. because we have no choice to update the role to technician.
        $availableOrganizationRoles = Role::select('role_id', 'role_uuid', 'name')
            ->where('name', '!=', 'Technician')
            ->get();

        $current_roles = $this->roles->pluck('name')->toArray();

        $address = $this->getAddress();
        $address['state'] = [
            'value' => $this->state?->state_uuid,
            'label' => $this->state?->name,
        ];
        $address['country'] = [
            'value' => $this->country?->country_uuid,
            'label' => $this->country?->name,
        ];

        return [
            'account_info' => [
                'user_id' => $this->user_uuid,
                'first_name' => $this->first_name,
                'middle_name' => $this->middle_name,
                'last_name' => $this->last_name,
                'is_active' => $this->status === UserStatus::ACTIVE(),
                'email' => $this->email,
                'phone_number' => $this->phone_number,
                'address' => $address,
                'current_roles' => RoleResource::collection($this->roles),
                'role_editable' => ! in_array('Technician', $current_roles, true),
                'is_technician' => in_array('Technician', $current_roles, true),
                'password_last_updated' => [
                    'user' => [
                        'name' => $this->lastPasswordUpdatedUser->first_name ?? null,
                    ],
                    'date' => $this->last_password_updated_at ? DataFormatHelper::dateFormat($this->last_password_updated_at) : null,
                ],
            ],
            'profile_pic' => $this->profile_pic ?? null,
            'notifications' => [
                'email' => $this->enable_email_notification === 'yes',
                'sms' => $this->enable_sms_notification === 'yes',
                'push' => $this->enable_push_notification === 'yes',
            ],
            'roles' => RoleResource::collection($availableOrganizationRoles),
            $this->mergeWhen(in_array('Technician', $current_roles), [
                'technician_id' => $this->technician?->technician_uuid,
                'selected_skills' => ! empty($this->technician->skills) ? new SelectedSkillsResource($this->technician->skills) : [],
                'working_hours' => ! empty($this->technician->workingHours) ? DataFormatHelper::formatWorkingHours($this->technician->workingHours) : [],
            ]),
            'timezone' => $this->timezone?->name,
        ];
    }
}

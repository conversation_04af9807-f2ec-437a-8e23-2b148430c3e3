<?php

namespace App\Http\Resources\User;

use App\Models\Technician;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Technician
 */
class TechnicianResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'value' => $this->technician_id ?? null,
            'label' => $this->technician_name ?? null,
            'user_id' => $this->user_id,
            'timezone' => $this->timezone ?? null,
        ];
    }
}

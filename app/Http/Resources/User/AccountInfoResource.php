<?php

namespace App\Http\Resources\User;

use App\Enums\UserStatus;
use App\Helpers\DataFormatHelper;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin User
 */
class AccountInfoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $current_roles = $this->roles->pluck('name')->toArray();
        $address = $this->getAddress();
        $address['state'] = [
            'value' => $this->state?->state_uuid,
            'label' => $this->state?->name,
        ];
        $address['country'] = [
            'value' => $this->country?->country_uuid,
            'label' => $this->country?->name,
        ];

        return [
            'account_info' => [
                'user_id' => $this->user_uuid,
                'first_name' => $this->first_name,
                'middle_name' => $this->middle_name,
                'last_name' => $this->last_name,
                'is_active' => $this->status === UserStatus::ACTIVE(),
                'email' => $this->email,
                'phone_number' => $this->phone_number,
                'address' => $address,
                'current_roles' => RoleResource::collection($this->roles),
                'role_editable' => in_array('Technician', $current_roles) ? false : true,
                'is_technician' => in_array('Technician', $current_roles),
                'password_last_updated' => [
                    'user' => [
                        'name' => $this->lastPasswordUpdatedUser->first_name ?? null,
                    ],
                    'date' => $this->last_password_updated_at ? DataFormatHelper::dateFormat($this->last_password_updated_at) : null,
                ],
            ],
        ];
    }
}

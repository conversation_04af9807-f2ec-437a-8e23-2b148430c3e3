<?php

namespace App\Http\Resources\User\Group;

use App\Enums\UserStatus;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin User
 */
class StatusBasedGroupDataResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'label' => UserStatus::from($this->status)->name ?? null,
            'total' => $this->total ?? 0,
            'slug' => UserStatus::from($this->status)->value ?? null,
        ];
    }
}

<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Role
 */
class RoleResource extends JsonResource
{
    public static $wrap = null;

    /**
     * Transform the resource into an array.
     *
     * @return array<string|string>
     */
    public function toArray(Request $request): array
    {
        return [
            'value' => $this->role_uuid,
            'label' => $this->name,
        ];
    }
}

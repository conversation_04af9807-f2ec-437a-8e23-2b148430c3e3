<?php

namespace App\Http\Resources\User;

use App\Enums\UserStatus;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin User
 */
class UserListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'account_info' => [
                'user_id' => $this->user_uuid,
                'first_name' => $this->first_name,
                'last_name' => $this->last_name,
                'middle_name' => $this->middle_name,
                'email' => $this->email,
                'is_active' => $this->status === UserStatus::ACTIVE(),
                'current_roles' => RoleResource::collection($this->roles),
                'is_technician' => $this->roles->contains('name', 'Technician'),
            ],
            $this->mergeWhen($this->roles->contains('name', 'Technician'), [
                'technician_id' => $this->technician?->technician_uuid,
            ]),
            'profile_pic' => $this->profile_pic ?? null,
            'last_activity' => $this->last_activity_at ? $this->last_activity_at->toIso8601String() : $this->updated_at?->toIso8601String(),
            'group' => ! empty($request->group) ? $this->findGroupSlug($request->group) : null,
        ];
    }

    private function findGroupSlug(string $group): string
    {
        return match ($group) {
            'status' => UserStatus::from($this->status)->value,
            'role' => ! empty($this->role_name_slug_for_grouping) ? Str::slug($this->role_name_slug_for_grouping) : '',
            default => '',
        };
    }
}

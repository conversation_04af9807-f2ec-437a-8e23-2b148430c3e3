<?php

namespace App\Http\Resources\Technician;

use App\Enums\TechnicianAppointment;
use App\Helpers\DataFormatHelper;
use App\Models\TechnicianSkill;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin TechnicianSkill
 */
class TechnicianBlockOutResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'appointment_type' => TechnicianAppointment::BLOCK_OUT(),
            'technician_appointment_id' => $this['technicianAppointment']->technician_appointment_uuid ?? null,
            'work_order_id' => $this['technicianAppointment']->workOrder->work_order_uuid ?? null,
            'work_order_number' => $this['technicianAppointment']->workOrder->work_order_number ?? null,
            'technician' => [
                'technician_id' => $this['technician']->technician_uuid,
                'name' => $this['technician']->user->first_name,
            ],
            'note' => $this['technicianAppointment']->note ?? null,
            'is_block_out_all_day' => $this['technicianAppointment']->is_block_out_all_day,
            'date' => ! empty($this['technicianAppointment']->scheduled_start_time) ? $this['technicianAppointment']->scheduled_start_time->format('Y-m-d') : null,
            'start_time' => ! empty($this['technicianAppointment']->scheduled_start_time) ? DataFormatHelper::dateFormat($this['technicianAppointment']->scheduled_start_time) : null,
            'end_time' => ! empty($this['technicianAppointment']->scheduled_end_time) ? DataFormatHelper::dateFormat($this['technicianAppointment']->scheduled_end_time) : null,
            'timezone' => ! empty($this['technicianAppointment']->timezone->name) ? $this['technicianAppointment']->timezone->name : config('settings.default_timezone'),
        ];
    }
}

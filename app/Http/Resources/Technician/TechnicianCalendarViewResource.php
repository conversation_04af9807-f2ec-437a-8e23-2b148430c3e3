<?php

namespace App\Http\Resources\Technician;

use App\Models\TechnicianAppointment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin TechnicianAppointment
 */
class TechnicianCalendarViewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'appointment_date' => $this->appointment_date ?? null,
            'is_block_out_all_day' => ! empty($this->is_block_out_all_day) ? true : false,
            'block_out_appointment_exist' => ! empty($this->block_out_appointment_count) ? true : false,
            'regular_appointment_exist' => ! empty($this->regular_appointment_count) ? true : false,
        ];
    }
}

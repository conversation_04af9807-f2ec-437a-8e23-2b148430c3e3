<?php

namespace App\Http\Resources\Technician;

use App\Enums\Boolean;
use App\Http\Resources\WorkOrder\TripStatusResource;
use App\Models\WorkOrderTask;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin WorkOrderTask
 */
class WorkOrderTaskResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $appointment = $this->latestServiceCalls->first()?->appointment;

        $appointmentDetails = [
            'appointment_id' => $this->latestServiceCalls->first()?->work_order_service_call_uuid,
            'start_time' => $this->latestServiceCalls->first()?->scheduled_start_time?->toIso8601String(),
            'end_time' => $this->latestServiceCalls->first()?->scheduled_end_time?->toIso8601String(),
            'technician' => [
                'technician_id' => $this->technician_user->technician_uuid ?? null,
                'name' => ! empty($this->technician_user->user) ? $this->technician_user->user->getName() : null,
            ],
            'started_at' => $appointment?->actual_start_time?->toIso8601String(),
            'ended_at' => $appointment?->actual_end_time?->toIso8601String(),
            'is_active' => $this->latestServiceCalls->first()?->is_active === Boolean::YES(),
            'status' => new TripStatusResource($this->latestServiceCalls->first()?->state),
        ];

        $responseData = [
            'task_id' => $this->work_order_task_uuid,
        ];

        if ($request->expectsDevice()) {
            // For Mobile Device
            $responseData['problem'] = [
                'category' => $this->problemDiagnosis->subCategory->problemCategory->label,
            ];

            $responseData['appointment'] = $appointmentDetails;

        } else {
            // For Web
            $responseData['problem'] = [
                'category' => [
                    'category_id' => $this->problemDiagnosis->subCategory->problemCategory->problem_category_uuid,
                    'label' => $this->problemDiagnosis->subCategory->problemCategory->label,
                ],
            ];
            $responseData['appointments'] = [
                $appointmentDetails,
            ];
        }

        return $responseData;
    }
}

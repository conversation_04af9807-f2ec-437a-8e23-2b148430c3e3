<?php

namespace App\Http\Resources\Technician;

use App\Models\TechnicianAppointment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin TechnicianAppointment
 */
class TechnicianBlockOutDeleteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'technician_appointment_id' => $this->technician_appointment_uuid,
        ];
    }
}

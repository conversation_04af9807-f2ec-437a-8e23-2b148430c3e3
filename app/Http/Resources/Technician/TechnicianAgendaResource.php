<?php

namespace App\Http\Resources\Technician;

use App\Enums\Boolean;
use App\Enums\TechnicianAppointment as TechnicianAppointmentEnum;
use App\Enums\Trip;
use App\Http\Resources\WorkOrder\PriorityResource;
use App\Http\Resources\WorkOrder\TaskMaterialResource;
use App\Http\Resources\WorkOrder\TripStatusResource;
use App\Http\Resources\WorkOrder\WorkOrderStatusResource;
use App\Models\TechnicianAppointment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin TechnicianAppointment
 */
class TechnicianAgendaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $responseData = [
            'appointment_type' => $this->appointment_type,
            'technician_appointment_id' => $this->technician_appointment_uuid,
            'work_order_id' => $this->workOrder?->work_order_uuid,
            'work_order_number' => $this->workOrder?->work_order_number,
            'timezone' => $this->workOrder?->timezone->name,
        ];

        if ($this->appointment_type == TechnicianAppointmentEnum::BLOCK_OUT()) {
            $responseData['is_block_out_all_day'] = (bool) $this->is_block_out_all_day;
            $responseData['technician'] = [
                'technician_id' => $this->technician->technician_uuid,
                'name' => $this->technician->user?->getName(),
            ];
            $responseData['note'] = $this->note;
            $responseData['start_time'] = $this->scheduled_start_time->toIso8601String();
            $responseData['end_time'] = $this->scheduled_end_time->toIso8601String();
        }

        if ($this->appointment_type == TechnicianAppointmentEnum::REGULAR()) {
            $responseData['status'] = new WorkOrderStatusResource($this->workOrder?->state);
            $responseData['property'] = new WorkOrderPropertyResource($this->workOrder?->property);
            $taskResponse = [
                'task_id' => $this->workOrder?->tasks->first()?->work_order_task_uuid,
            ];

            $appointmentDetails = [
                'appointment_id' => $this->serviceCall?->work_order_service_call_uuid,
                'start_time' => $this->serviceCall?->scheduled_start_time?->toIso8601String(),
                'end_time' => $this->serviceCall?->scheduled_end_time?->toIso8601String(),
                'technician' => [
                    'technician_id' => $this->technician->technician_uuid ?? null,
                    'name' => $this->technician->user?->getName(),
                ],
                'started_at' => $this->actual_start_time?->toIso8601String(),
                'ended_at' => $this->actual_end_time?->toIso8601String(),
                'is_active' => $this->serviceCall?->is_active === Boolean::YES(),
                'status' => new TripStatusResource($this->serviceCall?->state),
                'trip_number' => $this->serviceCall?->work_order_service_call_number,
                'service_notes' => $this->serviceCall?->service_notes,
                'trip_end_with' => collect(Trip::workTypeOptions())
                    ->where('value', $this->serviceCall?->trip_end_with)
                    ->first(),
                'materials' => ! empty($this->serviceCall?->workOrderTaskMaterials) ? TaskMaterialResource::collection($this->serviceCall->workOrderTaskMaterials) : [],
                'paused_at' => $this->when(! empty($this->serviceCall->timer_paused_at), $this->serviceCall?->timer_paused_at?->toIso8601String()),
                'resumed_at' => $this->when(! empty($this->serviceCall->timer_resumed_at), $this->serviceCall?->timer_resumed_at?->toIso8601String()),
                'elapse_time_in_sec' => $this->adjusted_elapse_time_in_sec ?? 0,
            ];
            if ($request->expectsDevice()) {
                // For Mobile Device
                $taskResponse['problem'] = [
                    'category' => $this->workOrder?->tasks->first()?->problemDiagnosis->subCategory->problemCategory->label,
                ];
                $taskResponse['appointment'] = $appointmentDetails;
            } else {
                $responseData['priority'] = new PriorityResource($this->workOrder?->priority);
                $responseData['abilities'] = $this->workOrder?->resolveStateAbilities();
                $taskResponse['problem'] = [
                    'category' => [
                        'category_id' => $this->workOrder?->tasks->first()?->problemDiagnosis->subCategory->problemCategory->problem_category_uuid,
                        'label' => $this->workOrder?->tasks->first()?->problemDiagnosis->subCategory->problemCategory->label,
                    ],
                ];
                $taskResponse['appointments'] = [$appointmentDetails];
            }
            $responseData['work_order_tasks'] = [$taskResponse];
        }

        return $responseData;
    }
}

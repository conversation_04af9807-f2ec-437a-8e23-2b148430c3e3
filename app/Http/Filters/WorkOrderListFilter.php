<?php

namespace App\Http\Filters;

use App\Http\Requests\WorkOrder\ListRequest;
use App\Traits\WorkOrderListFilterTrait;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class WorkOrderListFilter extends QueryFilter
{
    use WorkOrderListFilterTrait;

    /**
     * Attributes allowed to sort by the respective field name in query
     *
     * @var array<string, string>
     */
    protected array $sortAttributes = [
        'category' => 'problem_categories_label',
        'status_group' => 'work_order_statuses.sort_order',
        'status' => 'work_order_statuses.slug',
        'technician' => 'technicians_name',
        'due_date' => 'work_orders.due_date',
        'created' => 'work_orders.created_at',
        'priority' => 'work_orders.priority',
        'state_updated' => 'work_orders.state_updated_at',
        'scheduled' => 'scheduled_start_time',
        'assignee' => 'assignee_name_for_sort',
        'health_score' => 'health_score_weight',
        'health_score_weight' => 'health_score_weight',
        'health_score_elapse_time' => 'health_score_elapse_time',
        'paused' => 'work_orders.paused_at',
        'canceled' => 'work_orders.canceled_at',
        'completed' => 'work_orders.work_completed_at',
        'closed' => 'closed_at',
    ];

    /**
     *  Overwrite parent constructor
     */
    public function __construct(ListRequest $request)
    {
        parent::__construct($request);
    }

    /**
     * Search by key
     */
    public function search(string $key): void
    {
        // If a new search criteria is added here, it must be check
        // priorityBasedGroupData, statusBasedGroupData, categoryBasedGroupData,
        // assigneeBasedGroupData, technicianBasedGroupData and tagBasedGroupData in WorkOrderController.

        $this->builder->where(function (Builder $query) use ($key) {
            $query->where('work_orders.work_order_number', 'like', "%{$key}%")
                ->orWhere('issues.description', 'like', "%{$key}%")
                ->orWhereRaw("TRIM(CONCAT(residents.first_name , ' ', COALESCE(residents.last_name, ''))) LIKE ?", ["%{$key}%"])
                ->orWhere('properties.full_address', 'like', "%{$key}%")
                ->orWhere('problem_categories.label', 'like', "%{$key}%")
                ->orWhere('service_requests.service_request_number', 'like', "%{$key}%");
        });
    }

    /**
     * Filter by Work Order id
     */
    public function workOrderId(string $workOrderId): void
    {
        $this->builder->whereUuid($workOrderId);
    }

    protected function prepareForFiltering(): void
    {
        // By default, sort is created_at
        $sortOrder = empty($this->request->sort) ? '-created' : $this->request->sort;

        //This sort for mobile app
        if (! empty($this->request->group_type) && empty($this->request->sort)) {
            $sortOrder = '-state_updated';
        }

        // Check if any group by is applied in the request then, we order the data by this group.
        if (! empty($this->request->group)) {

            $group = ($this->request->group == 'status') ? 'status_group' : $this->request->group;

            if ($this->request->group == $sortOrder) {
                $sortOrder = $group;
            } else {
                $sortOrder = "{$group},{$sortOrder}";
            }
        }
        if (! empty($this->request->sort) && in_array($this->request->sort, ['health_score', '-health_score'])) {
            $array_sort = $this->request->sort == 'health_score' ? ['health_score_weight', '-health_score_elapse_time'] : ['-health_score_weight', '-health_score_elapse_time'];
            $sortOrder = implode(',', $array_sort);
        }
        if (! empty($this->request->group) && $this->request->group == 'health_score') {
            $array_sort = ['-health_score_weight', 'health_score_elapse_time'];
            if (! empty($this->request->sort) && ! in_array($this->request->sort, ['health_score', '-health_score'])) {
                array_splice($array_sort, 1, 0, $this->request->sort);
            }
            $sortOrder = implode(',', $array_sort);

        }

        // Update the request payload.
        $this->request->merge([
            'sort' => $sortOrder,
        ]);
    }

    /**
     * Filter
     *
     * @param  array<string,string|array<int,array<string,string|array<string>>>>  $filters
     *
     * @throws Exception
     */
    protected function filter(array $filters): void
    {
        $this->builder = $this->filterQuery($filters, $this->builder);
    }
}

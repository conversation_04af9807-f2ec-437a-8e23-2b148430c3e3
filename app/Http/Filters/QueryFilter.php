<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

abstract class QueryFilter
{
    /**
     * The request object.
     */
    protected Request $request;

    /**
     * @var Builder<Model>
     *                     The builder instance.
     */
    protected Builder $builder;

    /**
     * Define these attributes to sort by the respective field name in db
     * Keys represent url fields and values represents db fields
     *
     * @var array<string, string>
     */
    protected array $sortAttributes = [];

    /**
     * Create a new QueryFilters instance.
     */
    public function __construct(Request $request)
    {
        $this->request = $request;

        $this->prepareForFiltering();
    }

    /**
     * Apply the filters to the builder.
     *
     * @param  Builder<Model>  $builder
     * @return Builder<Model>
     */
    public function apply(Builder $builder): Builder
    {
        $this->builder = $builder;
        foreach ($this->filters() as $name => $value) {
            if (! method_exists($this, $name) && ! method_exists($this, Str::camel($name))) {
                continue;
            }

            //Check the method name is camelcase. then the name covert to the camelcase.
            if (method_exists($this, Str::camel($name))) {
                $name = Str::camel($name);
            }

            if ($value != '') {
                $this->$name($value);
            }
        }

        return $this->builder;
    }

    /**
     * Get all request filters data.
     *
     * @return array<string, mixed>
     */
    public function filters(): array
    {
        return $this->request->all();
    }

    /**
     * Sort the collection by the sort field
     * Examples: sort= name,-status || sort=-name || sort=status
     */
    protected function sort(string $value): void
    {
        collect(explode(',', $value))->mapWithKeys(fn (string $field) => match ($field[0]) {
            '-' => [substr($field, 1) => 'desc'],
            ' ' => [substr($field, 1) => 'asc'],
            default => [$field => 'asc'],
        }
        )->each(function (string $order, string $field) {
            $sort_field = Arr::get($this->sortAttributes, $field);
            if ($sort_field) {
                $this->builder->orderBy($sort_field, $order);
            }
        });
    }

    protected function prepareForFiltering(): void {}
}

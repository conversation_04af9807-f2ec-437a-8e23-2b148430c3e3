<?php

namespace App\Http\Filters;

class VendorListFilter extends QueryFilter
{
    /**
     * Search by key
     */
    public function search(string $key): void
    {
        $this->builder->where(function ($query) use ($key) {
            $query->whereRaw("TRIM(CONCAT(vendors.first_name , ' ', COALESCE(vendors.last_name, ''))) LIKE ?", ["%{$key}%"])
                ->orWhere('city', 'like', "%{$key}%")
                ->orWhere('states.name', 'like', "%{$key}%");
        });
    }
}

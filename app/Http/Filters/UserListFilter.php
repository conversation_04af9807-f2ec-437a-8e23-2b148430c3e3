<?php

namespace App\Http\Filters;

use App\Http\Requests\User\ListRequest;
use App\Traits\UserListFilterTrait;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class UserListFilter extends QueryFilter
{
    use UserListFilterTrait;
    /**
     * Attributes allowed to sort by the respective field name in query
     *
     * @var array<string, string>
     */
    protected array $sortAttributes = [
        'name' => 'users.first_name',
        'email' => 'users.email',
        'status' => 'users.status',
        'last_activity' => 'users.updated_at',
        //this is for table sort
        'roles' => 'role_names',
        //this is for grouping
        'role' => 'role_name_for_grouping',
    ];

    /**
     *  Overwrite parent constructor
     */
    public function __construct(ListRequest $request)
    {
        parent::__construct($request);
    }

    /**
     * Search by key
     */
    public function search(string $key): void
    {
        $this->builder->where(function (Builder $query) use ($key) {
            $query->where(DB::raw("CONCAT_WS(' ', users.first_name, users.middle_name, users.last_name)"), 'like', $key . '%')
                ->orWhere('users.email', 'like', "%{$key}%")
                ->orWhere('users.phone_number', 'like', "%{$key}%");
        });
    }

    protected function prepareForFiltering(): void
    {
        // By default, sort is first_name
        $sortOrder = empty($this->request->sort) ? 'name' : $this->request->sort;

        // Check if any group by is applied in the request then, we order the data by this group.
        if (! empty($this->request->group)) {

            $group = $this->request->group;

            if ($this->request->group == $sortOrder) {
                $sortOrder = $group;
            } else {
                $sortOrder = "{$group},{$sortOrder}";
            }
        }

        // Update the request payload.
        $this->request->merge([
            'sort' => $sortOrder,
        ]);
    }

    /**
     * Filter
     *
     * @param  array<string,string|array<int,array<string,string|array<string>>>>  $filters
     *
     * @throws Exception
     */
    protected function filter(array $filters): void
    {
        $this->builder = $this->filterQuery($filters, $this->builder);
    }
}

<?php

namespace App\Http\Filters;

class RoleListFilter extends QueryFilter
{
    /**
     * Attributes allowed to sort by the respective field name in query
     *
     * @var array<string, string>
     */
    protected array $sortAttributes = [
        'name' => 'name',
        'users_count' => 'users_count',
    ];

    /**
     * Search by key
     */
    public function search(string $key): void
    {
        $this->builder->where(function ($query) use ($key) {
            $query->where('name', 'like', '%' . $key . '%')
                ->orWhere('description', 'LIKE', '%' . $key . '%');
        });
    }

    protected function prepareForFiltering(): void
    {
        $preparedPayload = [];
        if ($this->request->missing('sort')) {
            $preparedPayload['sort'] = 'name';
        }
        // Merge prepared inputs to the request.
        $this->request->merge($preparedPayload);
    }
}

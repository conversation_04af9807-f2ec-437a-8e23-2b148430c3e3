<?php

namespace App\Http\Filters;

use App\Http\Requests\WorkOrder\ListRequest;
use App\Traits\QuoteListFilterTrait;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class QuoteListFilter extends QueryFilter
{
    use QuoteListFilterTrait;

    /**
     * Attributes allowed to sort by the respective field name in query
     *
     * @var array<string, string>
     */
    protected array $sortAttributes = [
        'category' => 'problem_categories_label',
        'status' => 'quote_statuses.sort_order',
        'submitted_by' => 'quotes.submitted_user_id',
        'submitted_date' => 'quotes.submitted_at',
        'work_order_number' => 'work_orders.work_order_number',
        'tag' => 'tag_ids_for_group',
        'assignee' => 'assignee_name_for_sort',
        'approved_total' => 'approved_total',
        'total' => 'total_cost',
    ];

    /**
     *  Overwrite parent constructor
     */
    public function __construct(ListRequest $request)
    {
        parent::__construct($request);
    }

    /**
     * Search by key
     */
    public function search(string $key): void
    {
        // If a new search criteria is added here, it must be added in
        // priorityBasedGroupData, statusBasedGroupData, categoryBasedGroupData,
        // assigneeBasedGroupData, technicianBasedGroupData and tagBasedGroupData in WorkOrderController.

        $this->builder->where(function (Builder $query) use ($key) {
            $query->where('work_orders.work_order_number', 'like', "%{$key}%")
                ->orWhere('work_orders.description', 'like', "%{$key}%")
                ->orWhereRaw("TRIM(CONCAT(residents.first_name , ' ', COALESCE(residents.last_name, ''))) LIKE ?", ["%{$key}%"])
                ->orWhere('properties.full_address', 'like', "%{$key}%")
                ->orWhere('problem_categories.label', 'like', "%{$key}%");
        });
    }

    /**
     * Filter by Work Order id
     */
    public function workOrderId(string $workOrderId): void
    {
        $this->builder->whereUuid($workOrderId);
    }

    protected function prepareForFiltering(): void
    {
        // By default, sort is created_at
        $sortOrder = empty($this->request->sort) ? '-submitted_date' : $this->request->sort;

        // Check if any group by is applied in the request then, we order the data by this group.
        if (! empty($this->request->group)) {

            $group = $this->request->group;

            if ($this->request->group == $sortOrder) {
                $sortOrder = $group;
            } else {
                $sortOrder = "{$group},{$sortOrder}";
            }
        }

        // Update the request payload.
        $this->request->merge([
            'sort' => $sortOrder,
        ]);
    }

    /**
     * Filter
     *
     * @param  array<string,string|array<int,array<string,string|array<string>>>>  $filters
     *
     * @throws Exception
     */
    protected function filter(array $filters): void
    {
        $this->builder = $this->filterQuery($filters, $this->builder);
    }
}

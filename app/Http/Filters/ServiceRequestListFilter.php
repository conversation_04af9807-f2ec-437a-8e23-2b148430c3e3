<?php

namespace App\Http\Filters;

use App\Http\Requests\ServiceRequest\ListRequest;
use App\Traits\ServiceRequestListFilterTrait;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class ServiceRequestListFilter extends QueryFilter
{
    use ServiceRequestListFilterTrait;

    /**
     * Attributes allowed to sort by the respective field name in query
     *
     * @var array<string, string>
     */
    protected array $sortAttributes = [
        'status' => 'service_request_statuses.sort_order',
        'assignee' => 'assignee_name_for_sort',
        'imported_from' => 'service_request_sources.name',
        'added_date' => 'service_requests.created_at',
    ];

    /**
     *  Overwrite parent constructor
     */
    public function __construct(ListRequest $request)
    {
        parent::__construct($request);
    }

    public function search(string $key): void
    {
        // If a new search criteria is added here, it must be added in
        $this->builder->where(function (Builder $query) use ($key) {
            $query->where('service_requests.service_request_number', 'like', "%{$key}%")
                ->orWhere('service_requests.description', 'like', "%{$key}%")
                ->orWhereRaw("TRIM(CONCAT(residents.first_name , ' ', COALESCE(residents.last_name, ''))) LIKE ?", ["%{$key}%"])
                ->orWhere('properties.full_address', 'like', "%{$key}%")
                ->orWhere('problem_categories.label', 'like', "%{$key}%");
        });
    }

    /**
     * Filter by Service Request Id
     */
    public function serviceRequestId(string $serviceRequestId): void
    {
        $this->builder->whereUuid($serviceRequestId);
    }

    protected function prepareForFiltering(): void
    {
        // By default, sort is created_at
        $sortOrder = ! empty($this->request->sort) ? $this->request->sort : '-added_date';

        // Check if any group by is applied in the request then, we order the data by this group.
        if (! empty($this->request->group)) {

            $group = ($this->request->group == 'status') ? 'status' : $this->request->group;

            if ($this->request->group == $sortOrder) {
                $sortOrder = $group;
            } else {
                $sortOrder = "{$group},{$sortOrder}";
            }
        }

        // Update the request payload.
        $this->request->merge([
            'sort' => $sortOrder,
        ]);
    }

    /**
     * Filter
     *
     * @param  array<string,string|array<int,array<string,string|array<string>>>>  $filters
     *
     * @throws Exception
     */
    protected function filter(array $filters): void
    {
        $this->builder = $this->filterQuery($filters, $this->builder);
    }
}

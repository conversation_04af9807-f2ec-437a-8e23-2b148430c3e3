<?php

namespace App\Http\Filters;

use App\Models\WorkOrder;
use Illuminate\Support\Facades\DB;

class TagListFilter extends QueryFilter
{
    /**
     * Attributes allowed to sort by the respective field name in query
     *
     * @var array<string, string>
     */
    protected array $sortAttributes = [
        'name' => 'name',
    ];

    /**
     * Search by key
     */
    public function search(string $key): void
    {
        $this->builder->where(function ($query) use ($key) {
            $query->where('name', 'like', '%' . $key . '%');
        });
    }

    /**
     * Search by key
     */
    public function workOrderId(string $key): void
    {
        $workOrderId = WorkOrder::whereUuid($key)->firstOrFail();
        $existingTagIds = DB::table('work_order_tags')
            ->where('work_order_id', $workOrderId->work_order_id)
            ->pluck('tag_id');

        if ($existingTagIds->count()) {
            $this->builder->whereNotIn('tag_id', $existingTagIds);
        }
    }

    protected function prepareForFiltering(): void
    {
        $preparedPayload = [];
        if ($this->request->missing('sort')) {
            $preparedPayload['sort'] = 'name';
        }
        // Merge prepared inputs to the request.
        $this->request->merge($preparedPayload);
    }
}

<?php

namespace App\Http\Requests\ServiceRequest;

use App\Enums\Priority;
use App\Enums\PropertyAccessMethods;
use App\Http\Requests\BaseApiRequest;
use App\Models\ProblemCategory;
use App\Models\ProblemSubCategory;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Validation\Rules\Enum;

class StoreRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'request_type' => 'required',
            'priority' => ['required', new Enum(Priority::class)],
            'description' => 'required',
            // Property
            'property' => 'required|array',
            'property.property_name' => 'nullable|string',
            'property.full_address' => 'required|string|max:255',
            'property.street_address' => 'required|string|max:255',
            'property.unit_number' => 'nullable|string|max:30',
            'property.bed_unit' => 'nullable',
            'property.city' => 'required|string|max:255',
            'property.postal_zip_code' => 'required|digits:5',
            'property.state_id' => [
                'required',
                'exists:states,state_code',
            ],
            'property.country_id' => [
                'required',
                'exists:countries,alpha2_code',
            ],
            'property.latitude' => 'nullable',
            'property.longitude' => 'nullable',
            'property.access_info' => 'nullable|array',
            'property.access_info.method' => ['nullable', new Enum(PropertyAccessMethods::class)],
            'property.access_info.code' => ['required_if:property_access_method,' . PropertyAccessMethods::DIGITAL_KEY_CODE()],
            'property.access_info.note' => 'nullable',
            // Residents
            'residents' => 'sometimes|array',
            'residents.*.first_name' => 'sometimes|nullable',
            'residents.*.last_name' => 'sometimes|nullable',
            'residents.*.email' => 'nullable',
            'residents.*.phone_number' => 'nullable',
            'residents.*.is_primary_resident' => [
                'boolean',
            ],
            // Timezone
            'timezone' => 'nullable',
            // Categories
            'categories' => 'required|array',
            'categories.*.category' => [
                'required',
                'exists:problem_categories,slug',
            ],
            'categories.*.sub_category' => [
                'required',
                'exists:problem_sub_categories,slug',
            ],
            // Photos
            'photos' => 'nullable|array',
            'photos.*.uri' => 'nullable|url',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'request_type' => 'request type',
            'priority' => 'priority',
            'description' => 'description',
            // Property
            'property' => 'property',
            'property.property_name' => 'property name',
            'property.full_address' => 'property full address',
            'property.street_address' => 'property street address',
            'property.unit_number' => 'property unit number',
            'property.bed_unit' => 'property bed unit',
            'property.city' => 'city',
            'property.postal_zip_code' => 'postal zip code',
            'property.state_id' => 'state',
            'property.country_id' => 'country',
            'property.latitude' => 'property latitude',
            'property.longitude' => 'property longitude',
            'property.access_info' => 'property access info',
            'property.access_info.method' => 'property access method',
            'property.access_info.code' => 'property access code',
            'property.access_info.note' => 'property access note',
            // Residents
            'residents' => 'residents',
            'residents.*.first_name' => 'resident first name',
            'residents.*.last_name' => 'resident last name',
            'residents.*.email' => 'residents email',
            'residents.*.phone_number' => 'resident phone number',
            'residents.*.is_primary_resident' => 'primary resident',
            // Timezone
            'timezone' => 'timezone',
            // Categories
            'categories' => 'categories',
            'categories.*.category' => 'category',
            'categories.*.sub_category' => 'sub category',
            // Photos
            'photos' => 'photos',
            'photos.*.uri' => 'url',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'request_type.required' => 'Service request type required.',
            'priority.required' => 'Service request priority required.',
            'description.required' => 'Service request description required.',
            // Property
            'property.required' => 'Property details required.',
            'property.array' => 'Invalid property details payload provided.',
            'property.full_address.required' => 'Property full address required.',
            'property.street_address.required' => 'Property street address required.',
            'property.city.required' => 'City required.',
            'property.postal_zip_code.required' => 'Property postal zip code required.',
            'property.postal_zip_code.digits' => 'Property postal zip code must be 5 digits.',
            'property.state_id.required' => 'Property state id required.',
            'property.country_id.required' => 'Property country id required.',
            // Categories
            'categories.required' => 'Categories required.',
            'categories.array' => 'Invalid category payload provided.',
            'categories.*.category.required' => 'Category required.',
            'categories.*.sub_category.required' => 'Sub category required.',
        ];
    }

    public function prepareForValidation()
    {
        $categories = $this->input('categories');

        foreach ($categories as &$category) {
            // Get the problem category by slug
            $categoryModel = ProblemCategory::where('slug', $category['category'])->first();

            if ($categoryModel) {
                // Check if the category was found before accessing its properties
                $category['problem_category_id'] = $categoryModel->problem_category_id;

                // Get the problem sub category by slug and problem category id
                $subCategoryModel = ProblemSubCategory::where('slug', $category['sub_category'])
                    ->where('problem_category_id', $categoryModel->problem_category_id)
                    ->first();

                if ($subCategoryModel) {
                    $category['problem_sub_category_id'] = $subCategoryModel->problem_sub_category_id;
                }
            } else {
                $category['problem_category_id'] = null;
            }
        }

        // Merge the modified categories back into the request
        $this->merge(['categories' => $categories]);
    }
}

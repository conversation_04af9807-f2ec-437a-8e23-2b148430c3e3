<?php

namespace App\Http\Requests\ServiceRequest\Media;

use App\Enums\MediaType;
use App\Http\Requests\BaseApiRequest;

class ThumbnailUploadRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $mediaMaxByteSize = config('media.upload_max_file_size') * 1048576;

        return [
            'media' => 'required|array',
            'media.*.thumbnail' => 'required_with:media.*.thumbnail_id|image',
            'media.*.thumbnail_id' => 'required_with:media.*.thumbnail',
            'media.*.size' => 'required_with:media.*.thumbnail|lte:' . $mediaMaxByteSize,
            'media.*.mime_type' => 'required_with:media.*.thumbnail',
            'media.*.file_name' => 'required_with:media.*.thumbnail',
            'media_type' => 'sometimes|in:' . collect(MediaType::cases())->pluck('value')->implode(','),
        ];
    }
}

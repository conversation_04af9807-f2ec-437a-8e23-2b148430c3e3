<?php

namespace App\Http\Requests\ServiceRequest;

use App\Http\Requests\BaseApiRequest;
use Illuminate\Contracts\Validation\ValidationRule;

class ListRequest extends BaseApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'per_page' => 'sometimes|numeric|max:200',
            'search' => 'sometimes|nullable|min:3',
            'group' => 'sometimes|nullable',
            'status' => 'sometimes|nullable',
            'group_type' => 'sometimes|nullable|in:open,closed,paused',
            'filter' => 'sometimes|nullable|array',
            'filter.group_op' => 'sometimes|nullable|in:and,or',
            'filter.fl_group' => 'sometimes|nullable|array',
            'filter.fl_group.*.field' => 'required',
            'filter.fl_group.*.operation' => 'required',
            'filter.fl_group.*.values' => 'required|array',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $group = $this->get('group', null);

        $sort = $this->get('sort', null);

        if ($sort && $group) {
            // Check if sort key contains the same group
            // Then remove it from the sort key and force update the request
            $sort_array = explode(',', $sort);

            if (array_search($group, $sort_array) !== false) {
                $index = array_search($group, $sort_array);
                unset($sort_array[$index]);
            } elseif (array_search("-{$group}", $sort_array) !== false) {
                $index = array_search("-{$group}", $sort_array);
                unset($sort_array[$index]);
            }

            if (count($sort_array)) {
                $sort = implode(',', $sort_array);
            } else {
                $sort = $group;
            }
        }

        $this->merge([
            'sort' => $sort,
        ]);
    }
}

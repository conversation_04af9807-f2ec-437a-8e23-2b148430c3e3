<?php

namespace App\Http\Requests\Tag;

use App\Http\Requests\BaseApiRequest;

class UpdateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // Get the tag_id of tag from path route
        $tagId = $this->route('tag')->tag_id ?? null;
        $organizationId = $this->user()?->organization_id;

        return [
            'name' => "required|max:255|min:3|unique:tags,name,{$tagId},tag_id,deleted_at,NULL,organization_id,{$organizationId}",
            'color' => 'required|max:7',
            'text_color' => 'required|max:7',
        ];

    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.unique' => 'Already exist',
        ];
    }
}

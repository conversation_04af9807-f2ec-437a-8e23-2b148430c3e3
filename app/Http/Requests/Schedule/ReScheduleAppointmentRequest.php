<?php

namespace App\Http\Requests\Schedule;

use App\Enums\WorkToPerformTypes;
use App\Http\Requests\BaseApiRequest;
use App\Models\Quote;
use App\Rules\UuidExists;
use App\Services\Scheduling\Domain\Enums\SchedulingMethod;
use App\Services\Scheduling\Domain\Enums\SchedulingMode;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class ReScheduleAppointmentRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'rescheduling_reason' => 'required',
            'service_window_reference_id' => 'required|string',
            'mode' => ['required', new Enum(SchedulingMode::class)],
            'method' => ['required', new Enum(SchedulingMethod::class)],
            'work_to_perform' => ['required', new Enum(WorkToPerformTypes::class)],
            'quote_id' => [
                'nullable',
                Rule::requiredIf(
                    in_array($this->input('work_to_perform'), [WorkToPerformTypes::QUOTE_TASK()])
                ),
                new UuidExists(Quote::class, 'quote_uuid'),
            ],
        ];
    }
}

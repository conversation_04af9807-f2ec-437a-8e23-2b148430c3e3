<?php

namespace App\Http\Requests\Schedule;

use App\Http\Requests\BaseApiRequest;
use App\Services\Scheduling\Domain\Enums\SchedulingMethod;
use App\Services\Scheduling\Domain\Enums\SchedulingMode;
use Illuminate\Validation\Rules\Enum;

class GetTechnicianListRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'mode' => ['required', new Enum(SchedulingMode::class)],
            'method' => ['required', new Enum(SchedulingMethod::class)],
            'duration' => 'required|numeric',
        ];
    }
}

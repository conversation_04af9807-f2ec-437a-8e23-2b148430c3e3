<?php

namespace App\Http\Requests\WorkOrder\Media;

use App\Http\Requests\BaseApiRequest;

class MediaUploadRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string,string>
     */
    public function rules(): array
    {
        $mediaMaxKbSize = config('media.upload_max_file_size') * 1024;

        return [
            'media' => 'required|file|max:' . $mediaMaxKbSize,
        ];
    }
}

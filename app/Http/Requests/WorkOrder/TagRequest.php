<?php

namespace App\Http\Requests\WorkOrder;

use App\Http\Requests\BaseApiRequest;
use App\Models\Tag;
use App\Rules\UuidExists;

class TagRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'tags' => [
                'required',
                'array',
                new UuidExists(Tag::class, 'tag_uuid'),
            ],
            'action' => 'required|in:attach,detach',
        ];
    }
}

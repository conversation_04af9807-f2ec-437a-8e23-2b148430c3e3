<?php

namespace App\Http\Requests\WorkOrder;

use App\Enums\PropertyAccessMethods;
use App\Http\Requests\BaseApiRequest;
use Illuminate\Validation\Rules\Enum;

class AccessMethodUpdateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'property_access_method' => ['required', new Enum(PropertyAccessMethods::class)],
            'property_access_code' => ['required_if:property_access_method,' . PropertyAccessMethods::DIGITAL_KEY_CODE()],
            'property_access_note' => ['sometimes'],
        ];
    }
}

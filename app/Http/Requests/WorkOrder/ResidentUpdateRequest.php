<?php

namespace App\Http\Requests\WorkOrder;

use App\Http\Requests\BaseApiRequest;
use App\Models\Resident;
use App\Rules\UuidExists;
use Illuminate\Http\Request;

class ResidentUpdateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(Request $request): array
    {
        return [
            'residents' => 'required|array',
            'residents.*.resident_id' => [
                'required',
                new UuidExists(Resident::class, 'resident_uuid'),
            ],
            'residents.*.first_name' => [
                'required',
            ],
            'residents.*.last_name' => [
                'nullable',
            ],
            'residents.*.phone_number' => [
                'required',
            ],
            'residents.*.email' => [
                'required',
                'email',
            ],
            'residents.*.is_primary' => [
                'required', 'boolean',
            ],
        ];
    }
}

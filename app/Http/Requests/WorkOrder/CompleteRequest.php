<?php

namespace App\Http\Requests\WorkOrder;

use App\Enums\MaterialQuantityType;
use App\Enums\Trip;
use App\Http\Requests\BaseApiRequest;
use App\Rules\TripEndWithTypeRule;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class CompleteRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'service_notes' => [
                Rule::requiredIf(
                    ! in_array($this->input('trip_end_with'), [Trip::NO_WORK()])
                ), 'string',
            ],
            'trip_end_with' => [
                'required', Rule::in(Trip::tripEndWith()->pluck('value')->toArray()),
            ],
            'trip_end_with_type' => [
                Rule::requiredIf(
                    in_array($this->input('trip_end_with'), [Trip::PARTIALLY_COMPLETED(), Trip::NO_WORK()])
                ),
                new TripEndWithTypeRule,
            ],
            'completed_time_in_sec' => [
                Rule::requiredIf(
                    in_array($this->input('trip_end_with'), [Trip::PARTIALLY_COMPLETED(), Trip::ALL_COMPLETED()]) && ! $this->expectsDevice()
                ),
                'integer',
                'gt:0',
            ],
            'reason' => Rule::requiredIf(
                in_array($this->input('trip_end_with_type'), [Trip::OTHER(), Trip::FINISH_WITH_ANOTHER_DAY()])
            ),
            'travel_time_in_sec' => [
                Rule::requiredIf(
                    in_array($this->input('trip_end_with'), [Trip::PARTIALLY_COMPLETED(), Trip::ALL_COMPLETED()]) && ! $this->expectsDevice()
                ),
                'integer',
                'gt:0',
            ],
            'materials' => 'sometimes|array',
            'materials.*.label' => 'required',
            'materials.*.unit_price_in_cents' => 'required_without:materials.*.cost_in_cents|integer|gt:0',
            'materials.*.cost_in_cents' => 'required_without:materials.*.unit_price_in_cents|integer|gt:0',
            'materials.*.quantity' => 'required|integer|gt:0',
            'materials.*.quantity_type' => ['required', new Enum(MaterialQuantityType::class)],
        ];
    }
}

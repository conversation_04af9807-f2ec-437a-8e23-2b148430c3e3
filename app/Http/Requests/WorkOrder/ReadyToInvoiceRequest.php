<?php

namespace App\Http\Requests\WorkOrder;

use App\Enums\MaterialQuantityType;
use App\Http\Requests\BaseApiRequest;
use App\Models\WorkOrderTaskMaterial;
use App\Rules\UuidExists;
use Illuminate\Validation\Rules\Enum;

class ReadyToInvoiceRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        if (! empty($this->get('trips'))) {
            return [
                'trips' => 'required|array',
                'trips.*.service_notes' => 'required|string',
                'trips.materials.*.material_id' => ['nullable', new UuidExists(WorkOrderTaskMaterial::class, 'work_order_task_material_uuid')],
                'trips.materials.*.label' => 'required',
                'trips.materials.*.unit_price_in_cents' => 'required_without:trips.materials.*.cost_in_cents|integer|gt:0',
                'trips.materials.*.cost_in_cents' => 'required_without:trips.materials.*.unit_price_in_cents|integer|gt:0',
                'trips.materials.*.quantity' => 'required|integer|gt:0',
                'trips.materials.*.quantity_type' => ['required', new Enum(MaterialQuantityType::class)],
                'trips.*.completed_time_in_sec' => 'required|integer|gt:0',
                'trips.*.travel_time_in_sec' => 'required|integer|gt:0',

            ];
        } else {
            return [];
        }
    }
}

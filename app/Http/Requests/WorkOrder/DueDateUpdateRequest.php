<?php

namespace App\Http\Requests\WorkOrder;

use App\Http\Requests\BaseApiRequest;

class DueDateUpdateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'due_date' => 'required|date_format:Y-m-d|after_or_equal:' . date('Y-m-d'),
        ];
    }
}

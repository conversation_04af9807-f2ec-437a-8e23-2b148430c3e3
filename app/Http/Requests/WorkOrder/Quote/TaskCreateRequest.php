<?php

namespace App\Http\Requests\WorkOrder\Quote;

use App\Enums\MarkUpFeeTypes;
use App\Enums\MaterialQuantityType;
use App\Http\Requests\BaseApiRequest;
use App\Models\Media;
use App\Rules\UuidExists;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\Rules\Enum;

class TaskCreateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'quote_task' => 'required|array',
            'quote_task.cost_in_cents' => 'required_without_all:quote_task.estimated_time,quote_task.labor_cost_in_cents|integer',
            'quote_task.estimated_time' => 'required_without_all:quote_task.cost_in_cents,quote_task.labor_cost_in_cents|numeric',
            'quote_task.labor_cost_in_cents' => 'required_without_all:quote_task.cost_in_cents,quote_task.estimated_time|integer|gt:0',
            'quote_task.markup_fee_type' => ['required_without_all:quote_task.cost_in_cents,quote_task.estimated_time', new Enum(MarkUpFeeTypes::class)],
            'quote_task.markup_fee_type_value' => 'required_without_all:quote_task.cost_in_cents,quote_task.estimated_time|numeric|gte:0',
            'quote_task.description' => 'required',
            'quote_task.materials.*' => 'sometimes|array',
            'quote_task.materials.*.label' => 'required|max:255',
            'quote_task.materials.*.unit_price_in_cents' => 'required_without_all:quote_task.materials.*.cost_in_cents,quote_task.materials.*.material_cost_in_cents|integer|gt:0',
            'quote_task.materials.*.cost_in_cents' => 'required_without_all:quote_task.materials.*.unit_price_in_cents,quote_task.materials.*.material_cost_in_cents|integer|gt:0',
            'quote_task.materials.*.material_cost_in_cents' => 'required_without_all:quote_task.materials.*.unit_price_in_cents,quote_task.materials.*.cost_in_cents|integer|gt:0',
            'quote_task.materials.*.markup_fee_type' => ['required_without_all:quote_task.materials.*.unit_price_in_cents,quote_task.materials.*.cost_in_cents', new Enum(MarkUpFeeTypes::class)],
            'quote_task.materials.*.markup_fee_type_value' => 'required_without_all:quote_task.materials.*.unit_price_in_cents,quote_task.materials.*.cost_in_cents|numeric|gte:0',
            'quote_task.materials.*.quantity' => 'required|integer|gt:0',
            'quote_task.materials.*.quantity_type' => ['required', new Enum(MaterialQuantityType::class)],
            'quote_task.media_ids' => 'sometimes|array',
            'quote_task.media_ids.*' => ['required', new UuidExists(Media::class, 'media_uuid')],
        ];
    }

    /**
     * Get the "after" validation callables for the request.
     *
     * @return array<int, Closure>
     */
    public function after(): array
    {
        return [
            function (Validator $validator) {
                $requestData = $this->all();
                $quoteTask = $requestData['quote_task'];
                if (! empty($quoteTask)) {

                    if (
                        ! empty($quoteTask['markup_fee_type']) &&
                        ! empty($quoteTask['markup_fee_type_value']) &&
                        $quoteTask['markup_fee_type'] === MarkUpFeeTypes::PERCENTAGE() &&
                        $quoteTask['markup_fee_type_value'] > 100
                    ) {
                        $validator->errors()->add(
                            'markup_fee_type_value',
                            'The mark up fee value must be less than 100'
                        );
                    }

                    //if material exists
                    if (! empty($quoteTask['materials'])) {
                        foreach ($quoteTask['materials'] as $material) {
                            if (
                                ! empty($material['markup_fee_type']) &&
                                ! empty($material['markup_fee_type_value']) &&
                                $material['markup_fee_type'] === MarkUpFeeTypes::PERCENTAGE() &&
                                $material['markup_fee_type_value'] > 100
                            ) {
                                $validator->errors()->add(
                                    'material.markup_fee_type_value',
                                    'The mark up fee value must be less than 100'
                                );
                            }
                        }
                    }

                }
            },
        ];
    }
}

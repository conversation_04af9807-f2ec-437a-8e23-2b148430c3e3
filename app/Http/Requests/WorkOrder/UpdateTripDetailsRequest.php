<?php

namespace App\Http\Requests\WorkOrder;

use App\Enums\CostTypes;
use App\Enums\MaterialQuantityType;
use App\Http\Requests\BaseApiRequest;
use App\Models\Material;
use App\Models\Media;
use App\Rules\UuidExists;
use Illuminate\Validation\Rules\Enum;

class UpdateTripDetailsRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'service_notes' => 'required|string',
            'materials.*' => 'sometimes|array',
            'materials.*.material_id' => ['sometimes', new UuidExists(Material::class, 'material_uuid')],
            'materials.*.label' => 'required|max:255',
            'materials.*.quantity' => 'required|integer|gt:0',
            'materials.*.quantity_type' => ['required', new Enum(MaterialQuantityType::class)],
            'materials.*.cost_type' => ['required', new Enum(CostTypes::class)],
            'materials.*.unit_price_in_cents' => [
                'required_if:materials.*.cost_type,' . CostTypes::PER_UNIT(),
                'integer',
                'gt:0',
            ],
            'materials.*.cost_in_cents' => [
                'required_if:materials.*.cost_type,' . CostTypes::TOTAL(),
                'integer',
                'gt:0',
            ],
            'media_ids' => ['required', new UuidExists(Media::class, 'media_uuid')],
            'quality_check_status' => 'required|string|in:passed,missing_info,unresolved',
            'note_to_provider' => 'required_if:quality_check_status,missing_info',
        ];
    }
}

<?php

namespace App\Http\Requests\WorkOrder;

use App\Http\Requests\BaseApiRequest;
use App\Models\QuoteTask;
use App\Rules\UuidExists;

class ApproveQuoteRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'approve_quote_task_ids' => 'required|array',
            'approve_quote_task_ids.*' => new UuidExists(QuoteTask::class, 'quote_task_uuid'),
            'reject_quote_task_ids' => 'nullable|array',
            'reject_quote_task_ids.*' => new UuidExists(QuoteTask::class, 'quote_task_uuid'),
        ];
    }
}

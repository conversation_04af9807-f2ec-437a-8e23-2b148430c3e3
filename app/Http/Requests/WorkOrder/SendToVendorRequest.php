<?php

namespace App\Http\Requests\WorkOrder;

use App\Enums\Boolean;
use App\Http\Requests\BaseApiRequest;
use App\Models\Vendor;
use App\Services\Vendor\Enum\Service;
use Closure;
use Illuminate\Validation\Validator;

class SendToVendorRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'vendor_ids' => 'array',
            'select_vendor_all' => ['boolean'],
            'vendor_instructions' => 'filled|string|max:65534',
            'assignment_method' => ['required', 'in:assigned,dispatch'],
            'nte_amount_in_cents' => [
                'required',
                'numeric',
                'min:0',
                'digits_between:1,12',
            ],
        ];
    }

    /**
     * Get the "after" validation callables for the request.
     *
     * @return array<int,Closure>
     */
    public function after(): array
    {
        $isAllSelected = $this->boolean('select_vendor_all');
        $errors = [];

        if (! $isAllSelected) {
            $vendorIds = $this->input('vendor_ids', []);
            if (empty($vendorIds)) {
                $errors[] = function (Validator $validator) {
                    $validator->errors()->add('vendor_ids', 'Select at least one vendor.');
                };
            }
        } else {
            $vendorsExist = Vendor::join('organization_vendors', function ($join) {
                $join->on('organization_vendors.vendor_id', 'vendors.vendor_id')
                    ->where('organization_vendors.organization_id', request()->user()->organization_id);
            })
                ->where('vendors.service', Service::THIRD_PARTY_VENDOR())
                ->where('vendors.is_active', Boolean::YES())
                ->exists();

            if (! $vendorsExist) {
                $errors[] = function (Validator $validator) {
                    $validator->errors()->add('vendor_ids', 'No eligible vendors found for this organization.');
                };
            }
        }

        if (
            $this->input('assignment_method') === 'assigned' &&
            empty($this->input('service_window_reference_id'))
        ) {
            $errors[] = function (Validator $validator) {
                $validator->errors()->add(
                    'service_window_reference_id',
                    'Please select a service window when using the "assigned" method.'
                );
            };
        }

        return $errors;
    }
}

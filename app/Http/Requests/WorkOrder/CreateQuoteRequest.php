<?php

namespace App\Http\Requests\WorkOrder;

use App\Enums\MarkUpFeeTypes;
use App\Enums\MaterialQuantityType;
use App\Http\Requests\BaseApiRequest;
use App\Models\Media;
use App\Rules\UuidExists;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\Rules\Enum;

class CreateQuoteRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'quote_tasks' => 'required|array',
            'quote_tasks.*.cost_in_cents' => 'required_without_all:quote_tasks.*.estimated_time,quote_tasks.*.labor_cost_in_cents|integer',
            'quote_tasks.*.estimated_time' => 'required_without_all:quote_tasks.*.cost_in_cents,quote_tasks.*.labor_cost_in_cents|numeric',
            'quote_tasks.*.labor_cost_in_cents' => 'required_without_all:quote_tasks.*.cost_in_cents,quote_tasks.*.estimated_time|integer|gt:0',
            'quote_tasks.*.markup_fee_type' => ['required_without_all:quote_tasks.*.cost_in_cents,quote_tasks.*.estimated_time', new Enum(MarkUpFeeTypes::class)],
            'quote_tasks.*.markup_fee_type_value' => 'required_without_all:quote_tasks.*.cost_in_cents,quote_tasks.*.estimated_time|numeric|gte:0',
            'quote_tasks.*.description' => 'required',
            'quote_tasks.*.materials' => 'sometimes|array',
            'quote_tasks.*.materials.*.label' => 'required|max:255',
            'quote_tasks.*.materials.*.unit_price_in_cents' => 'required_without_all:quote_tasks.*.materials.*.cost_in_cents,quote_tasks.*.materials.*.material_cost_in_cents|integer|gt:0',
            'quote_tasks.*.materials.*.cost_in_cents' => 'required_without_all:quote_tasks.*.materials.*.unit_price_in_cents,quote_tasks.*.materials.*.material_cost_in_cents|integer|gt:0',
            'quote_tasks.*.materials.*.material_cost_in_cents' => 'required_without_all:quote_tasks.*.materials.*.unit_price_in_cents,quote_tasks.*.materials.*.cost_in_cents|integer|gt:0',
            'quote_tasks.*.materials.*.markup_fee_type' => ['required_without_all:quote_tasks.*.materials.*.unit_price_in_cents,quote_tasks.*.materials.*.cost_in_cents', new Enum(MarkUpFeeTypes::class)],
            'quote_tasks.*.materials.*.markup_fee_type_value' => 'required_without_all:quote_tasks.*.materials.*.unit_price_in_cents,quote_tasks.*.materials.*.cost_in_cents|numeric|gte:0',
            'quote_tasks.*.materials.*.quantity' => 'required|integer|gt:0',
            'quote_tasks.*.materials.*.quantity_type' => ['required', new Enum(MaterialQuantityType::class)],
            'quote_tasks.*.media_ids' => 'sometimes|array',
            'quote_tasks.*.media_ids.*' => ['required', new UuidExists(Media::class, 'media_uuid')],
        ];
    }

    /**
     * Get the "after" validation callables for the request.
     *
     * @return array<int, Closure>
     */
    public function after(): array
    {
        return [
            function (Validator $validator) {
                $requestData = $this->all();
                if (! empty($requestData['quote_tasks'])) {
                    foreach ($requestData['quote_tasks'] as $quote_tasks) {
                        if (
                            ! empty($quote_tasks['markup_fee_type']) &&
                            ! empty($quote_tasks['markup_fee_type_value']) &&
                            $quote_tasks['markup_fee_type'] === MarkUpFeeTypes::PERCENTAGE() &&
                            $quote_tasks['markup_fee_type_value'] > 100
                        ) {
                            $validator->errors()->add(
                                'markup_fee_type_value',
                                'The mark up fee value must be less than 100'
                            );
                        }

                        //if material exists
                        if (! empty($quote_tasks['materials'])) {
                            foreach ($quote_tasks['materials'] as $material) {
                                if (
                                    ! empty($material['markup_fee_type']) &&
                                    ! empty($material['markup_fee_type_value']) &&
                                    $material['markup_fee_type'] === MarkUpFeeTypes::PERCENTAGE() &&
                                    $material['markup_fee_type_value'] > 100
                                ) {
                                    $validator->errors()->add(
                                        'material.markup_fee_type_value',
                                        'The mark up fee value must be less than 100'
                                    );
                                }
                            }
                        }

                    }
                }
            },
        ];
    }
}

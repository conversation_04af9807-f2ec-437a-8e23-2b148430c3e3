<?php

namespace App\Http\Requests\WorkOrder;

use App\Http\Requests\BaseApiRequest;

class UpdateNteAmountRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'nte_amount_in_cents' => [
                'required',
                'numeric',
                'min:0',
                'digits_between:1,12',
            ],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'nte_amount_in_cents.required' => 'The NTE amount field is required.',
            'nte_amount_in_cents.numeric' => 'The NTE amount field must be a number.',
            'nte_amount_in_cents.min' => 'The NTE amount field must be at least 0.',
            //The value is stored in cents, but it's displayed as dollars on the front end, which is why the message displays between 1 and 10 digits.
            'nte_amount_in_cents.digits_between' => 'The NTE amount field must be between 1 and 10 digits.',
        ];
    }
}

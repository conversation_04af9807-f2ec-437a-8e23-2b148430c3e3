<?php

namespace App\Http\Requests\WorkOrder;

use App\Enums\Priority;
use App\Enums\PropertyAccessMethods;
use App\Http\Requests\BaseApiRequest;
use App\Models\ProblemDiagnosis;
use Illuminate\Validation\Rules\Enum;

class StoreRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'description' => 'required',
            'priority' => ['required', new Enum(Priority::class)],
            'problem_diagnoses' => 'required|array',
            'problem_diagnoses.*.problem_diagnosis_id' => [
                'required',
                'exists:problem_diagnoses,problem_diagnosis_id',
            ],
            'property' => 'required|array',
            'property.property_name' => 'nullable|string',
            'property.full_address' => 'required|string|max:255',
            'property.street_address' => 'required|string|max:255',
            'property.unit_number' => 'nullable|string|max:30',
            'property.bed_unit' => 'nullable',
            'property.city' => 'required|string|max:255',
            'property.postal_zip_code' => 'required|digits:5',
            'property.state_id' => [
                'required',
                'exists:states,state_code',
            ],
            'property.country_id' => [
                'required',
                'exists:countries,alpha2_code',
            ],
            'property.latitude' => 'nullable',
            'property.longitude' => 'nullable',
            'property.access_info' => 'nullable|array',
            'property.access_info.method' => ['nullable', new Enum(PropertyAccessMethods::class)],
            'property.access_info.code' => ['required_if:property_access_method,' . PropertyAccessMethods::DIGITAL_KEY_CODE()],
            'property.access_info.note' => 'nullable',
            'residents' => 'sometimes|array',
            'residents.*.first_name' => 'sometimes|nullable',
            'residents.*.last_name' => 'sometimes|nullable',
            'residents.*.email' => 'nullable',
            'residents.*.phone_number' => 'nullable',
            'residents.*.is_primary_resident' => [
                'boolean',
            ],
            'problems' => 'required|array',
            'problems.*.diagnosis' => [
                'required',
                'exists:problem_diagnoses,slug',
            ],
            'problems.*.sub_category' => [
                'required',
                'exists:problem_sub_categories,slug',
            ],
            'problems.*.category' => [
                'required',
                'exists:problem_categories,slug',
            ],
            'photos' => 'nullable|array',
            'photos.*.uri' => 'nullable|url',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'description' => 'description',
            'priority' => 'priority',
            'problem_diagnoses' => 'problem diagnoses',
            'problem_diagnoses.*.problem_diagnosis_id' => 'problem diagnosis id',
            'property' => 'property',
            'property.property_name' => 'property name',
            'property.full_address' => 'property full address',
            'property.street_address' => 'property street address',
            'property.unit_number' => 'property unit number',
            'property.bed_unit' => 'property bed unit',
            'property.city' => 'city',
            'property.postal_zip_code' => 'postal zip code',
            'property.state_id' => 'state',
            'property.country_id' => 'country',
            'property.latitude' => 'property latitude',
            'property.longitude' => 'property longitude',
            'property.access_info' => 'property access info',
            'property.access_info.method' => 'property access method',
            'property.access_info.code' => 'property access code',
            'property.access_info.note' => 'property access note',
            'residents' => 'residents',
            'residents.*.first_name' => 'resident first name',
            'residents.*.last_name' => 'resident last name',
            'residents.*.email' => 'residents email',
            'residents.*.phone_number' => 'resident phone number',
            'residents.*.is_primary_resident' => 'primary resident',
            'problems.*.diagnosis' => 'problems diagnosis',
            'problems.*.sub_category' => 'problems sub category',
            'problems.*.category' => 'problems category',
            'photos' => 'photos',
            'photos.*.uri' => 'url',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'description.required' => 'Work order description required.',
            'priority.required' => 'Work order priority required.',
            'problem_diagnoses.*.required' => 'Work order problem diagnosis required.',
            'property.required' => 'Property details required.',
            'property.array' => 'Invalid property details payload provided.',
            'property.full_address.required' => 'Property full address required.',
            'property.street_address.required' => 'Property street address required.',
            'property.city.required' => 'City required.',
            'property.postal_zip_code.required' => 'Property postal zip code required.',
            'property.postal_zip_code.digits' => 'Property postal zip code must be 5 digits.',
            'property.state_id.required' => 'Property state id required.',
            'property.country_id.required' => 'Property country id required.',
            'residents.required' => 'Residents details required',
            'residents.array' => 'Invalid resident details payload provided.',
            'residents.*.first_name.required' => 'Resident first name required.',
            'residents.*.email.required' => 'Resident email required.',
            'residents.*.phone_number.required' => 'Resident phone number required.',
            'residents.*.is_primary_resident.required' => 'is_primary_resident key required',
            'residents.*.is_primary_resident.boolean' => 'is_primary_resident field must be true or false',
            'problem_diagnoses.*.problem_diagnosis_id' => 'Category not found.',
        ];
    }

    public function prepareForValidation()
    {
        $problems = collect(is_array($this->input('problems')) ? $this->input('problems') : []);

        if ($problems->count()) {

            $problems = $problems->map(function ($problem) {

                $diagnosis = ProblemDiagnosis::join('problem_sub_categories', 'problem_diagnoses.problem_sub_category_id', '=', 'problem_sub_categories.problem_sub_category_id')
                    ->join('problem_categories', 'problem_sub_categories.problem_category_id', '=', 'problem_categories.problem_category_id')
                    ->where('problem_diagnoses.slug', $problem['diagnosis'] ?? null)
                    ->where('problem_sub_categories.slug', $problem['sub_category'] ?? null)
                    ->where('problem_categories.slug', $problem['category'] ?? null)
                    ->first(['problem_diagnoses.problem_diagnosis_id']);

                return [
                    'problem_diagnosis_id' => $diagnosis?->problem_diagnosis_id,
                ];
            });
            $this->merge(['problem_diagnoses' => $problems->toArray()]);
        }
    }
}

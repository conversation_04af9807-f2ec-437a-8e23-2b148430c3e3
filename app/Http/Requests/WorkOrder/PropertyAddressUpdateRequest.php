<?php

namespace App\Http\Requests\WorkOrder;

use App\Http\Requests\BaseApiRequest;
use App\Models\Property;
use App\Models\State;
use App\Rules\UuidExists;

class PropertyAddressUpdateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'property_id' => [
                'required',
                new UuidExists(Property::class, 'property_uuid'),
            ],
            'street_address' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'state_id' => [
                'required',
                new UuidExists(State::class, 'state_uuid'),
            ],
            'postal_zip_code' => 'required|digits:5',
            'unit_number' => 'sometimes|nullable|string|max:30',
        ];
    }
}

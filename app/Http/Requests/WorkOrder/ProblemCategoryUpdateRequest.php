<?php

namespace App\Http\Requests\WorkOrder;

use App\Http\Requests\BaseApiRequest;
use App\Models\ProblemDiagnosis;
use App\Models\WorkOrderTask;
use App\Rules\UuidExists;

class ProblemCategoryUpdateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'work_order_task_id' => [
                'required',
                new UuidExists(WorkOrderTask::class, 'work_order_task_uuid'),
            ],
            'problem_diagnosis_id' => [
                'required',
                new UuidExists(ProblemDiagnosis::class, 'problem_diagnosis_uuid'),
            ],
        ];
    }
}

<?php

namespace App\Http\Requests\WorkOrder;

use App\Http\Requests\BaseApiRequest;
use App\Models\ProblemDiagnosis;
use App\Rules\UuidExists;

class ProblemCategoryCreateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'problem_diagnosis_id' => [
                'required',
                new UuidExists(ProblemDiagnosis::class, 'problem_diagnosis_uuid'),
            ],
        ];
    }
}

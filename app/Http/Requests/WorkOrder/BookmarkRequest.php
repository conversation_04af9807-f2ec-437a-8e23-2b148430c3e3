<?php

namespace App\Http\Requests\WorkOrder;

use App\Http\Requests\BaseApiRequest;

class BookmarkRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string,string>
     */
    public function rules(): array
    {
        return [
            'work_order_uuid' => 'required',
            'action' => 'required|in:add,remove',
        ];
    }
}

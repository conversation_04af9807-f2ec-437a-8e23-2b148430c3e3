<?php

namespace App\Http\Requests\Technician;

use App\Http\Requests\BaseApiRequest;
use Carbon\Carbon;
use Closure;
use Illuminate\Contracts\Validation\Validator;

class WorkingHoursUpdateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            '*.from' => 'required|date_format:h:i a|before:*.to',
            '*.to' => 'required|date_format:h:i a|after:*.from',
            '*.isEnabled' => 'required|boolean',
            'lunch_time_break' => 'required|array',
            'lunch_time_break.duration' => 'required',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            '*.from.required' => 'The from time is required.',
            '*.from.date_format' => 'The from time must be in the format h:i a.',
            '*.from.before' => 'The :attribute time must be before the to time.',
            '*.to.required' => 'The to time is required.',
            '*.to.date_format' => 'The to time must be in the format h:i a.',
            '*.to.after' => 'The :attribute time must be after the from time.',
            'lunch_time_break.required' => 'The lunch time break is required.',
            'lunch_time_break.duration.required' => 'The duration of the lunch time break is required.',
        ];
    }

    /**
     * Get the "after" validation callables for the request.
     *
     * @return array<int, Closure>
     */
    public function after(): array
    {
        $weekDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
        $workingHours = $this->all();

        $workingHours = collect($workingHours);

        // If lunch_time_break "isEnabled"  true for all time
        if (! empty($workingHours['lunch_time_break'])) {
            if (empty($workingHours['lunch_time_break']['isEnabled'])) {
                return [
                    function (Validator $validator) {
                        $validator->errors()->add(
                            'lunch_time_break',
                            'Lunch break isEnabled required.'
                        );
                    },
                ];
            }

            if (! empty($workingHours['lunch_time_break']['duration'])) {
                $lunchBreakFromTime = ! empty($workingHours['lunch_time_break']['from']) ? Carbon::parse($workingHours['lunch_time_break']['from']) : null;
                $lunchBreakToTime = ! empty($workingHours['lunch_time_break']['to']) ? Carbon::parse($workingHours['lunch_time_break']['to']) : null;

                $breakDuration = ! empty($lunchBreakFromTime && $lunchBreakToTime) ? $lunchBreakFromTime->diffInMinutes($lunchBreakToTime) : 0;
                if ($workingHours['lunch_time_break']['duration'] > $breakDuration) {
                    return [
                        function (Validator $validator) {
                            $validator->errors()->add(
                                'lunch_time_break.duration',
                                'Select a valid duration.'
                            );
                        },
                    ];
                }
            }
        }

        $requestedWeekDays = $workingHours->except('lunch_time_break');

        foreach ($requestedWeekDays as $weekDay => $times) {
            // Check keys are valid
            if (! in_array($weekDay, $weekDays)) {
                return [
                    function (Validator $validator) {
                        $validator->errors()->add(
                            'working_hours',
                            'Invalid data passed.'
                        );
                    },
                ];
            }
        }

        if (! $requestedWeekDays->where('isEnabled', true)->count()) {
            return [
                function (Validator $validator) {
                    $validator->errors()->add(
                        'working_hours',
                        'At least one week day is required.'
                    );
                },
            ];
        }

        return [];
    }
}

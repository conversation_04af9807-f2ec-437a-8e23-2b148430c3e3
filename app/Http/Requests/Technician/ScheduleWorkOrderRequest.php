<?php

namespace App\Http\Requests\Technician;

use App\Http\Requests\BaseApiRequest;
use App\Rules\TimingRule;

class ScheduleWorkOrderRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'date' => 'required|date_format:Y-m-d|after_or_equal:' . date('Y-m-d'),
            'timings' => [
                'required', 'array', 'min:1',
                new TimingRule,
            ],
            'duration_in_minutes' => 'required|integer|gt:0',
            'notes' => 'nullable|max:65534',
        ];
    }
}

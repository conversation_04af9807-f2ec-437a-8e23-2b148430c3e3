<?php

namespace App\Http\Requests\Technician;

use App\Enums\ServiceCallStatus;
use App\Http\Requests\BaseApiRequest;
use App\Models\TechnicianAppointment;
use App\Models\WorkOrder;
use App\Rules\UuidExists;
use Carbon\CarbonImmutable;
use Closure;
use Illuminate\Validation\Validator;

class StoreBlockOutRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'work_order_id' => [
                'nullable',
                new UuidExists(WorkOrder::class, 'work_order_uuid'),
            ],
            'scheduled_start_time' => 'required_if:is_block_out_all_day,false',
            'scheduled_end_time' => 'required_if:is_block_out_all_day,false',
            'note' => 'required|string',
            'date' => 'required',
            'is_block_out_all_day' => 'required|bool',
        ];
    }

    /**
     * Get the "after" validation callables for the request.
     *
     * @return array<int, Closure>
     */
    public function after(): array
    {
        /** @var \App\Models\Technician */
        $technician = request()->route('technician');

        $technicianTimezone = $technician->user?->timezone?->name ?? config('settings.default_timezone');
        $technicianDate = CarbonImmutable::parse($this->date . ' ' . CarbonImmutable::now($technicianTimezone)->toTimeString(), $technicianTimezone);

        $startTime = $this->is_block_out_all_day ? $technicianDate->startOfDay() : $technicianDate->setTimeFromTimeString($this->scheduled_start_time);
        $endTime = $this->is_block_out_all_day ? $technicianDate->endOfDay() : $technicianDate->setTimeFromTimeString($this->scheduled_end_time);

        $scheduledStartTime = CarbonImmutable::parse($startTime, $technicianTimezone)->setTimezone('UTC')->addSecond();
        $scheduledEndTime = CarbonImmutable::parse($endTime, $technicianTimezone)->setTimezone('UTC')->subSecond();

        $existingAgendaTimings = TechnicianAppointment::where('technician_id', $technician->technician_id)
            ->leftjoin('work_order_service_calls', 'work_order_service_calls.technician_appointment_id', 'technician_appointments.technician_appointment_id')
            ->where(function ($query) {
                $query->where('work_order_service_calls.status', ServiceCallStatus::ACTIVE)
                    ->orWhereNull('work_order_service_calls.status');
            })
            ->where('technician_appointments.scheduled_start_time', '<=', $scheduledEndTime)
            ->where('technician_appointments.scheduled_end_time', '>=', $scheduledStartTime)
            ->select(
                'technician_appointments.scheduled_start_time',
                'technician_appointments.scheduled_end_time',
                'technician_appointments.technician_appointment_id',
                'work_order_service_calls.status'
            )
            ->exists();

        if ($existingAgendaTimings) {
            return [
                function (Validator $validator) {
                    $validator->errors()->add(
                        'block_out',
                        'The given time already exists in the technician\'s schedule.'
                    );
                },
            ];
        }

        return [];
    }
}

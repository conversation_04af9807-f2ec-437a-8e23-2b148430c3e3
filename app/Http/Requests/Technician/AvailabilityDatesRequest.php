<?php

namespace App\Http\Requests\Technician;

use App\Http\Requests\BaseApiRequest;

class AvailabilityDatesRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'start_date' => 'required|date_format:Y-m-d',
            'duration_in_minutes' => 'required|gt:0',
        ];
    }
}

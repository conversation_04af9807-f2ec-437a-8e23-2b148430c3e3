<?php

namespace App\Http\Requests\Technician;

use App\Http\Requests\BaseApiRequest;
use App\Models\ProblemCategory;
use App\Models\ProblemDiagnosis;
use App\Models\ProblemSubCategory;
use Closure;
use Illuminate\Validation\Validator;

class SkillsUpdateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'problem_categories' => 'required|array',
            'problem_categories.*.is_selected' => 'required|boolean',
            'problem_categories.*.sub_categories' => 'required|array',
            'problem_categories.*.sub_categories.*.is_selected' => 'required|boolean',
            'problem_categories.*.sub_categories.*.problem_diagnosis_ids' => 'required_if:problem_categories.*.sub_categories.*.is_selected,true|array',
        ];
    }

    /**
     * Get the "after" validation callables for the request.
     *
     * @return array<int,Closure>
     */
    public function after(): array
    {
        $requestData = collect((array) $this->input('problem_categories'));

        $problemCategoryIds = $requestData->pluck('problem_category_id')->toArray();
        $subCategoryIds = $requestData->pluck('sub_categories.*.sub_category_id')->flatten()->toArray();
        $diagnosisIds = $requestData->pluck('sub_categories.*.problem_diagnosis_ids')->flatten()->toArray();

        if (count($problemCategoryIds) === 0) {
            return [
                function (Validator $validator) {
                    $validator->errors()->add(
                        'problem_category_id',
                        'Problem category id(s) required'
                    );
                },
            ];
        }

        if (count($subCategoryIds) === 0) {
            return [
                function (Validator $validator) {
                    $validator->errors()->add(
                        'sub_category_id',
                        'Sub category id(s) required'
                    );
                },
            ];
        }

        if (count($diagnosisIds) === 0) {
            return [
                function (Validator $validator) {
                    $validator->errors()->add(
                        'problem_diagnosis_id',
                        'Problem diagnosis id(s) required'
                    );
                },
            ];
        }

        $selectedProblemCategoryCount = ProblemCategory::select('problem_category_id')->whereUuid($problemCategoryIds)->count();
        if ($selectedProblemCategoryCount !== count($problemCategoryIds)) {
            return [
                function (Validator $validator) {
                    $validator->errors()->add(
                        'problem_category_id',
                        'Invalid problem_category_id'
                    );
                },
            ];
        }

        $selectedSubCategoryCount = ProblemSubCategory::select('problem_sub_category_id')->whereUuid($subCategoryIds)->count();
        if ($selectedSubCategoryCount !== count($subCategoryIds)) {
            return [
                function (Validator $validator) {
                    $validator->errors()->add(
                        'sub_category_id',
                        'Invalid sub_category_id'
                    );
                },
            ];
        }

        $selectedProblemDiagnosisCount = ProblemDiagnosis::select('problem_diagnosis_id')->whereUuid($diagnosisIds)->count();
        if ($selectedProblemDiagnosisCount !== count($diagnosisIds)) {
            return [
                function (Validator $validator) {
                    $validator->errors()->add(
                        'problem_diagnosis_id',
                        'Invalid problem_diagnosis_id'
                    );
                },
            ];
        }

        return [];
    }
}

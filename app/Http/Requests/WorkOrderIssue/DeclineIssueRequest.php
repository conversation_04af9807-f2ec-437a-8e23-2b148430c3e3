<?php

namespace App\Http\Requests\WorkOrderIssue;

use App\Http\Requests\BaseApiRequest;

class DeclineIssueRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'reason' => 'required|max:65535',
        ];
    }
}

<?php

namespace App\Http\Requests\WorkOrderIssue;

use App\Enums\CostTypes;
use App\Enums\MaterialQuantityType;
use App\Models\Media;
use App\Rules\UuidExists;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class MarkAsDoneIssueRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'service_notes' => 'required|string',
            'materials.*' => 'sometimes|array',
            'materials.*.label' => 'required|max:255',
            'materials.*.quantity' => 'required|integer|gt:0',
            'materials.*.quantity_type' => ['required', new Enum(MaterialQuantityType::class)],
            'materials.*.cost_type' => ['required', new Enum(CostTypes::class)],
            'materials.*.unit_price_in_cents' => [
                'required_if:materials.*.cost_type,' . CostTypes::PER_UNIT(),
                'integer',
                'gt:0',
            ],
            'materials.*.cost_in_cents' => [
                'required_if:materials.*.cost_type,' . CostTypes::TOTAL(),
                'integer',
                'gt:0',
            ],
            'before_media_ids' => 'required|array',
            'before_media_ids.*' => ['required', new UuidExists(Media::class, 'media_id')],
            'after_media_ids' => 'required|array',
            'after_media_ids.*' => ['required', new UuidExists(Media::class, 'media_id')],
            'issue_caused_by_resident' => 'sometimes|boolean',
            'issue_caused_details' => 'required_if:issue_caused_by_resident,true',
        ];
    }
}

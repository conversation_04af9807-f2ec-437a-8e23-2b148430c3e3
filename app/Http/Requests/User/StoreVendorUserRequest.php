<?php

namespace App\Http\Requests\User;

use App\Enums\UserTypes;
use App\Http\Requests\BaseApiRequest;
use Illuminate\Validation\Rule;

class StoreVendorUserRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'email',
                Rule::unique('users', 'email')
                    ->where('user_type', UserTypes::TECHNICIAN()),
            ],
            'first_name' => 'required|string|max:255',
            'last_name' => 'nullable|string|max:255',
        ];
    }
}

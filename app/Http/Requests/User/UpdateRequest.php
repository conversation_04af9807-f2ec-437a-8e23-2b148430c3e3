<?php

namespace App\Http\Requests\User;

use App\Enums\UserTypes;
use App\Http\Requests\BaseApiRequest;
use App\Models\Country;
use App\Models\Role;
use App\Models\State;
use App\Rules\UuidExists;
use Closure;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;
use Illuminate\Validation\Rules\Unique;

class UpdateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'first_name' => 'required|string|max:255',
            'middle_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'is_active' => 'required|boolean',
            'email' => [
                'required',
                'email',
                Rule::unique('users', 'email')
                    ->where('organization_id', $this->attributes->get('organization_id'))
                    ->when($this->user?->user_type === UserTypes::TECHNICIAN(), function (Unique $query) {
                        return $query->where('user_type', UserTypes::TECHNICIAN());
                    }, function (Unique $query) {
                        return $query->whereNot('user_type', UserTypes::TECHNICIAN());
                    })
                    ->ignore($this->user?->user_id, 'user_id'),
            ],
            'phone_number' => 'nullable|digits:10',
            'roles' => 'required|array',
            'roles.*' => [
                'required',
                new UuidExists(Role::class, 'role_uuid'),
            ],
            'address' => 'required|array',
            'address.country_id' => [
                'required',
                new UuidExists(Country::class, 'country_uuid'),
            ],
            'address.state_id' => [
                'required',
                new UuidExists(State::class, 'state_uuid'),
            ],
            'address.street_address' => 'required|max:255',
            'address.apt_suite_unit' => 'nullable|max:30',
            'address.city' => 'required|max:255',
            'address.zip_code' => 'required|digits:5',
            'password' => [
                'nullable',
                'confirmed',
                Password::min(8)
                    ->mixedCase()
                    ->numbers()
                    ->symbols(),
            ],
            'password_confirmation' => 'required_with:password',
        ];
    }

    /**
     * Get the "after" validation callable for the request.
     *
     * @return array<int, Closure>
     */
    public function after(): array
    {
        if ($this->user?->user_type !== UserTypes::TECHNICIAN()) {
            $technicianRole = Role::select('role_id', 'role_uuid')
                ->where('organization_id', $this->user()?->organization_id)
                ->where('name', 'Technician')
                ->first();

            if (! empty($technicianRole)) {
                if (in_array($technicianRole->role_uuid, $this->get('roles'))) {
                    return [
                        function (Validator $validator) {
                            $validator->errors()->add(
                                'roles',
                                "invalid role(s), can't updated to technician role"
                            );
                        },
                    ];
                }
            }
        }

        return [];
    }
}

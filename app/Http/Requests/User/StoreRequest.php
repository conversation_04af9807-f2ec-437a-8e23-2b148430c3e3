<?php

namespace App\Http\Requests\User;

use App\Enums\UserTypes;
use App\Http\Requests\BaseApiRequest;
use App\Models\Role;
use App\Rules\UuidExists;
use Illuminate\Validation\Rule;

class StoreRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'email',
                Rule::unique('users', 'email')
                    ->where('user_type', UserTypes::ACCOUNT_USER())
                    ->where('organization_id', $this->attributes->get('organization_id')),
            ],
            'first_name' => 'required|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'roles' => [
                'required',
                'array',
                new UuidExists(Role::class, 'role_uuid'),
            ],
        ];
    }
}

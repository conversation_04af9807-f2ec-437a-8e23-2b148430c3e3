<?php

namespace App\Http\Requests\View;

use App\Http\Requests\BaseApiRequest;
use App\Models\View;
use App\Models\ViewType;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Http\Request;
use Illuminate\Validation\Validator;

class StoreRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(Request $request): array
    {
        return [
            'view_type' => 'required|exists:view_types,slug',
            'payload' => 'required',
            'payload.columns' => 'required_if:view_type,users,work-orders',
            'payload.filters' => 'required_if:view_type,users,work-orders',
            'payload.grouping' => 'required_if:view_type,users,work-orders',
            'payload.calendarFilter' => 'required_if:view_type,calendar',
            'payload.calendarFilter.technician' => 'sometimes',
            'payload.calendarFilter.technician.label' => 'required_unless:payload.calendarFilter.technician,null',
            'payload.calendarFilter.technician.value' => 'required_unless:payload.calendarFilter.technician,null',
            'payload.calendarFilter.calendarMode' => 'required_if:view_type,calendar',
            'payload.calendarFilter.settings' => 'required_if:view_type,calendar',
            'name' => 'required|max:15',
        ];
    }

    /**
     * Get the "after" validation callable for the request.
     *
     * @return array<int, Closure>
     */
    public function after(): array
    {
        $userId = $this->user()?->user_id;
        $organizationId = $this->user()?->organization_id;
        $name = $this->get('name', null);
        if ($name) {
            $viewType = ViewType::where('slug', $this->get('view_type'))->firstOrFail();

            $view = View::withoutGlobalScope('organization')
                ->where('name', $name)
                ->where(function ($query) use ($userId, $organizationId) {
                    $query->where(function ($query) use ($userId, $organizationId) {
                        $query->where('organization_id', $organizationId)
                            ->where('user_id', $userId);
                    })
                        ->orWhere('scope', 'global');
                })
                ->where('view_type_id', $viewType->view_type_id)
                ->count();
            if ($view) {
                return [
                    function (Validator $validator) {
                        $validator->errors()->add(
                            'name',
                            'Name already used'
                        );

                    },
                ];
            }

        }

        return [];
    }
}

<?php

namespace App\Http\Requests\View;

use Illuminate\Foundation\Http\FormRequest;

class CountRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, array<int, string>>
     */
    public function rules(): array
    {
        return [
            'view_type' => [
                'required',
                'exists:view_types,slug',
            ],
        ];
    }
}

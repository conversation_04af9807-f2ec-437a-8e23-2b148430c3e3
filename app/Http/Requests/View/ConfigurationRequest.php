<?php

namespace App\Http\Requests\View;

use App\Http\Requests\BaseApiRequest;

class ConfigurationRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, array<int, string>>
     */
    public function rules(): array
    {
        return [
            'view_type' => [
                'required',
                'exists:view_types,slug',
            ],
        ];
    }
}

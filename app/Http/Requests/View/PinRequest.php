<?php

namespace App\Http\Requests\View;

use App\Http\Requests\BaseApiRequest;
use Illuminate\Http\Request;

class PinRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string,string>
     */
    public function rules(Request $request): array
    {
        return [
            'action' => 'required|in:pin,unpin',
            'view_type' => 'required|exists:view_types,slug',
        ];
    }
}

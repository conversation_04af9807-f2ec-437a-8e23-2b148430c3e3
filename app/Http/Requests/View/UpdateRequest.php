<?php

namespace App\Http\Requests\View;

use App\Http\Requests\BaseApiRequest;
use Illuminate\Http\Request;

class UpdateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string,string>
     */
    public function rules(Request $request): array
    {
        return [
            'view_type' => 'required|exists:view_types,slug',
            'payload' => 'required',
            'payload.columns' => 'required_if:view_type,users,work-orders',
            'payload.filters' => 'required_if:view_type,users,work-orders',
            'payload.grouping' => 'required_if:view_type,users,work-orders',
            'payload.calendarFilter' => 'required_if:view_type,calendar',
            'payload.calendarFilter.technician' => 'required_if:view_type,calendar',
            'payload.calendarFilter.calendarMode' => 'required_if:view_type,calendar',
            'payload.calendarFilter.settings' => 'required_if:view_type,calendar',
        ];
    }
}

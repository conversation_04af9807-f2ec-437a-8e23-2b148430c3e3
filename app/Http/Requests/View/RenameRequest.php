<?php

namespace App\Http\Requests\View;

use App\Http\Requests\BaseApiRequest;
use App\Models\View;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\Validator;

class RenameRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string,string>
     */
    public function rules(Request $request): array
    {
        return [
            'name' => 'required|max:15',
            'view_type' => 'required|exists:view_types,slug',
        ];
    }

    /**
     * Get the "after" validation callable for the request.
     *
     * @return array<int, Closure>
     */
    public function after(): array
    {
        $userId = $this->user()?->user_id;
        $organizationId = $this->user()?->organization_id;
        $name = $this->get('name', '');
        $currentView = $this->view;

        if ($currentView) {
            $view = View::withoutGlobalScope('organization')
                ->select('view_id')
                ->where('name', $name)
                ->WhereNotUuid($currentView->view_uuid)
                ->where(function ($query) use ($userId, $organizationId) {
                    $query->where(function ($query) use ($userId, $organizationId) {
                        $query->where('organization_id', $organizationId)
                            ->where('user_id', $userId);
                    })
                        ->orWhere('scope', 'global');
                })
                ->where('view_type_id', $currentView->view_type_id)
                ->count();

            return [
                function (Validator $validator) use ($view) {
                    if ($view) {
                        $validator->errors()->add(
                            'name',
                            'Name already used'
                        );
                    }
                },
            ];
        }

        return [];
    }
}

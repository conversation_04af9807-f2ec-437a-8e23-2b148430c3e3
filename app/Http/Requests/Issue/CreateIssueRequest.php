<?php

declare(strict_types=1);

namespace App\Http\Requests\Issue;

use App\Http\Requests\BaseApiRequest;
use App\Models\ProblemDiagnosis;
use App\Rules\UuidExists;

class CreateIssueRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|max:255',
            'description' => 'required|max:65535',
            'problem_diagnosis_id' => [
                'required',
                new UuidExists(ProblemDiagnosis::class, 'problem_diagnosis_uuid'),
            ],
        ];
    }
}

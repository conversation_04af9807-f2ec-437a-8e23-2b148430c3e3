<?php

namespace App\Http\Requests\Issue;

use App\Http\Requests\BaseApiRequest;
use App\Models\WorkOrder;
use App\Rules\UuidExists;

class AssignRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'work_order_id' => ['required', new UuidExists(WorkOrder::class, 'work_order_uuid')],
        ];
    }
}

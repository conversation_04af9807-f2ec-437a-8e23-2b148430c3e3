<?php

namespace App\Http\Requests\Auth;

use App\Http\Requests\BaseApiRequest;

class PublicApiAuthenticateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'scope' => 'required',
        ];
    }
}

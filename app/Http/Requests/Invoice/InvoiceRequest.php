<?php

namespace App\Http\Requests\Invoice;

use App\Enums\InvoiceLineItemTypes;
use App\Enums\InvoiceSubsidiaryTypes;
use App\Enums\MarkUpFeeTypes;
use App\Enums\MaterialQuantityType;
use App\Http\Requests\BaseApiRequest;
use App\Models\Invoice;
use App\Models\InvoiceLineItem;
use App\Models\Quote;
use App\Models\QuoteTask;
use App\Models\QuoteTaskMaterial;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderTaskMaterial;
use App\Rules\UuidExists;
use App\States\Invoices\Draft;
use App\States\Invoices\PaymentPending;
use Closure;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Validator;

class InvoiceRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {

        return [
            'invoice_id' => [
                'sometimes',
                new UuidExists(Invoice::class, 'invoice_uuid'),
            ],
            'invoice_status' => 'sometimes|in:' . Draft::$name . ',' . PaymentPending::$name,
            'work_order_info' => 'array|required',
            'work_order_info.description' => 'string|required',
            'trips' => 'array|required',
            'trips.*.work_order_service_call_id' => [
                'required',
                new UuidExists(WorkOrderServiceCall::class, 'work_order_service_call_uuid'),
            ],
            'trips.*.work_order_service_call_status' => 'required_with:trips.*.work_order_service_call_uuid',
            'trips.*.scheduled_start_time' => 'sometimes',
            'trips.*.scheduled_end_time' => 'sometimes',
            'trips.*.media_details' => 'sometimes|array',
            'trips.*.invoice_line_items' => 'array|required',
            'trips.*.invoice_line_items.*.invoice_line_item_id' => [
                'sometimes',
                new UuidExists(InvoiceLineItem::class, 'invoice_line_item_uuid'),
            ],
            'trips.*.invoice_line_items.*.item_type' => [
                'required',
                new Enum(InvoiceLineItemTypes::class),
            ],
            'trips.*.invoice_line_items.*.description' => 'nullable|string',
            'trips.*.invoice_line_items.*.total_markup_fee_in_cents' => [
                'integer',
                'gte:0',
                'required_unless:trips.*.invoice_line_items.*.item_type,' . InvoiceLineItemTypes::SERVICE_NOTE(),
            ],
            'trips.*.invoice_line_items.*.cost_in_cents' => [
                'integer',
                'gte:0',
                'required_unless:trips.*.invoice_line_items.*.item_type,' . InvoiceLineItemTypes::SERVICE_NOTE(),
            ],
            'trips.*.invoice_line_items.*.is_enabled' => [
                'boolean',
                'required_if:trips.*.invoice_line_items.*.item_type,' . InvoiceLineItemTypes::TRIP_FEE() . ',' . InvoiceLineItemTypes::NO_SHOW_FEE(),
            ],
            'trips.*.invoice_line_items.*.total_cost_in_cents' => [
                'integer',
                'gte:0',
                'required_unless:trips.*.invoice_line_items.*.item_type,' . InvoiceLineItemTypes::SERVICE_NOTE(),
            ],
            'trips.*.invoice_line_items.*.quote_id' => [
                'required_if:trips.*.invoice_line_items.*.item_type,' . InvoiceLineItemTypes::SERVICE_NOTE(),
                new UuidExists(Quote::class, 'quote_uuid'),
            ],
            'trips.*.invoice_line_items.*.quote_task_id' => [
                'required_if:trips.*.invoice_line_items.*.item_type,' . InvoiceLineItemTypes::QUOTE_TASK(),
                new UuidExists(QuoteTask::class, 'quote_task_uuid'),
            ],

            'trips.*.invoice_line_items.*.subsidiaries' => 'array|required_unless:trips.*.invoice_line_items.*.item_type,' . InvoiceLineItemTypes::MATERIAL(),
            'trips.*.invoice_line_items.*.subsidiaries.*.subsidiary_type' => ['required', new Enum(InvoiceSubsidiaryTypes::class)],
            'trips.*.invoice_line_items.*.subsidiaries.*.work_order_task_material_id' => [
                'nullable',
                new UuidExists(WorkOrderTaskMaterial::class, 'work_order_task_material_uuid'),
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.quote_task_material_id' => [
                'nullable',
                new UuidExists(QuoteTaskMaterial::class, 'quote_task_material_uuid'),
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.description' => [
                'nullable',
                'string',
                'required_if:trips.*.invoice_line_items.*.subsidiaries.*.subsidiary_type,' . InvoiceSubsidiaryTypes::MATERIAL() . ',' . InvoiceSubsidiaryTypes::LABOR(),
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.hourly_rate_in_cents' => [
                'gte:0',
                'integer',
                'required_if:trips.*.invoice_line_items.*.subsidiaries.*.subsidiary_type,' . InvoiceSubsidiaryTypes::HOURLY_LABOR() . ',' . InvoiceSubsidiaryTypes::DRIVE_RATE(),
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.hours' => [
                'gte:0',
                'integer',
                'required_if:trips.*.invoice_line_items.*.subsidiaries.*.subsidiary_type,' . InvoiceSubsidiaryTypes::HOURLY_LABOR() . ',' . InvoiceSubsidiaryTypes::DRIVE_RATE(),
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.minutes' => [
                'gte:0',
                'integer',
                'required_if:trips.*.invoice_line_items.*.subsidiaries.*.subsidiary_type,' . InvoiceSubsidiaryTypes::HOURLY_LABOR() . ',' . InvoiceSubsidiaryTypes::DRIVE_RATE(),
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.markup_fee_type' => [
                'nullable',
                new Enum(MarkUpFeeTypes::class),
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.markup_fee_type_value' => [
                'nullable',
                'gte:0',
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.markup_fee_in_cents' => [
                'nullable',
                'integer',
                'gte:0',
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.cost_in_cents' => [
                'nullable',
                'required_if:trips.*.invoice_line_items.*.subsidiaries.*.subsidiary_type,' . InvoiceSubsidiaryTypes::MATERIAL() . ',' . InvoiceSubsidiaryTypes::HOURLY_LABOR() . ',' . InvoiceSubsidiaryTypes::DRIVE_RATE(),
                'integer',
                'gte:0',
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.total_cost_in_cents' => [
                'nullable',
                'required_if:trips.*.invoice_line_items.*.subsidiaries.*.subsidiary_type,' . InvoiceSubsidiaryTypes::MATERIAL() . ',' . InvoiceSubsidiaryTypes::HOURLY_LABOR() . ',' . InvoiceSubsidiaryTypes::DRIVE_RATE(),
                'integer',
                'gte:0',
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.quantity' => [
                'required_if:trips.*.invoice_line_items.*.subsidiaries.*.subsidiary_type,' . InvoiceSubsidiaryTypes::MATERIAL(),
                'integer',
                'gte:0',
            ],
            'trips.*.invoice_line_items.*.subsidiaries.*.quantity_type' => [
                'nullable',
                'required_if:trips.*.invoice_line_items.*.subsidiaries.*.subsidiary_type,' . InvoiceSubsidiaryTypes::MATERIAL(),
                new Enum(MaterialQuantityType::class),
            ],
        ];
    }

    /**
     * Get the "after" validation callables for the request.
     *
     * @return array<int, Closure>
     */
    public function after(): array
    {
        return [
            function (Validator $validator) {
                $requestData = $this->all();
                if (isset($requestData['trips']) && count($requestData['trips'])) {
                    foreach ($requestData['trips'] as $trip) {
                        $invoiceItems = $trip['invoice_line_items'];
                        foreach ($invoiceItems as $invoiceItem) {
                            if ($invoiceItem['item_type'] === InvoiceLineItemTypes::MATERIAL()) {
                                foreach ($invoiceItem['subsidiaries'] as $subsidiary) {
                                    if (in_array($subsidiary['subsidiary_type'], [
                                        InvoiceSubsidiaryTypes::MATERIAL(),
                                    ]
                                    )) {
                                        if (
                                            ! isset($subsidiary['markup_fee_in_cents']) ||
                                            is_null($subsidiary['markup_fee_in_cents'])
                                        ) {
                                            $validator->errors()->add(
                                                'material.subsidiaries.markup_fee_in_cents',
                                                'The markup_fee_in_cents field is required when line item type is material and subsidiary type is material'
                                            );
                                        }
                                        if (
                                            ! isset($subsidiary['markup_fee_type_value']) ||
                                            is_null($subsidiary['markup_fee_type_value'])
                                        ) {
                                            $validator->errors()->add(
                                                'material.subsidiaries.markup_fee_type_value',
                                                'The markup_fee_type_value field is required when line item type is material and subsidiary type is material'
                                            );
                                        }
                                        if (
                                            ! isset($subsidiary['markup_fee_type']) ||
                                            is_null($subsidiary['markup_fee_type'])
                                        ) {
                                            $validator->errors()->add(
                                                'material.subsidiaries.markup_fee_type',
                                                'The markup_fee_type field is required when line item type is material and subsidiary type is material'
                                            );
                                        }

                                        if (
                                            ! empty($subsidiary['markup_fee_type']) &&
                                            ! empty($subsidiary['markup_fee_type_value']) &&
                                            $subsidiary['markup_fee_type'] === MarkUpFeeTypes::PERCENTAGE() &&
                                            $subsidiary['markup_fee_type_value'] > 100
                                        ) {
                                            $validator->errors()->add(
                                                'material.subsidiaries.markup_fee_type_value',
                                                'The mark up fee value must be less than 100'
                                            );
                                        }

                                    }
                                }
                            }
                            if ($invoiceItem['item_type'] === InvoiceLineItemTypes::QUOTE_TASK()) {

                                foreach ($invoiceItem['subsidiaries'] as $subsidiary) {
                                    if (in_array($subsidiary['subsidiary_type'], [
                                        InvoiceSubsidiaryTypes::MATERIAL(),
                                        InvoiceSubsidiaryTypes::LABOR(),
                                    ]
                                    )) {
                                        if (
                                            ! isset($subsidiary['markup_fee_in_cents']) ||
                                            is_null($subsidiary['markup_fee_in_cents'])
                                        ) {
                                            $validator->errors()->add(
                                                'quote_task.subsidiaries.markup_fee_in_cents',
                                                'The markup_fee_in_cents field is required when line item type is material and subsidiary type is material/labor'
                                            );
                                        }
                                        if (
                                            ! isset($subsidiary['markup_fee_type_value']) ||
                                            is_null($subsidiary['markup_fee_type_value'])
                                        ) {
                                            $validator->errors()->add(
                                                'quote_task.subsidiaries.markup_fee_type_value',
                                                'The markup_fee_type_value field is required when line item type is material and subsidiary type is material/labor'
                                            );
                                        }
                                        if (
                                            ! isset($subsidiary['markup_fee_type']) ||
                                            is_null($subsidiary['markup_fee_type'])
                                        ) {
                                            $validator->errors()->add(
                                                'quote_task.subsidiaries.markup_fee_type',
                                                'The markup_fee_type field is required when line item type is material and subsidiary type is material/labor'
                                            );
                                        }
                                        if (
                                            ! empty($subsidiary['markup_fee_type']) &&
                                            ! empty($subsidiary['markup_fee_type_value']) &&
                                            $subsidiary['markup_fee_type'] === MarkUpFeeTypes::PERCENTAGE() &&
                                            $subsidiary['markup_fee_type_value'] > 100
                                        ) {
                                            $validator->errors()->add(
                                                'quote_task.subsidiaries.markup_fee_type_value',
                                                'The mark up fee value must be less than 100'
                                            );
                                        }
                                        if ($subsidiary['subsidiary_type'] === InvoiceSubsidiaryTypes::MATERIAL() &&
                                            empty($subsidiary['quote_task_material_id'])
                                        ) {
                                            $validator->errors()->add(
                                                'quote_task.subsidiaries.quote_task_material_id',
                                                'The quote task material id is required'
                                            );
                                        }

                                        if ($subsidiary['subsidiary_type'] === InvoiceSubsidiaryTypes::LABOR()) {
                                            if (empty($subsidiary['quote_task_id'])) {
                                                $validator->errors()->add(
                                                    'quote_task.subsidiaries.quote_task_id',
                                                    'Quote task id required.'
                                                );
                                            } else {
                                                if (! QuoteTask::select('quote_task_id')->whereUuid($subsidiary['quote_task_id'])->count()) {
                                                    $validator->errors()->add(
                                                        'quote_task.subsidiaries.quote_task_id',
                                                        'Invalid quote task id.'
                                                    );
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            if ($invoiceItem['item_type'] === InvoiceLineItemTypes::HOURLY_LABOR()) {

                                foreach ($invoiceItem['subsidiaries'] as $subsidiary) {
                                    if (in_array($subsidiary['subsidiary_type'], [
                                        InvoiceSubsidiaryTypes::HOURLY_LABOR(),
                                        InvoiceSubsidiaryTypes::DRIVE_RATE(),
                                    ]
                                    )) {
                                        if (
                                            ! isset($subsidiary['markup_fee_in_cents']) ||
                                            is_null($subsidiary['markup_fee_in_cents'])
                                        ) {
                                            $validator->errors()->add(
                                                'hourly_labor.subsidiaries.markup_fee_in_cents',
                                                'The markup_fee_in_cents field is required when line item type is material and subsidiary type is hourly_labor'
                                            );
                                        }
                                        if (
                                            ! isset($subsidiary['markup_fee_type_value']) ||
                                            is_null($subsidiary['markup_fee_type_value'])
                                        ) {
                                            $validator->errors()->add(
                                                'hourly_labor.subsidiaries.markup_fee_type_value',
                                                'The markup_fee_type_value field is required when line item type is hourly labor and subsidiary type is hourly_labor'
                                            );
                                        }
                                        if (
                                            ! isset($subsidiary['markup_fee_type']) ||
                                            is_null($subsidiary['markup_fee_type'])
                                        ) {
                                            $validator->errors()->add(
                                                'hourly_labor.subsidiaries.markup_fee_type',
                                                'The markup_fee_type field is required when line item type is hourly labor and subsidiary type is hourly_labor'
                                            );
                                        }
                                        if (
                                            empty($subsidiary['hours']) &&
                                            empty($subsidiary['minutes'])
                                        ) {
                                            $validator->errors()->add(
                                                'hourly_labor.subsidiaries.minutes',
                                                'The working hour(s) minimum one minute required'
                                            );
                                        }
                                        if (
                                            ! empty($subsidiary['markup_fee_type']) &&
                                            ! empty($subsidiary['markup_fee_type_value']) &&
                                            $subsidiary['markup_fee_type'] === MarkUpFeeTypes::PERCENTAGE() &&
                                            $subsidiary['markup_fee_type_value'] > 100
                                        ) {
                                            $validator->errors()->add(
                                                'hourly_labor.subsidiaries.markup_fee_type_value',
                                                'The mark up fee value must be less than 100'
                                            );
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
        ];
    }
}

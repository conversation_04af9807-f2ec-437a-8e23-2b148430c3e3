<?php

namespace App\Http\Requests\Organization;

use App\Http\Requests\BaseApiRequest;
use App\Models\Country;
use App\Models\State;
use App\Rules\UuidExists;

class UpdateRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'sometimes|string|max:255',
            'address' => 'sometimes|string|max:255',
            'city' => 'sometimes|string|max:255',
            'zip_code' => 'sometimes|string|max:5',
            'state_id' => [
                'sometimes',
                new UuidExists(State::class, 'state_uuid'),
            ],
            'country_id' => [
                'sometimes',
                new UuidExists(Country::class, 'country_uuid'),
            ],
            'logo' => 'sometimes|image|mimes:jpeg,png,jpg,gif,svg',
            'phone_number' => 'sometimes|digits_between:10,12',
        ];
    }
}

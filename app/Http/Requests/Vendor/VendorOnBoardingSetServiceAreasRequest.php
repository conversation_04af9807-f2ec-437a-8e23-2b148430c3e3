<?php

namespace App\Http\Requests\Vendor;

use App\Http\Requests\BaseApiRequest;

class VendorOnBoardingSetServiceAreasRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string,string>
     */
    public function rules(): array
    {
        return [
            'onboardingId' => 'required|string',
            'zip_codes' => 'required|array|min:1',
            'radius' => 'required|string',
        ];
    }
}

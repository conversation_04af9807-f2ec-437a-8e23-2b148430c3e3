<?php

namespace App\Http\Requests\Vendor;

use App\Http\Requests\BaseApiRequest;
use App\Services\Vendor\Enum\Service;
use Illuminate\Validation\Rules\Enum;

class StoreRequest extends BaseApiRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<mixed>
     */
    public function rules(): array
    {
        return [
            'company_name' => 'required|string|max:255',
            'service' => ['required', new Enum(Service::class)],
            'organizations' => [
                'required',
                'array',
            ],
            'organizations.*.organization_id' => [
                'required',
                'exists:organizations,organization_id',
            ],
            'organizations.*.client_id' => [
                'required',
                'string',
            ],
            'organizations.*.secret' => [
                'required',
                'string',
            ],
            'organizations.*.webhook_secret' => [
                'required',
                'string',
            ],
        ];
    }
}

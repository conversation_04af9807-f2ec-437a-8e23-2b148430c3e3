<?php

namespace App\Http\Requests\Vendor;

use App\Http\Requests\BaseApiRequest;
use App\Models\Vendor;
use App\Rules\UuidExists;

class VendorOnboardingInitialRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string,string>
     */
    public function rules(): array
    {
        return [
            'vendor_id' => [
                'required',
                new UuidExists(Vendor::class, 'vendor_uuid'),
            ],
            'signed_url' => [
                'required',
                'url',
            ],
        ];
    }
}

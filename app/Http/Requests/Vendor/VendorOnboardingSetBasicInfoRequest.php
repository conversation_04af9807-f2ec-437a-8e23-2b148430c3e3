<?php

namespace App\Http\Requests\Vendor;

use App\Http\Requests\BaseApiRequest;

class VendorOnboardingSetBasicInfoRequest extends BaseApiRequest
{
    private const REQUIRED_STRING_MAX_255 = 'required|string|max:255';
    private const REQUIRED_STRING_MAX_100 = 'required|string|max:100';

    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string,string>
     */
    public function rules(): array
    {
        return [
            'onboardingId' => 'required|string',
            'first_name' => self::REQUIRED_STRING_MAX_255,
            'last_name' => self::REQUIRED_STRING_MAX_255,
            'phone_number' => self::REQUIRED_STRING_MAX_255,
            'address1' => self::REQUIRED_STRING_MAX_255,
            'city' => self::REQUIRED_STRING_MAX_100,
            'state_id' => self::REQUIRED_STRING_MAX_100,
            'postal_zip_code' => 'required|string|max:20',
        ];
    }
}

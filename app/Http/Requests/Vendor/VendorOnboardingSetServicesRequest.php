<?php

namespace App\Http\Requests\Vendor;

use App\Http\Requests\BaseApiRequest;

class VendorOnboardingSetServicesRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string,string>
     */
    public function rules(): array
    {
        return [
            'onboardingId' => 'required|string',
            'services' => 'required|array|min:1',
            'services.*.problem_sub_category_id' => 'required|uuid',
        ];
    }
}

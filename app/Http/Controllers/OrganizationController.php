<?php

namespace App\Http\Controllers;

use App\Http\Requests\Organization\UpdateRequest;
use App\Http\Resources\Organization\OrganizationResource;
use App\Http\Resources\TemplateResource;
use App\Models\Country;
use App\Models\Organization;
use App\Models\State;
use App\Traits\WorkOrderListFilterTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Ramsey\Uuid\Uuid;

class OrganizationController extends Controller
{
    use WorkOrderListFilterTrait;

    public function show(Request $request): OrganizationResource
    {
        /** @var Organization $organization */
        $organization = $request->user()?->organization;

        return new OrganizationResource($organization);
    }

    public function update(UpdateRequest $request): OrganizationResource
    {
        /** @var Organization $organization */
        $organization = $request->user()?->organization;

        $payload = [];
        if ($request->input('name')) {
            $payload['name'] = $request->input('name');
        }
        if ($request->input('street_address')) {
            $payload['street_address'] = $request->input('street_address');
        }
        if ($request->input('city')) {
            $payload['city'] = $request->input('city');
        }
        if ($request->input('zip_code')) {
            $payload['zip_code'] = $request->input('zip_code');
        }
        if ($request->input('state')) {
            $payload['state_id'] = State::when(Uuid::isValid($request->input('state')), function ($query) use ($request) {
                $query->whereUuid($request->input('state'));
            })
                ->orWhere('state_code', $request->input('state'))
                ->firstOrFail()->state_id ?? null;
        }

        $payload['country_id'] = Country::where('alpha2_code', 'US')->firstOrFail()->country_id ?? null;
        if ($request->hasFile('logo')) {
            $path = $organization->getMediaPathPrefix();

            /** @var UploadedFile $file */
            $file = $request->file('logo');

            $extension = $file->getClientOriginalExtension() ?? $file->guessExtension();

            $fileName = "logo.{$extension}";

            $manager = ImageManager::imagick();
            $image = $manager->read($file->getContent());
            $image->scale(width: config('settings.image.logo.width'), height: config('settings.image.logo.height'));

            $uploaded = Storage::put($path . "/{$fileName}", $image->encodeByExtension($extension));

            if ($uploaded) {
                $payload['logo_file_name'] = $fileName;
            }
        }

        if ($request->input('phone_number')) {
            $payload['phone_number'] = $request->input('phone_number');
        }

        $organization->update($payload);

        if (! empty($payload['logo_file_name'])) {
            Cache::forget("{$organization->organization_uuid}_logo");
        }

        return new OrganizationResource($organization);

    }

    public function getTemplates(Request $request): AnonymousResourceCollection
    {
        $this->authorize('templateView', Organization::class);

        return TemplateResource::collection(
            collect([
                'default' => 'Default',
                'green' => 'Green',
                'purple' => 'Purple',
                'teal' => 'Teal',
                'dark' => 'Dark',
            ])->map(fn ($label, $value) => [
                'label' => $label,
                'value' => $value,
            ])->values()
        );
    }

    /**
     * @return OrganizationResource
     */
    public function updateTemplate(Request $request)
    {
        $this->authorize('templateUpdate', Organization::class);

        $request->validate([
            'template' => 'required|in:default,green,purple,teal,dark',
        ]);

        $request->user()?->organization?->update([
            'template' => $request->template,
        ]);

        return new OrganizationResource($request->user()?->organization);
    }
}

<?php

namespace App\Http\Controllers;

use App\Actions\ServiceRequest\AddResidentAvailability;
use App\Exceptions\ResidentAvailabilityException;
use App\Helpers\Helper;
use App\Http\Requests\ServiceRequest\ResidentAvailabilityRequest;
use App\Http\Resources\ResidentAvailability\ResidentAvailabilityResource;
use App\Http\Resources\ResidentAvailability\ResidentAvailabilityShowResource as PublicPageResidentAvailabilityDisplayResource;
use App\Models\ServiceRequest;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Response;

class ResidentAvailabilityController extends Controller
{
    public function store(ResidentAvailabilityRequest $request, string $serviceRequestPublicId): ResidentAvailabilityResource|JsonResponse
    {
        try {
            DB::beginTransaction();
            $serviceRequest = ServiceRequest::with([
                'residentAvailabilities:resident_availability_id,resident_availability_uuid,service_request_id,user_id,timing,availability_date,day_passed,created_at',
                'timezone:timezone_id,name',
                'property:property_id,property_uuid,property_name,full_address,latitude,longitude,state_id,street_address,city,unit_number,postal_zip_code',
                'property.state:state_id,state_uuid,name,state_code',
                'organization:organization_id,organization_uuid,domain,logo_file_name',
            ])
                ->select(
                    'service_request_id', 'timezone_id', 'service_request_uuid', 'state', 'organization_id', 'property_id',
                    'service_request_status_id', 'service_request_reference_number', 'service_request_number'
                )
                ->whereUuid($serviceRequestPublicId)
                ->first();

            if (empty($serviceRequest)) {
                throw ResidentAvailabilityException::invalidServiceRequest();
            }

            if ($serviceRequest->residentAvailabilities->whereNull('user_id')->isNotEmpty()) {
                $serviceRequest->residentAvailabilities->whereNull('user_id')->each(function ($availability) {
                    $availability->delete();
                });
            }

            $payload = $request->all();
            $adminAddedAvailability = $serviceRequest->residentAvailabilities->whereNotNull('user_id');

            foreach ($payload['resident_availability'] as $availability) {

                $date = CarbonImmutable::parse($availability['date']);

                // Check the availability date already exists
                $availabilityDate = $adminAddedAvailability->where('availability_date', $date);
                $availabilityTimings = [];

                // Find selected day current timings stored in our system
                if ($availabilityDate->isNotEmpty()) {

                    $availabilityTimings = $availabilityDate->pluck('timing')->toArray();
                    foreach ($availability['timings'] as $timing) {
                        if (($key = array_search($timing, $availabilityTimings)) !== false) {
                            unset($availabilityTimings[$key]);
                        }
                    }

                    if (count($availabilityTimings)) {
                        $availabilityDate->whereIn('timing', $availabilityTimings)->each->delete();
                    }
                }
            }

            $serviceRequest = AddResidentAvailability::run($serviceRequest, $payload, null);
            $serviceRequest->availability_requested_at = null;
            $serviceRequest->save();

            DB::commit();

            return new ResidentAvailabilityResource($serviceRequest);

        } catch (ResidentAvailabilityException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Resident Availability store API failed[ResidentAvailabilityException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            DB::rollBack();

            Helper::exceptionLog(exception: $exception, message: 'Resident Availability store API failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Show resident availabilities
     */
    public function show(Request $request, string $serviceRequestPublicId): PublicPageResidentAvailabilityDisplayResource|JsonResponse
    {
        try {
            $serviceRequest = ServiceRequest::with([
                'residentAvailabilities:resident_availability_id,resident_availability_uuid,service_request_id,user_id,timing,availability_date,day_passed,created_at',
                'timezone:timezone_id,name',
                'property:property_id,property_uuid,property_name,full_address,latitude,longitude,state_id,street_address,city,unit_number,postal_zip_code',
                'property.state:state_id,state_uuid,name,state_code',
                'organization:organization_id,organization_uuid,domain,logo_file_name',
            ])
                ->select(
                    'service_request_id', 'timezone_id', 'service_request_uuid', 'state', 'organization_id', 'property_id',
                    'service_request_status_id', 'service_request_reference_number', 'service_request_number'
                )
                ->whereUuid($serviceRequestPublicId)
                ->first();

            if (empty($serviceRequest)) {
                throw ResidentAvailabilityException::invalidServiceRequest();
            }

            return new PublicPageResidentAvailabilityDisplayResource($serviceRequest);

        } catch (ResidentAvailabilityException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Resident Availability show API failed[ResidentAvailabilityException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Resident Availability show API failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }
}

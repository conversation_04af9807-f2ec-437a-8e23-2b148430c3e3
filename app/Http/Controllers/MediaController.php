<?php

namespace App\Http\Controllers;

use App\Helpers\Helper;
use App\Http\Middleware\MakeTusMediaFileName;
use App\Models\Organization;
use App\Models\WorkOrder;
use App\Packages\OrganizationRolePermission\Exceptions\OrganizationNotFound;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Symfony\Component\HttpFoundation\Response as HttpResponse;

class MediaController extends Controller
{
    public function __invoke(Request $request): HttpResponse
    {
        //TODO: Image upload temporary validation fix
        $request->merge([
            'media_headers' => [
                'work_order_id' => $request->header('Work-Order-Id'),
                'media_id' => $request->header('Media-Id'),
            ],
        ]);

        $request->validate([
            'media_headers.work_order_id' => 'required',
            'media_headers.media_id' => 'required',
        ]);

        try {
            /** @var Organization $organization */
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFound;
            }

            $mediaServer = app('media-server');
            $mediaServer->middleware()->add(MakeTusMediaFileName::class);

            $pathDir = config('media.upload.dir') . '/' . $organization->getMediaPathPrefix();

            if ($request->header('Work-Order-Id')) {
                $workOrder = WorkOrder::whereUuid($request->header('Work-Order-Id'))->firstOrFail();
                $pathDir .= "/work-orders/{$workOrder->work_order_uuid}";
            }

            if ($request->header('Media-Id')) {
                $mediaServer->setUploadKey($request->header('Media-Id'));
            }

            $mediaServer->setUploadDir($pathDir);

            return $mediaServer->serve();
        } catch (OrganizationNotFound $exception) {
            Helper::exceptionLog($exception, message: __('Media upload error[OrganizationNotFound]'));

            return Response::unprocessableEntity(message: __($exception->getMessage()));
        } catch (ModelNotFoundException $exception) {
            Helper::exceptionLog($exception, message: __('Media upload error[ModelNotFoundException]'));

            return Response::notFound();
        } catch (Exception $exception) {
            Helper::exceptionLog($exception, message: __('Media upload error[Exception]'), notify: true);

            return Response::internalServerError(message: $exception->getMessage());
        }
    }
}

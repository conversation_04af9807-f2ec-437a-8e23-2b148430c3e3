<?php

namespace App\Http\Controllers\Developer;

use App\DataTables\DeveloperAlertLogsDataTable;
use App\DataTables\PublicApiWorkOrderWebhookEventsDataTable;
use App\DataTables\RequestLogsDataTable;
use App\DataTables\VendorPublicApiLogDataTable;
use App\DataTables\WebhookCallsDataTable;
use App\Http\Controllers\Controller;
use App\Models\DeveloperAlert;
use App\Models\Organization;
use App\Models\PublicApiWorkOrderWebhookEvent;
use App\Models\RequestLog;
use App\Models\VendorPublicApiLog;
use App\Models\WebhookCall;
use Illuminate\Contracts\View\View;

class LogViewerController extends Controller
{
    /*
    * List request logs.
    */
    public function requestLogs(RequestLogsDataTable $dataTable): mixed
    {
        $organizations = Organization::select('name', 'organization_uuid')->get()->pluck('name', 'organization_uuid');

        return $dataTable->render('developer.logs.requests.index', ['organizations' => $organizations]);
    }

    /**
     * Show request log detailed view.
     */
    public function requestLogShow(RequestLog $requestLog): View
    {
        return view('developer.logs.requests.show', ['requestLog' => $requestLog]);
    }

    /**
     * List request logs.
     */
    public function incomingWebhookLogs(WebhookCallsDataTable $dataTable): mixed
    {
        return $dataTable->render('developer.logs.webhooks.incoming.index');
    }

    /**
     * Show request log detailed view.
     */
    public function incomingWebhookLogShow(WebhookCall $webhookCall): View
    {
        return view('developer.logs.webhooks.incoming.show', ['webhookCall' => $webhookCall]);
    }

    /**
     * List public api workorder webhook events logs.
     */
    public function outgoingWebhookLogs(PublicApiWorkOrderWebhookEventsDataTable $dataTable): mixed
    {
        return $dataTable->render('developer.logs.webhooks.outgoing.index');
    }

    /**
     * Show public api workorder webhook event detailed view.
     */
    public function outgoingWebhookLogShow(string $publicApiWoWebhookEventUuid): View
    {
        $publicApiWorkOrderWebhookEvent = PublicApiWorkOrderWebhookEvent::whereUuid($publicApiWoWebhookEventUuid)->first();

        return view('developer.logs.webhooks.outgoing.show', ['publicApiWorkOrderWebhookEvent' => $publicApiWorkOrderWebhookEvent]);
    }

    /**
     * List public api workorder webhook events logs.
     */
    public function developerAlerts(DeveloperAlertLogsDataTable $dataTable): mixed
    {
        $organizations = Organization::select('name', 'organization_uuid')->get()->pluck('name', 'organization_uuid');

        return $dataTable->render('developer.logs.alerts.index', ['organizations' => $organizations]);
    }

    /**
     * Show public api workorder webhook event detailed view.
     */
    public function developerAlertShow(string $publicApiWoWebhookEventId): View
    {
        $developerAlert = DeveloperAlert::findOrFail($publicApiWoWebhookEventId);

        return view('developer.logs.alerts.show', ['developerAlert' => $developerAlert]);
    }

    public function vendorPublicApiLogs(VendorPublicApiLogDataTable $vendorPublicApiLogDataTable): mixed
    {
        return $vendorPublicApiLogDataTable->render('developer.logs.vendor_public_api_logs.index');
    }

    public function vendorPublicApiLogShow(VendorPublicApiLog $vendorPublicApiLog): View
    {
        return view('developer.logs.vendor_public_api_logs.show', ['vendorPublicApiLog' => $vendorPublicApiLog]);
    }
}

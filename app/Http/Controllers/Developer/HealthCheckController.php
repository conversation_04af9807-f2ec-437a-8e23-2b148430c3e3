<?php

namespace App\Http\Controllers\Developer;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;
use Throwable;

class HealthCheckController extends Controller
{
    private const STATUS_SUCCESS_MESSAGE = 'success';
    private const STATUS_DATABASE_FAILURE = 'database failed to connect';
    private const STATUS_CACHE_FAILURE = 'cache driver [%s] failed to connect';
    private const STATUS_LOGGING_FAILURE = 'logging failed';

    /**
     * Performs a basic check to verify the application's operational status.
     *
     * @return JsonResponse Returns a JSON response indicating a successful status.
     */
    public function basicCheck(): JsonResponse
    {
        return response()->json('success');
    }

    /**
     * Checks the application's utilities including database connection,
     * cache connection, and logging system.
     *
     * @return JsonResponse Returns a JSON response with the status message
     *                      and HTTP status code based on the results of the checks.
     */
    public function utilitiesCheck(): JsonResponse
    {
        $statusMessage = self::STATUS_SUCCESS_MESSAGE;
        $statusCode = ResponseAlias::HTTP_OK;

        if (! $this->checkDatabaseConnection()) {
            $statusMessage = self::STATUS_DATABASE_FAILURE;
            $statusCode = ResponseAlias::HTTP_SERVICE_UNAVAILABLE;
        } elseif (! $this->checkCacheConnection()) {
            $driver = config('cache.default');
            $statusMessage = sprintf(self::STATUS_CACHE_FAILURE, $driver);
            $statusCode = ResponseAlias::HTTP_SERVICE_UNAVAILABLE;
        } elseif (! $this->checkLogging()) {
            $statusMessage = self::STATUS_LOGGING_FAILURE;
            $statusCode = ResponseAlias::HTTP_SERVICE_UNAVAILABLE;
        }

        return response()->json($statusMessage, $statusCode);
    }

    /**
     * Verifies the application's connection to the database.
     *
     * Attempts to establish a connection to the database and logs any
     * exceptions encountered during the process.
     *
     * @return bool Returns true if the database connection is successful;
     *              otherwise, returns false in case of an exception.
     */
    private function checkDatabaseConnection(): bool
    {
        try {
            DB::connection()->getPdo();
            if (! app()->runningUnitTests()) {
                echo 'DB Connected successfully' . PHP_EOL;
            }

            return true;
        } catch (Throwable $exception) {
            Helper::exceptionLog($exception);

            return false;
        }
    }

    /**
     * Checks the connection to the cache system and verifies if the cache driver is properly configured.
     *
     * @return bool Returns true if the cache connection is established successfully, otherwise false.
     */
    private function checkCacheConnection(): bool
    {
        try {
            Cache::remember('test', now()->addSecond(), fn () => 'test');
            $driver = config('cache.default');
            if (! app()->runningUnitTests()) {
                echo "Cache [{$driver}] Connected successfully" . PHP_EOL;
            }

            return true;
        } catch (Throwable $exception) {
            Helper::exceptionLog($exception);

            return false;
        }
    }

    /**
     * Checks if the logging functionality is operational.
     *
     * @return bool Returns true if logging succeeds, otherwise false.
     */
    private function checkLogging(): bool
    {
        try {
            info('test logging');
            if (! app()->runningUnitTests()) {
                echo 'Logging success' . PHP_EOL;
            }

            return true;
        } catch (Throwable $exception) {
            Helper::exceptionLog($exception);

            return false;
        }
    }
}

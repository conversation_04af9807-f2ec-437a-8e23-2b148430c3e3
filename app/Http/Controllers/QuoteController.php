<?php

namespace App\Http\Controllers;

use App\Enums\DateRanges;
use App\Enums\QuoteStatus;
use App\Exceptions\GroupingExceptions\QuoteGroupingsException;
use App\Exceptions\NotFoundException\OrganizationNotFoundException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Requests\Quote\ListRequest;
use App\Http\Requests\WorkOrder\FilterValuesRequest;
use App\Http\Requests\WorkOrder\GroupViewRequest;
use App\Http\Resources\Quote\Group\AssigneeBasedGroupDataResource;
use App\Http\Resources\Quote\Group\CategoryBasedGroupDataResource;
use App\Http\Resources\Quote\Group\StatusBasedGroupDataResource;
use App\Http\Resources\Quote\Group\SubmittedBasedGroupDataResource;
use App\Http\Resources\Quote\Group\WorkOrderNumberBasedGroupDataResource;
use App\Http\Resources\Quote\ListResource;
use App\Http\Resources\Quote\ListStatusResource;
use App\Http\Resources\WorkOrder\Filter\AssigneeFilterResource;
use App\Http\Resources\WorkOrder\Filter\CategoryFilterResource;
use App\Http\Resources\WorkOrder\Filter\OverdueFilterResource;
use App\Http\Resources\WorkOrder\Filter\TagFilterResource;
use App\Http\Resources\WorkOrder\Filter\TechnicianFilterResource;
use App\Http\Resources\WorkOrder\Group\TagBasedGroupDataResource;
use App\Models\ProblemCategory;
use App\Models\Quote;
use App\Models\QuoteTask;
use App\Models\Tag;
use App\Models\User;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use InvalidArgumentException;
use LogicException;
use UnexpectedValueException;

class QuoteController extends Controller
{
    /**
     * QuoteController constructor
     */
    public function __construct()
    {
        // No dependencies needed - using traits through models
    }

    /**
     * Display Listing of quote resource
     */
    public function index(ListRequest $request): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('viewAny', Quote::class);

        $user = $request->user();

        try {

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $organizationId = $user->organization_id;

            $quotes = Quote::with(relations: [
                'workOrder',
                'serviceCall',
                'submittedUser:user_id,user_uuid,first_name,last_name,middle_name',
                'approvedUser',
                'rejectedUser',
                'quoteTasks' => function ($query) {
                    return $query->select(
                        'quote_task_id', 'quote_id', 'quote_task_uuid', 'markup_fee_type', 'markup_fee_type_value', 'markup_fee_in_cents',
                        'cost_in_cents', 'total_cost_in_cents', 'estimated_time', 'description', 'status', 'quote_task_number'
                    )
                        ->with([
                            'quoteTaskMaterials' => function ($query) {
                                return $query->select(
                                    'quote_task_id', 'label', 'markup_fee_type', 'markup_fee_type_value', 'markup_fee_in_cents',
                                    'cost_in_cents', 'total_cost_in_cents', 'quantity', 'quantity_type', 'cost_type', 'quote_task_material_uuid',
                                    'quote_task_material_id'
                                );
                            },
                        ])
                        ->orderBy('created_at');
                },
                'workOrderTask',
                'workOrder.resident:resident_id,resident_uuid,first_name,last_name,phone_number',
                'workOrder.property:property_id,property_uuid,property_name,full_address,latitude,longitude,state_id,street_address,city,unit_number,postal_zip_code',
                'workOrder.property.state:state_id,state_uuid,name,state_code',
                'workOrder.property.residents:resident_id,resident_uuid,property_id,first_name,last_name,phone_number,email',
                'workOrder.assignees:work_order_assignee_id,work_order_assignee_uuid,work_order_id,user_id',
                'workOrder.assignees.user:user_id,user_uuid,first_name,last_name,middle_name,profile_pic',
                'workOrder.timezone:timezone_id,name',
                'workOrder.tasks:work_order_task_id,work_order_task_uuid,work_order_id,problem_diagnosis_id',
                'workOrder.tasks.problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label',
                'workOrder.tasks.problemDiagnosis.subCategory:problem_sub_category_id,problem_sub_category_uuid,problem_category_id,label',
                'workOrder.tasks.problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
                'workOrder.tags' => function ($query) {
                    $query->select('tags.tag_id', 'tag_uuid', 'name', 'slug', 'color', 'text_color', 'type')
                        ->withCount('workOrders');
                },
            ])
                ->joinSub(
                    QuoteTask::selectRaw(
                        'quote_tasks.quote_id,
                                     CAST(SUM(quote_tasks.total_cost_in_cents) AS SIGNED) / 100 AS total_cost,
                                     CAST(SUM(CASE WHEN quote_tasks.status = ? THEN quote_tasks.total_cost_in_cents ELSE 0 END) AS SIGNED) / 100 AS total_approved_cost',
                        [QuoteStatus::APPROVED()]
                    )
                        ->where('quote_tasks.organization_id', $organizationId)
                        ->whereNull('quote_tasks.deleted_at')
                        ->groupBy('quote_tasks.quote_id'),
                    'qlm',
                    function ($join) {
                        $join->on('quotes.quote_id', '=', 'qlm.quote_id');
                    }
                )
                ->join('quote_statuses', 'quote_statuses.slug', 'quotes.status')
                ->leftjoinSub(
                    QuoteTask::selectRaw('quote_tasks.quote_id,
                    (SUM(quote_task_materials.total_cost_in_cents) / 100) as total_material_cost,
                    (SUM(CASE WHEN quote_tasks.status = ? THEN quote_task_materials.total_cost_in_cents ELSE 0 END) / 100) as total_material_approved_cost', [QuoteStatus::APPROVED()])
                        ->join('quote_task_materials', 'quote_tasks.quote_task_id', '=', 'quote_task_materials.quote_task_id')
                        ->where('quote_tasks.organization_id', $organizationId)
                        ->whereNull('quote_task_materials.deleted_at')
                        ->groupBy('quote_tasks.quote_id'),
                    'qln',
                    function ($join) {
                        $join->on('quotes.quote_id', '=', 'qln.quote_id');
                    }
                )
                ->leftjoin('work_order_tasks', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('work_order_tasks.work_order_id', 'quotes.work_order_id')
                        ->where('work_order_tasks.organization_id', $organizationId);
                })
                ->leftjoin('work_orders', 'work_orders.work_order_id', 'quotes.work_order_id')
                ->join('properties', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('work_orders.property_id', 'properties.property_id')
                        ->where('properties.organization_id', $organizationId);
                })
                ->leftjoin('residents', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('work_orders.requesting_resident_id', 'residents.resident_id')
                        ->where('residents.organization_id', $organizationId);
                })
                ->leftjoin('problem_diagnoses', 'problem_diagnoses.problem_diagnosis_id', 'work_order_tasks.problem_diagnosis_id')
                ->leftjoin('problem_sub_categories', 'problem_diagnoses.problem_sub_category_id', 'problem_sub_categories.problem_sub_category_id')
                ->leftjoin('problem_categories', 'problem_categories.problem_category_id', 'problem_sub_categories.problem_category_id')
                ->when(! empty($request->group) && $request->group === 'assignee', function (Builder $query) use ($organizationId) {
                    return $query->join('work_order_assignees', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_assignees.work_order_id', 'quotes.work_order_id')
                            ->whereNull('work_order_assignees.deleted_at')
                            ->join('users', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('users.user_id', '=', 'work_order_assignees.user_id')
                                    ->where('users.organization_id', '=', $organizationId);
                            });
                    });
                }, function ($query) use ($organizationId) {
                    return $query->leftjoin('work_order_assignees', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_assignees.work_order_id', 'quotes.work_order_id')
                            ->whereNull('work_order_assignees.deleted_at')
                            ->join('users', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('users.user_id', '=', 'work_order_assignees.user_id')
                                    ->where('users.organization_id', '=', $organizationId);
                            });
                    });
                })
                ->when(! empty($request->group) && $request->group === 'tag', function (Builder $query) use ($organizationId) {
                    return $query->join('work_order_tags', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tags.work_order_id', 'quotes.work_order_id')
                            ->join('tags', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('tags.tag_id', '=', 'work_order_tags.tag_id')
                                    ->where('tags.organization_id', '=', $organizationId)
                                    ->whereNull('tags.deleted_at');
                            });
                    });
                }, function ($query) use ($organizationId) {
                    return $query->leftJoin('work_order_tags', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tags.work_order_id', 'quotes.work_order_id')
                            ->join('tags', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('tags.tag_id', '=', 'work_order_tags.tag_id')
                                    ->where('tags.organization_id', '=', $organizationId)
                                    ->whereNull('tags.deleted_at');
                            });
                    });
                })
                // Apply filtering and searching
                ->process($request->all(), 'quote')
                // Apply sorting
                ->applySorting(
                    Quote::prepareSortValue(
                        $request->sort ?? null,
                        $request->group ?? null,
                        'quote'
                    ),
                    'quote'
                )
                ->groupBy('quotes.quote_id')
                ->select(
                    'quotes.quote_id',
                    'quotes.quote_uuid',
                    'quotes.quote_number',
                    'quotes.work_order_id',
                    'quotes.submitted_user_id',
                    DB::raw('CAST(quotes.status as char) as status'),
                    'quotes.submitted_at',
                    'quotes.rejected_at',
                    'quotes.approved_at',
                    'quotes.rejected_user_id',
                    'quotes.work_order_task_id',
                    'quotes.approved_user_id',
                    'quotes.work_order_service_call_id',
                    'work_orders.priority',
                    'work_orders.due_date',
                    'work_orders.work_order_number',
                    'quote_statuses.sort_order',
                    DB::raw('CAST(COALESCE(CAST(qlm.total_cost AS DOUBLE), 0) + (COALESCE(CAST(SUM(qln.total_material_cost) AS DOUBLE), 0) / COUNT(quotes.quote_uuid)) AS DOUBLE) AS total_cost'),
                    DB::raw('CAST(COALESCE(CAST(qlm.total_approved_cost AS DOUBLE), 0) + (COALESCE(CAST(SUM(qln.total_material_approved_cost) AS DOUBLE), 0) / COUNT(quotes.quote_uuid)) AS DOUBLE) AS approved_total'),
                    DB::raw(
                        "CASE WHEN GROUP_CONCAT(DISTINCT problem_categories.label ORDER BY problem_categories.label ASC SEPARATOR '-') IS NULL THEN 'Unknown'
                    ELSE GROUP_CONCAT(DISTINCT problem_categories.label ORDER BY problem_categories.label ASC SEPARATOR '-')
                    END AS problem_categories_label"
                    ),
                    DB::raw("GROUP_CONCAT(DISTINCT users.first_name ORDER BY users.first_name ASC SEPARATOR '') AS assignee_name_for_sort"),
                    DB::raw("CONCAT('assignee_ids_',GROUP_CONCAT(DISTINCT work_order_assignees.user_id SEPARATOR '_')) AS assignee_ids_for_group"),
                    DB::raw("CONCAT('tag_ids_',GROUP_CONCAT(DISTINCT tags.tag_id SEPARATOR '_')) AS tag_ids_for_group"),
                )

                ->paginate($request->input('per_page', config('pagination.quote_list.per_page')));

            return ListResource::collection($quotes);
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Quote listing api Failed[UnexpectedValueException]');

            return Response::unprocessableEntity(errors: [$exception->getMessage()], message: __('The server was unable to process the request because unexpected behavior occurred.'));
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Quote listing api Failed[InvalidArgumentException]');

            return Response::badRequest(errors: [$exception->getMessage()], message: __('The server was unable to process the request because it contains invalid data.'));
        } catch (LogicException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Quote listing api Failed[LogicException]');

            return Response::unprocessableEntity(errors: [$exception->getMessage()], message: __('Something went wrong, The server was unable to process'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Quote listing api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __($exception->getMessage()));
        }
    }

    public function getFilterValues(FilterValuesRequest $request): JsonResource|JsonResponse
    {
        try {
            $this->authorize('viewAny', Quote::class);

            $filterType = $request->get('type');
            $organizationId = request()->user()?->organization_id;

            if (! $organizationId) {
                throw new OrganizationNotFoundException;
            }

            switch ($filterType) {
                case 'category':
                    $filterData = ProblemCategory::select('problem_category_uuid', 'label')
                        ->orderBy('label')
                        ->get();

                    // Assuming $category is an instance of ProblemCategory
                    $category = new ProblemCategory;
                    $category->label = 'Unknown';

                    // Append the new instance to the $filterData array
                    $filterData[] = $category;

                    return CategoryFilterResource::collection($filterData);
                case 'status':
                    $filterData = collect(QuoteStatus::cases())->map(fn ($status) => [
                        'value' => $status->value,
                        'label' => QuoteStatus::label($status->value, 'web'),
                    ])->all();

                    return ListStatusResource::collection($filterData);
                case 'submitted_by':
                    $technicians = User::where('organization_id', $organizationId)
                        ->select('user_uuid', 'first_name', 'last_name')
                        ->orderBy('first_name')
                        ->get();

                    $filterData = $technicians->map(function ($technician) {
                        return (object) [
                            'label' => $technician->first_name . ' ' . $technician->last_name,
                            'value' => $technician->user_uuid,
                        ];
                    })->sortBy('label')->values();

                    return TechnicianFilterResource::collection($filterData);
                case 'assignee':
                    $filterData = User::whereIn('user_type', ['account_user'])
                        ->where('organization_id', $organizationId)
                        ->select('user_uuid', 'first_name', 'last_name')
                        ->orderBy('first_name')
                        ->get();

                    return AssigneeFilterResource::collection($filterData);
                case 'due_date':
                    $filterData = DateRanges::makeCollection();

                    return OverdueFilterResource::collection($filterData);
                case 'submitted_date':
                    $filterData = DateRanges::makeCollection(except: DateRanges::OVERDUE());
                    $excludeValues = array_merge([DateRanges::OVERDUE()], DateRanges::excludeFutureDates()->toArray());
                    $filterData = DateRanges::makeCollection(except: $excludeValues);

                    return OverdueFilterResource::collection($filterData);
                case 'tag':
                    $filterData = Tag::select('tag_uuid', 'name')
                        ->where('organization_id', $organizationId)
                        ->orderBy('name')
                        ->get();

                    return TagFilterResource::collection($filterData);
                default:
                    throw new UnexpectedValueException(__("Invalid filter data request, {$filterType} filter not specified."));
            }
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get Filter Values api failed[UnexpectedValueException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get Filter Values api failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Get group view details.
     */
    public function getGroupView(GroupViewRequest $request): JsonResource|JsonResponse
    {
        try {
            $this->authorize('viewAny', Quote::class);

            $group = $request->get('group');
            $organizationId = request()->user()?->organization_id;

            if (! $organizationId) {
                throw new OrganizationNotFoundException;
            }

            switch ($group) {
                case 'work_order_number':
                    $groupData = $this->workOrderNumberBasedGroupData($organizationId, $request);

                    return WorkOrderNumberBasedGroupDataResource::collection($groupData);
                case 'status':
                    $groupData = $this->statusBasedGroupData($organizationId, $request);

                    return StatusBasedGroupDataResource::collection($groupData);
                case 'category':
                    $groupData = $this->categoryBasedGroupData($organizationId, $request);

                    return CategoryBasedGroupDataResource::collection($groupData);
                case 'assignee':
                    $groupData = $this->assigneeBasedGroupData($organizationId, $request);

                    return AssigneeBasedGroupDataResource::collection($groupData);
                case 'submitted_by':
                    $groupData = $this->submittedBasedGroupData($organizationId, $request);

                    return SubmittedBasedGroupDataResource::collection($groupData);
                case 'tag':
                    $groupData = $this->tagBasedGroupData($organizationId, $request);

                    return TagBasedGroupDataResource::collection($groupData);
                default:
                    throw new UnexpectedValueException(__("Invalid group [{$group}] provided."));
            }
        } catch (QuoteGroupingsException $exception) {
            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get Quote view api Failed[UnexpectedValueException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (AuthorizationException|OrganizationNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get Quote view api Failed due to ' . get_class($exception));

            return Response::unauthorized(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get Quote view api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * @return Collection<int, Quote>
     */
    public function workOrderNumberBasedGroupData(int $organizationId, Request $request): Collection
    {
        return Quote::with(['workOrder'])
            ->leftjoin('work_orders', 'work_orders.work_order_id', 'quotes.work_order_id')
            ->when(! empty($request->search) || ! empty($request->filter), function (Builder $query) use ($organizationId) {
                $query->join('properties', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('work_orders.property_id', 'properties.property_id')
                        ->where('properties.organization_id', $organizationId);
                })
                    ->leftjoin('work_order_tasks', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tasks.work_order_id', 'work_orders.work_order_id')
                            ->where('work_order_tasks.organization_id', $organizationId);
                    })
                    ->leftjoin('problem_diagnoses', 'problem_diagnoses.problem_diagnosis_id', 'work_order_tasks.problem_diagnosis_id')
                    ->leftjoin('problem_sub_categories', 'problem_diagnoses.problem_sub_category_id', 'problem_sub_categories.problem_sub_category_id')
                    ->leftjoin('problem_categories', 'problem_categories.problem_category_id', 'problem_sub_categories.problem_category_id')
                    ->leftjoin('residents', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_orders.requesting_resident_id', 'residents.resident_id')
                            ->where('residents.organization_id', $organizationId);
                    })
                    ->leftJoin('work_order_tags', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tags.work_order_id', 'work_orders.work_order_id')
                            ->join('tags', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('tags.tag_id', 'work_order_tags.tag_id')
                                    ->where('tags.organization_id', $organizationId)
                                    ->whereNull('tags.deleted_at');
                            });
                    })
                    ->leftjoin('work_order_assignees', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_assignees.work_order_id', 'quotes.work_order_id')
                            ->whereNull('work_order_assignees.deleted_at')
                            ->join('users', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('users.user_id', 'work_order_assignees.user_id')
                                    ->where('users.organization_id', $organizationId);
                            });
                    });
            })
            // Search query
            ->when(! empty($request->search), function (Builder $query) use ($request) {
                $query->where(function ($subQuery) use ($request) {
                    $subQuery->where('work_orders.work_order_number', 'like', "%{$request->search}%")
                        ->orWhere('work_orders.description', 'like', "%{$request->search}%")
                        ->orWhereRaw("TRIM(CONCAT(residents.first_name , ' ', COALESCE(residents.last_name, ''))) LIKE ?", ["%{$request->search}%"])
                        ->orWhere('properties.full_address', 'like', "%{$request->search}%")
                        ->orWhere('problem_categories.label', 'like', "%{$request->search}%");
                });
            })
            // Filter query
            ->when(! empty($request->filter), function (Builder $filterQuery) use ($request) {
                $this->filterService->filterQuery($request->filter, $filterQuery, 'quote');
            })
            ->select(
                'quotes.work_order_id',
                DB::raw('count(DISTINCT quotes.quote_id) as total'),
            )
            ->orderBy('quotes.work_order_id')
            ->groupBy('quotes.work_order_id')
            ->get();
    }

    /**
     * @return Collection<int, Quote>
     */
    public function statusBasedGroupData(int $organizationId, Request $request): Collection
    {
        return Quote::join('quote_tasks', 'quotes.quote_id', 'quote_tasks.quote_id')
            ->when(! empty($request->search) || ! empty($request->filter), function (Builder $query) use ($organizationId) {
                $query->leftjoin('work_orders', 'work_orders.work_order_id', 'quotes.work_order_id')
                    ->join('properties', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_orders.property_id', 'properties.property_id')
                            ->where('properties.organization_id', $organizationId);
                    })
                    ->leftjoin('work_order_tasks', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tasks.work_order_id', 'quotes.work_order_id')
                            ->where('work_order_tasks.organization_id', $organizationId);
                    })
                    ->leftjoin('problem_diagnoses', 'problem_diagnoses.problem_diagnosis_id', 'work_order_tasks.problem_diagnosis_id')
                    ->leftjoin('problem_sub_categories', 'problem_diagnoses.problem_sub_category_id', 'problem_sub_categories.problem_sub_category_id')
                    ->leftjoin('problem_categories', 'problem_categories.problem_category_id', 'problem_sub_categories.problem_category_id')
                    ->leftjoin('residents', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_orders.requesting_resident_id', 'residents.resident_id')
                            ->where('residents.organization_id', $organizationId);
                    })
                    ->leftJoin('work_order_tags', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tags.work_order_id', 'work_orders.work_order_id')
                            ->join('tags', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('tags.tag_id', 'work_order_tags.tag_id')
                                    ->where('tags.organization_id', $organizationId)
                                    ->whereNull('tags.deleted_at');
                            });
                    })
                    ->leftJoin('work_order_assignees', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_assignees.work_order_id', 'quotes.work_order_id')
                            ->whereNull('work_order_assignees.deleted_at')
                            ->join('users', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('users.user_id', '=', 'work_order_assignees.user_id')
                                    ->where('users.organization_id', '=', $organizationId);
                            });
                    });
            })
            // Search query
            ->when(! empty($request->search), function (Builder $query) use ($request) {
                $query->where(function ($subQuery) use ($request) {
                    $subQuery->where('work_orders.work_order_number', 'like', "%{$request->search}%")
                        ->orWhere('work_orders.description', 'like', "%{$request->search}%")
                        ->orWhereRaw("TRIM(CONCAT(residents.first_name , ' ', COALESCE(residents.last_name, ''))) LIKE ?", ["%{$request->search}%"])
                        ->orWhere('properties.full_address', 'like', "%{$request->search}%")
                        ->orWhere('problem_categories.label', 'like', "%{$request->search}%");
                });
            })
            // Filter query
            ->when(! empty($request->filter), function (Builder $filterQuery) use ($request) {
                $this->filterService->filterQuery($request->filter, $filterQuery, 'quote');
            })
            ->select(
                'quotes.status',
                DB::raw('count(DISTINCT quotes.quote_id) as total'),
            )
            ->orderBy('quotes.status')
            ->groupBy('quotes.status')
            ->get();
    }

    /**
     * @return array<int,mixed>
     */
    public function categoryBasedGroupData(int $organizationId, Request $request): array
    {
        $quoteCategories = Quote::with(['workOrder'])
            ->leftjoin('work_orders', 'work_orders.work_order_id', 'quotes.work_order_id')
            ->leftjoin('work_order_tasks', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('work_order_tasks.work_order_id', 'quotes.work_order_id')
                    ->where('work_order_tasks.organization_id', $organizationId);
            })
            ->leftjoin('problem_diagnoses', 'problem_diagnoses.problem_diagnosis_id', 'work_order_tasks.problem_diagnosis_id')
            ->leftjoin('problem_sub_categories', 'problem_diagnoses.problem_sub_category_id', 'problem_sub_categories.problem_sub_category_id')
            ->leftjoin('problem_categories', 'problem_categories.problem_category_id', 'problem_sub_categories.problem_category_id')
            ->when(! empty($request->search) || ! empty($request->filter), function (Builder $query) use ($organizationId) {
                $query->join('properties', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('work_orders.property_id', 'properties.property_id')
                        ->where('properties.organization_id', $organizationId);
                })
                    ->leftjoin('residents', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_orders.requesting_resident_id', 'residents.resident_id')
                            ->where('residents.organization_id', $organizationId);
                    })
                    ->leftJoin('work_order_tags', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tags.work_order_id', 'work_orders.work_order_id')
                            ->join('tags', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('tags.tag_id', 'work_order_tags.tag_id')
                                    ->where('tags.organization_id', $organizationId)
                                    ->whereNull('tags.deleted_at');
                            });
                    })
                    ->leftJoin('work_order_assignees', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_assignees.work_order_id', 'quotes.work_order_id')
                            ->whereNull('work_order_assignees.deleted_at')
                            ->join('users', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('users.user_id', '=', 'work_order_assignees.user_id')
                                    ->where('users.organization_id', '=', $organizationId);
                            });
                    });
            })
            // Search query
            ->when(! empty($request->search), function (Builder $query) use ($request) {
                $query->where(function (Builder $subQuery) use ($request) {
                    $subQuery->where('work_orders.work_order_number', 'like', "%{$request->search}%")
                        ->orWhere('work_orders.description', 'like', "%{$request->search}%")
                        ->orWhereRaw("TRIM(CONCAT(residents.first_name , ' ', COALESCE(residents.last_name, ''))) LIKE ?", ["%{$request->search}%"])
                        ->orWhere('properties.full_address', 'like', "%{$request->search}%")
                        ->orWhere('problem_categories.label', 'like', "%{$request->search}%");
                });
            })
            // Filter query
            ->when(! empty($request->filter), function (Builder $filterQuery) use ($request) {
                $this->filterService->filterQuery($request->filter, $filterQuery, 'quote');
            })
            ->select(
                DB::raw("
                    CASE
                        WHEN GROUP_CONCAT(DISTINCT problem_categories.label ORDER BY problem_categories.label ASC SEPARATOR '-') IS NULL THEN 'Unknown'
                        ELSE GROUP_CONCAT(DISTINCT problem_categories.label ORDER BY problem_categories.label ASC SEPARATOR '-')
                    END AS problem_categories_label"),
            )
            ->groupBy('quotes.quote_id')
            ->get()
            ->groupBy('problem_categories_label');

        $formattedData = [];

        foreach ($quoteCategories as $problemCategory => $quote) {
            $formattedData[] = [
                'label' => Str::replace('-', ', ', (string) $problemCategory),
                'total' => count($quote),
                'slug' => Str::slug($problemCategory),
            ];
        }

        return $formattedData;
    }

    /**
     * @return array<int, mixed>
     */
    public function assigneeBasedGroupData(int $organizationId, Request $request): array
    {
        $quotes = Quote::with(['workOrder'])
            ->leftjoin('work_orders', 'work_orders.work_order_id', 'quotes.work_order_id')
            ->join('work_order_assignees', function ($joinQuery) {
                $joinQuery->on('work_orders.work_order_id', 'work_order_assignees.work_order_id')
                    ->whereNull('work_order_assignees.deleted_at');
            })
            ->join('users', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('work_order_assignees.user_id', 'users.user_id')
                    ->where('users.organization_id', $organizationId);
            })
            ->when(! empty($request->search) || ! empty($request->filter), function (Builder $query) use ($organizationId) {
                $query->join('properties', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('work_orders.property_id', 'properties.property_id')
                        ->where('properties.organization_id', $organizationId);
                })
                    ->leftjoin('work_order_tasks', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tasks.work_order_id', 'quotes.work_order_id')
                            ->where('work_order_tasks.organization_id', $organizationId);
                    })
                    ->leftjoin('problem_diagnoses', 'problem_diagnoses.problem_diagnosis_id', 'work_order_tasks.problem_diagnosis_id')
                    ->leftjoin('problem_sub_categories', 'problem_diagnoses.problem_sub_category_id', 'problem_sub_categories.problem_sub_category_id')
                    ->leftjoin('problem_categories', 'problem_categories.problem_category_id', 'problem_sub_categories.problem_category_id')
                    ->leftjoin('residents', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_orders.requesting_resident_id', 'residents.resident_id')
                            ->where('residents.organization_id', $organizationId);
                    })
                    ->leftJoin('work_order_tags', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tags.work_order_id', 'work_orders.work_order_id')
                            ->join('tags', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('tags.tag_id', 'work_order_tags.tag_id')
                                    ->where('tags.organization_id', $organizationId)
                                    ->whereNull('tags.deleted_at');
                            });
                    });
            })
            // Search query
            ->when(! empty($request->search), function (Builder $query) use ($request) {
                $query->where(function (Builder $subQuery) use ($request) {
                    $subQuery->where('work_orders.work_order_number', 'like', "%{$request->search}%")
                        ->orWhere('work_orders.description', 'like', "%{$request->search}%")
                        ->orWhereRaw("TRIM(CONCAT(residents.first_name , ' ', COALESCE(residents.last_name, ''))) LIKE ?", ["%{$request->search}%"])
                        ->orWhere('properties.full_address', 'like', "%{$request->search}%")
                        ->orWhere('problem_categories.label', 'like', "%{$request->search}%");
                });
            })
            // Filter query
            ->when(! empty($request->filter), function (Builder $filterQuery) use ($request) {
                $this->filterService->filterQuery($request->filter, $filterQuery, 'quote');
            })
            ->select(
                DB::raw("CONCAT('assignee_ids_',GROUP_CONCAT(DISTINCT work_order_assignees.user_id SEPARATOR '_')) AS assignees_slug"),
                DB::raw("GROUP_CONCAT(DISTINCT users.first_name SEPARATOR ', ') AS assignee_name"),
            )
            ->groupBy('quotes.quote_id')
            ->get()
            ->groupBy('assignees_slug');

        $formattedData = [];

        foreach ($quotes as $assignees => $workOrder) {
            array_push($formattedData, [
                'label' => ! empty($workOrder->first()->assignee_name) ? $workOrder->first()->assignee_name : '',
                'total' => count($workOrder),
                'slug' => Str::slug($assignees),
            ]);
        }

        return $formattedData;
    }

    /**
     * @return array<int, mixed>
     */
    public function submittedBasedGroupData(int $organizationId, Request $request): array
    {
        $quotes = Quote::with(['submittedUser'])
            ->join('users', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('quotes.submitted_user_id', 'users.user_id')
                    ->where('users.organization_id', $organizationId);
            })
            ->when(! empty($request->search) || ! empty($request->filter), function (Builder $query) use ($organizationId) {
                $query->leftjoin('work_orders', 'work_orders.work_order_id', 'quotes.work_order_id')
                    ->join('properties', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_orders.property_id', 'properties.property_id')
                            ->where('properties.organization_id', $organizationId);
                    })
                    ->leftjoin('work_order_tasks', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tasks.work_order_id', 'quotes.work_order_id')
                            ->where('work_order_tasks.organization_id', $organizationId);
                    })
                    ->leftjoin('problem_diagnoses', 'problem_diagnoses.problem_diagnosis_id', 'work_order_tasks.problem_diagnosis_id')
                    ->leftjoin('problem_sub_categories', 'problem_diagnoses.problem_sub_category_id', 'problem_sub_categories.problem_sub_category_id')
                    ->leftjoin('problem_categories', 'problem_categories.problem_category_id', 'problem_sub_categories.problem_category_id')
                    ->leftjoin('residents', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_orders.requesting_resident_id', 'residents.resident_id')
                            ->where('residents.organization_id', $organizationId);
                    })
                    ->leftJoin('work_order_tags', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tags.work_order_id', 'work_orders.work_order_id')
                            ->join('tags', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('tags.tag_id', 'work_order_tags.tag_id')
                                    ->where('tags.organization_id', $organizationId)
                                    ->whereNull('tags.deleted_at');
                            });
                    });
            })
            // Search query
            ->when(! empty($request->search), function (Builder $query) use ($request) {
                $query->where(function (Builder $subQuery) use ($request) {
                    $subQuery->where('work_orders.work_order_number', 'like', "%{$request->search}%")
                        ->orWhere('work_orders.description', 'like', "%{$request->search}%")
                        ->orWhereRaw("TRIM(CONCAT(residents.first_name , ' ', COALESCE(residents.last_name, ''))) LIKE ?", ["%{$request->search}%"])
                        ->orWhere('properties.full_address', 'like', "%{$request->search}%")
                        ->orWhere('problem_categories.label', 'like', "%{$request->search}%");
                });
            })
            // Filter query
            ->when(! empty($request->filter), function (Builder $filterQuery) use ($request) {
                $this->filterService->filterQuery($request->filter, $filterQuery, 'quote');
            })
            ->select(
                DB::raw('BIN_TO_UUID(users.user_uuid) as user_uuid'),
                DB::raw("CONCAT_WS(' ', users.first_name, users.last_name) AS submitted_by"),
            )
            ->groupBy('quotes.quote_id')
            ->get()
            ->groupBy('submitted_by');

        $formattedData = [];

        foreach ($quotes as $user => $quote) {
            array_push($formattedData, [
                'label' => $user,
                'total' => count($quote),
                'slug' => Str::slug(! empty($quote->first()->user_uuid) ? $quote->first()->user_uuid : ''),
            ]);
        }

        return $formattedData;
    }

    /**
     * Return job tag and its job counts
     *
     * @return array<int,array<string,string|int>>
     */
    public function tagBasedGroupData(int $organizationId, GroupViewRequest $request): array
    {
        $quotes = Quote::with(['workOrder'])
            ->leftjoin('work_orders', 'work_orders.work_order_id', 'quotes.work_order_id')
            ->join('work_order_tags', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('work_order_tags.work_order_id', 'work_orders.work_order_id')
                    ->join('tags', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('tags.tag_id', 'work_order_tags.tag_id')
                            ->where('tags.organization_id', $organizationId)
                            ->whereNull('tags.deleted_at');
                    });
            })
            ->when(! empty($request->search) || ! empty($request->filter), function (Builder $query) use ($organizationId) {
                $query->join('properties', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('work_orders.property_id', 'properties.property_id')
                        ->where('properties.organization_id', $organizationId);
                })
                    ->leftjoin('work_order_tasks', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_tasks.work_order_id', 'quotes.work_order_id')
                            ->where('work_order_tasks.organization_id', $organizationId);
                    })
                    ->leftjoin('problem_diagnoses', 'problem_diagnoses.problem_diagnosis_id', 'work_order_tasks.problem_diagnosis_id')
                    ->leftjoin('problem_sub_categories', 'problem_diagnoses.problem_sub_category_id', 'problem_sub_categories.problem_sub_category_id')
                    ->leftjoin('problem_categories', 'problem_categories.problem_category_id', 'problem_sub_categories.problem_category_id')
                    ->leftjoin('residents', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_orders.requesting_resident_id', 'residents.resident_id')
                            ->where('residents.organization_id', $organizationId);
                    })
                    ->leftjoin('work_order_assignees', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('work_order_assignees.work_order_id', 'quotes.work_order_id')
                            ->whereNull('work_order_assignees.deleted_at')
                            ->join('users', function ($joinQuery) use ($organizationId) {
                                $joinQuery->on('users.user_id', '=', 'work_order_assignees.user_id')
                                    ->where('users.organization_id', '=', $organizationId);
                            });
                    });
            })
            // Search query
            ->when(! empty($request->search), function (Builder $query) use ($request) {
                $query->where(function (Builder $subQuery) use ($request) {
                    $subQuery->where('work_orders.work_order_number', 'like', "%{$request->search}%")
                        ->orWhere('work_orders.description', 'like', "%{$request->search}%")
                        ->orWhereRaw("TRIM(CONCAT(residents.first_name , ' ', COALESCE(residents.last_name, ''))) LIKE ?", ["%{$request->search}%"])
                        ->orWhere('properties.full_address', 'like', "%{$request->search}%")
                        ->orWhere('problem_categories.label', 'like', "%{$request->search}%");
                });
            })
            // Filter query
            ->when(! empty($request->filter), function (Builder $filterQuery) use ($request) {
                $this->filterService->filterQuery($request->filter, $filterQuery, 'quote');
            })
            ->select(
                DB::raw("CONCAT('tag_ids_',GROUP_CONCAT(DISTINCT tags.tag_id SEPARATOR '_')) AS tag_slug"),
                DB::raw("GROUP_CONCAT(DISTINCT tags.name SEPARATOR ', ') AS tag_name"),
            )
            ->groupBy('quotes.quote_id')
            ->get()
            ->groupBy('tag_slug');

        $formattedData = [];

        foreach ($quotes as $tags => $workOrder) {
            array_push($formattedData, [
                'label' => ! empty($workOrder->first()->tag_name) ? $workOrder->first()->tag_name : '',
                'total' => count($workOrder),
                'slug' => Str::slug($tags),
            ]);
        }

        return $formattedData;
    }
}

<?php

namespace App\Http\Controllers;

use App\Enums\Boolean;
use App\Helpers\Helper;
use App\Http\Requests\Schedule\GetTechnicianListRequest;
use App\Http\Resources\Scheduling\GetTechnicianListResource;
use App\Http\Resources\Scheduling\GetTechnicianSchedulesResources;
use App\Http\Resources\Scheduling\GetVendorAvailabilityResources;
use App\Http\Resources\Scheduling\ScheduleContextResource;
use App\Http\Resources\Scheduling\ScheduleVendorResource;
use App\Models\Organization;
use App\Models\ResidentAvailability;
use App\Models\Technician;
use App\Models\Vendor;
use App\Models\WorkOrder;
use App\Packages\OrganizationRolePermission\Exceptions\OrganizationNotFound;
use App\Services\Scheduling\Domain\Enums\SchedulingMethod;
use App\Services\Scheduling\SchedulingService;
use App\Services\Vendor\Enum\Service;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;

class SchedulingController extends Controller
{
    public function __construct(protected SchedulingService $scheduleService) {}

    /**
     * Display a listing of the resource.
     */
    public function index(): void {}

    /**
     * Show the form for creating a new resource.
     */
    public function create(): void
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(WorkOrder $workOrder): void
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(WorkOrder $workOrder): void
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, WorkOrder $workOrder): void
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(WorkOrder $workOrder): void
    {
        //
    }

    public function getContext(Request $request, WorkOrder $workOrder): ScheduleContextResource|JsonResponse
    {
        $this->authorize('schedule', $workOrder);

        /** @var Organization $organization */
        $organization = $request->user()?->organization;

        if (! $organization) {
            throw new OrganizationNotFound;
        }

        try {
            $hasResidentAvailability = ResidentAvailability::where('service_request_id', $workOrder->service_request_id)
                ->where('day_passed', 'no')
                ->exists();

            if (! $hasResidentAvailability) {
                throw new InvalidArgumentException(__('Please provide resident availability to proceed with scheduling.'));
            }

            $workOrder->load('workOrderSource:work_order_source_id,slug')->loadCount('issues');

            if (empty($workOrder->issues_count)) {
                throw new InvalidArgumentException(__('There is no issues associated with the work order.'));
            }

            $workOrderIsAlreadySendToLula = false;

            $options = $this->scheduleService->getTaskSchedulingOptions($organization->organization_id, $workOrder->work_order_id);

            $options->workOrderSourceSlug = $workOrder->workOrderSource->slug ?? null;
            $options->workOrderSourceSlug = $workOrder->workOrderSource->slug ?? null;
            $options->workOrderIsAlreadySendToLula = $workOrderIsAlreadySendToLula;

            return new ScheduleContextResource($options);
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage());

            return Response::unprocessableEntity($exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);

            return Response::internalServerError(message: $exception->getMessage());
        }
    }

    public function getVendors(Request $request, WorkOrder $workOrder): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('schedule', $workOrder);

        /** @var Organization $organization */
        $organization = $request->user()?->organization;

        if (! $organization) {
            throw new OrganizationNotFound;
        }

        try {
            $vendors = Vendor::join('organization_vendors', function ($query) use ($organization) {
                return $query->on('organization_vendors.vendor_id', 'vendors.vendor_id')
                    ->where('organization_vendors.organization_id', $organization->organization_id);
            })
                ->where('vendors.service', Service::THIRD_PARTY_VENDOR())
                ->where('vendors.is_active', Boolean::YES())
                ->when(! empty($request->search), function ($query) use ($request) {
                    $search = $request->search;

                    return $query->where(function ($query) use ($search) {
                        $query->where(DB::raw('CONCAT_WS(" ", vendors.first_name, vendors.last_name)'), 'like', $search . '%')
                            ->orWhere('vendors.first_name', 'like', $search . '%')
                            ->orWhere('vendors.last_name', 'like', $search . '%');
                    });
                })
                ->orderBy('vendors.first_name')
                ->orderBy('vendors.last_name')
                ->orderBy('vendors.vendor_id')
                ->select(
                    'vendors.vendor_id',
                    'vendors.vendor_uuid',
                    'vendors.first_name',
                    'vendors.last_name',
                    'vendors.service',
                    'vendors.is_active'
                )
                ->paginate($request->input('per_page', config('pagination.vendor_list.schedule_per_page')));

            return ScheduleVendorResource::collection($vendors);
        } catch (OrganizationNotFound $exception) {
            return Response::unprocessableEntity($exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);

            return Response::internalServerError(message: $exception->getMessage());
        }
    }

    public function getTechnicianList(GetTechnicianListRequest $request, WorkOrder $workOrder): GetTechnicianListResource|JsonResponse
    {
        $this->authorize('schedule', $workOrder);

        $organization = $request->user()?->organization;

        if (! $organization) {
            throw new OrganizationNotFound;
        }

        try {
            // TODO How do we use mode?
            $technicianList = $this->scheduleService->getTechnicianList(
                $organization->organization_id,
                $workOrder,
                SchedulingMethod::from($request->input('method', SchedulingMethod::EARLIEST)),
                $request->integer('duration'),
                true,
            );

            return new GetTechnicianListResource($technicianList);
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);

            return Response::internalServerError(message: $exception->getMessage());
        }
    }

    public function getTechnicianSchedules(GetTechnicianListRequest $request, WorkOrder $workOrder, Technician $technician): GetTechnicianSchedulesResources|JsonResponse
    {
        $this->authorize('schedule', $workOrder);

        $organization = $request->user()?->organization;

        if (! $organization) {
            throw new OrganizationNotFound;
        }

        try {
            // TODO How do we use mode?
            $technicianSchedules = $this->scheduleService->getTechnicianSchedules(
                $organization->organization_id,
                $workOrder,
                SchedulingMethod::from($request->input('method', SchedulingMethod::EARLIEST)),
                $request->integer('duration'),
                $technician->technician_uuid,
            );

            $timezone = $workOrder->timezone->name ?? config('settings.default_timezone');

            return new GetTechnicianSchedulesResources($technicianSchedules, $timezone);
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);

            return Response::internalServerError(message: $exception->getMessage());
        }
    }

    public function getVendorAvailability(Request $request, WorkOrder $workOrder, Vendor $vendor): GetVendorAvailabilityResources|JsonResponse
    {
        $this->authorize('schedule', $workOrder);

        $organization = $request->user()?->organization;

        if (! $organization) {
            throw new OrganizationNotFound;
        }

        try {
            $timezone = $workOrder->timezone->name ?? config('settings.default_timezone');

            $vendorAvailabilities = ResidentAvailability::select('timing', 'availability_date')
                ->where('service_request_id', $workOrder->service_request_id)
                ->where('organization_id', $organization->organization_id)
                ->where('day_passed', 'no')
                ->get();

            $availabilityMap = $vendorAvailabilities
                ->groupBy(fn ($a) => CarbonImmutable::parse($a['availability_date'])->toDateString());

            return new GetVendorAvailabilityResources($availabilityMap, $timezone);
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);

            return Response::internalServerError(message: $exception->getMessage());
        }
    }
}

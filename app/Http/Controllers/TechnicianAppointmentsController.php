<?php

namespace App\Http\Controllers;

use App\Enums\ServiceCallStatus;
use App\Enums\TechnicianAppointment as EnumsTechnicianAppointment;
use App\Helpers\Helper;
use App\Http\Requests\Technician\AvailabilityDatesRequest;
use App\Http\Requests\Technician\BlockOutUpdateRequest;
use App\Http\Requests\Technician\StoreBlockOutRequest;
use App\Http\Resources\Technician\TechnicianAgendaResource;
use App\Http\Resources\Technician\TechnicianBlockOutDeleteResource;
use App\Http\Resources\Technician\TechnicianBlockOutResource;
use App\Http\Resources\Technician\TechnicianCalendarViewResource;
use App\Models\Technician;
use App\Models\TechnicianAppointment;
use App\Models\User;
use App\Models\WorkOrder;
use App\Packages\OrganizationRolePermission\Exceptions\OrganizationNotFound;
use App\States\WorkOrders\Canceled;
use App\Traits\TechnicianScheduleTrait;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriodImmutable;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;
use LogicException;
use UnexpectedValueException;

class TechnicianAppointmentsController extends Controller
{
    use TechnicianScheduleTrait;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, Technician $technician): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('viewTechnicianBlockOut', [User::class, $technician]);

        $request->validate([
            'start_date' => 'required|date_format:Y-m-d',
            'end_date' => 'required|date_format:Y-m-d',
        ]);

        $technicianTimezone = $technician->user?->timezone?->name ?? config('settings.default_timezone');

        $startDate = CarbonImmutable::parse($request->start_date . ' ' . CarbonImmutable::now($technicianTimezone)->toTimeString(), $technicianTimezone);
        $endDate = CarbonImmutable::parse($request->end_date . ' ' . CarbonImmutable::now($technicianTimezone)->toTimeString(), $technicianTimezone);

        try {
            $organizationId = $request->user()?->organization_id;

            $technicianAppointments = TechnicianAppointment::with([
                'serviceCall.workOrderTaskMaterials',
                'workOrder:work_order_id,work_order_uuid,state,property_id,organization_id,priority,work_order_number,timezone_id',
                'workOrder.timezone:timezone_id,name',
                'workOrder.property:property_id,property_uuid,property_name,street_address,unit_number,city,postal_zip_code,state_id',
                'workOrder.property.state',
                'workOrder.tasks.latestServiceCalls.appointment',
                'workOrder.tasks.latestServiceCalls.createdQuote',
                'workOrder.tasks.problemDiagnosis:problem_diagnosis_id,problem_sub_category_id',
                'workOrder.tasks.problemDiagnosis.subCategory:problem_sub_category_id,problem_category_id',
                'workOrder.tasks.problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
                'technician:technician_id,user_id,technician_uuid',
                'technician.user:user_id,first_name,last_name,middle_name',
                'workOrder.latestInvoices',
            ])
                ->leftJoin('work_order_service_calls', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('technician_appointments.technician_appointment_id', 'work_order_service_calls.technician_appointment_id')
                        ->where('work_order_service_calls.organization_id', $organizationId)
                        ->whereIn('work_order_service_calls.status', [ServiceCallStatus::ACTIVE(), ServiceCallStatus::COMPLETED()]);
                })
                ->leftJoin('work_orders', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('technician_appointments.work_order_id', 'work_orders.work_order_id')
                        ->where('work_orders.organization_id', $organizationId);
                })
                ->where(function ($query) use ($startDate, $endDate) {
                    $query->where('technician_appointments.scheduled_start_time', '>=', $startDate->startOfDay()->setTimezone('UTC'))
                        ->where('technician_appointments.scheduled_end_time', '<=', $endDate->endOfDay()->setTimezone('UTC'));
                })
                ->where('technician_appointments.technician_id', $technician->technician_id)
                ->where(function ($query) {
                    $query->whereIn('work_order_service_calls.status', [ServiceCallStatus::ACTIVE(), ServiceCallStatus::COMPLETED()])
                        ->orWhere('technician_appointments.appointment_type', EnumsTechnicianAppointment::BLOCK_OUT());
                })
                ->where(function ($query) {
                    $query->where('work_orders.state', '<>', Canceled::$name)
                        ->orWhereNull('work_orders.state');
                })
                ->orderBy('technician_appointments.scheduled_start_time', 'ASC')
                ->orderBy('technician_appointments.technician_appointment_id')
                ->select(
                    'technician_appointments.technician_appointment_id',
                    'technician_appointments.technician_appointment_uuid',
                    'technician_appointments.technician_id',
                    'technician_appointments.work_order_id',
                    'technician_appointments.work_order_task_id',
                    'technician_appointments.appointment_type',
                    'technician_appointments.is_block_out_all_day',
                    'technician_appointments.note',
                    'technician_appointments.scheduled_start_time',
                    'technician_appointments.scheduled_end_time',
                    'technician_appointments.actual_start_time',
                    'technician_appointments.actual_end_time',
                    'technician_appointments.adjusted_elapse_time_in_sec',
                )
                ->groupBy('technician_appointments.technician_appointment_id')
                ->get();

            return TechnicianAgendaResource::collection($technicianAppointments);

        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Agenda listing api Failed[UnexpectedValueException]');

            return Response::unprocessableEntity(errors: [$exception->getMessage()], message: __('The server was unable to process the request because unexpected behavior occurred.'));
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Agenda listing api Failed[InvalidArgumentException]');

            return Response::badRequest(errors: [$exception->getMessage()], message: __('The server was unable to process the request because it contains invalid data.'));
        } catch (LogicException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Agenda listing api Failed[LogicException]');

            return Response::unprocessableEntity(errors: [$exception->getMessage()], message: __('Something went wrong, The server was unable to process'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Agenda listing api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreBlockOutRequest $request, Technician $technician): JsonResponse|JsonResource
    {
        $this->authorize('createTechnicianBlockOut', [User::class, $technician]);

        $organization = $request->user()?->organization;

        if (empty($organization)) {
            throw new OrganizationNotFound;
        }

        DB::beginTransaction();

        try {
            $technicianTimezone = $technician->user?->timezone?->name ?? config('settings.default_timezone');
            $technicianDate = CarbonImmutable::parse($request->date . ' ' . CarbonImmutable::now($technicianTimezone)->toTimeString(), $technicianTimezone);

            if ($request->is_block_out_all_day) {
                $scheduledStartTime = $technicianDate->startOfDay();
                $scheduledEndTime = $technicianDate->endOfDay();
            } else {
                $scheduledStartTime = $technicianDate->setTimeFromTimeString($request->scheduled_start_time);
                $scheduledEndTime = $technicianDate->setTimeFromTimeString($request->scheduled_end_time);
            }

            $scheduledStartTime = CarbonImmutable::parse($scheduledStartTime, $technicianTimezone)->setTimezone('UTC');
            $scheduledEndTime = CarbonImmutable::parse($scheduledEndTime, $technicianTimezone)->setTimezone('UTC');

            if (! empty($request->work_order_id)) {
                $workOrder = WorkOrder::whereUuid($request->work_order_id)->firstOrFail();
            }

            $technicianAppointment = new TechnicianAppointment;

            if (! empty($organization)) {
                $technicianAppointment->organization_id = $organization->organization_id;
            }

            $technicianAppointment->technician_id = $technician->technician_id;
            $technicianAppointment->is_block_out_all_day = $request->is_block_out_all_day;
            $technicianAppointment->work_order_id = ! empty($workOrder) ? $workOrder->work_order_id : null;
            $technicianAppointment->work_order_task_id = ! empty($workOrder) ? ($workOrder->tasks->first()?->work_order_task_id) : null;
            $technicianAppointment->note = $request->note;
            $technicianAppointment->scheduled_start_time = $scheduledStartTime;
            $technicianAppointment->scheduled_end_time = $scheduledEndTime;
            $technicianAppointment->appointment_type = EnumsTechnicianAppointment::BLOCK_OUT();
            $technicianAppointment->save();

            DB::commit();

            $data['technicianAppointment'] = $technicianAppointment;
            $data['technician'] = $technician;

            return new TechnicianBlockOutResource($data);

        } catch (ModelNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage());

            return Response::notFound(message: $exception->getMessage());
        } catch (QueryException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);

            return Response::error(
                errors: [$exception->getMessage()],
                message: __('Unable to add technician block out.')
            );
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);

            return Response::error(
                errors: [$exception->getMessage()],
                message: __('Unable to add technician block out.')
            );
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @throws OrganizationNotFound
     */
    public function update(BlockOutUpdateRequest $request, Technician $technician, TechnicianAppointment $technicianAppointment): JsonResponse|TechnicianBlockOutResource
    {
        $this->authorize('updateTechnicianBlockOut', [User::class, $technician, $technicianAppointment]);

        $technicianTimezone = $technician->user?->timezone?->name ?? config('settings.default_timezone');
        $technicianDate = CarbonImmutable::parse($request->date . ' ' . CarbonImmutable::now($technicianTimezone)->toTimeString(), $technicianTimezone);

        if ($request->is_block_out_all_day) {
            $scheduledStartTime = $technicianDate->startOfDay();
            $scheduledEndTime = $technicianDate->endOfDay();
        } else {
            $scheduledStartTime = $technicianDate->setTimeFromTimeString($request->scheduled_start_time);
            $scheduledEndTime = $technicianDate->setTimeFromTimeString($request->scheduled_end_time);
        }

        $scheduledStartTime = CarbonImmutable::parse($scheduledStartTime, $technicianTimezone)->setTimezone('UTC');
        $scheduledEndTime = CarbonImmutable::parse($scheduledEndTime, $technicianTimezone)->setTimezone('UTC');

        $organization = $request->user()?->organization;

        if (empty($organization)) {
            throw new OrganizationNotFound;
        }

        try {
            DB::beginTransaction();

            if (! empty($request->work_order_id)) {
                $workOrder = WorkOrder::whereUuid($request->work_order_id)->firstOrFail();
            }

            $technicianAppointment->technician_id = $technician->technician_id;
            $technicianAppointment->is_block_out_all_day = $request->is_block_out_all_day;
            $technicianAppointment->work_order_id = ! empty($workOrder) ? $workOrder->work_order_id : null;
            $technicianAppointment->work_order_task_id = ! empty($workOrder) ? ($workOrder->tasks->first()?->work_order_task_id) : null;
            $technicianAppointment->note = $request->note;
            $technicianAppointment->scheduled_start_time = $scheduledStartTime;
            $technicianAppointment->scheduled_end_time = $scheduledEndTime;
            $technicianAppointment->organization_id = $organization->organization_id;
            $technicianAppointment->appointment_type = EnumsTechnicianAppointment::BLOCK_OUT();

            $technicianAppointment->save();

            DB::commit();

            $data['technicianAppointment'] = $technicianAppointment;
            $data['technician'] = $technician;

            return new TechnicianBlockOutResource($data);

        } catch (OrganizationNotFound $exception) {
            DB::rollBack();

            Helper::exceptionLog(exception: $exception, message: 'Technician block out update Failed, due to OrganizationNotFoundException');

            return Response::unprocessableEntity(message: __($exception->getMessage()));
        } catch (Exception $exception) {
            DB::rollBack();

            Helper::exceptionLog(exception: $exception, additionalInfo: [
                'technicianAppointmentId' => $technicianAppointment->technician_appointment_id,
                'technicianId' => $technician->technician_id,
            ], message: 'Technician block out update Failed[Exception]', notify: true);

            return Response::error(
                errors: [$exception->getMessage()],
                message: __('Unable to update technician block out.')
            );
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Technician $technician, TechnicianAppointment $technicianAppointment): JsonResponse|TechnicianBlockOutDeleteResource
    {
        $this->authorize('deleteTechnicianBlockOut', [User::class, $technician, $technicianAppointment]);

        try {
            $technicianAppointment->delete();

            return new TechnicianBlockOutDeleteResource($technicianAppointment);

        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Technician block out delete api failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * View technician calendar
     */
    public function viewTechnicianCalendar(Request $request, Technician $technician): AnonymousResourceCollection|TechnicianCalendarViewResource|JsonResponse
    {
        $this->authorize('viewTechnicianBlockOut', [User::class, $technician]);

        $request->validate([
            'date' => 'required',
        ]);

        try {

            $technicianTimezone = $technician->user?->timezone?->name ?? config('settings.default_timezone');
            $technicianDate = CarbonImmutable::parse($request->date . ' ' . CarbonImmutable::now($technicianTimezone)->toTimeString(), $technicianTimezone);

            $organizationId = $request->user()?->organization_id;

            if ($request->expectsDevice()) {

                $startDate = $technicianDate->subWeeks(2);
                $endDate = $technicianDate->addWeeks(2);
            } else {

                $startDate = $technicianDate->startOfMonth()->setTimezone('UTC');
                $endDate = $technicianDate->endOfMonth()->setTimezone('UTC');
            }

            $technicianAppointments = TechnicianAppointment::where('technician_appointments.technician_id', $technician->technician_id)
                ->where('technician_appointments.organization_id', $organizationId)
                ->where(function ($query) use ($startDate, $endDate) {
                    $query->where('technician_appointments.scheduled_start_time', '>=', $startDate)
                        ->where('technician_appointments.scheduled_end_time', '<=', $endDate);
                })
                ->leftJoin('work_order_service_calls', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('technician_appointments.technician_appointment_id', 'work_order_service_calls.technician_appointment_id')
                        ->where('work_order_service_calls.organization_id', $organizationId)
                        ->whereIn('work_order_service_calls.status', [ServiceCallStatus::ACTIVE(), ServiceCallStatus::COMPLETED()]);
                })
                ->leftJoin('work_orders', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('technician_appointments.work_order_id', 'work_orders.work_order_id')
                        ->where('work_orders.organization_id', $organizationId);
                })
                ->where(function ($query) {
                    $query->where('work_orders.state', '<>', Canceled::$name)
                        ->orWhereNull('work_orders.state');
                })
                ->where(function ($query) {
                    $query->whereIn('work_order_service_calls.status', [ServiceCallStatus::ACTIVE(), ServiceCallStatus::COMPLETED()])
                        ->orWhere('technician_appointments.appointment_type', EnumsTechnicianAppointment::BLOCK_OUT());
                })
                ->select(
                    DB::raw('MAX(technician_appointments.is_block_out_all_day) as is_block_out_all_day'),
                    DB::raw('SUM(CASE WHEN technician_appointments.appointment_type = "regular" THEN 1 ELSE 0 END) as regular_appointment_count'),
                    DB::raw('SUM(CASE WHEN technician_appointments.appointment_type = "block-out" THEN 1 ELSE 0 END) as block_out_appointment_count'),
                    DB::raw("DATE(CONVERT_TZ(technician_appointments.scheduled_start_time, 'UTC', '{$technicianTimezone}')) as appointment_date"),
                )
                ->groupBy('appointment_date')
                ->get();

            return TechnicianCalendarViewResource::collection($technicianAppointments);

        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Technician calendar list failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Get technician availability dates with in a specific date range
     */
    public function getAvailabilityDates(AvailabilityDatesRequest $request, Technician $technician): JsonResponse
    {
        $this->authorize('scheduleTrip', [User::class]);

        try {
            $technician->load([
                'user:user_id,timezone_id',
                'user.timezone:timezone_id,name',
                'workingHours:technician_working_hour_id,technician_id,weekday,work_start_at,work_end_at,is_enabled',
            ]);

            // Find tech timezone
            $technicianTimezone = $technician->user?->timezone?->name ?? config('settings.default_timezone');
            $technicianWorkingHours = $technician->workingHours ?? null;

            // Convert requested date to tech timezone
            $startDate = CarbonImmutable::parse($request->start_date . ' ' . CarbonImmutable::now($technicianTimezone)->toTimeString(), $technicianTimezone);

            $duration = $request->duration_in_minutes ?? 30;

            $monthStartDate = $startDate->startOfMonth()->setTimezone('UTC');
            $monthEndDate = $startDate->endOfMonth()->setTimezone('UTC');

            $technicianAppointments = $this->findTechAppointmentsBetweenTwoDays($technician, $monthStartDate, $monthEndDate);

            $calendarDays = CarbonPeriodImmutable::create($monthStartDate, $monthEndDate)->toArray();
            $notAvailableDayAndTimings = [];

            // If any appointments/block out exist filter those days
            $notAvailableDayAndTimings = $this->filterWithAppointments(
                $calendarDays,
                $technicianAppointments,
                $technicianWorkingHours,
                $technicianTimezone,
                $duration,
                $notAvailableDayAndTimings
            );

            $notAvailableDayAndTimings = collect((array) $notAvailableDayAndTimings)
                ->groupBy('date')
                ->values()
                ->map(function ($timings) use ($technicianTimezone) {
                    $timingValues = array_values(array_filter($timings->pluck('timing')->unique()->toArray()));

                    return [
                        'date' => CarbonImmutable::parse($timings->first()->date . ' ' . CarbonImmutable::now($technicianTimezone)->toTimeString(), $technicianTimezone)
                            ->startOfDay()->setTimezone('UTC')->toIso8601String(),
                        'timings' => count($timingValues) < 3 ? $timingValues : [],
                    ];
                })->toArray();

            return response()->json([
                'not_available_date_and_timings' => $notAvailableDayAndTimings,
            ]);

        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Technician reschedule availability date fetch api failed', notify: true);

            return response()->internalServerError(message: __('Failed to load calender'));
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Helpers\DataFormatHelper;
use App\Helpers\Helper;
use App\Http\Requests\Technician\SkillsUpdateRequest;
use App\Http\Requests\Technician\WorkingHoursUpdateRequest;
use App\Http\Resources\Technician\SelectedSkillsResource;
use App\Models\ProblemCategory;
use App\Models\Technician;
use App\Models\TechnicianSkill;
use App\Models\TechnicianWorkingHour;
use App\Models\User;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Ramsey\Uuid\Uuid;

class TechnicianController extends Controller
{
    /**
     * Update working hours
     */
    public function updateWorkingHours(WorkingHoursUpdateRequest $request, Technician $technician): JsonResponse|JsonResource
    {
        $this->authorize('updateWorkingHours', [User::class, $technician]);

        DB::beginTransaction();

        try {
            $workingHours = $request->all();
            $lunchStartTime = CarbonImmutable::parse($workingHours['lunch_time_break']['from']);
            $lunchEndTime = CarbonImmutable::parse($workingHours['lunch_time_break']['to']);
            $lunchBreakDuration = $workingHours['lunch_time_break']['duration'];

            foreach ($workingHours as $key => $workingHour) {
                if ($key === 'lunch_time_break') {
                    continue;
                }
                $workStartTime = CarbonImmutable::parse($workingHour['from']);
                $workEndTime = CarbonImmutable::parse($workingHour['to']);

                TechnicianWorkingHour::updateOrCreate([
                    'technician_id' => $technician->technician_id,
                    'organization_id' => $technician->organization_id,
                    'weekday' => $key,
                ], [
                    'work_start_at' => $workStartTime,
                    'work_end_at' => $workEndTime,
                    'lunch_break_slot_start' => $lunchStartTime,
                    'lunch_break_slot_end' => $lunchEndTime,
                    'lunch_break_duration_in_minutes' => $lunchBreakDuration,
                    'is_enabled' => $workingHour['isEnabled'] ? 'yes' : 'no',
                ]);
            }

            DB::commit();

            return new JsonResource([
                'working_hours' => DataFormatHelper::formatWorkingHours($technician->workingHours),
            ]);

        } catch (ModelNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Technician working hours update api failed[ModelNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (QueryException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Technician working hours update api failed[QueryException]', notify: true);

            return Response::error(
                errors: [$exception->getMessage()],
                message: __('Unable to edit working hours.')
            );
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Technician working hours update api failed [Exception].', notify: true);

            return Response::error(
                errors: [$exception->getMessage()],
                message: __('Unable to edit working hours.')
            );
        }
    }

    /**
     * Importing working hours
     */
    public function importWorkingHours(Technician $technician, Request $request): JsonResponse|JsonResource
    {
        $this->authorize('importWorkingHours', [User::class, $technician]);
        try {

            $technicianWorkingHours = TechnicianWorkingHour::select('technician_working_hour_id', 'technician_id', 'weekday',
                'work_start_at', 'work_end_at', 'lunch_break_slot_start', 'lunch_break_slot_end',
                'lunch_break_duration_in_minutes', 'is_enabled'
            )->where('technician_id', $technician->technician_id)
                ->get();

            if (empty($technicianWorkingHours)) {
                throw new ModelNotFoundException(__('Working hours not found.'));
            }

            return new JsonResource([
                'working_hours' => DataFormatHelper::formatWorkingHours($technicianWorkingHours),
            ]);

        } catch (ModelNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Working Hours import API failed[ModelNotFoundException].');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Working Hours import API failed[Exception].', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Update technician skills
     */
    public function updateTechnicianSkills(SkillsUpdateRequest $request, Technician $technician): JsonResponse|JsonResource
    {
        $this->authorize('updateTechnicianSkill', [User::class, $technician]);

        DB::beginTransaction();

        try {
            $requestProblemCategories = collect((array) $request->problem_categories);

            // Select requested problem_category_id from request
            $requestProblemCategoryIds = $requestProblemCategories->pluck('problem_category_id')->toArray();

            // Select problem categories only requested.
            $selectedProblemCategories = ProblemCategory::select('problem_category_id', 'problem_category_uuid')
                ->with('problemSubCategories:problem_category_id,problem_sub_category_id,problem_sub_category_uuid',
                    'problemSubCategories.problemDiagnoses:problem_sub_category_id,problem_diagnosis_uuid,problem_diagnosis_id'
                )
                ->whereUuid($requestProblemCategoryIds)
                ->get();

            if (empty($selectedProblemCategories)) {
                throw new ModelNotFoundException(__('Selected problem categories not found.'));
            }

            // Delete all available technician skills
            $technician->skills()->delete();
            $technicianSkills = [];
            $model = new TechnicianSkill;

            foreach ($requestProblemCategories as $problemCategory) {

                $selectedProblemCategory = $selectedProblemCategories
                    ->where('problem_category_uuid', $problemCategory['problem_category_id'])
                    ->first();

                if (empty($selectedProblemCategory)) {
                    throw new ModelNotFoundException(__("Selected problem category({$problemCategory['problem_category_id']}) not found."));
                }
                // Check if the user select the category the store only category id
                if (! empty($problemCategory['is_selected'])) {
                    $uuid = $model->resolveUuid();
                    $technicianSkills[] = [
                        'technician_skill_uuid' => Uuid::fromString(strtolower($uuid->toString()))->getBytes(),
                        'technician_id' => $technician->technician_id,
                        'organization_id' => $technician->organization_id,
                        'problem_category_id' => $selectedProblemCategory->problem_category_id,
                        'problem_sub_category_id' => null,
                        'problem_diagnosis_id' => null,
                    ];

                    continue;
                }

                //Get all sub category associate with selected category.
                $subCategories = $selectedProblemCategory->problemSubCategories;

                // Fetch user requested sub categories
                $requestedSubCategories = $problemCategory['sub_categories'];

                foreach ($requestedSubCategories as $subCategory) {
                    $selectedSubCategory = $subCategories
                        ->where('problem_sub_category_uuid', $subCategory['sub_category_id'])
                        ->first();

                    if (empty($selectedSubCategory)) {
                        throw new ModelNotFoundException(__("Selected problem sub category({$subCategory['sub_category_id']}) not found."));
                    }
                    // Check if the user select the sub category then store only category id
                    if (! empty($subCategory['is_selected'])) {
                        $uuid = $model->resolveUuid();
                        $technicianSkills[] = [
                            'technician_skill_uuid' => Uuid::fromString(strtolower($uuid->toString()))->getBytes(),
                            'technician_id' => $technician->technician_id,
                            'organization_id' => $technician->organization_id,
                            'problem_category_id' => null,
                            'problem_sub_category_id' => $selectedSubCategory->problem_sub_category_id,
                            'problem_diagnosis_id' => null,
                        ];

                        continue;
                    }

                    //Get all diagnosis associate with sub category.
                    $problemDiagnoses = $selectedSubCategory->problemDiagnoses;

                    if (! empty($subCategory['problem_diagnosis_ids']) && count($subCategory['problem_diagnosis_ids'])) {
                        // Fetch user requested diagnosis
                        $selectedDiagnoses = $problemDiagnoses
                            ->whereIn('problem_diagnosis_uuid', $subCategory['problem_diagnosis_ids'])
                            ->pluck('problem_diagnosis_id')
                            ->toArray();

                        if (empty($selectedDiagnoses)) {
                            $diagnosesIds = implode(',', $subCategory['problem_diagnosis_ids']);
                            throw new ModelNotFoundException(__("Selected problem diagnoses({$diagnosesIds}) not found."));
                        }

                        foreach ($selectedDiagnoses as $selectedDiagnosisId) {
                            $uuid = $model->resolveUuid();
                            $technicianSkills[] = [
                                'technician_skill_uuid' => Uuid::fromString(strtolower($uuid->toString()))->getBytes(),
                                'technician_id' => $technician->technician_id,
                                'organization_id' => $technician->organization_id,
                                'problem_category_id' => null,
                                'problem_sub_category_id' => null,
                                'problem_diagnosis_id' => $selectedDiagnosisId,
                            ];
                        }
                    }
                }
            }

            TechnicianSkill::insert($technicianSkills);

            DB::commit();

            return new SelectedSkillsResource($technician->skills);

        } catch (ModelNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Technician skills update api failed[ModelNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (QueryException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Technician skills update api failed[QueryException]');

            return Response::error(
                errors: [$exception->getMessage()],
                message: __('Unable to update technician skills.')
            );
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Technician skills update api failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Importing technician skills
     */
    public function importTechnicianSkills(Technician $technician): JsonResponse|JsonResource
    {
        $this->authorize('importTechnicianSkill', [User::class, $technician]);

        try {

            if (empty($technician->skills)) {
                throw new ModelNotFoundException(__('Technician skills not found.'));
            }

            return new SelectedSkillsResource($technician->skills);

        } catch (ModelNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Technician skills import api failed[ModelNotFoundException].');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Technician skills import api failed[Exception].', notify: true);

            return Response::internalServerError();
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Exceptions\LulaWebhookException;
use App\Exceptions\WebhookException;
use App\Helpers\Helper;
use App\Jobs\Webhook\ProcessLulaWebhookJob;
use App\Models\Vendor;
use App\Models\WorkOrder;
use App\Services\Vendor\Enum\Service;
use App\Services\Vendor\Services\Lula\Webhook\LulaWebhookConfig;
use App\Services\Vendor\Services\Lula\Webhook\LulaWebhookSignatureValidator;
use App\Services\Webhook\WebhookClientProcessor;
use App\States\WorkOrders\Completed;
use App\States\WorkOrders\QualityCheck;
use App\States\WorkOrders\ReadyToInvoice;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Validator as ValidatorType;
use Spatie\WebhookClient\Exceptions\InvalidConfig;
use Symfony\Component\HttpFoundation\Response as WebhookResponse;

class LulaWebhookController extends Controller
{
    /**
     * @throws InvalidConfig
     * @throws WebhookException
     */
    public function __invoke(Request $request): Response|JsonResponse|WebhookResponse
    {
        $request->validate([
            'eventType' => 'required',
            'data' => 'required|array',
        ]);

        try {
            request()->headers->add(['Device-Type' => 'Webhook']);
            Log::debug('webhook payload', [
                'request' => $request->all(),
                'headers' => $request->headers,
            ]);

            $data = collect((array) $request->data)->first();

            if (empty($data)) {
                return Response::notFound(__('Work order details not found'));
            }

            $vendor = Vendor::select('vendor_id')
                ->where('service', Service::LULA())
                ->firstOrFail();

            $workOrder = WorkOrder::with([
                'organization:organization_id,webhook_secret_key',
                'organization.vendorSettings:organization_id,vendor_id,webhook_secret_key',
                'tasks:work_order_task_id,work_order_id',
            ])
                ->where('vendor_work_order_id', $data['workOrderId'])
                ->select(
                    'work_order_id', 'work_order_uuid', 'state', 'organization_id'
                )
                ->first();

            if (empty($workOrder)) {
                return Response::message(__('Unable to find the requested work order'));
            }

            // Following the "Quality Check" status, we did not take any events triggered from the lula.
            if ($workOrder->state->equals(QualityCheck::class, ReadyToInvoice::class, Completed::class)) {
                return Response::message(__('Work order state not completable to handle this request'));
            }

            if (empty($workOrder->organization->vendorSettings)) {
                return Response::notFound(__('Unable to find associated vendor settings'));
            }

            $lulaVendorSettings = $workOrder->organization->vendorSettings->where('vendor_id', $vendor->vendor_id)->first();

            if (empty($lulaVendorSettings)) {
                return Response::notFound(__('Unable to find associated vendor settings'));
            }

            $workOrderTask = $workOrder->tasks->first();

            if (empty($workOrderTask)) {
                return Response::notFound(__('Work order task associated with the work order not found'));
            }

            /** @var array<string, mixed> $config */
            $config = config('webhook-client.configs');

            $webhookConfig = new LulaWebhookConfig(collect($config)
                ->map(function (array $config) use ($lulaVendorSettings) {
                    $config['signing_secret'] = $lulaVendorSettings->webhook_secret_key ?? null;
                    $config['timestamp_header_name'] = 'Api-Timestamp';
                    $config['process_webhook_job'] = ProcessLulaWebhookJob::class;
                    $config['signature_validator'] = LulaWebhookSignatureValidator::class;

                    return $config;
                })->first() ?? []);

            request()->merge([
                'organization_id' => $workOrder->organization->organization_id,
            ]);
            request()->attributes->add([
                'organization_id' => $workOrder->organization->organization_id,
                'organization' => $workOrder->organization,
            ]);

            $validator = match ($request->eventType) {
                'core:workorder.scheduling-in-progress' => $this->validateScheduleInProgressPayload($request, $workOrder),
                'core:workorder.scheduled' => $this->validateSchedulePayload($request, $workOrder),
                'core:workorder.work-in-progress' => $this->validateWorkInProgressPayload($request, $workOrder),
                'core:workorder.paused' => $this->validateWorkPausedPayload($request, $workOrder),
                'core:workorder.quality-check' => $this->validateQualityCheckPayload($request, $workOrder),
                'core:workorder.work-completed' => $this->validateWorkCompletedPayload($request, $workOrder),
                'core:workorder.canceled' => $this->validateWorkCancelPayload($request, $workOrder),
                'core:workorder.note.added' => $this->validateNoteAddedPayload($request, $workOrder),
                'core:workorder.invoice-ready' => $this->validateInvoicePayload($request, $workOrder),
                default => $this->invalidAction($request)
            };

            if ($validator->fails()) {
                info('Lula webhook failed due to validation', [
                    'errors' => $validator->errors()->toArray(),
                ]);

                return Response::message(__('Validation failed'));
            }

            return (new WebhookClientProcessor($request, $webhookConfig))->process();
        } catch (LulaWebhookException $exception) {
            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Exception occurred while handling webhook[WebhookController]', notify: true);

            return Response::internalServerError();
        }
    }

    public function invalidAction(Request $request): ValidatorType
    {
        $validator = Validator::make($request->all(), []);

        return $validator;
    }

    public function validateScheduleInProgressPayload(Request $request, WorkOrder $workOrder): ValidatorType
    {
        $validator = Validator::make($request->all(), []);

        return $validator;
    }

    public function validateSchedulePayload(Request $request, WorkOrder $workOrder): ValidatorType
    {
        $data = collect((array) $request->data)->first();
        $validator = Validator::make($data, [
            'serviceSchedule.serviceWindow.startTimeUTC' => 'required|date',
            'serviceSchedule.serviceWindow.endTimeUTC' => 'required|date',
        ]);

        return $validator;
    }

    public function validateWorkInProgressPayload(Request $request, WorkOrder $workOrder): ValidatorType
    {
        $validator = Validator::make($request->all(), []);

        return $validator;
    }

    public function validateWorkPausedPayload(Request $request, WorkOrder $workOrder): ValidatorType
    {
        $data = collect((array) $request->data)->first();
        $validator = Validator::make($data, []);

        return $validator;
    }

    public function validateQualityCheckPayload(Request $request, WorkOrder $workOrder): ValidatorType
    {
        $validator = Validator::make($request->all(), []);

        return $validator;
    }

    public function validateWorkCompletedPayload(Request $request, WorkOrder $workOrder): ValidatorType
    {
        $data = collect((array) $request->data)->first();
        $validator = Validator::make($data, [
            'completedDetails.type' => 'required',
        ]);

        return $validator;
    }

    public function validateWorkCancelPayload(Request $request, WorkOrder $workOrder): ValidatorType
    {
        $data = collect((array) $request->data)->first();
        $validator = Validator::make($data, []);

        return $validator;
    }

    public function validateNoteAddedPayload(Request $request, WorkOrder $workOrder): ValidatorType
    {
        $data = collect((array) $request->data)->first();
        $validator = Validator::make($data, [
            'note.note' => 'required',
        ]);

        return $validator;
    }

    public function validateInvoicePayload(Request $request, WorkOrder $workOrder): ValidatorType
    {
        $data = collect((array) $request->data)->first();

        $validator = Validator::make($data, [
            'invoices' => 'required|array',
        ]);

        return $validator;
    }
}

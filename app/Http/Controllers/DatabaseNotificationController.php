<?php

namespace App\Http\Controllers;

use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Resources\Notification\NotificationResource;
use App\Models\DatabaseNotification;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Response;

class DatabaseNotificationController extends Controller
{
    public function index(Request $request): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('viewAny', DatabaseNotification::class);
        try {
            /** @var User $user */
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $notifications = $user->notifications()->with([
                'user:user_id,user_uuid,first_name,last_name,middle_name',
                'workOrder:work_order_id,work_order_uuid,work_order_number,property_id,timezone_id,priority',
                'workOrder.property:property_id,street_address,unit_number,city,postal_zip_code,state_id',
                'workOrder.property.state:state_id,state_code',
                'workOrder.timezone:timezone_id,name',
                'workOrder.tasks.problemDiagnosis.subCategory.problemCategory:problem_category_id,label',

                'serviceRequest:service_request_id,service_request_uuid,service_request_number,property_id,timezone_id,priority',
                'serviceRequest.property:property_id,street_address,unit_number,city,postal_zip_code,state_id',
                'serviceRequest.property.state:state_id,state_code',
                'serviceRequest.timezone:timezone_id,name',
                'serviceRequest.categories:service_request_category_uuid,service_request_id,problem_category_id,problem_sub_category_id',
                'serviceRequest.categories.problemCategory:problem_category_id,label,slug',
            ])
                ->whereNull('cleared_at')
                ->orderBy('notification_id', 'desc')
                ->cursorPaginate($request->input('per_page', config('pagination.notifications.per_page')));

            return NotificationResource::collection($notifications);
        } catch (UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Notification fetch api failed[UserNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Notification fetch api failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    public function clearedNotifications(Request $request): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('viewAny', DatabaseNotification::class);
        try {
            /** @var User $user */
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $notifications = $user->clearedNotifications()->with([
                'user:user_id,user_uuid,first_name,last_name,middle_name',
                'workOrder:work_order_id,work_order_uuid,work_order_number,property_id,timezone_id',
                'workOrder.property:property_id,street_address,unit_number,city,postal_zip_code,state_id',
                'workOrder.property.state:state_id,state_code',
                'workOrder.timezone:timezone_id,name',
                'workOrder.tasks.problemDiagnosis.subCategory.problemCategory:problem_category_id,label',

                'serviceRequest:service_request_id,service_request_uuid,service_request_number,property_id,timezone_id',
                'serviceRequest.property:property_id,street_address,unit_number,city,postal_zip_code,state_id',
                'serviceRequest.property.state:state_id,state_code',
                'serviceRequest.timezone:timezone_id,name',
                'serviceRequest.categories:service_request_category_uuid,service_request_id,problem_category_id,problem_sub_category_id',
                'serviceRequest.categories.problemCategory:problem_category_id,label,slug',
            ])
                ->orderBy('notification_id', 'desc')
                ->cursorPaginate($request->input('per_page', config('pagination.notifications.per_page')));

            return NotificationResource::collection($notifications);
        } catch (UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Cleared notification fetch api failed[UserNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Cleared notification fetch api failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }
}

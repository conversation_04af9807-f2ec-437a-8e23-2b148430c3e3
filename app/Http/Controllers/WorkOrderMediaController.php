<?php

namespace App\Http\Controllers;

use App\Enums\Boolean;
use App\Enums\ImageConversionType;
use App\Enums\ImageResizeTypes;
use App\Enums\MediaType;
use App\Events\WorkOrder\PublicWebhook\Lula\MediaDeleteLulaWebhook;
use App\Events\WorkOrder\PublicWebhook\Lula\MediaUploadCompleteLulaWebhook;
use App\Exceptions\IssueException;
use App\Exceptions\NotFoundException\OrganizationNotFoundException;
use App\Exceptions\WorkOrderMediaLimitException;
use App\Helpers\Helper;
use App\Http\Requests\WorkOrder\Media\MediaUploadRequest;
use App\Http\Requests\WorkOrder\Media\ThumbnailUploadRequest;
use App\Http\Resources\WorkOrder\Media\OriginalMediaResource;
use App\Http\Resources\WorkOrder\Media\ThumbnailMediaResource;
use App\Jobs\ImageResizeJob;
use App\Models\Media;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\Models\WorkOrderMedia;
use App\Models\WorkOrderTask;
use Exception;
use Illuminate\Contracts\Filesystem\FileNotFoundException as FilesystemFileNotFoundException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response as mediaResponse;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use phpseclib3\Exception\FileNotFoundException;
use Spatie\FlareClient\Http\Exceptions\NotFound;
use Symfony\Component\HttpFoundation\File\Exception\ExtensionFileException;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Mime\MimeTypes;

class WorkOrderMediaController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(ThumbnailUploadRequest $request, WorkOrder $workOrder): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('create', [WorkOrderMedia::class, $workOrder]);

        try {
            DB::beginTransaction();

            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            $mediaType = $request->get('media_type');
            $issueId = $request->get('issue_id');

            $this->validateMediaLimit($workOrder, $mediaType, $issueId);

            // For uploading media for work order issue.
            if ($request->has('issue_id')) {
                $issue = WorkOrderIssue::whereUuid($request->get('issue_id'))
                    ->select('work_order_issue_id')
                    ->firstOrFail();

                if (empty($issue)) {
                    throw new NotFound(__('Work order issue not found'));
                }

                if (! empty($mediaType) && ! empty($issue)) {
                    if (in_array($mediaType, [MediaType::BEFORE_MEDIA(), MediaType::AFTER_MEDIA()])) {
                        $issue->loadCount([
                            'media' => function ($query) use ($mediaType) {
                                $query->where('has_thumbnail', Boolean::YES())
                                    ->where('media_type', $mediaType);
                            },
                        ]);
                    }

                    $issueMediaCount = $issue->media_count;

                    if ($issueMediaCount >= config('media.work_order_media_limit.work_order')) {
                        throw new WorkOrderMediaLimitException(config('media.work_order_media_limit.work_order'));
                    }
                }
            }

            $uploadedMediaThumbnails = [];
            $path = $organization->getMediaPathPrefix() . "/work-orders/{$workOrder->work_order_uuid}";

            if ($request->has('media')) {
                foreach ($request->media as $key => $media) {
                    $mediaModel = null;

                    if ($request->hasFile("media.{$key}.thumbnail")) {
                        /** @var UploadedFile $file */
                        $file = $request->file("media.{$key}.thumbnail");
                        if (! $file) {
                            throw new FileNotFoundException(__("The file [media.{$key}.thumbnail] not found"));
                        }

                        // Generating a unique name for the original file.
                        $fileName = Str::uuid();

                        // Generating the thumbnail name
                        $thumbnailFileName = $fileName . '_thumbnail'; // Preferred for thumbnail file name

                        // We are assuming that the extension from the request will be the original file extension.
                        $extension = $media['extension'] ?? null;

                        // If we are unable to find the extension from the extension we will try to find it from the file name
                        if (! $extension) {
                            $extension = Str::afterLast($media['file_name'], '.');
                        }

                        // Still no extension, then we will find it from the mime_type that provided in the request.
                        if (! $extension) {
                            $extension = (new MimeTypes)->getExtensions($media['mime_type'])[0] ?? null;
                        }

                        if (! $extension) {
                            throw new ExtensionFileException(__("We couldn't find the file extension"));
                        }

                        // Finding the thumbnail from the file.
                        $thumbnailExtension = $file->guessExtension() ?? $file->getClientOriginalExtension();

                        if (! $thumbnailExtension) {
                            throw new ExtensionFileException(__("We couldn't find the thumbnail file extension"));
                        }

                        // Appending extensions to the file names.
                        $thumbnailFileName .= ".{$thumbnailExtension}";
                        $fileName .= ".{$extension}";

                        // Storing file to s3
                        $uploaded = Storage::putFileAs($path, $file, $thumbnailFileName);

                        if ($uploaded) {
                            $mediaModel = Media::create([
                                'organization_id' => $organization->organization_id,
                                'original_file_name' => $media['file_name'] ?? null,
                                'original_thumbnail_file_name' => $file->getClientOriginalName(),
                                'file_name' => $fileName,
                                'thumbnail_file_name' => $thumbnailFileName,
                                'mime_type' => $media['mime_type'] ?? null,
                                'size' => $media['size'] ?? 0,
                                'extension' => $extension,
                                'thumbnail_extension' => $thumbnailExtension,
                            ]);

                            WorkOrderMedia::create([
                                'media_id' => $mediaModel->media_id,
                                'organization_id' => $organization->organization_id,
                                'work_order_id' => $workOrder->work_order_id,
                                'user_id' => $request->user()?->user_id,
                                'media_type' => $mediaType,
                                'has_thumbnail' => Boolean::YES(),
                            ]);
                        }
                    }

                    $uploadedMediaThumbnails[] = [
                        'media_uuid' => $media['thumbnail_id'],
                        'is_uploaded' => ! empty($mediaModel),
                        'stored_media_uuid' => $mediaModel->media_uuid ?? null,
                        'thumbnail_url' => ! empty($mediaModel) ?
                            $mediaModel->getTemporaryMediaUrl(type: ImageConversionType::THUMBNAIL()) : null,
                        'media_type' => $mediaType,
                    ];
                }
            }

            DB::commit();

            return ThumbnailMediaResource::collection($uploadedMediaThumbnails);
        } catch (OrganizationNotFoundException|WorkOrderMediaLimitException|IssueException|FileNotFoundException|NotFound|ExtensionFileException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Upload Work order Thumbnail Media API Failed');

            return Response::unprocessableEntity(message: __($exception->getMessage()));
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Upload Work order Thumbnail Media API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Return work order media
     */
    public function show(Request $request, WorkOrder $workOrder, WorkOrderTask $workOrderTask, Media $media): JsonResponse|mediaResponse
    {
        try {
            $workOrder->loadMissing('organization');

            if (empty($workOrder->organization)) {
                throw new OrganizationNotFoundException;
            }

            $headers = [
                'Content-Type' => 'image/jpeg',
            ];

            $fileName = $media->thumbnail_file_name;

            if ($request->type === 'original') {
                $fileName = $media->file_name;
                $headers['Content-Type'] = $media->mime_type;
            }

            $mediaPath = $media->getBasePath($workOrder->organization, $workOrder->work_order_uuid) . "/{$fileName}";

            if (! Storage::exists($mediaPath)) {
                throw new FilesystemFileNotFoundException;
            }

            return Response::make(Storage::get($mediaPath), 200, $headers);
        } catch (OrganizationNotFoundException|FilesystemFileNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work order media content fetch api failed due to ' . get_class($exception));

            return Response::notFound(message: __('Media not found'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work order media content fetch api failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, WorkOrder $workOrder, Media $media): JsonResponse
    {
        $this->authorize('delete', [$media->workOrderMedia, $workOrder]);

        try {
            DB::beginTransaction();

            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            if (empty($media->workOrderMedia)) {
                throw new ModelNotFoundException(__('Media not found.'));
            }

            $media->load('workOrderMedia.quoteTask');

            $media->workOrderMedia?->delete();
            $media->delete();

            DB::commit();
            event(new MediaDeleteLulaWebhook($media, $workOrder));

            return Response::message(message: __('Work order media deleted successfully.'));
        } catch (OrganizationNotFoundException|ModelNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Delete Work Order Media api Failed due to ' . get_class($exception));

            return Response::unprocessableEntity(message: __($exception->getMessage()));
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Delete Work Order Media api Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Upload Original Work order media
     */
    public function uploadOriginalMedia(MediaUploadRequest $request, WorkOrder $workOrder, Media $media): OriginalMediaResource|JsonResponse
    {
        $this->authorize('create', [WorkOrderMedia::class, $workOrder]);

        try {

            DB::beginTransaction();

            $organization = $request->user()?->organization;
            $workOrder->loadMissing('tasks', 'tasks.serviceCalls');

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            if (empty($media->workOrderMedia)) {
                throw new ModelNotFoundException(__('Media not found.'));
            }

            $path = $organization->getMediaPathPrefix() . "/work-orders/{$workOrder->work_order_uuid}";
            $file = $request->file('media');

            if (! $file) {
                throw new FileNotFoundException(__('The file [media] not found'));
            }
            if (is_array($file)) {
                throw new UnprocessableEntityHttpException(__('Multiple files not support.'));
            }

            $fileExtension = $file->getClientOriginalExtension() ?? $file->guessExtension();

            if (! $fileExtension) {
                throw new ExtensionFileException(__("We couldn't find the thumbnail file extension"));
            }

            // Comparig media extension with uploading file extension.
            if ($media->extension != $fileExtension) {
                throw new ExtensionFileException(__("Uploading file extension don't match"));
            }

            // Storing file to s3
            $uploaded = Storage::putFileAs($path, $file, $media->file_name);
            $media->workOrderMedia->has_upload_completed = Boolean::YES();
            $media->workOrderMedia->save();

            //Media type image can only be optimized
            if (! empty($media->mime_type) && str_starts_with($media->mime_type, 'image/')) {
                dispatch(new ImageResizeJob($media, [ImageResizeTypes::OPTIMIZED()]))->afterCommit()->onQueue('attachment');
            }

            DB::commit();

            event(new MediaUploadCompleteLulaWebhook($media->media_id));

            return new OriginalMediaResource($media->workOrderMedia);
        } catch (OrganizationNotFoundException|ModelNotFoundException|ExtensionFileException|FileNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Delete Work Order Media api Failed due to ' . get_class($exception));

            return Response::unprocessableEntity(message: __($exception->getMessage()));
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Upload Original Work Order Media api Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Upload media limit validation
     */
    public function validateMediaLimit(WorkOrder $workOrder, ?string $mediaType, ?string $issueId): void
    {
        if (! $mediaType) {
            // Work order media limit validation.
            $workOrder->loadCount([
                'media' => function ($query) {
                    $query->where('has_thumbnail', Boolean::YES())
                        ->whereNull('media_type');
                },
            ]);

            $workOrderMediaCount = $workOrder->media_count;
            if ($workOrderMediaCount >= config('media.work_order_media_limit.work_order')) {
                throw new WorkOrderMediaLimitException(config('media.work_order_media_limit.work_order'));
            }
        }

        // Work order issue before after media limit validation.
        if (! empty($mediaType) && ! empty($issueId) && in_array($mediaType, [MediaType::BEFORE_MEDIA(), MediaType::AFTER_MEDIA()])) {
            $workOrderIssue = WorkOrderIssue::select(['work_order_issue_id', 'work_order_id'])
                ->withCount([
                    'media' => function ($query) use ($mediaType) {
                        $query->where('has_thumbnail', Boolean::YES())
                            ->where('media_type', $mediaType);
                    },
                ])
                ->whereUuid($issueId)
                ->firstOrFail();

            $workOrderIssueMediaCount = $workOrderIssue->media_count;
            $limit = config('media.work_order_media_limit.work_order');

            if ($workOrderIssueMediaCount >= $limit) {
                throw IssueException::mediaValidation($mediaType, $limit);
            }
        }
    }
}

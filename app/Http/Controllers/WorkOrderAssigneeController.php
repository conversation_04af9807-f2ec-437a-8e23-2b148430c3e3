<?php

namespace App\Http\Controllers;

use App\Enums\UserTypes;
use App\Events\WorkOrder\WorkOrderAssigneeAdded;
use App\Events\WorkOrder\WorkOrderAssigneeRemoved;
use App\Events\WorkOrder\WorkOrderAssigneeUpdated;
use App\Exceptions\WorkOrderAssigneeException;
use App\Helpers\Helper;
use App\Http\Resources\WorkOrder\WorkOrderAssigneeResource;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderAssignee;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;

class WorkOrderAssigneeController extends Controller
{
    //store work order assignees
    public function store(Request $request, WorkOrder $workOrder, User $assignee): WorkOrderAssigneeResource|JsonResponse
    {
        $this->authorize('manageAssignee', $workOrder);
        try {

            if ($assignee->user_type !== UserTypes::ACCOUNT_USER()) {
                throw WorkOrderAssigneeException::invalidUserType();
            }

            if ($assignee->organization_id !== $workOrder->organization_id) {
                throw WorkOrderAssigneeException::organizationMismatch();
            }

            if (WorkOrderAssignee::where('work_order_id', $workOrder->work_order_id)
                ->where('user_id', $assignee->user_id)
                ->exists()) {
                throw WorkOrderAssigneeException::alreadyExisting();
            }

            $workOrderAssignee = WorkOrderAssignee::updateOrCreate([
                'work_order_id' => $workOrder->work_order_id,
                'user_id' => $assignee->user_id,
            ], [
                'work_order_id' => $workOrder->work_order_id,
                'user_id' => $assignee->user_id,
                'action_by_user_id' => $request->user()?->user_id,
            ]);
            /** @var User $authUser */
            $authUser = $request->user();

            WorkOrderAssigneeUpdated::broadcast($workOrder, $assignee, 'add')->toOthers();
            // If self assign we prevent the notification
            if ($authUser->user_id !== $assignee->user_id) {
                $workOrder->load([
                    'tasks' => function ($query) {
                        return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                            ->with([
                                'problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label',
                                'problemDiagnosis.subCategory:problem_sub_category_id,problem_sub_category_uuid,problem_category_id,label',
                                'problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
                            ]);
                    },
                ]);
                WorkOrderAssigneeAdded::dispatch($workOrder, $assignee, $authUser);
            }

            return new WorkOrderAssigneeResource($workOrderAssignee);
        } catch (WorkOrderAssigneeException $exception) {
            return Response::unprocessableEntity($exception->getMessage());
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Invalid request data.');

            return Response::unprocessableEntity(__('Invalid request data.'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Add work order assignee api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __($exception->getMessage()));
        }
    }

    /**
     * Destroy the work order assignee
     */
    public function destroy(Request $request, WorkOrder $workOrder, User $assignee): JsonResponse
    {
        $this->authorize('manageAssignee', $workOrder);
        try {
            $workOrderAssignee = WorkOrderAssignee::where([
                'work_order_id' => $workOrder->work_order_id,
                'user_id' => $assignee->user_id,
            ])->first();

            if (empty($workOrderAssignee)) {
                throw WorkOrderAssigneeException::assigneeNotFound();
            }
            $workOrderAssignee->removed_by_user_id = $request->user()?->user_id;
            $workOrderAssignee->save();

            $workOrderAssignee->delete();
            WorkOrderAssigneeUpdated::broadcast($workOrder, $assignee, 'delete')->toOthers();
            WorkOrderAssigneeRemoved::dispatch($workOrder, $assignee);

            return Response::message(message: __('Work order assignee deleted successfully.'));
        } catch (WorkOrderAssigneeException $exception) {
            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (ModelNotFoundException $exception) {
            Helper::exceptionLog($exception, message: __('Work order assignee delete[ModelNotFoundException]'));

            return Response::notFound();
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order assignee Delete API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }
}

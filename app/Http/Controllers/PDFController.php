<?php

namespace App\Http\Controllers;

use App\Enums\Boolean;
use App\Enums\ImageConversionType;
use App\Enums\InvoiceLineItemTypes;
use App\Enums\InvoiceSubsidiaryTypes;
use App\Enums\MediaType;
use App\Enums\QuoteStatus;
use App\Enums\QuoteTaskStatus;
use App\Enums\ScheduleTypes;
use App\Helpers\Helper;
use App\Models\Invoice;
use App\Models\Media;
use App\Models\Organization;
use App\Models\Property;
use App\Models\Quote;
use App\Models\QuoteTask;
use App\Models\QuoteTaskMaterial;
use App\Services\Vendor\Enum\Service;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Carbon\CarbonImmutable;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Fluent;
use Illuminate\Support\Str;

class PDFController extends Controller
{
    public function __construct()
    {
        ini_set('max_execution_time', 300);
        ini_set('memory_limit', '512M');
    }

    public function viewQuote(Quote $quote): Response
    {
        $quote->loadMissing(
            'workOrder:work_order_id,work_order_uuid,organization_id,requesting_resident_id,due_date,timezone_id,work_order_number',
            'workOrder.organization:organization_id,domain,street_address,city,zip_code,state_id,logo_file_name,phone_number',
            'workOrder.timezone:timezone_id,name',
            'workOrder.tasks:work_order_id,work_order_task_id,problem_diagnosis_id',
            'workOrder.tasks.diagnosis:problem_diagnosis_id,problem_sub_category_id,label',
            'workOrder.tasks.diagnosis.subCategory:problem_sub_category_id,problem_category_id,label',
            'workOrder.tasks.diagnosis.subCategory.problemCategory:problem_category_id,label',
            'workOrder.resident.property:property_id,street_address,unit_number,city,state_id,postal_zip_code',
            'workOrder.resident:resident_id,first_name,last_name,phone_number,property_id',
            'quoteTasks:quote_task_id,quote_id,quote_task_number,total_cost_in_cents,status,description',
            'quoteTasks.quoteTaskMaterials:quote_task_id,total_cost_in_cents,quantity,label',
        );

        $quote->loadMissing(['quoteTasks.media' => function ($query) {
            $query->wherePivot('has_upload_completed', Boolean::YES())
                ->select(
                    'media.media_id', 'media.media_uuid', 'media.file_name', 'media.optimized_file_name',
                    'media.thumbnail_file_name', 'media.mime_type', 'media.extension'
                );
        }]);

        $workOrder = $quote->workOrder;
        $resident = $quote->workOrder?->resident ?? null;
        $workOrderTask = $quote->workOrder?->tasks->first();

        /** @var Organization $organization */
        $organization = $workOrder?->organization;

        $quote->unsetRelation('workOrder');
        $workOrder?->unsetRelation('organization');

        $timezone = $workOrder->timezone->name ?? config('settings.default_timezone');

        $quoteData = new Fluent([
            'status' => QuoteStatus::label(status: $quote->status, source: 'web'),
            'status_slug' => $quote->status,
            'submitted_at' => $quote->submitted_at ? $quote->submitted_at->setTimezone($timezone)->format('d M, Y') : '',
            'quote_number' => $quote->quote_number,
            'estimated_amount_label' => match ($quote->status) {
                QuoteStatus::PENDING_APPROVAL() => 'Quote Estimate (USD)',
                default => 'Approved Estimate (USD)'
            },
            'status_class' => match ($quote->status) {
                QuoteStatus::APPROVED() => 'success',
                QuoteStatus::REJECTED() => 'error',
                default => 'pending'
            },
            'line_item_total_amount_label' => match ($quote->status) {
                QuoteStatus::PENDING_APPROVAL() => 'Quote Estimate',
                QuoteStatus::REJECTED() => 'Total',
                default => 'Approved Estimate'
            },
            'line_item_total_amount_label_class' => match ($quote->status) {
                QuoteStatus::REJECTED() => 'value-disabled',
                default => ''
            },
            'estimated_amount' => match ($quote->status) {
                QuoteStatus::QUOTE_PENDING_REVIEW() => array_sum(
                    [
                        $quote->quoteTasks->where('status', QuoteTaskStatus::QUOTE_PENDING_REVIEW())->sum('total_cost_in_cents'),
                        $quote->quoteTasks->where('status', QuoteTaskStatus::QUOTE_PENDING_REVIEW())
                            ->pluck('quoteTaskMaterials')->collapse()->flatten()->sum('total_cost_in_cents'),
                    ]
                ),
                QuoteStatus::PENDING_APPROVAL() => array_sum(
                    [
                        $quote->quoteTasks->where('status', QuoteTaskStatus::PENDING_DECISION())->sum('total_cost_in_cents'),
                        $quote->quoteTasks->where('status', QuoteTaskStatus::PENDING_DECISION())
                            ->pluck('quoteTaskMaterials')->collapse()->flatten()->sum('total_cost_in_cents'),
                    ]
                ),
                QuoteStatus::APPROVED(), QuoteStatus::PARTIALLY_APPROVED() => array_sum(
                    [
                        $quote->quoteTasks->whereIn('status', [QuoteTaskStatus::APPROVED()])->sum('total_cost_in_cents'),
                        $quote->quoteTasks->whereIn('status', [QuoteTaskStatus::APPROVED()])
                            ->pluck('quoteTaskMaterials')->collapse()->flatten()->sum('total_cost_in_cents'),
                    ]
                ),
                default => 0.00
            },
            'problem' => new Fluent([
                'category' => $workOrderTask->diagnosis->subCategory->problemCategory->label ?? '',
                'sub_category' => $workOrderTask->diagnosis->subCategory->label ?? '',
                'diagnosis' => $workOrderTask->diagnosis->label ?? '',
            ]),
            'work_order' => new Fluent([
                'due_date' => ! empty($workOrder->due_date) ? CarbonImmutable::parse($workOrder->due_date)->format('d M, Y') : '-',
                'work_order_number' => $workOrder->work_order_number ?? '',
            ]),
            'organization' => new Fluent([
                'logo_uri' => $organization->logoUrl(),
                'address' => implode(', ', array_filter([$organization->street_address, $organization->city, ($organization->state->state_code ?? '') . " {$organization->zip_code}"])),
                'phone_number' => ! empty($organization->phone_number) ? Helper::displayPhoneNumber($organization->phone_number) : '',
            ]),
            'resident' => new Fluent([
                'name' => $resident?->getName(),
                'address' => implode(', ', array_filter(
                    Arr::only(
                        $resident?->property->getAddress() ?? [],
                        ['street_address', 'city', 'state_code', 'zip_code']
                    )
                )),
                'phone_number' => $resident->phone_number ?? '',
            ]),
            'quoteTasks' => clone $quote->quoteTasks->map(function (QuoteTask $quoteTask) use ($workOrder) {
                return new Fluent([
                    'description' => $quoteTask->description,
                    'number' => $quoteTask->quote_task_number,
                    'labor_amount' => $quoteTask->total_cost_in_cents,
                    'status' => QuoteTaskStatus::label($quoteTask->status),
                    'status_slug' => $quoteTask->status,
                    'status_class' => match ($quoteTask->status) {
                        QuoteTaskStatus::APPROVED() => 'success',
                        QuoteTaskStatus::REJECTED() => 'error',
                        default => 'pending'
                    },
                    'value_class' => match ($quoteTask->status) {
                        QuoteTaskStatus::REJECTED() => 'value-disabled',
                        default => ''
                    },
                    'media' => $quoteTask->media->filter(
                        fn (Media $media) => Str::startsWith(Str::lower($media->mime_type ?? ''), 'image')
                    )->map(
                        fn (Media $media) => $workOrder ? [
                            'uri' => URL::temporarySignedRoute('public.media', now()->addSeconds(config('media.cache.temporary_file_cache_expire_in_seconds')), [
                                $media->media_uuid,
                                ! empty($media->pivot) && $media->pivot->has_optimized === Boolean::YES() ? ImageConversionType::OPTIMIZED() : ImageConversionType::ORIGINAL(),
                            ]),
                            'extension' => $media->extension,
                        ] : null
                    ),
                    'materials' => clone $quoteTask->quoteTaskMaterials->map(function (QuoteTaskMaterial $material) {
                        return new Fluent([
                            'label' => $material->label,
                            'amount' => $material->total_cost_in_cents,
                            'quantity' => $material->quantity,
                        ]);
                    }),
                ]);
            }),
        ]);

        $pdfName = "quote-{$quote->quote_number}-wo-{$workOrder?->work_order_number}";

        return SnappyPdf::loadView('pdf.quote', ['quoteData' => $quoteData])
            ->setOption('zoom', '.9')
            ->inline($pdfName);
    }

    /**
     * Fetch invoice details
     */
    public function viewInvoice(Invoice $invoice): Response
    {
        $invoice->load([
            'workOrder:work_order_id,work_order_number,organization_id,timezone_id,due_date,property_id,work_order_uuid,requesting_resident_id,description' => [
                'timezone',
                'organization:organization_id,organization_uuid,logo_file_name,street_address,city,zip_code,phone_number,domain,state_id' => [
                    'state:state_id,state_code',
                ],
                'tasks:work_order_task_id,work_order_id,problem_diagnosis_id' => [
                    'diagnosis:problem_diagnosis_id,problem_sub_category_id,label' => [
                        'subCategory:problem_sub_category_id,problem_category_id,label' => [
                            'problemCategory:problem_category_id,label',
                        ],
                    ],
                ],
                'resident:resident_id,first_name,last_name,phone_number,property_id' => [
                    'property:property_id,property_name,street_address,city,state_id,postal_zip_code' => [
                        'state:state_id,state_code',
                    ],
                ],
                'property:property_id,property_name,street_address,city,state_id,postal_zip_code' => [
                    'state:state_id,state_code',
                ],
            ],
            'lineItems:invoice_line_item_id,invoice_id,invoice_line_item_type,work_order_service_call_id,quote_id,quote_task_id,description,total_cost_in_cents' => [
                'subsidiaries:work_order_task_material_id,invoice_line_item_id,subsidiary_type,total_cost_in_cents,quantity,description',
                'quoteTask:quote_task_id,quote_task_number',
                'serviceCall:work_order_service_call_id,work_order_service_call_number,scheduled_start_time,lula_appointment_id' => [
                    'media' => function ($query) {
                        return $query->with([
                            'workOrder:work_orders.work_order_id,work_orders.organization_id,work_orders.work_order_uuid',
                            'workOrder.organization:organizations.organization_id,organizations.domain',
                        ])->wherePivot('has_thumbnail', Boolean::YES())
                            ->whereIn('media_type', [MediaType::AFTER_MEDIA, MediaType::BEFORE_MEDIA]);
                    },
                    'lulaAppointment:lula_appointment_id,service_category_label',
                ],
            ],
        ]);

        $workOrder = $invoice->workOrder;
        $resident = $invoice->workOrder?->resident ?? null;
        $workOrderTask = $invoice->workOrder->tasks->first() ?? null;

        /** @var Organization $organization */
        $organization = $workOrder->organization;

        /** @var Property $property */
        $property = $workOrder->property;

        $lineItems = $invoice->lineItems;

        $invoice->unsetRelation('workOrder');
        $workOrder->unsetRelation('organization');
        $workOrder->unsetRelation('property');
        $invoice->unsetRelation('lineItems');

        $invoiceData = new Fluent([
            'problem' => new Fluent([
                'category' => $workOrderTask->diagnosis->subCategory->problemCategory->label ?? '',
                'sub_category' => $workOrderTask->diagnosis->subCategory->label ?? '',
                'diagnosis' => $workOrderTask->diagnosis->label ?? '',
            ]),
            'work_order' => new Fluent([
                'due_date' => $workOrder->due_date ?? '',
                'work_order_number' => $workOrder->work_order_number ?? '',
                'description' => $invoice->description ?? '',
                'timezone' => $workOrder->timezone->name,
            ]),
            'invoice' => new Fluent([
                'invoice_number' => $invoice->invoice_number,
                'invoice_date' => $invoice->created_at ?? '',
                'amount_due' => $invoice->total_cost_in_cents ?? 0,
            ]),
            'organization' => new Fluent([
                'logo_uri' => $organization->logoUrl(),
                'address' => implode(', ', array_filter([$organization->street_address, $organization->city, ($organization->state->state_code ?? '') . " {$organization->zip_code}"])),
                'phone_number' => ! empty($organization->phone_number) ? Helper::displayPhoneNumber($organization->phone_number) : '',
            ]),
            'property' => new Fluent([
                'name' => $property->property_name,
                'street_address' => $property->street_address,
                'address' => implode(', ', array_filter(
                    Arr::only(
                        $property->getAddress() ?? [],
                        ['city', 'state_code']
                    )
                )),
                'zip_code' => $property->postal_zip_code,
                'phone_number' => ! empty($property->phone_number) ? Helper::displayPhoneNumber($property->phone_number) : '',
            ]),
            'resident' => new Fluent([
                'name' => $resident?->getName(),
                'street_address' => $resident?->property->street_address,
                'address' => implode(', ', array_filter(
                    Arr::only(
                        $resident?->property->getAddress() ?? [],
                        ['city', 'state_code']
                    )
                )),
                'zip_code' => $resident?->property->postal_zip_code,
                'phone_number' => ! empty($resident->phone_number) ? Helper::displayPhoneNumber($resident->phone_number) : '',
            ]),
        ]);

        $invoiceDetails = null;
        $images = null;

        // Line items grouped by trip
        $hourlyTriplineItems = $lineItems->whereNull('quote_id')->whereNull('quote_task_id');
        $quoteTriplineItems = $lineItems->filter(function ($lineItem) {
            return ! empty($lineItem->quote_id) || ! empty($lineItem->quote_task_id);
        });

        // Trip related invoice details
        if ($hourlyTriplineItems->isNotEmpty()) {
            $hourlyTriplineItems = $hourlyTriplineItems->groupBy('work_order_service_call_id');
            foreach ($hourlyTriplineItems as $triplineItems) {
                $serviceCall = $triplineItems->pluck('serviceCall')->unique('work_order_service_call_id')->first();
                // Quote trip number
                $tripNumber = $serviceCall->work_order_service_call_number;
                $lineTotal = $triplineItems->sum('total_cost_in_cents') ?? 0;
                $invoiceDetails[$tripNumber]['scheduled_start_time'] = $serviceCall?->scheduled_start_time;
                // Trip labor cost in cents
                $laborCostInCents = $triplineItems->whereIn('invoice_line_item_type', InvoiceLineItemTypes::hourlyTypes()->toArray())
                    ->sum('total_cost_in_cents') ?? 0;
                // Trip material cost in cents
                $materialCostInCents = $triplineItems->where('invoice_line_item_type', InvoiceLineItemTypes::MATERIAL())
                    ->sum('total_cost_in_cents') ?? 0;

                $invoiceDetails[$tripNumber]['labor_cost_in_cents'] = $laborCostInCents;
                $invoiceDetails[$tripNumber]['material_cost_in_cents'] = $materialCostInCents;
                $invoiceDetails[$tripNumber]['line_total'] = $lineTotal;

                if (! empty($serviceCall->lulaAppointment)) {
                    $invoiceDetails[$tripNumber]['trip_type'] = ScheduleTypes::LULA_PRO();
                    $invoiceDetails[$tripNumber]['service_category'] = $serviceCall->lulaAppointment->service_category_label ?? '-';
                } else {
                    $invoiceDetails[$tripNumber]['trip_type'] = ScheduleTypes::IN_HOUSE();
                }

                foreach ($triplineItems as $triplineItem) {
                    if (InvoiceLineItemTypes::hourlyTypes()->contains($triplineItem->invoice_line_item_type)) {
                        $invoiceDetails[$tripNumber]['line_item_type'] = $triplineItem->invoice_line_item_type;
                        $invoiceDetails[$tripNumber]['service_notes'] = $triplineItem->description;
                    }

                    if ($triplineItem->invoice_line_item_type == InvoiceLineItemTypes::MATERIAL()) {
                        foreach ($triplineItem->subsidiaries as $subsidiary) {
                            // Trip materials invoice details
                            if ($triplineItem->invoice_line_item_type == InvoiceLineItemTypes::MATERIAL()) {
                                $invoiceDetails[$tripNumber]['materials'][] = [
                                    'cost' => $subsidiary->total_cost_in_cents,
                                    'quantity' => $subsidiary->quantity,
                                    'description' => $subsidiary->description,
                                ];
                            }
                        }
                    }
                }
            }
        }

        // Quote related invoice details
        if ($quoteTriplineItems->isNotEmpty()) {
            $quoteTriplineItems = $quoteTriplineItems->groupBy('work_order_service_call_id');
            foreach ($quoteTriplineItems as $triplineItems) {
                $serviceCall = $triplineItems->pluck('serviceCall')->unique('work_order_service_call_id')->first();
                $lineItemSubsidiaries = $triplineItems->pluck('subsidiaries')->flatten();

                // Quote trip number
                $quoteTripNumber = $serviceCall->work_order_service_call_number;
                // Quote trip labor cost in cents
                $laborCostInCents = $lineItemSubsidiaries->where('subsidiary_type', InvoiceSubsidiaryTypes::LABOR())
                    ->sum('total_cost_in_cents') ?? 0;
                // Quote trip material cost in cents
                $materialCostInCents = $lineItemSubsidiaries->where('subsidiary_type', InvoiceSubsidiaryTypes::MATERIAL())
                    ->sum('total_cost_in_cents') ?? 0;
                // Quote trip line total
                $quoteLineTotal = $triplineItems->sum('total_cost_in_cents') ?? 0;

                $invoiceDetails[$quoteTripNumber]['scheduled_start_time'] = $serviceCall?->scheduled_start_time;
                $invoiceDetails[$quoteTripNumber]['labor_cost_in_cents'] = $laborCostInCents;
                $invoiceDetails[$quoteTripNumber]['material_cost_in_cents'] = $materialCostInCents;
                $invoiceDetails[$quoteTripNumber]['line_total'] = $quoteLineTotal;

                foreach ($triplineItems as $triplineItem) {
                    // Quote trip service notes
                    if ($triplineItem->invoice_line_item_type == InvoiceLineItemTypes::SERVICE_NOTE()) {
                        $invoiceDetails[$quoteTripNumber]['service_notes'] = $triplineItem->description;

                        continue;
                    }
                    if ($triplineItem->invoice_line_item_type == InvoiceLineItemTypes::QUOTE_TASK()) {
                        $invoiceDetails[$quoteTripNumber]['line_item_type'] = $triplineItem->invoice_line_item_type;
                        // Quote tasks details
                        $invoiceDetails[$quoteTripNumber]['quote_tasks'][$triplineItem->quoteTask?->quote_task_number] = [
                            'description' => $triplineItem->description,
                            'labor_cost_in_cents' => $triplineItem->subsidiaries->where('subsidiary_type', InvoiceSubsidiaryTypes::LABOR())
                                ->sum('total_cost_in_cents') ?? 0,
                        ];

                        if ($triplineItem->subsidiaries->isNotEmpty()) {
                            foreach ($triplineItem->subsidiaries as $subsidiary) {
                                // Quote tasks material details
                                if ($subsidiary->subsidiary_type == InvoiceSubsidiaryTypes::MATERIAL()) {
                                    $invoiceDetails[$quoteTripNumber]['quote_tasks'][$triplineItem->quoteTask?->quote_task_number]['materials'][] = [
                                        'cost' => $subsidiary->total_cost_in_cents,
                                        'quantity' => $subsidiary->quantity,
                                        'description' => $subsidiary->description,
                                    ];
                                }
                            }
                        }
                    }
                }
            }
        }

        // Trip media
        $trips = $lineItems->pluck('serviceCall')->unique('work_order_service_call_id')->sortBy('work_order_service_call_number');
        if ($trips->isNotEmpty()) {
            foreach ($trips as $trip) {
                $tripNumber = $trip->work_order_service_call_number;
                $tripMedia = $trip->media->unique('media_id')->flatten();
                if ($tripMedia->isNotEmpty()) {
                    $images[$tripNumber] = [
                        'trip_number' => $trip->work_order_service_call_number,
                        'scheduled_start_time' => $trip->scheduled_start_time,
                    ];
                    foreach ($tripMedia as $media) {
                        if (Str::startsWith(Str::lower($media->mime_type ?? ''), 'image')) {
                            $images[$tripNumber][$media->pivot->media_type][] = [
                                'extension' => $media->extension,
                                'media_type' => $media->pivot->media_type,
                                'url' => $media->getTemporaryMediaUrl(
                                    type: $media->pivot->has_optimized === Boolean::YES() ? ImageConversionType::OPTIMIZED() : ImageConversionType::ORIGINAL()
                                ),
                            ];
                        }
                    }
                }
            }
        }

        $pdfName = "invoice-{$invoice->invoice_number}-wo-{$workOrder->work_order_number}";

        return SnappyPdf::setOption('zoom', '.9')->loadView('pdf.invoice', [
            'invoiceData' => $invoiceData, 'invoiceDetails' => $invoiceDetails, 'images' => $images,
        ])->inline($pdfName);
    }
}

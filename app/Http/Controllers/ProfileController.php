<?php

namespace App\Http\Controllers;

use App\Http\Resources\User\ProfileResource;
use App\Models\User;
use Illuminate\Http\Request;

class ProfileController extends Controller
{
    /**
     * Fetch user details
     */
    public function profile(Request $request): ProfileResource
    {
        /** @var User $user */
        $user = $request->user();
        $user->loadCount('unClearedUnReadNotifications');
        $user->loadMissing('timezone', 'organization', 'technician');

        return new ProfileResource($user);
    }
}

<?php

namespace App\Http\Controllers;

use App\Enums\UserStatus;
use App\Enums\UserTypes;
use App\Enums\ViewTypes;
use App\Events\User\DefaultPinnedQuotesView;
use App\Events\User\UserInvited;
use App\Exceptions\AWS\Cognito\CognitoAPIFailedException;
use App\Exceptions\GroupingExceptions\UserGroupingException;
use App\Exceptions\NotFoundException\OrganizationNotFoundException;
use App\Exceptions\NotFoundException\OwnerNotExistsException;
use App\Helpers\Helper;
use App\Http\Filters\UserListFilter;
use App\Http\Requests\User\FilterValuesRequest;
use App\Http\Requests\User\GroupViewRequest;
use App\Http\Requests\User\ListRequest;
use App\Http\Requests\User\StoreRequest;
use App\Http\Requests\User\UpdateRequest;
use App\Http\Resources\User\AccountInfoResource;
use App\Http\Resources\User\Group\RoleBasedGroupDataResource;
use App\Http\Resources\User\Group\StatusBasedGroupDataResource;
use App\Http\Resources\User\UserListResource;
use App\Http\Resources\User\UserResource;
use App\Http\Resources\User\UserRoleResource;
use App\Http\Resources\User\UserStatusResource;
use App\Models\Country;
use App\Models\Organization;
use App\Models\Role;
use App\Models\State;
use App\Models\Timezone;
use App\Models\User;
use App\Models\UserPinnedView;
use App\Models\View;
use App\Models\ViewType;
use App\Traits\UserListFilterTrait;
use App\Traits\ViewTrait;
use Aws\CognitoIdentityProvider\CognitoIdentityProviderClient;
use Aws\CognitoIdentityProvider\Exception\CognitoIdentityProviderException;
use Carbon\Carbon;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use InvalidArgumentException;
use LogicException;
use RuntimeException;
use Throwable;
use UnexpectedValueException;

class UserController extends Controller
{
    use UserListFilterTrait, ViewTrait;

    protected CognitoIdentityProviderClient $cognitoClient;

    /**
     * UserController constructor
     */
    public function __construct()
    {
        $region = config('services.cognito.region');

        // Create a CognitoIdentityProviderClient object
        $this->cognitoClient = new CognitoIdentityProviderClient([
            'region' => $region,
            'version' => 'latest',
        ]);
    }

    /**
     * List all users
     */
    public function index(ListRequest $request, UserListFilter $filter): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('viewAny', User::class);

        try {
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            $users = User::with(['roles:role_uuid,role_id,name', 'technician'])
                ->leftJoin('user_role', 'user_role.user_id', 'users.user_id')
                ->leftJoin('roles', function (JoinClause $joinQuery) use ($organization) {
                    $joinQuery->on('roles.role_id', 'user_role.role_id')
                        ->where('roles.organization_id', $organization->organization_id);
                })
                ->select(
                    'users.user_id', 'users.user_uuid', 'users.first_name', 'users.last_name', 'users.middle_name',
                    'users.email', 'users.status', 'users.created_at', 'users.updated_at', 'users.deleted_at', 'users.profile_pic',
                    'users.last_activity_at',
                    DB::raw("GROUP_CONCAT(DISTINCT roles.name ORDER BY roles.name ASC  SEPARATOR ',') AS role_names"),
                    DB::raw("CONCAT(COUNT(DISTINCT roles.role_id),GROUP_CONCAT(DISTINCT roles.name ORDER BY roles.name ASC  SEPARATOR '-')) AS role_name_for_grouping"),
                    DB::raw("GROUP_CONCAT(DISTINCT roles.name ORDER BY roles.name ASC  SEPARATOR '-') AS role_name_slug_for_grouping")
                )
                ->filter($filter)
                ->orderBy('users.user_id')
                ->groupBy('users.user_id')
                ->paginate(perPage: $request->input('per_page', config('pagination.user_list.per_page')));

            return UserListResource::collection($users);
        } catch (OrganizationNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User listing api failed[OrganizationNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User listing api failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * @throws AuthorizationException
     */
    public function store(StoreRequest $request): JsonResource|JsonResponse
    {
        $this->authorize('create', User::class);

        $email = $request->input('email');
        $name = trim($request->input('first_name') . ' ' . $request->input('last_name'));

        $password_combination_array = [
            Str::password(6, numbers: false, symbols: false), '@', random_int(0, 9999),
        ];
        $password = implode('', $password_combination_array);

        $attributes = [
            [
                'Name' => 'email',
                'Value' => $email,
            ],
            [
                'Name' => 'name',
                'Value' => $name,
            ],
            [
                'Name' => 'email_verified',
                'Value' => 'True',
            ],
            // Add any additional attributes here
        ];

        DB::beginTransaction();

        try {
            $organization = Organization::where('organization_id', $request->attributes->get('organization_id'))
                ->first();

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            $adminCreateUser = [
                'UserPoolId' => $organization->user_pool_id,
                'Username' => $email,
                'TemporaryPassword' => $password,
                'UserAttributes' => $attributes,
                'DesiredDeliveryMediums' => ['EMAIL'],
                'ForceAliasCreation' => true,
            ];

            if (! $request->input('notify_user', true)) {
                $adminCreateUser['MessageAction'] = 'SUPPRESS';
            }

            $result = $this->cognitoClient->adminCreateUser($adminCreateUser);

            if (empty($result)) {
                throw new CognitoAPIFailedException(__('User pool creation failed.'));
            }
            $userData = $result->get('User');

            $roles = Role::whereUuid($request->input('roles'))->get(['role_id']);

            $user = User::create([
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'email' => $email,
                'cognito_user_id' => $userData['Username'] ?? '',
                'cognito_user_name' => $userData['Username'] ?? '',
                'organization_id' => $organization->organization_id,
                'country_id' => Country::where('alpha3_code', 'USA')->firstOrFail(['country_id'])->country_id,
                'timezone_id' => Timezone::where('name', config('settings.default_timezone'))->first()?->timezone_id,
            ]);

            $user->assignRole($roles->pluck('role_id')->all());

            // Add unhealthy work order for user as pinned view (this is pinned to user as default but unpinnable)
            $viewType = ViewType::select('view_type_id')
                ->where('slug', ViewTypes::WORK_ORDERS())
                ->first();

            $unhealthyWO = View::withoutGlobalScope('organization')
                ->select('view_id')
                ->where('view_type_id', $viewType?->view_type_id)
                ->where('scope', 'global')
                ->where('name', 'Unhealthy WOs')
                ->first();

            UserPinnedView::updateOrCreate([
                'user_id' => $user->user_id,
                'view_id' => $unhealthyWO?->view_id,
            ]);

            DB::commit();

            UserInvited::dispatch($user);
            DefaultPinnedQuotesView::dispatch($user);

            return new JsonResource([
                'user_id' => $user->user_id,
                'name' => $user->first_name . ' ' . $user->last_name,
                'email' => $user->email,
                'cognito_user_id' => $user->cognito_user_id,
                'temporary_password' => $password,
            ]);
        } catch (CognitoIdentityProviderException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'User store api failed[CognitoIdentityProviderException]');

            // If the token is invalid, the getUser API will throw an exception
            return Response::unprocessableEntity(message: $exception->getAwsErrorMessage());
        } catch (OrganizationNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'User store api failed[OrganizationNotFoundException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (QueryException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'User store api failed[QueryException]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, unable to create user.'));
        } catch (CognitoAPIFailedException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'User store api failed[CognitoAPIFailedException]', notify: true);

            return Response::internalServerError(message: $exception->getMessage());
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'User store api failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Show user details
     *
     * @throws AuthorizationException
     */
    public function show(Request $request, User $user): UserResource
    {
        $this->authorize('view', $user);
        $user->loadMissing(
            'roles', 'state', 'country', 'technician:technician_id,user_id,technician_uuid', 'technician.workingHours', 'lastPasswordUpdatedUser:user_id,first_name',
            'technician.skills:technician_skill_id,technician_id,problem_diagnosis_id,problem_category_id,problem_sub_category_id', 'timezone',
        );

        return new UserResource($user);
    }

    /**
     * Update user details
     *
     * @throws AuthorizationException
     */
    public function update(UpdateRequest $request, User $user): AccountInfoResource|JsonResponse
    {
        $this->authorize('update', $user);

        try {
            $updateUserDetailsInPool = false;
            if ($user->first_name !== $request->input('first_name')) {
                $updateUserDetailsInPool = true;
            }

            if ($user->last_name !== $request->input('last_name')) {
                $updateUserDetailsInPool = true;
            }

            if ($user->middle_name !== $request->input('middle_name')) {
                $updateUserDetailsInPool = true;
            }

            if ($user->email !== $request->input('email')) {
                $updateUserDetailsInPool = true;
            }

            // Select user pool id. for technicians users we use suppurate user pool.
            $userPoolId = $user->user_type === UserTypes::TECHNICIAN()
                ? config('services.cognito.provider.user_pool_id') : $user->organization?->user_pool_id;

            // Update user personal details like name and email.
            $updateCognitoUserDetailsPayload = [
                'first_name' => $request->input('first_name'),
                'middle_name' => $request->input('middle_name'),
                'last_name' => $request->input('last_name'),
                'email' => $request->input('email'),
                'cognito_user_id' => $user->cognito_user_id,
                'cognito_user_name' => $user->cognito_user_name ?? $user->cognito_user_id,
                'user_pool_id' => $userPoolId,
            ];

            $this->updateCognitoUserDetails($user, $updateCognitoUserDetailsPayload, $updateUserDetailsInPool);

            // Update user status.
            $userStatus = $request->is_active ? UserStatus::ACTIVE() : UserStatus::INACTIVE();

            $updateUserStatusInPool = $user->status !== $userStatus;

            $updateUserStatusPayload = [
                'status' => $userStatus,
                'cognito_user_id' => $user->cognito_user_id,
                'cognito_user_name' => $user->cognito_user_name ?? $user->cognito_user_id,
                'user_pool_id' => $userPoolId,
            ];

            $this->updateUserStatus($user, $updateUserStatusPayload, $updateUserStatusInPool);

            DB::beginTransaction();

            $address = $request->input('address');

            // Fetch state
            $state = State::select('state_id', 'state_code')
                ->whereUuid($address['state_id'])
                ->first();
            // Fetch state
            $country = Country::select('country_id')
                ->whereUuid($address['country_id'])
                ->first();

            // Update user other personal information.
            $user->phone_number = $request->input('phone_number');
            $user->street_address = $address['street_address'] ?? null;
            $user->apt_suite_unit = $address['apt_suite_unit'] ?? null;
            $user->city = $address['city'] ?? null;
            $user->zip_code = $address['zip_code'] ?? null;
            $user->state_id = $state->state_id ?? null;
            $user->country_id = $country->country_id ?? null;
            $user->save();

            // user role other than technician can update the roles
            if ($user->user_type !== UserTypes::TECHNICIAN()) {
                $roles = $request->get('roles');
                $user->syncRoles(Role::whereUuid($roles)->get(['role_id', 'name']));
            }

            DB::commit();

            // Update user password
            if (! empty($request->password)) {
                $passwordResetPayLoad = [
                    'password' => $request->password,
                    'cognito_user_id' => $user->cognito_user_id,
                    'cognito_user_name' => $user->cognito_user_name ?? $user->cognito_user_id,
                    'user_pool_id' => $userPoolId,
                    'updated_user_id' => $request->user()?->user_id,
                ];
                $this->resetUserPassword($user, $passwordResetPayLoad);
            }

            $user->loadMissing('roles', 'state', 'country', 'technician:technician_id,user_id,technician_uuid', 'technician.workingHours', 'lastPasswordUpdatedUser:user_id,first_name');

            return new AccountInfoResource($user);

        } catch (CognitoAPIFailedException|RuntimeException|Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'User update api failed due to ' . get_class($exception), notify: true);

            return Response::error(
                errors: [$exception->getMessage()],
                message: __('Something went wrong. unable to update user details.')
            );
        }
    }

    /**
     * @throws AuthorizationException
     */
    public function destroy(Request $request, User $user): JsonResponse|UserResource
    {
        $this->authorize('delete', $user);

        try {
            $organization = $user->organization;
            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            if ($user->hasRole('Super Admin')) {
                $ownerExists = $organization->users()->whereHas('roles', function ($query) {
                    $query->where('name', 'Super Admin');
                })->count() > 1;

                if (! $ownerExists) {
                    return Response::unprocessableEntity('At least one Super Admin required for a organization.');
                }
            }

            $cognitoUserDetails = [
                'Username' => $user->cognito_user_name ?? $user->cognito_user_id,
                'UserPoolId' => $organization->user_pool_id,
            ];

            $user->update([
                'status' => UserStatus::INACTIVE,
            ]);

            $this->cognitoClient->adminUserGlobalSignOutAsync($cognitoUserDetails);

            $this->cognitoClient->adminDisableUser($cognitoUserDetails);

            DB::commit();

            return new UserResource($user);

        } catch (OrganizationNotFoundException|CognitoIdentityProviderException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'User delete api failed due to ' . get_class($exception));

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (QueryException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'User delete api failed[QueryException]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, unable to delete user.'));
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'User delete api failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    public function filters(FilterValuesRequest $request): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('viewAny', User::class);

        try {
            $filterType = $request->get('type');
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            $status = UserStatus::statusValueLabelFormat();

            $roles = $organization->getCachedRoles();

            return match ($filterType) {
                'status' => UserStatusResource::collection($status),
                'role' => UserRoleResource::collection($roles),
                default => throw new UnexpectedValueException(__("Unexpected filter type {$filterType} provided.")),
            };

        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User filter api failed[UnexpectedValueException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (OrganizationNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User filter api failed[OrganizationNotFoundException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User filter api failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Update Cognito UserDetails
     *
     * @param  array<string,string>  $payLoad
     */
    public function updateCognitoUserDetails(User $user, array $payLoad, bool $updateCognito): void
    {
        try {
            DB::beginTransaction();

            if (empty($payLoad['first_name'])) {
                throw new InvalidArgumentException(__('first name field is required for update cognito user details.'));
            }

            if (empty($payLoad['email'])) {
                throw new InvalidArgumentException(__('email field is required for update cognito user details.'));
            }

            if (empty($payLoad['cognito_user_name'])) {
                throw new InvalidArgumentException(__('cognito user name field is required for update cognito user details.'));
            }

            if (empty($payLoad['user_pool_id'])) {
                throw new InvalidArgumentException(__('user pool id field is required for update cognito user details.'));
            }

            $user->first_name = $payLoad['first_name'];
            $user->middle_name = $payLoad['middle_name'] ?? null;
            $user->last_name = $payLoad['last_name'] ?? null;
            $user->email = $payLoad['email'];
            $user->save();

            if ($updateCognito) {
                $fullName = trim("{$user->first_name} {$user->middle_name} {$user->last_name}");
                $userAttributeUpdatePayload = [
                    'Username' => $payLoad['cognito_user_name'],
                    'UserPoolId' => $payLoad['user_pool_id'],
                    'UserAttributes' => [
                        [
                            'Name' => 'name',
                            'Value' => $fullName,
                        ],
                        [
                            'Name' => 'email',
                            'Value' => $payLoad['email'],
                        ],
                    ],
                ];

                $this->cognitoClient->adminUpdateUserAttributes($userAttributeUpdatePayload);
            }

            DB::commit();
        } catch (CognitoIdentityProviderException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Update Cognito User Details api failed[CognitoIdentityProviderException]', notify: true);
            throw new CognitoAPIFailedException($exception->getMessage());
        } catch (InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Update Cognito User Details api failed[InvalidArgumentException]');
            throw new RuntimeException($exception->getMessage());
        }
    }

    public function updateNotificationSubscription(Request $request, User $user): JsonResponse
    {
        $this->authorize('update', $user);

        $request->validate([
            'notification_type' => 'required|in:email,sms,push',
            'notification_type_enabled' => ['required', 'in:0,1'],
        ]);

        $notification_type = match ($request->input('notification_type')) {
            'email' => 'enable_email_notification',
            'sms' => 'enable_sms_notification',
            'push' => 'enable_push_notification',
            default => throw new InvalidArgumentException('Invalid notification type argument given.')
        };

        $user->{$notification_type} = $request->input('notification_type_enabled') ? 'yes' : 'no';
        $user->save();

        return response()->json([
            $notification_type => $user->{$notification_type} === 'yes',
        ]);
    }

    /**
     * Update User Status
     *
     * @param  array<string,string>  $payLoad
     */
    public function updateUserStatus(User $user, array $payLoad, bool $updateCognito): void
    {
        DB::beginTransaction();
        try {
            if (empty($payLoad['status'])) {
                throw new InvalidArgumentException(__('status field is required for update cognito user details.'));
            }

            if (empty($payLoad['cognito_user_id'])) {
                throw new InvalidArgumentException(__('cognito user id field is required for update cognito user details.'));
            }

            if (empty($payLoad['user_pool_id'])) {
                throw new InvalidArgumentException(__('user pool id field is required for update cognito user details.'));
            }

            if ($user->hasRole('Owner')) {
                $organizationId = $user->organization_id;

                $ownerCount = Organization::join('users', function ($query) {
                    $query->on('users.organization_id', '=', 'organizations.organization_id')
                        ->whereNull('users.deleted_at')
                        ->where('users.status', UserStatus::ACTIVE());
                })
                    ->join('user_role', 'user_role.user_id', 'users.user_id')
                    ->join('roles', function ($query) {
                        $query->on('roles.role_id', '=', 'user_role.role_id')
                            ->where('roles.name', 'Owner');
                    })
                    ->where('organizations.organization_id', $organizationId)
                    ->count();

                if ($ownerCount < 1) {
                    throw new OwnerNotExistsException('At least one Owner required for a organization.');
                }
            }

            $cognitoUserDetails = [
                'Username' => $payLoad['cognito_user_name'],
                'UserPoolId' => $payLoad['user_pool_id'],
            ];

            $user->update([
                'status' => UserStatus::from($payLoad['status'])->value,
            ]);

            if ($updateCognito) {
                if ($user->status === UserStatus::INACTIVE()) {
                    $this->cognitoClient->adminUserGlobalSignOutAsync($cognitoUserDetails);
                    $this->cognitoClient->adminDisableUser($cognitoUserDetails);
                } elseif ($user->status === UserStatus::ACTIVE()) {
                    $this->cognitoClient->adminEnableUser($cognitoUserDetails);
                }
            }

            DB::commit();
        } catch (CognitoIdentityProviderException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Update User Status api failed[CognitoIdentityProviderException]', notify: true);

            throw new CognitoAPIFailedException($exception->getMessage());
        } catch (QueryException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Update User Status api failed[QueryException]');

            throw new RuntimeException(__('Failed to update user status.'));
        } catch (OwnerNotExistsException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Update User Status api failed[OwnerNotExistsException]');

            throw new RuntimeException($exception->getMessage());
        }
    }

    /**
     * Update User Status
     *
     * @param  array<string,string|int>  $payLoad
     *
     * @throws Throwable
     */
    public function resetUserPassword(User $user, array $payLoad): void
    {
        DB::beginTransaction();
        try {
            if (empty($payLoad['password'])) {
                throw new InvalidArgumentException(__('password field is required for update cognito user details.'));
            }

            if (empty($payLoad['cognito_user_id'])) {
                throw new InvalidArgumentException(__('cognito user id field is required for update cognito user details.'));
            }

            if (empty($payLoad['user_pool_id'])) {
                throw new InvalidArgumentException(__('user pool id field is required for update cognito user details.'));
            }

            $user->last_password_updated_user_id = ! empty($payLoad['updated_user_id']) ? (int) $payLoad['updated_user_id'] : null;
            $user->last_password_updated_at = Carbon::now();
            $user->save();

            // Update user password.
            $this->cognitoClient->adminSetUserPassword([
                'Password' => $payLoad['password'],
                'Permanent' => true,
                'UserPoolId' => $payLoad['user_pool_id'],
                'Username' => $payLoad['cognito_user_name'],
            ]);

            DB::commit();
        } catch (CognitoIdentityProviderException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Reset User Password api Failed[CognitoIdentityProviderException]', notify: true);
            throw new CognitoAPIFailedException($exception->getMessage());
        }
    }

    /**
     *  Get group view details.
     */
    public function getGroupView(GroupViewRequest $request): JsonResource|JsonResponse
    {
        try {
            $this->authorize('viewAny', User::class);

            $group = $request->get('group');
            $organizationId = request()->user()?->organization_id;

            if (! $organizationId) {
                throw new OrganizationNotFoundException;
            }

            switch ($group) {
                case 'status':
                    $groupData = $this->statusBasedGroupData($organizationId, $request);

                    return StatusBasedGroupDataResource::collection($groupData);
                case 'role':
                    $groupData = $this->roleBasedGroupData($organizationId, $request);

                    return RoleBasedGroupDataResource::collection($groupData);
                default:
                    throw new UnexpectedValueException(__("Invalid group [{$group}] provided."));
            }

        } catch (UserGroupingException $exception) {

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User Group view api Failed[UnexpectedValueException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (AuthorizationException|OrganizationNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User Group view api Failed due to ' . get_class($exception));

            return Response::unauthorized(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User Group view api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Return user status and its role counts
     *
     * @return Collection<int, User>
     */
    private function statusBasedGroupData(int $organizationId, GroupViewRequest $request): Collection
    {
        try {
            $userStatus = User::leftJoin('user_role', 'user_role.user_id', 'users.user_id')
                ->join('roles', function (JoinClause $joinQuery) use ($organizationId) {
                    $joinQuery->on('roles.role_id', '=', 'user_role.role_id')
                        ->where('roles.organization_id', $organizationId)
                        ->whereNull('roles.deleted_at');
                })

                // Search query
                ->when(! empty($request->search), function (Builder $query) use ($request) {
                    $query->where(function (Builder $query) use ($request) {
                        $query->whereRaw("TRIM(CONCAT(users.first_name , ' ', COALESCE(users.last_name, ''))) LIKE ?", ["%{$request->search}%"])
                            ->orWhere('users.email', 'like', "%{$request->search}%")
                            ->orWhere('users.phone_number', 'like', "%{$request->search}%");
                    });
                })

                // Filter query
                ->when(! empty($request->filter), function (Builder $filterQuery) use ($request) {
                    $this->filterQuery($request->filter, $filterQuery);
                })
                ->select('users.status', DB::raw('count(DISTINCT users.user_id) as total'))
                ->groupBy('users.status')
                ->get();

            return $userStatus;

        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User Status Based Group Data API Failed[UnexpectedValueException]');

            throw new UserGroupingException(__('The grouping process was failed due to some unexpected value(s).'));
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User Status Based Group Data API Failed[InvalidArgumentException]');

            throw new UserGroupingException(__('The grouping process was failed due to some invalid value(s).'));
        } catch (LogicException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User Status Based Group Data API Failed[LogicException]');

            throw new UserGroupingException(__('The grouping process was failed due to an unexpected condition(s).'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User Status Based Group Data API Failed[Exception]', notify: true);

            throw new UserGroupingException(__('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Return user role(s) group and its role counts
     *
     * @return array<int,mixed>
     */
    private function roleBasedGroupData(int $organizationId, GroupViewRequest $request): array
    {
        try {
            $roles = User::leftJoin('user_role', 'user_role.user_id', 'users.user_id')
                ->join('roles', function (JoinClause $joinQuery) use ($organizationId) {
                    $joinQuery->on('roles.role_id', '=', 'user_role.role_id')
                        ->where('roles.organization_id', $organizationId)
                        ->whereNull('roles.deleted_at');
                })

                // Search query
                ->when(! empty($request->search), function (Builder $query) use ($request) {
                    $query->where(function (Builder $query) use ($request) {
                        $query->whereRaw("TRIM(CONCAT(users.first_name , ' ', COALESCE(users.last_name, ''))) LIKE ?", ["%{$request->search}%"])
                            ->orWhere('users.email', 'like', "%{$request->search}%")
                            ->orWhere('users.phone_number', 'like', "%{$request->search}%");
                    });
                })

                // Filter query
                ->when(! empty($request->filter), function (Builder $filterQuery) use ($request) {
                    $this->filterQuery($request->filter, $filterQuery);
                })
                ->select(
                    DB::raw("GROUP_CONCAT(DISTINCT roles.name ORDER BY roles.name ASC  SEPARATOR '-') AS role_name_slug_for_grouping")
                )
                ->orderBy('users.user_id')
                ->groupBy('users.user_id')
                ->get()
                ->groupBy('role_name_slug_for_grouping');

            $formattedRoles = [];
            foreach ($roles as $roleName => $userDetails) {
                $formattedRoles[] = [
                    'label' => Str::replace('-', ', ', (string) $roleName),
                    'total' => count($userDetails),
                    'slug' => Str::slug($roleName),
                ];
            }

            return $formattedRoles;

        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User Role Based Group Data API Failed[UnexpectedValueException]');

            throw new UserGroupingException(__('The grouping process was failed due to some unexpected value(s).'));
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User Role Based Group Data API Failed[InvalidArgumentException]');

            throw new UserGroupingException(__('The grouping process was failed due to some invalid value(s).'));
        } catch (LogicException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User Role Based Group Data API Failed[LogicException]');

            throw new UserGroupingException(__('The grouping process was failed due to an unexecuted condition(s).'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'User Role Based Group Data API Failed[Exception]', notify: true);

            throw new UserGroupingException(__('Something went wrong, The server was unable to process'));
        }
    }
}

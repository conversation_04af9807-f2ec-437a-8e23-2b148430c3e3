<?php

namespace App\Http\Controllers;

use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Resources\ServiceRequest\ActivityLogResource;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestActivityLog;
use App\Models\ServiceRequestNote;
use App\Models\WorkOrderActivityLog;
use App\Models\WorkOrderNote;
use App\Services\WorkOrderActivity\ActivityComposite;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Response;

class ServiceRequestActivityLogController extends Controller
{
    /**
     * @throws AuthorizationException
     */
    public function index(Request $request, ServiceRequest $serviceRequest): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('viewActivityLogs', $serviceRequest);

        try {
            $user = $request->user();

            if (! $user) {
                throw new UserNotFoundException;
            }

            $serviceRequestActivityLogs = ServiceRequestActivityLog::with('triggeredBy:user_id,first_name,middle_name,last_name')
                ->where('organization_id', $user->organization_id)
                ->where('service_request_id', $serviceRequest->service_request_id)
                ->select('service_request_activity_log_uuid', 'event', 'event_attributes', 'triggered_by', 'created_at')
                ->get();

            $mediaIds = [];
            foreach ($serviceRequestActivityLogs as $serviceRequestActivityLog) {
                $eventAttributes = $serviceRequestActivityLog->event_attributes;

                if (! empty($eventAttributes['medias'])) {
                    $mediaIds[] = $eventAttributes['medias'];
                }
            }

            $mediaIds = collect($mediaIds)->flatten()->unique()->all();

            $serviceRequest->load([
                'media' => function ($query) use ($mediaIds) {
                    return $query->whereUuid($mediaIds)
                        ->withTrashed()
                        ->select(
                            'media.media_id', 'media.media_uuid', 'media.mime_type', 'media.thumbnail_file_name',
                            'media.optimized_file_name', 'media.file_name'
                        );
                }, 'organization:organization_id,domain',
            ]);

            $serviceRequestNotes = ServiceRequestNote::with(
                'user:user_id,first_name,last_name,middle_name,profile_pic,user_uuid',
                'lastModifiedUser:user_id,first_name,middle_name,last_name,profile_pic,user_uuid'
            )
                ->where('organization_id', $user->organization_id)
                ->where('service_request_id', $serviceRequest->service_request_id)
                ->select(
                    'service_request_note_id', 'service_request_note_uuid', 'service_request_status_id',
                    'user_id', 'note_type', 'note', 'last_modified_at', 'last_modified_user_id', 'created_at'
                )
                ->orderBy('created_at')
                ->get();

            $workOrderIds = $serviceRequest->workOrders()->pluck('work_order_id')->toArray();

            $workOrderActivityLogs = collect();
            $workOrderNotes = collect();
            if (count($workOrderIds)) {
                $workOrderActivityLogs = WorkOrderActivityLog::with(
                    'triggeredBy:user_id,first_name,middle_name,last_name',
                    'workOrder.media:media_id,media.media_uuid,media.mime_type,media.thumbnail_file_name',
                )
                    ->where('organization_id', $user->organization_id)
                    ->whereIn('work_order_id', $workOrderIds)
                    ->select('work_order_activity_log_uuid', 'event', 'event_attributes', 'triggered_by', 'created_at', 'work_order_id')
                    ->get();

                $workOrderNotes = WorkOrderNote::with(
                    'user:user_id,first_name,last_name,middle_name,profile_pic,user_uuid',
                    'vendor:vendor_id,vendor_uuid,company_name,log_file_name,service',
                    'lastModifiedUser:user_id,first_name,middle_name,last_name,profile_pic,user_uuid'
                )
                    ->where('organization_id', $user->organization_id)
                    ->whereIn('work_order_id', $workOrderIds)
                    ->select(
                        'work_order_note_id', 'work_order_note_uuid', 'work_order_id',
                        'user_id', 'note_type', 'note', 'last_modified_at', 'last_modified_user_id', 'created_at', 'vendor_id'
                    )
                    ->orderBy('created_at')
                    ->get();
            }

            $serviceRequestActivityLogs->each->setRelation('serviceRequest', $serviceRequest);
            $activityLogComposite = new ActivityComposite;
            $activityLogComposite->addActivities($serviceRequestActivityLogs, $serviceRequestNotes, $workOrderActivityLogs, $workOrderNotes);
            $activityLogComposite->getActivities();

            return ActivityLogResource::collection($activityLogComposite->paginate(config('pagination.service_request_activity_log.per_page')));
        } catch (UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request Activity Log List API failed[UserNotFoundException]');

            return Response::unauthorized(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request Activity Log List API failed[Exception]', notify: true);

            return Response::internalServerError($exception->getMessage());
        }
    }

    /**
     * @param  array<int|string, mixed>  $items
     * @param  array<int|string, mixed>  $options
     * @return LengthAwarePaginator<Collection<int|string, mixed>>
     */
    public function paginate(array $items, int $perPage = 15, ?int $page = null, array $options = []): LengthAwarePaginator
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
        $items = Collection::make($items);

        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }
}

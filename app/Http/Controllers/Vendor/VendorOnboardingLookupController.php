<?php

namespace App\Http\Controllers\Vendor;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Http\Resources\ProblemCategoryResource;
use App\Http\Resources\StateResource;
use App\Models\ProblemCategory;
use App\Models\State;
use App\Models\VendorOnboardingStatus;
use Exception;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;
use Throwable;

class VendorOnboardingLookupController extends Controller
{
    public function getLookups(Request $request, ?string $requestTypes = null): JsonResponse
    {
        try {
            $requestTypes = array_filter(explode(',', $requestTypes ?? $request->get('request_types')));
            $response = [];
            foreach ($requestTypes as $requestType) {
                $response[$requestType] = match ($requestType) {
                    'location_api_key' => $this->getLocationApiKey(),
                    'problem_categories' => $this->getProblemCategory(),
                    'vendor_onboarding_statuses' => $this->getOnboardingStatuses(),
                    'states' => $this->getStates(),
                    default => throw new InvalidArgumentException("Invalid argument [{$requestType}] for lookup"),
                };
            }

            return response()->json($response, 200);
        } catch (InvalidArgumentException $exception) {
            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);

            return Response::internalServerError();
        }
    }

    protected function getProblemCategory(): AnonymousResourceCollection
    {
        $problemCategories = Cache::remember(
            config('settings.cache.look_up.problem_category.name'),
            now()->addMinutes(config('settings.cache.look_up.problem_category.expire_time_in_minutes')),
            static fn () => ProblemCategory::select('problem_category_id', 'problem_category_uuid', 'label', 'slug')
                ->with([
                    'problemSubCategories' => function (Builder $query) {
                        $query->select('problem_category_id', 'problem_sub_category_id', 'label', 'slug', 'problem_sub_category_uuid');
                        $query->orderBy('label');
                    },
                    'problemSubCategories.problemDiagnoses' => function (Builder $query) {
                        $query->select('problem_sub_category_id', 'problem_diagnosis_uuid', 'label', 'slug');
                        $query->orderBy('label');
                    },
                ],
                )
                ->orderBy('label')
                ->get()
        );

        return ProblemCategoryResource::collection($problemCategories);
    }

    protected function getLocationApiKey(): JsonResource
    {
        try {
            $apiKey['key'] = Helper::getLocationApiKey();

            return new JsonResource($apiKey);
        } catch (Throwable $exception) {

            return $this->handleApiExceptions($exception, 'Failed on getting location api key');
        }

    }

    protected function getOnboardingStatuses(): JsonResource
    {
        $vendorOnboardingStatuses = VendorOnboardingStatus::select(['label', 'slug', 'sort_order'])
            ->orderBy('sort_order')
            ->get();

        return new JsonResource($vendorOnboardingStatuses);
    }

    protected function getStates(): AnonymousResourceCollection
    {
        $states = Cache::remember(
            config('settings.cache.look_up.state.name'),
            now()->addMinutes(config('settings.cache.look_up.state.expire_time_in_minutes')),
            static fn () => State::all()
        );

        return StateResource::collection($states);
    }
}

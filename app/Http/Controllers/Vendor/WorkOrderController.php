<?php

namespace App\Http\Controllers\Vendor;

use App\Enums\Boolean;
use App\Enums\MediaType;
use App\Enums\WorkOrderStatus;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Http\Controllers\Controller;
use App\Http\Filters\WorkOrderListFilter;
use App\Http\Requests\Vendor\ListRequest;
use App\Http\Resources\Vendor\ListResource;
use App\Http\Resources\Vendor\WorkOrderResource;
use App\Models\QueryBuilder\WorkOrderListQueryBuilder;
use App\Models\User;
use App\Models\WorkOrder;
use App\States\WorkOrders\Canceled;
use App\Traits\ApiExceptionHandler;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Throwable;

class WorkOrderController extends Controller
{
    use ApiExceptionHandler;
    use ApiResponse;

    /**
     * Work order listing
     */
    public function index(ListRequest $request, WorkOrderListFilter $workOrderListFilter): AnonymousResourceCollection|JsonResponse
    {
        $user = $request->user();

        if (empty($user)) {
            return $this->handleException(new UserNotFoundException);
        }

        try {
            $organizationId = $user->organization_id;

            // Get work orders
            $workOrders = WorkOrderListQueryBuilder::make()
                ->withRelations($this->getWorkOrderRelations())
                ->joinStatusRelations()
                ->joinServiceCategoryRelations($organizationId)
                ->joinServiceRequestAddressAndResidentRelations($organizationId)
                ->joinWorkOrderHealthRelations()
                ->joinWorkOrderAssigneeRelations($request, $organizationId)
                ->joinWorkOrderTagRelations($request, $organizationId)
                ->joinTripRelations($request, $organizationId)
                ->applyFilter($workOrderListFilter)
                ->groupByWorkOrder()
                ->selectFields($this->getWorkOrderSelectFields())
                ->getPaginatedData($request->input('per_page', config('pagination.work_order_list.per_page')));

            // Get tab counts
            $tabCounts = $this->getTabTotalCounts($request, $user);

            return ListResource::collection($workOrders)->additional([
                'meta' => [
                    'tab_counts' => $tabCounts,
                ],
            ]);
        } catch (Throwable $exception) {
            return $this->handleApiExceptions($exception, 'Get vendor work order lists API Failed');
        }
    }

    /**
     * Work order details
     */
    public function show(Request $request, WorkOrder $workOrder): JsonResource|JsonResponse
    {
        $this->authorize('view', $workOrder);

        $user = $request->user();

        if (empty($user)) {
            return $this->handleException(new UserNotFoundException);
        }

        try {
            $skippedMediaTypes = $this->getSkippedMediaTypes($request);
            $issueMediaTypes = $this->getIssueMediaTypes($request);
            $workOrder->load($this->getShowEagerLoadRelations($skippedMediaTypes, $issueMediaTypes));

            return new WorkOrderResource($workOrder);
        } catch (Throwable $exception) {
            return $this->handleApiExceptions($exception, 'Get vendor work order details API Failed');
        }
    }

    /**
     * Work order listing eagerload relations
     */
    private function getWorkOrderRelations(): array
    {
        return [
            'serviceRequest:service_request_id,property_id',
            'serviceRequest.property:property_id,property_uuid,property_name,full_address,latitude,longitude,state_id,street_address,city,unit_number,postal_zip_code',
            'serviceRequest.property.state:state_id,state_uuid,name,state_code',
            'serviceRequest.resident:resident_id,resident_uuid',
            'healthLogs.healthTracker:work_order_health_tracker_id,classification,weight',
            'timezone:timezone_id,name',
            'assignees:work_order_assignee_id,work_order_assignee_uuid,work_order_id,user_id',
            'assignees.user:user_id,user_uuid,first_name,last_name,middle_name,profile_pic',
            'tags' => function ($query) {
                $query->select('tags.tag_id', 'tag_uuid', 'name', 'slug', 'color', 'text_color', 'type')
                    ->withCount('workOrders');
            },
            'workOrderIssues' => function ($query) {
                $query->select(
                    'work_order_issue_id',
                    'work_order_issue_uuid',
                    'work_order_id',
                    'issue_id',
                    'state'
                )->with([
                    'workOrder:work_order_id,work_order_uuid,work_order_number',
                    'issue:issue_id,issue_uuid,title,description,problem_diagnosis_id,state' => [
                        'workOrders' => function ($query) {
                            $query->select('work_orders.work_order_id', 'work_order_uuid', 'work_order_number', 'work_orders.state')
                                ->withCount('issues')
                                ->with('latestTrips:work_order_id,work_order_service_call_uuid,work_order_service_call_number,state')
                                ->whereNotState('work_orders.state', Canceled::class);
                        },
                        'problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label' => [
                            'subCategory:problem_sub_category_id,problem_sub_category_uuid,label,problem_category_id' => [
                                'problemCategory:problem_category_id,problem_category_uuid,label',
                            ],
                        ],
                    ],
                ]);
            },
            'latestTrips' => function ($query) {
                $query->select(
                    'work_order_service_call_id',
                    'work_order_service_call_uuid',
                    'work_order_service_call_number',
                    'organization_id',
                    'work_order_id',
                    'technician_appointment_id',
                    'vendor_appointment_id',
                    'lula_appointment_id',
                    'scheduled_start_time',
                    'scheduled_end_time',
                    'state',
                    'state_updated_at',
                    'additional_notes',
                    'note_to_provider',
                    'status',
                    'is_active',
                    'last_modified_at',
                    'en_route_at',
                    'en_route_timer_paused_at',
                    'en_route_timer_resumed_at',
                    'drive_time_in_sec',
                    'adjusted_drive_time_in_sec',
                    'work_started_at',
                    'work_timer_paused_at',
                    'work_timer_resumed_at',
                    'work_completed_at',
                    'labor_time_in_sec',
                    'adjusted_labor_time_in_sec',
                    'created_at'
                )
                    ->with([
                        'technicianAppointment:technician_appointment_id,technician_appointment_uuid,technician_id',
                        'technicianAppointment.technician:technician_id,technician_uuid,user_id',
                        'technicianAppointment.technician.user:user_id,first_name,last_name,middle_name,profile_pic',
                        'vendorAppointment:vendor_appointment_id,vendor_id,vendor_appointment_uuid,vendor_instructions',
                        'vendorAppointment.vendorAllocations:vendor_allocation_id,vendor_appointment_id',
                        'vendorAppointment.vendorAllocations.vendor:vendor_id,first_name,last_name,vendor_uuid',
                        'vendorAppointment.vendor:vendor_id,first_name,last_name,vendor_uuid',
                        'lulaAppointment:lula_appointment_id,lula_appointment_uuid',
                    ]);
            },
            'property:property_id,property_uuid,property_name,full_address,latitude,longitude,state_id,street_address,city,unit_number,postal_zip_code',
            'property.state:state_id,state_uuid,name,state_code',
        ];
    }

    /**
     * Work order listing selected fields
     */
    private function getWorkOrderSelectFields(): array
    {
        return [
            'work_orders.work_order_id',
            'work_orders.service_request_id',
            'work_orders.work_order_uuid',
            'work_orders.organization_id',
            'work_orders.work_order_status_id',
            'work_orders.description',
            'work_orders.priority',
            'work_orders.created_at',
            'work_order_statuses.label as status',
            'properties.property_name',
            'properties.full_address',
            'work_order_statuses.slug as status_slug',
            'work_orders.work_order_number',
            'work_orders.due_date',
            'work_orders.state',
            'work_orders.timezone_id',
            'work_orders.nte_amount_in_cents',
            'work_orders.paused_at',
            'work_orders.canceled_at',
            'work_orders.resolved_at',
            'work_orders.work_completed_at',
            DB::raw('CAST(COALESCE(work_orders.work_completed_at, work_orders.canceled_at) AS DATETIME) as closed_at'),
            DB::raw("
                substring_index(
                    MAX(concat(active_trip.work_order_service_call_id, '|',active_trip.scheduled_start_time)), '|', -1
                ) as scheduled_start_time
            "),
        ];
    }

    /**
     * Work order listing tabs counts
     */
    private function getTabTotalCounts(ListRequest $request, User $user): array
    {
        $organizationId = $user->organization_id;

        $statusCounts = WorkOrderListQueryBuilder::make()
            ->withRelations($this->getWorkOrderRelations())
            ->joinStatusRelations()
            ->joinTripRelations($request, $organizationId)
            ->groupByWorkOrder('work_order_statuses.slug')
            ->selectFields([
                'work_order_statuses.slug as status_slug',
                DB::raw('COUNT(DISTINCT work_orders.work_order_id) as count'),
            ])
            ->pluckFields('count', 'status_slug')
            ->toArray();

        $statusGroups = [
            'open' => [WorkOrderStatus::SCHEDULED(), WorkOrderStatus::WORK_IN_PROGRESS()],
            'paused' => [WorkOrderStatus::PAUSED()],
            'closed' => [WorkOrderStatus::COMPLETED(), WorkOrderStatus::CANCELED()],
        ];

        $tabCounts = collect($statusGroups)->mapWithKeys(fn ($statuses, $tab) => [
            $tab => collect($statuses)->sum(fn ($status) => $statusCounts[$status] ?? 0),
        ]);

        return $tabCounts->toArray();
    }

    /**
     * Work order details skipped images
     *
     * @description type of images not to include on details level (ex: trip before and after result)
     */
    private function getSkippedMediaTypes(Request $request): array
    {
        $types = [MediaType::QUOTE_TASK()];

        if ($request->expectsDevice()) {
            $types[] = MediaType::AFTER_MEDIA();
            $types[] = MediaType::BEFORE_MEDIA();
        }

        return $types;
    }

    /**
     * Work order details issue images
     *
     * @description type of images to include on issues
     */
    private function getIssueMediaTypes(Request $request): array
    {
        $types = [MediaType::AFTER_MEDIA(), MediaType::BEFORE_MEDIA()];

        return $types;
    }

    /**
     * Work order details eager loaded relations
     */
    private function getShowEagerLoadRelations(array $skippedMediaTypes, array $issueMediaTypes): array
    {
        return [
            'timezone:timezone_id,name',
            // Vendor
            'vendor:vendor_id,company_name,first_name,last_name,service',
            // Status
            'status:work_order_status_id,label,slug',
            // Source
            'workOrderSource:work_order_source_id,slug',
            // Resident
            'resident:resident_id,resident_uuid,first_name,last_name,phone_number',
            // SR Property
            'serviceRequest.property:property_id,property_uuid,property_name,full_address,latitude,longitude,state_id,street_address,city,unit_number,postal_zip_code',
            'serviceRequest.property.state:state_id,state_uuid,name,state_code',
            'serviceRequest.property.residents:resident_id,resident_uuid,property_id,first_name,last_name,phone_number,email',
            // SR Resident
            'serviceRequest.resident:resident_id,resident_uuid,first_name,last_name,phone_number',
            // Created
            'createdUser:user_id,first_name,last_name,middle_name',
            // Invoices
            'latestInvoices:invoice_id,work_order_id,invoice_uuid,invoice_number,total_cost_in_cents,state,drafted_at,created_at,created_by_user_id,drafted_by_user_id',
            'latestInvoices.createdByUser:user_id,first_name,middle_name,last_name',
            'latestInvoices.draftedByUser:user_id,first_name,middle_name,last_name',
            // WO Property
            'property:property_id,property_uuid,property_name,full_address,latitude,longitude,state_id,street_address,city,unit_number,postal_zip_code',
            'property.state:state_id,state_uuid,name,state_code',
            'property.residents:resident_id,resident_uuid,property_id,first_name,last_name,phone_number,email',
            // WO Health
            'healthLogs.healthTracker:work_order_health_tracker_id,classification,weight',
            // Service Request
            'serviceRequest:service_request_id,service_request_uuid,service_request_number,state,property_id,requesting_resident_id,property_access_method,property_access_code,property_access_note',
            // Issues
            'serviceRequest.workOrders:work_order_id,service_request_id,work_order_uuid,work_order_number,state',
            'serviceRequest.workOrders.issues:issue_id,issue_uuid,problem_diagnosis_id,title,description,state',
            'serviceRequest.workOrders.issues.workOrderIssues:work_order_issue_id,work_order_issue_uuid,issue_id',
            'serviceRequest.workOrders.issues.workOrders:work_order_id,work_order_uuid,work_order_number',
            'serviceRequest.workOrders.issues.problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label',
            'serviceRequest.workOrders.issues.problemDiagnosis.subCategory:problem_sub_category_id,problem_sub_category_uuid,problem_category_id,label',
            'serviceRequest.workOrders.issues.problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
            'workOrderIssues' => function ($query) {
                $query->select(
                    'work_order_issue_id',
                    'work_order_issue_uuid',
                    'work_order_id',
                    'issue_id',
                    'state',
                    'service_notes',
                    'issue_caused_by_resident',
                    'issue_caused_details',
                    'decline_reason',
                    'declined_user_id',
                    'state_updated_at',
                    'created_at',
                    'updated_at',
                )->with([
                    'issue:issue_id,issue_uuid,title,description,problem_diagnosis_id,state' => [
                        'workOrders' => function ($query) {
                            $query->select('work_orders.work_order_id', 'work_order_uuid', 'work_order_number', 'work_orders.state')
                                ->withCount('issues')
                                ->with('latestTrips:work_order_id,work_order_service_call_uuid,work_order_service_call_number,state')
                                ->whereNotState('work_orders.state', Canceled::class);
                        },
                        'problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label' => [
                            'subCategory:problem_sub_category_id,problem_sub_category_uuid,label,problem_category_id' => [
                                'problemCategory:problem_category_id,problem_category_uuid,label',
                            ],
                        ],
                    ],
                ]);
            },
            // Issues Media
            'workOrderIssues.media' => function ($query) use ($issueMediaTypes) {
                $query->select(
                    'media.media_id',
                    'media.media_uuid',
                    'media.file_name',
                    'media.mime_type',
                    'media.thumbnail_file_name',
                    'media.optimized_file_name'
                )
                    ->wherePivot('has_thumbnail', Boolean::YES())
                    ->where(function ($query) use ($issueMediaTypes) {
                        $query->whereIn('work_order_media.media_type', $issueMediaTypes)
                            ->orWhereNull('work_order_media.media_type');
                    });
            },
            // Media
            'media' => function ($query) use ($skippedMediaTypes) {
                return $query->select(
                    'media.media_id',
                    'media.media_uuid',
                    'media.file_name',
                    'media.mime_type',
                    'media.thumbnail_file_name',
                    'media.optimized_file_name'
                )
                    ->wherePivot('has_thumbnail', Boolean::YES())
                    ->where(function ($query) use ($skippedMediaTypes) {
                        return $query->whereNotIn('work_order_media.media_type', $skippedMediaTypes)
                            ->orWhereNull('work_order_media.media_type');
                    });
            },
            // WO Assignees
            'assignees' => function ($query) {
                $query->with('user:user_id,user_uuid,first_name,last_name,middle_name,profile_pic')
                    ->select('work_order_assignee_id', 'work_order_assignee_uuid', 'work_order_id', 'user_id');
            },
            // Tags
            'tags' => function ($query) {
                $query->select('tags.tag_uuid', 'tags.tag_id', 'tags.name', 'tags.color', 'tags.text_color', 'tags.slug')
                    ->withCount('workOrders');
            },
            // Trips
            'latestTrips' => function ($query) {
                $query->select(
                    'work_order_service_call_id',
                    'work_order_service_call_uuid',
                    'work_order_service_call_number',
                    'organization_id',
                    'work_order_id',
                    'technician_appointment_id',
                    'vendor_appointment_id',
                    'lula_appointment_id',
                    'scheduled_start_time',
                    'scheduled_end_time',
                    'state',
                    'state_updated_at',
                    'additional_notes',
                    'note_to_provider',
                    'status',
                    'is_active',
                    'last_modified_at',
                    'en_route_at',
                    'en_route_timer_paused_at',
                    'en_route_timer_resumed_at',
                    'drive_time_in_sec',
                    'adjusted_drive_time_in_sec',
                    'work_started_at',
                    'work_timer_paused_at',
                    'work_timer_resumed_at',
                    'work_completed_at',
                    'labor_time_in_sec',
                    'adjusted_labor_time_in_sec',
                    'created_at'
                )
                    ->with([
                        'technicianAppointment:technician_appointment_id,technician_appointment_uuid,technician_id',
                        'technicianAppointment.technician:technician_id,technician_uuid,user_id',
                        'technicianAppointment.technician.user:user_id,first_name,last_name,middle_name,profile_pic',
                        'vendorAppointment:vendor_appointment_id,vendor_id,vendor_appointment_uuid,vendor_instructions',
                        'vendorAppointment.vendorAllocations:vendor_allocation_id,vendor_appointment_id',
                        'vendorAppointment.vendorAllocations.vendor:vendor_id,first_name,last_name,vendor_uuid',
                        'vendorAppointment.vendor:vendor_id,first_name,last_name,vendor_uuid',
                        'lulaAppointment:lula_appointment_id,lula_appointment_uuid',
                    ]);
            },
        ];
    }
}

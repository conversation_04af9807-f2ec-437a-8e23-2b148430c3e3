<?php

namespace App\Http\Controllers\Vendor;

use App\Enums\UserTypes;
use App\Enums\VendorOnBoardingStatus as VendorOnBoardingStatusEnum;
use App\Enums\VendorOnboardingStatuses;
use App\Exceptions\ForbiddenException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\OnboardingHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\VendorOnboardingSetBasicInfoRequest;
use App\Http\Requests\Vendor\VendorOnBoardingSetServiceAreasRequest;
use App\Http\Requests\Vendor\VendorOnboardingSetServicesRequest;
use App\Http\Requests\Vendor\VendorOnboardingSetStatusRequest;
use App\Http\Resources\Vendor\BasicInfoResource;
use App\Http\Resources\Vendor\ServiceAreaResource;
use App\Http\Resources\Vendor\VendorOnboardingResource;
use App\Mail\OnboardingMail;
use App\Models\Organization;
use App\Models\ProblemSubCategory;
use App\Models\State;
use App\Models\User;
use App\Models\Vendor;
use App\Models\VendorOnboarding;
use App\Models\VendorOnboardingStatus;
use App\Models\VendorService;
use App\Models\VendorServiceArea;
use App\Models\VendorUser;
use App\Traits\ApiExceptionHandler;
use App\Traits\ApiResponse;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;
use Throwable;

class VendorOnboardingController extends Controller
{
    use ApiExceptionHandler;
    use ApiResponse;

    private const STATUS_SELECT_FIELDS = 'status:vendor_onboarding_status_id,sort_order,slug';

    /**
     * Send onboarding link
     */
    public function sendVendorOnboardingLink(Request $request): JsonResponse
    {
        try {
            // Get user
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            // Validate inputs
            $request->validate([
                'type' => ['required', 'in:third-party'],
                'vendor_id' => ['required', 'array', 'min:1'],
                'vendor_id.*' => ['required', 'uuid'],
            ]);

            // Get request inputs
            $vendorUuids = $request->input('vendor_id');
            $type = $request->input('type');
            $isAccountUser = $user->user_type == UserTypes::ACCOUNT_USER();

            // Only allow the account user to access
            if (! $isAccountUser) {
                throw ForbiddenException::accessDenied();
            }

            DB::beginTransaction();

            // Get vendor status and organization
            $initialStatus = VendorOnboardingStatus::where('slug', VendorOnboardingStatuses::INITIAL())->firstOrFail();
            $organization = Organization::where('organization_id', $user->organization_id)->firstOrFail();

            // Generate and send signed url to all vendor
            foreach ($vendorUuids as $vendorUuid) {
                $vendor = Vendor::whereUuid($vendorUuid)->firstOrFail();

                // Create or update vendor (reset the access_count to 0 if onboarding already exist)
                $vendorOnboarding = VendorOnboarding::updateOrCreate(
                    [
                        'vendor_id' => $vendor->vendor_id,
                        'organization_id' => $organization->organization_id,
                    ],
                    [
                        'type' => $type,
                        'vendor_onboarding_status_id' => $initialStatus->vendor_onboarding_status_id,
                        'access_count' => 0,
                    ]
                );

                // Generate signed url
                $signedUrl = OnboardingHelper::generateSignedUrl(
                    'vendor_onboarding.invite',
                    $vendorOnboarding->vendor_onboarding_uuid,
                    $organization->name,
                    config('services.cognito.provider.onboarding_link_expiration') ?? 10080
                );

                // Create frontend signed url
                $url = parse_url($signedUrl);
                $queryParams = [];
                if (isset($url['query'])) {
                    parse_str($url['query'], $queryParams);
                }
                $query = '/onboarding';
                if (isset($url['query']) && ! empty($url['query'])) {
                    $query .= '?' . $url['query'];
                }
                $frontendUrl = config('services.cognito.provider.vendor_domain') . $query;

                // Send signed url to vendor email
                Mail::to($vendor->email)
                    ->queue(new OnboardingMail(
                        $frontendUrl,
                        $this->minutesToDaysSimple(config('services.cognito.provider.onboarding_link_expiration')) ?? 30,
                        $organization?->name,
                    ));
            }

            DB::commit();

            return $this->successResponse(null, 'Onboarding emails are being processed.');
        } catch (Throwable $exception) {
            DB::rollBack();

            return $this->handleApiExceptions($exception, 'Send onboarding signed url failed');
        }
    }

    /**
     * Update Basic Information
     */
    public function setBasicInfo(VendorOnboardingSetBasicInfoRequest $request): JsonResponse|BasicInfoResource
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $userId = $user->user_id;
            $onboardingId = $request->input('onboardingId');
            $isThirdPartyUser = $user->user_type == UserTypes::VENDOR();

            // Only allow the vendor to get access
            if (! $isThirdPartyUser) {
                throw ForbiddenException::accessDenied();
            }

            DB::beginTransaction();

            $vendorOnboarding = VendorOnboarding::with([
                self::STATUS_SELECT_FIELDS,
                'vendor:vendor_id,on_boarding_status',
            ])
                ->whereUuid($onboardingId)
                ->firstOrFail();

            $newStatus = VendorOnboardingStatus::where('slug', VendorOnboardingStatuses::SERVICE_OFFERED())->firstOrFail();

            // Only allow the vendor that owns the onboarding can update the services offered
            if ($vendorOnboarding->user_id !== $userId) {
                throw ForbiddenException::accessDenied();
            }

            // Only allow update if vendor onboarding have correct status
            if ($vendorOnboarding->status->slug !== VendorOnboardingStatuses::BASIC_INFO()) {
                throw new CouldNotPerformTransition;
            }

            $state = State::whereUuid($request->input('state_id'))->firstOrFail();
            $updateData = [
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'company_name' => $request->input('company_name'),
                'phone_number' => $request->input('phone_number'),
                'address1' => $request->input('address1'),
                'address2' => $request->input('apt_unit'),
                'city' => $request->input('city'),
                'postal_zip_code' => $request->input('postal_zip_code'),
                'state_province' => $state->state_code,
                'gl_insurance_expire_at' => $request->input('gl_insurance_expire_at') != '' ? Carbon::parse($request->input('gl_insurance_expire_at'))->toDateTimeString() : null,
            ];

            $vendorUser = VendorUser::where('vendor_id', $vendorOnboarding->vendor->vendor_id)->firstOrFail();
            if ($vendorUser) {
                User::where('user_id', $vendorUser->user_id)->update([
                    'first_name' => $request->input('first_name'),
                    'last_name' => $request->input('last_name'),
                    'state_id' => $state->state_id,
                    'city' => $request->input('city') ?? '',
                    'apt_suite_unit' => $request->input('apt_unit') ?? '',
                    'street_address' => $request->input('address1') ?? '',
                    'phone_number' => $request->input('phone_number') ?? '',
                ]);
            }

            Vendor::where('vendor_id', $vendorOnboarding->vendor->vendor_id)->update($updateData);

            $vendorOnboarding->vendor_onboarding_status_id = $newStatus->vendor_onboarding_status_id;

            $vendorOnboarding->save();
            $vendorOnboarding->refresh();
            DB::commit();

            return new BasicInfoResource($vendorOnboarding);
        } catch (Throwable $exception) {
            DB::rollBack();

            return $this->handleApiExceptions($exception, 'Set onboarding basic failed');
        }
    }

    public function getVendorOnboarding(Request $request): JsonResponse|VendorOnboardingResource
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $userId = $user->user_id;
            $onboardingId = $request->query('data');
            $isThirdPartyUser = $user->user_type == UserTypes::VENDOR();

            // Only allow the vendor to get access
            if (! $isThirdPartyUser) {
                throw ForbiddenException::accessDenied();
            }

            $vendorOnboarding = VendorOnboarding::with([
                'status',
                'vendor.vendorServices.problemDiagnosis.subCategory.problemCategory',
                'vendor.vendorServiceArea:vendor_service_area_id,vendor_service_area_uuid,zip_codes,radius',
            ])
                ->whereUuid($onboardingId)
                ->firstOrFail();

            if ($vendorOnboarding->user_id != $userId) {
                throw ForbiddenException::accessDenied();
            }

            return new VendorOnboardingResource($vendorOnboarding);
        } catch (Throwable $exception) {
            return $this->handleApiExceptions($exception, 'Get onboarding details API Failed');
        }
    }

    public function getStatus(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $userId = $user->user_id;
            $isThirdPartyUser = $user->user_type == UserTypes::VENDOR();

            // Only allow the vendor to get access
            if (! $isThirdPartyUser) {
                throw ForbiddenException::accessDenied();
            }

            $vendor = Vendor::with([
                'vendorOnboardings' => function ($query) {
                    $query->select('vendor_id', 'vendor_onboarding_uuid', 'vendor_onboarding_status_id', 'organization_id')
                        ->with([
                            'status' => function ($q) {
                                $q->select('vendor_onboarding_status_id', 'slug', 'label');
                            },
                            'organization' => function ($q) {
                                $q->select('organization_id', 'name');
                            },
                        ])
                        ->orderBy('created_at', 'desc');
                },
            ])
                ->whereHas('vendorUsers', function ($query) use ($userId) {
                    $query->where('user_id', $userId);
                })->first();

            // Generate signed url
            $signedUrl = OnboardingHelper::generateSignedUrl(
                'vendor_onboarding.invite',
                $vendor->vendorOnboardings->first()->vendor_onboarding_uuid,
                $vendor->vendorOnboardings->first()->organization->name,
                config('services.cognito.provider.onboarding_link_expiration') ?? 10080
            );

            // Generate frontend
            $url = parse_url($signedUrl);
            $queryString = '';
            if (isset($url['query']) && ! empty($url['query'])) {
                $queryString = '?' . $url['query'];
            }
            $frontendUrl = $queryString;

            $resData = [
                'onboarding' => [
                    'vendor_id' => $vendor->vendor_uuid,
                    'onboarding_id' => $vendor->vendorOnboardings->first()->vendor_onboarding_uuid,
                    'onboarding_step' => [
                        'slug' => $vendor->vendorOnboardings->first()->status->slug,
                        'label' => $vendor->vendorOnboardings->first()->status->label,
                    ],
                    'onboarding_status' => $vendor->on_boarding_status,
                    'signed_url_params' => $frontendUrl,
                ],
                'profile' => [
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'user_type' => $user->user_type,
                ],
            ];

            return $this->successResponse($resData);
        } catch (Throwable $exception) {
            return $this->handleApiExceptions($exception, 'Get onboarding details API Failed');
        }
    }

    public function setStatus(VendorOnboardingSetStatusRequest $request): JsonResponse
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $userId = $user->user_id;
            $onboardingId = $request->input('onboardingId');
            $status = $request->input('status');

            DB::beginTransaction();

            // Get vendor onboarding
            $vendorOnboarding = VendorOnboarding::with([
                'status:vendor_onboarding_status_id,sort_order',
                'vendor:vendor_id,on_boarding_status',
            ])
                ->whereUuid($onboardingId)
                ->firstOrFail();

            // Get new status
            $newStatus = VendorOnboardingStatus::where('slug', $status)->firstOrFail();

            $isApprovedStatus = $newStatus->slug == VendorOnboardingStatuses::APPROVED();
            $isSetPasswordStatus = $newStatus->slug == VendorOnboardingStatuses::SET_PASSWORD();
            $isAccountUser = $user->user_type == UserTypes::ACCOUNT_USER();

            // Changing the status back to set-password is not allowed
            if ($isSetPasswordStatus && $vendorOnboarding->status->sort_order > $newStatus->sort_order) {
                throw new CouldNotPerformTransition;
            }

            // Only allow the vendor who owns the onboarding can update the status (except approve)
            if (! $isAccountUser && ! $isApprovedStatus && $vendorOnboarding->user_id !== $userId) {
                throw ForbiddenException::accessDenied();
            }

            // Only allow coordinator to approve onboarding
            if ($isApprovedStatus && ! $isAccountUser) {
                throw ForbiddenException::accessDenied();
            }

            // Only allow coordinator with correct organization can approve the onboarding
            if ($isApprovedStatus && $isAccountUser) {
                $accountOrganization = $request->user()?->organization;
                if ($vendorOnboarding->organization_id != $accountOrganization->organization_id) {
                    throw ForbiddenException::accessDenied();
                }
            }

            // Change vendor status if status is approved
            if ($isApprovedStatus) {
                $vendorOnboarding->vendor->on_boarding_status = VendorOnBoardingStatusEnum::COMPLETE();
                $vendorOnboarding->vendor->save();
            }

            $vendorOnboarding->vendor_onboarding_status_id = $newStatus->vendor_onboarding_status_id;
            $vendorOnboarding->save();

            DB::commit();

            return response()->json([
                'status' => [
                    'slug' => $status,
                    'name' => VendorOnboardingStatuses::label($status),
                ],
            ], 200);
        } catch (Throwable $exception) {
            DB::rollBack();

            return $this->handleApiExceptions($exception, 'Get onboarding set status API Failed');
        }
    }

    public function setServices(VendorOnboardingSetServicesRequest $request): JsonResponse
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $userId = $user->user_id;
            $onboardingId = $request->input('onboardingId');
            $services = $request->input('services');
            $isThirdPartyUser = $user->user_type == UserTypes::VENDOR();

            // Only allow the vendor to get access
            if (! $isThirdPartyUser) {
                throw ForbiddenException::accessDenied();
            }

            DB::beginTransaction();

            // Get vendor onboarding
            $vendorOnboarding = VendorOnboarding::with([
                self::STATUS_SELECT_FIELDS,
            ])
                ->whereUuid($onboardingId)
                ->firstOrFail();

            $newStatus = VendorOnboardingStatus::where('slug', VendorOnboardingStatuses::SERVICE_AREA())->firstOrFail();

            // Only allow the vendor that owns the onboarding can update the services offered
            if ($vendorOnboarding->user_id !== $userId) {
                throw ForbiddenException::accessDenied();
            }

            // Only allow update if vendor onboarding have correct status
            if ($vendorOnboarding->status->slug !== VendorOnboardingStatuses::SERVICE_OFFERED()) {
                throw new CouldNotPerformTransition;
            }

            // Save the service offered
            $selectedSkills = $this->processServices($services, $vendorOnboarding);

            // Change status
            $vendorOnboarding->vendor_onboarding_status_id = $newStatus->vendor_onboarding_status_id;

            $vendorOnboarding->save();
            $vendorOnboarding->refresh();

            DB::commit();

            return response()->json([
                'selected_skills' => $selectedSkills,
            ], 200);
        } catch (Throwable $exception) {
            DB::rollBack();

            return $this->handleApiExceptions($exception, 'Onboarding set services API Failed');
        }
    }

    public function setServiceAreas(VendorOnBoardingSetServiceAreasRequest $request): JsonResponse|ServiceAreaResource
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $userId = $user->user_id;
            $onboardingId = $request->input('onboardingId');
            $zipCodes = $request->input('zip_codes');
            $radius = $request->input('radius');
            $isThirdPartyUser = $user->user_type == UserTypes::VENDOR();

            // Only allow the vendor to get access
            if (! $isThirdPartyUser) {
                throw ForbiddenException::accessDenied();
            }

            DB::beginTransaction();

            // Get vendor onboarding
            $vendorOnboarding = VendorOnboarding::with([
                self::STATUS_SELECT_FIELDS,
            ])
                ->whereUuid($onboardingId)
                ->firstOrFail();

            $newStatus = VendorOnboardingStatus::where('slug', VendorOnboardingStatuses::UNDER_REVIEW())->firstOrFail();

            // Only allow the vendor that owns the onboarding can update the service areas
            if ($vendorOnboarding->user_id !== $userId) {
                throw ForbiddenException::accessDenied();
            }

            // Only allow update if vendor onboarding have correct status
            if ($vendorOnboarding->status->slug !== VendorOnboardingStatuses::SERVICE_AREA()) {
                throw new CouldNotPerformTransition;
            }

            // Save service Areas
            $serviceArea = VendorServiceArea::updateOrCreate(
                [
                    'vendor_id' => $vendorOnboarding->vendor_id,
                ],
                [
                    'vendor_id' => $vendorOnboarding->vendor_id,
                    'radius' => $radius,
                    'zip_codes' => $zipCodes,
                ]
            );

            $vendorOnboarding->vendor_onboarding_status_id = $newStatus->vendor_onboarding_status_id;

            $vendorOnboarding->save();
            $vendorOnboarding->refresh();

            DB::commit();

            return new ServiceAreaResource($serviceArea);

        } catch (Throwable $exception) {
            DB::rollBack();

            return $this->handleApiExceptions($exception, 'Onboarding set service areas API Failed');
        }
    }

    private function processServices(array $services, VendorOnboarding $vendorOnboarding): array
    {
        $selectedSkills = [
            'selected_diagnosis_ids' => [],
            'selected_sub_category_ids' => [],
            'selected_problem_category_ids' => [],
        ];

        foreach ($services as $service) {
            $subCategory = ProblemSubCategory::with(['problemCategory', 'problemDiagnoses'])
                ->whereUuid($service['problem_sub_category_id'])
                ->firstOrFail();

            $category = $subCategory->problemCategory;
            $diagnoses = $subCategory->problemDiagnoses;

            foreach ($diagnoses as $diagnosis) {
                VendorService::updateOrCreate(
                    [
                        'vendor_id' => $vendorOnboarding->vendor_id,
                        'organization_id' => $vendorOnboarding->organization_id,
                        'problem_category_id' => $category->problem_category_id,
                        'problem_sub_category_id' => $subCategory->problem_sub_category_id,
                        'problem_diagnosis_id' => $diagnosis->problem_diagnosis_id,
                    ],
                    [
                        'updated_at' => now(),
                    ]
                );

                $uuid = $diagnosis->problem_diagnosis_uuid;
                if (! in_array($uuid, $selectedSkills['selected_diagnosis_ids'])) {
                    $selectedSkills['selected_diagnosis_ids'][] = $uuid;
                }
            }

            $subCategoryUuid = $subCategory->problem_sub_category_uuid;
            if (! in_array($subCategoryUuid, $selectedSkills['selected_sub_category_ids'])) {
                $selectedSkills['selected_sub_category_ids'][] = $subCategoryUuid;
            }

            $categoryUuid = $category->problem_category_uuid;
            if (! in_array($categoryUuid, $selectedSkills['selected_problem_category_ids'])) {
                $selectedSkills['selected_problem_category_ids'][] = $categoryUuid;
            }
        }

        return $selectedSkills;
    }

    private function minutesToDaysSimple($minutes)
    {
        return $minutes / (60 * 24) . ' days';
    }
}

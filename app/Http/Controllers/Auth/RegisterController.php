<?php

namespace App\Http\Controllers\Auth;

use App\Enums\Boolean;
use App\Enums\DomainType;
use App\Enums\UserTypes;
use App\Enums\VendorOnboardingStatuses;
use App\Exceptions\ForbiddenException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Exceptions\Organization\Public\ClientCredentialMissingException;
use App\Exceptions\Organization\Public\PublicApiNotEnabledException;
use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\PublicApiAuthenticateRequest;
use App\Http\Requests\Auth\SearchAddressRequest;
use App\Http\Requests\Auth\SetVendorPasswordRequest;
use App\Http\Resources\APITokenResource;
use App\Http\Resources\SearchAddressResource;
use App\Mail\PasswordChangedMail;
use App\Mail\PasswordResetMail;
use App\Models\Country;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\OrganizationIdentityProvider;
use App\Models\OrganizationVendor;
use App\Models\Role;
use App\Models\Timezone;
use App\Models\User;
use App\Models\VendorOnboarding;
use App\Models\VendorOnboardingStatus;
use App\Models\VendorUser;
use App\Packages\OrganizationRolePermission\Exceptions\OrganizationNotFound;
use App\Services\Aws\LocationService;
use App\Traits\ApiExceptionHandler;
use App\Traits\ApiResponse;
use Aws\CognitoIdentityProvider\CognitoIdentityProviderClient;
use Aws\CognitoIdentityProvider\Exception\CognitoIdentityProviderException;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;
use Spatie\ModelStates\Exceptions\CouldNotPerformTransition;
use Throwable;

class RegisterController extends Controller
{
    use ApiExceptionHandler;
    use ApiResponse;

    protected CognitoIdentityProviderClient $cognitoClient;

    private $locationService;

    /**
     * RegisterController constructor.
     */
    public function __construct(?CognitoIdentityProviderClient $cognitoClient = null, ?LocationService $locationService = null)
    {
        $region = config('services.cognito.region');

        // Create a CognitoIdentityProviderClient object
        // Note: Added the optional input to be able to use on unit test mock
        $this->cognitoClient = $cognitoClient ?? new CognitoIdentityProviderClient([
            'region' => $region,
            'version' => 'latest',
        ]);
        $this->locationService = $locationService ?? new LocationService([
            'region' => 'us-east-1',
            'version' => 'latest',
        ]);
    }

    /**
     * Send reset link email
     */
    public function forgotPassword(Request $request): JsonResponse
    {
        // First return - validation failure
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
            'client_key' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->unprocessableEntity([
                'message' => __('Validation failed'),
                'errors' => $validator->errors(),
            ]);
        }

        // Second return - user not found
        try {
            $user = User::select('user_id', 'email', 'user_uuid')
                ->where('email', $request->input('email'))
                ->firstOrFail();
        } catch (ModelNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get User details API Failed due to ' . get_class($exception));

            return Response::notFound(message: $exception->getMessage());
        }

        // Process the request
        $signedUrl = Helper::generateSecureLink($user->user_uuid, config('services.cognito.provider.reset_link_expiration'));
        $url = parse_url($signedUrl);
        $path = $url['path'];
        $pathSegments = explode('/', $path);
        $id = end($pathSegments);
        $query = '/verify/' . $id . (isset($url['query']) && ! empty($url['query']) ? '?' . $url['query'] : '');
        $frontendUrl = $request->input('client_key') . $query;
        $expiration = config('services.cognito.provider.reset_link_expiration') . ' minutes';

        return $this->sendEmail($request, $frontendUrl, $expiration);
        // Third return - success case

    }

    public function resetPassword(Request $request): JsonResponse
    {
        try {
            // Early validation failures will return from validateRequest()
            if ($errorResponse = $this->validateRequest($request)) {
                return $errorResponse;
            }

            return $this->processPasswordReset($request);
        } catch (Exception $e) {
            return Response::internalServerError();
        }
    }

    /**
     * Validate reset link email
     */
    public function verify(Request $request, $id): JsonResponse
    {
        try {
            if (! $request->hasValidSignature()) {
                return response()->unprocessableEntity(message: __('Invalid or expired URL.'));
            }
            $signedUrl = $request->fullUrl();

            return response()->json([
                'success' => true,
                'id' => $id,
                'signedUrl' => $signedUrl,
            ]);
        } catch (Exception $exception) {

            return Response::internalServerError();
        }
    }

    /**
     * Search address using AWS Location Services
     */
    public function searchAddress(SearchAddressRequest $request): AnonymousResourceCollection|JsonResponse
    {
        try {

            $query = $request->input('query');

            $apiKey = Helper::getLocationApiKey();
            $apiResponse = $this->locationService->searchPlace($query, [
                'FilterCountries' => ['USA'],
                'MaxResults' => 20,
            ], $apiKey);

            if ($apiResponse['Results'] === []) {
                return response()->unprocessableEntity(message: __('No locations found'));
            }
            $filteredResults = array_filter(
                $apiResponse['Results'],
                function ($result) {
                    return isset($result['Place']['PostalCode']);
                }
            );

            return SearchAddressResource::collection($filteredResults);
        } catch (Exception $exception) {
            Helper::exceptionLog($exception, message: 'Get location API Failed [Exception]');

            return Response::internalServerError();
        }
    }

    public function getVendorOnboardingInvite(Request $request): JsonResponse
    {
        try {
            $onboardingId = $request->query('data');

            // Get onboarding details
            $vendorOnboarding = VendorOnboarding::with([
                'vendor:vendor_id,email,first_name,last_name',
                'status:vendor_onboarding_status_id,slug,label',
            ])
                ->whereUuid($onboardingId)
                ->firstOrFail();

            DB::beginTransaction();

            $email = $vendorOnboarding->vendor->email;

            $resData = [
                'onboarding_details' => [
                    'status' => [
                        'slug' => $vendorOnboarding->status->slug,
                        'label' => $vendorOnboarding->status->label,
                    ],
                ],
                'auth' => [
                    'email' => $email,
                ],
            ];

            if (in_array($vendorOnboarding->status->slug, ['initial', 'set-password'])) {
                // Get email and generate password
                $passwordCombinationArray = [
                    Str::password(15, numbers: false, symbols: false),
                    '@',
                    random_int(0, 9999),
                ];
                $temporaryPassword = implode('', $passwordCombinationArray);

                // Check if user exist
                $userExists = User::where('email', $email)->where('user_type', UserTypes::VENDOR())->exists();

                if ($userExists) {
                    // Set new password - set to permanent
                    $this->cognitoClient->adminSetUserPassword([
                        'UserPoolId' => config('services.cognito.provider.user_pool_id'),
                        'Username' => $email,
                        'Password' => $temporaryPassword,
                        'Permanent' => true,
                    ]);
                } else {
                    // Create user on cognito
                    $createdUser = $this->cognitoClient->adminCreateUser([
                        'UserPoolId' => config('services.cognito.provider.user_pool_id'),
                        'Username' => $email,
                        'TemporaryPassword' => $temporaryPassword,
                        'UserAttributes' => [
                            ['Name' => 'email', 'Value' => $email],
                            ['Name' => 'name', 'Value' => trim($vendorOnboarding->vendor->first_name . ' ' . $vendorOnboarding->vendor->last_name)],
                            ['Name' => 'email_verified', 'Value' => 'True'],
                        ],
                        'DesiredDeliveryMediums' => ['EMAIL'],
                        'ForceAliasCreation' => true,
                        'MessageAction' => 'SUPPRESS',
                    ]);

                    // Set new password - set to permanent
                    $this->cognitoClient->adminSetUserPassword([
                        'UserPoolId' => config('services.cognito.provider.user_pool_id'),
                        'Username' => $email,
                        'Password' => $temporaryPassword,
                        'Permanent' => true,
                    ]);

                    // Create on foresight
                    $userData = $createdUser->get('User');
                    $foresightCreatedUser = User::create([
                        'first_name' => $vendorOnboarding->vendor->first_name,
                        'last_name' => $vendorOnboarding->vendor->last_name,
                        'email' => $email,
                        'cognito_user_id' => $userData['Username'] ?? '',
                        'cognito_user_name' => $userData['Username'] ?? '',
                        'organization_id' => $vendorOnboarding->organization_id,
                        'user_type' => UserTypes::VENDOR(),
                        'country_id' => Country::where('alpha3_code', 'USA')->firstOrFail(['country_id'])->country_id,
                        'timezone_id' => Timezone::where('name', config('settings.default_timezone'))->first()?->timezone_id,
                    ]);

                    // Associate organization to vendor
                    OrganizationVendor::firstOrCreate([
                        'organization_id' => $vendorOnboarding->organization_id,
                        'vendor_id' => $vendorOnboarding->vendor_id,
                    ]);

                    // Associate user to vendor
                    VendorUser::firstOrCreate([
                        'vendor_id' => $vendorOnboarding->vendor_id,
                        'user_id' => $foresightCreatedUser->user_id,
                    ]);

                    // Assign role to user
                    $role = Role::select('role_id')
                        ->where('organization_id', $vendorOnboarding->organization_id)
                        ->where('name', 'Vendor')
                        ->firstOrFail();
                    $foresightCreatedUser->assignRole($role->role_id);

                    // Associate the user to the onboarding
                    $vendorOnboarding->user_id = $foresightCreatedUser->user_id;
                }

                // Add auth data
                $resData['auth'] = [
                    'email' => $email,
                    'temporary_password' => $temporaryPassword,
                ];
            }

            $vendorOnboarding->access_count = $vendorOnboarding->access_count + 1;

            $vendorOnboarding->save();

            DB::commit();

            $response = $this->successResponse($resData);

            return $response
                ->header('Cache-Control', 'no-store, no-cache, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');
        } catch (Throwable $exception) {
            DB::rollBack();

            return $this->handleApiExceptions($exception, 'Onboarding set password API Failed');
        }
    }

    public function setVendorPassword(SetVendorPasswordRequest $request): JsonResponse
    {
        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $onboardingId = $request->input('onboardingId');
            $password = $request->input('password');
            $userId = $user->user_id;
            $isThirdPartyUser = $user->user_type == UserTypes::VENDOR();

            // Only allow the vendor to get access
            if (! $isThirdPartyUser) {
                throw ForbiddenException::accessDenied();
            }

            DB::beginTransaction();

            $vendorOnboarding = VendorOnboarding::with([
                'status:vendor_onboarding_status_id,sort_order,slug',
                'vendor:vendor_id,email',
            ])
                ->whereUuid($onboardingId)
                ->firstOrFail();

            $newStatus = VendorOnboardingStatus::where('slug', VendorOnboardingStatuses::BASIC_INFO())->firstOrFail();

            // Only allow the vendor that owns the onboarding can update the services offered
            if ($vendorOnboarding->user_id !== $userId) {
                throw ForbiddenException::accessDenied();
            }

            // Only allow update if vendor onboarding have correct status
            if ($vendorOnboarding->status->slug !== VendorOnboardingStatuses::SET_PASSWORD()) {
                throw new CouldNotPerformTransition;
            }

            // Set password
            $email = $vendorOnboarding->vendor->email;
            $this->cognitoClient->adminSetUserPassword([
                'UserPoolId' => config('services.cognito.provider.user_pool_id'),
                'Username' => $email,
                'Password' => $password,
                'Permanent' => true,
            ]);

            $vendorOnboarding->vendor_onboarding_status_id = $newStatus->vendor_onboarding_status_id;

            $vendorOnboarding->save();
            $vendorOnboarding->refresh();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => __('Success'),
            ]);
        } catch (Throwable $exception) {
            DB::rollBack();

            return $this->handleApiExceptions($exception, 'Onboarding set password API Failed');
        }
    }

    /**
     * Signup new user to aws cognito
     */
    public function signup(Request $request): JsonResponse
    {
        if (is_null($request->header('User-Pool-Id'))) {
            return response()->unprocessableEntity(message: __('Invalid user pool id'));
        }

        $request->validate([
            'email' => 'required|email',
            'first_name' => 'required|string',
            'last_name' => 'nullable|string',
            'password' => [
                'required',
                Password::min(8)
                    ->mixedCase()
                    ->numbers()
                    ->symbols(),
            ],
            'password_confirmation' => 'required|same:password',
        ]);

        $organization = Organization::where('user_pool_id', $request->header('User-Pool-Id'))
            ->first(['user_pool_api_client_id', 'user_pool_api_client_secret', 'organization_id']);

        if (is_null($organization)) {
            return response()->unprocessableEntity(message: __('Invalid user pool id'));
        }

        $user_exists_in_user_pool = User::where('email', $request->input('email'))
            ->where('organization_id', $organization->organization_id)
            ->where('user_type', '<>', UserTypes::TECHNICIAN())
            ->exists();

        if ($user_exists_in_user_pool) {
            return response()->unprocessableEntity(message: __('User with this email already exists.'));
        }

        $request->merge(['organization_id' => $organization->organization_id]);

        $appClientId = $organization->user_pool_api_client_id;
        $clientSecret = $organization->user_pool_api_client_secret;

        if (is_null($appClientId) || is_null($clientSecret)) {
            return response()->unprocessableEntity(message: __('Invalid user pool id or client secret'));
        }

        $message = $request->input('email') . $appClientId;
        $secretHash = base64_encode(hash_hmac('sha256', $message, $clientSecret, true));
        $name = trim($request->input('first_name') . ' ' . $request->input('last_name'));

        try {
            $result = $this->cognitoClient->signUp([
                'ClientId' => $appClientId,
                'Username' => $request->input('email'),
                'Password' => $request->input('password'),
                'SecretHash' => $secretHash,
                'UserAttributes' => [
                    [
                        'Name' => 'email',
                        'Value' => $request->input('email'),
                    ],
                    [
                        'Name' => 'name',
                        'Value' => $name,
                    ],
                ],
            ]);

            if ($result) {
                $request->merge([
                    'cognito_user_id' => $result->get('UserSub'),
                    'cognito_user_name' => $result->get('UserSub'),
                    'country_id' => Country::where('alpha3_code', 'USA')->firstOrFail(['country_id'])->country_id,
                ]);
                //If successful, create the user in local db
                $user = User::create($request->only(
                    'first_name',
                    'last_name',
                    'email',
                    'cognito_user_id',
                    'cognito_user_name',
                    'organization_id',
                    'country_id'
                ));

                return response()->json([
                    'user_id' => $user->user_id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'cognito_user_id' => $user->cognito_user_id,
                    'user_confirmed' => $result->get('UserConfirmed'),
                    'code_delivery_details' => $result->get('CodeDeliveryDetails'),
                ]);
            }
        } catch (CognitoIdentityProviderException $e) {
            Helper::exceptionLog(exception: $e, message: 'Signup API Failed[CognitoIdentityProviderException]', notify: true);

            // If the token is invalid, the getUser API will throw an exception
            return response()->unprocessableEntity(message: $e->getAwsErrorMessage());
        }

        return response()->internalServerError(message: __('Provider registration failed'));
    }

    /**
     * create a new customer account
     *
     * @throws Exception
     */
    public function createOrganization(Request $request): JsonResponse
    {
        if ($request->getUser() !== config('settings.public_api.basic_auth.username') || $request->getPassword() !== config('settings.public_api.basic_auth.password')) {
            return Response::unauthorized('Unauthorized');
        }
        $request->validate([
            'email' => 'required|email|unique:users',
            'first_name' => 'required|string',
            'last_name' => 'sometimes|string',
            'password' => 'required|string',
            'domain' => [
                'required',
                'unique:organizations,domain',
                "regex:/^(?!(www|http|https))\w+(.\w+)+$/",
            ],
            'organization_name' => 'required|string',
        ], [
            'domain.regex' => 'Provide a valid domain',
        ]);

        return $this->setupUserPool($request);
    }

    /**
     * Create new user pool
     */
    public function setupUserPool(Request $request): JsonResponse
    {
        try {
            $userPool = $this->createUserPool($request);

            $userPoolResult = $userPool->get('UserPool');
            $userPoolId = $userPoolResult['Id'];
            $userPoolName = $userPoolResult['Name'];
            $userPoolClient = null;
            if ($userPoolId) {
                $userPoolClient = $this->createUserPoolClient($userPoolId, $userPoolName);
                $clientAppClientId = $userPoolClient->get('UserPoolClient')['ClientId'];

                $userPoolClientWithSecret = $this->createUserPoolClient($userPoolId, $userPoolName, true);
                $apiClientAppClientId = $userPoolClientWithSecret->get('UserPoolClient')['ClientId'];
                $apiClientAppSecretKey = $userPoolClientWithSecret->get('UserPoolClient')['ClientSecret'];

                $message = $request->input('email') . $apiClientAppClientId;
                $secretHash = base64_encode(hash_hmac('sha256', $message, $apiClientAppSecretKey, true));
                $name = trim($request->input('first_name') . ' ' . $request->input('last_name'));
                $signupResult = $this->cognitoClient->signUp([
                    'ClientId' => $apiClientAppClientId,
                    'Username' => $request->input('email'),
                    'Password' => $request->input('password'),
                    'SecretHash' => $secretHash,
                    'UserAttributes' => [
                        [
                            'Name' => 'email',
                            'Value' => $request->input('email'),
                        ],
                        [
                            'Name' => 'name',
                            'Value' => $name,
                        ],
                    ],
                ]);

                if ($signupResult) {
                    $request->merge([
                        'cognito_user_id' => $signupResult->get('UserSub'),
                        'cognito_user_name' => $signupResult->get('UserSub'),
                        'country_id' => Country::where('alpha3_code', 'USA')->firstOrFail(['country_id'])->country_id,
                        'name' => $name,
                        'timezone_id' => Timezone::where('name', config('settings.default_timezone'))->first()?->timezone_id,
                    ]);
                    //If successful, create the user in local db
                    $user = User::create($request->only(
                        'name',
                        'first_name',
                        'last_name',
                        'email',
                        'cognito_user_id',
                        'cognito_user_name',
                        'country_id',
                        'timezone_id'
                    ));

                    $organization = Organization::create([
                        'name' => $userPoolName,
                        'domain_type' => DomainType::SUB_DOMAIN,
                        'domain' => $request->input('domain'),
                        'user_pool_id' => $userPoolId,
                        'user_pool_app_client_id' => $clientAppClientId,
                        'user_pool_api_client_id' => $apiClientAppClientId,
                        'user_pool_api_client_secret' => $apiClientAppSecretKey,
                    ]);

                    $organization->features()->sync(
                        Feature::pluck('feature_id')->toArray()
                    );

                    $user->organization_id = $organization->organization_id;
                    $user->save();

                    $organization->loadMissing('features.permissions');

                    $this->syncUserRolePermissions($organization, $user);

                    return response()->json([
                        'user_id' => $user->user_id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'cognito_user_id' => $user->cognito_user_id,
                        'user_confirmed' => $signupResult->get('UserConfirmed'),
                        'code_delivery_details' => $signupResult->get('CodeDeliveryDetails'),
                        'user_pool_id' => $userPoolId,
                        'user_pool_app_client_id' => $clientAppClientId,
                    ]);
                }
            }
        } catch (CognitoIdentityProviderException $exception) {
            Helper::exceptionLog($exception, message: 'Organization create[CognitoIdentityProviderException]', notify: true);

            return response()->unprocessableEntity(message: $exception->getAwsErrorMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog($exception, message: 'Organization create error[Exception]', notify: true);

            return Response::internalServerError();
        }

        return response()->internalServerError(message: __('User pool creation failed'));
    }

    /**
     * Get client configurations for the current login session.
     */
    public function findLoginConfig(Request $request): JsonResource|JsonResponse
    {
        $request->validate([
            'client_key' => 'required|string',
        ]);

        $clientKey = $request->input('client_key');

        $vendorDomain = config('services.cognito.provider.vendor_domain');

        if ($clientKey === $vendorDomain) {
            return new JsonResource([
                'region' => 'us-east-1',
                'user_pool_id' => config('services.cognito.provider.user_pool_id'),
                'client_id' => config('services.cognito.provider.client_id'),
                'template' => 'default',
                'auth_domain' => config('services.cognito.provider.auth_domain'),
                'providers' => [],
            ]);
        } else {
            $organization = Organization::where('domain', $clientKey)->first([
                'user_pool_id',
                'user_pool_app_client_id',
                'template',
                'user_pool_domain',
                'organization_id',
            ]);

            if (! $organization) {
                return response()->unprocessableEntity(message: __('Invalid client key'));
            }

            $userPoolId = $organization->user_pool_id;
            $clientId = $organization->user_pool_app_client_id;
            $authDomain = $organization->user_pool_domain;
            $providers = [];

            $organization->identityProviders->each(function (OrganizationIdentityProvider $identityProvider) use (&$providers) {
                $provider = [
                    'enabled' => true,
                    'provider_name' => $identityProvider->name,
                    'provider' => $identityProvider->type,
                ];
                if (! empty($identityProvider->client_id)) {
                    $provider['client_id'] = $identityProvider->client_id;
                }

                $providers[] = $provider;
            });

            if ($request->input('client_type') === 'provider') {
                $userPoolId = config('services.cognito.provider.user_pool_id');
                $clientId = config('services.cognito.provider.client_id');
                $authDomain = config('services.cognito.provider.auth_domain');
            }

            return new JsonResource([
                'region' => 'us-east-1',
                'user_pool_id' => $userPoolId,
                'client_id' => $clientId,
                'template' => $organization->template,
                'auth_domain' => $authDomain,
                'providers' => $providers,
            ]);
        }
    }

    public function findProviderLoginConfig(): JsonResource
    {
        $userPoolId = config('services.cognito.provider.user_pool_id');
        $clientId = config('services.cognito.provider.client_id');
        $authDomain = config('services.cognito.provider.auth_domain');

        return new JsonResource([
            'region' => 'us-east-1',
            'user_pool_id' => $userPoolId,
            'client_id' => $clientId,
            'auth_domain' => $authDomain,
        ]);
    }

    /**
     * Generate token
     */
    public function authenticateClientCredential(PublicApiAuthenticateRequest $request): APITokenResource|JsonResponse
    {
        try {
            $clientId = $request->getUser();
            $secretKey = $request->getPassword();

            $organization = Organization::where('user_pool_public_api_client_id', $clientId)
                ->first();

            if (empty($organization)) {
                throw new OrganizationNotFound(message: __('Invalid credentials'));
            }

            if ($organization->user_pool_public_api_client_secret !== $secretKey) {
                throw new OrganizationNotFound(message: __('Invalid credentials'));
            }

            if ($organization->public_api_enabled !== Boolean::YES()) {
                throw new PublicApiNotEnabledException(__('Public api is not enabled.'));
            }

            if (empty($organization->user_pool_public_api_client_id)) {
                throw new ClientCredentialMissingException(__('Invalid request'));
            }

            if (empty($organization->user_pool_public_api_client_secret)) {
                throw new ClientCredentialMissingException(__('Invalid request'));
            }

            $response = Http::withBasicAuth($organization->user_pool_public_api_client_id, $organization->user_pool_public_api_client_secret)
                ->asForm()
                ->contentType('application/x-www-form-urlencoded')
                ->post("{$organization->user_pool_public_api_client_endpoint}/oauth2/token", [
                    'grant_type' => 'client_credentials',
                    'scope' => $request->input('scope'),
                ]);

            return new APITokenResource($response->json());
        } catch (OrganizationNotFound $exception) {
            Helper::exceptionLog($exception, message: 'Trying to auth public api, organization not found due to invalid client IO');

            return Response::notFound($exception->getMessage());
        } catch (ClientCredentialMissingException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Trying to auth public api, Invalid request due to invalid client id or secret key', notify: true);

            return Response::badRequest(message: $exception->getMessage());
        } catch (PublicApiNotEnabledException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Trying to auth public api, Public api is not enabled for this organization');

            return Response::unauthorized($exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Create user pool client
     */
    protected function createUserPoolClient(string $userPoolId, string $userPoolName, bool $secret = false): \Aws\Result
    {
        $clientName = $userPoolName . ($secret ? '-api-app' : '-client-app');
        $request = request();

        return $this->cognitoClient->createUserPoolClient([
            'UserPoolId' => $userPoolId,
            'ClientName' => $clientName,
            'GenerateSecret' => $secret,
            'AllowedOAuthScopes' => [
                'phone',
                'email',
                'openid',
                'profile',
                'aws.cognito.signin.user.admin',
            ],
            'AllowedOAuthFlows' => ['code'],
            'CallbackURLs' => ["https://{$request->input('domain')}"],
            'LogoutURLs' => ["https://{$request->input('domain')}/signin"],
            'AllowedOAuthFlowsUserPoolClient' => true,
            'RefreshTokenValidity' => 30,
            'AccessTokenValidity' => 1,
            'IdTokenValidity' => 1,
            'PreventUserExistenceErrors' => 'ENABLED',
            'ExplicitAuthFlows' => [],
            'SupportedIdentityProviders' => [],
            'TokenValidityUnits' => [
                'AccessToken' => 'days',
                'IdToken' => 'days',
                'RefreshToken' => 'days',
            ],
        ]);
    }

    /**
     * Create user pool for a customer
     */
    protected function createUserPool(Request $request): \Aws\Result
    {
        $poolName = Str::slug($request->input('organization_name'));
        $userDataRequired = [
            [
                'AttributeDataType' => 'String',
                'Name' => 'email',
                'DeveloperOnlyAttribute' => false,
                'Mutable' => true,
                'Required' => true,
                'StringAttributeConstraints' => [
                    'MinLength' => '1',
                ],
            ],
            [
                'AttributeDataType' => 'String',
                'Name' => 'name',
                'DeveloperOnlyAttribute' => false,
                'Mutable' => true,
                'Required' => true,
                'StringAttributeConstraints' => [
                    'MinLength' => '1',
                ],
            ],
        ];
        $passwordPolicy = [
            'PasswordPolicy' => [
                'MinimumLength' => 8,
                'RequireLowercase' => true,
                'RequireNumbers' => true,
                'RequireSymbols' => true,
                'RequireUppercase' => true,
                'TemporaryPasswordValidityDays' => 7,
            ],
        ];
        $verificationMessageTemplate = [
            'DefaultEmailOption' => 'CONFIRM_WITH_CODE',
            'EmailMessage' => 'Your verification code is {####}',
            'EmailSubject' => 'Verify your email address',
            'SmsMessage' => 'Your verification code is {####}',
        ];

        return $this->cognitoClient->createUserPool([
            'PoolName' => $poolName,
            'Schema' => $userDataRequired,
            'Policies' => $passwordPolicy,
            'AutoVerifiedAttributes' => ['email'],
            'EmailConfiguration' => [
                'EmailSendingAccount' => 'COGNITO_DEFAULT',
            ],
            'AdminCreateUserConfig' => ['AllowAdminCreateUserOnly' => false],
            'UsernameAttributes' => ['email'],
            'VerificationMessageTemplate' => $verificationMessageTemplate,
            'MfaConfiguration' => 'OPTIONAL',
            'SmsConfiguration' => [
                'ExternalId' => config('services.cognito.sns.external_id'),
                'SnsCallerArn' => config('services.cognito.sns.arn'),
            ],
            'UsernameConfiguration' => [
                'CaseSensitive' => false,
            ],
        ]);
    }

    /**
     * Create Roles and permission for a organization
     */
    protected function syncUserRolePermissions(Organization $organization, User $user): void
    {
        $owner = Role::create(['name' => 'Owner', 'organization_id' => $organization->organization_id]);
        $features = $organization->features;
        $organizationPermissions = [];
        foreach ($features as $feature) {
            $organizationPermissions = array_merge($organizationPermissions, $feature->permissions->pluck('name')->toArray());
        }
        $owner->syncPermissions($organizationPermissions);
        $user->assignRole($owner);

        $technician = Role::updateOrCreate(['name' => 'Technician', 'organization_id' => $organization->organization_id]);

        $technicianPermissions = [
            'WorkOrderManagement.WorkOrder.list',
            'WorkOrderManagement.WorkOrder.view',
            'WorkOrderManagement.WorkOrder.update',
            'WorkOrderManagement.workOrderMedia.create',
            'WorkOrderManagement.workOrderMedia.delete',
            'WorkOrderManagement.WorkOrder.enroute',
            'WorkOrderManagement.WorkOrder.startTimer',
            'WorkOrderManagement.WorkOrder.pause',
            'WorkOrderManagement.WorkOrder.stopTrip',
            'WorkOrderManagement.WorkOrder.complete',
            'WorkOrderManagement.WorkOrder.submitQuote',
            'CalendarManagement.Calendar.view',
            'WorkOrderManagement.Trip.pause',
            'WorkOrderManagement.Trip.resume',
            'ApplicationSettings.Application.access',
            'ApplicationSettings.Application.rescheduleTrips',
            'VendorPortalManagement.WorkOrder.list',
            'VendorPortalManagement.WorkOrder.view',
        ];
        $technicianPermissions = array_filter($technicianPermissions, function ($permission) use ($organizationPermissions) {
            return in_array($permission, $organizationPermissions, true);
        });
        $technician->syncPermissions($technicianPermissions);

        // Third part vendor
        $thirdPartyVendor = Role::updateOrCreate([
            'name' => 'Vendor',
            'description' => 'Vendor',
            'organization_id' => $organization->organization_id,
        ]);
        $thirdPartyVendorPermissions = [
            'WorkOrderManagement.WorkOrder.list',
            'WorkOrderManagement.WorkOrder.view',
            'WorkOrderManagement.WorkOrder.update',
            'WorkOrderManagement.workOrderMedia.create',
            'WorkOrderManagement.workOrderMedia.delete',
            'WorkOrderManagement.WorkOrder.enroute',
            'WorkOrderManagement.WorkOrder.startTimer',
            'WorkOrderManagement.WorkOrder.pause',
            'WorkOrderManagement.WorkOrder.stopTrip',
            'WorkOrderManagement.WorkOrder.complete',
            'WorkOrderManagement.WorkOrder.submitQuote',
            'CalendarManagement.Calendar.view',
            'WorkOrderManagement.Trip.pause',
            'WorkOrderManagement.Trip.resume',
            'ApplicationSettings.Application.access',
            'ApplicationSettings.Application.rescheduleTrips',
            'VendorPortalManagement.WorkOrder.list',
            'VendorPortalManagement.WorkOrder.view',
        ];
        $thirdPartyVendorPermissions = array_filter($thirdPartyVendorPermissions, function ($permission) use ($organizationPermissions) {
            return in_array($permission, $organizationPermissions, true);
        });
        $thirdPartyVendor->syncPermissions($thirdPartyVendorPermissions);
    }

    private function sendEmail(Request $request, $frontendUrl, $expiration): JsonResponse
    {
        try {
            Mail::to($request->input('email'))->queue(new PasswordResetMail($frontendUrl, $expiration));

            return response()->json([
                'data' => 'If your email address exists in our system, you will receive a password reset link shortly.',
            ]);
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get User details API Failed due to ' . get_class($exception));

            return Response::internalServerError(message: __('User pool creation failed'));
        }
    }

    private function validateRequest(Request $request): ?JsonResponse
    {
        if ($response = $this->validateSignedUrl($request)) {
            return $response;
        }

        if ($response = $this->validatePassword($request)) {
            return $response;
        }

        return null;
    }

    private function validateSignedUrl(Request $request): ?JsonResponse
    {
        $response = null;

        if (empty($request->input('signedUrl'))) {
            $response = response()->unprocessableEntity(message: __('Missing signed URL.'));
        }

        if ($response === null) {
            $signedRequest = Helper::buildStringUrl($request->input('signedUrl'));
            if (! URL::hasValidSignature($signedRequest)) {
                $response = response()->unprocessableEntity(message: __('Invalid or expired URL.'));
            }
        }

        if ($response === null) {
            $path = parse_url($signedRequest, PHP_URL_PATH);
            if (empty(basename($path))) {
                $response = response()->unprocessableEntity(message: __('Invalid signed URL format.'));
            }
        }

        return $response;
    }

    private function validatePassword(Request $request): ?JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'password' => [
                'required',
                'min:8',
                'regex:/[0-9]/',
                'regex:/[@$!%*?&]/',
            ],
            'client_key' => 'required|string',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors();
            $message = __('Validation failed.'); // default message

            if ($errors->has('password')) {
                $message = __('Invalid password. Password must be at least 8 characters long and contain a number and a special character.');
            } elseif ($errors->has('client_key')) {
                $message = __('Client key is required.');
            }

            return response()->unprocessableEntity(message: $message);
        }

        return null;
    }

    private function processPasswordReset(Request $request): JsonResponse
    {
        $signedRequest = Helper::buildStringUrl($request->input('signedUrl'));
        $path = parse_url($signedRequest, PHP_URL_PATH);
        $id = basename($path);

        $user = User::select('user_id', 'email', 'user_uuid', 'user_type')
            ->whereUuid($id)
            ->firstOrFail();

        $clientKey = $request->input('client_key');
        $vendorDomain = config('services.cognito.provider.vendor_domain');

        $userPoolId = ($clientKey === $vendorDomain)
            ? config('services.cognito.provider.user_pool_id')
            : Organization::where('domain', $clientKey)
                ->firstOrFail(['user_pool_id'])
                ->user_pool_id;

        $this->cognitoClient->adminSetUserPassword([
            'UserPoolId' => $userPoolId,
            'Username' => $user->email,
            'Password' => $request->input('password'),
            'Permanent' => true,
        ]);

        Mail::to($user->email)->queue(new PasswordChangedMail);

        return response()->json([
            'success' => true,
            'id' => $id,
        ]);
    }
}

<?php

namespace App\Http\Controllers\Auth;

use App\Enums\UserTypes;
use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Country;
use App\Models\Organization;
use App\Models\Timezone;
use App\Models\User;
use App\Traits\ValidatesJWT;
use Aws\CognitoIdentityProvider\CognitoIdentityProviderClient;
use Aws\Exception\AwsException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use JsonException;

class SSOController extends Controller
{
    use ValidatesJWT;

    public function validateUser(Request $request): \Illuminate\Http\Response|JsonResponse
    {
        $token = $request->bearerToken();
        if (! $token) {
            return Response::unauthorized(message: __('Token is required.'));
        }

        $claims = $request->attributes->get('cognito_claims') ?? $this->validateJWT($token);

        $userPoolId = Str::afterLast($claims->iss, '/');

        $organization = Organization::where('user_pool_id', $userPoolId)
            ->firstOrFail(['user_pool_api_client_id', 'organization_id']);

        $cognitoUserId = $claims->sub;
        $username = $claims->username;

        $userDetails = $this->getCognitoUserDetails($userPoolId, $username);

        if (! empty($userDetails->get('errors'))) {
            return Response::unprocessableEntity(message: $userDetails['errors']);
        }

        $user = User::where('email', $userDetails->get('email'))
            ->where('organization_id', $organization->organization_id)
            ->where('user_type', '<>', UserTypes::TECHNICIAN())
            ->first();

        if (! $user) {
            $timezone = Timezone::where('name', config('settings.default_timezone'))->first();
            $userModel = User::create([
                'first_name' => $userDetails->get('given_name', $userDetails->get('name')),
                'last_name' => $userDetails->get('family_name'),
                'cognito_user_name' => $username,
                'email' => $userDetails->get('email'),
                'cognito_user_id' => $cognitoUserId,
                'organization_id' => $organization->organization_id,
                'country_id' => Country::where('alpha3_code', 'USA')->firstOrFail(['country_id'])->country_id,
                'timezone_id' => $timezone?->timezone_id,
            ]);

            return Response::message('Validated');
        }

        return Response::noContent();
    }

    /**
     * @return Collection<string, mixed>
     */
    public function getCognitoUserDetails(string $userPoolId, string $username): Collection
    {
        $region = config('services.cognito.region');

        $client = new CognitoIdentityProviderClient([
            'region' => $region,
            'version' => 'latest',
        ]);

        try {
            $result = $client->adminGetUser([
                'UserPoolId' => $userPoolId,
                'Username' => $username,
            ]);

            $userAttributesFromResult = json_decode(json_encode($result['UserAttributes'], JSON_THROW_ON_ERROR), false, 512, JSON_THROW_ON_ERROR);
            $userAttributes = collect();
            foreach ($userAttributesFromResult as $userAttributeResult) {
                $userAttributes->put($userAttributeResult->Name, $userAttributeResult->Value);

                if ($userAttributeResult->Name === 'identities') {
                    $userAttributes->put($userAttributeResult->Name, json_decode($userAttributeResult->Value, false, 512, JSON_THROW_ON_ERROR));
                }
            }
            $userAttributes->put('status', $result['UserStatus']);
            $userAttributes->put('username', $username);
            $userAttributes->put('user_pool_id', $userPoolId);

            return $userAttributes;
        } catch (AwsException $e) {
            // Handle any exceptions that occur during the API call
            // For example, handle the case when the user does not exist
            Helper::exceptionLog(exception: $e, message: $e->getMessage(), notify: true);

            return collect([
                'errors' => $e->getMessage(),
            ]);
        } catch (JsonException $e) {
            Helper::exceptionLog(exception: $e, message: $e->getMessage());

            return collect([
                'errors' => $e->getMessage(),
            ]);
        }
    }
}

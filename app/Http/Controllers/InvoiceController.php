<?php

namespace App\Http\Controllers;

use App\Enums\Boolean;
use App\Enums\MediaType;
use App\Exceptions\InvoiceException;
use App\Helpers\Helper;
use App\Http\Resources\Invoice\InvoiceListResource;
use App\Http\Resources\Invoice\InvoiceResource;
use App\Http\Resources\WorkOrder\Invoice\InvoiceResource as WorkOrderInvoiceResource;
use App\Models\Invoice;
use App\Models\WorkOrder;
use App\States\WorkOrders\ReadyToInvoice;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Response;

class InvoiceController extends Controller
{
    public function index(Request $request, WorkOrder $workOrder): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('viewAny', Invoice::class);
        try {

            $invoices = Invoice::with('createdByUser', 'draftedByUser')
                ->where('work_order_id', $workOrder->work_order_id)
                ->latest()
                ->get();

            return InvoiceListResource::collection($invoices);
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Invoice List failed', notify: true);

            return Response::internalServerError();
        }
    }

    public function show(Request $request, WorkOrder $workOrder, Invoice $invoice): InvoiceResource|JsonResponse
    {
        $this->authorize('view', $invoice);
        try {

            if ($workOrder->work_order_id !== $invoice->work_order_id) {
                throw InvoiceException::invalidInvoice();
            }

            $invoice->load([
                'createdByUser',
                'lineItems.quote:quote_id,quote_uuid',
                'lineItems.quoteTask:quote_task_id,quote_task_uuid,quote_task_number',
                'lineItems.serviceCall.media' => function ($query) {
                    return $query->with([
                        'workOrder:work_orders.work_order_id,work_orders.organization_id,work_orders.work_order_uuid',
                        'workOrder.organization:organizations.organization_id,organizations.domain',
                    ])->wherePivot('has_thumbnail', Boolean::YES())
                        ->whereIn('media_type', [MediaType::AFTER_MEDIA, MediaType::BEFORE_MEDIA]);
                },
                'lineItems.serviceCall.lulaAppointment:lula_appointment_id,work_order_reference_number',
                'lineItems.subsidiaries.quoteTaskMaterial:quote_task_material_id,quote_task_material_uuid,cost_type',
                'lineItems.subsidiaries.workOrderTaskMaterial:work_order_task_material_id,work_order_task_material_uuid,cost_type',
                'lineItems.subsidiaries.quoteTask:quote_task_id,quote_task_uuid',
                'lineItems.quote.serviceCalls.media' => function ($query) {
                    return $query->with([
                        'workOrder:work_orders.work_order_id,work_orders.organization_id,work_orders.work_order_uuid',
                        'workOrder.organization:organizations.organization_id,organizations.domain',
                    ])->wherePivot('has_thumbnail', Boolean::YES())
                        ->whereIn('media_type', [MediaType::AFTER_MEDIA, MediaType::BEFORE_MEDIA]);
                },
                //for ability check
                'workOrder.tasks.latestServiceCalls.createdQuote',
                'workOrder.tasks.latestServiceCalls.appointment',
            ]);

            return new InvoiceResource($invoice);
        } catch (InvoiceException $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage());

            return Response::unprocessableEntity(message: __($exception->getMessage()));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Invoice Show API Failed[Exception]', additionalInfo: [
                'workOrderId' => $workOrder->work_order_id,
                'InvoiceId' => $invoice->invoice_id,
            ], notify: true);

            return Response::internalServerError();
        }
    }

    public function workOrderInvoiceSummary(Request $request, WorkOrder $workOrder): WorkOrderInvoiceResource|InvoiceResource|JsonResponse
    {
        $this->authorize('create', [Invoice::class, $request->route('workOrder')]);
        try {

            if (! $workOrder->state->equals(ReadyToInvoice::class)) {
                throw InvoiceException::invalidWorkOrderStatus($workOrder->state->label());
            }

            $workOrder->load([
                'tasks.allServiceCalls.appointment',
                'tasks.allServiceCalls.lulaAppointment:lula_appointment_id,work_order_reference_number,service_category_label',
                'tasks.allServiceCalls.workOrderTaskMaterials',
                'tasks.allServiceCalls.media.workOrder',
                'tasks.allServiceCalls.media' => function ($query) {
                    return $query->with([
                        'workOrder:work_orders.work_order_id,work_orders.organization_id,work_orders.work_order_uuid',
                        'workOrder.organization:organizations.organization_id,organizations.domain',
                    ])->wherePivot('has_thumbnail', Boolean::YES())
                        ->whereIn('media_type', [MediaType::AFTER_MEDIA, MediaType::BEFORE_MEDIA]);
                },
                'tasks.allServiceCalls.vendorExternalInvoices' => function ($query) {
                    return $query->select('description', 'amount_in_cents', 'amount_remaining_cents', 'work_order_service_call_id', 'extras');
                },
            ]);

            return new WorkOrderInvoiceResource($workOrder);
        } catch (InvoiceException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Invoice Summary failed due to ' . get_class($exception));

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Invoice Summary failed', notify: true);

            return Response::internalServerError();
        }
    }
}

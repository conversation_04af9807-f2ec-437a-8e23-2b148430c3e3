<?php

namespace App\Http\Controllers;

use App\Enums\MaterialQuantityType;
use App\Enums\Priority;
use App\Enums\PropertyAccessMethods;
use App\Enums\ScheduleTimings;
use App\Enums\Trip;
use App\Enums\WorkOrderStatus;
use App\Helpers\Helper;
use App\Http\Resources\CountryResource;
use App\Http\Resources\LookupResource;
use App\Http\Resources\ProblemCategoryResource;
use App\Http\Resources\QuantityTypeResources;
use App\Http\Resources\StateResource;
use App\Http\Resources\User\TechnicianResource;
use App\Http\Resources\WorkOrder\AssigneeListResource;
use App\Models\Country;
use App\Models\ProblemCategory;
use App\Models\State;
use App\Models\Technician;
use App\Models\User;
use App\Models\VendorOnboardingStatus;
use App\States\ServiceCalls\EnRoute;
use App\States\ServiceCalls\Working;
use Carbon\CarbonImmutable;
use Carbon\CarbonPeriodImmutable;
use Exception;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;

class LookupController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, ?string $requestTypes = null): JsonResponse|LookupResource
    {
        try {
            $requestTypes = array_filter(explode(',', $requestTypes ?? $request->get('request_types')));
            $response = [];
            foreach ($requestTypes as $requestType) {
                $response[$requestType] = match ($requestType) {
                    'states' => $this->getStates(),
                    'countries' => $this->getCountry(),
                    'problem_categories' => $this->getProblemCategory(),
                    'priorities' => $this->getPriorities(),
                    'access_methods' => $this->getPropertyAccessMethods(),
                    'technicians' => $this->getTechnicians(),
                    'assignees' => $this->getAssignees($request)->resource,
                    'quantity_types' => $this->getQuantityTypes(),
                    'end_trip_options' => $this->endTripeOptions(),
                    'schedule_timings' => collect(ScheduleTimings::all())->where('value', '<>', ScheduleTimings::ANYTIME())->toArray(),
                    'expected_durations' => $this->expectedDurations(),
                    'open_filters' => $this->getFieldAppFilters($requestType),
                    'close_filters' => $this->getFieldAppFilters($requestType),
                    'pause_filters' => $this->getFieldAppFilters($requestType),
                    'vendor_onboarding_statuses' => $this->getOnboardingStatuses(),
                    default => throw new InvalidArgumentException("Invalid argument [{$requestType}] for lookup"),
                };
            }

            return new LookupResource($response);
        } catch (InvalidArgumentException $exception) {
            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: $exception->getMessage(), notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Filters of field app
     *
     * @param  mixed  $requestType
     */
    protected function getFieldAppFilters($requestType): JsonResource
    {

        $filters = [
            'date' => [
                [
                    'value' => 'today',
                    'label' => 'Today',
                ],
                [
                    'value' => 'this_week',
                    'label' => 'This Week',
                ],
                [
                    'value' => 'this_month',
                    'label' => 'This Month',
                ],
            ],
        ];

        if ($requestType === 'open_filters') {
            // for mobile app we want to change work_in_progress to working
            $openWOStatus = collect(WorkOrderStatus::valueLabelForMobileApp('open'))
                ->map(function ($item) {
                    if ($item['value'] === WorkOrderStatus::WORK_IN_PROGRESS()) {
                        $item['value'] = Working::$name;
                    }

                    return $item;
                });

            $scheduleKey = $openWOStatus->search(fn ($item) => $item['value'] === WorkOrderStatus::SCHEDULED());

            if ($scheduleKey !== false) {
                $openWOStatus->splice($scheduleKey + 1, 0, [[
                    'label' => 'En-route',
                    'value' => EnRoute::$name,
                ]]);
            }

            $filters['status'] = $openWOStatus;
            $filters['sort_by'] = [
                [
                    'value' => 'scheduled',
                    'label' => 'Scheduled Date: Earliest',
                ],
                [
                    'value' => '-scheduled',
                    'label' => 'Scheduled Date: Latest',
                ],
            ];

        } elseif ($requestType === 'close_filters') {
            $closedWOStatus = WorkOrderStatus::valueLabelForMobileApp('closed');
            usort($closedWOStatus, fn ($a, $b) => $a['label'] <=> $b['label']);
            $filters['status'] = $closedWOStatus;

            $filters['sort_by'] = [
                [
                    'value' => 'completed',
                    'label' => 'Completion Date: Earliest',
                ],
                [
                    'value' => '-completed',
                    'label' => 'Completion Date: Latest',
                ],
            ];
        } elseif ($requestType === 'pause_filters') {
            $filters['sort_by'] = [
                [
                    'value' => 'paused',
                    'label' => 'Pause Date: Earliest',
                ],
                [
                    'value' => '-paused',
                    'label' => 'Pause Date: Latest',
                ],
            ];
        }

        return new JsonResource(collect($filters));
    }

    protected function getPriorities(): JsonResource
    {
        return new JsonResource(Priority::priorityValueLabelFormat());
    }

    protected function getPropertyAccessMethods(): JsonResource
    {
        return new JsonResource(PropertyAccessMethods::apiResponseFormat());
    }

    protected function getStates(): AnonymousResourceCollection
    {
        $states = Cache::remember(
            config('settings.cache.look_up.state.name'),
            now()->addMinutes(config('settings.cache.look_up.state.expire_time_in_minutes')),
            static fn () => State::all()
        );

        return StateResource::collection($states);
    }

    protected function getCountry(): AnonymousResourceCollection
    {
        $countries = Cache::remember(
            config('settings.cache.look_up.country.name'),
            now()->addMinutes(config('settings.cache.look_up.country.expire_time_in_minutes')),
            static fn () => Country::all()
        )->sortBy('name');

        return CountryResource::collection($countries);
    }

    protected function getProblemCategory(): AnonymousResourceCollection
    {
        $problemCategories = Cache::remember(
            config('settings.cache.look_up.problem_category.name'),
            now()->addMinutes(config('settings.cache.look_up.problem_category.expire_time_in_minutes')),
            static fn () => ProblemCategory::select('problem_category_id', 'problem_category_uuid', 'label', 'slug')
                ->with(
                    [
                        'problemSubCategories' => function (Builder $query) {
                            $query->select('problem_category_id', 'problem_sub_category_id', 'label', 'slug', 'problem_sub_category_uuid');
                            $query->orderBy('label');
                        },
                        'problemSubCategories.problemDiagnoses' => function (Builder $query) {
                            $query->select('problem_sub_category_id', 'problem_diagnosis_uuid', 'label', 'slug');
                            $query->orderBy('label');
                        },
                    ],
                )
                ->orderBy('label')
                ->get()
        );

        return ProblemCategoryResource::collection($problemCategories);
    }

    protected function getTechnicians(): AnonymousResourceCollection
    {
        $technicians = Technician::with([
            'user:user_id,user_uuid,first_name,last_name,middle_name,timezone_id',
            'user.timezone',
        ])
            ->select('technician_id', 'technician_uuid', 'user_id')
            ->get();

        $sortedData = $technicians->map(function ($technician) {
            return (object) [
                'technician_name' => $technician->user?->getName(),
                'technician_id' => $technician->technician_uuid,
                'user_id' => $technician->user?->user_uuid,
                'timezone' => $technician->user?->timezone?->name,
            ];
        })->sortBy('technician_name')->values();

        return TechnicianResource::collection($sortedData);
    }

    protected function getAssignees(Request $request): AnonymousResourceCollection
    {
        $organizationId = $request->user()?->organization_id;

        $assignees = User::whereIn('user_type', ['account_user'])
            ->where('organization_id', $organizationId)
            ->where('status', 'active')
            ->select('user_uuid', 'profile_pic', 'first_name', 'last_name', 'middle_name')
            ->orderBy('first_name')
            ->get();

        return AssigneeListResource::collection($assignees);
    }

    protected function getQuantityTypes(): AnonymousResourceCollection
    {
        return QuantityTypeResources::collection(MaterialQuantityType::allTypes());
    }

    protected function endTripeOptions(): JsonResource
    {
        $endTripOptions = [
            'work_type' => Trip::workTypeOptions(),
            'partial_complete' => Trip::partialCompleteOptions(),
            'no_work' => Trip::noWorkOptions(),
        ];

        return new JsonResource($endTripOptions);
    }

    protected function expectedDurations(): JsonResource
    {
        return Cache::remember(config('settings.cache.look_up.expected_durations.name'), now()->addMinutes(config('settings.cache.look_up.expected_durations.expire_time_in_minutes')), function () {
            $startDateTime = CarbonImmutable::now()->startOfDay();
            $endDateTime = CarbonImmutable::now()->startOfDay()->addMinutes(config('settings.cache.look_up.expected_durations.max_allowed_time_in_minutes'));
            $intervals = CarbonPeriodImmutable::create($startDateTime, '30 minutes', $endDateTime)->toArray();
            $durations = [];

            foreach ($intervals as $interval) {
                $endTime = $interval->addMinutes(30);

                if ($endTime->gt($endDateTime)) {
                    continue;
                }

                $diff = $startDateTime->diff($endTime);

                $parts = [];
                if ($diff->h > 0) {
                    $parts[] = $diff->h . ' hour' . ($diff->h > 1 ? 's' : '');
                }

                if ($diff->i > 0) {
                    $parts[] = $diff->i . ' minute' . ($diff->i > 1 ? 's' : '');
                }

                $durations[] = [
                    'label' => implode(' ', $parts),
                    'value' => $startDateTime->diffInRealMinutes($endTime),
                ];
            }

            return new JsonResource(collect($durations)->sortBy('value'));
        });
    }

    protected function getOnboardingStatuses(): JsonResource
    {
        $vendorOnboardingStatuses = VendorOnboardingStatus::select(['label', 'slug', 'sort_order'])
            ->orderBy('sort_order')
            ->get();

        return new JsonResource($vendorOnboardingStatuses);
    }
}

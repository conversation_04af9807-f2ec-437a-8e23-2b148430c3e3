<?php

namespace App\Http\Controllers;

use App\Events\Tag\TagApplied;
use App\Exceptions\NotFoundException\OrganizationNotFoundException;
use App\Helpers\Helper;
use App\Http\Filters\TagListFilter;
use App\Http\Requests\Tag\StoreRequest;
use App\Http\Requests\Tag\UpdateRequest;
use App\Http\Resources\Tag\TagResource;
use App\Models\Organization;
use App\Models\Tag;
use App\Models\WorkOrderTags;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;
use Spatie\FlareClient\Http\Exceptions\InvalidData;

class TagController extends Controller
{
    /**
     * Organization roles with permissions
     *
     * @throws AuthorizationException
     */
    public function index(Request $request, TagListFilter $filter): AnonymousResourceCollection
    {
        $this->authorize('viewAny', Tag::class);

        $tags = Tag::withCount('workOrders')
            ->filter($filter)
            ->paginate($request->input('per_page', config('pagination.role_list.per_page')));

        return TagResource::collection($tags);

    }

    /**
     * Creating tag
     *
     * @throws AuthorizationException
     */
    public function store(StoreRequest $request): TagResource|JsonResponse
    {
        $this->authorize('create', Tag::class);

        try {
            /** @var Organization $organization */
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            $tag = Tag::create([
                'name' => trim($request->input('name')),
                'slug' => Str::slug($request->input('name')),
                'color' => $request->input('color'),
                'text_color' => $request->input('text_color'),
                'organization_id' => $organization->organization_id,
            ]);

            $tag->loadCount('workOrders');
            $tag->setRelation('organization', $organization);

            TagApplied::broadcast($tag, 'created')->toOthers();

            return new TagResource($tag);
        } catch (OrganizationNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Tag create API failed[OrganizationNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Tag create API failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Show tag details
     *
     * @throws AuthorizationException
     */
    public function show(Tag $tag): TagResource
    {
        $this->authorize('view', $tag);
        $tag->loadCount('workOrders');

        return new TagResource($tag);
    }

    /**
     * Update tag details
     *
     * @throws AuthorizationException
     */
    public function update(UpdateRequest $request, Tag $tag): TagResource|JsonResponse
    {
        $this->authorize('update', $tag);

        try {
            /** @var Organization $organization */
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }
            $tag->update([
                'name' => trim($request->get('name')),
                'color' => $request->get('color'),
                'text_color' => $request->get('text_color'),
                'slug' => Str::slug($request->input('name')),
            ]);

            $tag->loadCount('workOrders');
            $tag->setRelation('organization', $organization);
            TagApplied::broadcast($tag, 'updated')->toOthers();

            return new TagResource($tag);
        } catch (OrganizationNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Tag update API failed[OrganizationNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Tag update api failed[Exception]', notify: true);

            return Response::internalServerError();
        }

    }

    /**
     * destroy tag details
     *
     * @throws AuthorizationException
     */
    public function destroy(Tag $tag, Request $request): TagResource|JsonResponse
    {
        $this->authorize('delete', $tag);

        try {
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            WorkOrderTags::where('tag_id', $tag->tag_id)->delete();

            $tag->delete();

            $tag->setRelation('organization', $organization);
            $tag->loadCount('workOrders');

            TagApplied::broadcast($tag, 'deleted')->toOthers();

            return new TagResource($tag);

        } catch (OrganizationNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Tag delete API failed[OrganizationNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (InvalidData $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Tag delete API failed [InvalidData]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Tag delete API failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }
}

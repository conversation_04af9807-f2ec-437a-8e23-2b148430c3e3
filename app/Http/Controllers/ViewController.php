<?php

namespace App\Http\Controllers;

use App\Enums\Boolean;
use App\Exceptions\NotFoundException\OrganizationNotFoundException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\DataFormatHelper;
use App\Helpers\Helper;
use App\Http\Requests\View\ConfigurationRequest;
use App\Http\Requests\View\CountRequest;
use App\Http\Requests\View\DuplicateRequest;
use App\Http\Requests\View\PinRequest;
use App\Http\Requests\View\RenameRequest;
use App\Http\Requests\View\StoreRequest;
use App\Http\Requests\View\UpdateRequest;
use App\Http\Resources\View\ConfigurationResource;
use App\Http\Resources\View\DeleteResource;
use App\Http\Resources\View\PinnedViewCount;
use App\Http\Resources\View\PinResource;
use App\Http\Resources\View\SetDefaultResource;
use App\Http\Resources\View\StoreResource;
use App\Http\Resources\View\UpdatedResource;
use App\Http\Resources\View\ViewResource;
use App\Models\View;
use App\Models\ViewType;
use App\Models\WorkOrder;
use App\Services\FilterService;
use App\Traits\ViewTrait;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;
use UnexpectedValueException;

class ViewController extends Controller
{
    use ViewTrait;

    /**
     * The filter service instance.
     */
    protected FilterService $filterService;

    /**
     * Create a new controller instance.
     */
    public function __construct(FilterService $filterService)
    {
        $this->filterService = $filterService;
    }

    /**
     * View all views related to work order list.
     */
    public function index(Request $request): JsonResource|JsonResponse
    {
        $request->validate([
            'view_type' => [
                'required',
                'exists:view_types,slug',
            ],
        ]);

        $viewType = ViewType::select('view_type_id', 'slug')
            ->where('slug', $request->input('view_type'))
            ->firstOrFail();

        $this->authorize('viewAny', [View::class, $viewType]);

        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $views = View::withoutGlobalScope('organization')
                ->with('userPinnedViews')
                ->leftJoin('user_default_views', function ($join) use ($user) {
                    $join->on('views.view_id', '=', 'user_default_views.view_id')
                        ->where('user_default_views.user_id', '=', $user->user_id);
                })
                ->where('views.view_type_id', $viewType->view_type_id)
                ->where(function ($query) use ($user) {
                    $query->where(function ($query) use ($user) {
                        $query->where('views.organization_id', $user->organization_id)
                            ->where('views.user_id', $user->user_id);
                    })
                        ->orWhere('views.scope', 'global');
                })
                ->select('views.view_id', 'views.view_uuid', 'views.name', 'user_default_views.user_default_view_id', 'user_default_views.user_id', 'views.scope')
                ->get();

            $defaultViewCount = $views->where('name', 'Default View')->count();
            if ($defaultViewCount > 1) {
                $views = $views->reject(function ($item) use ($user) {
                    return $item->name == 'Default View' && $item->user_id != $user->user_id;
                });
            }

            $defaultViewId = $views->whereNotNull('user_default_view_id')->where('user_id', $user->user_id)->first()?->view_id;

            $request->merge([
                'default_view_id' => $defaultViewId,
            ]);

            return ViewResource::collection($views);
        } catch (UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View Index API Failed[UserNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View Index API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Store a new work order view
     */
    public function store(StoreRequest $request): StoreResource|JsonResponse
    {
        $viewType = ViewType::select('view_type_id', 'slug')
            ->where('slug', $request->view_type)
            ->firstOrFail();

        $this->authorize('create', [View::class, $viewType]);

        DB::beginTransaction();
        try {
            $organization = $request->user()?->organization;

            if (empty($organization)) {
                throw new OrganizationNotFoundException;
            }

            $payload = [
                'view_type_id' => $viewType->view_type_id,
                'payload' => $request->input('payload'),
                'name' => trim($request->input('name')),
                'user_id' => $request->user()?->user_id,
                'organization_id' => $organization->organization_id,
            ];

            $view = $this->createView($payload);

            DB::commit();

            return new StoreResource($view);

        } catch (OrganizationNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'View Store api Failed[OrganizationNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'View Store api Failed[InvalidArgumentException]');

            return Response::unprocessableEntity(message: 'Failed to create view.');
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'View Store api Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Update view
     */
    public function update(UpdateRequest $request, View $view): JsonResource|JsonResponse
    {
        $view->loadMissing('viewType');

        $this->authorize('update', [$view, $request->view_type]);

        try {
            $view = $this->updateView($view, [
                'payload' => $request->payload,
            ]);

            $view->load(['userPinnedViews', 'userDefaultViews']);

            return new UpdatedResource($view);
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View update api failed[InvalidArgumentException]');

            return Response::unprocessableEntity(message: __('Failed to update view.'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View update API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Delete a specified view
     */
    public function destroy(Request $request, View $view): JsonResource|JsonResponse
    {
        $request->validate([
            'view_type' => [
                'required',
                'exists:view_types,slug',
            ],
        ]);

        $view->loadMissing('viewType');

        $this->authorize('delete', [$view, $request->view_type]);

        DB::beginTransaction();

        try {
            $userId = $request->user()?->user_id;
            if (empty($userId)) {
                throw new UserNotFoundException;
            }

            $this->destroyView($view, $userId);

            DB::commit();

            return new DeleteResource($view);

        } catch (UserNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'View delete api Failed[UserNotFoundException]');

            return Response::unprocessableEntity(
                errors: [$exception->getMessage()],
                message: __('The server was unable to process the request because it contains invalid data.')
            );
        } catch (InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'View delete api Failed[InvalidArgumentException]');

            return Response::unprocessableEntity(message: __('Failed  to delete view.'));
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'View delete api Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Set default view
     */
    public function setAsDefault(Request $request, View $view): JsonResponse|JsonResource
    {
        $request->validate([
            'view_type' => [
                'required',
                'exists:view_types,slug',
            ],
        ]);

        $view->loadMissing('viewType');

        $this->authorize('setDefault', [$view, $request->view_type]);

        DB::beginTransaction();

        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $this->setAsDefaultView($view, $user->user_id);

            DB::commit();

            return new SetDefaultResource($view);

        } catch (UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View set As Default api Failed[UserNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'View set As Default api Failed[InvalidArgumentException]');

            return Response::unprocessableEntity(message: __('View set As Default api Failed'));
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, additionalInfo: [
                'viewId' => $view->view_id,
            ], message: 'View set As Default api Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Pin the view to navigation
     */
    public function pinView(PinRequest $request, View $view): JsonResponse|JsonResource
    {
        $this->authorize('pinOrUnpin', [$view, $request->view_type]);

        DB::beginTransaction();

        try {
            $userId = $request->user()?->user_id;
            $action = $request->input('action');

            $this->pinOrUnpinView($view, $action, $userId);

            DB::commit();

            $view->load('viewType');

            return new PinResource($view);

        } catch (InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'View Pin API Failed[InvalidArgumentException]');

            return Response::unprocessableEntity(message: __('The server was unable to process the request because it contains invalid data.'));
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'View Pin API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Rename view
     */
    public function rename(RenameRequest $request, View $view): JsonResource|JsonResponse
    {
        $this->authorize('update', [$view, $request->view_type]);

        try {
            $view = $this->updateView($view, [
                'name' => $request->name,
            ]);

            return new UpdatedResource($view);
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View Rename api Failed[InvalidArgumentException]');

            return Response::unprocessableEntity(message: __('View Rename api Failed'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View Rename api Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Duplicate a new work order view
     */
    public function duplicate(DuplicateRequest $request, View $view): StoreResource|JsonResponse
    {
        $view->loadMissing('viewType');

        $this->authorize('duplicate', [$view, $request->view_type]);

        DB::beginTransaction();
        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $payload = [
                'view_type_id' => $view->view_type_id,
                'payload' => $view->payload,
                'name' => trim($request->input('name')),
                'user_id' => $request->user()?->user_id,
                'organization_id' => $request->user()?->organization_id,
            ];

            $duplicateView = $this->createView($payload);
            DB::commit();

            return new StoreResource($duplicateView);
        } catch (UserNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'View duplicate api Failed[UserNotFoundException]');

            return Response::unprocessableEntity(message: 'View duplicate api Failed');
        } catch (InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'View duplicate api Failed[InvalidArgumentException]');

            return Response::unprocessableEntity(message: __('View duplicate api Failed'));
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, additionalInfo: [
                'viewId' => $view->view_id,
            ], message: 'View duplicate api Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     *  Fetch view configuration values
     */
    public function getViewConfig(ConfigurationRequest $request, ?string $view_uuid = null): JsonResource|JsonResponse
    {
        $viewType = ViewType::select('view_type_id', 'slug')
            ->where('slug', $request->input('view_type'))
            ->firstOrFail();

        $this->authorize('viewAny', [View::class, $viewType]);

        try {
            $user = $request->user() ?? null;

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $view = $this->viewConfig($viewType, $view_uuid, $user);

            if (empty($view->payload)) {
                throw new ModelNotFoundException(__('View not found.'));
            }

            return new ConfigurationResource($view->payload);

        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View Configuration API Failed[InvalidArgumentException].');

            return Response::unprocessableEntity(message: __('The server was unable to process the request because it contains invalid data.'));
        } catch (ModelNotFoundException|UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View Configuration API Failed due to ' . get_class($exception));

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {

            Helper::exceptionLog(exception: $exception, message: 'View Configuration API Failed[Exception].', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     *  Get pinned views work order count under a specified menu
     */
    public function getViewCount(CountRequest $request): AnonymousResourceCollection|JsonResponse
    {
        $viewType = ViewType::select('view_type_id', 'slug')
            ->where('slug', $request->view_type)
            ->firstOrFail();

        $this->authorize('viewAny', [View::class, $viewType]);

        try {
            $user = $request->user() ?? null;

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $userPinnedViews = View::withoutGlobalScope('organization')
                ->join('user_pinned_views', function (JoinClause $joinQuery) use ($user) {
                    $joinQuery->on('views.view_id', 'user_pinned_views.view_id')
                        ->where('user_pinned_views.user_id', $user->user_id);
                })
                ->where('views.view_type_id', $viewType->view_type_id)
                ->select('views.view_id', 'views.view_uuid', 'views.name', 'views.payload')
                ->groupBy('views.view_id')
                ->get();

            $organizationDefaultView = View::withoutGlobalScope('organization')
                ->where('view_type_id', $viewType->view_type_id)
                ->where('name', 'Default View')
                ->select('view_id', 'view_uuid', 'name', 'payload')
                ->get();

            $userViews = $organizationDefaultView->merge($userPinnedViews);

            $organizationId = $user->organization_id;

            $workOrderCountQuery = $this->workOrderViewType($organizationId);

            if (! $workOrderCountQuery instanceof \Illuminate\Database\Eloquent\Builder) {
                $workOrderCountQuery = WorkOrder::query();
            }

            $serviceRequestResponse = null;

            switch ($request->view_type) {
                case 'work-orders':
                    $workOrderCountQuery = $this->workOrderViewType($organizationId);
                    break;
                case 'service-requests':
                    $serviceRequestResponse = new JsonResponse([]);
                    break;
                default:
                    throw new UnexpectedValueException(__("Invalid view type [{$request->view_type}] provided."));
            }

            if (isset($serviceRequestResponse)) {
                return $serviceRequestResponse;
            }

            $userPinnedViews = $userViews->map(function (View $userView, int $key) use ($workOrderCountQuery): array {

                $appliedFilters = ! empty($userView->payload->filters->applied) ?
                    DataFormatHelper::convertWorkOrderFilterObjectToArray($userView->payload->filters->applied) : [];

                $countQuery = $this->filterService->filterQuery($appliedFilters, clone $workOrderCountQuery, 'work_order');

                return [
                    'view_uuid' => $userView->view_uuid,
                    'count' => count($countQuery->get()),
                ];
            });

            return PinnedViewCount::collection($userPinnedViews);

        } catch (UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View count api failed[UserNotFoundException]');

            return Response::unprocessableEntity(message: 'View count api failed');
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View count api failed[UnexpectedValueException]');

            return Response::unprocessableEntity(message: 'View count api failed');
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'View count api failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * @return Builder<WorkOrder>
     */
    private function workOrderViewType(?int $organizationId): Builder
    {
        $workOrderCountQuery = WorkOrder::select('work_orders.work_order_id')
            ->join('properties', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('work_orders.property_id', 'properties.property_id')
                    ->where('properties.organization_id', $organizationId);
            })
            ->join('work_order_statuses', 'work_orders.work_order_status_id', 'work_order_statuses.work_order_status_id')
            ->join('work_order_tasks', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('work_order_tasks.work_order_id', 'work_orders.work_order_id')
                    ->where('work_order_tasks.organization_id', $organizationId);
            })
            ->leftJoin('problem_diagnoses', 'problem_diagnoses.problem_diagnosis_id', 'work_order_tasks.problem_diagnosis_id')
            ->leftJoin('problem_sub_categories', 'problem_diagnoses.problem_sub_category_id', 'problem_sub_categories.problem_sub_category_id')
            ->leftJoin('problem_categories', 'problem_categories.problem_category_id', 'problem_sub_categories.problem_category_id')
            ->leftJoin('work_order_service_call_tasks', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('work_order_tasks.work_order_task_id', 'work_order_service_call_tasks.work_order_task_id')
                    ->where('work_order_service_call_tasks.organization_id', $organizationId);
            })
            ->leftJoin('work_order_service_calls', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('work_order_service_call_tasks.work_order_service_call_id', 'work_order_service_calls.work_order_service_call_id')
                    ->where('work_order_service_calls.organization_id', $organizationId)
                    ->where('work_order_service_calls.is_active', Boolean::YES());
            })
            ->leftJoin('technician_appointments', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('work_order_service_calls.technician_appointment_id', 'technician_appointments.technician_appointment_id')
                    ->where('technician_appointments.organization_id', $organizationId)
                    ->whereNull('technician_appointments.deleted_at');
            })
            ->leftJoin('technicians', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('technician_appointments.technician_id', 'technicians.technician_id')
                    ->where('technicians.organization_id', $organizationId);
            })
            ->leftjoin('work_order_assignees', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('work_order_assignees.work_order_id', 'work_orders.work_order_id')
                    ->whereNull('work_order_assignees.deleted_at')
                    ->join('users', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('users.user_id', '=', 'work_order_assignees.user_id')
                            ->where('users.organization_id', '=', $organizationId);
                    });
            })
            ->leftJoin('work_order_tags', function ($joinQuery) use ($organizationId) {
                $joinQuery->on('work_order_tags.work_order_id', 'work_orders.work_order_id')
                    ->join('tags', function ($joinQuery) use ($organizationId) {
                        $joinQuery->on('tags.tag_id', '=', 'work_order_tags.tag_id')
                            ->where('tags.organization_id', '=', $organizationId)
                            ->whereNull('tags.deleted_at');
                    });
            })
            ->leftJoin('work_order_health_logs', function ($joinQuery) {
                $joinQuery->on('work_order_health_logs.work_order_id', 'work_orders.work_order_id')
                    ->join('work_order_health_trackers', function ($joinQuery) {
                        $joinQuery->on('work_order_health_trackers.work_order_health_tracker_id', 'work_order_health_logs.work_order_health_tracker_id');
                    });
            })
            ->groupBy('work_orders.work_order_id')
            ->limit(10);

        return $workOrderCountQuery;

    }
}

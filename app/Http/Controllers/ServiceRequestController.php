<?php

namespace App\Http\Controllers;

use App\Actions\ServiceRequest\ScopingServiceRequest;
use App\Enums\Boolean;
use App\Enums\DateRanges;
use App\Enums\ImageConversionType;
use App\Enums\MediaType;
use App\Enums\ServiceRequestStatusTypes as ServiceRequestStatusEnum;
use App\Events\Issue\ServiceRequestIssueUpdated;
use App\Events\ServiceRequest\ServiceRequestAccessInfoUpdated;
use App\Events\ServiceRequest\ServiceRequestDescriptionUpdated;
use App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderCreated;
use App\Events\ServiceRequest\WorkOrder\SyncServiceRequestWorkOrder;
use App\Exceptions\ForbiddenException;
use App\Exceptions\GroupingExceptions\ServiceRequestGroupingException;
use App\Exceptions\IssueException;
use App\Exceptions\MediaUploadException;
use App\Exceptions\NotFoundException\OrganizationNotFoundException;
use App\Exceptions\NotFoundException\ServiceRequestNotFoundException;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Filters\ServiceRequestListFilter;
use App\Http\Requests\ServiceRequest\AccessMethodUpdateRequest;
use App\Http\Requests\ServiceRequest\DescriptionUpdateRequest;
use App\Http\Requests\ServiceRequest\FilterValuesRequest;
use App\Http\Requests\ServiceRequest\GroupViewRequest;
use App\Http\Requests\ServiceRequest\ListRequest;
use App\Http\Requests\ServiceRequest\StoreRequest;
use App\Http\Requests\ServiceRequest\StoreWorkOrderRequest;
use App\Http\Resources\ServiceRequest\Filter\AddedDateFilterResource;
use App\Http\Resources\ServiceRequest\Filter\AssigneeFilterResource;
use App\Http\Resources\ServiceRequest\Filter\ImportedFromFilterResource;
use App\Http\Resources\ServiceRequest\Filter\StatusFilterResource;
use App\Http\Resources\ServiceRequest\Group\AssigneeBasedGroupDataResource;
use App\Http\Resources\ServiceRequest\Group\ImportFromBasedGroupDataResource;
use App\Http\Resources\ServiceRequest\Group\StatusBasedGroupDataResource;
use App\Http\Resources\ServiceRequest\ListResource;
use App\Http\Resources\ServiceRequest\MediaResource;
use App\Http\Resources\ServiceRequest\PropertyAccessInfoResource;
use App\Http\Resources\ServiceRequest\ServiceRequestDescriptionResource;
use App\Http\Resources\ServiceRequest\ServiceRequestResource;
use App\Http\Resources\ServiceRequest\StoreResource;
use App\Http\Resources\ServiceRequest\WorkOrderStoreResource;
use App\Models\Issue;
use App\Models\Media;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestDescription;
use App\Models\ServiceRequestMedia;
use App\Models\ServiceRequestSource;
use App\Models\ServiceRequestStatus;
use App\Models\Timezone;
use App\Models\User;
use App\Packages\OrganizationRolePermission\Exceptions\OrganizationNotFound;
use App\Services\ServiceRequestRegister\ServiceRequestRegisterClient;
use App\Services\WorkOrderRegister\WorkOrderRegisterClient;
use App\States\Issue\Assigned;
use App\States\ServiceRequests\Closed;
use App\States\ServiceRequests\Created;
use App\States\WorkOrders\Canceled;
use App\Traits\ServiceRequestListFilterTrait;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use InvalidArgumentException;
use LogicException;
use phpseclib3\Exception\FileNotFoundException;
use Symfony\Component\HttpFoundation\File\Exception\ExtensionFileException;
use Symfony\Component\Mime\MimeTypes;
use Symfony\Component\Translation\Exception\NotFoundResourceException;
use Throwable;
use TypeError;
use UnexpectedValueException;

class ServiceRequestController extends Controller
{
    use ServiceRequestListFilterTrait;

    /**
     * Display a listing of the resource.
     *
     * @throws AuthorizationException
     */
    public function index(ListRequest $request, ServiceRequestListFilter $serviceRequestListFilter): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('viewAny', ServiceRequest::class);
        $user = $request->user();

        try {
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $organizationId = $user->organization_id;

            $serviceRequests = ServiceRequest::with([
                'property:property_id,property_uuid,property_name,full_address,latitude,longitude,state_id,street_address,city,unit_number,postal_zip_code',
                'property.state:state_id,state_uuid,name,state_code',
                'type:service_request_type_id,label,slug',
                'source:service_request_source_id,name,slug',
                'categories:service_request_category_uuid,service_request_id,problem_category_id,problem_sub_category_id',
                'categories.problemCategory:problem_category_id,label,slug',
                'categories.problemCategory.problemSubCategories:problem_sub_category_id,label,slug',
                'resident:resident_id,resident_uuid',
                'timezone:timezone_id,name',
                'assignees:service_request_assignee_id,service_request_assignee_uuid,service_request_id,user_id',
                'assignees.user:user_id,user_uuid,first_name,last_name,middle_name,profile_pic',
                'latestDescription:service_request_description_id,service_request_descriptions.service_request_id,description',
            ])
                ->join('properties', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('service_requests.property_id', 'properties.property_id')
                        ->where('properties.organization_id', $organizationId);
                })
                ->leftjoin('residents', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('service_requests.requesting_resident_id', 'residents.resident_id')
                        ->where('residents.organization_id', $organizationId);
                })
                ->join('service_request_types', 'service_requests.service_request_type_id', 'service_request_types.service_request_type_id')
                ->join('service_request_sources', 'service_requests.service_request_source_id', 'service_request_sources.service_request_source_id')
                ->join('service_request_statuses', 'service_requests.service_request_status_id', 'service_request_statuses.service_request_status_id')
                ->leftJoin('service_request_categories', 'service_requests.service_request_id', '=', 'service_request_categories.service_request_id')
                ->leftJoin('problem_categories', 'service_request_categories.problem_category_id', '=', 'problem_categories.problem_category_id')
                ->when(! empty($request->group) && $request->group === 'assignee', function (Builder $query) {
                    return $query->join('service_request_assignees', function ($joinQuery) {
                        $joinQuery->on('service_requests.service_request_id', 'service_request_assignees.service_request_id')
                            ->whereNull('service_request_assignees.deleted_at');
                    });
                }, function ($query) {
                    return $query->leftJoin('service_request_assignees', function ($joinQuery) {
                        $joinQuery->on('service_requests.service_request_id', 'service_request_assignees.service_request_id')
                            ->whereNull('service_request_assignees.deleted_at');
                    });
                })
                ->leftJoin('users', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('users.user_id', '=', 'service_request_assignees.user_id')
                        ->where('users.organization_id', '=', $organizationId);
                })
                ->filter($serviceRequestListFilter)
                ->groupBy('service_requests.service_request_id')
                ->select(
                    'service_requests.service_request_id',
                    'service_requests.service_request_uuid',
                    'service_requests.service_request_number',
                    'service_requests.organization_id',
                    'service_requests.property_id',
                    'service_requests.requesting_resident_id',
                    'service_requests.timezone_id',
                    'service_requests.state',
                    'service_requests.description',
                    'service_requests.priority',
                    'service_requests.created_at',
                    'service_request_sources.name as imported_from',
                    'service_request_statuses.slug as status_slug',
                    'service_request_types.service_request_type_id',
                    'service_request_sources.service_request_source_id',
                    DB::raw("GROUP_CONCAT(DISTINCT users.first_name ORDER BY users.first_name ASC SEPARATOR ', ') AS assignee_name_for_sort"),
                    DB::raw("CONCAT('assignee_ids_',GROUP_CONCAT(DISTINCT service_request_assignees.user_id SEPARATOR '_')) AS assignee_ids_for_group")
                )
                ->paginate($request->input('per_page', config('pagination.service_request_list.per_page')));

            return ListResource::collection($serviceRequests);
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request listing api Failed[UnexpectedValueException]');

            return Response::unprocessableEntity(errors: [$exception->getMessage()], message: __('The server was unable to process the request because unexpected behavior occurred.'));
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request listing api Failed[InvalidArgumentException]');

            return Response::badRequest(errors: [$exception->getMessage()], message: __('The server was unable to process the request because it contains invalid data.'));
        } catch (LogicException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request listing api Failed[LogicException]');

            return Response::unprocessableEntity(errors: [$exception->getMessage()], message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request listing api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Store a newly created Service Request.
     *
     * @throws Throwable
     */
    public function store(StoreRequest $request, ServiceRequestRegisterClient $serviceRequestRegisterClient): StoreResource|JsonResponse
    {

        $this->authorize('create', ServiceRequest::class);

        DB::beginTransaction();
        try {
            Log::info('Service Request Create Payload', $request->all());

            $user = $request->user();
            // Find organization
            $organization = $user?->organization;

            if (empty($organization)) {
                throw new OrganizationNotFound;
            }

            $requestedTimezone = $request->timezone ?? config('settings.default_timezone');
            $timezone = Timezone::where('name', $requestedTimezone)->first();

            if (empty($timezone)) {
                throw new ModelNotFoundException('Timezone not found');
            }

            $serviceRequest = $serviceRequestRegisterClient->register($request, $organization, $timezone);

            if ($serviceRequest) {
                DB::commit();

                return new StoreResource($serviceRequest);
            }

            throw new NotFoundResourceException(__('Service request create failed.'));
        } catch (ModelNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Service Request creation Failed, due to ModelNotFoundException');

            return Response::notFound(message: $exception->getMessage());
        } catch (OrganizationNotFound $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Service Request creation Failed, due to OrganizationNotFound');

            return Response::unauthorized(message: __('Invalid Organization.'));
        } catch (NotFoundResourceException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Service Request creation Failed, due to NotFoundResourceException');

            return Response::unauthorized(message: __($exception->getMessage()));
        } catch (Exception|TypeError $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Service Request creation Failed', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, ServiceRequest $serviceRequest): JsonResource|JsonResponse
    {
        $this->authorize('view', $serviceRequest);

        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $skippedMediaType = [MediaType::QUOTE_TASK()];
            if ($request->expectsDevice()) {
                // For mobile we only show customer uploaded medias in medias
                $skippedMediaType = [MediaType::QUOTE_TASK(), MediaType::AFTER_MEDIA(), MediaType::BEFORE_MEDIA()];
            }
            $serviceRequest->load([
                'status:service_request_status_id,label,slug',
                'workOrders.status:work_order_status_id,label,slug',
                'updatedByUser:user_id,first_name,last_name',
                'source:service_request_source_id,name,slug',
                'workOrders.property:property_id,property_uuid,property_name,full_address,latitude,longitude,state_id,street_address,city,unit_number,postal_zip_code',
                'workOrders.property.state:state_id,state_uuid,name,state_code',
                'workOrders.property.residents:resident_id,resident_uuid,property_id,first_name,last_name,phone_number,email',
                'timezone:timezone_id,name',
                'workOrders.latestInvoices:invoice_id,work_order_id,invoice_uuid,invoice_number,total_cost_in_cents,state,drafted_at,created_at,created_by_user_id,drafted_by_user_id',
                'resident:resident_id,resident_uuid,first_name,last_name,phone_number',
                'property:property_id,property_uuid,property_name,full_address,latitude,longitude,state_id,street_address,city,unit_number,postal_zip_code',
                'property.state:state_id,state_uuid,name,state_code',
                'type:service_request_type_id,service_request_type_uuid,label,slug',
                'residentAvailabilities:resident_availability_id,resident_availability_uuid,service_request_id,user_id,timing,availability_date,day_passed,created_at',
                'source:service_request_source_id,name,slug',
                'serviceRequestDescriptions' => function ($query) {
                    $query->with('createdUser:user_id,first_name,middle_name,last_name')
                        ->select('service_request_description_uuid', 'service_request_id', 'description', 'additional_info', 'user_id', 'created_at', 'deleted_at')
                        ->orderBy('created_at', 'desc')
                        ->withTrashed();
                },
                'workOrders.workOrderIssues:work_order_issue_id,work_order_issue_uuid,work_order_id,issue_id' => [
                    'issue:issue_id,issue_uuid,title,problem_diagnosis_id',
                    'issue.problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id',
                    'issue.problemDiagnosis.subCategory:problem_sub_category_id,problem_sub_category_uuid,problem_category_id',
                    'issue.problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
                ],
                'workOrders.workOrderSource:work_order_source_id,slug',
                'workOrders.assignees' => function ($query) {
                    $query->with('user:user_id,user_uuid,first_name,last_name,middle_name,profile_pic')
                        ->select('work_order_assignee_id', 'work_order_assignee_uuid', 'work_order_id', 'user_id');
                },
                'workOrders.media' => function ($query) use ($skippedMediaType) {
                    return $query->select(
                        'media.media_id',
                        'media.media_uuid',
                        'media.file_name',
                        'media.mime_type',
                        'media.thumbnail_file_name',
                        'media.optimized_file_name'
                    )
                        ->wherePivot('has_thumbnail', Boolean::YES())
                        ->where(function ($query) use ($skippedMediaType) {
                            return $query->whereNotIn('work_order_media.media_type', $skippedMediaType)
                                ->orWhereNull('work_order_media.media_type');
                        });
                },
                'assignees' => function ($query) {
                    $query->select('service_request_assignee_id', 'service_request_assignee_uuid', 'service_request_id', 'user_id')
                        ->with('user:user_id,first_name,last_name,middle_name,profile_pic,user_uuid');
                },

                'media' => function ($query) use ($skippedMediaType) {
                    return $query->select(
                        'media.media_id',
                        'media.media_uuid',
                        'media.file_name',
                        'media.mime_type',
                        'media.thumbnail_file_name',
                        'media.optimized_file_name',
                        'media.deleted_at',
                    )
                        ->wherePivot('has_thumbnail', Boolean::YES())
                        ->where(function ($query) use ($skippedMediaType) {
                            return $query->whereNotIn('service_request_media.media_type', $skippedMediaType)
                                ->orWhereNull('service_request_media.media_type');
                        })->withTrashed();
                },
                'workOrders.tasks' => function ($query) {
                    return $query->select('work_order_task_id', 'work_order_task_uuid', 'work_order_id', 'problem_diagnosis_id')
                        ->with([
                            'problemDiagnosis:problem_diagnosis_id,problem_diagnosis_uuid,problem_sub_category_id,label',
                            'problemDiagnosis.subCategory:problem_sub_category_id,problem_sub_category_uuid,problem_category_id,label',
                            'problemDiagnosis.subCategory.problemCategory:problem_category_id,problem_category_uuid,label',
                            'latestQuotes' => function ($query) {
                                return $query->select(
                                    'quote_id',
                                    'work_order_task_id',
                                    'quote_uuid',
                                    'status',
                                    'quote_number',
                                    'submitted_at',
                                    'submitted_user_id',
                                    'reviewed_at',
                                    'reviewed_user_id',
                                    'approved_at',
                                    'approved_user_id',
                                    'rejected_at',
                                    'rejected_user_id',
                                    'created_at',
                                    'updated_at'
                                )
                                    ->with([
                                        'submittedUser:user_id,user_uuid,first_name,last_name,middle_name',
                                        'reviewedUser:user_id,user_uuid,first_name,last_name,middle_name',
                                        'approvedUser:user_id,user_uuid,first_name,last_name,middle_name',
                                        'rejectedUser:user_id,user_uuid,first_name,last_name,middle_name',
                                        'quoteTasks' => function ($query) {
                                            return $query->select(
                                                'quote_task_id',
                                                'quote_id',
                                                'quote_task_uuid',
                                                'markup_fee_type',
                                                'markup_fee_type_value',
                                                'markup_fee_in_cents',
                                                'cost_in_cents',
                                                'total_cost_in_cents',
                                                'estimated_time',
                                                'description',
                                                'status',
                                                'quote_task_number'
                                            )
                                                ->with([
                                                    'quoteTaskMaterials' => function ($query) {
                                                        return $query->select(
                                                            'quote_task_id',
                                                            'label',
                                                            'markup_fee_type',
                                                            'markup_fee_type_value',
                                                            'markup_fee_in_cents',
                                                            'cost_in_cents',
                                                            'total_cost_in_cents',
                                                            'quantity',
                                                            'quantity_type',
                                                            'cost_type',
                                                            'quote_task_material_uuid',
                                                            'quote_task_material_id'
                                                        );
                                                    },
                                                    'media' => function ($query) {
                                                        return $query->select(
                                                            'media.media_id',
                                                            'media.media_uuid',
                                                            'media.file_name',
                                                            'media.mime_type',
                                                            'media.thumbnail_file_name',
                                                            'media.optimized_file_name'
                                                        )
                                                            ->wherePivot('has_thumbnail', Boolean::YES())
                                                            ->wherePivot('media_type', MediaType::QUOTE_TASK());
                                                    },
                                                ])
                                                ->orderBy('created_at');
                                        },
                                    ]);
                            },
                            'latestServiceCalls' => function ($query) {
                                return $query->select(
                                    'work_order_service_calls.work_order_service_call_id',
                                    'work_order_service_calls.work_order_service_call_uuid',
                                    'work_order_service_calls.work_order_service_call_number',
                                    'work_order_service_calls.organization_id',
                                    'work_order_service_calls.work_order_id',
                                    'work_order_service_calls.technician_appointment_id',
                                    'work_order_service_calls.lula_appointment_id',
                                    'work_order_service_calls.vendor_appointment_id',
                                    'work_order_service_calls.scheduled_start_time',
                                    'work_order_service_calls.scheduled_end_time',
                                    'work_order_service_calls.state',
                                    'work_order_service_calls.service_notes',
                                    'work_order_service_calls.status',
                                    'work_order_service_calls.work_to_perform',
                                    'work_order_service_calls.quote_id',
                                    'work_order_service_calls.is_active',
                                    'work_order_service_calls.trip_end_with',
                                    'work_order_service_calls.trip_end_with_type',
                                    'work_order_service_calls.trip_end_with_reason',
                                    'work_order_service_calls.last_modified_user',
                                    'work_order_service_calls.last_modified_at',
                                    'work_order_service_calls.timer_paused_at',
                                    'work_order_service_calls.timer_resumed_at',
                                    'work_order_service_calls.timer_paused_reason',
                                    'work_order_service_calls.created_at',
                                )->with([
                                    'createdQuote:quote_id,quote_uuid,work_order_service_call_id,status',
                                    'assignedQuote:quote_id,quote_number',
                                    'vendorExternalInvoices:vendor_external_invoice_id,vendor_external_invoice_url,work_order_service_call_id,amount_in_cents',
                                    'media' => function ($query) {
                                        return $query->select(
                                            'media.media_id',
                                            'media.media_uuid',
                                            'media.file_name',
                                            'media.mime_type',
                                            'media.thumbnail_file_name',
                                            'media.optimized_file_name'
                                        )
                                            ->wherePivot('has_thumbnail', Boolean::YES())
                                            ->wherePivotIn('media_type', [MediaType::AFTER_MEDIA(), MediaType::BEFORE_MEDIA()]);
                                    },
                                    'workOrderTaskMaterials' => function ($query) {
                                        return $query->select(
                                            'work_order_task_material_uuid',
                                            'label',
                                            'cost_in_cents',
                                            'quantity',
                                            'quantity_type',
                                            'work_order_service_call_id',
                                            'cost_type'
                                        );
                                    },
                                    'appointment' => function ($query) {
                                        $query->select(
                                            'actual_start_time',
                                            'actual_end_time',
                                            'rescheduled_reason',
                                            'adjusted_elapse_time_in_sec',
                                            'enroute_at',
                                            'adjusted_travel_time_in_sec',
                                            'technician_id',
                                            'technician_appointment_id',
                                        )
                                            ->with([
                                                'technician:technician_id,technician_uuid,user_id',
                                                'technician.user:user_id,first_name,middle_name,last_name,profile_pic',
                                            ]);
                                    },
                                    'lulaAppointment' => function ($query) {
                                        $query->select(
                                            'lula_appointment_id',
                                            'vendor_instructions',
                                            'work_order_reference_number',
                                            'service_category_label',
                                            'scheduled_start_time',
                                            'scheduled_end_time',
                                            'estimated_return_start_time',
                                            'estimated_return_end_time',
                                            'paused_reason',
                                            'rescheduled_reason'
                                        );
                                    },
                                    'vendorAppointment' => function ($query) {
                                        $query->select(
                                            'vendor_appointment_id',
                                            'vendor_appointment_uuid',
                                            'organization_id',
                                            'vendor_id',
                                            'vendor_instructions',
                                        )
                                            ->with([
                                                'vendor:vendor_id,first_name,last_name,vendor_uuid',
                                                'vendorAllocations.vendor:vendor_id,first_name,last_name,vendor_uuid',
                                            ]);
                                    },
                                ]);
                            },

                        ]);
                },
                'issues' => function ($query) {
                    $query->select('issue_id', 'service_request_id', 'issue_uuid', 'title', 'description', 'problem_diagnosis_id', 'state', 'created_at')
                        ->with([
                            'workOrderIssues:work_order_id,issue_id',
                            'workOrders' => function ($query) {
                                $query->select(
                                    'work_orders.work_order_id',
                                    'work_orders.work_order_uuid',
                                    'work_orders.work_order_number',
                                    'work_orders.state',
                                )
                                    ->whereNotState('work_orders.state', Canceled::class);
                            },
                            'problemDiagnosis' => function ($q) {
                                $q->select('problem_diagnosis_id', 'problem_diagnosis_uuid', 'problem_sub_category_id', 'label')
                                    ->with([
                                        'subCategory' => function ($q2) {
                                            $q2->select('problem_sub_category_id', 'problem_sub_category_uuid', 'label', 'problem_category_id')
                                                ->with([
                                                    'problemCategory' => function ($q3) {
                                                        $q3->select('problem_category_id', 'problem_category_uuid', 'label');
                                                    },
                                                ]);
                                        },
                                    ]);
                            },
                        ])
                        ->orderBy('created_at');
                },
            ]);

            // Count all work orders including soft deleted
            $serviceRequest->loadCount([
                'workOrders' => function ($query) {
                    $query->withTrashed();
                },
            ]);

            if ($serviceRequest->state->equals(Created::class)) {
                ScopingServiceRequest::make()->handle($serviceRequest, $user);
            }

            // for issue ability check
            $serviceRequest->issues->each(function (Issue $issue) use ($serviceRequest) {
                $issue->setRelation('serviceRequest', (object) [
                    'service_request_id' => $issue->service_request_id,
                    'state' => $serviceRequest->state,
                ]);
            });

            return new ServiceRequestResource($serviceRequest);
        } catch (ServiceRequestNotFoundException|UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request details api failed[Exception].');

            return Response::notFound($exception->getMessage());
        } catch (ForbiddenException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request details api Failed[ForbiddenException]');

            return Response::forbidden(message: __($exception->getMessage()));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request details api failed[Exception].', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @throws OrganizationNotFoundException|AuthorizationException
     */
    public function update(Request $request, ServiceRequest $serviceRequest): JsonResource|JsonResponse
    {
        $this->authorize('update', $serviceRequest);

        $request->validate([
            'media' => 'sometimes|array',
            'media.*.media' => 'required_with:media.*.media_id|image',
            'media.*.media_id' => 'required_with:media.*.media',
            'media.*.size' => 'required_with:media.*.media',
            'media.*.mime_type' => 'required_with:media.*.media',
            'media.*.file_name' => 'required_with:media.*.media',
            'service_request_status' => 'sometimes|in:' . collect(ServiceRequestStatusEnum::cases())->pluck('value')->implode(','),
        ]);

        try {
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            return match ($request->get('service_request_status')) {
                ServiceRequestStatusEnum::CLOSED() => $this->markAsComplete($request, $serviceRequest),
                default => throw new InvalidArgumentException('Invalid action requested.')
            };
        } catch (OrganizationNotFound $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request update api Failed[OrganizationNotFound]');

            return Response::unprocessableEntity(message: __($exception->getMessage()));
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request update api Failed[InvalidArgumentException]');

            return Response::unprocessableEntity(message: __('Unable to process your request, the payload contains invalid data.'));
        } catch (MediaUploadException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request update api Failed[MediaUploadException]');

            return Response::unprocessableEntity(message: __('Unable to process media upload, please try again.'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request update api Failed[Exception]', notify: true);

            return Response::internalServerError($exception->getMessage());
        }
    }

    /**
     * mark admin availability viewed
     */
    public function markAdminAvailabilityViewed(Request $request, ServiceRequest $serviceRequest): JsonResponse
    {
        $this->authorize('update', $serviceRequest);

        try {
            $serviceRequest->availability_viewed_user_id = $request->user()?->user_id;
            $serviceRequest->save();

            return Response::message(message: __('Availability viewed'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order Availability Viewed api Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     *  Get group view details.
     */
    public function getGroupView(GroupViewRequest $request): JsonResource|JsonResponse
    {
        try {
            $this->authorize('viewAny', ServiceRequest::class);

            $group = $request->get('group');
            $organizationId = request()->user()?->organization_id;

            if (! $organizationId) {
                throw new OrganizationNotFoundException;
            }

            switch ($group) {
                case 'status':
                    $groupData = $this->statusBasedGroupData($organizationId, $request);

                    return StatusBasedGroupDataResource::collection($groupData);
                case 'assignee':
                    $groupData = $this->assigneeBasedGroupData($organizationId, $request);

                    return AssigneeBasedGroupDataResource::collection($groupData);
                case 'imported_from':
                    $groupData = $this->importedFromBasedGroupData($organizationId, $request);

                    return ImportFromBasedGroupDataResource::collection($groupData);

                default:
                    throw new UnexpectedValueException(__("Invalid group [{$group}] provided."));
            }
        } catch (ServiceRequestGroupingException $exception) {
            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get Group view api Failed[UnexpectedValueException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (AuthorizationException|OrganizationNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get Group view api Failed due to ' . get_class($exception));

            return Response::unauthorized(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get Group view api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     *  Get selected filter data set
     */
    public function getFilterValues(FilterValuesRequest $request): JsonResource|JsonResponse
    {
        try {
            $this->authorize('viewAny', ServiceRequest::class);

            $filterType = $request->get('type');
            $organizationId = request()->user()?->organization_id;

            if (! $organizationId) {
                throw new OrganizationNotFoundException;
            }

            switch ($filterType) {
                case 'status':
                    $filterData = Cache::remember(
                        config('settings.cache.service_request.filter.status.name'),
                        now()->addMinutes(config('settings.cache.service_request.filter.status.expire_time_in_minutes')),
                        static fn () => ServiceRequestStatus::select('label', 'slug', 'sort_order')
                            ->get()
                            ->sortBy('sort_order')
                    );

                    return StatusFilterResource::collection($filterData);
                case 'assignee':
                    $filterData = User::whereIn('user_type', ['account_user'])
                        ->where('organization_id', $organizationId)
                        ->select('user_uuid', 'first_name', 'last_name')
                        ->orderBy('first_name')
                        ->get();

                    return AssigneeFilterResource::collection($filterData);

                case 'imported_from':
                    $filterData = Cache::remember(
                        config('settings.cache.service_request.filter.source.name'),
                        now()->addMinutes(config('settings.cache.service_request.filter.source.expire_time_in_minutes')),
                        static fn () => ServiceRequestSource::select('name', 'slug')
                            ->get()
                            ->sortBy('name')
                    );

                    return ImportedFromFilterResource::collection($filterData);
                case 'added_date':
                    $filterData = DateRanges::makeCollection([DateRanges::OVERDUE(), DateRanges::TOMORROW(), DateRanges::NEXT_WEEK(), DateRanges::NEXT_MONTH()]);

                    return AddedDateFilterResource::collection($filterData);
                default:
                    throw new UnexpectedValueException(__("Invalid filter data request, {$filterType} filter not specified."));
            }
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get Filter Values api failed[UnexpectedValueException]');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (OrganizationNotFoundException|AuthorizationException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get Filter Values api failed due to ' . get_class($exception));

            return Response::unauthorized(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Get Filter Values api failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Update service request description
     */
    public function updateDescription(DescriptionUpdateRequest $request, ServiceRequest $serviceRequest): JsonResponse|ServiceRequestDescriptionResource
    {
        $this->authorize('update', $serviceRequest);

        try {
            $user = $request->user();
            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $description = trim($request->get('description'));

            // Retrieve the latest description
            $latestDescription = ServiceRequestDescription::where('service_request_id', $serviceRequest->service_request_id)
                ->select('service_request_description_id', 'service_request_description_uuid', 'description', 'additional_info', 'created_at', 'deleted_at', 'user_id')
                ->latest()
                ->first();

            $serviceRequest = $serviceRequest->load([
                'media' => function ($query) {
                    return $query->select(
                        'media.media_id',
                        'media.media_uuid',
                        'media.file_name',
                        'media.mime_type',
                        'media.thumbnail_file_name',
                        'media.optimized_file_name',
                        'media.deleted_at',
                    )
                        ->wherePivot('has_upload_completed', Boolean::YES())
                        ->withTrashed();
                },
            ]);

            DB::beginTransaction();
            if ($this->needToCreateServiceRequestDescription($description, $latestDescription)) {
                //Delete existing service_request_descriptions
                ServiceRequestDescription::where('service_request_id', $serviceRequest->service_request_id)->delete();

                $additionalInfo = null;

                if ($serviceRequest->media->whereNull('deleted_at')->isNotEmpty()) {
                    $additionalInfo['media_ids'] = $serviceRequest->media->whereNull('deleted_at')->pluck('media_id')->toArray();
                }

                $serviceRequestDescription = ServiceRequestDescription::create([
                    'service_request_id' => $serviceRequest->service_request_id,
                    'user_id' => $user->user_id,
                    'description' => $description,
                    'organization_id' => $serviceRequest->organization_id,
                    'additional_info' => $additionalInfo,
                ]);

                ServiceRequestDescriptionUpdated::broadcast($serviceRequestDescription->service_request_description_id)->toOthers();

                $latestDescription = $serviceRequestDescription;
            }

            DB::commit();

            if (! empty($latestDescription->additional_info['media_ids'])) {
                $medias = MediaResource::collection($serviceRequest->media->whereIn('media_id', $latestDescription->additional_info['media_ids']) ?? collect([]));
                $latestDescription->setRelation('medias', $medias);
            }

            return new ServiceRequestDescriptionResource($latestDescription);
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request Description Update API Failed[Exception]');

            return Response::internalServerError();
        }
    }

    /**
     * Update service request access method
     */
    public function updateAccessMethod(AccessMethodUpdateRequest $request, ServiceRequest $serviceRequest): JsonResponse|PropertyAccessInfoResource
    {
        $this->authorize('update', $serviceRequest);

        try {
            $serviceRequest->property_access_method = $request->input('property_access_method') ?? null;
            $serviceRequest->property_access_code = $request->input('property_access_code') ?? null;
            $serviceRequest->property_access_note = $request->input('property_access_note') ?? null;
            $serviceRequest->save();

            ServiceRequestAccessInfoUpdated::dispatch($serviceRequest->service_request_id);

            return new PropertyAccessInfoResource($serviceRequest);
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request Access Method API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Create work order under service request
     *
     * @throws Throwable
     */
    public function createWorkOrder(StoreWorkOrderRequest $request, ServiceRequest $serviceRequest, WorkOrderRegisterClient $orderRegisterClient): WorkOrderStoreResource|JsonResponse
    {
        $this->authorize('create', ServiceRequest::class);

        DB::beginTransaction();
        try {
            Log::info('Work Order Create Payload', $request->all());

            $user = $request->user();
            $issues = null;
            // Find organization
            $organization = $user?->organization;

            if (empty($organization)) {
                throw new OrganizationNotFound;
            }

            $requestedTimezone = $request->timezone ?? config('settings.default_timezone');
            $timezone = Timezone::where('name', $requestedTimezone)->first();

            if (empty($timezone)) {
                throw new ModelNotFoundException('Timezone not found');
            }

            $issueUuids = isset($request->issue_ids) ? $request->issue_ids : [];
            if (count($issueUuids)) {
                $issues = Issue::whereUuid($issueUuids)->get();
                $issueProblemDiagnosisIds = $issues->pluck('problem_diagnosis_id')->toArray();
                $issueServiceRequestIds = $issues->pluck('service_request_id')->toArray();
                // Check whether the issue belongs to service request.
                if (
                    count($issueServiceRequestIds) &&
                    ! empty(array_diff($issueServiceRequestIds, [$serviceRequest->service_request_id]))
                ) {
                    throw IssueException::invalid();
                }

                foreach ($issues as $issue) {
                    if (! $issue->state->canTransitionTo(Assigned::class)) {
                        throw IssueException::actionNotAllowed();
                    }
                }

                // Set default problem_diagnosis_id for work work order
                // TO DO: Remove problem_diagnosis based on Work Order Issue related changes.
                $problemDiagnosisIds = array_map(function ($problemDiagnosisId) {
                    return ['problem_diagnosis_id' => $problemDiagnosisId];
                }, $issueProblemDiagnosisIds);

                $request->merge(['problem_diagnoses' => $problemDiagnosisIds]);
            }

            $workOrder = $orderRegisterClient->register($request, $organization, $timezone, $serviceRequest);

            if ($workOrder) {
                // Dispatch Event – Service Request WorkOrder Details: Work Order Created
                broadcast(new ServiceRequestWorkOrderCreated(
                    $workOrder->work_order_id,
                    $user->user_id
                ))->toOthers();

                SyncServiceRequestWorkOrder::dispatch($serviceRequest->service_request_id);

                $this->triggerIssueUpdatedEvents($issues);

                DB::commit();

                $workOrder->load(['workOrderIssues' => function ($query) {
                    $query->with('issue:issue_id,issue_uuid,title,state')
                        ->select('work_order_id', 'issue_id', 'state');
                }]);

                return new WorkOrderStoreResource($workOrder);
            }

            throw new NotFoundResourceException(__('Workorder create failed.'));
        } catch (ModelNotFoundException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order creation Failed, due to ModelNotFoundException');

            $response = Response::notFound(message: $exception->getMessage());
        } catch (OrganizationNotFound $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order creation Failed, due to OrganizationNotFound');
            $response = Response::unauthorized(message: __('Invalid Organization.'));
        } catch (IssueException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order creation Failed, due to IssueException');

            $response = Response::unprocessableEntity(message: $exception->getMessage());
        } catch (NotFoundResourceException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order creation Failed, due to NotFoundResourceException');

            $response = Response::unauthorized(message: __($exception->getMessage()));
        } catch (Exception|TypeError $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order creation Failed', notify: true);

            $response = Response::internalServerError();
        }

        return $response;
    }

    /**
     * @param  Collection<int,Issues>  $issues
     */
    protected function triggerIssueUpdatedEvents(?Collection $issues): void
    {
        if (! empty($issues)) {
            foreach ($issues as $issue) {
                ServiceRequestIssueUpdated::broadcast($issue->issue_id)->toOthers();
            }
        }
    }

    protected function markAsComplete(Request $request, ServiceRequest $serviceRequest): JsonResource
    {
        try {
            DB::beginTransaction();

            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $organization = $user->organization;
            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            $serviceRequest->state->transitionTo(Closed::class, $user);
            $uploadedMediaThumbnails = [];
            $path = $organization->getMediaPathPrefix() . "/service-requests/{$serviceRequest->service_request_uuid}";

            if ($request->has('media')) {
                foreach ($request->get('media') as $key => $media) {
                    $mediaModel = null;

                    if ($request->hasFile("media.{$key}.media")) {
                        /** @var UploadedFile $file */
                        $file = $request->file("media.{$key}.media");
                        if (! $file) {
                            throw new FileNotFoundException("The file [media.{$key}.media] not found");
                        }

                        // Generating a unique name for the original file.
                        $fileName = Str::uuid();
                        // Generating the thumbnail name
                        $thumbnailFileName = $fileName . '_thumbnail'; // Preferred for thumbnail file name

                        // We are assuming that the extension from the request will be the original file extension.
                        $extension = $media['extension'] ?? null;

                        // If we are unable to find the extension from the extension we will try to find it from the file name
                        if (! $extension) {
                            $extension = Str::afterLast($media['file_name'], '.');
                        }

                        // Still no extension, then we will find it from the mime_type that provided in the request.
                        if (! $extension) {
                            $extension = (new MimeTypes)->getExtensions($media['mime_type'])[0] ?? null;
                        }

                        if (! $extension) {
                            throw new ExtensionFileException(__("We couldn't find the file extension"));
                        }

                        // Finding the thumbnail from the file.
                        $thumbnailExtension = $file->getClientOriginalExtension() ?? $file->guessExtension();

                        if (! $thumbnailExtension) {
                            throw new ExtensionFileException(__("We couldn't find the thumbnail file extension"));
                        }

                        // Appending extensions to the file names.
                        $thumbnailFileName .= ".{$thumbnailExtension}";
                        $fileName .= ".{$extension}";

                        // Storing file to s3
                        $uploaded = Storage::putFileAs($path, $file, $thumbnailFileName);

                        $mediaModel = Media::create([
                            'organization_id' => $organization->organization_id,
                            'original_file_name' => $media['file_name'] ?? null,
                            'original_thumbnail_file_name' => $file->getClientOriginalName(),
                            'file_name' => $fileName,
                            'thumbnail_file_name' => $thumbnailFileName,
                            'mime_type' => $media['mime_type'] ?? null,
                            'size' => $media['size'] ?? 0,
                            'extension' => $extension,
                            'thumbnail_extension' => $thumbnailExtension,
                        ]);

                        $serviceRequestMediaModel = ServiceRequestMedia::create([
                            'media_id' => $mediaModel->media_id,
                            'organization_id' => $organization->organization_id,
                            'work_order_id' => $serviceRequest->service_request_id,
                            'user_id' => $request->user()?->user_id,
                            'media_type' => MediaType::AFTER_MEDIA(),
                            'has_thumbnail' => $uploaded ? Boolean::YES() : Boolean::NO(),
                        ]);
                    }

                    $uploadedMediaThumbnails[] = [
                        'media_uuid' => $media['media_id'],
                        'is_uploaded' => ! empty($mediaModel),
                        'stored_media_uuid' => $mediaModel->media_uuid ?? null,
                        'thumbnail_url' => ! empty($mediaModel) ?
                            $mediaModel->getTemporaryMediaUrl(type: ImageConversionType::THUMBNAIL()) : null,
                    ];
                }
            }

            DB::commit();

            return new JsonResource([
                'media' => $uploadedMediaThumbnails,
                'service_request_id' => $serviceRequest->service_request_uuid,
                'service_request_status' => $serviceRequest->status->label ?? null,
            ]);
        } catch (FileNotFoundException|ExtensionFileException|Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order Mark As Complete API Failed');
            throw new MediaUploadException($exception->getMessage());
        }
    }

    /**
     * Return job status and its job counts
     *
     * @return Collection<int, ServiceRequest>
     */
    private function statusBasedGroupData(int $organizationId, GroupViewRequest $request): Collection
    {
        try {
            $serviceRequestStatus = ServiceRequest::join('service_request_statuses', 'service_requests.state', '=', 'service_request_statuses.slug')
                ->select(
                    'service_request_statuses.label',
                    'service_request_statuses.slug',
                    'service_request_statuses.sort_order',
                    DB::raw('count(service_requests.service_request_id) as total')
                )
                ->when(! empty($request->filter), function (Builder $filterQuery) use ($request) {
                    $this->filterQuery($request->filter, $filterQuery);
                })
                ->groupBy(
                    'service_request_statuses.label',
                    'service_request_statuses.slug',
                    'service_request_statuses.sort_order'
                )
                ->where('service_requests.organization_id', $organizationId)
                ->orderBy('service_request_statuses.sort_order')
                ->get();

            return $serviceRequestStatus;
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order Status Based Group Data api Failed[UnexpectedValueException]');
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to some unexpected value(s).'));
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order Status Based Group Data api Failed[InvalidArgumentException]');
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to some invalid value(s).'));
        } catch (LogicException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order Status Based Group Data api Failed[LogicException]');
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to an unexecuted condition(s).'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order Status Based Group Data api Failed[Exception]', notify: true);
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to an unexpected error(s).'));
        }
    }

    /**
     * Return job assignees and its job counts
     *
     * @return array<int, mixed>
     */
    private function importedFromBasedGroupData(int $organizationId, GroupViewRequest $request): array
    {
        try {
            $serviceRequests = ServiceRequest::select(
                'service_request_sources.name',
                'service_request_sources.slug',
                DB::raw('count(service_requests.service_request_id) as total')
            )
                ->join('service_request_sources', 'service_requests.service_request_source_id', '=', 'service_request_sources.service_request_source_id')
                ->where('service_requests.organization_id', $organizationId)
                ->when(! empty($request->filter), function (Builder $filterQuery) use ($request) {
                    $this->filterQuery($request->filter, $filterQuery);
                })
                ->groupBy('service_request_sources.name', 'service_request_sources.slug')
                ->get();

            $formattedData = [];

            foreach ($serviceRequests as $serviceRequest) {
                array_push($formattedData, [
                    'label' => $serviceRequest->name ?? null,
                    'total' => $serviceRequest->total ?? null,
                    'slug' => $serviceRequest->slug ?? null,
                ]);
            }

            return $formattedData;
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Assignee Based Group Data api Failed[UnexpectedValueException]');
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to some unexpected value(s).'));
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Assignee Based Group Data api Failed[InvalidArgumentException]');
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to some invalid value(s).'));
        } catch (LogicException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Assignee Based Group Data api Failed[LogicException]');
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to an unexecuted condition(s).'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Assignee Based Group Data api Failed[Exception]', notify: true);
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to an unexpected error(s).'));
        }
    }

    /**
     * Return job assignees and its job counts
     *
     * @return array<int, mixed>
     */
    private function assigneeBasedGroupData(int $organizationId, GroupViewRequest $request): array
    {
        try {
            $serviceRequests = ServiceRequest::join('service_request_assignees', function ($joinQuery) {
                $joinQuery->on('service_requests.service_request_id', 'service_request_assignees.service_request_id')
                    ->whereNull('service_request_assignees.deleted_at');
            })
                ->join('users', function ($joinQuery) use ($organizationId) {
                    $joinQuery->on('service_request_assignees.user_id', 'users.user_id')
                        ->where('users.organization_id', $organizationId);
                })

                ->select(
                    DB::raw("CONCAT('assignee_ids_',GROUP_CONCAT(DISTINCT service_request_assignees.user_id SEPARATOR '_')) AS assignees_slug"),
                    DB::raw("GROUP_CONCAT(DISTINCT users.first_name SEPARATOR ', ') AS assignee_name"),
                )
                ->when(! empty($request->filter), function (Builder $filterQuery) use ($request) {
                    $this->filterQuery($request->filter, $filterQuery);
                })
                ->groupBy('service_requests.service_request_id')
                ->get()
                ->groupBy('assignees_slug');

            $formattedData = [];

            foreach ($serviceRequests as $assignees => $serviceRequest) {
                array_push($formattedData, [
                    'label' => ! empty($serviceRequest->first()->assignee_name) ? $serviceRequest->first()->assignee_name : '',
                    'total' => count($serviceRequest),
                    'slug' => Str::slug($assignees),
                ]);
            }

            return $formattedData;
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Assignee Based Group Data api Failed[UnexpectedValueException]');
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to some unexpected value(s).'));
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Assignee Based Group Data api Failed[InvalidArgumentException]');
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to some invalid value(s).'));
        } catch (LogicException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Assignee Based Group Data api Failed[LogicException]');
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to an unexecuted condition(s).'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Assignee Based Group Data api Failed[Exception]', notify: true);
            throw new ServiceRequestGroupingException(__('The grouping process was failed due to an unexpected error(s).'));
        }
    }

    private function needToCreateServiceRequestDescription(string $description, ?ServiceRequestDescription $serviceRequestDescription = null): bool
    {

        if (empty($serviceRequestDescription) || empty($serviceRequestDescription->description)) {
            return true;
        }

        if (strcmp($serviceRequestDescription->description, $description) !== 0) {
            return true;
        }

        return false;
    }
}

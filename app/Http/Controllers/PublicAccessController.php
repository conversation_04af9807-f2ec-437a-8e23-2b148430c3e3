<?php

namespace App\Http\Controllers;

use App\Enums\ImageConversionType;
use App\Helpers\Helper;
use App\Models\Media;
use App\Models\ServiceRequest;
use App\Models\WorkOrder;
use App\Services\VideoStream\VideoStreamer;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class PublicAccessController extends Controller
{
    public function getMedia(string $mediaUUID, ?string $type = null, ?string $fileName = null): StreamedResponse|Response|JsonResponse
    {
        $media = Media::withTrashed()->with(['workOrder' => function ($query) {
            $query->withTrashedParents();
        }])
            ->whereUuid($mediaUUID)->firstOrFail([
                'media_id', 'media_uuid', 'organization_id', 'file_name', 'original_file_name', 'optimized_file_name',
                'thumbnail_file_name', 'mime_type',
            ]);

        try {
            $additionalData = [
                'media' => $media->media_uuid,
            ];
            /** @var WorkOrder $workOrder */
            $workOrder = $media->workOrder;

            if (! $workOrder) {
                return Response::notFound();
            }

            $basePath = $media->getBasePath($workOrder->organization, $workOrder->work_order_uuid);

            $fileName = match ($type) {
                ImageConversionType::THUMBNAIL() => $media->thumbnail_file_name,
                ImageConversionType::OPTIMIZED() => $media->optimized_file_name,
                default => $media->file_name
            };

            // If the image conversion type is not available, we are falling back to original file.
            if (empty($fileName)) {
                $fileName = $media->file_name;
            }

            $additionalData['base_path'] = $basePath;
            $additionalData['fileName'] = $fileName;

            if (! Storage::exists($basePath . '/' . $fileName)) {
                return Response::notFound();
            }

            if (isset($_SERVER['HTTP_RANGE'])) {
                $stream = new VideoStreamer(
                    $basePath . '/' . $fileName
                );

                $stream->start();
            }

            return Storage::response($basePath . '/' . $fileName);
        } catch (Exception $exception) {

            Helper::exceptionLog(
                exception: $exception,
                additionalInfo: $additionalData,
                message: ("Public Access: [Media:{$media->media_uuid}]" . $exception->getMessage()),
                notify: true
            );

            return Response::notFound();
        }
    }

    public function getSrMedia(string $mediaUUID, ?string $type = null, ?string $fileName = null): StreamedResponse|Response|JsonResponse
    {
        $media = Media::withTrashed()->with(['serviceRequest' => function ($query) {
            $query->withTrashedParents();
        }])
            ->whereUuid($mediaUUID)->firstOrFail([
                'media_id', 'media_uuid', 'organization_id', 'file_name', 'original_file_name', 'optimized_file_name',
                'thumbnail_file_name', 'mime_type',
            ]);

        try {
            $additionalData = [
                'media' => $media->media_uuid,
            ];
            /** @var ServiceRequest $serviceRequest */
            $serviceRequest = $media->serviceRequest;
            $basePath = $media->getServiceRequestBasePath($serviceRequest->organization, $serviceRequest->service_request_uuid);

            $serviceRequestMediaFileName = match ($type) {
                ImageConversionType::THUMBNAIL() => $media->thumbnail_file_name,
                ImageConversionType::OPTIMIZED() => $media->optimized_file_name,
                default => $media->file_name
            };

            // If the image conversion type is not available, we are falling back to original file.
            if (empty($serviceRequestMediaFileName)) {
                $serviceRequestMediaFileName = $media->file_name;
            }

            $additionalData['base_path'] = $basePath;
            $additionalData['fileName'] = $serviceRequestMediaFileName;

            if (! Storage::exists($basePath . '/' . $serviceRequestMediaFileName)) {
                return Response::notFound();
            }

            if (isset($_SERVER['HTTP_RANGE'])) {
                $stream = new VideoStreamer(
                    $basePath . '/' . $serviceRequestMediaFileName
                );

                $stream->start();
            }

            return Storage::response($basePath . '/' . $serviceRequestMediaFileName);
        } catch (Exception $exception) {

            Helper::exceptionLog(
                exception: $exception,
                additionalInfo: $additionalData,
                message: ("Public Access: [Media:{$media->media_uuid}]" . $exception->getMessage()),
                notify: true
            );

            return Response::notFound();
        }
    }
}

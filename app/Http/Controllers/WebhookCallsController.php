<?php

namespace App\Http\Controllers;

use App\DataTables\WebhookCallsDataTable;
use App\Models\WebhookCall;
use Illuminate\Http\Request;

class WebhookCallsController extends Controller
{
    /*
    * List request logs.
    */
    public function index(WebhookCallsDataTable $dataTable): mixed
    {
        return $dataTable->render('webhook_calls.index');
    }

    /**
     * Show request log detailed view.
     */
    public function show(WebhookCall $webhookCall): mixed
    {
        return view('webhook_calls.show', ['webhookCall' => $ $webhookCall]);
    }
}

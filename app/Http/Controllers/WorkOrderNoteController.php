<?php

namespace App\Http\Controllers;

use App\Enums\WorkOrderNoteTypes;
use App\Events\WorkOrder\Note\WorkOrderNoteCreated;
use App\Events\WorkOrder\Note\WorkOrderNoteDeleted;
use App\Events\WorkOrder\Note\WorkOrderNoteUpdated;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Exceptions\WorkOrderNoteException;
use App\Helpers\Helper;
use App\Http\Requests\WorkOrder\Note\CreateRequest;
use App\Http\Requests\WorkOrder\Note\UpdateRequest;
use App\Http\Resources\WorkOrder\Note\NoteListResource;
use App\Models\WorkOrder;
use App\Models\WorkOrderNote;
use App\Models\WorkOrderNoteLog;
use Carbon\Carbon;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;

class WorkOrderNoteController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @throws AuthorizationException
     */
    public function index(Request $request, WorkOrder $workOrder): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('viewAny', WorkOrderNote::class);
        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if ($user->organization_id !== $workOrder->organization_id) {
                throw WorkOrderNoteException::inValidWorkOrder();
            }

            $workOrderNotes = WorkOrderNote::with(
                'user:user_id,first_name,last_name,middle_name,profile_pic,user_uuid',
                'lastModifiedUser:user_id,first_name,middle_name,last_name,profile_pic,user_uuid'
            )
                ->where('work_order_id', $workOrder->work_order_id)
                ->where('organization_id', $user->organization_id)
                ->select(
                    'work_order_note_id', 'work_order_note_uuid', 'work_order_id',
                    'user_id', 'note_type', 'note', 'last_modified_at', 'last_modified_user_id', 'created_at'
                )
                ->orderBy('created_at')
                ->get();

            return NoteListResource::collection($workOrderNotes);

        } catch (UserNotFoundException|WorkOrderNoteException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order listing api Failed, due to user not found');

            return Response::unprocessableEntity(errors: [$exception->getMessage()], message: __('The server was unable to process the request because unexpected behavior occurred.'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Work Order listing api Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    public function store(CreateRequest $request, WorkOrder $workOrder): NoteListResource|JsonResponse
    {
        $this->authorize('create', WorkOrderNote::class);
        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if ($user->organization_id !== $workOrder->organization_id) {
                throw WorkOrderNoteException::inValidWorkOrder();
            }

            DB::beginTransaction();

            $workOrderNote = WorkOrderNote::create([
                'organization_id' => $user->organization_id,
                'work_order_id' => $workOrder->work_order_id,
                'work_order_status' => $workOrder->state->getValue(),
                'user_id' => $user->user_id,
                'note_type' => WorkOrderNoteTypes::INTERNAL(),
                'note' => trim($request->input('note')),
            ]);

            WorkOrderNoteLog::create([
                'organization_id' => $user->organization_id,
                'work_order_note_id' => $workOrderNote->work_order_note_id,
                'user_id' => $user->user_id,
                'note' => trim($request->input('note')),
            ]);

            WorkOrderNoteCreated::broadcast($workOrderNote->work_order_note_id, $user->user_id)->toOthers();

            DB::commit();

            return new NoteListResource($workOrderNote);

        } catch (UserNotFoundException|WorkOrderNoteException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Word Order Note store api failed');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Word Order Note store api failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Update work order note
     */
    public function update(UpdateRequest $request, WorkOrder $workOrder, WorkOrderNote $workOrderNote): JsonResponse|NoteListResource
    {
        $this->authorize('update', $workOrderNote);

        DB::beginTransaction();
        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if ($user->organization_id !== $workOrder->organization_id) {
                throw WorkOrderNoteException::inValidWorkOrder();
            }

            if ($workOrderNote->work_order_id !== $workOrder->work_order_id) {
                throw WorkOrderNoteException::inValidWorkOrderNote();
            }

            $workOrderNote->note = trim($request->input('note'));
            $workOrderNote->last_modified_user_id = $user->user_id;
            $workOrderNote->last_modified_at = Carbon::now();
            $workOrderNote->save();

            WorkOrderNoteLog::create([
                'organization_id' => $user->organization_id,
                'work_order_note_id' => $workOrderNote->work_order_note_id,
                'user_id' => $user->user_id,
                'note' => trim($request->input('note')),
            ]);

            WorkOrderNoteUpdated::broadcast($workOrderNote->work_order_note_id, $user->user_id)->toOthers();

            DB::commit();

            $workOrderNote->loadMissing(
                'user:user_id,first_name,last_name,middle_name,profile_pic,user_uuid',
                'lastModifiedUser:user_id,first_name,middle_name,last_name,profile_pic,user_uuid'
            );

            return new NoteListResource($workOrderNote);
        } catch (UserNotFoundException|WorkOrderNoteException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Word Order Note update api failed due to InvalidArgumentException');

            return Response::unprocessableEntity(message: 'Invalid argument passed to the request. Work order and task are not matching.');
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order Note Update API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Destroy the work order note
     */
    public function destroy(Request $request, WorkOrder $workOrder, WorkOrderNote $workOrderNote): JsonResponse
    {
        $this->authorize('delete', $workOrderNote);

        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            if ($user->organization_id !== $workOrder->organization_id) {
                throw WorkOrderNoteException::inValidWorkOrder();
            }

            if ($workOrderNote->work_order_id !== $workOrder->work_order_id) {
                throw WorkOrderNoteException::inValidWorkOrderNote();
            }

            DB::beginTransaction();
            // Now we save deleted user id on the last_modified_user_id column.
            // If a note was deleted then consider the last modified user as the deleted user and last_modified_at is the deleted time
            $workOrderNote->last_modified_user_id = $user->user_id;
            $workOrderNote->last_modified_at = Carbon::now();
            $workOrderNote->save();

            $workOrderNote->delete();

            WorkOrderNoteDeleted::broadcast($workOrderNote->work_order_note_id, $user->user_id)->toOthers();

            DB::commit();

            return Response::message(message: __('Work order note deleted successfully.'));
        } catch (UserNotFoundException|WorkOrderNoteException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Word Order Note delete api failed');

            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order Note Delete API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }
}

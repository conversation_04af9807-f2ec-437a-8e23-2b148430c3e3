<?php

namespace App\Http\Controllers;

use App\Enums\UserTypes;
use App\Exceptions\AWS\Cognito\CognitoAPIFailedException;
use App\Exceptions\NotFoundException\OrganizationNotFoundException;
use App\Helpers\Helper;
use App\Http\Filters\VendorListFilter;
use App\Http\Requests\User\StoreVendorUserRequest;
use App\Http\Requests\Vendor\StoreRequest;
use App\Http\Resources\AppfolioVendorResource;
use App\Models\Country;
use App\Models\OrganizationVendor;
use App\Models\Role;
use App\Models\Technician;
use App\Models\Timezone;
use App\Models\User;
use App\Models\Vendor;
use App\Services\Vendor\Enum\Service;
use Aws\CognitoIdentityProvider\CognitoIdentityProviderClient;
use Aws\CognitoIdentityProvider\Exception\CognitoIdentityProviderException;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;

class VendorController extends Controller
{
    protected CognitoIdentityProviderClient $cognitoClient;

    /**
     * UserController constructor
     */
    public function __construct()
    {
        $region = config('services.cognito.region');

        // Create a CognitoIdentityProviderClient object
        $this->cognitoClient = new CognitoIdentityProviderClient([
            'region' => $region,
            'version' => 'latest',
        ]);
    }

    /**
     * Display all vendor users.
     */
    public function index(Request $request, VendorListFilter $vendorFilter): AnonymousResourceCollection
    {
        $this->authorize('viewAny', Vendor::class);

        try {
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            $vendors = Vendor::join('organization_vendors', function ($query) use ($organization) {
                return $query->on('organization_vendors.vendor_id', 'vendors.vendor_id')
                    ->where('organization_vendors.organization_id', $organization->organization_id);
            })
                ->where('vendors.service', Service::THIRD_PARTY_VENDOR())
                ->leftJoin('states', 'vendors.state_province', 'states.state_code')
                ->select(
                    'vendors.vendor_id', 'vendors.vendor_uuid', 'vendors.company_name', 'vendors.is_company',
                    'vendors.first_name', 'vendors.last_name', 'vendors.email', 'vendors.phone_number', 'vendors.external_reference_link',
                    'vendors.address1', 'vendors.address2', 'vendors.city', 'vendors.state_province', 'vendors.country_id',
                    'vendors.is_active', 'vendors.gl_insurance_expire_at', 'vendors.on_boarding_status',
                    'vendors.postal_zip_code', 'states.name'
                )
                ->filter($vendorFilter)
                ->orderBy('vendors.first_name')
                ->orderBy('vendors.last_name')
                ->orderBy('vendors.vendor_id')
                ->paginate(config('pagination.vendor_list.per_page'));

            return AppfolioVendorResource::collection($vendors);
        } catch (OrganizationNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Vendor list api Failed[OrganizationNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Vendor list api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Store a new vendor user.
     */
    public function store(StoreVendorUserRequest $request): JsonResource|JsonResponse
    {
        $this->authorize('create', Vendor::class);

        $email = $request->input('email');
        $name = trim($request->input('first_name') . ' ' . $request->input('last_name'));

        $passwordCombinationArray = [
            Str::password(6, numbers: false, symbols: false), '@', random_int(0, 9999),
        ];
        $password = implode('', $passwordCombinationArray);

        $attributes = [
            [
                'Name' => 'email',
                'Value' => $email,
            ],
            [
                'Name' => 'name',
                'Value' => $name,
            ],
            [
                'Name' => 'email_verified',
                'Value' => 'True',
            ],
        ];

        DB::beginTransaction();

        try {
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            $user_exists = User::where('email', $email)
                ->where('user_type', UserTypes::TECHNICIAN())
                ->exists();

            if ($user_exists) {
                return response()->unprocessableEntity(message: __('User with this email already exists.'));
            }

            $adminCreateUser = [
                'UserPoolId' => config('services.cognito.provider.user_pool_id'),
                'Username' => $email,
                'TemporaryPassword' => $password,
                'UserAttributes' => $attributes,
                'DesiredDeliveryMediums' => ['EMAIL'],
                'ForceAliasCreation' => true,
            ];

            if (! $request->input('notify_user', true)) {
                $adminCreateUser['MessageAction'] = 'SUPPRESS';
            }

            $result = $this->cognitoClient->adminCreateUser($adminCreateUser);

            if (empty($result)) {
                throw new CognitoAPIFailedException(__('User pool creation failed.'));
            }
            $userData = $result->get('User');

            $user = User::create([
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'email' => $email,
                'cognito_user_id' => $userData['Username'] ?? '',
                'cognito_user_name' => $userData['Username'] ?? '',
                'organization_id' => $organization->organization_id,
                'user_type' => UserTypes::TECHNICIAN(),
                'country_id' => Country::where('alpha3_code', 'USA')->firstOrFail(['country_id'])->country_id,
                'timezone_id' => Timezone::where('name', config('settings.default_timezone'))->first()?->timezone_id,
            ]);

            // TODO :: move the vendor role name to constant or a enum value.
            $technicianRole = Role::select('role_id')
                ->where('organization_id', $organization->organization_id)
                ->where('name', 'Technician')
                ->firstOrFail();

            if (! empty($technicianRole)) {
                $user->assignRole($technicianRole->role_id);
            }

            $kcLat = 39.099724;
            $kcLong = -94.578331;

            $technician = Technician::updateOrCreate([
                'user_id' => $user->user_id,
            ], [
                'organization_id' => $user->organization_id,
                'latitude' => $kcLat,
                'longitude' => $kcLong,
                'max_travel_distance' => 50,
            ]);

            DB::commit();

            return new JsonResource([
                'user_id' => $user->user_uuid,
                'technician_id' => $technician->technician_uuid,
                'name' => $user->getName(),
                'email' => $user->email,
                'cognito_user_id' => $user->cognito_user_id,
                'temporary_password' => $password,
            ]);
        } catch (CognitoIdentityProviderException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Vendor store api Failed[CognitoIdentityProviderException]');

            // If the token is invalid, the getUser API will throw an exception
            return Response::unprocessableEntity(message: $exception->getAwsErrorMessage());
        } catch (QueryException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Vendor store api Failed[QueryException]');

            return Response::internalServerError(message: __('Something went wrong, unable to create role.'));
        } catch (CognitoAPIFailedException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Vendor store api Failed[CognitoAPIFailedException]', notify: true);

            return Response::internalServerError(message: $exception->getMessage());
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Vendor store api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Fetch vendor details.
     */
    public function show(Request $request, string $vendorId): AppfolioVendorResource|JsonResponse
    {
        $this->authorize('view', Vendor::class);

        try {
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            $vendor = Vendor::join('organization_vendors', function ($query) use ($organization) {
                return $query->on('organization_vendors.vendor_id', 'vendors.vendor_id')
                    ->where('organization_vendors.organization_id', $organization->organization_id);
            })
                ->where('vendors.service', Service::THIRD_PARTY_VENDOR())
                ->whereUuid($vendorId)
                ->select(
                    'vendors.vendor_id', 'vendors.vendor_uuid', 'vendors.company_name', 'vendors.is_company',
                    'vendors.first_name', 'vendors.last_name', 'vendors.email', 'vendors.phone_number',
                    'vendors.external_reference_link', 'vendors.address1', 'vendors.address2',
                    'vendors.city', 'vendors.state_province', 'vendors.country_id', 'vendors.is_active',
                    'vendors.gl_insurance_expire_at', 'vendors.on_boarding_status', 'vendors.postal_zip_code',
                    'vendors.last_sync_at', 'vendors.log_file_name'
                )
                ->firstOrFail();

            return new AppfolioVendorResource($vendor);
        } catch (OrganizationNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Vendor list api Failed[OrganizationNotFoundException]');

            return Response::notFound(message: $exception->getMessage());
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Vendor list api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Store a new vendor user.
     */
    public function storeVendor(StoreRequest $request): JsonResource|JsonResponse
    {
        DB::beginTransaction();

        try {
            $vendor = Vendor::updateOrCreate([
                'company_name' => $request->company_name,
                'service' => $request->service,
            ]);

            $organizations = $request->organizations;

            foreach ($organizations as $organization) {
                OrganizationVendor::updateOrCreate([
                    'organization_id' => $organization['organization_id'],
                    'vendor_id' => $vendor->vendor_id,
                ], [
                    'client_id' => $organization['client_id'],
                    'client_secret_key' => $organization['secret'],
                    'webhook_secret_key' => $organization['webhook_secret'],
                ]);
            }
            DB::commit();

            return new JsonResource([
                'vendor_name' => $vendor->company_name,
            ]);

        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Vendor store api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }
}

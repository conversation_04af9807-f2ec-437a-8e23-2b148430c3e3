<?php

namespace App\Http\Controllers;

use App\Enums\ServiceRequestNoteTypes;
use App\Events\ServiceRequest\ServiceRequestNoteCreated;
use App\Events\ServiceRequest\ServiceRequestNoteDeleted;
use App\Events\ServiceRequest\ServiceRequestNoteUpdated;
use App\Exceptions\NotFoundException\UserNotFoundException;
use App\Helpers\Helper;
use App\Http\Requests\ServiceRequest\Note\CreateRequest;
use App\Http\Requests\ServiceRequest\Note\UpdateRequest;
use App\Http\Resources\ServiceRequest\Note\NoteListResource;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestNote;
use App\Models\ServiceRequestNoteLog;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;
use LogicException;
use UnexpectedValueException;

class ServiceRequestNoteController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @throws AuthorizationException
     */
    public function index(Request $request, ServiceRequest $serviceRequest): AnonymousResourceCollection|JsonResponse
    {

        $this->authorize('viewAny', [ServiceRequest::class]);
        try {
            $user = $request->user();

            if (empty($user)) {
                throw new UserNotFoundException;
            }

            $serviceRequestNotes = ServiceRequestNote::with(
                'user:user_id,first_name,last_name,middle_name,profile_pic,user_uuid',
                'lastModifiedUser:user_id,first_name,middle_name,last_name,profile_pic,user_uuid'
            )
                ->where('service_request_id', $serviceRequest->service_request_id)
                ->where('organization_id', $user->organization_id)
                ->select(
                    'service_request_note_id', 'service_request_note_uuid', 'service_request_id', 'service_request_status_id',
                    'user_id', 'note_type', 'note', 'last_modified_at', 'last_modified_user_id', 'created_at'
                )
                ->orderBy('created_at')
                ->get();

            return NoteListResource::collection($serviceRequestNotes);

        } catch (UserNotFoundException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request Note listing api Failed, due to user not found');

            return Response::unprocessableEntity(errors: [$exception->getMessage()], message: __('The server was unable to process the request because unexpected behavior occurred.'));
        } catch (UnexpectedValueException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request Note listing api Failed[UnexpectedValueException]');

            return Response::unprocessableEntity(errors: [$exception->getMessage()], message: __('The server was unable to process the request because unexpected behavior occurred.'));
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request Note listing api Failed[InvalidArgumentException]');

            return Response::badRequest(errors: [$exception->getMessage()], message: __('The server was unable to process the request because it contains invalid data.'));
        } catch (LogicException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request Note listing api Failed[LogicException]', notify: true);

            return Response::internalServerError();
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service Request Note listing api Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    public function store(CreateRequest $request, ServiceRequest $serviceRequest): NoteListResource|JsonResponse
    {
        $this->authorize('create', [ServiceRequestNote::class]);
        DB::beginTransaction();
        try {

            $serviceRequestNote = ServiceRequestNote::create([
                'organization_id' => $request->user()?->organization_id,
                'service_request_id' => $serviceRequest->service_request_id,
                'service_request_status_id' => $serviceRequest->service_request_status_id,
                'user_id' => $request->user()?->user_id,
                'note_type' => ServiceRequestNoteTypes::INTERNAL(),
                'note' => trim($request->input('note')),
            ]);

            ServiceRequestNoteLog::create([
                'organization_id' => $request->user()?->organization_id,
                'service_request_note_id' => $serviceRequestNote->service_request_note_id,
                'user_id' => $request->user()?->user_id,
                'note' => trim($request->input('note')),
            ]);

            $serviceRequest->load([
                'organization:organization_id,organization_uuid',
            ]);

            /** @var User $authUser */
            $authUser = $request->user();
            ServiceRequestNoteCreated::dispatch($serviceRequest->service_request_id, $serviceRequestNote->service_request_note_id, $authUser);

            DB::commit();

            return new NoteListResource($serviceRequestNote);

        } catch (QueryException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Service Request Note store api failed[QueryException]', notify: true);

            return Response::internalServerError();
        } catch (InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Service Request Note store api failed due to InvalidArgumentException');

            return Response::unprocessableEntity(message: 'Invalid argument passed to the request.');
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Service Request Note store api failed[Exception]', notify: true);

            return Response::internalServerError($exception->getMessage());
        }
    }

    public function update(UpdateRequest $request, ServiceRequest $serviceRequest, ServiceRequestNote $serviceRequestNote): JsonResponse|NoteListResource
    {
        $this->authorize('update', [ServiceRequestNote::class, $serviceRequestNote]);

        DB::beginTransaction();
        try {

            $serviceRequestNote->note = trim($request->input('note'));
            $serviceRequestNote->last_modified_user_id = $request->user()?->user_id;
            $serviceRequestNote->last_modified_at = Carbon::now();
            $serviceRequestNote->save();

            ServiceRequestNoteLog::create([
                'organization_id' => $request->user()?->organization_id,
                'service_request_note_id' => $serviceRequestNote->service_request_note_id,
                'user_id' => $request->user()?->user_id,
                'note' => trim($request->input('note')),
            ]);

            DB::commit();

            $serviceRequestNote->loadMissing(
                'user:user_id,first_name,last_name,middle_name,profile_pic,user_uuid',
                'lastModifiedUser:user_id,first_name,middle_name,last_name,profile_pic,user_uuid'
            );
            $serviceRequest->load([
                'organization:organization_id,organization_uuid',
            ]);

            ServiceRequestNoteUpdated::dispatch($serviceRequest, $serviceRequestNote);

            return new NoteListResource($serviceRequestNote);
        } catch (InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Word Order Note update api failed due to InvalidArgumentException');

            return Response::unprocessableEntity(message: 'Invalid argument passed to the request.');
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Work Order Note Update API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }

    /**
     * Destroy the work order note
     */
    public function destroy(Request $request, ServiceRequest $serviceRequest, ServiceRequestNote $serviceRequestNote): JsonResponse
    {
        $this->authorize('delete', [ServiceRequestNote::class, $serviceRequestNote]);

        DB::beginTransaction();
        try {

            // Now we save deleted user id on the last_modified_user_id column.
            // If a note was deleted then consider the last modified user as the deleted user and last_modified_at is the deleted time
            $serviceRequestNote->last_modified_user_id = $request->user()?->user_id;
            $serviceRequestNote->last_modified_at = Carbon::now();
            $serviceRequestNote->save();

            $serviceRequestNote->delete();

            $serviceRequest->load([
                'organization:organization_id,organization_uuid',
            ]);

            ServiceRequestNoteDeleted::dispatch($serviceRequest, $serviceRequestNote);

            DB::commit();

            return Response::message(message: __('Service Request Note deleted successfully.'));
        } catch (InvalidArgumentException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Service Request Note delete api failed due to InvalidArgumentException');

            return Response::unprocessableEntity(message: 'Invalid argument passed to the request.');
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Service Request Note Delete API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Exceptions\NotFoundException\OrganizationNotFoundException;
use App\Exceptions\PermissionsFromInvalidFeatureException;
use App\Helpers\Helper;
use App\Http\Filters\RoleListFilter;
use App\Http\Resources\FeatureResource;
use App\Http\Resources\RoleResource;
use App\Models\Organization;
use App\Models\Permission;
use App\Models\Role;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;
use Illuminate\Validation\Rule;

class RoleController extends Controller
{
    /**
     * Organization roles with permissions
     *
     * @throws AuthorizationException
     */
    public function index(Request $request, RoleListFilter $filter): AnonymousResourceCollection
    {
        $this->authorize('viewAny', Role::class);

        $roles = Role::select('role_id', 'role_uuid', 'name', 'parent_role_id', 'description')
            ->withCount(
                ['users' => fn ($query) => $query->where('organization_id', $request->user()?->organization_id)]
            )
            ->filter($filter)
            ->paginate($request->input('per_page', config('pagination.role_list.per_page')));

        return RoleResource::collection($roles);
    }

    /**
     * Create role with permissions in the organization
     *
     * @throws AuthorizationException
     */
    public function store(Request $request): RoleResource|JsonResponse
    {
        $this->authorize('create', Role::class);

        $request->validate([
            'name' => [
                'required', 'max:255',
                Rule::unique('roles', 'name')
                    ->where('organization_id', $request->user()?->organization_id)
                    ->whereNull('deleted_at'),
            ],
            'parent_role_id' => 'sometimes|nullable|exists:roles,role_uuid',
            'permissions' => ['required', 'array'],
            'description' => 'sometimes|max:255',
        ]);

        try {
            DB::beginTransaction();

            /** @var Organization $organization */
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            if ($this->hasPermissionsFromInvalidFeatures($organization, $request->get('permissions'))) {
                throw new PermissionsFromInvalidFeatureException(__('Permission list contains invalid values.'));
            }

            $role = Role::create([
                'name' => trim($request->input('name')),
                'organization_id' => $organization->organization_id,
                'parent_role_id' => $request->input('parent_role_id'),
                'description' => $request->input('description'),
            ]);

            $permissions = Permission::select('permission_id')
                ->whereUuid($request->input('permissions'))
                ->pluck('permission_id');

            $role->permissions()->sync($permissions);

            $role->load('permissions');

            DB::commit();

            return new RoleResource($role);
        } catch (OrganizationNotFoundException|PermissionsFromInvalidFeatureException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Role create api failed due to ' . get_class($exception));

            return Response::unauthorized(message: $exception->getMessage());
        } catch (QueryException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Role create api failed[QueryException]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, unable to create role.'));
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Role create api failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Show role details
     *
     * @throws AuthorizationException
     */
    public function show(Role $role): RoleResource
    {
        $this->authorize('view', $role);

        $role->load('permissions');

        return new RoleResource($role);
    }

    /**
     * Create update role and permissions in the organization
     *
     * @throws AuthorizationException
     */
    public function update(Request $request, Role $role): RoleResource|JsonResponse
    {
        $this->authorize('update', $role);

        $organizationId = $request->user()?->organization_id;

        // Get the role_id of role from path route
        $roleId = $request->route('role')->role_id ?? null;

        $request->validate([
            'name' => "required|unique:roles,name,{$roleId},role_id,deleted_at,NULL,organization_id,{$organizationId}|max:255",
            'parent_role_id' => 'sometimes|nullable|exists:roles,role_uuid',
            'permissions' => 'required|array',
            'description' => 'sometimes|max:255',
        ]);

        try {
            DB::beginTransaction();

            /** @var Organization $organization */
            $organization = $request->user()?->organization;

            if (! $organization) {
                throw new OrganizationNotFoundException;
            }

            if ($this->hasPermissionsFromInvalidFeatures($organization, $request->get('permissions'))) {
                throw new PermissionsFromInvalidFeatureException(__('Permission list contains invalid values.'));
            }

            if ($request->has('name')) {
                $role->name = trim($request->get('name'));
            }

            if ($request->has('description')) {
                $role->description = $request->get('description');
            }

            $role->save();

            if ($request->has('permissions')) {
                $permissions = Permission::select('permission_id')
                    ->whereUuid($request->input('permissions'))
                    ->pluck('permission_id');

                $role->permissions()->sync($permissions);
            }

            $role->load('permissions');

            DB::commit();

            return new RoleResource($role);
        } catch (OrganizationNotFoundException|PermissionsFromInvalidFeatureException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Role update api failed due to ' . get_class($exception));

            return Response::unauthorized(message: $exception->getMessage());
        } catch (QueryException $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Role update api failed[QueryException]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, unable to create role.'));
        } catch (Exception $exception) {
            DB::rollBack();
            Helper::exceptionLog(exception: $exception, message: 'Role update api failed[Exception]', notify: true);

            return Response::internalServerError(message: __('Something went wrong, The server was unable to process'));
        }
    }

    /**
     * Destroy the role
     *
     * @throws AuthorizationException
     */
    public function destroy(Role $role): HttpResponse|JsonResponse
    {
        $this->authorize('delete', $role);

        $usersExist = DB::table('user_role')->where('role_id', $role->role_id)->count();
        if ($usersExist) {
            return response()->unprocessableEntity(message: __('Unable to delete the role because it is already in use.'));
        }

        $role->syncPermissions([]);
        $role->delete();

        return Response::noContent();
    }

    /**
     * Organization features with permissions
     *
     * @throws AuthorizationException
     */
    public function listPermissions(Request $request): AnonymousResourceCollection|JsonResponse
    {
        $this->authorize('viewAny', Role::class);

        /** @var Organization $organization */
        $organization = $request->user()?->organization;

        if (! $organization) {
            return Response::notFound();
        }

        $features = $organization
            ->features()
            ->with(['permissions' => function ($query) {
                $query->orderBy('label', 'asc');
            }])
            ->orderBy('name')
            ->get();

        return FeatureResource::collection($features);
    }

    /**
     * Check permissions from disabled features
     *
     * @param  string[]  $permissions
     */
    protected function hasPermissionsFromInvalidFeatures(Organization $organization, array $permissions): bool
    {
        return Permission::whereUuid($permissions)
            ->whereNotIn(
                'feature_id',
                $organization
                    ->getCachedFeatures()
                    ->pluck('feature_id')
                    ->toArray()
            )
            ->exists();
    }
}

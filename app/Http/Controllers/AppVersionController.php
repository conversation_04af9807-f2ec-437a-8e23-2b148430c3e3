<?php

namespace App\Http\Controllers;

use App\Helpers\Helper;
use App\Models\AppVersion;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use Symfony\Component\HttpFoundation\Response as HttpResponse;

class AppVersionController extends Controller
{
    /**
     * Check if requested app version is latest
     */
    public function checkLatestAppVersion(Request $request): HttpResponse|JsonResource
    {

        try {
            $headers = $request->headers->all();

            $data = [
                'is_latest_app' => true,
                'has_minimum_app_version' => true,
            ];

            $xAppVersion = $headers['device-app-version'][0] ?? '';
            $osType = $headers['device-os'][0] ?? '';
            $osType = strtolower($osType);

            $validator = Validator::make([
                'device-os' => $osType,
                'device-app-version' => $xAppVersion,
            ], [
                'device-os' => 'required|in:ios,android',
                'device-app-version' => 'required',
            ]);

            if ($validator->fails()) {
                return Response::unprocessableEntity(message: $validator->errors()->toArray());
            }

            $appVersion = AppVersion::latest()->first();

            if (empty($appVersion)) {
                return Response::unprocessableEntity(message: __('Latest App version details not found!'));
            }

            // Check if requested app version is below the latest app version.
            $latestAppVersion = $appVersion->{'technician_' . $osType . '_version'};
            $latestMinimumAppVersion = $appVersion->{'technician_minimum_' . $osType . '_version'};

            $data['minimum_app_version'] = $latestMinimumAppVersion;

            if (! empty($latestAppVersion) && version_compare($latestAppVersion, $xAppVersion) == 1) {
                $data['is_latest_app'] = false;

                if (! empty($latestMinimumAppVersion) && version_compare($latestMinimumAppVersion, $xAppVersion) == 1) {
                    $data['has_minimum_app_version'] = false;
                }
            }

            return new JsonResource($data);
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Exception occurred while checking Latest App Version', notify: true);

            return Response::internalServerError();
        }
    }
}

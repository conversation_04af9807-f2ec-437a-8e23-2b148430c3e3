<?php

namespace App\Http\Controllers;

use App\Enums\UserTypes;
use App\Events\ServiceRequest\ServiceRequestAssigneeAdded;
use App\Events\ServiceRequest\ServiceRequestAssigneeRemoved;
use App\Events\ServiceRequest\ServiceRequestAssigneeUpdated;
use App\Exceptions\ServiceRequestAssigneeException;
use App\Helpers\Helper;
use App\Http\Resources\ServiceRequest\AssigneeResource;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestAssignee;
use App\Models\User;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use InvalidArgumentException;

class ServiceRequestAssigneeController extends Controller
{
    //store service request assignees
    public function store(Request $request, ServiceRequest $serviceRequest, User $assignee): AssigneeResource|JsonResponse
    {
        $this->authorize('manageAssignee', $serviceRequest);
        try {

            if ($assignee->user_type !== UserTypes::ACCOUNT_USER()) {
                throw ServiceRequestAssigneeException::invalidUserType();
            }

            if ($assignee->organization_id !== $serviceRequest->organization_id) {
                throw ServiceRequestAssigneeException::organizationMismatch();
            }

            if (ServiceRequestAssignee::where('service_request_id', $serviceRequest->service_request_id)
                ->where('user_id', $assignee->user_id)
                ->exists()) {
                throw ServiceRequestAssigneeException::alreadyExisting();
            }

            $serviceRequestAssignee = ServiceRequestAssignee::updateOrCreate([
                'service_request_id' => $serviceRequest->service_request_id,
                'user_id' => $assignee->user_id,
            ], [
                'service_request_id' => $serviceRequest->service_request_id,
                'user_id' => $assignee->user_id,
                'action_by_user_id' => $request->user()?->user_id,
            ]);
            /** @var User $authUser */
            $authUser = $request->user();

            ServiceRequestAssigneeUpdated::broadcast($serviceRequest, $assignee, 'add')->toOthers();
            // If self assign we prevent the notification
            if ($authUser->user_id !== $assignee->user_id) {
                ServiceRequestAssigneeAdded::dispatch($serviceRequest, $assignee, $authUser);
            }

            return new AssigneeResource($serviceRequestAssignee);
        } catch (ServiceRequestAssigneeException $exception) {
            return Response::unprocessableEntity($exception->getMessage());
        } catch (InvalidArgumentException $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Invalid request data.');

            return Response::unprocessableEntity(__('Invalid request data.'));
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Add service request assignee api Failed[Exception]', notify: true);

            return Response::internalServerError(message: __($exception->getMessage()));
        }
    }

    /**
     * Destroy the service request assignee
     */
    public function destroy(Request $request, ServiceRequest $serviceRequest, User $assignee): JsonResponse
    {
        $this->authorize('manageAssignee', $serviceRequest);
        try {
            $serviceRequestAssignee = ServiceRequestAssignee::where([
                'service_request_id' => $serviceRequest->service_request_id,
                'user_id' => $assignee->user_id,
            ])->first();

            if (empty($serviceRequestAssignee)) {
                throw ServiceRequestAssigneeException::assigneeNotFound();
            }
            $serviceRequestAssignee->removed_by_user_id = $request->user()?->user_id;
            $serviceRequestAssignee->save();

            $serviceRequestAssignee->delete();
            ServiceRequestAssigneeUpdated::broadcast($serviceRequest, $assignee, 'delete')->toOthers();
            ServiceRequestAssigneeRemoved::dispatch($serviceRequest, $assignee);

            return Response::message(message: __('Service request assignee deleted successfully.'));
        } catch (ServiceRequestAssigneeException $exception) {
            return Response::unprocessableEntity(message: $exception->getMessage());
        } catch (ModelNotFoundException $exception) {
            Helper::exceptionLog($exception, message: __('Service request assignee delete[ModelNotFoundException]'));

            return Response::notFound();
        } catch (Exception $exception) {
            Helper::exceptionLog(exception: $exception, message: 'Service request assignee Delete API Failed[Exception]', notify: true);

            return Response::internalServerError();
        }
    }
}

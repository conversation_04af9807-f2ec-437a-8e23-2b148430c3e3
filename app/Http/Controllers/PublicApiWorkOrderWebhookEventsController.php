<?php

namespace App\Http\Controllers;

use App\DataTables\PublicApiWorkOrderWebhookEventsDataTable;
use App\Models\PublicApiWorkOrderWebhookEvent;

class PublicApiWorkOrderWebhookEventsController extends Controller
{
    /*
    * List public api workorder webhook events logs.
    */
    public function index(PublicApiWorkOrderWebhookEventsDataTable $dataTable): mixed
    {
        return $dataTable->render('public_api_work_order_webhook_events.index');
    }

    /**
     * Show public api workorder webhook event detailed view.
     */
    public function show(string $publicApiWoWebhookEventUuid): mixed
    {
        $publicApiWorkOrderWebhookEvent = PublicApiWorkOrderWebhookEvent::whereUuid($publicApiWoWebhookEventUuid)->first();

        return view('public_api_work_order_webhook_events.show', ['publicApiWorkOrderWebhookEvent' => $publicApiWorkOrderWebhookEvent]);
    }
}

<?php

namespace App\Http\Middleware;

use App\Models\Media;
use Illuminate\Support\Str;
use Symfony\Component\Mime\MimeTypes;
use TusPhp\Middleware\TusMiddleware;
use TusPhp\Request;
use TusPhp\Response;

class MakeTusMediaFileName implements TusMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Response $response): void
    {
        if ($request->header('Media-Id')) {
            $media = Media::whereUuid($request->header('Media-Id'))->firstOrFail();

            $fileName = $this->fileNameResolver($media, $request);

            $this->setNameInUploadMetaRequestHeader($request, $fileName);
        }
    }

    public function setNameInUploadMetaRequestHeader(Request $request, string $fileName): void
    {
        $uploadMetaData = $request->header('Upload-Metadata');
        $uploadMetaDataEncodedArray = [];
        $hasNameMetaValue = false;

        if (! empty($uploadMetaData)) {
            $uploadMetaDataChunks = explode(',', $uploadMetaData);

            foreach ($uploadMetaDataChunks as $chunk) {
                $pieces = explode(' ', trim($chunk));

                $key = $pieces[0];
                $value = $pieces[1] ?? '';

                if ($key === 'name') {
                    $value = base64_encode($fileName);
                    $hasNameMetaValue = true;
                }

                $uploadMetaDataEncodedArray[] = "{$key} {$value}";
            }
        }
        if (! $hasNameMetaValue) {
            $uploadMetaDataEncodedArray[] = 'name ' . base64_encode($fileName);
        }

        $request->getRequest()->headers->set('Upload-Metadata', implode(',', $uploadMetaDataEncodedArray));
    }

    protected function fileNameResolver(Media $media, Request $request): string
    {
        $storedFileName = $media->file_name;

        if (strtolower($request->method()) === 'post') {
            $fileType = $request->extractMeta('filetype');
            $fileName = $request->extractMeta('filename');

            if (Str::contains($fileName, '.')) {
                $extensionFromFileName = Str::afterLast($fileName, '.');
                $extensionFromFileType = (new MimeTypes)->getExtensions($fileType)[0] ?? null;

                if ($extensionFromFileName && $extensionFromFileType && ($extensionFromFileName === $extensionFromFileType)) {

                }
            }
        }

        return $storedFileName;
    }
}

<?php

namespace App\Http\Middleware;

use App\Models\AppVersion;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response as HttpResponse;
use Symfony\Component\HttpFoundation\Response;

class MinimumAppVeriosnRequiredMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $appVersion = Cache::remember(
            config('settings.cache.look_up.app_version_latest.name'),
            now()->addMinutes(config('settings.cache.look_up.app_version_latest.expire_time_in_minutes')),
            static fn () => AppVersion::latest()->first()
        );
        if ($appVersion) {
            $deviceOs = $request->headers->get('device-os');
            $deviceAppVersion = $request->headers->get('device-app-version');

            if ($deviceOs) {
                $deviceOs = strtolower($deviceOs);
                $currentMinimumRequiredVersion = $appVersion->{"technician_minimum_{$deviceOs}_version"};

                if (version_compare("{$deviceAppVersion}", "{$currentMinimumRequiredVersion}", 'lt')) {
                    return HttpResponse::unprocessableEntity(
                        message: __('Upgrade required, you are using an old version, which is no longer supports, please update to latest version to continue.')
                    );
                }
            }
        }

        return $next($request);
    }
}

<?php

namespace App\Http\Middleware;

use App\Exceptions\AWS\Cognito\JwtValidationException;
use App\Models\Organization;
use App\Models\User;
use App\Traits\ValidatesJWT;
use Carbon\CarbonImmutable;
use Closure;
use Exception;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class ValidateCognitoToken
{
    use ValidatesJWT;

    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $token = $request->bearerToken();

            if (! $token) {
                return response()
                    ->unauthorized(message: __('Token is required.'));
            }

            $claims = $request->attributes->get('cognito_claims') ?? $this->validateJWT($token);

            $userPoolId = Str::afterLast($claims->iss, '/');

            $cognitoUserId = $claims->sub;
            $clientId = $claims->client_id;

            if ($userPoolId === config('services.cognito.provider.user_pool_id')) {
                $user = User::with('organization')
                    ->where('cognito_user_id', $cognitoUserId)
                    ->firstOrFail();

                $this->updateLastActivity($user);

                $organization = $user->organization;
            } else {
                $organization = Organization::where('user_pool_id', $userPoolId)
                    ->firstOrFail(['user_pool_api_client_id', 'organization_id']);

                $user = null;

                if ($cognitoUserId === $clientId) {
                    // Handling m2m token ser.
                    $user = User::newModelInstance([
                        'first_name' => 'Public',
                        'last_name' => 'API',
                        'organization_id' => $organization->organization_id,
                        'user_type' => 'm2m',
                        'last_activity_at' => now(),
                    ]);

                    if (method_exists($user, 'setAttribute')) {
                        $user->setAttribute('scopes', array_map(static fn ($scope) => trim($scope), explode(' ', $claims->scope)));
                    }

                    if (method_exists($user, 'setRelation')) {
                        $user->setRelation('organization', $organization);
                    }
                }

                if ($user === null) {
                    $user = User::where('cognito_user_id', $cognitoUserId)
                        ->where('organization_id', $organization->organization_id)
                        ->firstOrFail();

                    $this->updateLastActivity($user);
                }
            }

            if (empty($organization)) {
                return response()
                    ->unauthorized(message: __('Invalid Organization.'));
            }
            if (method_exists($user, 'loadMissing')) {
                $user->loadMissing('organization');
            }

            if ($user instanceof Authenticatable) {
                auth()->login($user);
            }
            $request->setUserResolver(function () use ($user) {
                return $user;
            });

            $request->attributes->set('organization_id', $organization->organization_id);

            return $next($request);
        } catch (ModelNotFoundException $exception) {
            return response()->notFound();
        } catch (JwtValidationException|Exception $exception) {
            return response()->unauthorized(message: $exception->getMessage());
        }
    }

    protected function updateLastActivity(User $user): void
    {
        $user->last_activity_at = CarbonImmutable::now();
        $user->save();
    }
}

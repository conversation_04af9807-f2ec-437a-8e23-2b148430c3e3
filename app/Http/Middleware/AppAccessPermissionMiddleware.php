<?php

namespace App\Http\Middleware;

use App\Enums\UserTypes;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

/**
 * Ensure that the app access permission is granted to the technician when they request from the app
 */
class AppAccessPermissionMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->user()?->user_type === UserTypes::TECHNICIAN() &&
            Str::lower($request->header('Device-Type', ' ')) === Str::lower('Mobile')
        ) {
            if ($request->user() && $request->user()->cant('accessApp', User::class)) {

                return response()
                    ->unauthorized(message: __('This application is not accessible to this user.'));
            }

        }

        return $next($request);
    }
}

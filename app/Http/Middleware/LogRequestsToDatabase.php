<?php

namespace App\Http\Middleware;

use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\SSOController;
use App\Http\Controllers\WebhookController;
use App\Models\RequestLog;
use Closure;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Http\Response as IlluminateResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;

class LogRequestsToDatabase
{
    /**
     * The list of hidden request headers.
     *
     * @var array<int,string>
     */
    public static array $hiddenRequestHeaders = [
        'authorization',
        'php-auth-pw',
        'cookie',
        'x-csrf-token',
        'x-xsrf-token',
    ];

    /**
     * The list of hidden request parameters.
     *
     * @var array<int,string>
     */
    public static array $hiddenRequestParameters = [
        'password',
        'password_confirmation',
        '_token',
    ];
    /**
     * @var string[]
     */
    public static array $exceptRouteUri = [
        '/hcp',
        '/api/views',
        '/api/views/*',
        '/api/lookup/*',
        '/debug/*',
    ];

    /**
     * The list of hidden response parameters.
     *
     * @var array<int,string>
     */
    public static array $hiddenResponseParameters = [];

    /**
     * Format the given model to a readable string.
     *
     * @param  Model  $model
     */
    public static function given($model): string
    {
        return get_class($model) . ':' . implode('_', Arr::wrap($model->getKey()));
    }

    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     */
    public function handle($request, Closure $next): mixed
    {
        return $next($request);
    }

    /**
     * Log requests after the response has been sent to the browser.
     */
    public function terminate(Request $request, Response $response): void
    {
        if (
            ! config('settings.log_requests_to_db') ||
            $this->shouldIgnoreUnauthenticated($request)
        ) {
            return;
        }

        if ($this->shouldIgnoreRoute($request)) {
            return;
        }

        $startTime = defined('LARAVEL_START') ? LARAVEL_START : $request->server('REQUEST_TIME_FLOAT');

        $requestLog = new RequestLog;
        $requestLog->request_uuid = $request->get('request_uuid');
        $requestLog->device_type = $request->header('Device-Type', 'Web');
        $requestLog->organization_id = $request->user()?->organization_id;
        $requestLog->user_id = $request->user()?->user_id;
        $requestLog->method = $request->method();
        $requestLog->status_code = $response->getStatusCode();
        $requestLog->duration = $startTime ? floor((microtime(true) - (float) $startTime) * 1000) : null;
        $requestLog->memory = round(memory_get_peak_usage(true) / 1024 / 1024, 1);

        $content = [
            'ip_addresses' => $request->ips(),
            'uri' => str_replace($request->root(), '', $request->fullUrl()) ?: '/',
            'controller_action' => optional($request->route())->getActionName(),
            'middleware' => array_values(optional($request->route())->gatherMiddleware() ?? []),
            'headers' => [
                ...$this->headers($request->headers->all()),
                ...[
                    'cognito_claims' => Arr::only(
                        (array) $request->attributes->get('cognito_claims'), ['exp', 'iat', 'scope', 'auth_time', 'token_use']
                    ),
                ],
            ],
            'payload' => $this->payload($this->input($request)),
            'response' => $this->response($response),
            'exception' => session('exception_log'),
        ];

        $requestLog->content = $content;
        $requestLog->save();
    }

    /**
     * Determine if the content is within the set limits.
     */
    public function contentWithinLimits(string $content): bool
    {
        $limit = $this->options['size_limit'] ?? 64;

        return intdiv(mb_strlen($content), 1000) <= $limit;
    }

    /**
     * Format the given headers.
     *
     * @param  array<string,mixed>  $headers
     */
    protected function headers(array $headers): mixed
    {
        $headers = collect($headers)->map(function ($header) {
            return $header[0];
        })->toArray();

        return $this->hideParameters(
            $headers,
            static::$hiddenRequestHeaders
        );
    }

    /**
     * Format the given payload.
     */
    protected function payload(mixed $payload): mixed
    {
        return $this->hideParameters(
            $payload,
            static::$hiddenRequestParameters
        );
    }

    /**
     * Hide the given parameters.
     *
     * @param  array<int,string>  $hidden
     */
    protected function hideParameters(mixed $data, array $hidden): mixed
    {
        foreach ($hidden as $parameter) {
            if (Arr::get($data, $parameter)) {
                Arr::set($data, $parameter, '********');
            }
        }

        return $data;
    }

    /**
     * Format the given response object.
     *
     * @return array<string,mixed>|string
     */
    protected function response(Response $response): array|string
    {
        $content = $response->getContent();

        if (is_string($content)) {
            if (
                is_array(json_decode($content, true)) &&
                json_last_error() === JSON_ERROR_NONE
            ) {
                return $this->contentWithinLimits($content)
                    ? $this->hideParameters(json_decode($content, true), static::$hiddenResponseParameters)
                    : 'Purged - Content out of limit';
            }

            if (Str::startsWith(strtolower($response->headers->get('Content-Type') ?? ''), 'text/plain')) {
                return $this->contentWithinLimits($content) ? $content : 'Purged - Content out of limit';
            }
        }

        if ($response instanceof RedirectResponse) {
            return 'Redirected to ' . $response->getTargetUrl();
        }

        if ($response instanceof IlluminateResponse && $response->getOriginalContent() instanceof View) {
            return [
                'view' => $response->getOriginalContent()->getPath(),
                'data' => $this->extractDataFromView($response->getOriginalContent()),
            ];
        }

        if (is_string($content) && empty($content)) {
            return 'Empty Response';
        }

        return 'HTML Response';
    }

    /**
     * Extract the data from the given view in array form.
     *
     * @param  View  $view
     * @return array<mixed,mixed>
     */
    protected function extractDataFromView($view): array
    {
        return collect($view->getData())->map(function ($value) {
            if ($value instanceof Model) {
                return static::given($value);
            } elseif (is_object($value)) {
                return [
                    'class' => get_class($value),
                    'properties' => json_decode((string) json_encode($value), true),
                ];
            } else {
                return json_decode((string) json_encode($value), true);
            }
        })->toArray();
    }

    /**
     * Determine if the request should be ignored based on authentication status
     * except the auth controller based routes which have password reset requests
     * which are usually unauthenticated but need to be logged.
     */
    protected function shouldIgnoreUnauthenticated(Request $request): bool
    {
        // Ignoring calls for webhook calls
        if ($request->routeIs('webhook', 'webhook.*')) {
            return false;
        }

        return ! auth()->check() && ! empty($request->route()->controller) && (! in_array(get_class($request->route()->controller), [
            RegisterController::class, SSOController::class, WebhookController::class,
        ]));
    }

    /**
     * Determine if the request has a URI that should pass through CSRF verification.
     */
    protected function inExceptArray(Request $request): bool
    {
        foreach (static::$exceptRouteUri as $except) {
            if ($except !== '/') {
                $except = trim($except, '/');
            }

            if ($request->fullUrlIs($except) || $request->is($except)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine the requested url is in the except url, then ignore the log for the request.
     */
    protected function shouldIgnoreRoute(Request $request): bool
    {
        return $this->inExceptArray($request);
    }

    /**
     * Extract the input from the given request.
     *
     * @return array<mixed,mixed>
     */
    private function input(Request $request): array
    {
        $files = $request->files->all();

        array_walk_recursive($files, function (&$file) {
            $file = [
                'name' => $file->getClientOriginalName(),
                'size' => $file->isFile() ? ($file->getSize() / 1000) . 'KB' : '0',
            ];
        });

        return array_replace_recursive($request->input(), $files);
    }
}

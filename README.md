# Docker [LULA-SAAS] 

Creating Docker container(s) local testing environment for Foresight Project.

## Getting Started

Here we're creating Docker container setup on your local machine to test the project locally. This guide provides instructions for Ubuntu Linux.  macOS users should refer to the separate macOS section.

### Backend: lula-saas-platform

Base Image: php:8.2.3-fpm
Framework: <PERSON><PERSON> (mounted code, Composer, required PHP modules)
Other Tools: Node.js 16, Supervisord, Nginx
Container IP: ***********

### Frontend: lula-saas-frontend

Base Image: Amazon Linux 2
Build Tool: Vite.js (with Node.js 16)
Container IP: ***********

### Database: lula-saas-db

Service: MySQL 8.0
Container IP: ***********

### Cache: lula-saas-redis
Service: Redis (latest)
Container IP: ***********

### Reverse Proxy: lula-saas-caddy-rev-proxy
Service: Caddy (reverse proxy web server)
Container IP: ************

## Prerequisites need to be installed on local machine

### For ubuntu

#### Docker and Docker Compose Installation ####

We need to install docker and docker-compose to run this infrastructure and follow the installation steps according to your Operating System.

Click [here](https://docs.docker.com/engine/install/) for various OS or for more reference on Docker installation.

Click [here](https://docs.docker.com/compose/install/) for various OS or for more reference on Docker Compose installation.

Below are the steps for installing docker engine for Ubuntu 22.04 (LTS)

1) If any older versions of Docker such as docker, docker.io, or docker-engine are installed, uninstall them:

```
sudo apt-get remove docker docker-engine docker.io containerd runc
```
Set up the repository

```
sudo apt-get update
```
```
sudo apt-get install \
    ca-certificates \
    curl \
    gnupg
```
Add Docker’s official GPG key:

```
sudo install -m 0755 -d /etc/apt/keyrings
```
```
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg
```
```
sudo chmod a+r /etc/apt/keyrings/docker.gpg
```
Set up the stable repository
```
echo \
  "deb [arch="$(dpkg --print-architecture)" signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  "$(. /etc/os-release && echo "$VERSION_CODENAME")" stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
``` 

Install Docker Engine, containerd, and Docker Compose.

```
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
```

Test the installation.
```
$ docker-compose --version
```

2) Install Git

 Run the below commands if your machine OS is Ubuntu 20.04. Click [here](https://github.com/git-guides/install-git) for other OS or for more reference.

```
$ sudo apt-get update
```
```
$ sudo apt-get install git-all
```

## For macOS

Install Homebrew (if not already installed)


1. Open Terminal and run:

```
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

After installation, verify Homebrew is installed:

```
brew doctor
```

Install Docker Desktop

```
brew install --cask docker
```

Launch Docker from your Applications folder and follow any on-screen instructions (such as
granting permissions).


Install Git via Homebrew:

```
brew install git
```

Verify Git is installed:

```
git --version
```


## To Run The Project Image

### Step -01

Create a directory named "projects" in the home directory. (If alreaady exists ignore this step)

```
mkdir -p /home/<USER>/projects/ # On Ubuntu
mkdir -p ~/projects/ # On macOS (Home directory)
```

Also create a directory for the mysql files to store locally.

```
mkdir -p /home/<USER>/mysql/lula-saas # On Ubuntu
mkdir -p ~/mysql/lula-saas # On macOS
```

### Step -02

Navigate to "projects" directory.

```
cd /home/<USER>/projects/ # Ubuntu
cd ~/projects/ # macOS
```

And clone all the repositories.

lula-saas-platform:

```
<NAME_EMAIL>:luladev/lula-saas-platform.git
```
lula-saas-frontend:

```
<NAME_EMAIL>:luladev/lula-saas-frontend.git
```

### Step -03

In "docker-compose.yml" we're mounting four volumes locally. 

- ./:/var/www  >> ./ corresponds to the path "/home/<USER>/projects/lula-saas-platform/" where lula-saas-platform code resides on your local machine.

- ../lula-saas-frontend/:/var/www/html >> where ../lula-saas-frontend corresponds to the path "/home/<USER>/projects/lula-saas-frontend/" where lula-saas-frontend code resides on your local machine.

${DB_PATH}:/var/lib/mysql >> ${DB_PATH} refers the absolute path in the local machine where mysql files store locally.

If you've created or downloaded repo into a different directory, change the volume entries with your preferred local system paths.

### Step -04

Configure Environment Variables
Each repository contains an .env file that must be updated.

Backend: In lula-saas-platform/.env
Open the file in your text editor and update the following sample settings:

```
# Application URL
APP_URL=http://platform-api.lulasaas.local
# Database configuration
DB_CONNECTION=mysql
DB_HOST=***********
DB_READ_HOST=************
DB_PORT=3306
DB_DATABASE="lula-saas"
DB_USERNAME="saas-api"
DB_PASSWORD="your_db_password"
DB_PATH=/home/<USER>/mysql/lula-saas # Or ~/mysql/lula-saas on macOS
# Redis configuration
REDIS_HOST=***********
REDIS_PASSWORD=null
REDIS_PORT=6379
#AWS Configuration
AWS_ACCESS_KEY_ID= #Request from another engineer.
AWS_SECRET_ACCESS_KEY= #Request from another engineer.
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=foresight-local
AWS_USE_PATH_STYLE_ENDPOINT=false
#Provider User Pool - Request these values from another engineer.
PROVIDER_POOL_ID=
PROVIDER_CLIENT_ID=
PROVIDER_AUTH_DOMAIN=https://vendor-local.auth.us-east-1.amazoncognito.com
# Local user permissions (replace with your UID and GID)
UID=1000 # Run: id -u
GID=1000 # Run: id -g
```

Frontend: lula-saas-frontend/.env
Open the file and update the following:

```
UID=1000 # Replace with your UID
GID=1000 # Replace with your GID
VITE_AUTH_API_BASE_URL=http://platform-api.lulasaas.local
```

## Step 5

(Optional) Network Access Settings

If you wish to provide view access to colleagues or devices on your network, add:

```
LOCAL_IP=your_machine_IP # e.g., ***********
CONTAINER_IP=*********** # e.g., the IP of the platform container
```

### Step -06

Also, make sure to update hosts file entries as below.

```
$ cat /etc/hosts 
127.0.0.1 platform-api.lulasaas.local tenant1-local.lulasaas.local tenant2-local.lulasaas.local
```
## Step -07

Review and Update Docker Compose Configuration
Before building the containers, verify that the Docker Compose file reflects your local setup.

Open the docker-compose.yml file in your editor. (Its location is typically in the same directory as
the repositories or a designated Docker directory.)

Verify Volume Mappings:
Ensure the volume paths match where you cloned the repositories and where you created your
MySQL directory. For example:

```
services:
lula-saas-platform:
container_name: lula-saas-platform
build:
context: ./
dockerfile: ./Dockerfile_local
args:
- UID=$UID
- GID=$GID
volumes:
# Update these paths if your repos are in a different location.
- /home/<USER>/projects/lula-saas-platform/:/var/www
networks:
docker_net:
ipv4_address: ***********
extra_hosts:
- "platform.lulasaas.local:************"
1. Open the hosts file:

2. Add the following lines:

3. Save and exit.

- "platform-api.lulasaas.local:************"
lula-saas-frontend:
container_name: lula-saas-frontend
build:
context: /home/<USER>/projects/lula-saas-frontend/
args:
- UID=$UID
- GID=$GID
volumes:
- /home/<USER>/projects/lula-saas-frontend/:/var/www/html
networks:
docker_net:
ipv4_address: ***********
extra_hosts:
- "platform.lulasaas.local:************"
- "platform-api.lulasaas.local:************"
lula-saas-db:
container_name: lula-saas-db
build:
context: ./docker/8.0
command: ["--default-authentication-plugin=mysql_native_password", "--
log_bin_trust_function_creators=1"]
ports:
- "3306:3306"
volumes:
- ${DB_PATH}:/var/lib/mysql
environment:
MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
MYSQL_USER: '${DB_USERNAME}'
MYSQL_PASSWORD: '${DB_PASSWORD}'
MYSQL_DATABASE: '${DB_DATABASE}'
REPLICATION_USER: 'lula_replication'
REPLICATION_PASSWORD: 'lula_replication@pass1'
networks:
docker_net:
ipv4_address: ***********
extra_hosts:
- "platform.lulasaas.local:************"
- "platform-api.lulasaas.local:************"
# Other services (redis, caddy, etc.) follow a similar pattern...
```


Key Points:
Backend: Ensure /home/<USER>/projects/lula-saas-platform/ (or ~/projects/lula-saas-platform/ on
macOS) is mounted to /var/www.

Frontend: Ensure /home/<USER>/projects/lula-saas-frontend/ (or ~/projects/lula-saas-frontend/ on
macOS) is mounted to /var/www/html.
Database: Confirm the environment variable DB_PATH points to your local MySQL data directory.

Save any changes before proceeding.


### Step -08

Creation of container(s)

Navigate to Project's root directory, also where the files "Dockerfile_local" and "docker-compose.yml" resides. 

Also the entrypoint script location is "docker/run_local.sh" and that will run composer and supevisor once the docker is up. 

Now run the below command --> To build and image and then run the container.

```
docker-compose up --build -d
```
By executing the above command docker images will be created on your machine and containers will be started.

After the creation of containers, if required restore project's MySQL dump to the MySQL container having IP *********** for the first time and then it will be stored locally in the specified path later even if the container is destroyed. 

**That's it. Now you can view your project by visiting URLs**

* lula-saas-frontend: http://platform.lulasaas.local

### Step -09

To dump data to the database, run the below commands. 

```
$ docker exec -it --user www lula-saas-platform bash
```

```
$php artisan migrate:fresh --seed
```

Seeders are not yet ready, going forward seeders will created for updating database as and when there are changes to Database structure and data.



If you want to  execute anything specific within the container without login to container, you can do this by running below command.

```
$ docker exec -it lula-saas-platform [COMMAND]
```

If you want to login to "lula-saas-frontend" container as created user to perform any actions, then

```
$ docker exec -it --user lulasaas lula-saas-frontend bash
```

If you want to login to container with root privilege, then (not recommended)

```
$ docker exec -it lula-saas-frontend bash
```
Executing commands as root will result in creating or modifying the files with ownership as root user and mounted in the local machine, hence it is not recommended. 

**Other useful commands**

docker images --- To list the available images

docker ps --- To list the running containers

docker ps -a --- To list both running and stopped containers

docker-compose stop --- To stop the docker containers

docker-compose start --- To start the docker containers

docker-compose down --- To remove the docker containers

docker-compose up -d --- To run containers if the image already exists on your local system

docker-compose up --build -d --- To build / rebuild the image(s) and run the containers

**For any clarifications or for demo, contact Jayesh.**


## Creating user

**For creating your own owner user in local**
1. You will get all roles for the organization using the following Curl request.
```bash
curl --location 'http://platform-api.lulasaas.local/api/roles' \
--header 'Accept: application/json' \
--header 'authorization: Bearer {{organization_user_jwt_token}}'
```
2. Create your owner user using following cUrl request
```bash
curl --location 'http://platform-api.lulasaas.local/api/users' \
--header 'Accept: application/json' \
--header 'authorization: Bearer {{organization_user_jwt_token}}' \
--header 'Content-Type: application/json' \
--data-raw '{
  "first_name": "test",
  "last_name": "test",
  "email": "<EMAIL>",
  "roles": [owner_role_uuid]
}'
```

**For Creating Technician use the following curl request**
```bash
curl --location 'http://platform-api.lulasaas.local/api/vendors' \
--header 'Authorization: Bearer {{org_user_token}}' \
--header 'Accept: application/json' \
--header 'Content-Type: application/json' \
--data-raw '{
  "email" : "<EMAIL>",
  "first_name" : "test",
  "last_name" : "test"
}'
```

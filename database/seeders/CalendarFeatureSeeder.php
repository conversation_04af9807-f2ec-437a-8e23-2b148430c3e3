<?php

namespace Database\Seeders;

use App\Enums\Feature as FeatureEnum;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Role;
use Illuminate\Database\Seeder;

class CalendarFeatureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $featurePayload = [
            'name' => FeatureEnum::CALENDAR_MANAGEMENT(),
            'permissions' => [
                'CalendarManagement.Calendar.view' => 'View Calendar',
                'CalendarManagement.CalendarView.create' => 'Calendar View Create',
                'CalendarManagement.CalendarView.update' => 'Calendar View Update',
                'CalendarManagement.CalendarView.duplicate' => 'Calendar View Duplicate',
                'CalendarManagement.CalendarView.delete' => 'Calendar View Delete',
            ],
        ];

        $feature = Feature::updateOrCreate([
            'name' => $featurePayload['name'],
        ]);

        foreach ($featurePayload['permissions'] as $permission => $label) {
            $feature->permissions()->updateOrCreate([
                'name' => $permission,
                'feature_id' => $feature->feature_id,
            ], [
                'label' => $label,
            ]);
        }

        $organizations = Organization::all();
        foreach ($organizations as $organization) {

            $organization->features()->syncWithoutDetaching([$feature->feature_id]);
            $owner = Role::with('permissions')->where('name', 'Owner')
                ->where('organization_id', $organization->organization_id)
                ->first();

            $calendarPermissions = $feature->permissions->pluck('permission_id')->toArray();
            $ownerPermissions = $owner->permissions->pluck('permission_id')->toArray();
            $newPermissions = array_merge($calendarPermissions, $ownerPermissions);

            $owner->syncPermissions($newPermissions);
        }
    }
}

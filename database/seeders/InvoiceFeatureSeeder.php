<?php

namespace Database\Seeders;

use App\Enums\Feature as FeatureEnum;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class InvoiceFeatureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $featurePayload = [
            'name' => FeatureEnum::INVOICE_MANAGEMENT(),
            'permissions' => [
                'InvoiceManagement.Invoice.view' => 'View Invoice',
                'InvoiceManagement.Invoice.create' => 'Create Invoice',
                'InvoiceManagement.Invoice.update' => 'Update Invoice',
                'InvoiceManagement.Invoice.list' => 'List Invoice',
                'InvoiceManagement.Invoice.delete' => 'Delete Invoice',
                'InvoiceManagement.Invoice.void' => 'Void Invoice',
                'InvoiceManagement.Invoice.processPayment' => 'Invoice Payment Process',
            ],
        ];

        $feature = Feature::updateOrCreate([
            'name' => $featurePayload['name'],
        ]);

        foreach ($featurePayload['permissions'] as $permission => $label) {
            $feature->permissions()->updateOrCreate([
                'name' => $permission,
                'feature_id' => $feature->feature_id,
            ], [
                'label' => $label,
            ]);
        }

        $organizations = Organization::all();

        $this->command->getOutput()->progressStart(count($organizations));
        foreach ($organizations as $organization) {

            $organization->features()->syncWithoutDetaching([$feature->feature_id]);
            $owner = Role::with('permissions')->where('name', 'Owner')
                ->where('organization_id', $organization->organization_id)
                ->first();

            $invoicePermissions = $feature->permissions->pluck('permission_id')->toArray();
            $ownerPermissions = $owner->permissions->pluck('permission_id')->toArray();
            $newPermissions = array_merge($invoicePermissions, $ownerPermissions);

            $owner->syncPermissions($newPermissions);
            $this->command->getOutput()->progressAdvance();
        }
        $this->command->getOutput()->progressFinish();

        //Clear cached permissions
        Artisan::call('cache:clear');
    }
}

<?php

namespace Database\Seeders;

use App\Models\WorkOrderHealthViolation;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class WorkOrderHealthViolationsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            [
                'work_order_health_violation_id' => 1,
                'work_order_health_violation_uuid' => Str::uuid()->getBytes(),
                'label' => 'Unclaimed Too Long',
                'slug' => 'unclaimed-too-long',
                'created_by' => null,
            ],
            [
                'work_order_health_violation_id' => 2,
                'work_order_health_violation_uuid' => Str::uuid()->getBytes(),
                'label' => 'Emergency Missed ETA',
                'slug' => 'emergency-missed-eta',
                'created_by' => null,
            ],
            [
                'work_order_health_violation_id' => 3,
                'work_order_health_violation_uuid' => Str::uuid()->getBytes(),
                'label' => 'Running Clock',
                'slug' => 'running-clock',
                'created_by' => null,
            ],
        ];

        foreach ($data as $item) {
            WorkOrderHealthViolation::updateOrCreate(
                ['slug' => $item['slug']], // Check for an existing record by slug
                $item // Fields to insert or update
            );
        }
    }
}

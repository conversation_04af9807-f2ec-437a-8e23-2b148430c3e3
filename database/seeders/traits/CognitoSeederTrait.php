<?php

declare(strict_types=1);

namespace Database\Seeders\traits;

use App\Enums\UserTypes;
use App\Models\Country;
use App\Models\Organization;
use App\Models\State;
use App\Models\Timezone;
use App\Models\User;
use Aws\CognitoIdentityProvider\CognitoIdentityProviderClient;
use Faker\Factory;
use Illuminate\Database\QueryException;

trait CognitoSeederTrait
{
    protected function getCognitoUsers($poolName): array
    {
        $region = config('services.cognito.region');
        $client = new CognitoIdentityProviderClient([
            'region' => $region,
            'version' => 'latest',
        ]);

        $listUserRequestPayload = [
            'UserPoolId' => $poolName,
        ];

        $paginationToken = null;

        $users = [];

        do {
            if ($paginationToken) {
                $listUserRequestPayload['PaginationToken'] = $paginationToken;
            }
            $result = $client->ListUsers($listUserRequestPayload);

            $paginationToken = $result->get('PaginationToken');

            $users = [...$users, ...$result->get('Users')];
        } while ($result->get('PaginationToken'));

        return $users;
    }

    protected function importCognitoUser(array $cognitoUser, Organization $organization, UserTypes $userType): User
    {
        $faker = Factory::create();

        $stateIds = State::select('state_id')->get()->pluck('state_id');
        $timezone = Timezone::where('name', config('settings.default_timezone'))->first();

        $userAttributesFromResult = json_decode(json_encode($cognitoUser['Attributes'], JSON_THROW_ON_ERROR), false, 512, JSON_THROW_ON_ERROR);

        $userAttributes = collect();

        if (! empty($cognitoUser['Username'])) {
            $userAttributes->put('user_name', $cognitoUser['Username']);
        }

        foreach ($userAttributesFromResult as $userAttributeResult) {
            $userAttributes->put($userAttributeResult->Name, $userAttributeResult->Value);

            if ($userAttributeResult->Name === 'identities') {
                $userAttributes->put($userAttributeResult->Name, json_decode($userAttributeResult->Value, false, 512, JSON_THROW_ON_ERROR));
            }
        }

        $userAttributes->put('status', $cognitoUser['UserStatus']);
        $userAttributes->put('username', $cognitoUser['Username']);
        $userAttributes->put('is_enabled', $cognitoUser['Enabled']);

        $nameSplits = explode(' ', $userAttributes->get('name'));
        $firstName = array_shift($nameSplits);
        $middleName = array_shift($nameSplits);
        $lastName = $nameSplits ? implode(' ', $nameSplits) : null;

        // Check if any user exists with this email the update that user
        // Otherwise create a new user
        $user = User::withTrashed()
            ->select('user_id', 'organization_id', 'user_type', 'first_name', 'last_name', 'deleted_at')
            ->where('email', $userAttributes->get('email'))
            ->first();

        if (! empty($user)) {
            $user->user_type = $userType->value;
            $user->first_name = $firstName;
            $user->middle_name = $middleName;
            $user->last_name = $lastName;
            $user->save();
        } else {
            // Create a new user with given type
            try {
                $user = User::updateOrCreate([
                    'email' => $userAttributes->get('email'),
                    'organization_id' => $organization->organization_id,
                    'cognito_user_id' => $userAttributes->get('sub'),
                ], [
                    'cognito_user_name' => $userAttributes->get('user_name') ?? $userAttributes->get('sub'),
                    'first_name' => $firstName,
                    'middle_name' => $middleName,
                    'last_name' => $lastName,
                    'country_id' => Country::where('alpha3_code', 'USA')->firstOrFail(['country_id'])->country_id,
                    'user_type' => $userType->value,
                    'state_id' => $stateIds->random(),
                    'street_address' => $faker->streetAddress(),
                    'apt_suite_unit' => random_int(1, 10) . 'A',
                    'city' => $faker->city(),
                    'zip_code' => random_int(11111, 99999),
                    'timezone_id' => $timezone->timezone_id,
                    'profile_pic' => null,
                ]);
            } catch (QueryException $exception) {
                $this->command->info("The user with email [{$userAttributes->get('email')}] skipped due to duplication\n");
                $this->command->warn($exception->getMessage());
                $this->command->warn(PHP_EOL);
            }
        }

        if ($userAttributes->get('is_enabled') === false) {
            $user->delete();
        }

        return $user;
    }
}

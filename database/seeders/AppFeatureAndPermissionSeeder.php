<?php

namespace Database\Seeders;

use App\Enums\Feature as FeatureEnum;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class AppFeatureAndPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $featurePayload = [
            'name' => FeatureEnum::APPLICATION_SETTINGS(),
            'permissions' => [
                'ApplicationSettings.Application.access' => 'Access to the App',
                'ApplicationSettings.Application.rescheduleTrips' => 'Reschedule Trips',
            ],
        ];

        $this->command->info('Creating ' . FeatureEnum::APPLICATION_SETTINGS() . " Feature and its permissions\n");
        $applicationSettingsFeature = Feature::updateOrCreate([
            'name' => $featurePayload['name'],
        ]);

        foreach ($featurePayload['permissions'] as $permission => $label) {
            $applicationSettingsFeature->permissions()->updateOrCreate([
                'name' => $permission,
                'feature_id' => $applicationSettingsFeature->feature_id,
            ], [
                'label' => $label,
            ]);
        }
        $this->command->info("Feature and permissions created\n");
        $this->command->info('-------------- ' . FeatureEnum::APPLICATION_SETTINGS() . ' feature permissions ' . "--------------\n");
        $this->printArrayValues($applicationSettingsFeature->permissions->pluck('name')->toArray());
        $this->command->info("----------------------------------------------------------------------\n\n\n");

        $this->command->info("Adding feature to all existing organizations\n");

        $organizations = Organization::all();

        $this->command->getOutput()->progressStart(count($organizations));
        $this->command->info("\n\n");

        foreach ($organizations as $organization) {
            $this->command->info("*****************  Seeding started for organization: {$organization->name}  *****************\n");

            $organization->features()->syncWithoutDetaching([$applicationSettingsFeature->feature_id]);
            $this->command->info("Feature added successfully\n");

            $technician = Role::with('permissions')->where('name', 'Technician')
                ->where('organization_id', $organization->organization_id)
                ->first();

            if (! empty($technician)) {
                $appPermissions = $applicationSettingsFeature->permissions->pluck('permission_id')->toArray();
                $technicianPermissions = $technician->permissions->pluck('permission_id')->toArray();

                $this->command->info("---------- Existing technician role permissions ------------\n");
                $this->printArrayValues($technician->permissions->pluck('name')->toArray());
                $this->command->info("---------------------------------------------------------\n\n\n");

                $newPermissions = array_unique(array_merge($appPermissions, $technicianPermissions));
                $technician->syncPermissions($newPermissions);

                $this->command->info("-------------- Updated permissions --------------\n");
                $technician->load('permissions:permission_id,name');
                $this->printArrayValues($technician->permissions->pluck('name')->toArray());
                $this->command->info("----------------------------------------------------------\n\n\n");
            }
            $this->command->info("***************** Seeding completed for organization: {$organization->name}  *****************\n\n\n");
            $this->command->getOutput()->progressAdvance();
            $this->command->info("\n\n");
        }
        $this->command->getOutput()->progressFinish();
        //Clear cached permissions
        Artisan::call('cache:clear');
    }

    /**
     * @param  array<string,mixed>  $arrayValues
     */
    public function printArrayValues(array $arrayValues): void
    {
        foreach ($arrayValues as $key => $value) {
            $this->command->comment("{$key} => {$value} \n");
        }
    }
}

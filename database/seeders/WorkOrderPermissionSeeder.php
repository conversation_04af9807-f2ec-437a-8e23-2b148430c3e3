<?php

namespace Database\Seeders;

use App\Enums\Feature as FeatureEnum;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Role;
use Illuminate\Database\Seeder;

class WorkOrderPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        /**
         * To add new permission under Work Order Management just add the permission here
         */
        $featurePayload = [
            'name' => FeatureEnum::WORK_ORDER_MANAGEMENT(),
            'permissions' => [
                'WorkOrderManagement.WorkOrder.delete' => 'Delete Work Order',
            ],
        ];

        // Create feature
        $feature = Feature::updateOrCreate([
            'name' => $featurePayload['name'],
        ]);

        // Create permission under specific feature
        foreach ($featurePayload['permissions'] as $permission => $label) {
            $feature->permissions()->updateOrCreate([
                'name' => $permission,
                'feature_id' => $feature->feature_id,
            ], [
                'label' => $label,
            ]);
        }

        // Apply to all organization
        $organizations = Organization::all();
        foreach ($organizations as $organization) {

            $organization->features()->syncWithoutDetaching([$feature->feature_id]);
            $owner = Role::with('permissions')->where('name', 'Owner')
                ->where('organization_id', $organization->organization_id)
                ->first();

            $workOrderPermissions = $feature->permissions->pluck('permission_id')->toArray();
            $ownerPermissions = $owner->permissions->pluck('permission_id')->toArray();
            $newPermissions = array_merge($workOrderPermissions, $ownerPermissions);

            $owner->syncPermissions($newPermissions);
        }
    }
}

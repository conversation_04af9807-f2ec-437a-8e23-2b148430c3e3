<?php

namespace Database\Seeders;

use App\Enums\Feature as FeatureEnum;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class ServiceRequestResidentAvailabilityPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $featurePayload = [
            'name' => FeatureEnum::SERVICE_REQUEST_MANAGEMENT(),
            'permissions' => [
                'ServiceRequestManagement.Resident.requestAvailability' => 'Request Resident Availability',
                'ServiceRequestManagement.Resident.addAvailability' => 'Add Resident Availability',
                'ServiceRequestManagement.Resident.updateAvailability' => 'Update Resident Availability',
            ],
        ];

        $this->command->info('Creating resident availability permissions for ' . FeatureEnum::WORK_ORDER_MANAGEMENT() . "\n");

        $feature = Feature::select(['feature_id'])
            ->with('permissions:permission_id,feature_id,name,label')
            ->where('name', $featurePayload['name'])
            ->first();

        foreach ($featurePayload['permissions'] as $permission => $label) {
            $feature->permissions()->updateOrCreate([
                'name' => $permission,
                'feature_id' => $feature->feature_id,
            ], [
                'label' => $label,
            ]);
        }

        $this->command->info("Permissions created\n");

        $this->command->info('-------------- ' . FeatureEnum::WORK_ORDER_MANAGEMENT() . ' feature permissions ' . "--------------\n");
        $this->printArrayValues($feature->permissions()->pluck('name')->toArray());

        $this->command->info("----------------------------------------------------------------------\n\n\n");

        $this->command->info("Adding permissions to all existing owners in each organization\n");

        $organizations = Organization::select(['organization_id', 'name'])
            ->with('features:organization_feature.feature_id,organization_feature.organization_id')
            ->get();

        $this->command->getOutput()->progressStart(count($organizations));
        $this->command->info("\n\n");

        foreach ($organizations as $organization) {
            $this->command->info("*****************  Seeding started for organization: {$organization->name}  *****************\n");

            $owner = Role::select('role_id')
                ->with('permissions:role_permission.permission_id,role_permission.role_id,name')
                ->where('name', 'Owner')
                ->where('organization_id', $organization->organization_id)
                ->first();

            $residentAvailibilityPermissions = $feature->permissions()->pluck('permission_id')->toArray();

            $ownerPermissions = $owner->permissions->pluck('permission_id')->toArray();

            $this->command->info("---------- Existing owner role permissions ------------\n");

            $this->printArrayValues($owner->permissions->pluck('name')->toArray());
            $this->command->info("---------------------------------------------------------\n\n\n");

            $newPermissions = array_unique(array_merge($residentAvailibilityPermissions, $ownerPermissions));
            $owner->syncPermissions($newPermissions);

            $this->command->info("-------------- Updated permissions --------------\n");
            $owner->load('permissions:permission_id,name');
            $this->printArrayValues($owner->permissions->pluck('name')->toArray());
            $this->command->info("----------------------------------------------------------\n\n\n");

            $this->command->info("***************** Seeding completed for organization: {$organization->name}  *****************\n\n\n");

            $this->command->getOutput()->progressAdvance();
            $this->command->info("\n\n");
        }

        $this->command->getOutput()->progressFinish();
        //Clear cached permissions
        Artisan::call('cache:clear');
    }

    /**
     * @param  array<string,mixed>  $arrayValues
     */
    public function printArrayValues(array $arrayValues): void
    {
        foreach ($arrayValues as $key => $value) {
            $this->command->comment("{$key} => {$value} \n");
        }
    }
}

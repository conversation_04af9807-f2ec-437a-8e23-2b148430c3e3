<?php

namespace Database\Seeders;

use App\Enums\QuoteStatus;
use App\Enums\ServiceCallStatus;
use App\Enums\WorkToPerformTypes;
use App\Models\Quote;
use Illuminate\Database\Seeder;

class UpdateServiceCallForQuoteTaskSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Updating service call for quote task');

        $quotes = Quote::with([
            'serviceCall',
            'assignedServiceCalls',
            'workOrder.tasks.latestServiceCalls',
        ])
            ->where('status', QuoteStatus::APPROVED())
            ->get();

        $this->command->getOutput()->progressStart(count($quotes));
        foreach ($quotes as $quote) {
            // check this quote is assigned with a service call
            if ($quote->assignedServiceCalls->isEmpty()) {
                $quoteCreatedServiceCall = $quote->serviceCall;

                $workOrder = $quote->workOrder;
                $allOtherTrips = $workOrder->tasks?->first()->latestServiceCalls;

                //Find next completed trip
                $nextCompletedTrip = $allOtherTrips->where('work_order_service_call_id', '>', $quoteCreatedServiceCall->work_order_service_call_id)
                    ->whereIn('status', [ServiceCallStatus::COMPLETED(), ServiceCallStatus::ACTIVE()])
                    ->first();

                if (empty($nextCompletedTrip)) {
                    $nextCompletedTrip = $allOtherTrips->where('work_order_service_call_id', '>', $quoteCreatedServiceCall->work_order_service_call_id)
                        ->first();
                }

                if (empty($nextCompletedTrip)) {
                    $this->command->newLine();
                    $this->command->info("service call not found for quote : {$quote->quote_id}, workOrder: {$workOrder->work_order_id}, work order status : {$workOrder->state->getValue()}");
                } else {
                    $nextCompletedTrip->work_to_perform = WorkToPerformTypes::QUOTE_TASK();
                    $nextCompletedTrip->quote_id = $quote->quote_id;
                    $nextCompletedTrip->save();

                    $quote->last_assigned_service_call_id = $nextCompletedTrip->work_order_service_call_id;
                    $quote->save();
                }
            }
            $this->command->getOutput()->progressAdvance();
        }
        $this->command->getOutput()->progressFinish();
    }
}

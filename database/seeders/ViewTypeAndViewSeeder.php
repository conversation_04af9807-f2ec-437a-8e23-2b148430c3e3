<?php

namespace Database\Seeders;

use App\Enums\ViewTypes;
use App\Models\View;
use App\Models\ViewType;
use Illuminate\Database\Seeder;

class ViewTypeAndViewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $viewTypes = [
            [
                'label' => 'Work Orders',
                'slug' => ViewTypes::WORK_ORDERS,
                'default_views' => [
                    [
                        'scope' => 'global',
                        'name' => 'Default View',
                        'payload' => config('listview.work_order_default_payload'),
                    ],
                    //TODO: According to this ticket https://app.clickup.com/t/85ztm2jn9
                    //Currently it is not in scope, So we will add it later.
                    // [
                    //     'scope' => 'global',
                    //     'name' => 'Assigned to me',
                    //     'payload' => '{
                    //         "filters": {
                    //             "applied": {
                    //                 "group_op": null,
                    //                 "fl_group": []
                    //             },
                    //             "operators": {
                    //                 "is": {
                    //                     "value": "is",
                    //                     "label": "Is"
                    //                 },
                    //                 "is_not": {
                    //                     "value": "is_not",
                    //                     "label": "Is not"
                    //                 },
                    //                 "is_between": {
                    //                     "value": "is_between",
                    //                     "label": "Is between"
                    //                 },
                    //                 "is_after": {
                    //                     "value": "is_after",
                    //                     "label": "Is after"
                    //                 },
                    //                 "is_before": {
                    //                     "value": "is_before",
                    //                     "label": "Is before"
                    //                 }
                    //             },
                    //             "fields": [
                    //                 {
                    //                     "value": "status",
                    //                     "label": "Status",
                    //                     "ops": [
                    //                         "is",
                    //                         "is_not"
                    //                     ],
                    //                     "values_type": {
                    //                         "is": "multi-select",
                    //                         "is_not": "multi-select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "label": "Priority",
                    //                     "value": "priority",
                    //                     "ops": [
                    //                         "is",
                    //                         "is_not"
                    //                     ],
                    //                     "values_type": {
                    //                         "is": "multi-select",
                    //                         "is_not": "multi-select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "value": "assignee",
                    //                     "label": "Assignee",
                    //                     "ops": ["is"],
                    //                     "values_type": {
                    //                         "is": "multi-select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "value": "technician",
                    //                     "label": "Technician",
                    //                     "ops": ["is"],
                    //                     "values_type": {
                    //                         "is": "multi-select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "value": "category",
                    //                     "label": "Category",
                    //                     "ops": ["is"],
                    //                     "values_type": {
                    //                         "is": "multi-select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "label": "Due Date",
                    //                     "value": "due_date",
                    //                     "ops": [
                    //                         "is",
                    //                         "is_not"
                    //                     ],
                    //                     "values_type": {
                    //                         "is": "select",
                    //                         "is_not": "select",
                    //                         "is_between": "date-range-picker",
                    //                         "is_after": "select",
                    //                         "is_before": "select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "label": "Created Date",
                    //                     "value": "created_date",
                    //                     "ops": [
                    //                         "is",
                    //                         "is_not"
                    //                     ],
                    //                     "values_type": {
                    //                         "is": "select",
                    //                         "is_not": "select",
                    //                         "is_between": "date-range-picker",
                    //                         "is_after": "select",
                    //                         "is_before": "select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "label": "Scheduled Date",
                    //                     "value": "scheduled_date",
                    //                     "ops": [
                    //                         "is",
                    //                         "is_not"
                    //                     ],
                    //                     "values_type": {
                    //                         "is": "select",
                    //                         "is_not": "select",
                    //                         "is_between": "date-range-picker",
                    //                         "is_after": "select",
                    //                         "is_before": "select"
                    //                     }
                    //                 }
                    //             ]
                    //         },
                    //         "grouping": {
                    //             "g_by": {
                    //                 "label": "None",
                    //                 "value": "none"
                    //             },
                    //             "default": "none",
                    //             "g_options": [
                    //                 {
                    //                     "label": "None",
                    //                     "value": "none"
                    //                 },
                    //                 {
                    //                     "label": "Status",
                    //                     "value": "status"
                    //                 },
                    //                 {
                    //                     "label": "Priority",
                    //                     "value": "priority"
                    //                 },
                    //                 {
                    //                     "label": "Category",
                    //                     "value": "category"
                    //                 },
                    //                 {
                    //                     "label": "Technician",
                    //                     "value": "technician"
                    //                 },
                    //                 {
                    //                     "label": "Assignee",
                    //                     "value": "assignee"
                    //                 }
                    //             ]
                    //         },
                    //         "columns": [
                    //             {
                    //                 "label": "Category",
                    //                 "value": "category",
                    //                 "isEditable": false,
                    //                 "selected": true,
                    //                 "isStaticColumn": true,
                    //                 "sub_fields": [
                    //                     {
                    //                         "label": "WO ID",
                    //                         "value": "wo_id",
                    //                         "isEditable": true,
                    //                         "selected": false
                    //                     },
                    //                     {
                    //                         "label": "Tags",
                    //                         "value": "tags",
                    //                         "isEditable": true,
                    //                         "selected": false
                    //                     },
                    //                     {
                    //                         "label": "Property Address",
                    //                         "value": "property_address",
                    //                         "isEditable": true,
                    //                         "selected": false
                    //                     }
                    //                 ]
                    //             },
                    //             {
                    //                 "label": "Status",
                    //                 "value": "status",
                    //                 "isEditable": false,
                    //                 "isStaticColumn": true,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Technician",
                    //                 "value": "technician",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Assignee",
                    //                 "value": "assignee",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Priority",
                    //                 "value": "priority",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Due Date",
                    //                 "value": "due_date",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Scheduled Date",
                    //                 "value": "scheduled_date",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Created Date",
                    //                 "value": "created_date",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             }
                    //         ]
                    //     }',
                    // ],
                    // [
                    //     'scope' => 'global',
                    //     'name' => 'Bookmarks',
                    //     'payload' => '{
                    //         "filters": {
                    //             "applied": {
                    //                 "group_op": null,
                    //                 "fl_group": []
                    //             },
                    //             "operators": {
                    //                 "is": {
                    //                     "value": "is",
                    //                     "label": "Is"
                    //                 },
                    //                 "is_not": {
                    //                     "value": "is_not",
                    //                     "label": "Is not"
                    //                 },
                    //                 "is_between": {
                    //                     "value": "is_between",
                    //                     "label": "Is between"
                    //                 },
                    //                 "is_after": {
                    //                     "value": "is_after",
                    //                     "label": "Is after"
                    //                 },
                    //                 "is_before": {
                    //                     "value": "is_before",
                    //                     "label": "Is before"
                    //                 }
                    //             },
                    //             "fields": [
                    //                 {
                    //                     "value": "status",
                    //                     "label": "Status",
                    //                     "ops": [
                    //                         "is",
                    //                         "is_not"
                    //                     ],
                    //                     "values_type": {
                    //                         "is": "multi-select",
                    //                         "is_not": "multi-select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "label": "Priority",
                    //                     "value": "priority",
                    //                     "ops": [
                    //                         "is",
                    //                         "is_not"
                    //                     ],
                    //                     "values_type": {
                    //                         "is": "multi-select",
                    //                         "is_not": "multi-select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "value": "assignee",
                    //                     "label": "Assignee",
                    //                     "ops": ["is"],
                    //                     "values_type": {
                    //                         "is": "multi-select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "value": "technician",
                    //                     "label": "Technician",
                    //                     "ops": ["is"],
                    //                     "values_type": {
                    //                         "is": "multi-select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "value": "category",
                    //                     "label": "Category",
                    //                     "ops": ["is"],
                    //                     "values_type": {
                    //                         "is": "multi-select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "label": "Due Date",
                    //                     "value": "due_date",
                    //                     "ops": [
                    //                         "is",
                    //                         "is_not"
                    //                     ],
                    //                     "values_type": {
                    //                         "is": "select",
                    //                         "is_not": "select",
                    //                         "is_between": "date-range-picker",
                    //                         "is_after": "select",
                    //                         "is_before": "select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "label": "Created Date",
                    //                     "value": "created_date",
                    //                     "ops": [
                    //                         "is",
                    //                         "is_not"
                    //                     ],
                    //                     "values_type": {
                    //                         "is": "select",
                    //                         "is_not": "select",
                    //                         "is_between": "date-range-picker",
                    //                         "is_after": "select",
                    //                         "is_before": "select"
                    //                     }
                    //                 },
                    //                 {
                    //                     "label": "Scheduled Date",
                    //                     "value": "scheduled_date",
                    //                     "ops": [
                    //                         "is",
                    //                         "is_not"
                    //                     ],
                    //                     "values_type": {
                    //                         "is": "select",
                    //                         "is_not": "select",
                    //                         "is_between": "date-range-picker",
                    //                         "is_after": "select",
                    //                         "is_before": "select"
                    //                     }
                    //                 }
                    //             ]
                    //         },
                    //         "grouping": {
                    //             "g_by": {
                    //                 "label": "None",
                    //                 "value": "none"
                    //             },
                    //             "default": "none",
                    //             "g_options": [
                    //                 {
                    //                     "label": "None",
                    //                     "value": "none"
                    //                 },
                    //                 {
                    //                     "label": "Status",
                    //                     "value": "status"
                    //                 },
                    //                 {
                    //                     "label": "Priority",
                    //                     "value": "priority"
                    //                 },
                    //                 {
                    //                     "label": "Category",
                    //                     "value": "category"
                    //                 },
                    //                 {
                    //                     "label": "Technician",
                    //                     "value": "technician"
                    //                 },
                    //                 {
                    //                     "label": "Assignee",
                    //                     "value": "assignee"
                    //                 }
                    //             ]
                    //         },
                    //         "columns": [
                    //             {
                    //                 "label": "Category",
                    //                 "value": "category",
                    //                 "isEditable": false,
                    //                 "selected": true,
                    //                 "isStaticColumn": true,
                    //                 "sub_fields": [
                    //                     {
                    //                         "label": "WO ID",
                    //                         "value": "wo_id",
                    //                         "isEditable": true,
                    //                         "selected": false
                    //                     },
                    //                     {
                    //                         "label": "Tags",
                    //                         "value": "tags",
                    //                         "isEditable": true,
                    //                         "selected": false
                    //                     },
                    //                     {
                    //                         "label": "Property Address",
                    //                         "value": "property_address",
                    //                         "isEditable": true,
                    //                         "selected": false
                    //                     }
                    //                 ]
                    //             },
                    //             {
                    //                 "label": "Status",
                    //                 "value": "status",
                    //                 "isEditable": false,
                    //                 "isStaticColumn": true,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Technician",
                    //                 "value": "technician",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Assignee",
                    //                 "value": "assignee",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Priority",
                    //                 "value": "priority",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Due Date",
                    //                 "value": "due_date",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Scheduled Date",
                    //                 "value": "scheduled_date",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             },
                    //             {
                    //                 "label": "Created Date",
                    //                 "value": "created_date",
                    //                 "isEditable": true,
                    //                 "isStaticColumn": false,
                    //                 "selected": true
                    //             }
                    //         ]
                    //     }',
                    // ],
                ],
            ],
            [
                'label' => 'User List',
                'slug' => ViewTypes::USERS,
                'default_views' => [
                    [
                        'scope' => 'global',
                        'name' => 'Default View',
                        'payload' => config('listview.users_list_default_payload'),
                    ],
                ],
            ],
            [
                'label' => 'Calendar',
                'slug' => ViewTypes::CALENDAR,
                'default_views' => [
                    [
                        'scope' => 'global',
                        'name' => 'Default View',
                        'payload' => config('listview.calendar_list_default_payload'),
                    ],
                ],
            ],
        ];

        foreach ($viewTypes as $viewType) {
            // Create view type
            $createdViewType = ViewType::updateOrCreate([
                'slug' => $viewType['slug'],
            ], [
                'label' => $viewType['label'],
            ]);

            // Create default views under this view type.
            $defaultViews = $viewType['default_views'];

            foreach ($defaultViews as $defaultView) {
                View::updateOrCreate([
                    'name' => $defaultView['name'],
                    'scope' => $defaultView['scope'],
                    'view_type_id' => $createdViewType->view_type_id,
                ], [
                    // 'payload' => json_decode($defaultView['payload']),
                    'payload' => $defaultView['payload'],
                ]);
            }
        }
    }
}

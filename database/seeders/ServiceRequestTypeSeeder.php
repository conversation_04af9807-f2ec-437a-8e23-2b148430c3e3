<?php

namespace Database\Seeders;

use App\Enums\ServiceRequestTypeTypes;
use App\Models\ServiceRequestType;
use Illuminate\Database\Seeder;

class ServiceRequestTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $serviceRequestTypes =
        [
            [
                'label' => 'Maintenance request',
                'slug' => ServiceRequestTypeTypes::MAINTENANCE_REQUEST(),
            ],
        ];

        foreach ($serviceRequestTypes as $serviceRequestType) {
            ServiceRequestType::updateOrCreate($serviceRequestType);
        }
    }
}

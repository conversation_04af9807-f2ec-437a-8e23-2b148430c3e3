<?php

namespace Database\Seeders;

use App\Models\Technician;
use App\Models\TechnicianSkill;
use App\Models\WorkOrderTask;
use Exception;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Log;

class TechnicianSkillSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Fetch all technicians
        $technicians = Technician::select('technician_id', 'organization_id')->get();

        $technicians->each(function (Technician $technician) {
            $workOrderTask = WorkOrderTask::with('problemDiagnosis')
                ->select('work_order_task_id', 'work_order_task_uuid', 'problem_diagnosis_id')
                ->where('organization_id', $technician->organization_id)
                ->get();
            $problemDiagnosisIds = $workOrderTask->pluck('problemDiagnosis.problem_diagnosis_id')
                ->unique();

            //Create random number of skills
            $skillCount = rand(10, 50);
            for ($i = 0; $i < $skillCount; $i++) {
                try {
                    TechnicianSkill::updateOrCreate([
                        'technician_id' => $technician->technician_id,
                        'organization_id' => $technician->organization_id,
                        'problem_diagnosis_id' => $problemDiagnosisIds->random(),
                    ]);
                } catch (Exception $e) {
                    Log::warning('Potential duplicate due to random select', [$e->getMessage()]);
                }
            }
        });
    }
}

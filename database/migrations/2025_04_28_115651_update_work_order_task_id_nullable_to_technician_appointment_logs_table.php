<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('technician_appointment_logs', function () {
            DB::statement('ALTER TABLE technician_appointment_logs MODIFY COLUMN work_order_task_id bigint unsigned NULL');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('technician_appointment_logs', function () {
            DB::statement('ALTER TABLE technician_appointment_logs MODIFY COLUMN work_order_task_id bigint unsigned NOT NULL');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('technician_appointments', function (Blueprint $table) {
            $table->after('scheduled_end_time', function (Blueprint $table) {
                $table->dateTime('work_timer_paused_at')->nullable();
                $table->dateTime('work_timer_resumed_at')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('technician_appointments', function (Blueprint $table) {
            $table->dropColumn(
                'work_timer_paused_at',
                'work_timer_resumed_at'
            );
        });
    }
};

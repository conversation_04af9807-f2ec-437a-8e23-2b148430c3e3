<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->after('trip_end_with_reason', function (Blueprint $table) {
                $table->foreignId('last_modified_user')->nullable()
                    ->constrained('users', 'user_id');
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dropForeign(['last_modified_user']);
            $table->dropColumn(['last_modified_user']);
        });
    }
};

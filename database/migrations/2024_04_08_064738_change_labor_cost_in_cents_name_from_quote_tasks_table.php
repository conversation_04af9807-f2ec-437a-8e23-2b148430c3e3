<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quote_tasks', function (Blueprint $table) {
            if (Schema::hasColumn('quote_tasks', 'cost_in_cents')) {
                DB::statement('ALTER TABLE quote_tasks CHANGE cost_in_cents total_cost_in_cents bigint NULL');
            }

            if (Schema::hasColumn('quote_tasks', 'labor_cost_in_cents')) {
                DB::statement('ALTER TABLE quote_tasks CHANGE labor_cost_in_cents cost_in_cents bigint NULL');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quote_tasks', function (Blueprint $table) {
            if (Schema::hasColumn('quote_tasks', 'cost_in_cents')) {
                DB::statement('ALTER TABLE quote_tasks CHAN<PERSON> cost_in_cents labor_cost_in_cents bigint NULL');
            }

            if (Schema::hasColumn('quote_tasks', 'total_cost_in_cents')) {
                DB::statement('ALTER TABLE quote_tasks CHANGE total_cost_in_cents cost_in_cents bigint NULL');
            }
        });
    }
};

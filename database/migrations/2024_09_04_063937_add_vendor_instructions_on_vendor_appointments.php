<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendor_appointments', function (Blueprint $table) {
            $table->text('vendor_instructions')->nullable()->after('vendor_id');

            $table->dropForeign('vendor_appointments_vendor_id_foreign');
            $table->bigInteger('vendor_id')->nullable()->unsigned()->change();
            $table->foreign('vendor_id', 'vendor_appointments_vendor_id_foreign')->references('vendor_id')->on('vendors');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendor_appointments', function (Blueprint $table) {
            $table->dropColumn([
                'vendor_instructions',
            ]);
        });
    }
};

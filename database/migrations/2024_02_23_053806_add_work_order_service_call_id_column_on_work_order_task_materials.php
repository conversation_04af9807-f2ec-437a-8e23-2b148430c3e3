<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_task_materials', function (Blueprint $table) {
            $table->foreignId('work_order_service_call_id')->nullable()->after('quantity_type')->constrained('work_order_service_calls', 'work_order_service_call_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_task_materials', function (Blueprint $table) {

            Schema::table('work_order_task_materials', function (Blueprint $table) {
                $table->dropForeign(['work_order_service_call_id']);
                $table->dropColumn('work_order_service_call_id');
            });

        });
    }
};

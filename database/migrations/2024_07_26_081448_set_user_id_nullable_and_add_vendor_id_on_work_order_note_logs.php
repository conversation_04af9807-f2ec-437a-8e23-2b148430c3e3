<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_note_logs', function (Blueprint $table) {
            $table->foreignId('vendor_id')->after('user_id')->nullable()->constrained('vendors', 'vendor_id');

            $table->dropForeign('work_order_note_logs_user_id_foreign');
            $table->bigInteger('user_id')->nullable()->unsigned()->change();
            $table->foreign('user_id', 'work_order_note_logs_user_id_foreign')->references('user_id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_note_logs', function (Blueprint $table) {
            $table->dropConstrainedForeignId('vendor_id');
        });
    }
};

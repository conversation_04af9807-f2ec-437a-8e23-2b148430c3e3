<?php

use App\Models\TechnicianAppointment;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //Add columns to work order service calls
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dateTime('state_updated_at')->useCurrent()->after('state');
            $table->dateTime('en_route_at')->nullable()->after('last_modified_at');
            $table->after('en_route_at', function (Blueprint $table) {
                $table->dateTime('en_route_timer_paused_at')->nullable();
                $table->dateTime('en_route_timer_resumed_at')->nullable();
                $table->integer('drive_time_in_sec')->nullable();
                $table->integer('adjusted_drive_time_in_sec')->nullable();
                $table->dateTime('work_started_at')->nullable();
                $table->dateTime('work_timer_paused_at')->nullable();
                $table->dateTime('work_timer_resumed_at')->nullable();
                $table->dateTime('work_completed_at')->nullable();
                $table->integer('labor_time_in_sec')->nullable();
                $table->integer('adjusted_labor_time_in_sec')->nullable();
                $table->text('timer_adjusted_reason')->nullable();
            });
        });

        // Migrate column values from the technician appointments to work order service calls
        $technicianAppointments = TechnicianAppointment::with('serviceCall')
            ->whereHas('serviceCall')
            ->select('technician_appointment_id', 'en_route_timer_paused_at', 'en_route_timer_resumed_at',
                'work_timer_paused_at', 'work_timer_resumed_at', 'enroute_at', 'travel_time_in_sec',
                'actual_start_time', 'actual_end_time', 'actual_elapse_time_in_sec', 'adjusted_elapse_time_in_sec'
            )
            ->get();

        if ($technicianAppointments->count() > 0) {
            foreach ($technicianAppointments as $technicianAppointment) {
                $technicianAppointment->serviceCall->update([
                    'en_route_at' => $technicianAppointment->enroute_at,
                    'en_route_timer_paused_at' => $technicianAppointment->en_route_timer_paused_at,
                    'en_route_timer_resumed_at' => $technicianAppointment->en_route_timer_resumed_at,
                    'drive_time_in_sec' => $technicianAppointment->travel_time_in_sec,
                    'adjusted_drive_time_in_sec' => $technicianAppointment->adjusted_travel_time_in_sec,
                    'work_started_at' => $technicianAppointment->actual_start_time,
                    'work_timer_paused_at' => $technicianAppointment->work_timer_paused_at,
                    'work_timer_resumed_at' => $technicianAppointment->work_timer_resumed_at,
                    'work_completed_at' => $technicianAppointment->actual_end_time,
                    'labor_time_in_sec' => $technicianAppointment->actual_elapse_time_in_sec,
                    'adjusted_labor_time_in_sec' => $technicianAppointment->adjusted_elapse_time_in_sec,
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dropColumn([
                'state_updated_at',
                'en_route_timer_paused_at',
                'en_route_timer_resumed_at',
                'en_route_at',
                'en_route_timer_paused_at',
                'en_route_timer_resumed_at',
                'drive_time_in_sec',
                'adjusted_drive_time_in_sec',
                'work_started_at',
                'work_timer_paused_at',
                'work_timer_resumed_at',
                'work_completed_at',
                'labor_time_in_sec',
                'adjusted_labor_time_in_sec',
            ]);
        });
    }
};

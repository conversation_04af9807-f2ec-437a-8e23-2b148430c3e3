<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('technician_appointment_logs', function (Blueprint $table) {
            $table->unsignedBigInteger('work_order_task_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('technician_appointment_logs', function (Blueprint $table) {
            $table->unsignedBigInteger('work_order_task_id')->nullable(false)->change();
        });
    }
};

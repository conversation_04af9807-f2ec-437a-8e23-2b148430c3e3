<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (Schema::hasColumn('work_order_activity_logs', 'work_order_task_id')) {
            Schema::table('work_order_activity_logs', function (Blueprint $table) {
                $table->dropForeign('work_order_activity_logs_work_order_task_id_foreign');
                $table->bigInteger('work_order_task_id')->nullable()->unsigned()->change();
                //Remove the following line if disable foreign key
                $table->foreign('work_order_task_id')->references('work_order_task_id')->on('work_order_tasks');
            });
        }
    }

    public function down(): void
    {
        //Not required because we can't make a nullable column to non nullable
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_service_call_logs', function (Blueprint $table) {
            DB::statement("ALTER TABLE work_order_service_call_logs MODIFY COLUMN `action` enum('scheduled','re-scheduled','completed','en-route','working','paused','updated','schedule-in-pending','schedule-in-progress','canceled','claim-pending') NULL;");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_service_call_logs', function (Blueprint $table) {
            DB::statement("ALTER TABLE work_order_service_call_logs MODIFY COLUMN `action` enum('scheduled','re-scheduled','completed','en-route','working','paused','updated','schedule-in-pending','schedule-in-progress','canceled') NULL;");
        });
    }
};

<?php

use App\Enums\CostTypes;
use App\Enums\InvoiceSubsidiaryTypes;
use App\Enums\MarkUpFeeTypes;
use App\Enums\MaterialQuantityType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_line_item_subsidiaries', function (Blueprint $table) {
            $table->id('invoice_line_item_subsidiary_id');
            $table->realBinary('invoice_line_item_subsidiary_uuid')->unique('invoice_line_item_subsidiary_uuid_unique');
            $table->foreignId('invoice_line_item_id')->constrained('invoice_line_items', 'invoice_line_item_id');
            $table->foreignId('work_order_task_material_id')->nullable()->constrained('work_order_task_materials', 'work_order_task_material_id', 'line_item_subsidiaries_work_order_task_material_id_foreign');
            $table->foreignId('quote_task_material_id')->nullable()->constrained('quote_task_materials', 'quote_task_material_id');
            $table->foreignId('quote_task_id')->nullable()->constrained('quote_tasks', 'quote_task_id');
            $table->enum('subsidiary_type', InvoiceSubsidiaryTypes::values());
            $table->text('description')->nullable();
            $table->bigInteger('hourly_rate_in_cents')->nullable();
            $table->integer('duration_in_seconds')->nullable();
            $table->integer('quantity')->nullable();
            $table->enum('quantity_type', MaterialQuantityType::values())->nullable();
            $table->enum('cost_type', CostTypes::values())->nullable();
            $table->enum('markup_fee_type', MarkUpFeeTypes::values())->nullable();
            $table->decimal('markup_fee_type_value', 10, 2)->nullable();
            $table->bigInteger('markup_fee_in_cents')->default(0);
            $table->bigInteger('cost_in_cents')->default(0);
            $table->bigInteger('total_cost_in_cents')->default(0);
            $table->timestamps();
            $table->softDeletesDatetime();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_line_item_subsidiaries');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quote_tasks', function (Blueprint $table) {
            $table->integer('quote_task_number')
                ->nullable()
                ->after('quote_task_uuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quote_tasks', function (Blueprint $table) {
            $table->dropColumn(['quote_task_number']);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('problem_sub_categories', function (Blueprint $table) {
            $table->smallIncrements('problem_sub_category_id');
            $table->realBinary('problem_sub_category_uuid')->unique();
            $table->unsignedSmallInteger('problem_category_id');
            $table->foreign('problem_category_id')->references('problem_category_id')->on('problem_categories');
            $table->string('label', 100);
            $table->string('slug', 500);
            $table->datetimes();
            $table->softDeletesDatetime();

            $table->unique(['problem_category_id', 'slug']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('problem_sub_categories');
    }
};

<?php

use App\Enums\Trip;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->string('state')->after('scheduled_end_time');
            $table->integer('work_order_service_call_number')->nullable()->after('work_order_service_call_uuid');
            $table->after('status', function (Blueprint $table) {
                $table->enum('trip_end_with', [Trip::ALL_COMPLETED(), Trip::PARTIALLY_COMPLETED(), Trip::NO_WORK()])->nullable();
                $table->enum('trip_end_with_type', [
                    Trip::SUBMIT_QUOTE(), Trip::FINISH_WITH_ANOTHER_DAY(), Trip::GET_PARTS(), Trip::RESIDENT_DID_NOT_SHOW_UP(), Trip::OTHER(),
                ])
                    ->nullable();
                $table->text('trip_end_with_reason')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dropColumn([
                'state', 'trip_end_with', 'trip_end_with_type', 'trip_end_with_reason', 'work_order_service_call_number',
            ]);
        });
    }
};

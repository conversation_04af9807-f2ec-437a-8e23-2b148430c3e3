<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('work_order_tasks', 'problem_diagnosis_id')) {
            Schema::table('work_order_tasks', function (Blueprint $table) {
                DB::statement('ALTER TABLE work_order_tasks MODIFY COLUMN problem_diagnosis_id MEDIUMINT UNSIGNED NULL');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //Not required because we can't make a nullable column to non nullable
    }
};

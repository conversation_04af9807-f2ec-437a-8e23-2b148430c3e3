<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_issues', function (Blueprint $table) {
            // Rename service_note to service_notes
            $table->renameColumn('service_note', 'service_notes');
        });

        Schema::table('work_order_issues', function (Blueprint $table) {
            $table->text('service_notes')->nullable()->change();
            $table->after('service_notes', function (Blueprint $table) {
                $table->boolean('issue_caused_by_resident')->nullable();
                $table->text('issue_caused_details')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_issues', function (Blueprint $table) {
            // Revert the column type change
            $table->string('service_notes')->change();
            $table->dropColumn(['issue_caused_by_resident', 'issue_caused_details']);
        });

        Schema::table('work_order_issues', function (Blueprint $table) {
            // Rename it back
            $table->renameColumn('service_notes', 'service_note');
        });
    }
};

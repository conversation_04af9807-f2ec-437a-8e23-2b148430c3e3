<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('work_orders', function (Blueprint $table) {
            $table->string('work_order_reference_id', 255)->nullable()->after('work_order_source_id')->index();
            $table->string('work_order_reference_url', 500)->nullable()->after('work_order_reference_id');
            $table->string('work_order_reference_number')->nullable()->after('work_order_reference_url');
        });
    }

    public function down(): void
    {
        Schema::table('work_orders', function (Blueprint $table) {
            $table->dropColumn('work_order_reference_id', 'work_order_reference_url', 'work_order_reference_number');
        });
    }
};

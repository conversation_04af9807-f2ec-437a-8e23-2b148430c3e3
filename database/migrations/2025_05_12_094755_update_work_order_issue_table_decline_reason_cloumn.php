<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_issues', function (Blueprint $table) {
            $table->text('decline_reason')->nullable()->change();
            $table->foreignId('declined_user_id')->after('decline_reason')->nullable()->constrained('users', 'user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_issues', function (Blueprint $table) {
            $table->string('decline_reason')->nullable()->change();
            $table->dropForeign(['declined_user_id']);
            $table->dropColumn('declined_user_id');
        });
    }
};

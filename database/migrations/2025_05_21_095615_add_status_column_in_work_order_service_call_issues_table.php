<?php

use App\Enums\ServiceCallIssueStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_service_call_issues', function (Blueprint $table) {
            $table->enum('status', ServiceCallIssueStatus::values())->after('work_order_issue_id')->default(ServiceCallIssueStatus::ASSIGNED());
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_service_call_issues', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};

<?php

use App\Enums\Boolean;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('organizations', function (Blueprint $table) {

            $table->enum('is_appfolio_enabled', array_column(Boolean::cases(), 'value'))
                ->default(Boolean::NO())
                ->after('webhook_api_url')
                ->index();

            $table->string('appfolio_client_id', 500)
                ->after('is_appfolio_enabled')
                ->nullable();

            $table->string('appfolio_client_secret', 500)
                ->after('appfolio_client_id')
                ->nullable();

            $table->string('appfolio_vendor_id', 100)
                ->after('appfolio_client_secret')
                ->nullable();

            $table->string('appfolio_customer_id')
                ->after('appfolio_vendor_id')
                ->nullable();

            $table->dateTime('appfolio_integrated_at')
                ->after('appfolio_customer_id')
                ->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('organizations', function (Blueprint $table) {
            $table->dropColumn([
                'is_appfolio_enabled', 'appfolio_client_id', 'appfolio_client_secret',
                'appfolio_vendor_id', 'appfolio_integrated_at', 'appfolio_customer_id',
            ]);
        });
    }
};

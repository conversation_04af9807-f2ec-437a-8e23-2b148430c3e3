<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dateTime('last_modified_at')
                ->nullable()
                ->after('last_modified_user');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dropColumn(
                'last_modified_at'
            );
        });
    }
};

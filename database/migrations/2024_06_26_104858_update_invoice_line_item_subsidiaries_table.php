<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_line_item_subsidiaries', function (Blueprint $table) {
            DB::statement("ALTER TABLE invoice_line_item_subsidiaries MODIFY COLUMN subsidiary_type enum('hourly-labor','fee','labor','material','notes','drive-rate') NOT NULL;");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_line_item_subsidiaries', function (Blueprint $table) {
            DB::statement("ALTER TABLE invoice_line_item_subsidiaries MODIFY COLUMN subsidiary_type enum('hourly-labor','fee','labor','material','notes') NOT NULL;");
        });
    }
};

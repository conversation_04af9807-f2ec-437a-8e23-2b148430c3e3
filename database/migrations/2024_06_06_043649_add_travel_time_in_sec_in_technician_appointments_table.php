<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('technician_appointments', function (Blueprint $table) {
            $table->after('enroute_at', function (Blueprint $table) {
                $table->integer('travel_time_in_sec')->nullable();
                $table->integer('adjusted_travel_time_in_sec')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('technician_appointments', function (Blueprint $table) {
            $table->dropColumn([
                'travel_time_in_sec',
                'adjusted_travel_time_in_sec',
            ]);
        });
    }
};

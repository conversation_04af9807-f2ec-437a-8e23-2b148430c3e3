<?php

use App\Enums\MarkUpFeeTypes;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quote_task_materials', function (Blueprint $table) {
            $table->enum('markup_fee_type', MarkUpFeeTypes::values())->nullable()->after('label');
            $table->decimal('markup_fee_type_value', 10, 2)->nullable()->after('markup_fee_type');
            $table->bigInteger('markup_fee_in_cents')->nullable()->after('markup_fee_type_value');
            $table->bigInteger('material_cost_in_cents')->nullable()->after('markup_fee_in_cents');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quote_task_materials', function (Blueprint $table) {
            $table->dropColumn([
                'markup_fee_type',
                'markup_fee_type_value',
                'markup_fee_in_cents',
                'material_cost_in_cents',
            ]);
        });
    }
};

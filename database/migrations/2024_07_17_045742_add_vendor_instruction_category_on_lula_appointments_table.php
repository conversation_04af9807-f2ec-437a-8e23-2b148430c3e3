<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lula_appointments', function (Blueprint $table) {
            $table->text('vendor_instructions')->nullable()->after('organization_id');
            $table->string('work_order_reference_number')->after('vendor_instructions');
            $table->string('service_category_label')->nullable()->after('work_order_reference_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lula_appointments', function (Blueprint $table) {
            $table->dropColumn([
                'vendor_instructions',
                'work_order_reference_number',
                'service_category_label',
            ]);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_health_trackers', function (Blueprint $table) {
            $table->integer('weight')->nullable()->after('classification'); // Add the column
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_health_trackers', function (Blueprint $table) {
            $table->dropColumn('weight');
        });
    }
};

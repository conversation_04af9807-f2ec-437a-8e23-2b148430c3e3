<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dateTime('scheduled_start_time')->nullable()->change();
            $table->dateTime('scheduled_end_time')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dateTime('scheduled_start_time')->nullable(false)->change();
            $table->dateTime('scheduled_end_time')->nullable(false)->change();
        });
    }
};

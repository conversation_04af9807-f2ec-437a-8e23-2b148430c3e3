<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (Schema::hasTable('organizations')) {
            Schema::table('organizations', function (Blueprint $table) {
                $table->string('user_pool_domain', 500)->nullable()->after('user_pool_api_client_secret');
            });
        }
    }

    public function down(): void
    {
        if (Schema::hasColumns('organizations', ['user_pool_domain', 'microsoft_ad_provider_name'])) {
            Schema::table('organizations', function (Blueprint $table) {
                $table->dropColumn('user_pool_domain');
            });
        }
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('work_order_media', function (Blueprint $table) {
            if (! Schema::hasColumns('work_order_media', ['work_order_task_id, quote_task_id'])) {
                $table->foreignId('work_order_task_id')->nullable()->constrained('work_order_tasks', 'work_order_task_id');
                $table->foreignId('quote_task_id')->nullable()->constrained('quote_tasks', 'quote_task_id');
            }
        });
    }

    public function down(): void
    {
        if (Schema::hasColumns('work_order_media', ['work_order_task_id', 'quote_task_id'])) {
            Schema::table('work_order_media', function (Blueprint $table) {
                $table->dropForeign('work_order_media_work_order_task_id_foreign');
                $table->dropForeign('work_order_media_quote_task_id_foreign');
                $table->dropColumn(['work_order_task_id', 'quote_task_id']);
            });
        }
    }
};

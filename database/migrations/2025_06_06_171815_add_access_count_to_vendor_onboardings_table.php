<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendor_onboardings', function (Blueprint $table) {
            $table->integer('access_count')
                ->nullable()
                ->default(0)
                ->after('organization_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendor_onboardings', function (Blueprint $table) {
            $table->dropColumn('access_count');
        });
    }
};

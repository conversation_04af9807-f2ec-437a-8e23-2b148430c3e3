<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('public_api_work_order_webhook_events', function (Blueprint $table) {
            $table->id('public_api_work_order_webhook_event_id');
            $table->realBinary('public_api_work_order_webhook_event_uuid')->unique('public_api_wo_wh_event_uuid');
            $table->foreignId('work_order_id')->constrained('work_orders', 'work_order_id');
            $table->foreignId('work_order_source_id')->nullable()->constrained('work_order_sources', 'work_order_source_id', 'fk_work_order_source_id');
            $table->jsonb('payload')->nullable();
            $table->integer('version')->default(1);
            $table->enum('status', ['pending', 'delivered', 'retry_needed', 'expired', 'skipped'])->default('pending');
            $table->dateTime('next_attempt_at')->nullable();
            $table->smallInteger('attempt')->default(0);
            $table->jsonb('response')->nullable();
            $table->unsignedInteger('response_status_code')->nullable();
            $table->date('notified_at')->nullable();
            $table->dateTimes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('public_api_work_order_webhook_events');
    }
};

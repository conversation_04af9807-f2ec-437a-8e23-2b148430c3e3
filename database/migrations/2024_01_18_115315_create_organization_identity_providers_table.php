<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('organization_identity_providers', function (Blueprint $table) {
            $table->id('organization_identity_provider_id');
            $table->realBinary('organization_identity_provider_uuid');
            $table->foreignId('organization_id')->constrained('organizations', 'organization_id');
            $table->string('name');
            $table->enum('type', ['microsoft_ad', 'google']);
            $table->string('client_id', 500)->nullable();
            $table->text('secret')->nullable();
            $table->datetimes();
            $table->softDeletesDatetime();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('organization_identity_providers');
    }
};

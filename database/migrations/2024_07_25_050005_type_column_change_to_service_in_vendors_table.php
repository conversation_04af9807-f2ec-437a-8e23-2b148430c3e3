<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            DB::statement("ALTER TABLE vendors CHANGE `type` `service` enum('lula','third-party-vendor') NOT NULL;");
            $table->index('service');
            $table->string('log_file_name')->after('company_name')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            DB::statement("ALTER TABLE vendors CHANGE `service` `type` enum('lula','third-party-vendor') NOT NULL;");
            $table->dropIndex(['service']);
            $table->dropColumn([
                'log_file_name',
            ]);
        });
    }
};

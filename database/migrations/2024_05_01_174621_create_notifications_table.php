<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id('notification_id');
            $table->realBinary('notification_uuid')->unique();
            $table->uuid('reference_notification_id');
            $table->foreignId('organization_id')->nullable()->constrained('organizations', 'organization_id');
            $table->foreignId('work_order_id')->nullable()->constrained('work_orders', 'work_order_id');
            $table->foreignId('action_done_by')->nullable()->constrained('users', 'user_id');
            $table->string('type');
            $table->morphs('notifiable');
            $table->text('data');
            $table->dateTime('read_at')->nullable();
            $table->dateTime('cleared_at')->nullable();
            $table->datetimes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};

<?php

use App\Enums\WorkToPerformTypes;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->enum('work_to_perform', WorkToPerformTypes::values())
                ->default(WorkToPerformTypes::HOURLY_TASK())
                ->after('status');

            $table->foreignId('quote_id')
                ->after('work_to_perform')
                ->nullable()
                ->constrained('quotes', 'quote_id');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dropForeign(['quote_id']);
            $table->dropColumn(['work_to_perform', 'quote_id']);
        });
    }
};

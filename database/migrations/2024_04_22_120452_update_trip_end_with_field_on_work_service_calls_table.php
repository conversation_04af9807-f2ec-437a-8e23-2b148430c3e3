<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE work_order_service_calls MODIFY COLUMN trip_end_with_type enum('submit-quote','finish-with-another-day','await-for-quote-approval','get-parts','resident-did-not-show-up','other');");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE work_order_service_calls MODIFY COLUMN trip_end_with_type enum('submit-quote','finish-with-another-day','await-for-quote-approval','get-parts','resident-did-not-show-up','other');");
    }
};

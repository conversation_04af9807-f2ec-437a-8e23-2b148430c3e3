<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('appfolio_integration_skipped_service_requests', function (Blueprint $table) {
            $table->id('appfolio_integration_skipped_service_request_id');
            $table->foreignId('organization_id')->constrained('organizations', 'organization_id', 'fk_appfolio_skipped_org_id');
            $table->foreignId('service_request_id')->nullable()->constrained('service_requests', 'service_request_id', 'fk_appfolio_skipped_sr_id');
            $table->string('appfolio_work_order_uuid', 500)->index('appfolio_sr_work_order_uuid')->nullable();
            $table->string('appfolio_work_order_number')->index('appfolio_sr_work_order_number')->nullable();
            $table->string('appfolio_work_order_status')->index('appfolio_sr_work_order_status')->nullable();
            $table->string('appfolio_vendor_id', 500)->index(indexName: 'appfolio_sr_vendor_id')->nullable();
            $table->dateTime('foresight_service_request_created_at')->nullable();
            $table->dateTime('notified_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('appfolio_integration_skipped_service_requests');
    }
};

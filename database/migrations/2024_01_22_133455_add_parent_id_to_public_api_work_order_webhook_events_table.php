<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('public_api_work_order_webhook_events', function (Blueprint $table) {
            if (! Schema::hasColumn('public_api_work_order_webhook_events', 'parent_id')) {
                $table->foreignId('parent_id')->nullable()->after('notified_at')->constrained('public_api_work_order_webhook_events', 'public_api_work_order_webhook_event_id');
            }
        });
    }

    public function down(): void
    {
        if (Schema::hasColumn('public_api_work_order_webhook_events', 'public_api_work_order_webhook_event_id')) {
            Schema::table('public_api_work_order_webhook_events', function (Blueprint $table) {
                $table->dropForeign('public_api_work_order_webhook_events_parent_id_foreign');
                $table->dropColumn('parent_id');
            });
        }
    }
};

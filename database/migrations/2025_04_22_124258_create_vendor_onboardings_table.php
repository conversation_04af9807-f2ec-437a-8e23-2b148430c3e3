<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_onboardings', function (Blueprint $table) {
            $table->id('vendor_onboarding_id');
            $table->realBinary(column: 'vendor_onboarding_uuid')->unique();
            $table->enum('type', ['third-party', 'in-house']);
            $table->unsignedBigInteger('vendor_onboarding_status_id');
            $table->foreign('vendor_onboarding_status_id')
                ->references('vendor_onboarding_status_id')
                ->on('vendor_onboarding_statuses');
            $table->unsignedBigInteger('vendor_id')->nullable();
            $table->foreign('vendor_id')
                ->references('vendor_id')
                ->on('vendors');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')
                ->references('user_id')
                ->on('users');
            $table->unsignedBigInteger('organization_id')->nullable();
            $table->foreign('organization_id')
                ->references('organization_id')
                ->on('organizations');
            $table->datetimes();
            $table->softDeletesDatetime();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_onboardings');
    }
};

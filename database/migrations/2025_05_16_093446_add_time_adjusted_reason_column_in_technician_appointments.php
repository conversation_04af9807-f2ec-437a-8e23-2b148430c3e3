<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('technician_appointments', function (Blueprint $table) {
            $table->text('time_adjusted_reason')->after('adjusted_elapse_time_in_sec')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('technician_appointments', function (Blueprint $table) {
            $table->dropColumn(['time_adjusted_reason']);
        });
    }
};

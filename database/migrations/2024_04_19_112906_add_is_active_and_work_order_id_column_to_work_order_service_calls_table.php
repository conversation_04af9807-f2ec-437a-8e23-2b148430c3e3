<?php

use App\Enums\Boolean;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->foreignId('work_order_id')->nullable()->after('organization_id')->constrained('work_orders', 'work_order_id');
            $table->enum('is_active', [Boolean::YES(), Boolean::NO()])->default(Boolean::NO())->after('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dropConstrainedForeignId('work_order_id');
            $table->dropColumn('is_active');
        });
    }
};

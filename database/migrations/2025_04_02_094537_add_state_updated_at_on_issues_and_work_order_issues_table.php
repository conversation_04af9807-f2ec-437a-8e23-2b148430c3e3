<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('issues')) {
            Schema::table('issues', function (Blueprint $table) {
                $table->dateTime('state_updated_at')
                    ->after('state')
                    ->nullable();
            });
        }

        if (Schema::hasTable('work_order_issues')) {
            Schema::table('work_order_issues', function (Blueprint $table) {
                $table->dateTime('state_updated_at')
                    ->after('decline_reason')
                    ->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('issues') && Schema::hasColumn('issues', 'state_updated_at')) {
            Schema::table('issues', function (Blueprint $table) {
                $table->dropColumn('state_updated_at');
            });
        }

        if (Schema::hasTable('work_order_issues') && Schema::hasColumn('work_order_issues', 'state_updated_at')) {
            Schema::table('work_order_issues', function (Blueprint $table) {
                $table->dropColumn('state_updated_at');
            });
        }
    }
};

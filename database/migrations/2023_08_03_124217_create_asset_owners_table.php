<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('asset_owners', function (Blueprint $table) {
            $table->id('asset_owner_id');
            $table->realBinary('asset_owner_uuid')->unique();
            $table->foreignId('organization_id')->constrained('organizations', 'organization_id');
            $table->string('company_name')->nullable();
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email');
            $table->string('phone_number', 20);
            $table->boolean('is_company')->default(false);
            $table->integer('assets_owned')->nullable();
            $table->datetimes();
            $table->softDeletesDatetime();
        });
    }

    public function down()
    {
        Schema::dropIfExists('asset_owners');
    }
};

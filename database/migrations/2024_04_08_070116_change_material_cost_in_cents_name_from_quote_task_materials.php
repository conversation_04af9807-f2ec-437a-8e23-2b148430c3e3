<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quote_task_materials', function (Blueprint $table) {
            if (Schema::hasColumn('quote_task_materials', 'cost_in_cents')) {
                DB::statement('ALTER TABLE quote_task_materials CHANGE cost_in_cents total_cost_in_cents bigint NULL');
            }

            if (Schema::hasColumn('quote_task_materials', 'material_cost_in_cents')) {
                DB::statement('ALTER TABLE quote_task_materials CHANGE material_cost_in_cents cost_in_cents bigint NULL');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quote_task_materials', function (Blueprint $table) {
            if (Schema::hasColumn('quote_task_materials', 'cost_in_cents')) {
                DB::statement('ALTER TABLE quote_task_materials CHANGE cost_in_cents material_cost_in_cents bigint NULL');
            }

            if (Schema::hasColumn('quote_task_materials', 'total_cost_in_cents')) {
                DB::statement('ALTER TABLE quote_task_materials CHANGE total_cost_in_cents cost_in_cents bigint NULL');
            }
        });
    }
};

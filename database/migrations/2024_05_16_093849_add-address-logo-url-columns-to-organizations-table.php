<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('organizations')) {
            Schema::table('organizations', function (Blueprint $table) {
                $table->string('logo_file_name')->nullable()->after('webhook_api_url');
                $table->string('street_address')->nullable()->after('template');
                $table->string('city')->nullable()->after('street_address');
                $table->string('zip_code')->nullable()->after('city');
                $table->foreignId('state_id')->after('zip_code')->nullable()->constrained('states', 'state_id');
                $table->foreignId('country_id')->after('state_id')->nullable()->constrained('countries', 'country_id');
                $table->string('phone_number')->nullable()->after('state_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('organizations')) {
            Schema::table('organizations', function (Blueprint $table) {
                $table->dropColumn('logo_file_name');
                $table->dropColumn('street_address');
                $table->dropColumn('city');
                $table->dropColumn('zip_code');
                $table->dropColumn('phone_number');
                $table->dropConstrainedForeignId('state_id');
                $table->dropConstrainedForeignId('country_id');
            });
        }
    }
};

<?php

use App\Enums\CostTypes;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quote_task_materials', function (Blueprint $table) {
            $table->enum('cost_type', CostTypes::values())->nullable()->after('cost_in_cents');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quote_task_materials', function (Blueprint $table) {
            $table->dropColumn('cost_type');
        });
    }
};

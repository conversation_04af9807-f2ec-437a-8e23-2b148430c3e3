<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendor_external_invoices', function (Blueprint $table) {
            $table->json('extras')->nullable()->after('amount_remaining_cents');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendor_external_invoices', function (Blueprint $table) {
            $table->dropColumn([
                'extras',
            ]);
        });
    }
};

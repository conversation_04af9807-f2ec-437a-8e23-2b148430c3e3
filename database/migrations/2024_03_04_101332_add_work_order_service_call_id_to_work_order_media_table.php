<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_media', function (Blueprint $table) {
            $table->foreignId('work_order_service_call_id')->nullable()->after('has_upload_completed')
                ->constrained('work_order_service_calls', 'work_order_service_call_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_media', function (Blueprint $table) {
            $table->dropForeign('work_order_media_work_order_service_call_id_foreign');
            $table->dropColumn('work_order_service_call_id');
        });
    }
};

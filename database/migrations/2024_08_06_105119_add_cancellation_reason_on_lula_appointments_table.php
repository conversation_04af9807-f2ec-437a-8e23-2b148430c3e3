<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lula_appointments', function (Blueprint $table) {
            $table->after('rescheduled_reason', function (Blueprint $table) {
                $table->string('cancellation_reason')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lula_appointments', function (Blueprint $table) {
            $table->dropColumn([
                'cancellation_reason',
            ]);
        });
    }
};

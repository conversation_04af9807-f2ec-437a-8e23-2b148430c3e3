<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_logs', function (Blueprint $table) {
            DB::statement('ALTER TABLE invoice_logs MODIFY COLUMN markup_fee_type_value decimal(10,2) NULL;');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_logs', function (Blueprint $table) {
            DB::statement('ALTER TABLE invoice_logs MODIFY COLUMN markup_fee_type_value decimal(8,2) NULL;');
        });
    }
};

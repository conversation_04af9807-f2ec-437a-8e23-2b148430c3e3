<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_media', callback: function (Blueprint $table) {
            $table->dropForeign(['work_order_task_id']);
            $table->dropColumn('work_order_task_id');

            $table->foreignId('work_order_issue_id')
                ->nullable()
                ->after('work_order_id')
                ->constrained('work_order_issues', 'work_order_issue_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_media', function (Blueprint $table) {
            $table->foreignId('work_order_task_id')
                ->nullable()
                ->constrained('work_order_tasks', 'work_order_task_id');

            $table->dropConstrainedForeignId('work_order_issue_id');
        });
    }
};

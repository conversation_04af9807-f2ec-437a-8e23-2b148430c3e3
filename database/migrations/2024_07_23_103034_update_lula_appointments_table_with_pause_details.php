<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lula_appointments', function (Blueprint $table) {
            $table->after('service_category_label', function (Blueprint $table) {
                $table->dateTime('scheduled_start_time')->nullable();
                $table->dateTime('scheduled_end_time')->nullable();
                $table->dateTime('actual_start_time')->nullable();
                $table->dateTime('actual_end_time')->nullable();
                $table->string('paused_reason')->nullable();
                $table->string('rescheduled_reason')->nullable();
                $table->dateTime('estimated_return_start_time')->nullable();
                $table->dateTime('estimated_return_end_time')->nullable();
                $table->string('elapse_time_in_sec')->nullable();
            });

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lula_appointments', function (Blueprint $table) {
            $table->dropColumn([
                'scheduled_start_time',
                'scheduled_end_time',
                'actual_start_time',
                'actual_end_time',
                'paused_reason',
                'estimated_return_start_time',
                'estimated_return_end_time',
                'elapse_time_in_sec',
            ]);
        });
    }
};

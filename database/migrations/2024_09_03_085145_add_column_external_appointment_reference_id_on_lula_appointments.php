<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('lula_appointments', function (Blueprint $table) {
            $table->string('external_appointment_reference_id')->nullable()->after('organization_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lula_appointments', function (Blueprint $table) {
            $table->dropColumn([
                'external_appointment_reference_id',
            ]);
        });
    }
};

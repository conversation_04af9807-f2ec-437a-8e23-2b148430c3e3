<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('properties', function (Blueprint $table) {
            $table->string('navigation_address', 500)->nullable()->after('full_address');
            $table->string('google_place_id')->nullable()->after('longitude');
            $table->json('google_geocode_response')->nullable()->after('google_place_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('properties', function (Blueprint $table) {
            $table->dropColumn(['navigation_address', 'google_place_id', 'google_geocode_response']);
        });
    }
};

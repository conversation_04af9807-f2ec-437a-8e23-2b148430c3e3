<?php

use App\Enums\WorkOrderStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {

        if (Schema::hasTable('work_order_statuses')) {
            Schema::table('work_order_statuses', function (Blueprint $table) {
                $table->string('slug', 150)->unique()->index()->change();
            });
        }

        DB::table('work_order_statuses')
            ->where('slug', 'new')
            ->update(['slug' => WorkOrderStatus::NEW()]);

        if (Schema::hasColumns('work_orders', ['work_order_status_id', 'state'])) {
            Schema::table('work_orders', function (Blueprint $table) {
                $table->dropForeign('work_orders_work_order_status_id_foreign');
                $table->string('state', 150)->index()->change();
                $table->foreign('state')->references('slug')->on('work_order_statuses');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_orders', function (Blueprint $table) {
            $table->dropForeign('work_orders_state_foreign');
            $table->dropIndex('work_orders_state_index');
            $table->foreign('work_order_status_id')->references('work_order_status_id')->on('work_order_statuses');
        });

        if (Schema::hasTable('work_order_statuses')) {
            Schema::table('work_order_statuses', function (Blueprint $table) {
                $table->dropUnique('work_order_statuses_slug_unique');
            });
        }
    }
};

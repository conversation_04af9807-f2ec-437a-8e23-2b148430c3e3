<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('technician_appointments', function (Blueprint $table) {
            $table->dateTime('en_route_timer_paused_at')->nullable()->after('enroute_at');
            $table->dateTime('en_route_timer_resumed_at')->nullable()->after('en_route_timer_paused_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('technician_appointments', function (Blueprint $table) {
            $table->dropColumn([
                'en_route_timer_paused_at', 'en_route_timer_resumed_at',
            ]);
        });
    }
};

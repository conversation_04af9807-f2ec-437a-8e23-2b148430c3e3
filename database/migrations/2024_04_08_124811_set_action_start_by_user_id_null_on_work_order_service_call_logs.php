<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasColumn('work_order_service_call_logs', 'action_start_by_user_id')) {
            DB::statement('ALTER TABLE work_order_service_call_logs MODIFY COLUMN action_start_by_user_id bigint unsigned NULL');
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasColumn('work_order_service_call_logs', 'action_start_by_user_id')) {
            DB::statement('ALTER TABLE work_order_service_call_logs MODIFY COLUMN action_start_by_user_id bigint unsigned NOT NULL');
        }
    }
};

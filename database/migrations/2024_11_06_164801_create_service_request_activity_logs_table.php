<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        /** Create table */
        Schema::create('service_request_activity_logs', function (Blueprint $table) {
            $table->id('service_request_activity_log_id');
            $table->realBinary('service_request_activity_log_uuid')->unique('service_request_activity_log_uuid_unique');
            $table->unsignedBigInteger('service_request_id')->nullable();
            $table->unsignedBigInteger('work_order_id')->nullable();
            $table->unsignedBigInteger('work_order_task_id')->nullable();
            $table->unsignedBigInteger('organization_id');
            $table->unsignedBigInteger('triggered_by')->nullable();
            $table->enum('type', ['service-request', 'work-order'])->default('service-request');
            $table->string('event');
            $table->jsonb('event_attributes');
            $table->dateTimes();
            $table->softDeletesDatetime();
        });

        /** Create references */
        Schema::table('service_request_activity_logs', function (Blueprint $table) {
            $table->foreign('service_request_id')->references(columns: 'service_request_id')->on('service_requests');
            $table->foreign('work_order_id')->references('work_order_id')->on('work_orders');
            $table->foreign('work_order_task_id')->references('work_order_task_id')->on('work_order_tasks');
            $table->foreign('organization_id')->references('organization_id')->on('organizations');
            $table->foreign('triggered_by')->references('user_id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        /** Drop references */
        Schema::table('service_request_activity_logs', function (Blueprint $table) {
            $table->dropForeign(['service_request_id']);
            $table->dropForeign(['work_order_id']);
            $table->dropForeign(['work_order_task_id']);
            $table->dropForeign(['organization_id']);
            $table->dropForeign(['triggered_by']);
        });

        /** Drop table */
        Schema::dropIfExists('service_request_activity_logs');
    }
};

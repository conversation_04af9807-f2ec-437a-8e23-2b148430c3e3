<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('service_requests', function (Blueprint $table) {
            $table->foreignId('description_last_edited_user_id')
                ->after('service_request_reference_number')->nullable()
                ->constrained('users', 'user_id');
            $table->dateTime('description_last_edited_at')
                ->after('service_request_reference_number')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::table('service_requests', function (Blueprint $table) {
            $table->dropForeign(['description_last_edited_user_id']); // Drop foreign key constraint
            $table->dropColumn(['description_last_edited_user_id', 'description_last_edited_at']); // Remove columns
        });
    }
};

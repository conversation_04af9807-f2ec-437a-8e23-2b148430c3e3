<?php

use App\Services\Scheduling\Domain\Enums\SchedulingMethod;
use App\Services\Scheduling\Domain\Enums\SchedulingMode;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->enum('mode', array_column(SchedulingMode::cases(), 'value'))
                ->after('duration_minutes')
                ->default(SchedulingMode::MANUAL());

            $table->enum('method', array_column(SchedulingMethod::cases(), 'value'))
                ->after('mode')
                ->default(SchedulingMethod::EARLIEST());

        });
    }

    public function down(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dropColumn(['mode', 'method']);
        });
    }
};

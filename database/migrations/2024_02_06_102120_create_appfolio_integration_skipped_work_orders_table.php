<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('appfolio_integration_skipped_work_orders', function (Blueprint $table) {
            $table->id('appfolio_integration_skipped_work_order_id');
            $table->foreignId('organization_id')->constrained('organizations', 'organization_id');
            $table->foreignId('work_order_id')->nullable()->constrained('work_orders', 'work_order_id');
            $table->string('appfolio_work_order_uuid', 500)->index('appfolio_work_order_reference_uuid')->nullable();
            $table->string('display_number')->index('skipped_work_orders_display_number')->nullable();
            $table->string('work_order_url', 500)->nullable();
            $table->string('work_order_status')->index('skipped_work_orders_status')->nullable();
            $table->string('vendor_id', 500)->index('skipped_work_orders_vendor_id')->nullable();
            $table->dateTime('work_order_created_at')->nullable();
            $table->dateTime('notified_at')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('appfolio_integration_skipped_work_orders');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_service_call_logs', function () {
            DB::statement('ALTER TABLE work_order_service_call_logs MODIFY COLUMN work_order_task_id bigint unsigned NULL');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_service_call_logs', function () {
            DB::statement('ALTER TABLE work_order_service_call_logs MODIFY COLUMN work_order_task_id bigint unsigned NOT NULL');
        });
    }
};

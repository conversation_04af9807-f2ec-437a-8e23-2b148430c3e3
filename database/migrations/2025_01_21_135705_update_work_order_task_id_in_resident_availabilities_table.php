<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('resident_availabilities', function (Blueprint $table) {
            $table->unsignedBigInteger('work_order_task_id')->nullable()->default(null)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('resident_availabilities', function (Blueprint $table) {
            // Change back to the original column definition (update as needed)
            $table->unsignedBigInteger('work_order_task_id')->nullable(false)->default(0)->change();
        });
    }
};

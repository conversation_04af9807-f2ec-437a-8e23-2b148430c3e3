<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {

            $table->after('last_modified_user', function (Blueprint $table) {
                $table->dateTime('timer_paused_at')->nullable();
                $table->dateTime('timer_resumed_at')->nullable();
                $table->text('timer_paused_reason')->nullable();
            });
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('work_order_service_calls', function (Blueprint $table) {
            $table->dropColumn([
                'timer_paused_at', 'timer_resumed_at', 'timer_paused_reason',
            ]);
        });
    }
};

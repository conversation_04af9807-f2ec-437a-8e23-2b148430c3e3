<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('vendor_allocations', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['work_order_task_id']);
            $table->dropColumn('work_order_task_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendor_allocations', function (Blueprint $table) {
            $table->unsignedBigInteger('work_order_task_id')->nullable();

            $table->foreign('work_order_task_id')
                ->references('id')
                ->on('work_order_tasks')
                ->onDelete('cascade');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_requests', function (Blueprint $table) {
            $table->dateTime('availability_requested_at')->after('canceled_at')->nullable();
            $table->foreignId('availability_requested_user_id')->after('availability_requested_at')->nullable()->constrained('users', 'user_id');
            $table->foreignId('availability_viewed_user_id')->after('availability_requested_user_id')->nullable()->constrained('users', 'user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_requests', function (Blueprint $table) {
            $table->dropConstrainedForeignId('availability_viewed_user_id');
            $table->dropConstrainedForeignId('availability_requested_user_id');
            $table->dropColumn('availability_requested_at');
        });
    }
};

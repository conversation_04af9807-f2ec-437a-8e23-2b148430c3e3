<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            $table->foreignId('last_assigned_service_call_id')
                ->after('last_modified_user_id')
                ->nullable()
                ->constrained('work_order_service_calls', 'work_order_service_call_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            $table->dropForeign(['last_assigned_service_call_id']);
            $table->dropColumn(['last_assigned_service_call_id']);
        });
    }
};

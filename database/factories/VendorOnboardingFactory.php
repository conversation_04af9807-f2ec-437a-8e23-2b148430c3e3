<?php

namespace Database\Factories;

use App\Models\Organization;
use App\Models\User;
use App\Models\Vendor;
use App\Models\VendorOnboarding;
use App\Models\VendorOnboardingStatus;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\VendorOnboarding>
 */
class VendorOnboardingFactory extends Factory
{
    protected $model = VendorOnboarding::class;

    public function definition(): array
    {
        return [
            'vendor_onboarding_uuid' => Str::uuid()->toString(),
            'type' => $this->faker->randomElement(['in-house', 'third-party']),
            'vendor_onboarding_status_id' => VendorOnboardingStatus::factory(),
            'vendor_id' => Vendor::factory(),
            'user_id' => User::factory(),
            'organization_id' => Organization::factory(),
            'access_count' => 0,
        ];
    }
}

<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\State>
 */
class StateFactory extends Factory
{
    /**
     * List of US States.
     *
     * @var array<int, array<string, string>>
     */
    protected array $usStates = [
        ['name' => 'Alabama', 'state_code' => 'AL'],
        ['name' => 'Alaska', 'state_code' => 'AK'],
        ['name' => 'Arizona', 'state_code' => 'AZ'],
        ['name' => 'Arkansas', 'state_code' => 'AR'],
        ['name' => 'California', 'state_code' => 'CA'],
        ['name' => 'Colorado', 'state_code' => 'CO'],
        ['name' => 'Connecticut', 'state_code' => 'CT'],
        ['name' => 'Delaware', 'state_code' => 'DE'],
        ['name' => 'Florida', 'state_code' => 'FL'],
        ['name' => 'Georgia', 'state_code' => 'GA'],
        ['name' => 'Hawaii', 'state_code' => 'HI'],
        ['name' => 'Idaho', 'state_code' => 'ID'],
        ['name' => 'Illinois', 'state_code' => 'IL'],
        ['name' => 'Indiana', 'state_code' => 'IN'],
        ['name' => 'Iowa', 'state_code' => 'IA'],
        ['name' => 'Kansas', 'state_code' => 'KS'],
        ['name' => 'Kentucky', 'state_code' => 'KY'],
        ['name' => 'Louisiana', 'state_code' => 'LA'],
        ['name' => 'Maine', 'state_code' => 'ME'],
        ['name' => 'Maryland', 'state_code' => 'MD'],
        ['name' => 'Massachusetts', 'state_code' => 'MA'],
        ['name' => 'Michigan', 'state_code' => 'MI'],
        ['name' => 'Minnesota', 'state_code' => 'MN'],
        ['name' => 'Mississippi', 'state_code' => 'MS'],
        ['name' => 'Missouri', 'state_code' => 'MO'],
        ['name' => 'Montana', 'state_code' => 'MT'],
        ['name' => 'Nebraska', 'state_code' => 'NE'],
        ['name' => 'Nevada', 'state_code' => 'NV'],
        ['name' => 'New Hampshire', 'state_code' => 'NH'],
        ['name' => 'New Jersey', 'state_code' => 'NJ'],
        ['name' => 'New Mexico', 'state_code' => 'NM'],
        ['name' => 'New York', 'state_code' => 'NY'],
        ['name' => 'North Carolina', 'state_code' => 'NC'],
        ['name' => 'North Dakota', 'state_code' => 'ND'],
        ['name' => 'Ohio', 'state_code' => 'OH'],
        ['name' => 'Oklahoma', 'state_code' => 'OK'],
        ['name' => 'Oregon', 'state_code' => 'OR'],
        ['name' => 'Pennsylvania', 'state_code' => 'PA'],
        ['name' => 'Rhode Island', 'state_code' => 'RI'],
        ['name' => 'South Carolina', 'state_code' => 'SC'],
        ['name' => 'South Dakota', 'state_code' => 'SD'],
        ['name' => 'Tennessee', 'state_code' => 'TN'],
        ['name' => 'Texas', 'state_code' => 'TX'],
        ['name' => 'Utah', 'state_code' => 'UT'],
        ['name' => 'Vermont', 'state_code' => 'VT'],
        ['name' => 'Virginia', 'state_code' => 'VA'],
        ['name' => 'Washington', 'state_code' => 'WA'],
        ['name' => 'West Virginia', 'state_code' => 'WV'],
        ['name' => 'Wisconsin', 'state_code' => 'WI'],
        ['name' => 'Wyoming', 'state_code' => 'WY'],
    ];

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $state = fake()->randomElement($this->usStates);

        return [
            'name' => $state['name'],
            'state_code' => $state['state_code'],
        ];
    }

    /**
     * Create state with specified state code.
     */
    public function withStateCode(string $stateCode): Factory
    {
        $matchingState = collect($this->usStates)->firstWhere('state_code', strtoupper($stateCode));

        if (! $matchingState) {
            $matchingState = fake()->randomElement($this->usStates);
        }

        return $this->state(function (array $attributes) use ($matchingState) {
            return [
                'name' => $matchingState['name'],
                'state_code' => $matchingState['state_code'],
            ];
        });
    }
}

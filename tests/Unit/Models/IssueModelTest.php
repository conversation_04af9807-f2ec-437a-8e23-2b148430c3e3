<?php

declare(strict_types=1);

use App\Models\Issue;
use App\Models\IssueStatus;
use App\Models\Organization;
use App\Models\ProblemDiagnosis;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestStatus;
use App\Models\Timezone;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\Models\WorkOrderIssueStatus;
use App\States\Issue\Unassigned;
use App\States\WorkOrderIssue\Assigned;

beforeEach(function () {
    $this->organization = Organization::factory()->create();
    $this->serviceRequest = ServiceRequest::factory()
        ->for($this->organization)
        ->for(Property::factory()->for($this->organization))
        ->for(Timezone::factory())
        ->for(ServiceRequestStatus::factory(), 'status')
        ->create();
    $this->problemDiagnosis = ProblemDiagnosis::factory()->create();

    if (! WorkOrderIssueStatus::where('slug', Assigned::$name)->count()) {
        $this->workOrderIssueStatus = WorkOrderIssueStatus::factory()->create();
    } else {
        $this->workOrderIssueStatus = WorkOrderIssueStatus::where('slug', Assigned::$name)->first();
    }

    if (! IssueStatus::where('slug', Unassigned::$name)->count()) {
        IssueStatus::factory()->create();
    }
});

describe('Issue model has a proper relationship with', function () {
    test('organization', function () {
        $issue = Issue::factory()
            ->create([
                'organization_id' => $this->organization->organization_id,
                'problem_diagnosis_id' => $this->problemDiagnosis->problem_diagnosis_id,
                'service_request_id' => $this->serviceRequest->service_request_id,
            ]);

        expect($issue->organization)
            ->toBeInstanceOf(Organization::class);
    });

    test('problemDiagnosis', function () {
        $issue = Issue::factory()
            ->create([
                'organization_id' => $this->organization->organization_id,
                'problem_diagnosis_id' => $this->problemDiagnosis->problem_diagnosis_id,
                'service_request_id' => $this->serviceRequest->service_request_id,
            ]);

        expect($issue->problemDiagnosis)
            ->toBeInstanceOf(ProblemDiagnosis::class);
    });

    test('serviceRequest', function () {
        $issue = Issue::factory()
            ->create([
                'organization_id' => $this->organization->organization_id,
                'problem_diagnosis_id' => $this->problemDiagnosis->problem_diagnosis_id,
                'service_request_id' => $this->serviceRequest->service_request_id,
            ]);

        expect($issue->serviceRequest)
            ->toBeInstanceOf(ServiceRequest::class);
    });

    test('workOrderIssues', function () {
        $issue = Issue::factory()
            ->create([
                'organization_id' => $this->organization->organization_id,
                'problem_diagnosis_id' => $this->problemDiagnosis->problem_diagnosis_id,
                'service_request_id' => $this->serviceRequest->service_request_id,
            ]);

        $workOrder = WorkOrder::factory()
            ->for($this->organization)
            ->for(Property::factory()->for($this->organization))
            ->for(Timezone::factory())
            ->create();

        WorkOrderIssue::factory()->count(3)->create([
            'organization_id' => $this->organization->organization_id,
            'issue_id' => $issue->issue_id,
            'work_order_id' => $workOrder->work_order_id,
            'state' => $this->workOrderIssueStatus->slug,

        ]);

        $issue->refresh();

        expect($issue->workOrderIssues)->toHaveCount(3);
        expect($issue->workOrderIssues->first())->toBeInstanceOf(WorkOrderIssue::class);
    });

    test('workOrders', function () {
        $issue = Issue::factory()
            ->create([
                'organization_id' => $this->organization->organization_id,
                'problem_diagnosis_id' => $this->problemDiagnosis->problem_diagnosis_id,
                'service_request_id' => $this->serviceRequest->service_request_id,
            ]);

        $workOrder = WorkOrder::factory()
            ->for($this->organization)
            ->for(Property::factory()->for($this->organization))
            ->create();

        WorkOrderIssue::factory(2)->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $this->organization->organization_id,
        ]);
        expect($issue->workOrders)->toHaveCount(2);
    });
});

test('The model has fillable attributes that include the appropriate columns', function () {
    $model = new Issue;
    $table = $model->getTable();

    $columnToBeExclude = [
        'issue_id',
        'created_at',
        'updated_at',
    ];

    $columnsInTable = Schema::getColumnListing($table);
    $fillableColumnsInModel = $model->getFillable();

    expect($columnsInTable)->toBeArray();
    expect($fillableColumnsInModel)->toBeArray();

    $columnsToBeMatch = array_unique(array_merge($columnToBeExclude, $fillableColumnsInModel));

    // sort
    sort($columnsInTable);
    sort($columnsToBeMatch);

    expect(count($columnsInTable))->toBe(count($columnsToBeMatch));
    expect($columnsInTable)->toMatchArray($columnsToBeMatch);
});

<?php

use App\Models\IssueStatus;

test('The model has fillable attributes that include the appropriate columns', function () {
    $model = new IssueStatus;
    $table = $model->getTable();

    $columnToBeExclude = [
        'issue_status_id',
        'created_at',
        'updated_at',
    ];

    $columnsInTable = Schema::getColumnListing($table);
    $fillableColumnsInModel = $model->getFillable();

    expect($columnsInTable)->toBeArray();
    expect($fillableColumnsInModel)->toBeArray();

    $columnsToBeMatch = array_unique(array_merge($columnToBeExclude, $fillableColumnsInModel));

    // sort
    sort($columnsInTable);
    sort($columnsToBeMatch);

    expect(count($columnsInTable))->toBe(count($columnsToBeMatch));
    expect($columnsInTable)->toMatchArray($columnsToBeMatch);
});

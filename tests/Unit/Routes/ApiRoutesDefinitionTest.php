<?php

namespace Tests\Unit\Routes;

use PHPUnit\Framework\TestCase;

/**
 * Unit test with comprehensive coverage of routes/api.php file structure
 *
 * This test validates the route definitions by parsing the actual routes file
 * and ensuring all expected routes, middleware, and controllers are properly defined.
 *
 * @group ENG-5636-be-vendor-portal-feature-filters
 * @group routes
 * @group api-routes
 * @group unit
 */
class ApiRoutesDefinitionTest extends TestCase
{
    private string $routesFileContent;
    private array $routeDefinitions;

    protected function setUp(): void
    {
        parent::setUp();

        $routesFilePath = __DIR__ . '/../../../routes/api.php';
        $this->assertTrue(file_exists($routesFilePath), 'API routes file should exist');

        $this->routesFileContent = file_get_contents($routesFilePath);
        $this->parseRouteDefinitions();
    }

    /**
     * Parse route definitions from the routes file content
     */
    private function parseRouteDefinitions(): void
    {
        $this->routeDefinitions = [
            'public_routes' => [],
            'authenticated_routes' => [],
            'middleware_groups' => [],
            'route_groups' => [],
        ];

        // Extract route patterns
        $this->extractPublicRoutes();
        $this->extractAuthenticatedRoutes();
        $this->extractMiddlewareGroups();
        $this->extractRouteGroups();
    }

    /**
     * @test
     * @covers routes/api.php::1-49
     */
    public function file_has_proper_php_opening_and_imports()
    {
        $this->assertStringStartsWith('<?php', $this->routesFileContent);

        // Check for required imports
        $requiredImports = [
            'use Illuminate\Support\Facades\Route;',
            'use App\Http\Controllers',
        ];

        foreach ($requiredImports as $import) {
            $this->assertStringContainsString($import, $this->routesFileContent,
                "Routes file should contain import: {$import}");
        }
    }

    /**
     * @test
     * @covers routes/api.php::50-64
     */
    public function public_routes_are_properly_defined()
    {
        $expectedPublicRoutes = [
            "Route::post('register'",
            "Route::get('config'",
            "Route::post('forgot-password'",
            "Route::post('reset-password'",
            "Route::get('verify/{id}'",
            "Route::get('provider/config'",
            "Route::post('organization/create'",
            "Route::post('organization/validate-sso-user'",
            "Route::post('oauth2/token'",
            "Route::get('check-app-version'",
            "Route::post('vendor/login'",
            "Route::get('resident-availability/{serviceRequestPublicId}'",
            "Route::post('resident-availability/{serviceRequestPublicId}'",
            "Route::get('vendor-onboarding/invite'",
        ];

        foreach ($expectedPublicRoutes as $route) {
            $this->assertStringContainsString($route, $this->routesFileContent,
                "Public route should be defined: {$route}");
        }
    }

    /**
     * @test
     * @covers routes/api.php::65-85
     */
    public function vendor_onboarding_routes_are_grouped()
    {
        // Check for vendor onboarding route group
        $this->assertStringContainsString("Route::prefix('vendor-onboarding')", $this->routesFileContent);
        $this->assertStringContainsString("VendorOnboardingController", $this->routesFileContent);

        $vendorOnboardingRoutes = [
            "Route::post('/generate-signed-url'",
            "Route::get('/onboarding-details'",
            "Route::get('/status'",
            "Route::post('/set-password'",
            "Route::post('/set-basic-info'",
            "Route::post('/set-services'",
            "Route::post('/set-service-areas'",
            "Route::get('/lookup/{lookupType?}'",
            "Route::post('/set-status'",
        ];

        foreach ($vendorOnboardingRoutes as $route) {
            $this->assertStringContainsString($route, $this->routesFileContent,
                "Vendor onboarding route should be defined: {$route}");
        }
    }

    /**
     * @test
     * @covers routes/api.php::87-306
     */
    public function authenticated_routes_have_proper_middleware()
    {
        // Check for main authenticated route group
        $middlewarePattern = "Route::middleware(['auth.cognito', 'tenant', 'app.version.required', 'app.access'])";
        $this->assertStringContainsString($middlewarePattern, $this->routesFileContent,
            "Authenticated routes should have proper middleware group");
    }

    /**
     * @test
     * @covers routes/api.php::89-99
     */
    public function user_routes_are_defined()
    {
        $userRoutes = [
            "Route::get('/users/filter'",
            "Route::get('/users/group-view'",
            "Route::resource('users', UserController::class)",
            "Route::put('/users/{user}/subscribe-notification'",
        ];

        foreach ($userRoutes as $route) {
            $this->assertStringContainsString($route, $this->routesFileContent,
                "User route should be defined: {$route}");
        }
    }

    /**
     * @test
     * @covers routes/api.php::101-112
     */
    public function technician_routes_are_nested()
    {
        $this->assertStringContainsString("Route::prefix('technicians/{technician}')", $this->routesFileContent);

        $technicianRoutes = [
            "Route::get('/working-hours'",
            "Route::put('/working-hours'",
            "Route::get('/skills'",
            "Route::put('/skills'",
            "Route::get('/calendar'",
            "Route::get('/calendar/availability-dates'",
        ];

        foreach ($technicianRoutes as $route) {
            $this->assertStringContainsString($route, $this->routesFileContent,
                "Technician route should be defined: {$route}");
        }
    }

    /**
     * @test
     * @covers routes/api.php::113-118
     */
    public function role_and_permission_routes_exist()
    {
        $this->assertStringContainsString("Route::resource('roles', RoleController::class)", $this->routesFileContent);
        $this->assertStringContainsString("Route::get('/permissions'", $this->routesFileContent);
    }

    /**
     * @test
     * @covers routes/api.php::127-206
     */
    public function work_order_routes_are_comprehensive()
    {
        // Check for work order route group
        $this->assertStringContainsString("Route::prefix('work-orders')", $this->routesFileContent);

        $workOrderRoutes = [
            "Route::get('/group-view'",
            "Route::get('/filter'",
            "Route::get('/count'",
            "Route::resource('/', WorkOrderController::class)",
            "Route::patch('/{workOrder}/priority'",
            "Route::patch('/{workOrder}/due-date'",
            "Route::delete('/{workOrder}/due-date'",
            "Route::patch('/{workOrder}/access-method'",
            "Route::put('/{workOrder}/property-address'",
            "Route::put('/{workOrder}/resident-info'",
        ];

        foreach ($workOrderRoutes as $route) {
            $this->assertStringContainsString($route, $this->routesFileContent,
                "Work order route should be defined: {$route}");
        }
    }

    /**
     * @test
     * @covers routes/api.php::208-217
     */
    public function quote_routes_are_defined()
    {
        $this->assertStringContainsString("Route::prefix('quotes')", $this->routesFileContent);

        $quoteRoutes = [
            "Route::get('/group-view'",
            "Route::get('/filter'",
            "Route::resource('/', QuoteController::class)",
        ];

        foreach ($quoteRoutes as $route) {
            $this->assertStringContainsString($route, $this->routesFileContent,
                "Quote route should be defined: {$route}");
        }
    }

    /**
     * @test
     * @covers routes/api.php::220-235
     */
    public function vendor_routes_are_structured()
    {
        $this->assertStringContainsString("Route::prefix('vendors')", $this->routesFileContent);

        $vendorRoutes = [
            "Route::get('/work-orders'",
            "Route::get('/work-orders/{workOrder}'",
            "Route::resource('/', VendorController::class)",
        ];

        foreach ($vendorRoutes as $route) {
            $this->assertStringContainsString($route, $this->routesFileContent,
                "Vendor route should be defined: {$route}");
        }
    }

    /**
     * @test
     * @covers routes/api.php::237-270
     */
    public function service_request_routes_are_comprehensive()
    {
        $this->assertStringContainsString("Route::prefix('service-requests')", $this->routesFileContent);

        $serviceRequestRoutes = [
            "Route::get('/group-view'",
            "Route::get('/filter'",
            "Route::resource('/', ServiceRequestController::class)",
            "Route::resource('/{serviceRequest}/notes', ServiceRequestNoteController::class)",
            "Route::resource('/{serviceRequest}/media', ServiceRequestMediaController::class)",
            "Route::resource('/{serviceRequest}/assignees', ServiceRequestAssigneeController::class)",
            "Route::get('/{serviceRequest}/activity-log'",
        ];

        foreach ($serviceRequestRoutes as $route) {
            $this->assertStringContainsString($route, $this->routesFileContent,
                "Service request route should be defined: {$route}");
        }
    }

    /**
     * @test
     * @covers routes/api.php::272-285
     */
    public function view_management_routes_are_defined()
    {
        $this->assertStringContainsString("Route::prefix('views')", $this->routesFileContent);

        $viewRoutes = [
            "Route::get('/'",
            "Route::get('/count'",
            "Route::get('/config'",
            "Route::post('/'",
            "Route::post('/{view}/set-as-default'",
            "Route::post('/{view}/pin'",
            "Route::post('/{view}/rename'",
            "Route::resource('/{view}', ViewController::class)",
            "Route::post('/{view}/duplicate'",
        ];

        foreach ($viewRoutes as $route) {
            $this->assertStringContainsString($route, $this->routesFileContent,
                "View route should be defined: {$route}");
        }
    }

    /**
     * @test
     * @covers routes/api.php::287-307
     */
    public function miscellaneous_routes_are_defined()
    {
        $miscRoutes = [
            "Route::resource('tags', TagController::class)",
            "Route::get('/lookup/{lookupType?}'",
            "Route::get('/profile'",
            "Route::get('/organization'",
            "Route::post('/organization/update'",
            "Route::get('/notifications'",
            "Route::post('/notifications/clear'",
            "Route::get('/scheduling/availability'",
            "Route::post('/media/upload'",
            "Route::get('/invoices'",
        ];

        foreach ($miscRoutes as $route) {
            $this->assertStringContainsString($route, $this->routesFileContent,
                "Miscellaneous route should be defined: {$route}");
        }
    }

    /**
     * @test
     */
    public function all_controller_imports_are_present()
    {
        $requiredControllers = [
            'RegisterController',
            'SSOController',
            'AppVersionController',
            'VendorController',
            'ResidentAvailabilityController',
            'VendorOnboardingController',
            'UserController',
            'TechnicianController',
            'RoleController',
            'WorkOrderController',
            'QuoteController',
            'ServiceRequestController',
            'ViewController',
            'TagController',
            'LookupController',
            'ProfileController',
            'OrganizationController',
            'DatabaseNotificationController',
            'SchedulingController',
            'MediaController',
            'InvoiceController',
        ];

        foreach ($requiredControllers as $controller) {
            $this->assertStringContainsString($controller, $this->routesFileContent,
                "Controller should be imported: {$controller}");
        }
    }

    /**
     * @test
     */
    public function signed_middleware_is_applied_to_appropriate_routes()
    {
        // Check for signed middleware on verification routes
        $signedRoutes = [
            "->middleware('signed')",
        ];

        foreach ($signedRoutes as $middleware) {
            $this->assertStringContainsString($middleware, $this->routesFileContent,
                "Signed middleware should be applied: {$middleware}");
        }
    }

    /**
     * @test
     */
    public function throttle_middleware_is_applied_where_needed()
    {
        // Check for throttle middleware on media upload
        $this->assertStringContainsString("->middleware('throttle:media_upload')", $this->routesFileContent,
            "Throttle middleware should be applied to media upload");
    }

    /**
     * @test
     */
    public function route_names_are_properly_assigned()
    {
        $namedRoutes = [
            "->name('config')",
            "->name('forgot-password')",
            "->name('reset-password')",
            "->name('verify')",
            "->name('provider.config')",
            "->name('auth.public-api')",
            "->name('check-latest-app-version')",
            "->name('vendor.login')",
            "->name('resident-availability-show')",
            "->name('resident-availability-store')",
            "->name('vendor_onboarding.invite')",
        ];

        foreach ($namedRoutes as $namedRoute) {
            $this->assertStringContainsString($namedRoute, $this->routesFileContent,
                "Named route should be defined: {$namedRoute}");
        }
    }

    /**
     * Extract public routes from file content
     */
    private function extractPublicRoutes(): void
    {
        // Implementation for parsing public routes
        $this->routeDefinitions['public_routes'] = [];
    }

    /**
     * Extract authenticated routes from file content
     */
    private function extractAuthenticatedRoutes(): void
    {
        // Implementation for parsing authenticated routes
        $this->routeDefinitions['authenticated_routes'] = [];
    }

    /**
     * Extract middleware groups from file content
     */
    private function extractMiddlewareGroups(): void
    {
        // Implementation for parsing middleware groups
        $this->routeDefinitions['middleware_groups'] = [];
    }

    /**
     * Extract route groups from file content
     */
    private function extractRouteGroups(): void
    {
        // Implementation for parsing route groups
        $this->routeDefinitions['route_groups'] = [];
    }
}

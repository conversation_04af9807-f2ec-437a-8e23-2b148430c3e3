<?php

declare(strict_types=1);

use App\Http\Resources\WorkOrderIssue\WorkOrderIssueListResource;
use App\Models\Issue;
use App\Models\Organization;
use App\Models\ProblemCategory;
use App\Models\ProblemDiagnosis;
use App\Models\ProblemSubCategory;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use Illuminate\Http\Request;

test('WorkOrderIssueListResource transforms a single WorkOrderIssue correctly', function () {
    $problemCategory = ProblemCategory::factory()->create([
        'problem_category_uuid' => fake()->uuid(),
        'label' => fake()->word(),
    ]);

    $problemSubCategory = ProblemSubCategory::factory()->create([
        'problem_sub_category_uuid' => fake()->uuid(),
        'label' => fake()->word(),
    ]);
    $problemSubCategory->setRelation('problemCategory', $problemCategory);

    $problemDiagnosis = ProblemDiagnosis::factory()->create([
        'problem_diagnosis_uuid' => fake()->uuid(),
        'label' => fake()->word(),
    ]);
    $problemDiagnosis->setRelation('subCategory', $problemSubCategory);

    $organization = Organization::factory()->create();

    $serviceRequest = ServiceRequest::factory()
        ->for(Property::factory()->for($organization))
        ->for($organization)
        ->create();

    $issue = Issue::factory()->create([
        'issue_uuid' => fake()->uuid(),
        'problem_diagnosis_id' => $problemDiagnosis->problem_diagnosis_id,
        'service_request_id' => $serviceRequest->service_request_id,
        'organization_id' => $organization->organization_id,
    ]);
    $issue->setRelation('problemDiagnosis', $problemDiagnosis);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for(Property::factory()->for($organization))
        ->create();

    $workOrderIssue = WorkOrderIssue::factory()->create([
        'organization_id' => $organization->organization_id,
        'issue_id' => $issue->issue_id,
        'work_order_id' => $workOrder->work_order_id,
    ]);

    $workOrderIssue->setRelation('issue', $issue);

    $resource = new WorkOrderIssueListResource($workOrderIssue);

    $request = Request::create('/');

    $responseArray = $resource->toArray($request);

    expect($responseArray)->toBeArray()->toHaveKey('issue_id');
    expect($responseArray['issue_id'])->toBe($workOrderIssue->work_order_issue_uuid);
});

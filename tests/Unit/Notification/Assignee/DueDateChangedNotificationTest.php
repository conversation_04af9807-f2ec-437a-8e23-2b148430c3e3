<?php

use App\Channels\DatabaseChannel;
use App\Models\Country;
use App\Models\Organization;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\State;
use App\Models\Timezone;
use App\Models\User;
use App\Models\WorkOrder;
use App\Notifications\WorkOrder\DueDateChangedNotification;
use App\States\ServiceRequests\Scoping;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Notifications\Messages\BroadcastMessage;

test('via returns expected channels', function () {
    $notification = new DueDateChangedNotification(1, 2);
    $notifiable = User::factory()->create();

    expect($notification->via($notifiable))
        ->toEqual([DatabaseChannel::class, 'broadcast']);
});

test('database type returns expected string', function () {
    $notification = new DueDateChangedNotification(1, 2);
    $notifiable = User::factory()->create();

    expect($notification->databaseType($notifiable))
        ->toBe('work_orders.due_date.changed');
});

test('broadcast type returns expected string', function () {
    $notification = new DueDateChangedNotification(1, 2);

    expect($notification->broadcastType())
        ->toBe('work_orders.due_date.changed');
});

test('broadcast as returns expected string', function () {
    $notification = new DueDateChangedNotification(1, 2);

    expect($notification->broadcastAs())
        ->toBe('notification.work_orders.due_date.changed');
});

test('getWorkOrder method returns proper work order', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create([
            'state' => Scoping::class,
        ]);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'state' => WorkInProgress::class,
            'due_date' => CarbonImmutable::now(),
        ]);

    $notification = new DueDateChangedNotification($workOrder->work_order_id, 1);
    $result = $notification->getWorkOrder();
    expect($result)->toBeInstanceOf(WorkOrder::class)
        ->and($result->work_order_uuid)->toBe($workOrder->work_order_uuid)
        ->and($result->work_order_id)->toBe($workOrder->work_order_id)
        ->and($result->work_order_number)->toBe($workOrder->work_order_number)
        ->and($result->due_date)->not->toBeNull();
});

test('throws exception if getWorkOrder does not exist', function () {
    $notification = new DueDateChangedNotification(16646665656565656, ************);

    $notification->getWorkOrder();
})->throws(ModelNotFoundException::class);

test('returns property address as array when property exists', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create([
            'street_address' => '123 Main St',
            'unit_number' => 'Apt 4',
            'city' => 'Testville',
            'postal_zip_code' => '12345',
        ]);

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create([
            'state' => Scoping::class,
        ]);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'state' => WorkInProgress::class,
        ]);

    $notification = new DueDateChangedNotification($workOrder->work_order_id, 1);

    $address = $notification->getPropertyAddress();

    expect($address)->toBeArray()
        ->and($address)->toHaveKey('street_address', '123 Main St')
        ->and($address)->toHaveKey('apt_suite_unit', 'Apt 4')
        ->and($address)->toHaveKey('city', 'Testville')
        ->and($address)->toHaveKey('zip_code', '12345');
});

test('returns empty array if serviceRequest is missing', function () {
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create([
            'street_address' => '123 Main St',
            'unit_number' => 'Apt 4',
            'city' => 'Testville',
            'postal_zip_code' => '12345',
        ]);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($property)
        ->create([
            'state' => WorkInProgress::class,
            'service_request_id' => null,
        ]);

    $notification = new DueDateChangedNotification($workOrder->work_order_id, 1);

    $address = $notification->getPropertyAddress();

    expect($address)->toBeArray()->toBeEmpty();
});

test('getUser returns proper user', function () {
    $user = User::factory()->create([
        'first_name' => 'test',
        'middle_name' => 'a',
        'last_name' => 'user',
    ]);

    $notification = new DueDateChangedNotification(1, $user->user_id);
    $result = $notification->getUser();

    expect($result)->toBeInstanceOf(User::class)
        ->and($result->user_id)->toBe($user->user_id)
        ->and($result->user_uuid)->toBe($user->user_uuid)
        ->and($result->first_name)->toBe($user->first_name)
        ->and($result->last_name)->toBe($user->last_name)
        ->and($result->middle_name)->toBe($user->middle_name);
});

test('throws exception if work order does not exist', function () {
    $notification = new DueDateChangedNotification(415545554, ************);

    $notification->getUser();
})->throws(ModelNotFoundException::class);

test('returns correct broadcast message in toBroadcast', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create([
            'state' => Scoping::class,
        ]);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'state' => WorkInProgress::class,
            'priority' => 'high',
        ]);

    $user = User::factory()->create();

    $notification = new DueDateChangedNotification($workOrder->work_order_id, $user->user_id);

    $broadcast = $notification->toBroadcast($user);

    expect($broadcast)->toBeInstanceOf(BroadcastMessage::class);
    expect($broadcast->data['work_order'])->toHaveKeys(['work_order_id', 'address', 'work_order_number']);
    expect($broadcast->data['user'])->toHaveKeys(['user_id', 'name']);
    expect($broadcast->data['data'])->toHaveKey('due_date');
    expect($broadcast->data['type'])->toBe('work_orders.due_date.changed');
});

<?php

use App\Enums\UserTypes;
use App\Models\Country;
use App\Models\Issue;
use App\Models\IssueStatus;
use App\Models\Organization;
use App\Models\ProblemDiagnosis;
use App\Models\Property;
use App\Models\Resident;
use App\Models\ServiceRequest;
use App\Models\State;
use App\Models\Technician;
use App\Models\TechnicianAppointment;
use App\Models\Timezone;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\Models\WorkOrderIssueStatus;
use App\Models\WorkOrderServiceCall;
use App\Notifications\Resident\WorkOrder\EnRouteNotification;
use App\States\Issue\Assigned;
use App\States\ServiceCalls\Working;
use App\States\ServiceRequests\Scoping;
use App\States\WorkOrderIssue\Unresolved;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use NotificationChannels\Twilio\TwilioChannel;
use NotificationChannels\Twilio\TwilioSmsMessage;

test('Returns TwilioChannel in via', function () {
    $notification = new EnRouteNotification(1);
    $resident = Mockery::mock(Resident::class);

    $channels = $notification->via($resident);

    expect($channels)->toContain(TwilioChannel::class);
});

test('Returns correct message in toTwilio', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create([
            'state' => Scoping::class,
        ]);

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create([
            'label' => Assigned::$name,
            'slug' => Assigned::$name,
        ]);
    }

    if (! WorkOrderIssueStatus::where('slug', Unresolved::$name)->count()) {
        WorkOrderIssueStatus::factory()->create([
            'label' => Unresolved::$name,
            'slug' => Unresolved::$name,
        ]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    $issue = Issue::factory()
        ->for($organization)
        ->for($problemDiagnosis)
        ->for($serviceRequest)
        ->create([
            'state' => Assigned::class,
        ]);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'state' => WorkInProgress::class,
        ]);

    WorkOrderIssue::factory()->create([
        'work_order_id' => $workOrder->work_order_id,
        'issue_id' => $issue->issue_id,
        'organization_id' => $organization->organization_id,
        'state' => Unresolved::class,
    ]);

    $techUser = User::factory()
        ->for($timezone)
        ->for($organization)
        ->create([
            'user_type' => UserTypes::TECHNICIAN(),
        ]);

    $technician = Technician::factory()->create([
        'organization_id' => $organization->organization_id,
        'user_id' => $techUser->user_id,
    ]);

    $appointment = TechnicianAppointment::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_id' => $technician->technician_id,
        'work_order_id' => $workOrder->work_order_id,
        'actual_start_time' => CarbonImmutable::now(),
    ]);

    WorkOrderServiceCall::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_appointment_id' => $appointment->technician_appointment_id,
        'work_order_id' => $workOrder->work_order_id,
        'timer_paused_at' => CarbonImmutable::now(),
        'timer_resumed_at' => CarbonImmutable::now()->subMinutes(15),
        'scheduled_start_time' => CarbonImmutable::parse('2025-07-19 08:00:00'),
        'scheduled_end_time' => CarbonImmutable::parse('2025-07-19 10:00:00'),
        'state' => Working::class,
    ]);

    // Fetch work order timezone and convert start and end times to that timezone
    $timezone = $workOrder->timezone->name ?? config('settings.default_timezone');
    $scheduledStartTime = CarbonImmutable::parse('2025-07-19 08:00:00')->setTimezone($timezone);
    $scheduledEndTime = CarbonImmutable::parse('2025-07-19 10:00:00')->setTimezone($timezone);
    $arrivalTime = $scheduledStartTime->format('D, M j') . ', ' . $scheduledStartTime->format('g A') . '-' . $scheduledEndTime->format('g A');

    $notification = new EnRouteNotification($workOrder->work_order_id);

    $resident = new Resident;
    $resident->first_name = 'Test';

    $message = $notification->toTwilio($resident);

    $expectedContent = 'Hello Test, Your technician is now en route to your location for the scheduled service. They are expected to arrive at ' . $arrivalTime;

    expect($message)->toBeInstanceOf(TwilioSmsMessage::class)
        ->and($message->content)->toBe($expectedContent);
});

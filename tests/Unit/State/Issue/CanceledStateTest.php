<?php

declare(strict_types=1);

use App\Enums\IssueActions;
use App\Models\Issue;
use App\States\Issue\Canceled;

test('has the correct name property', function () {
    expect(Canceled::$name)->toBe('canceled');
});

test('has the correct action name property', function () {
    expect(Canceled::$actionName)->toBe('canceled');
});

test('returns the correct label', function () {
    $issue = Issue::factory()->make();
    $canceled = new Canceled($issue);
    expect($canceled->label())->toBe('Canceled');
});

test('returns the correct actions', function () {
    $issue = Issue::factory()->make();
    $canceled = new Canceled($issue);

    $expectedActions = [
        IssueActions::EDIT(),
        IssueActions::DELETE(),
        IssueActions::RESTORE(),
    ];

    $resultActions = $canceled->actions();

    sort($expectedActions);
    sort($resultActions);
    expect($expectedActions)->toMatchArray($resultActions);
});

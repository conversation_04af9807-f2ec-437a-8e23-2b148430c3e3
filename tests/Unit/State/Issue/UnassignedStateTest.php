<?php

declare(strict_types=1);

use App\Enums\IssueActions;
use App\Models\Issue;
use App\States\Issue\Unassigned;

test('has the correct name property', function () {
    expect(Unassigned::$name)->toBe('unassigned');
});

test('has the correct action name property', function () {
    expect(Unassigned::$actionName)->toBe('unassigned');
});

test('returns the correct label', function () {
    $issue = Issue::factory()->make();
    $unassign = new Unassigned($issue);
    expect($unassign->label())->toBe('Unassigned');
});

test('returns the correct actions', function () {
    $issue = Issue::factory()->make();
    $unassign = new Unassigned($issue);

    $expectedActions = [
        IssueActions::EDIT(),
        IssueActions::ASSIGN(),
        IssueActions::DELETE(),
        IssueActions::CANCEL(),
        IssueActions::ADD_NEW_WORK_ORDER(),
    ];

    $resultActions = $unassign->actions();

    sort($expectedActions);
    sort($resultActions);
    expect($expectedActions)->toMatchArray($resultActions);
});

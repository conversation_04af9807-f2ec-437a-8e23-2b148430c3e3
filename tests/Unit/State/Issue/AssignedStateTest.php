<?php

declare(strict_types=1);

use App\Enums\IssueActions;
use App\Models\Issue;
use App\States\Issue\Assigned;

test('has the correct name property', function () {
    expect(Assigned::$name)->toBe('assigned');
});

test('has the correct action name property', function () {
    expect(Assigned::$actionName)->toBe('assigned');
});

test('returns the correct label', function () {
    $issue = Issue::factory()->make();
    $assigned = new Assigned($issue);
    expect($assigned->label())->toBe('Assigned');
});

test('returns the correct actions', function () {
    $issue = Issue::factory()->make();
    $assigned = new Assigned($issue);

    $expectedActions = [
        IssueActions::EDIT(),
        IssueActions::UNASSIGN(),
        IssueActions::DELETE(),
        IssueActions::ADD_NEW_WORK_ORDER(),
        IssueActions::CANCEL(),
        IssueActions::ASSIGN(),
    ];

    $resultActions = $assigned->actions();

    sort($expectedActions);
    sort($resultActions);
    expect($expectedActions)->toMatchArray($resultActions);
});

<?php

declare(strict_types=1);

use App\Enums\WorkOrderIssueActions;
use App\Models\WorkOrderIssue;
use App\States\WorkOrderIssue\Canceled;

test('has the correct name property', function () {
    expect(Canceled::$name)->toBe('canceled');
});

test('has the correct action name property', function () {
    expect(Canceled::$actionName)->toBe('canceled');
});

test('returns the correct label', function () {
    $workOrderIssue = WorkOrderIssue::factory()->make();
    $canceled = new Canceled($workOrderIssue);
    expect($canceled->label())->toBe('Canceled');
});

test('returns the correct actions', function () {
    $workOrderIssue = WorkOrderIssue::factory()->make();
    $canceled = new Canceled($workOrderIssue);

    $expectedActions = [
        WorkOrderIssueActions::EDIT(),
        WorkOrderIssueActions::UNASSIGN(),
    ];

    $resultActions = $canceled->actions();

    sort($expectedActions);
    sort($resultActions);
    expect($expectedActions)->toMatchArray($resultActions);
});

<?php

declare(strict_types=1);

use App\Enums\WorkOrderIssueActions;
use App\Models\WorkOrderIssue;
use App\States\WorkOrderIssue\Assigned;

test('has the correct name property', function () {
    expect(Assigned::$name)->toBe('assigned');
});

test('has the correct action name property', function () {
    expect(Assigned::$actionName)->toBe('assigned');
});

test('returns the correct label', function () {
    $workOrderIssue = WorkOrderIssue::factory()->make();
    $assigned = new Assigned($workOrderIssue);

    expect($assigned->label())->toBe('Assigned');
});

test('returns the correct actions', function () {
    $workOrderIssue = WorkOrderIssue::factory()->make();
    $assigned = new Assigned($workOrderIssue);

    $expectedActions = [
        WorkOrderIssueActions::EDIT(),
        WorkOrderIssueActions::UNASSIGN(),
        WorkOrderIssueActions::ASSIGN(),
        WorkOrderIssueActions::MARK_AS_DONE(),
        WorkOrderIssueActions::DECLINE(),
    ];

    $resultActions = $assigned->actions();

    sort($expectedActions);
    sort($resultActions);
    expect($expectedActions)->toMatchArray($resultActions);
});

<?php

namespace Tests\Unit\Services;

use App\Models\User;
use App\Services\SortService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

/**
 * @group ENG-5636-be-vendor-portal-feature-filters
 */
class SortServiceTest extends TestCase
{
    use DatabaseTransactions;

    protected SortService $sortService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->sortService = new SortService();
    }

    public function testRegisterSortFieldMappings()
    {
        // Register custom sort field mappings
        $this->sortService->registerSortFieldMappings('test_entity', [
            'test_field' => 'test_column'
        ]);

        // Get the registered mappings
        $mappings = $this->sortService->getSortFieldMappings('test_entity');

        // Assert that the mappings include our test field
        $this->assertArrayHasKey('test_field', $mappings);
        $this->assertEquals('test_column', $mappings['test_field']);
    }

    public function testGetSortFieldMappings()
    {
        // Register custom sort field mappings
        $this->sortService->registerSortFieldMappings('test_entity', [
            'test_field' => 'test_column'
        ]);

        // Get the registered mappings
        $mappings = $this->sortService->getSortFieldMappings('test_entity');

        // Assert that the mappings include our test field
        $this->assertArrayHasKey('test_field', $mappings);
        $this->assertEquals('test_column', $mappings['test_field']);

        // Get mappings for a non-existent entity type
        $mappings = $this->sortService->getSortFieldMappings('non_existent_entity');

        // Assert that the mappings are an empty array
        $this->assertEmpty($mappings);
    }

    public function testGetDefaultSortField()
    {
        // Set a default sort field
        $this->sortService->setDefaultSortField('test_entity', 'test_field');

        // Get the default sort field
        $defaultField = $this->sortService->getDefaultSortField('test_entity');

        // Assert that the default field is correct
        $this->assertEquals('test_field', $defaultField);

        // Get a non-existent default field
        $defaultField = $this->sortService->getDefaultSortField('non_existent_entity');

        // Assert that the default field is '-created_at'
        $this->assertEquals('-created_at', $defaultField);
    }

    public function testApplySorting()
    {
        // Create a mock query builder
        $query = User::query();

        // Register sort field mappings
        $this->sortService->registerSortFieldMappings('user', [
            'name' => 'users.first_name'
        ]);

        // Apply sorting
        $result = $this->sortService->applySorting($query, 'name', 'user');

        // Assert that the query has an order by clause
        $this->assertStringContainsString(
            'order by',
            $result->toSql()
        );

        // Apply descending sorting
        $result = $this->sortService->applySorting($query, '-name', 'user');

        // Assert that the query has a descending order by clause
        $this->assertStringContainsString(
            'order by',
            $result->toSql()
        );

        // Apply sorting with a space prefix (should be treated as ascending)
        $result = $this->sortService->applySorting($query, ' name', 'user');

        // Assert that the query has an order by clause
        $this->assertStringContainsString(
            'order by',
            $result->toSql()
        );
    }

    public function testPrepareSortValue()
    {
        // Set a default sort field
        $this->sortService->setDefaultSortField('user', 'name');

        // Prepare sort value with no input
        $sortValue = $this->sortService->prepareSortValue(null, null, 'user');

        // Assert that the sort value is the default
        $this->assertEquals('name', $sortValue);

        // Prepare sort value with input
        $sortValue = $this->sortService->prepareSortValue('email', null, 'user');

        // Assert that the sort value is the input
        $this->assertEquals('email', $sortValue);

        // Prepare sort value with group
        $sortValue = $this->sortService->prepareSortValue(null, 'status', 'user');

        // Assert that the sort value includes the group
        $this->assertEquals('status,name', $sortValue);

        // Prepare sort value with both
        $sortValue = $this->sortService->prepareSortValue('email', 'status', 'user');

        // Assert that the sort value includes both
        $this->assertEquals('status,email', $sortValue);
    }

    public function testPrepareSortValueForWorkOrderWithGroup()
    {
        // Test special case for work orders with group_type
        $sortValue = $this->sortService->prepareSortValue('due_date', 'status', 'work_order');

        // Assert that the sort value is 'status_group,-state_updated' for work orders with group
        $this->assertEquals('status_group,-state_updated', $sortValue);

        // Test special case for work orders with status group
        $sortValue = $this->sortService->prepareSortValue('due_date', 'status', 'vendor_work_order');

        // Assert that the sort value includes status_group for vendor work orders
        $this->assertEquals('status_group,due_date', $sortValue);
    }

    public function testPrepareSortValueForWorkOrderWithHealthScore()
    {
        // Test special case for work orders with health_score sorting (ascending)
        $sortValue = $this->sortService->prepareSortValue('health_score', null, 'work_order');

        // Assert that the sort value is the expected health score fields
        $this->assertEquals('health_score_weight,-health_score_elapse_time', $sortValue);

        // Test special case for work orders with health_score sorting (descending)
        $sortValue = $this->sortService->prepareSortValue('-health_score', null, 'work_order');

        // Assert that the sort value is the expected health score fields
        $this->assertEquals('-health_score_weight,-health_score_elapse_time', $sortValue);
    }

    public function testPrepareSortValueForWorkOrderWithHealthScoreGrouping()
    {
        // Test special case for work orders with health_score grouping
        $sortValue = $this->sortService->prepareSortValue(null, 'health_score', 'work_order');

        // Assert that the sort value is the expected health score fields
        $this->assertEquals('-health_score_weight,health_score_elapse_time', $sortValue);

        // Test special case for work orders with health_score grouping and custom sort
        $sortValue = $this->sortService->prepareSortValue('due_date', 'health_score', 'work_order');

        // Assert that the sort value includes the custom sort field
        $this->assertEquals('-health_score_weight,due_date,health_score_elapse_time', $sortValue);

        // Test special case for work orders with health_score grouping and health_score sort
        $sortValue = $this->sortService->prepareSortValue('health_score', 'health_score', 'work_order');

        // Assert that the sort value is the expected health score fields
        $this->assertEquals('-health_score_weight,health_score_elapse_time', $sortValue);
    }

    public function testApplySortingWithMultipleFields()
    {
        // Create a mock query builder
        $query = User::query();

        // Register sort field mappings
        $this->sortService->registerSortFieldMappings('user', [
            'name' => 'users.first_name',
            'email' => 'users.email'
        ]);

        // Apply sorting with multiple fields
        $result = $this->sortService->applySorting($query, 'name,email', 'user');

        // Assert that the query has multiple order by clauses
        $sql = $result->toSql();
        $this->assertStringContainsString('order by', $sql);
    }

    public function testApplySortingWithNonExistentField()
    {
        // Create a mock query builder
        $query = User::query();

        // Apply sorting with a non-existent field
        $result = $this->sortService->applySorting($query, 'non_existent_field', 'user');

        // Assert that the query is unchanged (no order by clause for the non-existent field)
        $this->assertStringNotContainsString(
            'order by `non_existent_field`',
            $result->toSql()
        );
    }

    /**
     * @group skip
     */
    public function testApplySortingWithEmptySortValue()
    {
        // This test is skipped because the SortService doesn't handle empty sort values correctly
        // In practice, empty sort values are handled by the prepareSortValue method
        $this->markTestSkipped('SortService doesn\'t handle empty sort values directly');
    }
}

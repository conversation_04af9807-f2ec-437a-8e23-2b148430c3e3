<?php

namespace Tests\Unit\Services;

use App\Models\User;
use App\Models\WorkOrder;
use App\Services\AssigneeFilterService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Mockery;
use ReflectionMethod;
use Tests\TestCase;

class AssigneeFilterServiceTest extends TestCase
{
    protected AssigneeFilterService $assigneeFilterService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->assigneeFilterService = new AssigneeFilterService;
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * @test
     */
    public function uuidConversionInApplyWorkOrderAssigneeFilter()
    {
        // Create a mock query builder
        $mockQuery = Mockery::mock(Builder::class);
        $mockQuery->shouldReceive('whereExists')->andReturnSelf();

        // Create a test UUID
        $testUuid = 'e73f87cf-5bfc-4ee1-86ed-c1b9d0349dc7';

        // Get the protected method using reflection
        $method = new ReflectionMethod(AssigneeFilterService::class, 'applyWorkOrderAssigneeFilter');
        $method->setAccessible(true);

        // Call the method with our test UUID
        $result = $method->invoke($this->assigneeFilterService, $mockQuery, [$testUuid], 'where');

        // If we get here without errors, the test passes
        $this->assertInstanceOf(Builder::class, $result);
    }

    /**
     * @test
     */
    public function applyAssigneeFilterWithAndOperation()
    {
        // Get existing users from the database to test with
        $users = User::limit(2)->get();

        if ($users->count() < 2) {
            $this->markTestSkipped('Need at least 2 users in database to run this test');
        }

        $userUuids = $users->pluck('user_uuid')->toArray();

        $payload = [
            'group_operation' => 'and',
            'column_operation' => 'is',
            'field' => 'assignee',
            'column_name' => 'assignee',
            'values' => $userUuids,
        ];

        $query = WorkOrder::query();
        $result = $this->assigneeFilterService->applyAssigneeFilter($query, $payload, 'uuid', 'work_order');

        $this->assertInstanceOf(Builder::class, $result);

        // Check that the query was modified (has where clauses)
        $sql = $result->toSql();
        $this->assertStringContainsString('exists', strtolower($sql));
    }

    /**
     * @test
     */
    public function applyAssigneeFilterWithOrOperation()
    {
        // Get existing users from the database to test with
        $users = User::limit(2)->get();

        if ($users->count() < 2) {
            $this->markTestSkipped('Need at least 2 users in database to run this test');
        }

        $userUuids = $users->pluck('user_uuid')->toArray();

        $payload = [
            'group_operation' => 'or',
            'column_operation' => 'is',
            'field' => 'assignee',
            'column_name' => 'assignee',
            'values' => $userUuids,
        ];

        $query = WorkOrder::query();
        $result = $this->assigneeFilterService->applyAssigneeFilter($query, $payload, 'uuid', 'work_order');

        $this->assertInstanceOf(Builder::class, $result);

        // Check that the query was modified (has where clauses)
        $sql = $result->toSql();
        $this->assertStringContainsString('exists', strtolower($sql));
    }

    /**
     * @test
     */
    public function workOrderAssigneeFilterUsesCorrectExistsMethodForAndOperation()
    {
        // Create a mock query builder that tracks method calls
        $mockQuery = Mockery::mock(Builder::class);
        $mockQuery->shouldReceive('whereExists')
            ->once()
            ->andReturnSelf();
        $mockQuery->shouldNotReceive('orWhereExists');

        // Get a real user UUID from database
        $user = User::first();
        if (! $user) {
            $this->markTestSkipped('Need at least 1 user in database to run this test');
        }

        $formattedUuid = $user->user_uuid;

        // Get the protected method using reflection
        $method = new ReflectionMethod(AssigneeFilterService::class, 'applyWorkOrderAssigneeFilter');
        $method->setAccessible(true);

        // Call with 'where' clause (AND operation)
        $result = $method->invoke($this->assigneeFilterService, $mockQuery, [$formattedUuid], 'where');

        $this->assertInstanceOf(Builder::class, $result);
    }

    /**
     * @test
     */
    public function workOrderAssigneeFilterUsesCorrectExistsMethodForOrOperation()
    {
        // Create a mock query builder that tracks method calls
        $mockQuery = Mockery::mock(Builder::class);
        $mockQuery->shouldReceive('orWhereExists')
            ->once()
            ->andReturnSelf();
        $mockQuery->shouldNotReceive('whereExists');

        // Get a real user UUID from database
        $user = User::first();
        if (! $user) {
            $this->markTestSkipped('Need at least 1 user in database to run this test');
        }

        $formattedUuid = $user->user_uuid;

        // Get the protected method using reflection
        $method = new ReflectionMethod(AssigneeFilterService::class, 'applyWorkOrderAssigneeFilter');
        $method->setAccessible(true);

        // Call with 'orWhere' clause (OR operation)
        $result = $method->invoke($this->assigneeFilterService, $mockQuery, [$formattedUuid], 'orWhere');

        $this->assertInstanceOf(Builder::class, $result);
    }

    /**
     * @test
     */
    public function resolveWhereClauseReturnsCorrectClauseForAndOperation()
    {
        $method = new ReflectionMethod(AssigneeFilterService::class, 'resolveWhereClause');
        $method->setAccessible(true);

        $result = $method->invoke($this->assigneeFilterService, 'and', 'uuid');

        $this->assertEquals('where', $result);
    }

    /**
     * @test
     */
    public function resolveWhereClauseReturnsCorrectClauseForOrOperation()
    {
        $method = new ReflectionMethod(AssigneeFilterService::class, 'resolveWhereClause');
        $method->setAccessible(true);

        $result = $method->invoke($this->assigneeFilterService, 'or', 'uuid');

        $this->assertEquals('orWhere', $result);
    }

    /**
     * @test
     */
    public function applyAssigneeFilterWithIsNotOperation()
    {
        $this->markTestSkipped('Skipping this test pending functional review or business validation.');

        // Get existing users from the database to test with
        $users = User::limit(2)->get();

        if ($users->count() < 2) {
            $this->markTestSkipped('Need at least 2 users in database to run this test');
        }

        $userUuids = $users->pluck('user_uuid')->toArray();

        $payload = [
            'group_operation' => 'and',
            'column_operation' => 'is_not',
            'field' => 'assignee',
            'column_name' => 'assignee',
            'values' => $userUuids,
        ];

        $query = WorkOrder::query();
        $result = $this->assigneeFilterService->applyAssigneeFilter($query, $payload, 'not_uuid', 'work_order');

        $this->assertInstanceOf(Builder::class, $result);

        // Check that the query was modified (has where clauses)
        $sql = $result->toSql();
        $this->assertStringContainsString('not in', strtolower($sql));
    }

    /**
     * @test
     */
    public function applyAssigneeFilterWithUnassignedValue()
    {
        $payload = [
            'group_operation' => 'and',
            'column_operation' => 'is',
            'field' => 'assignee',
            'column_name' => 'assignee',
            'values' => ['unassigned'],
        ];

        $query = WorkOrder::query();
        $result = $this->assigneeFilterService->applyAssigneeFilter($query, $payload, 'uuid', 'work_order');

        $this->assertInstanceOf(Builder::class, $result);

        // Check that the query was modified (has where clauses)
        $sql = $result->toSql();
        $this->assertStringContainsString('not exists', strtolower($sql));
    }
}

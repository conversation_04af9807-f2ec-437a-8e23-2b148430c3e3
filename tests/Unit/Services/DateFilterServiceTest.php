<?php

namespace Tests\Unit\Services;

use App\Enums\DateFilterType;
use App\Enums\DateOperation;
use App\Models\User;
use App\Services\DateFilterService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use ReflectionClass;
use Tests\TestCase;
use UnexpectedValueException;

/**
 * @group ENG-5636-be-vendor-portal-feature-filters
 */
class DateFilterServiceTest extends TestCase
{
    use DatabaseTransactions;

    protected DateFilterService $dateFilterService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->dateFilterService = new DateFilterService;
    }

    /**
     * @test
     */
    public function applyDateFilterWithSingleDate()
    {
        // Create a mock query builder
        $query = User::query();

        // Create a payload for a single date filter
        $payload = [
            'group_operation' => 'and',
            'column_operation' => 'is',
            'field' => 'created_at',
            'column_name' => 'created_at',
            'values' => ['today'],
        ];

        // Apply date filter
        $result = $this->dateFilterService->applyDateFilter($query, $payload);

        // Assert that the result is a Builder instance
        $this->assertInstanceOf(Builder::class, $result);

        // Assert that the query has a where clause
        $sql = $result->toSql();
        $this->assertStringContainsString('where', $sql);
        $this->assertStringContainsString('date(`created_at`)', $sql);
    }

    /**
     * @test
     */
    public function applyDateFilterWithDateBetween()
    {
        // Create a mock query builder
        $query = User::query();

        // Create a payload for a date range filter
        $payload = [
            'group_operation' => 'and',
            'column_operation' => 'is',
            'field' => 'created_at',
            'column_name' => 'created_at',
            'values' => ['this_week'],
        ];

        // Apply date filter
        $result = $this->dateFilterService->applyDateFilter($query, $payload);

        // Assert that the result is a Builder instance
        $this->assertInstanceOf(Builder::class, $result);

        // Assert that the query has a between clause
        $sql = $result->toSql();
        $this->assertStringContainsString('between', $sql);
        $this->assertStringContainsString('`created_at`', $sql);
    }

    /**
     * @test
     */
    public function applyDateFilterWithDateNotBetween()
    {
        // Create a mock query builder
        $query = User::query();

        // Create a payload for a date range filter with is_not operation
        $payload = [
            'group_operation' => 'and',
            'column_operation' => 'is_not',
            'field' => 'created_at',
            'column_name' => 'created_at',
            'values' => ['this_month'],
        ];

        // Apply date filter
        $result = $this->dateFilterService->applyDateFilter($query, $payload);

        // Assert that the result is a Builder instance
        $this->assertInstanceOf(Builder::class, $result);

        // Assert that the query has a not between clause
        $sql = $result->toSql();
        $this->assertStringContainsString('not between', $sql);
        $this->assertStringContainsString('`created_at`', $sql);
    }

    /**
     * @test
     */
    public function applyDateFilterWithOrOperation()
    {
        // Create a mock query builder with an existing where clause
        $query = User::query()->where('id', '>', 0);

        // Create a payload for a date filter with OR operation
        $payload = [
            'group_operation' => 'or',
            'column_operation' => 'is',
            'field' => 'created_at',
            'column_name' => 'created_at',
            'values' => ['this_week'],
        ];

        // Apply date filter
        $result = $this->dateFilterService->applyDateFilter($query, $payload);

        // Assert that the result is a Builder instance
        $this->assertInstanceOf(Builder::class, $result);

        // Assert that the query has an or between clause
        $sql = $result->toSql();
        $this->assertStringContainsString('or', $sql);
        $this->assertStringContainsString('between', $sql);
    }

    /**
     * @test
     */
    public function applyDateFilterWithInvalidOperation()
    {
        // Create a mock query builder
        $query = User::query();

        // Create a payload with an invalid operation
        $payload = [
            'group_operation' => 'and',
            'column_operation' => 'invalid_operation',
            'field' => 'created_at',
            'column_name' => 'created_at',
            'values' => ['today'],
        ];

        // Expect an exception
        $this->expectException(UnexpectedValueException::class);
        $this->expectExceptionMessage('Invalid date operation: invalid_operation');

        // Apply date filter
        $this->dateFilterService->applyDateFilter($query, $payload);
    }

    /**
     * @test
     */
    public function applyDateFilterWithInvalidDateOperation()
    {
        // Create a mock query builder
        $query = User::query();

        // Create a payload with a valid operation but manipulate the internal operation
        $payload = [
            'group_operation' => 'and',
            'column_operation' => 'is',
            'field' => 'created_at',
            'column_name' => 'created_at',
            'values' => ['today'],
        ];

        // Mock the resolveOperatorSetForDateSlug method to return an invalid operation
        $dateFilterService = $this->getMockBuilder(DateFilterService::class)
            ->onlyMethods(['resolveOperatorSetForDateSlug'])
            ->getMock();

        $dateFilterService->method('resolveOperatorSetForDateSlug')
            ->willReturn([
                'operation' => 'invalid_operation',
                'operator' => '=',
                'value' => '2023-01-01',
            ]);

        // Expect an exception
        $this->expectException(UnexpectedValueException::class);
        $this->expectExceptionMessage('Unexpected date operation [invalid_operation] provided.');

        // Apply date filter
        $dateFilterService->applyDateFilter($query, $payload);
    }

    /**
     * @test
     */
    public function dateValuesForIsOperation()
    {
        // Test with single date slug
        $result = $this->dateFilterService->dateValuesForIsOperation('today');
        $this->assertEquals('date', $result['operation']);
        $this->assertEquals('=', $result['operator']);
        $this->assertIsString($result['value']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $result['value']);

        // Test with range date slug
        $result = $this->dateFilterService->dateValuesForIsOperation('this_week');
        $this->assertEquals('date_between', $result['operation']);
        $this->assertEquals('between', $result['operator']);
        $this->assertIsArray($result['value']);
        $this->assertCount(2, $result['value']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $result['value'][0]);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $result['value'][1]);
    }

    /**
     * @test
     */
    public function dateValuesForIsNotOperation()
    {
        // Test with single date slug
        $result = $this->dateFilterService->dateValuesForIsNotOperation('today');
        $this->assertEquals('date', $result['operation']);
        $this->assertEquals('!=', $result['operator']);
        $this->assertIsString($result['value']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $result['value']);

        // Test with range date slug
        $result = $this->dateFilterService->dateValuesForIsNotOperation('this_week');
        $this->assertEquals('date_not_between', $result['operation']);
        $this->assertEquals('not between', $result['operator']);
        $this->assertIsArray($result['value']);
        $this->assertCount(2, $result['value']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $result['value'][0]);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $result['value'][1]);
    }

    /**
     * @test
     */
    public function dateValuesForIsAfterOperation()
    {
        // Test with single date slug
        $result = $this->dateFilterService->dateValuesForIsAfterOperation('today');
        $this->assertEquals('date', $result['operation']);
        $this->assertEquals('>', $result['operator']);
        $this->assertIsString($result['value']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $result['value']);

        // Test with range date slug
        $result = $this->dateFilterService->dateValuesForIsAfterOperation('this_week');
        $this->assertEquals('date', $result['operation']);
        $this->assertEquals('>', $result['operator']);
        $this->assertIsString($result['value']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $result['value']);
    }

    /**
     * @test
     */
    public function dateValuesForIsBeForeOperation()
    {
        // Test with single date slug
        $result = $this->dateFilterService->dateValuesForIsBeForeOperation('today');
        $this->assertEquals('date', $result['operation']);
        $this->assertEquals('<', $result['operator']);
        $this->assertIsString($result['value']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $result['value']);

        // Test with range date slug
        $result = $this->dateFilterService->dateValuesForIsBeForeOperation('this_week');
        $this->assertEquals('date', $result['operation']);
        $this->assertEquals('<', $result['operator']);
        $this->assertIsString($result['value']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $result['value']);
    }

    /**
     * @test
     */
    public function dateOperationResponse()
    {
        // Test single value response
        $result = $this->dateFilterService->dateOperationResponse(DateFilterType::DATE, '=', '2023-01-01');
        $this->assertEquals('date', $result['operation']);
        $this->assertEquals('=', $result['operator']);
        $this->assertEquals('2023-01-01', $result['value']);

        // Test range value response
        $result = $this->dateFilterService->dateOperationResponse(DateFilterType::DATE_BETWEEN, 'between', '2023-01-01', '2023-01-31');
        $this->assertEquals('date_between', $result['operation']);
        $this->assertEquals('between', $result['operator']);
        $this->assertIsArray($result['value']);
        $this->assertEquals(['2023-01-01', '2023-01-31'], $result['value']);
    }

    /**
     * @test
     */
    public function resolveWhereClauseOperator()
    {
        // Test 'and' operation
        $result = $this->invokeMethod($this->dateFilterService, 'resolveWhereClauseOperator', ['and']);
        $this->assertEquals('where', $result);

        // Test 'or' operation
        $result = $this->invokeMethod($this->dateFilterService, 'resolveWhereClauseOperator', ['or']);
        $this->assertEquals('orWhere', $result);

        // Test invalid operation
        $this->expectException(UnexpectedValueException::class);
        $this->expectExceptionMessage('Unexpected group operation [invalid] provided.');
        $this->invokeMethod($this->dateFilterService, 'resolveWhereClauseOperator', ['invalid']);
    }

    /**
     * @test
     */
    public function getDateRangeFromSlug()
    {
        // Test with valid single date slug
        $result = $this->invokeMethod($this->dateFilterService, 'getDateRangeFromSlug', ['today']);
        $this->assertIsArray($result);
        $this->assertCount(3, $result);
        $this->assertEquals('single', $result[0]);
        $this->assertInstanceOf(Carbon::class, $result[1]);
        $this->assertNull($result[2]);

        // Test with valid range date slug
        $result = $this->invokeMethod($this->dateFilterService, 'getDateRangeFromSlug', ['this_week']);
        $this->assertIsArray($result);
        $this->assertCount(3, $result);
        $this->assertEquals('range', $result[0]);
        $this->assertInstanceOf(Carbon::class, $result[1]);
        $this->assertInstanceOf(Carbon::class, $result[2]);
    }

    /**
     * @test
     */
    public function getDateRangeFromSlugThrowsExceptionForInvalidSlug()
    {
        $this->expectException(UnexpectedValueException::class);
        $this->expectExceptionMessage('Unexpected date slug [invalid_slug] provided.');

        $this->invokeMethod($this->dateFilterService, 'getDateRangeFromSlug', ['invalid_slug']);
    }

    /**
     * @test
     */
    public function applyDateFilterWithInvalidGroupOperation()
    {
        // Create a mock query builder
        $query = User::query();

        // Create a payload with an invalid group operation
        $payload = [
            'group_operation' => 'invalid',
            'column_operation' => 'is',
            'field' => 'created_at',
            'column_name' => 'created_at',
            'values' => ['today'],
        ];

        // Expect an exception
        $this->expectException(UnexpectedValueException::class);
        $this->expectExceptionMessage('Unexpected group operation [invalid] provided.');

        // Apply date filter
        $this->dateFilterService->applyDateFilter($query, $payload);
    }

    /**
     * @test
     */
    public function resolveOperatorSetForDateSlug()
    {
        // Test IS operation
        $result = $this->dateFilterService->resolveOperatorSetForDateSlug('today', DateOperation::IS);
        $this->assertEquals('date', $result['operation']);
        $this->assertEquals('=', $result['operator']);
        $this->assertIsString($result['value']);

        // Test IS_NOT operation
        $result = $this->dateFilterService->resolveOperatorSetForDateSlug('today', DateOperation::IS_NOT);
        $this->assertEquals('date', $result['operation']);
        $this->assertEquals('!=', $result['operator']);
        $this->assertIsString($result['value']);

        // Test IS_AFTER operation
        $result = $this->dateFilterService->resolveOperatorSetForDateSlug('today', DateOperation::IS_AFTER);
        $this->assertEquals('date', $result['operation']);
        $this->assertEquals('>', $result['operator']);
        $this->assertIsString($result['value']);

        // Test IS_BEFORE operation
        $result = $this->dateFilterService->resolveOperatorSetForDateSlug('today', DateOperation::IS_BEFORE);
        $this->assertEquals('date', $result['operation']);
        $this->assertEquals('<', $result['operator']);
        $this->assertIsString($result['value']);

        // Test with date range
        $result = $this->dateFilterService->resolveOperatorSetForDateSlug('this_week', DateOperation::IS);
        $this->assertEquals('date_between', $result['operation']);
        $this->assertEquals('between', $result['operator']);
        $this->assertIsArray($result['value']);
        $this->assertCount(2, $result['value']);
    }

    /**
     * Helper method to invoke protected/private methods
     */
    protected function invokeMethod($object, string $methodName, array $parameters = [])
    {
        $reflection = new ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }
}

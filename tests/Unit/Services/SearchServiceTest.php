<?php

namespace Tests\Unit\Services;

use App\Models\User;
use App\Services\SearchService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

/**
 * @group ENG-5636-be-vendor-portal-feature-filters
 */
class SearchServiceTest extends TestCase
{
    use DatabaseTransactions;

    protected SearchService $searchService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->searchService = new SearchService();
    }

    public function testRegisterSearchHandler()
    {
        // Register a custom search handler
        $this->searchService->registerSearchHandler('test_entity', function ($query, $searchTerm) {
            return $query->where('test_column', 'like', "%{$searchTerm}%");
        });

        // Get the registered handler
        $handler = $this->searchService->getSearchHandler('test_entity');

        // Assert that the handler is callable
        $this->assertTrue(is_callable($handler));
    }

    public function testApplySearch()
    {
        // Create a mock query builder
        $query = User::query();

        // Register a test search handler
        $this->searchService->registerSearchHandler('test_entity', function ($query, $searchTerm) {
            return $query->where('test_column', 'like', "%{$searchTerm}%");
        });

        // Apply search
        $result = $this->searchService->applySearch($query, 'test', 'test_entity');

        // Assert that the query has a where clause
        $this->assertStringContainsString(
            'where `test_column` like ?',
            $result->toSql()
        );
    }

    public function testEmptySearchTermReturnsUnmodifiedQuery()
    {
        // Create a mock query builder
        $query = User::query();
        $originalSql = $query->toSql();

        // Apply empty search
        $result = $this->searchService->applySearch($query, '', 'test_entity');

        // Assert that the query is unchanged
        $this->assertEquals($originalSql, $result->toSql());
    }

    public function testDefaultSearchHandlersAreRegistered()
    {
        // Test that the default handlers are registered
        $this->assertNotNull($this->searchService->getSearchHandler('work_order'));
        $this->assertNotNull($this->searchService->getSearchHandler('vendor_work_order'));
        $this->assertNotNull($this->searchService->getSearchHandler('quote'));
        $this->assertNotNull($this->searchService->getSearchHandler('service_request'));
        $this->assertNotNull($this->searchService->getSearchHandler('user'));
        $this->assertNotNull($this->searchService->getSearchHandler('role'));
        $this->assertNotNull($this->searchService->getSearchHandler('tag'));
        $this->assertNotNull($this->searchService->getSearchHandler('vendor'));
    }
}

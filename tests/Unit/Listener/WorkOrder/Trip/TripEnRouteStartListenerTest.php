<?php

use App\Enums\UserTypes;
use App\Events\WorkOrder\ActivityLog\WorkOrderActivityLogCreated;
use App\Events\WorkOrder\Trip\TripEnRouteStart;
use App\Listeners\WorkOrder\Trip\TripEnRouteStartListener;
use App\Models\Country;
use App\Models\Issue;
use App\Models\IssueStatus;
use App\Models\Organization;
use App\Models\ProblemDiagnosis;
use App\Models\Property;
use App\Models\Resident;
use App\Models\ServiceRequest;
use App\Models\State;
use App\Models\Technician;
use App\Models\TechnicianAppointment;
use App\Models\Timezone;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\Models\WorkOrderIssueStatus;
use App\Models\WorkOrderServiceCall;
use App\Notifications\Resident\WorkOrder\EnRouteNotification;
use App\Services\WorkOrderActivity\Enums\ActivityLogEventTypes;
use App\States\Issue\Assigned;
use App\States\ServiceCalls\Scheduled as ServiceCallsScheduled;
use App\States\ServiceRequests\Scoping;
use App\States\WorkOrderIssue\Assigned as WorkOrderIssueAssigned;
use App\States\WorkOrders\Scheduled;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;

use function Pest\Laravel\assertDatabaseHas;

beforeEach(function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $this->organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $this->property = Property::factory()
        ->for($this->organization)
        ->create();

    // create a dummy service request
    $this->serviceRequest = ServiceRequest::factory()
        ->for($this->organization)
        ->for($this->property)
        ->for($timezone)
        ->for(
            Resident::factory()
                ->for($this->property)
                ->for($this->organization)
        )
        ->create([
            'state' => Scoping::class,
        ]);

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create([
            'label' => Assigned::$name,
            'slug' => Assigned::$name,
        ]);
    }

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create([
            'label' => WorkOrderIssueAssigned::$name,
            'slug' => WorkOrderIssueAssigned::$name,
        ]);
    }

    $this->problemDiagnosis = ProblemDiagnosis::factory()->create();

    $this->issue = Issue::factory()
        ->for($this->organization)
        ->for($this->problemDiagnosis)
        ->for($this->serviceRequest)
        ->create([
            'state' => Assigned::class,
        ]);

    $this->workOrder = WorkOrder::factory()
        ->for($this->organization)
        ->for($this->serviceRequest)
        ->for($this->property)
        ->create([
            'state' => Scheduled::class,
        ]);

    $this->workOrderIssue = WorkOrderIssue::factory()->create([
        'work_order_id' => $this->workOrder->work_order_id,
        'issue_id' => $this->issue->issue_id,
        'organization_id' => $this->organization->organization_id,
        'state' => WorkOrderIssueAssigned::class,
    ]);

    $this->techUser = User::factory()
        ->for($timezone)
        ->for($this->organization)
        ->create([
            'user_type' => UserTypes::TECHNICIAN(),
        ]);

    $this->technician = Technician::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'user_id' => $this->techUser->user_id,
    ]);

    $this->appointment = TechnicianAppointment::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'technician_id' => $this->technician->technician_id,
        'work_order_id' => $this->workOrder->work_order_id,
    ]);

    $this->trip = WorkOrderServiceCall::create([
        'organization_id' => $this->organization->organization_id,
        'work_order_id' => $this->workOrder->work_order_id,
        'technician_appointment_id' => $this->appointment->technician_appointment_id,
        'scheduled_start_time' => $this->appointment->scheduled_start_time,
        'scheduled_end_time' => $this->appointment->scheduled_end_time,
        'duration_minutes' => 30,
        'state' => ServiceCallsScheduled::class,
        'is_active' => 1,
    ]);
    Event::fake([WorkOrderActivityLogCreated::class]);
    Notification::fake();
});

test('creates activity log and broadcasts event', function () {
    $event = new TripEnRouteStart($this->trip->work_order_service_call_id, $this->techUser->user_id, ['foo' => 'bar'], false);

    $listener = new TripEnRouteStartListener;
    $listener->handle($event);

    assertDatabaseHas('work_order_activity_logs', [
        'work_order_id' => $this->trip->work_order_id,
        'organization_id' => $this->trip->organization_id,
        'triggered_by' => $this->techUser->user_id,
        'event' => ActivityLogEventTypes::WORK_ORDER_SERVICE_CALL_EN_ROUTE_INITIATED(),
    ]);

    Event::assertDispatched(WorkOrderActivityLogCreated::class);
});

test('Does not send resident notification if sendResidentNotification is false', function () {
    $event = new TripEnRouteStart($this->trip->work_order_service_call_id, $this->techUser->user_id, ['foo' => 'bar'], false);

    $listener = new TripEnRouteStartListener;
    $listener->handle($event);

    Notification::assertNothingSent();
});

test('Sends resident notification when enabled and resident has phone', function () {
    $event = new TripEnRouteStart($this->trip->work_order_service_call_id, $this->techUser->user_id, ['foo' => 'bar'], true);

    $listener = new TripEnRouteStartListener;
    $listener->handle($event);

    $resident = $this->serviceRequest->resident;
    $workOrder = $this->workOrder;

    Notification::assertSentTo(
        [$resident],
        EnRouteNotification::class,
        function ($notification) use ($workOrder) {
            return $notification->workOrderId === $workOrder->work_order_id;
        }
    );
});

test('does not send resident notification if resident has no phone', function () {
    $resident = $this->serviceRequest->resident;
    $resident->phone_number = null;
    $resident->save();

    $event = new TripEnRouteStart($this->trip->work_order_service_call_id, $this->techUser->user_id, ['foo' => 'bar'], false);

    $listener = new TripEnRouteStartListener;
    $listener->handle($event);

    Notification::assertNothingSent();
});

test('Handles missing userId gracefully', function () {
    $event = new TripEnRouteStart($this->trip->work_order_service_call_id, null, ['foo' => 'bar'], false);

    $listener = new TripEnRouteStartListener;
    $listener->handle($event);

    assertDatabaseHas('work_order_activity_logs', [
        'work_order_id' => $this->trip->work_order_id,
        'organization_id' => $this->trip->organization_id,
        'triggered_by' => null,
        'event' => ActivityLogEventTypes::WORK_ORDER_SERVICE_CALL_EN_ROUTE_INITIATED(),
    ]);
});

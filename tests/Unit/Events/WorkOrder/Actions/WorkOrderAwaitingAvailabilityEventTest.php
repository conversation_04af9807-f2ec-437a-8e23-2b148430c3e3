<?php

declare(strict_types=1);

use App\Enums\UserTypes;
use App\Events\WorkOrder\Actions\WorkOrderAwaitingAvailability;
use App\Models\Country;
use App\Models\Issue;
use App\Models\IssueStatus;
use App\Models\Organization;
use App\Models\ProblemDiagnosis;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\State;
use App\Models\Technician;
use App\Models\TechnicianAppointment;
use App\Models\Timezone;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\Models\WorkOrderIssueStatus;
use App\Models\WorkOrderServiceCall;
use App\States\Issue\Assigned;
use App\States\ServiceCalls\Scheduled;
use App\States\ServiceRequests\Scoping;
use App\States\WorkOrderIssue\Assigned as WorkOrderIssueAssigned;
use App\States\WorkOrders\WorkInProgress;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Database\Eloquent\ModelNotFoundException;

test('broadcastAs returns the expected name', function () {
    $event = new WorkOrderAwaitingAvailability(1, []);

    expect($event->broadcastAs())->toBe('workOrder.updated');
});

test('broadcastQueue returns the expected queue', function () {
    $event = new WorkOrderAwaitingAvailability(1, []);

    expect($event->broadcastQueue())->toBe('broadcasts');
});

test('broadcastOn returns the expected channel name', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create([
            'state' => Scoping::class,
        ]);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'state' => WorkInProgress::class,
        ]);

    $event = new WorkOrderAwaitingAvailability($workOrder->work_order_id, []);

    $channels = $event->broadcastOn();
    expect($channels)->toHaveCount(1)
        ->and($channels[0])->toBeInstanceOf(PrivateChannel::class)
        ->and($channels[0]->name)->toBe(
            "private-organization.{$organization->organization_uuid}.work-orders"
        );
});

test('broadcastWith returns expected data', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create([
            'state' => Scoping::class,
        ]);

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create([
            'label' => Assigned::$name,
            'slug' => Assigned::$name,
        ]);
    }

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create([
            'label' => WorkOrderIssueAssigned::$name,
            'slug' => WorkOrderIssueAssigned::$name,
        ]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    $issue = Issue::factory()
        ->for($organization)
        ->for($problemDiagnosis)
        ->for($serviceRequest)
        ->create([
            'state' => Assigned::class,
        ]);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'state' => WorkInProgress::class,
        ]);

    WorkOrderIssue::factory()->create([
        'work_order_id' => $workOrder->work_order_id,
        'issue_id' => $issue->issue_id,
        'organization_id' => $organization->organization_id,
        'state' => WorkOrderIssueAssigned::class,
    ]);

    $techUser = User::factory()
        ->for($timezone)
        ->for($organization)
        ->create([
            'user_type' => UserTypes::TECHNICIAN(),
        ]);

    $technician = Technician::factory()->create([
        'organization_id' => $organization->organization_id,
        'user_id' => $techUser->user_id,
    ]);

    $appointment = TechnicianAppointment::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_id' => $technician->technician_id,
        'work_order_id' => $workOrder->work_order_id,
    ]);

    WorkOrderServiceCall::create([
        'organization_id' => $organization->organization_id,
        'work_order_id' => $workOrder->work_order_id,
        'technician_appointment_id' => $appointment->technician_appointment_id,
        'scheduled_start_time' => $appointment->scheduled_start_time,
        'scheduled_end_time' => $appointment->scheduled_end_time,
        'duration_minutes' => 30,
        'state' => Scheduled::class,
        'is_active' => 1,
    ]);

    $event = new WorkOrderAwaitingAvailability($workOrder->work_order_id, [], $techUser->user_id);

    $broadcastData = $event->broadcastWith();
    expect($broadcastData['data']['work_order_id'])->toBe($workOrder->work_order_uuid);
    expect($broadcastData['data']['status']['label'])->toBe($workOrder->state->label());
    expect($broadcastData['data']['status']['value'])->toBe($workOrder->state->getValue());
});

test('getWorkOrderDetails fetches and returns work order', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create([
            'state' => Scoping::class,
        ]);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'state' => WorkInProgress::class,
        ]);

    $event = new WorkOrderAwaitingAvailability($workOrder->work_order_id, [], 1);

    $result = $event->getWorkOrderDetails();

    expect($result)->toBeInstanceOf(WorkOrder::class)
        ->and($result->work_order_uuid)->toBe($workOrder->work_order_uuid);
});

test('getWorkOrderDetails throws if work order not found', function () {
    $event = new WorkOrderAwaitingAvailability(54564654454544, [], 1);

    $event->getWorkOrderDetails();
})->throws(ModelNotFoundException::class);

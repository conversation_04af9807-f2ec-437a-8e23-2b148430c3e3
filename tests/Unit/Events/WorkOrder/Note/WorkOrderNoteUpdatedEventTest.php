<?php

declare(strict_types=1);

use App\Events\WorkOrder\Note\WorkOrderNoteUpdated;
use App\Http\Resources\WorkOrder\Note\NoteListResource;
use App\Models\Country;
use App\Models\Issue;
use App\Models\IssueStatus;
use App\Models\Organization;
use App\Models\ProblemDiagnosis;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\State;
use App\Models\Timezone;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\Models\WorkOrderIssueStatus;
use App\Models\WorkOrderNote;
use App\Services\WorkOrderActivity\Enums\ActivityLogEventTypes;
use App\States\Issue\Assigned;
use App\States\ServiceRequests\Scoping;
use App\States\WorkOrderIssue\Assigned as WorkOrderIssueAssigned;
use App\States\WorkOrders\WorkInProgress;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Database\Eloquent\ModelNotFoundException;

test('Retrieves work order note details correctly', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create([
            'state' => Scoping::class,
        ]);

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create([
            'label' => Assigned::$name,
            'slug' => Assigned::$name,
        ]);
    }

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create([
            'label' => WorkOrderIssueAssigned::$name,
            'slug' => WorkOrderIssueAssigned::$name,
        ]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    $issue = Issue::factory()
        ->for($organization)
        ->for($problemDiagnosis)
        ->for($serviceRequest)
        ->create([
            'state' => Assigned::class,
        ]);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'state' => WorkInProgress::class,
        ]);

    WorkOrderIssue::factory()->create([
        'work_order_id' => $workOrder->work_order_id,
        'issue_id' => $issue->issue_id,
        'organization_id' => $organization->organization_id,
        'state' => WorkOrderIssueAssigned::class,
    ]);

    $user = User::factory()->for($organization)->create();

    $workOrderNote = WorkOrderNote::factory()->create([
        'organization_id' => $organization->organization_id,
        'work_order_id' => $workOrder->work_order_id,
        'user_id' => $user->user_id,
        'work_order_status' => $workOrder->state->getValue(),
    ]);

    $event = new WorkOrderNoteUpdated($workOrderNote->work_order_note_id, $user->user_id);

    $data = $event->getWorkOrderNoteDetails();

    expect($data)->toBeInstanceOf(WorkOrderNote::class)
        ->and($data->work_order_note_id)->toBe($workOrderNote->work_order_note_id);
});

test('Handles missing issue gracefully', function () {
    $event = new WorkOrderNoteUpdated(6565465466544, 1);

    $event->getWorkOrderNoteDetails();
})->throws(ModelNotFoundException::class);

test('Returns the correct broadcast queue', function () {
    $event = new WorkOrderNoteUpdated(1, 1);

    expect($event->broadcastQueue())->toBe('broadcasts');
});

test('Returns the correct broadcast as', function () {
    $event = new WorkOrderNoteUpdated(1, 1);

    expect($event->broadcastAs())->toBe(ActivityLogEventTypes::WORK_ORDER_NOTE_UPDATED());
});

test('Broadcasts on the correct channel', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create([
            'state' => Scoping::class,
        ]);

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create([
            'label' => Assigned::$name,
            'slug' => Assigned::$name,
        ]);
    }

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create([
            'label' => WorkOrderIssueAssigned::$name,
            'slug' => WorkOrderIssueAssigned::$name,
        ]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    $issue = Issue::factory()
        ->for($organization)
        ->for($problemDiagnosis)
        ->for($serviceRequest)
        ->create([
            'state' => Assigned::class,
        ]);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'state' => WorkInProgress::class,
        ]);

    WorkOrderIssue::factory()->create([
        'work_order_id' => $workOrder->work_order_id,
        'issue_id' => $issue->issue_id,
        'organization_id' => $organization->organization_id,
        'state' => WorkOrderIssueAssigned::class,
    ]);

    $user = User::factory()->for($organization)->create();

    $workOrderNote = WorkOrderNote::factory()->create([
        'organization_id' => $organization->organization_id,
        'work_order_id' => $workOrder->work_order_id,
        'user_id' => $user->user_id,
        'work_order_status' => $workOrder->state->getValue(),
    ]);

    $event = new WorkOrderNoteUpdated($workOrderNote->work_order_note_id, $user->user_id);

    $channels = $event->broadcastOn();

    expect($channels)->toHaveCount(1)
        ->and($channels[0])->toBeInstanceOf(PrivateChannel::class)
        ->and($channels[0]->name)->toBe(
            "private-organization.{$organization->organization_uuid}.work-orders.{$workOrder->work_order_uuid}"
        );
});

test('Broadcasts with the correct data', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create([
            'state' => Scoping::class,
        ]);

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create([
            'label' => Assigned::$name,
            'slug' => Assigned::$name,
        ]);
    }

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create([
            'label' => WorkOrderIssueAssigned::$name,
            'slug' => WorkOrderIssueAssigned::$name,
        ]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    $issue = Issue::factory()
        ->for($organization)
        ->for($problemDiagnosis)
        ->for($serviceRequest)
        ->create([
            'state' => Assigned::class,
        ]);

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'state' => WorkInProgress::class,
        ]);

    WorkOrderIssue::factory()->create([
        'work_order_id' => $workOrder->work_order_id,
        'issue_id' => $issue->issue_id,
        'organization_id' => $organization->organization_id,
        'state' => WorkOrderIssueAssigned::class,
    ]);

    $user = User::factory()->for($organization)->create();

    $workOrderNote = WorkOrderNote::factory()->create([
        'organization_id' => $organization->organization_id,
        'work_order_id' => $workOrder->work_order_id,
        'user_id' => $user->user_id,
        'work_order_status' => $workOrder->state->getValue(),
    ]);

    $event = new WorkOrderNoteUpdated($workOrderNote->work_order_note_id, $user->user_id);

    $broadcastData = $event->broadcastWith();

    expect($broadcastData)->toHaveKey('data')
        ->and($broadcastData['data']['work_order_id'])->toBe($workOrder->work_order_uuid)
        ->and($broadcastData['data']['work_order_note'])->toBeInstanceOf(NoteListResource::class);
});

<?php

declare(strict_types=1);

use App\Events\WorkOrder\ActivityLog\BaseIssueEvent;
use App\Models\Country;
use App\Models\Media;
use App\Models\Organization;
use App\Models\Property;
use App\Models\State;
use App\Models\WorkOrder;
use App\Models\WorkOrderActivityLog;
use App\Models\WorkOrderMedia;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\UploadedFile;

test('Retrieves work order activity log correctly', function () {
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($property)
        ->create();

    $workOrderActivityLog = WorkOrderActivityLog::factory()->create([
        'organization_id' => $organization->organization_id,
        'work_order_id' => $workOrder->work_order_id,

    ]);

    $event = $this->getMockForAbstractClass(
        BaseIssueEvent::class,
        [$workOrderActivityLog->work_order_activity_log_id]
    );

    $retrievedLog = $event->getWorkOrderActivityLog();

    expect($retrievedLog)->toBeInstanceOf(WorkOrderActivityLog::class)
        ->and($retrievedLog->work_order_activity_log_id)->toBe($workOrderActivityLog->work_order_activity_log_id);
});

test('Returns the correct broadcast queue', function () {
    $event = $this->getMockForAbstractClass(
        BaseIssueEvent::class,
        [1, 1]
    );

    expect($event->broadcastQueue())->toBe('broadcasts');
});

test('Broadcasts on the correct channel', function () {
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($property)
        ->create();

    $workOrderActivityLog = WorkOrderActivityLog::factory()->create([
        'organization_id' => $organization->organization_id,
        'work_order_id' => $workOrder->work_order_id,

    ]);

    $event = $this->getMockForAbstractClass(
        BaseIssueEvent::class,
        [$workOrderActivityLog->work_order_activity_log_id]
    );

    $retrievedLog = $event->getWorkOrderActivityLog();

    expect($retrievedLog)->toBeInstanceOf(WorkOrderActivityLog::class)
        ->and($retrievedLog->work_order_activity_log_id)->toBe($workOrderActivityLog->work_order_activity_log_id);
});

test('Broadcasts with the correct data', function () {
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($property)
        ->create();

    $workOrderActivityLog = WorkOrderActivityLog::factory()->create([
        'organization_id' => $organization->organization_id,
        'work_order_id' => $workOrder->work_order_id,

    ]);

    $event = $this->getMockForAbstractClass(
        BaseIssueEvent::class,
        [$workOrderActivityLog->work_order_activity_log_id]
    );

    $event->workOrderActivityLog = $workOrderActivityLog;

    $broadcastData = $event->broadcastWith();

    expect($broadcastData)->toHaveKey('data');
    expect($broadcastData['data']['work_order_id'])->toBe($workOrder->work_order_uuid);
    expect($broadcastData['data'])->toHaveKey('activity_log');
});

test('Throws exception when work order activity log is not found', function () {
    $event = $this->getMockForAbstractClass(
        BaseIssueEvent::class,
        [999999999] // Non-existent ID
    );

    expect(fn () => $event->getWorkOrderActivityLog())->toThrow(ModelNotFoundException::class);
});

test('Replaces media IDs with media URLs in event attributes', function () {
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $responseData = $this->createScheduledWorkOrder(organizationId: $organization->organization_id);

    $workOrder = $responseData->workOrder;
    $appointment = $responseData->appointment;
    $user = $responseData->user;

    $media = UploadedFile::fake()->image('your-image-one.jpg', 100, 100)->size(100);
    $medias = Media::factory(5)->create(
        [
            'organization_id' => $organization->organization_id,
            'original_thumbnail_file_name' => $media->getClientOriginalName(),
            'mime_type' => $media->getClientMimeType(),
            'extension' => $media->getClientOriginalExtension(),
            'thumbnail_extension' => $media->getClientOriginalExtension(),
        ],
    );

    foreach ($medias as $media) {
        WorkOrderMedia::create([
            'media_id' => $media->media_id,
            'organization_id' => $organization->organization_id,
            'work_order_id' => $workOrder->work_order_id,
            'work_order_service_call_id' => $appointment->work_order_service_call_id,
            'user_id' => $user?->user_id,
        ]);
    }

    $workOrderActivityLog = WorkOrderActivityLog::factory()->create([
        'organization_id' => $organization->organization_id,
        'work_order_id' => $workOrder->work_order_id,
        'event_attributes' => [
            'medias' => [$media->media_uuid],
        ],
    ]);

    $event = $this->getMockForAbstractClass(
        BaseIssueEvent::class,
        [$workOrderActivityLog->work_order_activity_log_id]
    );

    $event->workOrderActivityLog = $workOrderActivityLog;

    $broadcastData = $event->broadcastWith();

    expect($broadcastData['data']['activity_log']['attributes']['medias'][0]['media_uuid'])->toBe($media->media_uuid);
    expect($broadcastData['data']['activity_log']['attributes']['medias'][0]['file_name'])->toBe($media->file_name);
    expect($broadcastData['data']['activity_log']['attributes']['medias'][0]['mime_type'])->toBe($media->mime_type);
});

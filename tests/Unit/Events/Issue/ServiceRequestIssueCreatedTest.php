<?php

declare(strict_types=1);

use App\Events\Issue\BaseIssueEvent;
use App\Events\Issue\ServiceRequestIssueCreated;
use App\Http\Resources\Issue\IssueResource;
use App\Models\Country;
use App\Models\Issue;
use App\Models\IssueStatus;
use App\Models\Organization;
use App\Models\ProblemCategory;
use App\Models\ProblemDiagnosis;
use App\Models\ProblemSubCategory;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestStatus;
use App\Models\State;
use App\Models\Timezone;
use App\States\Issue\Unassigned;
use Illuminate\Broadcasting\PrivateChannel;

test('It return correct broadcast queue', function () {
    $event = new ServiceRequestIssueCreated(1, 1);

    expect($event->broadcastQueue())->toBe('broadcasts');
});

test('It returns the correct broadcast channels', function () {
    $issue = Issue::factory()->make([
        'issue_id' => 1,
    ]);

    $organization = Organization::factory()->make([
        'organization_id' => 1,
        'organization_uuid' => fake()->uuid(),
    ]);

    $serviceRequest = ServiceRequest::factory()->make([
        'service_request_id' => 1,
        'service_request_uuid' => fake()->uuid(),
    ]);

    $issue->setRelation('organization', $organization);
    $issue->setRelation('serviceRequest', $serviceRequest);

    $event = Mockery::mock(ServiceRequestIssueCreated::class)->makePartial();
    $event->shouldReceive('getIssueDetails')->andReturn($issue);

    /** @var ServiceRequestIssueCreated $event */
    $channels = $event->broadcastOn();

    expect($channels)->toHaveCount(1)
        ->and($channels[0])->toBeInstanceOf(PrivateChannel::class)
        ->and($channels[0]->name)->toBe(
            "private-organization.{$issue->organization->organization_uuid}.service-requests.{$issue->serviceRequest->service_request_uuid}"
        );
});

test('It return the correct broadcast name', function () {
    $event = new ServiceRequestIssueCreated(1, 1);

    expect($event->broadcastAs())->toBe('serviceRequest.issue.created');
});

test('It returns the correct broadcast data', function () {
    $problemCategory = ProblemCategory::factory()->create();

    $problemSubCategory = ProblemSubCategory::factory()->create([
        'problem_category_id' => $problemCategory->problem_category_id,
    ]);

    $problemDiagnosis = ProblemDiagnosis::factory()->create([
        'problem_sub_category_id' => $problemSubCategory->problem_sub_category_id,
    ]);

    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for(Property::factory()->for($organization))
        ->for(Timezone::factory())
        ->for(ServiceRequestStatus::factory(), 'status')
        ->create();

    if (! IssueStatus::where('slug', Unassigned::$name)->count()) {
        IssueStatus::factory()->create();
    }

    $issue = Issue::factory()
        ->for($organization)
        ->for($problemDiagnosis)
        ->for(ProblemDiagnosis::factory())
        ->for($serviceRequest)
        ->create();

    $event = new ServiceRequestIssueCreated($issue->issue_id, 1);

    $data = $event->broadcastWith();

    expect($data)->toHaveKey('data')
        ->and($data['data'])->toBeInstanceOf(IssueResource::class)
        ->and($data['data']->resolve())->toHaveKeys(['issue_id', 'title', 'description', 'status'])
        ->and($data['data']->resolve()['issue_id'])->toBe($issue->issue_uuid);
});

test('Handles missing issue gracefully', function () {
    $event = new ServiceRequestIssueCreated(1234567896321, 1); // Non-existent issue ID

    expect(fn () => $event->getIssueDetails())->toThrow(Illuminate\Database\Eloquent\ModelNotFoundException::class);
});

test('retrieves the correct issue details', function () {
    $problemCategory = ProblemCategory::factory()->create();

    $problemSubCategory = ProblemSubCategory::factory()->create([
        'problem_category_id' => $problemCategory->problem_category_id,
    ]);

    $problemDiagnosis = ProblemDiagnosis::factory()->create([
        'problem_sub_category_id' => $problemSubCategory->problem_sub_category_id,
    ]);

    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for(Property::factory()->for($organization))
        ->for(Timezone::factory())
        ->for(ServiceRequestStatus::factory(), 'status')
        ->create();

    if (! IssueStatus::where('slug', Unassigned::$name)->count()) {
        IssueStatus::factory()->create();
    }

    $issue = Issue::factory()
        ->for($organization)
        ->for($problemDiagnosis)
        ->for(ProblemDiagnosis::factory())
        ->for($serviceRequest)
        ->create();

    $event = new ServiceRequestIssueCreated($issue->issue_id, 1);

    $retrievedIssue = $event->getIssueDetails();

    expect($retrievedIssue->issue_id)->toBe($issue->issue_id)
        ->and($retrievedIssue->title)->toBe($issue->title);
});

test('Extends the BaseIssueEvent interface', function () {
    $event = new ServiceRequestIssueCreated(1, 1);
    expect($event)->toBeInstanceOf(BaseIssueEvent::class);
});

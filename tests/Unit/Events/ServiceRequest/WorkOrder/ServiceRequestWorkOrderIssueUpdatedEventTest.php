<?php

use App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderBaseEvent;
use App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdated;
use App\Http\Resources\WorkOrderIssue\WorkOrderIssueStatusResource;
use App\Models\Country;
use App\Models\Issue;
use App\Models\IssueStatus;
use App\Models\Organization;
use App\Models\ProblemCategory;
use App\Models\ProblemDiagnosis;
use App\Models\ProblemSubCategory;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\State;
use App\Models\Timezone;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\States\Issue\Assigned;
use App\States\WorkOrders\Scheduled;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Contracts\Queue\ShouldQueueAfterCommit;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Resources\Json\JsonResource;

test('Event have ServiceRequestWorkOrderBaseEvent class', function () {
    $event = new ServiceRequestWorkOrderIssueUpdated(1);

    expect($event)->toBeInstanceOf(ServiceRequestWorkOrderBaseEvent::class);
});

test('Event have ShouldBroadcast class', function () {
    $event = new ServiceRequestWorkOrderIssueUpdated(1);

    expect($event)->toBeInstanceOf(ShouldBroadcast::class);
});

test('Event have ShouldDispatchAfterCommit class', function () {
    $event = new ServiceRequestWorkOrderIssueUpdated(1);

    expect($event)->toBeInstanceOf(ShouldDispatchAfterCommit::class);
});

test('Event have ShouldQueueAfterCommit class', function () {
    $event = new ServiceRequestWorkOrderIssueUpdated(1);

    expect($event)->toBeInstanceOf(ShouldQueueAfterCommit::class);
});

test('Event have proper methods', function () {
    $event = new ServiceRequestWorkOrderIssueUpdated(1);

    expect(method_exists($event, 'broadcastOn'))->toBeTrue();
    expect(method_exists($event, 'broadcastQueue'))->toBeTrue();
    expect(method_exists($event, 'broadcastAs'))->toBeTrue();
    expect(method_exists($event, 'broadcastWith'))->toBeTrue();
    expect(method_exists($event, 'getWorkOrderDetails'))->toBeTrue();
});

test('broadcasts on method return the correct channels', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create();

    $workOrder = WorkOrder::factory()
        ->for($serviceRequest)
        ->for($organization)
        ->for($property)
        ->create([
            'state' => Scheduled::class,
        ]);

    $event = new ServiceRequestWorkOrderIssueUpdated($workOrder->work_order_id);

    $channels = $event->broadcastOn();

    expect($channels)->toHaveCount(1);
    expect($channels[0])->toBeInstanceOf(PrivateChannel::class);
    expect($channels[0]->name)->toBe(
        "private-organization.{$organization->organization_uuid}.service-requests.{$serviceRequest->service_request_uuid}"
    );
});

test('broadcastQueue return the correct broadcast queue', function () {
    $event = new ServiceRequestWorkOrderIssueUpdated(1);

    expect($event->broadcastQueue())->toBe('broadcasts');
});

test('broadcastAs returns the correct broadcast name', function () {
    $event = new ServiceRequestWorkOrderIssueUpdated(1);

    expect($event->broadcastAs())->toBe('serviceRequest.workOrder.updated');
});

test('getWorkOrderDetails returns the given work order details', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create();

    $workOrder = WorkOrder::factory()
        ->for($serviceRequest)
        ->for($organization)
        ->for($property)
        ->create([
            'state' => Scheduled::class,
        ]);

    $event = new ServiceRequestWorkOrderIssueUpdated($workOrder->work_order_id);

    $data = $event->getWorkOrderDetails();

    expect($data)->toBeInstanceOf(WorkOrder::class);
    expect($data->work_order_id)->toBe($workOrder->work_order_id);
});

test('getWorkOrderDetails throws an exception when work order is not found', function () {
    $event = new ServiceRequestWorkOrderIssueUpdated(2211465464646);

    $event->getWorkOrderDetails();
})->throws(ModelNotFoundException::class);

test('broadcastWith return the correct pusher response', function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create();

    $workOrder = WorkOrder::factory()
        ->for($serviceRequest)
        ->for($organization)
        ->for($property)
        ->create([
            'state' => Scheduled::class,
        ]);

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create([
            'label' => Assigned::$name,
            'slug' => Assigned::$name,
        ]);
    }

    $problemCategory = ProblemCategory::factory()->create();

    $problemSubCategory = ProblemSubCategory::factory()->create([
        'problem_category_id' => $problemCategory->problem_category_id,
    ]);

    $problemDiagnosis = ProblemDiagnosis::factory()->create([
        'problem_sub_category_id' => $problemSubCategory->problem_sub_category_id,
    ]);

    $issue = Issue::factory()
        ->for($organization)
        ->for($problemDiagnosis)
        ->for($serviceRequest)
        ->create([
            'state' => Assigned::class,
        ]);

    WorkOrderIssue::factory()->create([
        'work_order_id' => $workOrder->work_order_id,
        'issue_id' => $issue->issue_id,
        'organization_id' => $organization->organization_id,
    ]);

    $event = new ServiceRequestWorkOrderIssueUpdated($workOrder->work_order_id);

    $result = $event->broadcastWith();

    expect($result)->toHaveKey('data');
    expect($result['data'])->toHaveKey('work_order_id');
    expect($result['data'])->toHaveKey('issues');
    expect($result['data'])->toHaveKey('abilities');

    expect($result['data']['work_order_id'])->not->toBeNull();
    expect($result['data']['issues'])->toBeInstanceOf(JsonResource::class);

    expect($result['data']['issues']->resolve())->toHaveCount(1);
    expect($result['data']['issues']->resolve()[0])->toHaveKey('issue_id');
    expect($result['data']['issues']->resolve()[0])->toHaveKey('title');
    expect($result['data']['issues']->resolve()[0])->toHaveKey('status');
    expect($result['data']['issues']->resolve()[0]['issue_id'])->not->toBeNull();
    expect($result['data']['issues']->resolve()[0]['title'])->not->toBeNull();
    expect($result['data']['issues']->resolve()[0]['status'])->toBeInstanceOf(WorkOrderIssueStatusResource::class);

    expect($result['data']['issues']->resolve()[0]['status']->resolve())->toHaveKey('label');
    expect($result['data']['issues']->resolve()[0]['status']->resolve())->toHaveKey('value');
    expect($result['data']['issues']->resolve()[0]['status']->resolve()['label'])->not->toBeNull();
    expect($result['data']['issues']->resolve()[0]['status']->resolve()['value'])->not->toBeNull();
    expect($result['data']['abilities'])->toBeArray();
});

<?php

use App\Events\ServiceRequest\ActivityLog\ServiceRequestActivityLogUpdated;
use App\Models\Country;
use App\Models\Organization;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestActivityLog;
use App\Models\State;
use App\Models\User;
use App\Services\ServiceRequestActivity\Enums\ActivityLogEventTypes;
use App\States\ServiceRequests\Scoping;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Database\Eloquent\ModelNotFoundException;

test('Event has the correct broadcast queue', function () {
    $event = new ServiceRequestActivityLogUpdated(1);

    expect($event->broadcastQueue())->toBe('broadcasts');
});

test('Event returns the correct broadcast channels', function () {
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->create([
            'state' => Scoping::class,
        ]);

    $user = User::factory()->create();

    $activityLog = ServiceRequestActivityLog::factory()->create([
        'service_request_id' => $serviceRequest->service_request_id,
        'organization_id' => $organization->organization_id,
        'triggered_by' => $user->user_id,
        'type' => 'service-request',
        'event' => ActivityLogEventTypes::SERVICE_REQUEST_STATUS_CHANGED(),
        'event_attributes' => [],
    ]);

    $event = new ServiceRequestActivityLogUpdated($activityLog->service_request_activity_log_id);

    $channels = $event->broadcastOn();

    expect($channels)->toHaveCount(1)
        ->and($channels[0])->toBeInstanceOf(PrivateChannel::class)
        ->and($channels[0]->name)->toBe("private-organization.{$organization->organization_uuid}.service-requests.{$serviceRequest->service_request_uuid}");
});

test('Event has the correct broadcast name', function () {
    $event = new ServiceRequestActivityLogUpdated(1);

    expect($event->broadcastAs())->toBe('serviceRequest.activityLog.updated');
});

test('Event returns the correct broadcast data', function () {
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->create([
            'state' => Scoping::class,
        ]);

    $user = User::factory()->create();

    $activityLog = ServiceRequestActivityLog::factory()->create([
        'service_request_id' => $serviceRequest->service_request_id,
        'organization_id' => $organization->organization_id,
        'triggered_by' => $user->user_id,
        'type' => 'service-request',
        'event' => ActivityLogEventTypes::SERVICE_REQUEST_STATUS_CHANGED(),
        'event_attributes' => [],
    ]);

    $event = new ServiceRequestActivityLogUpdated($activityLog->service_request_activity_log_id);

    $data = $event->broadcastWith();

    expect($data)->toHaveKey('data')
        ->and($data['data']['service_request_id'])->toBe($serviceRequest->service_request_uuid)
        ->and($data['data']['activity_log']['event'])->toBe(ActivityLogEventTypes::SERVICE_REQUEST_STATUS_CHANGED())
        ->and($data['data']['activity_log']['attributes'])->toBe([])
        ->and($data['data']['activity_log']['triggered_by'])->toBe($user->getName());
});

test('Event throws an exception if activity log is not found', function () {
    $event = new ServiceRequestActivityLogUpdated(99555145214544449);

    $event->broadcastWith();
})->throws(ModelNotFoundException::class);

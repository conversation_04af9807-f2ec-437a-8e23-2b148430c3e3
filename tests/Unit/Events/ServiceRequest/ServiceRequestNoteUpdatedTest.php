<?php

declare(strict_types=1);
use App\Events\ServiceRequest\ServiceRequestNoteUpdated;
use App\Models\Organization;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestNote;
use App\Models\ServiceRequestStatus;
use App\Models\Timezone;
use App\Models\User;
use App\Models\Vendor;
use App\Services\ServiceRequestActivity\Enums\ActivityLogEventTypes;
use App\Services\Vendor\Enum\Service;

test('ServiceRequestNoteUpdated event is broadcasted with correct data', function (): void {
    $organization = Organization::factory()->create();
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for(Property::factory()->for($organization))
        ->for(Timezone::factory())
        ->for(ServiceRequestStatus::factory(), 'status')
        ->create();
    $serviceRequestNote = ServiceRequestNote::factory()->create([
        'organization_id' => $organization->organization_id,
        'service_request_id' => $serviceRequest->service_request_id,
        'service_request_status_id' => $serviceRequest->service_request_status_id,
        'note_type' => 'internal',
        'note' => fake()->text(),
    ]);

    Event::fake();

    $event = new ServiceRequestNoteUpdated($serviceRequest, $serviceRequestNote);
    event($event);

    Event::assertDispatched(ServiceRequestNoteUpdated::class, function ($e) use ($serviceRequest, $serviceRequestNote) {
        return $e->serviceRequest->is($serviceRequest) && $e->serviceRequestNote->is($serviceRequestNote);
    });
});

test('ServiceRequestNoteUpdated event broadcastQueue returns correct data', function (): void {
    $organization = Organization::factory()->create();
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for(Property::factory()->for($organization))
        ->for(Timezone::factory())
        ->for(ServiceRequestStatus::factory(), 'status')
        ->create();
    $serviceRequestNote = ServiceRequestNote::factory()->create([
        'organization_id' => $organization->organization_id,
        'service_request_id' => $serviceRequest->service_request_id,
        'service_request_status_id' => $serviceRequest->service_request_status_id,
        'note_type' => 'internal',
        'note' => fake()->text(),
    ]);

    $event = new ServiceRequestNoteUpdated($serviceRequest, $serviceRequestNote);

    $broadcastQueue = $event->broadcastQueue();

    expect($broadcastQueue)->toBe('broadcasts');
});

test('ServiceRequestNoteUpdated event broadcastAs returns correct data', function (): void {
    $organization = Organization::factory()->create();
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for(Property::factory()->for($organization))
        ->for(Timezone::factory())
        ->for(ServiceRequestStatus::factory(), 'status')
        ->create();
    $serviceRequestNote = ServiceRequestNote::factory()->create([
        'organization_id' => $organization->organization_id,
        'service_request_id' => $serviceRequest->service_request_id,
        'service_request_status_id' => $serviceRequest->service_request_status_id,
        'note_type' => 'internal',
        'note' => fake()->text(),
    ]);

    $event = new ServiceRequestNoteUpdated($serviceRequest, $serviceRequestNote);

    $broadcastAs = $event->broadcastAs();

    expect($broadcastAs)->toBe(ActivityLogEventTypes::SERVICE_REQUEST_NOTE_UPDATED());
});

test('ServiceRequestNoteUpdated event broadcastOn returns correct channels', function (): void {
    $organization = Organization::factory()->create();
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for(Property::factory()->for($organization))
        ->for(Timezone::factory())
        ->for(ServiceRequestStatus::factory(), 'status')
        ->create();
    $serviceRequestNote = ServiceRequestNote::factory()->create([
        'organization_id' => $organization->organization_id,
        'service_request_id' => $serviceRequest->service_request_id,
        'service_request_status_id' => $serviceRequest->service_request_status_id,
        'note_type' => 'internal',
        'note' => fake()->text(),
    ]);

    $event = new ServiceRequestNoteUpdated($serviceRequest, $serviceRequestNote);

    $channels = $event->broadcastOn();

    expect($channels)->toHaveCount(1);
    expect($channels[0]->name)->toBe("private-organization.{$organization->organization_uuid}.service-requests.{$serviceRequest->service_request_uuid}");
});

test('ServiceRequestNoteUpdated event broadcastWith returns correct data for user', function (): void {
    $organization = Organization::factory()->create();
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for(Property::factory()->for($organization))
        ->for(Timezone::factory())
        ->for(ServiceRequestStatus::factory(), 'status')
        ->create();
    $user = User::factory()
        ->for($organization)
        ->create();
    $serviceRequestNote = ServiceRequestNote::factory()
        ->for($user)
        ->create([
            'organization_id' => $organization->organization_id,
            'service_request_id' => $serviceRequest->service_request_id,
            'service_request_status_id' => $serviceRequest->service_request_status_id,
            'note_type' => 'internal',
            'note' => fake()->text(),
        ]);

    $event = new ServiceRequestNoteUpdated($serviceRequest, $serviceRequestNote);

    $data = $event->broadcastWith();

    expect($data['data']['service_request_id'])->toBe($serviceRequest->service_request_uuid);
    expect($data['data']['service_request_note']['note_id'])->toBe($serviceRequestNote->service_request_note_uuid);
    expect($data['data']['service_request_note']['created_at'])->toBe($serviceRequestNote->created_at->toIso8601String());
    expect($data['data']['service_request_note']['created_user']['user_id'])->toBe($user->user_uuid);
});

test('ServiceRequestNoteUpdated event broadcastWith returns correct data for vendor', function (): void {
    $organization = Organization::factory()->create();
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for(Property::factory()->for($organization))
        ->for(Timezone::factory())
        ->for(ServiceRequestStatus::factory(), 'status')
        ->create();
    $vendor = Vendor::factory()->create(['service' => Service::LULA()]);
    $serviceRequestNote = ServiceRequestNote::factory()
        ->for($vendor)
        ->create([
            'organization_id' => $organization->organization_id,
            'service_request_id' => $serviceRequest->service_request_id,
            'service_request_status_id' => $serviceRequest->service_request_status_id,
            'note_type' => 'internal',
            'note' => fake()->text(),
        ]);

    $event = new ServiceRequestNoteUpdated($serviceRequest, $serviceRequestNote);

    $data = $event->broadcastWith();

    expect($data['data']['service_request_id'])->toBe($serviceRequest->service_request_uuid);
    expect($data['data']['service_request_note']['note_id'])->toBe($serviceRequestNote->service_request_note_uuid);
    expect($data['data']['service_request_note']['created_at'])->toBe($serviceRequestNote->created_at->toIso8601String());
    expect($data['data']['service_request_note']['created_user']['user_id'])->toBe($vendor->vendor_uuid);
});

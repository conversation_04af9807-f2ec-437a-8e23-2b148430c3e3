<?php

use App\Enums\Feature as FeatureEnum;
use App\Enums\ServiceCallStatus;
use App\Enums\UserTypes;
use App\Enums\WorkOrderStatus as EnumsWorkOrderStatus;
use App\Http\Controllers\WorkOrderController;
use App\Models\Issue;
use App\Models\IssueStatus;
use App\Models\ProblemCategory;
use App\Models\ProblemDiagnosis;
use App\Models\ProblemSubCategory;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestStatus;
use App\Models\ServiceRequestType;
use App\Models\Tag;
use App\Models\Technician;
use App\Models\TechnicianAppointment;
use App\Models\Timezone;
use App\Models\WorkOrder;
use App\Models\WorkOrderAssignee;
use App\Models\WorkOrderIssue;
use App\Models\WorkOrderIssueStatus;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderStatus;
use App\Models\WorkOrderTags;
use App\States\Issue\Assigned;
use App\States\ServiceCalls\Ended;
use App\States\ServiceCalls\EnRoute;
use App\States\ServiceCalls\Paused;
use App\States\ServiceCalls\Scheduled;
use App\States\ServiceCalls\Working;
use App\States\WorkOrderIssue\Assigned as WorkOrderIssueAssigned;
use App\States\WorkOrders\AwaitingAvailability;
use App\States\WorkOrders\Created;
use Carbon\CarbonImmutable;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

test('handles UnexpectedValueException in handleIndexException', function () {
    $controller = new WorkOrderController;
    $exception = new UnexpectedValueException('Unexpected value error');

    $response = $controller->handleIndexException($exception);

    expect($response)->toBeInstanceOf(JsonResponse::class)
        ->and($response->getStatusCode())->toBe(Response::HTTP_UNPROCESSABLE_ENTITY);
});

test('handles InvalidArgumentException in handleIndexException', function () {
    $controller = new WorkOrderController;
    $exception = new InvalidArgumentException('Invalid argument error');

    $response = $controller->handleIndexException($exception);

    expect($response)->toBeInstanceOf(JsonResponse::class)
        ->and($response->getStatusCode())->toBe(Response::HTTP_BAD_REQUEST);
});

test('handles LogicException in handleIndexException', function () {
    $controller = new WorkOrderController;
    $exception = new LogicException('Logic error');

    $response = $controller->handleIndexException($exception);

    expect($response)->toBeInstanceOf(JsonResponse::class)
        ->and($response->getStatusCode())->toBe(Response::HTTP_UNPROCESSABLE_ENTITY);
});

test('handles generic Exception in handleIndexException', function () {
    $controller = new WorkOrderController;
    $exception = new Exception('Generic error');

    $response = $controller->handleIndexException($exception);
    expect($response)->toBeInstanceOf(JsonResponse::class)
        ->and($response->getStatusCode())->toBe(Response::HTTP_INTERNAL_SERVER_ERROR);
});

test('The user with valid token can access work order list API response return success', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $result = $this->getJson(route('workOrder.index', []), $header);

    //Assert the API response successfully processed.
    expect($result)
        ->assertStatus(Response::HTTP_OK);
});

test('The user with invalid token can access work order list API response return success', function () {
    $result = $this->getJson(route('workOrder.index', []), ['Authorization' => 'Bearer']);

    //Assert the API response successfully processed.
    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

test('The work order list API response return correct work orders with out filter', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrders = WorkOrder::factory(5)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($timezone)
        ->create();

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }
    $result = $this->getJson(route('workOrder.index', []), $header);

    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data');
});

test('The work order list API response return correct response for web', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrders = WorkOrder::factory(5)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($timezone)
        ->create();

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $result = $this->getJson(route('workOrder.index', []), $header);

    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data')
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'abilities',
                    'trips',
                    'assignees',
                    'category',
                    'created_at',
                    'due_date',
                    'group',
                    'has_missing_data',
                    'health_score',
                    'nte_amount_in_cents',
                    'priority',
                    'property',
                    'provider',
                    'status',
                    'tags',
                    'timezone',
                    'work_order_id',
                    'work_order_number',
                ],
            ],
        ]);
});

test('The work order list API response return correct response for app', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list', UserTypes::TECHNICIAN());

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'Device-Type' => 'Mobile',
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrder = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($timezone)
        ->create();

    WorkOrderIssue::factory()->create([
        'work_order_id' => $workOrder->work_order_id,
        'issue_id' => $issue->issue_id,
        'organization_id' => $organization->organization_id,
        'state' => WorkOrderIssueAssigned::class,
    ]);
    $user->refresh();

    $appointmentOne = TechnicianAppointment::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_id' => $user->technician->technician_id,
        'work_order_id' => $workOrder->work_order_id,
    ]);

    WorkOrderServiceCall::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_appointment_id' => $appointmentOne->technician_appointment_id,
        'work_order_id' => $workOrder->work_order_id,
    ]);

    $result = $this->getJson(route('workOrder.index', []), $header);

    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data')
        ->assertJsonStructure([
            'data' => [
                '*' => [
                    'trips',
                    'nte_amount_in_cents',
                    'priority',
                    'property' => [
                        'address',
                        'name',
                    ],
                    'status',
                    'timezone',
                    'work_order_id',
                    'work_order_number',
                ],
            ],
        ]);
});

test('The work order list API with status filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
        ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        $workOrder->state = EnumsWorkOrderStatus::NEW();
        $workOrder->save();
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'status',
                    'operation' => 'is',
                    'values' => [EnumsWorkOrderStatus::NEW()],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->status->value)->toBe(EnumsWorkOrderStatus::NEW());
    }
});

test('The work order list API with not equal status filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $newWorkOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
        ]);

    $awaitingWorkOrder = WorkOrder::factory(2)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => AwaitingAvailability::$name,
        ]);

    $workOrders = $newWorkOrders->merge($awaitingWorkOrder);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'status',
                    'operation' => 'is_not',
                    'values' => [EnumsWorkOrderStatus::NEW()],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(2, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->status->value)->toBe(EnumsWorkOrderStatus::AWAITING_AVAILABILITY());
    }
});

test('The work order list API with priority filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrders = WorkOrder::factory(5)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'priority' => 'medium',
            'state' => Created::$name,
        ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $urgentPriorityWorkOrders = WorkOrder::factory(3)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'priority' => 'urgent',
            'state' => Created::$name,
        ]);

    foreach ($urgentPriorityWorkOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'priority',
                    'operation' => 'is',
                    'values' => ['urgent'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(3, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->priority->value)->toBe('urgent');
    }
});

test('The work order list API with not equal priority filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrders = WorkOrder::factory(5)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'priority' => 'medium',
            'state' => Created::$name,
        ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $urgentPriorityWorkOrders = WorkOrder::factory(3)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'priority' => 'urgent',
            'state' => Created::$name,
        ]);

    foreach ($urgentPriorityWorkOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'priority',
                    'operation' => 'is_not',
                    'values' => ['urgent'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->priority->value)->toBe('medium');
    }
});

test('The work order list API with technician filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $count = 5;
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'priority' => 'medium',
            'state' => Created::$name,
        ]);

    $technician = Technician::factory()->create([
        'organization_id' => $organization->organization_id,
        'user_id' => $user->user_id,
    ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        if ($count <= 3) {
            $appointment = TechnicianAppointment::factory()->create([
                'organization_id' => $organization->organization_id,
                'technician_id' => $technician->technician_id,
                'work_order_id' => $workOrder->work_order_id,
            ]);

            WorkOrderServiceCall::factory()->create([
                'organization_id' => $organization->organization_id,
                'technician_appointment_id' => $appointment->technician_appointment_id,
                'work_order_id' => $workOrder->work_order_id,
            ]);
        }

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'technician',
                    'operation' => 'is',
                    'values' => [$technician->technician_uuid],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(3, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect(count($workOrder->trips))->toBe(1);
        expect($workOrder->trips[0]->appointment->name)->toBe($technician->user->getName());
    }
});

test('The work order list API with assignee filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $count = 5;
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'priority' => 'medium',
            'state' => Created::$name,
        ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        if ($count <= 3) {
            WorkOrderAssignee::create([
                'work_order_id' => $workOrder->work_order_id,
                'user_id' => $user->user_id,
                'action_by_user_id' => $user->user_id,
            ]);
        }

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'assignee',
                    'operation' => 'is',
                    'values' => [$user->user_uuid],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(3, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect(count($workOrder->assignees))->toBe(1);
        expect($workOrder->assignees[0]->user_id)->toBe($user->user_uuid);
    }
});

test('The work order list API with not equal assignee filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $count = 5;
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'priority' => 'medium',
            'state' => Created::$name,
        ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        if ($count <= 3) {
            WorkOrderAssignee::create([
                'work_order_id' => $workOrder->work_order_id,
                'user_id' => $user->user_id,
                'action_by_user_id' => $user->user_id,
            ]);
        }

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'assignee',
                    'operation' => 'is_not',
                    'values' => [$user->user_uuid],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(2, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect(count($workOrder->assignees))->toBe(0);
    }
});

test('The work order list API with tag filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $count = 5;
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'priority' => 'medium',
            'state' => Created::$name,
        ]);

    $tag = Tag::factory()->create([
        'name' => 'Test Tag One',
        'slug' => 'test_tag_one',
        'organization_id' => $organization->organization_id,
    ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        if ($count <= 3) {
            WorkOrderTags::create([
                'work_order_id' => $workOrder->work_order_id,
                'tag_id' => $tag->tag_id,
            ]);
        }

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'tag',
                    'operation' => 'is',
                    'values' => [$tag->tag_uuid],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(3, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect(count($workOrder->tags))->toBe(1);
        expect($workOrder->tags[0]->tag_id)->toBe($tag->tag_uuid);
    }
});

test('The work order list API with not equal tag filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $count = 5;
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'priority' => 'medium',
            'state' => Created::$name,
        ]);

    $tag = Tag::factory()->create([
        'name' => 'Test Tag One',
        'slug' => 'test_tag_one',
        'organization_id' => $organization->organization_id,
    ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        if ($count <= 3) {
            WorkOrderTags::create([
                'work_order_id' => $workOrder->work_order_id,
                'tag_id' => $tag->tag_id,
            ]);
        }

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'tag',
                    'operation' => 'is_not',
                    'values' => [$tag->tag_uuid],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(2, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect(count($workOrder->tags))->toBe(0);
    }
});

test('The work order list API with category filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $count = 5;
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'priority' => 'medium',
            'state' => Created::$name,
        ]);

    $problemCategoryForFilter = ProblemCategory::factory()->create([
        'label' => 'Test Category',
        'slug' => 'test_category',
    ]);

    $problemSubCategoryForFilter = ProblemSubCategory::factory()->create([
        'problem_category_id' => $problemCategoryForFilter->problem_category_id,
        'label' => 'Test One',
        'slug' => 'test_one',
    ]);

    $problemDiagnosisForFilter = ProblemDiagnosis::factory()->create([
        'problem_sub_category_id' => $problemSubCategoryForFilter->problem_sub_category_id,
        'label' => 'Test One',
        'slug' => 'test_one',
    ]);

    $issueForFilter = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosisForFilter)
        ->create([
            'user_id' => $user->user_id,
        ]);

    foreach ($workOrders as $workOrder) {
        if ($count <= 2) {
            WorkOrderIssue::factory()->create([
                'work_order_id' => $workOrder->work_order_id,
                'issue_id' => $issueForFilter->issue_id,
                'organization_id' => $organization->organization_id,
                'state' => WorkOrderIssueAssigned::class,
            ]);
        } else {
            WorkOrderIssue::factory()->create([
                'work_order_id' => $workOrder->work_order_id,
                'issue_id' => $issue->issue_id,
                'organization_id' => $organization->organization_id,
                'state' => WorkOrderIssueAssigned::class,
            ]);
        }

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'category',
                    'operation' => 'is',
                    'values' => [$problemCategoryForFilter->problem_category_uuid],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(2, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect(count($workOrder->category))->toBe(1);
        expect($workOrder->category[0]->label)->toBe('Test Category');
    }
});

test('The work order list API with over due date filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->subDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is',
                    'values' => ['overdue'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with not over due date filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->subDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is_not',
                    'values' => ['overdue'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with due date today filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->subDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is',
                    'values' => ['today'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with due date not today filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->subDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is_not',
                    'values' => ['today'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with due date tomorrow filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->addDays(1),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is',
                    'values' => ['tomorrow'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with due date not tomorrow filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->addDays(1),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->addDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is_not',
                    'values' => ['tomorrow'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with due date is yesterday filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->subDays(1),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->addDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is',
                    'values' => ['yesterday'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with due date is not yesterday filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->subDays(1),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->addDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is_not',
                    'values' => ['yesterday'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with due date is this week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->startOfWeek()->addDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is',
                    'values' => ['this_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with due date is not this week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->startOfWeek()->addDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is_not',
                    'values' => ['this_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with due date is next week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->addWeek()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is',
                    'values' => ['next_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with due date is not next week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->addWeek()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is_not',
                    'values' => ['next_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with due date is last week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->subWeek()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is',
                    'values' => ['last_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with due date is not last week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->subWeek()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is_not',
                    'values' => ['last_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with due date is this month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->startOfMonth()->subDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is',
                    'values' => ['this_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with due date is not this month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->startOfMonth()->subDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is_not',
                    'values' => ['this_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with due date is next month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->addMonth()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->startOfMonth()->subDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is',
                    'values' => ['next_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with due date is not next month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->addMonth()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->startOfMonth()->subDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is_not',
                    'values' => ['next_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with due date is last month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->subMonth()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->addDay(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is',
                    'values' => ['last_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with due date is not last month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->subMonth()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'due_date' => CarbonImmutable::now()->addDay(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'due_date',
                    'operation' => 'is_not',
                    'values' => ['last_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with created date today filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->subDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is',
                    'values' => ['today'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with created date not today filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->subDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is_not',
                    'values' => ['today'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with created date tomorrow filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->addDays(1),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is',
                    'values' => ['tomorrow'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with created date not tomorrow filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->addDays(1),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->addDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is_not',
                    'values' => ['tomorrow'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with created date is yesterday filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->subDays(1),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->addDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is',
                    'values' => ['yesterday'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with created date is not yesterday filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->subDays(1),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->addDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is_not',
                    'values' => ['yesterday'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with created date is this week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->startOfWeek()->addDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is',
                    'values' => ['this_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with created date is not this week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->startOfWeek()->addDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is_not',
                    'values' => ['this_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with created date is next week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->addWeek()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is',
                    'values' => ['next_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with created date is not next week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->addWeek()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is_not',
                    'values' => ['next_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with created date is last week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->subWeek()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is',
                    'values' => ['last_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with created date is not last week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->subWeek()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is_not',
                    'values' => ['last_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with created date is this month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->startOfMonth()->subDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is',
                    'values' => ['this_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with created date is not this month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->startOfMonth()->subDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is_not',
                    'values' => ['this_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with created date is next month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->addMonth()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->startOfMonth()->subDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is',
                    'values' => ['next_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with created date is not next month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->addMonth()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->startOfMonth()->subDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is_not',
                    'values' => ['next_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with created date is last month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->subMonth()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is',
                    'values' => ['last_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with created date is not last month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now()->subMonth()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'created_at' => CarbonImmutable::now(),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'created_date',
                    'operation' => 'is_not',
                    'values' => ['last_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderTwo->work_order_uuid);
    }
});

test('The work order list API with paused date today filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'paused_at' => CarbonImmutable::now(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'paused_at' => CarbonImmutable::now()->subDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'paused_date',
                    'operation' => 'is',
                    'values' => ['today'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with paused date is this week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'paused_at' => CarbonImmutable::now()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'paused_at' => CarbonImmutable::now()->startOfWeek()->addDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'paused_date',
                    'operation' => 'is',
                    'values' => ['this_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with paused date is this month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'paused_at' => CarbonImmutable::now()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'paused_at' => CarbonImmutable::now()->startOfMonth()->subDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'paused_date',
                    'operation' => 'is',
                    'values' => ['this_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with completed date today filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'work_completed_at' => CarbonImmutable::now(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'work_completed_at' => CarbonImmutable::now()->subDays(2),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'completed_date',
                    'operation' => 'is',
                    'values' => ['today'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with completed date is this week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'work_completed_at' => CarbonImmutable::now()->startOfWeek()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'work_completed_at' => CarbonImmutable::now()->startOfWeek()->addDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'completed_date',
                    'operation' => 'is',
                    'values' => ['this_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with completed date is this month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'work_completed_at' => CarbonImmutable::now()->startOfMonth()->addDay(),
        ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
            'work_completed_at' => CarbonImmutable::now()->startOfMonth()->subDays(10),
        ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'completed_date',
                    'operation' => 'is',
                    'values' => ['this_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with schedule date today filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $technician = Technician::factory()->create([
        'organization_id' => $organization->organization_id,
        'user_id' => $user->user_id,
    ]);

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
        ]);

    $appointmentOne = TechnicianAppointment::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_id' => $technician->technician_id,
        'work_order_id' => $workOrderOne->work_order_id,
    ]);
    WorkOrderServiceCall::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_appointment_id' => $appointmentOne->technician_appointment_id,
        'work_order_id' => $workOrderOne->work_order_id,
        'scheduled_start_time' => CarbonImmutable::now(),
        'scheduled_end_time' => CarbonImmutable::now()->addHours(5),
    ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
        ]);

    $appointmentTwo = TechnicianAppointment::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_id' => $technician->technician_id,
        'work_order_id' => $workOrderOne->work_order_id,
    ]);
    WorkOrderServiceCall::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_appointment_id' => $appointmentTwo->technician_appointment_id,
        'work_order_id' => $workOrderOne->work_order_id,
        'scheduled_start_time' => CarbonImmutable::now()->subDays(2),
        'scheduled_end_time' => CarbonImmutable::now()->subDays(2)->addHours(5),
    ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'scheduled_date',
                    'operation' => 'is',
                    'values' => ['today'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with schedule date is this week filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $technician = Technician::factory()->create([
        'organization_id' => $organization->organization_id,
        'user_id' => $user->user_id,
    ]);

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
        ]);

    $appointmentOne = TechnicianAppointment::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_id' => $technician->technician_id,
        'work_order_id' => $workOrderOne->work_order_id,
    ]);
    WorkOrderServiceCall::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_appointment_id' => $appointmentOne->technician_appointment_id,
        'work_order_id' => $workOrderOne->work_order_id,
        'scheduled_start_time' => CarbonImmutable::now()->startOfWeek()->addDay(),
        'scheduled_end_time' => CarbonImmutable::now()->startOfWeek()->addDay()->addHours(5),
    ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
        ]);

    $appointmentTwo = TechnicianAppointment::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_id' => $technician->technician_id,
        'work_order_id' => $workOrderOne->work_order_id,
    ]);
    WorkOrderServiceCall::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_appointment_id' => $appointmentTwo->technician_appointment_id,
        'work_order_id' => $workOrderOne->work_order_id,
        'scheduled_start_time' => CarbonImmutable::now()->startOfWeek()->addDays(10),
        'scheduled_end_time' => CarbonImmutable::now()->startOfWeek()->addDays(10)->addHours(5),
    ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'scheduled_date',
                    'operation' => 'is',
                    'values' => ['this_week'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API with schedule date is this month filter return correct data', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list');

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $workOrderStatus = WorkOrderStatus::where('slug', EnumsWorkOrderStatus::NEW())->first();
    if (! $workOrderStatus) {
        $workOrderStatus = WorkOrderStatus::factory()->create([
            'label' => 'New',
            'slug' => EnumsWorkOrderStatus::NEW(),
        ]);
    }

    $technician = Technician::factory()->create([
        'organization_id' => $organization->organization_id,
        'user_id' => $user->user_id,
    ]);

    $workOrderOne = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
        ]);

    $appointmentOne = TechnicianAppointment::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_id' => $technician->technician_id,
        'work_order_id' => $workOrderOne->work_order_id,
    ]);
    WorkOrderServiceCall::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_appointment_id' => $appointmentOne->technician_appointment_id,
        'work_order_id' => $workOrderOne->work_order_id,
        'scheduled_start_time' => CarbonImmutable::now()->startOfMonth()->addDay()->addDay(),
        'scheduled_end_time' => CarbonImmutable::now()->startOfMonth()->addDay()->addDay()->addHours(5),
    ]);

    $workOrderTwo = WorkOrder::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->for($workOrderStatus, 'status')
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => Created::$name,
        ]);

    $appointmentTwo = TechnicianAppointment::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_id' => $technician->technician_id,
        'work_order_id' => $workOrderOne->work_order_id,
    ]);
    WorkOrderServiceCall::factory()->create([
        'organization_id' => $organization->organization_id,
        'technician_appointment_id' => $appointmentTwo->technician_appointment_id,
        'work_order_id' => $workOrderOne->work_order_id,
        'scheduled_start_time' => CarbonImmutable::now()->startOfMonth()->subDays(10),
        'scheduled_end_time' => CarbonImmutable::now()->startOfMonth()->subDays(10)->addHours(5),
    ]);

    $workOrders = [$workOrderOne, $workOrderTwo];

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'scheduled_date',
                    'operation' => 'is',
                    'values' => ['this_month'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(1, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->work_order_id)->toBe($workOrderOne->work_order_uuid);
    }
});

test('The work order list API response return correct response for app with status filter', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list', UserTypes::TECHNICIAN());

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;
    $user->refresh();
    $technician = $user->technician;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'Device-Type' => 'Mobile',
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
        ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        $appointment = TechnicianAppointment::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_id' => $technician->technician_id,
            'work_order_id' => $workOrder->work_order_id,
        ]);

        WorkOrderServiceCall::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_appointment_id' => $appointment->technician_appointment_id,
            'work_order_id' => $workOrder->work_order_id,
            'state' => EnRoute::class,
            'is_active' => 'yes',
        ]);

        if ($count <= 3) {
            $workOrder->state = EnumsWorkOrderStatus::WORK_IN_PROGRESS();
            $workOrder->save();
        } else {
            $workOrder->state = EnumsWorkOrderStatus::SCHEDULED();
            $workOrder->save();
        }

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'group_type' => 'open',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'status',
                    'operation' => 'is',
                    'values' => ['en_route'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(3, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->status->value)->toBe(EnRoute::$name);
        expect(count($workOrder->trips))->toBe(1);
        expect($workOrder->trips[0]->status->value)->toBe('en_route');
    }
});

test('The work order list API response return correct response for app with multiple status filter', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list', UserTypes::TECHNICIAN());

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;
    $user->refresh();
    $technician = $user->technician;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'Device-Type' => 'Mobile',
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
        ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        $appointment = TechnicianAppointment::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_id' => $technician->technician_id,
            'work_order_id' => $workOrder->work_order_id,
        ]);

        WorkOrderServiceCall::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_appointment_id' => $appointment->technician_appointment_id,
            'work_order_id' => $workOrder->work_order_id,
            'state' => EnRoute::class,
            'is_active' => 'yes',
        ]);

        if ($count <= 3) {
            $workOrder->state = EnumsWorkOrderStatus::WORK_IN_PROGRESS();
            $workOrder->save();
        } else {
            $workOrder->state = EnumsWorkOrderStatus::SCHEDULED();
            $workOrder->save();
        }

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'group_type' => 'open',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'status',
                    'operation' => 'is',
                    'values' => ['en_route', 'scheduled'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->status->value)->toBeIn([
            EnRoute::$name,
            EnumsWorkOrderStatus::SCHEDULED(),
        ]);
        expect(count($workOrder->trips))->toBe(1);
        expect($workOrder->trips[0]->status->value)->toBeIn([
            'en_route',
        ]);
    }
});

test('The work order list API response return correct response for app with multiple work in progress status filter', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list', UserTypes::TECHNICIAN());

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;
    $user->refresh();
    $technician = $user->technician;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'Device-Type' => 'Mobile',
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => EnumsWorkOrderStatus::WORK_IN_PROGRESS(),
        ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        $appointment = TechnicianAppointment::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_id' => $technician->technician_id,
            'work_order_id' => $workOrder->work_order_id,
        ]);

        $trip = WorkOrderServiceCall::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_appointment_id' => $appointment->technician_appointment_id,
            'work_order_id' => $workOrder->work_order_id,
            'state' => EnRoute::class,
            'is_active' => 'yes',
        ]);

        if ($count <= 3) {
            $trip->state = new Working($trip);
            $trip->save();
        }

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'group_type' => 'open',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'status',
                    'operation' => 'is',
                    'values' => ['working'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(3, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->status->value)->toBeIn([
            EnumsWorkOrderStatus::WORK_IN_PROGRESS(),
        ]);
        expect(count($workOrder->trips))->toBe(1);
        expect($workOrder->trips[0]->status->value)->toBeIn([
            'working',
        ]);
    }
});

test('The work order list API response return correct response for app with multiple work in progress and en-route status filter', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list', UserTypes::TECHNICIAN());

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;
    $user->refresh();
    $technician = $user->technician;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'Device-Type' => 'Mobile',
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => EnumsWorkOrderStatus::WORK_IN_PROGRESS(),
        ]);

    foreach ($workOrders as $workOrder) {
        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        $appointment = TechnicianAppointment::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_id' => $technician->technician_id,
            'work_order_id' => $workOrder->work_order_id,
        ]);

        $trip = WorkOrderServiceCall::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_appointment_id' => $appointment->technician_appointment_id,
            'work_order_id' => $workOrder->work_order_id,
            'state' => EnRoute::class,
            'is_active' => 'yes',
        ]);

        if ($count <= 3) {
            $trip->state = new Working($trip);
            $trip->save();
        }

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '',
        'group_type' => 'open',
        'filter' => [
            'group_op' => '',
            'fl_group' => [
                [
                    'field' => 'status',
                    'operation' => 'is',
                    'values' => ['working', 'en_route', 'schedule'],
                ],
            ],
        ],
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data');

    $result = json_decode($result->getContent());

    foreach ($result->data as $workOrder) {
        expect($workOrder->status->value)->toBeIn([
            EnumsWorkOrderStatus::WORK_IN_PROGRESS(),
            'en_route',
        ]);
        expect(count($workOrder->trips))->toBe(1);
        expect($workOrder->trips[0]->status->value)->toBeIn([
            'working', 'en_route',
        ]);
    }
});

test('The work order list API scheduled sort : Earliest work property ', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list', UserTypes::TECHNICIAN());

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;
    $user->refresh();
    $technician = $user->technician;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'Device-Type' => 'Mobile',
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $now = CarbonImmutable::now();
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => EnumsWorkOrderStatus::SCHEDULED(),
        ]);

    foreach ($workOrders as $workOrder) {
        $scheduleStartTime = $now->addHours($count);

        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        $appointment = TechnicianAppointment::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_id' => $technician->technician_id,
            'work_order_id' => $workOrder->work_order_id,
        ]);

        WorkOrderServiceCall::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_appointment_id' => $appointment->technician_appointment_id,
            'work_order_id' => $workOrder->work_order_id,
            'state' => Scheduled::class,
            'is_active' => 'yes',
            'scheduled_start_time' => $scheduleStartTime,
            'scheduled_end_time' => $scheduleStartTime,
        ]);

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '-scheduled',
        'group_type' => 'open',
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data');

    $result = json_decode($result->getContent());
    $previousStartTime = null;

    foreach ($result->data as $workOrder) {
        expect(count($workOrder->trips))->toBe(1);

        $currentStartTime = CarbonImmutable::parse($workOrder->trips[0]->appointment->schedule_start_time);

        // If not the first iteration, compare with previous
        if ($previousStartTime !== null) {
            expect($previousStartTime->gt($currentStartTime))->toBeTrue();
        }

        $previousStartTime = $currentStartTime;
    }
});

test('The work order list API scheduled sort : Latest work property ', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list', UserTypes::TECHNICIAN());

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;
    $user->refresh();
    $technician = $user->technician;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'Device-Type' => 'Mobile',
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $now = CarbonImmutable::now();
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => EnumsWorkOrderStatus::SCHEDULED(),
        ]);

    foreach ($workOrders as $workOrder) {
        $scheduleStartTime = $now->addHours($count);

        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        $appointment = TechnicianAppointment::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_id' => $technician->technician_id,
            'work_order_id' => $workOrder->work_order_id,
        ]);

        WorkOrderServiceCall::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_appointment_id' => $appointment->technician_appointment_id,
            'work_order_id' => $workOrder->work_order_id,
            'state' => Scheduled::class,
            'is_active' => 'yes',
            'scheduled_start_time' => $scheduleStartTime,
            'scheduled_end_time' => $scheduleStartTime,
        ]);

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => 'scheduled',
        'group_type' => 'open',
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data');

    $result = json_decode($result->getContent());
    $previousStartTime = null;

    foreach ($result->data as $workOrder) {
        expect(count($workOrder->trips))->toBe(1);

        $currentStartTime = CarbonImmutable::parse($workOrder->trips[0]->appointment->schedule_start_time);

        // If not the first iteration, compare with previous
        if ($previousStartTime !== null) {
            expect($previousStartTime->lt($currentStartTime))->toBeTrue();
        }

        $previousStartTime = $currentStartTime;
    }
});

test('The work order list API pause sort : Earliest work property ', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list', UserTypes::TECHNICIAN());

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;
    $user->refresh();
    $technician = $user->technician;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'Device-Type' => 'Mobile',
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $now = CarbonImmutable::now();
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => EnumsWorkOrderStatus::PAUSED(),
        ]);

    foreach ($workOrders as $workOrder) {

        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        $appointment = TechnicianAppointment::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_id' => $technician->technician_id,
            'work_order_id' => $workOrder->work_order_id,
        ]);

        WorkOrderServiceCall::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_appointment_id' => $appointment->technician_appointment_id,
            'work_order_id' => $workOrder->work_order_id,
            'state' => Paused::class,
            'is_active' => 'yes',
            'status' => ServiceCallStatus::COMPLETED(),
        ]);

        $workOrder->paused_at = $now->addHours($count);
        $workOrder->save();

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '-paused',
        'group_type' => 'paused',
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data');

    $result = json_decode($result->getContent());
    $previousStartTime = null;

    foreach ($result->data as $workOrder) {
        $currentStartTime = CarbonImmutable::parse($workOrder->paused_at);

        // If not the first iteration, compare with previous
        if ($previousStartTime !== null) {
            expect($previousStartTime->gt($currentStartTime))->toBeTrue();
        }

        $previousStartTime = $currentStartTime;
    }
});

test('The work order list API paused sort : Latest work property ', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list', UserTypes::TECHNICIAN());

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;
    $user->refresh();
    $technician = $user->technician;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'Device-Type' => 'Mobile',
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $now = CarbonImmutable::now();
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => EnumsWorkOrderStatus::PAUSED(),
        ]);

    foreach ($workOrders as $workOrder) {

        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        $appointment = TechnicianAppointment::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_id' => $technician->technician_id,
            'work_order_id' => $workOrder->work_order_id,
        ]);

        WorkOrderServiceCall::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_appointment_id' => $appointment->technician_appointment_id,
            'work_order_id' => $workOrder->work_order_id,
            'state' => Paused::class,
            'is_active' => 'yes',
            'status' => ServiceCallStatus::COMPLETED(),
        ]);

        $workOrder->paused_at = $now->addHours($count);
        $workOrder->save();

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => 'paused',
        'group_type' => 'paused',
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data');

    $result = json_decode($result->getContent());
    $previousStartTime = null;

    foreach ($result->data as $workOrder) {
        $currentStartTime = CarbonImmutable::parse($workOrder->paused_at);

        // If not the first iteration, compare with previous
        if ($previousStartTime !== null) {
            expect($previousStartTime->lt($currentStartTime))->toBeTrue();
        }

        $previousStartTime = $currentStartTime;
    }
});

test('The work order list API closed sort : Earliest work property ', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list', UserTypes::TECHNICIAN());

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;
    $user->refresh();
    $technician = $user->technician;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'Device-Type' => 'Mobile',
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $now = CarbonImmutable::now();
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => EnumsWorkOrderStatus::QUALITY_CHECK(),
        ]);

    foreach ($workOrders as $workOrder) {

        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        $appointment = TechnicianAppointment::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_id' => $technician->technician_id,
            'work_order_id' => $workOrder->work_order_id,
        ]);

        WorkOrderServiceCall::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_appointment_id' => $appointment->technician_appointment_id,
            'work_order_id' => $workOrder->work_order_id,
            'state' => Ended::class,
            'is_active' => 'yes',
            'status' => ServiceCallStatus::COMPLETED(),
        ]);

        $workOrder->work_completed_at = $now->addHours($count);
        $workOrder->save();

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => '-completed',
        'group_type' => 'closed',
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data');

    $result = json_decode($result->getContent());
    $previousStartTime = null;

    foreach ($result->data as $workOrder) {
        $currentStartTime = CarbonImmutable::parse($workOrder->completed_at);

        // If not the first iteration, compare with previous
        if ($previousStartTime !== null) {
            expect($previousStartTime->gt($currentStartTime))->toBeTrue();
        }

        $previousStartTime = $currentStartTime;
    }
});

test('The work order list API closed sort : Latest work property ', function () {
    $response = $this->createOrganizationAndUser(FeatureEnum::WORK_ORDER_MANAGEMENT(), 'WorkOrderManagement.WorkOrder.list', UserTypes::TECHNICIAN());

    $organization = $response->organization;

    $organization->makeCurrent();

    $user = $response->user;
    $user->refresh();
    $technician = $user->technician;

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'Device-Type' => 'Mobile',
    ];

    $timezone = Timezone::factory()->create();

    $property = Property::factory()
        ->for($organization)
        ->create();

    // create a dummy service request
    $serviceRequest = ServiceRequest::factory()
        ->for($organization)
        ->for($property)
        ->for($timezone)
        ->create();

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create(['slug' => Assigned::$name]);
    }

    $problemDiagnosis = ProblemDiagnosis::factory()->create();

    // Create a dummy issue
    $issue = Issue::factory()
        ->for($organization)
        ->for($serviceRequest)
        ->for($problemDiagnosis)
        ->create([
            'user_id' => $user->user_id,
        ]);

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create(['slug' => WorkOrderIssueAssigned::$name]);
    }

    $count = 5;
    $now = CarbonImmutable::now();
    $workOrders = WorkOrder::factory($count)
        ->for($organization)
        ->for($serviceRequest)
        ->for($property)
        ->create([
            'timezone_id' => $user->timezone_id,
            'state' => EnumsWorkOrderStatus::QUALITY_CHECK(),
        ]);

    foreach ($workOrders as $workOrder) {

        WorkOrderIssue::factory()->create([
            'work_order_id' => $workOrder->work_order_id,
            'issue_id' => $issue->issue_id,
            'organization_id' => $organization->organization_id,
            'state' => WorkOrderIssueAssigned::class,
        ]);

        $appointment = TechnicianAppointment::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_id' => $technician->technician_id,
            'work_order_id' => $workOrder->work_order_id,
        ]);

        WorkOrderServiceCall::factory()->create([
            'organization_id' => $organization->organization_id,
            'technician_appointment_id' => $appointment->technician_appointment_id,
            'work_order_id' => $workOrder->work_order_id,
            'state' => Ended::class,
            'is_active' => 'yes',
            'status' => ServiceCallStatus::COMPLETED(),
        ]);

        $workOrder->work_completed_at = $now->addHours($count);
        $workOrder->save();

        $count--;
    }

    $filterPayload = [
        'search' => '',
        'group' => '',
        'sort' => 'completed',
        'group_type' => 'closed',
    ];

    $result = $this->getJson(route('workOrder.index', $filterPayload), $header);
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonCount(5, 'data');

    $result = json_decode($result->getContent());
    $previousStartTime = null;

    foreach ($result->data as $workOrder) {
        $currentStartTime = CarbonImmutable::parse($workOrder->completed_at);

        // If not the first iteration, compare with previous
        if ($previousStartTime !== null) {
            expect($previousStartTime->lt($currentStartTime))->toBeTrue();
        }

        $previousStartTime = $currentStartTime;
    }
});

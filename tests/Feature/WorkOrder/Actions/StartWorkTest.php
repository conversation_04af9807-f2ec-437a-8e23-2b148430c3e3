<?php

use App\Actions\WorkOrders\StartWork;
use App\Enums\Boolean;
use App\Enums\Feature as FeatureEnum;
use App\Enums\ServiceCallActionTypes;
use App\Enums\UserTypes;
use App\Events\WorkOrder\Trip\TripWorking;
use App\Exceptions\WorkOrderException;
use App\Models\Country;
use App\Models\Feature;
use App\Models\Issue;
use App\Models\IssueStatus;
use App\Models\Organization;
use App\Models\ProblemDiagnosis;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\State;
use App\Models\Technician;
use App\Models\TechnicianAppointment;
use App\Models\Timezone;
use App\Models\User;
use App\Models\Vendor;
use App\Models\VendorAllocation;
use App\Models\VendorAppointment;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\Models\WorkOrderIssueStatus;
use App\Models\WorkOrderServiceCall;
use App\Models\WorkOrderServiceCallLog;
use App\States\Issue\Assigned;
use App\States\ServiceCalls\Ended;
use App\States\ServiceCalls\EnRoute;
use App\States\ServiceCalls\Paused;
use App\States\ServiceCalls\Working;
use App\States\ServiceRequests\Scoping;
use App\States\WorkOrderIssue\Assigned as WorkOrderIssueAssigned;
use App\States\WorkOrders\QualityCheck;
use App\States\WorkOrders\WorkInProgress;
use Carbon\CarbonImmutable;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lorisleiva\Actions\ActionRequest;

beforeEach(function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $this->organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $this->organization->makeCurrent();

    $this->property = Property::factory()
        ->for($this->organization)
        ->create();

    // create a dummy service request
    $this->serviceRequest = ServiceRequest::factory()
        ->for($this->organization)
        ->for($this->property)
        ->for($timezone)
        ->create([
            'state' => Scoping::class,
        ]);

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create([
            'label' => Assigned::$name,
            'slug' => Assigned::$name,
        ]);
    }

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueAssigned::$name)->count()) {
        WorkOrderIssueStatus::factory()->create([
            'label' => WorkOrderIssueAssigned::$name,
            'slug' => WorkOrderIssueAssigned::$name,
        ]);
    }

    $this->problemDiagnosis = ProblemDiagnosis::factory()->create();

    $this->issue = Issue::factory()
        ->for($this->organization)
        ->for($this->problemDiagnosis)
        ->for($this->serviceRequest)
        ->create([
            'state' => Assigned::class,
        ]);

    $this->workOrder = WorkOrder::factory()
        ->for($this->organization)
        ->for($this->serviceRequest)
        ->for($this->property)
        ->create([
            'state' => WorkInProgress::class,
        ]);

    $this->workOrderIssue = WorkOrderIssue::factory()->create([
        'work_order_id' => $this->workOrder->work_order_id,
        'issue_id' => $this->issue->issue_id,
        'organization_id' => $this->organization->organization_id,
        'state' => WorkOrderIssueAssigned::class,
    ]);

    $this->techUser = User::factory()
        ->for($timezone)
        ->for($this->organization)
        ->create([
            'user_type' => UserTypes::TECHNICIAN(),
        ]);

    $this->technician = Technician::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'user_id' => $this->techUser->user_id,
    ]);

    $this->appointment = TechnicianAppointment::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'technician_id' => $this->technician->technician_id,
        'work_order_id' => $this->workOrder->work_order_id,
    ]);

    $this->trip = WorkOrderServiceCall::create([
        'organization_id' => $this->organization->organization_id,
        'work_order_id' => $this->workOrder->work_order_id,
        'technician_appointment_id' => $this->appointment->technician_appointment_id,
        'scheduled_start_time' => $this->appointment->scheduled_start_time,
        'scheduled_end_time' => $this->appointment->scheduled_end_time,
        'duration_minutes' => 30,
        'state' => EnRoute::class,
        'is_active' => 1,
        'en_route_at' => CarbonImmutable::now(),
    ]);
});

test('User with a valid token can access start work api.', function () {
    // create the feature
    $feature = Feature::where('name', FeatureEnum::WORK_ORDER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => FeatureEnum::WORK_ORDER_MANAGEMENT(),
        ])->create();
    }

    $this->organization->features()->save($feature);
    $user = $this->createUserWithPermission($feature, $this->organization, 'WorkOrderManagement.WorkOrder.startTimer');

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $this->header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $result = $this->postJson(route('workOrder.action.startWork', [
        'workOrder' => $this->workOrder->work_order_uuid,
    ]), [], $this->header);

    // Assertion
    expect($result)
        ->assertStatus(Response::HTTP_OK);
});

test('start work api return proper keys for device', function () {
    // create the feature
    $feature = Feature::where('name', FeatureEnum::WORK_ORDER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => FeatureEnum::WORK_ORDER_MANAGEMENT(),
        ])->create();
    }

    $this->organization->features()->save($feature);
    $user = $this->createUserWithPermission($feature, $this->organization, 'WorkOrderManagement.WorkOrder.startTimer');

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
        'HTTP_DEVICE_TYPE' => 'Mobile',
    ];

    $result = $this->postJson(route('workOrder.action.startWork', [
        'workOrder' => $this->workOrder->work_order_uuid,
    ]), [], $header);

    // Assertion
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonStructure([
            'work_order_id',
            'trips' => [
                [
                    'trip_id',
                    'appointment' => [
                        'work_started_at',
                    ],
                    'status' => [
                        'label',
                        'value',
                        'color_class',
                    ],
                ],
            ],
            'status',
            'abilities',
        ]);
});

test('start work api return proper keys for web', function () {
    // create the feature
    $feature = Feature::where('name', FeatureEnum::WORK_ORDER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => FeatureEnum::WORK_ORDER_MANAGEMENT(),
        ])->create();
    }

    $this->organization->features()->save($feature);
    $user = $this->createUserWithPermission($feature, $this->organization, 'WorkOrderManagement.WorkOrder.startTimer');

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $result = $this->postJson(route('workOrder.action.startWork', [
        'workOrder' => $this->workOrder->work_order_uuid,
    ]), [], $header);

    // Assertion
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonStructure([
            'work_order_id',
            'trip' => [
                'trip_id',
                'work_started_at',
                'total_drive_time_in_sec',
                'status' => [
                    'label',
                    'value',
                    'color_class',
                ],
            ],
            'status',
            'abilities',
        ]);
});

test('User with an invalid token cannot access start work api.', function ($token) {
    $header = [
        'Authorization' => $token,
    ];

    $result = $this->postJson(route('workOrder.action.startWork', [
        'workOrder' => $this->workOrder->work_order_uuid,
    ]), [], $header);

    // Assertion
    expect($result)->assertStatus(Response::HTTP_UNAUTHORIZED);
})->with([
    'Bearer invalid-token',
    '',
]);

test('Handles UserNotFoundException correctly in asController method.', function () {
    $request = Mockery::mock(Request::class);
    $request->shouldReceive('user')->andReturn(null);

    $startWorkAction = new StartWork;

    /** @var Request $request */
    $response = $startWorkAction->asController($request, $this->workOrder);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_UNPROCESSABLE_ENTITY);
});

test('Handles the work order transaction exception correctly in asController method', function () {
    $request = Mockery::mock(Request::class);
    $request->shouldReceive('user')->andReturn($this->techUser);

    $startWorkAction = new StartWork;

    $this->workOrder->state = new QualityCheck($this->workOrder);
    $this->workOrder->save();

    /** @var Request $request */
    $response = $startWorkAction->asController($request, $this->workOrder);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_UNPROCESSABLE_ENTITY);
});

test('Can not start a work with out trip', function () {
    $request = Mockery::mock(Request::class);
    $request->shouldReceive('user')->andReturn($this->techUser);

    $startWorkAction = new StartWork;

    $this->trip->delete();

    /** @var Request $request */
    $response = $startWorkAction->asController($request, $this->workOrder);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_UNPROCESSABLE_ENTITY);
});

test('Can not start a work if it trip is not in En route status', function () {
    $request = Mockery::mock(Request::class);
    $request->shouldReceive('user')->andReturn($this->techUser);

    $startWorkAction = new StartWork;

    $this->trip->state = new Ended($this->trip);
    $this->trip->save();

    /** @var Request $request */
    $response = $startWorkAction->asController($request, $this->workOrder);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_UNPROCESSABLE_ENTITY);
});

test('Can not start work if it has no appointment', function () {
    $request = Mockery::mock(Request::class);
    $request->shouldReceive('user')->andReturn($this->techUser);

    $startWorkAction = new StartWork;

    $this->appointment->delete();

    /** @var Request $request */
    $response = $startWorkAction->asController($request, $this->workOrder);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_FORBIDDEN);
});

test('Can not start work if the working technician and requested technician not equal', function () {
    $techUser2 = User::factory()
        ->for($this->organization)
        ->create([
            'user_type' => UserTypes::TECHNICIAN(),
        ]);

    Technician::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'user_id' => $techUser2->user_id,
    ]);

    $techUser2->refresh();
    $request = Mockery::mock(Request::class);
    $request->shouldReceive('user')->andReturn($techUser2);

    $startWorkAction = new StartWork;

    /** @var Request $request */
    $response = $startWorkAction->asController($request, $this->workOrder);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_FORBIDDEN);
});

test('Handles exceptions properly during asController method execution.', function () {
    $this->techUser->refresh();
    $request = Mockery::mock(Request::class);
    $request->shouldReceive('user')->andReturn($this->techUser);

    $startWorkAction = Mockery::mock(StartWork::class)->makePartial();
    $startWorkAction->shouldReceive('handle')->andThrow(new Exception('Unexpected error'));

    /** @var StartWork $startWorkAction */
    $response = $startWorkAction->asController($request, $this->workOrder);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_INTERNAL_SERVER_ERROR);
});

test('authorize method returns true when user has permission to start timer', function () {
    // create the feature
    $feature = Feature::where('name', FeatureEnum::WORK_ORDER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => FeatureEnum::WORK_ORDER_MANAGEMENT(),
        ])->create();
    }

    $this->organization->features()->save($feature);
    $user = $this->createUserWithPermission($feature, $this->organization, 'WorkOrderManagement.WorkOrder.startTimer');

    $request = Mockery::mock(ActionRequest::class);
    $request->shouldReceive('user')->andReturn($user);
    $request->shouldReceive('route')->with('workOrder')->andReturn($this->workOrder);
    $startWorkAction = new StartWork;

    /** @var ActionRequest $request */
    $result = $startWorkAction->authorize($request);

    expect($result)->toBeTrue();
});

test('authorize method returns false when user is not authenticated.', function () {
    $request = Mockery::mock(ActionRequest::class);
    $request->shouldReceive('user')->andReturn(null);

    $startWorkAction = new StartWork;

    /** @var ActionRequest $request */
    $result = $startWorkAction->authorize($request);

    expect($result)->toBeFalse();
});

test('authorize method returns false when user does not have permission to start work.', function () {
    // create the feature
    $feature = Feature::where('name', FeatureEnum::WORK_ORDER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => FeatureEnum::WORK_ORDER_MANAGEMENT(),
        ])->create();
    }

    $this->organization->features()->save($feature);
    $user = $this->createUserWithPermission($feature, $this->organization, 'WorkOrderManagement.WorkOrder.delete');

    $request = Mockery::mock(ActionRequest::class);
    $request->shouldReceive('user')->andReturn($user);
    $request->shouldReceive('route')->with('workOrder')->andReturn($this->workOrder);
    $startWorkAction = new StartWork;

    /** @var ActionRequest $request */
    $result = $startWorkAction->authorize($request);

    expect($result)->toBeFalse();
});

test('handle method work successfully and trigger event', function () {
    Event::fake([TripWorking::class]);

    $this->trip->work_started_at = null;
    $this->trip->work_timer_resumed_at = null;
    $this->trip->save();

    $tripEnrouteLog = WorkOrderServiceCallLog::create([
        'work_order_service_call_id' => $this->trip->work_order_service_call_id,
        'organization_id' => $this->workOrder->organization_id,
        'work_order_id' => $this->workOrder->work_order_id,
        'action' => ServiceCallActionTypes::EN_ROUTE(),
        'action_start_by_user_id' => $this->techUser->user_id,
        'action_started_at' => CarbonImmutable::now(),
    ]);

    $tripEnrouteLog->created_at = CarbonImmutable::now()->subSeconds(20);
    $tripEnrouteLog->save();

    (new StartWork)->handle(
        $this->workOrder,
        $this->techUser,
        $this->trip
    );

    Event::assertDispatched(TripWorking::class);

    $this->workOrder->refresh();
    $this->trip->refresh();
    $this->appointment->refresh();
    $tripEnrouteLog->refresh();

    expect($this->workOrder->state)->toBeInstanceOf(WorkInProgress::class);
    expect($this->trip->state)->toBeInstanceOf(Working::class);

    expect($this->trip->last_modified_user)->toBe($this->techUser->user_id);
    expect($this->trip->last_modified_at)->not->toBeNull();

    expect($this->trip->work_started_at)->not->toBeNull();
    expect($this->trip->work_timer_resumed_at)->toBeNull();
    expect($tripEnrouteLog->action_ended_at)->not->toBeNull();
    expect($tripEnrouteLog->action_end_by_user_id)->toBe($this->techUser->user_id);

    $this->trip->load([
        'latestTripLogs:work_order_service_call_id,action,created_at',
    ]);

    expect($this->trip->latestTripLogs->first()->action)->toBe(ServiceCallActionTypes::WORKING());
});

test('handle method work successfully for a timer stopped trip', function () {

    $this->trip->state = new Paused($this->trip);
    $this->trip->save();

    $this->appointment->actual_start_time = null;
    $this->appointment->work_timer_resumed_at = null;
    $this->appointment->save();

    $tripEnrouteLog = WorkOrderServiceCallLog::create([
        'work_order_service_call_id' => $this->trip->work_order_service_call_id,
        'organization_id' => $this->workOrder->organization_id,
        'work_order_id' => $this->workOrder->work_order_id,
        'action' => ServiceCallActionTypes::EN_ROUTE(),
        'action_start_by_user_id' => $this->techUser->user_id,
        'action_started_at' => CarbonImmutable::now(),
    ]);

    $tripEnrouteLog->created_at = CarbonImmutable::now()->subSeconds(20);
    $tripEnrouteLog->save();

    (new StartWork)->handle(
        $this->workOrder,
        $this->techUser,
        $this->trip
    );

    $this->workOrder->refresh();
    $this->trip->refresh();
    $this->appointment->refresh();
    $tripEnrouteLog->refresh();

    expect($this->workOrder->state)->toBeInstanceOf(WorkInProgress::class);
    expect($this->trip->state)->toBeInstanceOf(Working::class);

    expect($this->trip->last_modified_user)->toBe($this->techUser->user_id);
    expect($this->trip->last_modified_at)->not->toBeNull();

    expect($this->trip->work_timer_resumed_at)->not->toBeNull();
    expect($this->trip->work_started_at)->toBeNull();
    expect($tripEnrouteLog->action_ended_at)->not->toBeNull();
    expect($tripEnrouteLog->action_end_by_user_id)->toBe($this->techUser->user_id);

    $this->trip->load([
        'latestTripLogs:work_order_service_call_id,action,created_at',
    ]);

    expect($this->trip->latestTripLogs->first()->action)->toBe(ServiceCallActionTypes::WORKING());
});

test('handle method throw exception if appointment is null', function () {
    $this->appointment->delete();

    $this->trip->refresh();

    (new StartWork)->handle(
        $this->workOrder,
        $this->techUser,
        $this->trip,
        true,
    );
})->throws(WorkOrderException::class);

test('handle method work successfully and trigger event for vendor', function () {
    Event::fake([TripWorking::class]);

    $vendor = Vendor::factory()->create([
        'is_active' => Boolean::YES(),
    ]);

    $vendorAppointment = VendorAppointment::create([
        'organization_id' => $this->organization->organization_id,
        'vendor_instructions' => 'test',
        'vendor_id' => $vendor->vendor_id,
    ]);

    VendorAllocation::create([
        'organization_id' => $this->workOrder->organization_id,
        'work_order_id' => $this->workOrder->work_order_id,
        'vendor_id' => $vendor->vendor_id,
        'vendor_appointment_id' => $vendorAppointment->vendor_appointment_id,
    ]);

    $this->trip->technician_appointment_id = null;
    $this->trip->vendor_appointment_id = $vendorAppointment->vendor_appointment_id;
    $this->trip->work_started_at = null;
    $this->trip->work_timer_resumed_at = null;
    $this->trip->save();

    $tripEnrouteLog = WorkOrderServiceCallLog::create([
        'work_order_service_call_id' => $this->trip->work_order_service_call_id,
        'organization_id' => $this->workOrder->organization_id,
        'work_order_id' => $this->workOrder->work_order_id,
        'action' => ServiceCallActionTypes::EN_ROUTE(),
        'action_start_by_user_id' => $this->techUser->user_id,
        'action_started_at' => CarbonImmutable::now(),
    ]);

    $tripEnrouteLog->created_at = CarbonImmutable::now()->subSeconds(20);
    $tripEnrouteLog->save();

    (new StartWork)->handle(
        $this->workOrder,
        $this->techUser,
        $this->trip
    );

    Event::assertDispatched(TripWorking::class);

    $this->workOrder->refresh();
    $this->trip->refresh();
    $tripEnrouteLog->refresh();

    expect($this->workOrder->state)->toBeInstanceOf(WorkInProgress::class);
    expect($this->trip->state)->toBeInstanceOf(Working::class);

    expect($this->trip->last_modified_user)->toBe($this->techUser->user_id);
    expect($this->trip->last_modified_at)->not->toBeNull();

    expect($this->trip->work_started_at)->not->toBeNull();
    expect($this->trip->work_timer_resumed_at)->toBeNull();
    expect($tripEnrouteLog->action_ended_at)->not->toBeNull();
    expect($tripEnrouteLog->action_end_by_user_id)->toBe($this->techUser->user_id);

    $this->trip->load([
        'latestTripLogs:work_order_service_call_id,action,created_at',
    ]);

    expect($this->trip->latestTripLogs->first()->action)->toBe(ServiceCallActionTypes::WORKING());
});

test('handle method work successfully for a timer stopped trip for vendor', function () {

    $vendor = Vendor::factory()->create([
        'is_active' => Boolean::YES(),
    ]);

    $vendorAppointment = VendorAppointment::create([
        'organization_id' => $this->organization->organization_id,
        'vendor_instructions' => 'test',
        'vendor_id' => $vendor->vendor_id,
    ]);

    VendorAllocation::create([
        'organization_id' => $this->workOrder->organization_id,
        'work_order_id' => $this->workOrder->work_order_id,
        'vendor_id' => $vendor->vendor_id,
        'vendor_appointment_id' => $vendorAppointment->vendor_appointment_id,
    ]);

    $this->trip->technician_appointment_id = null;
    $this->trip->vendor_appointment_id = $vendorAppointment->vendor_appointment_id;
    $this->trip->state = new Paused($this->trip);
    $this->trip->save();

    $tripEnrouteLog = WorkOrderServiceCallLog::create([
        'work_order_service_call_id' => $this->trip->work_order_service_call_id,
        'organization_id' => $this->workOrder->organization_id,
        'work_order_id' => $this->workOrder->work_order_id,
        'action' => ServiceCallActionTypes::EN_ROUTE(),
        'action_start_by_user_id' => $this->techUser->user_id,
        'action_started_at' => CarbonImmutable::now(),
    ]);

    $tripEnrouteLog->created_at = CarbonImmutable::now()->subSeconds(20);
    $tripEnrouteLog->save();

    (new StartWork)->handle(
        $this->workOrder,
        $this->techUser,
        $this->trip
    );

    $this->workOrder->refresh();
    $this->trip->refresh();
    $tripEnrouteLog->refresh();

    expect($this->workOrder->state)->toBeInstanceOf(WorkInProgress::class);
    expect($this->trip->state)->toBeInstanceOf(Working::class);

    expect($this->trip->last_modified_user)->toBe($this->techUser->user_id);
    expect($this->trip->last_modified_at)->not->toBeNull();

    expect($this->trip->work_timer_resumed_at)->not->toBeNull();
    expect($this->trip->work_started_at)->toBeNull();
    expect($tripEnrouteLog->action_ended_at)->not->toBeNull();
    expect($tripEnrouteLog->action_end_by_user_id)->toBe($this->techUser->user_id);

    $this->trip->load([
        'latestTripLogs:work_order_service_call_id,action,created_at',
    ]);

    expect($this->trip->latestTripLogs->first()->action)->toBe(ServiceCallActionTypes::WORKING());
});

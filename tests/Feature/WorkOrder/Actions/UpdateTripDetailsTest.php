<?php

use App\Actions\WorkOrders\UpdateTripDetails;
use App\Enums\Boolean;
use App\Enums\CostTypes;
use App\Enums\Feature as FeatureEnum;
use App\Enums\MaterialQuantityType;
use App\Enums\MediaType;
use App\Enums\UserTypes;
use App\Http\Requests\WorkOrder\UpdateTripDetailsRequest;
use App\Models\Country;
use App\Models\Feature;
use App\Models\Issue;
use App\Models\IssueStatus;
use App\Models\Material;
use App\Models\Media;
use App\Models\Organization;
use App\Models\ProblemDiagnosis;
use App\Models\Property;
use App\Models\Resident;
use App\Models\ServiceRequest;
use App\Models\State;
use App\Models\Technician;
use App\Models\TechnicianAppointment;
use App\Models\Timezone;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\Models\WorkOrderIssueStatus;
use App\Models\WorkOrderMedia;
use App\Models\WorkOrderServiceCall;
use App\States\Issue\Done;
use App\States\ServiceCalls\Ended;
use App\States\ServiceCalls\MissingInfo;
use App\States\ServiceCalls\Unresolved;
use App\States\WorkOrderIssue\QualityCheck as WorkOrderIssueQualityCheck;
use App\States\WorkOrders\QualityCheck;
use App\States\WorkOrders\Scheduled as WorkOrdersScheduled;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;
use Lorisleiva\Actions\ActionRequest;

beforeEach(function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $this->organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $this->organization->makeCurrent();

    $this->property = Property::factory()
        ->for($this->organization)
        ->create();

    // create a dummy service request
    $this->serviceRequest = ServiceRequest::factory()
        ->for($this->organization)
        ->for($this->property)
        ->for($timezone)
        ->for(
            Resident::factory()
                ->for($this->property)
                ->for($this->organization)
        )
        ->create();

    if (! IssueStatus::where('slug', Done::$name)->count()) {
        IssueStatus::factory()->create([
            'label' => Done::$name,
            'slug' => Done::$name,
        ]);
    }

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueQualityCheck::$name)->count()) {
        WorkOrderIssueStatus::factory()->create([
            'label' => WorkOrderIssueQualityCheck::$name,
            'slug' => WorkOrderIssueQualityCheck::$name,
        ]);
    }

    $this->problemDiagnosis = ProblemDiagnosis::factory()->create();

    $this->issue = Issue::factory()
        ->for($this->organization)
        ->for($this->problemDiagnosis)
        ->for($this->serviceRequest)
        ->create([
            'state' => Done::class,
        ]);

    $this->workOrder = WorkOrder::factory()
        ->for($this->organization)
        ->for($this->serviceRequest)
        ->for($this->property)
        ->create([
            'state' => QualityCheck::class,
        ]);

    $this->workOrderIssue = WorkOrderIssue::factory()->create([
        'work_order_id' => $this->workOrder->work_order_id,
        'issue_id' => $this->issue->issue_id,
        'organization_id' => $this->organization->organization_id,
        'state' => WorkOrderIssueQualityCheck::class,
    ]);

    $this->techUser = User::factory()
        ->for($timezone)
        ->for($this->organization)
        ->create([
            'user_type' => UserTypes::TECHNICIAN(),
        ]);

    $this->technician = Technician::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'user_id' => $this->techUser->user_id,
    ]);

    $this->appointment = TechnicianAppointment::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'technician_id' => $this->technician->technician_id,
        'work_order_id' => $this->workOrder->work_order_id,
    ]);

    $this->trip = WorkOrderServiceCall::create([
        'organization_id' => $this->organization->organization_id,
        'work_order_id' => $this->workOrder->work_order_id,
        'technician_appointment_id' => $this->appointment->technician_appointment_id,
        'scheduled_start_time' => $this->appointment->scheduled_start_time,
        'scheduled_end_time' => $this->appointment->scheduled_end_time,
        'duration_minutes' => 30,
        'state' => Ended::class,
        'is_active' => 1,
    ]);

    $this->user = User::factory()
        ->for($timezone)
        ->for($this->organization)
        ->create();
    $media = UploadedFile::fake()->image('your-image-one.jpg', 100, 100)->size(100);
    $this->media = Media::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'original_thumbnail_file_name' => $media->getClientOriginalName(),
        'mime_type' => $media->getClientMimeType(),
        'extension' => $media->getClientOriginalExtension(),
        'thumbnail_extension' => $media->getClientOriginalExtension(),
    ]);

    $this->workOrderMedia = WorkOrderMedia::create([
        'media_id' => $this->media->media_id,
        'organization_id' => $this->organization->organization_id,
        'work_order_id' => $this->workOrder->work_order_id,
        'quote_task_id' => null,
        'user_id' => $this->user?->user_id,
        'media_type' => MediaType::BEFORE_MEDIA(),
        'has_thumbnail' => Boolean::YES(),
    ]);

    $this->mediaIds = [$this->media->media_uuid];

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $this->header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];
});

test('Handles UserNotFoundException correctly in asController method.', function () {
    $request = Mockery::mock(UpdateTripDetailsRequest::class);
    $request->shouldReceive('user')->andReturn(null);

    $updateTripDetails = new UpdateTripDetails;

    /** @var Request $request */
    $response = $updateTripDetails->asController($request, $this->workOrder, $this->workOrderIssue, $this->trip);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_UNPROCESSABLE_ENTITY);
});

test('Handles the work order transaction exception correctly in asController method', function () {
    $request = Mockery::mock(UpdateTripDetailsRequest::class);
    $request->shouldReceive('user')->andReturn($this->user);

    $updateTripDetails = new UpdateTripDetails;

    /** @var Request $request */
    $response = $updateTripDetails->asController($request, $this->workOrder, $this->workOrderIssue, $this->trip);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_INTERNAL_SERVER_ERROR);
});

test('Cannot update trip details if work order is not in quality_check status', function () {
    $request = Mockery::mock(UpdateTripDetailsRequest::class);
    $request->shouldReceive('user')->andReturn($this->user);

    $updateTripDetails = new UpdateTripDetails;

    $this->workOrder->state = new WorkOrdersScheduled($this->workOrder);
    $this->workOrder->save();
    /** @var Request $request */
    $response = $updateTripDetails->asController($request, $this->workOrder, $this->workOrderIssue, $this->trip);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_UNPROCESSABLE_ENTITY);
});

test('authorize method returns true when user has permission to update trip details', function () {
    // create the feature
    $feature = Feature::where('name', FeatureEnum::WORK_ORDER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => FeatureEnum::WORK_ORDER_MANAGEMENT(),
        ])->create();
    }

    $this->organization->features()->save($feature);
    $user = $this->createUserWithPermission($feature, $this->organization, 'WorkOrderManagement.WorkOrder.updateTrip');

    $request = Mockery::mock(ActionRequest::class);
    $request->shouldReceive('user')->andReturn($user);
    $request->shouldReceive('route')->with('workOrder')->andReturn($this->workOrder);
    $updateTripDetails = new UpdateTripDetails;

    /** @var ActionRequest $request */
    $result = $updateTripDetails->authorize($request);

    expect($result)->toBeTrue();
});

test('authorize method returns false when user is not authenticated.', function () {
    $request = Mockery::mock(ActionRequest::class);
    $request->shouldReceive('user')->andReturn(null);

    $updateTripDetails = new UpdateTripDetails;

    /** @var ActionRequest $request */
    $result = $updateTripDetails->authorize($request);

    expect($result)->toBeFalse();
});

test('authorize method returns false when user does not have permission to en route the work order.', function () {
    // create the feature
    $feature = Feature::where('name', FeatureEnum::WORK_ORDER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => FeatureEnum::WORK_ORDER_MANAGEMENT(),
        ])->create();
    }

    $this->organization->features()->save($feature);
    $user = $this->createUserWithPermission($feature, $this->organization, 'WorkOrderManagement.WorkOrder.deleteTrip');

    $request = Mockery::mock(ActionRequest::class);
    $request->shouldReceive('user')->andReturn($user);
    $request->shouldReceive('route')->with('workOrder')->andReturn($this->workOrder);
    $updateTripDetails = new UpdateTripDetails;

    /** @var ActionRequest $request */
    $result = $updateTripDetails->authorize($request);

    expect($result)->toBeFalse();
});

test('Handle updates service_notes on activeTrip', function () {
    $updateTripDetails = new UpdateTripDetails;

    $payload = [
        'service_notes' => 'Test service notes',
        'media_ids' => $this->mediaIds,
    ];

    $updateTripDetails->updateNotes($this->trip, $payload);

    expect($this->trip->service_notes)->toBe('Test service notes');
});

test('Handle links media with work order issue and trip when media_id is present', function () {

    $updateTripDetails = new UpdateTripDetails;

    $tripId = $this->trip->work_order_service_call_id;

    $updateTripDetails->handleIssueMedia($this->workOrderIssue, $this->trip->work_order_service_call_id, $this->mediaIds);

    // Assert
    $this->workOrderMedia->refresh();
    expect($this->workOrderMedia->work_order_issue_id)->toBe($this->workOrderIssue->work_order_issue_id);
    expect($this->workOrderMedia->work_order_service_call_id)->toBe($tripId);

});

test('Handle sets additional_notes and note_to_provider on activeTrip', function () {
    $updateTripDetails = new UpdateTripDetails;

    $payload = [
        'media_ids' => $this->mediaIds,
        'additional_notes' => 'Some additional notes',
        'note_to_provider' => 'Note for provider',
        'service_notes' => 'Test service notes',
    ];

    $updateTripDetails->updateNotes($this->trip, $payload);

    expect($this->trip->additional_notes)->toBe('Some additional notes');
    expect($this->trip->note_to_provider)->toBe('Note for provider');
});

test('Handle sets state on activeTrip', function () {
    $updateTripDetails = new UpdateTripDetails;

    $payload = [
        'media_ids' => $this->mediaIds,
        'quality_check_status' => 'missing_info',
        'service_notes' => 'Test service notes',
    ];

    $updateTripDetails->updateTripState($this->trip, $this->user, $payload);

    expect($this->trip->state)->toBeInstanceOf(MissingInfo::class);

    $this->trip->state = Ended::class;
    $this->trip->save();

    $payload['quality_check_status'] = 'unresolved';

    $updateTripDetails->updateTripState($this->trip, $this->user, $payload);
    expect($this->trip->state)->toBeInstanceOf(Unresolved::class);

});

test('handleIssueMedia does nothing if mediaIds array is empty', function () {
    $updateTripDetails = new UpdateTripDetails;

    $mediaIds = [];
    $tripId = $this->trip->work_order_service_call_id;

    $updateTripDetails->handleIssueMedia($this->workOrderIssue, $tripId, $mediaIds);

    expect(true)->toBeTrue();
});

test('asController returns success response when trip details are updated successfully', function () {

    $updateTripDetails = new UpdateTripDetails;

    // Mock request with user and valid payload
    $request = Mockery::mock(UpdateTripDetailsRequest::class);
    $request->shouldReceive('user')->andReturn($this->user);
    $request->shouldReceive('all')->andReturn([
        'media_ids' => $this->mediaIds,
        'service_notes' => 'Trip completed successfully.',
        'quality_check_status' => 'passed',
        'materials' => [
            [
                'label' => 'Test Material',
                'quantity' => 5,
                'quantity_type' => MaterialQuantityType::UNITS(),
                'cost_type' => CostTypes::PER_UNIT(),
                'unit_price_in_cents' => 200,
                'cost_in_cents' => 0,
            ],
        ],

    ]);

    // Act
    $response = $updateTripDetails->asController($request, $this->workOrder, $this->workOrderIssue, $this->trip);

    // Assert
    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_OK);
    expect($response->getData(true)['message'])
        ->toBe(__('Successfully updated trip details.'));
});

test('updateOrCreateMaterial creates a new material and attaches it to the work order issue if material_id is not provided', function () {
    $updateTripDetails = new UpdateTripDetails;

    $materialData = [
        'label' => 'Test Material',
        'quantity' => 5,
        'quantity_type' => MaterialQuantityType::UNITS(),
        'cost_type' => CostTypes::PER_UNIT(),
        'unit_price_in_cents' => 200,
        'cost_in_cents' => 0,
    ];
    $payload = [
        'media_ids' => $this->mediaIds,
        'quality_check_status' => 'missing_info',
        'service_notes' => 'Test service notes',
        'materials' => [$materialData],
    ];
    $updateTripDetails->handle($this->trip, $this->workOrderIssue, $this->user, $payload);
    $updateTripDetails->updateOrCreateMaterial($this->workOrderIssue, $payload);

    $material = Material::where('label', 'Test Material')->first();

    expect($material)->not->toBeNull();
    expect($material->quantity)->toBe(5);
    expect($material->quantity_type)->toBe(MaterialQuantityType::UNITS());
    expect($material->cost_type)->toBe(CostTypes::PER_UNIT());
    expect($material->unit_price_in_cents)->toBe(200);
    expect($material->total_cost_in_cents)->toBe(1000);
    // Check that the material is attached to the work order issue
    $attached = $this->workOrderIssue->materials()->where('materials.material_id', $material->material_id)->exists();
    expect($attached)->toBeTrue();
});

test('updateOrCreateMaterial updates an existing material if material_id is provided', function () {
    $existingMaterial = Material::factory()->create([
        'label' => 'Old Material',
        'quantity' => 2,
        'quantity_type' => MaterialQuantityType::UNITS(),
        'cost_type' => CostTypes::PER_UNIT(),
        'unit_price_in_cents' => 100,
        'cost_in_cents' => 0,
        'total_cost_in_cents' => 200,
    ]);

    $updateTripDetails = new UpdateTripDetails;

    $materialData = [
        'material_id' => $existingMaterial->material_uuid,
        'label' => 'Updated Material',
        'quantity' => 3,
        'quantity_type' => MaterialQuantityType::UNITS(),
        'cost_type' => CostTypes::PER_UNIT(),
        'unit_price_in_cents' => 150,
        'cost_in_cents' => 0,
    ];

    $payload = [
        'materials' => [$materialData],
    ];
    $updateTripDetails->updateOrCreateMaterial($this->workOrderIssue, $payload);

    $existingMaterial->refresh();
    expect($existingMaterial->label)->toBe('Updated Material');
    expect($existingMaterial->quantity)->toBe(3);
    expect($existingMaterial->quantity_type)->toBe(MaterialQuantityType::UNITS());
    expect($existingMaterial->unit_price_in_cents)->toBe(150);
    expect($existingMaterial->total_cost_in_cents)->toBe(450);

    // Should not attach again if already attached
    $attachedCount = $this->workOrderIssue->materials()->where('materials.material_id', $existingMaterial->material_id)->count();
    expect($attachedCount)->toBeGreaterThanOrEqual(0);
});

test('updateTripState throws exception if transition is not allowed', function () {
    $updateTripDetails = new UpdateTripDetails;

    // Mock the state object to disallow transition
    $mockState = Mockery::mock($this->trip->state)->makePartial();
    $mockState->shouldReceive('canTransitionTo')->andReturn(false);
    $this->trip->state = $mockState;

    $payload = [
        'quality_check_status' => 'passed',
        'media_ids' => $this->mediaIds,
    ];

    expect(fn () => $updateTripDetails->updateTripState($this->trip, $this->user, $payload))
        ->toThrow(\App\Exceptions\WorkOrderException::class);
});

test('updateTripState transitions to correct state when allowed', function () {
    $updateTripDetails = new UpdateTripDetails;

    // Mock the state object to allow transition and expect transitionTo to be called
    $mockState = Mockery::mock($this->trip->state)->makePartial();
    $mockState->shouldReceive('canTransitionTo')->andReturn(true);
    $mockState->shouldReceive('transitionTo')->once();
    $this->trip->state = $mockState;

    $payload = [
        'quality_check_status' => 'passed',
        'media_ids' => $this->mediaIds,
    ];

    $updateTripDetails->updateTripState($this->trip, $this->user, $payload);

    // No exception means success, and transitionTo was called
    expect(true)->toBeTrue();
});

<?php

use App\Enums\Feature as FeatureConst;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Property;
use App\Models\Role;
use App\Models\ServiceRequest;
use App\Models\State;
use App\Models\Timezone;
use App\Models\User;
use App\Models\WorkOrder;
use Illuminate\Http\Response;
use Illuminate\Support\Str;

beforeEach(function () {
    // Setup organization has work order feature for the test.
    $this->organizationFeature = FeatureConst::WORK_ORDER_MANAGEMENT->value;
    $this->organization = Organization::join('organization_feature', 'organization_feature.organization_id', 'organizations.organization_id')
        ->join('features', function ($joinQuery) {
            $joinQuery->on('organization_feature.feature_id', 'features.feature_id')
                ->where('features.name', $this->organizationFeature);
        })
        ->select('organizations.organization_id', 'organizations.user_pool_id')
        ->first();
    $this->organization->makeCurrent();
    // Set a random user from organization for authentication.
    $this->user = User::where('users.organization_id', $this->organization->organization_id)
        ->join('user_role', 'user_role.user_id', 'users.user_id')
        ->join('roles', function ($joinQuery) {
            $joinQuery->on('roles.role_id', 'user_role.role_id')
                ->whereIn('roles.name', ['Admin', 'Owner']);
        })
        ->where('users.user_type', 'account_user')
        ->first();

    //Create a token
    $this->jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $this->header = [
        'Authorization' => "Bearer {$this->jtb->jwt}",
    ];

    $this->workOrder = WorkOrder::where('organization_id', $this->organization->organization_id)
        ->select('work_order_uuid', 'work_order_id', 'property_id')
        ->inRandomOrder()
        ->first();

    $this->timezone = Timezone::factory()->create();

    $this->property = Property::factory()
        ->for($this->organization)
        ->create();

    // create a dummy service request
    $this->serviceRequest = ServiceRequest::factory()
        ->for($this->organization)
        ->for($this->property)
        ->for($this->timezone)
        ->create();

    $this->workOrder->service_request_id = $this->serviceRequest->service_request_id;
    $this->workOrder->save();

    $this->propertyUuid = Property::where('property_id', $this->serviceRequest->property_id)->first()->property_uuid;
    $this->postPayLoad = [
        'property_id' => $this->propertyUuid,
        'street_address' => Str::random(30),
        'city' => Str::random(30),
        'state_id' => State::first()->state_uuid,
        'postal_zip_code' => rand(10000, 99999),
        'unit_number' => 'AFF156',
    ];
});
// Case: 1
test('User with an valid token can update work order property address and return proper response', function () {

    $result = $this->putJson(route('work_orders.property-address.update', ['workOrder' => $this->workOrder->work_order_uuid]), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonStructure([
            'property_id',
            'state' => [
                'value',
                'label',

            ],
            'address' => [
                'street_address',
                'apt_suite_unit',
                'city',
                'state_code',
                'zip_code',
            ],
        ]);
});
// Case: 2
test('User with an invalid token cannot access work order property address update API', function () {
    $this->header = [
        'Authorization' => 'Bearer invalid-token',
    ];

    $result = $this->putJson(route('work_orders.property-address.update', ['workOrder' => $this->workOrder->work_order_uuid]), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 3
test('User without a token cannot access work order property address update API', function () {
    $this->header = [
        'Authorization' => '',
    ];

    $result = $this->putJson(route('work_orders.property-address.update', ['workOrder' => $this->workOrder->work_order_uuid]), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 4
test('Unauthorized user cannot access work order update API', function () {

    //Check if the user has work order update permission
    $userPermissions = $this->user->permissions()->toArray();
    if (in_array('WorkOrderManagement.WorkOrder.update', $userPermissions)) {
        //Create a test role for work order manage without work order update permission
        $workOrderManager = Role::updateOrCreate(['name' => 'Work Order Manager', 'organization_id' => $this->organization->organization_id]);
        $workOrderManagePermissions = [
            'WorkOrderManagement.WorkOrder.list',
        ];
        $workOrderManager->syncPermissions($workOrderManagePermissions);
        $this->user->syncRoles($workOrderManager);
    }

    $result = $this->putJson(route('work_orders.property-address.update', ['workOrder' => $this->workOrder->work_order_uuid]), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
    //Clear cache after test execute
    $this->user->clearPermissionCache();
});

// Case: 5
test('Unauthorized organization cannot access work order property address update API', function () {
    //Check if the organization has work order feature
    $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();
    if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {
        $features = [
            FeatureConst::ROLE_MANAGEMENT,
            FeatureConst::TECHNICIAN_MANAGEMENT,
            FeatureConst::USER_MANAGEMENT,
            FeatureConst::ORGANIZATION_SETTINGS,
        ];
        $this->organization->features()
            ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
    }

    $result = $this->putJson(route('work_orders.property-address.update', ['workOrder' => $this->workOrder->work_order_uuid]), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

// Case: 6
test('The request with an empty payload shows the validation error.', function () {
    $postPayLoad = [];

    $result = $this->putJson(route('work_orders.property-address.update', ['workOrder' => $this->workOrder->work_order_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
        ->assertJsonStructure([
            'message',
            'errors',
        ]);
});

// Case: 7
test('The request with an invalid payload shows the validation error.', function () {

    $postPayLoad = [
        'property_id' => null,
        'street_address' => null,
        'city' => null,
        'state_id' => null,
        'postal_zip_code' => null,
        'unit_number' => null,
    ];

    $result = $this->putJson(route('work_orders.property-address.update', ['workOrder' => $this->workOrder->work_order_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertInvalid(['property_id', 'street_address', 'city', 'state_id', 'postal_zip_code']);

    //invalid zip code
    $postPayLoad['postal_zip_code'] = rand(5, 0);

    $result = $this->putJson(route('work_orders.property-address.update', ['workOrder' => $this->workOrder->work_order_uuid]), $postPayLoad, $this->header);
    expect($result)
        ->assertInvalid(['postal_zip_code']);

    //invalid street_address, unit_number and city
    $postPayLoad['street_address'] = Str::random(300);
    $postPayLoad['unit_number'] = Str::random(40);
    $postPayLoad['city'] = Str::random(300);

    $result = $this->putJson(route(
        'work_orders.property-address.update',
        [
            'workOrder' => $this->workOrder->work_order_uuid,
        ]
    ), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
        ->assertInvalid([
            'street_address',
            'city',
            'unit_number',
        ]);
});

// Case: 9
test('The work orders associated with one organization cannot be updated by another organization', function () {
    $workOrderFromAnotherOrganization = WorkOrder::select('work_order_uuid')
        ->where('organization_id', '<>', $this->organization->organization_id)
        ->first();

    $postPayLoad = [
        'property_id' => $this->propertyUuid,
        'street_address' => Str::random(30),
        'city' => Str::random(30),
        'state_id' => State::first()->state_uuid,
        'postal_zip_code' => rand(10000, 99999),
        'unit_number' => 'SDD56',
    ];
    $result = $this->putJson(route('work_orders.property-address.update', ['workOrder' => $workOrderFromAnotherOrganization->work_order_uuid]), $postPayLoad, $this->header);
    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

<?php

use App\Enums\Feature as FeatureEnum;
use App\Enums\UserTypes;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Role;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Http\Response;

beforeEach(function () {
    // Setup organization has tag management feature for the test.
    $this->organizationFeature = FeatureEnum::TAG_MANAGEMENT();

    $this->organization = Organization::join('organization_feature', 'organization_feature.organization_id', 'organizations.organization_id')
        ->join('features', function ($joinQuery) {
            $joinQuery->on('organization_feature.feature_id', 'features.feature_id')
                ->where('features.name', $this->organizationFeature);
        })
        ->select('organizations.organization_id', 'organizations.user_pool_id')
        ->first();

    $this->organization->makeCurrent();

    // Set a random user from organization for authentication.
    $this->user = User::where('users.organization_id', $this->organization->organization_id)
        ->join('user_role', 'user_role.user_id', 'users.user_id')
        ->join('roles', function ($joinQuery) {
            $joinQuery->on('roles.role_id', 'user_role.role_id')
                ->whereIn('roles.name', ['Admin', 'Owner']);
        })
        ->where('users.user_type', UserTypes::ACCOUNT_USER->value)
        ->first();

    //Create a token
    $this->jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $this->header = [
        'Authorization' => "Bearer {$this->jtb->jwt}",
    ];

    $this->tag = Tag::factory()
        ->create([
            'organization_id' => $this->organization->organization_id,
        ]);

    $this->responseDataStructure = [
        'tag_id',
        'name',
        'color',
        'text_color',
        'slug',
    ];
});

// Case: 1
test('User with a valid token can access tag details API and the return response contain correct keys.', function () {

    $result = $this->getJson(route('tags.show', ['tag' => $this->tag->tag_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonStructure($this->responseDataStructure);
});

// Case: 2
test('User with an invalid token cannot access tag details API.', function () {

    $this->header = [
        'Authorization' => 'Bearer invalid.token',
    ];

    $result = $this->getJson(route('tags.show', ['tag' => $this->tag->tag_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 3
test('User without a token cannot access tag details API.', function () {

    $this->header = [
        'Authorization' => 'Bearer ',
    ];

    $result = $this->getJson(route('tags.show', ['tag' => $this->tag->tag_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 4
test('The request with an invalid tag uuid parameter shows a Resource not found.', function () {

    $invalidTagUuid = fake()->uuid;

    $result = $this->getJson(route('tags.show', ['tag' => $invalidTagUuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_NOT_FOUND)
        ->assertJsonStructure([
            'message',
        ]);
});

// Case: 5
test('Unauthorized organization cannot access tag details API.', function () {

    //Check if the organization has tag management feature
    $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();

    if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {

        $features = [
            FeatureEnum::USER_MANAGEMENT,
            FeatureEnum::ROLE_MANAGEMENT,
            FeatureEnum::ORGANIZATION_SETTINGS,
        ];

        $this->organization->features()
            ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
    }

    $result = $this->getJson(route('tags.show', ['tag' => $this->tag->tag_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

});

// Case: 6
test('Unauthorized user cannot access tag details API.', function () {

    //Check if the user has tag view permission
    $userPermissions = $this->user->permissions()->toArray();

    if (in_array('TagManagement.Tag.view', $userPermissions)) {

        //Create a test role for tag manage without tag view permission
        $tagManager = Role::updateOrCreate(['name' => 'Tag Manager', 'organization_id' => $this->organization->organization_id]);

        $tagManagePermissions = [
            'TagManagement.Tag.list',
            'TagManagement.Tag.create',
        ];

        $tagManager->syncPermissions($tagManagePermissions);
        $this->user->syncRoles($tagManager);
    }

    $result = $this->getJson(route('tags.show', ['tag' => $this->tag->tag_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

    //Clear cache after test execute
    $this->user->clearPermissionCache();
});

// Case: 7
test('The tag associated with one organization are not visible to another organization.', function () {

    $anotherOrganization = Organization::select('organization_id')
        ->where('organization_id', '<>', $this->organization->organization_id)
        ->first();

    $tagFromAnotherOrganization = Tag::factory()
        ->create([
            'organization_id' => $anotherOrganization->organization_id,
        ]);

    $result = $this->getJson(route('tags.show', ['tag' => $tagFromAnotherOrganization->tag_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

<?php

namespace Tests\Feature\Services\Scheduling\Domain\Entities;

use App\Models\Organization;
use App\Models\Property;
use App\Models\ResidentAvailability;
use App\Models\ServiceRequest;
use App\Services\Scheduling\Domain\Collections\Appointments;
use App\Services\Scheduling\Domain\Collections\WorkingHours;
use App\Services\Scheduling\Domain\Entities\Appointment;
use App\Services\Scheduling\Domain\Entities\Location;
use App\Services\Scheduling\Domain\Entities\Technician;
use App\Services\Scheduling\Domain\Entities\TechnicianCalendar;
use App\Services\Scheduling\Domain\Entities\WorkingDay;
use App\Services\Scheduling\Domain\Enums\AppointmentType;
use App\Services\Scheduling\Domain\Enums\CarbonDayOfWeek;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Illuminate\Support\Str;

beforeEach(function () {
    // Create organization
    $this->organization = Organization::factory()
        ->create();

    $this->organization->makeCurrent();

    $property = Property::factory()
        ->for($this->organization)
        ->create();

    $this->serviceRequest = ServiceRequest::factory()
        ->for($this->organization)
        ->for($property)
        ->create();
});

test('Calendar returns a valid set of open service windows when no appointments', function () {
    $workingHours = new WorkingHours(
        new Location(39.10046200, -94.57824700),
        collect(CarbonDayOfWeek::cases())->map(function (CarbonDayOfWeek $day) {
            return new WorkingDay(
                workDay: $day,
                workStartsAt: CarbonImmutable::now()->setTime(13, 0),
                workEndsAt: CarbonImmutable::now()->setTime(15, 0)
            );
        })->toArray()
    );

    $technician = new Technician(
        random_int(100, 150),
        Str::uuid(),
        'John Smith',
        $workingHours,
        new Appointments,
        new Location(39.10046200, -94.57824700),
        50,
        config('settings.default_timezone')
    );

    ResidentAvailability::factory()
        ->for($this->organization)
        ->for($this->serviceRequest)
        ->create([
            'availability_date' => Carbon::today()->startOfDay(),
        ]);
    ResidentAvailability::factory()
        ->for($this->organization)
        ->for($this->serviceRequest)
        ->create([
            'availability_date' => Carbon::tomorrow()->startOfDay(),
        ]);

    $residentAvailabilities = ResidentAvailability::select('timing', 'availability_date')
        ->where('service_request_id', $this->serviceRequest->service_request_id)
        ->where('day_passed', 'no')
        ->get();

    $calendar = new TechnicianCalendar($technician, $residentAvailabilities, 30, false, config('settings.default_timezone'));

    $windows = $calendar->getOpenServiceWindows(30, CarbonImmutable::today()->startOfDay()->addDays(14));

    expect($windows->filter(function ($window) {
        return $window->onHours;
    }))->toHaveCount(8);
});

test('Calendar returns no windows when no working hours defined', function () {
    $workingHours = new WorkingHours(
        new Location(39.10046200, -94.57824700),
        []
    );

    $technician = new Technician(
        random_int(100, 150),
        Str::uuid(),
        'John Smith',
        $workingHours,
        new Appointments,
        new Location(39.10046200, -94.57824700),
        50,
        config('settings.default_timezone')
    );

    ResidentAvailability::factory()
        ->for($this->organization)
        ->for($this->serviceRequest)
        ->create([
            'availability_date' => Carbon::tomorrow()->startOfDay(),
        ]);

    $residentAvailabilities = ResidentAvailability::select('timing', 'availability_date')
        ->where('service_request_id', $this->serviceRequest->service_request_id)
        ->where('day_passed', 'no')
        ->get();

    $calendar = new TechnicianCalendar($technician, $residentAvailabilities, 30, false, config('settings.default_timezone'));

    $windows = $calendar->getOpenServiceWindows(30, CarbonImmutable::today()->startOfDay()->addDays(14));
    expect($windows->filter(function ($window) {
        return $window->onHours;
    }))->toHaveCount(0);
});

test('Calendar returns a valid set of open service windows when appointments', function () {
    $workingHours = new WorkingHours(
        new Location(39.10046200, -94.57824700),
        collect(CarbonDayOfWeek::cases())->map(function (CarbonDayOfWeek $day) {
            return new WorkingDay(
                workDay: $day,
                workStartsAt: CarbonImmutable::now()->setTime(13, 0),
                workEndsAt: CarbonImmutable::now()->setTime(15, 0)
            );
        })->toArray()
    );

    $appointments = new Appointments([new Appointment(
        id: random_int(100, 150),
        appointmentType: AppointmentType::WORK_ORDER,
        scheduledStartTime: CarbonImmutable::tomorrow(config('settings.default_timezone'))->setTime(14, 0),
        scheduledEndTime: CarbonImmutable::tomorrow(config('settings.default_timezone'))->setTime(14, 30),
        location: new Location(39.10046200, -94.57824700)
    )]);

    $technician = new Technician(
        random_int(100, 150),
        Str::uuid(),
        'John Smith',
        $workingHours,
        $appointments,
        new Location(39.10046200, -94.57824700),
        50,
        config('settings.default_timezone')
    );

    ResidentAvailability::factory()
        ->for($this->organization)
        ->for($this->serviceRequest)
        ->create([
            'availability_date' => Carbon::today()->startOfDay(),
        ]);
    ResidentAvailability::factory()
        ->for($this->organization)
        ->for($this->serviceRequest)
        ->create([
            'availability_date' => Carbon::tomorrow()->startOfDay(),
        ]);

    $residentAvailabilities = ResidentAvailability::select('timing', 'availability_date')
        ->where('service_request_id', $this->serviceRequest->service_request_id)
        ->where('day_passed', 'no')
        ->get();

    $calendar = new TechnicianCalendar($technician, $residentAvailabilities, 30, false, config('settings.default_timezone'));

    $windows = $calendar->getOpenServiceWindows(30, CarbonImmutable::today()->startOfDay()->addDays(14));
    expect($windows->filter(function ($window) {
        return $window->onHours;
    }))->toHaveCount(7);
});

test('Calendar returns a valid set of open service windows when appointments on multiple days', function () {
    $workingHours = new WorkingHours(
        new Location(39.10046200, -94.57824700),
        collect(CarbonDayOfWeek::cases())->map(function (CarbonDayOfWeek $day) {
            return new WorkingDay(
                workDay: $day,
                workStartsAt: CarbonImmutable::now()->setTime(13, 0),
                workEndsAt: CarbonImmutable::now()->setTime(15, 0)
            );
        })->toArray()
    );

    $appointments = new Appointments([
        new Appointment(
            id: random_int(100, 150),
            appointmentType: AppointmentType::WORK_ORDER,
            scheduledStartTime: CarbonImmutable::tomorrow(config('settings.default_timezone'))->setTime(14, 0),
            scheduledEndTime: CarbonImmutable::tomorrow(config('settings.default_timezone'))->setTime(14, 30),
            location: new Location(39.10046200, -94.57824700)
        ),
        new Appointment(
            id: random_int(100, 150),
            appointmentType: AppointmentType::WORK_ORDER,
            scheduledStartTime: CarbonImmutable::tomorrow(config('settings.default_timezone'))->addDay()->setTime(14, 30),
            scheduledEndTime: CarbonImmutable::tomorrow(config('settings.default_timezone'))->addDay()->setTime(15, 00),
            location: new Location(39.10046200, -94.57824700)
        ),
    ]);

    $technician = new Technician(
        random_int(100, 150),
        Str::uuid(),
        'John Smith',
        $workingHours,
        $appointments,
        new Location(39.10046200, -94.57824700),
        50,
        config('settings.default_timezone')
    );

    foreach (range(0, 2) as $daysToAdd) {
        ResidentAvailability::factory()
            ->for($this->organization)
            ->for($this->serviceRequest)
            ->create([
                'availability_date' => Carbon::today()->addDays($daysToAdd)->startOfDay(),
            ]);
    }

    $residentAvailabilities = ResidentAvailability::select('timing', 'availability_date')
        ->where('service_request_id', $this->serviceRequest->service_request_id)
        ->where('day_passed', 'no')
        ->get();

    $calendar = new TechnicianCalendar($technician, $residentAvailabilities, 30, false, config('settings.default_timezone'));

    $windows = $calendar->getOpenServiceWindows(30, CarbonImmutable::today()->startOfDay()->addDays(14));
    expect($windows->filter(function ($window) {
        return $window->onHours;
    }))->toHaveCount(10);
});

test('Calendar ignores appointments outside resident avilable time period', function () {
    $workingHours = new WorkingHours(
        new Location(39.10046200, -94.57824700),
        collect(CarbonDayOfWeek::cases())->map(function (CarbonDayOfWeek $day) {
            return new WorkingDay(
                workDay: $day,
                workStartsAt: CarbonImmutable::now()->setTime(13, 0),
                workEndsAt: CarbonImmutable::now()->setTime(15, 0)
            );
        })->toArray()
    );

    $appointments = new Appointments([
        new Appointment(
            id: random_int(100, 150),
            appointmentType: AppointmentType::WORK_ORDER,
            location: new Location(39.10046200, -94.57824700),
            scheduledStartTime: CarbonImmutable::tomorrow()->addDays(10)->setTime(14, 0),
            scheduledEndTime: CarbonImmutable::tomorrow()->addDays(10)->setTime(14, 30)
        ),
    ]);

    $technician = new Technician(
        random_int(100, 150),
        Str::uuid(),
        'John Smith',
        $workingHours,
        $appointments,
        new Location(39.10046200, -94.57824700),
        50,
        config('settings.default_timezone')
    );

    ResidentAvailability::factory()
        ->for($this->organization)
        ->for($this->serviceRequest)
        ->create([
            'availability_date' => Carbon::today()->startOfDay(),
        ]);
    ResidentAvailability::factory()
        ->for($this->organization)
        ->for($this->serviceRequest)
        ->create([
            'availability_date' => Carbon::tomorrow()->startOfDay(),
        ]);

    $residentAvailabilities = ResidentAvailability::select('timing', 'availability_date')
        ->where('service_request_id', $this->serviceRequest->service_request_id)
        ->where('day_passed', 'no')
        ->get();

    $calendar = new TechnicianCalendar($technician, $residentAvailabilities, 30, false, config('settings.default_timezone'));

    $windows = $calendar->getOpenServiceWindows(30, CarbonImmutable::today()->startOfDay()->addDays(14));
    expect($windows->filter(function ($window) {
        return $window->onHours;
    }))->toHaveCount(8);
});

<?php

use App\Enums\Feature as FeatureEnum;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Role;
use App\Models\User;
use App\Models\Vendor;
use Illuminate\Http\Response;

beforeEach(function () {
    // Setup organization has vendor management feature for the test.
    $this->organizationFeature = FeatureEnum::VENDOR_MANAGEMENT();
    $this->organization = Organization::join('organization_feature', 'organization_feature.organization_id', 'organizations.organization_id')
        ->join('features', function ($joinQuery) {
            $joinQuery->on('organization_feature.feature_id', 'features.feature_id')
                ->where('features.name', $this->organizationFeature);
        })
        ->select('organizations.organization_id', 'organizations.user_pool_id')
        ->first();
    $this->organization->makeCurrent();

    // Set a random user from organization for authentication.
    $this->user = User::where('users.organization_id', $this->organization->organization_id)
        ->join('user_role', 'user_role.user_id', 'users.user_id')
        ->join('roles', function ($joinQuery) {
            $joinQuery->on('roles.role_id', 'user_role.role_id')
                ->whereIn('roles.name', ['Admin', 'Owner']);
        })
        ->where('users.user_type', 'account_user')
        ->first();

    $this->jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    //Create Vendor
    $this->vendor = Vendor::factory()->create();

    $this->header = [
        'Authorization' => "Bearer {$this->jtb->jwt}",
    ];

    $this->postPayLoad = [
        'is_active' => collect(['true', 'false'])->random(),
    ];
});

test('User with a valid token can access vendor update status API and the return response contain correct keys', function () {

    $result = $this->patchJson(route('vendors.action.updateStatus', [
        'vendor' => $this->vendor->vendor_uuid,
    ]), $this->postPayLoad, $this->header);

    $responseData = json_decode($result->getContent(), true);

    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->expect($this->postPayLoad['is_active'])->toEqual($responseData['is_active']);
});

test('User with an invalid token cannot access vendor update status API', function () {

    $result = $this->patchJson(route('vendors.action.updateStatus', [
        'vendor' => $this->vendor->vendor_uuid,
    ]), $this->postPayLoad, [
        'Authorization' => 'Bearer invalid-token',
    ]);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

test('User with a valid token with invalid vendor id cannot access vendor update status API', function () {

    $result = $this->patchJson(route('vendors.action.updateStatus', [
        'vendor' => fake()->uuid(),
    ]), $this->postPayLoad, $this->header);
    expect($result)
        ->assertStatus(Response::HTTP_NOT_FOUND);
});

test('User without a token cannot access vendor update status API', function () {

    $result = $this->patchJson(route('vendors.action.updateStatus', [
        'vendor' => $this->vendor->vendor_uuid,
    ]), $this->postPayLoad,
        [
            'Authorization' => 'Bearer ',
        ]);
    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

test('Unauthorized user cannot access vendor update status API', function () {

    //Check if the user has vendor update permission
    $userPermissions = $this->user->permissions()->toArray();

    if (in_array('VendorManagement.Vendor.update', $userPermissions)) {

        //Create a test role for vendor manage without update permission
        $vendorManager = Role::updateOrCreate(['name' => 'Vendor Manager', 'organization_id' => $this->organization->organization_id]);
        $vendorManagePermissions = [
            'VendorManagement.Vendor.view',
        ];

        $vendorManager->syncPermissions($vendorManagePermissions);

        $this->user->syncRoles($vendorManager);
    }

    $result = $this->patchJson(route('vendors.action.updateStatus', [
        'vendor' => $this->vendor->vendor_uuid,
    ]), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

    //Clear cache after test execute
    $this->user->clearPermissionCache();
});

test('Unauthorized organization cannot access vendor update status API', function () {

    //Check if the organization has vendor management feature
    $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();

    if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {

        $features = [
            FeatureEnum::WORK_ORDER_MANAGEMENT,
            FeatureEnum::USER_MANAGEMENT,
            FeatureEnum::ORGANIZATION_SETTINGS,
        ];

        $this->organization->features()
            ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
    }

    $result = $this->patchJson(route('vendors.action.updateStatus', [
        'vendor' => $this->vendor->vendor_uuid,
    ]), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

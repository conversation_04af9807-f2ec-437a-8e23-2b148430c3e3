<?php

use App\Enums\Boolean;
use App\Enums\Feature as FeatureEnum;
use App\Events\ServiceRequest\ServiceRequestDescriptionUpdated;
use App\Http\Controllers\ServiceRequestController;
use App\Http\Requests\ServiceRequest\DescriptionUpdateRequest;
use App\Models\Country;
use App\Models\Feature;
use App\Models\Media;
use App\Models\Organization;
use App\Models\Permission;
use App\Models\Property;
use App\Models\Resident;
use App\Models\Role;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestDescription;
use App\Models\ServiceRequestMedia;
use App\Models\ServiceRequestStatus;
use App\Models\ServiceRequestType;
use App\Models\State;
use App\Models\Timezone;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Http\UploadedFile;

/** Setup authenticated user */
beforeEach(function () {
    // Create an organization with specific feature
    $feature = Feature::where('name', FeatureEnum::SERVICE_REQUEST_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => FeatureEnum::SERVICE_REQUEST_MANAGEMENT(),
        ])
            ->create();
    }

    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $this->organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->hasAttached($feature)
        ->create();

    $this->organization->makeCurrent();

    // Setup a user with 'Admin' role with necessary permissions
    $permission = Permission::where('name', 'ServiceRequestManagement.ServiceRequest.update')->first();

    if (empty($permission)) {
        $permission = Permission::factory()->state([
            'name' => 'ServiceRequestManagement.ServiceRequest.update',
            'label' => 'Service Request Update',
            'feature_id' => $feature->feature_id,
        ])
            ->create();
    }

    $this->user = User::factory()
        ->accountUser()
        ->for($state)
        ->for($country)
        ->for($timezone)
        ->for($this->organization)
        ->has(
            Role::factory()->state([
                'name' => 'Admin',
            ])
                ->for($this->organization)
                ->hasAttached($permission)
        )
        ->create();

    // Create a JWT token for the user
    $this->jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $this->header = [
        'Authorization' => "Bearer {$this->jtb->jwt}",
    ];

    $property = Property::factory()
        ->for($this->organization)
        ->create();

    // create a dummy service request
    $this->serviceRequest = ServiceRequest::factory()
        ->for($this->organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->for(
            Resident::factory()
                ->for($property)
                ->for($this->organization)
        )
        ->create();

    $this->serviceRequestUuid = $this->serviceRequest->service_request_uuid;
    $this->postPayLoad = [
        'description' => fake()->text(),
    ];
});

// Case: 1
test('User with a valid token can update service request description and return proper response.', function () {
    // Simulate request
    $result = $this->patchJson(route('service_requests.description.update', ['serviceRequest' => $this->serviceRequestUuid]), $this->postPayLoad, $this->header);
    // Assertion
    expect($result)
        ->assertStatus(Response::HTTP_CREATED)
        ->assertJsonIsObject()
        ->assertJsonStructure([
            'service_request_description_id',
            'description',
            'created_by',
            'created_at',
            'medias',
            'is_active',
        ])
        ->assertJsonPath('service_request_description_id', fn (string $id) => is_string($id))
        ->assertJsonPath('description', fn (string $description) => is_string($description))
        ->assertJsonPath('created_by', fn (string $name) => is_string($name) || is_null($name))
        ->assertJsonPath('created_at', fn (string $date) => is_string($date))
        ->assertJsonPath('medias', fn (array $medias) => is_array($medias))
        ->assertJsonPath('is_active', fn (bool $is_active) => is_bool($is_active));
});

// Case: 2
test('User with an invalid token cannot access service request description update API.', function ($token) {
    // Create invalid token
    $header = [
        'Authorization' => $token,
    ];
    // Simulate request
    $result = $this->patchJson(route('service_requests.description.update', ['serviceRequest' => $this->serviceRequestUuid]), $this->postPayLoad, $header);
    // Assertion
    expect($result)->assertStatus(Response::HTTP_UNAUTHORIZED);
})->with([
    'Bearer invalid-token',
    '',
]);

// Case: 3
test('The request with an invalid data return error response.', function (int $responseStatus, array $payload, ?string $uuid = null) {
    $uuid = $uuid ?? $this->serviceRequestUuid;
    $result = $this->patchJson(route('service_requests.description.update', ['serviceRequest' => $uuid]), $payload, $this->header);
    // Assertion
    expect($result)->assertStatus($responseStatus);
})->with([
    [
        Response::HTTP_NOT_FOUND,
        [
            'description' => fake()->text(),
        ],
        fake()->uuid,
    ],

    [
        Response::HTTP_UNPROCESSABLE_ENTITY,
        [
            'description' => str_repeat('a', 65536),
        ],

    ],

    [
        Response::HTTP_UNPROCESSABLE_ENTITY,
        [
            'description' => null,
        ],
    ],
]);

// Case: 4
test('The description is being stored accurately for the first time.', function () {
    Event::fake(ServiceRequestDescriptionUpdated::class);

    $media = UploadedFile::fake()->image('your-image-one.jpg', 100, 100)->size(100);
    $medias = Media::factory(5)->create(
        [
            'organization_id' => $this->organization->organization_id,
            'original_thumbnail_file_name' => $media->getClientOriginalName(),
            'mime_type' => $media->getClientMimeType(),
            'extension' => $media->getClientOriginalExtension(),
            'thumbnail_extension' => $media->getClientOriginalExtension(),
        ],
    );

    $mediaIds = [
        'media_ids' => $medias->pluck('media_id')->toArray(),
    ];

    foreach ($medias as $media) {
        ServiceRequestMedia::create([
            'service_request_id' => $this->serviceRequest->service_request_id,
            'media_id' => $media->media_id,
            'has_upload_completed' => Boolean::YES(),
        ]);
    }

    $result = $this->patchJson(route('service_requests.description.update', [
        'serviceRequest' => $this->serviceRequestUuid,
    ]), $this->postPayLoad, $this->header);

    // Assertion
    expect($result)
        ->assertStatus(Response::HTTP_CREATED)
        ->assertJsonPath('medias', fn (array $medias) => is_array($medias) && count($medias) == 5);

    Event::assertDispatched(ServiceRequestDescriptionUpdated::class);

    $serviceRequestDescription = ServiceRequestDescription::whereUuid($result->json()['service_request_description_id'])->first();

    expect($serviceRequestDescription->description)->toBe($this->postPayLoad['description']);
    expect($serviceRequestDescription->user_id)->toBe($this->user->user_id);
    expect($serviceRequestDescription->organization_id)->toBe($this->organization->organization_id);
    expect($serviceRequestDescription->deleted_at)->toBeNull();
    expect($serviceRequestDescription->additional_info)->toMatchArray($mediaIds);
});

// Case: 5
test('The description is being stored accurately.', function () {

    $result = $this->patchJson(route('service_requests.description.update', [
        'serviceRequest' => $this->serviceRequest->service_request_uuid,
    ]), $this->postPayLoad, $this->header);
    // Assertion
    expect($result)->assertStatus(Response::HTTP_CREATED);
});

// Case: 6
test('The description is being stored accurately and all the previous service request descriptions are soft deleted.', function () {

    $property = Property::factory()
        ->for($this->organization)
        ->create();

    $timezone = Timezone::factory()->create();

    // Create a service request
    $serviceRequest = ServiceRequest::factory()
        ->for($this->organization)
        ->for($property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->create();

    //Create 10 records
    ServiceRequestDescription::factory(10)->create([
        'service_request_id' => $serviceRequest->service_request_id,
        'organization_id' => $this->organization->organization_id,
        'description' => fake()->sentence(),
    ]);

    $serviceRequestDescriptionIds = ServiceRequestDescription::where('service_request_id', $serviceRequest->service_request_id)
        ->pluck('service_request_description_id')
        ->all();

    //Assure that 10 records are created
    expect(count($serviceRequestDescriptionIds))->toBe(10);

    $this->postPayLoad = [
        'description' => 'test',
    ];

    $result = $this->patchJson(route('service_requests.description.update', [
        'serviceRequest' => $serviceRequest->service_request_uuid,
    ]), $this->postPayLoad, $this->header);

    // Assure that 10 records created are deleted
    $this->assertSoftDeleted('service_request_descriptions', [
        'service_request_description_id' => $serviceRequestDescriptionIds,
    ]);

    $serviceRequestDescription = ServiceRequestDescription::where('service_request_id', $serviceRequest->service_request_id)->get();
    expect(count($serviceRequestDescription))->toBe(1);

    // Assertion
    expect($result)->assertStatus(Response::HTTP_CREATED);
});

// Case: 7
test('Check if the same description already exists, new record is not created.', function () {

    //Create a service request
    $serviceRequestDescription = ServiceRequestDescription::factory()->create([
        'service_request_id' => $this->serviceRequest->service_request_id,
        'organization_id' => $this->organization->organization_id,
        'description' => 'test',
    ]);

    //Fetch initial record count
    $initialServiceRequestDescription = ServiceRequestDescription::where('service_request_id', $this->serviceRequest->service_request_id)->get();

    $this->postPayLoad = [
        'description' => $serviceRequestDescription->description,
    ];
    $result = $this->patchJson(route('service_requests.description.update', [
        'serviceRequest' => $this->serviceRequest->service_request_uuid,
    ]), $this->postPayLoad, $this->header);

    $serviceRequestDescription = ServiceRequestDescription::where('service_request_id', $this->serviceRequest->service_request_id)->get();
    $deletedServiceRequestDescription = ServiceRequestDescription::whereNotNull('deleted_at')
        ->where('service_request_id', $this->serviceRequest->service_request_id)
        ->get();

    //Assure no record is deleted
    expect($deletedServiceRequestDescription)->toBeEmpty();
    expect(count($initialServiceRequestDescription))->toEqual(count($serviceRequestDescription));
    expect($result)->assertStatus(Response::HTTP_OK);
});

test('Update description user not found', function () {
    /** @var DescriptionUpdateRequest $request */
    $request = Mockery::mock(DescriptionUpdateRequest::class)
        ->shouldReceive('user')
        ->andReturn(null)
        ->getMock();

    Gate::shouldReceive('authorize')->with('update', Mockery::any())->andReturn(true);

    $controller = new ServiceRequestController;

    $response = $controller->updateDescription($request, $this->serviceRequest);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_INTERNAL_SERVER_ERROR);
});

test('Update description exception', function () {
    /** @var DescriptionUpdateRequest $request */
    $request = Mockery::mock(DescriptionUpdateRequest::class);
    $request->shouldReceive('get')->with('description')->andReturn('Updated description');
    $request->shouldReceive('user')->andReturn($this->user);
    Gate::shouldReceive('authorize')->with('update', Mockery::any())->andReturn(true);

    $controller = new ServiceRequestController;

    DB::shouldReceive('beginTransaction')->once();
    DB::shouldReceive('commit')->andThrow(new Exception('Test exception'));

    $response = $controller->updateDescription($request, $this->serviceRequest);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_INTERNAL_SERVER_ERROR);
});

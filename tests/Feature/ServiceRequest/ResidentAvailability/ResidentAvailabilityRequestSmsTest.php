<?php

use App\Enums\Feature as FeatureConst;
use App\Enums\Priority;
use App\Enums\ServiceRequestStatusTypes;
use App\Enums\UserTypes;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Property;
use App\Models\Resident;
use App\Models\Role;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestSource;
use App\Models\ServiceRequestStatus;
use App\Models\Timezone;
use App\Models\User;
use Illuminate\Http\Response;

beforeEach(function () {
    // Setup organization has work order feature for the test.
    $this->organizationFeature = FeatureConst::WORK_ORDER_MANAGEMENT();

    $this->organization = Organization::select(['organizations.organization_id', 'organizations.user_pool_id'])
        ->join('organization_feature', 'organization_feature.organization_id', 'organizations.organization_id')
        ->join('features', function ($joinQuery) {
            $joinQuery->on('organization_feature.feature_id', 'features.feature_id')
                ->where('features.name', $this->organizationFeature);
        })
        ->first();

    $this->organization->makeCurrent();

    // Set a random user from organization for authentication.
    $this->user = User::select(['users.user_id', 'users.cognito_user_id', 'users.organization_id'])
        ->with('organization:organization_id')
        ->where('users.organization_id', $this->organization->organization_id)
        ->join('user_role', 'user_role.user_id', 'users.user_id')
        ->join('roles', function ($joinQuery) {
            $joinQuery->on('roles.role_id', 'user_role.role_id')
                ->whereIn('roles.name', ['Admin', 'Owner']);
        })
        ->where('users.user_type', UserTypes::ACCOUNT_USER())
        ->first();

    //Create a token
    $this->jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $this->header = [
        'Authorization' => "Bearer {$this->jtb->jwt}",
    ];
    $this->imported_from = ServiceRequestSource::pluck('service_request_source_id');
    $this->residents = Resident::where('organization_id', $this->organization->organization_id)->pluck('resident_id');
    $this->priorities = collect(Priority::cases())->pluck('value');
    $this->serviceRequestStatuses = ServiceRequestStatus::whereIn('slug', [ServiceRequestStatusTypes::AWAITING_AVAILABILITY()])->select('service_request_status_id')->first();
    $this->properties = Property::where('organization_id', $this->organization->organization_id)->pluck('property_id');

    $this->timezone = Timezone::where('name', 'US/Central')->first();

    $this->serviceRequest = ServiceRequest::factory()
        ->for(Timezone::factory())
        ->create([
            'organization_id' => $this->organization->organization_id,
            'property_id' => $this->properties->random(),
            'description' => fake()->word(),
            'service_request_source_id' => $this->imported_from->random(),
            'service_request_status_id' => $this->serviceRequestStatuses->service_request_status_id,
            'requesting_resident_id' => $this->residents->random(),
            'priority' => $this->priorities->random(),
            'state' => ServiceRequestStatusTypes::AWAITING_AVAILABILITY(),
        ]);
    $page = 1;
    $this->per_page = 20;
    $this->payload = [
        'group_type' => 'open',
        'page' => $page,
        'per_page' => $this->per_page,

    ];
    $this->postPayLoad = [];

    $this->responseDataStructure = ['availability_requested_at'];
});

// Case: 1
test('User with a valid token can access the request resident availability API and the return response contains the correct keys.', function () {
    $serviceRequestStatus = ServiceRequestStatus::whereIn('slug', [ServiceRequestStatusTypes::AWAITING_AVAILABILITY()])->select('service_request_status_id')->first();

    $serviceRequest = ServiceRequest::factory()
        ->create([
            'organization_id' => $this->organization->organization_id,
            'property_id' => $this->properties->random(),
            'description' => fake()->word(),
            'service_request_source_id' => $this->imported_from->random(),
            'state' => ServiceRequestStatusTypes::AWAITING_AVAILABILITY(),
            'service_request_status_id' => $serviceRequestStatus->service_request_status_id,
            'requesting_resident_id' => $this->residents->random(),
            'priority' => $this->priorities->random(),
            'timezone_id' => $this->timezone->timezone_id,
        ]);

    $result = $this->postJson(route(
        'serviceRequest.action.requestResidentAvailability',
        [
            'serviceRequest' => $serviceRequest->service_request_uuid,
        ]
    ), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonStructure($this->responseDataStructure);
});

//Case: 2
test('The user with an invalid token cannot access the request resident availability API.', function () {
    $results = $this->getJson(route('serviceRequest.index', $this->payload), $this->header);
    $serviceRequest = json_decode($results->getContent())->data[0] ?? null;
    $this->header = [
        'Authorization' => 'Bearer Invalid-Token',
    ];

    $result = $this->postJson(route(
        'serviceRequest.action.requestResidentAvailability',
        [
            'serviceRequest' => $serviceRequest->service_request_id,
        ]
    ), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// //Case: 3
test('A user without a token cannot access the request resident availability API.', function () {

    $this->header = [
        'Authorization' => '',
    ];

    $result = $this->postJson(route(
        'serviceRequest.action.requestResidentAvailability',
        [
            'serviceRequest' => $this->serviceRequest->service_request_uuid,
        ]
    ), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 4
test('The request with an invalid work_order_uuid shows the Resource not found.', function () {

    $serviceRequestUuid = fake()->uuid();
    $result = $this->postJson(route(
        'serviceRequest.action.requestResidentAvailability',
        [
            'serviceRequest' => $serviceRequestUuid,
        ]
    ), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_NOT_FOUND);
});

// Case: 5
test('The request with an invalid work_order_task_uuid shows the Resource not found.', function () {

    $serviceRequestUuid = fake()->uuid();

    $result = $this->postJson(route(
        'serviceRequest.action.requestResidentAvailability',
        [
            'serviceRequest' => $serviceRequestUuid,
        ]
    ), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_NOT_FOUND);
});

// Case: 6
test('The request with an invalid work order uuid and work order task uuid shows the Resource not found.', function () {

    $serviceRequestUuid = fake()->uuid();

    $responseData = $this->postJson(route('serviceRequest.action.requestResidentAvailability', [
        'serviceRequest' => $serviceRequestUuid,
    ]), $this->postPayLoad, $this->header);

    expect($responseData)
        ->assertStatus(Response::HTTP_NOT_FOUND);
});

// Case: 7
test('Unauthorized organization cannot access request resident availability API.', function () {
    //Check if the organization has work order feature
    $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();
    if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {
        $features = [
            FeatureConst::ROLE_MANAGEMENT,
            FeatureConst::TECHNICIAN_MANAGEMENT,
            FeatureConst::USER_MANAGEMENT,
            FeatureConst::ORGANIZATION_SETTINGS,
        ];
        $this->organization->features()
            ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
    }
    $serviceRequest = ServiceRequest::factory()
        ->for(Timezone::factory())
        ->create([
            'organization_id' => $this->organization->organization_id,
            'property_id' => $this->properties->random(),
            'description' => fake()->word(),
            'service_request_source_id' => $this->imported_from->random(),
            'service_request_status_id' => $this->serviceRequestStatuses->service_request_status_id,
            'requesting_resident_id' => $this->residents->random(),
            'priority' => $this->priorities->random(),
            'timezone_id' => $this->timezone->timezone_id,
        ]);
    $result = $this->postJson(route(
        'serviceRequest.action.requestResidentAvailability',
        [
            'serviceRequest' => $serviceRequest->service_request_uuid,

        ]
    ), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

// Case: 8
test('Unauthorized user cannot access request resident availability API.', function () {

    //Check if the user has request availability permission
    $userPermissions = $this->user->permissions()->toArray();
    $serviceRequest = ServiceRequest::factory()
        ->for(Timezone::factory())
        ->create([
            'organization_id' => $this->organization->organization_id,
            'property_id' => $this->properties->random(),
            'description' => fake()->word(),
            'service_request_source_id' => $this->imported_from->random(),
            'service_request_status_id' => $this->serviceRequestStatuses->service_request_status_id,
            'requesting_resident_id' => $this->residents->random(),
            'priority' => $this->priorities->random(),
            'timezone_id' => $this->timezone->timezone_id,
        ]);
    if (in_array('ServiceRequestManagement.Resident.requestAvailability', $userPermissions)) {

        //Create a test role for  service request manage without request availability permission
        $serviceRequestManager = Role::updateOrCreate([
            'name' => 'Service Request Manager',
            'organization_id' => $this->organization->organization_id,
        ]);

        $serviceRequestPermissions = [
            'ServiceRequestManagement.Resident.addAvailability',
            'ServiceRequestManagement.Resident.updateAvailability',
        ];

        $serviceRequestManager->syncPermissions($serviceRequestPermissions);

        $this->user->syncRoles($serviceRequestManager);
    }

    $result = $this->postJson(route(
        'serviceRequest.action.requestResidentAvailability',
        [
            'serviceRequest' => $serviceRequest->service_request_uuid,
        ]
    ), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

    //Clear cache after test execute
    $this->user->clearPermissionCache();
});

// Case: 9
test('The service request associated with one organization cannot be access by another organization', function () {

    $anotherOrganization = Organization::select('organization_id')
        ->where('organization_id', '<>', $this->organization->organization_id)
        ->first();
    $serviceRequest = ServiceRequest::factory()
        ->for(Timezone::factory())
        ->create([
            'organization_id' => $this->organization->organization_id,
            'property_id' => $this->properties->random(),
            'description' => fake()->word(),
            'service_request_source_id' => $this->imported_from->random(),
            'service_request_status_id' => $this->serviceRequestStatuses->service_request_status_id,
            'requesting_resident_id' => $this->residents->random(),
            'priority' => $this->priorities->random(),
            'timezone_id' => $this->timezone->timezone_id,
        ]);
    $serviceRequest->organization_id = $anotherOrganization->organization_id;
    $serviceRequest->save();

    $result = $this->postJson(route(
        'serviceRequest.action.requestResidentAvailability',
        [
            'serviceRequest' => $serviceRequest->service_request_uuid,
        ]
    ), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

// Case: 10
test('The service request with invalid status cannot be access(other than in_progress, ready to schedule and awaiting availability)', function () {
    $serviceRequestStatuses = ServiceRequestStatus::whereIn('slug', [ServiceRequestStatusTypes::CLOSED()])->select('service_request_status_id')->first();

    $serviceRequest = ServiceRequest::factory()
        ->for(Timezone::factory())
        ->create([
            'organization_id' => $this->organization->organization_id,
            'property_id' => $this->properties->random(),
            'description' => fake()->word(),
            'state' => ServiceRequestStatusTypes::CLOSED(),
            'service_request_source_id' => $this->imported_from->random(),
            'service_request_status_id' => $serviceRequestStatuses->service_request_status_id,
            'requesting_resident_id' => $this->residents->random(),
            'priority' => $this->priorities->random(),
            'timezone_id' => $this->timezone->timezone_id,
        ]);

    $result = $this->postJson(route(
        'serviceRequest.action.requestResidentAvailability',
        [
            'serviceRequest' => $serviceRequest->service_request_uuid,
        ]
    ), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
});

// Case: 11
test('The work order with empty resident phone number cannot be access', function () {
    $serviceRequest = ServiceRequest::factory()
        ->for(Timezone::factory())
        ->create([
            'organization_id' => $this->organization->organization_id,
            'property_id' => $this->properties->random(),
            'description' => fake()->word(),
            'service_request_source_id' => $this->imported_from->random(),
            'service_request_status_id' => $this->serviceRequestStatuses->service_request_status_id,
            'requesting_resident_id' => $this->residents->random(),
            'priority' => $this->priorities->random(),
            'timezone_id' => $this->timezone->timezone_id,
        ]);
    $serviceRequest->resident->phone_number = null;
    $serviceRequest->resident->save();

    $result = $this->postJson(route(
        'serviceRequest.action.requestResidentAvailability',
        [
            'serviceRequest' => $serviceRequest->service_request_uuid,
        ]
    ), $this->postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
});

// Case: 15
test('The valid user can access the request resident availability action, and it will return the proper keys.', function () {
    $serviceRequestStatuses = ServiceRequestStatus::whereIn('slug', [ServiceRequestStatusTypes::SCOPING()])->select('service_request_status_id')->first();

    $service_request = ServiceRequest::factory()
        ->for(Timezone::factory())
        ->create([
            'organization_id' => $this->organization->organization_id,
            'property_id' => $this->properties->random(),
            'description' => fake()->word(),
            'state' => ServiceRequestStatusTypes::SCOPING(),
            'service_request_source_id' => $this->imported_from->random(),
            'service_request_status_id' => $serviceRequestStatuses->service_request_status_id,
            'requesting_resident_id' => $this->residents->random(),
            'priority' => $this->priorities->random(),
            'timezone_id' => $this->timezone->timezone_id,
        ]);

    $result = $this->postJson(route(
        'serviceRequest.action.requestResidentAvailability',
        [
            'serviceRequest' => $service_request->service_request_uuid,
        ]
    ), $this->postPayLoad, $this->header);
    $serviceRequest = ServiceRequest::select('state')
        ->whereUuid($service_request->service_request_uuid)
        ->first();
    if ($serviceRequest) {
        expect($serviceRequest->state->getValue())->toBe(ServiceRequestStatusTypes::AWAITING_AVAILABILITY());
    }
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonStructure($this->responseDataStructure);
});

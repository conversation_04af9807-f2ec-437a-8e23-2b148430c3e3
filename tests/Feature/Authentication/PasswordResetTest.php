<?php

use App\Mail\PasswordResetMail;
use App\Models\Organization;
use App\Models\Timezone;
use App\Models\User;
use Illuminate\Support\Facades\Mail;

uses()->group('ENG-5636-be-vendor-portal');

beforeEach(function () {
    Mail::fake();

    $this->organization = Organization::factory()->create();
    $this->organization->makeCurrent();

    $timezone = Timezone::factory()->create();
    // Set a random user from organization for authentication
    $this->user = User::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'email' => fake()->email(),
        'first_name' => fake()->firstName(),
        'user_type' => 'technician',
        'timezone_id' => $timezone->timezone_id,
    ]);

});

test('it sends a password reset email for a valid user email in vendors portal ', function () {

    $response = $this->postJson('/api/forgot-password', [
        'email' => $this->user->email,
        'client_key' => config('services.cognito.provider.vendor_domain'),
    ]);

    $response->assertStatus(200);
    $response->assertJson([
        'data' => 'If your email address exists in our system, you will receive a password reset link shortly.',
    ]);

    Mail::assertQueued(PasswordResetMail::class, function ($mail) {
        return $mail->hasTo($this->user->email);
    });
});
test('it sends a password reset email for a valid user email without client_key ', function () {

    $response = $this->postJson('/api/forgot-password', [
        'email' => $this->user->email,
    ]);

    $response->assertStatus(422);
    $response->assertJsonStructure([
        'message',
        'errors' => [
            'message',
            'errors' => [
                'client_key' => [],
            ],
        ],
    ])
        ->assertJsonFragment([
            'client_key' => ['The client key field is required.'],
        ]);
});

test('it fails with 422 for non-existent email', function () {
    $response = $this->postJson('/api/forgot-password', [
        'email' => fake()->email(),
    ]);

    $response->assertStatus(422);
    $response->assertJsonStructure([
        'message',
        'errors' => [
            'message',
            'errors' => [
                'email' => [],
            ],
        ],
    ])
        ->assertJsonFragment([
            'email' => ['The selected email is invalid.'],
        ]);
});

test('it fails with 422 when email is missing', function () {
    $response = $this->postJson('/api/forgot-password', ['email' => '']);

    $response->assertStatus(422);
    $response->assertJsonStructure([
        'message',
        'errors' => [
            'message',
            'errors' => [
                'email' => [],
            ],
        ],
    ])
        ->assertJsonFragment([
            'email' => ['The email field is required.'],
        ]);
});

test('it throws ModelNotFoundException for valid but non-existent email', function () {
    $response = $this->postJson('/api/forgot-password', [
        'email' => '<EMAIL>',
        'client_key' => config('services.cognito.provider.vendor_domain'),
    ]);

    $response->assertStatus(422);
    $response->assertJson([
        'message' => 'Validation failed (and 1 more error)',
        'errors' => [
            'message' => 'Validation failed',
            'errors' => [
                'email' => [
                    'The selected email is invalid.',
                ],
            ],
        ],
    ]);
});

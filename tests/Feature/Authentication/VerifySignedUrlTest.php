<?php

use App\Models\Organization;
use App\Models\Timezone;
use App\Models\User;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\URL;

uses()->group('ENG-5636-be-vendor-portal');

beforeEach(function () {

    $this->organization = Organization::factory()->create();
    $this->organization->makeCurrent();
    $timezone = Timezone::factory()->create();
    // Set a random user from organization for authentication
    $this->user = User::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'email' => fake()->email(),
        'first_name' => fake()->firstName(),
        'user_type' => 'technician',
        'timezone_id' => $timezone->timezone_id,
    ]);
});

test('valid signed URL passed as request input is processed successfully', function () {
    $signedUrl = URL::temporarySignedRoute(
        'verify',
        now()->addMinutes(5),
        ['id' => $this->user->user_uuid]
    );

    $response = $this->get($signedUrl);
    $response->assertOk();
    $response->assertJson([
        'success' => true,
        'id' => $this->user->user_uuid,
    ]);
});

test('invalid signature results in unprocessable response', function () {
    $invalidUrl = "/api/verify/{$this->user->user_uuid}?expires=1744395340&signature=invalidsignature123";

    $response = $this->getJson($invalidUrl);
    $response->assertStatus(Response::HTTP_FORBIDDEN);
});

test('valid signed URL but expire link', function () {
    $signedUrl = URL::temporarySignedRoute(
        'verify',
        now()->addSeconds(1),
        ['id' => $this->user->user_uuid]
    );
    sleep(3);
    $response = $this->get($signedUrl);
    $response->assertStatus(Response::HTTP_FORBIDDEN);

});

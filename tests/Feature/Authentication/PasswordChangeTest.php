<?php

use App\Models\Organization;
use App\Models\Timezone;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\URL;

uses()->group('ENG-5636-be-vendor-portal');

beforeEach(function () {
    Mail::fake();

    $this->organization = Organization::factory()->create();
    $this->organization->makeCurrent();

    // Set a random user from organization for authentication
    $timezone = Timezone::factory()->create();
    $this->user = User::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'email' => fake()->email(),
        'first_name' => fake()->firstName(),
        'user_type' => 'technician',
        'timezone_id' => $timezone->timezone_id,
    ]);

});

test('Check if password does not pass on based on requirements', function () {
    $signedUrl = URL::temporarySignedRoute(
        'verify',
        now()->addMinutes(5),
        ['id' => $this->user->user_uuid]
    );

    $response = $this->postJson('/api/reset-password', [
        'password' => 'test',
        'signedUrl' => $signedUrl,
        'client_key' => $this->organization->domain,
    ]);
    $response->assertStatus(422);
    $response->assertJson([
        'message' => 'Invalid password. Password must be at least 8 characters long and contain a number and a special character.',

    ]);

});
test('Password should be invalid when character is less than 8', function () {
    $signedUrl = URL::temporarySignedRoute(
        'verify',
        now()->addMinutes(5),
        ['id' => $this->user->user_uuid]
    );

    $response = $this->postJson('/api/reset-password', [
        'password' => $this->generateValidPassword(5),
        'signedUrl' => $signedUrl,
        'client_key' => $this->organization->domain,
    ]);
    $response->assertStatus(422);
    $response->assertJson([
        'message' => 'Invalid password. Password must be at least 8 characters long and contain a number and a special character.',

    ]);

});
test('Check if blank client_key is required', function () {
    $signedUrl = URL::temporarySignedRoute(
        'verify',
        now()->addMinutes(5),
        ['id' => $this->user->user_uuid]
    );

    $response = $this->postJson('/api/reset-password', [
        'password' => $this->generateValidPassword(8),
        'signedUrl' => $signedUrl,
        'client_key' => '',
    ]);
    $response->assertStatus(422);
    $response->assertJson([
        'message' => 'Client key is required.',

    ]);

});
test('Check if signedUrl is valid', function () {

    $response = $this->postJson('/api/reset-password', [
        'password' => $this->generateValidPassword(8),
        'signedUrl' => fake()->url(),
        'client_key' => $this->organization->domain,
    ]);

    $response->assertStatus(422);
    $response->assertJson([
        'message' => 'Invalid or expired URL.',
    ]);

});

test('it fails when signedUrl is missing', function () {
    $response = $this->postJson('/api/reset-password', [
        'password' => $this->generateValidPassword(8),
        'client_key' => $this->organization->domain,
    ]);

    $response->assertStatus(422);
    $response->assertJson([
        'message' => 'Missing signed URL.',
    ]);
});

test('it fails when signedUrl has invalid userid with empty ID', function () {
    $this->markTestSkipped('This feature is on review');
    $signedUrl = URL::temporarySignedRoute(
        'verify',
        now()->addMinutes(5),
        ['id' => '23423']
    );

    $response = $this->postJson('/api/reset-password', [
        'password' => $this->generateValidPassword(8),
        'signedUrl' => $signedUrl,
        'client_key' => $this->organization->domain,
    ]);

    $response->assertStatus(422);
    $response->assertJson([
        'message' => 'Invalid or expired URL.',
    ]);
});

test('it fails when organization is not found for given client_key', function () {
    $signedUrl = URL::temporarySignedRoute(
        'verify',
        now()->addMinutes(5),
        ['id' => $this->user->user_uuid]
    );

    $response = $this->postJson('/api/reset-password', [
        'password' => $this->generateValidPassword(8),
        'signedUrl' => $signedUrl,
        'client_key' => 'nonexistent-org.fake.local',
    ]);

    $response->assertStatus(500);
    $response->assertJson([
        'message' => 'Something went wrong, The server was unable to process.',
    ]);
});

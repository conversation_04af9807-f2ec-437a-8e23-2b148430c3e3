<?php

namespace Tests\Feature\Routes;

use App\Models\Organization;
use App\Models\Role;
use App\Models\ServiceRequest;
use App\Models\User;
use App\Models\Vendor;
use App\Models\WorkOrder;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

/**
 * Pruebas HTTP que ejercen todas las rutas de routes/api.php para cobertura de código
 * 
 * @group ENG-5636-be-vendor-portal-feature-filters
 * @group routes
 * @group coverage
 * @group http
 */
class ApiRoutesCoverageTest extends TestCase
{
    use DatabaseTransactions;

    protected Organization $organization;
    protected User $user;
    protected Role $role;

    protected function setUp(): void
    {
        parent::setUp();

        // Crear organización y usuario para pruebas autenticadas
        $this->organization = Organization::factory()->create();
        $this->organization->makeCurrent();

        $this->role = Role::factory()->for($this->organization)->create(['name' => 'Admin']);
        $this->user = User::factory()
            ->for($this->organization)
            ->hasAttached($this->role)
            ->create(['user_type' => 'account_user']);
    }

    /**
     * @test
     * @covers routes/api.php::50-64
     * Prueba todas las rutas públicas para cobertura
     */
    public function rutas_publicas_responden_correctamente()
    {
        // Forzar carga del archivo de rutas
        require base_path('routes/api.php');

        // GET /api/config
        $response = $this->getJson('/api/config');
        $response->assertStatus(200);

        // POST /api/forgot-password (esperamos validación)
        $response = $this->postJson('/api/forgot-password', []);
        $this->assertContains($response->status(), [422, 400, 200]);

        // POST /api/reset-password (esperamos validación)
        $response = $this->postJson('/api/reset-password', []);
        $this->assertContains($response->status(), [422, 400, 200]);

        // GET /api/provider/config
        $response = $this->getJson('/api/provider/config');
        $response->assertStatus(200);

        // POST /api/organization/create (esperamos validación)
        $response = $this->postJson('/api/organization/create', []);
        $this->assertContains($response->status(), [422, 400, 200]);

        // POST /api/organization/validate-sso-user (esperamos validación)
        $response = $this->postJson('/api/organization/validate-sso-user', []);
        $this->assertContains($response->status(), [422, 400, 200]);

        // GET /api/check-app-version
        $response = $this->getJson('/api/check-app-version');
        $this->assertContains($response->status(), [200, 404]);

        // POST /api/vendor/login (esperamos validación)
        $response = $this->postJson('/api/vendor/login', []);
        $this->assertContains($response->status(), [422, 400, 401]);
    }

    /**
     * @test
     * @covers routes/api.php::89-99
     * Prueba rutas de usuarios
     */
    public function rutas_usuarios_responden_correctamente()
    {
        require base_path('routes/api.php');

        // GET /api/users/filter
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/users/filter');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/users/group-view
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/users/group-view');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/users
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/users');
        $this->assertContains($response->status(), [200, 422]);

        // POST /api/users (esperamos validación)
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/users', []);
        $this->assertContains($response->status(), [422, 400, 201]);

        // GET /api/users/{user}
        $response = $this->actingAs($this->user, 'api')
            ->getJson("/api/users/{$this->user->user_uuid}");
        $this->assertContains($response->status(), [200, 404]);

        // PUT /api/users/{user} (esperamos validación)
        $response = $this->actingAs($this->user, 'api')
            ->putJson("/api/users/{$this->user->user_uuid}", []);
        $this->assertContains($response->status(), [422, 400, 200]);
    }

    /**
     * @test
     * @covers routes/api.php::101-112
     * Prueba rutas de técnicos
     */
    public function rutas_tecnicos_responden_correctamente()
    {
        require base_path('routes/api.php');

        $technician = User::factory()
            ->for($this->organization)
            ->create(['user_type' => 'technician']);

        // GET /api/technicians/{technician}/working-hours
        $response = $this->actingAs($this->user, 'api')
            ->getJson("/api/technicians/{$technician->user_uuid}/working-hours");
        $this->assertContains($response->status(), [200, 404, 422]);

        // PUT /api/technicians/{technician}/working-hours
        $response = $this->actingAs($this->user, 'api')
            ->putJson("/api/technicians/{$technician->user_uuid}/working-hours", []);
        $this->assertContains($response->status(), [422, 400, 200]);

        // GET /api/technicians/{technician}/skills
        $response = $this->actingAs($this->user, 'api')
            ->getJson("/api/technicians/{$technician->user_uuid}/skills");
        $this->assertContains($response->status(), [200, 404, 422]);

        // GET /api/technicians/{technician}/calendar
        $response = $this->actingAs($this->user, 'api')
            ->getJson("/api/technicians/{$technician->user_uuid}/calendar");
        $this->assertContains($response->status(), [200, 404, 422]);
    }

    /**
     * @test
     * @covers routes/api.php::113-118
     * Prueba rutas de roles y permisos
     */
    public function rutas_roles_responden_correctamente()
    {
        require base_path('routes/api.php');

        // GET /api/roles
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/roles');
        $this->assertContains($response->status(), [200, 422]);

        // POST /api/roles
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/roles', []);
        $this->assertContains($response->status(), [422, 400, 201]);

        // GET /api/roles/{role}
        $response = $this->actingAs($this->user, 'api')
            ->getJson("/api/roles/{$this->role->role_id}");
        $this->assertContains($response->status(), [200, 404]);

        // GET /api/permissions
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/permissions');
        $this->assertContains($response->status(), [200, 422]);
    }

    /**
     * @test
     * @covers routes/api.php::127-206
     * Prueba rutas de órdenes de trabajo
     */
    public function rutas_work_orders_responden_correctamente()
    {
        require base_path('routes/api.php');

        // GET /api/work-orders/group-view
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/work-orders/group-view');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/work-orders/filter
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/work-orders/filter');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/work-orders/count
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/work-orders/count');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/work-orders
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/work-orders');
        $this->assertContains($response->status(), [200, 422]);

        // POST /api/work-orders
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/work-orders', []);
        $this->assertContains($response->status(), [422, 400, 201]);

        // Crear una orden de trabajo para pruebas específicas
        $workOrder = WorkOrder::factory()->for($this->organization)->create();

        // GET /api/work-orders/{workOrder}
        $response = $this->actingAs($this->user, 'api')
            ->getJson("/api/work-orders/{$workOrder->work_order_uuid}");
        $this->assertContains($response->status(), [200, 404]);

        // PATCH /api/work-orders/{workOrder}/priority
        $response = $this->actingAs($this->user, 'api')
            ->patchJson("/api/work-orders/{$workOrder->work_order_uuid}/priority", []);
        $this->assertContains($response->status(), [422, 400, 200]);
    }

    /**
     * @test
     * @covers routes/api.php::237-270
     * Prueba rutas de solicitudes de servicio
     */
    public function rutas_service_requests_responden_correctamente()
    {
        require base_path('routes/api.php');

        // GET /api/service-requests/group-view
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/service-requests/group-view');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/service-requests/filter
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/service-requests/filter');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/service-requests
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/service-requests');
        $this->assertContains($response->status(), [200, 422]);

        // POST /api/service-requests
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/service-requests', []);
        $this->assertContains($response->status(), [422, 400, 201]);

        // Crear una solicitud de servicio para pruebas específicas
        $serviceRequest = ServiceRequest::factory()->for($this->organization)->create();

        // GET /api/service-requests/{serviceRequest}
        $response = $this->actingAs($this->user, 'api')
            ->getJson("/api/service-requests/{$serviceRequest->service_request_uuid}");
        $this->assertContains($response->status(), [200, 404]);
    }

    /**
     * @test
     * @covers routes/api.php::220-235
     * Prueba rutas de proveedores
     */
    public function rutas_vendors_responden_correctamente()
    {
        require base_path('routes/api.php');

        // GET /api/vendors/work-orders
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/vendors/work-orders');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/vendors
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/vendors');
        $this->assertContains($response->status(), [200, 422]);

        // POST /api/vendors
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/vendors', []);
        $this->assertContains($response->status(), [422, 400, 201]);

        // Crear un proveedor para pruebas específicas
        $vendor = Vendor::factory()->create();

        // GET /api/vendors/{vendorUser}
        $response = $this->actingAs($this->user, 'api')
            ->getJson("/api/vendors/{$vendor->vendor_uuid}");
        $this->assertContains($response->status(), [200, 404]);
    }

    /**
     * @test
     * @covers routes/api.php::287-307
     * Prueba rutas misceláneas
     */
    public function rutas_miscelaneas_responden_correctamente()
    {
        require base_path('routes/api.php');

        // GET /api/tags
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/tags');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/lookup
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/lookup');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/profile
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/profile');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/organization
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/organization');
        $this->assertContains($response->status(), [200, 422]);

        // GET /api/notifications
        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/notifications');
        $this->assertContains($response->status(), [200, 422]);
    }
}

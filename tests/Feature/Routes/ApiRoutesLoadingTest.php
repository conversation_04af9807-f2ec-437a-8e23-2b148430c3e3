<?php

namespace Tests\Feature\Routes;

use Illuminate\Support\Facades\Route;
use Tests\TestCase;

/**
 * Test to force loading of routes/api.php file and ensure code coverage
 *
 * @group ENG-5636-be-vendor-portal-feature-filters
 * @group routes
 * @group coverage
 */
class ApiRoutesLoadingTest extends TestCase
{

    /**
     * @test
     * Forces loading of routes/api.php file for code coverage
     */
    public function api_routes_are_loaded()
    {
        // Force explicit loading of routes/api.php file
        require base_path('routes/api.php');

        // Verify that routes are registered
        $routes = app('router')->getRoutes();
        $this->assertNotEmpty($routes, 'Routes should be loaded');

        // Verify that we have specific API routes
        $apiRoutes = collect($routes)->filter(function ($route) {
            return str_starts_with($route->uri(), 'api/') || $route->uri() === 'api';
        });

        $this->assertGreaterThan(0, $apiRoutes->count(), 'There should be API routes loaded');
    }

    /**
     * @test
     * Verifies that all main routes are registered
     */
    public function all_main_routes_are_registered()
    {
        // Force loading of the file
        require base_path('routes/api.php');

        $routeNames = collect(Route::getRoutes())->map(function ($route) {
            return $route->getName();
        })->filter()->toArray();

        // Verify critical public routes
        $criticalPublicRoutes = [
            'config',
            'forgot-password',
            'reset-password',
            'verify',
            'provider.config',
            'auth.public-api',
            'check-latest-app-version',
            'vendor.login',
        ];

        foreach ($criticalPublicRoutes as $routeName) {
            $this->assertContains($routeName, $routeNames,
                "Route '{$routeName}' should be registered");
        }

        // Verify critical authenticated routes
        $criticalAuthenticatedRoutes = [
            'users.index',
            'roles.index',
            'profile',
            'organization.show',
            'workOrder.index',
            'serviceRequest.index',
            'vendors.index',
        ];

        foreach ($criticalAuthenticatedRoutes as $routeName) {
            $this->assertContains($routeName, $routeNames,
                "Authenticated route '{$routeName}' should be registered");
        }
    }

    /**
     * @test
     * Verifies that route groups are correctly configured
     */
    public function route_groups_are_configured()
    {
        // Force loading of the file
        require base_path('routes/api.php');

        $routes = Route::getRoutes();
        $routeUris = collect($routes)->map(function ($route) {
            return $route->uri();
        })->toArray();

        // Verify main route groups
        $expectedGroups = [
            'vendor-onboarding/' => 'Vendor onboarding group',
            'users/' => 'Users group',
            'technicians/' => 'Technicians group',
            'work-orders/' => 'Work orders group',
            'service-requests/' => 'Service requests group',
            'vendors/' => 'Vendors group',
            'views/' => 'Views group',
        ];

        foreach ($expectedGroups as $prefix => $description) {
            $hasRoutesWithPrefix = collect($routeUris)->some(function ($uri) use ($prefix) {
                return str_contains($uri, $prefix);
            });

            $this->assertTrue($hasRoutesWithPrefix,
                "There should be at least one route with prefix '{$prefix}' for {$description}");
        }
    }

    /**
     * @test
     * Verifies that middlewares are correctly applied
     */
    public function middlewares_are_correctly_applied()
    {
        // Force loading of the file
        require base_path('routes/api.php');

        $routes = Route::getRoutes();

        // Verify that routes with authentication middleware exist
        $routesWithAuth = collect($routes)->filter(function ($route) {
            $middleware = $route->gatherMiddleware();
            return in_array('auth.cognito', $middleware) && in_array('tenant', $middleware);
        });

        $this->assertGreaterThan(0, $routesWithAuth->count(),
            'There should be routes with authentication middleware');

        // Verify that routes with signed middleware exist
        $routesWithSigned = collect($routes)->filter(function ($route) {
            $middleware = $route->gatherMiddleware();
            return in_array('signed', $middleware);
        });

        $this->assertGreaterThan(0, $routesWithSigned->count(),
            'There should be routes with signed middleware');

        // Verify that routes with throttle exist
        $routesWithThrottle = collect($routes)->filter(function ($route) {
            $middleware = $route->gatherMiddleware();
            return collect($middleware)->some(function ($m) {
                return str_contains($m, 'throttle:');
            });
        });

        $this->assertGreaterThan(0, $routesWithThrottle->count(),
            'There should be routes with throttle middleware');
    }

    /**
     * @test
     * Verifies that controllers are correctly linked
     */
    public function controllers_are_linked()
    {
        // Force loading of the file
        require base_path('routes/api.php');

        $routes = Route::getRoutes();
        $expectedControllers = [
            'RegisterController',
            'UserController',
            'WorkOrderController',
            'ServiceRequestController',
            'VendorController',
            'RoleController',
            'OrganizationController',
            'ProfileController',
            'TagController',
            'LookupController',
        ];

        foreach ($expectedControllers as $controller) {
            $hasController = collect($routes)->some(function ($route) use ($controller) {
                $action = $route->getAction();
                return isset($action['controller']) && str_contains($action['controller'], $controller);
            });

            $this->assertTrue($hasController,
                "Controller '{$controller}' should be linked to at least one route");
        }
    }

    /**
     * @test
     * Verifies that route parameters are correctly defined
     */
    public function route_parameters_are_defined()
    {
        // Force loading of the file
        require base_path('routes/api.php');

        $routes = Route::getRoutes();
        $expectedParameters = [
            'user' => 'User parameter',
            'workOrder' => 'Work order parameter',
            'serviceRequest' => 'Service request parameter',
            'vendor' => 'Vendor parameter',
            'role' => 'Role parameter',
            'tag' => 'Tag parameter',
            'technician' => 'Technician parameter',
        ];

        foreach ($expectedParameters as $parameter => $description) {
            $hasParameter = collect($routes)->some(function ($route) use ($parameter) {
                return in_array($parameter, $route->parameterNames());
            });

            $this->assertTrue($hasParameter,
                "There should be at least one route with parameter '{$parameter}' for {$description}");
        }
    }
}

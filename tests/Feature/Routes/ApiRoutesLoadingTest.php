<?php

namespace Tests\Feature\Routes;

use Illuminate\Support\Facades\Route;
use Tests\TestCase;

/**
 * Test para forzar la carga del archivo routes/api.php y garantizar cobertura de código
 *
 * @group ENG-5636-be-vendor-portal-feature-filters
 * @group routes
 * @group coverage
 */
class ApiRoutesLoadingTest extends TestCase
{

    /**
     * @test
     * Fuerza la carga del archivo routes/api.php para cobertura de código
     */
    public function las_rutas_api_estan_cargadas()
    {
        // Forzar la carga explícita del archivo routes/api.php
        require base_path('routes/api.php');

        // Verificar que las rutas están registradas
        $routes = app('router')->getRoutes();
        $this->assertNotEmpty($routes, 'Las rutas deben estar cargadas');

        // Verificar que tenemos rutas específicas de la API
        $apiRoutes = collect($routes)->filter(function ($route) {
            return str_starts_with($route->uri(), 'api/') || $route->uri() === 'api';
        });

        $this->assertGreaterThan(0, $apiRoutes->count(), 'Debe haber rutas API cargadas');
    }

    /**
     * @test
     * Verifica que todas las rutas principales están registradas
     */
    public function todas_las_rutas_principales_estan_registradas()
    {
        // Forzar la carga del archivo
        require base_path('routes/api.php');

        $routeNames = collect(Route::getRoutes())->map(function ($route) {
            return $route->getName();
        })->filter()->toArray();

        // Verificar rutas públicas críticas
        $rutasPublicasCriticas = [
            'config',
            'forgot-password',
            'reset-password',
            'verify',
            'provider.config',
            'auth.public-api',
            'check-latest-app-version',
            'vendor.login',
        ];

        foreach ($rutasPublicasCriticas as $rutaNombre) {
            $this->assertContains($rutaNombre, $routeNames,
                "La ruta '{$rutaNombre}' debe estar registrada");
        }

        // Verificar rutas autenticadas críticas
        $rutasAutenticadasCriticas = [
            'users.index',
            'roles.index',
            'profile',
            'organization.show',
            'workOrder.index',
            'serviceRequest.index',
            'vendors.index',
        ];

        foreach ($rutasAutenticadasCriticas as $rutaNombre) {
            $this->assertContains($rutaNombre, $routeNames,
                "La ruta autenticada '{$rutaNombre}' debe estar registrada");
        }
    }

    /**
     * @test
     * Verifica que los grupos de rutas están correctamente configurados
     */
    public function grupos_de_rutas_estan_configurados()
    {
        // Forzar la carga del archivo
        require base_path('routes/api.php');

        $routes = Route::getRoutes();
        $routeUris = collect($routes)->map(function ($route) {
            return $route->uri();
        })->toArray();

        // Verificar grupos de rutas principales
        $gruposEsperados = [
            'vendor-onboarding/' => 'Grupo de vendor onboarding',
            'users/' => 'Grupo de usuarios',
            'technicians/' => 'Grupo de técnicos',
            'work-orders/' => 'Grupo de órdenes de trabajo',
            'service-requests/' => 'Grupo de solicitudes de servicio',
            'vendors/' => 'Grupo de proveedores',
            'views/' => 'Grupo de vistas',
        ];

        foreach ($gruposEsperados as $prefijo => $descripcion) {
            $tieneRutasConPrefijo = collect($routeUris)->some(function ($uri) use ($prefijo) {
                return str_contains($uri, $prefijo);
            });

            $this->assertTrue($tieneRutasConPrefijo,
                "Debe existir al menos una ruta con el prefijo '{$prefijo}' para {$descripcion}");
        }
    }

    /**
     * @test
     * Verifica que los middlewares están correctamente aplicados
     */
    public function middlewares_estan_aplicados_correctamente()
    {
        // Forzar la carga del archivo
        require base_path('routes/api.php');

        $routes = Route::getRoutes();

        // Verificar que existen rutas con middleware de autenticación
        $rutasConAuth = collect($routes)->filter(function ($route) {
            $middleware = $route->gatherMiddleware();
            return in_array('auth.cognito', $middleware) && in_array('tenant', $middleware);
        });

        $this->assertGreaterThan(0, $rutasConAuth->count(),
            'Debe haber rutas con middleware de autenticación');

        // Verificar que existen rutas con middleware signed
        $rutasConSigned = collect($routes)->filter(function ($route) {
            $middleware = $route->gatherMiddleware();
            return in_array('signed', $middleware);
        });

        $this->assertGreaterThan(0, $rutasConSigned->count(),
            'Debe haber rutas con middleware signed');

        // Verificar que existen rutas con throttle
        $rutasConThrottle = collect($routes)->filter(function ($route) {
            $middleware = $route->gatherMiddleware();
            return collect($middleware)->some(function ($m) {
                return str_contains($m, 'throttle:');
            });
        });

        $this->assertGreaterThan(0, $rutasConThrottle->count(),
            'Debe haber rutas con middleware throttle');
    }

    /**
     * @test
     * Verifica que los controladores están correctamente vinculados
     */
    public function controladores_estan_vinculados()
    {
        // Forzar la carga del archivo
        require base_path('routes/api.php');

        $routes = Route::getRoutes();
        $controladoresEsperados = [
            'RegisterController',
            'UserController',
            'WorkOrderController',
            'ServiceRequestController',
            'VendorController',
            'RoleController',
            'OrganizationController',
            'ProfileController',
            'TagController',
            'LookupController',
        ];

        foreach ($controladoresEsperados as $controlador) {
            $tieneControlador = collect($routes)->some(function ($route) use ($controlador) {
                $action = $route->getAction();
                return isset($action['controller']) && str_contains($action['controller'], $controlador);
            });

            $this->assertTrue($tieneControlador,
                "El controlador '{$controlador}' debe estar vinculado a al menos una ruta");
        }
    }

    /**
     * @test
     * Verifica que los parámetros de ruta están correctamente definidos
     */
    public function parametros_de_ruta_estan_definidos()
    {
        // Forzar la carga del archivo
        require base_path('routes/api.php');

        $routes = Route::getRoutes();
        $parametrosEsperados = [
            'user' => 'Parámetro de usuario',
            'workOrder' => 'Parámetro de orden de trabajo',
            'serviceRequest' => 'Parámetro de solicitud de servicio',
            'vendor' => 'Parámetro de proveedor',
            'role' => 'Parámetro de rol',
            'tag' => 'Parámetro de etiqueta',
            'technician' => 'Parámetro de técnico',
        ];

        foreach ($parametrosEsperados as $parametro => $descripcion) {
            $tieneParametro = collect($routes)->some(function ($route) use ($parametro) {
                return in_array($parametro, $route->parameterNames());
            });

            $this->assertTrue($tieneParametro,
                "Debe existir al menos una ruta con el parámetro '{$parametro}' para {$descripcion}");
        }
    }
}

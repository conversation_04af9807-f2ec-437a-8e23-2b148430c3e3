<?php

use App\Enums\Feature as FeatureEnum;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use Aws\CognitoIdentityProvider\CognitoIdentityProviderClient;
use Illuminate\Http\Response;

beforeEach(function () {
    // Setup organization has role management feature for the test.
    $this->organizationFeature = FeatureEnum::ROLE_MANAGEMENT();

    $this->organization = Organization::join('organization_feature', 'organization_feature.organization_id', 'organizations.organization_id')
        ->join('features', function ($joinQuery) {
            $joinQuery->on('organization_feature.feature_id', 'features.feature_id')
                ->where('features.name', $this->organizationFeature);
        })
        ->select('organizations.organization_id', 'organizations.user_pool_id')
        ->first();

    $this->organization->makeCurrent();

    // Set a random user from organization for authentication.
    $this->user = User::where('users.organization_id', $this->organization->organization_id)
        ->join('user_role', 'user_role.user_id', 'users.user_id')
        ->join('roles', function ($joinQuery) {
            $joinQuery->on('roles.role_id', 'user_role.role_id')
                ->whereIn('roles.name', ['Admin', 'Owner']);
        })
        ->where('users.user_type', 'account_user')
        ->first();

    // Create a CognitoIdentityProviderClient object
    $this->cognitoClient = new CognitoIdentityProviderClient([
        'region' => config('services.cognito.region'),
        'version' => 'latest',
    ]);
});

// Case: 1
test('User with a valid token can create role and return proper response', function () {
    $jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $randomPermission = Permission::join('organization_feature', function ($join) {
        $join->on('permissions.feature_id', 'organization_feature.feature_id')
            ->where('organization_feature.organization_id', $this->user->organization_id);
    })
        ->select('permission_id', 'permission_uuid')
        ->get()
        ->pluck('permission_uuid')
        ->random(3)
        ->toArray();

    $postPayLoad = [
        'name' => fake()->firstName(),
        'organization_id' => $this->user->organization_id,
        'permissions' => $randomPermission,
    ];

    $result = $this->postJson(route('roles.store'), $postPayLoad, $header);
    $roleName = $postPayLoad['name'];

    expect($result)
        ->assertStatus(Response::HTTP_CREATED)
        ->assertJsonStructure([
            'role_id',
            'name',
            'parent_role_id',
            'permissions',
            'can_modify',
            'description',
        ]);

    $result = json_decode($result->getContent());
    expect($result->name)->toEqual($roleName);
});

// Case: 2
test('User with an invalid token cannot access role create API', function () {

    $header = [
        'Authorization' => 'Bearer invalid-token',
    ];
    $randomPermission = Permission::join('organization_feature', function ($join) {
        $join->on('permissions.feature_id', 'organization_feature.feature_id')
            ->where('organization_feature.organization_id', $this->user->organization_id);
    })
        ->select('permission_id', 'permission_uuid')
        ->get()
        ->pluck('permission_uuid')
        ->random(3)
        ->toArray();

    $postPayLoad = [
        'name' => fake()->firstName(),
        'organization_id' => $this->user->organization_id,
        'permissions' => $randomPermission,
    ];

    $result = $this->postJson(route('roles.store'), $postPayLoad, $header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 3
test('User without a token cannot access role create API', function () {

    $header = [
        'Authorization' => '',
    ];

    $randomPermission = Permission::join('organization_feature', function ($join) {
        $join->on('permissions.feature_id', 'organization_feature.feature_id')
            ->where('organization_feature.organization_id', $this->user->organization_id);
    })
        ->select('permission_id', 'permission_uuid')
        ->get()
        ->pluck('permission_uuid')
        ->random(3)
        ->toArray();

    $postPayLoad = [
        'name' => fake()->firstName(),
        'organization_id' => $this->user->organization_id,
        'permissions' => $randomPermission,
    ];

    $result = $this->postJson(route('roles.store'), $postPayLoad, $header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 4
test('Unauthorized user cannot access role create API', function () {

    $jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    //Check if the user has role store permission
    $userPermissions = $this->user->permissions()->toArray();

    if (in_array('RoleManagement.Role.create', $userPermissions)) {

        //Create a test role for role manage without store permission
        $roleManager = Role::updateOrCreate(['name' => 'Role Manager', 'organization_id' => $this->organization->organization_id]);
        $roleManagePermissions = [
            'RoleManagement.Role.update',
            'RoleManagement.Role.delete',
        ];

        $roleManager->syncPermissions($roleManagePermissions);

        $this->user->syncRoles($roleManager);
    }

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $randomPermission = Permission::join('organization_feature', function ($join) {
        $join->on('permissions.feature_id', 'organization_feature.feature_id')
            ->where('organization_feature.organization_id', $this->user->organization_id);
    })
        ->select('permission_id', 'permission_uuid')
        ->get()
        ->pluck('permission_uuid')
        ->random(3)
        ->toArray();

    $postPayLoad = [
        'name' => fake()->firstName(),
        'organization_id' => $this->user->organization_id,
        'permissions' => $randomPermission,
    ];

    $result = $this->postJson(route('roles.store'), $postPayLoad, $header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

    //Clear cache after test execute
    $this->user->clearPermissionCache();
});

// Case: 5
test('Unauthorized organization cannot access role create API', function () {

    $jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    //Check if the organization has role management feature
    $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();

    if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {

        $features = [
            FeatureEnum::WORK_ORDER_MANAGEMENT,
            FeatureEnum::USER_MANAGEMENT,
            FeatureEnum::ORGANIZATION_SETTINGS,
        ];

        $this->organization->features()
            ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
    }

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $randomPermission = Permission::join('organization_feature', function ($join) {
        $join->on('permissions.feature_id', 'organization_feature.feature_id')
            ->where('organization_feature.organization_id', $this->user->organization_id);
    })
        ->select('permission_id', 'permission_uuid')
        ->get()
        ->pluck('permission_uuid')
        ->random(3)
        ->toArray();

    $postPayLoad = [
        'name' => fake()->firstName(),
        'organization_id' => $this->user->organization_id,
        'permissions' => $randomPermission,
    ];

    $result = $this->postJson(route('roles.store'), $postPayLoad, $header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

});

// Case: 6
test('The request with an empty payload shows validation error.', function () {
    $jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $postPayLoad = [];

    $result = $this->postJson(route('roles.store'), $postPayLoad, $header);

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
        ->assertJsonStructure([
            'message',
            'errors',
        ]);
});

// Case: 7
test('The request with an invalid payload shows validation error.', function () {
    $jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $postPayLoad = [
        'name' => '',
        'organization_id' => '',
        'permissions' => '',
    ];

    $result = $this->postJson(route('roles.store'), $postPayLoad, $header);

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
        ->assertInvalid(['name', 'permissions']);
});

// Case: 8
test('The request with a payload having repeated name shows validation error.', function () {
    $jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $name = Role::select('name')->first();
    $postPayLoad = [
        'name' => $name,
        'organization_id' => $this->user->organization_id,
        'permissions' => '',
    ];

    $result = $this->postJson(route('roles.store'), $postPayLoad, $header);

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
        ->assertJsonStructure([
            'message',
            'errors',
        ]);
});

// Case: 9
test('It is possible to create a new role with the name of already deleted role.', function () {
    $jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $randomPermission = Permission::join('organization_feature', function ($join) {
        $join->on('permissions.feature_id', 'organization_feature.feature_id')
            ->where('organization_feature.organization_id', $this->user->organization_id);
    })
        ->select('permission_id', 'permission_uuid')
        ->get()
        ->pluck('permission_uuid')
        ->random(3)
        ->toArray();

    $this->role = Role::create([
        'name' => fake()->firstName(),
        'organization_id' => $this->user->organization_id,
    ]);

    $this->role->delete();

    $postPayLoad = [
        'name' => $this->role->name,
        'organization_id' => $this->role->organization_id,
        'permissions' => $randomPermission,
    ];

    $result = $this->postJson(route('roles.store'), $postPayLoad, $header);
    $responseData = json_decode($result->getContent());

    expect($responseData->name)->toEqual($this->role->name);
    expect($result)
        ->assertStatus(Response::HTTP_CREATED)
        ->assertJsonStructure([
            'role_id',
            'name',
            'parent_role_id',
            'permissions',
            'can_modify',
            'description',
        ]);
});

<?php

use App\Enums\Feature as FeatureEnum;
use App\Enums\ViewTypes;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Role;
use App\Models\Timezone;
use App\Models\User;
use App\Models\View;
use App\Models\ViewType;
use Illuminate\Http\Response;

beforeEach(function () {
    // Setup organization has work order management feature for the test.
    $this->organizationFeature = FeatureEnum::WORK_ORDER_MANAGEMENT();

    $this->organization = Organization::join('organization_feature', 'organization_feature.organization_id', 'organizations.organization_id')
        ->join('features', function ($joinQuery) {
            $joinQuery->on('organization_feature.feature_id', 'features.feature_id')
                ->where('features.name', $this->organizationFeature);
        })
        ->select('organizations.organization_id', 'organizations.user_pool_id')
        ->first();

    $this->organization->makeCurrent();

    // Set a random user from organization for authentication.
    $this->user = User::where('users.organization_id', $this->organization->organization_id)
        ->join('user_role', 'user_role.user_id', 'users.user_id')
        ->join('roles', function ($joinQuery) {
            $joinQuery->on('roles.role_id', 'user_role.role_id')
                ->whereIn('roles.name', ['Admin', 'Owner']);
        })
        ->where('users.user_type', 'account_user')
        ->first();

    //Create a token
    $this->jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $this->header = [
        'Authorization' => "Bearer {$this->jtb->jwt}",
    ];

});

// Case: 1
test('The valid user can duplicate his own work order view, and it will return the proper keys after the duplication.', function () {

    $postPayLoad = [
        'name' => 'Test view',
        'view_type' => ViewTypes::WORK_ORDERS(),
    ];

    $payload = config('listview.work_order_default_payload');

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::WORK_ORDERS())
        ->first();

    $view = View::create([
        'name' => 'Test',
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_CREATED)
        ->assertJsonStructure([
            'view' => [
                'label',
                'value',
            ],
            'is_default',
            'is_pinned',
            'is_editable',
            'is_deletable',
            'is_able_to_pin',
        ]);

});

// Case: 2
test('User with a invalid token cannot access work order view duplicate API', function () {

    $this->header = [
        'Authorization' => 'Bearer invalid-token',
    ];

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::WORK_ORDERS(),
    ];

    $payload = config('listview.work_order_default_payload');

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::WORK_ORDERS())
        ->first();

    $view = View::create([
        'name' => 'Test',
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 3
test('User without a token cannot access work order view duplicate API', function () {

    $this->header = [
        'Authorization' => '',
    ];

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::WORK_ORDERS(),
    ];

    $payload = config('listview.work_order_default_payload');

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::WORK_ORDERS())
        ->first();

    $view = View::create([
        'name' => 'Test',
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 4
test('The request with an empty payload shows the validation error.', function () {

    $postPayLoad = [];
    $payload = config('listview.work_order_default_payload');

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::WORK_ORDERS())
        ->first();

    $view = View::create([
        'name' => 'Test',
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
        ->assertJsonStructure([
            'message',
            'errors',
        ]);
});

// Case: 5
test('The request with an invalid payload shows the validation error.', function () {

    $postPayLoad = [
        'name' => '',
        'view_type' => '',
    ];
    $payload = config('listview.work_order_default_payload');

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::WORK_ORDERS())
        ->first();

    $view = View::create([
        'name' => 'Test',
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
        ->assertJsonStructure([
            'message',
            'errors',
        ]);
});

// Case: 6
test('Unauthorized organization cannot access work order view duplicate API.', function () {

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::WORK_ORDERS(),
    ];

    $payload = config('listview.work_order_default_payload');

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::WORK_ORDERS())
        ->first();

    $view = View::create([
        'name' => 'Test',
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    //Check if the organization has work order management feature
    $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();

    if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {

        $features = [
            FeatureEnum::TECHNICIAN_MANAGEMENT,
            FeatureEnum::ROLE_MANAGEMENT,
            FeatureEnum::USER_MANAGEMENT,
            FeatureEnum::ORGANIZATION_SETTINGS,
        ];

        $this->organization->features()
            ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
    }

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

});

//Case: 7
test('Unauthorized organization cannot access user view duplicate API.', function () {

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::USERS(),
    ];

    $payload = config('listview.users_list_default_payload');

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::USERS())
        ->first();

    $view = View::create([
        'name' => 'Test',
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    //Check if the organization has user management feature
    $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();

    if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {

        $features = [
            FeatureEnum::TECHNICIAN_MANAGEMENT,
            FeatureEnum::ROLE_MANAGEMENT,
            FeatureEnum::WORK_ORDER_MANAGEMENT,
            FeatureEnum::ORGANIZATION_SETTINGS,
        ];

        $this->organization->features()
            ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
    }

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

});

//Case: 8
test('Unauthorized organization cannot access calendar view duplicate API.', function () {

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::CALENDAR(),
    ];

    $payload = config('listview.users_list_default_payload');

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::CALENDAR())
        ->first();

    $view = View::create([
        'name' => 'Test',
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    //Check if the organization has calendar management feature
    $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();

    if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {

        $features = [
            FeatureEnum::TECHNICIAN_MANAGEMENT,
            FeatureEnum::ROLE_MANAGEMENT,
            FeatureEnum::WORK_ORDER_MANAGEMENT,
            FeatureEnum::ORGANIZATION_SETTINGS,
        ];

        $this->organization->features()
            ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
    }

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

});

// Case: 9
test('Unauthorized user cannot access view duplicate API (without work order duplicate permission)', function () {

    $payload = config('listview.work_order_default_payload');

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::WORK_ORDERS())
        ->first();

    $view = View::create([
        'name' => 'Test',
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    //Check if the user has work order duplicate permission
    $userPermissions = $this->user->permissions()->toArray();

    if (in_array('WorkOrderManagement.WorkOrderView.duplicate', $userPermissions)) {

        //Create a test role for work order manage without duplicate permission
        $workOrderManager = Role::updateOrCreate(['name' => 'Work Order Manager', 'organization_id' => $this->organization->organization_id]);

        $workOrderManagePermissions = [
            'WorkOrderManagement.WorkOrderView.update',
            'WorkOrderManagement.WorkOrderView.delete',
        ];

        $workOrderManager->syncPermissions($workOrderManagePermissions);

        $this->user->syncRoles($workOrderManager);
    }

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::WORK_ORDERS(),
    ];

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

    //Clear cache after test execute
    $this->user->clearPermissionCache();
});

// Case: 10
test('Unauthorized user cannot access view duplicate API (without user list permission)', function () {

    $payload = config('listview.users_list_default_payload');

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::USERS())
        ->first();

    $view = View::create([
        'name' => 'Test',
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    //Check if the user has work order list permission
    $userPermissions = $this->user->permissions()->toArray();

    if (in_array('UserManagement.Users.list', $userPermissions)) {

        //Create a test role for work order manage without list permission
        $workOrderManager = Role::updateOrCreate(['name' => 'User Manager', 'organization_id' => $this->organization->organization_id]);

        $workOrderManagePermissions = [
            'UserManagement.Users.update',
            'UserManagement.Users.delete',
        ];

        $workOrderManager->syncPermissions($workOrderManagePermissions);

        $this->user->syncRoles($workOrderManager);
    }

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::WORK_ORDERS(),
    ];

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

    //Clear cache after test execute
    $this->user->clearPermissionCache();
});

// Case: 11
test('Unauthorized user cannot access view duplicate API (without calendar duplicate permission)', function () {

    $payload = config('listview.calendar_list_default_payload');

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::USERS())
        ->first();

    $view = View::create([
        'name' => 'Test',
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    //Check if the user has work order list permission
    $userPermissions = $this->user->permissions()->toArray();

    if (in_array('CalendarManagement.CalendarView.duplicate', $userPermissions)) {

        //Create a test role for work order manage without list permission
        $calendarManager = Role::updateOrCreate(['name' => 'Calendar Manager', 'organization_id' => $this->organization->organization_id]);

        $calendarManagePermissions = [
            'CalendarManagement.CalendarView.update',
            'CalendarManagement.CalendarView.delete',
        ];

        $calendarManager->syncPermissions($calendarManagePermissions);

        $this->user->syncRoles($calendarManager);
    }

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::WORK_ORDERS(),
    ];

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

    //Clear cache after test execute
    $this->user->clearPermissionCache();
});

// Case: 12
test('The request with an existing work order view name shows the validation error.', function () {

    $payload = config('listview.work_order_default_payload');
    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::WORK_ORDERS())
        ->first();

    /**
     * Creating new view
     */
    $view = View::create([
        'name' => substr(fake()->name(), 0, 15),
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);

    $postPayLoad = [
        'name' => $view->name,
        'view_type' => ViewTypes::WORK_ORDERS(),
    ];

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
        ->assertJsonStructure([
            'message',
            'errors',
        ]);
});

// Case: 13
test('The request with an invalid view uuid payload shows the validation error.', function () {

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::WORK_ORDERS(),
    ];

    $result = $this->postJson(route('views.duplicate', ['view' => fake()->uuid()]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_NOT_FOUND)
        ->assertJsonStructure([
            'message',
        ]);
});

// Case: 14
test('The work order view associated with one organization cannot be duplicated by another organization.', function () {

    $payload = config('listview.work_order_default_payload');
    /**
     * Creating new organization
     */
    $organization = Organization::create([
        'name' => substr(fake()->name(), 0, 15),
        'domain_type' => 'sub',
        'domain' => 'Test Domain',
    ]);

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::WORK_ORDERS())
        ->first();

    /**
     * Creating new view
     */
    $view = View::create([
        'name' => substr(fake()->name(), 0, 15),
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $organization->organization_id,
    ]);

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::WORK_ORDERS(),
    ];

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

// Case: 15
test('The user view associated with one organization cannot be duplicated by another organization.', function () {

    $payload = config('listview.users_list_default_payload');
    /**
     * Creating new organization
     */
    $organization = Organization::create([
        'name' => substr(fake()->name(), 0, 15),
        'domain_type' => 'sub',
        'domain' => 'Test Domain',
    ]);

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::USERS())
        ->first();

    /**
     * Creating new view
     */
    $view = View::create([
        'name' => substr(fake()->name(), 0, 15),
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $organization->organization_id,
    ]);

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::USERS(),
    ];

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

// Case: 16
test('The calendar view associated with one organization cannot be duplicated by another organization.', function () {

    $payload = config('listview.calendar_list_default_payload');
    /**
     * Creating new organization
     */
    $organization = Organization::create([
        'name' => substr(fake()->name(), 0, 15),
        'domain_type' => 'sub',
        'domain' => 'Test Domain',
    ]);

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::CALENDAR())
        ->first();

    /**
     * Creating new view
     */
    $view = View::create([
        'name' => substr(fake()->name(), 0, 15),
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $organization->organization_id,
    ]);

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::CALENDAR(),
    ];

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

// // Case: 17
test('The work order view associated with one user cannot be duplicated by another user.', function () {

    $payload = config('listview.work_order_default_payload');

    $timezone = Timezone::where('name', config('settings.default_timezone'))->first();
    /**
     * Creating new user
     */
    $user = User::create([
        'organization_id' => $this->organization->organization_id,
        'email' => fake()->email(),
        'first_name' => fake()->firstName(),
        'timezone_id' => $timezone->timezone_id,
    ]);

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::WORK_ORDERS())
        ->first();

    /**
     * Creating new view
     */
    $view = View::create([
        'name' => substr(fake()->name(), 0, 15),
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $user->user_id,
        'organization_id' => $this->organization->organization_id,
    ]);

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::WORK_ORDERS(),
    ];

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

// Case: 18
test('The user view associated with one user cannot be duplicated by another user.', function () {

    $payload = config('listview.users_list_default_payload');

    $timezone = Timezone::where('name', config('settings.default_timezone'))->first();
    /**
     * Creating new user
     */
    $user = User::create([
        'organization_id' => $this->organization->organization_id,
        'email' => fake()->email(),
        'first_name' => fake()->firstName(),
        'timezone_id' => $timezone->timezone_id,
    ]);

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::USERS())
        ->first();

    /**
     * Creating new view
     */
    $view = View::create([
        'name' => substr(fake()->name(), 0, 15),
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $user->user_id,
        'organization_id' => $this->organization->organization_id,
    ]);

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::USERS(),
    ];

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

// Case: 19
test('The calendar view associated with one user cannot be duplicated by another user.', function () {

    $payload = config('listview.calendar_list_default_payload');

    $timezone = Timezone::where('name', config('settings.default_timezone'))->first();
    /**
     * Creating new user
     */
    $user = User::create([
        'organization_id' => $this->organization->organization_id,
        'email' => fake()->email(),
        'first_name' => fake()->firstName(),
        'timezone_id' => $timezone->timezone_id,
    ]);

    $viewType = ViewType::select('view_type_id')
        ->where('slug', ViewTypes::CALENDAR())
        ->first();

    /**
     * Creating new view
     */
    $view = View::create([
        'name' => substr(fake()->name(), 0, 15),
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $user->user_id,
        'organization_id' => $this->organization->organization_id,
    ]);

    $postPayLoad = [
        'name' => substr(fake()->name(), 0, 15),
        'view_type' => ViewTypes::CALENDAR(),
    ];

    $result = $this->postJson(route('views.duplicate', ['view' => $view->view_uuid]), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

// Case: 20
test('The request with a predefined view name in another view can be recreated.', function () {

    $payload = config('listview.work_order_default_payload');
    $viewType = ViewType::select('view_type_id', 'slug')
        ->where('slug', ViewTypes::WORK_ORDERS())
        ->first();

    /**
     * Creating new view
     */
    $workOrderView = View::create([
        'name' => substr(fake()->name(), 0, 15),
        'scope' => 'individual',
        'payload' => $payload,
        'view_type_id' => $viewType->view_type_id,
        'user_id' => $this->user->user_id,
        'organization_id' => $this->user->organization_id,
    ]);
    $postPayLoad = [
        'name' => $workOrderView->name,
        'payload' => config('listview.calendar_list_default_payload'),
        'view_type' => ViewTypes::CALENDAR,
    ];

    $result = $this->postJson(route('views.store'), $postPayLoad, $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_CREATED)
        ->assertJsonStructure([
            'view' => [
                'label',
                'value',
            ],
            'is_default',
            'is_pinned',
            'is_editable',
            'is_deletable',
            'is_able_to_pin',
        ]);
});

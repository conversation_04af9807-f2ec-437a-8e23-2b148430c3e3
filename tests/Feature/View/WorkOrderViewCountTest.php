<?php

// this api for work order view count is currently not in use, re write this test if it using
// use App\Enums\Feature as FeatureEnum;
// use App\Enums\ViewTypes;
// use App\Models\Feature;
// use App\Models\Organization;
// use App\Models\Role;
// use App\Models\User;
// use App\Models\UserPinnedView;
// use App\Models\View;
// use App\Models\ViewType;
// use App\Models\WorkOrder;
// use App\States\WorkOrders\Completed;
// use Illuminate\Http\Response;

// beforeEach(function () {
//     // Setup organization has work order management feature for the test.
//     $this->organizationFeature = FeatureEnum::WORK_ORDER_MANAGEMENT();

//     $this->organization = Organization::join('organization_feature', 'organization_feature.organization_id', 'organizations.organization_id')
//         ->join('features', function ($joinQuery) {
//             $joinQuery->on('organization_feature.feature_id', 'features.feature_id')
//                 ->where('features.name', $this->organizationFeature);
//         })
//         ->select('organizations.organization_id', 'organizations.user_pool_id')
//         ->first();

//     $this->organization->makeCurrent();

//     // Set a random user from organization for authentication.
//     $this->user = User::where('users.organization_id', $this->organization->organization_id)
//         ->join('user_role', 'user_role.user_id', 'users.user_id')
//         ->join('roles', function ($joinQuery) {
//             $joinQuery->on('roles.role_id', 'user_role.role_id')
//                 ->whereIn('roles.name', ['Admin', 'Owner']);
//         })
//         ->where('users.user_type', 'account_user')
//         ->first();

//     //Create a token
//     $this->jtb = $this->getJwtTestBundle(
//         $this->user->cognito_user_id,
//         $this->organization->user_pool_id
//     );

//     $this->header = [
//         'Authorization' => "Bearer {$this->jtb->jwt}",
//     ];

// });

// // Case: 1
// test('The valid user can view the view count, and it contains proper keys.', function () {

//     $viewType = ViewType::select('view_type_id', 'slug')
//         ->where('slug', ViewTypes::WORK_ORDERS())
//         ->first();

//     $result = $this->getJson(route('views.count', ['view_type' => $viewType->slug]), $this->header);
//     //Assert the API response successfully processed.
//     expect($result)
//         ->assertStatus(Response::HTTP_OK)

//     //Assert the API response contains valid data.
//         ->assertJsonStructure([
//             '*' => [
//                 'view_uuid',
//                 'count',
//             ],
//         ]);

// });

// // Case: 2
// test('User with a invalid token cannot access view count API.', function () {

//     $this->header = [
//         'Authorization' => 'Bearer invalid.token',
//     ];
//     $viewType = ViewType::select('view_type_id', 'slug')
//         ->where('slug', ViewTypes::WORK_ORDERS())
//         ->first();

//     $result = $this->getJson(route('views.count', ['view_type' => $viewType->slug]), $this->header);

//     expect($result)
//         ->assertStatus(Response::HTTP_UNAUTHORIZED);
// });

// // Case: 3
// test('User without a token cannot access view count API.', function () {

//     $this->header = [
//         'Authorization' => 'Bearer ',
//     ];

//     $viewType = ViewType::select('view_type_id', 'slug')
//         ->where('slug', ViewTypes::WORK_ORDERS())
//         ->first();

//     $result = $this->getJson(route('views.count', ['view_type' => $viewType->slug]), $this->header);

//     expect($result)
//         ->assertStatus(Response::HTTP_UNAUTHORIZED);
// });

// // Case: 4
// test('Unauthorized organization cannot access work order view count API.', function () {

//     $viewType = ViewType::select('view_type_id', 'slug')
//         ->where('slug', ViewTypes::WORK_ORDERS())
//         ->first();

//     //Check if the organization has work order management feature
//     $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();

//     if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {

//         $features = [
//             FeatureEnum::TECHNICIAN_MANAGEMENT,
//             FeatureEnum::ROLE_MANAGEMENT,
//             FeatureEnum::USER_MANAGEMENT,
//             FeatureEnum::ORGANIZATION_SETTINGS,
//         ];

//         $this->organization->features()
//             ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
//     }

//     $result = $this->getJson(route('views.count', ['view_type' => $viewType->slug]), $this->header);
//     expect($result)
//         ->assertStatus(Response::HTTP_FORBIDDEN);
// });

// // Case: 5
// test('Unauthorized organization cannot access user view count API.', function () {

//     $viewType = ViewType::select('view_type_id', 'slug')
//         ->where('slug', ViewTypes::USERS())
//         ->first();

//     //Check if the organization has work order management feature
//     $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();

//     if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {

//         $features = [
//             FeatureEnum::TECHNICIAN_MANAGEMENT,
//             FeatureEnum::ROLE_MANAGEMENT,
//             FeatureEnum::WORK_ORDER_MANAGEMENT,
//             FeatureEnum::ORGANIZATION_SETTINGS,
//         ];

//         $this->organization->features()
//             ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
//     }

//     $result = $this->getJson(route('views.count', ['view_type' => $viewType->slug]), $this->header);
//     expect($result)
//         ->assertStatus(Response::HTTP_FORBIDDEN);
// });

// // Case: 6
// test('Unauthorized organization cannot access calendar view count API.', function () {

//     $viewType = ViewType::select('view_type_id', 'slug')
//         ->where('slug', ViewTypes::CALENDAR())
//         ->first();

//     //Check if the organization has work order management feature
//     $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();

//     if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {

//         $features = [
//             FeatureEnum::TECHNICIAN_MANAGEMENT,
//             FeatureEnum::ROLE_MANAGEMENT,
//             FeatureEnum::WORK_ORDER_MANAGEMENT,
//             FeatureEnum::ORGANIZATION_SETTINGS,
//         ];

//         $this->organization->features()
//             ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
//     }

//     $result = $this->getJson(route('views.count', ['view_type' => $viewType->slug]), $this->header);
//     expect($result)
//         ->assertStatus(Response::HTTP_FORBIDDEN);
// });

// // Case: 7
// test('Unauthorized user cannot access work order view count API.', function () {

//     $viewType = ViewType::select('view_type_id', 'slug')
//         ->where('slug', ViewTypes::WORK_ORDERS())
//         ->first();

//     //Check if the user has work order view store permission
//     $userPermissions = $this->user->permissions()->toArray();

//     if (in_array('WorkOrderManagement.WorkOrder.list', $userPermissions)) {

//         //Create a test role for work order manage without list permission
//         $workOrderManager = Role::updateOrCreate(['name' => 'Work Order Manager', 'organization_id' => $this->organization->organization_id]);

//         $workOrderManagePermissions = [
//             'WorkOrderManagement.WorkOrderView.update',
//             'WorkOrderManagement.WorkOrderView.delete',
//         ];

//         $workOrderManager->syncPermissions($workOrderManagePermissions);

//         $this->user->syncRoles($workOrderManager);
//     }

//     $result = $this->getJson(route('views.count', ['view_type' => $viewType->slug]), $this->header);
//     expect($result)
//         ->assertStatus(Response::HTTP_FORBIDDEN);

//     //Clear cache after test execute
//     $this->user->clearPermissionCache();
// });

// // Case: 8
// test('Unauthorized user cannot access user view count API.', function () {

//     $viewType = ViewType::select('view_type_id', 'slug')
//         ->where('slug', ViewTypes::USERS())
//         ->first();

//     //Check if the user has work order view store permission
//     $userPermissions = $this->user->permissions()->toArray();

//     if (in_array('UserManagement.Users.list', $userPermissions)) {

//         //Create a test role for work order manage without list permission
//         $workOrderManager = Role::updateOrCreate(['name' => 'Work Order Manager', 'organization_id' => $this->organization->organization_id]);

//         $workOrderManagePermissions = [
//             'UserManagement.Users.view',
//             'UserManagement.Users.create',
//         ];

//         $workOrderManager->syncPermissions($workOrderManagePermissions);

//         $this->user->syncRoles($workOrderManager);
//     }

//     $result = $this->getJson(route('views.count', ['view_type' => $viewType->slug]), $this->header);
//     expect($result)
//         ->assertStatus(Response::HTTP_FORBIDDEN);

//     //Clear cache after test execute
//     $this->user->clearPermissionCache();
// });

// // Case: 9
// test('Unauthorized user cannot access calendar view count API.', function () {

//     $viewType = ViewType::select('view_type_id', 'slug')
//         ->where('slug', ViewTypes::CALENDAR())
//         ->first();

//     //Check if the user has work order view permission
//     $userPermissions = $this->user->permissions()->toArray();

//     if (in_array('CalendarManagement.Calendar.view', $userPermissions)) {

//         //Create a test role for work order manage without view permission
//         $calendarManager = Role::updateOrCreate(['name' => 'Calendar Manager', 'organization_id' => $this->organization->organization_id]);

//         $calendarManagePermissions = [
//             'CalendarManagement.CalendarView.duplicate',
//             'CalendarManagement.CalendarView.create',
//         ];

//         $calendarManager->syncPermissions($calendarManagePermissions);

//         $this->user->syncRoles($calendarManager);
//     }

//     $result = $this->getJson(route('views.count', ['view_type' => $viewType->slug]), $this->header);
//     expect($result)
//         ->assertStatus(Response::HTTP_FORBIDDEN);

//     //Clear cache after test execute
//     $this->user->clearPermissionCache();
// });

// // Case: 10
// test('User with an invalid view type.', function () {

//     $viewType = substr(fake()->name(), 0, 15);

//     $result = $this->getJson(route('views.count', ['view_type' => $viewType]), $this->header);
//     expect($result)
//         ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY);
// });

// // Case: 11
// test('Count of a pinned work order view type is correct.', function () {

//     $count = WorkOrder::whereState('state', Completed::class)->where('organization_id', $this->user->organization_id)->count();
//     $count = $count >= 10 ? 10 : $count;

//     $workOrderViewPayload = [
//         [
//             'label' => 'Category',
//             'value' => 'category',
//             'selected' => true,
//             'isEditable' => false,
//             'sub_fields' => [
//                 [
//                     'label' => 'Property Address',
//                     'value' => 'property_address',
//                     'selected' => true,
//                     'isEditable' => true,
//                 ],
//             ],
//             'isStaticColumn' => true,
//         ],
//         [
//             'label' => 'Status',
//             'value' => 'status',
//             'selected' => true,
//             'isEditable' => false,
//             'isStaticColumn' => true,
//         ],
//         [
//             'label' => 'Technician',
//             'value' => 'technician',
//             'selected' => true,
//             'isEditable' => true,
//             'isStaticColumn' => false,
//         ],
//         [
//             'label' => 'Priority',
//             'value' => 'priority',
//             'selected' => true,
//             'isEditable' => true,
//             'isStaticColumn' => false,
//         ],
//         [
//             'label' => 'Due Date',
//             'value' => 'due_date',
//             'selected' => true,
//             'isEditable' => true,
//             'isStaticColumn' => false,
//         ],
//         [
//             'label' => 'Scheduled Date',
//             'value' => 'scheduled_date',
//             'selected' => true,
//             'isEditable' => true,
//             'isStaticColumn' => false,
//         ],
//         [
//             'label' => 'Created Date',
//             'value' => 'created_date',
//             'selected' => true,
//             'isEditable' => true,
//             'isStaticColumn' => false,
//         ],
//         'filters' => [
//             'fields' => [
//                 [
//                     'ops' => [
//                         'is',
//                         'is_not',
//                     ],
//                     'label' => 'Status',
//                     'value' => 'status',
//                     'values_type' => [
//                         'is' => 'multi-select',
//                         'is_not' => 'multi-select',
//                     ],
//                 ],
//                 [
//                     'ops' => [
//                         'is',
//                         'is_not',
//                     ],
//                     'label' => 'Priority',
//                     'value' => 'priority',
//                     'values_type' => [
//                         'is' => 'multi-select',
//                         'is_not' => 'multi-select',
//                     ],
//                 ],
//                 [
//                     'ops' => [
//                         'is',
//                     ],
//                     'label' => 'Technician',
//                     'value' => 'technician',
//                     'values_type' => [
//                         'is' => 'multi-select',
//                     ],
//                 ],
//                 [
//                     'ops' => [
//                         'is',
//                     ],
//                     'label' => 'Category',
//                     'value' => 'category',
//                     'values_type' => [
//                         'is' => 'multi-select',
//                     ],
//                 ],
//                 [
//                     'ops' => [
//                         'is',
//                         'is_not',
//                     ],
//                     'label' => 'Due Date',
//                     'value' => 'due_date',
//                     'values_type' => [
//                         'is' => 'select',
//                         'is_not' => 'select',
//                         'is_after' => 'select',
//                         'is_before' => 'select',
//                         'is_between' => 'date-range-picker',
//                     ],
//                 ],
//                 [
//                     'ops' => [
//                         'is',
//                         'is_not',
//                     ],
//                     'label' => 'Created Date',
//                     'value' => 'created_date',
//                     'values_type' => [
//                         'is' => 'select',
//                         'is_not' => 'select',
//                         'is_after' => 'select',
//                         'is_before' => 'select',
//                         'is_between' => 'date-range-picker',
//                     ],
//                 ],
//             ],
//             'applied' => [
//                 'fl_group' => [
//                     [
//                         'field' => [
//                             'label' => 'Status',
//                             'value' => 'status',
//                         ],
//                         'value' => [
//                             [
//                                 'label' => 'Completed',
//                                 'value' => 'completed',
//                             ],
//                         ],
//                         'operation' => [
//                             'label' => 'is',
//                             'value' => 'is',
//                         ],
//                     ],
//                 ],
//                 'group_op' => null,
//             ],
//             'operators' => [
//                 'is' => [
//                     'label' => 'is',
//                     'value' => 'is',
//                 ],
//                 'is_not' => [
//                     'label' => 'Is not',
//                     'value' => 'is_not',
//                 ],
//                 'is_after' => [
//                     'label' => 'Is after',
//                     'value' => 'is_after',
//                 ],
//                 'is_before' => [
//                     'label' => 'Is before',
//                     'value' => 'is_before',
//                 ],
//                 'is_between' => [
//                     'label' => 'Is between',
//                     'value' => 'is_between',
//                 ],
//             ],

//             'grouping' => [
//                 'g_by' => [
//                     'label' => 'None',
//                     'value' => 'none',
//                 ],
//                 'default' => 'none',
//                 'g_options' => [
//                     [
//                         'label' => 'None',
//                         'value' => 'none',
//                     ],
//                     [
//                         'label' => 'Status',
//                         'value' => 'status',
//                     ],
//                     [
//                         'label' => 'Priority',
//                         'value' => 'priority',
//                     ],
//                     [
//                         'label' => 'Category',
//                         'value' => 'category',
//                     ],
//                     [
//                         'label' => 'Technician',
//                         'value' => 'technician',
//                     ],
//                 ],
//             ],
//         ],
//     ];

//     $workOrderViewType = ViewType::select('view_type_id', 'slug')
//         ->where('slug', ViewTypes::WORK_ORDERS())
//         ->first();

//     $workOrderView = View::create([
//         'name' => fake()->name(),
//         'scope' => 'individual',
//         'payload' => $workOrderViewPayload,
//         'view_type_id' => $workOrderViewType->view_type_id,
//         'user_id' => $this->user->user_id,
//         'organization_id' => $this->user->organization_id,
//     ]);

//     UserPinnedView::updateOrCreate([
//         'user_id' => $this->user->user_id,
//         'view_id' => $workOrderView->view_id,
//     ]);

//     $result = $this->getJson(route('views.count', ['view_type' => ViewTypes::WORK_ORDERS()]), $this->header);
//     expect($result)
//         ->assertStatus(Response::HTTP_OK);

//     $responseData = json_decode($result->getContent());
//     expect(collect($responseData)->where('view_uuid', $workOrderView->view_uuid)->first()->count)->toEqual($count);

// });

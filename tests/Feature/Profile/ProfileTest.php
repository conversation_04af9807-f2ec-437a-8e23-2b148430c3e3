<?php

use App\Models\Organization;
use App\Models\User;
use Illuminate\Http\Response;

beforeEach(function () {
    // Prepare something before each test run

    // Setup organization for the test
    $this->organization = Organization::factory()->create();
    $this->organization->makeCurrent();

    // Set a random user from organization for authentication
    $this->user = User::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'user_type' => 'account_user',
    ]);
});

test('Authenticated user can access profile data', function () {
    $jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $result = $this->get(route('profile'), [
        'Authorization' => "Bearer {$jtb->jwt}",
    ]);

    expect($result)
        ->assertJsonStructure([
            'user_id',
            'name',
            'email',
            'roles',
            'permissions',
            'organization' => [
                'organization_id',
                'name',
            ],
            'timezone',
        ])
        ->assertStatus(Response::HTTP_OK);
});

test('User with invalid token cannot access profile data', function () {
    $result = $this->get(route('profile'), [
        'Authorization' => 'Bearer INVALID TOKEN',
    ]);

    expect($result)
        ->assertJsonStructure([
            'message',
        ])
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

test('User without token cannot access profile data', function () {
    $result = $this->get(route('profile'));

    expect($result)
        ->assertJsonStructure([
            'message',
        ])
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

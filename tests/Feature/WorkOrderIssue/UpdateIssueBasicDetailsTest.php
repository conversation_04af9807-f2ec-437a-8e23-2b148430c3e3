<?php

declare(strict_types=1);

use App\Actions\WorkOrderIssue\UpdateIssueBasicDetails;
use App\Enums\Feature as EnumsFeature;
use App\Events\Issue\ServiceRequestIssueUpdated;
use App\Events\ServiceRequest\WorkOrder\ServiceRequestWorkOrderIssueUpdated;
use App\Events\ServiceRequest\WorkOrder\SyncServiceRequestWorkOrder;
use App\Events\WorkOrder\Issue\WorkOrderIssueUpdated;
use App\Http\Requests\Issue\CreateIssueRequest;
use App\Models\Country;
use App\Models\Feature;
use App\Models\Issue;
use App\Models\IssueStatus;
use App\Models\Organization;
use App\Models\ProblemDiagnosis;
use App\Models\Property;
use App\Models\ServiceRequest;
use App\Models\ServiceRequestStatus;
use App\Models\ServiceRequestType;
use App\Models\State;
use App\Models\Timezone;
use App\Models\User;
use App\Models\WorkOrder;
use App\Models\WorkOrderIssue;
use App\Models\WorkOrderIssueStatus;
use App\States\Issue\Assigned;
use App\States\Issue\Unassigned;
use App\States\ServiceRequests\Scoping;
use App\States\WorkOrderIssue\Assigned as WorkOrderIssueAssigned;
use App\States\WorkOrderIssue\Canceled as WorkOrderIssueCanceled;
use App\States\WorkOrders\ReadyToSchedule;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Event;
use Lorisleiva\Actions\ActionRequest;

beforeEach(function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    $this->organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->create();

    $this->organization->makeCurrent();

    $this->property = Property::factory()
        ->for($this->organization)
        ->create();

    // create a dummy service request
    $this->serviceRequest = ServiceRequest::factory()
        ->for($this->organization)
        ->for($this->property)
        ->for($timezone)
        ->for(ServiceRequestStatus::factory(), 'status')
        ->for(ServiceRequestType::factory(), 'type')
        ->create([
            'state' => Scoping::class,
        ]);

    if (! IssueStatus::where('slug', Assigned::$name)->count()) {
        IssueStatus::factory()->create([
            'label' => Assigned::$name,
            'slug' => Assigned::$name,
        ]);
    }

    if (! IssueStatus::where('slug', Unassigned::$name)->count()) {
        IssueStatus::factory()->create([
            'label' => Unassigned::$name,
            'slug' => Unassigned::$name,
        ]);
    }

    if (! WorkOrderIssueStatus::where('slug', WorkOrderIssueCanceled::$name)->count()) {
        WorkOrderIssueStatus::factory()->create([
            'label' => WorkOrderIssueCanceled::$name,
            'slug' => WorkOrderIssueCanceled::$name,
        ]);
    }

    $this->problemDiagnosis = ProblemDiagnosis::factory()->create();

    $this->issue = Issue::factory()
        ->for($this->organization)
        ->for($this->problemDiagnosis)
        ->for($this->serviceRequest)
        ->create([
            'state' => Assigned::class,
        ]);

    $this->workOrder = WorkOrder::factory()
        ->for($this->organization)
        ->for($this->serviceRequest)
        ->for($this->property)
        ->create([
            'state' => ReadyToSchedule::class,
        ]);

    $this->workOrderIssue = WorkOrderIssue::factory()->create([
        'work_order_id' => $this->workOrder->work_order_id,
        'issue_id' => $this->issue->issue_id,
        'organization_id' => $this->organization->organization_id,
        'state' => WorkOrderIssueAssigned::class,
    ]);

});

test('User with a valid token can access update issue api.', function () {
    $payload = [
        'title' => 'New Issue Title',
        'problem_diagnosis_id' => $this->problemDiagnosis->problem_diagnosis_uuid,
        'description' => 'New Issue Description',
    ];
    $feature = Feature::where('name', EnumsFeature::WORK_ORDER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => EnumsFeature::WORK_ORDER_MANAGEMENT(),
        ])->create();
    }

    $this->organization->features()->save($feature);
    $user = $this->createUserWithPermission($feature, $this->organization, 'WorkOrderManagement.WorkOrder.create');

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $this->header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $result = $this->putJson(route('workOrder.issue.action.updateBasicDetails', [
        'workOrder' => $this->workOrder->work_order_uuid,
        'workOrderIssue' => $this->workOrderIssue->work_order_issue_uuid,
    ]), $payload, $this->header);

    // Assertion
    expect($result)
        ->assertStatus(Response::HTTP_OK);
});

test('Update issue api return proper key.', function () {
    // create the feature
    $feature = Feature::where('name', EnumsFeature::WORK_ORDER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => EnumsFeature::WORK_ORDER_MANAGEMENT(),
        ])->create();
    }

    $this->organization->features()->save($feature);
    $user = $this->createUserWithPermission($feature, $this->organization, 'WorkOrderManagement.WorkOrder.create');

    // Create a JWT token for the user
    $jtb = $this->getJwtTestBundle(
        $user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $this->header = [
        'Authorization' => "Bearer {$jtb->jwt}",
    ];

    $payload = [
        'title' => 'New Issue Title',
        'problem_diagnosis_id' => $this->problemDiagnosis->problem_diagnosis_uuid,
        'description' => 'New Issue Description',
    ];

    $result = $this->putJson(route('workOrder.issue.action.updateBasicDetails', [
        'workOrder' => $this->workOrder->work_order_uuid,
        'workOrderIssue' => $this->workOrderIssue->work_order_issue_uuid,
    ]), $payload, $this->header);

    // Assertion
    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonStructure([
            'issue_id',
            'title',
            'status',
            'abilities',
            'work_orders' => [
                '*' => [
                    'work_order_id',
                    'work_order_number',
                    'issues_count',
                ],
            ],
            'description',
            'problem' => [
                'category' => [
                    'category_id',
                    'label',
                ],
                'sub_category' => [
                    'sub_category_id',
                    'label',
                ],
                'diagnosis' => [
                    'diagnosis_id',
                    'label',
                ],
            ],
        ]);
});

test('User with an invalid token cannot access update issue api.', function ($token) {
    $header = [
        'Authorization' => $token,
    ];

    $payload = [
        'title' => 'New Issue Title',
        'problem_diagnosis_id' => $this->problemDiagnosis->problem_diagnosis_uuid,
        'description' => 'New Issue Description',
    ];

    $result = $this->postJson(route('workOrder.issue.action.unassign', [
        'workOrder' => $this->workOrder->work_order_uuid,
        'workOrderIssue' => $this->workOrderIssue->work_order_issue_uuid,
    ]), $payload, $header);

    // Assertion
    expect($result)->assertStatus(Response::HTTP_UNAUTHORIZED);
})->with([
    'Bearer invalid-token',
    '',
]);

test('Handles UserNotFoundException correctly in asController method.', function () {
    $request = Mockery::mock(CreateIssueRequest::class);
    $request->shouldReceive('user')->andReturn(null);

    $updateAction = new UpdateIssueBasicDetails;

    /** @var Request $request */
    $response = $updateAction->asController($request, $this->workOrder, $this->workOrderIssue);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_UNPROCESSABLE_ENTITY);
});

test('Handles notFound exception correctly in asController method.', function () {
    $request = Mockery::mock(CreateIssueRequest::class);
    $request->shouldReceive('user')->andReturn(User::factory()->create());

    $updateAction = new UpdateIssueBasicDetails;

    $this->issue->delete();

    /** @var Request $request */
    $response = $updateAction->asController($request, $this->workOrder, $this->workOrderIssue);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_UNPROCESSABLE_ENTITY);
});

test('Handles exceptions properly during asController method execution.', function () {
    $request = Mockery::mock(CreateIssueRequest::class);
    $request->shouldReceive('user')->andReturn(User::factory()->create());

    $updateAction = Mockery::mock(UpdateIssueBasicDetails::class)->makePartial();
    $updateAction->shouldReceive('handle')->andThrow(new Exception('Unexpected error'));

    /** @var UpdateIssueBasicDetails $updateAction */
    $response = $updateAction->asController($request, $this->workOrder, $this->workOrderIssue);

    expect($response)->toBeInstanceOf(JsonResponse::class);
    expect($response->status())->toBe(Response::HTTP_INTERNAL_SERVER_ERROR);
});

test('authorize method returns true when user has permission to assign issue', function () {
    // create the feature
    $feature = Feature::where('name', EnumsFeature::WORK_ORDER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => EnumsFeature::WORK_ORDER_MANAGEMENT(),
        ])->create();
    }

    $this->organization->features()->save($feature);
    $user = $this->createUserWithPermission($feature, $this->organization, 'WorkOrderManagement.WorkOrder.create');

    $request = Mockery::mock(ActionRequest::class);
    $request->shouldReceive('user')->andReturn($user);
    $updateAction = new UpdateIssueBasicDetails;

    /** @var ActionRequest $request */
    $result = $updateAction->authorize($request);

    expect($result)->toBeTrue();
});

test('authorize method returns false when user is not authenticated.', function () {
    $request = Mockery::mock(ActionRequest::class);
    $request->shouldReceive('user')->andReturn(null);

    $updateAction = new UpdateIssueBasicDetails;

    /** @var ActionRequest $request */
    $result = $updateAction->authorize($request);

    expect($result)->toBeFalse();
});

test('authorize method returns false when user does not have permission to create the work order.', function () {
    // create the feature
    $feature = Feature::where('name', EnumsFeature::WORK_ORDER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => EnumsFeature::WORK_ORDER_MANAGEMENT(),
        ])->create();
    }

    $this->organization->features()->save($feature);
    $user = $this->createUserWithPermission($feature, $this->organization, 'WorkOrderManagement.WorkOrder.delete');

    $request = Mockery::mock(ActionRequest::class);
    $request->shouldReceive('user')->andReturn($user);
    $updateAction = new UpdateIssueBasicDetails;

    /** @var ActionRequest $request */
    $result = $updateAction->authorize($request);

    expect($result)->toBeFalse();
});

test('handle method assign the issue successfully and trigger event', function () {
    $user = User::factory()->create();

    Event::fake([
        ServiceRequestIssueUpdated::class,
        WorkOrderIssueUpdated::class,
        ServiceRequestWorkOrderIssueUpdated::class,
        SyncServiceRequestWorkOrder::class,
    ]);

    $payload = [
        'title' => 'New Issue Title',
        'problem_diagnosis_id' => $this->problemDiagnosis->problem_diagnosis_uuid,
        'description' => 'New Issue Description',
    ];

    (new UpdateIssueBasicDetails)->handle($payload, $this->issue, $user);

    Event::assertDispatched(ServiceRequestIssueUpdated::class);
    Event::assertDispatched(WorkOrderIssueUpdated::class);
    Event::assertDispatched(ServiceRequestWorkOrderIssueUpdated::class);
    Event::assertDispatched(SyncServiceRequestWorkOrder::class);

    $this->issue->refresh();

    expect($this->issue->title)->toBe($payload['title']);
    expect($this->issue->description)->toBe($payload['description']);
});

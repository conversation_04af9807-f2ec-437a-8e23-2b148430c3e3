<?php

use App\Models\Organization;
use Illuminate\Http\Response;

test('can get config with valid domain', function () {
    $organization = Organization::factory()->create();

    $result = $this->get(route('config', [
        'client_key' => $organization->domain,
    ]));

    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonStructure([
            'region',
            'user_pool_id',
            'client_id',
            'template',
            'auth_domain',
        ]);
});

test('doesnt get config without valid domain', function () {
    $result = $this->get(route('config', [
        'client_key' => 'example.org',
    ]));

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
        ->assertJsonStructure([
            'message',
        ]);
});

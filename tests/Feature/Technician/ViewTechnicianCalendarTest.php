<?php

use App\Enums\Feature as FeatureEnum;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Role;
use App\Models\Technician;
use App\Models\User;
use Faker\Factory;
use Illuminate\Http\Response;

beforeEach(function () {
    // Setup organization has user management feature for the test.
    $this->organizationFeature = FeatureEnum::USER_MANAGEMENT();

    $this->organization = Organization::join('organization_feature', 'organization_feature.organization_id', 'organizations.organization_id')
        ->join('features', function ($joinQuery) {
            $joinQuery->on('organization_feature.feature_id', 'features.feature_id')
                ->where('features.name', $this->organizationFeature);
        })
        ->select('organizations.organization_id', 'organizations.user_pool_id')
        ->first();

    $this->organization->makeCurrent();

    // Set a random user from organization for authentication.
    $this->user = User::where('users.organization_id', $this->organization->organization_id)
        ->join('user_role', 'user_role.user_id', 'users.user_id')
        ->join('roles', function ($joinQuery) {
            $joinQuery->on('roles.role_id', 'user_role.role_id')
                ->whereIn('roles.name', ['Admin', 'Owner']);
        })
        ->where('users.user_type', 'account_user')
        ->first();

    //Create a token
    $this->jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $this->header = [
        'Authorization' => "Bearer {$this->jtb->jwt}",
    ];

    $this->technician = Technician::where('organization_id', $this->organization->organization_id)
        ->first();
});

// Case: 1
test('A user with an invalid token cannot view technician calendar.', function () {

    $this->header = [
        'Authorization' => 'Bearer invalid-token',
    ];

    $result = $this->getJson(route('technicians.view.calendar', ['technician' => $this->technician->technician_uuid]), $this->header);
    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 2
test('A user without a token cannot view technician calendar.', function () {

    $this->header = [
        'Authorization' => '',
    ];

    $result = $this->getJson(route('technicians.view.calendar', ['technician' => $this->technician->technician_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 3
test('Unauthorized organization cannot view technician calendar.', function () {

    //Check if the organization has user management feature
    $organizationCurrentFeatures = $this->organization->features()->pluck('name')->toArray();

    if (in_array($this->organizationFeature, $organizationCurrentFeatures)) {

        $features = [
            FeatureEnum::WORK_ORDER_MANAGEMENT,
            FeatureEnum::ROLE_MANAGEMENT,
            FeatureEnum::BILLING,
            FeatureEnum::ORGANIZATION_SETTINGS,
        ];

        $this->organization->features()
            ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());
    }

    $result = $this->getJson(route('technicians.view.calendar', ['technician' => $this->technician->technician_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

});

// Case: 4
test('Unauthorized user cannot view technician calendar.', function () {

    //Check if the user has user management update permission
    $userPermissions = $this->user->permissions()->toArray();

    if (in_array('UserManagement.TechnicianBlockOut.view', $userPermissions)) {

        //Create a test role for user management without user management create permission
        $userManager = Role::updateOrCreate(['name' => 'User Manager', 'organization_id' => $this->organization->organization_id]);

        $userManagePermissions = [
            'UserManagement.TechnicianBlockOut.update',
        ];

        $userManager->syncPermissions($userManagePermissions);

        $this->user->syncRoles($userManager);
    }

    $result = $this->getJson(route('technicians.view.calendar', ['technician' => $this->technician->technician_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

    //Clear cache after test execute
    $this->user->clearPermissionCache();
});

// Case: 5
test('The request without form data shows the validation error.', function () {

    $result = $this->getJson(route('technicians.view.calendar', ['technician' => $this->technician->technician_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNPROCESSABLE_ENTITY)
        ->assertJsonStructure([
            'message',
            'errors',
        ]);
});

// Case: 6
test('The valid user can view technician calendar, and it will return the proper keys.', function () {

    $faker = Factory::create();
    $randomDate = $faker->dateTimeThisDecade->format('Y-m-d');

    $postPayLoad = [
        'is_block_out_all_day' => true,
        'note' => Str::random(10),
        'date' => $randomDate,
    ];

    $technicianAppointment = $this->postJson(route('technicians.block-out-appointments.store', [
        'technician' => $this->technician->technician_uuid,
    ]), $postPayLoad, $this->header);

    if (! empty($technicianAppointment)) {
        $result = $this->getJson(
            route('technicians.view.calendar', [
                'technician' => $this->technician->technician_uuid,
                'date' => $randomDate,
            ]),
            $this->header
        );

        expect($result)
            ->assertStatus(Response::HTTP_OK)
            ->assertJsonStructure([
                '*' => [
                    'appointment_date',
                    'is_block_out_all_day',
                    'block_out_appointment_exist',
                    'regular_appointment_exist',
                ],
            ]);
    }
});

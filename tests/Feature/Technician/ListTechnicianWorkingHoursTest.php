<?php

use App\Enums\DomainType;
use App\Enums\Feature as FeatureEnum;
use App\Enums\UserTypes;
use App\Models\Country;
use App\Models\Feature;
use App\Models\Organization;
use App\Models\Role;
use App\Models\State;
use App\Models\Technician;
use App\Models\TechnicianWorkingHour;
use App\Models\Timezone;
use App\Models\User;
use App\Services\Scheduling\Domain\Enums\CarbonDayOfWeek;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Http\Response;

beforeEach(function () {
    $timezone = Timezone::factory()->create();
    $state = State::factory()->create();
    $country = Country::factory()->create();

    // create the feature
    $feature = Feature::where('name', FeatureEnum::USER_MANAGEMENT())->first();

    if (empty($feature)) {
        $feature = Feature::factory()->state([
            'name' => FeatureEnum::USER_MANAGEMENT(),
        ])->create();
    }

    $this->organization = Organization::factory()
        ->for($state)
        ->for($country)
        ->hasAttached($feature)
        ->create();

    $this->organization->makeCurrent();

    $this->user = $this->createUserWithPermission($feature, $this->organization, 'UserManagement.WorkingHour.import');

    //Create a token
    $this->jtb = $this->getJwtTestBundle(
        $this->user->cognito_user_id,
        $this->organization->user_pool_id
    );

    $this->header = [
        'Authorization' => "Bearer {$this->jtb->jwt}",
    ];

    $this->techUser = User::factory()
        ->for($timezone)
        ->for($this->organization)
        ->create([
            'user_type' => UserTypes::TECHNICIAN(),
        ]);

    $this->technician = Technician::factory()->create([
        'organization_id' => $this->organization->organization_id,
        'user_id' => $this->techUser->user_id,
    ]);

    $workHours = collect(CarbonDayOfWeek::cases())
        ->map(function (CarbonDayOfWeek $day) {
            return ['weekday' => CarbonDayOfWeek::toString($day)];
        })
        ->toArray();
    $workingHours = TechnicianWorkingHour::factory()
        ->count(7)
        ->state(new Sequence(...$workHours))
        ->create([
            'technician_id' => $this->technician->technician_id,
            'organization_id' => $this->technician->organization_id,
        ]);
});

// Case: 1
test('The valid user can import the working hours, and it will return the proper keys.', function () {

    $result = $this->getJson(route('technicians.working_hours', ['technician' => $this->technician->technician_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_OK)
        ->assertJsonStructure([
            'working_hours' => [
                'monday' => [
                    'from',
                    'to',
                    'isEnabled',
                ],
                'tuesday' => [
                    'from',
                    'to',
                    'isEnabled',
                ],
                'wednesday' => [
                    'from',
                    'to',
                    'isEnabled',
                ],
                'thursday' => [
                    'from',
                    'to',
                    'isEnabled',
                ],
                'friday' => [
                    'from',
                    'to',
                    'isEnabled',
                ],
                'lunch_time_break' => [
                    'from',
                    'to',
                    'duration',
                    'isEnabled',
                ],
                'saturday' => [
                    'from',
                    'to',
                    'isEnabled',
                ],
                'sunday' => [
                    'from',
                    'to',
                    'isEnabled',
                ],
            ],
        ]);
});

// Case: 2
test('A user with an invalid token cannot access the working hours import API.', function () {

    $this->header = [
        'Authorization' => 'Bearer invalid-token',
    ];

    $result = $this->getJson(route('technicians.working_hours', ['technician' => $this->technician->technician_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 3
test('A user without a token cannot access the working hours update API.', function () {

    $this->header = [
        'Authorization' => '',
    ];

    $result = $this->getJson(route('technicians.working_hours', ['technician' => $this->technician->technician_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_UNAUTHORIZED);
});

// Case: 4
test('Unauthorized organization cannot access working hours import API.', function () {
    $features = [
        FeatureEnum::WORK_ORDER_MANAGEMENT,
        FeatureEnum::ROLE_MANAGEMENT,
        FeatureEnum::BILLING,
        FeatureEnum::ORGANIZATION_SETTINGS,
    ];

    $this->organization->features()
        ->sync(Feature::whereIn('name', $features)->pluck('feature_id')->toArray());

    $result = $this->getJson(route('technicians.working_hours', ['technician' => $this->technician->technician_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

});

// Case: 5
test('Unauthorized user cannot access working hours import API.', function () {

    //Check if the user has working hours update permission
    $userPermissions = $this->user->permissions()->toArray();

    if (in_array('UserManagement.WorkingHour.import', $userPermissions)) {

        //Create a test role for working hours manage without update permission
        $userManager = Role::updateOrCreate(['name' => 'User Manager', 'organization_id' => $this->organization->organization_id]);

        $userManagePermissions = [
            'UserManagement.WorkingHour.create',
            'UserManagement.WorkingHour.list',
        ];

        $userManager->syncPermissions($userManagePermissions);

        $this->user->syncRoles($userManager);
    }

    $result = $this->getJson(route('technicians.working_hours', ['technician' => $this->technician->technician_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);

    //Clear cache after test execute
    $this->user->clearPermissionCache();
});

// Case: 6
test("Non technician users can't get the working hours data", function () {

    //creating new user with account_user user type(except technician)
    $timezone = Timezone::where('name', config('settings.default_timezone'))->first();
    $importFromUser = User::create(
        [
            'organization_id' => $this->organization->organization_id,
            'email' => fake()->email(),
            'first_name' => fake()->firstName(),
            'user_type' => 'account_user',
            'timezone_id' => $timezone->timezone_id,
        ]);

    $result = $this->getJson(route('technicians.working_hours', ['technician' => $importFromUser->user_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_NOT_FOUND);
});

// Case: 6
test("The user associated with one organization can't get the working hours data for another organization.", function () {

    $organization = Organization::select('organization_id')
        ->where('organization_id', '<>', $this->organization->organization_id)
        ->first();

    if (empty($organization)) {
        // Create a new organization
        $organization = Organization::create([
            'name' => 'Test Org',
            'domain_type' => DomainType::SUB_DOMAIN,
            'domain' => 'test.domain',
            'user_pool_id' => 'test_pool_id',
            'user_pool_app_client_id' => 'user_pool_app_client_id',
            'user_pool_api_client_id' => 'user_pool_api_client_id',
            'user_pool_api_client_secret' => 'pj3te5niqrusn27qlgni92tmn7bn8iekg7o7gvuvv4uiohc9ihv',
            'template' => 'default',
        ]);
    }
    $timezone = Timezone::where('name', config('settings.default_timezone'))->first();

    $user = User::create([
        'organization_id' => $organization->organization_id,
        'email' => fake()->email(),
        'first_name' => fake()->firstName(),
        'user_type' => 'account_user',
        'timezone_id' => $timezone->timezone_id,
    ]);

    $technician = Technician::create([
        'organization_id' => $organization->organization_id,
        'user_id' => $user->user_id,
    ]);

    $result = $this->getJson(route('technicians.working_hours', ['technician' => $technician->technician_uuid]), $this->header);

    expect($result)
        ->assertStatus(Response::HTTP_FORBIDDEN);
});

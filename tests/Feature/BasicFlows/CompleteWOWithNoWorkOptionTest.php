<?php

//------------------------------------------- To-Do: This basic flow has changed, and we can modify it later.

// use App\Enums\Priority;
// use App\Enums\PropertyAccessMethods;
// use App\Enums\Trip;
// use App\Enums\WorkToPerformTypes;
// use App\Events\WorkOrder\NewWorkOrderCreated;
// use App\Events\WorkOrder\WorkOrderCreated;
// use App\Models\Country;
// use App\Models\Issue;
// use App\Models\ProblemCategory;
// use App\Models\ProblemDiagnosis;
// use App\Models\ProblemSubCategory;
// use App\Models\ServiceRequest;
// use App\Models\ServiceRequestStatus;
// use App\Models\ServiceRequestType;
// use App\Models\State;
// use App\Models\Technician;
// use App\Models\TechnicianSkill;
// use App\Models\TechnicianWorkingHour;
// use App\Models\User;
// use App\Models\WorkOrder;
// use App\Models\WorkOrderIssue;
// use App\Services\Scheduling\Domain\Enums\CarbonDayOfWeek;
// use App\Services\Scheduling\Domain\Enums\SchedulingMethod;
// use App\Services\Scheduling\Domain\Enums\SchedulingMode;
// use App\States\ServiceCalls\Ended;
// use App\States\ServiceCalls\EnRoute;
// use App\States\ServiceCalls\Paused as ServiceCallPaused;
// use App\States\ServiceCalls\Working;
// use App\States\WorkOrders\Canceled;
// use App\States\WorkOrders\Paused;
// use App\States\WorkOrders\Scheduled;
// use Illuminate\Database\Eloquent\Factories\Sequence;
// use Illuminate\Http\Response;

// beforeEach(function () {
//     $this->token = $this->getJwtToken();
// });

// test('Create a new work order and progress it through all the necessary state until it is No work with trip_end_with_type as "I need to submit a quote myself").', function () {
//     Event::fake([
//         WorkOrderCreated::class,
//         NewWorkOrderCreated::class,
//     ]);

//     $header = [
//         'Authorization' => "Bearer {$this->token}",
//     ];

//     $access_info = [
//         'method' => PropertyAccessMethods::DIGITAL_KEY_CODE(),
//         'code' => 123,
//         'note' => '123 is the access code',
//     ];

//     $problemDiagnosis = ProblemDiagnosis::join('problem_sub_categories', 'problem_diagnoses.problem_sub_category_id', '=', 'problem_sub_categories.problem_sub_category_id')
//         ->join('problem_categories', 'problem_sub_categories.problem_category_id', '=', 'problem_categories.problem_category_id')
//         ->select('problem_diagnoses.slug as problem_diagnosis_slug', 'problem_sub_categories.slug as problem_sub_category_slug', 'problem_categories.slug as problem_category_slug')
//         ->inRandomOrder()
//         ->first();

//     $payLoad = [
//         'description' => 'Test description',
//         'priority' => Priority::HIGH(),
//         'property' => [
//             'property_name' => 'Property name',
//             'full_address' => '30 Capitol Sq SW, Atlanta, GA 30301',
//             'street_address' => '30 Capitol Sq SW',
//             'unit_number' => 'A125',
//             'bed_unit' => 2,
//             'city' => 'Atlanta',
//             'postal_zip_code' => '30301',
//             'state_id' => State::select('state_code')->inRandomOrder()->first()->state_code,
//             'country_id' => Country::select('alpha2_code')->inRandomOrder()->first()->alpha2_code,
//             'latitude' => 39.********,
//             'longitude' => -94.********,
//             'access_info' => $access_info,
//         ],
//         'residents' => [
//             [
//                 'first_name' => 'First',
//                 'last_name' => 'resident',
//                 'email' => '<EMAIL>',
//                 'phone_number' => '1234567890',
//                 'is_primary_resident' => true,
//             ],
//             [
//                 'first_name' => 'Second',
//                 'last_name' => 'resident',
//                 'email' => '<EMAIL>',
//                 'phone_number' => '1234567890',
//                 'is_primary_resident' => false,
//             ],
//         ],
//         'problems' => [
//             [
//                 'diagnosis' => $problemDiagnosis->problem_diagnosis_slug,
//                 'sub_category' => $problemDiagnosis->problem_sub_category_slug,
//                 'category' => $problemDiagnosis->problem_category_slug,
//             ],
//         ],
//     ];

//     /**---------------------------------- Create Work Order ------------------------------------------------- */

//     $result1 = $this->makeApiRequest('postJson', route('workOrder.store'), $payLoad, $header);

//     expect($result1)
//         ->assertStatus(Response::HTTP_CREATED)
//         ->assertJsonStructure([
//             'work_order_id',
//             'work_order_number',
//             'created_at',
//         ]);
//     Event::assertDispatched(WorkOrderCreated::class);
//     $result1 = $result1->json();

//     $workOrderId = $result1['work_order_id'];

//     $workOrder = WorkOrder::with('tasks:work_order_task_id,work_order_task_uuid,work_order_id,organization_id,problem_diagnosis_id')
//         ->whereUuid($workOrderId)
//         ->first();

//     // create a dummy service request
//     $serviceRequest = ServiceRequest::factory()
//         ->for($workOrder->organization)
//         ->for($workOrder->property)
//         ->for($workOrder->timezone)
//         ->for(ServiceRequestStatus::factory(), 'status')
//         ->for(ServiceRequestType::factory(), 'type')
//         ->create();

//     $workOrder->service_request_id = $serviceRequest->service_request_id;
//     $workOrder->save();

//     $workOrderTasks = $workOrder->tasks;

//     $workOrderTaskId = $workOrderTasks->first()->work_order_task_uuid;

//     unset($result1);

//     /**---------------------------------- Ready To Schedule Work Order ------------------------------------------------- */

//     $result2 = $this->makeApiRequest('patchJson', route('workOrder.action.readyToScheduleWorkOrder', [
//         'workOrder' => $workOrderId,
//         'workOrderTask' => $workOrderTaskId
//     ]), [], $header);

//     expect($result2)
//         ->assertStatus(Response::HTTP_OK);

//     $result2 = $result2->json();
//     expect($result2)
//         ->toHaveKey('work_order_id')
//         ->and($result2['status'])->toBeArray()
//         ->toHaveKey('label', 'Ready To Schedule')
//         ->toHaveKey('value', 'ready_to_schedule')
//         ->toHaveKey('color_class')
//         ->and($result2['abilities'])->toBeArray()
//         ->toContain(Scheduled::$actionName)
//         ->toContain(Paused::$actionName)
//         ->toContain(Canceled::$actionName);

//     unset($result2);

//     /**---------------------------------- Schedule Work Order ------------------------------------------------- */

//     //Select a random user from the organization.
//     $user = User::where('users.organization_id', $workOrderTasks->first()->organization_id)
//         ->where('users.user_type', 'account_user')
//         ->first();

//     //Create technician
//     $technician = Technician::create([
//         'organization_id' => $workOrderTasks->first()->organization_id,
//         'user_id' => $user->user_id,
//         'latitude' => 39.********,
//         'longitude' => -94.********,
//         'max_travel_distance' => 30,
//     ]);

//     $workHours = collect(CarbonDayOfWeek::cases())
//         ->map(function (CarbonDayOfWeek $day) {
//             return ['weekday' => CarbonDayOfWeek::toString($day)];
//         })
//         ->toArray();
//     //Create technician working hour
//     TechnicianWorkingHour::factory()
//         ->count(7)
//         ->state(new Sequence(...$workHours))
//         ->create([
//             'technician_id' => $technician->technician_id,
//             'organization_id' => $technician->organization_id,
//         ]);
//     //Create technician skill
//     TechnicianSkill::create([
//         'technician_id' => $technician->technician_id,
//         'organization_id' => $technician->organization_id,
//         'problem_diagnosis_id' => $workOrderTasks->first()->problem_diagnosis_id,
//     ]);

//     $problemCategory = ProblemCategory::create([
//         'label' => 'Test PC',
//         'slug' => 'test-pc',
//     ]);

//     $problemSubCategory = ProblemSubCategory::create([
//         'label' => 'Test Sub Category',
//         'slug' => 'test-sub-category',
//         'problem_category_id' => $problemCategory->problem_category_id,
//     ]);

//     $diagnosis = ProblemDiagnosis::create([
//         'label' => 'Test Diagnosis',
//         'slug' => 'test-diagnosis',
//         'problem_sub_category_id' => $problemSubCategory->problem_sub_category_id,
//     ]);

//     $issue = Issue::factory()
//         ->for($workOrder->organization)
//         ->for($workOrder->serviceRequest)
//         ->for($user)
//         ->create([
//             'problem_diagnosis_id' => $diagnosis->problem_diagnosis_id,
//         ]);

//     WorkOrderIssue::factory()
//         ->for($workOrder)
//         ->for($issue)
//         ->for($workOrder->organization)
//         ->create();

//     $payload = [
//         'workOrder' => $workOrderId,
//         'workOrderTask' => $workOrderTaskId,
//     ];

//     $result = $this->makeApiRequest('getJson', route('task_schedule.get_context', $payload), $header);

//     expect($result)
//         ->assertStatus(Response::HTTP_OK);

//     $result = $result->json();

//     expect($result)
//         ->toHaveKey('recommended_provider_type')
//         ->and($result['provider_settings'])->toBeArray()
//         ->and($result['provider_settings']['technician'])->toBeArray()
//         ->toHaveKey('recommended_mode')
//         ->toHaveKey('recommended_method')
//         ->toHaveKey('current_method')
//         ->toHaveKey('current_mode')
//         ->toHaveKey('recommended_duration')
//         ->toHaveKey('current_schedule_duration')
//         ->toHaveKey('qualified_technicians')
//         ->toHaveKey('work_to_perform')
//         ->toHaveKey('quote')
//         ->toHaveKey('is_enabled')
//         ->and($result['provider_settings']['technician']['available_modes'])->toBeArray()
//         ->and($result['provider_settings']['technician']['available_methods'])->toBeArray()
//         ->and($result['provider_settings']['technician']['available_durations'])->toBeArray()
//         ->and($result['provider_settings']['vendor'])->toBeArray()
//         ->toHaveKey('is_enabled')
//         ->and($result['provider_settings']['lula'])->toBeArray()
//         ->and($result['provider_settings']['lula']['vendor'])
//         ->and($result['provider_settings']['lula']['is_enabled'])
//         ->and($result['provider_settings']['lula']['estimated_cost'])->toBeArray()
//         ->toHaveKey('lowerCents')
//         ->toHaveKey('upperCents')
//         ->and($result['schedule_options'])->toBeArray()
//         ->and($result['schedule_options'][0]['value'])
//         ->and($result['schedule_options'][0]['label']);

//     $payload = [
//         'workOrder' => $workOrderId,
//         'workOrderTask' => $workOrderTaskId,
//         'method' => SchedulingMethod::EARLIEST(),
//         'mode' => SchedulingMode::MANUAL(),
//         'duration' => 60,
//     ];

//     $result = $this->makeApiRequest('getJson', route('task_schedule.get_technician_list', $payload), $header);

//     expect($result)
//         ->assertStatus(Response::HTTP_OK);

//     $result = $result->json();
//     expect($result)
//         ->and($result['technicians'])->toBeArray()
//         ->and($result['technicians'][0]['technician_uuid'])
//         ->and($result['technicians'][0]['full_name'])
//         ->and($result['ranked_appointments'])->toBeArray()
//         ->and($result['recommended_appointments'])->toBeArray()
//         ->and($result['recommended_appointments'][0]['service_window_reference_id'])
//         ->and($result['recommended_appointments'][0]['rank'])
//         ->and($result['recommended_appointments'][0]['on_hours'])
//         ->and($result['recommended_appointments'][0]['technician_uuid'])
//         ->and($result['recommended_appointments'][0]['provider_start_time'])
//         ->and($result['recommended_appointments'][0]['provider_end_time'])
//         ->and($result['recommended_appointments'][0]['resident_start_time'])
//         ->and($result['recommended_appointments'][0]['resident_end_time']);

//     // Check First technician have proper available windows
//     $result = $this->makeApiRequest('getJson', route('task_schedule.get_technician_schedules', [
//         'workOrder' => $workOrderId,
//         'workOrderTask' => $workOrderTaskId,
//         'technician' => $technician->technician_uuid,
//         'method' => SchedulingMethod::EFFICIENT(),
//         'mode' => SchedulingMode::MANUAL(),
//         'duration' => 240,
//     ]), $header);

//     expect($result)
//         ->assertStatus(Response::HTTP_OK)
//         ->and($result['technicians'])->toHaveCount(1)
//         ->and(count($result['technicians'][0]['availabilities']))->toBeGreaterThanOrEqual(30)
//         ->and($result['technicians'][0]['technician_uuid'])->toEqual($technician->technician_uuid);

//     $responseData = $result->json();

//     $serviceWindowId = collect($responseData['technicians'][0]['availabilities'])
//         ->pluck('service_window_reference_id')
//         ->first();

//     $payload = [
//         'service_window_reference_id' => $serviceWindowId,
//     ];

//     $result3 = $this->makeApiRequest('postJson', route('workOrder.action.scheduleWorkOrder', [
//         'workOrder' => $workOrderId,
//         'workOrderTask' => $workOrderTaskId,
//         'method' => SchedulingMethod::EARLIEST(),
//         'mode' => SchedulingMode::AUTOMATED(),
//         'work_to_perform' => WorkToPerformTypes::HOURLY_TASK(),
//     ]), $payload, $header);

//     expect($result3)
//         ->assertStatus(Response::HTTP_OK);

//     $result3 = $result3->json();

//     expect($result3)
//         ->toHaveKey('appointment_id')
//         ->toHaveKey('trip_number')
//         ->toHaveKey('is_active')
//         ->toHaveKey('start_time')
//         ->toHaveKey('end_time')
//         ->toHaveKey('duration_minutes')
//         ->toHaveKey('rescheduled_reason')
//         ->toHaveKey('is_rescheduled')
//         ->toHaveKey('last_modified_at')
//         ->and($result3['trip_status'])->toBeArray()
//         ->toHaveKey('label', 'Scheduled')
//         ->toHaveKey('value', 'scheduled')
//         ->toHaveKey('color_class')
//         ->and($result3['status'])->toBeArray()
//         ->toHaveKey('label', 'Scheduled')
//         ->toHaveKey('value', 'scheduled')
//         ->and($result3['technician'])->toBeArray()
//         ->toHaveKey('technician_id')
//         ->toHaveKey('name')
//         ->toHaveKey('profile_pic')
//         ->and($result3['work_performed'])->toBeArray()
//         ->toHaveKey('label')
//         ->toHaveKey('value')
//         ->and($result3['abilities'])->toBeArray()
//         ->toContain(EnRoute::$actionName)
//         ->toContain(Paused::$actionName)
//         ->toContain(Canceled::$actionName)
//         ->toContain('re_schedule');

//     unset($result3);

//     /**---------------------------------- Enroute Work Order ------------------------------------------------- */

//     $result4 = $this->makeApiRequest('patchJson', route('workOrder.action.enRouteWorkOrder', [
//         'workOrder' => $workOrderId,
//         'workOrderTask' => $workOrderTaskId
//     ]), [], $header);

//     expect($result4)
//         ->assertStatus(Response::HTTP_OK);

//     $result4 = $result4->json();

//     expect($result4)
//         ->toHaveKey('work_order_id', $workOrderId)
//         ->toHaveKey('work_order_task_id', $workOrderTaskId)
//         ->toHaveKey('enroute_at')
//         ->toHaveKey('appointment_id')
//         ->and($result4['status'])->toBeArray()
//         ->toHaveKey('label', 'Work In Progress')
//         ->toHaveKey('value', 'work_in_progress')
//         ->toHaveKey('color_class')
//         ->and($result4['abilities'])->toBeArray()
//         ->toContain(Paused::$actionName)
//         ->toContain(Canceled::$actionName)
//         ->toContain(Working::$actionName)
//         ->toContain('stop_trip');

//     unset($result4);

//     /**---------------------------------- Start Timer Work Order ------------------------------------------------- */

//     $result5 = $this->makeApiRequest('postJson', route('workOrder.action.startTimerWorkOrder', [
//         'workOrder' => $workOrderId,
//         'workOrderTask' => $workOrderTaskId
//     ]), [], $header);

//     expect($result5)
//         ->assertStatus(Response::HTTP_OK);

//     $result5 = $result5->json();

//     expect($result5)
//         ->toHaveKey('work_order_id', $workOrderId)
//         ->toHaveKey('work_order_task_id', $workOrderTaskId)
//         ->toHaveKey('technician_appointment_id')
//         ->toHaveKey('actual_start_time')
//         ->and($result5['status'])->toBeArray()
//         ->toHaveKey('label', 'Work In Progress')
//         ->toHaveKey('value', 'work_in_progress')
//         ->toHaveKey('color_class')
//         ->and($result5['appointment'])->toBeArray()
//         ->toHaveKey('appointment_id')
//         ->toHaveKey('travel_time_in_sec')
//         ->and($result5['appointment']['status'])->toBeArray()
//         ->toHaveKey('label', 'Working')
//         ->toHaveKey('value', 'working')
//         ->toHaveKey('color_class')
//         ->and($result5['abilities'])->toBeArray()
//         ->toContain(Canceled::$actionName)
//         ->toContain(Ended::$actionName)
//         ->toContain(ServiceCallPaused::$actionName)
//         ->toContain('submit_quote')
//         ->toContain('can_submit_quote')
//         ->toContain('add_materials');

//     unset($result5);

//     /**---------------------------------- Pause Timer Work Order ------------------------------------------------- */

//     $payload = [
//         'timer_paused_reason' => fake()->word(),
//     ];

//     $result6 = $this->makeApiRequest('postJson', route('workOrder.action.pauseWorkOrderTrip', [
//         'workOrder' => $workOrderId,
//         'workOrderTask' => $workOrderTaskId
//     ]), $payload, $header);

//     expect($result6)
//         ->assertStatus(Response::HTTP_OK);

//     $result6 = $result6->json();

//     expect($result6)
//         ->toHaveKey('work_order_service_call_id')
//         ->toHaveKey('elapse_time_in_sec')
//         ->toHaveKey('paused_at')
//         ->toHaveKey('timer_paused_reason')
//         ->toHaveKey('resumed_at')
//         ->and($result6['status'])->toBeArray()
//         ->toHaveKey('label', 'Work In Progress')
//         ->toHaveKey('value', 'work_in_progress')
//         ->toHaveKey('color_class')
//         ->and($result6['appointment'])->toBeArray()
//         ->and($result6['appointment']['status'])->toBeArray()
//         ->toHaveKey('label', 'Working')
//         ->toHaveKey('value', 'working')
//         ->toHaveKey('color_class')
//         ->and($result6['abilities'])->toBeArray()
//         ->toContain(Paused::$actionName)
//         ->toContain(Canceled::$actionName)
//         ->toContain(Ended::$actionName)
//         ->toContain('resume')
//         ->toContain('submit_quote')
//         ->toContain('can_submit_quote')
//         ->toContain('add_materials');

//     unset($result6);

//     /**---------------------------------- Resume Timer Work Order ------------------------------------------------- */

//     $result7 = $this->makeApiRequest('postJson', route('workOrder.action.resumeWorkOrderTrip', [
//         'workOrder' => $workOrderId,
//         'workOrderTask' => $workOrderTaskId
//     ]), $payload, $header);

//     expect($result7)
//         ->assertStatus(Response::HTTP_OK);

//     $result7 = $result7->json();

//     expect($result7)
//         ->toHaveKey('work_order_service_call_id')
//         ->toHaveKey('resumed_at')
//         ->toHaveKey('elapse_time_in_sec')
//         ->and($result7['status'])->toBeArray()
//         ->toHaveKey('label', 'Work In Progress')
//         ->toHaveKey('value', 'work_in_progress')
//         ->toHaveKey('color_class')
//         ->and($result7['appointment'])->toBeArray()
//         ->and($result7['appointment']['status'])->toBeArray()
//         ->toHaveKey('label', 'Timer Paused')
//         ->toHaveKey('value', 'paused')
//         ->toHaveKey('color_class')
//         ->and($result7['abilities'])->toBeArray()
//         ->toContain(Canceled::$actionName)
//         ->toContain(Ended::$actionName)
//         ->toContain(ServiceCallPaused::$actionName)
//         ->toContain('submit_quote')
//         ->toContain('can_submit_quote')
//         ->toContain('add_materials');

//     unset($result7);

//     /**---------------------------------- Complete Work Order ------------------------------------------------- */

//     $postPayLoad = [
//         'service_notes' => 'Sample service notes',
//         'trip_end_with' => Trip::NO_WORK(),
//         'trip_end_with_type' => Trip::SUBMIT_QUOTE(),
//         'completed_time_in_sec' => fake()->numberBetween(1, 300),
//         'travel_time_in_sec' => fake()->numberBetween(1, 300),
//         'reason' => 'test',
//     ];

//     $result8 = $this->makeApiRequest('postJson', route('workOrder.action.completeWorkOrder', [
//         'workOrder' => $workOrderId,
//         'workOrderTask' => $workOrderTaskId,
//     ]), $postPayLoad, $header);

//     expect($result8)
//         ->assertStatus(Response::HTTP_OK);

//     $result8 = $result8->json();

//     expect($result8)
//         ->toHaveKey('work_order_id', $workOrderId)
//         ->toHaveKey('work_completed_at')
//         ->toHaveKey('service_notes')
//         ->and($result8['status'])->toBeArray()
//         ->toHaveKey('label', 'Paused')
//         ->toHaveKey('value', 'paused')
//         ->toHaveKey('color_class')
//         ->and($result8['appointment'])->toBeArray()
//         ->toHaveKey('appointment_id')
//         ->toHaveKey('trip_number')
//         ->toHaveKey('is_active')
//         ->toHaveKey('is_canceled')
//         ->toHaveKey('started_at')
//         ->toHaveKey('ended_at')
//         ->toHaveKey('rescheduled_reason')
//         ->toHaveKey('elapse_time_in_sec')
//         ->toHaveKey('en_route_at')
//         ->toHaveKey('travel_time_in_sec')
//         ->and($result8['appointment']['status'])->toBeArray()
//         ->toHaveKey('label', 'Ended')
//         ->toHaveKey('value', 'ended')
//         ->toHaveKey('color_class')
//         ->and($result8['appointment'])->toHaveKey('is_canceled')
//         ->and($result8['appointment']['work_performed'])->toBeArray()
//         ->toHaveKey('value', 'hourly-task')
//         ->toHaveKey('label', 'Hourly Rate')
//         ->and($result8['appointment'])->toHaveKey('last_modified_at')
//         ->and($result8['appointment'])->toHaveKey('start_time')
//         ->and($result8['appointment'])->toHaveKey('end_time')
//         ->and($result8['appointment'])->toHaveKey('materials')
//         ->and($result8['appointment'])->toHaveKey('quote_id')
//         ->and($result8['appointment'])->toHaveKey('media')
//         ->and($result8['appointment']['trip_end_with'])->toBeArray()
//         ->toHaveKey('label', 'No Work')
//         ->toHaveKey('value', 'no-work')
//         ->and($result8['appointment']['trip_end_with_type'])->toBeArray()
//         ->toHaveKey('label', 'I need to submit a quote myself')
//         ->toHaveKey('value', 'submit-quote')
//         ->and($result8['appointment'])->toHaveKey('reason')
//         ->and($result8['appointment'])->toHaveKey('is_rescheduled')
//         ->and($result8['appointment'])->toHaveKey('service_notes')
//         ->toHaveKey('disable_materials')
//         ->toHaveKey('trip_type')
//         ->and($result8['appointment']['technician'])->toBeArray()
//         ->toHaveKey('name')
//         ->toHaveKey('technician_id')
//         ->toHaveKey('profile_pic')
//         ->and($result8['abilities'])->toBeArray()
//         ->toContain(Canceled::$actionName)
//         ->toContain('submit_quote');

//     unset($result8);
// });

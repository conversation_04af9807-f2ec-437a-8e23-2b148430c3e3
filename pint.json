{"preset": "laravel", "rules": {"blank_line_between_import_groups": true, "concat_space": {"spacing": "one"}, "class_attributes_separation": {"elements": {"method": "one"}}, "curly_braces_position": {"control_structures_opening_brace": "same_line", "functions_opening_brace": "next_line_unless_newline_at_signature_end", "anonymous_functions_opening_brace": "same_line", "classes_opening_brace": "next_line_unless_newline_at_signature_end", "anonymous_classes_opening_brace": "next_line_unless_newline_at_signature_end", "allow_single_line_empty_anonymous_classes": true, "allow_single_line_anonymous_functions": false}, "explicit_string_variable": true, "global_namespace_import": {"import_classes": true, "import_constants": true, "import_functions": true}, "new_with_braces": {"named_class": false, "anonymous_class": false}, "ordered_imports": {"sort_algorithm": "alpha", "imports_order": ["const", "class", "function"]}, "php_unit_test_annotation": {"style": "annotation"}, "simple_to_complex_string_variable": true}, "notPath": ["ide_helper.php", "ide_helper_models.php"]}
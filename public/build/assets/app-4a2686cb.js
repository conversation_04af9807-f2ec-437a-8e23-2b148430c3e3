var ut="top",xt="bottom",At="right",ft="left",eo="auto",Br=[ut,xt,At,ft],Qn="start",Ir="end",sc="clippingParents",rl="viewport",Dr="popper",lc="reference",xs=Br.reduce(function(e,t){return e.concat([t+"-"+Qn,t+"-"+Ir])},[]),al=[].concat(Br,[eo]).reduce(function(e,t){return e.concat([t,t+"-"+Qn,t+"-"+Ir])},[]),uc="beforeRead",fc="read",cc="afterRead",dc="beforeMain",hc="main",pc="afterMain",vc="beforeWrite",mc="write",bc="afterWrite",_c=[uc,fc,cc,dc,hc,pc,vc,mc,bc];function tn(e){return e?(e.nodeName||"").toLowerCase():null}function Et(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Zn(e){var t=Et(e).Element;return e instanceof t||e instanceof Element}function Rt(e){var t=Et(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function il(e){if(typeof ShadowRoot>"u")return!1;var t=Et(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function zp(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var r=t.styles[n]||{},a=t.attributes[n]||{},o=t.elements[n];!Rt(o)||!tn(o)||(Object.assign(o.style,r),Object.keys(a).forEach(function(s){var u=a[s];u===!1?o.removeAttribute(s):o.setAttribute(s,u===!0?"":u)}))})}function Jp(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(r){var a=t.elements[r],o=t.attributes[r]||{},s=Object.keys(t.styles.hasOwnProperty(r)?t.styles[r]:n[r]),u=s.reduce(function(f,d){return f[d]="",f},{});!Rt(a)||!tn(a)||(Object.assign(a.style,u),Object.keys(o).forEach(function(f){a.removeAttribute(f)}))})}}const ol={name:"applyStyles",enabled:!0,phase:"write",fn:zp,effect:Jp,requires:["computeStyles"]};function en(e){return e.split("-")[0]}var Jn=Math.max,Fi=Math.min,Rr=Math.round;function As(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function gc(){return!/^((?!chrome|android).)*safari/i.test(As())}function Pr(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var r=e.getBoundingClientRect(),a=1,o=1;t&&Rt(e)&&(a=e.offsetWidth>0&&Rr(r.width)/e.offsetWidth||1,o=e.offsetHeight>0&&Rr(r.height)/e.offsetHeight||1);var s=Zn(e)?Et(e):window,u=s.visualViewport,f=!gc()&&n,d=(r.left+(f&&u?u.offsetLeft:0))/a,h=(r.top+(f&&u?u.offsetTop:0))/o,b=r.width/a,_=r.height/o;return{width:b,height:_,top:h,right:d+b,bottom:h+_,left:d,x:d,y:h}}function sl(e){var t=Pr(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function yc(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&il(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function gn(e){return Et(e).getComputedStyle(e)}function Qp(e){return["table","td","th"].indexOf(tn(e))>=0}function kn(e){return((Zn(e)?e.ownerDocument:e.document)||window.document).documentElement}function to(e){return tn(e)==="html"?e:e.assignedSlot||e.parentNode||(il(e)?e.host:null)||kn(e)}function Ku(e){return!Rt(e)||gn(e).position==="fixed"?null:e.offsetParent}function Zp(e){var t=/firefox/i.test(As()),n=/Trident/i.test(As());if(n&&Rt(e)){var r=gn(e);if(r.position==="fixed")return null}var a=to(e);for(il(a)&&(a=a.host);Rt(a)&&["html","body"].indexOf(tn(a))<0;){var o=gn(a);if(o.transform!=="none"||o.perspective!=="none"||o.contain==="paint"||["transform","perspective"].indexOf(o.willChange)!==-1||t&&o.willChange==="filter"||t&&o.filter&&o.filter!=="none")return a;a=a.parentNode}return null}function xa(e){for(var t=Et(e),n=Ku(e);n&&Qp(n)&&gn(n).position==="static";)n=Ku(n);return n&&(tn(n)==="html"||tn(n)==="body"&&gn(n).position==="static")?t:n||Zp(e)||t}function ll(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function ba(e,t,n){return Jn(e,Fi(t,n))}function ev(e,t,n){var r=ba(e,t,n);return r>n?n:r}function wc(){return{top:0,right:0,bottom:0,left:0}}function Tc(e){return Object.assign({},wc(),e)}function Cc(e,t){return t.reduce(function(n,r){return n[r]=e,n},{})}var tv=function(t,n){return t=typeof t=="function"?t(Object.assign({},n.rects,{placement:n.placement})):t,Tc(typeof t!="number"?t:Cc(t,Br))};function nv(e){var t,n=e.state,r=e.name,a=e.options,o=n.elements.arrow,s=n.modifiersData.popperOffsets,u=en(n.placement),f=ll(u),d=[ft,At].indexOf(u)>=0,h=d?"height":"width";if(!(!o||!s)){var b=tv(a.padding,n),_=sl(o),y=f==="y"?ut:ft,g=f==="y"?xt:At,S=n.rects.reference[h]+n.rects.reference[f]-s[f]-n.rects.popper[h],L=s[f]-n.rects.reference[f],O=xa(o),C=O?f==="y"?O.clientHeight||0:O.clientWidth||0:0,Y=S/2-L/2,W=b[y],B=C-_[h]-b[g],Q=C/2-_[h]/2+Y,v=ba(W,Q,B),ee=f;n.modifiersData[r]=(t={},t[ee]=v,t.centerOffset=v-Q,t)}}function rv(e){var t=e.state,n=e.options,r=n.element,a=r===void 0?"[data-popper-arrow]":r;a!=null&&(typeof a=="string"&&(a=t.elements.popper.querySelector(a),!a)||yc(t.elements.popper,a)&&(t.elements.arrow=a))}const Dc={name:"arrow",enabled:!0,phase:"main",fn:nv,effect:rv,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function kr(e){return e.split("-")[1]}var av={top:"auto",right:"auto",bottom:"auto",left:"auto"};function iv(e,t){var n=e.x,r=e.y,a=t.devicePixelRatio||1;return{x:Rr(n*a)/a||0,y:Rr(r*a)/a||0}}function Yu(e){var t,n=e.popper,r=e.popperRect,a=e.placement,o=e.variation,s=e.offsets,u=e.position,f=e.gpuAcceleration,d=e.adaptive,h=e.roundOffsets,b=e.isFixed,_=s.x,y=_===void 0?0:_,g=s.y,S=g===void 0?0:g,L=typeof h=="function"?h({x:y,y:S}):{x:y,y:S};y=L.x,S=L.y;var O=s.hasOwnProperty("x"),C=s.hasOwnProperty("y"),Y=ft,W=ut,B=window;if(d){var Q=xa(n),v="clientHeight",ee="clientWidth";if(Q===Et(n)&&(Q=kn(n),gn(Q).position!=="static"&&u==="absolute"&&(v="scrollHeight",ee="scrollWidth")),Q=Q,a===ut||(a===ft||a===At)&&o===Ir){W=xt;var G=b&&Q===B&&B.visualViewport?B.visualViewport.height:Q[v];S-=G-r.height,S*=f?1:-1}if(a===ft||(a===ut||a===xt)&&o===Ir){Y=At;var re=b&&Q===B&&B.visualViewport?B.visualViewport.width:Q[ee];y-=re-r.width,y*=f?1:-1}}var ie=Object.assign({position:u},d&&av),pe=h===!0?iv({x:y,y:S},Et(n)):{x:y,y:S};if(y=pe.x,S=pe.y,f){var se;return Object.assign({},ie,(se={},se[W]=C?"0":"",se[Y]=O?"0":"",se.transform=(B.devicePixelRatio||1)<=1?"translate("+y+"px, "+S+"px)":"translate3d("+y+"px, "+S+"px, 0)",se))}return Object.assign({},ie,(t={},t[W]=C?S+"px":"",t[Y]=O?y+"px":"",t.transform="",t))}function ov(e){var t=e.state,n=e.options,r=n.gpuAcceleration,a=r===void 0?!0:r,o=n.adaptive,s=o===void 0?!0:o,u=n.roundOffsets,f=u===void 0?!0:u,d={placement:en(t.placement),variation:kr(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:a,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Yu(Object.assign({},d,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:f})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Yu(Object.assign({},d,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:f})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const ul={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:ov,data:{}};var vi={passive:!0};function sv(e){var t=e.state,n=e.instance,r=e.options,a=r.scroll,o=a===void 0?!0:a,s=r.resize,u=s===void 0?!0:s,f=Et(t.elements.popper),d=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&d.forEach(function(h){h.addEventListener("scroll",n.update,vi)}),u&&f.addEventListener("resize",n.update,vi),function(){o&&d.forEach(function(h){h.removeEventListener("scroll",n.update,vi)}),u&&f.removeEventListener("resize",n.update,vi)}}const fl={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:sv,data:{}};var lv={left:"right",right:"left",bottom:"top",top:"bottom"};function Di(e){return e.replace(/left|right|bottom|top/g,function(t){return lv[t]})}var uv={start:"end",end:"start"};function Gu(e){return e.replace(/start|end/g,function(t){return uv[t]})}function cl(e){var t=Et(e),n=t.pageXOffset,r=t.pageYOffset;return{scrollLeft:n,scrollTop:r}}function dl(e){return Pr(kn(e)).left+cl(e).scrollLeft}function fv(e,t){var n=Et(e),r=kn(e),a=n.visualViewport,o=r.clientWidth,s=r.clientHeight,u=0,f=0;if(a){o=a.width,s=a.height;var d=gc();(d||!d&&t==="fixed")&&(u=a.offsetLeft,f=a.offsetTop)}return{width:o,height:s,x:u+dl(e),y:f}}function cv(e){var t,n=kn(e),r=cl(e),a=(t=e.ownerDocument)==null?void 0:t.body,o=Jn(n.scrollWidth,n.clientWidth,a?a.scrollWidth:0,a?a.clientWidth:0),s=Jn(n.scrollHeight,n.clientHeight,a?a.scrollHeight:0,a?a.clientHeight:0),u=-r.scrollLeft+dl(e),f=-r.scrollTop;return gn(a||n).direction==="rtl"&&(u+=Jn(n.clientWidth,a?a.clientWidth:0)-o),{width:o,height:s,x:u,y:f}}function hl(e){var t=gn(e),n=t.overflow,r=t.overflowX,a=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+a+r)}function Sc(e){return["html","body","#document"].indexOf(tn(e))>=0?e.ownerDocument.body:Rt(e)&&hl(e)?e:Sc(to(e))}function _a(e,t){var n;t===void 0&&(t=[]);var r=Sc(e),a=r===((n=e.ownerDocument)==null?void 0:n.body),o=Et(r),s=a?[o].concat(o.visualViewport||[],hl(r)?r:[]):r,u=t.concat(s);return a?u:u.concat(_a(to(s)))}function Es(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function dv(e,t){var n=Pr(e,!1,t==="fixed");return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}function zu(e,t,n){return t===rl?Es(fv(e,n)):Zn(t)?dv(t,n):Es(cv(kn(e)))}function hv(e){var t=_a(to(e)),n=["absolute","fixed"].indexOf(gn(e).position)>=0,r=n&&Rt(e)?xa(e):e;return Zn(r)?t.filter(function(a){return Zn(a)&&yc(a,r)&&tn(a)!=="body"}):[]}function pv(e,t,n,r){var a=t==="clippingParents"?hv(e):[].concat(t),o=[].concat(a,[n]),s=o[0],u=o.reduce(function(f,d){var h=zu(e,d,r);return f.top=Jn(h.top,f.top),f.right=Fi(h.right,f.right),f.bottom=Fi(h.bottom,f.bottom),f.left=Jn(h.left,f.left),f},zu(e,s,r));return u.width=u.right-u.left,u.height=u.bottom-u.top,u.x=u.left,u.y=u.top,u}function xc(e){var t=e.reference,n=e.element,r=e.placement,a=r?en(r):null,o=r?kr(r):null,s=t.x+t.width/2-n.width/2,u=t.y+t.height/2-n.height/2,f;switch(a){case ut:f={x:s,y:t.y-n.height};break;case xt:f={x:s,y:t.y+t.height};break;case At:f={x:t.x+t.width,y:u};break;case ft:f={x:t.x-n.width,y:u};break;default:f={x:t.x,y:t.y}}var d=a?ll(a):null;if(d!=null){var h=d==="y"?"height":"width";switch(o){case Qn:f[d]=f[d]-(t[h]/2-n[h]/2);break;case Ir:f[d]=f[d]+(t[h]/2-n[h]/2);break}}return f}function $r(e,t){t===void 0&&(t={});var n=t,r=n.placement,a=r===void 0?e.placement:r,o=n.strategy,s=o===void 0?e.strategy:o,u=n.boundary,f=u===void 0?sc:u,d=n.rootBoundary,h=d===void 0?rl:d,b=n.elementContext,_=b===void 0?Dr:b,y=n.altBoundary,g=y===void 0?!1:y,S=n.padding,L=S===void 0?0:S,O=Tc(typeof L!="number"?L:Cc(L,Br)),C=_===Dr?lc:Dr,Y=e.rects.popper,W=e.elements[g?C:_],B=pv(Zn(W)?W:W.contextElement||kn(e.elements.popper),f,h,s),Q=Pr(e.elements.reference),v=xc({reference:Q,element:Y,strategy:"absolute",placement:a}),ee=Es(Object.assign({},Y,v)),G=_===Dr?ee:Q,re={top:B.top-G.top+O.top,bottom:G.bottom-B.bottom+O.bottom,left:B.left-G.left+O.left,right:G.right-B.right+O.right},ie=e.modifiersData.offset;if(_===Dr&&ie){var pe=ie[a];Object.keys(re).forEach(function(se){var ae=[At,xt].indexOf(se)>=0?1:-1,we=[ut,xt].indexOf(se)>=0?"y":"x";re[se]+=pe[we]*ae})}return re}function vv(e,t){t===void 0&&(t={});var n=t,r=n.placement,a=n.boundary,o=n.rootBoundary,s=n.padding,u=n.flipVariations,f=n.allowedAutoPlacements,d=f===void 0?al:f,h=kr(r),b=h?u?xs:xs.filter(function(g){return kr(g)===h}):Br,_=b.filter(function(g){return d.indexOf(g)>=0});_.length===0&&(_=b);var y=_.reduce(function(g,S){return g[S]=$r(e,{placement:S,boundary:a,rootBoundary:o,padding:s})[en(S)],g},{});return Object.keys(y).sort(function(g,S){return y[g]-y[S]})}function mv(e){if(en(e)===eo)return[];var t=Di(e);return[Gu(e),t,Gu(t)]}function bv(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var a=n.mainAxis,o=a===void 0?!0:a,s=n.altAxis,u=s===void 0?!0:s,f=n.fallbackPlacements,d=n.padding,h=n.boundary,b=n.rootBoundary,_=n.altBoundary,y=n.flipVariations,g=y===void 0?!0:y,S=n.allowedAutoPlacements,L=t.options.placement,O=en(L),C=O===L,Y=f||(C||!g?[Di(L)]:mv(L)),W=[L].concat(Y).reduce(function(it,tt){return it.concat(en(tt)===eo?vv(t,{placement:tt,boundary:h,rootBoundary:b,padding:d,flipVariations:g,allowedAutoPlacements:S}):tt)},[]),B=t.rects.reference,Q=t.rects.popper,v=new Map,ee=!0,G=W[0],re=0;re<W.length;re++){var ie=W[re],pe=en(ie),se=kr(ie)===Qn,ae=[ut,xt].indexOf(pe)>=0,we=ae?"width":"height",Te=$r(t,{placement:ie,boundary:h,rootBoundary:b,altBoundary:_,padding:d}),me=ae?se?At:ft:se?xt:ut;B[we]>Q[we]&&(me=Di(me));var xe=Di(me),Le=[];if(o&&Le.push(Te[pe]<=0),u&&Le.push(Te[me]<=0,Te[xe]<=0),Le.every(function(it){return it})){G=ie,ee=!1;break}v.set(ie,Le)}if(ee)for(var Fe=g?3:1,ct=function(tt){var oe=W.find(function(_e){var ln=v.get(_e);if(ln)return ln.slice(0,tt).every(function(sr){return sr})});if(oe)return G=oe,"break"},We=Fe;We>0;We--){var ze=ct(We);if(ze==="break")break}t.placement!==G&&(t.modifiersData[r]._skip=!0,t.placement=G,t.reset=!0)}}const Ac={name:"flip",enabled:!0,phase:"main",fn:bv,requiresIfExists:["offset"],data:{_skip:!1}};function Ju(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Qu(e){return[ut,At,xt,ft].some(function(t){return e[t]>=0})}function _v(e){var t=e.state,n=e.name,r=t.rects.reference,a=t.rects.popper,o=t.modifiersData.preventOverflow,s=$r(t,{elementContext:"reference"}),u=$r(t,{altBoundary:!0}),f=Ju(s,r),d=Ju(u,a,o),h=Qu(f),b=Qu(d);t.modifiersData[n]={referenceClippingOffsets:f,popperEscapeOffsets:d,isReferenceHidden:h,hasPopperEscaped:b},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":h,"data-popper-escaped":b})}const Ec={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:_v};function gv(e,t,n){var r=en(e),a=[ft,ut].indexOf(r)>=0?-1:1,o=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,s=o[0],u=o[1];return s=s||0,u=(u||0)*a,[ft,At].indexOf(r)>=0?{x:u,y:s}:{x:s,y:u}}function yv(e){var t=e.state,n=e.options,r=e.name,a=n.offset,o=a===void 0?[0,0]:a,s=al.reduce(function(h,b){return h[b]=gv(b,t.rects,o),h},{}),u=s[t.placement],f=u.x,d=u.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=f,t.modifiersData.popperOffsets.y+=d),t.modifiersData[r]=s}const Nc={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:yv};function wv(e){var t=e.state,n=e.name;t.modifiersData[n]=xc({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}const pl={name:"popperOffsets",enabled:!0,phase:"read",fn:wv,data:{}};function Tv(e){return e==="x"?"y":"x"}function Cv(e){var t=e.state,n=e.options,r=e.name,a=n.mainAxis,o=a===void 0?!0:a,s=n.altAxis,u=s===void 0?!1:s,f=n.boundary,d=n.rootBoundary,h=n.altBoundary,b=n.padding,_=n.tether,y=_===void 0?!0:_,g=n.tetherOffset,S=g===void 0?0:g,L=$r(t,{boundary:f,rootBoundary:d,padding:b,altBoundary:h}),O=en(t.placement),C=kr(t.placement),Y=!C,W=ll(O),B=Tv(W),Q=t.modifiersData.popperOffsets,v=t.rects.reference,ee=t.rects.popper,G=typeof S=="function"?S(Object.assign({},t.rects,{placement:t.placement})):S,re=typeof G=="number"?{mainAxis:G,altAxis:G}:Object.assign({mainAxis:0,altAxis:0},G),ie=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,pe={x:0,y:0};if(Q){if(o){var se,ae=W==="y"?ut:ft,we=W==="y"?xt:At,Te=W==="y"?"height":"width",me=Q[W],xe=me+L[ae],Le=me-L[we],Fe=y?-ee[Te]/2:0,ct=C===Qn?v[Te]:ee[Te],We=C===Qn?-ee[Te]:-v[Te],ze=t.elements.arrow,it=y&&ze?sl(ze):{width:0,height:0},tt=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:wc(),oe=tt[ae],_e=tt[we],ln=ba(0,v[Te],it[Te]),sr=Y?v[Te]/2-Fe-ln-oe-re.mainAxis:ct-ln-oe-re.mainAxis,Nt=Y?-v[Te]/2+Fe+ln+_e+re.mainAxis:We+ln+_e+re.mainAxis,Jr=t.elements.arrow&&xa(t.elements.arrow),Dn=Jr?W==="y"?Jr.clientTop||0:Jr.clientLeft||0:0,Wn=(se=ie==null?void 0:ie[W])!=null?se:0,ri=me+sr-Wn-Dn,Oo=me+Nt-Wn,lr=ba(y?Fi(xe,ri):xe,me,y?Jn(Le,Oo):Le);Q[W]=lr,pe[W]=lr-me}if(u){var Vn,Mt=W==="x"?ut:ft,Io=W==="x"?xt:At,un=Q[B],ur=B==="y"?"height":"width",Tt=un+L[Mt],Sn=un-L[Io],fn=[ut,ft].indexOf(O)!==-1,le=(Vn=ie==null?void 0:ie[B])!=null?Vn:0,Ze=fn?Tt:un-v[ur]-ee[ur]-le+re.altAxis,ai=fn?un+v[ur]+ee[ur]-le-re.altAxis:Sn,ii=y&&fn?ev(Ze,un,ai):ba(y?Ze:Tt,un,y?ai:Sn);Q[B]=ii,pe[B]=ii-un}t.modifiersData[r]=pe}}const Lc={name:"preventOverflow",enabled:!0,phase:"main",fn:Cv,requiresIfExists:["offset"]};function Dv(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Sv(e){return e===Et(e)||!Rt(e)?cl(e):Dv(e)}function xv(e){var t=e.getBoundingClientRect(),n=Rr(t.width)/e.offsetWidth||1,r=Rr(t.height)/e.offsetHeight||1;return n!==1||r!==1}function Av(e,t,n){n===void 0&&(n=!1);var r=Rt(t),a=Rt(t)&&xv(t),o=kn(t),s=Pr(e,a,n),u={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(r||!r&&!n)&&((tn(t)!=="body"||hl(o))&&(u=Sv(t)),Rt(t)?(f=Pr(t,!0),f.x+=t.clientLeft,f.y+=t.clientTop):o&&(f.x=dl(o))),{x:s.left+u.scrollLeft-f.x,y:s.top+u.scrollTop-f.y,width:s.width,height:s.height}}function Ev(e){var t=new Map,n=new Set,r=[];e.forEach(function(o){t.set(o.name,o)});function a(o){n.add(o.name);var s=[].concat(o.requires||[],o.requiresIfExists||[]);s.forEach(function(u){if(!n.has(u)){var f=t.get(u);f&&a(f)}}),r.push(o)}return e.forEach(function(o){n.has(o.name)||a(o)}),r}function Nv(e){var t=Ev(e);return _c.reduce(function(n,r){return n.concat(t.filter(function(a){return a.phase===r}))},[])}function Lv(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function Ov(e){var t=e.reduce(function(n,r){var a=n[r.name];return n[r.name]=a?Object.assign({},a,r,{options:Object.assign({},a.options,r.options),data:Object.assign({},a.data,r.data)}):r,n},{});return Object.keys(t).map(function(n){return t[n]})}var Zu={placement:"bottom",modifiers:[],strategy:"absolute"};function ef(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function no(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,r=n===void 0?[]:n,a=t.defaultOptions,o=a===void 0?Zu:a;return function(u,f,d){d===void 0&&(d=o);var h={placement:"bottom",orderedModifiers:[],options:Object.assign({},Zu,o),modifiersData:{},elements:{reference:u,popper:f},attributes:{},styles:{}},b=[],_=!1,y={state:h,setOptions:function(O){var C=typeof O=="function"?O(h.options):O;S(),h.options=Object.assign({},o,h.options,C),h.scrollParents={reference:Zn(u)?_a(u):u.contextElement?_a(u.contextElement):[],popper:_a(f)};var Y=Nv(Ov([].concat(r,h.options.modifiers)));return h.orderedModifiers=Y.filter(function(W){return W.enabled}),g(),y.update()},forceUpdate:function(){if(!_){var O=h.elements,C=O.reference,Y=O.popper;if(ef(C,Y)){h.rects={reference:Av(C,xa(Y),h.options.strategy==="fixed"),popper:sl(Y)},h.reset=!1,h.placement=h.options.placement,h.orderedModifiers.forEach(function(re){return h.modifiersData[re.name]=Object.assign({},re.data)});for(var W=0;W<h.orderedModifiers.length;W++){if(h.reset===!0){h.reset=!1,W=-1;continue}var B=h.orderedModifiers[W],Q=B.fn,v=B.options,ee=v===void 0?{}:v,G=B.name;typeof Q=="function"&&(h=Q({state:h,options:ee,name:G,instance:y})||h)}}}},update:Lv(function(){return new Promise(function(L){y.forceUpdate(),L(h)})}),destroy:function(){S(),_=!0}};if(!ef(u,f))return y;y.setOptions(d).then(function(L){!_&&d.onFirstUpdate&&d.onFirstUpdate(L)});function g(){h.orderedModifiers.forEach(function(L){var O=L.name,C=L.options,Y=C===void 0?{}:C,W=L.effect;if(typeof W=="function"){var B=W({state:h,name:O,instance:y,options:Y}),Q=function(){};b.push(B||Q)}})}function S(){b.forEach(function(L){return L()}),b=[]}return y}}var Iv=no(),Rv=[fl,pl,ul,ol],Pv=no({defaultModifiers:Rv}),kv=[fl,pl,ul,ol,Nc,Ac,Lc,Dc,Ec],vl=no({defaultModifiers:kv});const Oc=Object.freeze(Object.defineProperty({__proto__:null,afterMain:pc,afterRead:cc,afterWrite:bc,applyStyles:ol,arrow:Dc,auto:eo,basePlacements:Br,beforeMain:dc,beforeRead:uc,beforeWrite:vc,bottom:xt,clippingParents:sc,computeStyles:ul,createPopper:vl,createPopperBase:Iv,createPopperLite:Pv,detectOverflow:$r,end:Ir,eventListeners:fl,flip:Ac,hide:Ec,left:ft,main:hc,modifierPhases:_c,offset:Nc,placements:al,popper:Dr,popperGenerator:no,popperOffsets:pl,preventOverflow:Lc,read:fc,reference:lc,right:At,start:Qn,top:ut,variationPlacements:xs,viewport:rl,write:mc},Symbol.toStringTag,{value:"Module"}));/*!
  * Bootstrap v5.3.3 (https://getbootstrap.com/)
  * Copyright 2011-2024 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */const Nn=new Map,es={set(e,t,n){Nn.has(e)||Nn.set(e,new Map);const r=Nn.get(e);if(!r.has(t)&&r.size!==0){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(r.keys())[0]}.`);return}r.set(t,n)},get(e,t){return Nn.has(e)&&Nn.get(e).get(t)||null},remove(e,t){if(!Nn.has(e))return;const n=Nn.get(e);n.delete(t),n.size===0&&Nn.delete(e)}},$v=1e6,Fv=1e3,Ns="transitionend",Ic=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(t,n)=>`#${CSS.escape(n)}`)),e),Hv=e=>e==null?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),Mv=e=>{do e+=Math.floor(Math.random()*$v);while(document.getElementById(e));return e},jv=e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:n}=window.getComputedStyle(e);const r=Number.parseFloat(t),a=Number.parseFloat(n);return!r&&!a?0:(t=t.split(",")[0],n=n.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(n))*Fv)},Rc=e=>{e.dispatchEvent(new Event(Ns))},mn=e=>!e||typeof e!="object"?!1:(typeof e.jquery<"u"&&(e=e[0]),typeof e.nodeType<"u"),Rn=e=>mn(e)?e.jquery?e[0]:e:typeof e=="string"&&e.length>0?document.querySelector(Ic(e)):null,Wr=e=>{if(!mn(e)||e.getClientRects().length===0)return!1;const t=getComputedStyle(e).getPropertyValue("visibility")==="visible",n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const r=e.closest("summary");if(r&&r.parentNode!==n||r===null)return!1}return t},Pn=e=>!e||e.nodeType!==Node.ELEMENT_NODE||e.classList.contains("disabled")?!0:typeof e.disabled<"u"?e.disabled:e.hasAttribute("disabled")&&e.getAttribute("disabled")!=="false",Pc=e=>{if(!document.documentElement.attachShadow)return null;if(typeof e.getRootNode=="function"){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?Pc(e.parentNode):null},Hi=()=>{},Aa=e=>{e.offsetHeight},kc=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,ts=[],Bv=e=>{document.readyState==="loading"?(ts.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of ts)t()}),ts.push(e)):e()},$t=()=>document.documentElement.dir==="rtl",Ht=e=>{Bv(()=>{const t=kc();if(t){const n=e.NAME,r=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=r,e.jQueryInterface)}})},bt=(e,t=[],n=e)=>typeof e=="function"?e(...t):n,$c=(e,t,n=!0)=>{if(!n){bt(e);return}const r=5,a=jv(t)+r;let o=!1;const s=({target:u})=>{u===t&&(o=!0,t.removeEventListener(Ns,s),bt(e))};t.addEventListener(Ns,s),setTimeout(()=>{o||Rc(t)},a)},ml=(e,t,n,r)=>{const a=e.length;let o=e.indexOf(t);return o===-1?!n&&r?e[a-1]:e[0]:(o+=n?1:-1,r&&(o=(o+a)%a),e[Math.max(0,Math.min(o,a-1))])},Wv=/[^.]*(?=\..*)\.|.*/,Vv=/\..*/,qv=/::\d+$/,ns={};let tf=1;const Fc={mouseenter:"mouseover",mouseleave:"mouseout"},Uv=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function Hc(e,t){return t&&`${t}::${tf++}`||e.uidEvent||tf++}function Mc(e){const t=Hc(e);return e.uidEvent=t,ns[t]=ns[t]||{},ns[t]}function Xv(e,t){return function n(r){return bl(r,{delegateTarget:e}),n.oneOff&&z.off(e,r.type,t),t.apply(e,[r])}}function Kv(e,t,n){return function r(a){const o=e.querySelectorAll(t);for(let{target:s}=a;s&&s!==this;s=s.parentNode)for(const u of o)if(u===s)return bl(a,{delegateTarget:s}),r.oneOff&&z.off(e,a.type,t,n),n.apply(s,[a])}}function jc(e,t,n=null){return Object.values(e).find(r=>r.callable===t&&r.delegationSelector===n)}function Bc(e,t,n){const r=typeof t=="string",a=r?n:t||n;let o=Wc(e);return Uv.has(o)||(o=e),[r,a,o]}function nf(e,t,n,r,a){if(typeof t!="string"||!e)return;let[o,s,u]=Bc(t,n,r);t in Fc&&(s=(g=>function(S){if(!S.relatedTarget||S.relatedTarget!==S.delegateTarget&&!S.delegateTarget.contains(S.relatedTarget))return g.call(this,S)})(s));const f=Mc(e),d=f[u]||(f[u]={}),h=jc(d,s,o?n:null);if(h){h.oneOff=h.oneOff&&a;return}const b=Hc(s,t.replace(Wv,"")),_=o?Kv(e,n,s):Xv(e,s);_.delegationSelector=o?n:null,_.callable=s,_.oneOff=a,_.uidEvent=b,d[b]=_,e.addEventListener(u,_,o)}function Ls(e,t,n,r,a){const o=jc(t[n],r,a);o&&(e.removeEventListener(n,o,!!a),delete t[n][o.uidEvent])}function Yv(e,t,n,r){const a=t[n]||{};for(const[o,s]of Object.entries(a))o.includes(r)&&Ls(e,t,n,s.callable,s.delegationSelector)}function Wc(e){return e=e.replace(Vv,""),Fc[e]||e}const z={on(e,t,n,r){nf(e,t,n,r,!1)},one(e,t,n,r){nf(e,t,n,r,!0)},off(e,t,n,r){if(typeof t!="string"||!e)return;const[a,o,s]=Bc(t,n,r),u=s!==t,f=Mc(e),d=f[s]||{},h=t.startsWith(".");if(typeof o<"u"){if(!Object.keys(d).length)return;Ls(e,f,s,o,a?n:null);return}if(h)for(const b of Object.keys(f))Yv(e,f,b,t.slice(1));for(const[b,_]of Object.entries(d)){const y=b.replace(qv,"");(!u||t.includes(y))&&Ls(e,f,s,_.callable,_.delegationSelector)}},trigger(e,t,n){if(typeof t!="string"||!e)return null;const r=kc(),a=Wc(t),o=t!==a;let s=null,u=!0,f=!0,d=!1;o&&r&&(s=r.Event(t,n),r(e).trigger(s),u=!s.isPropagationStopped(),f=!s.isImmediatePropagationStopped(),d=s.isDefaultPrevented());const h=bl(new Event(t,{bubbles:u,cancelable:!0}),n);return d&&h.preventDefault(),f&&e.dispatchEvent(h),h.defaultPrevented&&s&&s.preventDefault(),h}};function bl(e,t={}){for(const[n,r]of Object.entries(t))try{e[n]=r}catch{Object.defineProperty(e,n,{configurable:!0,get(){return r}})}return e}function rf(e){if(e==="true")return!0;if(e==="false")return!1;if(e===Number(e).toString())return Number(e);if(e===""||e==="null")return null;if(typeof e!="string")return e;try{return JSON.parse(decodeURIComponent(e))}catch{return e}}function rs(e){return e.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const bn={setDataAttribute(e,t,n){e.setAttribute(`data-bs-${rs(t)}`,n)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${rs(t)}`)},getDataAttributes(e){if(!e)return{};const t={},n=Object.keys(e.dataset).filter(r=>r.startsWith("bs")&&!r.startsWith("bsConfig"));for(const r of n){let a=r.replace(/^bs/,"");a=a.charAt(0).toLowerCase()+a.slice(1,a.length),t[a]=rf(e.dataset[r])}return t},getDataAttribute(e,t){return rf(e.getAttribute(`data-bs-${rs(t)}`))}};class Ea{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,n){const r=mn(n)?bn.getDataAttribute(n,"config"):{};return{...this.constructor.Default,...typeof r=="object"?r:{},...mn(n)?bn.getDataAttributes(n):{},...typeof t=="object"?t:{}}}_typeCheckConfig(t,n=this.constructor.DefaultType){for(const[r,a]of Object.entries(n)){const o=t[r],s=mn(o)?"element":Hv(o);if(!new RegExp(a).test(s))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${r}" provided type "${s}" but expected type "${a}".`)}}}const Gv="5.3.3";class Ut extends Ea{constructor(t,n){super(),t=Rn(t),t&&(this._element=t,this._config=this._getConfig(n),es.set(this._element,this.constructor.DATA_KEY,this))}dispose(){es.remove(this._element,this.constructor.DATA_KEY),z.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,n,r=!0){$c(t,n,r)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return es.get(Rn(t),this.DATA_KEY)}static getOrCreateInstance(t,n={}){return this.getInstance(t)||new this(t,typeof n=="object"?n:null)}static get VERSION(){return Gv}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const as=e=>{let t=e.getAttribute("data-bs-target");if(!t||t==="#"){let n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),t=n&&n!=="#"?n.trim():null}return t?t.split(",").map(n=>Ic(n)).join(","):null},fe={find(e,t=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(t,e))},findOne(e,t=document.documentElement){return Element.prototype.querySelector.call(t,e)},children(e,t){return[].concat(...e.children).filter(n=>n.matches(t))},parents(e,t){const n=[];let r=e.parentNode.closest(t);for(;r;)n.push(r),r=r.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(e){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(n=>`${n}:not([tabindex^="-"])`).join(",");return this.find(t,e).filter(n=>!Pn(n)&&Wr(n))},getSelectorFromElement(e){const t=as(e);return t&&fe.findOne(t)?t:null},getElementFromSelector(e){const t=as(e);return t?fe.findOne(t):null},getMultipleElementsFromSelector(e){const t=as(e);return t?fe.find(t):[]}},ro=(e,t="hide")=>{const n=`click.dismiss${e.EVENT_KEY}`,r=e.NAME;z.on(document,n,`[data-bs-dismiss="${r}"]`,function(a){if(["A","AREA"].includes(this.tagName)&&a.preventDefault(),Pn(this))return;const o=fe.getElementFromSelector(this)||this.closest(`.${r}`);e.getOrCreateInstance(o)[t]()})},zv="alert",Jv="bs.alert",Vc=`.${Jv}`,Qv=`close${Vc}`,Zv=`closed${Vc}`,em="fade",tm="show";class Na extends Ut{static get NAME(){return zv}close(){if(z.trigger(this._element,Qv).defaultPrevented)return;this._element.classList.remove(tm);const n=this._element.classList.contains(em);this._queueCallback(()=>this._destroyElement(),this._element,n)}_destroyElement(){this._element.remove(),z.trigger(this._element,Zv),this.dispose()}static jQueryInterface(t){return this.each(function(){const n=Na.getOrCreateInstance(this);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}ro(Na,"close");Ht(Na);const nm="button",rm="bs.button",am=`.${rm}`,im=".data-api",om="active",af='[data-bs-toggle="button"]',sm=`click${am}${im}`;class La extends Ut{static get NAME(){return nm}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(om))}static jQueryInterface(t){return this.each(function(){const n=La.getOrCreateInstance(this);t==="toggle"&&n[t]()})}}z.on(document,sm,af,e=>{e.preventDefault();const t=e.target.closest(af);La.getOrCreateInstance(t).toggle()});Ht(La);const lm="swipe",Vr=".bs.swipe",um=`touchstart${Vr}`,fm=`touchmove${Vr}`,cm=`touchend${Vr}`,dm=`pointerdown${Vr}`,hm=`pointerup${Vr}`,pm="touch",vm="pen",mm="pointer-event",bm=40,_m={endCallback:null,leftCallback:null,rightCallback:null},gm={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class Mi extends Ea{constructor(t,n){super(),this._element=t,!(!t||!Mi.isSupported())&&(this._config=this._getConfig(n),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents())}static get Default(){return _m}static get DefaultType(){return gm}static get NAME(){return lm}dispose(){z.off(this._element,Vr)}_start(t){if(!this._supportPointerEvents){this._deltaX=t.touches[0].clientX;return}this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX)}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),bt(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=bm)return;const n=t/this._deltaX;this._deltaX=0,n&&bt(n>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(z.on(this._element,dm,t=>this._start(t)),z.on(this._element,hm,t=>this._end(t)),this._element.classList.add(mm)):(z.on(this._element,um,t=>this._start(t)),z.on(this._element,fm,t=>this._move(t)),z.on(this._element,cm,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType===vm||t.pointerType===pm)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const ym="carousel",wm="bs.carousel",$n=`.${wm}`,qc=".data-api",Tm="ArrowLeft",Cm="ArrowRight",Dm=500,sa="next",_r="prev",Sr="left",Si="right",Sm=`slide${$n}`,is=`slid${$n}`,xm=`keydown${$n}`,Am=`mouseenter${$n}`,Em=`mouseleave${$n}`,Nm=`dragstart${$n}`,Lm=`load${$n}${qc}`,Om=`click${$n}${qc}`,Uc="carousel",mi="active",Im="slide",Rm="carousel-item-end",Pm="carousel-item-start",km="carousel-item-next",$m="carousel-item-prev",Xc=".active",Kc=".carousel-item",Fm=Xc+Kc,Hm=".carousel-item img",Mm=".carousel-indicators",jm="[data-bs-slide], [data-bs-slide-to]",Bm='[data-bs-ride="carousel"]',Wm={[Tm]:Si,[Cm]:Sr},Vm={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},qm={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class qr extends Ut{constructor(t,n){super(t,n),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=fe.findOne(Mm,this._element),this._addEventListeners(),this._config.ride===Uc&&this.cycle()}static get Default(){return Vm}static get DefaultType(){return qm}static get NAME(){return ym}next(){this._slide(sa)}nextWhenVisible(){!document.hidden&&Wr(this._element)&&this.next()}prev(){this._slide(_r)}pause(){this._isSliding&&Rc(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(this._config.ride){if(this._isSliding){z.one(this._element,is,()=>this.cycle());return}this.cycle()}}to(t){const n=this._getItems();if(t>n.length-1||t<0)return;if(this._isSliding){z.one(this._element,is,()=>this.to(t));return}const r=this._getItemIndex(this._getActive());if(r===t)return;const a=t>r?sa:_r;this._slide(a,n[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&z.on(this._element,xm,t=>this._keydown(t)),this._config.pause==="hover"&&(z.on(this._element,Am,()=>this.pause()),z.on(this._element,Em,()=>this._maybeEnableCycle())),this._config.touch&&Mi.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const r of fe.find(Hm,this._element))z.on(r,Nm,a=>a.preventDefault());const n={leftCallback:()=>this._slide(this._directionToOrder(Sr)),rightCallback:()=>this._slide(this._directionToOrder(Si)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),Dm+this._config.interval))}};this._swipeHelper=new Mi(this._element,n)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const n=Wm[t.key];n&&(t.preventDefault(),this._slide(this._directionToOrder(n)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const n=fe.findOne(Xc,this._indicatorsElement);n.classList.remove(mi),n.removeAttribute("aria-current");const r=fe.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);r&&(r.classList.add(mi),r.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const n=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=n||this._config.defaultInterval}_slide(t,n=null){if(this._isSliding)return;const r=this._getActive(),a=t===sa,o=n||ml(this._getItems(),r,a,this._config.wrap);if(o===r)return;const s=this._getItemIndex(o),u=y=>z.trigger(this._element,y,{relatedTarget:o,direction:this._orderToDirection(t),from:this._getItemIndex(r),to:s});if(u(Sm).defaultPrevented||!r||!o)return;const d=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(s),this._activeElement=o;const h=a?Pm:Rm,b=a?km:$m;o.classList.add(b),Aa(o),r.classList.add(h),o.classList.add(h);const _=()=>{o.classList.remove(h,b),o.classList.add(mi),r.classList.remove(mi,b,h),this._isSliding=!1,u(is)};this._queueCallback(_,r,this._isAnimated()),d&&this.cycle()}_isAnimated(){return this._element.classList.contains(Im)}_getActive(){return fe.findOne(Fm,this._element)}_getItems(){return fe.find(Kc,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return $t()?t===Sr?_r:sa:t===Sr?sa:_r}_orderToDirection(t){return $t()?t===_r?Sr:Si:t===_r?Si:Sr}static jQueryInterface(t){return this.each(function(){const n=qr.getOrCreateInstance(this,t);if(typeof t=="number"){n.to(t);return}if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}z.on(document,Om,jm,function(e){const t=fe.getElementFromSelector(this);if(!t||!t.classList.contains(Uc))return;e.preventDefault();const n=qr.getOrCreateInstance(t),r=this.getAttribute("data-bs-slide-to");if(r){n.to(r),n._maybeEnableCycle();return}if(bn.getDataAttribute(this,"slide")==="next"){n.next(),n._maybeEnableCycle();return}n.prev(),n._maybeEnableCycle()});z.on(window,Lm,()=>{const e=fe.find(Bm);for(const t of e)qr.getOrCreateInstance(t)});Ht(qr);const Um="collapse",Xm="bs.collapse",Oa=`.${Xm}`,Km=".data-api",Ym=`show${Oa}`,Gm=`shown${Oa}`,zm=`hide${Oa}`,Jm=`hidden${Oa}`,Qm=`click${Oa}${Km}`,os="show",Er="collapse",bi="collapsing",Zm="collapsed",eb=`:scope .${Er} .${Er}`,tb="collapse-horizontal",nb="width",rb="height",ab=".collapse.show, .collapse.collapsing",Os='[data-bs-toggle="collapse"]',ib={parent:null,toggle:!0},ob={parent:"(null|element)",toggle:"boolean"};class Fr extends Ut{constructor(t,n){super(t,n),this._isTransitioning=!1,this._triggerArray=[];const r=fe.find(Os);for(const a of r){const o=fe.getSelectorFromElement(a),s=fe.find(o).filter(u=>u===this._element);o!==null&&s.length&&this._triggerArray.push(a)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return ib}static get DefaultType(){return ob}static get NAME(){return Um}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(ab).filter(u=>u!==this._element).map(u=>Fr.getOrCreateInstance(u,{toggle:!1}))),t.length&&t[0]._isTransitioning||z.trigger(this._element,Ym).defaultPrevented)return;for(const u of t)u.hide();const r=this._getDimension();this._element.classList.remove(Er),this._element.classList.add(bi),this._element.style[r]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const a=()=>{this._isTransitioning=!1,this._element.classList.remove(bi),this._element.classList.add(Er,os),this._element.style[r]="",z.trigger(this._element,Gm)},s=`scroll${r[0].toUpperCase()+r.slice(1)}`;this._queueCallback(a,this._element,!0),this._element.style[r]=`${this._element[s]}px`}hide(){if(this._isTransitioning||!this._isShown()||z.trigger(this._element,zm).defaultPrevented)return;const n=this._getDimension();this._element.style[n]=`${this._element.getBoundingClientRect()[n]}px`,Aa(this._element),this._element.classList.add(bi),this._element.classList.remove(Er,os);for(const a of this._triggerArray){const o=fe.getElementFromSelector(a);o&&!this._isShown(o)&&this._addAriaAndCollapsedClass([a],!1)}this._isTransitioning=!0;const r=()=>{this._isTransitioning=!1,this._element.classList.remove(bi),this._element.classList.add(Er),z.trigger(this._element,Jm)};this._element.style[n]="",this._queueCallback(r,this._element,!0)}_isShown(t=this._element){return t.classList.contains(os)}_configAfterMerge(t){return t.toggle=!!t.toggle,t.parent=Rn(t.parent),t}_getDimension(){return this._element.classList.contains(tb)?nb:rb}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(Os);for(const n of t){const r=fe.getElementFromSelector(n);r&&this._addAriaAndCollapsedClass([n],this._isShown(r))}}_getFirstLevelChildren(t){const n=fe.find(eb,this._config.parent);return fe.find(t,this._config.parent).filter(r=>!n.includes(r))}_addAriaAndCollapsedClass(t,n){if(t.length)for(const r of t)r.classList.toggle(Zm,!n),r.setAttribute("aria-expanded",n)}static jQueryInterface(t){const n={};return typeof t=="string"&&/show|hide/.test(t)&&(n.toggle=!1),this.each(function(){const r=Fr.getOrCreateInstance(this,n);if(typeof t=="string"){if(typeof r[t]>"u")throw new TypeError(`No method named "${t}"`);r[t]()}})}}z.on(document,Qm,Os,function(e){(e.target.tagName==="A"||e.delegateTarget&&e.delegateTarget.tagName==="A")&&e.preventDefault();for(const t of fe.getMultipleElementsFromSelector(this))Fr.getOrCreateInstance(t,{toggle:!1}).toggle()});Ht(Fr);const of="dropdown",sb="bs.dropdown",ar=`.${sb}`,_l=".data-api",lb="Escape",sf="Tab",ub="ArrowUp",lf="ArrowDown",fb=2,cb=`hide${ar}`,db=`hidden${ar}`,hb=`show${ar}`,pb=`shown${ar}`,Yc=`click${ar}${_l}`,Gc=`keydown${ar}${_l}`,vb=`keyup${ar}${_l}`,xr="show",mb="dropup",bb="dropend",_b="dropstart",gb="dropup-center",yb="dropdown-center",Gn='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',wb=`${Gn}.${xr}`,xi=".dropdown-menu",Tb=".navbar",Cb=".navbar-nav",Db=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",Sb=$t()?"top-end":"top-start",xb=$t()?"top-start":"top-end",Ab=$t()?"bottom-end":"bottom-start",Eb=$t()?"bottom-start":"bottom-end",Nb=$t()?"left-start":"right-start",Lb=$t()?"right-start":"left-start",Ob="top",Ib="bottom",Rb={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Pb={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class qt extends Ut{constructor(t,n){super(t,n),this._popper=null,this._parent=this._element.parentNode,this._menu=fe.next(this._element,xi)[0]||fe.prev(this._element,xi)[0]||fe.findOne(xi,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Rb}static get DefaultType(){return Pb}static get NAME(){return of}toggle(){return this._isShown()?this.hide():this.show()}show(){if(Pn(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!z.trigger(this._element,hb,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(Cb))for(const r of[].concat(...document.body.children))z.on(r,"mouseover",Hi);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(xr),this._element.classList.add(xr),z.trigger(this._element,pb,t)}}hide(){if(Pn(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!z.trigger(this._element,cb,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const r of[].concat(...document.body.children))z.off(r,"mouseover",Hi);this._popper&&this._popper.destroy(),this._menu.classList.remove(xr),this._element.classList.remove(xr),this._element.setAttribute("aria-expanded","false"),bn.removeDataAttribute(this._menu,"popper"),z.trigger(this._element,db,t)}}_getConfig(t){if(t=super._getConfig(t),typeof t.reference=="object"&&!mn(t.reference)&&typeof t.reference.getBoundingClientRect!="function")throw new TypeError(`${of.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(typeof Oc>"u")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;this._config.reference==="parent"?t=this._parent:mn(this._config.reference)?t=Rn(this._config.reference):typeof this._config.reference=="object"&&(t=this._config.reference);const n=this._getPopperConfig();this._popper=vl(t,this._menu,n)}_isShown(){return this._menu.classList.contains(xr)}_getPlacement(){const t=this._parent;if(t.classList.contains(bb))return Nb;if(t.classList.contains(_b))return Lb;if(t.classList.contains(gb))return Ob;if(t.classList.contains(yb))return Ib;const n=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return t.classList.contains(mb)?n?xb:Sb:n?Eb:Ab}_detectNavbar(){return this._element.closest(Tb)!==null}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(n=>Number.parseInt(n,10)):typeof t=="function"?n=>t(n,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(bn.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...bt(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:n}){const r=fe.find(Db,this._menu).filter(a=>Wr(a));r.length&&ml(r,n,t===lf,!r.includes(n)).focus()}static jQueryInterface(t){return this.each(function(){const n=qt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}static clearMenus(t){if(t.button===fb||t.type==="keyup"&&t.key!==sf)return;const n=fe.find(wb);for(const r of n){const a=qt.getInstance(r);if(!a||a._config.autoClose===!1)continue;const o=t.composedPath(),s=o.includes(a._menu);if(o.includes(a._element)||a._config.autoClose==="inside"&&!s||a._config.autoClose==="outside"&&s||a._menu.contains(t.target)&&(t.type==="keyup"&&t.key===sf||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const u={relatedTarget:a._element};t.type==="click"&&(u.clickEvent=t),a._completeHide(u)}}static dataApiKeydownHandler(t){const n=/input|textarea/i.test(t.target.tagName),r=t.key===lb,a=[ub,lf].includes(t.key);if(!a&&!r||n&&!r)return;t.preventDefault();const o=this.matches(Gn)?this:fe.prev(this,Gn)[0]||fe.next(this,Gn)[0]||fe.findOne(Gn,t.delegateTarget.parentNode),s=qt.getOrCreateInstance(o);if(a){t.stopPropagation(),s.show(),s._selectMenuItem(t);return}s._isShown()&&(t.stopPropagation(),s.hide(),o.focus())}}z.on(document,Gc,Gn,qt.dataApiKeydownHandler);z.on(document,Gc,xi,qt.dataApiKeydownHandler);z.on(document,Yc,qt.clearMenus);z.on(document,vb,qt.clearMenus);z.on(document,Yc,Gn,function(e){e.preventDefault(),qt.getOrCreateInstance(this).toggle()});Ht(qt);const zc="backdrop",kb="fade",uf="show",ff=`mousedown.bs.${zc}`,$b={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Fb={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Jc extends Ea{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return $b}static get DefaultType(){return Fb}static get NAME(){return zc}show(t){if(!this._config.isVisible){bt(t);return}this._append();const n=this._getElement();this._config.isAnimated&&Aa(n),n.classList.add(uf),this._emulateAnimation(()=>{bt(t)})}hide(t){if(!this._config.isVisible){bt(t);return}this._getElement().classList.remove(uf),this._emulateAnimation(()=>{this.dispose(),bt(t)})}dispose(){this._isAppended&&(z.off(this._element,ff),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(kb),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=Rn(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),z.on(t,ff,()=>{bt(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){$c(t,this._getElement(),this._config.isAnimated)}}const Hb="focustrap",Mb="bs.focustrap",ji=`.${Mb}`,jb=`focusin${ji}`,Bb=`keydown.tab${ji}`,Wb="Tab",Vb="forward",cf="backward",qb={autofocus:!0,trapElement:null},Ub={autofocus:"boolean",trapElement:"element"};class Qc extends Ea{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return qb}static get DefaultType(){return Ub}static get NAME(){return Hb}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),z.off(document,ji),z.on(document,jb,t=>this._handleFocusin(t)),z.on(document,Bb,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,z.off(document,ji))}_handleFocusin(t){const{trapElement:n}=this._config;if(t.target===document||t.target===n||n.contains(t.target))return;const r=fe.focusableChildren(n);r.length===0?n.focus():this._lastTabNavDirection===cf?r[r.length-1].focus():r[0].focus()}_handleKeydown(t){t.key===Wb&&(this._lastTabNavDirection=t.shiftKey?cf:Vb)}}const df=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",hf=".sticky-top",_i="padding-right",pf="margin-right";class Is{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,_i,n=>n+t),this._setElementAttributes(df,_i,n=>n+t),this._setElementAttributes(hf,pf,n=>n-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,_i),this._resetElementAttributes(df,_i),this._resetElementAttributes(hf,pf)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,n,r){const a=this.getWidth(),o=s=>{if(s!==this._element&&window.innerWidth>s.clientWidth+a)return;this._saveInitialAttribute(s,n);const u=window.getComputedStyle(s).getPropertyValue(n);s.style.setProperty(n,`${r(Number.parseFloat(u))}px`)};this._applyManipulationCallback(t,o)}_saveInitialAttribute(t,n){const r=t.style.getPropertyValue(n);r&&bn.setDataAttribute(t,n,r)}_resetElementAttributes(t,n){const r=a=>{const o=bn.getDataAttribute(a,n);if(o===null){a.style.removeProperty(n);return}bn.removeDataAttribute(a,n),a.style.setProperty(n,o)};this._applyManipulationCallback(t,r)}_applyManipulationCallback(t,n){if(mn(t)){n(t);return}for(const r of fe.find(t,this._element))n(r)}}const Xb="modal",Kb="bs.modal",Ft=`.${Kb}`,Yb=".data-api",Gb="Escape",zb=`hide${Ft}`,Jb=`hidePrevented${Ft}`,Zc=`hidden${Ft}`,ed=`show${Ft}`,Qb=`shown${Ft}`,Zb=`resize${Ft}`,e_=`click.dismiss${Ft}`,t_=`mousedown.dismiss${Ft}`,n_=`keydown.dismiss${Ft}`,r_=`click${Ft}${Yb}`,vf="modal-open",a_="fade",mf="show",ss="modal-static",i_=".modal.show",o_=".modal-dialog",s_=".modal-body",l_='[data-bs-toggle="modal"]',u_={backdrop:!0,focus:!0,keyboard:!0},f_={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class er extends Ut{constructor(t,n){super(t,n),this._dialog=fe.findOne(o_,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Is,this._addEventListeners()}static get Default(){return u_}static get DefaultType(){return f_}static get NAME(){return Xb}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||z.trigger(this._element,ed,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(vf),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){!this._isShown||this._isTransitioning||z.trigger(this._element,zb).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(mf),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){z.off(window,Ft),z.off(this._dialog,Ft),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Jc({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new Qc({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const n=fe.findOne(s_,this._dialog);n&&(n.scrollTop=0),Aa(this._element),this._element.classList.add(mf);const r=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,z.trigger(this._element,Qb,{relatedTarget:t})};this._queueCallback(r,this._dialog,this._isAnimated())}_addEventListeners(){z.on(this._element,n_,t=>{if(t.key===Gb){if(this._config.keyboard){this.hide();return}this._triggerBackdropTransition()}}),z.on(window,Zb,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),z.on(this._element,t_,t=>{z.one(this._element,e_,n=>{if(!(this._element!==t.target||this._element!==n.target)){if(this._config.backdrop==="static"){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(vf),this._resetAdjustments(),this._scrollBar.reset(),z.trigger(this._element,Zc)})}_isAnimated(){return this._element.classList.contains(a_)}_triggerBackdropTransition(){if(z.trigger(this._element,Jb).defaultPrevented)return;const n=this._element.scrollHeight>document.documentElement.clientHeight,r=this._element.style.overflowY;r==="hidden"||this._element.classList.contains(ss)||(n||(this._element.style.overflowY="hidden"),this._element.classList.add(ss),this._queueCallback(()=>{this._element.classList.remove(ss),this._queueCallback(()=>{this._element.style.overflowY=r},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,n=this._scrollBar.getWidth(),r=n>0;if(r&&!t){const a=$t()?"paddingLeft":"paddingRight";this._element.style[a]=`${n}px`}if(!r&&t){const a=$t()?"paddingRight":"paddingLeft";this._element.style[a]=`${n}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,n){return this.each(function(){const r=er.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof r[t]>"u")throw new TypeError(`No method named "${t}"`);r[t](n)}})}}z.on(document,r_,l_,function(e){const t=fe.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),z.one(t,ed,a=>{a.defaultPrevented||z.one(t,Zc,()=>{Wr(this)&&this.focus()})});const n=fe.findOne(i_);n&&er.getInstance(n).hide(),er.getOrCreateInstance(t).toggle(this)});ro(er);Ht(er);const c_="offcanvas",d_="bs.offcanvas",Tn=`.${d_}`,td=".data-api",h_=`load${Tn}${td}`,p_="Escape",bf="show",_f="showing",gf="hiding",v_="offcanvas-backdrop",nd=".offcanvas.show",m_=`show${Tn}`,b_=`shown${Tn}`,__=`hide${Tn}`,yf=`hidePrevented${Tn}`,rd=`hidden${Tn}`,g_=`resize${Tn}`,y_=`click${Tn}${td}`,w_=`keydown.dismiss${Tn}`,T_='[data-bs-toggle="offcanvas"]',C_={backdrop:!0,keyboard:!0,scroll:!1},D_={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class yn extends Ut{constructor(t,n){super(t,n),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return C_}static get DefaultType(){return D_}static get NAME(){return c_}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||z.trigger(this._element,m_,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||new Is().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(_f);const r=()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(bf),this._element.classList.remove(_f),z.trigger(this._element,b_,{relatedTarget:t})};this._queueCallback(r,this._element,!0)}hide(){if(!this._isShown||z.trigger(this._element,__).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(gf),this._backdrop.hide();const n=()=>{this._element.classList.remove(bf,gf),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new Is().reset(),z.trigger(this._element,rd)};this._queueCallback(n,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=()=>{if(this._config.backdrop==="static"){z.trigger(this._element,yf);return}this.hide()},n=!!this._config.backdrop;return new Jc({className:v_,isVisible:n,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:n?t:null})}_initializeFocusTrap(){return new Qc({trapElement:this._element})}_addEventListeners(){z.on(this._element,w_,t=>{if(t.key===p_){if(this._config.keyboard){this.hide();return}z.trigger(this._element,yf)}})}static jQueryInterface(t){return this.each(function(){const n=yn.getOrCreateInstance(this,t);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}z.on(document,y_,T_,function(e){const t=fe.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),Pn(this))return;z.one(t,rd,()=>{Wr(this)&&this.focus()});const n=fe.findOne(nd);n&&n!==t&&yn.getInstance(n).hide(),yn.getOrCreateInstance(t).toggle(this)});z.on(window,h_,()=>{for(const e of fe.find(nd))yn.getOrCreateInstance(e).show()});z.on(window,g_,()=>{for(const e of fe.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(e).position!=="fixed"&&yn.getOrCreateInstance(e).hide()});ro(yn);Ht(yn);const S_=/^aria-[\w-]*$/i,ad={"*":["class","dir","id","lang","role",S_],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},x_=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),A_=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,E_=(e,t)=>{const n=e.nodeName.toLowerCase();return t.includes(n)?x_.has(n)?!!A_.test(e.nodeValue):!0:t.filter(r=>r instanceof RegExp).some(r=>r.test(n))};function N_(e,t,n){if(!e.length)return e;if(n&&typeof n=="function")return n(e);const a=new window.DOMParser().parseFromString(e,"text/html"),o=[].concat(...a.body.querySelectorAll("*"));for(const s of o){const u=s.nodeName.toLowerCase();if(!Object.keys(t).includes(u)){s.remove();continue}const f=[].concat(...s.attributes),d=[].concat(t["*"]||[],t[u]||[]);for(const h of f)E_(h,d)||s.removeAttribute(h.nodeName)}return a.body.innerHTML}const L_="TemplateFactory",O_={allowList:ad,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},I_={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},R_={entry:"(string|element|function|null)",selector:"(string|element)"};class P_ extends Ea{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return O_}static get DefaultType(){return I_}static get NAME(){return L_}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[a,o]of Object.entries(this._config.content))this._setContent(t,o,a);const n=t.children[0],r=this._resolvePossibleFunction(this._config.extraClass);return r&&n.classList.add(...r.split(" ")),n}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[n,r]of Object.entries(t))super._typeCheckConfig({selector:n,entry:r},R_)}_setContent(t,n,r){const a=fe.findOne(r,t);if(a){if(n=this._resolvePossibleFunction(n),!n){a.remove();return}if(mn(n)){this._putElementInTemplate(Rn(n),a);return}if(this._config.html){a.innerHTML=this._maybeSanitize(n);return}a.textContent=n}}_maybeSanitize(t){return this._config.sanitize?N_(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return bt(t,[this])}_putElementInTemplate(t,n){if(this._config.html){n.innerHTML="",n.append(t);return}n.textContent=t.textContent}}const k_="tooltip",$_=new Set(["sanitize","allowList","sanitizeFn"]),ls="fade",F_="modal",gi="show",H_=".tooltip-inner",wf=`.${F_}`,Tf="hide.bs.modal",la="hover",us="focus",M_="click",j_="manual",B_="hide",W_="hidden",V_="show",q_="shown",U_="inserted",X_="click",K_="focusin",Y_="focusout",G_="mouseenter",z_="mouseleave",J_={AUTO:"auto",TOP:"top",RIGHT:$t()?"left":"right",BOTTOM:"bottom",LEFT:$t()?"right":"left"},Q_={allowList:ad,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},Z_={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class ir extends Ut{constructor(t,n){if(typeof Oc>"u")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,n),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Q_}static get DefaultType(){return Z_}static get NAME(){return k_}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),z.off(this._element.closest(wf),Tf,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;const t=z.trigger(this._element,this.constructor.eventName(V_)),r=(Pc(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!r)return;this._disposePopper();const a=this._getTipElement();this._element.setAttribute("aria-describedby",a.getAttribute("id"));const{container:o}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(o.append(a),z.trigger(this._element,this.constructor.eventName(U_))),this._popper=this._createPopper(a),a.classList.add(gi),"ontouchstart"in document.documentElement)for(const u of[].concat(...document.body.children))z.on(u,"mouseover",Hi);const s=()=>{z.trigger(this._element,this.constructor.eventName(q_)),this._isHovered===!1&&this._leave(),this._isHovered=!1};this._queueCallback(s,this.tip,this._isAnimated())}hide(){if(!this._isShown()||z.trigger(this._element,this.constructor.eventName(B_)).defaultPrevented)return;if(this._getTipElement().classList.remove(gi),"ontouchstart"in document.documentElement)for(const a of[].concat(...document.body.children))z.off(a,"mouseover",Hi);this._activeTrigger[M_]=!1,this._activeTrigger[us]=!1,this._activeTrigger[la]=!1,this._isHovered=null;const r=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),z.trigger(this._element,this.constructor.eventName(W_)))};this._queueCallback(r,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const n=this._getTemplateFactory(t).toHtml();if(!n)return null;n.classList.remove(ls,gi),n.classList.add(`bs-${this.constructor.NAME}-auto`);const r=Mv(this.constructor.NAME).toString();return n.setAttribute("id",r),this._isAnimated()&&n.classList.add(ls),n}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new P_({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[H_]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(ls)}_isShown(){return this.tip&&this.tip.classList.contains(gi)}_createPopper(t){const n=bt(this._config.placement,[this,t,this._element]),r=J_[n.toUpperCase()];return vl(this._element,t,this._getPopperConfig(r))}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(n=>Number.parseInt(n,10)):typeof t=="function"?n=>t(n,this._element):t}_resolvePossibleFunction(t){return bt(t,[this._element])}_getPopperConfig(t){const n={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:r=>{this._getTipElement().setAttribute("data-popper-placement",r.state.placement)}}]};return{...n,...bt(this._config.popperConfig,[n])}}_setListeners(){const t=this._config.trigger.split(" ");for(const n of t)if(n==="click")z.on(this._element,this.constructor.eventName(X_),this._config.selector,r=>{this._initializeOnDelegatedTarget(r).toggle()});else if(n!==j_){const r=n===la?this.constructor.eventName(G_):this.constructor.eventName(K_),a=n===la?this.constructor.eventName(z_):this.constructor.eventName(Y_);z.on(this._element,r,this._config.selector,o=>{const s=this._initializeOnDelegatedTarget(o);s._activeTrigger[o.type==="focusin"?us:la]=!0,s._enter()}),z.on(this._element,a,this._config.selector,o=>{const s=this._initializeOnDelegatedTarget(o);s._activeTrigger[o.type==="focusout"?us:la]=s._element.contains(o.relatedTarget),s._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},z.on(this._element.closest(wf),Tf,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(!this._element.getAttribute("aria-label")&&!this._element.textContent.trim()&&this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,n){clearTimeout(this._timeout),this._timeout=setTimeout(t,n)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const n=bn.getDataAttributes(this._element);for(const r of Object.keys(n))$_.has(r)&&delete n[r];return t={...n,...typeof t=="object"&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:Rn(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[n,r]of Object.entries(this._config))this.constructor.Default[n]!==r&&(t[n]=r);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const n=ir.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}}Ht(ir);const eg="popover",tg=".popover-header",ng=".popover-body",rg={...ir.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},ag={...ir.DefaultType,content:"(null|string|element|function)"};class ao extends ir{static get Default(){return rg}static get DefaultType(){return ag}static get NAME(){return eg}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[tg]:this._getTitle(),[ng]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const n=ao.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}}Ht(ao);const ig="scrollspy",og="bs.scrollspy",gl=`.${og}`,sg=".data-api",lg=`activate${gl}`,Cf=`click${gl}`,ug=`load${gl}${sg}`,fg="dropdown-item",gr="active",cg='[data-bs-spy="scroll"]',fs="[href]",dg=".nav, .list-group",Df=".nav-link",hg=".nav-item",pg=".list-group-item",vg=`${Df}, ${hg} > ${Df}, ${pg}`,mg=".dropdown",bg=".dropdown-toggle",_g={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},gg={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Ia extends Ut{constructor(t,n){super(t,n),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return _g}static get DefaultType(){return gg}static get NAME(){return ig}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=Rn(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(n=>Number.parseFloat(n))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(z.off(this._config.target,Cf),z.on(this._config.target,Cf,fs,t=>{const n=this._observableSections.get(t.target.hash);if(n){t.preventDefault();const r=this._rootElement||window,a=n.offsetTop-this._element.offsetTop;if(r.scrollTo){r.scrollTo({top:a,behavior:"smooth"});return}r.scrollTop=a}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(n=>this._observerCallback(n),t)}_observerCallback(t){const n=s=>this._targetLinks.get(`#${s.target.id}`),r=s=>{this._previousScrollData.visibleEntryTop=s.target.offsetTop,this._process(n(s))},a=(this._rootElement||document.documentElement).scrollTop,o=a>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=a;for(const s of t){if(!s.isIntersecting){this._activeTarget=null,this._clearActiveClass(n(s));continue}const u=s.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(o&&u){if(r(s),!a)return;continue}!o&&!u&&r(s)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=fe.find(fs,this._config.target);for(const n of t){if(!n.hash||Pn(n))continue;const r=fe.findOne(decodeURI(n.hash),this._element);Wr(r)&&(this._targetLinks.set(decodeURI(n.hash),n),this._observableSections.set(n.hash,r))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(gr),this._activateParents(t),z.trigger(this._element,lg,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(fg)){fe.findOne(bg,t.closest(mg)).classList.add(gr);return}for(const n of fe.parents(t,dg))for(const r of fe.prev(n,vg))r.classList.add(gr)}_clearActiveClass(t){t.classList.remove(gr);const n=fe.find(`${fs}.${gr}`,t);for(const r of n)r.classList.remove(gr)}static jQueryInterface(t){return this.each(function(){const n=Ia.getOrCreateInstance(this,t);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}z.on(window,ug,()=>{for(const e of fe.find(cg))Ia.getOrCreateInstance(e)});Ht(Ia);const yg="tab",wg="bs.tab",or=`.${wg}`,Tg=`hide${or}`,Cg=`hidden${or}`,Dg=`show${or}`,Sg=`shown${or}`,xg=`click${or}`,Ag=`keydown${or}`,Eg=`load${or}`,Ng="ArrowLeft",Sf="ArrowRight",Lg="ArrowUp",xf="ArrowDown",cs="Home",Af="End",zn="active",Ef="fade",ds="show",Og="dropdown",id=".dropdown-toggle",Ig=".dropdown-menu",hs=`:not(${id})`,Rg='.list-group, .nav, [role="tablist"]',Pg=".nav-item, .list-group-item",kg=`.nav-link${hs}, .list-group-item${hs}, [role="tab"]${hs}`,od='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',ps=`${kg}, ${od}`,$g=`.${zn}[data-bs-toggle="tab"], .${zn}[data-bs-toggle="pill"], .${zn}[data-bs-toggle="list"]`;class tr extends Ut{constructor(t){super(t),this._parent=this._element.closest(Rg),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),z.on(this._element,Ag,n=>this._keydown(n)))}static get NAME(){return yg}show(){const t=this._element;if(this._elemIsActive(t))return;const n=this._getActiveElem(),r=n?z.trigger(n,Tg,{relatedTarget:t}):null;z.trigger(t,Dg,{relatedTarget:n}).defaultPrevented||r&&r.defaultPrevented||(this._deactivate(n,t),this._activate(t,n))}_activate(t,n){if(!t)return;t.classList.add(zn),this._activate(fe.getElementFromSelector(t));const r=()=>{if(t.getAttribute("role")!=="tab"){t.classList.add(ds);return}t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),z.trigger(t,Sg,{relatedTarget:n})};this._queueCallback(r,t,t.classList.contains(Ef))}_deactivate(t,n){if(!t)return;t.classList.remove(zn),t.blur(),this._deactivate(fe.getElementFromSelector(t));const r=()=>{if(t.getAttribute("role")!=="tab"){t.classList.remove(ds);return}t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),z.trigger(t,Cg,{relatedTarget:n})};this._queueCallback(r,t,t.classList.contains(Ef))}_keydown(t){if(![Ng,Sf,Lg,xf,cs,Af].includes(t.key))return;t.stopPropagation(),t.preventDefault();const n=this._getChildren().filter(a=>!Pn(a));let r;if([cs,Af].includes(t.key))r=n[t.key===cs?0:n.length-1];else{const a=[Sf,xf].includes(t.key);r=ml(n,t.target,a,!0)}r&&(r.focus({preventScroll:!0}),tr.getOrCreateInstance(r).show())}_getChildren(){return fe.find(ps,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,n){this._setAttributeIfNotExists(t,"role","tablist");for(const r of n)this._setInitialAttributesOnChild(r)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const n=this._elemIsActive(t),r=this._getOuterElement(t);t.setAttribute("aria-selected",n),r!==t&&this._setAttributeIfNotExists(r,"role","presentation"),n||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const n=fe.getElementFromSelector(t);n&&(this._setAttributeIfNotExists(n,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(n,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,n){const r=this._getOuterElement(t);if(!r.classList.contains(Og))return;const a=(o,s)=>{const u=fe.findOne(o,r);u&&u.classList.toggle(s,n)};a(id,zn),a(Ig,ds),r.setAttribute("aria-expanded",n)}_setAttributeIfNotExists(t,n,r){t.hasAttribute(n)||t.setAttribute(n,r)}_elemIsActive(t){return t.classList.contains(zn)}_getInnerElement(t){return t.matches(ps)?t:fe.findOne(ps,t)}_getOuterElement(t){return t.closest(Pg)||t}static jQueryInterface(t){return this.each(function(){const n=tr.getOrCreateInstance(this);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}z.on(document,xg,od,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),!Pn(this)&&tr.getOrCreateInstance(this).show()});z.on(window,Eg,()=>{for(const e of fe.find($g))tr.getOrCreateInstance(e)});Ht(tr);const Fg="toast",Hg="bs.toast",Fn=`.${Hg}`,Mg=`mouseover${Fn}`,jg=`mouseout${Fn}`,Bg=`focusin${Fn}`,Wg=`focusout${Fn}`,Vg=`hide${Fn}`,qg=`hidden${Fn}`,Ug=`show${Fn}`,Xg=`shown${Fn}`,Kg="fade",Nf="hide",yi="show",wi="showing",Yg={animation:"boolean",autohide:"boolean",delay:"number"},Gg={animation:!0,autohide:!0,delay:5e3};class Ra extends Ut{constructor(t,n){super(t,n),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Gg}static get DefaultType(){return Yg}static get NAME(){return Fg}show(){if(z.trigger(this._element,Ug).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(Kg);const n=()=>{this._element.classList.remove(wi),z.trigger(this._element,Xg),this._maybeScheduleHide()};this._element.classList.remove(Nf),Aa(this._element),this._element.classList.add(yi,wi),this._queueCallback(n,this._element,this._config.animation)}hide(){if(!this.isShown()||z.trigger(this._element,Vg).defaultPrevented)return;const n=()=>{this._element.classList.add(Nf),this._element.classList.remove(wi,yi),z.trigger(this._element,qg)};this._element.classList.add(wi),this._queueCallback(n,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(yi),super.dispose()}isShown(){return this._element.classList.contains(yi)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay)))}_onInteraction(t,n){switch(t.type){case"mouseover":case"mouseout":{this._hasMouseInteraction=n;break}case"focusin":case"focusout":{this._hasKeyboardInteraction=n;break}}if(n){this._clearTimeout();return}const r=t.relatedTarget;this._element===r||this._element.contains(r)||this._maybeScheduleHide()}_setListeners(){z.on(this._element,Mg,t=>this._onInteraction(t,!0)),z.on(this._element,jg,t=>this._onInteraction(t,!1)),z.on(this._element,Bg,t=>this._onInteraction(t,!0)),z.on(this._element,Wg,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const n=Ra.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}ro(Ra);Ht(Ra);const zg=Object.freeze(Object.defineProperty({__proto__:null,Alert:Na,Button:La,Carousel:qr,Collapse:Fr,Dropdown:qt,Modal:er,Offcanvas:yn,Popover:ao,ScrollSpy:Ia,Tab:tr,Toast:Ra,Tooltip:ir},Symbol.toStringTag,{value:"Module"}));function sd(e,t){return function(){return e.apply(t,arguments)}}const{toString:Jg}=Object.prototype,{getPrototypeOf:yl}=Object,io=(e=>t=>{const n=Jg.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Cn=e=>(e=e.toLowerCase(),t=>io(t)===e),oo=e=>t=>typeof t===e,{isArray:Ur}=Array,wa=oo("undefined");function Qg(e){return e!==null&&!wa(e)&&e.constructor!==null&&!wa(e.constructor)&&wn(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const ld=Cn("ArrayBuffer");function Zg(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&ld(e.buffer),t}const ey=oo("string"),wn=oo("function"),ud=oo("number"),wl=e=>e!==null&&typeof e=="object",ty=e=>e===!0||e===!1,Ai=e=>{if(io(e)!=="object")return!1;const t=yl(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},ny=Cn("Date"),ry=Cn("File"),ay=Cn("Blob"),iy=Cn("FileList"),oy=e=>wl(e)&&wn(e.pipe),sy=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||wn(e.append)&&((t=io(e))==="formdata"||t==="object"&&wn(e.toString)&&e.toString()==="[object FormData]"))},ly=Cn("URLSearchParams"),uy=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Pa(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,a;if(typeof e!="object"&&(e=[e]),Ur(e))for(r=0,a=e.length;r<a;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let u;for(r=0;r<s;r++)u=o[r],t.call(null,e[u],u,e)}}function fd(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,a;for(;r-- >0;)if(a=n[r],t===a.toLowerCase())return a;return null}const cd=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),dd=e=>!wa(e)&&e!==cd;function Rs(){const{caseless:e}=dd(this)&&this||{},t={},n=(r,a)=>{const o=e&&fd(t,a)||a;Ai(t[o])&&Ai(r)?t[o]=Rs(t[o],r):Ai(r)?t[o]=Rs({},r):Ur(r)?t[o]=r.slice():t[o]=r};for(let r=0,a=arguments.length;r<a;r++)arguments[r]&&Pa(arguments[r],n);return t}const fy=(e,t,n,{allOwnKeys:r}={})=>(Pa(t,(a,o)=>{n&&wn(a)?e[o]=sd(a,n):e[o]=a},{allOwnKeys:r}),e),cy=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),dy=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},hy=(e,t,n,r)=>{let a,o,s;const u={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)s=a[o],(!r||r(s,e,t))&&!u[s]&&(t[s]=e[s],u[s]=!0);e=n!==!1&&yl(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},py=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},vy=e=>{if(!e)return null;if(Ur(e))return e;let t=e.length;if(!ud(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},my=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&yl(Uint8Array)),by=(e,t)=>{const r=(e&&e[Symbol.iterator]).call(e);let a;for(;(a=r.next())&&!a.done;){const o=a.value;t.call(e,o[0],o[1])}},_y=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},gy=Cn("HTMLFormElement"),yy=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,a){return r.toUpperCase()+a}),Lf=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),wy=Cn("RegExp"),hd=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Pa(n,(a,o)=>{t(a,o,e)!==!1&&(r[o]=a)}),Object.defineProperties(e,r)},Ty=e=>{hd(e,(t,n)=>{if(wn(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(wn(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Cy=(e,t)=>{const n={},r=a=>{a.forEach(o=>{n[o]=!0})};return Ur(e)?r(e):r(String(e).split(t)),n},Dy=()=>{},Sy=(e,t)=>(e=+e,Number.isFinite(e)?e:t),vs="abcdefghijklmnopqrstuvwxyz",Of="0123456789",pd={DIGIT:Of,ALPHA:vs,ALPHA_DIGIT:vs+vs.toUpperCase()+Of},xy=(e=16,t=pd.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n};function Ay(e){return!!(e&&wn(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const Ey=e=>{const t=new Array(10),n=(r,a)=>{if(wl(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[a]=r;const o=Ur(r)?[]:{};return Pa(r,(s,u)=>{const f=n(s,a+1);!wa(f)&&(o[u]=f)}),t[a]=void 0,o}}return r};return n(e,0)},U={isArray:Ur,isArrayBuffer:ld,isBuffer:Qg,isFormData:sy,isArrayBufferView:Zg,isString:ey,isNumber:ud,isBoolean:ty,isObject:wl,isPlainObject:Ai,isUndefined:wa,isDate:ny,isFile:ry,isBlob:ay,isRegExp:wy,isFunction:wn,isStream:oy,isURLSearchParams:ly,isTypedArray:my,isFileList:iy,forEach:Pa,merge:Rs,extend:fy,trim:uy,stripBOM:cy,inherits:dy,toFlatObject:hy,kindOf:io,kindOfTest:Cn,endsWith:py,toArray:vy,forEachEntry:by,matchAll:_y,isHTMLForm:gy,hasOwnProperty:Lf,hasOwnProp:Lf,reduceDescriptors:hd,freezeMethods:Ty,toObjectSet:Cy,toCamelCase:yy,noop:Dy,toFiniteNumber:Sy,findKey:fd,global:cd,isContextDefined:dd,ALPHABET:pd,generateString:xy,isSpecCompliantForm:Ay,toJSONObject:Ey};function Re(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a)}U.inherits(Re,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:U.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const vd=Re.prototype,md={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{md[e]={value:e}});Object.defineProperties(Re,md);Object.defineProperty(vd,"isAxiosError",{value:!0});Re.from=(e,t,n,r,a,o)=>{const s=Object.create(vd);return U.toFlatObject(e,s,function(f){return f!==Error.prototype},u=>u!=="isAxiosError"),Re.call(s,e.message,t,n,r,a),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};const Ny=null;function Ps(e){return U.isPlainObject(e)||U.isArray(e)}function bd(e){return U.endsWith(e,"[]")?e.slice(0,-2):e}function If(e,t,n){return e?e.concat(t).map(function(a,o){return a=bd(a),!n&&o?"["+a+"]":a}).join(n?".":""):t}function Ly(e){return U.isArray(e)&&!e.some(Ps)}const Oy=U.toFlatObject(U,{},null,function(t){return/^is[A-Z]/.test(t)});function so(e,t,n){if(!U.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=U.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(S,L){return!U.isUndefined(L[S])});const r=n.metaTokens,a=n.visitor||h,o=n.dots,s=n.indexes,f=(n.Blob||typeof Blob<"u"&&Blob)&&U.isSpecCompliantForm(t);if(!U.isFunction(a))throw new TypeError("visitor must be a function");function d(g){if(g===null)return"";if(U.isDate(g))return g.toISOString();if(!f&&U.isBlob(g))throw new Re("Blob is not supported. Use a Buffer instead.");return U.isArrayBuffer(g)||U.isTypedArray(g)?f&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function h(g,S,L){let O=g;if(g&&!L&&typeof g=="object"){if(U.endsWith(S,"{}"))S=r?S:S.slice(0,-2),g=JSON.stringify(g);else if(U.isArray(g)&&Ly(g)||(U.isFileList(g)||U.endsWith(S,"[]"))&&(O=U.toArray(g)))return S=bd(S),O.forEach(function(Y,W){!(U.isUndefined(Y)||Y===null)&&t.append(s===!0?If([S],W,o):s===null?S:S+"[]",d(Y))}),!1}return Ps(g)?!0:(t.append(If(L,S,o),d(g)),!1)}const b=[],_=Object.assign(Oy,{defaultVisitor:h,convertValue:d,isVisitable:Ps});function y(g,S){if(!U.isUndefined(g)){if(b.indexOf(g)!==-1)throw Error("Circular reference detected in "+S.join("."));b.push(g),U.forEach(g,function(O,C){(!(U.isUndefined(O)||O===null)&&a.call(t,O,U.isString(C)?C.trim():C,S,_))===!0&&y(O,S?S.concat(C):[C])}),b.pop()}}if(!U.isObject(e))throw new TypeError("data must be an object");return y(e),t}function Rf(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Tl(e,t){this._pairs=[],e&&so(e,this,t)}const _d=Tl.prototype;_d.append=function(t,n){this._pairs.push([t,n])};_d.toString=function(t){const n=t?function(r){return t.call(this,r,Rf)}:Rf;return this._pairs.map(function(a){return n(a[0])+"="+n(a[1])},"").join("&")};function Iy(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function gd(e,t,n){if(!t)return e;const r=n&&n.encode||Iy,a=n&&n.serialize;let o;if(a?o=a(t,n):o=U.isURLSearchParams(t)?t.toString():new Tl(t,n).toString(r),o){const s=e.indexOf("#");s!==-1&&(e=e.slice(0,s)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Ry{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){U.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Pf=Ry,yd={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Py=typeof URLSearchParams<"u"?URLSearchParams:Tl,ky=typeof FormData<"u"?FormData:null,$y=typeof Blob<"u"?Blob:null,Fy=(()=>{let e;return typeof navigator<"u"&&((e=navigator.product)==="ReactNative"||e==="NativeScript"||e==="NS")?!1:typeof window<"u"&&typeof document<"u"})(),Hy=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Zt={isBrowser:!0,classes:{URLSearchParams:Py,FormData:ky,Blob:$y},isStandardBrowserEnv:Fy,isStandardBrowserWebWorkerEnv:Hy,protocols:["http","https","file","blob","url","data"]};function My(e,t){return so(e,new Zt.classes.URLSearchParams,Object.assign({visitor:function(n,r,a,o){return Zt.isNode&&U.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function jy(e){return U.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function By(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}function wd(e){function t(n,r,a,o){let s=n[o++];const u=Number.isFinite(+s),f=o>=n.length;return s=!s&&U.isArray(a)?a.length:s,f?(U.hasOwnProp(a,s)?a[s]=[a[s],r]:a[s]=r,!u):((!a[s]||!U.isObject(a[s]))&&(a[s]=[]),t(n,r,a[s],o)&&U.isArray(a[s])&&(a[s]=By(a[s])),!u)}if(U.isFormData(e)&&U.isFunction(e.entries)){const n={};return U.forEachEntry(e,(r,a)=>{t(jy(r),a,n,0)}),n}return null}const Wy={"Content-Type":void 0};function Vy(e,t,n){if(U.isString(e))try{return(t||JSON.parse)(e),U.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const lo={transitional:yd,adapter:["xhr","http"],transformRequest:[function(t,n){const r=n.getContentType()||"",a=r.indexOf("application/json")>-1,o=U.isObject(t);if(o&&U.isHTMLForm(t)&&(t=new FormData(t)),U.isFormData(t))return a&&a?JSON.stringify(wd(t)):t;if(U.isArrayBuffer(t)||U.isBuffer(t)||U.isStream(t)||U.isFile(t)||U.isBlob(t))return t;if(U.isArrayBufferView(t))return t.buffer;if(U.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let u;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return My(t,this.formSerializer).toString();if((u=U.isFileList(t))||r.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return so(u?{"files[]":t}:t,f&&new f,this.formSerializer)}}return o||a?(n.setContentType("application/json",!1),Vy(t)):t}],transformResponse:[function(t){const n=this.transitional||lo.transitional,r=n&&n.forcedJSONParsing,a=this.responseType==="json";if(t&&U.isString(t)&&(r&&!this.responseType||a)){const s=!(n&&n.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(u){if(s)throw u.name==="SyntaxError"?Re.from(u,Re.ERR_BAD_RESPONSE,this,null,this.response):u}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Zt.classes.FormData,Blob:Zt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};U.forEach(["delete","get","head"],function(t){lo.headers[t]={}});U.forEach(["post","put","patch"],function(t){lo.headers[t]=U.merge(Wy)});const Cl=lo,qy=U.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Uy=e=>{const t={};let n,r,a;return e&&e.split(`
`).forEach(function(s){a=s.indexOf(":"),n=s.substring(0,a).trim().toLowerCase(),r=s.substring(a+1).trim(),!(!n||t[n]&&qy[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},kf=Symbol("internals");function ua(e){return e&&String(e).trim().toLowerCase()}function Ei(e){return e===!1||e==null?e:U.isArray(e)?e.map(Ei):String(e)}function Xy(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Ky=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ms(e,t,n,r,a){if(U.isFunction(r))return r.call(this,t,n);if(a&&(t=n),!!U.isString(t)){if(U.isString(r))return t.indexOf(r)!==-1;if(U.isRegExp(r))return r.test(t)}}function Yy(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Gy(e,t){const n=U.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(a,o,s){return this[r].call(this,t,a,o,s)},configurable:!0})})}class uo{constructor(t){t&&this.set(t)}set(t,n,r){const a=this;function o(u,f,d){const h=ua(f);if(!h)throw new Error("header name must be a non-empty string");const b=U.findKey(a,h);(!b||a[b]===void 0||d===!0||d===void 0&&a[b]!==!1)&&(a[b||f]=Ei(u))}const s=(u,f)=>U.forEach(u,(d,h)=>o(d,h,f));return U.isPlainObject(t)||t instanceof this.constructor?s(t,n):U.isString(t)&&(t=t.trim())&&!Ky(t)?s(Uy(t),n):t!=null&&o(n,t,r),this}get(t,n){if(t=ua(t),t){const r=U.findKey(this,t);if(r){const a=this[r];if(!n)return a;if(n===!0)return Xy(a);if(U.isFunction(n))return n.call(this,a,r);if(U.isRegExp(n))return n.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=ua(t),t){const r=U.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||ms(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let a=!1;function o(s){if(s=ua(s),s){const u=U.findKey(r,s);u&&(!n||ms(r,r[u],u,n))&&(delete r[u],a=!0)}}return U.isArray(t)?t.forEach(o):o(t),a}clear(t){const n=Object.keys(this);let r=n.length,a=!1;for(;r--;){const o=n[r];(!t||ms(this,this[o],o,t,!0))&&(delete this[o],a=!0)}return a}normalize(t){const n=this,r={};return U.forEach(this,(a,o)=>{const s=U.findKey(r,o);if(s){n[s]=Ei(a),delete n[o];return}const u=t?Yy(o):String(o).trim();u!==o&&delete n[o],n[u]=Ei(a),r[u]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return U.forEach(this,(r,a)=>{r!=null&&r!==!1&&(n[a]=t&&U.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(a=>r.set(a)),r}static accessor(t){const r=(this[kf]=this[kf]={accessors:{}}).accessors,a=this.prototype;function o(s){const u=ua(s);r[u]||(Gy(a,s),r[u]=!0)}return U.isArray(t)?t.forEach(o):o(t),this}}uo.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);U.freezeMethods(uo.prototype);U.freezeMethods(uo);const _n=uo;function bs(e,t){const n=this||Cl,r=t||n,a=_n.from(r.headers);let o=r.data;return U.forEach(e,function(u){o=u.call(n,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function Td(e){return!!(e&&e.__CANCEL__)}function ka(e,t,n){Re.call(this,e??"canceled",Re.ERR_CANCELED,t,n),this.name="CanceledError"}U.inherits(ka,Re,{__CANCEL__:!0});function zy(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new Re("Request failed with status code "+n.status,[Re.ERR_BAD_REQUEST,Re.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}const Jy=Zt.isStandardBrowserEnv?function(){return{write:function(n,r,a,o,s,u){const f=[];f.push(n+"="+encodeURIComponent(r)),U.isNumber(a)&&f.push("expires="+new Date(a).toGMTString()),U.isString(o)&&f.push("path="+o),U.isString(s)&&f.push("domain="+s),u===!0&&f.push("secure"),document.cookie=f.join("; ")},read:function(n){const r=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove:function(n){this.write(n,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}();function Qy(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Zy(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}function Cd(e,t){return e&&!Qy(t)?Zy(e,t):t}const e0=Zt.isStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");let r;function a(o){let s=o;return t&&(n.setAttribute("href",s),s=n.href),n.setAttribute("href",s),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:n.pathname.charAt(0)==="/"?n.pathname:"/"+n.pathname}}return r=a(window.location.href),function(s){const u=U.isString(s)?a(s):s;return u.protocol===r.protocol&&u.host===r.host}}():function(){return function(){return!0}}();function t0(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function n0(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a=0,o=0,s;return t=t!==void 0?t:1e3,function(f){const d=Date.now(),h=r[o];s||(s=d),n[a]=f,r[a]=d;let b=o,_=0;for(;b!==a;)_+=n[b++],b=b%e;if(a=(a+1)%e,a===o&&(o=(o+1)%e),d-s<t)return;const y=h&&d-h;return y?Math.round(_*1e3/y):void 0}}function $f(e,t){let n=0;const r=n0(50,250);return a=>{const o=a.loaded,s=a.lengthComputable?a.total:void 0,u=o-n,f=r(u),d=o<=s;n=o;const h={loaded:o,total:s,progress:s?o/s:void 0,bytes:u,rate:f||void 0,estimated:f&&s&&d?(s-o)/f:void 0,event:a};h[t?"download":"upload"]=!0,e(h)}}const r0=typeof XMLHttpRequest<"u",a0=r0&&function(e){return new Promise(function(n,r){let a=e.data;const o=_n.from(e.headers).normalize(),s=e.responseType;let u;function f(){e.cancelToken&&e.cancelToken.unsubscribe(u),e.signal&&e.signal.removeEventListener("abort",u)}U.isFormData(a)&&(Zt.isStandardBrowserEnv||Zt.isStandardBrowserWebWorkerEnv)&&o.setContentType(!1);let d=new XMLHttpRequest;if(e.auth){const y=e.auth.username||"",g=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";o.set("Authorization","Basic "+btoa(y+":"+g))}const h=Cd(e.baseURL,e.url);d.open(e.method.toUpperCase(),gd(h,e.params,e.paramsSerializer),!0),d.timeout=e.timeout;function b(){if(!d)return;const y=_n.from("getAllResponseHeaders"in d&&d.getAllResponseHeaders()),S={data:!s||s==="text"||s==="json"?d.responseText:d.response,status:d.status,statusText:d.statusText,headers:y,config:e,request:d};zy(function(O){n(O),f()},function(O){r(O),f()},S),d=null}if("onloadend"in d?d.onloadend=b:d.onreadystatechange=function(){!d||d.readyState!==4||d.status===0&&!(d.responseURL&&d.responseURL.indexOf("file:")===0)||setTimeout(b)},d.onabort=function(){d&&(r(new Re("Request aborted",Re.ECONNABORTED,e,d)),d=null)},d.onerror=function(){r(new Re("Network Error",Re.ERR_NETWORK,e,d)),d=null},d.ontimeout=function(){let g=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const S=e.transitional||yd;e.timeoutErrorMessage&&(g=e.timeoutErrorMessage),r(new Re(g,S.clarifyTimeoutError?Re.ETIMEDOUT:Re.ECONNABORTED,e,d)),d=null},Zt.isStandardBrowserEnv){const y=(e.withCredentials||e0(h))&&e.xsrfCookieName&&Jy.read(e.xsrfCookieName);y&&o.set(e.xsrfHeaderName,y)}a===void 0&&o.setContentType(null),"setRequestHeader"in d&&U.forEach(o.toJSON(),function(g,S){d.setRequestHeader(S,g)}),U.isUndefined(e.withCredentials)||(d.withCredentials=!!e.withCredentials),s&&s!=="json"&&(d.responseType=e.responseType),typeof e.onDownloadProgress=="function"&&d.addEventListener("progress",$f(e.onDownloadProgress,!0)),typeof e.onUploadProgress=="function"&&d.upload&&d.upload.addEventListener("progress",$f(e.onUploadProgress)),(e.cancelToken||e.signal)&&(u=y=>{d&&(r(!y||y.type?new ka(null,e,d):y),d.abort(),d=null)},e.cancelToken&&e.cancelToken.subscribe(u),e.signal&&(e.signal.aborted?u():e.signal.addEventListener("abort",u)));const _=t0(h);if(_&&Zt.protocols.indexOf(_)===-1){r(new Re("Unsupported protocol "+_+":",Re.ERR_BAD_REQUEST,e));return}d.send(a||null)})},Ni={http:Ny,xhr:a0};U.forEach(Ni,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const i0={getAdapter:e=>{e=U.isArray(e)?e:[e];const{length:t}=e;let n,r;for(let a=0;a<t&&(n=e[a],!(r=U.isString(n)?Ni[n.toLowerCase()]:n));a++);if(!r)throw r===!1?new Re(`Adapter ${n} is not supported by the environment`,"ERR_NOT_SUPPORT"):new Error(U.hasOwnProp(Ni,n)?`Adapter '${n}' is not available in the build`:`Unknown adapter '${n}'`);if(!U.isFunction(r))throw new TypeError("adapter is not a function");return r},adapters:Ni};function _s(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ka(null,e)}function Ff(e){return _s(e),e.headers=_n.from(e.headers),e.data=bs.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),i0.getAdapter(e.adapter||Cl.adapter)(e).then(function(r){return _s(e),r.data=bs.call(e,e.transformResponse,r),r.headers=_n.from(r.headers),r},function(r){return Td(r)||(_s(e),r&&r.response&&(r.response.data=bs.call(e,e.transformResponse,r.response),r.response.headers=_n.from(r.response.headers))),Promise.reject(r)})}const Hf=e=>e instanceof _n?e.toJSON():e;function Hr(e,t){t=t||{};const n={};function r(d,h,b){return U.isPlainObject(d)&&U.isPlainObject(h)?U.merge.call({caseless:b},d,h):U.isPlainObject(h)?U.merge({},h):U.isArray(h)?h.slice():h}function a(d,h,b){if(U.isUndefined(h)){if(!U.isUndefined(d))return r(void 0,d,b)}else return r(d,h,b)}function o(d,h){if(!U.isUndefined(h))return r(void 0,h)}function s(d,h){if(U.isUndefined(h)){if(!U.isUndefined(d))return r(void 0,d)}else return r(void 0,h)}function u(d,h,b){if(b in t)return r(d,h);if(b in e)return r(void 0,d)}const f={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:u,headers:(d,h)=>a(Hf(d),Hf(h),!0)};return U.forEach(Object.keys(e).concat(Object.keys(t)),function(h){const b=f[h]||a,_=b(e[h],t[h],h);U.isUndefined(_)&&b!==u||(n[h]=_)}),n}const Dd="1.3.6",Dl={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Dl[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Mf={};Dl.transitional=function(t,n,r){function a(o,s){return"[Axios v"+Dd+"] Transitional option '"+o+"'"+s+(r?". "+r:"")}return(o,s,u)=>{if(t===!1)throw new Re(a(s," has been removed"+(n?" in "+n:"")),Re.ERR_DEPRECATED);return n&&!Mf[s]&&(Mf[s]=!0,console.warn(a(s," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,s,u):!0}};function o0(e,t,n){if(typeof e!="object")throw new Re("options must be an object",Re.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],s=t[o];if(s){const u=e[o],f=u===void 0||s(u,o,e);if(f!==!0)throw new Re("option "+o+" must be "+f,Re.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new Re("Unknown option "+o,Re.ERR_BAD_OPTION)}}const ks={assertOptions:o0,validators:Dl},Ln=ks.validators;class Bi{constructor(t){this.defaults=t,this.interceptors={request:new Pf,response:new Pf}}request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Hr(this.defaults,n);const{transitional:r,paramsSerializer:a,headers:o}=n;r!==void 0&&ks.assertOptions(r,{silentJSONParsing:Ln.transitional(Ln.boolean),forcedJSONParsing:Ln.transitional(Ln.boolean),clarifyTimeoutError:Ln.transitional(Ln.boolean)},!1),a!=null&&(U.isFunction(a)?n.paramsSerializer={serialize:a}:ks.assertOptions(a,{encode:Ln.function,serialize:Ln.function},!0)),n.method=(n.method||this.defaults.method||"get").toLowerCase();let s;s=o&&U.merge(o.common,o[n.method]),s&&U.forEach(["delete","get","head","post","put","patch","common"],g=>{delete o[g]}),n.headers=_n.concat(s,o);const u=[];let f=!0;this.interceptors.request.forEach(function(S){typeof S.runWhen=="function"&&S.runWhen(n)===!1||(f=f&&S.synchronous,u.unshift(S.fulfilled,S.rejected))});const d=[];this.interceptors.response.forEach(function(S){d.push(S.fulfilled,S.rejected)});let h,b=0,_;if(!f){const g=[Ff.bind(this),void 0];for(g.unshift.apply(g,u),g.push.apply(g,d),_=g.length,h=Promise.resolve(n);b<_;)h=h.then(g[b++],g[b++]);return h}_=u.length;let y=n;for(b=0;b<_;){const g=u[b++],S=u[b++];try{y=g(y)}catch(L){S.call(this,L);break}}try{h=Ff.call(this,y)}catch(g){return Promise.reject(g)}for(b=0,_=d.length;b<_;)h=h.then(d[b++],d[b++]);return h}getUri(t){t=Hr(this.defaults,t);const n=Cd(t.baseURL,t.url);return gd(n,t.params,t.paramsSerializer)}}U.forEach(["delete","get","head","options"],function(t){Bi.prototype[t]=function(n,r){return this.request(Hr(r||{},{method:t,url:n,data:(r||{}).data}))}});U.forEach(["post","put","patch"],function(t){function n(r){return function(o,s,u){return this.request(Hr(u||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:s}))}}Bi.prototype[t]=n(),Bi.prototype[t+"Form"]=n(!0)});const Li=Bi;class Sl{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(a=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](a);r._listeners=null}),this.promise.then=a=>{let o;const s=new Promise(u=>{r.subscribe(u),o=u}).then(a);return s.cancel=function(){r.unsubscribe(o)},s},t(function(o,s,u){r.reason||(r.reason=new ka(o,s,u),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}static source(){let t;return{token:new Sl(function(a){t=a}),cancel:t}}}const s0=Sl;function l0(e){return function(n){return e.apply(null,n)}}function u0(e){return U.isObject(e)&&e.isAxiosError===!0}const $s={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries($s).forEach(([e,t])=>{$s[t]=e});const f0=$s;function Sd(e){const t=new Li(e),n=sd(Li.prototype.request,t);return U.extend(n,Li.prototype,t,{allOwnKeys:!0}),U.extend(n,t,null,{allOwnKeys:!0}),n.create=function(a){return Sd(Hr(e,a))},n}const Ge=Sd(Cl);Ge.Axios=Li;Ge.CanceledError=ka;Ge.CancelToken=s0;Ge.isCancel=Td;Ge.VERSION=Dd;Ge.toFormData=so;Ge.AxiosError=Re;Ge.Cancel=Ge.CanceledError;Ge.all=function(t){return Promise.all(t)};Ge.spread=l0;Ge.isAxiosError=u0;Ge.mergeConfig=Hr;Ge.AxiosHeaders=_n;Ge.formToJSON=e=>wd(U.isHTMLForm(e)?new FormData(e):e);Ge.HttpStatusCode=f0;Ge.default=Ge;const c0=Ge;window.axios=c0;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";var d0=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Fs={},h0={get exports(){return Fs},set exports(e){Fs=e}};/*!
 * jQuery JavaScript Library v3.7.1
 * https://jquery.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2023-08-28T13:37Z
 */(function(e){(function(t,n){e.exports=t.document?n(t,!0):function(r){if(!r.document)throw new Error("jQuery requires a window with a document");return n(r)}})(typeof window<"u"?window:d0,function(t,n){var r=[],a=Object.getPrototypeOf,o=r.slice,s=r.flat?function(l){return r.flat.call(l)}:function(l){return r.concat.apply([],l)},u=r.push,f=r.indexOf,d={},h=d.toString,b=d.hasOwnProperty,_=b.toString,y=_.call(Object),g={},S=function(c){return typeof c=="function"&&typeof c.nodeType!="number"&&typeof c.item!="function"},L=function(c){return c!=null&&c===c.window},O=t.document,C={type:!0,src:!0,nonce:!0,noModule:!0};function Y(l,c,p){p=p||O;var m,w,T=p.createElement("script");if(T.text=l,c)for(m in C)w=c[m]||c.getAttribute&&c.getAttribute(m),w&&T.setAttribute(m,w);p.head.appendChild(T).parentNode.removeChild(T)}function W(l){return l==null?l+"":typeof l=="object"||typeof l=="function"?d[h.call(l)]||"object":typeof l}var B="3.7.1",Q=/HTML$/i,v=function(l,c){return new v.fn.init(l,c)};v.fn=v.prototype={jquery:B,constructor:v,length:0,toArray:function(){return o.call(this)},get:function(l){return l==null?o.call(this):l<0?this[l+this.length]:this[l]},pushStack:function(l){var c=v.merge(this.constructor(),l);return c.prevObject=this,c},each:function(l){return v.each(this,l)},map:function(l){return this.pushStack(v.map(this,function(c,p){return l.call(c,p,c)}))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(v.grep(this,function(l,c){return(c+1)%2}))},odd:function(){return this.pushStack(v.grep(this,function(l,c){return c%2}))},eq:function(l){var c=this.length,p=+l+(l<0?c:0);return this.pushStack(p>=0&&p<c?[this[p]]:[])},end:function(){return this.prevObject||this.constructor()},push:u,sort:r.sort,splice:r.splice},v.extend=v.fn.extend=function(){var l,c,p,m,w,T,D=arguments[0]||{},I=1,N=arguments.length,P=!1;for(typeof D=="boolean"&&(P=D,D=arguments[I]||{},I++),typeof D!="object"&&!S(D)&&(D={}),I===N&&(D=this,I--);I<N;I++)if((l=arguments[I])!=null)for(c in l)m=l[c],!(c==="__proto__"||D===m)&&(P&&m&&(v.isPlainObject(m)||(w=Array.isArray(m)))?(p=D[c],w&&!Array.isArray(p)?T=[]:!w&&!v.isPlainObject(p)?T={}:T=p,w=!1,D[c]=v.extend(P,T,m)):m!==void 0&&(D[c]=m));return D},v.extend({expando:"jQuery"+(B+Math.random()).replace(/\D/g,""),isReady:!0,error:function(l){throw new Error(l)},noop:function(){},isPlainObject:function(l){var c,p;return!l||h.call(l)!=="[object Object]"?!1:(c=a(l),c?(p=b.call(c,"constructor")&&c.constructor,typeof p=="function"&&_.call(p)===y):!0)},isEmptyObject:function(l){var c;for(c in l)return!1;return!0},globalEval:function(l,c,p){Y(l,{nonce:c&&c.nonce},p)},each:function(l,c){var p,m=0;if(ee(l))for(p=l.length;m<p&&c.call(l[m],m,l[m])!==!1;m++);else for(m in l)if(c.call(l[m],m,l[m])===!1)break;return l},text:function(l){var c,p="",m=0,w=l.nodeType;if(!w)for(;c=l[m++];)p+=v.text(c);return w===1||w===11?l.textContent:w===9?l.documentElement.textContent:w===3||w===4?l.nodeValue:p},makeArray:function(l,c){var p=c||[];return l!=null&&(ee(Object(l))?v.merge(p,typeof l=="string"?[l]:l):u.call(p,l)),p},inArray:function(l,c,p){return c==null?-1:f.call(c,l,p)},isXMLDoc:function(l){var c=l&&l.namespaceURI,p=l&&(l.ownerDocument||l).documentElement;return!Q.test(c||p&&p.nodeName||"HTML")},merge:function(l,c){for(var p=+c.length,m=0,w=l.length;m<p;m++)l[w++]=c[m];return l.length=w,l},grep:function(l,c,p){for(var m,w=[],T=0,D=l.length,I=!p;T<D;T++)m=!c(l[T],T),m!==I&&w.push(l[T]);return w},map:function(l,c,p){var m,w,T=0,D=[];if(ee(l))for(m=l.length;T<m;T++)w=c(l[T],T,p),w!=null&&D.push(w);else for(T in l)w=c(l[T],T,p),w!=null&&D.push(w);return s(D)},guid:1,support:g}),typeof Symbol=="function"&&(v.fn[Symbol.iterator]=r[Symbol.iterator]),v.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(l,c){d["[object "+c+"]"]=c.toLowerCase()});function ee(l){var c=!!l&&"length"in l&&l.length,p=W(l);return S(l)||L(l)?!1:p==="array"||c===0||typeof c=="number"&&c>0&&c-1 in l}function G(l,c){return l.nodeName&&l.nodeName.toLowerCase()===c.toLowerCase()}var re=r.pop,ie=r.sort,pe=r.splice,se="[\\x20\\t\\r\\n\\f]",ae=new RegExp("^"+se+"+|((?:^|[^\\\\])(?:\\\\.)*)"+se+"+$","g");v.contains=function(l,c){var p=c&&c.parentNode;return l===p||!!(p&&p.nodeType===1&&(l.contains?l.contains(p):l.compareDocumentPosition&&l.compareDocumentPosition(p)&16))};var we=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function Te(l,c){return c?l==="\0"?"�":l.slice(0,-1)+"\\"+l.charCodeAt(l.length-1).toString(16)+" ":"\\"+l}v.escapeSelector=function(l){return(l+"").replace(we,Te)};var me=O,xe=u;(function(){var l,c,p,m,w,T=xe,D,I,N,P,j,q=v.expando,H=0,J=0,ge=ci(),Oe=ci(),Ce=ci(),et=ci(),Ye=function(E,R){return E===R&&(w=!0),0},Xt="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",Kt="(?:\\\\[\\da-fA-F]{1,6}"+se+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",Ae="\\["+se+"*("+Kt+")(?:"+se+"*([*^$|!~]?=)"+se+`*(?:'((?:\\\\.|[^\\\\'])*)'|"((?:\\\\.|[^\\\\"])*)"|(`+Kt+"))|)"+se+"*\\]",Kn=":("+Kt+`)(?:\\((('((?:\\\\.|[^\\\\'])*)'|"((?:\\\\.|[^\\\\"])*)")|((?:\\\\.|[^\\\\()[\\]]|`+Ae+")*)|.*)\\)|)",Ie=new RegExp(se+"+","g"),Ve=new RegExp("^"+se+"*,"+se+"*"),aa=new RegExp("^"+se+"*([>+~]|"+se+")"+se+"*"),Ko=new RegExp(se+"|>"),Yt=new RegExp(Kn),ia=new RegExp("^"+Kt+"$"),Gt={ID:new RegExp("^#("+Kt+")"),CLASS:new RegExp("^\\.("+Kt+")"),TAG:new RegExp("^("+Kt+"|[*])"),ATTR:new RegExp("^"+Ae),PSEUDO:new RegExp("^"+Kn),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+se+"*(even|odd|(([+-]|)(\\d*)n|)"+se+"*(?:([+-]|)"+se+"*(\\d+)|))"+se+"*\\)|)","i"),bool:new RegExp("^(?:"+Xt+")$","i"),needsContext:new RegExp("^"+se+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+se+"*((?:-\\d)?\\d*)"+se+"*\\)|)(?=[^-]|$)","i")},xn=/^(?:input|select|textarea|button)$/i,An=/^h\d$/i,Ot=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Yo=/[+~]/,dn=new RegExp("\\\\[\\da-fA-F]{1,6}"+se+"?|\\\\([^\\r\\n\\f])","g"),hn=function(E,R){var F="0x"+E.slice(1)-65536;return R||(F<0?String.fromCharCode(F+65536):String.fromCharCode(F>>10|55296,F&1023|56320))},Vp=function(){En()},qp=hi(function(E){return E.disabled===!0&&G(E,"fieldset")},{dir:"parentNode",next:"legend"});function Up(){try{return D.activeElement}catch{}}try{T.apply(r=o.call(me.childNodes),me.childNodes),r[me.childNodes.length].nodeType}catch{T={apply:function(R,F){xe.apply(R,o.call(F))},call:function(R){xe.apply(R,o.call(arguments,1))}}}function $e(E,R,F,M){var V,Z,te,ue,ne,De,ve,ye=R&&R.ownerDocument,Se=R?R.nodeType:9;if(F=F||[],typeof E!="string"||!E||Se!==1&&Se!==9&&Se!==11)return F;if(!M&&(En(R),R=R||D,N)){if(Se!==11&&(ne=Ot.exec(E)))if(V=ne[1]){if(Se===9)if(te=R.getElementById(V)){if(te.id===V)return T.call(F,te),F}else return F;else if(ye&&(te=ye.getElementById(V))&&$e.contains(R,te)&&te.id===V)return T.call(F,te),F}else{if(ne[2])return T.apply(F,R.getElementsByTagName(E)),F;if((V=ne[3])&&R.getElementsByClassName)return T.apply(F,R.getElementsByClassName(V)),F}if(!et[E+" "]&&(!P||!P.test(E))){if(ve=E,ye=R,Se===1&&(Ko.test(E)||aa.test(E))){for(ye=Yo.test(E)&&Go(R.parentNode)||R,(ye!=R||!g.scope)&&((ue=R.getAttribute("id"))?ue=v.escapeSelector(ue):R.setAttribute("id",ue=q)),De=oa(E),Z=De.length;Z--;)De[Z]=(ue?"#"+ue:":scope")+" "+di(De[Z]);ve=De.join(",")}try{return T.apply(F,ye.querySelectorAll(ve)),F}catch{et(E,!0)}finally{ue===q&&R.removeAttribute("id")}}}return Xu(E.replace(ae,"$1"),R,F,M)}function ci(){var E=[];function R(F,M){return E.push(F+" ")>c.cacheLength&&delete R[E.shift()],R[F+" "]=M}return R}function Bt(E){return E[q]=!0,E}function mr(E){var R=D.createElement("fieldset");try{return!!E(R)}catch{return!1}finally{R.parentNode&&R.parentNode.removeChild(R),R=null}}function Xp(E){return function(R){return G(R,"input")&&R.type===E}}function Kp(E){return function(R){return(G(R,"input")||G(R,"button"))&&R.type===E}}function qu(E){return function(R){return"form"in R?R.parentNode&&R.disabled===!1?"label"in R?"label"in R.parentNode?R.parentNode.disabled===E:R.disabled===E:R.isDisabled===E||R.isDisabled!==!E&&qp(R)===E:R.disabled===E:"label"in R?R.disabled===E:!1}}function Yn(E){return Bt(function(R){return R=+R,Bt(function(F,M){for(var V,Z=E([],F.length,R),te=Z.length;te--;)F[V=Z[te]]&&(F[V]=!(M[V]=F[V]))})})}function Go(E){return E&&typeof E.getElementsByTagName<"u"&&E}function En(E){var R,F=E?E.ownerDocument||E:me;return F==D||F.nodeType!==9||!F.documentElement||(D=F,I=D.documentElement,N=!v.isXMLDoc(D),j=I.matches||I.webkitMatchesSelector||I.msMatchesSelector,I.msMatchesSelector&&me!=D&&(R=D.defaultView)&&R.top!==R&&R.addEventListener("unload",Vp),g.getById=mr(function(M){return I.appendChild(M).id=v.expando,!D.getElementsByName||!D.getElementsByName(v.expando).length}),g.disconnectedMatch=mr(function(M){return j.call(M,"*")}),g.scope=mr(function(){return D.querySelectorAll(":scope")}),g.cssHas=mr(function(){try{return D.querySelector(":has(*,:jqfake)"),!1}catch{return!0}}),g.getById?(c.filter.ID=function(M){var V=M.replace(dn,hn);return function(Z){return Z.getAttribute("id")===V}},c.find.ID=function(M,V){if(typeof V.getElementById<"u"&&N){var Z=V.getElementById(M);return Z?[Z]:[]}}):(c.filter.ID=function(M){var V=M.replace(dn,hn);return function(Z){var te=typeof Z.getAttributeNode<"u"&&Z.getAttributeNode("id");return te&&te.value===V}},c.find.ID=function(M,V){if(typeof V.getElementById<"u"&&N){var Z,te,ue,ne=V.getElementById(M);if(ne){if(Z=ne.getAttributeNode("id"),Z&&Z.value===M)return[ne];for(ue=V.getElementsByName(M),te=0;ne=ue[te++];)if(Z=ne.getAttributeNode("id"),Z&&Z.value===M)return[ne]}return[]}}),c.find.TAG=function(M,V){return typeof V.getElementsByTagName<"u"?V.getElementsByTagName(M):V.querySelectorAll(M)},c.find.CLASS=function(M,V){if(typeof V.getElementsByClassName<"u"&&N)return V.getElementsByClassName(M)},P=[],mr(function(M){var V;I.appendChild(M).innerHTML="<a id='"+q+"' href='' disabled='disabled'></a><select id='"+q+"-\r\\' disabled='disabled'><option selected=''></option></select>",M.querySelectorAll("[selected]").length||P.push("\\["+se+"*(?:value|"+Xt+")"),M.querySelectorAll("[id~="+q+"-]").length||P.push("~="),M.querySelectorAll("a#"+q+"+*").length||P.push(".#.+[+~]"),M.querySelectorAll(":checked").length||P.push(":checked"),V=D.createElement("input"),V.setAttribute("type","hidden"),M.appendChild(V).setAttribute("name","D"),I.appendChild(M).disabled=!0,M.querySelectorAll(":disabled").length!==2&&P.push(":enabled",":disabled"),V=D.createElement("input"),V.setAttribute("name",""),M.appendChild(V),M.querySelectorAll("[name='']").length||P.push("\\["+se+"*name"+se+"*="+se+`*(?:''|"")`)}),g.cssHas||P.push(":has"),P=P.length&&new RegExp(P.join("|")),Ye=function(M,V){if(M===V)return w=!0,0;var Z=!M.compareDocumentPosition-!V.compareDocumentPosition;return Z||(Z=(M.ownerDocument||M)==(V.ownerDocument||V)?M.compareDocumentPosition(V):1,Z&1||!g.sortDetached&&V.compareDocumentPosition(M)===Z?M===D||M.ownerDocument==me&&$e.contains(me,M)?-1:V===D||V.ownerDocument==me&&$e.contains(me,V)?1:m?f.call(m,M)-f.call(m,V):0:Z&4?-1:1)}),D}$e.matches=function(E,R){return $e(E,null,null,R)},$e.matchesSelector=function(E,R){if(En(E),N&&!et[R+" "]&&(!P||!P.test(R)))try{var F=j.call(E,R);if(F||g.disconnectedMatch||E.document&&E.document.nodeType!==11)return F}catch{et(R,!0)}return $e(R,D,null,[E]).length>0},$e.contains=function(E,R){return(E.ownerDocument||E)!=D&&En(E),v.contains(E,R)},$e.attr=function(E,R){(E.ownerDocument||E)!=D&&En(E);var F=c.attrHandle[R.toLowerCase()],M=F&&b.call(c.attrHandle,R.toLowerCase())?F(E,R,!N):void 0;return M!==void 0?M:E.getAttribute(R)},$e.error=function(E){throw new Error("Syntax error, unrecognized expression: "+E)},v.uniqueSort=function(E){var R,F=[],M=0,V=0;if(w=!g.sortStable,m=!g.sortStable&&o.call(E,0),ie.call(E,Ye),w){for(;R=E[V++];)R===E[V]&&(M=F.push(V));for(;M--;)pe.call(E,F[M],1)}return m=null,E},v.fn.uniqueSort=function(){return this.pushStack(v.uniqueSort(o.apply(this)))},c=v.expr={cacheLength:50,createPseudo:Bt,match:Gt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(E){return E[1]=E[1].replace(dn,hn),E[3]=(E[3]||E[4]||E[5]||"").replace(dn,hn),E[2]==="~="&&(E[3]=" "+E[3]+" "),E.slice(0,4)},CHILD:function(E){return E[1]=E[1].toLowerCase(),E[1].slice(0,3)==="nth"?(E[3]||$e.error(E[0]),E[4]=+(E[4]?E[5]+(E[6]||1):2*(E[3]==="even"||E[3]==="odd")),E[5]=+(E[7]+E[8]||E[3]==="odd")):E[3]&&$e.error(E[0]),E},PSEUDO:function(E){var R,F=!E[6]&&E[2];return Gt.CHILD.test(E[0])?null:(E[3]?E[2]=E[4]||E[5]||"":F&&Yt.test(F)&&(R=oa(F,!0))&&(R=F.indexOf(")",F.length-R)-F.length)&&(E[0]=E[0].slice(0,R),E[2]=F.slice(0,R)),E.slice(0,3))}},filter:{TAG:function(E){var R=E.replace(dn,hn).toLowerCase();return E==="*"?function(){return!0}:function(F){return G(F,R)}},CLASS:function(E){var R=ge[E+" "];return R||(R=new RegExp("(^|"+se+")"+E+"("+se+"|$)"))&&ge(E,function(F){return R.test(typeof F.className=="string"&&F.className||typeof F.getAttribute<"u"&&F.getAttribute("class")||"")})},ATTR:function(E,R,F){return function(M){var V=$e.attr(M,E);return V==null?R==="!=":R?(V+="",R==="="?V===F:R==="!="?V!==F:R==="^="?F&&V.indexOf(F)===0:R==="*="?F&&V.indexOf(F)>-1:R==="$="?F&&V.slice(-F.length)===F:R==="~="?(" "+V.replace(Ie," ")+" ").indexOf(F)>-1:R==="|="?V===F||V.slice(0,F.length+1)===F+"-":!1):!0}},CHILD:function(E,R,F,M,V){var Z=E.slice(0,3)!=="nth",te=E.slice(-4)!=="last",ue=R==="of-type";return M===1&&V===0?function(ne){return!!ne.parentNode}:function(ne,De,ve){var ye,Se,ce,He,pt,nt=Z!==te?"nextSibling":"previousSibling",It=ne.parentNode,zt=ue&&ne.nodeName.toLowerCase(),br=!ve&&!ue,ot=!1;if(It){if(Z){for(;nt;){for(ce=ne;ce=ce[nt];)if(ue?G(ce,zt):ce.nodeType===1)return!1;pt=nt=E==="only"&&!pt&&"nextSibling"}return!0}if(pt=[te?It.firstChild:It.lastChild],te&&br){for(Se=It[q]||(It[q]={}),ye=Se[E]||[],He=ye[0]===H&&ye[1],ot=He&&ye[2],ce=He&&It.childNodes[He];ce=++He&&ce&&ce[nt]||(ot=He=0)||pt.pop();)if(ce.nodeType===1&&++ot&&ce===ne){Se[E]=[H,He,ot];break}}else if(br&&(Se=ne[q]||(ne[q]={}),ye=Se[E]||[],He=ye[0]===H&&ye[1],ot=He),ot===!1)for(;(ce=++He&&ce&&ce[nt]||(ot=He=0)||pt.pop())&&!((ue?G(ce,zt):ce.nodeType===1)&&++ot&&(br&&(Se=ce[q]||(ce[q]={}),Se[E]=[H,ot]),ce===ne)););return ot-=V,ot===M||ot%M===0&&ot/M>=0}}},PSEUDO:function(E,R){var F,M=c.pseudos[E]||c.setFilters[E.toLowerCase()]||$e.error("unsupported pseudo: "+E);return M[q]?M(R):M.length>1?(F=[E,E,"",R],c.setFilters.hasOwnProperty(E.toLowerCase())?Bt(function(V,Z){for(var te,ue=M(V,R),ne=ue.length;ne--;)te=f.call(V,ue[ne]),V[te]=!(Z[te]=ue[ne])}):function(V){return M(V,0,F)}):M}},pseudos:{not:Bt(function(E){var R=[],F=[],M=Zo(E.replace(ae,"$1"));return M[q]?Bt(function(V,Z,te,ue){for(var ne,De=M(V,null,ue,[]),ve=V.length;ve--;)(ne=De[ve])&&(V[ve]=!(Z[ve]=ne))}):function(V,Z,te){return R[0]=V,M(R,null,te,F),R[0]=null,!F.pop()}}),has:Bt(function(E){return function(R){return $e(E,R).length>0}}),contains:Bt(function(E){return E=E.replace(dn,hn),function(R){return(R.textContent||v.text(R)).indexOf(E)>-1}}),lang:Bt(function(E){return ia.test(E||"")||$e.error("unsupported lang: "+E),E=E.replace(dn,hn).toLowerCase(),function(R){var F;do if(F=N?R.lang:R.getAttribute("xml:lang")||R.getAttribute("lang"))return F=F.toLowerCase(),F===E||F.indexOf(E+"-")===0;while((R=R.parentNode)&&R.nodeType===1);return!1}}),target:function(E){var R=t.location&&t.location.hash;return R&&R.slice(1)===E.id},root:function(E){return E===I},focus:function(E){return E===Up()&&D.hasFocus()&&!!(E.type||E.href||~E.tabIndex)},enabled:qu(!1),disabled:qu(!0),checked:function(E){return G(E,"input")&&!!E.checked||G(E,"option")&&!!E.selected},selected:function(E){return E.parentNode&&E.parentNode.selectedIndex,E.selected===!0},empty:function(E){for(E=E.firstChild;E;E=E.nextSibling)if(E.nodeType<6)return!1;return!0},parent:function(E){return!c.pseudos.empty(E)},header:function(E){return An.test(E.nodeName)},input:function(E){return xn.test(E.nodeName)},button:function(E){return G(E,"input")&&E.type==="button"||G(E,"button")},text:function(E){var R;return G(E,"input")&&E.type==="text"&&((R=E.getAttribute("type"))==null||R.toLowerCase()==="text")},first:Yn(function(){return[0]}),last:Yn(function(E,R){return[R-1]}),eq:Yn(function(E,R,F){return[F<0?F+R:F]}),even:Yn(function(E,R){for(var F=0;F<R;F+=2)E.push(F);return E}),odd:Yn(function(E,R){for(var F=1;F<R;F+=2)E.push(F);return E}),lt:Yn(function(E,R,F){var M;for(F<0?M=F+R:F>R?M=R:M=F;--M>=0;)E.push(M);return E}),gt:Yn(function(E,R,F){for(var M=F<0?F+R:F;++M<R;)E.push(M);return E})}},c.pseudos.nth=c.pseudos.eq;for(l in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})c.pseudos[l]=Xp(l);for(l in{submit:!0,reset:!0})c.pseudos[l]=Kp(l);function Uu(){}Uu.prototype=c.filters=c.pseudos,c.setFilters=new Uu;function oa(E,R){var F,M,V,Z,te,ue,ne,De=Oe[E+" "];if(De)return R?0:De.slice(0);for(te=E,ue=[],ne=c.preFilter;te;){(!F||(M=Ve.exec(te)))&&(M&&(te=te.slice(M[0].length)||te),ue.push(V=[])),F=!1,(M=aa.exec(te))&&(F=M.shift(),V.push({value:F,type:M[0].replace(ae," ")}),te=te.slice(F.length));for(Z in c.filter)(M=Gt[Z].exec(te))&&(!ne[Z]||(M=ne[Z](M)))&&(F=M.shift(),V.push({value:F,type:Z,matches:M}),te=te.slice(F.length));if(!F)break}return R?te.length:te?$e.error(E):Oe(E,ue).slice(0)}function di(E){for(var R=0,F=E.length,M="";R<F;R++)M+=E[R].value;return M}function hi(E,R,F){var M=R.dir,V=R.next,Z=V||M,te=F&&Z==="parentNode",ue=J++;return R.first?function(ne,De,ve){for(;ne=ne[M];)if(ne.nodeType===1||te)return E(ne,De,ve);return!1}:function(ne,De,ve){var ye,Se,ce=[H,ue];if(ve){for(;ne=ne[M];)if((ne.nodeType===1||te)&&E(ne,De,ve))return!0}else for(;ne=ne[M];)if(ne.nodeType===1||te)if(Se=ne[q]||(ne[q]={}),V&&G(ne,V))ne=ne[M]||ne;else{if((ye=Se[Z])&&ye[0]===H&&ye[1]===ue)return ce[2]=ye[2];if(Se[Z]=ce,ce[2]=E(ne,De,ve))return!0}return!1}}function zo(E){return E.length>1?function(R,F,M){for(var V=E.length;V--;)if(!E[V](R,F,M))return!1;return!0}:E[0]}function Yp(E,R,F){for(var M=0,V=R.length;M<V;M++)$e(E,R[M],F);return F}function pi(E,R,F,M,V){for(var Z,te=[],ue=0,ne=E.length,De=R!=null;ue<ne;ue++)(Z=E[ue])&&(!F||F(Z,M,V))&&(te.push(Z),De&&R.push(ue));return te}function Jo(E,R,F,M,V,Z){return M&&!M[q]&&(M=Jo(M)),V&&!V[q]&&(V=Jo(V,Z)),Bt(function(te,ue,ne,De){var ve,ye,Se,ce,He=[],pt=[],nt=ue.length,It=te||Yp(R||"*",ne.nodeType?[ne]:ne,[]),zt=E&&(te||!R)?pi(It,He,E,ne,De):It;if(F?(ce=V||(te?E:nt||M)?[]:ue,F(zt,ce,ne,De)):ce=zt,M)for(ve=pi(ce,pt),M(ve,[],ne,De),ye=ve.length;ye--;)(Se=ve[ye])&&(ce[pt[ye]]=!(zt[pt[ye]]=Se));if(te){if(V||E){if(V){for(ve=[],ye=ce.length;ye--;)(Se=ce[ye])&&ve.push(zt[ye]=Se);V(null,ce=[],ve,De)}for(ye=ce.length;ye--;)(Se=ce[ye])&&(ve=V?f.call(te,Se):He[ye])>-1&&(te[ve]=!(ue[ve]=Se))}}else ce=pi(ce===ue?ce.splice(nt,ce.length):ce),V?V(null,ue,ce,De):T.apply(ue,ce)})}function Qo(E){for(var R,F,M,V=E.length,Z=c.relative[E[0].type],te=Z||c.relative[" "],ue=Z?1:0,ne=hi(function(ye){return ye===R},te,!0),De=hi(function(ye){return f.call(R,ye)>-1},te,!0),ve=[function(ye,Se,ce){var He=!Z&&(ce||Se!=p)||((R=Se).nodeType?ne(ye,Se,ce):De(ye,Se,ce));return R=null,He}];ue<V;ue++)if(F=c.relative[E[ue].type])ve=[hi(zo(ve),F)];else{if(F=c.filter[E[ue].type].apply(null,E[ue].matches),F[q]){for(M=++ue;M<V&&!c.relative[E[M].type];M++);return Jo(ue>1&&zo(ve),ue>1&&di(E.slice(0,ue-1).concat({value:E[ue-2].type===" "?"*":""})).replace(ae,"$1"),F,ue<M&&Qo(E.slice(ue,M)),M<V&&Qo(E=E.slice(M)),M<V&&di(E))}ve.push(F)}return zo(ve)}function Gp(E,R){var F=R.length>0,M=E.length>0,V=function(Z,te,ue,ne,De){var ve,ye,Se,ce=0,He="0",pt=Z&&[],nt=[],It=p,zt=Z||M&&c.find.TAG("*",De),br=H+=It==null?1:Math.random()||.1,ot=zt.length;for(De&&(p=te==D||te||De);He!==ot&&(ve=zt[He])!=null;He++){if(M&&ve){for(ye=0,!te&&ve.ownerDocument!=D&&(En(ve),ue=!N);Se=E[ye++];)if(Se(ve,te||D,ue)){T.call(ne,ve);break}De&&(H=br)}F&&((ve=!Se&&ve)&&ce--,Z&&pt.push(ve))}if(ce+=He,F&&He!==ce){for(ye=0;Se=R[ye++];)Se(pt,nt,te,ue);if(Z){if(ce>0)for(;He--;)pt[He]||nt[He]||(nt[He]=re.call(ne));nt=pi(nt)}T.apply(ne,nt),De&&!Z&&nt.length>0&&ce+R.length>1&&v.uniqueSort(ne)}return De&&(H=br,p=It),pt};return F?Bt(V):V}function Zo(E,R){var F,M=[],V=[],Z=Ce[E+" "];if(!Z){for(R||(R=oa(E)),F=R.length;F--;)Z=Qo(R[F]),Z[q]?M.push(Z):V.push(Z);Z=Ce(E,Gp(V,M)),Z.selector=E}return Z}function Xu(E,R,F,M){var V,Z,te,ue,ne,De=typeof E=="function"&&E,ve=!M&&oa(E=De.selector||E);if(F=F||[],ve.length===1){if(Z=ve[0]=ve[0].slice(0),Z.length>2&&(te=Z[0]).type==="ID"&&R.nodeType===9&&N&&c.relative[Z[1].type]){if(R=(c.find.ID(te.matches[0].replace(dn,hn),R)||[])[0],R)De&&(R=R.parentNode);else return F;E=E.slice(Z.shift().value.length)}for(V=Gt.needsContext.test(E)?0:Z.length;V--&&(te=Z[V],!c.relative[ue=te.type]);)if((ne=c.find[ue])&&(M=ne(te.matches[0].replace(dn,hn),Yo.test(Z[0].type)&&Go(R.parentNode)||R))){if(Z.splice(V,1),E=M.length&&di(Z),!E)return T.apply(F,M),F;break}}return(De||Zo(E,ve))(M,R,!N,F,!R||Yo.test(E)&&Go(R.parentNode)||R),F}g.sortStable=q.split("").sort(Ye).join("")===q,En(),g.sortDetached=mr(function(E){return E.compareDocumentPosition(D.createElement("fieldset"))&1}),v.find=$e,v.expr[":"]=v.expr.pseudos,v.unique=v.uniqueSort,$e.compile=Zo,$e.select=Xu,$e.setDocument=En,$e.tokenize=oa,$e.escape=v.escapeSelector,$e.getText=v.text,$e.isXML=v.isXMLDoc,$e.selectors=v.expr,$e.support=v.support,$e.uniqueSort=v.uniqueSort})();var Le=function(l,c,p){for(var m=[],w=p!==void 0;(l=l[c])&&l.nodeType!==9;)if(l.nodeType===1){if(w&&v(l).is(p))break;m.push(l)}return m},Fe=function(l,c){for(var p=[];l;l=l.nextSibling)l.nodeType===1&&l!==c&&p.push(l);return p},ct=v.expr.match.needsContext,We=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function ze(l,c,p){return S(c)?v.grep(l,function(m,w){return!!c.call(m,w,m)!==p}):c.nodeType?v.grep(l,function(m){return m===c!==p}):typeof c!="string"?v.grep(l,function(m){return f.call(c,m)>-1!==p}):v.filter(c,l,p)}v.filter=function(l,c,p){var m=c[0];return p&&(l=":not("+l+")"),c.length===1&&m.nodeType===1?v.find.matchesSelector(m,l)?[m]:[]:v.find.matches(l,v.grep(c,function(w){return w.nodeType===1}))},v.fn.extend({find:function(l){var c,p,m=this.length,w=this;if(typeof l!="string")return this.pushStack(v(l).filter(function(){for(c=0;c<m;c++)if(v.contains(w[c],this))return!0}));for(p=this.pushStack([]),c=0;c<m;c++)v.find(l,w[c],p);return m>1?v.uniqueSort(p):p},filter:function(l){return this.pushStack(ze(this,l||[],!1))},not:function(l){return this.pushStack(ze(this,l||[],!0))},is:function(l){return!!ze(this,typeof l=="string"&&ct.test(l)?v(l):l||[],!1).length}});var it,tt=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,oe=v.fn.init=function(l,c,p){var m,w;if(!l)return this;if(p=p||it,typeof l=="string")if(l[0]==="<"&&l[l.length-1]===">"&&l.length>=3?m=[null,l,null]:m=tt.exec(l),m&&(m[1]||!c))if(m[1]){if(c=c instanceof v?c[0]:c,v.merge(this,v.parseHTML(m[1],c&&c.nodeType?c.ownerDocument||c:O,!0)),We.test(m[1])&&v.isPlainObject(c))for(m in c)S(this[m])?this[m](c[m]):this.attr(m,c[m]);return this}else return w=O.getElementById(m[2]),w&&(this[0]=w,this.length=1),this;else return!c||c.jquery?(c||p).find(l):this.constructor(c).find(l);else{if(l.nodeType)return this[0]=l,this.length=1,this;if(S(l))return p.ready!==void 0?p.ready(l):l(v)}return v.makeArray(l,this)};oe.prototype=v.fn,it=v(O);var _e=/^(?:parents|prev(?:Until|All))/,ln={children:!0,contents:!0,next:!0,prev:!0};v.fn.extend({has:function(l){var c=v(l,this),p=c.length;return this.filter(function(){for(var m=0;m<p;m++)if(v.contains(this,c[m]))return!0})},closest:function(l,c){var p,m=0,w=this.length,T=[],D=typeof l!="string"&&v(l);if(!ct.test(l)){for(;m<w;m++)for(p=this[m];p&&p!==c;p=p.parentNode)if(p.nodeType<11&&(D?D.index(p)>-1:p.nodeType===1&&v.find.matchesSelector(p,l))){T.push(p);break}}return this.pushStack(T.length>1?v.uniqueSort(T):T)},index:function(l){return l?typeof l=="string"?f.call(v(l),this[0]):f.call(this,l.jquery?l[0]:l):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(l,c){return this.pushStack(v.uniqueSort(v.merge(this.get(),v(l,c))))},addBack:function(l){return this.add(l==null?this.prevObject:this.prevObject.filter(l))}});function sr(l,c){for(;(l=l[c])&&l.nodeType!==1;);return l}v.each({parent:function(l){var c=l.parentNode;return c&&c.nodeType!==11?c:null},parents:function(l){return Le(l,"parentNode")},parentsUntil:function(l,c,p){return Le(l,"parentNode",p)},next:function(l){return sr(l,"nextSibling")},prev:function(l){return sr(l,"previousSibling")},nextAll:function(l){return Le(l,"nextSibling")},prevAll:function(l){return Le(l,"previousSibling")},nextUntil:function(l,c,p){return Le(l,"nextSibling",p)},prevUntil:function(l,c,p){return Le(l,"previousSibling",p)},siblings:function(l){return Fe((l.parentNode||{}).firstChild,l)},children:function(l){return Fe(l.firstChild)},contents:function(l){return l.contentDocument!=null&&a(l.contentDocument)?l.contentDocument:(G(l,"template")&&(l=l.content||l),v.merge([],l.childNodes))}},function(l,c){v.fn[l]=function(p,m){var w=v.map(this,c,p);return l.slice(-5)!=="Until"&&(m=p),m&&typeof m=="string"&&(w=v.filter(m,w)),this.length>1&&(ln[l]||v.uniqueSort(w),_e.test(l)&&w.reverse()),this.pushStack(w)}});var Nt=/[^\x20\t\r\n\f]+/g;function Jr(l){var c={};return v.each(l.match(Nt)||[],function(p,m){c[m]=!0}),c}v.Callbacks=function(l){l=typeof l=="string"?Jr(l):v.extend({},l);var c,p,m,w,T=[],D=[],I=-1,N=function(){for(w=w||l.once,m=c=!0;D.length;I=-1)for(p=D.shift();++I<T.length;)T[I].apply(p[0],p[1])===!1&&l.stopOnFalse&&(I=T.length,p=!1);l.memory||(p=!1),c=!1,w&&(p?T=[]:T="")},P={add:function(){return T&&(p&&!c&&(I=T.length-1,D.push(p)),function j(q){v.each(q,function(H,J){S(J)?(!l.unique||!P.has(J))&&T.push(J):J&&J.length&&W(J)!=="string"&&j(J)})}(arguments),p&&!c&&N()),this},remove:function(){return v.each(arguments,function(j,q){for(var H;(H=v.inArray(q,T,H))>-1;)T.splice(H,1),H<=I&&I--}),this},has:function(j){return j?v.inArray(j,T)>-1:T.length>0},empty:function(){return T&&(T=[]),this},disable:function(){return w=D=[],T=p="",this},disabled:function(){return!T},lock:function(){return w=D=[],!p&&!c&&(T=p=""),this},locked:function(){return!!w},fireWith:function(j,q){return w||(q=q||[],q=[j,q.slice?q.slice():q],D.push(q),c||N()),this},fire:function(){return P.fireWith(this,arguments),this},fired:function(){return!!m}};return P};function Dn(l){return l}function Wn(l){throw l}function ri(l,c,p,m){var w;try{l&&S(w=l.promise)?w.call(l).done(c).fail(p):l&&S(w=l.then)?w.call(l,c,p):c.apply(void 0,[l].slice(m))}catch(T){p.apply(void 0,[T])}}v.extend({Deferred:function(l){var c=[["notify","progress",v.Callbacks("memory"),v.Callbacks("memory"),2],["resolve","done",v.Callbacks("once memory"),v.Callbacks("once memory"),0,"resolved"],["reject","fail",v.Callbacks("once memory"),v.Callbacks("once memory"),1,"rejected"]],p="pending",m={state:function(){return p},always:function(){return w.done(arguments).fail(arguments),this},catch:function(T){return m.then(null,T)},pipe:function(){var T=arguments;return v.Deferred(function(D){v.each(c,function(I,N){var P=S(T[N[4]])&&T[N[4]];w[N[1]](function(){var j=P&&P.apply(this,arguments);j&&S(j.promise)?j.promise().progress(D.notify).done(D.resolve).fail(D.reject):D[N[0]+"With"](this,P?[j]:arguments)})}),T=null}).promise()},then:function(T,D,I){var N=0;function P(j,q,H,J){return function(){var ge=this,Oe=arguments,Ce=function(){var Ye,Xt;if(!(j<N)){if(Ye=H.apply(ge,Oe),Ye===q.promise())throw new TypeError("Thenable self-resolution");Xt=Ye&&(typeof Ye=="object"||typeof Ye=="function")&&Ye.then,S(Xt)?J?Xt.call(Ye,P(N,q,Dn,J),P(N,q,Wn,J)):(N++,Xt.call(Ye,P(N,q,Dn,J),P(N,q,Wn,J),P(N,q,Dn,q.notifyWith))):(H!==Dn&&(ge=void 0,Oe=[Ye]),(J||q.resolveWith)(ge,Oe))}},et=J?Ce:function(){try{Ce()}catch(Ye){v.Deferred.exceptionHook&&v.Deferred.exceptionHook(Ye,et.error),j+1>=N&&(H!==Wn&&(ge=void 0,Oe=[Ye]),q.rejectWith(ge,Oe))}};j?et():(v.Deferred.getErrorHook?et.error=v.Deferred.getErrorHook():v.Deferred.getStackHook&&(et.error=v.Deferred.getStackHook()),t.setTimeout(et))}}return v.Deferred(function(j){c[0][3].add(P(0,j,S(I)?I:Dn,j.notifyWith)),c[1][3].add(P(0,j,S(T)?T:Dn)),c[2][3].add(P(0,j,S(D)?D:Wn))}).promise()},promise:function(T){return T!=null?v.extend(T,m):m}},w={};return v.each(c,function(T,D){var I=D[2],N=D[5];m[D[1]]=I.add,N&&I.add(function(){p=N},c[3-T][2].disable,c[3-T][3].disable,c[0][2].lock,c[0][3].lock),I.add(D[3].fire),w[D[0]]=function(){return w[D[0]+"With"](this===w?void 0:this,arguments),this},w[D[0]+"With"]=I.fireWith}),m.promise(w),l&&l.call(w,w),w},when:function(l){var c=arguments.length,p=c,m=Array(p),w=o.call(arguments),T=v.Deferred(),D=function(I){return function(N){m[I]=this,w[I]=arguments.length>1?o.call(arguments):N,--c||T.resolveWith(m,w)}};if(c<=1&&(ri(l,T.done(D(p)).resolve,T.reject,!c),T.state()==="pending"||S(w[p]&&w[p].then)))return T.then();for(;p--;)ri(w[p],D(p),T.reject);return T.promise()}});var Oo=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;v.Deferred.exceptionHook=function(l,c){t.console&&t.console.warn&&l&&Oo.test(l.name)&&t.console.warn("jQuery.Deferred exception: "+l.message,l.stack,c)},v.readyException=function(l){t.setTimeout(function(){throw l})};var lr=v.Deferred();v.fn.ready=function(l){return lr.then(l).catch(function(c){v.readyException(c)}),this},v.extend({isReady:!1,readyWait:1,ready:function(l){(l===!0?--v.readyWait:v.isReady)||(v.isReady=!0,!(l!==!0&&--v.readyWait>0)&&lr.resolveWith(O,[v]))}}),v.ready.then=lr.then;function Vn(){O.removeEventListener("DOMContentLoaded",Vn),t.removeEventListener("load",Vn),v.ready()}O.readyState==="complete"||O.readyState!=="loading"&&!O.documentElement.doScroll?t.setTimeout(v.ready):(O.addEventListener("DOMContentLoaded",Vn),t.addEventListener("load",Vn));var Mt=function(l,c,p,m,w,T,D){var I=0,N=l.length,P=p==null;if(W(p)==="object"){w=!0;for(I in p)Mt(l,c,I,p[I],!0,T,D)}else if(m!==void 0&&(w=!0,S(m)||(D=!0),P&&(D?(c.call(l,m),c=null):(P=c,c=function(j,q,H){return P.call(v(j),H)})),c))for(;I<N;I++)c(l[I],p,D?m:m.call(l[I],I,c(l[I],p)));return w?l:P?c.call(l):N?c(l[0],p):T},Io=/^-ms-/,un=/-([a-z])/g;function ur(l,c){return c.toUpperCase()}function Tt(l){return l.replace(Io,"ms-").replace(un,ur)}var Sn=function(l){return l.nodeType===1||l.nodeType===9||!+l.nodeType};function fn(){this.expando=v.expando+fn.uid++}fn.uid=1,fn.prototype={cache:function(l){var c=l[this.expando];return c||(c={},Sn(l)&&(l.nodeType?l[this.expando]=c:Object.defineProperty(l,this.expando,{value:c,configurable:!0}))),c},set:function(l,c,p){var m,w=this.cache(l);if(typeof c=="string")w[Tt(c)]=p;else for(m in c)w[Tt(m)]=c[m];return w},get:function(l,c){return c===void 0?this.cache(l):l[this.expando]&&l[this.expando][Tt(c)]},access:function(l,c,p){return c===void 0||c&&typeof c=="string"&&p===void 0?this.get(l,c):(this.set(l,c,p),p!==void 0?p:c)},remove:function(l,c){var p,m=l[this.expando];if(m!==void 0){if(c!==void 0)for(Array.isArray(c)?c=c.map(Tt):(c=Tt(c),c=c in m?[c]:c.match(Nt)||[]),p=c.length;p--;)delete m[c[p]];(c===void 0||v.isEmptyObject(m))&&(l.nodeType?l[this.expando]=void 0:delete l[this.expando])}},hasData:function(l){var c=l[this.expando];return c!==void 0&&!v.isEmptyObject(c)}};var le=new fn,Ze=new fn,ai=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ii=/[A-Z]/g;function op(l){return l==="true"?!0:l==="false"?!1:l==="null"?null:l===+l+""?+l:ai.test(l)?JSON.parse(l):l}function hu(l,c,p){var m;if(p===void 0&&l.nodeType===1)if(m="data-"+c.replace(ii,"-$&").toLowerCase(),p=l.getAttribute(m),typeof p=="string"){try{p=op(p)}catch{}Ze.set(l,c,p)}else p=void 0;return p}v.extend({hasData:function(l){return Ze.hasData(l)||le.hasData(l)},data:function(l,c,p){return Ze.access(l,c,p)},removeData:function(l,c){Ze.remove(l,c)},_data:function(l,c,p){return le.access(l,c,p)},_removeData:function(l,c){le.remove(l,c)}}),v.fn.extend({data:function(l,c){var p,m,w,T=this[0],D=T&&T.attributes;if(l===void 0){if(this.length&&(w=Ze.get(T),T.nodeType===1&&!le.get(T,"hasDataAttrs"))){for(p=D.length;p--;)D[p]&&(m=D[p].name,m.indexOf("data-")===0&&(m=Tt(m.slice(5)),hu(T,m,w[m])));le.set(T,"hasDataAttrs",!0)}return w}return typeof l=="object"?this.each(function(){Ze.set(this,l)}):Mt(this,function(I){var N;if(T&&I===void 0)return N=Ze.get(T,l),N!==void 0||(N=hu(T,l),N!==void 0)?N:void 0;this.each(function(){Ze.set(this,l,I)})},null,c,arguments.length>1,null,!0)},removeData:function(l){return this.each(function(){Ze.remove(this,l)})}}),v.extend({queue:function(l,c,p){var m;if(l)return c=(c||"fx")+"queue",m=le.get(l,c),p&&(!m||Array.isArray(p)?m=le.access(l,c,v.makeArray(p)):m.push(p)),m||[]},dequeue:function(l,c){c=c||"fx";var p=v.queue(l,c),m=p.length,w=p.shift(),T=v._queueHooks(l,c),D=function(){v.dequeue(l,c)};w==="inprogress"&&(w=p.shift(),m--),w&&(c==="fx"&&p.unshift("inprogress"),delete T.stop,w.call(l,D,T)),!m&&T&&T.empty.fire()},_queueHooks:function(l,c){var p=c+"queueHooks";return le.get(l,p)||le.access(l,p,{empty:v.Callbacks("once memory").add(function(){le.remove(l,[c+"queue",p])})})}}),v.fn.extend({queue:function(l,c){var p=2;return typeof l!="string"&&(c=l,l="fx",p--),arguments.length<p?v.queue(this[0],l):c===void 0?this:this.each(function(){var m=v.queue(this,l,c);v._queueHooks(this,l),l==="fx"&&m[0]!=="inprogress"&&v.dequeue(this,l)})},dequeue:function(l){return this.each(function(){v.dequeue(this,l)})},clearQueue:function(l){return this.queue(l||"fx",[])},promise:function(l,c){var p,m=1,w=v.Deferred(),T=this,D=this.length,I=function(){--m||w.resolveWith(T,[T])};for(typeof l!="string"&&(c=l,l=void 0),l=l||"fx";D--;)p=le.get(T[D],l+"queueHooks"),p&&p.empty&&(m++,p.empty.add(I));return I(),w.promise(c)}});var pu=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Qr=new RegExp("^(?:([+-])=|)("+pu+")([a-z%]*)$","i"),cn=["Top","Right","Bottom","Left"],qn=O.documentElement,fr=function(l){return v.contains(l.ownerDocument,l)},sp={composed:!0};qn.getRootNode&&(fr=function(l){return v.contains(l.ownerDocument,l)||l.getRootNode(sp)===l.ownerDocument});var oi=function(l,c){return l=c||l,l.style.display==="none"||l.style.display===""&&fr(l)&&v.css(l,"display")==="none"};function vu(l,c,p,m){var w,T,D=20,I=m?function(){return m.cur()}:function(){return v.css(l,c,"")},N=I(),P=p&&p[3]||(v.cssNumber[c]?"":"px"),j=l.nodeType&&(v.cssNumber[c]||P!=="px"&&+N)&&Qr.exec(v.css(l,c));if(j&&j[3]!==P){for(N=N/2,P=P||j[3],j=+N||1;D--;)v.style(l,c,j+P),(1-T)*(1-(T=I()/N||.5))<=0&&(D=0),j=j/T;j=j*2,v.style(l,c,j+P),p=p||[]}return p&&(j=+j||+N||0,w=p[1]?j+(p[1]+1)*p[2]:+p[2],m&&(m.unit=P,m.start=j,m.end=w)),w}var mu={};function lp(l){var c,p=l.ownerDocument,m=l.nodeName,w=mu[m];return w||(c=p.body.appendChild(p.createElement(m)),w=v.css(c,"display"),c.parentNode.removeChild(c),w==="none"&&(w="block"),mu[m]=w,w)}function cr(l,c){for(var p,m,w=[],T=0,D=l.length;T<D;T++)m=l[T],m.style&&(p=m.style.display,c?(p==="none"&&(w[T]=le.get(m,"display")||null,w[T]||(m.style.display="")),m.style.display===""&&oi(m)&&(w[T]=lp(m))):p!=="none"&&(w[T]="none",le.set(m,"display",p)));for(T=0;T<D;T++)w[T]!=null&&(l[T].style.display=w[T]);return l}v.fn.extend({show:function(){return cr(this,!0)},hide:function(){return cr(this)},toggle:function(l){return typeof l=="boolean"?l?this.show():this.hide():this.each(function(){oi(this)?v(this).show():v(this).hide()})}});var Zr=/^(?:checkbox|radio)$/i,bu=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,_u=/^$|^module$|\/(?:java|ecma)script/i;(function(){var l=O.createDocumentFragment(),c=l.appendChild(O.createElement("div")),p=O.createElement("input");p.setAttribute("type","radio"),p.setAttribute("checked","checked"),p.setAttribute("name","t"),c.appendChild(p),g.checkClone=c.cloneNode(!0).cloneNode(!0).lastChild.checked,c.innerHTML="<textarea>x</textarea>",g.noCloneChecked=!!c.cloneNode(!0).lastChild.defaultValue,c.innerHTML="<option></option>",g.option=!!c.lastChild})();var Lt={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};Lt.tbody=Lt.tfoot=Lt.colgroup=Lt.caption=Lt.thead,Lt.th=Lt.td,g.option||(Lt.optgroup=Lt.option=[1,"<select multiple='multiple'>","</select>"]);function dt(l,c){var p;return typeof l.getElementsByTagName<"u"?p=l.getElementsByTagName(c||"*"):typeof l.querySelectorAll<"u"?p=l.querySelectorAll(c||"*"):p=[],c===void 0||c&&G(l,c)?v.merge([l],p):p}function Ro(l,c){for(var p=0,m=l.length;p<m;p++)le.set(l[p],"globalEval",!c||le.get(c[p],"globalEval"))}var up=/<|&#?\w+;/;function gu(l,c,p,m,w){for(var T,D,I,N,P,j,q=c.createDocumentFragment(),H=[],J=0,ge=l.length;J<ge;J++)if(T=l[J],T||T===0)if(W(T)==="object")v.merge(H,T.nodeType?[T]:T);else if(!up.test(T))H.push(c.createTextNode(T));else{for(D=D||q.appendChild(c.createElement("div")),I=(bu.exec(T)||["",""])[1].toLowerCase(),N=Lt[I]||Lt._default,D.innerHTML=N[1]+v.htmlPrefilter(T)+N[2],j=N[0];j--;)D=D.lastChild;v.merge(H,D.childNodes),D=q.firstChild,D.textContent=""}for(q.textContent="",J=0;T=H[J++];){if(m&&v.inArray(T,m)>-1){w&&w.push(T);continue}if(P=fr(T),D=dt(q.appendChild(T),"script"),P&&Ro(D),p)for(j=0;T=D[j++];)_u.test(T.type||"")&&p.push(T)}return q}var yu=/^([^.]*)(?:\.(.+)|)/;function dr(){return!0}function hr(){return!1}function Po(l,c,p,m,w,T){var D,I;if(typeof c=="object"){typeof p!="string"&&(m=m||p,p=void 0);for(I in c)Po(l,I,p,m,c[I],T);return l}if(m==null&&w==null?(w=p,m=p=void 0):w==null&&(typeof p=="string"?(w=m,m=void 0):(w=m,m=p,p=void 0)),w===!1)w=hr;else if(!w)return l;return T===1&&(D=w,w=function(N){return v().off(N),D.apply(this,arguments)},w.guid=D.guid||(D.guid=v.guid++)),l.each(function(){v.event.add(this,c,w,m,p)})}v.event={global:{},add:function(l,c,p,m,w){var T,D,I,N,P,j,q,H,J,ge,Oe,Ce=le.get(l);if(Sn(l))for(p.handler&&(T=p,p=T.handler,w=T.selector),w&&v.find.matchesSelector(qn,w),p.guid||(p.guid=v.guid++),(N=Ce.events)||(N=Ce.events=Object.create(null)),(D=Ce.handle)||(D=Ce.handle=function(et){return typeof v<"u"&&v.event.triggered!==et.type?v.event.dispatch.apply(l,arguments):void 0}),c=(c||"").match(Nt)||[""],P=c.length;P--;)I=yu.exec(c[P])||[],J=Oe=I[1],ge=(I[2]||"").split(".").sort(),J&&(q=v.event.special[J]||{},J=(w?q.delegateType:q.bindType)||J,q=v.event.special[J]||{},j=v.extend({type:J,origType:Oe,data:m,handler:p,guid:p.guid,selector:w,needsContext:w&&v.expr.match.needsContext.test(w),namespace:ge.join(".")},T),(H=N[J])||(H=N[J]=[],H.delegateCount=0,(!q.setup||q.setup.call(l,m,ge,D)===!1)&&l.addEventListener&&l.addEventListener(J,D)),q.add&&(q.add.call(l,j),j.handler.guid||(j.handler.guid=p.guid)),w?H.splice(H.delegateCount++,0,j):H.push(j),v.event.global[J]=!0)},remove:function(l,c,p,m,w){var T,D,I,N,P,j,q,H,J,ge,Oe,Ce=le.hasData(l)&&le.get(l);if(!(!Ce||!(N=Ce.events))){for(c=(c||"").match(Nt)||[""],P=c.length;P--;){if(I=yu.exec(c[P])||[],J=Oe=I[1],ge=(I[2]||"").split(".").sort(),!J){for(J in N)v.event.remove(l,J+c[P],p,m,!0);continue}for(q=v.event.special[J]||{},J=(m?q.delegateType:q.bindType)||J,H=N[J]||[],I=I[2]&&new RegExp("(^|\\.)"+ge.join("\\.(?:.*\\.|)")+"(\\.|$)"),D=T=H.length;T--;)j=H[T],(w||Oe===j.origType)&&(!p||p.guid===j.guid)&&(!I||I.test(j.namespace))&&(!m||m===j.selector||m==="**"&&j.selector)&&(H.splice(T,1),j.selector&&H.delegateCount--,q.remove&&q.remove.call(l,j));D&&!H.length&&((!q.teardown||q.teardown.call(l,ge,Ce.handle)===!1)&&v.removeEvent(l,J,Ce.handle),delete N[J])}v.isEmptyObject(N)&&le.remove(l,"handle events")}},dispatch:function(l){var c,p,m,w,T,D,I=new Array(arguments.length),N=v.event.fix(l),P=(le.get(this,"events")||Object.create(null))[N.type]||[],j=v.event.special[N.type]||{};for(I[0]=N,c=1;c<arguments.length;c++)I[c]=arguments[c];if(N.delegateTarget=this,!(j.preDispatch&&j.preDispatch.call(this,N)===!1)){for(D=v.event.handlers.call(this,N,P),c=0;(w=D[c++])&&!N.isPropagationStopped();)for(N.currentTarget=w.elem,p=0;(T=w.handlers[p++])&&!N.isImmediatePropagationStopped();)(!N.rnamespace||T.namespace===!1||N.rnamespace.test(T.namespace))&&(N.handleObj=T,N.data=T.data,m=((v.event.special[T.origType]||{}).handle||T.handler).apply(w.elem,I),m!==void 0&&(N.result=m)===!1&&(N.preventDefault(),N.stopPropagation()));return j.postDispatch&&j.postDispatch.call(this,N),N.result}},handlers:function(l,c){var p,m,w,T,D,I=[],N=c.delegateCount,P=l.target;if(N&&P.nodeType&&!(l.type==="click"&&l.button>=1)){for(;P!==this;P=P.parentNode||this)if(P.nodeType===1&&!(l.type==="click"&&P.disabled===!0)){for(T=[],D={},p=0;p<N;p++)m=c[p],w=m.selector+" ",D[w]===void 0&&(D[w]=m.needsContext?v(w,this).index(P)>-1:v.find(w,this,null,[P]).length),D[w]&&T.push(m);T.length&&I.push({elem:P,handlers:T})}}return P=this,N<c.length&&I.push({elem:P,handlers:c.slice(N)}),I},addProp:function(l,c){Object.defineProperty(v.Event.prototype,l,{enumerable:!0,configurable:!0,get:S(c)?function(){if(this.originalEvent)return c(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[l]},set:function(p){Object.defineProperty(this,l,{enumerable:!0,configurable:!0,writable:!0,value:p})}})},fix:function(l){return l[v.expando]?l:new v.Event(l)},special:{load:{noBubble:!0},click:{setup:function(l){var c=this||l;return Zr.test(c.type)&&c.click&&G(c,"input")&&si(c,"click",!0),!1},trigger:function(l){var c=this||l;return Zr.test(c.type)&&c.click&&G(c,"input")&&si(c,"click"),!0},_default:function(l){var c=l.target;return Zr.test(c.type)&&c.click&&G(c,"input")&&le.get(c,"click")||G(c,"a")}},beforeunload:{postDispatch:function(l){l.result!==void 0&&l.originalEvent&&(l.originalEvent.returnValue=l.result)}}}};function si(l,c,p){if(!p){le.get(l,c)===void 0&&v.event.add(l,c,dr);return}le.set(l,c,!1),v.event.add(l,c,{namespace:!1,handler:function(m){var w,T=le.get(this,c);if(m.isTrigger&1&&this[c]){if(T)(v.event.special[c]||{}).delegateType&&m.stopPropagation();else if(T=o.call(arguments),le.set(this,c,T),this[c](),w=le.get(this,c),le.set(this,c,!1),T!==w)return m.stopImmediatePropagation(),m.preventDefault(),w}else T&&(le.set(this,c,v.event.trigger(T[0],T.slice(1),this)),m.stopPropagation(),m.isImmediatePropagationStopped=dr)}})}v.removeEvent=function(l,c,p){l.removeEventListener&&l.removeEventListener(c,p)},v.Event=function(l,c){if(!(this instanceof v.Event))return new v.Event(l,c);l&&l.type?(this.originalEvent=l,this.type=l.type,this.isDefaultPrevented=l.defaultPrevented||l.defaultPrevented===void 0&&l.returnValue===!1?dr:hr,this.target=l.target&&l.target.nodeType===3?l.target.parentNode:l.target,this.currentTarget=l.currentTarget,this.relatedTarget=l.relatedTarget):this.type=l,c&&v.extend(this,c),this.timeStamp=l&&l.timeStamp||Date.now(),this[v.expando]=!0},v.Event.prototype={constructor:v.Event,isDefaultPrevented:hr,isPropagationStopped:hr,isImmediatePropagationStopped:hr,isSimulated:!1,preventDefault:function(){var l=this.originalEvent;this.isDefaultPrevented=dr,l&&!this.isSimulated&&l.preventDefault()},stopPropagation:function(){var l=this.originalEvent;this.isPropagationStopped=dr,l&&!this.isSimulated&&l.stopPropagation()},stopImmediatePropagation:function(){var l=this.originalEvent;this.isImmediatePropagationStopped=dr,l&&!this.isSimulated&&l.stopImmediatePropagation(),this.stopPropagation()}},v.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},v.event.addProp),v.each({focus:"focusin",blur:"focusout"},function(l,c){function p(m){if(O.documentMode){var w=le.get(this,"handle"),T=v.event.fix(m);T.type=m.type==="focusin"?"focus":"blur",T.isSimulated=!0,w(m),T.target===T.currentTarget&&w(T)}else v.event.simulate(c,m.target,v.event.fix(m))}v.event.special[l]={setup:function(){var m;if(si(this,l,!0),O.documentMode)m=le.get(this,c),m||this.addEventListener(c,p),le.set(this,c,(m||0)+1);else return!1},trigger:function(){return si(this,l),!0},teardown:function(){var m;if(O.documentMode)m=le.get(this,c)-1,m?le.set(this,c,m):(this.removeEventListener(c,p),le.remove(this,c));else return!1},_default:function(m){return le.get(m.target,l)},delegateType:c},v.event.special[c]={setup:function(){var m=this.ownerDocument||this.document||this,w=O.documentMode?this:m,T=le.get(w,c);T||(O.documentMode?this.addEventListener(c,p):m.addEventListener(l,p,!0)),le.set(w,c,(T||0)+1)},teardown:function(){var m=this.ownerDocument||this.document||this,w=O.documentMode?this:m,T=le.get(w,c)-1;T?le.set(w,c,T):(O.documentMode?this.removeEventListener(c,p):m.removeEventListener(l,p,!0),le.remove(w,c))}}}),v.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(l,c){v.event.special[l]={delegateType:c,bindType:c,handle:function(p){var m,w=this,T=p.relatedTarget,D=p.handleObj;return(!T||T!==w&&!v.contains(w,T))&&(p.type=D.origType,m=D.handler.apply(this,arguments),p.type=c),m}}}),v.fn.extend({on:function(l,c,p,m){return Po(this,l,c,p,m)},one:function(l,c,p,m){return Po(this,l,c,p,m,1)},off:function(l,c,p){var m,w;if(l&&l.preventDefault&&l.handleObj)return m=l.handleObj,v(l.delegateTarget).off(m.namespace?m.origType+"."+m.namespace:m.origType,m.selector,m.handler),this;if(typeof l=="object"){for(w in l)this.off(w,c,l[w]);return this}return(c===!1||typeof c=="function")&&(p=c,c=void 0),p===!1&&(p=hr),this.each(function(){v.event.remove(this,l,p,c)})}});var fp=/<script|<style|<link/i,cp=/checked\s*(?:[^=]|=\s*.checked.)/i,dp=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function wu(l,c){return G(l,"table")&&G(c.nodeType!==11?c:c.firstChild,"tr")&&v(l).children("tbody")[0]||l}function hp(l){return l.type=(l.getAttribute("type")!==null)+"/"+l.type,l}function pp(l){return(l.type||"").slice(0,5)==="true/"?l.type=l.type.slice(5):l.removeAttribute("type"),l}function Tu(l,c){var p,m,w,T,D,I,N;if(c.nodeType===1){if(le.hasData(l)&&(T=le.get(l),N=T.events,N)){le.remove(c,"handle events");for(w in N)for(p=0,m=N[w].length;p<m;p++)v.event.add(c,w,N[w][p])}Ze.hasData(l)&&(D=Ze.access(l),I=v.extend({},D),Ze.set(c,I))}}function vp(l,c){var p=c.nodeName.toLowerCase();p==="input"&&Zr.test(l.type)?c.checked=l.checked:(p==="input"||p==="textarea")&&(c.defaultValue=l.defaultValue)}function pr(l,c,p,m){c=s(c);var w,T,D,I,N,P,j=0,q=l.length,H=q-1,J=c[0],ge=S(J);if(ge||q>1&&typeof J=="string"&&!g.checkClone&&cp.test(J))return l.each(function(Oe){var Ce=l.eq(Oe);ge&&(c[0]=J.call(this,Oe,Ce.html())),pr(Ce,c,p,m)});if(q&&(w=gu(c,l[0].ownerDocument,!1,l,m),T=w.firstChild,w.childNodes.length===1&&(w=T),T||m)){for(D=v.map(dt(w,"script"),hp),I=D.length;j<q;j++)N=w,j!==H&&(N=v.clone(N,!0,!0),I&&v.merge(D,dt(N,"script"))),p.call(l[j],N,j);if(I)for(P=D[D.length-1].ownerDocument,v.map(D,pp),j=0;j<I;j++)N=D[j],_u.test(N.type||"")&&!le.access(N,"globalEval")&&v.contains(P,N)&&(N.src&&(N.type||"").toLowerCase()!=="module"?v._evalUrl&&!N.noModule&&v._evalUrl(N.src,{nonce:N.nonce||N.getAttribute("nonce")},P):Y(N.textContent.replace(dp,""),N,P))}return l}function Cu(l,c,p){for(var m,w=c?v.filter(c,l):l,T=0;(m=w[T])!=null;T++)!p&&m.nodeType===1&&v.cleanData(dt(m)),m.parentNode&&(p&&fr(m)&&Ro(dt(m,"script")),m.parentNode.removeChild(m));return l}v.extend({htmlPrefilter:function(l){return l},clone:function(l,c,p){var m,w,T,D,I=l.cloneNode(!0),N=fr(l);if(!g.noCloneChecked&&(l.nodeType===1||l.nodeType===11)&&!v.isXMLDoc(l))for(D=dt(I),T=dt(l),m=0,w=T.length;m<w;m++)vp(T[m],D[m]);if(c)if(p)for(T=T||dt(l),D=D||dt(I),m=0,w=T.length;m<w;m++)Tu(T[m],D[m]);else Tu(l,I);return D=dt(I,"script"),D.length>0&&Ro(D,!N&&dt(l,"script")),I},cleanData:function(l){for(var c,p,m,w=v.event.special,T=0;(p=l[T])!==void 0;T++)if(Sn(p)){if(c=p[le.expando]){if(c.events)for(m in c.events)w[m]?v.event.remove(p,m):v.removeEvent(p,m,c.handle);p[le.expando]=void 0}p[Ze.expando]&&(p[Ze.expando]=void 0)}}}),v.fn.extend({detach:function(l){return Cu(this,l,!0)},remove:function(l){return Cu(this,l)},text:function(l){return Mt(this,function(c){return c===void 0?v.text(this):this.empty().each(function(){(this.nodeType===1||this.nodeType===11||this.nodeType===9)&&(this.textContent=c)})},null,l,arguments.length)},append:function(){return pr(this,arguments,function(l){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var c=wu(this,l);c.appendChild(l)}})},prepend:function(){return pr(this,arguments,function(l){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var c=wu(this,l);c.insertBefore(l,c.firstChild)}})},before:function(){return pr(this,arguments,function(l){this.parentNode&&this.parentNode.insertBefore(l,this)})},after:function(){return pr(this,arguments,function(l){this.parentNode&&this.parentNode.insertBefore(l,this.nextSibling)})},empty:function(){for(var l,c=0;(l=this[c])!=null;c++)l.nodeType===1&&(v.cleanData(dt(l,!1)),l.textContent="");return this},clone:function(l,c){return l=l??!1,c=c??l,this.map(function(){return v.clone(this,l,c)})},html:function(l){return Mt(this,function(c){var p=this[0]||{},m=0,w=this.length;if(c===void 0&&p.nodeType===1)return p.innerHTML;if(typeof c=="string"&&!fp.test(c)&&!Lt[(bu.exec(c)||["",""])[1].toLowerCase()]){c=v.htmlPrefilter(c);try{for(;m<w;m++)p=this[m]||{},p.nodeType===1&&(v.cleanData(dt(p,!1)),p.innerHTML=c);p=0}catch{}}p&&this.empty().append(c)},null,l,arguments.length)},replaceWith:function(){var l=[];return pr(this,arguments,function(c){var p=this.parentNode;v.inArray(this,l)<0&&(v.cleanData(dt(this)),p&&p.replaceChild(c,this))},l)}}),v.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(l,c){v.fn[l]=function(p){for(var m,w=[],T=v(p),D=T.length-1,I=0;I<=D;I++)m=I===D?this:this.clone(!0),v(T[I])[c](m),u.apply(w,m.get());return this.pushStack(w)}});var ko=new RegExp("^("+pu+")(?!px)[a-z%]+$","i"),$o=/^--/,li=function(l){var c=l.ownerDocument.defaultView;return(!c||!c.opener)&&(c=t),c.getComputedStyle(l)},Du=function(l,c,p){var m,w,T={};for(w in c)T[w]=l.style[w],l.style[w]=c[w];m=p.call(l);for(w in c)l.style[w]=T[w];return m},mp=new RegExp(cn.join("|"),"i");(function(){function l(){if(P){N.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",P.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",qn.appendChild(N).appendChild(P);var j=t.getComputedStyle(P);p=j.top!=="1%",I=c(j.marginLeft)===12,P.style.right="60%",T=c(j.right)===36,m=c(j.width)===36,P.style.position="absolute",w=c(P.offsetWidth/3)===12,qn.removeChild(N),P=null}}function c(j){return Math.round(parseFloat(j))}var p,m,w,T,D,I,N=O.createElement("div"),P=O.createElement("div");P.style&&(P.style.backgroundClip="content-box",P.cloneNode(!0).style.backgroundClip="",g.clearCloneStyle=P.style.backgroundClip==="content-box",v.extend(g,{boxSizingReliable:function(){return l(),m},pixelBoxStyles:function(){return l(),T},pixelPosition:function(){return l(),p},reliableMarginLeft:function(){return l(),I},scrollboxSize:function(){return l(),w},reliableTrDimensions:function(){var j,q,H,J;return D==null&&(j=O.createElement("table"),q=O.createElement("tr"),H=O.createElement("div"),j.style.cssText="position:absolute;left:-11111px;border-collapse:separate",q.style.cssText="box-sizing:content-box;border:1px solid",q.style.height="1px",H.style.height="9px",H.style.display="block",qn.appendChild(j).appendChild(q).appendChild(H),J=t.getComputedStyle(q),D=parseInt(J.height,10)+parseInt(J.borderTopWidth,10)+parseInt(J.borderBottomWidth,10)===q.offsetHeight,qn.removeChild(j)),D}}))})();function ea(l,c,p){var m,w,T,D,I=$o.test(c),N=l.style;return p=p||li(l),p&&(D=p.getPropertyValue(c)||p[c],I&&D&&(D=D.replace(ae,"$1")||void 0),D===""&&!fr(l)&&(D=v.style(l,c)),!g.pixelBoxStyles()&&ko.test(D)&&mp.test(c)&&(m=N.width,w=N.minWidth,T=N.maxWidth,N.minWidth=N.maxWidth=N.width=D,D=p.width,N.width=m,N.minWidth=w,N.maxWidth=T)),D!==void 0?D+"":D}function Su(l,c){return{get:function(){if(l()){delete this.get;return}return(this.get=c).apply(this,arguments)}}}var xu=["Webkit","Moz","ms"],Au=O.createElement("div").style,Eu={};function bp(l){for(var c=l[0].toUpperCase()+l.slice(1),p=xu.length;p--;)if(l=xu[p]+c,l in Au)return l}function Fo(l){var c=v.cssProps[l]||Eu[l];return c||(l in Au?l:Eu[l]=bp(l)||l)}var _p=/^(none|table(?!-c[ea]).+)/,gp={position:"absolute",visibility:"hidden",display:"block"},Nu={letterSpacing:"0",fontWeight:"400"};function Lu(l,c,p){var m=Qr.exec(c);return m?Math.max(0,m[2]-(p||0))+(m[3]||"px"):c}function Ho(l,c,p,m,w,T){var D=c==="width"?1:0,I=0,N=0,P=0;if(p===(m?"border":"content"))return 0;for(;D<4;D+=2)p==="margin"&&(P+=v.css(l,p+cn[D],!0,w)),m?(p==="content"&&(N-=v.css(l,"padding"+cn[D],!0,w)),p!=="margin"&&(N-=v.css(l,"border"+cn[D]+"Width",!0,w))):(N+=v.css(l,"padding"+cn[D],!0,w),p!=="padding"?N+=v.css(l,"border"+cn[D]+"Width",!0,w):I+=v.css(l,"border"+cn[D]+"Width",!0,w));return!m&&T>=0&&(N+=Math.max(0,Math.ceil(l["offset"+c[0].toUpperCase()+c.slice(1)]-T-N-I-.5))||0),N+P}function Ou(l,c,p){var m=li(l),w=!g.boxSizingReliable()||p,T=w&&v.css(l,"boxSizing",!1,m)==="border-box",D=T,I=ea(l,c,m),N="offset"+c[0].toUpperCase()+c.slice(1);if(ko.test(I)){if(!p)return I;I="auto"}return(!g.boxSizingReliable()&&T||!g.reliableTrDimensions()&&G(l,"tr")||I==="auto"||!parseFloat(I)&&v.css(l,"display",!1,m)==="inline")&&l.getClientRects().length&&(T=v.css(l,"boxSizing",!1,m)==="border-box",D=N in l,D&&(I=l[N])),I=parseFloat(I)||0,I+Ho(l,c,p||(T?"border":"content"),D,m,I)+"px"}v.extend({cssHooks:{opacity:{get:function(l,c){if(c){var p=ea(l,"opacity");return p===""?"1":p}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(l,c,p,m){if(!(!l||l.nodeType===3||l.nodeType===8||!l.style)){var w,T,D,I=Tt(c),N=$o.test(c),P=l.style;if(N||(c=Fo(I)),D=v.cssHooks[c]||v.cssHooks[I],p!==void 0){if(T=typeof p,T==="string"&&(w=Qr.exec(p))&&w[1]&&(p=vu(l,c,w),T="number"),p==null||p!==p)return;T==="number"&&!N&&(p+=w&&w[3]||(v.cssNumber[I]?"":"px")),!g.clearCloneStyle&&p===""&&c.indexOf("background")===0&&(P[c]="inherit"),(!D||!("set"in D)||(p=D.set(l,p,m))!==void 0)&&(N?P.setProperty(c,p):P[c]=p)}else return D&&"get"in D&&(w=D.get(l,!1,m))!==void 0?w:P[c]}},css:function(l,c,p,m){var w,T,D,I=Tt(c),N=$o.test(c);return N||(c=Fo(I)),D=v.cssHooks[c]||v.cssHooks[I],D&&"get"in D&&(w=D.get(l,!0,p)),w===void 0&&(w=ea(l,c,m)),w==="normal"&&c in Nu&&(w=Nu[c]),p===""||p?(T=parseFloat(w),p===!0||isFinite(T)?T||0:w):w}}),v.each(["height","width"],function(l,c){v.cssHooks[c]={get:function(p,m,w){if(m)return _p.test(v.css(p,"display"))&&(!p.getClientRects().length||!p.getBoundingClientRect().width)?Du(p,gp,function(){return Ou(p,c,w)}):Ou(p,c,w)},set:function(p,m,w){var T,D=li(p),I=!g.scrollboxSize()&&D.position==="absolute",N=I||w,P=N&&v.css(p,"boxSizing",!1,D)==="border-box",j=w?Ho(p,c,w,P,D):0;return P&&I&&(j-=Math.ceil(p["offset"+c[0].toUpperCase()+c.slice(1)]-parseFloat(D[c])-Ho(p,c,"border",!1,D)-.5)),j&&(T=Qr.exec(m))&&(T[3]||"px")!=="px"&&(p.style[c]=m,m=v.css(p,c)),Lu(p,m,j)}}}),v.cssHooks.marginLeft=Su(g.reliableMarginLeft,function(l,c){if(c)return(parseFloat(ea(l,"marginLeft"))||l.getBoundingClientRect().left-Du(l,{marginLeft:0},function(){return l.getBoundingClientRect().left}))+"px"}),v.each({margin:"",padding:"",border:"Width"},function(l,c){v.cssHooks[l+c]={expand:function(p){for(var m=0,w={},T=typeof p=="string"?p.split(" "):[p];m<4;m++)w[l+cn[m]+c]=T[m]||T[m-2]||T[0];return w}},l!=="margin"&&(v.cssHooks[l+c].set=Lu)}),v.fn.extend({css:function(l,c){return Mt(this,function(p,m,w){var T,D,I={},N=0;if(Array.isArray(m)){for(T=li(p),D=m.length;N<D;N++)I[m[N]]=v.css(p,m[N],!1,T);return I}return w!==void 0?v.style(p,m,w):v.css(p,m)},l,c,arguments.length>1)}});function ht(l,c,p,m,w){return new ht.prototype.init(l,c,p,m,w)}v.Tween=ht,ht.prototype={constructor:ht,init:function(l,c,p,m,w,T){this.elem=l,this.prop=p,this.easing=w||v.easing._default,this.options=c,this.start=this.now=this.cur(),this.end=m,this.unit=T||(v.cssNumber[p]?"":"px")},cur:function(){var l=ht.propHooks[this.prop];return l&&l.get?l.get(this):ht.propHooks._default.get(this)},run:function(l){var c,p=ht.propHooks[this.prop];return this.options.duration?this.pos=c=v.easing[this.easing](l,this.options.duration*l,0,1,this.options.duration):this.pos=c=l,this.now=(this.end-this.start)*c+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),p&&p.set?p.set(this):ht.propHooks._default.set(this),this}},ht.prototype.init.prototype=ht.prototype,ht.propHooks={_default:{get:function(l){var c;return l.elem.nodeType!==1||l.elem[l.prop]!=null&&l.elem.style[l.prop]==null?l.elem[l.prop]:(c=v.css(l.elem,l.prop,""),!c||c==="auto"?0:c)},set:function(l){v.fx.step[l.prop]?v.fx.step[l.prop](l):l.elem.nodeType===1&&(v.cssHooks[l.prop]||l.elem.style[Fo(l.prop)]!=null)?v.style(l.elem,l.prop,l.now+l.unit):l.elem[l.prop]=l.now}}},ht.propHooks.scrollTop=ht.propHooks.scrollLeft={set:function(l){l.elem.nodeType&&l.elem.parentNode&&(l.elem[l.prop]=l.now)}},v.easing={linear:function(l){return l},swing:function(l){return .5-Math.cos(l*Math.PI)/2},_default:"swing"},v.fx=ht.prototype.init,v.fx.step={};var vr,ui,yp=/^(?:toggle|show|hide)$/,wp=/queueHooks$/;function Mo(){ui&&(O.hidden===!1&&t.requestAnimationFrame?t.requestAnimationFrame(Mo):t.setTimeout(Mo,v.fx.interval),v.fx.tick())}function Iu(){return t.setTimeout(function(){vr=void 0}),vr=Date.now()}function fi(l,c){var p,m=0,w={height:l};for(c=c?1:0;m<4;m+=2-c)p=cn[m],w["margin"+p]=w["padding"+p]=l;return c&&(w.opacity=w.width=l),w}function Ru(l,c,p){for(var m,w=(jt.tweeners[c]||[]).concat(jt.tweeners["*"]),T=0,D=w.length;T<D;T++)if(m=w[T].call(p,c,l))return m}function Tp(l,c,p){var m,w,T,D,I,N,P,j,q="width"in c||"height"in c,H=this,J={},ge=l.style,Oe=l.nodeType&&oi(l),Ce=le.get(l,"fxshow");p.queue||(D=v._queueHooks(l,"fx"),D.unqueued==null&&(D.unqueued=0,I=D.empty.fire,D.empty.fire=function(){D.unqueued||I()}),D.unqueued++,H.always(function(){H.always(function(){D.unqueued--,v.queue(l,"fx").length||D.empty.fire()})}));for(m in c)if(w=c[m],yp.test(w)){if(delete c[m],T=T||w==="toggle",w===(Oe?"hide":"show"))if(w==="show"&&Ce&&Ce[m]!==void 0)Oe=!0;else continue;J[m]=Ce&&Ce[m]||v.style(l,m)}if(N=!v.isEmptyObject(c),!(!N&&v.isEmptyObject(J))){q&&l.nodeType===1&&(p.overflow=[ge.overflow,ge.overflowX,ge.overflowY],P=Ce&&Ce.display,P==null&&(P=le.get(l,"display")),j=v.css(l,"display"),j==="none"&&(P?j=P:(cr([l],!0),P=l.style.display||P,j=v.css(l,"display"),cr([l]))),(j==="inline"||j==="inline-block"&&P!=null)&&v.css(l,"float")==="none"&&(N||(H.done(function(){ge.display=P}),P==null&&(j=ge.display,P=j==="none"?"":j)),ge.display="inline-block")),p.overflow&&(ge.overflow="hidden",H.always(function(){ge.overflow=p.overflow[0],ge.overflowX=p.overflow[1],ge.overflowY=p.overflow[2]})),N=!1;for(m in J)N||(Ce?"hidden"in Ce&&(Oe=Ce.hidden):Ce=le.access(l,"fxshow",{display:P}),T&&(Ce.hidden=!Oe),Oe&&cr([l],!0),H.done(function(){Oe||cr([l]),le.remove(l,"fxshow");for(m in J)v.style(l,m,J[m])})),N=Ru(Oe?Ce[m]:0,m,H),m in Ce||(Ce[m]=N.start,Oe&&(N.end=N.start,N.start=0))}}function Cp(l,c){var p,m,w,T,D;for(p in l)if(m=Tt(p),w=c[m],T=l[p],Array.isArray(T)&&(w=T[1],T=l[p]=T[0]),p!==m&&(l[m]=T,delete l[p]),D=v.cssHooks[m],D&&"expand"in D){T=D.expand(T),delete l[m];for(p in T)p in l||(l[p]=T[p],c[p]=w)}else c[m]=w}function jt(l,c,p){var m,w,T=0,D=jt.prefilters.length,I=v.Deferred().always(function(){delete N.elem}),N=function(){if(w)return!1;for(var q=vr||Iu(),H=Math.max(0,P.startTime+P.duration-q),J=H/P.duration||0,ge=1-J,Oe=0,Ce=P.tweens.length;Oe<Ce;Oe++)P.tweens[Oe].run(ge);return I.notifyWith(l,[P,ge,H]),ge<1&&Ce?H:(Ce||I.notifyWith(l,[P,1,0]),I.resolveWith(l,[P]),!1)},P=I.promise({elem:l,props:v.extend({},c),opts:v.extend(!0,{specialEasing:{},easing:v.easing._default},p),originalProperties:c,originalOptions:p,startTime:vr||Iu(),duration:p.duration,tweens:[],createTween:function(q,H){var J=v.Tween(l,P.opts,q,H,P.opts.specialEasing[q]||P.opts.easing);return P.tweens.push(J),J},stop:function(q){var H=0,J=q?P.tweens.length:0;if(w)return this;for(w=!0;H<J;H++)P.tweens[H].run(1);return q?(I.notifyWith(l,[P,1,0]),I.resolveWith(l,[P,q])):I.rejectWith(l,[P,q]),this}}),j=P.props;for(Cp(j,P.opts.specialEasing);T<D;T++)if(m=jt.prefilters[T].call(P,l,j,P.opts),m)return S(m.stop)&&(v._queueHooks(P.elem,P.opts.queue).stop=m.stop.bind(m)),m;return v.map(j,Ru,P),S(P.opts.start)&&P.opts.start.call(l,P),P.progress(P.opts.progress).done(P.opts.done,P.opts.complete).fail(P.opts.fail).always(P.opts.always),v.fx.timer(v.extend(N,{elem:l,anim:P,queue:P.opts.queue})),P}v.Animation=v.extend(jt,{tweeners:{"*":[function(l,c){var p=this.createTween(l,c);return vu(p.elem,l,Qr.exec(c),p),p}]},tweener:function(l,c){S(l)?(c=l,l=["*"]):l=l.match(Nt);for(var p,m=0,w=l.length;m<w;m++)p=l[m],jt.tweeners[p]=jt.tweeners[p]||[],jt.tweeners[p].unshift(c)},prefilters:[Tp],prefilter:function(l,c){c?jt.prefilters.unshift(l):jt.prefilters.push(l)}}),v.speed=function(l,c,p){var m=l&&typeof l=="object"?v.extend({},l):{complete:p||!p&&c||S(l)&&l,duration:l,easing:p&&c||c&&!S(c)&&c};return v.fx.off?m.duration=0:typeof m.duration!="number"&&(m.duration in v.fx.speeds?m.duration=v.fx.speeds[m.duration]:m.duration=v.fx.speeds._default),(m.queue==null||m.queue===!0)&&(m.queue="fx"),m.old=m.complete,m.complete=function(){S(m.old)&&m.old.call(this),m.queue&&v.dequeue(this,m.queue)},m},v.fn.extend({fadeTo:function(l,c,p,m){return this.filter(oi).css("opacity",0).show().end().animate({opacity:c},l,p,m)},animate:function(l,c,p,m){var w=v.isEmptyObject(l),T=v.speed(c,p,m),D=function(){var I=jt(this,v.extend({},l),T);(w||le.get(this,"finish"))&&I.stop(!0)};return D.finish=D,w||T.queue===!1?this.each(D):this.queue(T.queue,D)},stop:function(l,c,p){var m=function(w){var T=w.stop;delete w.stop,T(p)};return typeof l!="string"&&(p=c,c=l,l=void 0),c&&this.queue(l||"fx",[]),this.each(function(){var w=!0,T=l!=null&&l+"queueHooks",D=v.timers,I=le.get(this);if(T)I[T]&&I[T].stop&&m(I[T]);else for(T in I)I[T]&&I[T].stop&&wp.test(T)&&m(I[T]);for(T=D.length;T--;)D[T].elem===this&&(l==null||D[T].queue===l)&&(D[T].anim.stop(p),w=!1,D.splice(T,1));(w||!p)&&v.dequeue(this,l)})},finish:function(l){return l!==!1&&(l=l||"fx"),this.each(function(){var c,p=le.get(this),m=p[l+"queue"],w=p[l+"queueHooks"],T=v.timers,D=m?m.length:0;for(p.finish=!0,v.queue(this,l,[]),w&&w.stop&&w.stop.call(this,!0),c=T.length;c--;)T[c].elem===this&&T[c].queue===l&&(T[c].anim.stop(!0),T.splice(c,1));for(c=0;c<D;c++)m[c]&&m[c].finish&&m[c].finish.call(this);delete p.finish})}}),v.each(["toggle","show","hide"],function(l,c){var p=v.fn[c];v.fn[c]=function(m,w,T){return m==null||typeof m=="boolean"?p.apply(this,arguments):this.animate(fi(c,!0),m,w,T)}}),v.each({slideDown:fi("show"),slideUp:fi("hide"),slideToggle:fi("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(l,c){v.fn[l]=function(p,m,w){return this.animate(c,p,m,w)}}),v.timers=[],v.fx.tick=function(){var l,c=0,p=v.timers;for(vr=Date.now();c<p.length;c++)l=p[c],!l()&&p[c]===l&&p.splice(c--,1);p.length||v.fx.stop(),vr=void 0},v.fx.timer=function(l){v.timers.push(l),v.fx.start()},v.fx.interval=13,v.fx.start=function(){ui||(ui=!0,Mo())},v.fx.stop=function(){ui=null},v.fx.speeds={slow:600,fast:200,_default:400},v.fn.delay=function(l,c){return l=v.fx&&v.fx.speeds[l]||l,c=c||"fx",this.queue(c,function(p,m){var w=t.setTimeout(p,l);m.stop=function(){t.clearTimeout(w)}})},function(){var l=O.createElement("input"),c=O.createElement("select"),p=c.appendChild(O.createElement("option"));l.type="checkbox",g.checkOn=l.value!=="",g.optSelected=p.selected,l=O.createElement("input"),l.value="t",l.type="radio",g.radioValue=l.value==="t"}();var Pu,ta=v.expr.attrHandle;v.fn.extend({attr:function(l,c){return Mt(this,v.attr,l,c,arguments.length>1)},removeAttr:function(l){return this.each(function(){v.removeAttr(this,l)})}}),v.extend({attr:function(l,c,p){var m,w,T=l.nodeType;if(!(T===3||T===8||T===2)){if(typeof l.getAttribute>"u")return v.prop(l,c,p);if((T!==1||!v.isXMLDoc(l))&&(w=v.attrHooks[c.toLowerCase()]||(v.expr.match.bool.test(c)?Pu:void 0)),p!==void 0){if(p===null){v.removeAttr(l,c);return}return w&&"set"in w&&(m=w.set(l,p,c))!==void 0?m:(l.setAttribute(c,p+""),p)}return w&&"get"in w&&(m=w.get(l,c))!==null?m:(m=v.find.attr(l,c),m??void 0)}},attrHooks:{type:{set:function(l,c){if(!g.radioValue&&c==="radio"&&G(l,"input")){var p=l.value;return l.setAttribute("type",c),p&&(l.value=p),c}}}},removeAttr:function(l,c){var p,m=0,w=c&&c.match(Nt);if(w&&l.nodeType===1)for(;p=w[m++];)l.removeAttribute(p)}}),Pu={set:function(l,c,p){return c===!1?v.removeAttr(l,p):l.setAttribute(p,p),p}},v.each(v.expr.match.bool.source.match(/\w+/g),function(l,c){var p=ta[c]||v.find.attr;ta[c]=function(m,w,T){var D,I,N=w.toLowerCase();return T||(I=ta[N],ta[N]=D,D=p(m,w,T)!=null?N:null,ta[N]=I),D}});var Dp=/^(?:input|select|textarea|button)$/i,Sp=/^(?:a|area)$/i;v.fn.extend({prop:function(l,c){return Mt(this,v.prop,l,c,arguments.length>1)},removeProp:function(l){return this.each(function(){delete this[v.propFix[l]||l]})}}),v.extend({prop:function(l,c,p){var m,w,T=l.nodeType;if(!(T===3||T===8||T===2))return(T!==1||!v.isXMLDoc(l))&&(c=v.propFix[c]||c,w=v.propHooks[c]),p!==void 0?w&&"set"in w&&(m=w.set(l,p,c))!==void 0?m:l[c]=p:w&&"get"in w&&(m=w.get(l,c))!==null?m:l[c]},propHooks:{tabIndex:{get:function(l){var c=v.find.attr(l,"tabindex");return c?parseInt(c,10):Dp.test(l.nodeName)||Sp.test(l.nodeName)&&l.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),g.optSelected||(v.propHooks.selected={get:function(l){var c=l.parentNode;return c&&c.parentNode&&c.parentNode.selectedIndex,null},set:function(l){var c=l.parentNode;c&&(c.selectedIndex,c.parentNode&&c.parentNode.selectedIndex)}}),v.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){v.propFix[this.toLowerCase()]=this});function Un(l){var c=l.match(Nt)||[];return c.join(" ")}function Xn(l){return l.getAttribute&&l.getAttribute("class")||""}function jo(l){return Array.isArray(l)?l:typeof l=="string"?l.match(Nt)||[]:[]}v.fn.extend({addClass:function(l){var c,p,m,w,T,D;return S(l)?this.each(function(I){v(this).addClass(l.call(this,I,Xn(this)))}):(c=jo(l),c.length?this.each(function(){if(m=Xn(this),p=this.nodeType===1&&" "+Un(m)+" ",p){for(T=0;T<c.length;T++)w=c[T],p.indexOf(" "+w+" ")<0&&(p+=w+" ");D=Un(p),m!==D&&this.setAttribute("class",D)}}):this)},removeClass:function(l){var c,p,m,w,T,D;return S(l)?this.each(function(I){v(this).removeClass(l.call(this,I,Xn(this)))}):arguments.length?(c=jo(l),c.length?this.each(function(){if(m=Xn(this),p=this.nodeType===1&&" "+Un(m)+" ",p){for(T=0;T<c.length;T++)for(w=c[T];p.indexOf(" "+w+" ")>-1;)p=p.replace(" "+w+" "," ");D=Un(p),m!==D&&this.setAttribute("class",D)}}):this):this.attr("class","")},toggleClass:function(l,c){var p,m,w,T,D=typeof l,I=D==="string"||Array.isArray(l);return S(l)?this.each(function(N){v(this).toggleClass(l.call(this,N,Xn(this),c),c)}):typeof c=="boolean"&&I?c?this.addClass(l):this.removeClass(l):(p=jo(l),this.each(function(){if(I)for(T=v(this),w=0;w<p.length;w++)m=p[w],T.hasClass(m)?T.removeClass(m):T.addClass(m);else(l===void 0||D==="boolean")&&(m=Xn(this),m&&le.set(this,"__className__",m),this.setAttribute&&this.setAttribute("class",m||l===!1?"":le.get(this,"__className__")||""))}))},hasClass:function(l){var c,p,m=0;for(c=" "+l+" ";p=this[m++];)if(p.nodeType===1&&(" "+Un(Xn(p))+" ").indexOf(c)>-1)return!0;return!1}});var xp=/\r/g;v.fn.extend({val:function(l){var c,p,m,w=this[0];return arguments.length?(m=S(l),this.each(function(T){var D;this.nodeType===1&&(m?D=l.call(this,T,v(this).val()):D=l,D==null?D="":typeof D=="number"?D+="":Array.isArray(D)&&(D=v.map(D,function(I){return I==null?"":I+""})),c=v.valHooks[this.type]||v.valHooks[this.nodeName.toLowerCase()],(!c||!("set"in c)||c.set(this,D,"value")===void 0)&&(this.value=D))})):w?(c=v.valHooks[w.type]||v.valHooks[w.nodeName.toLowerCase()],c&&"get"in c&&(p=c.get(w,"value"))!==void 0?p:(p=w.value,typeof p=="string"?p.replace(xp,""):p??"")):void 0}}),v.extend({valHooks:{option:{get:function(l){var c=v.find.attr(l,"value");return c??Un(v.text(l))}},select:{get:function(l){var c,p,m,w=l.options,T=l.selectedIndex,D=l.type==="select-one",I=D?null:[],N=D?T+1:w.length;for(T<0?m=N:m=D?T:0;m<N;m++)if(p=w[m],(p.selected||m===T)&&!p.disabled&&(!p.parentNode.disabled||!G(p.parentNode,"optgroup"))){if(c=v(p).val(),D)return c;I.push(c)}return I},set:function(l,c){for(var p,m,w=l.options,T=v.makeArray(c),D=w.length;D--;)m=w[D],(m.selected=v.inArray(v.valHooks.option.get(m),T)>-1)&&(p=!0);return p||(l.selectedIndex=-1),T}}}}),v.each(["radio","checkbox"],function(){v.valHooks[this]={set:function(l,c){if(Array.isArray(c))return l.checked=v.inArray(v(l).val(),c)>-1}},g.checkOn||(v.valHooks[this].get=function(l){return l.getAttribute("value")===null?"on":l.value})});var na=t.location,ku={guid:Date.now()},Bo=/\?/;v.parseXML=function(l){var c,p;if(!l||typeof l!="string")return null;try{c=new t.DOMParser().parseFromString(l,"text/xml")}catch{}return p=c&&c.getElementsByTagName("parsererror")[0],(!c||p)&&v.error("Invalid XML: "+(p?v.map(p.childNodes,function(m){return m.textContent}).join(`
`):l)),c};var $u=/^(?:focusinfocus|focusoutblur)$/,Fu=function(l){l.stopPropagation()};v.extend(v.event,{trigger:function(l,c,p,m){var w,T,D,I,N,P,j,q,H=[p||O],J=b.call(l,"type")?l.type:l,ge=b.call(l,"namespace")?l.namespace.split("."):[];if(T=q=D=p=p||O,!(p.nodeType===3||p.nodeType===8)&&!$u.test(J+v.event.triggered)&&(J.indexOf(".")>-1&&(ge=J.split("."),J=ge.shift(),ge.sort()),N=J.indexOf(":")<0&&"on"+J,l=l[v.expando]?l:new v.Event(J,typeof l=="object"&&l),l.isTrigger=m?2:3,l.namespace=ge.join("."),l.rnamespace=l.namespace?new RegExp("(^|\\.)"+ge.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,l.result=void 0,l.target||(l.target=p),c=c==null?[l]:v.makeArray(c,[l]),j=v.event.special[J]||{},!(!m&&j.trigger&&j.trigger.apply(p,c)===!1))){if(!m&&!j.noBubble&&!L(p)){for(I=j.delegateType||J,$u.test(I+J)||(T=T.parentNode);T;T=T.parentNode)H.push(T),D=T;D===(p.ownerDocument||O)&&H.push(D.defaultView||D.parentWindow||t)}for(w=0;(T=H[w++])&&!l.isPropagationStopped();)q=T,l.type=w>1?I:j.bindType||J,P=(le.get(T,"events")||Object.create(null))[l.type]&&le.get(T,"handle"),P&&P.apply(T,c),P=N&&T[N],P&&P.apply&&Sn(T)&&(l.result=P.apply(T,c),l.result===!1&&l.preventDefault());return l.type=J,!m&&!l.isDefaultPrevented()&&(!j._default||j._default.apply(H.pop(),c)===!1)&&Sn(p)&&N&&S(p[J])&&!L(p)&&(D=p[N],D&&(p[N]=null),v.event.triggered=J,l.isPropagationStopped()&&q.addEventListener(J,Fu),p[J](),l.isPropagationStopped()&&q.removeEventListener(J,Fu),v.event.triggered=void 0,D&&(p[N]=D)),l.result}},simulate:function(l,c,p){var m=v.extend(new v.Event,p,{type:l,isSimulated:!0});v.event.trigger(m,null,c)}}),v.fn.extend({trigger:function(l,c){return this.each(function(){v.event.trigger(l,c,this)})},triggerHandler:function(l,c){var p=this[0];if(p)return v.event.trigger(l,c,p,!0)}});var Ap=/\[\]$/,Hu=/\r?\n/g,Ep=/^(?:submit|button|image|reset|file)$/i,Np=/^(?:input|select|textarea|keygen)/i;function Wo(l,c,p,m){var w;if(Array.isArray(c))v.each(c,function(T,D){p||Ap.test(l)?m(l,D):Wo(l+"["+(typeof D=="object"&&D!=null?T:"")+"]",D,p,m)});else if(!p&&W(c)==="object")for(w in c)Wo(l+"["+w+"]",c[w],p,m);else m(l,c)}v.param=function(l,c){var p,m=[],w=function(T,D){var I=S(D)?D():D;m[m.length]=encodeURIComponent(T)+"="+encodeURIComponent(I??"")};if(l==null)return"";if(Array.isArray(l)||l.jquery&&!v.isPlainObject(l))v.each(l,function(){w(this.name,this.value)});else for(p in l)Wo(p,l[p],c,w);return m.join("&")},v.fn.extend({serialize:function(){return v.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var l=v.prop(this,"elements");return l?v.makeArray(l):this}).filter(function(){var l=this.type;return this.name&&!v(this).is(":disabled")&&Np.test(this.nodeName)&&!Ep.test(l)&&(this.checked||!Zr.test(l))}).map(function(l,c){var p=v(this).val();return p==null?null:Array.isArray(p)?v.map(p,function(m){return{name:c.name,value:m.replace(Hu,`\r
`)}}):{name:c.name,value:p.replace(Hu,`\r
`)}}).get()}});var Lp=/%20/g,Op=/#.*$/,Ip=/([?&])_=[^&]*/,Rp=/^(.*?):[ \t]*([^\r\n]*)$/mg,Pp=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,kp=/^(?:GET|HEAD)$/,$p=/^\/\//,Mu={},Vo={},ju="*/".concat("*"),qo=O.createElement("a");qo.href=na.href;function Bu(l){return function(c,p){typeof c!="string"&&(p=c,c="*");var m,w=0,T=c.toLowerCase().match(Nt)||[];if(S(p))for(;m=T[w++];)m[0]==="+"?(m=m.slice(1)||"*",(l[m]=l[m]||[]).unshift(p)):(l[m]=l[m]||[]).push(p)}}function Wu(l,c,p,m){var w={},T=l===Vo;function D(I){var N;return w[I]=!0,v.each(l[I]||[],function(P,j){var q=j(c,p,m);if(typeof q=="string"&&!T&&!w[q])return c.dataTypes.unshift(q),D(q),!1;if(T)return!(N=q)}),N}return D(c.dataTypes[0])||!w["*"]&&D("*")}function Uo(l,c){var p,m,w=v.ajaxSettings.flatOptions||{};for(p in c)c[p]!==void 0&&((w[p]?l:m||(m={}))[p]=c[p]);return m&&v.extend(!0,l,m),l}function Fp(l,c,p){for(var m,w,T,D,I=l.contents,N=l.dataTypes;N[0]==="*";)N.shift(),m===void 0&&(m=l.mimeType||c.getResponseHeader("Content-Type"));if(m){for(w in I)if(I[w]&&I[w].test(m)){N.unshift(w);break}}if(N[0]in p)T=N[0];else{for(w in p){if(!N[0]||l.converters[w+" "+N[0]]){T=w;break}D||(D=w)}T=T||D}if(T)return T!==N[0]&&N.unshift(T),p[T]}function Hp(l,c,p,m){var w,T,D,I,N,P={},j=l.dataTypes.slice();if(j[1])for(D in l.converters)P[D.toLowerCase()]=l.converters[D];for(T=j.shift();T;)if(l.responseFields[T]&&(p[l.responseFields[T]]=c),!N&&m&&l.dataFilter&&(c=l.dataFilter(c,l.dataType)),N=T,T=j.shift(),T){if(T==="*")T=N;else if(N!=="*"&&N!==T){if(D=P[N+" "+T]||P["* "+T],!D){for(w in P)if(I=w.split(" "),I[1]===T&&(D=P[N+" "+I[0]]||P["* "+I[0]],D)){D===!0?D=P[w]:P[w]!==!0&&(T=I[0],j.unshift(I[1]));break}}if(D!==!0)if(D&&l.throws)c=D(c);else try{c=D(c)}catch(q){return{state:"parsererror",error:D?q:"No conversion from "+N+" to "+T}}}}return{state:"success",data:c}}v.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:na.href,type:"GET",isLocal:Pp.test(na.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":ju,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":v.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(l,c){return c?Uo(Uo(l,v.ajaxSettings),c):Uo(v.ajaxSettings,l)},ajaxPrefilter:Bu(Mu),ajaxTransport:Bu(Vo),ajax:function(l,c){typeof l=="object"&&(c=l,l=void 0),c=c||{};var p,m,w,T,D,I,N,P,j,q,H=v.ajaxSetup({},c),J=H.context||H,ge=H.context&&(J.nodeType||J.jquery)?v(J):v.event,Oe=v.Deferred(),Ce=v.Callbacks("once memory"),et=H.statusCode||{},Ye={},Xt={},Kt="canceled",Ae={readyState:0,getResponseHeader:function(Ie){var Ve;if(N){if(!T)for(T={};Ve=Rp.exec(w);)T[Ve[1].toLowerCase()+" "]=(T[Ve[1].toLowerCase()+" "]||[]).concat(Ve[2]);Ve=T[Ie.toLowerCase()+" "]}return Ve==null?null:Ve.join(", ")},getAllResponseHeaders:function(){return N?w:null},setRequestHeader:function(Ie,Ve){return N==null&&(Ie=Xt[Ie.toLowerCase()]=Xt[Ie.toLowerCase()]||Ie,Ye[Ie]=Ve),this},overrideMimeType:function(Ie){return N==null&&(H.mimeType=Ie),this},statusCode:function(Ie){var Ve;if(Ie)if(N)Ae.always(Ie[Ae.status]);else for(Ve in Ie)et[Ve]=[et[Ve],Ie[Ve]];return this},abort:function(Ie){var Ve=Ie||Kt;return p&&p.abort(Ve),Kn(0,Ve),this}};if(Oe.promise(Ae),H.url=((l||H.url||na.href)+"").replace($p,na.protocol+"//"),H.type=c.method||c.type||H.method||H.type,H.dataTypes=(H.dataType||"*").toLowerCase().match(Nt)||[""],H.crossDomain==null){I=O.createElement("a");try{I.href=H.url,I.href=I.href,H.crossDomain=qo.protocol+"//"+qo.host!=I.protocol+"//"+I.host}catch{H.crossDomain=!0}}if(H.data&&H.processData&&typeof H.data!="string"&&(H.data=v.param(H.data,H.traditional)),Wu(Mu,H,c,Ae),N)return Ae;P=v.event&&H.global,P&&v.active++===0&&v.event.trigger("ajaxStart"),H.type=H.type.toUpperCase(),H.hasContent=!kp.test(H.type),m=H.url.replace(Op,""),H.hasContent?H.data&&H.processData&&(H.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&(H.data=H.data.replace(Lp,"+")):(q=H.url.slice(m.length),H.data&&(H.processData||typeof H.data=="string")&&(m+=(Bo.test(m)?"&":"?")+H.data,delete H.data),H.cache===!1&&(m=m.replace(Ip,"$1"),q=(Bo.test(m)?"&":"?")+"_="+ku.guid+++q),H.url=m+q),H.ifModified&&(v.lastModified[m]&&Ae.setRequestHeader("If-Modified-Since",v.lastModified[m]),v.etag[m]&&Ae.setRequestHeader("If-None-Match",v.etag[m])),(H.data&&H.hasContent&&H.contentType!==!1||c.contentType)&&Ae.setRequestHeader("Content-Type",H.contentType),Ae.setRequestHeader("Accept",H.dataTypes[0]&&H.accepts[H.dataTypes[0]]?H.accepts[H.dataTypes[0]]+(H.dataTypes[0]!=="*"?", "+ju+"; q=0.01":""):H.accepts["*"]);for(j in H.headers)Ae.setRequestHeader(j,H.headers[j]);if(H.beforeSend&&(H.beforeSend.call(J,Ae,H)===!1||N))return Ae.abort();if(Kt="abort",Ce.add(H.complete),Ae.done(H.success),Ae.fail(H.error),p=Wu(Vo,H,c,Ae),!p)Kn(-1,"No Transport");else{if(Ae.readyState=1,P&&ge.trigger("ajaxSend",[Ae,H]),N)return Ae;H.async&&H.timeout>0&&(D=t.setTimeout(function(){Ae.abort("timeout")},H.timeout));try{N=!1,p.send(Ye,Kn)}catch(Ie){if(N)throw Ie;Kn(-1,Ie)}}function Kn(Ie,Ve,aa,Ko){var Yt,ia,Gt,xn,An,Ot=Ve;N||(N=!0,D&&t.clearTimeout(D),p=void 0,w=Ko||"",Ae.readyState=Ie>0?4:0,Yt=Ie>=200&&Ie<300||Ie===304,aa&&(xn=Fp(H,Ae,aa)),!Yt&&v.inArray("script",H.dataTypes)>-1&&v.inArray("json",H.dataTypes)<0&&(H.converters["text script"]=function(){}),xn=Hp(H,xn,Ae,Yt),Yt?(H.ifModified&&(An=Ae.getResponseHeader("Last-Modified"),An&&(v.lastModified[m]=An),An=Ae.getResponseHeader("etag"),An&&(v.etag[m]=An)),Ie===204||H.type==="HEAD"?Ot="nocontent":Ie===304?Ot="notmodified":(Ot=xn.state,ia=xn.data,Gt=xn.error,Yt=!Gt)):(Gt=Ot,(Ie||!Ot)&&(Ot="error",Ie<0&&(Ie=0))),Ae.status=Ie,Ae.statusText=(Ve||Ot)+"",Yt?Oe.resolveWith(J,[ia,Ot,Ae]):Oe.rejectWith(J,[Ae,Ot,Gt]),Ae.statusCode(et),et=void 0,P&&ge.trigger(Yt?"ajaxSuccess":"ajaxError",[Ae,H,Yt?ia:Gt]),Ce.fireWith(J,[Ae,Ot]),P&&(ge.trigger("ajaxComplete",[Ae,H]),--v.active||v.event.trigger("ajaxStop")))}return Ae},getJSON:function(l,c,p){return v.get(l,c,p,"json")},getScript:function(l,c){return v.get(l,void 0,c,"script")}}),v.each(["get","post"],function(l,c){v[c]=function(p,m,w,T){return S(m)&&(T=T||w,w=m,m=void 0),v.ajax(v.extend({url:p,type:c,dataType:T,data:m,success:w},v.isPlainObject(p)&&p))}}),v.ajaxPrefilter(function(l){var c;for(c in l.headers)c.toLowerCase()==="content-type"&&(l.contentType=l.headers[c]||"")}),v._evalUrl=function(l,c,p){return v.ajax({url:l,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(m){v.globalEval(m,c,p)}})},v.fn.extend({wrapAll:function(l){var c;return this[0]&&(S(l)&&(l=l.call(this[0])),c=v(l,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&c.insertBefore(this[0]),c.map(function(){for(var p=this;p.firstElementChild;)p=p.firstElementChild;return p}).append(this)),this},wrapInner:function(l){return S(l)?this.each(function(c){v(this).wrapInner(l.call(this,c))}):this.each(function(){var c=v(this),p=c.contents();p.length?p.wrapAll(l):c.append(l)})},wrap:function(l){var c=S(l);return this.each(function(p){v(this).wrapAll(c?l.call(this,p):l)})},unwrap:function(l){return this.parent(l).not("body").each(function(){v(this).replaceWith(this.childNodes)}),this}}),v.expr.pseudos.hidden=function(l){return!v.expr.pseudos.visible(l)},v.expr.pseudos.visible=function(l){return!!(l.offsetWidth||l.offsetHeight||l.getClientRects().length)},v.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch{}};var Mp={0:200,1223:204},ra=v.ajaxSettings.xhr();g.cors=!!ra&&"withCredentials"in ra,g.ajax=ra=!!ra,v.ajaxTransport(function(l){var c,p;if(g.cors||ra&&!l.crossDomain)return{send:function(m,w){var T,D=l.xhr();if(D.open(l.type,l.url,l.async,l.username,l.password),l.xhrFields)for(T in l.xhrFields)D[T]=l.xhrFields[T];l.mimeType&&D.overrideMimeType&&D.overrideMimeType(l.mimeType),!l.crossDomain&&!m["X-Requested-With"]&&(m["X-Requested-With"]="XMLHttpRequest");for(T in m)D.setRequestHeader(T,m[T]);c=function(I){return function(){c&&(c=p=D.onload=D.onerror=D.onabort=D.ontimeout=D.onreadystatechange=null,I==="abort"?D.abort():I==="error"?typeof D.status!="number"?w(0,"error"):w(D.status,D.statusText):w(Mp[D.status]||D.status,D.statusText,(D.responseType||"text")!=="text"||typeof D.responseText!="string"?{binary:D.response}:{text:D.responseText},D.getAllResponseHeaders()))}},D.onload=c(),p=D.onerror=D.ontimeout=c("error"),D.onabort!==void 0?D.onabort=p:D.onreadystatechange=function(){D.readyState===4&&t.setTimeout(function(){c&&p()})},c=c("abort");try{D.send(l.hasContent&&l.data||null)}catch(I){if(c)throw I}},abort:function(){c&&c()}}}),v.ajaxPrefilter(function(l){l.crossDomain&&(l.contents.script=!1)}),v.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(l){return v.globalEval(l),l}}}),v.ajaxPrefilter("script",function(l){l.cache===void 0&&(l.cache=!1),l.crossDomain&&(l.type="GET")}),v.ajaxTransport("script",function(l){if(l.crossDomain||l.scriptAttrs){var c,p;return{send:function(m,w){c=v("<script>").attr(l.scriptAttrs||{}).prop({charset:l.scriptCharset,src:l.url}).on("load error",p=function(T){c.remove(),p=null,T&&w(T.type==="error"?404:200,T.type)}),O.head.appendChild(c[0])},abort:function(){p&&p()}}}});var Vu=[],Xo=/(=)\?(?=&|$)|\?\?/;v.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var l=Vu.pop()||v.expando+"_"+ku.guid++;return this[l]=!0,l}}),v.ajaxPrefilter("json jsonp",function(l,c,p){var m,w,T,D=l.jsonp!==!1&&(Xo.test(l.url)?"url":typeof l.data=="string"&&(l.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&Xo.test(l.data)&&"data");if(D||l.dataTypes[0]==="jsonp")return m=l.jsonpCallback=S(l.jsonpCallback)?l.jsonpCallback():l.jsonpCallback,D?l[D]=l[D].replace(Xo,"$1"+m):l.jsonp!==!1&&(l.url+=(Bo.test(l.url)?"&":"?")+l.jsonp+"="+m),l.converters["script json"]=function(){return T||v.error(m+" was not called"),T[0]},l.dataTypes[0]="json",w=t[m],t[m]=function(){T=arguments},p.always(function(){w===void 0?v(t).removeProp(m):t[m]=w,l[m]&&(l.jsonpCallback=c.jsonpCallback,Vu.push(m)),T&&S(w)&&w(T[0]),T=w=void 0}),"script"}),g.createHTMLDocument=function(){var l=O.implementation.createHTMLDocument("").body;return l.innerHTML="<form></form><form></form>",l.childNodes.length===2}(),v.parseHTML=function(l,c,p){if(typeof l!="string")return[];typeof c=="boolean"&&(p=c,c=!1);var m,w,T;return c||(g.createHTMLDocument?(c=O.implementation.createHTMLDocument(""),m=c.createElement("base"),m.href=O.location.href,c.head.appendChild(m)):c=O),w=We.exec(l),T=!p&&[],w?[c.createElement(w[1])]:(w=gu([l],c,T),T&&T.length&&v(T).remove(),v.merge([],w.childNodes))},v.fn.load=function(l,c,p){var m,w,T,D=this,I=l.indexOf(" ");return I>-1&&(m=Un(l.slice(I)),l=l.slice(0,I)),S(c)?(p=c,c=void 0):c&&typeof c=="object"&&(w="POST"),D.length>0&&v.ajax({url:l,type:w||"GET",dataType:"html",data:c}).done(function(N){T=arguments,D.html(m?v("<div>").append(v.parseHTML(N)).find(m):N)}).always(p&&function(N,P){D.each(function(){p.apply(this,T||[N.responseText,P,N])})}),this},v.expr.pseudos.animated=function(l){return v.grep(v.timers,function(c){return l===c.elem}).length},v.offset={setOffset:function(l,c,p){var m,w,T,D,I,N,P,j=v.css(l,"position"),q=v(l),H={};j==="static"&&(l.style.position="relative"),I=q.offset(),T=v.css(l,"top"),N=v.css(l,"left"),P=(j==="absolute"||j==="fixed")&&(T+N).indexOf("auto")>-1,P?(m=q.position(),D=m.top,w=m.left):(D=parseFloat(T)||0,w=parseFloat(N)||0),S(c)&&(c=c.call(l,p,v.extend({},I))),c.top!=null&&(H.top=c.top-I.top+D),c.left!=null&&(H.left=c.left-I.left+w),"using"in c?c.using.call(l,H):q.css(H)}},v.fn.extend({offset:function(l){if(arguments.length)return l===void 0?this:this.each(function(w){v.offset.setOffset(this,l,w)});var c,p,m=this[0];if(m)return m.getClientRects().length?(c=m.getBoundingClientRect(),p=m.ownerDocument.defaultView,{top:c.top+p.pageYOffset,left:c.left+p.pageXOffset}):{top:0,left:0}},position:function(){if(this[0]){var l,c,p,m=this[0],w={top:0,left:0};if(v.css(m,"position")==="fixed")c=m.getBoundingClientRect();else{for(c=this.offset(),p=m.ownerDocument,l=m.offsetParent||p.documentElement;l&&(l===p.body||l===p.documentElement)&&v.css(l,"position")==="static";)l=l.parentNode;l&&l!==m&&l.nodeType===1&&(w=v(l).offset(),w.top+=v.css(l,"borderTopWidth",!0),w.left+=v.css(l,"borderLeftWidth",!0))}return{top:c.top-w.top-v.css(m,"marginTop",!0),left:c.left-w.left-v.css(m,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var l=this.offsetParent;l&&v.css(l,"position")==="static";)l=l.offsetParent;return l||qn})}}),v.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(l,c){var p=c==="pageYOffset";v.fn[l]=function(m){return Mt(this,function(w,T,D){var I;if(L(w)?I=w:w.nodeType===9&&(I=w.defaultView),D===void 0)return I?I[c]:w[T];I?I.scrollTo(p?I.pageXOffset:D,p?D:I.pageYOffset):w[T]=D},l,m,arguments.length)}}),v.each(["top","left"],function(l,c){v.cssHooks[c]=Su(g.pixelPosition,function(p,m){if(m)return m=ea(p,c),ko.test(m)?v(p).position()[c]+"px":m})}),v.each({Height:"height",Width:"width"},function(l,c){v.each({padding:"inner"+l,content:c,"":"outer"+l},function(p,m){v.fn[m]=function(w,T){var D=arguments.length&&(p||typeof w!="boolean"),I=p||(w===!0||T===!0?"margin":"border");return Mt(this,function(N,P,j){var q;return L(N)?m.indexOf("outer")===0?N["inner"+l]:N.document.documentElement["client"+l]:N.nodeType===9?(q=N.documentElement,Math.max(N.body["scroll"+l],q["scroll"+l],N.body["offset"+l],q["offset"+l],q["client"+l])):j===void 0?v.css(N,P,I):v.style(N,P,j,I)},c,D?w:void 0,D)}})}),v.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(l,c){v.fn[c]=function(p){return this.on(c,p)}}),v.fn.extend({bind:function(l,c,p){return this.on(l,null,c,p)},unbind:function(l,c){return this.off(l,null,c)},delegate:function(l,c,p,m){return this.on(c,l,p,m)},undelegate:function(l,c,p){return arguments.length===1?this.off(l,"**"):this.off(c,l||"**",p)},hover:function(l,c){return this.on("mouseenter",l).on("mouseleave",c||l)}}),v.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(l,c){v.fn[c]=function(p,m){return arguments.length>0?this.on(c,null,p,m):this.trigger(c)}});var jp=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;v.proxy=function(l,c){var p,m,w;if(typeof c=="string"&&(p=l[c],c=l,l=p),!!S(l))return m=o.call(arguments,2),w=function(){return l.apply(c||this,m.concat(o.call(arguments)))},w.guid=l.guid=l.guid||v.guid++,w},v.holdReady=function(l){l?v.readyWait++:v.ready(!0)},v.isArray=Array.isArray,v.parseJSON=JSON.parse,v.nodeName=G,v.isFunction=S,v.isWindow=L,v.camelCase=Tt,v.type=W,v.now=Date.now,v.isNumeric=function(l){var c=v.type(l);return(c==="number"||c==="string")&&!isNaN(l-parseFloat(l))},v.trim=function(l){return l==null?"":(l+"").replace(jp,"$1")};var Bp=t.jQuery,Wp=t.$;return v.noConflict=function(l){return t.$===v&&(t.$=Wp),l&&t.jQuery===v&&(t.jQuery=Bp),v},typeof n>"u"&&(t.jQuery=t.$=v),v})})(h0);const on=Fs;/*! DataTables 1.13.7
 * ©2008-2023 SpryMedia Ltd - datatables.net/license
 */var x=on,K=function(e,t){if(K.factory(e,t))return K;if(this instanceof K)return x(e).DataTable(t);t=e,this.$=function(s,u){return this.api(!0).$(s,u)},this._=function(s,u){return this.api(!0).rows(s,u).data()},this.api=function(s){return s?new Ee(Xi(this[Xe.iApiIndex])):new Ee(this)},this.fnAddData=function(s,u){var f=this.api(!0),d=Array.isArray(s)&&(Array.isArray(s[0])||x.isPlainObject(s[0]))?f.rows.add(s):f.row.add(s);return(u===void 0||u)&&f.draw(),d.flatten().toArray()},this.fnAdjustColumnSizing=function(s){var u=this.api(!0).columns.adjust(),f=u.settings()[0],d=f.oScroll;s===void 0||s?u.draw(!1):(d.sX!==""||d.sY!=="")&&_o(f)},this.fnClearTable=function(s){var u=this.api(!0).clear();(s===void 0||s)&&u.draw()},this.fnClose=function(s){this.api(!0).row(s).child.hide()},this.fnDeleteRow=function(s,u,f){var d=this.api(!0),h=d.rows(s),b=h.settings()[0],_=b.aoData[h[0][0]];return h.remove(),u&&u.call(this,b,_),(f===void 0||f)&&d.draw(),_},this.fnDestroy=function(s){this.api(!0).destroy(s)},this.fnDraw=function(s){this.api(!0).draw(s)},this.fnFilter=function(s,u,f,d,h,b){var _=this.api(!0);u==null?_.search(s,f,d,b):_.column(u).search(s,f,d,b),_.draw()},this.fnGetData=function(s,u){var f=this.api(!0);if(s!==void 0){var d=s.nodeName?s.nodeName.toLowerCase():"";return u!==void 0||d=="td"||d=="th"?f.cell(s,u).data():f.row(s).data()||null}return f.data().toArray()},this.fnGetNodes=function(s){var u=this.api(!0);return s!==void 0?u.row(s).node():u.rows().nodes().flatten().toArray()},this.fnGetPosition=function(s){var u=this.api(!0),f=s.nodeName.toUpperCase();if(f=="TR")return u.row(s).index();if(f=="TD"||f=="TH"){var d=u.cell(s).index();return[d.row,d.columnVisible,d.column]}return null},this.fnIsOpen=function(s){return this.api(!0).row(s).child.isShown()},this.fnOpen=function(s,u,f){return this.api(!0).row(s).child(u,f).show().child()[0]},this.fnPageChange=function(s,u){var f=this.api(!0).page(s);(u===void 0||u)&&f.draw(!1)},this.fnSetColumnVis=function(s,u,f){var d=this.api(!0).column(s).visible(u);(f===void 0||f)&&d.columns.adjust().draw()},this.fnSettings=function(){return Xi(this[Xe.iApiIndex])},this.fnSort=function(s){this.api(!0).order(s).draw()},this.fnSortListener=function(s,u,f){this.api(!0).order.listener(s,u,f)},this.fnUpdate=function(s,u,f,d,h){var b=this.api(!0);return f==null?b.row(u).data(s):b.cell(u,f).data(s),(h===void 0||h)&&b.columns.adjust(),(d===void 0||d)&&b.draw(),0},this.fnVersionCheck=Xe.fnVersionCheck;var n=this,r=t===void 0,a=this.length;r&&(t={}),this.oApi=this.internal=Xe.internal;for(var o in K.ext.internal)o&&(this[o]=ph(o));return this.each(function(){var s={},u=a>1?Us(s,t,!0):t,f=0,d,h=this.getAttribute("id"),b=!1,_=K.defaults,y=x(this);if(this.nodeName.toLowerCase()!="table"){Pt(null,0,"Non-table node initialisation ("+this.nodeName+")",2);return}Wf(_),Od(_.column),pn(_,_,!0),pn(_.column,_.column,!0),pn(_,x.extend(u,y.data()),!0);var g=K.settings;for(f=0,d=g.length;f<d;f++){var S=g[f];if(S.nTable==this||S.nTHead&&S.nTHead.parentNode==this||S.nTFoot&&S.nTFoot.parentNode==this){var L=u.bRetrieve!==void 0?u.bRetrieve:_.bRetrieve,O=u.bDestroy!==void 0?u.bDestroy:_.bDestroy;if(r||L)return S.oInstance;if(O){S.oInstance.fnDestroy();break}else{Pt(S,0,"Cannot reinitialise DataTable",3);return}}if(S.sTableId==this.id){g.splice(f,1);break}}(h===null||h==="")&&(h="DataTables_Table_"+K.ext._unique++,this.id=h);var C=x.extend(!0,{},K.models.oSettings,{sDestroyWidth:y[0].style.width,sInstance:h,sTableId:h});C.nTable=this,C.oApi=n.internal,C.oInit=u,g.push(C),C.oInstance=n.length===1?n:y.dataTable(),Wf(u),js(u.oLanguage),u.aLengthMenu&&!u.iDisplayLength&&(u.iDisplayLength=Array.isArray(u.aLengthMenu[0])?u.aLengthMenu[0][0]:u.aLengthMenu[0]),u=Us(x.extend(!0,{},_),u),Wt(C.oFeatures,u,["bPaginate","bLengthChange","bFilter","bSort","bSortMulti","bInfo","bProcessing","bAutoWidth","bSortClasses","bServerSide","bDeferRender"]),Wt(C,u,["asStripeClasses","ajax","fnServerData","fnFormatNumber","sServerMethod","aaSorting","aaSortingFixed","aLengthMenu","sPaginationType","sAjaxSource","sAjaxDataProp","iStateDuration","sDom","bSortCellsTop","iTabIndex","fnStateLoadCallback","fnStateSaveCallback","renderer","searchDelay","rowId",["iCookieDuration","iStateDuration"],["oSearch","oPreviousSearch"],["aoSearchCols","aoPreSearchCols"],["iDisplayLength","_iDisplayLength"]]),Wt(C.oScroll,u,[["sScrollX","sX"],["sScrollXInner","sXInner"],["sScrollY","sY"],["bScrollCollapse","bCollapse"]]),Wt(C.oLanguage,u,"fnInfoCallback"),st(C,"aoDrawCallback",u.fnDrawCallback,"user"),st(C,"aoServerParams",u.fnServerParams,"user"),st(C,"aoStateSaveParams",u.fnStateSaveParams,"user"),st(C,"aoStateLoadParams",u.fnStateLoadParams,"user"),st(C,"aoStateLoaded",u.fnStateLoaded,"user"),st(C,"aoRowCallback",u.fnRowCallback,"user"),st(C,"aoRowCreatedCallback",u.fnCreatedRow,"user"),st(C,"aoHeaderCallback",u.fnHeaderCallback,"user"),st(C,"aoFooterCallback",u.fnFooterCallback,"user"),st(C,"aoInitComplete",u.fnInitComplete,"user"),st(C,"aoPreDrawCallback",u.fnPreDrawCallback,"user"),C.rowIdFn=Mr(u.rowId),Id(C);var Y=C.oClasses;if(x.extend(Y,K.ext.classes,u.oClasses),y.addClass(Y.sTable),C.iInitDisplayStart===void 0&&(C.iInitDisplayStart=u.iDisplayStart,C._iDisplayStart=u.iDisplayStart),u.iDeferLoading!==null){C.bDeferLoading=!0;var W=Array.isArray(u.iDeferLoading);C._iRecordsDisplay=W?u.iDeferLoading[0]:u.iDeferLoading,C._iRecordsTotal=W?u.iDeferLoading[1]:u.iDeferLoading}var B=C.oLanguage;x.extend(!0,B,u.oLanguage),B.sUrl?(x.ajax({dataType:"json",url:B.sUrl,success:function(ae){pn(_.oLanguage,ae),js(ae),x.extend(!0,B,ae,C.oInit.oLanguage),Pe(C,null,"i18n",[C]),ga(C)},error:function(){ga(C)}}),b=!0):Pe(C,null,"i18n",[C]),u.asStripeClasses===null&&(C.asStripeClasses=[Y.sStripeOdd,Y.sStripeEven]);var Q=C.asStripeClasses,v=y.children("tbody").find("tr").eq(0);x.inArray(!0,x.map(Q,function(ae,we){return v.hasClass(ae)}))!==-1&&(x("tbody tr",this).removeClass(Q.join(" ")),C.asDestroyStripes=Q.slice());var ee=[],G,re=this.getElementsByTagName("thead");if(re.length!==0&&(Ca(C.aoHeader,re[0]),ee=vo(C)),u.aoColumns===null)for(G=[],f=0,d=ee.length;f<d;f++)G.push(null);else G=u.aoColumns;for(f=0,d=G.length;f<d;f++)xl(C,ee?ee[f]:null);if(Rd(C,u.aoColumnDefs,G,function(ae,we){Vi(C,ae,we)}),v.length){var ie=function(ae,we){return ae.getAttribute("data-"+we)!==null?we:null};x(v[0]).children("th, td").each(function(ae,we){var Te=C.aoColumns[ae];if(Te||Pt(C,0,"Incorrect column count",18),Te.mData===ae){var me=ie(we,"sort")||ie(we,"order"),xe=ie(we,"filter")||ie(we,"search");(me!==null||xe!==null)&&(Te.mData={_:ae+".display",sort:me!==null?ae+".@data-"+me:void 0,type:me!==null?ae+".@data-"+me:void 0,filter:xe!==null?ae+".@data-"+xe:void 0},Te._isArrayHost=!0,Vi(C,ae))}})}var pe=C.oFeatures,se=function(){if(u.aaSorting===void 0){var ae=C.aaSorting;for(f=0,d=ae.length;f<d;f++)ae[f][1]=C.aoColumns[f].asSorting[0]}Ui(C),pe.bSort&&st(C,"aoDrawCallback",function(){if(C.bSorted){var Le=Kr(C),Fe={};x.each(Le,function(ct,We){Fe[We.src]=We.dir}),Pe(C,null,"order",[C,Le,Fe]),nh(C)}}),st(C,"aoDrawCallback",function(){(C.bSorted||rt(C)==="ssp"||pe.bDeferRender)&&Ui(C)},"sc");var we=y.children("caption").each(function(){this._captionSide=x(this).css("caption-side")}),Te=y.children("thead");Te.length===0&&(Te=x("<thead/>").appendTo(y)),C.nTHead=Te[0];var me=y.children("tbody");me.length===0&&(me=x("<tbody/>").insertAfter(Te)),C.nTBody=me[0];var xe=y.children("tfoot");if(xe.length===0&&we.length>0&&(C.oScroll.sX!==""||C.oScroll.sY!=="")&&(xe=x("<tfoot/>").appendTo(y)),xe.length===0||xe.children().length===0?y.addClass(Y.sNoFooter):xe.length>0&&(C.nTFoot=xe[0],Ca(C.aoFooter,C.nTFoot)),u.aaData)for(f=0;f<u.aaData.length;f++)Hn(C,u.aaData[f]);else(C.bDeferLoading||rt(C)=="dom")&&ho(C,x(C.nTBody).children("tr"));C.aiDisplay=C.aiDisplayMaster.slice(),C.bInitialised=!0,b===!1&&ga(C)};st(C,"aoDrawCallback",qa,"state_save"),u.bStateSave?(pe.bStateSave=!0,ah(C,u,se)):se()}),n=null,this},Xe,Ee,de,Me,gs={},jf=/[\r\n\u2028]/g,Wi=/<.*?>/g,p0=/^\d{2,4}[\.\/\-]\d{1,2}[\.\/\-]\d{1,2}([T ]{1}\d{1,2}[:\.]\d{2}([\.:]\d{2})?)?$/,v0=new RegExp("(\\"+["/",".","*","+","?","|","(",")","[","]","{","}","\\","$","^","-"].join("|\\")+")","g"),Hs=/['\u00A0,$£€¥%\u2009\u202F\u20BD\u20a9\u20BArfkɃΞ]/gi,nn=function(e){return!e||e===!0||e==="-"},xd=function(e){var t=parseInt(e,10);return!isNaN(t)&&isFinite(e)?t:null},Ad=function(e,t){return gs[t]||(gs[t]=new RegExp(Il(t),"g")),typeof e=="string"&&t!=="."?e.replace(/\./g,"").replace(gs[t],"."):e},Ms=function(e,t,n){var r=typeof e,a=r==="string";return r==="number"||r==="bigint"||nn(e)?!0:(t&&a&&(e=Ad(e,t)),n&&a&&(e=e.replace(Hs,"")),!isNaN(parseFloat(e))&&isFinite(e))},m0=function(e){return nn(e)||typeof e=="string"},Bf=function(e,t,n){if(nn(e))return!0;var r=m0(e);return r&&Ms(b0(e),t,n)?!0:null},_t=function(e,t,n){var r=[],a=0,o=e.length;if(n!==void 0)for(;a<o;a++)e[a]&&e[a][t]&&r.push(e[a][t][n]);else for(;a<o;a++)e[a]&&r.push(e[a][t]);return r},$a=function(e,t,n,r){var a=[],o=0,s=t.length;if(r!==void 0)for(;o<s;o++)e[t[o]][n]&&a.push(e[t[o]][n][r]);else for(;o<s;o++)a.push(e[t[o]][n]);return a},Nr=function(e,t){var n=[],r;t===void 0?(t=0,r=e):(r=t,t=e);for(var a=t;a<r;a++)n.push(a);return n},Ed=function(e){for(var t=[],n=0,r=e.length;n<r;n++)e[n]&&t.push(e[n]);return t},b0=function(e){return e.replace(Wi,"").replace(/<script/i,"")},_0=function(e){if(e.length<2)return!0;for(var t=e.slice().sort(),n=t[0],r=1,a=t.length;r<a;r++){if(t[r]===n)return!1;n=t[r]}return!0},fo=function(e){if(_0(e))return e.slice();var t=[],n,r,a=e.length,o,s=0;e:for(r=0;r<a;r++){for(n=e[r],o=0;o<s;o++)if(t[o]===n)continue e;t.push(n),s++}return t},Nd=function(e,t){if(Array.isArray(t))for(var n=0;n<t.length;n++)Nd(e,t[n]);else e.push(t);return e},Ld=function(e,t){return t===void 0&&(t=0),this.indexOf(e,t)!==-1};Array.isArray||(Array.isArray=function(e){return Object.prototype.toString.call(e)==="[object Array]"});Array.prototype.includes||(Array.prototype.includes=Ld);String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")});String.prototype.includes||(String.prototype.includes=Ld);K.util={throttle:function(e,t){var n=t!==void 0?t:200,r,a;return function(){var o=this,s=+new Date,u=arguments;r&&s<r+n?(clearTimeout(a),a=setTimeout(function(){r=void 0,e.apply(o,u)},n)):(r=s,e.apply(o,u))}},escapeRegex:function(e){return e.replace(v0,"\\$1")},set:function(e){if(x.isPlainObject(e))return K.util.set(e._);if(e===null)return function(){};if(typeof e=="function")return function(n,r,a){e(n,"set",r,a)};if(typeof e=="string"&&(e.indexOf(".")!==-1||e.indexOf("[")!==-1||e.indexOf("(")!==-1)){var t=function(n,r,a){for(var o=Bs(a),s,u=o[o.length-1],f,d,h,b,_=0,y=o.length-1;_<y;_++){if(o[_]==="__proto__"||o[_]==="constructor")throw new Error("Cannot set prototype values");if(f=o[_].match(fa),d=o[_].match(yr),f){if(o[_]=o[_].replace(fa,""),n[o[_]]=[],s=o.slice(),s.splice(0,_+1),b=s.join("."),Array.isArray(r))for(var g=0,S=r.length;g<S;g++)h={},t(h,r[g],b),n[o[_]].push(h);else n[o[_]]=r;return}else d&&(o[_]=o[_].replace(yr,""),n=n[o[_]](r));(n[o[_]]===null||n[o[_]]===void 0)&&(n[o[_]]={}),n=n[o[_]]}u.match(yr)?n=n[u.replace(yr,"")](r):n[u.replace(fa,"")]=r};return function(n,r){return t(n,r,e)}}else return function(n,r){n[e]=r}},get:function(e){if(x.isPlainObject(e)){var t={};return x.each(e,function(r,a){a&&(t[r]=K.util.get(a))}),function(r,a,o,s){var u=t[a]||t._;return u!==void 0?u(r,a,o,s):r}}else{if(e===null)return function(r){return r};if(typeof e=="function")return function(r,a,o,s){return e(r,a,o,s)};if(typeof e=="string"&&(e.indexOf(".")!==-1||e.indexOf("[")!==-1||e.indexOf("(")!==-1)){var n=function(r,a,o){var s,u,f,d;if(o!=="")for(var h=Bs(o),b=0,_=h.length;b<_;b++){if(s=h[b].match(fa),u=h[b].match(yr),s){if(h[b]=h[b].replace(fa,""),h[b]!==""&&(r=r[h[b]]),f=[],h.splice(0,b+1),d=h.join("."),Array.isArray(r))for(var y=0,g=r.length;y<g;y++)f.push(n(r[y],a,d));var S=s[0].substring(1,s[0].length-1);r=S===""?f:f.join(S);break}else if(u){h[b]=h[b].replace(yr,""),r=r[h[b]]();continue}if(r===null||r[h[b]]===null)return null;if(r===void 0||r[h[b]]===void 0)return;r=r[h[b]]}return r};return function(r,a){return n(r,a,e)}}else return function(r,a){return r[e]}}}};function Fa(e){var t="a aa ai ao as b fn i m o s ",n,r,a={};x.each(e,function(o,s){n=o.match(/^([^A-Z]+?)([A-Z])/),n&&t.indexOf(n[1]+" ")!==-1&&(r=o.replace(n[0],n[2].toLowerCase()),a[r]=o,n[1]==="o"&&Fa(e[o]))}),e._hungarianMap=a}function pn(e,t,n){e._hungarianMap||Fa(e);var r;x.each(t,function(a,o){r=e._hungarianMap[a],r!==void 0&&(n||t[r]===void 0)&&(r.charAt(0)==="o"?(t[r]||(t[r]={}),x.extend(!0,t[r],t[a]),pn(e[r],t[r],n)):t[r]=t[a])})}function js(e){var t=K.defaults.oLanguage,n=t.sDecimal;if(n&&Xs(n),e){var r=e.sZeroRecords;!e.sEmptyTable&&r&&t.sEmptyTable==="No data available in table"&&Wt(e,e,"sZeroRecords","sEmptyTable"),!e.sLoadingRecords&&r&&t.sLoadingRecords==="Loading..."&&Wt(e,e,"sZeroRecords","sLoadingRecords"),e.sInfoThousands&&(e.sThousands=e.sInfoThousands);var a=e.sDecimal;a&&n!==a&&Xs(a)}}var vt=function(e,t,n){e[t]!==void 0&&(e[n]=e[t])};function Wf(e){vt(e,"ordering","bSort"),vt(e,"orderMulti","bSortMulti"),vt(e,"orderClasses","bSortClasses"),vt(e,"orderCellsTop","bSortCellsTop"),vt(e,"order","aaSorting"),vt(e,"orderFixed","aaSortingFixed"),vt(e,"paging","bPaginate"),vt(e,"pagingType","sPaginationType"),vt(e,"pageLength","iDisplayLength"),vt(e,"searching","bFilter"),typeof e.sScrollX=="boolean"&&(e.sScrollX=e.sScrollX?"100%":""),typeof e.scrollX=="boolean"&&(e.scrollX=e.scrollX?"100%":"");var t=e.aoSearchCols;if(t)for(var n=0,r=t.length;n<r;n++)t[n]&&pn(K.models.oSearch,t[n])}function Od(e){vt(e,"orderable","bSortable"),vt(e,"orderData","aDataSort"),vt(e,"orderSequence","asSorting"),vt(e,"orderDataType","sortDataType");var t=e.aDataSort;typeof t=="number"&&!Array.isArray(t)&&(e.aDataSort=[t])}function Id(e){if(!K.__browser){var t={};K.__browser=t;var n=x("<div/>").css({position:"fixed",top:0,left:x(window).scrollLeft()*-1,height:1,width:1,overflow:"hidden"}).append(x("<div/>").css({position:"absolute",top:1,left:1,width:100,overflow:"scroll"}).append(x("<div/>").css({width:"100%",height:10}))).appendTo("body"),r=n.children(),a=r.children();t.barWidth=r[0].offsetWidth-r[0].clientWidth,t.bScrollOversize=a[0].offsetWidth===100&&r[0].clientWidth!==100,t.bScrollbarLeft=Math.round(a.offset().left)!==1,t.bBounding=!!n[0].getBoundingClientRect().width,n.remove()}x.extend(e.oBrowser,K.__browser),e.oScroll.iBarWidth=K.__browser.barWidth}function Vf(e,t,n,r,a,o){var s=r,u,f=!1;for(n!==void 0&&(u=n,f=!0);s!==a;)e.hasOwnProperty(s)&&(u=f?t(u,e[s],s,e):e[s],f=!0,s+=o);return u}function xl(e,t){var n=K.defaults.column,r=e.aoColumns.length,a=x.extend({},K.models.oColumn,n,{nTh:t||document.createElement("th"),sTitle:n.sTitle?n.sTitle:t?t.innerHTML:"",aDataSort:n.aDataSort?n.aDataSort:[r],mData:n.mData?n.mData:r,idx:r});e.aoColumns.push(a);var o=e.aoPreSearchCols;o[r]=x.extend({},K.models.oSearch,o[r]),Vi(e,r,x(t).data())}function Vi(e,t,n){var r=e.aoColumns[t],a=e.oClasses,o=x(r.nTh);if(!r.sWidthOrig){r.sWidthOrig=o.attr("width")||null;var s=(o.attr("style")||"").match(/width:\s*(\d+[pxem%]+)/);s&&(r.sWidthOrig=s[1])}if(n!=null){Od(n),pn(K.defaults.column,n,!0),n.mDataProp!==void 0&&!n.mData&&(n.mData=n.mDataProp),n.sType&&(r._sManualType=n.sType),n.className&&!n.sClass&&(n.sClass=n.className),n.sClass&&o.addClass(n.sClass);var u=r.sClass;x.extend(r,n),Wt(r,n,"sWidth","sWidthOrig"),u!==r.sClass&&(r.sClass=u+" "+r.sClass),n.iDataSort!==void 0&&(r.aDataSort=[n.iDataSort]),Wt(r,n,"aDataSort"),r.ariaTitle||(r.ariaTitle=o.attr("aria-label"))}var f=r.mData,d=Mr(f),h=r.mRender?Mr(r.mRender):null,b=function(g){return typeof g=="string"&&g.indexOf("@")!==-1};r._bAttrSrc=x.isPlainObject(f)&&(b(f.sort)||b(f.type)||b(f.filter)),r._setter=null,r.fnGetData=function(g,S,L){var O=d(g,S,void 0,L);return h&&S?h(O,S,g,L):O},r.fnSetData=function(g,S,L){return On(f)(g,S,L)},typeof f!="number"&&!r._isArrayHost&&(e._rowReadObject=!0),e.oFeatures.bSort||(r.bSortable=!1,o.addClass(a.sSortableNone));var _=x.inArray("asc",r.asSorting)!==-1,y=x.inArray("desc",r.asSorting)!==-1;!r.bSortable||!_&&!y?(r.sSortingClass=a.sSortableNone,r.sSortingClassJUI=""):_&&!y?(r.sSortingClass=a.sSortableAsc,r.sSortingClassJUI=a.sSortJUIAscAllowed):!_&&y?(r.sSortingClass=a.sSortableDesc,r.sSortingClassJUI=a.sSortJUIDescAllowed):(r.sSortingClass=a.sSortable,r.sSortingClassJUI=a.sSortJUI)}function Ha(e){if(e.oFeatures.bAutoWidth!==!1){var t=e.aoColumns;Pl(e);for(var n=0,r=t.length;n<r;n++)t[n].nTh.style.width=t[n].sWidth}var a=e.oScroll;(a.sY!==""||a.sX!=="")&&_o(e),Pe(e,null,"column-sizing",[e])}function Ma(e,t){var n=co(e,"bVisible");return typeof n[t]=="number"?n[t]:null}function ja(e,t){var n=co(e,"bVisible"),r=x.inArray(t,n);return r!==-1?r:null}function Xr(e){var t=0;return x.each(e.aoColumns,function(n,r){r.bVisible&&x(r.nTh).css("display")!=="none"&&t++}),t}function co(e,t){var n=[];return x.map(e.aoColumns,function(r,a){r[t]&&n.push(a)}),n}function Al(e){var t=e.aoColumns,n=e.aoData,r=K.ext.type.detect,a,o,s,u,f,d,h,b,_;for(a=0,o=t.length;a<o;a++)if(h=t[a],_=[],!h.sType&&h._sManualType)h.sType=h._sManualType;else if(!h.sType){for(s=0,u=r.length;s<u;s++){for(f=0,d=n.length;f<d&&(_[f]===void 0&&(_[f]=gt(e,f,a,"type")),b=r[s](_[f],e),!(!b&&s!==r.length-1||b==="html"&&!nn(_[f])));f++);if(b){h.sType=b;break}}h.sType||(h.sType="string")}}function Rd(e,t,n,r){var a,o,s,u,f,d,h,b=e.aoColumns;if(t)for(a=t.length-1;a>=0;a--){h=t[a];var _=h.target!==void 0?h.target:h.targets!==void 0?h.targets:h.aTargets;for(Array.isArray(_)||(_=[_]),s=0,u=_.length;s<u;s++)if(typeof _[s]=="number"&&_[s]>=0){for(;b.length<=_[s];)xl(e);r(_[s],h)}else if(typeof _[s]=="number"&&_[s]<0)r(b.length+_[s],h);else if(typeof _[s]=="string")for(f=0,d=b.length;f<d;f++)(_[s]=="_all"||x(b[f].nTh).hasClass(_[s]))&&r(f,h)}if(n)for(a=0,o=n.length;a<o;a++)r(a,n[a])}function Hn(e,t,n,r){var a=e.aoData.length,o=x.extend(!0,{},K.models.oRow,{src:n?"dom":"data",idx:a});o._aData=t,e.aoData.push(o);for(var s=e.aoColumns,u=0,f=s.length;u<f;u++)s[u].sType=null;e.aiDisplayMaster.push(a);var d=e.rowIdFn(t);return d!==void 0&&(e.aIds[d]=o),(n||!e.oFeatures.bDeferRender)&&Nl(e,a,n,r),a}function ho(e,t){var n;return t instanceof x||(t=x(t)),t.map(function(r,a){return n=El(e,a),Hn(e,n.data,a,n.cells)})}function g0(e,t){return t._DT_RowIndex!==void 0?t._DT_RowIndex:null}function y0(e,t,n){return x.inArray(n,e.aoData[t].anCells)}function gt(e,t,n,r){r==="search"?r="filter":r==="order"&&(r="sort");var a=e.iDraw,o=e.aoColumns[n],s=e.aoData[t]._aData,u=o.sDefaultContent,f=o.fnGetData(s,r,{settings:e,row:t,col:n});if(f===void 0)return e.iDrawError!=a&&u===null&&(Pt(e,0,"Requested unknown parameter "+(typeof o.mData=="function"?"{function}":"'"+o.mData+"'")+" for row "+t+", column "+n,4),e.iDrawError=a),u;if((f===s||f===null)&&u!==null&&r!==void 0)f=u;else if(typeof f=="function")return f.call(s);if(f===null&&r==="display")return"";if(r==="filter"){var d=K.ext.type.search;d[o.sType]&&(f=d[o.sType](f))}return f}function Pd(e,t,n,r){var a=e.aoColumns[n],o=e.aoData[t]._aData;a.fnSetData(o,r,{settings:e,row:t,col:n})}var fa=/\[.*?\]$/,yr=/\(\)$/;function Bs(e){return x.map(e.match(/(\\.|[^\.])+/g)||[""],function(t){return t.replace(/\\\./g,".")})}var Mr=K.util.get,On=K.util.set;function Ws(e){return _t(e.aoData,"_aData")}function po(e){e.aoData.length=0,e.aiDisplayMaster.length=0,e.aiDisplay.length=0,e.aIds={}}function Oi(e,t,n){for(var r=-1,a=0,o=e.length;a<o;a++)e[a]==t?r=a:e[a]>t&&e[a]--;r!=-1&&n===void 0&&e.splice(r,1)}function Ba(e,t,n,r){var a=e.aoData[t],o,s,u=function(h,b){for(;h.childNodes.length;)h.removeChild(h.firstChild);h.innerHTML=gt(e,t,b,"display")};if(n==="dom"||(!n||n==="auto")&&a.src==="dom")a._aData=El(e,a,r,r===void 0?void 0:a._aData).data;else{var f=a.anCells;if(f)if(r!==void 0)u(f[r],r);else for(o=0,s=f.length;o<s;o++)u(f[o],o)}a._aSortData=null,a._aFilterData=null;var d=e.aoColumns;if(r!==void 0)d[r].sType=null;else{for(o=0,s=d.length;o<s;o++)d[o].sType=null;Ll(e,a)}}function El(e,t,n,r){var a=[],o=t.firstChild,s,u,f=0,d,h=e.aoColumns,b=e._rowReadObject;r=r!==void 0?r:b?{}:[];var _=function(C,Y){if(typeof C=="string"){var W=C.indexOf("@");if(W!==-1){var B=C.substring(W+1),Q=On(C);Q(r,Y.getAttribute(B))}}},y=function(C){if(n===void 0||n===f)if(u=h[f],d=C.innerHTML.trim(),u&&u._bAttrSrc){var Y=On(u.mData._);Y(r,d),_(u.mData.sort,C),_(u.mData.type,C),_(u.mData.filter,C)}else b?(u._setter||(u._setter=On(u.mData)),u._setter(r,d)):r[f]=d;f++};if(o)for(;o;)s=o.nodeName.toUpperCase(),(s=="TD"||s=="TH")&&(y(o),a.push(o)),o=o.nextSibling;else{a=t.anCells;for(var g=0,S=a.length;g<S;g++)y(a[g])}var L=t.firstChild?t:t.nTr;if(L){var O=L.getAttribute("id");O&&On(e.rowId)(r,O)}return{data:r,cells:a}}function Nl(e,t,n,r){var a=e.aoData[t],o=a._aData,s=[],u,f,d,h,b,_;if(a.nTr===null){for(u=n||document.createElement("tr"),a.nTr=u,a.anCells=s,u._DT_RowIndex=t,Ll(e,a),h=0,b=e.aoColumns.length;h<b;h++)d=e.aoColumns[h],_=!n,f=_?document.createElement(d.sCellType):r[h],f||Pt(e,0,"Incorrect column count",18),f._DT_CellIndex={row:t,column:h},s.push(f),(_||(d.mRender||d.mData!==h)&&(!x.isPlainObject(d.mData)||d.mData._!==h+".display"))&&(f.innerHTML=gt(e,t,h,"display")),d.sClass&&(f.className+=" "+d.sClass),d.bVisible&&!n?u.appendChild(f):!d.bVisible&&n&&f.parentNode.removeChild(f),d.fnCreatedCell&&d.fnCreatedCell.call(e.oInstance,f,gt(e,t,h),o,t,h);Pe(e,"aoRowCreatedCallback",null,[u,o,t,s])}}function Ll(e,t){var n=t.nTr,r=t._aData;if(n){var a=e.rowIdFn(r);if(a&&(n.id=a),r.DT_RowClass){var o=r.DT_RowClass.split(" ");t.__rowc=t.__rowc?fo(t.__rowc.concat(o)):o,x(n).removeClass(t.__rowc.join(" ")).addClass(r.DT_RowClass)}r.DT_RowAttr&&x(n).attr(r.DT_RowAttr),r.DT_RowData&&x(n).data(r.DT_RowData)}}function kd(e){var t,n,r,a,o,s=e.nTHead,u=e.nTFoot,f=x("th, td",s).length===0,d=e.oClasses,h=e.aoColumns;for(f&&(a=x("<tr/>").appendTo(s)),t=0,n=h.length;t<n;t++)o=h[t],r=x(o.nTh).addClass(o.sClass),f&&r.appendTo(a),e.oFeatures.bSort&&(r.addClass(o.sSortingClass),o.bSortable!==!1&&(r.attr("tabindex",e.iTabIndex).attr("aria-controls",e.sTableId),$l(e,o.nTh,t))),o.sTitle!=r[0].innerHTML&&r.html(o.sTitle),Ml(e,"header")(e,r,o,d);if(f&&Ca(e.aoHeader,s),x(s).children("tr").children("th, td").addClass(d.sHeaderTH),x(u).children("tr").children("th, td").addClass(d.sFooterTH),u!==null){var b=e.aoFooter[0];for(t=0,n=b.length;t<n;t++)o=h[t],o?(o.nTf=b[t].cell,o.sClass&&x(o.nTf).addClass(o.sClass)):Pt(e,0,"Incorrect column count",18)}}function Ta(e,t,n){var r,a,o,s,u,f,d,h=[],b=[],_=e.aoColumns.length,y,g;if(t){for(n===void 0&&(n=!1),r=0,a=t.length;r<a;r++){for(h[r]=t[r].slice(),h[r].nTr=t[r].nTr,o=_-1;o>=0;o--)!e.aoColumns[o].bVisible&&!n&&h[r].splice(o,1);b.push([])}for(r=0,a=h.length;r<a;r++){if(d=h[r].nTr,d)for(;f=d.firstChild;)d.removeChild(f);for(o=0,s=h[r].length;o<s;o++)if(y=1,g=1,b[r][o]===void 0){for(d.appendChild(h[r][o].cell),b[r][o]=1;h[r+y]!==void 0&&h[r][o].cell==h[r+y][o].cell;)b[r+y][o]=1,y++;for(;h[r][o+g]!==void 0&&h[r][o].cell==h[r][o+g].cell;){for(u=0;u<y;u++)b[r+u][o+g]=1;g++}x(h[r][o].cell).attr("rowspan",y).attr("colspan",g)}}}}function Mn(e,t){w0(e);var n=Pe(e,"aoPreDrawCallback","preDraw",[e]);if(x.inArray(!1,n)!==-1){Dt(e,!1);return}var r=[],a=0,o=e.asStripeClasses,s=o.length,u=e.oLanguage,f=rt(e)=="ssp",d=e.aiDisplay,h=e._iDisplayStart,b=e.fnDisplayEnd();if(e.bDrawing=!0,e.bDeferLoading)e.bDeferLoading=!1,e.iDraw++,Dt(e,!1);else if(!f)e.iDraw++;else if(!e.bDestroying&&!t){Fd(e);return}if(d.length!==0)for(var _=f?0:h,y=f?e.aoData.length:b,g=_;g<y;g++){var S=d[g],L=e.aoData[S];L.nTr===null&&Nl(e,S);var O=L.nTr;if(s!==0){var C=o[a%s];L._sRowStripe!=C&&(x(O).removeClass(L._sRowStripe).addClass(C),L._sRowStripe=C)}Pe(e,"aoRowCallback",null,[O,L._aData,a,g,S]),r.push(O),a++}else{var Y=u.sZeroRecords;e.iDraw==1&&rt(e)=="ajax"?Y=u.sLoadingRecords:u.sEmptyTable&&e.fnRecordsTotal()===0&&(Y=u.sEmptyTable),r[0]=x("<tr/>",{class:s?o[0]:""}).append(x("<td />",{valign:"top",colSpan:Xr(e),class:e.oClasses.sRowEmpty}).html(Y))[0]}Pe(e,"aoHeaderCallback","header",[x(e.nTHead).children("tr")[0],Ws(e),h,b,d]),Pe(e,"aoFooterCallback","footer",[x(e.nTFoot).children("tr")[0],Ws(e),h,b,d]);var W=x(e.nTBody);W.children().detach(),W.append(x(r)),Pe(e,"aoDrawCallback","draw",[e]),e.bSorted=!1,e.bFiltered=!1,e.bDrawing=!1}function nr(e,t){var n=e.oFeatures,r=n.bSort,a=n.bFilter;r&&th(e),a?Va(e,e.oPreviousSearch):e.aiDisplay=e.aiDisplayMaster.slice(),t!==!0&&(e._iDisplayStart=0),e._drawHold=t,Mn(e),e._drawHold=!1}function $d(e){var t=e.oClasses,n=x(e.nTable),r=x("<div/>").insertBefore(n),a=e.oFeatures,o=x("<div/>",{id:e.sTableId+"_wrapper",class:t.sWrapper+(e.nTFoot?"":" "+t.sNoFooter)});e.nHolding=r[0],e.nTableWrapper=o[0],e.nTableReinsertBefore=e.nTable.nextSibling;for(var s=e.sDom.split(""),u,f,d,h,b,_,y=0;y<s.length;y++){if(u=null,f=s[y],f=="<"){if(d=x("<div/>")[0],h=s[y+1],h=="'"||h=='"'){for(b="",_=2;s[y+_]!=h;)b+=s[y+_],_++;if(b=="H"?b=t.sJUIHeader:b=="F"&&(b=t.sJUIFooter),b.indexOf(".")!=-1){var g=b.split(".");d.id=g[0].substr(1,g[0].length-1),d.className=g[1]}else b.charAt(0)=="#"?d.id=b.substr(1,b.length-1):d.className=b;y+=_}o.append(d),o=x(d)}else if(f==">")o=o.parent();else if(f=="l"&&a.bPaginate&&a.bLengthChange)u=Yd(e);else if(f=="f"&&a.bFilter)u=jd(e);else if(f=="r"&&a.bProcessing)u=zd(e);else if(f=="t")u=Jd(e);else if(f=="i"&&a.bInfo)u=Ud(e);else if(f=="p"&&a.bPaginate)u=Gd(e);else if(K.ext.feature.length!==0){for(var S=K.ext.feature,L=0,O=S.length;L<O;L++)if(f==S[L].cFeature){u=S[L].fnInit(e);break}}if(u){var C=e.aanFeatures;C[f]||(C[f]=[]),C[f].push(u),o.append(u)}}r.replaceWith(o),e.nHolding=null}function Ca(e,t){var n=x(t).children("tr"),r,a,o,s,u,f,d,h,b,_,y,g=function(S,L,O){for(var C=S[L];C[O];)O++;return O};for(e.splice(0,e.length),o=0,f=n.length;o<f;o++)e.push([]);for(o=0,f=n.length;o<f;o++)for(r=n[o],h=0,a=r.firstChild;a;){if(a.nodeName.toUpperCase()=="TD"||a.nodeName.toUpperCase()=="TH")for(b=a.getAttribute("colspan")*1,_=a.getAttribute("rowspan")*1,b=!b||b===0||b===1?1:b,_=!_||_===0||_===1?1:_,d=g(e,o,h),y=b===1,u=0;u<b;u++)for(s=0;s<_;s++)e[o+s][d+u]={cell:a,unique:y},e[o+s].nTr=r;a=a.nextSibling}}function vo(e,t,n){var r=[];n||(n=e.aoHeader,t&&(n=[],Ca(n,t)));for(var a=0,o=n.length;a<o;a++)for(var s=0,u=n[a].length;s<u;s++)n[a][s].unique&&(!r[s]||!e.bSortCellsTop)&&(r[s]=n[a][s].cell);return r}function w0(e){var t=rt(e)=="ssp",n=e.iInitDisplayStart;n!==void 0&&n!==-1&&(e._iDisplayStart=t?n:n>=e.fnRecordsDisplay()?0:n,e.iInitDisplayStart=-1)}function mo(e,t,n){if(Pe(e,"aoServerParams","serverParams",[t]),t&&Array.isArray(t)){var r={},a=/(.*?)\[\]$/;x.each(t,function(b,_){var y=_.name.match(a);if(y){var g=y[0];r[g]||(r[g]=[]),r[g].push(_.value)}else r[_.name]=_.value}),t=r}var o,s=e.ajax,u=e.oInstance,f=function(b){var _=e.jqXHR?e.jqXHR.status:null;(b===null||typeof _=="number"&&_==204)&&(b={},Wa(e,b,[]));var y=b.error||b.sError;y&&Pt(e,0,y),e.json=b,Pe(e,null,"xhr",[e,b,e.jqXHR]),n(b)};if(x.isPlainObject(s)&&s.data){o=s.data;var d=typeof o=="function"?o(t,e):o;t=typeof o=="function"&&d?d:x.extend(!0,t,d),delete s.data}var h={data:t,success:f,dataType:"json",cache:!1,type:e.sServerMethod,error:function(b,_,y){var g=Pe(e,null,"xhr",[e,null,e.jqXHR]);x.inArray(!0,g)===-1&&(_=="parsererror"?Pt(e,0,"Invalid JSON response",1):b.readyState===4&&Pt(e,0,"Ajax error",7)),Dt(e,!1)}};e.oAjaxData=t,Pe(e,null,"preXhr",[e,t]),e.fnServerData?e.fnServerData.call(u,e.sAjaxSource,x.map(t,function(b,_){return{name:_,value:b}}),f,e):e.sAjaxSource||typeof s=="string"?e.jqXHR=x.ajax(x.extend(h,{url:s||e.sAjaxSource})):typeof s=="function"?e.jqXHR=s.call(u,t,f,e):(e.jqXHR=x.ajax(x.extend(h,s)),s.data=o)}function Fd(e){e.iDraw++,Dt(e,!0);var t=e._drawHold;mo(e,Hd(e),function(n){e._drawHold=t,Md(e,n),e._drawHold=!1})}function Hd(e){var t=e.aoColumns,n=t.length,r=e.oFeatures,a=e.oPreviousSearch,o=e.aoPreSearchCols,s,u=[],f,d,h,b=Kr(e),_=e._iDisplayStart,y=r.bPaginate!==!1?e._iDisplayLength:-1,g=function(O,C){u.push({name:O,value:C})};g("sEcho",e.iDraw),g("iColumns",n),g("sColumns",_t(t,"sName").join(",")),g("iDisplayStart",_),g("iDisplayLength",y);var S={draw:e.iDraw,columns:[],order:[],start:_,length:y,search:{value:a.sSearch,regex:a.bRegex}};for(s=0;s<n;s++)d=t[s],h=o[s],f=typeof d.mData=="function"?"function":d.mData,S.columns.push({data:f,name:d.sName,searchable:d.bSearchable,orderable:d.bSortable,search:{value:h.sSearch,regex:h.bRegex}}),g("mDataProp_"+s,f),r.bFilter&&(g("sSearch_"+s,h.sSearch),g("bRegex_"+s,h.bRegex),g("bSearchable_"+s,d.bSearchable)),r.bSort&&g("bSortable_"+s,d.bSortable);r.bFilter&&(g("sSearch",a.sSearch),g("bRegex",a.bRegex)),r.bSort&&(x.each(b,function(O,C){S.order.push({column:C.col,dir:C.dir}),g("iSortCol_"+O,C.col),g("sSortDir_"+O,C.dir)}),g("iSortingCols",b.length));var L=K.ext.legacy.ajax;return L===null?e.sAjaxSource?u:S:L?u:S}function Md(e,t){var n=function(d,h){return t[d]!==void 0?t[d]:t[h]},r=Wa(e,t),a=n("sEcho","draw"),o=n("iTotalRecords","recordsTotal"),s=n("iTotalDisplayRecords","recordsFiltered");if(a!==void 0){if(a*1<e.iDraw)return;e.iDraw=a*1}r||(r=[]),po(e),e._iRecordsTotal=parseInt(o,10),e._iRecordsDisplay=parseInt(s,10);for(var u=0,f=r.length;u<f;u++)Hn(e,r[u]);e.aiDisplay=e.aiDisplayMaster.slice(),Mn(e,!0),e._bInitComplete||qi(e,t),Dt(e,!1)}function Wa(e,t,n){var r=x.isPlainObject(e.ajax)&&e.ajax.dataSrc!==void 0?e.ajax.dataSrc:e.sAjaxDataProp;if(!n)return r==="data"?t.aaData||t[r]:r!==""?Mr(r)(t):t;On(r)(t,n)}function jd(e){var t=e.oClasses,n=e.sTableId,r=e.oLanguage,a=e.oPreviousSearch,o=e.aanFeatures,s='<input type="search" class="'+t.sFilterInput+'"/>',u=r.sSearch;u=u.match(/_INPUT_/)?u.replace("_INPUT_",s):u+s;var f=x("<div/>",{id:o.f?null:n+"_filter",class:t.sFilter}).append(x("<label/>").append(u)),d=function(_){o.f;var y=this.value?this.value:"";a.return&&_.key!=="Enter"||y!=a.sSearch&&(Va(e,{sSearch:y,bRegex:a.bRegex,bSmart:a.bSmart,bCaseInsensitive:a.bCaseInsensitive,return:a.return}),e._iDisplayStart=0,Mn(e))},h=e.searchDelay!==null?e.searchDelay:rt(e)==="ssp"?400:0,b=x("input",f).val(a.sSearch).attr("placeholder",r.sSearchPlaceholder).on("keyup.DT search.DT input.DT paste.DT cut.DT",h?kl(d,h):d).on("mouseup.DT",function(_){setTimeout(function(){d.call(b[0],_)},10)}).on("keypress.DT",function(_){if(_.keyCode==13)return!1}).attr("aria-controls",n);return x(e.nTable).on("search.dt.DT",function(_,y){if(e===y)try{b[0]!==document.activeElement&&b.val(a.sSearch)}catch{}}),f[0]}function Va(e,t,n){var r=e.oPreviousSearch,a=e.aoPreSearchCols,o=function(f){r.sSearch=f.sSearch,r.bRegex=f.bRegex,r.bSmart=f.bSmart,r.bCaseInsensitive=f.bCaseInsensitive,r.return=f.return},s=function(f){return f.bEscapeRegex!==void 0?!f.bEscapeRegex:f.bRegex};if(Al(e),rt(e)!="ssp"){Vd(e,t.sSearch,n,s(t),t.bSmart,t.bCaseInsensitive),o(t);for(var u=0;u<a.length;u++)Wd(e,a[u].sSearch,u,s(a[u]),a[u].bSmart,a[u].bCaseInsensitive);Bd(e)}else o(t);e.bFiltered=!0,Pe(e,null,"search",[e])}function Bd(e){for(var t=K.ext.search,n=e.aiDisplay,r,a,o=0,s=t.length;o<s;o++){for(var u=[],f=0,d=n.length;f<d;f++)a=n[f],r=e.aoData[a],t[o](e,r._aFilterData,a,r._aData,f)&&u.push(a);n.length=0,x.merge(n,u)}}function Wd(e,t,n,r,a,o){if(t!==""){for(var s,u=[],f=e.aiDisplay,d=Ol(t,r,a,o),h=0;h<f.length;h++)s=e.aoData[f[h]]._aFilterData[n],d.test(s)&&u.push(f[h]);e.aiDisplay=u}}function Vd(e,t,n,r,a,o){var s=Ol(t,r,a,o),u=e.oPreviousSearch.sSearch,f=e.aiDisplayMaster,d,h,b,_=[];if(K.ext.search.length!==0&&(n=!0),h=qd(e),t.length<=0)e.aiDisplay=f.slice();else{for((h||n||r||u.length>t.length||t.indexOf(u)!==0||e.bSorted)&&(e.aiDisplay=f.slice()),d=e.aiDisplay,b=0;b<d.length;b++)s.test(e.aoData[d[b]]._sFilterRow)&&_.push(d[b]);e.aiDisplay=_}}function Ol(e,t,n,r){if(e=t?e:Il(e),n){var a=x.map(e.match(/["\u201C][^"\u201D]+["\u201D]|[^ ]+/g)||[""],function(o){if(o.charAt(0)==='"'){var s=o.match(/^"(.*)"$/);o=s?s[1]:o}else if(o.charAt(0)==="“"){var s=o.match(/^\u201C(.*)\u201D$/);o=s?s[1]:o}return o.replace('"',"")});e="^(?=.*?"+a.join(")(?=.*?")+").*$"}return new RegExp(e,r?"i":"")}var Il=K.util.escapeRegex,Ii=x("<div>")[0],T0=Ii.textContent!==void 0;function qd(e){var t=e.aoColumns,n,r,a,o,s,u,f,d,h=!1;for(r=0,o=e.aoData.length;r<o;r++)if(d=e.aoData[r],!d._aFilterData){for(u=[],a=0,s=t.length;a<s;a++)n=t[a],n.bSearchable?(f=gt(e,r,a,"filter"),f===null&&(f=""),typeof f!="string"&&f.toString&&(f=f.toString())):f="",f.indexOf&&f.indexOf("&")!==-1&&(Ii.innerHTML=f,f=T0?Ii.textContent:Ii.innerText),f.replace&&(f=f.replace(/[\r\n\u2028]/g,"")),u.push(f);d._aFilterData=u,d._sFilterRow=u.join("  "),h=!0}return h}function qf(e){return{search:e.sSearch,smart:e.bSmart,regex:e.bRegex,caseInsensitive:e.bCaseInsensitive}}function Uf(e){return{sSearch:e.search,bSmart:e.smart,bRegex:e.regex,bCaseInsensitive:e.caseInsensitive}}function Ud(e){var t=e.sTableId,n=e.aanFeatures.i,r=x("<div/>",{class:e.oClasses.sInfo,id:n?null:t+"_info"});return n||(e.aoDrawCallback.push({fn:Xd,sName:"information"}),r.attr("role","status").attr("aria-live","polite"),x(e.nTable).attr("aria-describedby",t+"_info")),r[0]}function Xd(e){var t=e.aanFeatures.i;if(t.length!==0){var n=e.oLanguage,r=e._iDisplayStart+1,a=e.fnDisplayEnd(),o=e.fnRecordsTotal(),s=e.fnRecordsDisplay(),u=s?n.sInfo:n.sInfoEmpty;s!==o&&(u+=" "+n.sInfoFiltered),u+=n.sInfoPostFix,u=Kd(e,u);var f=n.fnInfoCallback;f!==null&&(u=f.call(e.oInstance,e,r,a,o,s,u)),x(t).html(u)}}function Kd(e,t){var n=e.fnFormatNumber,r=e._iDisplayStart+1,a=e._iDisplayLength,o=e.fnRecordsDisplay(),s=a===-1;return t.replace(/_START_/g,n.call(e,r)).replace(/_END_/g,n.call(e,e.fnDisplayEnd())).replace(/_MAX_/g,n.call(e,e.fnRecordsTotal())).replace(/_TOTAL_/g,n.call(e,o)).replace(/_PAGE_/g,n.call(e,s?1:Math.ceil(r/a))).replace(/_PAGES_/g,n.call(e,s?1:Math.ceil(o/a)))}function ga(e){var t,n,r=e.iInitDisplayStart,a=e.aoColumns,o,s=e.oFeatures,u=e.bDeferLoading;if(!e.bInitialised){setTimeout(function(){ga(e)},200);return}for($d(e),kd(e),Ta(e,e.aoHeader),Ta(e,e.aoFooter),Dt(e,!0),s.bAutoWidth&&Pl(e),t=0,n=a.length;t<n;t++)o=a[t],o.sWidth&&(o.nTh.style.width=qe(o.sWidth));Pe(e,null,"preInit",[e]),nr(e);var f=rt(e);(f!="ssp"||u)&&(f=="ajax"?mo(e,[],function(d){var h=Wa(e,d);for(t=0;t<h.length;t++)Hn(e,h[t]);e.iInitDisplayStart=r,nr(e),Dt(e,!1),qi(e,d)}):(Dt(e,!1),qi(e)))}function qi(e,t){e._bInitComplete=!0,(t||e.oInit.aaData)&&Ha(e),Pe(e,null,"plugin-init",[e,t]),Pe(e,"aoInitComplete","init",[e,t])}function Rl(e,t){var n=parseInt(t,10);e._iDisplayLength=n,Hl(e),Pe(e,null,"length",[e,n])}function Yd(e){for(var t=e.oClasses,n=e.sTableId,r=e.aLengthMenu,a=Array.isArray(r[0]),o=a?r[0]:r,s=a?r[1]:r,u=x("<select/>",{name:n+"_length","aria-controls":n,class:t.sLengthSelect}),f=0,d=o.length;f<d;f++)u[0][f]=new Option(typeof s[f]=="number"?e.fnFormatNumber(s[f]):s[f],o[f]);var h=x("<div><label/></div>").addClass(t.sLength);return e.aanFeatures.l||(h[0].id=n+"_length"),h.children().append(e.oLanguage.sLengthMenu.replace("_MENU_",u[0].outerHTML)),x("select",h).val(e._iDisplayLength).on("change.DT",function(b){Rl(e,x(this).val()),Mn(e)}),x(e.nTable).on("length.dt.DT",function(b,_,y){e===_&&x("select",h).val(y)}),h[0]}function Gd(e){var t=e.sPaginationType,n=K.ext.pager[t],r=typeof n=="function",a=function(u){Mn(u)},o=x("<div/>").addClass(e.oClasses.sPaging+t)[0],s=e.aanFeatures;return r||n.fnInit(e,o,a),s.p||(o.id=e.sTableId+"_paginate",e.aoDrawCallback.push({fn:function(u){if(r){var f=u._iDisplayStart,d=u._iDisplayLength,h=u.fnRecordsDisplay(),b=d===-1,_=b?0:Math.ceil(f/d),y=b?1:Math.ceil(h/d),g=n(_,y),S,L;for(S=0,L=s.p.length;S<L;S++)Ml(u,"pageButton")(u,s.p[S],S,g,_,y)}else n.fnUpdate(u,a)},sName:"pagination"})),o}function bo(e,t,n){var r=e._iDisplayStart,a=e._iDisplayLength,o=e.fnRecordsDisplay();o===0||a===-1?r=0:typeof t=="number"?(r=t*a,r>o&&(r=0)):t=="first"?r=0:t=="previous"?(r=a>=0?r-a:0,r<0&&(r=0)):t=="next"?r+a<o&&(r+=a):t=="last"?r=Math.floor((o-1)/a)*a:Pt(e,0,"Unknown paging action: "+t,5);var s=e._iDisplayStart!==r;return e._iDisplayStart=r,s?(Pe(e,null,"page",[e]),n&&Mn(e)):Pe(e,null,"page-nc",[e]),s}function zd(e){return x("<div/>",{id:e.aanFeatures.r?null:e.sTableId+"_processing",class:e.oClasses.sProcessing,role:"status"}).html(e.oLanguage.sProcessing).append("<div><div></div><div></div><div></div><div></div></div>").insertBefore(e.nTable)[0]}function Dt(e,t){e.oFeatures.bProcessing&&x(e.aanFeatures.r).css("display",t?"block":"none"),Pe(e,null,"processing",[e,t])}function Jd(e){var t=x(e.nTable),n=e.oScroll;if(n.sX===""&&n.sY==="")return e.nTable;var r=n.sX,a=n.sY,o=e.oClasses,s=t.children("caption"),u=s.length?s[0]._captionSide:null,f=x(t[0].cloneNode(!1)),d=x(t[0].cloneNode(!1)),h=t.children("tfoot"),b="<div/>",_=function(C){return C?qe(C):null};h.length||(h=null);var y=x(b,{class:o.sScrollWrapper}).append(x(b,{class:o.sScrollHead}).css({overflow:"hidden",position:"relative",border:0,width:r?_(r):"100%"}).append(x(b,{class:o.sScrollHeadInner}).css({"box-sizing":"content-box",width:n.sXInner||"100%"}).append(f.removeAttr("id").css("margin-left",0).append(u==="top"?s:null).append(t.children("thead"))))).append(x(b,{class:o.sScrollBody}).css({position:"relative",overflow:"auto",width:_(r)}).append(t));h&&y.append(x(b,{class:o.sScrollFoot}).css({overflow:"hidden",border:0,width:r?_(r):"100%"}).append(x(b,{class:o.sScrollFootInner}).append(d.removeAttr("id").css("margin-left",0).append(u==="bottom"?s:null).append(t.children("tfoot")))));var g=y.children(),S=g[0],L=g[1],O=h?g[2]:null;return r&&x(L).on("scroll.DT",function(C){var Y=this.scrollLeft;S.scrollLeft=Y,h&&(O.scrollLeft=Y)}),x(L).css("max-height",a),n.bCollapse||x(L).css("height",a),e.nScrollHead=S,e.nScrollBody=L,e.nScrollFoot=O,e.aoDrawCallback.push({fn:_o,sName:"scrolling"}),y[0]}function _o(e){var t=e.oScroll,n=t.sX,r=t.sXInner,a=t.sY,o=t.iBarWidth,s=x(e.nScrollHead),u=s[0].style,f=s.children("div"),d=f[0].style,h=f.children("table"),b=e.nScrollBody,_=x(b),y=b.style,g=x(e.nScrollFoot),S=g.children("div"),L=S.children("table"),O=x(e.nTHead),C=x(e.nTable),Y=C[0],W=Y.style,B=e.nTFoot?x(e.nTFoot):null,Q=e.oBrowser,v=Q.bScrollOversize;_t(e.aoColumns,"nTh");var ee,G,re,ie,pe,se,ae=[],we=[],Te=[],me=[],xe,Le,Fe,ct=function(oe){var _e=oe.style;_e.paddingTop="0",_e.paddingBottom="0",_e.borderTopWidth="0",_e.borderBottomWidth="0",_e.height=0},We=b.scrollHeight>b.clientHeight;if(e.scrollBarVis!==We&&e.scrollBarVis!==void 0){e.scrollBarVis=We,Ha(e);return}else e.scrollBarVis=We;C.children("thead, tfoot").remove(),B&&(se=B.clone().prependTo(C),G=B.find("tr"),ie=se.find("tr"),se.find("[id]").removeAttr("id")),pe=O.clone().prependTo(C),ee=O.find("tr"),re=pe.find("tr"),pe.find("th, td").removeAttr("tabindex"),pe.find("[id]").removeAttr("id"),n||(y.width="100%",s[0].style.width="100%"),x.each(vo(e,pe),function(oe,_e){xe=Ma(e,oe),_e.style.width=e.aoColumns[xe].sWidth}),B&&Jt(function(oe){oe.style.width=""},ie),Fe=C.outerWidth(),n===""?(W.width="100%",v&&(C.find("tbody").height()>b.offsetHeight||_.css("overflow-y")=="scroll")&&(W.width=qe(C.outerWidth()-o)),Fe=C.outerWidth()):r!==""&&(W.width=qe(r),Fe=C.outerWidth()),Jt(ct,re),Jt(function(oe){var _e=window.getComputedStyle?window.getComputedStyle(oe).width:qe(x(oe).width());Te.push(oe.innerHTML),ae.push(_e)},re),Jt(function(oe,_e){oe.style.width=ae[_e]},ee),x(re).css("height",0),B&&(Jt(ct,ie),Jt(function(oe){me.push(oe.innerHTML),we.push(qe(x(oe).css("width")))},ie),Jt(function(oe,_e){oe.style.width=we[_e]},G),x(ie).height(0)),Jt(function(oe,_e){oe.innerHTML='<div class="dataTables_sizing">'+Te[_e]+"</div>",oe.childNodes[0].style.height="0",oe.childNodes[0].style.overflow="hidden",oe.style.width=ae[_e]},re),B&&Jt(function(oe,_e){oe.innerHTML='<div class="dataTables_sizing">'+me[_e]+"</div>",oe.childNodes[0].style.height="0",oe.childNodes[0].style.overflow="hidden",oe.style.width=we[_e]},ie),Math.round(C.outerWidth())<Math.round(Fe)?(Le=b.scrollHeight>b.offsetHeight||_.css("overflow-y")=="scroll"?Fe+o:Fe,v&&(b.scrollHeight>b.offsetHeight||_.css("overflow-y")=="scroll")&&(W.width=qe(Le-o)),(n===""||r!=="")&&Pt(e,1,"Possible column misalignment",6)):Le="100%",y.width=qe(Le),u.width=qe(Le),B&&(e.nScrollFoot.style.width=qe(Le)),a||v&&(y.height=qe(Y.offsetHeight+o));var ze=C.outerWidth();h[0].style.width=qe(ze),d.width=qe(ze);var it=C.height()>b.clientHeight||_.css("overflow-y")=="scroll",tt="padding"+(Q.bScrollbarLeft?"Left":"Right");d[tt]=it?o+"px":"0px",B&&(L[0].style.width=qe(ze),S[0].style.width=qe(ze),S[0].style[tt]=it?o+"px":"0px"),C.children("colgroup").insertBefore(C.children("thead")),_.trigger("scroll"),(e.bSorted||e.bFiltered)&&!e._drawHold&&(b.scrollTop=0)}function Jt(e,t,n){for(var r=0,a=0,o=t.length,s,u;a<o;){for(s=t[a].firstChild,u=n?n[a].firstChild:null;s;)s.nodeType===1&&(n?e(s,u,r):e(s,r),r++),s=s.nextSibling,u=n?u.nextSibling:null;a++}}var C0=/<.*?>/g;function Pl(e){var t=e.nTable,n=e.aoColumns,r=e.oScroll,a=r.sY,o=r.sX,s=r.sXInner,u=n.length,f=co(e,"bVisible"),d=x("th",e.nTHead),h=t.getAttribute("width"),b=t.parentNode,_=!1,y,g,S,L=e.oBrowser,O=L.bScrollOversize,C=t.style.width;C&&C.indexOf("%")!==-1&&(h=C);var Y=Qd(_t(n,"sWidthOrig"),b);for(y=0;y<f.length;y++)g=n[f[y]],g.sWidth!==null&&(g.sWidth=Y[y],_=!0);if(O||!_&&!o&&!a&&u==Xr(e)&&u==d.length)for(y=0;y<u;y++){var W=Ma(e,y);W!==null&&(n[W].sWidth=qe(d.eq(y).width()))}else{var B=x(t).clone().css("visibility","hidden").removeAttr("id");B.find("tbody tr").remove();var Q=x("<tr/>").appendTo(B.find("tbody"));for(B.find("thead, tfoot").remove(),B.append(x(e.nTHead).clone()).append(x(e.nTFoot).clone()),B.find("tfoot th, tfoot td").css("width",""),d=vo(e,B.find("thead")[0]),y=0;y<f.length;y++)g=n[f[y]],d[y].style.width=g.sWidthOrig!==null&&g.sWidthOrig!==""?qe(g.sWidthOrig):"",g.sWidthOrig&&o&&x(d[y]).append(x("<div/>").css({width:g.sWidthOrig,margin:0,padding:0,border:0,height:1}));if(e.aoData.length)for(y=0;y<f.length;y++)S=f[y],g=n[S],x(Zd(e,S)).clone(!1).append(g.sContentPadding).appendTo(Q);x("[name]",B).removeAttr("name");var v=x("<div/>").css(o||a?{position:"absolute",top:0,left:0,height:1,right:0,overflow:"hidden"}:{}).append(B).appendTo(b);o&&s?B.width(s):o?(B.css("width","auto"),B.removeAttr("width"),B.width()<b.clientWidth&&h&&B.width(b.clientWidth)):a?B.width(b.clientWidth):h&&B.width(h);var ee=0;for(y=0;y<f.length;y++){var G=x(d[y]),re=G.outerWidth()-G.width(),ie=L.bBounding?Math.ceil(d[y].getBoundingClientRect().width):G.outerWidth();ee+=ie,n[f[y]].sWidth=qe(ie-re)}t.style.width=qe(ee),v.remove()}if(h&&(t.style.width=qe(h)),(h||o)&&!e._reszEvt){var pe=function(){x(window).on("resize.DT-"+e.sInstance,kl(function(){Ha(e)}))};O?setTimeout(pe,1e3):pe(),e._reszEvt=!0}}var kl=K.util.throttle;function Qd(e,t){for(var n=[],r=[],a=0;a<e.length;a++)e[a]?n.push(x("<div/>").css("width",qe(e[a])).appendTo(t||document.body)):n.push(null);for(var a=0;a<e.length;a++)r.push(n[a]?n[a][0].offsetWidth:null);return x(n).remove(),r}function Zd(e,t){var n=eh(e,t);if(n<0)return null;var r=e.aoData[n];return r.nTr?r.anCells[t]:x("<td/>").html(gt(e,n,t,"display"))[0]}function eh(e,t){for(var n,r=-1,a=-1,o=0,s=e.aoData.length;o<s;o++)n=gt(e,o,t,"display")+"",n=n.replace(C0,""),n=n.replace(/&nbsp;/g," "),n.length>r&&(r=n.length,a=o);return a}function qe(e){return e===null?"0px":typeof e=="number"?e<0?"0px":e+"px":e.match(/\d$/)?e+"px":e}function Kr(e){var t,n,r,a=[],o=e.aoColumns,s,u,f,d,h=e.aaSortingFixed,b=x.isPlainObject(h),_=[],y=function(g){g.length&&!Array.isArray(g[0])?_.push(g):x.merge(_,g)};for(Array.isArray(h)&&y(h),b&&h.pre&&y(h.pre),y(e.aaSorting),b&&h.post&&y(h.post),t=0;t<_.length;t++)for(d=_[t][0],s=o[d].aDataSort,n=0,r=s.length;n<r;n++)u=s[n],f=o[u].sType||"string",_[t]._idx===void 0&&(_[t]._idx=x.inArray(_[t][1],o[u].asSorting)),a.push({src:d,col:u,dir:_[t][1],index:_[t]._idx,type:f,formatter:K.ext.type.order[f+"-pre"]});return a}function th(e){var t,n,r,a=[],o=K.ext.type.order,s=e.aoData;e.aoColumns;var u=0,f,d=e.aiDisplayMaster,h;for(Al(e),h=Kr(e),t=0,n=h.length;t<n;t++)f=h[t],f.formatter&&u++,rh(e,f.col);if(rt(e)!="ssp"&&h.length!==0){for(t=0,r=d.length;t<r;t++)a[d[t]]=t;u===h.length?d.sort(function(b,_){var y,g,S,L,O,C=h.length,Y=s[b]._aSortData,W=s[_]._aSortData;for(S=0;S<C;S++)if(O=h[S],y=Y[O.col],g=W[O.col],L=y<g?-1:y>g?1:0,L!==0)return O.dir==="asc"?L:-L;return y=a[b],g=a[_],y<g?-1:y>g?1:0}):d.sort(function(b,_){var y,g,S,L,O,C,Y=h.length,W=s[b]._aSortData,B=s[_]._aSortData;for(S=0;S<Y;S++)if(O=h[S],y=W[O.col],g=B[O.col],C=o[O.type+"-"+O.dir]||o["string-"+O.dir],L=C(y,g),L!==0)return L;return y=a[b],g=a[_],y<g?-1:y>g?1:0})}e.bSorted=!0}function nh(e){for(var t,n,r=e.aoColumns,a=Kr(e),o=e.oLanguage.oAria,s=0,u=r.length;s<u;s++){var f=r[s],d=f.asSorting,h=f.ariaTitle||f.sTitle.replace(/<.*?>/g,""),b=f.nTh;b.removeAttribute("aria-sort"),f.bSortable?(a.length>0&&a[0].col==s?(b.setAttribute("aria-sort",a[0].dir=="asc"?"ascending":"descending"),n=d[a[0].index+1]||d[0]):n=d[0],t=h+(n==="asc"?o.sSortAscending:o.sSortDescending)):t=h,b.setAttribute("aria-label",t)}}function Vs(e,t,n,r){var a=e.aoColumns[t],o=e.aaSorting,s=a.asSorting,u,f=function(h,b){var _=h._idx;return _===void 0&&(_=x.inArray(h[1],s)),_+1<s.length?_+1:b?null:0};if(typeof o[0]=="number"&&(o=e.aaSorting=[o]),n&&e.oFeatures.bSortMulti){var d=x.inArray(t,_t(o,"0"));d!==-1?(u=f(o[d],!0),u===null&&o.length===1&&(u=0),u===null?o.splice(d,1):(o[d][1]=s[u],o[d]._idx=u)):(o.push([t,s[0],0]),o[o.length-1]._idx=0)}else o.length&&o[0][0]==t?(u=f(o[0]),o.length=1,o[0][1]=s[u],o[0]._idx=u):(o.length=0,o.push([t,s[0]]),o[0]._idx=0);nr(e),typeof r=="function"&&r(e)}function $l(e,t,n,r){var a=e.aoColumns[n];Fl(t,{},function(o){a.bSortable!==!1&&(e.oFeatures.bProcessing?(Dt(e,!0),setTimeout(function(){Vs(e,n,o.shiftKey,r),rt(e)!=="ssp"&&Dt(e,!1)},0)):Vs(e,n,o.shiftKey,r))})}function Ui(e){var t=e.aLastSort,n=e.oClasses.sSortColumn,r=Kr(e),a=e.oFeatures,o,s,u;if(a.bSort&&a.bSortClasses){for(o=0,s=t.length;o<s;o++)u=t[o].src,x(_t(e.aoData,"anCells",u)).removeClass(n+(o<2?o+1:3));for(o=0,s=r.length;o<s;o++)u=r[o].src,x(_t(e.aoData,"anCells",u)).addClass(n+(o<2?o+1:3))}e.aLastSort=r}function rh(e,t){var n=e.aoColumns[t],r=K.ext.order[n.sSortDataType],a;r&&(a=r.call(e.oInstance,e,t,ja(e,t)));for(var o,s,u=K.ext.type.order[n.sType+"-pre"],f=0,d=e.aoData.length;f<d;f++)o=e.aoData[f],o._aSortData||(o._aSortData=[]),(!o._aSortData[t]||r)&&(s=r?a[f]:gt(e,f,t,"sort"),o._aSortData[t]=u?u(s):s)}function qa(e){if(!e._bLoadingState){var t={time:+new Date,start:e._iDisplayStart,length:e._iDisplayLength,order:x.extend(!0,[],e.aaSorting),search:qf(e.oPreviousSearch),columns:x.map(e.aoColumns,function(n,r){return{visible:n.bVisible,search:qf(e.aoPreSearchCols[r])}})};e.oSavedState=t,Pe(e,"aoStateSaveParams","stateSaveParams",[e,t]),e.oFeatures.bStateSave&&!e.bDestroying&&e.fnStateSaveCallback.call(e.oInstance,e,t)}}function ah(e,t,n){if(!e.oFeatures.bStateSave){n();return}var r=function(o){qs(e,o,n)},a=e.fnStateLoadCallback.call(e.oInstance,e,r);return a!==void 0&&qs(e,a,n),!0}function qs(e,t,n){var r,a,o=e.aoColumns;e._bLoadingState=!0;var s=e._bInitComplete?new K.Api(e):null;if(!t||!t.time){e._bLoadingState=!1,n();return}var u=Pe(e,"aoStateLoadParams","stateLoadParams",[e,t]);if(x.inArray(!1,u)!==-1){e._bLoadingState=!1,n();return}var f=e.iStateDuration;if(f>0&&t.time<+new Date-f*1e3){e._bLoadingState=!1,n();return}if(t.columns&&o.length!==t.columns.length){e._bLoadingState=!1,n();return}if(e.oLoadedState=x.extend(!0,{},t),t.length!==void 0&&(s?s.page.len(t.length):e._iDisplayLength=t.length),t.start!==void 0&&(s===null?(e._iDisplayStart=t.start,e.iInitDisplayStart=t.start):bo(e,t.start/e._iDisplayLength)),t.order!==void 0&&(e.aaSorting=[],x.each(t.order,function(h,b){e.aaSorting.push(b[0]>=o.length?[0,b[1]]:b)})),t.search!==void 0&&x.extend(e.oPreviousSearch,Uf(t.search)),t.columns){for(r=0,a=t.columns.length;r<a;r++){var d=t.columns[r];d.visible!==void 0&&(s?s.column(r).visible(d.visible,!1):o[r].bVisible=d.visible),d.search!==void 0&&x.extend(e.aoPreSearchCols[r],Uf(d.search))}s&&s.columns.adjust()}e._bLoadingState=!1,Pe(e,"aoStateLoaded","stateLoaded",[e,t]),n()}function Xi(e){var t=K.settings,n=x.inArray(e,_t(t,"nTable"));return n!==-1?t[n]:null}function Pt(e,t,n,r){if(n="DataTables warning: "+(e?"table id="+e.sTableId+" - ":"")+n,r&&(n+=". For more information about this error, please see https://datatables.net/tn/"+r),t)window.console&&console.log&&console.log(n);else{var a=K.ext,o=a.sErrMode||a.errMode;if(e&&Pe(e,null,"error",[e,r,n]),o=="alert")alert(n);else{if(o=="throw")throw new Error(n);typeof o=="function"&&o(e,r,n)}}}function Wt(e,t,n,r){if(Array.isArray(n)){x.each(n,function(a,o){Array.isArray(o)?Wt(e,t,o[0],o[1]):Wt(e,t,o)});return}r===void 0&&(r=n),t[n]!==void 0&&(e[r]=t[n])}function Us(e,t,n){var r;for(var a in t)t.hasOwnProperty(a)&&(r=t[a],x.isPlainObject(r)?(x.isPlainObject(e[a])||(e[a]={}),x.extend(!0,e[a],r)):n&&a!=="data"&&a!=="aaData"&&Array.isArray(r)?e[a]=r.slice():e[a]=r);return e}function Fl(e,t,n){x(e).on("click.DT",t,function(r){x(e).trigger("blur"),n(r)}).on("keypress.DT",t,function(r){r.which===13&&(r.preventDefault(),n(r))}).on("selectstart.DT",function(){return!1})}function st(e,t,n,r){n&&e[t].push({fn:n,sName:r})}function Pe(e,t,n,r){var a=[];if(t&&(a=x.map(e[t].slice().reverse(),function(u,f){return u.fn.apply(e.oInstance,r)})),n!==null){var o=x.Event(n+".dt"),s=x(e.nTable);s.trigger(o,r),s.parents("body").length===0&&x("body").trigger(o,r),a.push(o.result)}return a}function Hl(e){var t=e._iDisplayStart,n=e.fnDisplayEnd(),r=e._iDisplayLength;t>=n&&(t=n-r),t-=t%r,(r===-1||t<0)&&(t=0),e._iDisplayStart=t}function Ml(e,t){var n=e.renderer,r=K.ext.renderer[t];return x.isPlainObject(n)&&n[t]?r[n[t]]||r._:typeof n=="string"&&r[n]||r._}function rt(e){return e.oFeatures.bServerSide?"ssp":e.ajax||e.sAjaxSource?"ajax":"dom"}var ih=[],Je=Array.prototype,D0=function(e){var t,n,r=K.settings,a=x.map(r,function(o,s){return o.nTable});if(e){if(e.nTable&&e.oApi)return[e];if(e.nodeName&&e.nodeName.toLowerCase()==="table")return t=x.inArray(e,a),t!==-1?[r[t]]:null;if(e&&typeof e.settings=="function")return e.settings().toArray();typeof e=="string"?n=x(e):e instanceof x&&(n=e)}else return[];if(n)return n.map(function(o){return t=x.inArray(this,a),t!==-1?r[t]:null}).toArray()};Ee=function(e,t){if(!(this instanceof Ee))return new Ee(e,t);var n=[],r=function(s){var u=D0(s);u&&n.push.apply(n,u)};if(Array.isArray(e))for(var a=0,o=e.length;a<o;a++)r(e[a]);else r(e);this.context=fo(n),t&&x.merge(this,t),this.selector={rows:null,cols:null,opts:null},Ee.extend(this,this,ih)};K.Api=Ee;x.extend(Ee.prototype,{any:function(){return this.count()!==0},concat:Je.concat,context:[],count:function(){return this.flatten().length},each:function(e){for(var t=0,n=this.length;t<n;t++)e.call(this,this[t],t,this);return this},eq:function(e){var t=this.context;return t.length>e?new Ee(t[e],this[e]):null},filter:function(e){var t=[];if(Je.filter)t=Je.filter.call(this,e,this);else for(var n=0,r=this.length;n<r;n++)e.call(this,this[n],n,this)&&t.push(this[n]);return new Ee(this.context,t)},flatten:function(){var e=[];return new Ee(this.context,e.concat.apply(e,this.toArray()))},join:Je.join,indexOf:Je.indexOf||function(e,t){for(var n=t||0,r=this.length;n<r;n++)if(this[n]===e)return n;return-1},iterator:function(e,t,n,r){var a=[],o,s,u,f,d,h=this.context,b,_,y,g=this.selector;for(typeof e=="string"&&(r=n,n=t,t=e,e=!1),s=0,u=h.length;s<u;s++){var S=new Ee(h[s]);if(t==="table")o=n.call(S,h[s],s),o!==void 0&&a.push(o);else if(t==="columns"||t==="rows")o=n.call(S,h[s],this[s],s),o!==void 0&&a.push(o);else if(t==="column"||t==="column-rows"||t==="row"||t==="cell")for(_=this[s],t==="column-rows"&&(b=go(h[s],g.opts)),f=0,d=_.length;f<d;f++)y=_[f],t==="cell"?o=n.call(S,h[s],y.row,y.column,s,f):o=n.call(S,h[s],y,s,f,b),o!==void 0&&a.push(o)}if(a.length||r){var L=new Ee(h,e?a.concat.apply([],a):a),O=L.selector;return O.rows=g.rows,O.cols=g.cols,O.opts=g.opts,L}return this},lastIndexOf:Je.lastIndexOf||function(e,t){return this.indexOf.apply(this.toArray.reverse(),arguments)},length:0,map:function(e){var t=[];if(Je.map)t=Je.map.call(this,e,this);else for(var n=0,r=this.length;n<r;n++)t.push(e.call(this,this[n],n));return new Ee(this.context,t)},pluck:function(e){var t=K.util.get(e);return this.map(function(n){return t(n)})},pop:Je.pop,push:Je.push,reduce:Je.reduce||function(e,t){return Vf(this,e,t,0,this.length,1)},reduceRight:Je.reduceRight||function(e,t){return Vf(this,e,t,this.length-1,-1,-1)},reverse:Je.reverse,selector:null,shift:Je.shift,slice:function(){return new Ee(this.context,this)},sort:Je.sort,splice:Je.splice,toArray:function(){return Je.slice.call(this)},to$:function(){return x(this)},toJQuery:function(){return x(this)},unique:function(){return new Ee(this.context,fo(this))},unshift:Je.unshift});Ee.extend=function(e,t,n){if(!(!n.length||!t||!(t instanceof Ee)&&!t.__dt_wrapper)){var r,a,o,s=function(u,f,d){return function(){var h=f.apply(u,arguments);return Ee.extend(h,h,d.methodExt),h}};for(r=0,a=n.length;r<a;r++)o=n[r],t[o.name]=o.type==="function"?s(e,o.val,o):o.type==="object"?{}:o.val,t[o.name].__dt_wrapper=!0,Ee.extend(e,t[o.name],o.propExt)}};Ee.register=de=function(e,t){if(Array.isArray(e)){for(var n=0,r=e.length;n<r;n++)Ee.register(e[n],t);return}var a,o,s=e.split("."),u=ih,f,d,h=function(_,y){for(var g=0,S=_.length;g<S;g++)if(_[g].name===y)return _[g];return null};for(a=0,o=s.length;a<o;a++){d=s[a].indexOf("()")!==-1,f=d?s[a].replace("()",""):s[a];var b=h(u,f);b||(b={name:f,val:{},methodExt:[],propExt:[],type:"object"},u.push(b)),a===o-1?(b.val=t,b.type=typeof t=="function"?"function":x.isPlainObject(t)?"object":"other"):u=d?b.methodExt:b.propExt}};Ee.registerPlural=Me=function(e,t,n){Ee.register(e,n),Ee.register(t,function(){var r=n.apply(this,arguments);return r===this?this:r instanceof Ee?r.length?Array.isArray(r[0])?new Ee(r.context,r[0]):r[0]:void 0:r})};var oh=function(e,t){if(Array.isArray(e))return x.map(e,function(r){return oh(r,t)});if(typeof e=="number")return[t[e]];var n=x.map(t,function(r,a){return r.nTable});return x(n).filter(e).map(function(r){var a=x.inArray(this,n);return t[a]}).toArray()};de("tables()",function(e){return e!=null?new Ee(oh(e,this.context)):this});de("table()",function(e){var t=this.tables(e),n=t.context;return n.length?new Ee(n[0]):t});Me("tables().nodes()","table().node()",function(){return this.iterator("table",function(e){return e.nTable},1)});Me("tables().body()","table().body()",function(){return this.iterator("table",function(e){return e.nTBody},1)});Me("tables().header()","table().header()",function(){return this.iterator("table",function(e){return e.nTHead},1)});Me("tables().footer()","table().footer()",function(){return this.iterator("table",function(e){return e.nTFoot},1)});Me("tables().containers()","table().container()",function(){return this.iterator("table",function(e){return e.nTableWrapper},1)});de("draw()",function(e){return this.iterator("table",function(t){e==="page"?Mn(t):(typeof e=="string"&&(e=e!=="full-hold"),nr(t,e===!1))})});de("page()",function(e){return e===void 0?this.page.info().page:this.iterator("table",function(t){bo(t,e)})});de("page.info()",function(e){if(this.context.length!==0){var t=this.context[0],n=t._iDisplayStart,r=t.oFeatures.bPaginate?t._iDisplayLength:-1,a=t.fnRecordsDisplay(),o=r===-1;return{page:o?0:Math.floor(n/r),pages:o?1:Math.ceil(a/r),start:n,end:t.fnDisplayEnd(),length:r,recordsTotal:t.fnRecordsTotal(),recordsDisplay:a,serverSide:rt(t)==="ssp"}}});de("page.len()",function(e){return e===void 0?this.context.length!==0?this.context[0]._iDisplayLength:void 0:this.iterator("table",function(t){Rl(t,e)})});var sh=function(e,t,n){if(n){var r=new Ee(e);r.one("draw",function(){n(r.ajax.json())})}if(rt(e)=="ssp")nr(e,t);else{Dt(e,!0);var a=e.jqXHR;a&&a.readyState!==4&&a.abort(),mo(e,[],function(o){po(e);for(var s=Wa(e,o),u=0,f=s.length;u<f;u++)Hn(e,s[u]);nr(e,t),Dt(e,!1)})}};de("ajax.json()",function(){var e=this.context;if(e.length>0)return e[0].json});de("ajax.params()",function(){var e=this.context;if(e.length>0)return e[0].oAjaxData});de("ajax.reload()",function(e,t){return this.iterator("table",function(n){sh(n,t===!1,e)})});de("ajax.url()",function(e){var t=this.context;return e===void 0?t.length===0?void 0:(t=t[0],t.ajax?x.isPlainObject(t.ajax)?t.ajax.url:t.ajax:t.sAjaxSource):this.iterator("table",function(n){x.isPlainObject(n.ajax)?n.ajax.url=e:n.ajax=e})});de("ajax.url().load()",function(e,t){return this.iterator("table",function(n){sh(n,t===!1,e)})});var jl=function(e,t,n,r,a){var o=[],s,u,f,d,h,b,_=typeof t;for((!t||_==="string"||_==="function"||t.length===void 0)&&(t=[t]),f=0,d=t.length;f<d;f++)for(u=t[f]&&t[f].split&&!t[f].match(/[\[\(:]/)?t[f].split(","):[t[f]],h=0,b=u.length;h<b;h++)s=n(typeof u[h]=="string"?u[h].trim():u[h]),s&&s.length&&(o=o.concat(s));var y=Xe.selector[e];if(y.length)for(f=0,d=y.length;f<d;f++)o=y[f](r,a,o);return fo(o)},Bl=function(e){return e||(e={}),e.filter&&e.search===void 0&&(e.search=e.filter),x.extend({search:"none",order:"current",page:"all"},e)},Wl=function(e){for(var t=0,n=e.length;t<n;t++)if(e[t].length>0)return e[0]=e[t],e[0].length=1,e.length=1,e.context=[e.context[t]],e;return e.length=0,e},go=function(e,t){var n,r,a,o=[],s=e.aiDisplay,u=e.aiDisplayMaster,f=t.search,d=t.order,h=t.page;if(rt(e)=="ssp")return f==="removed"?[]:Nr(0,u.length);if(h=="current")for(n=e._iDisplayStart,r=e.fnDisplayEnd();n<r;n++)o.push(s[n]);else if(d=="current"||d=="applied"){if(f=="none")o=u.slice();else if(f=="applied")o=s.slice();else if(f=="removed"){for(var b={},n=0,r=s.length;n<r;n++)b[s[n]]=null;o=x.map(u,function(_){return b.hasOwnProperty(_)?null:_})}}else if(d=="index"||d=="original")for(n=0,r=e.aoData.length;n<r;n++)f=="none"?o.push(n):(a=x.inArray(n,s),(a===-1&&f=="removed"||a>=0&&f=="applied")&&o.push(n));return o},S0=function(e,t,n){var r,a=function(o){var s=xd(o),u=e.aoData;if(s!==null&&!n)return[s];if(r||(r=go(e,n)),s!==null&&x.inArray(s,r)!==-1)return[s];if(o==null||o==="")return r;if(typeof o=="function")return x.map(r,function(y){var g=u[y];return o(y,g._aData,g.nTr)?y:null});if(o.nodeName){var f=o._DT_RowIndex,d=o._DT_CellIndex;if(f!==void 0)return u[f]&&u[f].nTr===o?[f]:[];if(d)return u[d.row]&&u[d.row].nTr===o.parentNode?[d.row]:[];var h=x(o).closest("*[data-dt-row]");return h.length?[h.data("dt-row")]:[]}if(typeof o=="string"&&o.charAt(0)==="#"){var b=e.aIds[o.replace(/^#/,"")];if(b!==void 0)return[b.idx]}var _=Ed($a(e.aoData,r,"nTr"));return x(_).filter(o).map(function(){return this._DT_RowIndex}).toArray()};return jl("row",t,a,e,n)};de("rows()",function(e,t){e===void 0?e="":x.isPlainObject(e)&&(t=e,e=""),t=Bl(t);var n=this.iterator("table",function(r){return S0(r,e,t)},1);return n.selector.rows=e,n.selector.opts=t,n});de("rows().nodes()",function(){return this.iterator("row",function(e,t){return e.aoData[t].nTr||void 0},1)});de("rows().data()",function(){return this.iterator(!0,"rows",function(e,t){return $a(e.aoData,t,"_aData")},1)});Me("rows().cache()","row().cache()",function(e){return this.iterator("row",function(t,n){var r=t.aoData[n];return e==="search"?r._aFilterData:r._aSortData},1)});Me("rows().invalidate()","row().invalidate()",function(e){return this.iterator("row",function(t,n){Ba(t,n,e)})});Me("rows().indexes()","row().index()",function(){return this.iterator("row",function(e,t){return t},1)});Me("rows().ids()","row().id()",function(e){for(var t=[],n=this.context,r=0,a=n.length;r<a;r++)for(var o=0,s=this[r].length;o<s;o++){var u=n[r].rowIdFn(n[r].aoData[this[r][o]]._aData);t.push((e===!0?"#":"")+u)}return new Ee(n,t)});Me("rows().remove()","row().remove()",function(){var e=this;return this.iterator("row",function(t,n,r){var a=t.aoData,o=a[n],s,u,f,d,h,b;for(a.splice(n,1),s=0,u=a.length;s<u;s++)if(h=a[s],b=h.anCells,h.nTr!==null&&(h.nTr._DT_RowIndex=s),b!==null)for(f=0,d=b.length;f<d;f++)b[f]._DT_CellIndex.row=s;Oi(t.aiDisplayMaster,n),Oi(t.aiDisplay,n),Oi(e[r],n,!1),t._iRecordsDisplay>0&&t._iRecordsDisplay--,Hl(t);var _=t.rowIdFn(o._aData);_!==void 0&&delete t.aIds[_]}),this.iterator("table",function(t){for(var n=0,r=t.aoData.length;n<r;n++)t.aoData[n].idx=n}),this});de("rows.add()",function(e){var t=this.iterator("table",function(r){var a,o,s,u=[];for(o=0,s=e.length;o<s;o++)a=e[o],a.nodeName&&a.nodeName.toUpperCase()==="TR"?u.push(ho(r,a)[0]):u.push(Hn(r,a));return u},1),n=this.rows(-1);return n.pop(),x.merge(n,t),n});de("row()",function(e,t){return Wl(this.rows(e,t))});de("row().data()",function(e){var t=this.context;if(e===void 0)return t.length&&this.length?t[0].aoData[this[0]]._aData:void 0;var n=t[0].aoData[this[0]];return n._aData=e,Array.isArray(e)&&n.nTr&&n.nTr.id&&On(t[0].rowId)(e,n.nTr.id),Ba(t[0],this[0],"data"),this});de("row().node()",function(){var e=this.context;return e.length&&this.length&&e[0].aoData[this[0]].nTr||null});de("row.add()",function(e){e instanceof x&&e.length&&(e=e[0]);var t=this.iterator("table",function(n){return e.nodeName&&e.nodeName.toUpperCase()==="TR"?ho(n,e)[0]:Hn(n,e)});return this.row(t[0])});x(document).on("plugin-init.dt",function(e,t){var n=new Ee(t),r="on-plugin-init",a="stateSaveParams."+r,o="destroy. "+r;n.on(a,function(u,f,d){for(var h=f.rowIdFn,b=f.aoData,_=[],y=0;y<b.length;y++)b[y]._detailsShow&&_.push("#"+h(b[y]._aData));d.childRows=_}),n.on(o,function(){n.off(a+" "+o)});var s=n.state.loaded();s&&s.childRows&&n.rows(x.map(s.childRows,function(u){return u.replace(/:/g,"\\:")})).every(function(){Pe(t,null,"requestChild",[this])})});var x0=function(e,t,n,r){var a=[],o=function(s,u){if(Array.isArray(s)||s instanceof x){for(var f=0,d=s.length;f<d;f++)o(s[f],u);return}if(s.nodeName&&s.nodeName.toLowerCase()==="tr")a.push(s);else{var h=x("<tr><td></td></tr>").addClass(u);x("td",h).addClass(u).html(s)[0].colSpan=Xr(e),a.push(h[0])}};o(n,r),t._details&&t._details.detach(),t._details=x(a),t._detailsShow&&t._details.insertAfter(t.nTr)},lh=K.util.throttle(function(e){qa(e[0])},500),Vl=function(e,t){var n=e.context;if(n.length){var r=n[0].aoData[t!==void 0?t:e[0]];r&&r._details&&(r._details.remove(),r._detailsShow=void 0,r._details=void 0,x(r.nTr).removeClass("dt-hasChild"),lh(n))}},uh=function(e,t){var n=e.context;if(n.length&&e.length){var r=n[0].aoData[e[0]];r._details&&(r._detailsShow=t,t?(r._details.insertAfter(r.nTr),x(r.nTr).addClass("dt-hasChild")):(r._details.detach(),x(r.nTr).removeClass("dt-hasChild")),Pe(n[0],null,"childRow",[t,e.row(e[0])]),A0(n[0]),lh(n))}},A0=function(e){var t=new Ee(e),n=".dt.DT_details",r="draw"+n,a="column-sizing"+n,o="destroy"+n,s=e.aoData;t.off(r+" "+a+" "+o),_t(s,"_details").length>0&&(t.on(r,function(u,f){e===f&&t.rows({page:"current"}).eq(0).each(function(d){var h=s[d];h._detailsShow&&h._details.insertAfter(h.nTr)})}),t.on(a,function(u,f,d,h){if(e===f)for(var b,_=Xr(f),y=0,g=s.length;y<g;y++)b=s[y],b._details&&b._details.each(function(){var S=x(this).children("td");S.length==1&&S.attr("colspan",_)})}),t.on(o,function(u,f){if(e===f)for(var d=0,h=s.length;d<h;d++)s[d]._details&&Vl(t,d)}))},E0="",Ua=E0+"row().child",yo=Ua+"()";de(yo,function(e,t){var n=this.context;return e===void 0?n.length&&this.length?n[0].aoData[this[0]]._details:void 0:(e===!0?this.child.show():e===!1?Vl(this):n.length&&this.length&&x0(n[0],n[0].aoData[this[0]],e,t),this)});de([Ua+".show()",yo+".show()"],function(e){return uh(this,!0),this});de([Ua+".hide()",yo+".hide()"],function(){return uh(this,!1),this});de([Ua+".remove()",yo+".remove()"],function(){return Vl(this),this});de(Ua+".isShown()",function(){var e=this.context;return e.length&&this.length&&e[0].aoData[this[0]]._detailsShow||!1});var N0=/^([^:]+):(name|visIdx|visible)$/,fh=function(e,t,n,r,a){for(var o=[],s=0,u=a.length;s<u;s++)o.push(gt(e,a[s],t));return o},L0=function(e,t,n){var r=e.aoColumns,a=_t(r,"sName"),o=_t(r,"nTh"),s=function(u){var f=xd(u);if(u==="")return Nr(r.length);if(f!==null)return[f>=0?f:r.length+f];if(typeof u=="function"){var d=go(e,n);return x.map(r,function(S,L){return u(L,fh(e,L,0,0,d),o[L])?L:null})}var h=typeof u=="string"?u.match(N0):"";if(h)switch(h[2]){case"visIdx":case"visible":var b=parseInt(h[1],10);if(b<0){var _=x.map(r,function(S,L){return S.bVisible?L:null});return[_[_.length+b]]}return[Ma(e,b)];case"name":return x.map(a,function(S,L){return S===h[1]?L:null});default:return[]}if(u.nodeName&&u._DT_CellIndex)return[u._DT_CellIndex.column];var y=x(o).filter(u).map(function(){return x.inArray(this,o)}).toArray();if(y.length||!u.nodeName)return y;var g=x(u).closest("*[data-dt-column]");return g.length?[g.data("dt-column")]:[]};return jl("column",t,s,e,n)},O0=function(e,t,n){var r=e.aoColumns,a=r[t],o=e.aoData,s,u,f,d;if(n===void 0)return a.bVisible;if(a.bVisible!==n){if(n){var h=x.inArray(!0,_t(r,"bVisible"),t+1);for(u=0,f=o.length;u<f;u++)d=o[u].nTr,s=o[u].anCells,d&&d.insertBefore(s[t],s[h]||null)}else x(_t(e.aoData,"anCells",t)).detach();a.bVisible=n}};de("columns()",function(e,t){e===void 0?e="":x.isPlainObject(e)&&(t=e,e=""),t=Bl(t);var n=this.iterator("table",function(r){return L0(r,e,t)},1);return n.selector.cols=e,n.selector.opts=t,n});Me("columns().header()","column().header()",function(e,t){return this.iterator("column",function(n,r){return n.aoColumns[r].nTh},1)});Me("columns().footer()","column().footer()",function(e,t){return this.iterator("column",function(n,r){return n.aoColumns[r].nTf},1)});Me("columns().data()","column().data()",function(){return this.iterator("column-rows",fh,1)});Me("columns().dataSrc()","column().dataSrc()",function(){return this.iterator("column",function(e,t){return e.aoColumns[t].mData},1)});Me("columns().cache()","column().cache()",function(e){return this.iterator("column-rows",function(t,n,r,a,o){return $a(t.aoData,o,e==="search"?"_aFilterData":"_aSortData",n)},1)});Me("columns().nodes()","column().nodes()",function(){return this.iterator("column-rows",function(e,t,n,r,a){return $a(e.aoData,a,"anCells",t)},1)});Me("columns().visible()","column().visible()",function(e,t){var n=this,r=this.iterator("column",function(a,o){if(e===void 0)return a.aoColumns[o].bVisible;O0(a,o,e)});return e!==void 0&&this.iterator("table",function(a){Ta(a,a.aoHeader),Ta(a,a.aoFooter),a.aiDisplay.length||x(a.nTBody).find("td[colspan]").attr("colspan",Xr(a)),qa(a),n.iterator("column",function(o,s){Pe(o,null,"column-visibility",[o,s,e,t])}),(t===void 0||t)&&n.columns.adjust()}),r});Me("columns().indexes()","column().index()",function(e){return this.iterator("column",function(t,n){return e==="visible"?ja(t,n):n},1)});de("columns.adjust()",function(){return this.iterator("table",function(e){Ha(e)},1)});de("column.index()",function(e,t){if(this.context.length!==0){var n=this.context[0];if(e==="fromVisible"||e==="toData")return Ma(n,t);if(e==="fromData"||e==="toVisible")return ja(n,t)}});de("column()",function(e,t){return Wl(this.columns(e,t))});var I0=function(e,t,n){var r=e.aoData,a=go(e,n),o=Ed($a(r,a,"anCells")),s=x(Nd([],o)),u,f=e.aoColumns.length,d,h,b,_,y,g,S=function(L){var O=typeof L=="function";if(L==null||O){for(d=[],h=0,b=a.length;h<b;h++)for(u=a[h],_=0;_<f;_++)y={row:u,column:_},O?(g=r[u],L(y,gt(e,u,_),g.anCells?g.anCells[_]:null)&&d.push(y)):d.push(y);return d}if(x.isPlainObject(L))return L.column!==void 0&&L.row!==void 0&&x.inArray(L.row,a)!==-1?[L]:[];var C=s.filter(L).map(function(Y,W){return{row:W._DT_CellIndex.row,column:W._DT_CellIndex.column}}).toArray();return C.length||!L.nodeName?C:(g=x(L).closest("*[data-dt-row]"),g.length?[{row:g.data("dt-row"),column:g.data("dt-column")}]:[])};return jl("cell",t,S,e,n)};de("cells()",function(e,t,n){if(x.isPlainObject(e)&&(e.row===void 0?(n=e,e=null):(n=t,t=null)),x.isPlainObject(t)&&(n=t,t=null),t==null)return this.iterator("table",function(_){return I0(_,e,Bl(n))});var r=n?{page:n.page,order:n.order,search:n.search}:{},a=this.columns(t,r),o=this.rows(e,r),s,u,f,d,h=this.iterator("table",function(_,y){var g=[];for(s=0,u=o[y].length;s<u;s++)for(f=0,d=a[y].length;f<d;f++)g.push({row:o[y][s],column:a[y][f]});return g},1),b=n&&n.selected?this.cells(h,n):h;return x.extend(b.selector,{cols:t,rows:e,opts:n}),b});Me("cells().nodes()","cell().node()",function(){return this.iterator("cell",function(e,t,n){var r=e.aoData[t];return r&&r.anCells?r.anCells[n]:void 0},1)});de("cells().data()",function(){return this.iterator("cell",function(e,t,n){return gt(e,t,n)},1)});Me("cells().cache()","cell().cache()",function(e){return e=e==="search"?"_aFilterData":"_aSortData",this.iterator("cell",function(t,n,r){return t.aoData[n][e][r]},1)});Me("cells().render()","cell().render()",function(e){return this.iterator("cell",function(t,n,r){return gt(t,n,r,e)},1)});Me("cells().indexes()","cell().index()",function(){return this.iterator("cell",function(e,t,n){return{row:t,column:n,columnVisible:ja(e,n)}},1)});Me("cells().invalidate()","cell().invalidate()",function(e){return this.iterator("cell",function(t,n,r){Ba(t,n,e,r)})});de("cell()",function(e,t,n){return Wl(this.cells(e,t,n))});de("cell().data()",function(e){var t=this.context,n=this[0];return e===void 0?t.length&&n.length?gt(t[0],n[0].row,n[0].column):void 0:(Pd(t[0],n[0].row,n[0].column,e),Ba(t[0],n[0].row,"data",n[0].column),this)});de("order()",function(e,t){var n=this.context;return e===void 0?n.length!==0?n[0].aaSorting:void 0:(typeof e=="number"?e=[[e,t]]:e.length&&!Array.isArray(e[0])&&(e=Array.prototype.slice.call(arguments)),this.iterator("table",function(r){r.aaSorting=e.slice()}))});de("order.listener()",function(e,t,n){return this.iterator("table",function(r){$l(r,e,t,n)})});de("order.fixed()",function(e){if(!e){var t=this.context,n=t.length?t[0].aaSortingFixed:void 0;return Array.isArray(n)?{pre:n}:n}return this.iterator("table",function(r){r.aaSortingFixed=x.extend(!0,{},e)})});de(["columns().order()","column().order()"],function(e){var t=this;return this.iterator("table",function(n,r){var a=[];x.each(t[r],function(o,s){a.push([s,e])}),n.aaSorting=a})});de("search()",function(e,t,n,r){var a=this.context;return e===void 0?a.length!==0?a[0].oPreviousSearch.sSearch:void 0:this.iterator("table",function(o){o.oFeatures.bFilter&&Va(o,x.extend({},o.oPreviousSearch,{sSearch:e+"",bRegex:t===null?!1:t,bSmart:n===null?!0:n,bCaseInsensitive:r===null?!0:r}),1)})});Me("columns().search()","column().search()",function(e,t,n,r){return this.iterator("column",function(a,o){var s=a.aoPreSearchCols;if(e===void 0)return s[o].sSearch;a.oFeatures.bFilter&&(x.extend(s[o],{sSearch:e+"",bRegex:t===null?!1:t,bSmart:n===null?!0:n,bCaseInsensitive:r===null?!0:r}),Va(a,a.oPreviousSearch,1))})});de("state()",function(){return this.context.length?this.context[0].oSavedState:null});de("state.clear()",function(){return this.iterator("table",function(e){e.fnStateSaveCallback.call(e.oInstance,e,{})})});de("state.loaded()",function(){return this.context.length?this.context[0].oLoadedState:null});de("state.save()",function(){return this.iterator("table",function(e){qa(e)})});K.use=function(e,t){t==="lib"||e.fn?x=e:t=="win"||e.document?(window=e,document=e.document):(t==="datetime"||e.type==="DateTime")&&(K.DateTime=e)};K.factory=function(e,t){var n=!1;return e&&e.document&&(window=e,document=e.document),t&&t.fn&&t.fn.jquery&&(x=t,n=!0),n};K.versionCheck=K.fnVersionCheck=function(e){for(var t=K.version.split("."),n=e.split("."),r,a,o=0,s=n.length;o<s;o++)if(r=parseInt(t[o],10)||0,a=parseInt(n[o],10)||0,r!==a)return r>a;return!0};K.isDataTable=K.fnIsDataTable=function(e){var t=x(e).get(0),n=!1;return e instanceof K.Api?!0:(x.each(K.settings,function(r,a){var o=a.nScrollHead?x("table",a.nScrollHead)[0]:null,s=a.nScrollFoot?x("table",a.nScrollFoot)[0]:null;(a.nTable===t||o===t||s===t)&&(n=!0)}),n)};K.tables=K.fnTables=function(e){var t=!1;x.isPlainObject(e)&&(t=e.api,e=e.visible);var n=x.map(K.settings,function(r){if(!e||e&&x(r.nTable).is(":visible"))return r.nTable});return t?new Ee(n):n};K.camelToHungarian=pn;de("$()",function(e,t){var n=this.rows(t).nodes(),r=x(n);return x([].concat(r.filter(e).toArray(),r.find(e).toArray()))});x.each(["on","one","off"],function(e,t){de(t+"()",function(){var n=Array.prototype.slice.call(arguments);n[0]=x.map(n[0].split(/\s/),function(a){return a.match(/\.dt\b/)?a:a+".dt"}).join(" ");var r=x(this.tables().nodes());return r[t].apply(r,n),this})});de("clear()",function(){return this.iterator("table",function(e){po(e)})});de("settings()",function(){return new Ee(this.context,this.context)});de("init()",function(){var e=this.context;return e.length?e[0].oInit:null});de("data()",function(){return this.iterator("table",function(e){return _t(e.aoData,"_aData")}).flatten()});de("destroy()",function(e){return e=e||!1,this.iterator("table",function(t){var n=t.oClasses,r=t.nTable,a=t.nTBody,o=t.nTHead,s=t.nTFoot,u=x(r),f=x(a),d=x(t.nTableWrapper),h=x.map(t.aoData,function(S){return S.nTr}),b;t.bDestroying=!0,Pe(t,"aoDestroyCallback","destroy",[t]),e||new Ee(t).columns().visible(!0),d.off(".DT").find(":not(tbody *)").off(".DT"),x(window).off(".DT-"+t.sInstance),r!=o.parentNode&&(u.children("thead").detach(),u.append(o)),s&&r!=s.parentNode&&(u.children("tfoot").detach(),u.append(s)),t.aaSorting=[],t.aaSortingFixed=[],Ui(t),x(h).removeClass(t.asStripeClasses.join(" ")),x("th, td",o).removeClass(n.sSortable+" "+n.sSortableAsc+" "+n.sSortableDesc+" "+n.sSortableNone),f.children().detach(),f.append(h);var _=t.nTableWrapper.parentNode,y=e?"remove":"detach";u[y](),d[y](),!e&&_&&(_.insertBefore(r,t.nTableReinsertBefore),u.css("width",t.sDestroyWidth).removeClass(n.sTable),b=t.asDestroyStripes.length,b&&f.children().each(function(S){x(this).addClass(t.asDestroyStripes[S%b])}));var g=x.inArray(t,K.settings);g!==-1&&K.settings.splice(g,1)})});x.each(["column","row","cell"],function(e,t){de(t+"s().every()",function(n){var r=this.selector.opts,a=this;return this.iterator(t,function(o,s,u,f,d){n.call(a[t](s,t==="cell"?u:r,t==="cell"?r:void 0),s,u,f,d)})})});de("i18n()",function(e,t,n){var r=this.context[0],a=Mr(e)(r.oLanguage);return a===void 0&&(a=t),n!==void 0&&x.isPlainObject(a)&&(a=a[n]!==void 0?a[n]:a._),typeof a=="string"?a.replace("%d",n):a});K.version="1.13.7";K.settings=[];K.models={};K.models.oSearch={bCaseInsensitive:!0,sSearch:"",bRegex:!1,bSmart:!0,return:!1};K.models.oRow={nTr:null,anCells:null,_aData:[],_aSortData:null,_aFilterData:null,_sFilterRow:null,_sRowStripe:"",src:null,idx:-1};K.models.oColumn={idx:null,aDataSort:null,asSorting:null,bSearchable:null,bSortable:null,bVisible:null,_sManualType:null,_bAttrSrc:!1,fnCreatedCell:null,fnGetData:null,fnSetData:null,mData:null,mRender:null,nTh:null,nTf:null,sClass:null,sContentPadding:null,sDefaultContent:null,sName:null,sSortDataType:"std",sSortingClass:null,sSortingClassJUI:null,sTitle:null,sType:null,sWidth:null,sWidthOrig:null};K.defaults={aaData:null,aaSorting:[[0,"asc"]],aaSortingFixed:[],ajax:null,aLengthMenu:[10,25,50,100],aoColumns:null,aoColumnDefs:null,aoSearchCols:[],asStripeClasses:null,bAutoWidth:!0,bDeferRender:!1,bDestroy:!1,bFilter:!0,bInfo:!0,bLengthChange:!0,bPaginate:!0,bProcessing:!1,bRetrieve:!1,bScrollCollapse:!1,bServerSide:!1,bSort:!0,bSortMulti:!0,bSortCellsTop:!1,bSortClasses:!0,bStateSave:!1,fnCreatedRow:null,fnDrawCallback:null,fnFooterCallback:null,fnFormatNumber:function(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,this.oLanguage.sThousands)},fnHeaderCallback:null,fnInfoCallback:null,fnInitComplete:null,fnPreDrawCallback:null,fnRowCallback:null,fnServerData:null,fnServerParams:null,fnStateLoadCallback:function(e){try{return JSON.parse((e.iStateDuration===-1?sessionStorage:localStorage).getItem("DataTables_"+e.sInstance+"_"+location.pathname))}catch{return{}}},fnStateLoadParams:null,fnStateLoaded:null,fnStateSaveCallback:function(e,t){try{(e.iStateDuration===-1?sessionStorage:localStorage).setItem("DataTables_"+e.sInstance+"_"+location.pathname,JSON.stringify(t))}catch{}},fnStateSaveParams:null,iStateDuration:7200,iDeferLoading:null,iDisplayLength:10,iDisplayStart:0,iTabIndex:0,oClasses:{},oLanguage:{oAria:{sSortAscending:": activate to sort column ascending",sSortDescending:": activate to sort column descending"},oPaginate:{sFirst:"First",sLast:"Last",sNext:"Next",sPrevious:"Previous"},sEmptyTable:"No data available in table",sInfo:"Showing _START_ to _END_ of _TOTAL_ entries",sInfoEmpty:"Showing 0 to 0 of 0 entries",sInfoFiltered:"(filtered from _MAX_ total entries)",sInfoPostFix:"",sDecimal:"",sThousands:",",sLengthMenu:"Show _MENU_ entries",sLoadingRecords:"Loading...",sProcessing:"",sSearch:"Search:",sSearchPlaceholder:"",sUrl:"",sZeroRecords:"No matching records found"},oSearch:x.extend({},K.models.oSearch),sAjaxDataProp:"data",sAjaxSource:null,sDom:"lfrtip",searchDelay:null,sPaginationType:"simple_numbers",sScrollX:"",sScrollXInner:"",sScrollY:"",sServerMethod:"GET",renderer:null,rowId:"DT_RowId"};Fa(K.defaults);K.defaults.column={aDataSort:null,iDataSort:-1,asSorting:["asc","desc"],bSearchable:!0,bSortable:!0,bVisible:!0,fnCreatedCell:null,mData:null,mRender:null,sCellType:"td",sClass:"",sContentPadding:"",sDefaultContent:null,sName:"",sSortDataType:"std",sTitle:null,sType:null,sWidth:null};Fa(K.defaults.column);K.models.oSettings={oFeatures:{bAutoWidth:null,bDeferRender:null,bFilter:null,bInfo:null,bLengthChange:null,bPaginate:null,bProcessing:null,bServerSide:null,bSort:null,bSortMulti:null,bSortClasses:null,bStateSave:null},oScroll:{bCollapse:null,iBarWidth:0,sX:null,sXInner:null,sY:null},oLanguage:{fnInfoCallback:null},oBrowser:{bScrollOversize:!1,bScrollbarLeft:!1,bBounding:!1,barWidth:0},ajax:null,aanFeatures:[],aoData:[],aiDisplay:[],aiDisplayMaster:[],aIds:{},aoColumns:[],aoHeader:[],aoFooter:[],oPreviousSearch:{},aoPreSearchCols:[],aaSorting:null,aaSortingFixed:[],asStripeClasses:null,asDestroyStripes:[],sDestroyWidth:0,aoRowCallback:[],aoHeaderCallback:[],aoFooterCallback:[],aoDrawCallback:[],aoRowCreatedCallback:[],aoPreDrawCallback:[],aoInitComplete:[],aoStateSaveParams:[],aoStateLoadParams:[],aoStateLoaded:[],sTableId:"",nTable:null,nTHead:null,nTFoot:null,nTBody:null,nTableWrapper:null,bDeferLoading:!1,bInitialised:!1,aoOpenRows:[],sDom:null,searchDelay:null,sPaginationType:"two_button",iStateDuration:0,aoStateSave:[],aoStateLoad:[],oSavedState:null,oLoadedState:null,sAjaxSource:null,sAjaxDataProp:null,jqXHR:null,json:void 0,oAjaxData:void 0,fnServerData:null,aoServerParams:[],sServerMethod:null,fnFormatNumber:null,aLengthMenu:null,iDraw:0,bDrawing:!1,iDrawError:-1,_iDisplayLength:10,_iDisplayStart:0,_iRecordsTotal:0,_iRecordsDisplay:0,oClasses:{},bFiltered:!1,bSorted:!1,bSortCellsTop:null,oInit:null,aoDestroyCallback:[],fnRecordsTotal:function(){return rt(this)=="ssp"?this._iRecordsTotal*1:this.aiDisplayMaster.length},fnRecordsDisplay:function(){return rt(this)=="ssp"?this._iRecordsDisplay*1:this.aiDisplay.length},fnDisplayEnd:function(){var e=this._iDisplayLength,t=this._iDisplayStart,n=t+e,r=this.aiDisplay.length,a=this.oFeatures,o=a.bPaginate;return a.bServerSide?o===!1||e===-1?t+r:Math.min(t+e,this._iRecordsDisplay):!o||n>r||e===-1?r:n},oInstance:null,sInstance:null,iTabIndex:0,nScrollHead:null,nScrollFoot:null,aLastSort:[],oPlugins:{},rowIdFn:null,rowId:null};K.ext=Xe={buttons:{},classes:{},builder:"-source-",errMode:"alert",feature:[],search:[],selector:{cell:[],column:[],row:[]},internal:{},legacy:{ajax:null},pager:{},renderer:{pageButton:{},header:{}},order:{},type:{detect:[],search:{},order:{}},_unique:0,fnVersionCheck:K.fnVersionCheck,iApiIndex:0,oJUIClasses:{},sVersion:K.version};x.extend(Xe,{afnFiltering:Xe.search,aTypes:Xe.type.detect,ofnSearch:Xe.type.search,oSort:Xe.type.order,afnSortData:Xe.order,aoFeatures:Xe.feature,oApi:Xe.internal,oStdClasses:Xe.classes,oPagination:Xe.pager});x.extend(K.ext.classes,{sTable:"dataTable",sNoFooter:"no-footer",sPageButton:"paginate_button",sPageButtonActive:"current",sPageButtonDisabled:"disabled",sStripeOdd:"odd",sStripeEven:"even",sRowEmpty:"dataTables_empty",sWrapper:"dataTables_wrapper",sFilter:"dataTables_filter",sInfo:"dataTables_info",sPaging:"dataTables_paginate paging_",sLength:"dataTables_length",sProcessing:"dataTables_processing",sSortAsc:"sorting_asc",sSortDesc:"sorting_desc",sSortable:"sorting",sSortableAsc:"sorting_desc_disabled",sSortableDesc:"sorting_asc_disabled",sSortableNone:"sorting_disabled",sSortColumn:"sorting_",sFilterInput:"",sLengthSelect:"",sScrollWrapper:"dataTables_scroll",sScrollHead:"dataTables_scrollHead",sScrollHeadInner:"dataTables_scrollHeadInner",sScrollBody:"dataTables_scrollBody",sScrollFoot:"dataTables_scrollFoot",sScrollFootInner:"dataTables_scrollFootInner",sHeaderTH:"",sFooterTH:"",sSortJUIAsc:"",sSortJUIDesc:"",sSortJUI:"",sSortJUIAscAllowed:"",sSortJUIDescAllowed:"",sSortJUIWrapper:"",sSortIcon:"",sJUIHeader:"",sJUIFooter:""});var ch=K.ext.pager;function ca(e,t){var n=[],r=ch.numbers_length,a=Math.floor(r/2);return t<=r?n=Nr(0,t):e<=a?(n=Nr(0,r-2),n.push("ellipsis"),n.push(t-1)):e>=t-1-a?(n=Nr(t-(r-2),t),n.splice(0,0,"ellipsis"),n.splice(0,0,0)):(n=Nr(e-a+2,e+a-1),n.push("ellipsis"),n.push(t-1),n.splice(0,0,"ellipsis"),n.splice(0,0,0)),n.DT_el="span",n}x.extend(ch,{simple:function(e,t){return["previous","next"]},full:function(e,t){return["first","previous","next","last"]},numbers:function(e,t){return[ca(e,t)]},simple_numbers:function(e,t){return["previous",ca(e,t),"next"]},full_numbers:function(e,t){return["first","previous",ca(e,t),"next","last"]},first_last_numbers:function(e,t){return["first",ca(e,t),"last"]},_numbers:ca,numbers_length:7});x.extend(!0,K.ext.renderer,{pageButton:{_:function(e,t,n,r,a,o){var s=e.oClasses,u=e.oLanguage.oPaginate,f=e.oLanguage.oAria.paginate||{},d,h,b=function(y,g){var S,L,O,C,Y=s.sPageButtonDisabled,W=function(ee){bo(e,ee.data.action,!0)};for(S=0,L=g.length;S<L;S++)if(C=g[S],Array.isArray(C)){var B=x("<"+(C.DT_el||"div")+"/>").appendTo(y);b(B,C)}else{var Q=!1;switch(d=null,h=C,C){case"ellipsis":y.append('<span class="ellipsis">&#x2026;</span>');break;case"first":d=u.sFirst,a===0&&(Q=!0);break;case"previous":d=u.sPrevious,a===0&&(Q=!0);break;case"next":d=u.sNext,(o===0||a===o-1)&&(Q=!0);break;case"last":d=u.sLast,(o===0||a===o-1)&&(Q=!0);break;default:d=e.fnFormatNumber(C+1),h=a===C?s.sPageButtonActive:"";break}if(d!==null){var v=e.oInit.pagingTag||"a";Q&&(h+=" "+Y),O=x("<"+v+">",{class:s.sPageButton+" "+h,"aria-controls":e.sTableId,"aria-disabled":Q?"true":null,"aria-label":f[C],role:"link","aria-current":h===s.sPageButtonActive?"page":null,"data-dt-idx":C,tabindex:Q?-1:e.iTabIndex,id:n===0&&typeof C=="string"?e.sTableId+"_"+C:null}).html(d).appendTo(y),Fl(O,{action:C},W)}}},_;try{_=x(t).find(document.activeElement).data("dt-idx")}catch{}b(x(t).empty(),r),_!==void 0&&x(t).find("[data-dt-idx="+_+"]").trigger("focus")}}});x.extend(K.ext.type.detect,[function(e,t){var n=t.oLanguage.sDecimal;return Ms(e,n)?"num"+n:null},function(e,t){if(e&&!(e instanceof Date)&&!p0.test(e))return null;var n=Date.parse(e);return n!==null&&!isNaN(n)||nn(e)?"date":null},function(e,t){var n=t.oLanguage.sDecimal;return Ms(e,n,!0)?"num-fmt"+n:null},function(e,t){var n=t.oLanguage.sDecimal;return Bf(e,n)?"html-num"+n:null},function(e,t){var n=t.oLanguage.sDecimal;return Bf(e,n,!0)?"html-num-fmt"+n:null},function(e,t){return nn(e)||typeof e=="string"&&e.indexOf("<")!==-1?"html":null}]);x.extend(K.ext.type.search,{html:function(e){return nn(e)?e:typeof e=="string"?e.replace(jf," ").replace(Wi,""):""},string:function(e){return nn(e)?e:typeof e=="string"?e.replace(jf," "):e}});var Ti=function(e,t,n,r){if(e!==0&&(!e||e==="-"))return-1/0;var a=typeof e;return a==="number"||a==="bigint"?e:(t&&(e=Ad(e,t)),e.replace&&(n&&(e=e.replace(n,"")),r&&(e=e.replace(r,""))),e*1)};function Xs(e){x.each({num:function(t){return Ti(t,e)},"num-fmt":function(t){return Ti(t,e,Hs)},"html-num":function(t){return Ti(t,e,Wi)},"html-num-fmt":function(t){return Ti(t,e,Wi,Hs)}},function(t,n){Xe.type.order[t+e+"-pre"]=n,t.match(/^html\-/)&&(Xe.type.search[t+e]=Xe.type.search.html)})}x.extend(Xe.type.order,{"date-pre":function(e){var t=Date.parse(e);return isNaN(t)?-1/0:t},"html-pre":function(e){return nn(e)?"":e.replace?e.replace(/<.*?>/g,"").toLowerCase():e+""},"string-pre":function(e){return nn(e)?"":typeof e=="string"?e.toLowerCase():e.toString?e.toString():""},"string-asc":function(e,t){return e<t?-1:e>t?1:0},"string-desc":function(e,t){return e<t?1:e>t?-1:0}});Xs("");x.extend(!0,K.ext.renderer,{header:{_:function(e,t,n,r){x(e.nTable).on("order.dt.DT",function(a,o,s,u){if(e===o){var f=n.idx;t.removeClass(r.sSortAsc+" "+r.sSortDesc).addClass(u[f]=="asc"?r.sSortAsc:u[f]=="desc"?r.sSortDesc:n.sSortingClass)}})},jqueryui:function(e,t,n,r){x("<div/>").addClass(r.sSortJUIWrapper).append(t.contents()).append(x("<span/>").addClass(r.sSortIcon+" "+n.sSortingClassJUI)).appendTo(t),x(e.nTable).on("order.dt.DT",function(a,o,s,u){if(e===o){var f=n.idx;t.removeClass(r.sSortAsc+" "+r.sSortDesc).addClass(u[f]=="asc"?r.sSortAsc:u[f]=="desc"?r.sSortDesc:n.sSortingClass),t.find("span."+r.sSortIcon).removeClass(r.sSortJUIAsc+" "+r.sSortJUIDesc+" "+r.sSortJUI+" "+r.sSortJUIAscAllowed+" "+r.sSortJUIDescAllowed).addClass(u[f]=="asc"?r.sSortJUIAsc:u[f]=="desc"?r.sSortJUIDesc:n.sSortingClassJUI)}})}}});var Ri=function(e){return Array.isArray(e)&&(e=e.join(",")),typeof e=="string"?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"):e};function Xf(e,t,n,r,a){return window.moment?e[t](a):window.luxon?e[n](a):r?e[r](a):e}var Kf=!1;function Ki(e,t,n){var r;if(window.moment){if(r=window.moment.utc(e,t,n,!0),!r.isValid())return null}else if(window.luxon){if(r=t&&typeof e=="string"?window.luxon.DateTime.fromFormat(e,t):window.luxon.DateTime.fromISO(e),!r.isValid)return null;r.setLocale(n)}else t?(Kf||alert("DataTables warning: Formatted date without Moment.js or Luxon - https://datatables.net/tn/17"),Kf=!0):r=new Date(e);return r}function ys(e){return function(t,n,r,a){arguments.length===0?(r="en",n=null,t=null):arguments.length===1?(r="en",n=t,t=null):arguments.length===2&&(r=n,n=t,t=null);var o="datetime-"+n;return K.ext.type.order[o]||(K.ext.type.detect.unshift(function(s){return s===o?o:!1}),K.ext.type.order[o+"-asc"]=function(s,u){var f=s.valueOf(),d=u.valueOf();return f===d?0:f<d?-1:1},K.ext.type.order[o+"-desc"]=function(s,u){var f=s.valueOf(),d=u.valueOf();return f===d?0:f>d?-1:1}),function(s,u){if(s==null)if(a==="--now"){var f=new Date;s=new Date(Date.UTC(f.getFullYear(),f.getMonth(),f.getDate(),f.getHours(),f.getMinutes(),f.getSeconds()))}else s="";if(u==="type")return o;if(s==="")return u!=="sort"?"":Ki("0000-01-01 00:00:00",null,r);if(n!==null&&t===n&&u!=="sort"&&u!=="type"&&!(s instanceof Date))return s;var d=Ki(s,t,r);if(d===null)return s;if(u==="sort")return d;var h=n===null?Xf(d,"toDate","toJSDate","")[e]():Xf(d,"format","toFormat","toISOString",n);return u==="display"?Ri(h):h}}}var dh=",",hh=".";if(window.Intl!==void 0)try{for(var da=new Intl.NumberFormat().formatToParts(100000.1),wr=0;wr<da.length;wr++)da[wr].type==="group"?dh=da[wr].value:da[wr].type==="decimal"&&(hh=da[wr].value)}catch{}K.datetime=function(e,t){var n="datetime-detect-"+e;t||(t="en"),K.ext.type.order[n]||(K.ext.type.detect.unshift(function(r){var a=Ki(r,e,t);return r===""||a?n:!1}),K.ext.type.order[n+"-pre"]=function(r){return Ki(r,e,t)||0})};K.render={date:ys("toLocaleDateString"),datetime:ys("toLocaleString"),time:ys("toLocaleTimeString"),number:function(e,t,n,r,a){return e==null&&(e=dh),t==null&&(t=hh),{display:function(o){if(typeof o!="number"&&typeof o!="string"||o===""||o===null)return o;var s=o<0?"-":"",u=parseFloat(o);if(isNaN(u))return Ri(o);u=u.toFixed(n),o=Math.abs(u);var f=parseInt(o,10),d=n?t+(o-f).toFixed(n).substring(2):"";return f===0&&parseFloat(d)===0&&(s=""),s+(r||"")+f.toString().replace(/\B(?=(\d{3})+(?!\d))/g,e)+d+(a||"")}}},text:function(){return{display:Ri,filter:Ri}}};function ph(e){return function(){var t=[Xi(this[K.ext.iApiIndex])].concat(Array.prototype.slice.call(arguments));return K.ext.internal[e].apply(this,t)}}x.extend(K.ext.internal,{_fnExternApiFunc:ph,_fnBuildAjax:mo,_fnAjaxUpdate:Fd,_fnAjaxParameters:Hd,_fnAjaxUpdateDraw:Md,_fnAjaxDataSrc:Wa,_fnAddColumn:xl,_fnColumnOptions:Vi,_fnAdjustColumnSizing:Ha,_fnVisibleToColumnIndex:Ma,_fnColumnIndexToVisible:ja,_fnVisbleColumns:Xr,_fnGetColumns:co,_fnColumnTypes:Al,_fnApplyColumnDefs:Rd,_fnHungarianMap:Fa,_fnCamelToHungarian:pn,_fnLanguageCompat:js,_fnBrowserDetect:Id,_fnAddData:Hn,_fnAddTr:ho,_fnNodeToDataIndex:g0,_fnNodeToColumnIndex:y0,_fnGetCellData:gt,_fnSetCellData:Pd,_fnSplitObjNotation:Bs,_fnGetObjectDataFn:Mr,_fnSetObjectDataFn:On,_fnGetDataMaster:Ws,_fnClearTable:po,_fnDeleteIndex:Oi,_fnInvalidate:Ba,_fnGetRowElements:El,_fnCreateTr:Nl,_fnBuildHead:kd,_fnDrawHead:Ta,_fnDraw:Mn,_fnReDraw:nr,_fnAddOptionsHtml:$d,_fnDetectHeader:Ca,_fnGetUniqueThs:vo,_fnFeatureHtmlFilter:jd,_fnFilterComplete:Va,_fnFilterCustom:Bd,_fnFilterColumn:Wd,_fnFilter:Vd,_fnFilterCreateSearch:Ol,_fnEscapeRegex:Il,_fnFilterData:qd,_fnFeatureHtmlInfo:Ud,_fnUpdateInfo:Xd,_fnInfoMacros:Kd,_fnInitialise:ga,_fnInitComplete:qi,_fnLengthChange:Rl,_fnFeatureHtmlLength:Yd,_fnFeatureHtmlPaginate:Gd,_fnPageChange:bo,_fnFeatureHtmlProcessing:zd,_fnProcessingDisplay:Dt,_fnFeatureHtmlTable:Jd,_fnScrollDraw:_o,_fnApplyToChildren:Jt,_fnCalculateColumnWidths:Pl,_fnThrottle:kl,_fnConvertToWidth:Qd,_fnGetWidestNode:Zd,_fnGetMaxLenString:eh,_fnStringToCss:qe,_fnSortFlatten:Kr,_fnSort:th,_fnSortAria:nh,_fnSortListener:Vs,_fnSortAttachListener:$l,_fnSortingClasses:Ui,_fnSortData:rh,_fnSaveState:qa,_fnLoadState:ah,_fnImplementState:qs,_fnSettingsFromNode:Xi,_fnLog:Pt,_fnMap:Wt,_fnBindAction:Fl,_fnCallbackReg:st,_fnCallbackFire:Pe,_fnLengthOverflow:Hl,_fnRenderer:Ml,_fnDataSource:rt,_fnRowAttributes:Ll,_fnExtend:Us,_fnCalculateEnd:function(){}});x.fn.dataTable=K;K.$=x;x.fn.dataTableSettings=K.settings;x.fn.dataTableExt=K.ext;x.fn.DataTable=function(e){return x(this).dataTable(e).api()};x.each(K,function(e,t){x.fn.DataTable[e]=t});/*! DataTables Bootstrap 5 integration
 * 2020 SpryMedia Ltd - datatables.net/license
 */let Lr=on;Lr.extend(!0,K.defaults,{dom:"<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>><'row dt-row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",renderer:"bootstrap"});Lr.extend(K.ext.classes,{sWrapper:"dataTables_wrapper dt-bootstrap5",sFilterInput:"form-control form-control-sm",sLengthSelect:"form-select form-select-sm",sProcessing:"dataTables_processing card",sPageButton:"paginate_button page-item"});K.ext.renderer.pageButton.bootstrap=function(e,t,n,r,a,o){var s=new K.Api(e),u=e.oClasses,f=e.oLanguage.oPaginate,d=e.oLanguage.oAria.paginate||{},h,b,_=function(L,O){var C,Y,W,B,Q=function(ee){ee.preventDefault(),!Lr(ee.currentTarget).hasClass("disabled")&&s.page()!=ee.data.action&&s.page(ee.data.action).draw("page")};for(C=0,Y=O.length;C<Y;C++)if(B=O[C],Array.isArray(B))_(L,B);else{switch(h="",b="",B){case"ellipsis":h="&#x2026;",b="disabled";break;case"first":h=f.sFirst,b=B+(a>0?"":" disabled");break;case"previous":h=f.sPrevious,b=B+(a>0?"":" disabled");break;case"next":h=f.sNext,b=B+(a<o-1?"":" disabled");break;case"last":h=f.sLast,b=B+(a<o-1?"":" disabled");break;default:h=B+1,b=a===B?"active":"";break}if(h){var v=b.indexOf("disabled")!==-1;W=Lr("<li>",{class:u.sPageButton+" "+b,id:n===0&&typeof B=="string"?e.sTableId+"_"+B:null}).append(Lr("<a>",{href:v?null:"#","aria-controls":e.sTableId,"aria-disabled":v?"true":null,"aria-label":d[B],role:"link","aria-current":b==="active"?"page":null,"data-dt-idx":B,tabindex:v?-1:e.iTabIndex,class:"page-link"}).html(h)).appendTo(L),e.oApi._fnBindAction(W,{action:B},Q)}}},y=Lr(t),g;try{g=y.find(document.activeElement).data("dt-idx")}catch{}var S=y.children("ul.pagination");S.length?S.empty():S=y.html("<ul/>").children("ul").addClass("pagination"),_(S,r),g!==void 0&&y.find("[data-dt-idx="+g+"]").trigger("focus")};/*! DataTables 1.13.10
 * ©2008-2024 SpryMedia Ltd - datatables.net/license
 */var A=on,k=function(e,t){if(k.factory(e,t))return k;if(this instanceof k)return A(e).DataTable(t);t=e,this.$=function(s,u){return this.api(!0).$(s,u)},this._=function(s,u){return this.api(!0).rows(s,u).data()},this.api=function(s){return s?new Ne(Qi(this[Ke.iApiIndex])):new Ne(this)},this.fnAddData=function(s,u){var f=this.api(!0),d=Array.isArray(s)&&(Array.isArray(s[0])||A.isPlainObject(s[0]))?f.rows.add(s):f.row.add(s);return(u===void 0||u)&&f.draw(),d.flatten().toArray()},this.fnAdjustColumnSizing=function(s){var u=this.api(!0).columns.adjust(),f=u.settings()[0],d=f.oScroll;s===void 0||s?u.draw(!1):(d.sX!==""||d.sY!=="")&&Eo(f)},this.fnClearTable=function(s){var u=this.api(!0).clear();(s===void 0||s)&&u.draw()},this.fnClose=function(s){this.api(!0).row(s).child.hide()},this.fnDeleteRow=function(s,u,f){var d=this.api(!0),h=d.rows(s),b=h.settings()[0],_=b.aoData[h[0][0]];return h.remove(),u&&u.call(this,b,_),(f===void 0||f)&&d.draw(),_},this.fnDestroy=function(s){this.api(!0).destroy(s)},this.fnDraw=function(s){this.api(!0).draw(s)},this.fnFilter=function(s,u,f,d,h,b){var _=this.api(!0);u==null?_.search(s,f,d,b):_.column(u).search(s,f,d,b),_.draw()},this.fnGetData=function(s,u){var f=this.api(!0);if(s!==void 0){var d=s.nodeName?s.nodeName.toLowerCase():"";return u!==void 0||d=="td"||d=="th"?f.cell(s,u).data():f.row(s).data()||null}return f.data().toArray()},this.fnGetNodes=function(s){var u=this.api(!0);return s!==void 0?u.row(s).node():u.rows().nodes().flatten().toArray()},this.fnGetPosition=function(s){var u=this.api(!0),f=s.nodeName.toUpperCase();if(f=="TR")return u.row(s).index();if(f=="TD"||f=="TH"){var d=u.cell(s).index();return[d.row,d.columnVisible,d.column]}return null},this.fnIsOpen=function(s){return this.api(!0).row(s).child.isShown()},this.fnOpen=function(s,u,f){return this.api(!0).row(s).child(u,f).show().child()[0]},this.fnPageChange=function(s,u){var f=this.api(!0).page(s);(u===void 0||u)&&f.draw(!1)},this.fnSetColumnVis=function(s,u,f){var d=this.api(!0).column(s).visible(u);(f===void 0||f)&&d.columns.adjust().draw()},this.fnSettings=function(){return Qi(this[Ke.iApiIndex])},this.fnSort=function(s){this.api(!0).order(s).draw()},this.fnSortListener=function(s,u,f){this.api(!0).order.listener(s,u,f)},this.fnUpdate=function(s,u,f,d,h){var b=this.api(!0);return f==null?b.row(u).data(s):b.cell(u,f).data(s),(h===void 0||h)&&b.columns.adjust(),(d===void 0||d)&&b.draw(),0},this.fnVersionCheck=Ke.fnVersionCheck;var n=this,r=t===void 0,a=this.length;r&&(t={}),this.oApi=this.internal=Ke.internal;for(var o in k.ext.internal)o&&(this[o]=rp(o));return this.each(function(){var s={},u=a>1?el(s,t,!0):t,f=0,d,h=this.getAttribute("id"),b=!1,_=k.defaults,y=A(this);if(this.nodeName.toLowerCase()!="table"){kt(null,0,"Non-table node initialisation ("+this.nodeName+")",2);return}zf(_),yh(_.column),vn(_,_,!0),vn(_.column,_.column,!0),vn(_,A.extend(u,y.data()),!0);var g=k.settings;for(f=0,d=g.length;f<d;f++){var S=g[f];if(S.nTable==this||S.nTHead&&S.nTHead.parentNode==this||S.nTFoot&&S.nTFoot.parentNode==this){var L=u.bRetrieve!==void 0?u.bRetrieve:_.bRetrieve,O=u.bDestroy!==void 0?u.bDestroy:_.bDestroy;if(r||L)return S.oInstance;if(O){S.oInstance.fnDestroy();break}else{kt(S,0,"Cannot reinitialise DataTable",3);return}}if(S.sTableId==this.id){g.splice(f,1);break}}(h===null||h==="")&&(h="DataTables_Table_"+k.ext._unique++,this.id=h);var C=A.extend(!0,{},k.models.oSettings,{sDestroyWidth:y[0].style.width,sInstance:h,sTableId:h});C.nTable=this,C.oApi=n.internal,C.oInit=u,g.push(C),C.oInstance=n.length===1?n:y.dataTable(),zf(u),Gs(u.oLanguage),u.aLengthMenu&&!u.iDisplayLength&&(u.iDisplayLength=Array.isArray(u.aLengthMenu[0])?u.aLengthMenu[0][0]:u.aLengthMenu[0]),u=el(A.extend(!0,{},_),u),Vt(C.oFeatures,u,["bPaginate","bLengthChange","bFilter","bSort","bSortMulti","bInfo","bProcessing","bAutoWidth","bSortClasses","bServerSide","bDeferRender"]),Vt(C,u,["asStripeClasses","ajax","fnServerData","fnFormatNumber","sServerMethod","aaSorting","aaSortingFixed","aLengthMenu","sPaginationType","sAjaxSource","sAjaxDataProp","iStateDuration","sDom","bSortCellsTop","iTabIndex","fnStateLoadCallback","fnStateSaveCallback","renderer","searchDelay","rowId",["iCookieDuration","iStateDuration"],["oSearch","oPreviousSearch"],["aoSearchCols","aoPreSearchCols"],["iDisplayLength","_iDisplayLength"]]),Vt(C.oScroll,u,[["sScrollX","sX"],["sScrollXInner","sXInner"],["sScrollY","sY"],["bScrollCollapse","bCollapse"]]),Vt(C.oLanguage,u,"fnInfoCallback"),lt(C,"aoDrawCallback",u.fnDrawCallback,"user"),lt(C,"aoServerParams",u.fnServerParams,"user"),lt(C,"aoStateSaveParams",u.fnStateSaveParams,"user"),lt(C,"aoStateLoadParams",u.fnStateLoadParams,"user"),lt(C,"aoStateLoaded",u.fnStateLoaded,"user"),lt(C,"aoRowCallback",u.fnRowCallback,"user"),lt(C,"aoRowCreatedCallback",u.fnCreatedRow,"user"),lt(C,"aoHeaderCallback",u.fnHeaderCallback,"user"),lt(C,"aoFooterCallback",u.fnFooterCallback,"user"),lt(C,"aoInitComplete",u.fnInitComplete,"user"),lt(C,"aoPreDrawCallback",u.fnPreDrawCallback,"user"),C.rowIdFn=jr(u.rowId),wh(C);var Y=C.oClasses;if(A.extend(Y,k.ext.classes,u.oClasses),y.addClass(Y.sTable),C.iInitDisplayStart===void 0&&(C.iInitDisplayStart=u.iDisplayStart,C._iDisplayStart=u.iDisplayStart),u.iDeferLoading!==null){C.bDeferLoading=!0;var W=Array.isArray(u.iDeferLoading);C._iRecordsDisplay=W?u.iDeferLoading[0]:u.iDeferLoading,C._iRecordsTotal=W?u.iDeferLoading[1]:u.iDeferLoading}var B=C.oLanguage;A.extend(!0,B,u.oLanguage),B.sUrl?(A.ajax({dataType:"json",url:B.sUrl,success:function(ae){vn(_.oLanguage,ae),Gs(ae),A.extend(!0,B,ae,C.oInit.oLanguage),ke(C,null,"i18n",[C]),ya(C)},error:function(){ya(C)}}),b=!0):ke(C,null,"i18n",[C]),u.asStripeClasses===null&&(C.asStripeClasses=[Y.sStripeOdd,Y.sStripeEven]);var Q=C.asStripeClasses,v=y.children("tbody").find("tr").eq(0);A.inArray(!0,A.map(Q,function(ae,we){return v.hasClass(ae)}))!==-1&&(A("tbody tr",this).removeClass(Q.join(" ")),C.asDestroyStripes=Q.slice());var ee=[],G,re=this.getElementsByTagName("thead");if(re.length!==0&&(Sa(C.aoHeader,re[0]),ee=So(C)),u.aoColumns===null)for(G=[],f=0,d=ee.length;f<d;f++)G.push(null);else G=u.aoColumns;for(f=0,d=G.length;f<d;f++)ql(C,ee?ee[f]:null);if(Th(C,u.aoColumnDefs,G,function(ae,we){Gi(C,ae,we)}),v.length){var ie=function(ae,we){return ae.getAttribute("data-"+we)!==null?we:null};A(v[0]).children("th, td").each(function(ae,we){var Te=C.aoColumns[ae];if(Te||kt(C,0,"Incorrect column count",18),Te.mData===ae){var me=ie(we,"sort")||ie(we,"order"),xe=ie(we,"filter")||ie(we,"search");(me!==null||xe!==null)&&(Te.mData={_:ae+".display",sort:me!==null?ae+".@data-"+me:void 0,type:me!==null?ae+".@data-"+me:void 0,filter:xe!==null?ae+".@data-"+xe:void 0},Te._isArrayHost=!0,Gi(C,ae))}})}var pe=C.oFeatures,se=function(){if(u.aaSorting===void 0){var ae=C.aaSorting;for(f=0,d=ae.length;f<d;f++)ae[f][1]=C.aoColumns[f].asSorting[0]}Ji(C),pe.bSort&&lt(C,"aoDrawCallback",function(){if(C.bSorted){var Le=Gr(C),Fe={};A.each(Le,function(ct,We){Fe[We.src]=We.dir}),ke(C,null,"order",[C,Le,Fe]),Uh(C)}}),lt(C,"aoDrawCallback",function(){(C.bSorted||at(C)==="ssp"||pe.bDeferRender)&&Ji(C)},"sc");var we=y.children("caption").each(function(){this._captionSide=A(this).css("caption-side")}),Te=y.children("thead");Te.length===0&&(Te=A("<thead/>").appendTo(y)),C.nTHead=Te[0];var me=y.children("tbody");me.length===0&&(me=A("<tbody/>").insertAfter(Te)),C.nTBody=me[0];var xe=y.children("tfoot");if(xe.length===0&&we.length>0&&(C.oScroll.sX!==""||C.oScroll.sY!=="")&&(xe=A("<tfoot/>").appendTo(y)),xe.length===0||xe.children().length===0?y.addClass(Y.sNoFooter):xe.length>0&&(C.nTFoot=xe[0],Sa(C.aoFooter,C.nTFoot)),u.aaData)for(f=0;f<u.aaData.length;f++)jn(C,u.aaData[f]);else(C.bDeferLoading||at(C)=="dom")&&Co(C,A(C.nTBody).children("tr"));C.aiDisplay=C.aiDisplayMaster.slice(),C.bInitialised=!0,b===!1&&ya(C)};lt(C,"aoDrawCallback",ei,"state_save"),u.bStateSave?(pe.bStateSave=!0,Kh(C,u,se)):se()}),n=null,this},Ke,Ne,he,je,ws={},Yf=/[\r\n\u2028]/g,Yi=/<.*?>/g,R0=/^\d{2,4}[\.\/\-]\d{1,2}[\.\/\-]\d{1,2}([T ]{1}\d{1,2}[:\.]\d{2}([\.:]\d{2})?)?$/,P0=new RegExp("(\\"+["/",".","*","+","?","|","(",")","[","]","{","}","\\","$","^","-"].join("|\\")+")","g"),Ks=/['\u00A0,$£€¥%\u2009\u202F\u20BD\u20a9\u20BArfkɃΞ]/gi,rn=function(e){return!e||e===!0||e==="-"},vh=function(e){var t=parseInt(e,10);return!isNaN(t)&&isFinite(e)?t:null},mh=function(e,t){return ws[t]||(ws[t]=new RegExp(zl(t),"g")),typeof e=="string"&&t!=="."?e.replace(/\./g,"").replace(ws[t],"."):e},Ys=function(e,t,n){var r=typeof e,a=r==="string";return r==="number"||r==="bigint"||rn(e)?!0:(t&&a&&(e=mh(e,t)),n&&a&&(e=e.replace(Ks,"")),!isNaN(parseFloat(e))&&isFinite(e))},k0=function(e){return rn(e)||typeof e=="string"},Gf=function(e,t,n){if(rn(e))return!0;var r=k0(e);return r&&Ys($0(e),t,n)?!0:null},yt=function(e,t,n){var r=[],a=0,o=e.length;if(n!==void 0)for(;a<o;a++)e[a]&&e[a][t]&&r.push(e[a][t][n]);else for(;a<o;a++)e[a]&&r.push(e[a][t]);return r},Xa=function(e,t,n,r){var a=[],o=0,s=t.length;if(r!==void 0)for(;o<s;o++)e[t[o]][n]&&a.push(e[t[o]][n][r]);else for(;o<s;o++)a.push(e[t[o]][n]);return a},Or=function(e,t){var n=[],r;t===void 0?(t=0,r=e):(r=t,t=e);for(var a=t;a<r;a++)n.push(a);return n},bh=function(e){for(var t=[],n=0,r=e.length;n<r;n++)e[n]&&t.push(e[n]);return t},$0=function(e){return e.replace(Yi,"").replace(/<script/i,"")},F0=function(e){if(e.length<2)return!0;for(var t=e.slice().sort(),n=t[0],r=1,a=t.length;r<a;r++){if(t[r]===n)return!1;n=t[r]}return!0},wo=function(e){if(F0(e))return e.slice();var t=[],n,r,a=e.length,o,s=0;e:for(r=0;r<a;r++){for(n=e[r],o=0;o<s;o++)if(t[o]===n)continue e;t.push(n),s++}return t},_h=function(e,t){if(Array.isArray(t))for(var n=0;n<t.length;n++)_h(e,t[n]);else e.push(t);return e},gh=function(e,t){return t===void 0&&(t=0),this.indexOf(e,t)!==-1};Array.isArray||(Array.isArray=function(e){return Object.prototype.toString.call(e)==="[object Array]"});Array.prototype.includes||(Array.prototype.includes=gh);String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")});String.prototype.includes||(String.prototype.includes=gh);k.util={throttle:function(e,t){var n=t!==void 0?t:200,r,a;return function(){var o=this,s=+new Date,u=arguments;r&&s<r+n?(clearTimeout(a),a=setTimeout(function(){r=void 0,e.apply(o,u)},n)):(r=s,e.apply(o,u))}},escapeRegex:function(e){return e.replace(P0,"\\$1")},set:function(e){if(A.isPlainObject(e))return k.util.set(e._);if(e===null)return function(){};if(typeof e=="function")return function(n,r,a){e(n,"set",r,a)};if(typeof e=="string"&&(e.indexOf(".")!==-1||e.indexOf("[")!==-1||e.indexOf("(")!==-1)){var t=function(n,r,a){for(var o=zs(a),s,u=o[o.length-1],f,d,h,b,_=0,y=o.length-1;_<y;_++){if(o[_]==="__proto__"||o[_]==="constructor")throw new Error("Cannot set prototype values");if(f=o[_].match(ha),d=o[_].match(Tr),f){if(o[_]=o[_].replace(ha,""),n[o[_]]=[],s=o.slice(),s.splice(0,_+1),b=s.join("."),Array.isArray(r))for(var g=0,S=r.length;g<S;g++)h={},t(h,r[g],b),n[o[_]].push(h);else n[o[_]]=r;return}else d&&(o[_]=o[_].replace(Tr,""),n=n[o[_]](r));(n[o[_]]===null||n[o[_]]===void 0)&&(n[o[_]]={}),n=n[o[_]]}u.match(Tr)?n=n[u.replace(Tr,"")](r):n[u.replace(ha,"")]=r};return function(n,r){return t(n,r,e)}}else return function(n,r){n[e]=r}},get:function(e){if(A.isPlainObject(e)){var t={};return A.each(e,function(r,a){a&&(t[r]=k.util.get(a))}),function(r,a,o,s){var u=t[a]||t._;return u!==void 0?u(r,a,o,s):r}}else{if(e===null)return function(r){return r};if(typeof e=="function")return function(r,a,o,s){return e(r,a,o,s)};if(typeof e=="string"&&(e.indexOf(".")!==-1||e.indexOf("[")!==-1||e.indexOf("(")!==-1)){var n=function(r,a,o){var s,u,f,d;if(o!=="")for(var h=zs(o),b=0,_=h.length;b<_;b++){if(s=h[b].match(ha),u=h[b].match(Tr),s){if(h[b]=h[b].replace(ha,""),h[b]!==""&&(r=r[h[b]]),f=[],h.splice(0,b+1),d=h.join("."),Array.isArray(r))for(var y=0,g=r.length;y<g;y++)f.push(n(r[y],a,d));var S=s[0].substring(1,s[0].length-1);r=S===""?f:f.join(S);break}else if(u){h[b]=h[b].replace(Tr,""),r=r[h[b]]();continue}if(r===null||r[h[b]]===null)return null;if(r===void 0||r[h[b]]===void 0)return;r=r[h[b]]}return r};return function(r,a){return n(r,a,e)}}else return function(r,a){return r[e]}}}};function Ka(e){var t="a aa ai ao as b fn i m o s ",n,r,a={};A.each(e,function(o,s){n=o.match(/^([^A-Z]+?)([A-Z])/),n&&t.indexOf(n[1]+" ")!==-1&&(r=o.replace(n[0],n[2].toLowerCase()),a[r]=o,n[1]==="o"&&Ka(e[o]))}),e._hungarianMap=a}function vn(e,t,n){e._hungarianMap||Ka(e);var r;A.each(t,function(a,o){r=e._hungarianMap[a],r!==void 0&&(n||t[r]===void 0)&&(r.charAt(0)==="o"?(t[r]||(t[r]={}),A.extend(!0,t[r],t[a]),vn(e[r],t[r],n)):t[r]=t[a])})}function Gs(e){var t=k.defaults.oLanguage,n=t.sDecimal;if(n&&tl(n),e){var r=e.sZeroRecords;!e.sEmptyTable&&r&&t.sEmptyTable==="No data available in table"&&Vt(e,e,"sZeroRecords","sEmptyTable"),!e.sLoadingRecords&&r&&t.sLoadingRecords==="Loading..."&&Vt(e,e,"sZeroRecords","sLoadingRecords"),e.sInfoThousands&&(e.sThousands=e.sInfoThousands);var a=e.sDecimal;a&&n!==a&&tl(a)}}var mt=function(e,t,n){e[t]!==void 0&&(e[n]=e[t])};function zf(e){mt(e,"ordering","bSort"),mt(e,"orderMulti","bSortMulti"),mt(e,"orderClasses","bSortClasses"),mt(e,"orderCellsTop","bSortCellsTop"),mt(e,"order","aaSorting"),mt(e,"orderFixed","aaSortingFixed"),mt(e,"paging","bPaginate"),mt(e,"pagingType","sPaginationType"),mt(e,"pageLength","iDisplayLength"),mt(e,"searching","bFilter"),typeof e.sScrollX=="boolean"&&(e.sScrollX=e.sScrollX?"100%":""),typeof e.scrollX=="boolean"&&(e.scrollX=e.scrollX?"100%":"");var t=e.aoSearchCols;if(t)for(var n=0,r=t.length;n<r;n++)t[n]&&vn(k.models.oSearch,t[n])}function yh(e){mt(e,"orderable","bSortable"),mt(e,"orderData","aDataSort"),mt(e,"orderSequence","asSorting"),mt(e,"orderDataType","sortDataType");var t=e.aDataSort;typeof t=="number"&&!Array.isArray(t)&&(e.aDataSort=[t])}function wh(e){if(!k.__browser){var t={};k.__browser=t;var n=A("<div/>").css({position:"fixed",top:0,left:A(window).scrollLeft()*-1,height:1,width:1,overflow:"hidden"}).append(A("<div/>").css({position:"absolute",top:1,left:1,width:100,overflow:"scroll"}).append(A("<div/>").css({width:"100%",height:10}))).appendTo("body"),r=n.children(),a=r.children();t.barWidth=r[0].offsetWidth-r[0].clientWidth,t.bScrollOversize=a[0].offsetWidth===100&&r[0].clientWidth!==100,t.bScrollbarLeft=Math.round(a.offset().left)!==1,t.bBounding=!!n[0].getBoundingClientRect().width,n.remove()}A.extend(e.oBrowser,k.__browser),e.oScroll.iBarWidth=k.__browser.barWidth}function Jf(e,t,n,r,a,o){var s=r,u,f=!1;for(n!==void 0&&(u=n,f=!0);s!==a;)e.hasOwnProperty(s)&&(u=f?t(u,e[s],s,e):e[s],f=!0,s+=o);return u}function ql(e,t){var n=k.defaults.column,r=e.aoColumns.length,a=A.extend({},k.models.oColumn,n,{nTh:t||document.createElement("th"),sTitle:n.sTitle?n.sTitle:t?t.innerHTML:"",aDataSort:n.aDataSort?n.aDataSort:[r],mData:n.mData?n.mData:r,idx:r});e.aoColumns.push(a);var o=e.aoPreSearchCols;o[r]=A.extend({},k.models.oSearch,o[r]),Gi(e,r,A(t).data())}function Gi(e,t,n){var r=e.aoColumns[t],a=e.oClasses,o=A(r.nTh);if(!r.sWidthOrig){r.sWidthOrig=o.attr("width")||null;var s=(o.attr("style")||"").match(/width:\s*(\d+[pxem%]+)/);s&&(r.sWidthOrig=s[1])}if(n!=null){yh(n),vn(k.defaults.column,n,!0),n.mDataProp!==void 0&&!n.mData&&(n.mData=n.mDataProp),n.sType&&(r._sManualType=n.sType),n.className&&!n.sClass&&(n.sClass=n.className),n.sClass&&o.addClass(n.sClass);var u=r.sClass;A.extend(r,n),Vt(r,n,"sWidth","sWidthOrig"),u!==r.sClass&&(r.sClass=u+" "+r.sClass),n.iDataSort!==void 0&&(r.aDataSort=[n.iDataSort]),Vt(r,n,"aDataSort"),r.ariaTitle||(r.ariaTitle=o.attr("aria-label"))}var f=r.mData,d=jr(f),h=r.mRender?jr(r.mRender):null,b=function(g){return typeof g=="string"&&g.indexOf("@")!==-1};r._bAttrSrc=A.isPlainObject(f)&&(b(f.sort)||b(f.type)||b(f.filter)),r._setter=null,r.fnGetData=function(g,S,L){var O=d(g,S,void 0,L);return h&&S?h(O,S,g,L):O},r.fnSetData=function(g,S,L){return In(f)(g,S,L)},typeof f!="number"&&!r._isArrayHost&&(e._rowReadObject=!0),e.oFeatures.bSort||(r.bSortable=!1,o.addClass(a.sSortableNone));var _=A.inArray("asc",r.asSorting)!==-1,y=A.inArray("desc",r.asSorting)!==-1;!r.bSortable||!_&&!y?(r.sSortingClass=a.sSortableNone,r.sSortingClassJUI=""):_&&!y?(r.sSortingClass=a.sSortableAsc,r.sSortingClassJUI=a.sSortJUIAscAllowed):!_&&y?(r.sSortingClass=a.sSortableDesc,r.sSortingClassJUI=a.sSortJUIDescAllowed):(r.sSortingClass=a.sSortable,r.sSortingClassJUI=a.sSortJUI)}function Ya(e){if(e.oFeatures.bAutoWidth!==!1){var t=e.aoColumns;Ql(e);for(var n=0,r=t.length;n<r;n++)t[n].nTh.style.width=t[n].sWidth}var a=e.oScroll;(a.sY!==""||a.sX!=="")&&Eo(e),ke(e,null,"column-sizing",[e])}function Ga(e,t){var n=To(e,"bVisible");return typeof n[t]=="number"?n[t]:null}function za(e,t){var n=To(e,"bVisible"),r=A.inArray(t,n);return r!==-1?r:null}function Yr(e){var t=0;return A.each(e.aoColumns,function(n,r){r.bVisible&&A(r.nTh).css("display")!=="none"&&t++}),t}function To(e,t){var n=[];return A.map(e.aoColumns,function(r,a){r[t]&&n.push(a)}),n}function Ul(e){var t=e.aoColumns,n=e.aoData,r=k.ext.type.detect,a,o,s,u,f,d,h,b,_;for(a=0,o=t.length;a<o;a++)if(h=t[a],_=[],!h.sType&&h._sManualType)h.sType=h._sManualType;else if(!h.sType){for(s=0,u=r.length;s<u;s++){for(f=0,d=n.length;f<d&&(_[f]===void 0&&(_[f]=wt(e,f,a,"type")),b=r[s](_[f],e),!(!b&&s!==r.length-1||b==="html"&&!rn(_[f])));f++);if(b){h.sType=b;break}}h.sType||(h.sType="string")}}function Th(e,t,n,r){var a,o,s,u,f,d,h,b=e.aoColumns;if(t)for(a=t.length-1;a>=0;a--){h=t[a];var _=h.target!==void 0?h.target:h.targets!==void 0?h.targets:h.aTargets;for(Array.isArray(_)||(_=[_]),s=0,u=_.length;s<u;s++)if(typeof _[s]=="number"&&_[s]>=0){for(;b.length<=_[s];)ql(e);r(_[s],h)}else if(typeof _[s]=="number"&&_[s]<0)r(b.length+_[s],h);else if(typeof _[s]=="string")for(f=0,d=b.length;f<d;f++)(_[s]=="_all"||A(b[f].nTh).hasClass(_[s]))&&r(f,h)}if(n)for(a=0,o=n.length;a<o;a++)r(a,n[a])}function jn(e,t,n,r){var a=e.aoData.length,o=A.extend(!0,{},k.models.oRow,{src:n?"dom":"data",idx:a});o._aData=t,e.aoData.push(o);for(var s=e.aoColumns,u=0,f=s.length;u<f;u++)s[u].sType=null;e.aiDisplayMaster.push(a);var d=e.rowIdFn(t);return d!==void 0&&(e.aIds[d]=o),(n||!e.oFeatures.bDeferRender)&&Kl(e,a,n,r),a}function Co(e,t){var n;return t instanceof A||(t=A(t)),t.map(function(r,a){return n=Xl(e,a),jn(e,n.data,a,n.cells)})}function H0(e,t){return t._DT_RowIndex!==void 0?t._DT_RowIndex:null}function M0(e,t,n){return A.inArray(n,e.aoData[t].anCells)}function wt(e,t,n,r){r==="search"?r="filter":r==="order"&&(r="sort");var a=e.iDraw,o=e.aoColumns[n],s=e.aoData[t]._aData,u=o.sDefaultContent,f=o.fnGetData(s,r,{settings:e,row:t,col:n});if(f===void 0)return e.iDrawError!=a&&u===null&&(kt(e,0,"Requested unknown parameter "+(typeof o.mData=="function"?"{function}":"'"+o.mData+"'")+" for row "+t+", column "+n,4),e.iDrawError=a),u;if((f===s||f===null)&&u!==null&&r!==void 0)f=u;else if(typeof f=="function")return f.call(s);if(f===null&&r==="display")return"";if(r==="filter"){var d=k.ext.type.search;d[o.sType]&&(f=d[o.sType](f))}return f}function Ch(e,t,n,r){var a=e.aoColumns[n],o=e.aoData[t]._aData;a.fnSetData(o,r,{settings:e,row:t,col:n})}var ha=/\[.*?\]$/,Tr=/\(\)$/;function zs(e){return A.map(e.match(/(\\.|[^\.])+/g)||[""],function(t){return t.replace(/\\\./g,".")})}var jr=k.util.get,In=k.util.set;function Js(e){return yt(e.aoData,"_aData")}function Do(e){e.aoData.length=0,e.aiDisplayMaster.length=0,e.aiDisplay.length=0,e.aIds={}}function Pi(e,t,n){for(var r=-1,a=0,o=e.length;a<o;a++)e[a]==t?r=a:e[a]>t&&e[a]--;r!=-1&&n===void 0&&e.splice(r,1)}function Ja(e,t,n,r){var a=e.aoData[t],o,s,u=function(h,b){for(;h.childNodes.length;)h.removeChild(h.firstChild);h.innerHTML=wt(e,t,b,"display")};if(n==="dom"||(!n||n==="auto")&&a.src==="dom")a._aData=Xl(e,a,r,r===void 0?void 0:a._aData).data;else{var f=a.anCells;if(f)if(r!==void 0)u(f[r],r);else for(o=0,s=f.length;o<s;o++)u(f[o],o)}a._aSortData=null,a._aFilterData=null;var d=e.aoColumns;if(r!==void 0)d[r].sType=null;else{for(o=0,s=d.length;o<s;o++)d[o].sType=null;Yl(e,a)}}function Xl(e,t,n,r){var a=[],o=t.firstChild,s,u,f=0,d,h=e.aoColumns,b=e._rowReadObject;r=r!==void 0?r:b?{}:[];var _=function(C,Y){if(typeof C=="string"){var W=C.indexOf("@");if(W!==-1){var B=C.substring(W+1),Q=In(C);Q(r,Y.getAttribute(B))}}},y=function(C){if(n===void 0||n===f)if(u=h[f],d=C.innerHTML.trim(),u&&u._bAttrSrc){var Y=In(u.mData._);Y(r,d),_(u.mData.sort,C),_(u.mData.type,C),_(u.mData.filter,C)}else b?(u._setter||(u._setter=In(u.mData)),u._setter(r,d)):r[f]=d;f++};if(o)for(;o;)s=o.nodeName.toUpperCase(),(s=="TD"||s=="TH")&&(y(o),a.push(o)),o=o.nextSibling;else{a=t.anCells;for(var g=0,S=a.length;g<S;g++)y(a[g])}var L=t.firstChild?t:t.nTr;if(L){var O=L.getAttribute("id");O&&In(e.rowId)(r,O)}return{data:r,cells:a}}function Kl(e,t,n,r){var a=e.aoData[t],o=a._aData,s=[],u,f,d,h,b,_;if(a.nTr===null){for(u=n||document.createElement("tr"),a.nTr=u,a.anCells=s,u._DT_RowIndex=t,Yl(e,a),h=0,b=e.aoColumns.length;h<b;h++)d=e.aoColumns[h],_=!n,f=_?document.createElement(d.sCellType):r[h],f||kt(e,0,"Incorrect column count",18),f._DT_CellIndex={row:t,column:h},s.push(f),(_||(d.mRender||d.mData!==h)&&(!A.isPlainObject(d.mData)||d.mData._!==h+".display"))&&(f.innerHTML=wt(e,t,h,"display")),d.sClass&&(f.className+=" "+d.sClass),d.bVisible&&!n?u.appendChild(f):!d.bVisible&&n&&f.parentNode.removeChild(f),d.fnCreatedCell&&d.fnCreatedCell.call(e.oInstance,f,wt(e,t,h),o,t,h);ke(e,"aoRowCreatedCallback",null,[u,o,t,s])}}function Yl(e,t){var n=t.nTr,r=t._aData;if(n){var a=e.rowIdFn(r);if(a&&(n.id=a),r.DT_RowClass){var o=r.DT_RowClass.split(" ");t.__rowc=t.__rowc?wo(t.__rowc.concat(o)):o,A(n).removeClass(t.__rowc.join(" ")).addClass(r.DT_RowClass)}r.DT_RowAttr&&A(n).attr(r.DT_RowAttr),r.DT_RowData&&A(n).data(r.DT_RowData)}}function Dh(e){var t,n,r,a,o,s=e.nTHead,u=e.nTFoot,f=A("th, td",s).length===0,d=e.oClasses,h=e.aoColumns;for(f&&(a=A("<tr/>").appendTo(s)),t=0,n=h.length;t<n;t++)o=h[t],r=A(o.nTh).addClass(o.sClass),f&&r.appendTo(a),e.oFeatures.bSort&&(r.addClass(o.sSortingClass),o.bSortable!==!1&&(r.attr("tabindex",e.iTabIndex).attr("aria-controls",e.sTableId),eu(e,o.nTh,t))),o.sTitle!=r[0].innerHTML&&r.html(o.sTitle),ru(e,"header")(e,r,o,d);if(f&&Sa(e.aoHeader,s),A(s).children("tr").children("th, td").addClass(d.sHeaderTH),A(u).children("tr").children("th, td").addClass(d.sFooterTH),u!==null){var b=e.aoFooter[0];for(t=0,n=b.length;t<n;t++)o=h[t],o?(o.nTf=b[t].cell,o.sClass&&A(o.nTf).addClass(o.sClass)):kt(e,0,"Incorrect column count",18)}}function Da(e,t,n){var r,a,o,s,u,f,d,h=[],b=[],_=e.aoColumns.length,y,g;if(t){for(n===void 0&&(n=!1),r=0,a=t.length;r<a;r++){for(h[r]=t[r].slice(),h[r].nTr=t[r].nTr,o=_-1;o>=0;o--)!e.aoColumns[o].bVisible&&!n&&h[r].splice(o,1);b.push([])}for(r=0,a=h.length;r<a;r++){if(d=h[r].nTr,d)for(;f=d.firstChild;)d.removeChild(f);for(o=0,s=h[r].length;o<s;o++)if(y=1,g=1,b[r][o]===void 0){for(d.appendChild(h[r][o].cell),b[r][o]=1;h[r+y]!==void 0&&h[r][o].cell==h[r+y][o].cell;)b[r+y][o]=1,y++;for(;h[r][o+g]!==void 0&&h[r][o].cell==h[r][o+g].cell;){for(u=0;u<y;u++)b[r+u][o+g]=1;g++}A(h[r][o].cell).attr("rowspan",y).attr("colspan",g)}}}}function Bn(e,t){j0(e);var n=ke(e,"aoPreDrawCallback","preDraw",[e]);if(A.inArray(!1,n)!==-1){St(e,!1);return}var r=[],a=0,o=e.asStripeClasses,s=o.length,u=e.oLanguage,f=at(e)=="ssp",d=e.aiDisplay,h=e._iDisplayStart,b=e.fnDisplayEnd();if(e.bDrawing=!0,e.bDeferLoading)e.bDeferLoading=!1,e.iDraw++,St(e,!1);else if(!f)e.iDraw++;else if(!e.bDestroying&&!t){xh(e);return}if(d.length!==0)for(var _=f?0:h,y=f?e.aoData.length:b,g=_;g<y;g++){var S=d[g],L=e.aoData[S];L.nTr===null&&Kl(e,S);var O=L.nTr;if(s!==0){var C=o[a%s];L._sRowStripe!=C&&(A(O).removeClass(L._sRowStripe).addClass(C),L._sRowStripe=C)}ke(e,"aoRowCallback",null,[O,L._aData,a,g,S]),r.push(O),a++}else{var Y=u.sZeroRecords;e.iDraw==1&&at(e)=="ajax"?Y=u.sLoadingRecords:u.sEmptyTable&&e.fnRecordsTotal()===0&&(Y=u.sEmptyTable),r[0]=A("<tr/>",{class:s?o[0]:""}).append(A("<td />",{valign:"top",colSpan:Yr(e),class:e.oClasses.sRowEmpty}).html(Y))[0]}ke(e,"aoHeaderCallback","header",[A(e.nTHead).children("tr")[0],Js(e),h,b,d]),ke(e,"aoFooterCallback","footer",[A(e.nTFoot).children("tr")[0],Js(e),h,b,d]);var W=A(e.nTBody);W.children().detach(),W.append(A(r)),ke(e,"aoDrawCallback","draw",[e]),e.bSorted=!1,e.bFiltered=!1,e.bDrawing=!1}function rr(e,t){var n=e.oFeatures,r=n.bSort,a=n.bFilter;r&&qh(e),a?Za(e,e.oPreviousSearch):e.aiDisplay=e.aiDisplayMaster.slice(),t!==!0&&(e._iDisplayStart=0),e._drawHold=t,Bn(e),e._drawHold=!1}function Sh(e){var t=e.oClasses,n=A(e.nTable),r=A("<div/>").insertBefore(n),a=e.oFeatures,o=A("<div/>",{id:e.sTableId+"_wrapper",class:t.sWrapper+(e.nTFoot?"":" "+t.sNoFooter)});e.nHolding=r[0],e.nTableWrapper=o[0],e.nTableReinsertBefore=e.nTable.nextSibling;for(var s=e.sDom.split(""),u,f,d,h,b,_,y=0;y<s.length;y++){if(u=null,f=s[y],f=="<"){if(d=A("<div/>")[0],h=s[y+1],h=="'"||h=='"'){for(b="",_=2;s[y+_]!=h;)b+=s[y+_],_++;if(b=="H"?b=t.sJUIHeader:b=="F"&&(b=t.sJUIFooter),b.indexOf(".")!=-1){var g=b.split(".");d.id=g[0].substr(1,g[0].length-1),d.className=g[1]}else b.charAt(0)=="#"?d.id=b.substr(1,b.length-1):d.className=b;y+=_}o.append(d),o=A(d)}else if(f==">")o=o.parent();else if(f=="l"&&a.bPaginate&&a.bLengthChange)u=Fh(e);else if(f=="f"&&a.bFilter)u=Nh(e);else if(f=="r"&&a.bProcessing)u=Mh(e);else if(f=="t")u=jh(e);else if(f=="i"&&a.bInfo)u=Ph(e);else if(f=="p"&&a.bPaginate)u=Hh(e);else if(k.ext.feature.length!==0){for(var S=k.ext.feature,L=0,O=S.length;L<O;L++)if(f==S[L].cFeature){u=S[L].fnInit(e);break}}if(u){var C=e.aanFeatures;C[f]||(C[f]=[]),C[f].push(u),o.append(u)}}r.replaceWith(o),e.nHolding=null}function Sa(e,t){var n=A(t).children("tr"),r,a,o,s,u,f,d,h,b,_,y,g=function(S,L,O){for(var C=S[L];C[O];)O++;return O};for(e.splice(0,e.length),o=0,f=n.length;o<f;o++)e.push([]);for(o=0,f=n.length;o<f;o++)for(r=n[o],h=0,a=r.firstChild;a;){if(a.nodeName.toUpperCase()=="TD"||a.nodeName.toUpperCase()=="TH")for(b=a.getAttribute("colspan")*1,_=a.getAttribute("rowspan")*1,b=!b||b===0||b===1?1:b,_=!_||_===0||_===1?1:_,d=g(e,o,h),y=b===1,u=0;u<b;u++)for(s=0;s<_;s++)e[o+s][d+u]={cell:a,unique:y},e[o+s].nTr=r;a=a.nextSibling}}function So(e,t,n){var r=[];n||(n=e.aoHeader,t&&(n=[],Sa(n,t)));for(var a=0,o=n.length;a<o;a++)for(var s=0,u=n[a].length;s<u;s++)n[a][s].unique&&(!r[s]||!e.bSortCellsTop)&&(r[s]=n[a][s].cell);return r}function j0(e){var t=at(e)=="ssp",n=e.iInitDisplayStart;n!==void 0&&n!==-1&&(e._iDisplayStart=t?n:n>=e.fnRecordsDisplay()?0:n,e.iInitDisplayStart=-1)}function xo(e,t,n){if(ke(e,"aoServerParams","serverParams",[t]),t&&Array.isArray(t)){var r={},a=/(.*?)\[\]$/;A.each(t,function(b,_){var y=_.name.match(a);if(y){var g=y[0];r[g]||(r[g]=[]),r[g].push(_.value)}else r[_.name]=_.value}),t=r}var o,s=e.ajax,u=e.oInstance,f=function(b){var _=e.jqXHR?e.jqXHR.status:null;(b===null||typeof _=="number"&&_==204)&&(b={},Qa(e,b,[]));var y=b.error||b.sError;y&&kt(e,0,y),e.json=b,ke(e,null,"xhr",[e,b,e.jqXHR]),n(b)};if(A.isPlainObject(s)&&s.data){o=s.data;var d=typeof o=="function"?o(t,e):o;t=typeof o=="function"&&d?d:A.extend(!0,t,d),delete s.data}var h={data:t,success:f,dataType:"json",cache:!1,type:e.sServerMethod,error:function(b,_,y){var g=ke(e,null,"xhr",[e,null,e.jqXHR]);A.inArray(!0,g)===-1&&(_=="parsererror"?kt(e,0,"Invalid JSON response",1):b.readyState===4&&kt(e,0,"Ajax error",7)),St(e,!1)}};e.oAjaxData=t,ke(e,null,"preXhr",[e,t]),e.fnServerData?e.fnServerData.call(u,e.sAjaxSource,A.map(t,function(b,_){return{name:_,value:b}}),f,e):e.sAjaxSource||typeof s=="string"?e.jqXHR=A.ajax(A.extend(h,{url:s||e.sAjaxSource})):typeof s=="function"?e.jqXHR=s.call(u,t,f,e):(e.jqXHR=A.ajax(A.extend(h,s)),s.data=o)}function xh(e){e.iDraw++,St(e,!0);var t=e._drawHold;xo(e,Ah(e),function(n){e._drawHold=t,Eh(e,n),e._drawHold=!1})}function Ah(e){var t=e.aoColumns,n=t.length,r=e.oFeatures,a=e.oPreviousSearch,o=e.aoPreSearchCols,s,u=[],f,d,h,b=Gr(e),_=e._iDisplayStart,y=r.bPaginate!==!1?e._iDisplayLength:-1,g=function(O,C){u.push({name:O,value:C})};g("sEcho",e.iDraw),g("iColumns",n),g("sColumns",yt(t,"sName").join(",")),g("iDisplayStart",_),g("iDisplayLength",y);var S={draw:e.iDraw,columns:[],order:[],start:_,length:y,search:{value:a.sSearch,regex:a.bRegex}};for(s=0;s<n;s++)d=t[s],h=o[s],f=typeof d.mData=="function"?"function":d.mData,S.columns.push({data:f,name:d.sName,searchable:d.bSearchable,orderable:d.bSortable,search:{value:h.sSearch,regex:h.bRegex}}),g("mDataProp_"+s,f),r.bFilter&&(g("sSearch_"+s,h.sSearch),g("bRegex_"+s,h.bRegex),g("bSearchable_"+s,d.bSearchable)),r.bSort&&g("bSortable_"+s,d.bSortable);r.bFilter&&(g("sSearch",a.sSearch),g("bRegex",a.bRegex)),r.bSort&&(A.each(b,function(O,C){S.order.push({column:C.col,dir:C.dir}),g("iSortCol_"+O,C.col),g("sSortDir_"+O,C.dir)}),g("iSortingCols",b.length));var L=k.ext.legacy.ajax;return L===null?e.sAjaxSource?u:S:L?u:S}function Eh(e,t){var n=function(d,h){return t[d]!==void 0?t[d]:t[h]},r=Qa(e,t),a=n("sEcho","draw"),o=n("iTotalRecords","recordsTotal"),s=n("iTotalDisplayRecords","recordsFiltered");if(a!==void 0){if(a*1<e.iDraw)return;e.iDraw=a*1}r||(r=[]),Do(e),e._iRecordsTotal=parseInt(o,10),e._iRecordsDisplay=parseInt(s,10);for(var u=0,f=r.length;u<f;u++)jn(e,r[u]);e.aiDisplay=e.aiDisplayMaster.slice(),Bn(e,!0),e._bInitComplete||zi(e,t),St(e,!1)}function Qa(e,t,n){var r=A.isPlainObject(e.ajax)&&e.ajax.dataSrc!==void 0?e.ajax.dataSrc:e.sAjaxDataProp;if(!n)return r==="data"?t.aaData||t[r]:r!==""?jr(r)(t):t;In(r)(t,n)}function Nh(e){var t=e.oClasses,n=e.sTableId,r=e.oLanguage,a=e.oPreviousSearch,o=e.aanFeatures,s='<input type="search" class="'+t.sFilterInput+'"/>',u=r.sSearch;u=u.match(/_INPUT_/)?u.replace("_INPUT_",s):u+s;var f=A("<div/>",{id:o.f?null:n+"_filter",class:t.sFilter}).append(A("<label/>").append(u)),d=function(_){o.f;var y=this.value?this.value:"";a.return&&_.key!=="Enter"||y!=a.sSearch&&(Za(e,{sSearch:y,bRegex:a.bRegex,bSmart:a.bSmart,bCaseInsensitive:a.bCaseInsensitive,return:a.return}),e._iDisplayStart=0,Bn(e))},h=e.searchDelay!==null?e.searchDelay:at(e)==="ssp"?400:0,b=A("input",f).val(a.sSearch).attr("placeholder",r.sSearchPlaceholder).on("keyup.DT search.DT input.DT paste.DT cut.DT",h?Zl(d,h):d).on("mouseup.DT",function(_){setTimeout(function(){d.call(b[0],_)},10)}).on("keypress.DT",function(_){if(_.keyCode==13)return!1}).attr("aria-controls",n);return A(e.nTable).on("search.dt.DT",function(_,y){if(e===y)try{b[0]!==document.activeElement&&b.val(a.sSearch)}catch{}}),f[0]}function Za(e,t,n){var r=e.oPreviousSearch,a=e.aoPreSearchCols,o=function(f){r.sSearch=f.sSearch,r.bRegex=f.bRegex,r.bSmart=f.bSmart,r.bCaseInsensitive=f.bCaseInsensitive,r.return=f.return},s=function(f){return f.bEscapeRegex!==void 0?!f.bEscapeRegex:f.bRegex};if(Ul(e),at(e)!="ssp"){Ih(e,t.sSearch,n,s(t),t.bSmart,t.bCaseInsensitive),o(t);for(var u=0;u<a.length;u++)Oh(e,a[u].sSearch,u,s(a[u]),a[u].bSmart,a[u].bCaseInsensitive);Lh(e)}else o(t);e.bFiltered=!0,ke(e,null,"search",[e])}function Lh(e){for(var t=k.ext.search,n=e.aiDisplay,r,a,o=0,s=t.length;o<s;o++){for(var u=[],f=0,d=n.length;f<d;f++)a=n[f],r=e.aoData[a],t[o](e,r._aFilterData,a,r._aData,f)&&u.push(a);n.length=0,A.merge(n,u)}}function Oh(e,t,n,r,a,o){if(t!==""){for(var s,u=[],f=e.aiDisplay,d=Gl(t,r,a,o),h=0;h<f.length;h++)s=e.aoData[f[h]]._aFilterData[n],d.test(s)&&u.push(f[h]);e.aiDisplay=u}}function Ih(e,t,n,r,a,o){var s=Gl(t,r,a,o),u=e.oPreviousSearch.sSearch,f=e.aiDisplayMaster,d,h,b,_=[];if(k.ext.search.length!==0&&(n=!0),h=Rh(e),t.length<=0)e.aiDisplay=f.slice();else{for((h||n||r||u.length>t.length||t.indexOf(u)!==0||e.bSorted)&&(e.aiDisplay=f.slice()),d=e.aiDisplay,b=0;b<d.length;b++)s.test(e.aoData[d[b]]._sFilterRow)&&_.push(d[b]);e.aiDisplay=_}}function Gl(e,t,n,r){if(e=t?e:zl(e),n){var a=A.map(e.match(/["\u201C][^"\u201D]+["\u201D]|[^ ]+/g)||[""],function(o){if(o.charAt(0)==='"'){var s=o.match(/^"(.*)"$/);o=s?s[1]:o}else if(o.charAt(0)==="“"){var s=o.match(/^\u201C(.*)\u201D$/);o=s?s[1]:o}return o.replace('"',"")});e="^(?=.*?"+a.join(")(?=.*?")+").*$"}return new RegExp(e,r?"i":"")}var zl=k.util.escapeRegex,ki=A("<div>")[0],B0=ki.textContent!==void 0;function Rh(e){var t=e.aoColumns,n,r,a,o,s,u,f,d,h=!1;for(r=0,o=e.aoData.length;r<o;r++)if(d=e.aoData[r],!d._aFilterData){for(u=[],a=0,s=t.length;a<s;a++)n=t[a],n.bSearchable?(f=wt(e,r,a,"filter"),f===null&&(f=""),typeof f!="string"&&f.toString&&(f=f.toString())):f="",f.indexOf&&f.indexOf("&")!==-1&&(ki.innerHTML=f,f=B0?ki.textContent:ki.innerText),f.replace&&(f=f.replace(/[\r\n\u2028]/g,"")),u.push(f);d._aFilterData=u,d._sFilterRow=u.join("  "),h=!0}return h}function Qf(e){return{search:e.sSearch,smart:e.bSmart,regex:e.bRegex,caseInsensitive:e.bCaseInsensitive}}function Zf(e){return{sSearch:e.search,bSmart:e.smart,bRegex:e.regex,bCaseInsensitive:e.caseInsensitive}}function Ph(e){var t=e.sTableId,n=e.aanFeatures.i,r=A("<div/>",{class:e.oClasses.sInfo,id:n?null:t+"_info"});return n||(e.aoDrawCallback.push({fn:kh,sName:"information"}),r.attr("role","status").attr("aria-live","polite"),A(e.nTable).attr("aria-describedby",t+"_info")),r[0]}function kh(e){var t=e.aanFeatures.i;if(t.length!==0){var n=e.oLanguage,r=e._iDisplayStart+1,a=e.fnDisplayEnd(),o=e.fnRecordsTotal(),s=e.fnRecordsDisplay(),u=s?n.sInfo:n.sInfoEmpty;s!==o&&(u+=" "+n.sInfoFiltered),u+=n.sInfoPostFix,u=$h(e,u);var f=n.fnInfoCallback;f!==null&&(u=f.call(e.oInstance,e,r,a,o,s,u)),A(t).html(u)}}function $h(e,t){var n=e.fnFormatNumber,r=e._iDisplayStart+1,a=e._iDisplayLength,o=e.fnRecordsDisplay(),s=a===-1;return t.replace(/_START_/g,n.call(e,r)).replace(/_END_/g,n.call(e,e.fnDisplayEnd())).replace(/_MAX_/g,n.call(e,e.fnRecordsTotal())).replace(/_TOTAL_/g,n.call(e,o)).replace(/_PAGE_/g,n.call(e,s?1:Math.ceil(r/a))).replace(/_PAGES_/g,n.call(e,s?1:Math.ceil(o/a)))}function ya(e){var t,n,r=e.iInitDisplayStart,a=e.aoColumns,o,s=e.oFeatures,u=e.bDeferLoading;if(!e.bInitialised){setTimeout(function(){ya(e)},200);return}for(Sh(e),Dh(e),Da(e,e.aoHeader),Da(e,e.aoFooter),St(e,!0),s.bAutoWidth&&Ql(e),t=0,n=a.length;t<n;t++)o=a[t],o.sWidth&&(o.nTh.style.width=Ue(o.sWidth));ke(e,null,"preInit",[e]),rr(e);var f=at(e);(f!="ssp"||u)&&(f=="ajax"?xo(e,[],function(d){var h=Qa(e,d);for(t=0;t<h.length;t++)jn(e,h[t]);e.iInitDisplayStart=r,rr(e),St(e,!1),zi(e,d)}):(St(e,!1),zi(e)))}function zi(e,t){e._bInitComplete=!0,(t||e.oInit.aaData)&&Ya(e),ke(e,null,"plugin-init",[e,t]),ke(e,"aoInitComplete","init",[e,t])}function Jl(e,t){var n=parseInt(t,10);e._iDisplayLength=n,nu(e),ke(e,null,"length",[e,n])}function Fh(e){for(var t=e.oClasses,n=e.sTableId,r=e.aLengthMenu,a=Array.isArray(r[0]),o=a?r[0]:r,s=a?r[1]:r,u=A("<select/>",{name:n+"_length","aria-controls":n,class:t.sLengthSelect}),f=0,d=o.length;f<d;f++)u[0][f]=new Option(typeof s[f]=="number"?e.fnFormatNumber(s[f]):s[f],o[f]);var h=A("<div><label/></div>").addClass(t.sLength);return e.aanFeatures.l||(h[0].id=n+"_length"),h.children().append(e.oLanguage.sLengthMenu.replace("_MENU_",u[0].outerHTML)),A("select",h).val(e._iDisplayLength).on("change.DT",function(b){Jl(e,A(this).val()),Bn(e)}),A(e.nTable).on("length.dt.DT",function(b,_,y){e===_&&A("select",h).val(y)}),h[0]}function Hh(e){var t=e.sPaginationType,n=k.ext.pager[t],r=typeof n=="function",a=function(u){Bn(u)},o=A("<div/>").addClass(e.oClasses.sPaging+t)[0],s=e.aanFeatures;return r||n.fnInit(e,o,a),s.p||(o.id=e.sTableId+"_paginate",e.aoDrawCallback.push({fn:function(u){if(r){var f=u._iDisplayStart,d=u._iDisplayLength,h=u.fnRecordsDisplay(),b=d===-1,_=b?0:Math.ceil(f/d),y=b?1:Math.ceil(h/d),g=n(_,y),S,L;for(S=0,L=s.p.length;S<L;S++)ru(u,"pageButton")(u,s.p[S],S,g,_,y)}else n.fnUpdate(u,a)},sName:"pagination"})),o}function Ao(e,t,n){var r=e._iDisplayStart,a=e._iDisplayLength,o=e.fnRecordsDisplay();o===0||a===-1?r=0:typeof t=="number"?(r=t*a,r>o&&(r=0)):t=="first"?r=0:t=="previous"?(r=a>=0?r-a:0,r<0&&(r=0)):t=="next"?r+a<o&&(r+=a):t=="last"?r=Math.floor((o-1)/a)*a:kt(e,0,"Unknown paging action: "+t,5);var s=e._iDisplayStart!==r;return e._iDisplayStart=r,s?(ke(e,null,"page",[e]),n&&Bn(e)):ke(e,null,"page-nc",[e]),s}function Mh(e){return A("<div/>",{id:e.aanFeatures.r?null:e.sTableId+"_processing",class:e.oClasses.sProcessing,role:"status"}).html(e.oLanguage.sProcessing).append("<div><div></div><div></div><div></div><div></div></div>").insertBefore(e.nTable)[0]}function St(e,t){e.oFeatures.bProcessing&&A(e.aanFeatures.r).css("display",t?"block":"none"),ke(e,null,"processing",[e,t])}function jh(e){var t=A(e.nTable),n=e.oScroll;if(n.sX===""&&n.sY==="")return e.nTable;var r=n.sX,a=n.sY,o=e.oClasses,s=t.children("caption"),u=s.length?s[0]._captionSide:null,f=A(t[0].cloneNode(!1)),d=A(t[0].cloneNode(!1)),h=t.children("tfoot"),b="<div/>",_=function(C){return C?Ue(C):null};h.length||(h=null);var y=A(b,{class:o.sScrollWrapper}).append(A(b,{class:o.sScrollHead}).css({overflow:"hidden",position:"relative",border:0,width:r?_(r):"100%"}).append(A(b,{class:o.sScrollHeadInner}).css({"box-sizing":"content-box",width:n.sXInner||"100%"}).append(f.removeAttr("id").css("margin-left",0).append(u==="top"?s:null).append(t.children("thead"))))).append(A(b,{class:o.sScrollBody}).css({position:"relative",overflow:"auto",width:_(r)}).append(t));h&&y.append(A(b,{class:o.sScrollFoot}).css({overflow:"hidden",border:0,width:r?_(r):"100%"}).append(A(b,{class:o.sScrollFootInner}).append(d.removeAttr("id").css("margin-left",0).append(u==="bottom"?s:null).append(t.children("tfoot")))));var g=y.children(),S=g[0],L=g[1],O=h?g[2]:null;return r&&A(L).on("scroll.DT",function(C){var Y=this.scrollLeft;S.scrollLeft=Y,h&&(O.scrollLeft=Y)}),A(L).css("max-height",a),n.bCollapse||A(L).css("height",a),e.nScrollHead=S,e.nScrollBody=L,e.nScrollFoot=O,e.aoDrawCallback.push({fn:Eo,sName:"scrolling"}),y[0]}function Eo(e){var t=e.oScroll,n=t.sX,r=t.sXInner,a=t.sY,o=t.iBarWidth,s=A(e.nScrollHead),u=s[0].style,f=s.children("div"),d=f[0].style,h=f.children("table"),b=e.nScrollBody,_=A(b),y=b.style,g=A(e.nScrollFoot),S=g.children("div"),L=S.children("table"),O=A(e.nTHead),C=A(e.nTable),Y=C[0],W=Y.style,B=e.nTFoot?A(e.nTFoot):null,Q=e.oBrowser,v=Q.bScrollOversize;yt(e.aoColumns,"nTh");var ee,G,re,ie,pe,se,ae=[],we=[],Te=[],me=[],xe,Le,Fe,ct=function(oe){var _e=oe.style;_e.paddingTop="0",_e.paddingBottom="0",_e.borderTopWidth="0",_e.borderBottomWidth="0",_e.height=0},We=b.scrollHeight>b.clientHeight;if(e.scrollBarVis!==We&&e.scrollBarVis!==void 0){e.scrollBarVis=We,Ya(e);return}else e.scrollBarVis=We;C.children("thead, tfoot").remove(),B&&(se=B.clone().prependTo(C),G=B.find("tr"),ie=se.find("tr"),se.find("[id]").removeAttr("id")),pe=O.clone().prependTo(C),ee=O.find("tr"),re=pe.find("tr"),pe.find("th, td").removeAttr("tabindex"),pe.find("[id]").removeAttr("id"),n||(y.width="100%",s[0].style.width="100%"),A.each(So(e,pe),function(oe,_e){xe=Ga(e,oe),_e.style.width=e.aoColumns[xe].sWidth}),B&&Qt(function(oe){oe.style.width=""},ie),Fe=C.outerWidth(),n===""?(W.width="100%",v&&(C.find("tbody").height()>b.offsetHeight||_.css("overflow-y")=="scroll")&&(W.width=Ue(C.outerWidth()-o)),Fe=C.outerWidth()):r!==""&&(W.width=Ue(r),Fe=C.outerWidth()),Qt(ct,re),Qt(function(oe){var _e=window.getComputedStyle?window.getComputedStyle(oe).width:Ue(A(oe).width());Te.push(oe.innerHTML),ae.push(_e)},re),Qt(function(oe,_e){oe.style.width=ae[_e]},ee),A(re).css("height",0),B&&(Qt(ct,ie),Qt(function(oe){me.push(oe.innerHTML),we.push(Ue(A(oe).css("width")))},ie),Qt(function(oe,_e){oe.style.width=we[_e]},G),A(ie).height(0)),Qt(function(oe,_e){oe.innerHTML='<div class="dataTables_sizing">'+Te[_e]+"</div>",oe.childNodes[0].style.height="0",oe.childNodes[0].style.overflow="hidden",oe.style.width=ae[_e]},re),B&&Qt(function(oe,_e){oe.innerHTML='<div class="dataTables_sizing">'+me[_e]+"</div>",oe.childNodes[0].style.height="0",oe.childNodes[0].style.overflow="hidden",oe.style.width=we[_e]},ie),Math.round(C.outerWidth())<Math.round(Fe)?(Le=b.scrollHeight>b.offsetHeight||_.css("overflow-y")=="scroll"?Fe+o:Fe,v&&(b.scrollHeight>b.offsetHeight||_.css("overflow-y")=="scroll")&&(W.width=Ue(Le-o)),(n===""||r!=="")&&kt(e,1,"Possible column misalignment",6)):Le="100%",y.width=Ue(Le),u.width=Ue(Le),B&&(e.nScrollFoot.style.width=Ue(Le)),a||v&&(y.height=Ue(Y.offsetHeight+o));var ze=C.outerWidth();h[0].style.width=Ue(ze),d.width=Ue(ze);var it=C.height()>b.clientHeight||_.css("overflow-y")=="scroll",tt="padding"+(Q.bScrollbarLeft?"Left":"Right");d[tt]=it?o+"px":"0px",B&&(L[0].style.width=Ue(ze),S[0].style.width=Ue(ze),S[0].style[tt]=it?o+"px":"0px"),C.children("colgroup").insertBefore(C.children("thead")),_.trigger("scroll"),(e.bSorted||e.bFiltered)&&!e._drawHold&&(b.scrollTop=0)}function Qt(e,t,n){for(var r=0,a=0,o=t.length,s,u;a<o;){for(s=t[a].firstChild,u=n?n[a].firstChild:null;s;)s.nodeType===1&&(n?e(s,u,r):e(s,r),r++),s=s.nextSibling,u=n?u.nextSibling:null;a++}}var W0=/<.*?>/g;function Ql(e){var t=e.nTable,n=e.aoColumns,r=e.oScroll,a=r.sY,o=r.sX,s=r.sXInner,u=n.length,f=To(e,"bVisible"),d=A("th",e.nTHead),h=t.getAttribute("width"),b=t.parentNode,_=!1,y,g,S,L=e.oBrowser,O=L.bScrollOversize,C=t.style.width;C&&C.indexOf("%")!==-1&&(h=C);var Y=Bh(yt(n,"sWidthOrig"),b);for(y=0;y<f.length;y++)g=n[f[y]],g.sWidth!==null&&(g.sWidth=Y[y],_=!0);if(O||!_&&!o&&!a&&u==Yr(e)&&u==d.length)for(y=0;y<u;y++){var W=Ga(e,y);W!==null&&(n[W].sWidth=Ue(d.eq(y).width()))}else{var B=A(t).clone().css("visibility","hidden").removeAttr("id");B.find("tbody tr").remove();var Q=A("<tr/>").appendTo(B.find("tbody"));for(B.find("thead, tfoot").remove(),B.append(A(e.nTHead).clone()).append(A(e.nTFoot).clone()),B.find("tfoot th, tfoot td").css("width",""),d=So(e,B.find("thead")[0]),y=0;y<f.length;y++)g=n[f[y]],d[y].style.width=g.sWidthOrig!==null&&g.sWidthOrig!==""?Ue(g.sWidthOrig):"",g.sWidthOrig&&o&&A(d[y]).append(A("<div/>").css({width:g.sWidthOrig,margin:0,padding:0,border:0,height:1}));if(e.aoData.length)for(y=0;y<f.length;y++)S=f[y],g=n[S],A(Wh(e,S)).clone(!1).append(g.sContentPadding).appendTo(Q);A("[name]",B).removeAttr("name");var v=A("<div/>").css(o||a?{position:"absolute",top:0,left:0,height:1,right:0,overflow:"hidden"}:{}).append(B).appendTo(b);o&&s?B.width(s):o?(B.css("width","auto"),B.removeAttr("width"),B.width()<b.clientWidth&&h&&B.width(b.clientWidth)):a?B.width(b.clientWidth):h&&B.width(h);var ee=0;for(y=0;y<f.length;y++){var G=A(d[y]),re=G.outerWidth()-G.width(),ie=L.bBounding?Math.ceil(d[y].getBoundingClientRect().width):G.outerWidth();ee+=ie,n[f[y]].sWidth=Ue(ie-re)}t.style.width=Ue(ee),v.remove()}if(h&&(t.style.width=Ue(h)),(h||o)&&!e._reszEvt){var pe=function(){A(window).on("resize.DT-"+e.sInstance,Zl(function(){Ya(e)}))};O?setTimeout(pe,1e3):pe(),e._reszEvt=!0}}var Zl=k.util.throttle;function Bh(e,t){for(var n=[],r=[],a=0;a<e.length;a++)e[a]?n.push(A("<div/>").css("width",Ue(e[a])).appendTo(t||document.body)):n.push(null);for(var a=0;a<e.length;a++)r.push(n[a]?n[a][0].offsetWidth:null);return A(n).remove(),r}function Wh(e,t){var n=Vh(e,t);if(n<0)return null;var r=e.aoData[n];return r.nTr?r.anCells[t]:A("<td/>").html(wt(e,n,t,"display"))[0]}function Vh(e,t){for(var n,r=-1,a=-1,o=0,s=e.aoData.length;o<s;o++)n=wt(e,o,t,"display")+"",n=n.replace(W0,""),n=n.replace(/&nbsp;/g," "),n.length>r&&(r=n.length,a=o);return a}function Ue(e){return e===null?"0px":typeof e=="number"?e<0?"0px":e+"px":e.match(/\d$/)?e+"px":e}function Gr(e){var t,n,r,a=[],o=e.aoColumns,s,u,f,d,h=e.aaSortingFixed,b=A.isPlainObject(h),_=[],y=function(g){g.length&&!Array.isArray(g[0])?_.push(g):A.merge(_,g)};for(Array.isArray(h)&&y(h),b&&h.pre&&y(h.pre),y(e.aaSorting),b&&h.post&&y(h.post),t=0;t<_.length;t++)for(d=_[t][0],s=o[d].aDataSort,n=0,r=s.length;n<r;n++)u=s[n],f=o[u].sType||"string",_[t]._idx===void 0&&(_[t]._idx=A.inArray(_[t][1],o[u].asSorting)),a.push({src:d,col:u,dir:_[t][1],index:_[t]._idx,type:f,formatter:k.ext.type.order[f+"-pre"]});return a}function qh(e){var t,n,r,a=[],o=k.ext.type.order,s=e.aoData;e.aoColumns;var u=0,f,d=e.aiDisplayMaster,h;for(Ul(e),h=Gr(e),t=0,n=h.length;t<n;t++)f=h[t],f.formatter&&u++,Xh(e,f.col);if(at(e)!="ssp"&&h.length!==0){for(t=0,r=d.length;t<r;t++)a[d[t]]=t;u===h.length?d.sort(function(b,_){var y,g,S,L,O,C=h.length,Y=s[b]._aSortData,W=s[_]._aSortData;for(S=0;S<C;S++)if(O=h[S],y=Y[O.col],g=W[O.col],L=y<g?-1:y>g?1:0,L!==0)return O.dir==="asc"?L:-L;return y=a[b],g=a[_],y<g?-1:y>g?1:0}):d.sort(function(b,_){var y,g,S,L,O,C,Y=h.length,W=s[b]._aSortData,B=s[_]._aSortData;for(S=0;S<Y;S++)if(O=h[S],y=W[O.col],g=B[O.col],C=o[O.type+"-"+O.dir]||o["string-"+O.dir],L=C(y,g),L!==0)return L;return y=a[b],g=a[_],y<g?-1:y>g?1:0})}e.bSorted=!0}function Uh(e){for(var t,n,r=e.aoColumns,a=Gr(e),o=e.oLanguage.oAria,s=0,u=r.length;s<u;s++){var f=r[s],d=f.asSorting,h=f.ariaTitle||f.sTitle.replace(/<.*?>/g,""),b=f.nTh;b.removeAttribute("aria-sort"),f.bSortable?(a.length>0&&a[0].col==s?(b.setAttribute("aria-sort",a[0].dir=="asc"?"ascending":"descending"),n=d[a[0].index+1]||d[0]):n=d[0],t=h+(n==="asc"?o.sSortAscending:o.sSortDescending)):t=h,b.setAttribute("aria-label",t)}}function Qs(e,t,n,r){var a=e.aoColumns[t],o=e.aaSorting,s=a.asSorting,u,f=function(h,b){var _=h._idx;return _===void 0&&(_=A.inArray(h[1],s)),_+1<s.length?_+1:b?null:0};if(typeof o[0]=="number"&&(o=e.aaSorting=[o]),n&&e.oFeatures.bSortMulti){var d=A.inArray(t,yt(o,"0"));d!==-1?(u=f(o[d],!0),u===null&&o.length===1&&(u=0),u===null?o.splice(d,1):(o[d][1]=s[u],o[d]._idx=u)):(o.push([t,s[0],0]),o[o.length-1]._idx=0)}else o.length&&o[0][0]==t?(u=f(o[0]),o.length=1,o[0][1]=s[u],o[0]._idx=u):(o.length=0,o.push([t,s[0]]),o[0]._idx=0);rr(e),typeof r=="function"&&r(e)}function eu(e,t,n,r){var a=e.aoColumns[n];tu(t,{},function(o){a.bSortable!==!1&&(e.oFeatures.bProcessing?(St(e,!0),setTimeout(function(){Qs(e,n,o.shiftKey,r),at(e)!=="ssp"&&St(e,!1)},0)):Qs(e,n,o.shiftKey,r))})}function Ji(e){var t=e.aLastSort,n=e.oClasses.sSortColumn,r=Gr(e),a=e.oFeatures,o,s,u;if(a.bSort&&a.bSortClasses){for(o=0,s=t.length;o<s;o++)u=t[o].src,A(yt(e.aoData,"anCells",u)).removeClass(n+(o<2?o+1:3));for(o=0,s=r.length;o<s;o++)u=r[o].src,A(yt(e.aoData,"anCells",u)).addClass(n+(o<2?o+1:3))}e.aLastSort=r}function Xh(e,t){var n=e.aoColumns[t],r=k.ext.order[n.sSortDataType],a;r&&(a=r.call(e.oInstance,e,t,za(e,t)));for(var o,s,u=k.ext.type.order[n.sType+"-pre"],f=0,d=e.aoData.length;f<d;f++)o=e.aoData[f],o._aSortData||(o._aSortData=[]),(!o._aSortData[t]||r)&&(s=r?a[f]:wt(e,f,t,"sort"),o._aSortData[t]=u?u(s):s)}function ei(e){if(!e._bLoadingState){var t={time:+new Date,start:e._iDisplayStart,length:e._iDisplayLength,order:A.extend(!0,[],e.aaSorting),search:Qf(e.oPreviousSearch),columns:A.map(e.aoColumns,function(n,r){return{visible:n.bVisible,search:Qf(e.aoPreSearchCols[r])}})};e.oSavedState=t,ke(e,"aoStateSaveParams","stateSaveParams",[e,t]),e.oFeatures.bStateSave&&!e.bDestroying&&e.fnStateSaveCallback.call(e.oInstance,e,t)}}function Kh(e,t,n){if(!e.oFeatures.bStateSave){n();return}var r=function(o){Zs(e,o,n)},a=e.fnStateLoadCallback.call(e.oInstance,e,r);return a!==void 0&&Zs(e,a,n),!0}function Zs(e,t,n){var r,a,o=e.aoColumns;e._bLoadingState=!0;var s=e._bInitComplete?new k.Api(e):null;if(!t||!t.time){e._bLoadingState=!1,n();return}var u=ke(e,"aoStateLoadParams","stateLoadParams",[e,t]);if(A.inArray(!1,u)!==-1){e._bLoadingState=!1,n();return}var f=e.iStateDuration;if(f>0&&t.time<+new Date-f*1e3){e._bLoadingState=!1,n();return}if(t.columns&&o.length!==t.columns.length){e._bLoadingState=!1,n();return}if(e.oLoadedState=A.extend(!0,{},t),t.length!==void 0&&(s?s.page.len(t.length):e._iDisplayLength=t.length),t.start!==void 0&&(s===null?(e._iDisplayStart=t.start,e.iInitDisplayStart=t.start):Ao(e,t.start/e._iDisplayLength)),t.order!==void 0&&(e.aaSorting=[],A.each(t.order,function(h,b){e.aaSorting.push(b[0]>=o.length?[0,b[1]]:b)})),t.search!==void 0&&A.extend(e.oPreviousSearch,Zf(t.search)),t.columns){for(r=0,a=t.columns.length;r<a;r++){var d=t.columns[r];d.visible!==void 0&&(s?s.column(r).visible(d.visible,!1):o[r].bVisible=d.visible),d.search!==void 0&&A.extend(e.aoPreSearchCols[r],Zf(d.search))}s&&s.columns.adjust()}e._bLoadingState=!1,ke(e,"aoStateLoaded","stateLoaded",[e,t]),n()}function Qi(e){var t=k.settings,n=A.inArray(e,yt(t,"nTable"));return n!==-1?t[n]:null}function kt(e,t,n,r){if(n="DataTables warning: "+(e?"table id="+e.sTableId+" - ":"")+n,r&&(n+=". For more information about this error, please see https://datatables.net/tn/"+r),t)window.console&&console.log&&console.log(n);else{var a=k.ext,o=a.sErrMode||a.errMode;if(e&&ke(e,null,"error",[e,r,n]),o=="alert")alert(n);else{if(o=="throw")throw new Error(n);typeof o=="function"&&o(e,r,n)}}}function Vt(e,t,n,r){if(Array.isArray(n)){A.each(n,function(a,o){Array.isArray(o)?Vt(e,t,o[0],o[1]):Vt(e,t,o)});return}r===void 0&&(r=n),t[n]!==void 0&&(e[r]=t[n])}function el(e,t,n){var r;for(var a in t)t.hasOwnProperty(a)&&(r=t[a],A.isPlainObject(r)?(A.isPlainObject(e[a])||(e[a]={}),A.extend(!0,e[a],r)):n&&a!=="data"&&a!=="aaData"&&Array.isArray(r)?e[a]=r.slice():e[a]=r);return e}function tu(e,t,n){A(e).on("click.DT",t,function(r){A(e).trigger("blur"),n(r)}).on("keypress.DT",t,function(r){r.which===13&&(r.preventDefault(),n(r))}).on("selectstart.DT",function(){return!1})}function lt(e,t,n,r){n&&e[t].push({fn:n,sName:r})}function ke(e,t,n,r){var a=[];if(t&&(a=A.map(e[t].slice().reverse(),function(u,f){return u.fn.apply(e.oInstance,r)})),n!==null){var o=A.Event(n+".dt"),s=A(e.nTable);s.trigger(o,r),s.parents("body").length===0&&A("body").trigger(o,r),a.push(o.result)}return a}function nu(e){var t=e._iDisplayStart,n=e.fnDisplayEnd(),r=e._iDisplayLength;t>=n&&(t=n-r),t-=t%r,(r===-1||t<0)&&(t=0),e._iDisplayStart=t}function ru(e,t){var n=e.renderer,r=k.ext.renderer[t];return A.isPlainObject(n)&&n[t]?r[n[t]]||r._:typeof n=="string"&&r[n]||r._}function at(e){return e.oFeatures.bServerSide?"ssp":e.ajax||e.sAjaxSource?"ajax":"dom"}var Yh=[],Qe=Array.prototype,V0=function(e){var t,n,r=k.settings,a=A.map(r,function(o,s){return o.nTable});if(e){if(e.nTable&&e.oApi)return[e];if(e.nodeName&&e.nodeName.toLowerCase()==="table")return t=A.inArray(e,a),t!==-1?[r[t]]:null;if(e&&typeof e.settings=="function")return e.settings().toArray();typeof e=="string"?n=A(e):e instanceof A&&(n=e)}else return[];if(n)return n.map(function(o){return t=A.inArray(this,a),t!==-1?r[t]:null}).toArray()};Ne=function(e,t){if(!(this instanceof Ne))return new Ne(e,t);var n=[],r=function(s){var u=V0(s);u&&n.push.apply(n,u)};if(Array.isArray(e))for(var a=0,o=e.length;a<o;a++)r(e[a]);else r(e);this.context=wo(n),t&&A.merge(this,t),this.selector={rows:null,cols:null,opts:null},Ne.extend(this,this,Yh)};k.Api=Ne;A.extend(Ne.prototype,{any:function(){return this.count()!==0},concat:Qe.concat,context:[],count:function(){return this.flatten().length},each:function(e){for(var t=0,n=this.length;t<n;t++)e.call(this,this[t],t,this);return this},eq:function(e){var t=this.context;return t.length>e?new Ne(t[e],this[e]):null},filter:function(e){var t=[];if(Qe.filter)t=Qe.filter.call(this,e,this);else for(var n=0,r=this.length;n<r;n++)e.call(this,this[n],n,this)&&t.push(this[n]);return new Ne(this.context,t)},flatten:function(){var e=[];return new Ne(this.context,e.concat.apply(e,this.toArray()))},join:Qe.join,indexOf:Qe.indexOf||function(e,t){for(var n=t||0,r=this.length;n<r;n++)if(this[n]===e)return n;return-1},iterator:function(e,t,n,r){var a=[],o,s,u,f,d,h=this.context,b,_,y,g=this.selector;for(typeof e=="string"&&(r=n,n=t,t=e,e=!1),s=0,u=h.length;s<u;s++){var S=new Ne(h[s]);if(t==="table")o=n.call(S,h[s],s),o!==void 0&&a.push(o);else if(t==="columns"||t==="rows")o=n.call(S,h[s],this[s],s),o!==void 0&&a.push(o);else if(t==="column"||t==="column-rows"||t==="row"||t==="cell")for(_=this[s],t==="column-rows"&&(b=No(h[s],g.opts)),f=0,d=_.length;f<d;f++)y=_[f],t==="cell"?o=n.call(S,h[s],y.row,y.column,s,f):o=n.call(S,h[s],y,s,f,b),o!==void 0&&a.push(o)}if(a.length||r){var L=new Ne(h,e?a.concat.apply([],a):a),O=L.selector;return O.rows=g.rows,O.cols=g.cols,O.opts=g.opts,L}return this},lastIndexOf:Qe.lastIndexOf||function(e,t){return this.indexOf.apply(this.toArray.reverse(),arguments)},length:0,map:function(e){var t=[];if(Qe.map)t=Qe.map.call(this,e,this);else for(var n=0,r=this.length;n<r;n++)t.push(e.call(this,this[n],n));return new Ne(this.context,t)},pluck:function(e){var t=k.util.get(e);return this.map(function(n){return t(n)})},pop:Qe.pop,push:Qe.push,reduce:Qe.reduce||function(e,t){return Jf(this,e,t,0,this.length,1)},reduceRight:Qe.reduceRight||function(e,t){return Jf(this,e,t,this.length-1,-1,-1)},reverse:Qe.reverse,selector:null,shift:Qe.shift,slice:function(){return new Ne(this.context,this)},sort:Qe.sort,splice:Qe.splice,toArray:function(){return Qe.slice.call(this)},to$:function(){return A(this)},toJQuery:function(){return A(this)},unique:function(){return new Ne(this.context,wo(this))},unshift:Qe.unshift});Ne.extend=function(e,t,n){if(!(!n.length||!t||!(t instanceof Ne)&&!t.__dt_wrapper)){var r,a,o,s=function(u,f,d){return function(){var h=f.apply(u,arguments);return Ne.extend(h,h,d.methodExt),h}};for(r=0,a=n.length;r<a;r++)o=n[r],t[o.name]=o.type==="function"?s(e,o.val,o):o.type==="object"?{}:o.val,t[o.name].__dt_wrapper=!0,Ne.extend(e,t[o.name],o.propExt)}};Ne.register=he=function(e,t){if(Array.isArray(e)){for(var n=0,r=e.length;n<r;n++)Ne.register(e[n],t);return}var a,o,s=e.split("."),u=Yh,f,d,h=function(_,y){for(var g=0,S=_.length;g<S;g++)if(_[g].name===y)return _[g];return null};for(a=0,o=s.length;a<o;a++){d=s[a].indexOf("()")!==-1,f=d?s[a].replace("()",""):s[a];var b=h(u,f);b||(b={name:f,val:{},methodExt:[],propExt:[],type:"object"},u.push(b)),a===o-1?(b.val=t,b.type=typeof t=="function"?"function":A.isPlainObject(t)?"object":"other"):u=d?b.methodExt:b.propExt}};Ne.registerPlural=je=function(e,t,n){Ne.register(e,n),Ne.register(t,function(){var r=n.apply(this,arguments);return r===this?this:r instanceof Ne?r.length?Array.isArray(r[0])?new Ne(r.context,r[0]):r[0]:void 0:r})};var Gh=function(e,t){if(Array.isArray(e))return A.map(e,function(r){return Gh(r,t)});if(typeof e=="number")return[t[e]];var n=A.map(t,function(r,a){return r.nTable});return A(n).filter(e).map(function(r){var a=A.inArray(this,n);return t[a]}).toArray()};he("tables()",function(e){return e!=null?new Ne(Gh(e,this.context)):this});he("table()",function(e){var t=this.tables(e),n=t.context;return n.length?new Ne(n[0]):t});je("tables().nodes()","table().node()",function(){return this.iterator("table",function(e){return e.nTable},1)});je("tables().body()","table().body()",function(){return this.iterator("table",function(e){return e.nTBody},1)});je("tables().header()","table().header()",function(){return this.iterator("table",function(e){return e.nTHead},1)});je("tables().footer()","table().footer()",function(){return this.iterator("table",function(e){return e.nTFoot},1)});je("tables().containers()","table().container()",function(){return this.iterator("table",function(e){return e.nTableWrapper},1)});he("draw()",function(e){return this.iterator("table",function(t){e==="page"?Bn(t):(typeof e=="string"&&(e=e!=="full-hold"),rr(t,e===!1))})});he("page()",function(e){return e===void 0?this.page.info().page:this.iterator("table",function(t){Ao(t,e)})});he("page.info()",function(e){if(this.context.length!==0){var t=this.context[0],n=t._iDisplayStart,r=t.oFeatures.bPaginate?t._iDisplayLength:-1,a=t.fnRecordsDisplay(),o=r===-1;return{page:o?0:Math.floor(n/r),pages:o?1:Math.ceil(a/r),start:n,end:t.fnDisplayEnd(),length:r,recordsTotal:t.fnRecordsTotal(),recordsDisplay:a,serverSide:at(t)==="ssp"}}});he("page.len()",function(e){return e===void 0?this.context.length!==0?this.context[0]._iDisplayLength:void 0:this.iterator("table",function(t){Jl(t,e)})});var zh=function(e,t,n){if(n){var r=new Ne(e);r.one("draw",function(){n(r.ajax.json())})}if(at(e)=="ssp")rr(e,t);else{St(e,!0);var a=e.jqXHR;a&&a.readyState!==4&&a.abort(),xo(e,[],function(o){Do(e);for(var s=Qa(e,o),u=0,f=s.length;u<f;u++)jn(e,s[u]);rr(e,t),St(e,!1)})}};he("ajax.json()",function(){var e=this.context;if(e.length>0)return e[0].json});he("ajax.params()",function(){var e=this.context;if(e.length>0)return e[0].oAjaxData});he("ajax.reload()",function(e,t){return this.iterator("table",function(n){zh(n,t===!1,e)})});he("ajax.url()",function(e){var t=this.context;return e===void 0?t.length===0?void 0:(t=t[0],t.ajax?A.isPlainObject(t.ajax)?t.ajax.url:t.ajax:t.sAjaxSource):this.iterator("table",function(n){A.isPlainObject(n.ajax)?n.ajax.url=e:n.ajax=e})});he("ajax.url().load()",function(e,t){return this.iterator("table",function(n){zh(n,t===!1,e)})});var au=function(e,t,n,r,a){var o=[],s,u,f,d,h,b,_=typeof t;for((!t||_==="string"||_==="function"||t.length===void 0)&&(t=[t]),f=0,d=t.length;f<d;f++)for(u=t[f]&&t[f].split&&!t[f].match(/[\[\(:]/)?t[f].split(","):[t[f]],h=0,b=u.length;h<b;h++)s=n(typeof u[h]=="string"?u[h].trim():u[h]),s&&s.length&&(o=o.concat(s));var y=Ke.selector[e];if(y.length)for(f=0,d=y.length;f<d;f++)o=y[f](r,a,o);return wo(o)},iu=function(e){return e||(e={}),e.filter&&e.search===void 0&&(e.search=e.filter),A.extend({search:"none",order:"current",page:"all"},e)},ou=function(e){for(var t=0,n=e.length;t<n;t++)if(e[t].length>0)return e[0]=e[t],e[0].length=1,e.length=1,e.context=[e.context[t]],e;return e.length=0,e},No=function(e,t){var n,r,a,o=[],s=e.aiDisplay,u=e.aiDisplayMaster,f=t.search,d=t.order,h=t.page;if(at(e)=="ssp")return f==="removed"?[]:Or(0,u.length);if(h=="current")for(n=e._iDisplayStart,r=e.fnDisplayEnd();n<r;n++)o.push(s[n]);else if(d=="current"||d=="applied"){if(f=="none")o=u.slice();else if(f=="applied")o=s.slice();else if(f=="removed"){for(var b={},n=0,r=s.length;n<r;n++)b[s[n]]=null;o=A.map(u,function(_){return b.hasOwnProperty(_)?null:_})}}else if(d=="index"||d=="original")for(n=0,r=e.aoData.length;n<r;n++)f=="none"?o.push(n):(a=A.inArray(n,s),(a===-1&&f=="removed"||a>=0&&f=="applied")&&o.push(n));return o},q0=function(e,t,n){var r,a=function(o){var s=vh(o),u=e.aoData;if(s!==null&&!n)return[s];if(r||(r=No(e,n)),s!==null&&A.inArray(s,r)!==-1)return[s];if(o==null||o==="")return r;if(typeof o=="function")return A.map(r,function(y){var g=u[y];return o(y,g._aData,g.nTr)?y:null});if(o.nodeName){var f=o._DT_RowIndex,d=o._DT_CellIndex;if(f!==void 0)return u[f]&&u[f].nTr===o?[f]:[];if(d)return u[d.row]&&u[d.row].nTr===o.parentNode?[d.row]:[];var h=A(o).closest("*[data-dt-row]");return h.length?[h.data("dt-row")]:[]}if(typeof o=="string"&&o.charAt(0)==="#"){var b=e.aIds[o.replace(/^#/,"")];if(b!==void 0)return[b.idx]}var _=bh(Xa(e.aoData,r,"nTr"));return A(_).filter(o).map(function(){return this._DT_RowIndex}).toArray()};return au("row",t,a,e,n)};he("rows()",function(e,t){e===void 0?e="":A.isPlainObject(e)&&(t=e,e=""),t=iu(t);var n=this.iterator("table",function(r){return q0(r,e,t)},1);return n.selector.rows=e,n.selector.opts=t,n});he("rows().nodes()",function(){return this.iterator("row",function(e,t){return e.aoData[t].nTr||void 0},1)});he("rows().data()",function(){return this.iterator(!0,"rows",function(e,t){return Xa(e.aoData,t,"_aData")},1)});je("rows().cache()","row().cache()",function(e){return this.iterator("row",function(t,n){var r=t.aoData[n];return e==="search"?r._aFilterData:r._aSortData},1)});je("rows().invalidate()","row().invalidate()",function(e){return this.iterator("row",function(t,n){Ja(t,n,e)})});je("rows().indexes()","row().index()",function(){return this.iterator("row",function(e,t){return t},1)});je("rows().ids()","row().id()",function(e){for(var t=[],n=this.context,r=0,a=n.length;r<a;r++)for(var o=0,s=this[r].length;o<s;o++){var u=n[r].rowIdFn(n[r].aoData[this[r][o]]._aData);t.push((e===!0?"#":"")+u)}return new Ne(n,t)});je("rows().remove()","row().remove()",function(){var e=this;return this.iterator("row",function(t,n,r){var a=t.aoData,o=a[n],s,u,f,d,h,b;for(a.splice(n,1),s=0,u=a.length;s<u;s++)if(h=a[s],b=h.anCells,h.nTr!==null&&(h.nTr._DT_RowIndex=s),b!==null)for(f=0,d=b.length;f<d;f++)b[f]._DT_CellIndex.row=s;Pi(t.aiDisplayMaster,n),Pi(t.aiDisplay,n),Pi(e[r],n,!1),t._iRecordsDisplay>0&&t._iRecordsDisplay--,nu(t);var _=t.rowIdFn(o._aData);_!==void 0&&delete t.aIds[_]}),this.iterator("table",function(t){for(var n=0,r=t.aoData.length;n<r;n++)t.aoData[n].idx=n}),this});he("rows.add()",function(e){var t=this.iterator("table",function(r){var a,o,s,u=[];for(o=0,s=e.length;o<s;o++)a=e[o],a.nodeName&&a.nodeName.toUpperCase()==="TR"?u.push(Co(r,a)[0]):u.push(jn(r,a));return u},1),n=this.rows(-1);return n.pop(),A.merge(n,t),n});he("row()",function(e,t){return ou(this.rows(e,t))});he("row().data()",function(e){var t=this.context;if(e===void 0)return t.length&&this.length?t[0].aoData[this[0]]._aData:void 0;var n=t[0].aoData[this[0]];return n._aData=e,Array.isArray(e)&&n.nTr&&n.nTr.id&&In(t[0].rowId)(e,n.nTr.id),Ja(t[0],this[0],"data"),this});he("row().node()",function(){var e=this.context;return e.length&&this.length&&e[0].aoData[this[0]].nTr||null});he("row.add()",function(e){e instanceof A&&e.length&&(e=e[0]);var t=this.iterator("table",function(n){return e.nodeName&&e.nodeName.toUpperCase()==="TR"?Co(n,e)[0]:jn(n,e)});return this.row(t[0])});A(document).on("plugin-init.dt",function(e,t){var n=new Ne(t),r="on-plugin-init",a="stateSaveParams."+r,o="destroy. "+r;n.on(a,function(u,f,d){for(var h=f.rowIdFn,b=f.aoData,_=[],y=0;y<b.length;y++)b[y]._detailsShow&&_.push("#"+h(b[y]._aData));d.childRows=_}),n.on(o,function(){n.off(a+" "+o)});var s=n.state.loaded();s&&s.childRows&&n.rows(A.map(s.childRows,function(u){return u.replace(/:/g,"\\:")})).every(function(){ke(t,null,"requestChild",[this])})});var U0=function(e,t,n,r){var a=[],o=function(s,u){if(Array.isArray(s)||s instanceof A){for(var f=0,d=s.length;f<d;f++)o(s[f],u);return}if(s.nodeName&&s.nodeName.toLowerCase()==="tr")a.push(s);else{var h=A("<tr><td></td></tr>").addClass(u);A("td",h).addClass(u).html(s)[0].colSpan=Yr(e),a.push(h[0])}};o(n,r),t._details&&t._details.detach(),t._details=A(a),t._detailsShow&&t._details.insertAfter(t.nTr)},Jh=k.util.throttle(function(e){ei(e[0])},500),su=function(e,t){var n=e.context;if(n.length){var r=n[0].aoData[t!==void 0?t:e[0]];r&&r._details&&(r._details.remove(),r._detailsShow=void 0,r._details=void 0,A(r.nTr).removeClass("dt-hasChild"),Jh(n))}},Qh=function(e,t){var n=e.context;if(n.length&&e.length){var r=n[0].aoData[e[0]];r._details&&(r._detailsShow=t,t?(r._details.insertAfter(r.nTr),A(r.nTr).addClass("dt-hasChild")):(r._details.detach(),A(r.nTr).removeClass("dt-hasChild")),ke(n[0],null,"childRow",[t,e.row(e[0])]),X0(n[0]),Jh(n))}},X0=function(e){var t=new Ne(e),n=".dt.DT_details",r="draw"+n,a="column-sizing"+n,o="destroy"+n,s=e.aoData;t.off(r+" "+a+" "+o),yt(s,"_details").length>0&&(t.on(r,function(u,f){e===f&&t.rows({page:"current"}).eq(0).each(function(d){var h=s[d];h._detailsShow&&h._details.insertAfter(h.nTr)})}),t.on(a,function(u,f,d,h){if(e===f)for(var b,_=Yr(f),y=0,g=s.length;y<g;y++)b=s[y],b._details&&b._details.each(function(){var S=A(this).children("td");S.length==1&&S.attr("colspan",_)})}),t.on(o,function(u,f){if(e===f)for(var d=0,h=s.length;d<h;d++)s[d]._details&&su(t,d)}))},K0="",ti=K0+"row().child",Lo=ti+"()";he(Lo,function(e,t){var n=this.context;return e===void 0?n.length&&this.length?n[0].aoData[this[0]]._details:void 0:(e===!0?this.child.show():e===!1?su(this):n.length&&this.length&&U0(n[0],n[0].aoData[this[0]],e,t),this)});he([ti+".show()",Lo+".show()"],function(e){return Qh(this,!0),this});he([ti+".hide()",Lo+".hide()"],function(){return Qh(this,!1),this});he([ti+".remove()",Lo+".remove()"],function(){return su(this),this});he(ti+".isShown()",function(){var e=this.context;return e.length&&this.length&&e[0].aoData[this[0]]._detailsShow||!1});var Y0=/^([^:]+):(name|visIdx|visible)$/,Zh=function(e,t,n,r,a){for(var o=[],s=0,u=a.length;s<u;s++)o.push(wt(e,a[s],t));return o},G0=function(e,t,n){var r=e.aoColumns,a=yt(r,"sName"),o=yt(r,"nTh"),s=function(u){var f=vh(u);if(u==="")return Or(r.length);if(f!==null)return[f>=0?f:r.length+f];if(typeof u=="function"){var d=No(e,n);return A.map(r,function(S,L){return u(L,Zh(e,L,0,0,d),o[L])?L:null})}var h=typeof u=="string"?u.match(Y0):"";if(h)switch(h[2]){case"visIdx":case"visible":var b=parseInt(h[1],10);if(b<0){var _=A.map(r,function(S,L){return S.bVisible?L:null});return[_[_.length+b]]}return[Ga(e,b)];case"name":return A.map(a,function(S,L){return S===h[1]?L:null});default:return[]}if(u.nodeName&&u._DT_CellIndex)return[u._DT_CellIndex.column];var y=A(o).filter(u).map(function(){return A.inArray(this,o)}).toArray();if(y.length||!u.nodeName)return y;var g=A(u).closest("*[data-dt-column]");return g.length?[g.data("dt-column")]:[]};return au("column",t,s,e,n)},z0=function(e,t,n){var r=e.aoColumns,a=r[t],o=e.aoData,s,u,f,d;if(n===void 0)return a.bVisible;if(a.bVisible!==n){if(n){var h=A.inArray(!0,yt(r,"bVisible"),t+1);for(u=0,f=o.length;u<f;u++)d=o[u].nTr,s=o[u].anCells,d&&d.insertBefore(s[t],s[h]||null)}else A(yt(e.aoData,"anCells",t)).detach();a.bVisible=n}};he("columns()",function(e,t){e===void 0?e="":A.isPlainObject(e)&&(t=e,e=""),t=iu(t);var n=this.iterator("table",function(r){return G0(r,e,t)},1);return n.selector.cols=e,n.selector.opts=t,n});je("columns().header()","column().header()",function(e,t){return this.iterator("column",function(n,r){return n.aoColumns[r].nTh},1)});je("columns().footer()","column().footer()",function(e,t){return this.iterator("column",function(n,r){return n.aoColumns[r].nTf},1)});je("columns().data()","column().data()",function(){return this.iterator("column-rows",Zh,1)});je("columns().dataSrc()","column().dataSrc()",function(){return this.iterator("column",function(e,t){return e.aoColumns[t].mData},1)});je("columns().cache()","column().cache()",function(e){return this.iterator("column-rows",function(t,n,r,a,o){return Xa(t.aoData,o,e==="search"?"_aFilterData":"_aSortData",n)},1)});je("columns().nodes()","column().nodes()",function(){return this.iterator("column-rows",function(e,t,n,r,a){return Xa(e.aoData,a,"anCells",t)},1)});je("columns().visible()","column().visible()",function(e,t){var n=this,r=this.iterator("column",function(a,o){if(e===void 0)return a.aoColumns[o].bVisible;z0(a,o,e)});return e!==void 0&&this.iterator("table",function(a){Da(a,a.aoHeader),Da(a,a.aoFooter),a.aiDisplay.length||A(a.nTBody).find("td[colspan]").attr("colspan",Yr(a)),ei(a),n.iterator("column",function(o,s){ke(o,null,"column-visibility",[o,s,e,t])}),(t===void 0||t)&&n.columns.adjust()}),r});je("columns().indexes()","column().index()",function(e){return this.iterator("column",function(t,n){return e==="visible"?za(t,n):n},1)});he("columns.adjust()",function(){return this.iterator("table",function(e){Ya(e)},1)});he("column.index()",function(e,t){if(this.context.length!==0){var n=this.context[0];if(e==="fromVisible"||e==="toData")return Ga(n,t);if(e==="fromData"||e==="toVisible")return za(n,t)}});he("column()",function(e,t){return ou(this.columns(e,t))});var J0=function(e,t,n){var r=e.aoData,a=No(e,n),o=bh(Xa(r,a,"anCells")),s=A(_h([],o)),u,f=e.aoColumns.length,d,h,b,_,y,g,S=function(L){var O=typeof L=="function";if(L==null||O){for(d=[],h=0,b=a.length;h<b;h++)for(u=a[h],_=0;_<f;_++)y={row:u,column:_},O?(g=r[u],L(y,wt(e,u,_),g.anCells?g.anCells[_]:null)&&d.push(y)):d.push(y);return d}if(A.isPlainObject(L))return L.column!==void 0&&L.row!==void 0&&A.inArray(L.row,a)!==-1?[L]:[];var C=s.filter(L).map(function(Y,W){return{row:W._DT_CellIndex.row,column:W._DT_CellIndex.column}}).toArray();return C.length||!L.nodeName?C:(g=A(L).closest("*[data-dt-row]"),g.length?[{row:g.data("dt-row"),column:g.data("dt-column")}]:[])};return au("cell",t,S,e,n)};he("cells()",function(e,t,n){if(A.isPlainObject(e)&&(e.row===void 0?(n=e,e=null):(n=t,t=null)),A.isPlainObject(t)&&(n=t,t=null),t==null)return this.iterator("table",function(_){return J0(_,e,iu(n))});var r=n?{page:n.page,order:n.order,search:n.search}:{},a=this.columns(t,r),o=this.rows(e,r),s,u,f,d,h=this.iterator("table",function(_,y){var g=[];for(s=0,u=o[y].length;s<u;s++)for(f=0,d=a[y].length;f<d;f++)g.push({row:o[y][s],column:a[y][f]});return g},1),b=n&&n.selected?this.cells(h,n):h;return A.extend(b.selector,{cols:t,rows:e,opts:n}),b});je("cells().nodes()","cell().node()",function(){return this.iterator("cell",function(e,t,n){var r=e.aoData[t];return r&&r.anCells?r.anCells[n]:void 0},1)});he("cells().data()",function(){return this.iterator("cell",function(e,t,n){return wt(e,t,n)},1)});je("cells().cache()","cell().cache()",function(e){return e=e==="search"?"_aFilterData":"_aSortData",this.iterator("cell",function(t,n,r){return t.aoData[n][e][r]},1)});je("cells().render()","cell().render()",function(e){return this.iterator("cell",function(t,n,r){return wt(t,n,r,e)},1)});je("cells().indexes()","cell().index()",function(){return this.iterator("cell",function(e,t,n){return{row:t,column:n,columnVisible:za(e,n)}},1)});je("cells().invalidate()","cell().invalidate()",function(e){return this.iterator("cell",function(t,n,r){Ja(t,n,e,r)})});he("cell()",function(e,t,n){return ou(this.cells(e,t,n))});he("cell().data()",function(e){var t=this.context,n=this[0];return e===void 0?t.length&&n.length?wt(t[0],n[0].row,n[0].column):void 0:(Ch(t[0],n[0].row,n[0].column,e),Ja(t[0],n[0].row,"data",n[0].column),this)});he("order()",function(e,t){var n=this.context;return e===void 0?n.length!==0?n[0].aaSorting:void 0:(typeof e=="number"?e=[[e,t]]:e.length&&!Array.isArray(e[0])&&(e=Array.prototype.slice.call(arguments)),this.iterator("table",function(r){r.aaSorting=e.slice()}))});he("order.listener()",function(e,t,n){return this.iterator("table",function(r){eu(r,e,t,n)})});he("order.fixed()",function(e){if(!e){var t=this.context,n=t.length?t[0].aaSortingFixed:void 0;return Array.isArray(n)?{pre:n}:n}return this.iterator("table",function(r){r.aaSortingFixed=A.extend(!0,{},e)})});he(["columns().order()","column().order()"],function(e){var t=this;return this.iterator("table",function(n,r){var a=[];A.each(t[r],function(o,s){a.push([s,e])}),n.aaSorting=a})});he("search()",function(e,t,n,r){var a=this.context;return e===void 0?a.length!==0?a[0].oPreviousSearch.sSearch:void 0:this.iterator("table",function(o){o.oFeatures.bFilter&&Za(o,A.extend({},o.oPreviousSearch,{sSearch:e+"",bRegex:t===null?!1:t,bSmart:n===null?!0:n,bCaseInsensitive:r===null?!0:r}),1)})});je("columns().search()","column().search()",function(e,t,n,r){return this.iterator("column",function(a,o){var s=a.aoPreSearchCols;if(e===void 0)return s[o].sSearch;a.oFeatures.bFilter&&(A.extend(s[o],{sSearch:e+"",bRegex:t===null?!1:t,bSmart:n===null?!0:n,bCaseInsensitive:r===null?!0:r}),Za(a,a.oPreviousSearch,1))})});he("state()",function(){return this.context.length?this.context[0].oSavedState:null});he("state.clear()",function(){return this.iterator("table",function(e){e.fnStateSaveCallback.call(e.oInstance,e,{})})});he("state.loaded()",function(){return this.context.length?this.context[0].oLoadedState:null});he("state.save()",function(){return this.iterator("table",function(e){ei(e)})});k.use=function(e,t){t==="lib"||e.fn?A=e:t=="win"||e.document?(window=e,document=e.document):(t==="datetime"||e.type==="DateTime")&&(k.DateTime=e)};k.factory=function(e,t){var n=!1;return e&&e.document&&(window=e,document=e.document),t&&t.fn&&t.fn.jquery&&(A=t,n=!0),n};k.versionCheck=k.fnVersionCheck=function(e){for(var t=k.version.split("."),n=e.split("."),r,a,o=0,s=n.length;o<s;o++)if(r=parseInt(t[o],10)||0,a=parseInt(n[o],10)||0,r!==a)return r>a;return!0};k.isDataTable=k.fnIsDataTable=function(e){var t=A(e).get(0),n=!1;return e instanceof k.Api?!0:(A.each(k.settings,function(r,a){var o=a.nScrollHead?A("table",a.nScrollHead)[0]:null,s=a.nScrollFoot?A("table",a.nScrollFoot)[0]:null;(a.nTable===t||o===t||s===t)&&(n=!0)}),n)};k.tables=k.fnTables=function(e){var t=!1;A.isPlainObject(e)&&(t=e.api,e=e.visible);var n=A.map(k.settings,function(r){if(!e||e&&A(r.nTable).is(":visible"))return r.nTable});return t?new Ne(n):n};k.camelToHungarian=vn;he("$()",function(e,t){var n=this.rows(t).nodes(),r=A(n);return A([].concat(r.filter(e).toArray(),r.find(e).toArray()))});A.each(["on","one","off"],function(e,t){he(t+"()",function(){var n=Array.prototype.slice.call(arguments);n[0]=A.map(n[0].split(/\s/),function(a){return a.match(/\.dt\b/)?a:a+".dt"}).join(" ");var r=A(this.tables().nodes());return r[t].apply(r,n),this})});he("clear()",function(){return this.iterator("table",function(e){Do(e)})});he("settings()",function(){return new Ne(this.context,this.context)});he("init()",function(){var e=this.context;return e.length?e[0].oInit:null});he("data()",function(){return this.iterator("table",function(e){return yt(e.aoData,"_aData")}).flatten()});he("destroy()",function(e){return e=e||!1,this.iterator("table",function(t){var n=t.oClasses,r=t.nTable,a=t.nTBody,o=t.nTHead,s=t.nTFoot,u=A(r),f=A(a),d=A(t.nTableWrapper),h=A.map(t.aoData,function(S){return S.nTr}),b;t.bDestroying=!0,ke(t,"aoDestroyCallback","destroy",[t]),e||new Ne(t).columns().visible(!0),d.off(".DT").find(":not(tbody *)").off(".DT"),A(window).off(".DT-"+t.sInstance),r!=o.parentNode&&(u.children("thead").detach(),u.append(o)),s&&r!=s.parentNode&&(u.children("tfoot").detach(),u.append(s)),t.aaSorting=[],t.aaSortingFixed=[],Ji(t),A(h).removeClass(t.asStripeClasses.join(" ")),A("th, td",o).removeClass(n.sSortable+" "+n.sSortableAsc+" "+n.sSortableDesc+" "+n.sSortableNone),f.children().detach(),f.append(h);var _=t.nTableWrapper.parentNode,y=e?"remove":"detach";u[y](),d[y](),!e&&_&&(_.insertBefore(r,t.nTableReinsertBefore),u.css("width",t.sDestroyWidth).removeClass(n.sTable),b=t.asDestroyStripes.length,b&&f.children().each(function(S){A(this).addClass(t.asDestroyStripes[S%b])}));var g=A.inArray(t,k.settings);g!==-1&&k.settings.splice(g,1)})});A.each(["column","row","cell"],function(e,t){he(t+"s().every()",function(n){var r=this.selector.opts,a=this;return this.iterator(t,function(o,s,u,f,d){n.call(a[t](s,t==="cell"?u:r,t==="cell"?r:void 0),s,u,f,d)})})});he("i18n()",function(e,t,n){var r=this.context[0],a=jr(e)(r.oLanguage);return a===void 0&&(a=t),n!==void 0&&A.isPlainObject(a)&&(a=a[n]!==void 0?a[n]:a._),typeof a=="string"?a.replace("%d",n):a});k.version="1.13.10";k.settings=[];k.models={};k.models.oSearch={bCaseInsensitive:!0,sSearch:"",bRegex:!1,bSmart:!0,return:!1};k.models.oRow={nTr:null,anCells:null,_aData:[],_aSortData:null,_aFilterData:null,_sFilterRow:null,_sRowStripe:"",src:null,idx:-1};k.models.oColumn={idx:null,aDataSort:null,asSorting:null,bSearchable:null,bSortable:null,bVisible:null,_sManualType:null,_bAttrSrc:!1,fnCreatedCell:null,fnGetData:null,fnSetData:null,mData:null,mRender:null,nTh:null,nTf:null,sClass:null,sContentPadding:null,sDefaultContent:null,sName:null,sSortDataType:"std",sSortingClass:null,sSortingClassJUI:null,sTitle:null,sType:null,sWidth:null,sWidthOrig:null};k.defaults={aaData:null,aaSorting:[[0,"asc"]],aaSortingFixed:[],ajax:null,aLengthMenu:[10,25,50,100],aoColumns:null,aoColumnDefs:null,aoSearchCols:[],asStripeClasses:null,bAutoWidth:!0,bDeferRender:!1,bDestroy:!1,bFilter:!0,bInfo:!0,bLengthChange:!0,bPaginate:!0,bProcessing:!1,bRetrieve:!1,bScrollCollapse:!1,bServerSide:!1,bSort:!0,bSortMulti:!0,bSortCellsTop:!1,bSortClasses:!0,bStateSave:!1,fnCreatedRow:null,fnDrawCallback:null,fnFooterCallback:null,fnFormatNumber:function(e){return e.toString().replace(/\B(?=(\d{3})+(?!\d))/g,this.oLanguage.sThousands)},fnHeaderCallback:null,fnInfoCallback:null,fnInitComplete:null,fnPreDrawCallback:null,fnRowCallback:null,fnServerData:null,fnServerParams:null,fnStateLoadCallback:function(e){try{return JSON.parse((e.iStateDuration===-1?sessionStorage:localStorage).getItem("DataTables_"+e.sInstance+"_"+location.pathname))}catch{return{}}},fnStateLoadParams:null,fnStateLoaded:null,fnStateSaveCallback:function(e,t){try{(e.iStateDuration===-1?sessionStorage:localStorage).setItem("DataTables_"+e.sInstance+"_"+location.pathname,JSON.stringify(t))}catch{}},fnStateSaveParams:null,iStateDuration:7200,iDeferLoading:null,iDisplayLength:10,iDisplayStart:0,iTabIndex:0,oClasses:{},oLanguage:{oAria:{sSortAscending:": activate to sort column ascending",sSortDescending:": activate to sort column descending"},oPaginate:{sFirst:"First",sLast:"Last",sNext:"Next",sPrevious:"Previous"},sEmptyTable:"No data available in table",sInfo:"Showing _START_ to _END_ of _TOTAL_ entries",sInfoEmpty:"Showing 0 to 0 of 0 entries",sInfoFiltered:"(filtered from _MAX_ total entries)",sInfoPostFix:"",sDecimal:"",sThousands:",",sLengthMenu:"Show _MENU_ entries",sLoadingRecords:"Loading...",sProcessing:"",sSearch:"Search:",sSearchPlaceholder:"",sUrl:"",sZeroRecords:"No matching records found"},oSearch:A.extend({},k.models.oSearch),sAjaxDataProp:"data",sAjaxSource:null,sDom:"lfrtip",searchDelay:null,sPaginationType:"simple_numbers",sScrollX:"",sScrollXInner:"",sScrollY:"",sServerMethod:"GET",renderer:null,rowId:"DT_RowId"};Ka(k.defaults);k.defaults.column={aDataSort:null,iDataSort:-1,asSorting:["asc","desc"],bSearchable:!0,bSortable:!0,bVisible:!0,fnCreatedCell:null,mData:null,mRender:null,sCellType:"td",sClass:"",sContentPadding:"",sDefaultContent:null,sName:"",sSortDataType:"std",sTitle:null,sType:null,sWidth:null};Ka(k.defaults.column);k.models.oSettings={oFeatures:{bAutoWidth:null,bDeferRender:null,bFilter:null,bInfo:null,bLengthChange:null,bPaginate:null,bProcessing:null,bServerSide:null,bSort:null,bSortMulti:null,bSortClasses:null,bStateSave:null},oScroll:{bCollapse:null,iBarWidth:0,sX:null,sXInner:null,sY:null},oLanguage:{fnInfoCallback:null},oBrowser:{bScrollOversize:!1,bScrollbarLeft:!1,bBounding:!1,barWidth:0},ajax:null,aanFeatures:[],aoData:[],aiDisplay:[],aiDisplayMaster:[],aIds:{},aoColumns:[],aoHeader:[],aoFooter:[],oPreviousSearch:{},aoPreSearchCols:[],aaSorting:null,aaSortingFixed:[],asStripeClasses:null,asDestroyStripes:[],sDestroyWidth:0,aoRowCallback:[],aoHeaderCallback:[],aoFooterCallback:[],aoDrawCallback:[],aoRowCreatedCallback:[],aoPreDrawCallback:[],aoInitComplete:[],aoStateSaveParams:[],aoStateLoadParams:[],aoStateLoaded:[],sTableId:"",nTable:null,nTHead:null,nTFoot:null,nTBody:null,nTableWrapper:null,bDeferLoading:!1,bInitialised:!1,aoOpenRows:[],sDom:null,searchDelay:null,sPaginationType:"two_button",iStateDuration:0,aoStateSave:[],aoStateLoad:[],oSavedState:null,oLoadedState:null,sAjaxSource:null,sAjaxDataProp:null,jqXHR:null,json:void 0,oAjaxData:void 0,fnServerData:null,aoServerParams:[],sServerMethod:null,fnFormatNumber:null,aLengthMenu:null,iDraw:0,bDrawing:!1,iDrawError:-1,_iDisplayLength:10,_iDisplayStart:0,_iRecordsTotal:0,_iRecordsDisplay:0,oClasses:{},bFiltered:!1,bSorted:!1,bSortCellsTop:null,oInit:null,aoDestroyCallback:[],fnRecordsTotal:function(){return at(this)=="ssp"?this._iRecordsTotal*1:this.aiDisplayMaster.length},fnRecordsDisplay:function(){return at(this)=="ssp"?this._iRecordsDisplay*1:this.aiDisplay.length},fnDisplayEnd:function(){var e=this._iDisplayLength,t=this._iDisplayStart,n=t+e,r=this.aiDisplay.length,a=this.oFeatures,o=a.bPaginate;return a.bServerSide?o===!1||e===-1?t+r:Math.min(t+e,this._iRecordsDisplay):!o||n>r||e===-1?r:n},oInstance:null,sInstance:null,iTabIndex:0,nScrollHead:null,nScrollFoot:null,aLastSort:[],oPlugins:{},rowIdFn:null,rowId:null};k.ext=Ke={buttons:{},classes:{},builder:"-source-",errMode:"alert",feature:[],search:[],selector:{cell:[],column:[],row:[]},internal:{},legacy:{ajax:null},pager:{},renderer:{pageButton:{},header:{}},order:{},type:{detect:[],search:{},order:{}},_unique:0,fnVersionCheck:k.fnVersionCheck,iApiIndex:0,oJUIClasses:{},sVersion:k.version};A.extend(Ke,{afnFiltering:Ke.search,aTypes:Ke.type.detect,ofnSearch:Ke.type.search,oSort:Ke.type.order,afnSortData:Ke.order,aoFeatures:Ke.feature,oApi:Ke.internal,oStdClasses:Ke.classes,oPagination:Ke.pager});A.extend(k.ext.classes,{sTable:"dataTable",sNoFooter:"no-footer",sPageButton:"paginate_button",sPageButtonActive:"current",sPageButtonDisabled:"disabled",sStripeOdd:"odd",sStripeEven:"even",sRowEmpty:"dataTables_empty",sWrapper:"dataTables_wrapper",sFilter:"dataTables_filter",sInfo:"dataTables_info",sPaging:"dataTables_paginate paging_",sLength:"dataTables_length",sProcessing:"dataTables_processing",sSortAsc:"sorting_asc",sSortDesc:"sorting_desc",sSortable:"sorting",sSortableAsc:"sorting_desc_disabled",sSortableDesc:"sorting_asc_disabled",sSortableNone:"sorting_disabled",sSortColumn:"sorting_",sFilterInput:"",sLengthSelect:"",sScrollWrapper:"dataTables_scroll",sScrollHead:"dataTables_scrollHead",sScrollHeadInner:"dataTables_scrollHeadInner",sScrollBody:"dataTables_scrollBody",sScrollFoot:"dataTables_scrollFoot",sScrollFootInner:"dataTables_scrollFootInner",sHeaderTH:"",sFooterTH:"",sSortJUIAsc:"",sSortJUIDesc:"",sSortJUI:"",sSortJUIAscAllowed:"",sSortJUIDescAllowed:"",sSortJUIWrapper:"",sSortIcon:"",sJUIHeader:"",sJUIFooter:""});var ep=k.ext.pager;function pa(e,t){var n=[],r=ep.numbers_length,a=Math.floor(r/2);return t<=r?n=Or(0,t):e<=a?(n=Or(0,r-2),n.push("ellipsis"),n.push(t-1)):e>=t-1-a?(n=Or(t-(r-2),t),n.splice(0,0,"ellipsis"),n.splice(0,0,0)):(n=Or(e-a+2,e+a-1),n.push("ellipsis"),n.push(t-1),n.splice(0,0,"ellipsis"),n.splice(0,0,0)),n.DT_el="span",n}A.extend(ep,{simple:function(e,t){return["previous","next"]},full:function(e,t){return["first","previous","next","last"]},numbers:function(e,t){return[pa(e,t)]},simple_numbers:function(e,t){return["previous",pa(e,t),"next"]},full_numbers:function(e,t){return["first","previous",pa(e,t),"next","last"]},first_last_numbers:function(e,t){return["first",pa(e,t),"last"]},_numbers:pa,numbers_length:7});A.extend(!0,k.ext.renderer,{pageButton:{_:function(e,t,n,r,a,o){var s=e.oClasses,u=e.oLanguage.oPaginate,f=e.oLanguage.oAria.paginate||{},d,h,b=function(y,g){var S,L,O,C,Y=s.sPageButtonDisabled,W=function(ee){Ao(e,ee.data.action,!0)};for(S=0,L=g.length;S<L;S++)if(C=g[S],Array.isArray(C)){var B=A("<"+(C.DT_el||"div")+"/>").appendTo(y);b(B,C)}else{var Q=!1;switch(d=null,h=C,C){case"ellipsis":y.append('<span class="ellipsis">&#x2026;</span>');break;case"first":d=u.sFirst,a===0&&(Q=!0);break;case"previous":d=u.sPrevious,a===0&&(Q=!0);break;case"next":d=u.sNext,(o===0||a===o-1)&&(Q=!0);break;case"last":d=u.sLast,(o===0||a===o-1)&&(Q=!0);break;default:d=e.fnFormatNumber(C+1),h=a===C?s.sPageButtonActive:"";break}if(d!==null){var v=e.oInit.pagingTag||"a";Q&&(h+=" "+Y),O=A("<"+v+">",{class:s.sPageButton+" "+h,"aria-controls":e.sTableId,"aria-disabled":Q?"true":null,"aria-label":f[C],role:"link","aria-current":h===s.sPageButtonActive?"page":null,"data-dt-idx":C,tabindex:Q?-1:e.iTabIndex,id:n===0&&typeof C=="string"?e.sTableId+"_"+C:null}).html(d).appendTo(y),tu(O,{action:C},W)}}},_;try{_=A(t).find(document.activeElement).data("dt-idx")}catch{}b(A(t).empty(),r),_!==void 0&&A(t).find("[data-dt-idx="+_+"]").trigger("focus")}}});A.extend(k.ext.type.detect,[function(e,t){var n=t.oLanguage.sDecimal;return Ys(e,n)?"num"+n:null},function(e,t){if(e&&!(e instanceof Date)&&!R0.test(e))return null;var n=Date.parse(e);return n!==null&&!isNaN(n)||rn(e)?"date":null},function(e,t){var n=t.oLanguage.sDecimal;return Ys(e,n,!0)?"num-fmt"+n:null},function(e,t){var n=t.oLanguage.sDecimal;return Gf(e,n)?"html-num"+n:null},function(e,t){var n=t.oLanguage.sDecimal;return Gf(e,n,!0)?"html-num-fmt"+n:null},function(e,t){return rn(e)||typeof e=="string"&&e.indexOf("<")!==-1?"html":null}]);A.extend(k.ext.type.search,{html:function(e){return rn(e)?e:typeof e=="string"?e.replace(Yf," ").replace(Yi,""):""},string:function(e){return rn(e)?e:typeof e=="string"?e.replace(Yf," "):e}});var Ci=function(e,t,n,r){if(e!==0&&(!e||e==="-"))return-1/0;var a=typeof e;return a==="number"||a==="bigint"?e:(t&&(e=mh(e,t)),e.replace&&(n&&(e=e.replace(n,"")),r&&(e=e.replace(r,""))),e*1)};function tl(e){A.each({num:function(t){return Ci(t,e)},"num-fmt":function(t){return Ci(t,e,Ks)},"html-num":function(t){return Ci(t,e,Yi)},"html-num-fmt":function(t){return Ci(t,e,Yi,Ks)}},function(t,n){Ke.type.order[t+e+"-pre"]=n,t.match(/^html\-/)&&(Ke.type.search[t+e]=Ke.type.search.html)})}A.extend(Ke.type.order,{"date-pre":function(e){var t=Date.parse(e);return isNaN(t)?-1/0:t},"html-pre":function(e){return rn(e)?"":e.replace?e.replace(/<.*?>/g,"").toLowerCase():e+""},"string-pre":function(e){return rn(e)?"":typeof e=="string"?e.toLowerCase():e.toString?e.toString():""},"string-asc":function(e,t){return e<t?-1:e>t?1:0},"string-desc":function(e,t){return e<t?1:e>t?-1:0}});tl("");A.extend(!0,k.ext.renderer,{header:{_:function(e,t,n,r){A(e.nTable).on("order.dt.DT",function(a,o,s,u){if(e===o){var f=n.idx;t.removeClass(r.sSortAsc+" "+r.sSortDesc).addClass(u[f]=="asc"?r.sSortAsc:u[f]=="desc"?r.sSortDesc:n.sSortingClass)}})},jqueryui:function(e,t,n,r){A("<div/>").addClass(r.sSortJUIWrapper).append(t.contents()).append(A("<span/>").addClass(r.sSortIcon+" "+n.sSortingClassJUI)).appendTo(t),A(e.nTable).on("order.dt.DT",function(a,o,s,u){if(e===o){var f=n.idx;t.removeClass(r.sSortAsc+" "+r.sSortDesc).addClass(u[f]=="asc"?r.sSortAsc:u[f]=="desc"?r.sSortDesc:n.sSortingClass),t.find("span."+r.sSortIcon).removeClass(r.sSortJUIAsc+" "+r.sSortJUIDesc+" "+r.sSortJUI+" "+r.sSortJUIAscAllowed+" "+r.sSortJUIDescAllowed).addClass(u[f]=="asc"?r.sSortJUIAsc:u[f]=="desc"?r.sSortJUIDesc:n.sSortingClassJUI)}})}}});var $i=function(e){return Array.isArray(e)&&(e=e.join(",")),typeof e=="string"?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"):e};function ec(e,t,n,r,a){return window.moment?e[t](a):window.luxon?e[n](a):r?e[r](a):e}var tc=!1;function Zi(e,t,n){var r;if(window.moment){if(r=window.moment.utc(e,t,n,!0),!r.isValid())return null}else if(window.luxon){if(r=t&&typeof e=="string"?window.luxon.DateTime.fromFormat(e,t):window.luxon.DateTime.fromISO(e),!r.isValid)return null;r.setLocale(n)}else t?(tc||alert("DataTables warning: Formatted date without Moment.js or Luxon - https://datatables.net/tn/17"),tc=!0):r=new Date(e);return r}function Ts(e){return function(t,n,r,a){arguments.length===0?(r="en",n=null,t=null):arguments.length===1?(r="en",n=t,t=null):arguments.length===2&&(r=n,n=t,t=null);var o="datetime-"+n;return k.ext.type.order[o]||(k.ext.type.detect.unshift(function(s){return s===o?o:!1}),k.ext.type.order[o+"-asc"]=function(s,u){var f=s.valueOf(),d=u.valueOf();return f===d?0:f<d?-1:1},k.ext.type.order[o+"-desc"]=function(s,u){var f=s.valueOf(),d=u.valueOf();return f===d?0:f>d?-1:1}),function(s,u){if(s==null)if(a==="--now"){var f=new Date;s=new Date(Date.UTC(f.getFullYear(),f.getMonth(),f.getDate(),f.getHours(),f.getMinutes(),f.getSeconds()))}else s="";if(u==="type")return o;if(s==="")return u!=="sort"?"":Zi("0000-01-01 00:00:00",null,r);if(n!==null&&t===n&&u!=="sort"&&u!=="type"&&!(s instanceof Date))return s;var d=Zi(s,t,r);if(d===null)return s;if(u==="sort")return d;var h=n===null?ec(d,"toDate","toJSDate","")[e]():ec(d,"format","toFormat","toISOString",n);return u==="display"?$i(h):h}}}var tp=",",np=".";if(window.Intl!==void 0)try{for(var va=new Intl.NumberFormat().formatToParts(100000.1),Cr=0;Cr<va.length;Cr++)va[Cr].type==="group"?tp=va[Cr].value:va[Cr].type==="decimal"&&(np=va[Cr].value)}catch{}k.datetime=function(e,t){var n="datetime-detect-"+e;t||(t="en"),k.ext.type.order[n]||(k.ext.type.detect.unshift(function(r){var a=Zi(r,e,t);return r===""||a?n:!1}),k.ext.type.order[n+"-pre"]=function(r){return Zi(r,e,t)||0})};k.render={date:Ts("toLocaleDateString"),datetime:Ts("toLocaleString"),time:Ts("toLocaleTimeString"),number:function(e,t,n,r,a){return e==null&&(e=tp),t==null&&(t=np),{display:function(o){if(typeof o!="number"&&typeof o!="string"||o===""||o===null)return o;var s=o<0?"-":"",u=parseFloat(o);if(isNaN(u))return $i(o);u=u.toFixed(n),o=Math.abs(u);var f=parseInt(o,10),d=n?t+(o-f).toFixed(n).substring(2):"";return f===0&&parseFloat(d)===0&&(s=""),s+(r||"")+f.toString().replace(/\B(?=(\d{3})+(?!\d))/g,e)+d+(a||"")}}},text:function(){return{display:$i,filter:$i}}};function rp(e){return function(){var t=[Qi(this[k.ext.iApiIndex])].concat(Array.prototype.slice.call(arguments));return k.ext.internal[e].apply(this,t)}}A.extend(k.ext.internal,{_fnExternApiFunc:rp,_fnBuildAjax:xo,_fnAjaxUpdate:xh,_fnAjaxParameters:Ah,_fnAjaxUpdateDraw:Eh,_fnAjaxDataSrc:Qa,_fnAddColumn:ql,_fnColumnOptions:Gi,_fnAdjustColumnSizing:Ya,_fnVisibleToColumnIndex:Ga,_fnColumnIndexToVisible:za,_fnVisbleColumns:Yr,_fnGetColumns:To,_fnColumnTypes:Ul,_fnApplyColumnDefs:Th,_fnHungarianMap:Ka,_fnCamelToHungarian:vn,_fnLanguageCompat:Gs,_fnBrowserDetect:wh,_fnAddData:jn,_fnAddTr:Co,_fnNodeToDataIndex:H0,_fnNodeToColumnIndex:M0,_fnGetCellData:wt,_fnSetCellData:Ch,_fnSplitObjNotation:zs,_fnGetObjectDataFn:jr,_fnSetObjectDataFn:In,_fnGetDataMaster:Js,_fnClearTable:Do,_fnDeleteIndex:Pi,_fnInvalidate:Ja,_fnGetRowElements:Xl,_fnCreateTr:Kl,_fnBuildHead:Dh,_fnDrawHead:Da,_fnDraw:Bn,_fnReDraw:rr,_fnAddOptionsHtml:Sh,_fnDetectHeader:Sa,_fnGetUniqueThs:So,_fnFeatureHtmlFilter:Nh,_fnFilterComplete:Za,_fnFilterCustom:Lh,_fnFilterColumn:Oh,_fnFilter:Ih,_fnFilterCreateSearch:Gl,_fnEscapeRegex:zl,_fnFilterData:Rh,_fnFeatureHtmlInfo:Ph,_fnUpdateInfo:kh,_fnInfoMacros:$h,_fnInitialise:ya,_fnInitComplete:zi,_fnLengthChange:Jl,_fnFeatureHtmlLength:Fh,_fnFeatureHtmlPaginate:Hh,_fnPageChange:Ao,_fnFeatureHtmlProcessing:Mh,_fnProcessingDisplay:St,_fnFeatureHtmlTable:jh,_fnScrollDraw:Eo,_fnApplyToChildren:Qt,_fnCalculateColumnWidths:Ql,_fnThrottle:Zl,_fnConvertToWidth:Bh,_fnGetWidestNode:Wh,_fnGetMaxLenString:Vh,_fnStringToCss:Ue,_fnSortFlatten:Gr,_fnSort:qh,_fnSortAria:Uh,_fnSortListener:Qs,_fnSortAttachListener:eu,_fnSortingClasses:Ji,_fnSortData:Xh,_fnSaveState:ei,_fnLoadState:Kh,_fnImplementState:Zs,_fnSettingsFromNode:Qi,_fnLog:kt,_fnMap:Vt,_fnBindAction:tu,_fnCallbackReg:lt,_fnCallbackFire:ke,_fnLengthOverflow:nu,_fnRenderer:ru,_fnDataSource:at,_fnRowAttributes:Yl,_fnExtend:el,_fnCalculateEnd:function(){}});A.fn.dataTable=k;k.$=A;A.fn.dataTableSettings=k.settings;A.fn.dataTableExt=k.ext;A.fn.DataTable=function(e){return A(this).dataTable(e).api()};A.each(k,function(e,t){A.fn.DataTable[e]=t});/*! Buttons for DataTables 2.4.2
 * © SpryMedia Ltd - datatables.net/license
 */let X=on;var Q0=0,Z0=0,Ct=k.ext.buttons,nl=null;function lu(e,t,n){X.fn.animate?e.stop().fadeIn(t,n):(e.css("display","block"),n&&n.call(e))}function uu(e,t,n){X.fn.animate?e.stop().fadeOut(t,n):(e.css("display","none"),n&&n.call(e))}var Be=function(e,t){if(!(this instanceof Be))return function(n){return new Be(n,e).container()};typeof t>"u"&&(t={}),t===!0&&(t={}),Array.isArray(t)&&(t={buttons:t}),this.c=X.extend(!0,{},Be.defaults,t),t.buttons&&(this.c.buttons=t.buttons),this.s={dt:new k.Api(e),buttons:[],listenKeys:"",namespace:"dtb"+Q0++},this.dom={container:X("<"+this.c.dom.container.tag+"/>").addClass(this.c.dom.container.className)},this._constructor()};X.extend(Be.prototype,{action:function(e,t){var n=this._nodeToButton(e);return t===void 0?n.conf.action:(n.conf.action=t,this)},active:function(e,t){var n=this._nodeToButton(e),r=this.c.dom.button.active,a=X(n.node);return n.inCollection&&this.c.dom.collection.button&&this.c.dom.collection.button.active!==void 0&&(r=this.c.dom.collection.button.active),t===void 0?a.hasClass(r):(a.toggleClass(r,t===void 0?!0:t),this)},add:function(e,t,n){var r=this.s.buttons;if(typeof t=="string"){for(var a=t.split("-"),o=this.s,s=0,u=a.length-1;s<u;s++)o=o.buttons[a[s]*1];r=o.buttons,t=a[a.length-1]*1}return this._expandButton(r,e,e!==void 0?e.split:void 0,(e===void 0||e.split===void 0||e.split.length===0)&&o!==void 0,!1,t),(n===void 0||n===!0)&&this._draw(),this},collectionRebuild:function(e,t){var n=this._nodeToButton(e);if(t!==void 0){var r;for(r=n.buttons.length-1;r>=0;r--)this.remove(n.buttons[r].node);for(n.conf.prefixButtons&&t.unshift.apply(t,n.conf.prefixButtons),n.conf.postfixButtons&&t.push.apply(t,n.conf.postfixButtons),r=0;r<t.length;r++){var a=t[r];this._expandButton(n.buttons,a,a!==void 0&&a.config!==void 0&&a.config.split!==void 0,!0,a.parentConf!==void 0&&a.parentConf.split!==void 0,null,a.parentConf)}}this._draw(n.collection,n.buttons)},container:function(){return this.dom.container},disable:function(e){var t=this._nodeToButton(e);return X(t.node).addClass(this.c.dom.button.disabled).prop("disabled",!0),this},destroy:function(){X("body").off("keyup."+this.s.namespace);var e=this.s.buttons.slice(),t,n;for(t=0,n=e.length;t<n;t++)this.remove(e[t].node);this.dom.container.remove();var r=this.s.dt.settings()[0];for(t=0,n=r.length;t<n;t++)if(r.inst===this){r.splice(t,1);break}return this},enable:function(e,t){if(t===!1)return this.disable(e);var n=this._nodeToButton(e);return X(n.node).removeClass(this.c.dom.button.disabled).prop("disabled",!1),this},index:function(e,t,n){t||(t="",n=this.s.buttons);for(var r=0,a=n.length;r<a;r++){var o=n[r].buttons;if(n[r].node===e)return t+r;if(o&&o.length){var s=this.index(e,r+"-",o);if(s!==null)return s}}return null},name:function(){return this.c.name},node:function(e){if(!e)return this.dom.container;var t=this._nodeToButton(e);return X(t.node)},processing:function(e,t){var n=this.s.dt,r=this._nodeToButton(e);return t===void 0?X(r.node).hasClass("processing"):(X(r.node).toggleClass("processing",t),X(n.table().node()).triggerHandler("buttons-processing.dt",[t,n.button(e),n,X(e),r.conf]),this)},remove:function(e){var t=this._nodeToButton(e),n=this._nodeToHost(e),r=this.s.dt;if(t.buttons.length)for(var a=t.buttons.length-1;a>=0;a--)this.remove(t.buttons[a].node);t.conf.destroying=!0,t.conf.destroy&&t.conf.destroy.call(r.button(e),r,X(e),t.conf),this._removeKey(t.conf),X(t.node).remove();var o=X.inArray(t,n);return n.splice(o,1),this},text:function(e,t){var n=this._nodeToButton(e),r=n.textNode,a=this.s.dt,o=X(n.node),s=function(u){return typeof u=="function"?u(a,o,n.conf):u};return t===void 0?s(n.conf.text):(n.conf.text=t,r.html(s(t)),this)},_constructor:function(){var e=this,t=this.s.dt,n=t.settings()[0],r=this.c.buttons;n._buttons||(n._buttons=[]),n._buttons.push({inst:this,name:this.c.name});for(var a=0,o=r.length;a<o;a++)this.add(r[a]);t.on("destroy",function(s,u){u===n&&e.destroy()}),X("body").on("keyup."+this.s.namespace,function(s){if(!document.activeElement||document.activeElement===document.body){var u=String.fromCharCode(s.keyCode).toLowerCase();e.s.listenKeys.toLowerCase().indexOf(u)!==-1&&e._keypress(u,s)}})},_addKey:function(e){e.key&&(this.s.listenKeys+=X.isPlainObject(e.key)?e.key.key:e.key)},_draw:function(e,t){e||(e=this.dom.container,t=this.s.buttons),e.children().detach();for(var n=0,r=t.length;n<r;n++)e.append(t[n].inserter),e.append(" "),t[n].buttons&&t[n].buttons.length&&this._draw(t[n].collection,t[n].buttons)},_expandButton:function(e,t,n,r,a,o,s){var u=this.s.dt,f=!1,d=this.c.dom.collection,h=Array.isArray(t)?t:[t];t===void 0&&(h=Array.isArray(n)?n:[n]);for(var b=0,_=h.length;b<_;b++){var y=this._resolveExtends(h[b]);if(y){if(f=!!(y.config&&y.config.split),Array.isArray(y)){this._expandButton(e,y,g!==void 0&&g.conf!==void 0?g.conf.split:void 0,r,s!==void 0&&s.split!==void 0,o,s);continue}var g=this._buildButton(y,r,y.split!==void 0||y.config!==void 0&&y.config.split!==void 0,a);if(g){if(o!=null?(e.splice(o,0,g),o++):e.push(g),g.conf.buttons&&(g.collection=X("<"+d.container.content.tag+"/>"),g.conf._collection=g.collection,X(g.node).append(d.action.dropHtml),this._expandButton(g.buttons,g.conf.buttons,g.conf.split,!f,f,o,g.conf)),g.conf.split){g.collection=X("<"+d.container.tag+"/>"),g.conf._collection=g.collection;for(var S=0;S<g.conf.split.length;S++){var L=g.conf.split[S];typeof L=="object"&&(L.parent=s,L.collectionLayout===void 0&&(L.collectionLayout=g.conf.collectionLayout),L.dropup===void 0&&(L.dropup=g.conf.dropup),L.fade===void 0&&(L.fade=g.conf.fade))}this._expandButton(g.buttons,g.conf.buttons,g.conf.split,!f,f,o,g.conf)}g.conf.parent=s,y.init&&y.init.call(u.button(g.node),u,X(g.node),y)}}}},_buildButton:function(e,t,n,r){var a=this.c.dom,o,s=this.s.dt,u=function(v){return typeof v=="function"?v(s,h,e):v},f=X.extend(!0,{},a.button);if(t&&n&&a.collection.split?X.extend(!0,f,a.collection.split.action):r||t?X.extend(!0,f,a.collection.button):n&&X.extend(!0,f,a.split.button),e.spacer){var d=X("<"+f.spacer.tag+"/>").addClass("dt-button-spacer "+e.style+" "+f.spacer.className).html(u(e.text));return{conf:e,node:d,inserter:d,buttons:[],inCollection:t,isSplit:n,collection:null,textNode:d}}if(e.available&&!e.available(s,e)&&!e.hasOwnProperty("html"))return!1;var h;if(e.hasOwnProperty("html"))h=X(e.html);else{var b=function(v,ee,G,re){re.action.call(ee.button(G),v,ee,G,re),X(ee.table().node()).triggerHandler("buttons-action.dt",[ee.button(G),ee,G,re])},_=e.tag||f.tag,y=e.clickBlurs===void 0?!0:e.clickBlurs;if(h=X("<"+_+"/>").addClass(f.className).attr("tabindex",this.s.dt.settings()[0].iTabIndex).attr("aria-controls",this.s.dt.table().node().id).on("click.dtb",function(v){v.preventDefault(),!h.hasClass(f.disabled)&&e.action&&b(v,s,h,e),y&&h.trigger("blur")}).on("keypress.dtb",function(v){v.keyCode===13&&(v.preventDefault(),!h.hasClass(f.disabled)&&e.action&&b(v,s,h,e))}),_.toLowerCase()==="a"&&h.attr("href","#"),_.toLowerCase()==="button"&&h.attr("type","button"),f.liner.tag){var g=X("<"+f.liner.tag+"/>").html(u(e.text)).addClass(f.liner.className);f.liner.tag.toLowerCase()==="a"&&g.attr("href","#"),h.append(g),o=g}else h.html(u(e.text)),o=h;e.enabled===!1&&h.addClass(f.disabled),e.className&&h.addClass(e.className),e.titleAttr&&h.attr("title",u(e.titleAttr)),e.attr&&h.attr(e.attr),e.namespace||(e.namespace=".dt-button-"+Z0++),e.config!==void 0&&e.config.split&&(e.split=e.config.split)}var S=this.c.dom.buttonContainer,L;S&&S.tag?L=X("<"+S.tag+"/>").addClass(S.className).append(h):L=h,this._addKey(e),this.c.buttonCreated&&(L=this.c.buttonCreated(e,L));var O;if(n){var C=t?X.extend(!0,this.c.dom.split,this.c.dom.collection.split):this.c.dom.split,Y=C.wrapper;O=X("<"+Y.tag+"/>").addClass(Y.className).append(h);var W=X.extend(e,{align:C.dropdown.align,attr:{"aria-haspopup":"dialog","aria-expanded":!1},className:C.dropdown.className,closeButton:!1,splitAlignClass:C.dropdown.splitAlignClass,text:C.dropdown.text});this._addKey(W);var B=function(v,ee,G,re){Ct.split.action.call(ee.button(O),v,ee,G,re),X(ee.table().node()).triggerHandler("buttons-action.dt",[ee.button(G),ee,G,re]),G.attr("aria-expanded",!0)},Q=X('<button class="'+C.dropdown.className+' dt-button"></button>').html(C.dropdown.dropHtml).on("click.dtb",function(v){v.preventDefault(),v.stopPropagation(),Q.hasClass(f.disabled)||B(v,s,Q,W),y&&Q.trigger("blur")}).on("keypress.dtb",function(v){v.keyCode===13&&(v.preventDefault(),Q.hasClass(f.disabled)||B(v,s,Q,W))});e.split.length===0&&Q.addClass("dtb-hide-drop"),O.append(Q).attr(W.attr)}return{conf:e,node:n?O.get(0):h.get(0),inserter:n?O:L,buttons:[],inCollection:t,isSplit:n,inSplit:r,collection:null,textNode:o}},_nodeToButton:function(e,t){t||(t=this.s.buttons);for(var n=0,r=t.length;n<r;n++){if(t[n].node===e)return t[n];if(t[n].buttons.length){var a=this._nodeToButton(e,t[n].buttons);if(a)return a}}},_nodeToHost:function(e,t){t||(t=this.s.buttons);for(var n=0,r=t.length;n<r;n++){if(t[n].node===e)return t;if(t[n].buttons.length){var a=this._nodeToHost(e,t[n].buttons);if(a)return a}}},_keypress:function(e,t){if(!t._buttonsHandled){var n=function(a,o){if(a.key){if(a.key===e)t._buttonsHandled=!0,X(o).click();else if(X.isPlainObject(a.key)){if(a.key.key!==e||a.key.shiftKey&&!t.shiftKey||a.key.altKey&&!t.altKey||a.key.ctrlKey&&!t.ctrlKey||a.key.metaKey&&!t.metaKey)return;t._buttonsHandled=!0,X(o).click()}}},r=function(a){for(var o=0,s=a.length;o<s;o++)n(a[o].conf,a[o].node),a[o].buttons.length&&r(a[o].buttons)};r(this.s.buttons)}},_removeKey:function(e){if(e.key){var t=X.isPlainObject(e.key)?e.key.key:e.key,n=this.s.listenKeys.split(""),r=X.inArray(t,n);n.splice(r,1),this.s.listenKeys=n.join("")}},_resolveExtends:function(e){var t=this,n=this.s.dt,r,a,o=function(h){for(var b=0;!X.isPlainObject(h)&&!Array.isArray(h);){if(h===void 0)return;if(typeof h=="function"){if(h=h.call(t,n,e),!h)return!1}else if(typeof h=="string"){if(!Ct[h])return{html:h};h=Ct[h]}if(b++,b>30)throw"Buttons: Too many iterations"}return Array.isArray(h)?h:X.extend({},h)};for(e=o(e);e&&e.extend;){if(!Ct[e.extend])throw"Cannot extend unknown button type: "+e.extend;var s=o(Ct[e.extend]);if(Array.isArray(s))return s;if(!s)return!1;var u=s.className;e.config!==void 0&&s.config!==void 0&&(e.config=X.extend({},s.config,e.config)),e=X.extend({},s,e),u&&e.className!==u&&(e.className=u+" "+e.className),e.extend=s.extend}var f=e.postfixButtons;if(f)for(e.buttons||(e.buttons=[]),r=0,a=f.length;r<a;r++)e.buttons.push(f[r]);var d=e.prefixButtons;if(d)for(e.buttons||(e.buttons=[]),r=0,a=d.length;r<a;r++)e.buttons.splice(r,0,d[r]);return e},_popover:function(e,t,n,r){var a=t,o=this.c,s=!1,u=X.extend({align:"button-left",autoClose:!1,background:!0,backgroundClassName:"dt-button-background",closeButton:!0,containerClassName:o.dom.collection.container.className,contentClassName:o.dom.collection.container.content.className,collectionLayout:"",collectionTitle:"",dropup:!1,fade:400,popoverTitle:"",rightAlignClassName:"dt-button-right",tag:o.dom.collection.container.tag},n),f=u.tag+"."+u.containerClassName.replace(/ /g,"."),d=t.node(),h=function(){s=!0,uu(X(f),u.fade,function(){X(this).detach()}),X(a.buttons('[aria-haspopup="dialog"][aria-expanded="true"]').nodes()).attr("aria-expanded","false"),X("div.dt-button-background").off("click.dtb-collection"),Be.background(!1,u.backgroundClassName,u.fade,d),X(window).off("resize.resize.dtb-collection"),X("body").off(".dtb-collection"),a.off("buttons-action.b-internal"),a.off("destroy")};if(e===!1){h();return}var b=X(a.buttons('[aria-haspopup="dialog"][aria-expanded="true"]').nodes());b.length&&(d.closest(f).length&&(d=b.eq(0)),h());var _=X(".dt-button",e).length,y="";_===3?y="dtb-b3":_===2?y="dtb-b2":_===1&&(y="dtb-b1");var g=X("<"+u.tag+"/>").addClass(u.containerClassName).addClass(u.collectionLayout).addClass(u.splitAlignClass).addClass(y).css("display","none").attr({"aria-modal":!0,role:"dialog"});e=X(e).addClass(u.contentClassName).attr("role","menu").appendTo(g),d.attr("aria-expanded","true"),d.parents("body")[0]!==document.body&&(d=document.body.lastChild),u.popoverTitle?g.prepend('<div class="dt-button-collection-title">'+u.popoverTitle+"</div>"):u.collectionTitle&&g.prepend('<div class="dt-button-collection-title">'+u.collectionTitle+"</div>"),u.closeButton&&g.prepend('<div class="dtb-popover-close">&times;</div>').addClass("dtb-collection-closeable"),lu(g.insertAfter(d),u.fade);var S=X(t.table().container()),L=g.css("position");if((u.span==="container"||u.align==="dt-container")&&(d=d.parent(),g.css("width",S.width())),L==="absolute"){var O=X(d[0].offsetParent),C=d.position(),Y=d.offset(),W=O.offset(),B=O.position(),Q=window.getComputedStyle(O[0]);W.height=O.outerHeight(),W.width=O.width()+parseFloat(Q.paddingLeft),W.right=W.left+W.width,W.bottom=W.top+W.height;var v=C.top+d.outerHeight(),ee=C.left;g.css({top:v,left:ee}),Q=window.getComputedStyle(g[0]);var G=g.offset();G.height=g.outerHeight(),G.width=g.outerWidth(),G.right=G.left+G.width,G.bottom=G.top+G.height,G.marginTop=parseFloat(Q.marginTop),G.marginBottom=parseFloat(Q.marginBottom),u.dropup&&(v=C.top-G.height-G.marginTop-G.marginBottom),(u.align==="button-right"||g.hasClass(u.rightAlignClassName))&&(ee=C.left-G.width+d.outerWidth()),(u.align==="dt-container"||u.align==="container")&&(ee<C.left&&(ee=-C.left),ee+G.width>W.width&&(ee=W.width-G.width)),B.left+ee+G.width>X(window).width()&&(ee=X(window).width()-G.width-B.left),Y.left+ee<0&&(ee=-Y.left),B.top+v+G.height>X(window).height()+X(window).scrollTop()&&(v=C.top-G.height-G.marginTop-G.marginBottom),B.top+v<X(window).scrollTop()&&(v=C.top+d.outerHeight()),g.css({top:v,left:ee})}else{var L=function(){var ie=X(window).height()/2,pe=g.height()/2;pe>ie&&(pe=ie),g.css("marginTop",pe*-1)};L(),X(window).on("resize.dtb-collection",function(){L()})}u.background&&Be.background(!0,u.backgroundClassName,u.fade,u.backgroundHost||d),X("div.dt-button-background").on("click.dtb-collection",function(){}),u.autoClose&&setTimeout(function(){a.on("buttons-action.b-internal",function(re,ie,pe,se){se[0]!==d[0]&&h()})},0),X(g).trigger("buttons-popover.dt"),a.on("destroy",h),setTimeout(function(){s=!1,X("body").on("click.dtb-collection",function(re){if(!s){var ie=X.fn.addBack?"addBack":"andSelf",pe=X(re.target).parent()[0];(!X(re.target).parents()[ie]().filter(e).length&&!X(pe).hasClass("dt-buttons")||X(re.target).hasClass("dt-button-background"))&&h()}}).on("keyup.dtb-collection",function(re){re.keyCode===27&&h()}).on("keydown.dtb-collection",function(re){var ie=X("a, button",e),pe=document.activeElement;re.keyCode===9&&(ie.index(pe)===-1?(ie.first().focus(),re.preventDefault()):re.shiftKey?pe===ie[0]&&(ie.last().focus(),re.preventDefault()):pe===ie.last()[0]&&(ie.first().focus(),re.preventDefault()))})},0)}});Be.background=function(e,t,n,r){n===void 0&&(n=400),r||(r=document.body),e?lu(X("<div/>").addClass(t).css("display","none").insertAfter(r),n):uu(X("div."+t),n,function(){X(this).removeClass(t).remove()})};Be.instanceSelector=function(e,t){if(e==null)return X.map(t,function(o){return o.inst});var n=[],r=X.map(t,function(o){return o.name}),a=function(o){if(Array.isArray(o)){for(var s=0,u=o.length;s<u;s++)a(o[s]);return}if(typeof o=="string")if(o.indexOf(",")!==-1)a(o.split(","));else{var f=X.inArray(o.trim(),r);f!==-1&&n.push(t[f].inst)}else typeof o=="number"?n.push(t[o].inst):typeof o=="object"&&n.push(o)};return a(e),n};Be.buttonSelector=function(e,t){for(var n=[],r=function(f,d,h){for(var b,_,y=0,g=d.length;y<g;y++)b=d[y],b&&(_=h!==void 0?h+y:y+"",f.push({node:b.node,name:b.conf.name,idx:_}),b.buttons&&r(f,b.buttons,_+"-"))},a=function(f,d){var h,b,_=[];r(_,d.s.buttons);var y=X.map(_,function(C){return C.node});if(Array.isArray(f)||f instanceof X){for(h=0,b=f.length;h<b;h++)a(f[h],d);return}if(f==null||f==="*")for(h=0,b=_.length;h<b;h++)n.push({inst:d,node:_[h].node});else if(typeof f=="number")d.s.buttons[f]&&n.push({inst:d,node:d.s.buttons[f].node});else if(typeof f=="string")if(f.indexOf(",")!==-1){var g=f.split(",");for(h=0,b=g.length;h<b;h++)a(g[h].trim(),d)}else if(f.match(/^\d+(\-\d+)*$/)){var S=X.map(_,function(C){return C.idx});n.push({inst:d,node:_[X.inArray(f,S)].node})}else if(f.indexOf(":name")!==-1){var L=f.replace(":name","");for(h=0,b=_.length;h<b;h++)_[h].name===L&&n.push({inst:d,node:_[h].node})}else X(y).filter(f).each(function(){n.push({inst:d,node:this})});else if(typeof f=="object"&&f.nodeName){var O=X.inArray(f,y);O!==-1&&n.push({inst:d,node:y[O]})}},o=0,s=e.length;o<s;o++){var u=e[o];a(t,u)}return n};Be.stripData=function(e,t){return typeof e!="string"||(e=e.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,""),e=e.replace(/<!\-\-.*?\-\->/g,""),(!t||t.stripHtml)&&(e=e.replace(/<[^>]*>/g,"")),(!t||t.trim)&&(e=e.replace(/^\s+|\s+$/g,"")),(!t||t.stripNewlines)&&(e=e.replace(/\n/g," ")),(!t||t.decodeEntities)&&(nl?e=nl(e):(rc.innerHTML=e,e=rc.value))),e};Be.entityDecoder=function(e){nl=e};Be.defaults={buttons:["copy","excel","csv","pdf","print"],name:"main",tabIndex:0,dom:{container:{tag:"div",className:"dt-buttons"},collection:{action:{dropHtml:'<span class="dt-button-down-arrow">&#x25BC;</span>'},container:{className:"dt-button-collection",content:{className:"",tag:"div"},tag:"div"}},button:{tag:"button",className:"dt-button",active:"dt-button-active",disabled:"disabled",spacer:{className:"dt-button-spacer",tag:"span"},liner:{tag:"span",className:""}},split:{action:{className:"dt-button-split-drop-button dt-button",tag:"button"},dropdown:{align:"split-right",className:"dt-button-split-drop",dropHtml:'<span class="dt-button-down-arrow">&#x25BC;</span>',splitAlignClass:"dt-button-split-left",tag:"button"},wrapper:{className:"dt-button-split",tag:"div"}}}};Be.version="2.4.2";X.extend(Ct,{collection:{text:function(e){return e.i18n("buttons.collection","Collection")},className:"buttons-collection",closeButton:!1,init:function(e,t,n){t.attr("aria-expanded",!1)},action:function(e,t,n,r){r._collection.parents("body").length?this.popover(!1,r):this.popover(r._collection,r),e.type==="keypress"&&X("a, button",r._collection).eq(0).focus()},attr:{"aria-haspopup":"dialog"}},split:{text:function(e){return e.i18n("buttons.split","Split")},className:"buttons-split",closeButton:!1,init:function(e,t,n){return t.attr("aria-expanded",!1)},action:function(e,t,n,r){this.popover(r._collection,r)},attr:{"aria-haspopup":"dialog"}},copy:function(e,t){if(Ct.copyHtml5)return"copyHtml5"},csv:function(e,t){if(Ct.csvHtml5&&Ct.csvHtml5.available(e,t))return"csvHtml5"},excel:function(e,t){if(Ct.excelHtml5&&Ct.excelHtml5.available(e,t))return"excelHtml5"},pdf:function(e,t){if(Ct.pdfHtml5&&Ct.pdfHtml5.available(e,t))return"pdfHtml5"},pageLength:function(e){var t=e.settings()[0].aLengthMenu,n=[],r=[],a=function(u){return u.i18n("buttons.pageLength",{"-1":"Show all rows",_:"Show %d rows"},u.page.len())};if(Array.isArray(t[0]))n=t[0],r=t[1];else for(var o=0;o<t.length;o++){var s=t[o];X.isPlainObject(s)?(n.push(s.value),r.push(s.label)):(n.push(s),r.push(s))}return{extend:"collection",text:a,className:"buttons-page-length",autoClose:!0,buttons:X.map(n,function(u,f){return{text:r[f],className:"button-page-length",action:function(d,h){h.page.len(u).draw()},init:function(d,h,b){var _=this,y=function(){_.active(d.page.len()===u)};d.on("length.dt"+b.namespace,y),y()},destroy:function(d,h,b){d.off("length.dt"+b.namespace)}}}),init:function(u,f,d){var h=this;u.on("length.dt"+d.namespace,function(){h.text(d.text)})},destroy:function(u,f,d){u.off("length.dt"+d.namespace)}}},spacer:{style:"empty",spacer:!0,text:function(e){return e.i18n("buttons.spacer","")}}});k.Api.register("buttons()",function(e,t){t===void 0&&(t=e,e=void 0),this.selector.buttonGroup=e;var n=this.iterator(!0,"table",function(r){if(r._buttons)return Be.buttonSelector(Be.instanceSelector(e,r._buttons),t)},!0);return n._groupSelector=e,n});k.Api.register("button()",function(e,t){var n=this.buttons(e,t);return n.length>1&&n.splice(1,n.length),n});k.Api.registerPlural("buttons().active()","button().active()",function(e){return e===void 0?this.map(function(t){return t.inst.active(t.node)}):this.each(function(t){t.inst.active(t.node,e)})});k.Api.registerPlural("buttons().action()","button().action()",function(e){return e===void 0?this.map(function(t){return t.inst.action(t.node)}):this.each(function(t){t.inst.action(t.node,e)})});k.Api.registerPlural("buttons().collectionRebuild()","button().collectionRebuild()",function(e){return this.each(function(t){for(var n=0;n<e.length;n++)typeof e[n]=="object"&&(e[n].parentConf=t);t.inst.collectionRebuild(t.node,e)})});k.Api.register(["buttons().enable()","button().enable()"],function(e){return this.each(function(t){t.inst.enable(t.node,e)})});k.Api.register(["buttons().disable()","button().disable()"],function(){return this.each(function(e){e.inst.disable(e.node)})});k.Api.register("button().index()",function(){var e=null;return this.each(function(t){var n=t.inst.index(t.node);n!==null&&(e=n)}),e});k.Api.registerPlural("buttons().nodes()","button().node()",function(){var e=X();return X(this.each(function(t){e=e.add(t.inst.node(t.node))})),e});k.Api.registerPlural("buttons().processing()","button().processing()",function(e){return e===void 0?this.map(function(t){return t.inst.processing(t.node)}):this.each(function(t){t.inst.processing(t.node,e)})});k.Api.registerPlural("buttons().text()","button().text()",function(e){return e===void 0?this.map(function(t){return t.inst.text(t.node)}):this.each(function(t){t.inst.text(t.node,e)})});k.Api.registerPlural("buttons().trigger()","button().trigger()",function(){return this.each(function(e){e.inst.node(e.node).trigger("click")})});k.Api.register("button().popover()",function(e,t){return this.map(function(n){return n.inst._popover(e,this.button(this[0].node),t)})});k.Api.register("buttons().containers()",function(){var e=X(),t=this._groupSelector;return this.iterator(!0,"table",function(n){if(n._buttons)for(var r=Be.instanceSelector(t,n._buttons),a=0,o=r.length;a<o;a++)e=e.add(r[a].container())}),e});k.Api.register("buttons().container()",function(){return this.containers().eq(0)});k.Api.register("button().add()",function(e,t,n){var r=this.context;if(r.length){var a=Be.instanceSelector(this._groupSelector,r[0]._buttons);a.length&&a[0].add(t,e,n)}return this.button(this._groupSelector,e)});k.Api.register("buttons().destroy()",function(){return this.pluck("inst").unique().each(function(e){e.destroy()}),this});k.Api.registerPlural("buttons().remove()","buttons().remove()",function(){return this.each(function(e){e.inst.remove(e.node)}),this});var ma;k.Api.register("buttons.info()",function(e,t,n){var r=this;return e===!1?(this.off("destroy.btn-info"),uu(X("#datatables_buttons_info"),400,function(){X(this).remove()}),clearTimeout(ma),ma=null,this):(ma&&clearTimeout(ma),X("#datatables_buttons_info").length&&X("#datatables_buttons_info").remove(),e=e?"<h2>"+e+"</h2>":"",lu(X('<div id="datatables_buttons_info" class="dt-button-info"/>').html(e).append(X("<div/>")[typeof t=="string"?"html":"append"](t)).css("display","none").appendTo("body")),n!==void 0&&n!==0&&(ma=setTimeout(function(){r.buttons.info(!1)},n)),this.on("destroy.btn-info",function(){r.buttons.info(!1)}),this)});k.Api.register("buttons.exportData()",function(e){if(this.context.length)return nw(new k.Api(this.context[0]),e)});k.Api.register("buttons.exportInfo()",function(e){return e||(e={}),{filename:ew(e),title:tw(e),messageTop:nc(this,e.message||e.messageTop,"top"),messageBottom:nc(this,e.messageBottom,"bottom")}});var ew=function(e){var t=e.filename==="*"&&e.title!=="*"&&e.title!==void 0&&e.title!==null&&e.title!==""?e.title:e.filename;if(typeof t=="function"&&(t=t()),t==null)return null;t.indexOf("*")!==-1&&(t=t.replace("*",X("head > title").text()).trim()),t=t.replace(/[^a-zA-Z0-9_\u00A1-\uFFFF\.,\-_ !\(\)]/g,"");var n=fu(e.extension);return n||(n=""),t+n},fu=function(e){return e==null?null:typeof e=="function"?e():e},tw=function(e){var t=fu(e.title);return t===null?null:t.indexOf("*")!==-1?t.replace("*",X("head > title").text()||"Exported data"):t},nc=function(e,t,n){var r=fu(t);if(r===null)return null;var a=X("caption",e.table().container()).eq(0);if(r==="*"){var o=a.css("caption-side");return o!==n?null:a.length?a.text():""}return r},rc=X("<textarea/>")[0],nw=function(e,t){var n=X.extend(!0,{},{rows:null,columns:"",modifier:{search:"applied",order:"applied"},orthogonal:"display",stripHtml:!0,stripNewlines:!0,decodeEntities:!0,trim:!0,format:{header:function(Y){return Be.stripData(Y,n)},footer:function(Y){return Be.stripData(Y,n)},body:function(Y){return Be.stripData(Y,n)}},customizeData:null},t),r=e.columns(n.columns).indexes().map(function(Y){var W=e.column(Y).header();return n.format.header(W.innerHTML,Y,W)}).toArray(),a=e.table().footer()?e.columns(n.columns).indexes().map(function(Y){var W=e.column(Y).footer();return n.format.footer(W?W.innerHTML:"",Y,W)}).toArray():null,o=X.extend({},n.modifier);e.select&&typeof e.select.info=="function"&&o.selected===void 0&&e.rows(n.rows,X.extend({selected:!0},o)).any()&&X.extend(o,{selected:!0});for(var s=e.rows(n.rows,o).indexes().toArray(),u=e.cells(s,n.columns),f=u.render(n.orthogonal).toArray(),d=u.nodes().toArray(),h=r.length,b=h>0?f.length/h:0,_=[],y=0,g=0,S=b;g<S;g++){for(var L=[h],O=0;O<h;O++)L[O]=n.format.body(f[y],g,O,d[y]),y++;_[g]=L}var C={header:r,footer:a,body:_};return n.customizeData&&n.customizeData(C),C};X.fn.dataTable.Buttons=Be;X.fn.DataTable.Buttons=Be;X(document).on("init.dt plugin-init.dt",function(e,t){if(e.namespace==="dt"){var n=t.oInit.buttons||k.defaults.buttons;n&&!t._buttons&&new Be(t,n).container()}});function ap(e,t){var n=new k.Api(e),r=t||n.init().buttons||k.defaults.buttons;return new Be(n,r).container()}k.ext.feature.push({fnInit:ap,cFeature:"B"});k.ext.features&&k.ext.features.register("buttons",ap);/*! Bootstrap integration for DataTables' Buttons
 * © SpryMedia Ltd - datatables.net/license
 */let ac=on;ac.extend(!0,K.Buttons.defaults,{dom:{container:{className:"dt-buttons btn-group flex-wrap"},button:{className:"btn btn-secondary",active:"active"},collection:{action:{dropHtml:""},container:{tag:"div",className:"dropdown-menu dt-button-collection"},closeButton:!1,button:{tag:"a",className:"dt-button dropdown-item",active:"dt-button-active",disabled:"disabled",spacer:{className:"dropdown-divider",tag:"hr"}}},split:{action:{tag:"a",className:"btn btn-secondary dt-button-split-drop-button",closeButton:!1},dropdown:{tag:"button",dropHtml:"",className:"btn btn-secondary dt-button-split-drop dropdown-toggle dropdown-toggle-split",closeButton:!1,align:"split-left",splitAlignClass:"dt-button-split-left"},wrapper:{tag:"div",className:"dt-button-split btn-group",closeButton:!1}}},buttonCreated:function(e,t){return e.buttons?ac('<div class="btn-group"/>').append(t):t}});K.ext.buttons.collection.className+=" dropdown-toggle";K.ext.buttons.collection.rightAlignClassName="dropdown-menu-right";/*! Select for DataTables 1.7.0-dev
 * © SpryMedia Ltd - datatables.net/license/mit
 */let be=on;k.select={};k.select.version="1.7.0-dev";k.select.init=function(e){var t=e.settings()[0];if(!t._select){var n=e.state.loaded(),r=function(S,L,O){if(!(O===null||O.select===void 0)){if(e.rows({selected:!0}).any()&&e.rows().deselect(),O.select.rows!==void 0&&e.rows(O.select.rows).select(),e.columns({selected:!0}).any()&&e.columns().deselect(),O.select.columns!==void 0&&e.columns(O.select.columns).select(),e.cells({selected:!0}).any()&&e.cells().deselect(),O.select.cells!==void 0)for(var C=0;C<O.select.cells.length;C++)e.cell(O.select.cells[C].row,O.select.cells[C].column).select();e.state.save()}};e.on("stateSaveParams",function(S,L,O){O.select={},O.select.rows=e.rows({selected:!0}).ids(!0).toArray(),O.select.columns=e.columns({selected:!0})[0],O.select.cells=e.cells({selected:!0})[0].map(function(C){return{row:e.row(C.row).id(!0),column:C.column}})}).on("stateLoadParams",r).one("init",function(){r(void 0,void 0,n)});var a=t.oInit.select,o=k.defaults.select,s=a===void 0?o:a,u="row",f="api",d=!1,h=!0,b=!0,_="td, th",y="selected",g=!1;t._select={},s===!0?(f="os",g=!0):typeof s=="string"?(f=s,g=!0):be.isPlainObject(s)&&(s.blurable!==void 0&&(d=s.blurable),s.toggleable!==void 0&&(h=s.toggleable),s.info!==void 0&&(b=s.info),s.items!==void 0&&(u=s.items),s.style!==void 0?(f=s.style,g=!0):(f="os",g=!0),s.selector!==void 0&&(_=s.selector),s.className!==void 0&&(y=s.className)),e.select.selector(_),e.select.items(u),e.select.style(f),e.select.blurable(d),e.select.toggleable(h),e.select.info(b),t._select.className=y,be.fn.dataTable.ext.order["select-checkbox"]=function(S,L){return this.api().column(L,{order:"index"}).nodes().map(function(O){return S._select.items==="row"?be(O).parent().hasClass(S._select.className):S._select.items==="cell"?be(O).hasClass(S._select.className):!1})},!g&&be(e.table().node()).hasClass("selectable")&&e.select.style("os")}};function ic(e,t,n){var r,a,o,s=function(f,d){if(f>d){var h=d;d=f,f=h}var b=!1;return e.columns(":visible").indexes().filter(function(_){return _===f&&(b=!0),_===d?(b=!1,!0):b})},u=function(f,d){var h=e.rows({search:"applied"}).indexes();if(h.indexOf(f)>h.indexOf(d)){var b=d;d=f,f=b}var _=!1;return h.filter(function(y){return y===f&&(_=!0),y===d?(_=!1,!0):_})};!e.cells({selected:!0}).any()&&!n?(a=s(0,t.column),o=u(0,t.row)):(a=s(n.column,t.column),o=u(n.row,t.row)),r=e.cells(o,a).flatten(),e.cells(t,{selected:!0}).any()?e.cells(r).deselect():e.cells(r).select()}function cu(e){var t=e.settings()[0],n=t._select.selector;be(e.table().container()).off("mousedown.dtSelect",n).off("mouseup.dtSelect",n).off("click.dtSelect",n),be("body").off("click.dtSelect"+du(e.table().node()))}function ip(e){var t=be(e.table().container()),n=e.settings()[0],r=n._select.selector,a;t.on("mousedown.dtSelect",r,function(o){(o.shiftKey||o.metaKey||o.ctrlKey)&&t.css("-moz-user-select","none").one("selectstart.dtSelect",r,function(){return!1}),window.getSelection&&(a=window.getSelection())}).on("mouseup.dtSelect",r,function(){t.css("-moz-user-select","")}).on("click.dtSelect",r,function(o){var s=e.select.items(),u;if(a){var f=window.getSelection();if((!f.anchorNode||be(f.anchorNode).closest("table")[0]===e.table().node())&&f!==a)return}var d=e.settings()[0],h=e.settings()[0].oClasses.sWrapper.trim().replace(/ +/g,".");if(be(o.target).closest("div."+h)[0]==e.table().container()){var b=e.cell(be(o.target).closest("td, th"));if(b.any()){var _=be.Event("user-select.dt");if(an(e,_,[s,b,o]),!_.isDefaultPrevented()){var y=b.index();s==="row"?(u=y.row,Cs(o,e,d,"row",u)):s==="column"?(u=b.index().column,Cs(o,e,d,"column",u)):s==="cell"&&(u=b.index(),Cs(o,e,d,"cell",u)),d._select_lastCell=y}}}}),be("body").on("click.dtSelect"+du(e.table().node()),function(o){if(n._select.blurable){if(be(o.target).parents().filter(e.table().container()).length||be(o.target).parents("html").length===0||be(o.target).parents("div.DTE").length)return;var s=be.Event("select-blur.dt");if(an(e,s,[o.target,o]),s.isDefaultPrevented())return;ni(n,!0)}})}function an(e,t,n,r){r&&!e.flatten().length||(typeof t=="string"&&(t=t+".dt"),n.unshift(e),be(e.table().node()).trigger(t,n))}function rw(e){var t=e.settings()[0];if(!(!t._select.info||!t.aanFeatures.i)&&e.select.style()!=="api"){var n=e.rows({selected:!0}).flatten().length,r=e.columns({selected:!0}).flatten().length,a=e.cells({selected:!0}).flatten().length,o=function(s,u,f){s.append(be('<span class="select-item"/>').append(e.i18n("select."+u+"s",{_:"%d "+u+"s selected",0:"",1:"1 "+u+" selected"},f)))};be.each(t.aanFeatures.i,function(s,u){u=be(u);var f=be('<span class="select-info"/>');o(f,"row",n),o(f,"column",r),o(f,"cell",a);var d=u.children("span.select-info");d.length&&d.remove(),f.text()!==""&&u.append(f)})}}function aw(e){var t=new k.Api(e);e._select_init=!0,e.aoRowCreatedCallback.push({fn:function(n,r,a){var o,s,u=e.aoData[a];for(u._select_selected&&be(n).addClass(e._select.className),o=0,s=e.aoColumns.length;o<s;o++)(e.aoColumns[o]._select_selected||u._selected_cells&&u._selected_cells[o])&&be(u.anCells[o]).addClass(e._select.className)},sName:"select-deferRender"}),t.on("preXhr.dt.dtSelect",function(n,r){if(r===t.settings()[0]){var a=t.rows({selected:!0}).ids(!0).filter(function(s){return s!==void 0}),o=t.cells({selected:!0}).eq(0).map(function(s){var u=t.row(s.row).id(!0);return u?{row:u,column:s.column}:void 0}).filter(function(s){return s!==void 0});t.one("draw.dt.dtSelect",function(){t.rows(a).select(),o.any()&&o.each(function(s){t.cells(s.row,s.column).select()})})}}),t.on("draw.dtSelect.dt select.dtSelect.dt deselect.dtSelect.dt info.dt",function(){rw(t),t.state.save()}),t.on("destroy.dtSelect",function(){be(t.rows({selected:!0}).nodes()).removeClass(t.settings()[0]._select.className),cu(t),t.off(".dtSelect"),be("body").off(".dtSelect"+du(t.table().node()))})}function oc(e,t,n,r){var a=e[t+"s"]({search:"applied"}).indexes(),o=be.inArray(r,a),s=be.inArray(n,a);if(!e[t+"s"]({selected:!0}).any()&&o===-1)a.splice(be.inArray(n,a)+1,a.length);else{if(o>s){var u=s;s=o,o=u}a.splice(s+1,a.length),a.splice(0,o)}e[t](n,{selected:!0}).any()?(a.splice(be.inArray(n,a),1),e[t+"s"](a).deselect()):e[t+"s"](a).select()}function ni(e,t){if(t||e._select.style==="single"){var n=new k.Api(e);n.rows({selected:!0}).deselect(),n.columns({selected:!0}).deselect(),n.cells({selected:!0}).deselect()}}function Cs(e,t,n,r,a){var o=t.select.style(),s=t.select.toggleable(),u=t[r](a,{selected:!0}).any();if(!(u&&!s))if(o==="os")if(e.ctrlKey||e.metaKey)t[r](a).select(!u);else if(e.shiftKey)r==="cell"?ic(t,a,n._select_lastCell||null):oc(t,r,a,n._select_lastCell?n._select_lastCell[r]:null);else{var f=t[r+"s"]({selected:!0});u&&f.flatten().length===1?t[r](a).deselect():(f.deselect(),t[r](a).select())}else o=="multi+shift"&&e.shiftKey?r==="cell"?ic(t,a,n._select_lastCell||null):oc(t,r,a,n._select_lastCell?n._select_lastCell[r]:null):t[r](a).select(!u)}function du(e){return e.id.replace(/[^a-zA-Z0-9\-\_]/g,"-")}be.each([{type:"row",prop:"aoData"},{type:"column",prop:"aoColumns"}],function(e,t){k.ext.selector[t.type].push(function(n,r,a){var o=r.selected,s,u=[];if(o!==!0&&o!==!1)return a;for(var f=0,d=a.length;f<d;f++)s=n[t.prop][a[f]],(o===!0&&s._select_selected===!0||o===!1&&!s._select_selected)&&u.push(a[f]);return u})});k.ext.selector.cell.push(function(e,t,n){var r=t.selected,a,o=[];if(r===void 0)return n;for(var s=0,u=n.length;s<u;s++)a=e.aoData[n[s].row],(r===!0&&a._selected_cells&&a._selected_cells[n[s].column]===!0||r===!1&&(!a._selected_cells||!a._selected_cells[n[s].column]))&&o.push(n[s]);return o});var sn=k.Api.register,zr=k.Api.registerPlural;sn("select()",function(){return this.iterator("table",function(e){k.select.init(new k.Api(e))})});sn("select.blurable()",function(e){return e===void 0?this.context[0]._select.blurable:this.iterator("table",function(t){t._select.blurable=e})});sn("select.toggleable()",function(e){return e===void 0?this.context[0]._select.toggleable:this.iterator("table",function(t){t._select.toggleable=e})});sn("select.info()",function(e){return e===void 0?this.context[0]._select.info:this.iterator("table",function(t){t._select.info=e})});sn("select.items()",function(e){return e===void 0?this.context[0]._select.items:this.iterator("table",function(t){t._select.items=e,an(new k.Api(t),"selectItems",[e])})});sn("select.style()",function(e){return e===void 0?this.context[0]._select.style:this.iterator("table",function(t){t._select||k.select.init(new k.Api(t)),t._select_init||aw(t),t._select.style=e;var n=new k.Api(t);cu(n),e!=="api"&&ip(n),an(new k.Api(t),"selectStyle",[e])})});sn("select.selector()",function(e){return e===void 0?this.context[0]._select.selector:this.iterator("table",function(t){cu(new k.Api(t)),t._select.selector=e,t._select.style!=="api"&&ip(new k.Api(t))})});zr("rows().select()","row().select()",function(e){var t=this;return e===!1?this.deselect():(this.iterator("row",function(n,r){ni(n),n.aoData[r]._select_selected=!0,be(n.aoData[r].nTr).addClass(n._select.className)}),this.iterator("table",function(n,r){an(t,"select",["row",t[r]],!0)}),this)});sn("row().selected()",function(){var e=this.context[0];return!!(e&&this.length&&e.aoData[this[0]]&&e.aoData[this[0]]._select_selected)});zr("columns().select()","column().select()",function(e){var t=this;return e===!1?this.deselect():(this.iterator("column",function(n,r){ni(n),n.aoColumns[r]._select_selected=!0;var a=new k.Api(n).column(r);be(a.header()).addClass(n._select.className),be(a.footer()).addClass(n._select.className),a.nodes().to$().addClass(n._select.className)}),this.iterator("table",function(n,r){an(t,"select",["column",t[r]],!0)}),this)});sn("column().selected()",function(){var e=this.context[0];return!!(e&&this.length&&e.aoColumns[this[0]]&&e.aoColumns[this[0]]._select_selected)});zr("cells().select()","cell().select()",function(e){var t=this;return e===!1?this.deselect():(this.iterator("cell",function(n,r,a){ni(n);var o=n.aoData[r];o._selected_cells===void 0&&(o._selected_cells=[]),o._selected_cells[a]=!0,o.anCells&&be(o.anCells[a]).addClass(n._select.className)}),this.iterator("table",function(n,r){an(t,"select",["cell",t.cells(t[r]).indexes().toArray()],!0)}),this)});sn("cell().selected()",function(){var e=this.context[0];if(e&&this.length){var t=e.aoData[this[0][0].row];if(t&&t._selected_cells&&t._selected_cells[this[0][0].column])return!0}return!1});zr("rows().deselect()","row().deselect()",function(){var e=this;return this.iterator("row",function(t,n){t.aoData[n]._select_selected=!1,t._select_lastCell=null,be(t.aoData[n].nTr).removeClass(t._select.className)}),this.iterator("table",function(t,n){an(e,"deselect",["row",e[n]],!0)}),this});zr("columns().deselect()","column().deselect()",function(){var e=this;return this.iterator("column",function(t,n){t.aoColumns[n]._select_selected=!1;var r=new k.Api(t),a=r.column(n);be(a.header()).removeClass(t._select.className),be(a.footer()).removeClass(t._select.className),r.cells(null,n).indexes().each(function(o){var s=t.aoData[o.row],u=s._selected_cells;s.anCells&&(!u||!u[o.column])&&be(s.anCells[o.column]).removeClass(t._select.className)})}),this.iterator("table",function(t,n){an(e,"deselect",["column",e[n]],!0)}),this});zr("cells().deselect()","cell().deselect()",function(){var e=this;return this.iterator("cell",function(t,n,r){var a=t.aoData[n];a._selected_cells!==void 0&&(a._selected_cells[r]=!1),a.anCells&&!t.aoColumns[r]._select_selected&&be(a.anCells[r]).removeClass(t._select.className)}),this.iterator("table",function(t,n){an(e,"deselect",["cell",e[n]],!0)}),this});function Ar(e,t){return function(n){return n.i18n("buttons."+e,t)}}function Ds(e){var t=e._eventNamespace;return"draw.dt.DT"+t+" select.dt.DT"+t+" deselect.dt.DT"+t}function iw(e,t){return!!(be.inArray("rows",t.limitTo)!==-1&&e.rows({selected:!0}).any()||be.inArray("columns",t.limitTo)!==-1&&e.columns({selected:!0}).any()||be.inArray("cells",t.limitTo)!==-1&&e.cells({selected:!0}).any())}var Ss=0;be.extend(k.ext.buttons,{selected:{text:Ar("selected","Selected"),className:"buttons-selected",limitTo:["rows","columns","cells"],init:function(e,t,n){var r=this;n._eventNamespace=".select"+Ss++,e.on(Ds(n),function(){r.enable(iw(e,n))}),this.disable()},destroy:function(e,t,n){e.off(n._eventNamespace)}},selectedSingle:{text:Ar("selectedSingle","Selected single"),className:"buttons-selected-single",init:function(e,t,n){var r=this;n._eventNamespace=".select"+Ss++,e.on(Ds(n),function(){var a=e.rows({selected:!0}).flatten().length+e.columns({selected:!0}).flatten().length+e.cells({selected:!0}).flatten().length;r.enable(a===1)}),this.disable()},destroy:function(e,t,n){e.off(n._eventNamespace)}},selectAll:{text:Ar("selectAll","Select all"),className:"buttons-select-all",action:function(e,t,n,r){var a=this.select.items(),o=r.selectorModifier;o?(typeof o=="function"&&(o=o.call(t,e,t,n,r)),this[a+"s"](o).select()):this[a+"s"]().select()}},selectNone:{text:Ar("selectNone","Deselect all"),className:"buttons-select-none",action:function(){ni(this.settings()[0],!0)},init:function(e,t,n){var r=this;n._eventNamespace=".select"+Ss++,e.on(Ds(n),function(){var a=e.rows({selected:!0}).flatten().length+e.columns({selected:!0}).flatten().length+e.cells({selected:!0}).flatten().length;r.enable(a>0)}),this.disable()},destroy:function(e,t,n){e.off(n._eventNamespace)}},showSelected:{text:Ar("showSelected","Show only selected"),className:"buttons-show-selected",action:function(e,t,n,r){if(r._filter){var a=k.ext.search.indexOf(r._filter);a!==-1&&(k.ext.search.splice(a,1),r._filter=null),this.active(!1)}else{var o=function(s,u,f){return s!==t.settings()[0]?!0:s.aoData[f]._select_selected};r._filter=o,k.ext.search.push(o),this.active(!0)}t.draw()}}});be.each(["Row","Column","Cell"],function(e,t){var n=t.toLowerCase();k.ext.buttons["select"+t+"s"]={text:Ar("select"+t+"s","Select "+n+"s"),className:"buttons-select-"+n+"s",action:function(){this.select.items(n)},init:function(r){var a=this;r.on("selectItems.dt.DT",function(o,s,u){a.active(u===n)})}}});be.fn.DataTable.select=k.select;be(document).on("preInit.dt.dtSelect",function(e,t){e.namespace==="dt"&&k.select.init(new k.Api(t))});window._buildUrl=function(e,t){let n=e.ajax.url()||"",r=e.ajax.params();return r.action=t,n+"?"+$.param(r)};document.addEventListener("DOMContentLoaded",function(){let e=$("table");e.on("select.dt",function(t,n,r,a){n.rows({selected:!0}).every(function(o,s,u){var f=this.data();f.deleted_at==null&&(n.button("restore:name").disable(),n.button("forceDelete:name").disable(),n.button("forceDeleteSingle:name").disable())})}),e.on("deselect.dt",function(t,n,r,a){n.rows({selected:!0}).every(function(o,s,u){var f=this.data();f.deleted_at==null&&(n.button("restore:name").disable(),n.button("forceDelete:name").disable(),n.button("forceDeleteSingle:name").disable())})})});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.add={name:"add",className:"buttons-add btn-success",text:'<i class="bi bi-plus"></i> New',action:function(e,t,n,r){let a=window.location.toString();a.indexOf("?")>0&&(a=a.substring(0,a.indexOf("?"))),window.location=a+"/create"}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.export={name:"export",extend:"collection",className:"btn-primary",text:'Export&nbsp;<span class="caret"/>',buttons:[{extend:"csv",text:"CSV"},{extend:"excel",text:"Excel"},{extend:"pdf",text:"PDF"}]}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.csv={name:"csv",className:"buttons-csv btn-primary",titleAttr:"Export as CSV",text:'<i class="bi bi-filetype-csv"></i>',action:function(e,t,n,r){window.location=_buildUrl(t,"csv")}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.excel={name:"excel",className:"buttons-excel btn-primary",titleAttr:"Export as Excel",text:'<i class="bi bi-file-earmark-excel"></i>',action:function(e,t,n,r){window.location=_buildUrl(t,"excel")}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.pdf={name:"pdf",className:"buttons-pdf btn-primary",titleAttr:"Export as PDF",text:'<i class="bi bi-file-pdf"></i>',action:function(e,t,n,r){window.location=_buildUrl(t,"pdf")}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.print={name:"print",className:"buttons-print btn-primary",titleAttr:"Print",text:'<i class="bi bi-printer"></i>',action:function(e,t,n,r){window.location=_buildUrl(t,"print")}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.reset={name:"reset",className:"btn-primary",titleAttr:"Reset",text:'<i class="bi bi-arrow-counterclockwise"></i>',action:function(e,t,n,r){$(".dataTable").find(":input").each(function(){$(this).val("")}).each(function(a){let o=$.fn.dataTable.util.escapeRegex($(this).val());t.table().column($(this).closest("th").index()).search(o||"",!1,!0)}),t.search("").draw()}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.reload={name:"reload",className:"btn-primary",titleAttr:"Reload",text:'<i class="bi bi-arrow-repeat"></i>',action:function(e,t,n,r){t.draw(!1)},init:function(e,t,n){e.on("processing.dt",(r,a,o)=>{let s=$(t);o?s.html(`<i class="spinner-border spinner-border-sm" role="status">
  <span class="visually-hidden">Loading...</span>
</i>`):s.html('<i class="bi bi-arrow-repeat"></i>'),s.attr("disabled",o)})}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.restore={name:"restore",extend:"selected",className:"buttons-restore btn-success",text:'<i class="bi bi-undo"></i> Restore',action:function(e,t,n,r){(r.editor||t.editor()).remove(t.rows({selected:!0}).indexes(),{title:r.formTitle||"Restore Record",message:function(o,s){let u=s.row({selected:!0}).data();return u.DTE_Restore||"Are you sure you want to restore record # "+u.DT_RowId+"?"},buttons:[{text:'<i class="bi bi-undo"></i> Restore',className:"btn btn-success btn-editor-restore",action:function(){this.submit(null,null,function(o){o.action="restore"})}},{text:"Cancel",className:"btn ml-2",action:function(){this.close()}}]})}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.duplicate={name:"duplicate",extend:"selected",className:"buttons-duplicate btn-success",text:'<i class="bi bi-copy"></i> Duplicate',action:function(e,t,n,r){(r.editor||t.editor()).edit(t.rows({selected:!0}).indexes(),{title:r.formTitle||"Duplicate Record",buttons:r.formButtons||[{text:'<i class="bi bi-copy"></i> Duplicate',className:"btn btn-success btn-editor-duplicate",action:function(){this.submit()}},{text:"Cancel",className:"btn ml-2",action:function(){this.close()}}]}).mode("create")}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.duplicateSingle={name:"duplicateSingle",extend:"selectedSingle",className:"buttons-duplicate btn-success",text:'<i class="bi bi-copy"></i> Duplicate',action:function(e,t,n,r){(r.editor||t.editor()).edit(t.rows({selected:!0}).indexes(),{title:r.formTitle||"Duplicate Record",buttons:r.formButtons||[{text:'<i class="bi bi-copy"></i> Duplicate',className:"btn btn-success btn-editor-duplicate",action:function(){this.submit()}},{text:"Cancel",className:"btn ml-2",action:function(){this.close()}}]}).mode("create")}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.forceDelete={name:"forceDelete",extend:"selected",className:"buttons-force-delete btn-danger",text:'<i class="bi bi-trash"></i> Force Delete',action:function(e,t,n,r){(r.editor||t.editor()).remove(t.rows({selected:!0}).indexes(),{title:r.formTitle||"Force Delete Record(/s)",message:function(o,s){let u=s.rows(o.modifier()).data();return"Are you sure you want to force delete the following record(s)? <ul><li>"+(u[0].hasOwnProperty("DTE_Remove")?u.pluck("DTE_Remove"):u.pluck("DT_RowId")).join("</li><li>")+"</li></ul>"},buttons:[{text:'<i class="bi bi-trash"></i> Delete',className:"btn btn-danger btn-editor-remove",action:function(){this.submit(null,null,function(o){o.action="forceDelete"})}},{text:"Cancel",className:"btn ml-2",action:function(){this.close()}}]})}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.forceDeleteSingle={name:"forceDeleteSingle",extend:"selectedSingle",className:"buttons-force-delete btn-danger",text:'<i class="bi bi-trash"></i> Force Delete',action:function(e,t,n,r){(r.editor||t.editor()).remove(t.rows({selected:!0}).indexes(),{title:r.formTitle||"Force Delete Record",message:function(o,s){let u=s.row({selected:!0}).data();return u.DTE_Remove||"Are you sure you want to force delete record # "+u.DT_RowId+"?"},buttons:[{text:'<i class="bi bi-trash"></i> Delete',className:"btn btn-danger btn-editor-remove",action:function(){this.submit(null,null,function(o){o.action="forceDelete"})}},{text:"Cancel",className:"btn ml-2",action:function(){this.close()}}]})}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.url={name:"url",extend:"selectedSingle",className:"buttons-url",text:"URL Action (change me)",action:function(e,t,n,r){let a=t.row({selected:!0}).data(),o=r.data||"DTE_URL",s=a[o]||"#";r.target=="_blank"?window.open(s,"_blank"):window.location=s}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.ajax={name:"ajax",extend:"selectedSingle",className:"buttons-ajax",text:"Ajax Action (Change Me)",action:function(e,t,n,r){let a=t.row({selected:!0}).data(),o=a[r.data||"DTE_AJAX"]||"",s=r.method||"POST";if(r.hasOwnProperty("confirmation")&&!confirm(r.confirmation))return r.hasOwnProperty("onCancel")&&r.onCancel(),!1;$.ajax({url:o,method:s,data:a}).done(u=>{r.hasOwnProperty("onSuccess")&&r.onSuccess(u),t.draw()}).fail(u=>{r.hasOwnProperty("onError")&&r.onError(u)})}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.ajaxBatch={name:"ajaxBatch",extend:"selected",className:"buttons-ajax",text:"Ajax Batch Action (Change Me)",action:function(e,t,n,r){let a=t.rows({selected:!0}).data(),o={data:[]};for(i=0;i<a.count();i++)o.data.push(a[i]);if(r.hasOwnProperty("confirmation")&&!confirm(r.confirmation))return r.hasOwnProperty("onCancel")&&r.onCancel(),!1;let s=r.url||"",u=r.method||"POST";$.ajax({url:s,method:u,data:o}).done(f=>{r.hasOwnProperty("onSuccess")&&r.onSuccess(f),t.draw()}).fail(f=>{r.hasOwnProperty("onError")&&r.onError(f)})}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.toggleScope={name:"toggleScope",className:"buttons-toggle",text:'<i class="bi bi-square"></i> Toggle',action:function(e,t,n,r){n.find("i").toggleClass("bi-check-square").toggleClass("bi-square");let a=r.scope,o=r.key||"scopes";t.on("preXhr."+a,(s,u,f)=>{f[o]=f[o]||{},f[o][a]=n.find("i.bi-check-square").length}),t.draw()}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.ext.buttons.withTrashed={name:"withTrashed",className:"buttons-toggle",text:'<i class="bi bi-square"></i> Show Deleted',action:function(e,t,n,r){n.find("i").toggleClass("fa-check-square").toggleClass("fa-square");let a=r.key||"scopes";t.on("preXhr.withTrashed",(o,s,u)=>{u[a]=u[a]||{},u[a].withTrashed=n.find("i.fa-check-square").length}),t.draw()}},$.fn.dataTable.ext.buttons.onlyTrashed={name:"onlyTrashed",className:"buttons-toggle",text:'<i class="bi bi-square"></i> Only Deleted',action:function(e,t,n,r){n.find("i").toggleClass("fa-check-square").toggleClass("fa-square");let a=r.key||"scopes";t.on("preXhr.onlyTrashed",(o,s,u)=>{u[a]=u[a]||{},u[a].onlyTrashed=n.find("i.fa-check-square").length}),t.draw()}}});document.addEventListener("DOMContentLoaded",function(){$.fn.dataTable.render.badge=function(e){return function(t,n,r){return e||(e="info"),'<span class="badge badge-'+e+'">'+t+"</span>"}},$.fn.dataTable.render.boolean=function(){return function(e){let t="danger",n="N";return(e||e==="1")&&(t="success",n="Y"),'<span class="badge badge-'+t+'">'+n+"</span>"}},$.fn.dataTable.render.suffix=function(e){return function(t){return t+" "+e}},$.fn.dataTable.render.prefix=function(e){return function(t){return e+" "+t}}});window.jQuery=window.$=on;window.bootstrap=zg;window.DataTable=K;on.extend(!0,K.defaults,{dom:"<'row'<'col-sm-12 mb-4'B>><'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>><'row'<'col-sm-12'tr>><'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>"});on.extend(!0,K.Buttons.defaults,{dom:{buttonLiner:{tag:""}}});on.extend(K.ext.classes,{sTable:"dataTable table table-striped table-bordered table-hover"});

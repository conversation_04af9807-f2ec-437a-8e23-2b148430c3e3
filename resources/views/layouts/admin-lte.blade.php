<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>{{ config('app.name', 'Laravel') }} | Dev Portal</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon/favicon-'.strtolower(app()->environment()).'.png') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fontsource/source-sans-3@5.0.12/index.css"
          integrity="sha256-tXJfXfp6Ewt1ilPzLDtQnJV4hclT9XuaZUKyUvmyr+Q=" crossorigin="anonymous"><!--end::Fonts-->
    <!--begin::Third Party Plugin(OverlayScrollbars)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.3.0/styles/overlayscrollbars.min.css"
          integrity="sha256-dSokZseQNT08wYEWiz5iLI8QPlKxG+TswNRD8k35cpg=" crossorigin="anonymous">
    <!--end::Third Party Plugin(OverlayScrollbars)--><!--begin::Third Party Plugin(Bootstrap Icons)-->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.min.css"
          integrity="sha256-Qsx5lrStHZyR9REqhUF8iQt73X06c8LGIUPzpOhwRrI=" crossorigin="anonymous">
    <!--end::Third Party Plugin(Bootstrap Icons)--><!--begin::Required Plugin(AdminLTE)-->
    <link rel="stylesheet" href="{{ asset('layouts/developer/admin-lte/css/adminlte.min.css') }}">

    @stack('css')
</head> <!--end::Head--> <!--begin::Body-->

<body class="layout-fixed sidebar-expand-lg sidebar-mini bg-body-tertiary sidebar-open"> <!--begin::App Wrapper-->
<div class="app-wrapper"> <!--begin::Header-->
    <nav class="app-header navbar navbar-expand bg-body"> <!--begin::Container-->
        <div class="container-fluid"> <!--begin::Start Navbar Links-->
            <ul class="navbar-nav">
                <li class="nav-item"><a class="nav-link" data-lte-toggle="sidebar" href="#" role="button"> <i
                            class="bi bi-list"></i> </a></li>
                {{--                <li class="nav-item d-none d-md-block"><a href="#" class="nav-link">Home</a></li>--}}
                {{--                <li class="nav-item d-none d-md-block"><a href="#" class="nav-link">Contact</a></li>--}}
            </ul> <!--end::Start Navbar Links--> <!--begin::End Navbar Links-->
            <ul class="navbar-nav ms-auto"> <!--begin::Navbar Search-->
                {{--                <li class="nav-item"><a class="nav-link" data-widget="navbar-search" href="#" role="button"> <i--}}
                {{--                            class="bi bi-search"></i> </a></li> <!--end::Navbar Search-->--}}
                {{--                <!--begin::Messages Dropdown Menu-->--}}
                {{--                <li class="nav-item dropdown"><a class="nav-link" data-bs-toggle="dropdown" href="#"> <i--}}
                {{--                            class="bi bi-chat-text"></i> <span class="navbar-badge badge text-bg-danger">3</span> </a>--}}
                {{--                    <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end"><a href="#" class="dropdown-item">--}}
                {{--                            <!--begin::Message-->--}}
                {{--                            <div class="d-flex">--}}
                {{--                                <div class="flex-shrink-0"><img src="../../../dist/assets/img/user1-128x128.jpg"--}}
                {{--                                                                alt="User Avatar"--}}
                {{--                                                                class="img-size-50 rounded-circle me-3"></div>--}}
                {{--                                <div class="flex-grow-1">--}}
                {{--                                    <h3 class="dropdown-item-title">--}}
                {{--                                        Brad Diesel--}}
                {{--                                        <span class="float-end fs-7 text-danger"><i class="bi bi-star-fill"></i></span>--}}
                {{--                                    </h3>--}}
                {{--                                    <p class="fs-7">Call me whenever you can...</p>--}}
                {{--                                    <p class="fs-7 text-secondary"><i class="bi bi-clock-fill me-1"></i> 4 Hours Ago--}}
                {{--                                    </p>--}}
                {{--                                </div>--}}
                {{--                            </div> <!--end::Message-->--}}
                {{--                        </a>--}}
                {{--                        <div class="dropdown-divider"></div>--}}
                {{--                        <a href="#" class="dropdown-item"> <!--begin::Message-->--}}
                {{--                            <div class="d-flex">--}}
                {{--                                <div class="flex-shrink-0"><img src="../../../dist/assets/img/user8-128x128.jpg"--}}
                {{--                                                                alt="User Avatar"--}}
                {{--                                                                class="img-size-50 rounded-circle me-3"></div>--}}
                {{--                                <div class="flex-grow-1">--}}
                {{--                                    <h3 class="dropdown-item-title">--}}
                {{--                                        John Pierce--}}
                {{--                                        <span class="float-end fs-7 text-secondary"> <i--}}
                {{--                                                class="bi bi-star-fill"></i> </span>--}}
                {{--                                    </h3>--}}
                {{--                                    <p class="fs-7">I got your message bro</p>--}}
                {{--                                    <p class="fs-7 text-secondary"><i class="bi bi-clock-fill me-1"></i> 4 Hours Ago--}}
                {{--                                    </p>--}}
                {{--                                </div>--}}
                {{--                            </div> <!--end::Message-->--}}
                {{--                        </a>--}}
                {{--                        <div class="dropdown-divider"></div>--}}
                {{--                        <a href="#" class="dropdown-item"> <!--begin::Message-->--}}
                {{--                            <div class="d-flex">--}}
                {{--                                <div class="flex-shrink-0"><img src="../../../dist/assets/img/user3-128x128.jpg"--}}
                {{--                                                                alt="User Avatar"--}}
                {{--                                                                class="img-size-50 rounded-circle me-3"></div>--}}
                {{--                                <div class="flex-grow-1">--}}
                {{--                                    <h3 class="dropdown-item-title">--}}
                {{--                                        Nora Silvester--}}
                {{--                                        <span class="float-end fs-7 text-warning"> <i--}}
                {{--                                                class="bi bi-star-fill"></i> </span>--}}
                {{--                                    </h3>--}}
                {{--                                    <p class="fs-7">The subject goes here</p>--}}
                {{--                                    <p class="fs-7 text-secondary"><i class="bi bi-clock-fill me-1"></i> 4 Hours Ago--}}
                {{--                                    </p>--}}
                {{--                                </div>--}}
                {{--                            </div> <!--end::Message-->--}}
                {{--                        </a>--}}
                {{--                        <div class="dropdown-divider"></div>--}}
                {{--                        <a href="#" class="dropdown-item dropdown-footer">See All Messages</a>--}}
                {{--                    </div>--}}
                {{--                </li> <!--end::Messages Dropdown Menu--> <!--begin::Notifications Dropdown Menu-->--}}
                {{--                <li class="nav-item dropdown"><a class="nav-link" data-bs-toggle="dropdown" href="#"> <i--}}
                {{--                            class="bi bi-bell-fill"></i> <span class="navbar-badge badge text-bg-warning">15</span> </a>--}}
                {{--                    <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end"><span--}}
                {{--                            class="dropdown-item dropdown-header">15 Notifications</span>--}}
                {{--                        <div class="dropdown-divider"></div>--}}
                {{--                        <a href="#" class="dropdown-item"> <i class="bi bi-envelope me-2"></i> 4 new messages--}}
                {{--                            <span class="float-end text-secondary fs-7">3 mins</span> </a>--}}
                {{--                        <div class="dropdown-divider"></div>--}}
                {{--                        <a href="#" class="dropdown-item"> <i class="bi bi-people-fill me-2"></i> 8 friend requests--}}
                {{--                            <span class="float-end text-secondary fs-7">12 hours</span> </a>--}}
                {{--                        <div class="dropdown-divider"></div>--}}
                {{--                        <a href="#" class="dropdown-item"> <i class="bi bi-file-earmark-fill me-2"></i> 3 new reports--}}
                {{--                            <span class="float-end text-secondary fs-7">2 days</span> </a>--}}
                {{--                        <div class="dropdown-divider"></div>--}}
                {{--                        <a href="#" class="dropdown-item dropdown-footer">--}}
                {{--                            See All Notifications--}}
                {{--                        </a>--}}
                {{--                    </div>--}}
                {{--                </li> <!--end::Notifications Dropdown Menu--> <!--begin::Fullscreen Toggle-->--}}
                <li class="nav-item"><a class="nav-link" href="#" data-lte-toggle="fullscreen"> <i
                            data-lte-icon="maximize" class="bi bi-arrows-fullscreen"></i> <i data-lte-icon="minimize"
                                                                                             class="bi bi-fullscreen-exit"
                                                                                             style="display: none;"></i>
                    </a></li> <!--end::Fullscreen Toggle--> <!--begin::User Menu Dropdown-->
                {{--                <li class="nav-item dropdown user-menu"><a href="#" class="nav-link dropdown-toggle"--}}
                {{--                                                           data-bs-toggle="dropdown"> <img--}}
                {{--                            src="../../../dist/assets/img/user2-160x160.jpg" class="user-image rounded-circle shadow"--}}
                {{--                            alt="User Image"> <span class="d-none d-md-inline">Alexander Pierce</span> </a>--}}
                {{--                    <ul class="dropdown-menu dropdown-menu-lg dropdown-menu-end"> <!--begin::User Image-->--}}
                {{--                        <li class="user-header text-bg-primary"><img src="../../../dist/assets/img/user2-160x160.jpg"--}}
                {{--                                                                     class="rounded-circle shadow" alt="User Image">--}}
                {{--                            <p>--}}
                {{--                                Alexander Pierce - Web Developer--}}
                {{--                                <small>Member since Nov. 2023</small>--}}
                {{--                            </p>--}}
                {{--                        </li> <!--end::User Image--> <!--begin::Menu Body-->--}}
                {{--                        <li class="user-body"> <!--begin::Row-->--}}
                {{--                            <div class="row">--}}
                {{--                                <div class="col-4 text-center"><a href="#">Followers</a></div>--}}
                {{--                                <div class="col-4 text-center"><a href="#">Sales</a></div>--}}
                {{--                                <div class="col-4 text-center"><a href="#">Friends</a></div>--}}
                {{--                            </div> <!--end::Row-->--}}
                {{--                        </li> <!--end::Menu Body--> <!--begin::Menu Footer-->--}}
                {{--                        <li class="user-footer"><a href="#" class="btn btn-default btn-flat">Profile</a> <a href="#"--}}
                {{--                                                                                                            class="btn btn-default btn-flat float-end">Sign--}}
                {{--                                out</a></li> <!--end::Menu Footer-->--}}
                {{--                    </ul>--}}
                {{--                </li> <!--end::User Menu Dropdown-->--}}
            </ul> <!--end::End Navbar Links-->
        </div> <!--end::Container-->
    </nav> <!--end::Header--> <!--begin::Sidebar-->
    <aside class="app-sidebar bg-body-secondary shadow" data-bs-theme="dark"> <!--begin::Sidebar Brand-->
        <div class="sidebar-brand"> <!--begin::Brand Link--> <a href="../index.html" class="brand-link">
                <!--begin::Brand Image--> <img
                    src="{{ asset('favicon/favicon-'.strtolower(app()->environment()).'.png') }}"
                    alt="Foresight Dev Portal"
                    class="brand-image opacity-75 shadow"> <!--end::Brand Image-->
                <!--begin::Brand Text--> <span class="brand-text fw-light">Dev Portal</span> <!--end::Brand Text--> </a>
            <!--end::Brand Link--> </div> <!--end::Sidebar Brand--> <!--begin::Sidebar Wrapper-->
        <div class="sidebar-wrapper">
            <nav class="mt-2"> <!--begin::Sidebar Menu-->
                <ul class="nav sidebar-menu flex-column" data-lte-toggle="treeview" role="menu" data-accordion="false">
                    <li class="nav-item"><a href="{{ route('Debug::dashboard') }}"
                                            class="nav-link @if (request()->routeIs('Debug::dashboard')) active @endif">
                            <i class="nav-icon bi bi-speedometer"></i>
                            <p>Dashboard</p>
                        </a></li>

                    <li class="nav-item @if (request()->routeIs('Debug::logs.*')) menu-open @endif">
                        <a href="#" class="nav-link">
                            <i class="nav-icon bi bi-clipboard-fill"></i>
                            <p>
                                Logs
                                {{--                                <span class="nav-badge badge text-bg-secondary me-3">6</span>--}}
                                <i class="nav-arrow bi bi-chevron-right"></i>
                            </p>
                        </a>
                        <ul class="nav nav-treeview">
                            <li class="nav-item">
                                <a href="{{ route('Debug::logs.request.index') }}"
                                   class="nav-link @if (request()->routeIs('Debug::logs.request.*')) active @endif">
                                    <i class="nav-icon bi bi-circle fs-8"></i>
                                    <p><small>Requests</small></p>
                                </a></li>
                            <li class="nav-item">
                                <a href="{{ route('Debug::logs.developer.alerts.index') }}"
                                   class="nav-link @if (request()->routeIs('Debug::logs.developer.alerts.*')) active @endif">
                                    <i class="nav-icon bi bi-circle fs-8"></i>
                                    <p><small>Developer Alerts</small></p>
                                </a></li>
                            <li class="nav-item">
                                <a href="{{ route('Debug::logs.webhook.incoming.index') }}"
                                   class="nav-link @if (request()->routeIs('Debug::logs.webhook.incoming.*')) active @endif">
                                    <i class="nav-icon bi bi-circle fs-8"></i>
                                    <p><small>Webhook Incoming</small></p>
                                </a></li>
                            <li class="nav-item"><a href="{{ route('Debug::logs.webhook.outgoing.index') }}"
                                                    class="nav-link @if (request()->routeIs('Debug::logs.webhook.outgoing.*')) active @endif">
                                    <i
                                        class="nav-icon bi bi-circle fs-8"></i>
                                    <p><small>Webhook Outgoing</small></p>
                                </a></li>

                            <li class="nav-item"><a href="{{ route('Debug::logs.vendor.public.api.index') }}"
                                                    class="nav-link @if (request()->routeIs('Debug::logs.vendor.public.api.*')) active @endif">
                                    <i
                                        class="nav-icon bi bi-circle fs-8"></i>
                                    <p><small>Vendor Public API</small></p>
                                </a></li>
                        </ul>
                    </li>
                </ul> <!--end::Sidebar Menu-->
            </nav>
        </div> <!--end::Sidebar Wrapper-->
    </aside> <!--end::Sidebar--> <!--begin::App Main-->
    <main class="app-main"> <!--begin::App Content Header-->
        <div class="app-content-header"> <!--begin::Container-->
            <div class="container-fluid"> <!--begin::Row-->
                <div class="row">
                    <div class="col-sm-6">
                        <h3 class="mb-0">{{ $heading ?? "" }}</h3>
                    </div>
                    {{--                    <div class="col-sm-6">--}}
                    {{--                        <ol class="breadcrumb float-sm-end">--}}
                    {{--                            <li class="breadcrumb-item"><a href="#">Home</a></li>--}}
                    {{--                            <li class="breadcrumb-item active" aria-current="page">--}}
                    {{--                                Collapsed Sidebar--}}
                    {{--                            </li>--}}
                    {{--                        </ol>--}}
                    {{--                    </div>--}}
                </div> <!--end::Row-->
            </div> <!--end::Container-->
        </div> <!--end::App Content Header--> <!--begin::App Content-->
        <div class="app-content"> <!--begin::Container-->
            @yield('content')
        </div> <!--end::App Content-->
    </main> <!--end::App Main--> <!--begin::Footer-->
{{--    <footer class="app-footer"> <!--begin::To the end-->--}}
{{--        <div class="float-end d-none d-sm-inline">Anything you want</div> <!--end::To the end--> <!--begin::Copyright-->--}}
{{--        <strong>--}}
{{--            Powered By--}}
{{--            <a href="https://lula.life" class="text-decoration-none">Lula</a>.--}}
{{--        </strong>--}}
{{--        All rights reserved.--}}
{{--        <!--end::Copyright-->--}}
{{--    </footer> <!--end::Footer-->--}}
</div> <!--end::App Wrapper--> <!--begin::Script--> <!--begin::Third Party Plugin(OverlayScrollbars)-->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="https://kit.fontawesome.com/6138cc94e2.js" crossorigin="anonymous"></script>

<script src="https://cdn.jsdelivr.net/npm/overlayscrollbars@2.3.0/browser/overlayscrollbars.browser.es6.min.js"
        integrity="sha256-H2VM7BKda+v2Z4+DRy69uknwxjyDRhszjXFhsL4gD3w=" crossorigin="anonymous"></script>
<!--end::Third Party Plugin(OverlayScrollbars)--><!--begin::Required Plugin(popperjs for Bootstrap 5)-->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"
        integrity="sha256-whL0tQWoY1Ku1iskqPFvmZ+CHsvmRWx/PIoEvIeWh4I=" crossorigin="anonymous"></script>
<!--end::Required Plugin(popperjs for Bootstrap 5)--><!--begin::Required Plugin(Bootstrap 5)-->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.min.js"
        integrity="sha256-YMa+wAM6QkVyz999odX7lPRxkoYAan8suedu4k2Zur8=" crossorigin="anonymous"></script>
<!--end::Required Plugin(Bootstrap 5)--><!--begin::Required Plugin(AdminLTE)-->
<script src="{{ asset('layouts/developer/admin-lte/js/adminlte.min.js') }}"></script>
<!--end::Required Plugin(AdminLTE)--><!--begin::OverlayScrollbars Configure-->
<script>
    const SELECTOR_SIDEBAR_WRAPPER = ".sidebar-wrapper";
    const Default = {
        scrollbarTheme: "os-theme-light",
        scrollbarAutoHide: "leave",
        scrollbarClickScroll: true,
    };
    document.addEventListener("DOMContentLoaded", function () {
        const sidebarWrapper = document.querySelector(SELECTOR_SIDEBAR_WRAPPER);
        if (
            sidebarWrapper &&
            typeof OverlayScrollbarsGlobal?.OverlayScrollbars !== "undefined"
        ) {
            OverlayScrollbarsGlobal.OverlayScrollbars(sidebarWrapper, {
                scrollbars: {
                    theme: Default.scrollbarTheme,
                    autoHide: Default.scrollbarAutoHide,
                    clickScroll: Default.scrollbarClickScroll,
                },
            });
        }
    });
</script> <!--end::OverlayScrollbars Configure--> <!--end::Script-->

@stack('scripts')

</body><!--end::Body-->

</html>


{{--<!doctype html>--}}
{{--<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">--}}
{{--<head>--}}
{{--    <meta charset="utf-8">--}}
{{--    <meta name="viewport" content="width=device-width, initial-scale=1">--}}

{{--    <!-- CSRF Token -->--}}
{{--    <meta name="csrf-token" content="{{ csrf_token() }}">--}}

{{--    <title>{{ config('app.name', 'Laravel') }}</title>--}}

{{--    <link rel="icon" type="image/x-icon" href="{{ asset('favicon/favicon-'.strtolower(app()->environment()).'.png') }}">--}}

{{--    <!-- Fonts -->--}}
{{--    <link rel="dns-prefetch" href="//fonts.bunny.net">--}}
{{--    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet">--}}
{{--    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"--}}
{{--          integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">--}}

{{--    <link rel="stylesheet" href="{{asset('layouts/developer/admin-lte/css/adminlte.min.css')}}">--}}
{{--    @stack('css')--}}

{{--    <!-- Scripts -->--}}
{{--    @stack('vite')--}}
{{--</head>--}}
{{--<body class="hold-transition layout-top-nav bg-light">--}}
{{--<div class="wrapper">--}}

{{--    <nav class="main-header navbar navbar-expand-md navbar-light navbar-white mb-3">--}}
{{--        <div class="container">--}}
{{--            <a href="{{ route('request-logs.index') }}" class="navbar-brand">--}}
{{--                <img src="{{ asset('favicon/favicon-'.strtolower(app()->environment()).'.png') }}"--}}
{{--                     alt="Foresight Developer Console" class="brand-image img-circle elevation-3" style="opacity: .8">--}}
{{--                <span class="brand-text font-weight-light">Foresight Developer Console</span>--}}
{{--            </a>--}}
{{--            <button class="navbar-toggler order-1" type="button" data-toggle="collapse" data-target="#navbarCollapse"--}}
{{--                    aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">--}}
{{--                <span class="navbar-toggler-icon"></span>--}}
{{--            </button>--}}
{{--        </div>--}}
{{--        <div class="container">--}}
{{--            <div class="collapse navbar-collapse order-3" id="navbarCollapse">--}}
{{--                <ul class="navbar-nav">--}}
{{--                    <li class="nav-item" title="Request Logs">--}}
{{--                        <a href="{{ route('request-logs.index') }}"--}}
{{--                           class="nav-link @if (request()->routeIs('request-logs.index', 'request-logs.show')) active @endif">Request--}}
{{--                            Logs</a>--}}
{{--                    </li>--}}
{{--                    @if (config('telescope.enabled') && app()->isLocal())--}}
{{--                        <li class="nav-item">--}}
{{--                            <a href="{{ url('telescope') }}" class="nav-link">Telescope</a>--}}
{{--                        </li>--}}
{{--                    @endif--}}
{{--                    <li class="nav-item" title="Model: WebhookCalls">--}}
{{--                        <a href="{{ route('webhook.incoming.index') }}"--}}
{{--                           class="nav-link @if (request()->routeIs('webhook.incoming.index', 'webhook.incoming.show')) active @endif">Incoming--}}
{{--                            Webhook Logs</a>--}}
{{--                    </li>--}}
{{--                    <li class="nav-item" title="Model: PublicApiWorkOrderWebhookEvent">--}}
{{--                        <a href="{{ route('webhook.outgoing.index') }}"--}}
{{--                           class="nav-link @if (request()->routeIs('webhook.outgoing.index', 'webhook.outgoing.show')) active @endif">Outgoing--}}
{{--                            Webhook Logs</a>--}}
{{--                    </li>--}}
{{--                    --}}{{--                    <li class="nav-item dropdown">--}}
{{--                    --}}{{--                        <a id="dropdownSubMenu1" href="#" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="nav-link dropdown-toggle">Dropdown</a>--}}
{{--                    --}}{{--                        <ul aria-labelledby="dropdownSubMenu1" class="dropdown-menu border-0 shadow">--}}
{{--                    --}}{{--                            <li><a href="#" class="dropdown-item">Some action </a></li>--}}
{{--                    --}}{{--                            <li><a href="#" class="dropdown-item">Some other action</a></li>--}}
{{--                    --}}{{--                            <li class="dropdown-divider"></li>--}}

{{--                    --}}{{--                            <li class="dropdown-submenu dropdown-hover">--}}
{{--                    --}}{{--                                <a id="dropdownSubMenu2" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="dropdown-item dropdown-toggle">Hover for action</a>--}}
{{--                    --}}{{--                                <ul aria-labelledby="dropdownSubMenu2" class="dropdown-menu border-0 shadow">--}}
{{--                    --}}{{--                                    <li>--}}
{{--                    --}}{{--                                        <a tabindex="-1" href="#" class="dropdown-item">level 2</a>--}}
{{--                    --}}{{--                                    </li>--}}

{{--                    --}}{{--                                    <li class="dropdown-submenu">--}}
{{--                    --}}{{--                                        <a id="dropdownSubMenu3" href="#" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" class="dropdown-item dropdown-toggle">level 2</a>--}}
{{--                    --}}{{--                                        <ul aria-labelledby="dropdownSubMenu3" class="dropdown-menu border-0 shadow">--}}
{{--                    --}}{{--                                            <li><a href="#" class="dropdown-item">3rd level</a></li>--}}
{{--                    --}}{{--                                            <li><a href="#" class="dropdown-item">3rd level</a></li>--}}
{{--                    --}}{{--                                        </ul>--}}
{{--                    --}}{{--                                    </li>--}}

{{--                    --}}{{--                                    <li><a href="#" class="dropdown-item">level 2</a></li>--}}
{{--                    --}}{{--                                    <li><a href="#" class="dropdown-item">level 2</a></li>--}}
{{--                    --}}{{--                                </ul>--}}
{{--                    --}}{{--                            </li>--}}

{{--                    --}}{{--                        </ul>--}}
{{--                    --}}{{--                    </li>--}}
{{--                </ul>--}}

{{--                --}}{{--                <form class="form-inline ml-0 ml-md-3">--}}
{{--                --}}{{--                    <div class="input-group input-group-sm">--}}
{{--                --}}{{--                        <input class="form-control form-control-navbar" type="search" placeholder="Search" aria-label="Search">--}}
{{--                --}}{{--                        <div class="input-group-append">--}}
{{--                --}}{{--                            <button class="btn btn-navbar" type="submit">--}}
{{--                --}}{{--                                <i class="fas fa-search"></i>--}}
{{--                --}}{{--                            </button>--}}
{{--                --}}{{--                        </div>--}}
{{--                --}}{{--                    </div>--}}
{{--                --}}{{--                </form>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </nav>--}}


{{--    <div class="content-wrapper">--}}
{{--        --}}{{--        <div class="mt-3 bg-transparent">--}}

{{--        --}}{{--        </div>--}}

{{--    </div>--}}


{{--    <aside class="control-sidebar control-sidebar-dark">--}}

{{--    </aside>--}}


{{--    --}}{{--    <footer class="main-footer">--}}

{{--    --}}{{--        <div class="float-right d-none d-sm-inline">--}}
{{--    --}}{{--            Anything you want--}}
{{--    --}}{{--        </div>--}}

{{--    --}}{{--        <strong>Copyright &copy; {{now()->year}} <a href="{{ url('') }}">Foresight</a>.</strong> All rights reserved.--}}
{{--    --}}{{--    </footer>--}}
{{--</div>--}}

{{--<script src="https://code.jquery.com/jquery-3.7.1.min.js"--}}
{{--        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>--}}
{{--<script src="{{asset('layouts/developer/admin-lte/js/adminlte.min.js')}}"></script>--}}
{{--<script src="https://kit.fontawesome.com/6138cc94e2.js" crossorigin="anonymous"></script>--}}
{{--@stack('scripts')--}}
{{--</body>--}}
{{--</html>--}}

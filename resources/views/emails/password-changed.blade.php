<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en" xml:lang="en">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <title>Password Reset</title>
    <style type="text/css">
        /* ========== DEFAULT/STARTER STYLES (Light Mode) ========== */
        #light-img {
            display: block !important;
            height: 31.3px !important;
            width: 120px !important;
        }

        #dark-img {
            display: none !important;
            height: 31.3px !important;
            width: 120px !important;
        }

        /* ========== DARK MODE OVERRIDES ========== */
        /* Must come AFTER default styles to override them */
        @media (prefers-color-scheme: dark) {
            #light-img {
                display: block !important;
            }

            #dark-img {
                display: none !important;
            }
        }

        /* ========== OUTLOOK-SPECIFIC DARK MODE ========== */
        /* Comes last as final override */
        [data-ogsc] #light-img {
            display: none !important;
        }

        [data-ogsc] #dark-img {
            display: block !important;
        }

        /* Client-specific styles */
        body,
        table,
        td,
        a {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table,
        td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }

        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }


        body {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
        }

        /* iOS BLUE LINKS */
        a[x-apple-data-detectors] {
            color: inherit !important;
            text-decoration: none !important;
            font-size: inherit !important;
            font-family: inherit !important;
            font-weight: inherit !important;
            line-height: inherit !important;
        }

        /* Main styles */
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            background-color: #F2F4F7;
            color: #000000;
        }

        h4,
        .h4 {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-size: 20px;
            line-height: 28px;
            font-weight: 500;
            letter-spacing: 0px;
            color: #101828;
        }

        .button {
            display: inline-block;
            padding: 8px 24px;
            background-color: #1369E9;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            font-size: 14px;
        }

        /* Responsive styles */
        @media screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }

            .content {
                padding: 20px 15px !important;
            }

            h1 {
                font-size: 18px !important;
            }

            p,
            .subtitle {
                font-size: 14px !important;
            }
        }
    </style>
</head>

<body style="margin: 0; padding: 15px 0 0 0; background-color: #F2F4F7;">

    <!--[if (gte mso 9)|(IE)]>
<table width="100%" cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td>
<![endif]-->

    <!-- Main Email Container -->
    <table   style="background-color: #F2F4F7;text-align:center;">
        <tbody>
        <tr>
            <td  style="padding: 15px 0;text-align:center;">
                <!--[if (gte mso 9)|(IE)]>
            <table width="445" align="center" cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td>
            <![endif]-->

                <table class="email-container"  style="width: 445px; max-width: 445px; background-color: #ffffff; border: 1px solid #e0e0e0; border-radius: 16px; box-shadow: 0px 20px 32px -8px rgba(16, 24, 40, 0.05);">
                   <tbody>
                    <tr>
                        <td class="header" style="padding: 30px 20px;">

                            <img id="light-img"
                                 src="{{ asset('/img/foresight_dark_logo.png') }}"
                                alt="Logo Here"
                                height="31.3"
                                width="120"
                                style="display:block;height:31.3px;width:120px;">

                            <!-- Dark mode image (hidden by default) -->
                            <img id="dark-img"
                                src="{{ asset('/img/foresight_white_logo.png') }}"
                                alt="Logo Here"
                                height="31.3"
                                width="120"
                                style="display:block;height:31.3px;width:120px;">
                        </td>
                    </tr>

                    <!-- Content -->
                    <tr>
                        <td class="content" style="padding: 0 20px 30px 20px;text-align:left;">
                            <h4 style="font-family: Arial, sans-serif; color: #101828; font-size: 20px; font-weight: 500; letter-spacing: 0px; line-height: 26px; margin-top: 0; margin-bottom: 20px;">Password Changed</h4>
                            <p class="subtitle" style="font-family: Arial, sans-serif; color: #344054; line-height: 20px; margin-bottom: 10px; font-weight: 400; letter-spacing: 0px; font-size: 14px;">Your Foresight password has been successfully changed. If this was you, no further action is needed.</p>
                            <p class="subtitle" style="font-family: Arial, sans-serif; color: #344054; line-height: 20px; margin-bottom: 10px; font-weight: 400; letter-spacing: 0px; font-size: 14px;">If you didn't request this change, please reset your password immediately or contact our support team.</p>
                        </td>
                    </tr>
    </tbody>
                </table>

                <!--[if (gte mso 9)|(IE)]>
                    </td>
                </tr>
            </table>
            <![endif]-->
            </td>
        </tr>
    </tbody>
    </table>

    <!-- Footer -->
    <table   style="background-color: #F2F4F7;text-align:center;">
        <tbody>
        <tr>
            <td  style="text-align:center;">
                <table style="width:445px;max-width:445px;" >
                    <tr>
                        <td class="footer" style="text-align: center; font-size: 12px; color: #999999;">
                            <div class="address" style="margin-bottom: 10px;">7285 W. 132nd Street, Suite 150, Overland Park, KS 68213</div>
                            <div>Foresight © {{ date('Y') }} All Rights Reserved</div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </tbody>
    </table>

    <!--[if (gte mso 9)|(IE)]>
        </td>
    </tr>
</table>
<![endif]-->

</body>

</html>
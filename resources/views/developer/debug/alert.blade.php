<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Developer Alert Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 20px auto;
            background-color: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            background-color: #4d6885;
            text-align: center;
            color: #ffffff;
            padding: 10px 20px;
            border-radius: 10px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .content {
            padding: 20px;
            line-height: 1.6;
        }
        .content h2 {
            color: #333333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ $data['message'] ?? 'Developer Alert Notification' }}</h1>
        </div>
        <div class="content">
            <p>Hello,</p>
            <p>Please take the necessary actions for this alert: {{ $data['message'] ?? '' }}</p>
            <ul>
                @foreach ($data as $key => $value)
                    @if ($key !== 'message')
                        <li><strong>{{ $key }}:</strong> {{ $value }}</li>
                    @endif
                @endforeach
            </ul>
        </div>
    </div>
</body>
</html>

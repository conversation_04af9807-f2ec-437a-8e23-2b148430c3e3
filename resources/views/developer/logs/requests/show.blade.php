@extends('layouts.admin-lte', ['heading' => "Request Logs"])
@push('css')
    <style>
        .json-viewer-theme-dark, .json-viewer-theme-light {
            padding: 20px;
        }
    </style>
@endpush
@section('content')
    <div class="container">
        <div class="card">
            <div class="card-header">Content</div>
            <div class="card-body">
                <div class="json-viewer"></div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/@textea/json-viewer@3"></script>
    <script>
        const data = JSON.parse(@json(json_encode($requestLog->content)))
        new JsonViewer({
            value: data,
            theme: 'auto'
        }).render('.json-viewer')

    </script>
@endpush

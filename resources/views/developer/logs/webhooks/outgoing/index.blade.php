@extends('layouts.admin-lte', ['heading' => "Webhook Outgoing"])

@push('css')
    <link href="https://cdn.datatables.net/2.0.0/css/dataTables.bootstrap5.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
@endpush
@push('vite')
    @vite(['resources/css/app.css', 'resources/js/app.js'])
@endpush
@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        Webhook Calls
                        <span class="float-end">
                            <a href="#" class="btn btn-sm btn-outline-secondary"
                               onclick="LaravelDataTables ? LaravelDataTables.webhook_call_id.ajax.reload(): false;">
                                <i class="fa fa-rotate-right"></i>
                            </a>
                        </span>
                    </div>
{{--                    <div class="card-body">--}}
{{--                        <div class="row justify-content-end">--}}
{{--                            <div class="col-md-4">--}}
{{--                                <label for="search" class="form-label">Search</label>--}}
{{--                                <input type="text" class="form-control" name="search[value]" id="search">--}}
{{--                            </div>--}}
{{--                            <div class="col-md-3">--}}
{{--                                <label for="datetimes" class="form-label">Period</label>--}}
{{--                                <input type="text" class="form-control" name="datetimes" id="datetimes">--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    </div>--}}
                    <div class="card-body table-responsive">
                        {!! $dataTable->table() !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="https://cdn.datatables.net/2.0.0/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.0.0/js/dataTables.bootstrap5.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-throttle-debounce/1.1/jquery.ba-throttle-debounce.min.js"
            integrity="sha512-JZSo0h5TONFYmyLMqp8k4oPhuo6yNk9mHM+FY50aBjpypfofqtEWsAgRDQm94ImLCzSaHeqNvYuD9382CEn2zw=="
            crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    {{ $dataTable->scripts(attributes: ['type' => 'module']) }}

    <script>
        $(document).ajaxSuccess(function () {
            $('[data-toggle="popover"]').popover({
                container: 'body',
                placement: 'auto'
            })
            $("[data-toggle=tooltip]").tooltip();
            // any other code
        });
    </script>

    <script>
        $(function () {
            $('input[name="datetimes"]').daterangepicker({
                timePicker: true,
                startDate: moment().subtract(1, 'day'),
                endDate: moment().endOf('day'),
                locale: {
                    format: 'DD-MM hh:mm A'
                }
            });

            $('input[name="datetimes"]').on('apply.daterangepicker', function (ev, picker) {
                LaravelDataTables.webhook_call_id.ajax.reload();
            });

            $('#search').keyup($.debounce(250, () => LaravelDataTables.webhook_call_id.search($('#search').val()).draw()));
        });
    </script>
@endpush

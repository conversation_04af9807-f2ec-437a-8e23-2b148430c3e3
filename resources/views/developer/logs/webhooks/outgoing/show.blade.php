@extends('layouts.admin-lte', ['heading' => "Webhook Outgoing"])
@push('css')
    <style>
        .json-viewer-theme-dark, .json-viewer-theme-light {
            padding: 20px;
        }
    </style>
@endpush
@section('content')
    <div class="container">

        <div class="container">
            <div class="accordion" id="webhookCallsAccording">
                <div class="row">
                    <div class="col">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="panelsStayOpen-headingOne">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#panelsStayOpen-collapseOne" aria-expanded="true"
                                        aria-controls="panelsStayOpen-collapseOne">
                                    Webhook Outgoing Payload
                                </button>
                            </h2>
                            <div id="panelsStayOpen-collapseOne" class="accordion-collapse collapse show"
                                 aria-labelledby="panelsStayOpen-headingOne">
                                <div class="accordion-body">
                                    <div class="json-viewer-payload"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="panelsStayOpen-headingThree">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#panelsStayOpen-collapseThree" aria-expanded="true"
                                        aria-controls="panelsStayOpen-collapseThree">
                                    Response
                                </button>
                            </h2>
                            <div id="panelsStayOpen-collapseThree" class="accordion-collapse collapse show"
                                 aria-labelledby="panelsStayOpen-headingThree">
                                <div class="accordion-body">
                                    <div class="json-viewer-response"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/@textea/json-viewer@3"></script>
    <script>
        const payload = JSON.parse(@json(json_encode($publicApiWorkOrderWebhookEvent->payload)))
        new JsonViewer({
            value: payload,
            theme: 'auto'
        }).render('.json-viewer-payload')

        const response = JSON.parse(@json(json_encode($publicApiWorkOrderWebhookEvent->response)))
        new JsonViewer({
            value: response,
            theme: 'auto'
        }).render('.json-viewer-response')
    </script>
@endpush

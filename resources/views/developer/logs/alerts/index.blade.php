@extends('layouts.admin-lte', ['heading' => "Developer Alerts"])

@push('css')
    <link href="https://cdn.datatables.net/2.0.0/css/dataTables.bootstrap5.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
@endpush
@push('vite')
    @vite(['resources/css/app.css', 'resources/js/app.js'])
@endpush
@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        Request Logs
                        <span class="float-end">
                            <a href="javascript:void(0)" class="btn btn-sm btn-outline-danger btn-clear-filter"
                               onclick="">
                                <i class="fa fa-close"></i> Clear Filters
                            </a>
                            <a href="javascript:void(0)" class="btn btn-sm btn-outline-secondary btn-reload"
                               onclick="LaravelDataTables ? LaravelDataTables.developer_alert_id.ajax.reload(): false;">
                                <i class="fa fa-rotate-right"></i>
                            </a>
                        </span>
                    </div>
                    <div class="card-body py-2 px-3 form-filters">
                        <div class="row justify-content-end">
                            <div class="col-md-6">
                                <div class="form-group mb-2">
                                    <label for="search" class="form-label col-form-label-sm">Search</label>
                                    <input type="text" class="form-control form-control-sm" name="search[value]"
                                           id="search" value="{{ request()->input('search.value') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-2">
                                    <label for="organizations"
                                           class="form-label col-form-label-sm">Organizations</label>
                                    <select class="form-select trigger-search form-control-sm" name="organization"
                                            aria-label="Default select example" id="organizations">
                                        <option selected value="">Organization</option>
                                        @foreach ($organizations as $organizationUuid => $organization)
                                            <option
                                                @if (request()->input('organization') === $organizationUuid) selected
                                                @endif value="{{ $organizationUuid }}">{{ $organization }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group mb-2">
                                    <label for="datetimes" class="form-label col-form-label-sm">Period</label>
                                    <input type="text" class="form-control form-control-sm" name="datetimes"
                                           id="datetimes" value="{{ request()->input('date_range') }}">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body table-responsive">
                        {!! $dataTable->table() !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="https://cdn.datatables.net/2.0.0/js/dataTables.js"></script>
    <script src="https://cdn.datatables.net/2.0.0/js/dataTables.bootstrap5.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-throttle-debounce/1.1/jquery.ba-throttle-debounce.min.js"
            integrity="sha512-JZSo0h5TONFYmyLMqp8k4oPhuo6yNk9mHM+FY50aBjpypfofqtEWsAgRDQm94ImLCzSaHeqNvYuD9382CEn2zw=="
            crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    {{ $dataTable->scripts(attributes: ['type' => 'module']) }}

    <script>
        $(document).ajaxSuccess(function () {
            $('[data-toggle="popover"]').popover({
                container: 'body',
                placement: 'auto'
            })
            $("[data-toggle=tooltip]").tooltip();
            // any other code
        });
    </script>

    <script>
        let defaultStartDate = moment().subtract(1, 'day');
        let defaultEndDate = moment().endOf('day');
        $(function () {
            let dateRange = `{{ request()->input('date_range') }}` || (defaultStartDate.format('DD-MM hh:mm A') + ' - ' + defaultEndDate.format('DD-MM hh:mm A'));
            dateRange = dateRange.split(' - ');
            dateRange.forEach((date, index) => {
                dateRange[index] = moment(date, "DD-MM hh:mm A")
            });

            $('#datetimes').daterangepicker({
                timePicker: true,
                startDate: dateRange[0],
                endDate: dateRange[1],
                locale: {
                    format: 'DD-MM hh:mm A'
                }
            });

            $('#datetimes').on('apply.daterangepicker', function (ev, picker) {
                LaravelDataTables.developer_alert_id.ajax.reload();
            });

            $('.trigger-search').change($.debounce(250, () => LaravelDataTables.developer_alert_id.search($('#search').val()).draw()));
            $('#search').keyup($.debounce(250, () => LaravelDataTables.developer_alert_id.search($('#search').val()).draw()));

            $('.btn-clear-filter').click(function () {
                $('.form-filters input:not([name="datetimes"]),select').val('');

                $('#datetimes').data('daterangepicker').setStartDate(defaultStartDate);
                $('#datetimes').data('daterangepicker').setEndDate(defaultEndDate);

                LaravelDataTables.developer_alert_id.search($('#search').val()).draw();
            });
        });

        $(document).ready(function () {
            if ($('.form-filters input:not([name="datetimes"]),select').val()) {
                setTimeout(() => $('.btn-reload').click(), 100);
            }
        });
    </script>
@endpush

@extends('layouts.admin-lte', ['heading' => "Developer Alerts"])
@push('css')
    <style>
        .json-viewer-theme-dark, .json-viewer-theme-light {
            padding: 20px;
        }
    </style>
@endpush

@section('content')
    <div class="container">
        <div class="accordion" id="webhookCallsAccording">
            <div class="accordion-item mb-3">
                <h2 class="accordion-header" id="panelsStayOpen-headingOne">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                            data-bs-target="#panelsStayOpen-collapseOne" aria-expanded="false"
                            aria-controls="panelsStayOpen-collapseOne">
                        Context
                    </button>
                </h2>
                <div id="panelsStayOpen-collapseOne" class="accordion-collapse collapse"
                     aria-labelledby="panelsStayOpen-headingOne">
                    <div class="accordion-body">
                        <div class="json-viewer-context"></div>
                    </div>
                </div>
            </div>
            <div class="accordion-item mb-3">
                <h2 class="accordion-header" id="panelsStayOpen-headingTwo">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#panelsStayOpen-collapseTwo" aria-expanded="false"
                            aria-controls="panelsStayOpen-collapseTwo">
                        Extra
                    </button>
                </h2>
                <div id="panelsStayOpen-collapseTwo" class="accordion-collapse collapse"
                     aria-labelledby="panelsStayOpen-headingTwo">
                    <div class="accordion-body">
                        <div class="json-viewer-extra"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="container">

    </div>
    <div class="container">

    </div>
@endsection

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/@textea/json-viewer@3"></script>
    <script>
        const context = JSON.parse(@json(json_encode($developerAlert->context)))
        new JsonViewer({
            value: context,
            theme: 'auto'
        }).render('.json-viewer-context')

        const extra = JSON.parse(@json(json_encode($developerAlert->extra)))
        new JsonViewer({
            value: extra,
            theme: 'auto'
        }).render('.json-viewer-extra')
    </script>
@endpush

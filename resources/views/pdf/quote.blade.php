@php use App\Enums\QuoteStatus;use App\Enums\QuoteTaskStatus;use App\Helpers\Helper; @endphp
    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <title>Quote #{{ $quoteData->quote_number }} - WO #{{ $quoteData->work_order->work_order_number }}</title>
    <style type="text/css">
        * {
            margin: 0;
            padding: 0;
        }

        body {
            background: #fff;
            font-family: "Inter", sans-serif;
            font-size: 16px;
            padding: 0 !important;
            margin: 0 !important;
        }

        .status {

            font-weight: 500;
        }

        .pending {
            color: #FF6200;
        }

        .error {
            color: #EA1C1C;
        }

        .success {
            color: #28B97B;
        }

        .tab-divider {

            margin: 20px 0 10px 0 !important;
            border-style: none;
            border: solid 1px #D0D5DD;
        }

        .value-disabled {
            color: #AFB5C0 !important;
        }

        .page-break {
            overflow: hidden;
            page-break-after: always;
        }

        thead {
            display: table-row-group;
        }

        tfoot {
            display: table-row-group;
        }

        tr td {
            color: #101828;
        }

        p {
            word-break: break-word;
        }

        /*tr, td {*/
        /*    page-break-inside: avoid !important;*/
        /*}*/
    </style>
</head>
<body>
<table align="center" border="0" cellpadding="25" cellspacing="0"
       style="margin: 0 auto; background-color: #fff;padding: 0;"
       width="960px">
    <tr>
        <td style="padding: 25px 0;">
            <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%">
                <tr>
                    <td style="width: 50%; text-align: left;">
                        <img
                            src="data:image/jpeg;base64,{{ base64_encode(@file_get_contents($quoteData->organization->logo_uri)) }}"
                            alt="" style="width: 200px; max-height: 100px"/>
                    </td>
                    <td style="width: 50%; text-align: right;">
                        <table align="right" border="0" cellpadding="0" cellspacing="0" style="" width="100%">
                            <tr>
                                <td>
                                    <p style="color: #667085;font-weight: 500;">{{ $quoteData->organization->address }} {{ $quoteData->organization->zip_code }}</p>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <p style="color: #667085;font-weight: 500;">{{ $quoteData->organization->phone_number }}</p>
                                </td>
                            </tr>

                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td style="padding: 25px 0">
            <hr style="border: solid 1px #D0D5DD;">
        </td>
    </tr>
    <tr>
        <td valign="top" style="padding: 25px 0 0">
            <table border="0" cellpadding="15" cellspacing="15"
                   style="background-color: #F9FAFB; border: solid 1px #D0D5DD; border-radius: 12px; padding: 10px 10px 9px 10px ;"
                   width="100%">
                <tr>
                    <td>
                        <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tr>
                                <td style="width: 25%;" valign="top">
                                    <table>
                                        <tr>
                                            <td>
                                                <p style="color: #667085;font-weight: 500;padding-bottom: 6px;">Quote
                                                    Status</p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p class="status {{ $quoteData->status_class }}">{{ $quoteData->status }}</p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <td style="width: 28%;" valign="top">
                                    <table>
                                        <tr>
                                            <td>
                                                <p style="color: #667085;padding-bottom: 6px;font-weight: 500;">
                                                    Resident</p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p style="color: #101828; font-weight: 500;">{{ $quoteData->resident->name }}</p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <address
                                                    style="color: #667085; font-style: normal;font-weight: 500; line-height: 22px">
                                                    {{ $quoteData->resident->address??"" }}<br/>
                                                    {{ Helper::displayPhoneNumber($quoteData->resident->phone_number) }}
                                                </address>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <td style="width: 22%;" valign="top">
                                    <table width="100%">
                                        <tr>
                                            <td>
                                                <table width="100%">
                                                    <tr>
                                                        <td>
                                                            <p style="color: #667085;font-weight: 500;padding-bottom: 6px;">
                                                                Submit Date</p>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <p style="color: #101828;font-weight: 500;">{{ $quoteData->submitted_at }}</p>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style="height: 10px;"></td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <table width="100%">
                                                    <tr>
                                                        <td>
                                                            <p style="color: #667085;font-weight: 500;padding-bottom: 6px;">
                                                                Due Date</p>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <p style="color: #101828;font-weight: 500;">{{ $quoteData->work_order?->due_date }}</p>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <td style="width: 25%;" valign="top" align="right">
                                    <table width="100%" cellpadding="0" cellspacing="0" align="right">
                                        <tr>
                                            <td>
                                                <p style="color: #667085; text-align: right;font-weight: 500;padding-bottom: 6px;">{{ $quoteData->estimated_amount_label }}</p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p style="color: #101828; text-align: right; font-size: 32px; font-weight: bold;">
                                                    ${{ $quoteData->estimated_amount ? number_format($quoteData->estimated_amount/100, 2) : 0.00 }}</p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" cellpadding="0" cellspacing="0">
                            <tr>
                                <td style="height: 15px;"></td>
                            </tr>
                            <tr>
                                <td>
                                    <p style="color: #667085;font-weight: 500;padding-bottom: 6px;">Service Type</p>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <p style="color: #101828; font-weight: 500;">{{ $quoteData->problem->category }}
                                        > {{ $quoteData->problem->sub_category }}
                                        > {{ $quoteData->problem->diagnosis }}</p>
                                </td>
                            </tr>
                            <tr>
                                <td style="height: 25px;"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" cellpadding="0" cellspacing="0" border="0">
                            <thead>
                            <tr style="">
                                <th style="color: #667085; font-size: 12px; text-transform: uppercase; font-weight: 400; text-align: left; border-top: solid 1px #D0D5DD; border-bottom: solid 1px #D0D5DD; padding: 10px 0; width: 60%;">
                                    Quote Scope & Estimate
                                </th>
                                <th style="color: #667085; font-size: 12px; text-transform: uppercase; font-weight: 400; text-align: left; border-top: solid 1px #D0D5DD; border-bottom: solid 1px #D0D5DD; width: 13.33%; text-align: center;padding-left: 20px;font-weight: 500;">
                                    Labor
                                </th>
                                <th style="color: #667085; font-size: 12px; text-transform: uppercase; font-weight: 400; text-align: left; border-top: solid 1px #D0D5DD; border-bottom: solid 1px #D0D5DD; width: 13.33%;text-align: center;padding-left: 20px;font-weight: 500;">
                                    Material
                                </th>
                                <th style="color: #667085; font-size: 12px; text-transform: uppercase; font-weight: 400; text-align: left; border-top: solid 1px #D0D5DD; border-bottom: solid 1px #D0D5DD; width: 13.33%;text-align: right;font-weight: 500;">
                                    Line Total
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            @php $lineItemTotal = 0;@endphp
                            @foreach ($quoteData->quoteTasks as $quoteTask)
                                <tr>
                                    <td colspan="4" style="width: 60%;">
                                        <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                            <tr>
                                                <td style="color:#101828; font-weight: 500; padding: 10px 0 6px; width: 60%;">
                                                    <div style="">
                                                        Task #{{ $quoteTask->number }} - <span
                                                            class="{{ $quoteTask->status_class }}"
                                                            style="display: inline-table;position: relative;top: 4.5px;">{{ $quoteTask->status }}</span>
                                                    </div>
                                                </td>
                                                <td class="{{ $quoteTask->value_class }}"
                                                    style="color:#101828; font-weight: 500; padding: 10px 0 6px; width: 13.33%;text-align: right;">
                                                    ${{ $quoteTask->labor_amount ? number_format($quoteTask->labor_amount / 100, 2) : 0.00 }}
                                                </td>
                                                <td class="{{ $quoteTask->value_class }}"
                                                    style="color:#101828; font-weight: 500; padding: 10px 0 6px; width: 13.33%;text-align: right;">
                                                    ${{ $quoteTask->materials->sum('amount') ? number_format($quoteTask->materials->sum('amount')/100, 2) : 0.00 }}
                                                </td>
                                                <td class="{{ $quoteTask->value_class }}"
                                                    style="color:#101828; font-weight: 500; padding: 10px 0 6px; width: 13.33%;text-align: right;">
                                                    ${{ number_format(($quoteTask->labor_amount+$quoteTask->materials->sum('amount'))/100, 2) }}
                                                </td>
                                                @php
                                                    $shouldSkipLineTotalCalculation = in_array($quoteData->status_slug, [QuoteStatus::APPROVED(), QuoteStatus::PARTIALLY_APPROVED()], true) && $quoteTask->status_slug === QuoteTaskStatus::REJECTED();
                                                    $lineItemTotal += empty($shouldSkipLineTotalCalculation) ? ($quoteTask->labor_amount+$quoteTask->materials->sum('amount')) : 0;

                                                @endphp
                                            </tr>
                                            <tr>
                                                <td class="{{ $quoteTask->value_class }}" style="margin-bottom: 10px;">
                                                    <p
                                                        style="font-size: 14px; line-height: 23px;font-weight: 600; color: #101828; font-style: italic;">
                                                        {!! nl2br($quoteTask->description) !!}</p></td>
                                                <td class="{{ $quoteTask->value_class }}"
                                                    style="font-size: 14px;padding-bottom: 6px;color: #667085;text-align: right;">
                                                    ${{ $quoteTask->labor_amount ? number_format($quoteTask->labor_amount / 100, 2) : 0.00 }}</td>
                                                <td class="{{ $quoteTask->value_class }}"
                                                    style="padding-bottom: 6px;color: #667085;text-align: right;"></td>
                                                <td class="{{ $quoteTask->value_class }}"
                                                    style="padding-bottom: 6px;color: #667085;text-align: right;"></td>
                                            </tr>
                                            @foreach ($quoteTask->materials as $material)
                                                <tr>
                                                    <td colspan="4" style="height: 6px;"></td>
                                                </tr>
                                                <tr style="line-height: 12px;" class="{{ $quoteTask->value_class }}">
                                                    <td class="{{ $quoteTask->value_class }}">
                                                        <p style="font-size: 14px; color: #101828;">
                                                            • {{ $material->label }}
                                                            - {{ $material->quantity }} </p></td>
                                                    <td class="{{ $quoteTask->value_class }}"
                                                        style="color: #667085;text-align: right;"></td>
                                                    <td class="{{ $quoteTask->value_class }}"
                                                        style="font-size: 14px;padding-bottom: 6px;color: #667085;text-align: right;">
                                                        ${{ $material->amount ? number_format($material->amount/100, 2) : 0.00 }}</td>
                                                    <td class="{{ $quoteTask->value_class }}"
                                                        style="color: #667085;text-align: right;"></td>
                                                </tr>
                                            @endforeach
                                        </table>
                                    </td>
                                </tr>

                                @if (!$loop->last)
                                    <tr>
                                        <td colspan="4">
                                            <hr class="tab-divider">
                                        </td>
                                    </tr>
                                @endif
                            @endforeach
                            <tr>
                                <td>
                                    <div class="spacer" style="padding: 13px 0;"></div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <hr class="tab-divider">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="spacer" style="padding: 7px 0;"></div>
                                </td>
                            </tr>
                            <tr>
                                <td class="{{ $quoteData->line_item_total_amount_label_class }}"
                                    style="width:60%; text-align: right;font-weight: 500;">{{ $quoteData->line_item_total_amount_label }}</td>
                                <td></td>
                                <td></td>
                                <td class="{{ $quoteData->line_item_total_amount_label_class }}"
                                    style="font-weight: 500;text-align: right;">
                                    ${{ $lineItemTotal ? number_format($lineItemTotal/100,2) : 0.00 }}</td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

@if (!empty($quoteData->quoteTasks))
    <div class="page-break"></div>
    <table align="center" border="0" cellpadding="25" cellspacing="25" style="margin: 0 auto; background-color: #fff;"
           width="960px">
        @php $media = $quoteData->quoteTasks->pluck('media')->collapse() @endphp

        @if ($media->count())
            <tr>
                <td>
                    <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <td>
                                <p style="color: #667085;font-size: 16px;font-weight: 500;margin-bottom: 6px;">Media</p>
                            </td>
                        </tr>
                        <tr>
                            @foreach ($media as $mediaUri)
                                <td style="width: 100%;height: 620px;display: inline-block;background-size: contain;background-position: center;background-repeat: no-repeat;margin-bottom: 15px;background-image: url(data:image/{{ $mediaUri['extension'] }};base64,{{ base64_encode(@file_get_contents($mediaUri['uri'])) }})"></td>
                            @endforeach
                        </tr>
                    </table>
                </td>
            </tr>
        @endif
    </table>
@endif
</body>
</html>

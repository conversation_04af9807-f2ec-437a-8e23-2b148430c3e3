@php
    use App\Helpers\Helper;
    use Carbon\CarbonImmutable;
@endphp
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #{{ $invoiceData->invoice->invoice_number }} - WO #{{ $invoiceData->work_order->work_order_number }}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <style>
        body {
            font-family: "Inter", sans-serif;
        }

        p {
            margin: 0;
        }

        thead {
            display: table-row-group;
        }

        tfoot {
            display: table-row-group;
        }

        .page {
            page-break-inside: avoid;
            overflow: hidden;
            page-break-before: always;
        }
    </style>
</head>

<body
    style="text-align: center; margin: 0;padding: 0px; -webkit-text-size-adjust: 100%;background-color: #ffffff; color: #000000"
    align="center">
    <div style="width: 100%; background-color: #ffffff;">
        <table align="center" style="text-align: center; vertical-align: top; width: 100%;">
            <tbody>
                <tr>
                    <td width="168px" style="border-bottom: solid 1px #D0D5DD;padding-bottom: 20px;">
                        <img src="data:image/jpeg;base64,{{ base64_encode(@file_get_contents($invoiceData->organization->logo_uri)) }}"
                            alt="" style="width: 200px" />
                    </td>
                    <td style="border-bottom: solid 1px #D0D5DD;padding-bottom: 20px;">
                        <p style="text-align: end;font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;">
                            {{ $invoiceData->organization->address }} {{ $invoiceData->organization->zip_code }}
                        </p>
                        <p style="text-align: end;font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;">
                            {{ $invoiceData->organization->phone_number }}
                        </p>
                    </td>
                </tr>
            </tbody>
        </table>
        <div
            style="border: solid 1px #D0D5DD;background-color: #F9FAFB;border-radius: 12px;padding: 25px 25px 24px 25px;margin-top: 20px;">
            <table width="100%" style="margin-bottom: 46px;">
                <tbody>
                    <tr>
                        <td style="vertical-align:top;width: 30%;padding-right: 10px;">
                            <p
                                style="margin-bottom: 6px;font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;text-align: start;">
                                Billed to
                            </p>
                            <p
                                style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: start;">
                                {{ $invoiceData->property->name }}
                            </p>
                        </td>
                        <td style="vertical-align:top;width: 30%;padding-right: 10px;">
                            <p
                                style="margin-bottom: 6px;font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;text-align: start;">
                                Resident</p>
                            <p
                                style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: start;">
                                {{ $invoiceData->resident->name }}</p>
                        </td>
                        <td style="vertical-align:top;width: 17%;padding-right: 10px;">
                            <p
                                style="margin-bottom: 6px;font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;text-align: start;">
                                Invoice Date</p>
                            <p
                                style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: start;">
                                {{ $invoiceData->invoice->invoice_date->timezone($invoiceData->work_order->timezone)->format('d M, Y') }}
                            </p>
                        </td>
                        <td style="vertical-align:top;width: 23%;padding-right: 10px;">
                            <p
                                style="margin-bottom: 6px;font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;text-align: end;">
                                Amount Due (USD)</p>
                            <p
                                style="font-size: 32px;line-height: 35px;font-weight: 700;color: #1369E9;text-align: end;word-break: break-all;">
                                ${{ number_format($invoiceData->invoice->amount_due / 100, 2) }}</p>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;width: 30%;padding-right: 10px;">
                            <p
                                style="font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;text-align: start;margin-top: -15px;">
                                {{ $invoiceData->property->street_address }}</p>
                            <p
                                style="font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;text-align: start;">
                                {{ $invoiceData->property->address }} -
                                {{ $invoiceData->property->zip_code }}
                                <br> {{ $invoiceData->property->phone_number }}
                            </p>
                        </td>
                        <td style="vertical-align:top;width: 30%;padding-right: 10px;">
                            <p
                                style="font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;text-align: start;margin-top: -15px;">
                                {{ $invoiceData->resident->street_address }}</p>
                            <p
                                style="font-size: 16px;line-height: 22px;font-weight: number_format500;color: #667085;text-align: start;">
                                {{ $invoiceData->resident->address }}, -
                                {{ $invoiceData->resident->zip_code }} <br>
                                {{ $invoiceData->resident->phone_number }}
                            </p>
                        </td>
                        <td style="vertical-align:top;width: 17%;padding-right: 10px;">
                            <p
                                style="margin-bottom: 6px;font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;text-align: start;">
                                Due Date</p>
                            <p
                                style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: start;">
                                {{ !empty($invoiceData->work_order->due_date) ? CarbonImmutable::parse($invoiceData->work_order->due_date)->format('d M, Y') : '' }}
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <td style="vertical-align:top;padding-top: 25px;" colspan="2">
                            <p
                                style="margin-bottom: 6px;font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;text-align: start;">
                                Service Type</p>
                            <p
                                style="margin-bottom: 6px;font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: start;">
                                {{ $invoiceData->problem->category }}
                                > {{ $invoiceData->problem->sub_category }} >
                                {{ $invoiceData->problem->diagnosis }}</p>
                            <p style="font-size: 14px;line-height: 20px;color: #101828;text-align: start;">
                                {{ $invoiceData->work_order->description }}</p>
                        </td>
                        <td style="vertical-align:top;padding-top: 25px;" colspan="2">
                            <p
                                style="margin-bottom: 6px;font-size: 16px;line-height: 22px;font-weight: 500;color: #667085;text-align: end;">
                                WO ID</p>
                            <p
                                style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: end;">
                                {{ $invoiceData->work_order->work_order_number }}</p>
                        </td>
                    </tr>
                </tbody>
            </table>
            <table width="100%" style="border-collapse: collapse;">
                <thead>
                    <tr>
                        <th
                            style="font-size: 12px;line-height: 1;letter-spacing: 1px;font-weight: 500;color: #667085;text-transform: uppercase;text-align: start;border-top: solid 1px #D0D5DD;border-bottom: solid 1px #D0D5DD;padding: 14px 0;width: 55%;">
                            Line Detail</th>
                        <th
                            style="font-size: 12px;line-height: 1;letter-spacing: 1px;font-weight: 500;color: #667085;text-transform: uppercase;text-align: end;border-top: solid 1px #D0D5DD;border-bottom: solid 1px #D0D5DD;padding: 14px 0;width: 15%;">
                            LAbor</th>
                        <th
                            style="font-size: 12px;line-height: 1;letter-spacing: 1px;font-weight: 500;color: #667085;text-transform: uppercase;text-align: end;border-top: solid 1px #D0D5DD;border-bottom: solid 1px #D0D5DD;padding: 14px 0;width: 15%;">
                            MAterial</th>
                        <th
                            style="font-size: 12px;line-height: 1;letter-spacing: 1px;font-weight: 500;color: #667085;text-transform: uppercase;text-align: end;border-top: solid 1px #D0D5DD;border-bottom: solid 1px #D0D5DD;padding: 14px 0;width: 15%;">
                            Line Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($invoiceDetails as $key => $invoiceDetail)
                        {{-- Quote related trip invoice details --}}
                        @if (isset($invoiceDetail['line_item_type']) &&
                                $invoiceDetail['line_item_type'] == App\Enums\InvoiceLineItemTypes::QUOTE_TASK())
                            <tr>
                                <td
                                    style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: start;padding-top: 24px;">
                                    Quote -
                                    {{ isset($invoiceDetail['scheduled_start_time']) ? $invoiceDetail['scheduled_start_time']->timezone($invoiceData->work_order->timezone)->format('d M, Y') : '' }}
                                </td>
                                <td
                                    style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: end;">
                                    ${{ isset($invoiceDetail['labor_cost_in_cents']) ? number_format($invoiceDetail['labor_cost_in_cents'] / 100, 2) : '0.00' }}
                                </td>
                                <td
                                    style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: end;">
                                    ${{ isset($invoiceDetail['material_cost_in_cents']) ? number_format($invoiceDetail['material_cost_in_cents'] / 100, 2) : '0.00' }}
                                </td>
                                <td
                                    style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: end;">
                                    ${{ isset($invoiceDetail['line_total']) ? number_format($invoiceDetail['line_total'] / 100, 2) : '0.00' }}
                                </td>
                            </tr>
                            <tr>
                                <td
                                    style="font-size: 14px;line-height: 20px;font-weight: 600;color: #101828;text-align: start;font-style: italic;">
                                    {{ isset($invoiceDetail['service_notes']) ? $invoiceDetail['service_notes'] : '' }}
                                </td>
                                <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;"></td>
                                <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;"></td>
                                <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;"></td>
                            </tr>

                            @foreach ($invoiceDetail['quote_tasks'] as $key => $quoteTask)
                                <tr>
                                    <td style="font-size: 14px;line-height: 20px;color: #101828;text-align: start;">
                                        <span style="font-weight: 600;"> Task #{{ $key }}:</span>
                                        {{ $quoteTask['description'] }}

                                    </td>
                                    <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;">
                                        ${{ isset($invoiceDetail['labor_cost_in_cents']) ? number_format($quoteTask['labor_cost_in_cents'] / 100, 2) : '0.00' }}
                                    </td>
                                    <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;"></td>
                                    <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;"></td>
                                </tr>
                                @if (isset($quoteTask['materials']))
                                    @foreach ($quoteTask['materials'] as $material)
                                        <tr>
                                            <td
                                                style="font-size: 14px;line-height: 20px;color: #101828;text-align: start;">
                                                {{ $material['description'] }} - {{ $material['quantity'] }} </td>
                                            <td
                                                style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;">
                                            </td>
                                            <td
                                                style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;">
                                                ${{ isset($material['cost']) ? number_format($material['cost'] / 100, 2) : '0.00' }}
                                            </td>
                                            <td
                                                style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;">
                                            </td>
                                        </tr>
                                    @endforeach
                                @endif
                            @endforeach
                        @else
                            {{-- Trip invoice details --}}
                            <tr>
                                @if (! empty($invoiceDetail['trip_type']) && $invoiceDetail['trip_type'] === \App\Enums\ScheduleTypes::LULA_PRO())
                                    <td
                                        style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: start;padding-top: 24px;">
                                        {{ ! empty($invoiceDetail['service_category']) ? $invoiceDetail['service_category'] : '' }}
                                    </td>
                                @else
                                    <td
                                        style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: start;padding-top: 24px;">
                                        Trip #{{ $key }} -
                                        {{ isset($invoiceDetail['scheduled_start_time']) ? $invoiceDetail['scheduled_start_time']->timezone($invoiceData->work_order->timezone)->format('d M, Y') : '' }}
                                    </td>
                                @endif
                                <td
                                    style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: end;padding-top: 24px;">
                                    ${{ isset($invoiceDetail['labor_cost_in_cents']) ? number_format($invoiceDetail['labor_cost_in_cents'] / 100, 2) : '0.00' }}
                                </td>
                                <td
                                    style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: end;padding-top: 24px;">
                                    ${{ isset($invoiceDetail['material_cost_in_cents']) ? number_format($invoiceDetail['material_cost_in_cents'] / 100, 2) : '0.00' }}
                                </td>
                                <td
                                    style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: end;padding-top: 24px;">
                                    ${{ isset($invoiceDetail['line_total']) ? number_format($invoiceDetail['line_total'] / 100, 2) : '0.00' }}
                                </td>
                            </tr>
                            <tr>
                                @if ($invoiceDetail['line_item_type'] == App\Enums\InvoiceLineItemTypes::NO_SHOW_FEE())
                                    <td
                                        style="font-size: 14px;line-height: 20px;font-weight: 600;color: #101828;text-align: start;font-style: italic;">
                                        Resident No-Show Fee
                                    </td>
                                @elseif ($invoiceDetail['line_item_type'] == App\Enums\InvoiceLineItemTypes::TRIP_FEE())
                                    <td
                                        style="font-size: 14px;line-height: 20px;font-weight: 600;color: #101828;text-align: start;font-style: italic;">
                                        Trip Fee
                                    </td>
                                @else
                                    <td
                                        style="font-size: 14px;line-height: 20px;font-weight: 600;color: #101828;text-align: start;font-style: italic;">
                                        {{ isset($invoiceDetail['service_notes']) ? $invoiceDetail['service_notes'] : '' }}
                                    </td>
                                    <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;">
                                        ${{ isset($invoiceDetail['labor_cost_in_cents']) ? number_format($invoiceDetail['labor_cost_in_cents'] / 100, 2) : '0.00' }}
                                    </td>
                                @endif

                                <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;"></td>
                                <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;"></td>
                            </tr>

                            @if (isset($invoiceDetail['materials']))
                                @foreach ($invoiceDetail['materials'] as $material)
                                    <tr>
                                        <td style="font-size: 14px;line-height: 20px;color: #101828;text-align: start;">
                                            {{ $material['description'] }} - {{ $material['quantity'] }}</td>
                                        <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;">
                                        </td>
                                        <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;">
                                            ${{ isset($material['cost']) ? number_format($material['cost'] / 100, 2) : '0.00' }}
                                        </td>
                                        <td style="font-size: 14px;line-height: 20px;color: #667085;text-align: end;">
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                        @endif
                    @endforeach
                    <tr>
                        <td colspan="4" style="padding-bottom: 46px;"></td>
                    </tr>
                    <tr>
                        <td colspan="1"
                            style="font-size: 16px;line-height: 22px;font-weight: 500;color: #101828;text-align: end;border-top: solid 1px #D0D5DD;padding-top: 24px;">
                            Total</td>
                        <td colspan="3"
                            style="font-size: 16px;line-height: 22px;font-weight: 500;color: #1369E9;text-align: end;border-top: solid 1px #D0D5DD;padding-top: 24px;">
                            ${{ number_format($invoiceData->invoice->amount_due / 100, 2) }}</td>
                    </tr>

                </tbody>
            </table>
        </div>
        {{-- Trip Before After Photos --}}
        @if (!empty($images))
            @foreach ($images as $image)
                <div class="page">
                    @if (isset($image['before-media']))
                        <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%">
                            <tr>
                                <td>
                                    <p style="color: #667085;font-size: 16px;margin-bottom: 6px;text-align: left;">
                                        Trip Date
                                    </p>
                                    <p style="color: #101828;font-size: 16px;margin-bottom: 16px;text-align: left;">
                                        {{ isset($image['scheduled_start_time']) ? $image['scheduled_start_time']->timezone($invoiceData->work_order->timezone)->format('d M, Y') : '' }}
                                    </p>
                                </td>
                            </tr>
                            @if (isset($image['before-media']))
                                <tr>
                                    <td>
                                        <p
                                            style="color: #667085;font-size: 16px;text-align: left;padding-top: 6px;padding-bottom: 6px">
                                            Before Photos
                                        </p>
                                    </td>
                                </tr>
                                <tr align="left">
                                    @foreach ($image['before-media'] as $beforeMedia)
                                        <td
                                            style="width: 100%;height: 620px;display: inline-block;background-size: contain;background-position: center;background-repeat: no-repeat;margin-bottom: 15px;background-image: url(data:image/{{ $beforeMedia['extension'] }};base64,{{ base64_encode(@file_get_contents($beforeMedia['url'])) }})">
                                        </td>
                                    @endforeach
                                </tr>
                            @endif
                        </table>
                    @endif
                    @if (isset($image['after-media']))
                        <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%">
                            @if (isset($image['after-media']))
                                <tr>
                                    <td>
                                        <p
                                            style="color: #667085;font-size: 16px;text-align: left;padding-top: 6px;padding-bottom: 6px;">
                                            After Photos
                                        </p>
                                    </td>
                                </tr>
                                <tr align="left">
                                    @foreach ($image['after-media'] as $afterMedia)
                                        <td
                                            style="width: 100%;height: 620px;display: inline-block;background-size: contain;background-position: center;background-repeat: no-repeat;margin-bottom: 15px;background-image: url(data:image/{{ $afterMedia['extension'] }};base64,{{ base64_encode(@file_get_contents($afterMedia['url'])) }})">
                                        </td>
                                    @endforeach
                                </tr>
                            @endif
                        </table>
                    @endif
                </div>
            @endforeach
        @endif
</body>

</html>

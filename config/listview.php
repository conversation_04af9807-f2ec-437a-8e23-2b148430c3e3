<?php

/*
|--------------------------------------------------------------------------
| List View
|--------------------------------------------------------------------------
|
| Here you may configure the default values of view
|
*/
return [
    'service_request_default_payload' => [
        'filters' => [
            'applied' => [
                'group_op' => null,
                'fl_group' => [],
            ],
            'operators' => [
                'is' => [
                    'value' => 'is',
                    'label' => 'is',
                ],
                'is_not' => [
                    'value' => 'is_not',
                    'label' => 'Is not',
                ],
                'is_between' => [
                    'value' => 'is_between',
                    'label' => 'Is between',
                ],
                'is_after' => [
                    'value' => 'is_after',
                    'label' => 'Is after',
                ],
                'is_before' => [
                    'value' => 'is_before',
                    'label' => 'Is before',
                ],
            ],
            'fields' => [
                [
                    'value' => 'status',
                    'label' => 'Status',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                        'is_not' => 'multi-select',
                    ],
                ],
                [
                    'value' => 'assignee',
                    'label' => 'Assignee',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                        'is_not' => 'multi-select',
                    ],
                ],
                [
                    'value' => 'imported_from',
                    'label' => 'Imported From',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                        'is_not' => 'multi-select',
                    ],
                ],
                [
                    'value' => 'added_date',
                    'label' => 'Added Date',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'select',
                        'is_not' => 'select',
                        'is_between' => 'date-range-picker',
                        'is_after' => 'select',
                        'is_before' => 'select',
                    ],
                ],
            ],
        ],
        'grouping' => [
            'g_by' => [
                'label' => 'None',
                'value' => 'none',
            ],
            'default' => 'none',
            'g_options' => [
                [
                    'label' => 'None',
                    'value' => 'none',
                ],
                [
                    'label' => 'Status',
                    'value' => 'status',
                ],
                [
                    'label' => 'Assignee',
                    'value' => 'assignee',
                ],
                [
                    'label' => 'Imported From',
                    'value' => 'imported_from',
                ],
            ],
        ],
        'columns' => [
            [
                'label' => 'Id',
                'value' => 'service_request_number',
                'isEditable' => false,
                'isStaticColumn' => true,
                'selected' => true,
            ],
            [
                'label' => 'Address',
                'value' => 'address',
                'isEditable' => false,
                'isStaticColumn' => true,
                'selected' => true,
            ],
            [
                'label' => 'Type',
                'value' => 'type', // Service request type table
                'isEditable' => false,
                'isStaticColumn' => true,
                'selected' => true,
            ],
            [
                'label' => 'Assignee',
                'value' => 'assignee',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            [
                'label' => 'Status',
                'value' => 'status',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            [
                'label' => 'Imported From',
                'value' => 'imported_from', // Service request source table
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            [
                'label' => 'Added',
                'value' => 'added_date',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
        ],
    ],

    /**
     *  payload of a default work order view. It is used for view seeder and testing.
     */
    'work_order_default_payload' => [
        'filters' => [
            'applied' => [
                'group_op' => null,
                'fl_group' => [],
            ],
            'operators' => [
                'is' => [
                    'value' => 'is',
                    'label' => 'is',
                ],
                'is_not' => [
                    'value' => 'is_not',
                    'label' => 'Is not',
                ],
                'is_between' => [
                    'value' => 'is_between',
                    'label' => 'Is between',
                ],
                'is_after' => [
                    'value' => 'is_after',
                    'label' => 'Is after',
                ],
                'is_before' => [
                    'value' => 'is_before',
                    'label' => 'Is before',
                ],
            ],
            'fields' => [
                [
                    'value' => 'status',
                    'label' => 'Status',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                        'is_not' => 'multi-select',
                    ],
                ],
                [
                    'value' => 'priority',
                    'label' => 'Priority',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                        'is_not' => 'multi-select',
                    ],
                ],
                // Currently this option is no display for filter
                // [
                //     'value' => 'assignee',
                //     'label' => 'Assignee',
                //     'ops' => [
                //         'is',
                //     ],
                //     'values_type' => [
                //         'is' => 'multi-select',
                //     ],
                // ],
                [
                    'value' => 'technician',
                    'label' => 'Technician',
                    'ops' => [
                        'is',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                    ],
                ],
                [
                    'value' => 'category',
                    'label' => 'Category',
                    'ops' => [
                        'is',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                    ],
                ],
                [
                    'value' => 'due_date',
                    'label' => 'Due Date',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'select',
                        'is_not' => 'select',
                        'is_between' => 'date-range-picker',
                        'is_after' => 'select',
                        'is_before' => 'select',
                    ],
                ],
                [
                    'value' => 'created_date',
                    'label' => 'Created Date',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'select',
                        'is_not' => 'select',
                        'is_between' => 'date-range-picker',
                        'is_after' => 'select',
                        'is_before' => 'select',
                    ],
                ],
                [
                    'value' => 'health_score',
                    'label' => 'Health Score',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                    ],
                ],
                // Currently this option is no display for filter. Because the scheduled_date is not a single felid for a work oder it depends the work order task
                // [
                //     'value' => 'scheduled_date',
                //     'label' => 'Scheduled Date',
                //     'ops' => [
                //         'is',
                //         'is_not',
                //     ],
                //     'values_type' => [
                //         'is' => 'select',
                //         'is_not' => 'select',
                //         'is_between' => 'date-range-picker',
                //         'is_after' => 'select',
                //         'is_before' => 'select',
                //     ],
                // ],
            ],
        ],
        'grouping' => [
            'g_by' => [
                'label' => 'None',
                'value' => 'none',
            ],
            'default' => 'none',
            'g_options' => [
                [
                    'label' => 'None',
                    'value' => 'none',
                ],
                [
                    'label' => 'Status',
                    'value' => 'status',
                ],
                [
                    'label' => 'Priority',
                    'value' => 'priority',
                ],
                [
                    'label' => 'Category',
                    'value' => 'category',
                ],
                [
                    'label' => 'Technician',
                    'value' => 'technician',
                ],
                [
                    'label' => 'Health Score',
                    'value' => 'health_score',
                ],
                // [
                //     'label' => 'Assignee',
                //     'value' => 'assignee',
                // ],
            ],
        ],
        'columns' => [
            [
                'label' => 'Category',
                'value' => 'category',
                'isEditable' => false,
                'selected' => true,
                'isStaticColumn' => true,
                'sub_fields' => [
                    // Note::WO ID is not in this scope
                    // [
                    //     'label' => 'WO ID',
                    //     'value' => 'wo_id',
                    //     'isEditable' => true,
                    //     'selected' => false,
                    // ],
                    // // Note::Tag is not in this scope
                    // [
                    //     'label' => 'Tags',
                    //     'value' => 'tags',
                    //     'isEditable' => true,
                    //     'selected' => false,
                    // ],
                    [
                        'label' => 'Property Address',
                        'value' => 'property_address',
                        'isEditable' => true,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'label' => 'Status',
                'value' => 'status',
                'isEditable' => false,
                'isStaticColumn' => true,
                'selected' => true,
            ],
            [
                'label' => 'Technician',
                'value' => 'technician',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            // [
            //     'label' => 'Assignee',
            //     'value' => 'assignee',
            //     'isEditable' => true,
            //     'isStaticColumn' => false,
            //     'selected' => true,
            // ],
            [
                'label' => 'Priority',
                'value' => 'priority',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            [
                'label' => 'Due Date',
                'value' => 'due_date',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            [
                'label' => 'Scheduled Date',
                'value' => 'scheduled_date',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            [
                'label' => 'Created Date',
                'value' => 'created_date',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            [
                'label' => 'Health Score',
                'value' => 'health_score',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
        ],
    ],

    'quotes_default_payload' => [
        'filters' => [
            'applied' => [
                'group_op' => null,
                'fl_group' => [],
            ],
            'operators' => [
                'is' => [
                    'value' => 'is',
                    'label' => 'is',
                ],
                'is_not' => [
                    'value' => 'is_not',
                    'label' => 'Is not',
                ],
                'is_between' => [
                    'value' => 'is_between',
                    'label' => 'Is between',
                ],
                'is_after' => [
                    'value' => 'is_after',
                    'label' => 'Is after',
                ],
                'is_before' => [
                    'value' => 'is_before',
                    'label' => 'Is before',
                ],
            ],
            'fields' => [
                [
                    'value' => 'status',
                    'label' => 'Status',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                        'is_not' => 'multi-select',
                    ],
                ],
                [
                    'value' => 'category',
                    'label' => 'Category',
                    'ops' => [
                        'is',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                    ],
                ],
                [
                    'value' => 'assignee',
                    'label' => 'Assignee',
                    'ops' => [
                        'is',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                    ],
                ],
                [
                    'value' => 'submitted_by',
                    'label' => 'Submitted By',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                    ],
                ],
                [
                    'value' => 'submitted_date',
                    'label' => 'Submitted Date',
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                    ],
                ],
                [
                    'value' => 'tag',
                    'label' => 'Tags',
                    'ops' => [
                        'is',
                    ],
                    'values_type' => [
                        'is' => 'multi-select',
                    ],
                ],
            ],
        ],
        'grouping' => [
            'g_by' => [
                'label' => 'None',
                'value' => 'none',
            ],
            'default' => 'none',
            'g_options' => [
                [
                    'label' => 'None',
                    'value' => 'none',
                ],
                [
                    'label' => 'WO',
                    'value' => 'work_order_number',
                ],
                [
                    'label' => 'Status',
                    'value' => 'status',
                ],
                [
                    'label' => 'Category',
                    'value' => 'category',
                ],
                [
                    'label' => 'Assignee',
                    'value' => 'assignee',
                ],
                [
                    'label' => 'Submitted By',
                    'value' => 'submitted_by',
                ],
                [
                    'label' => 'Tags',
                    'value' => 'tag',
                ],
            ],
        ],
        'columns' => [
            [
                'label' => 'Category',
                'value' => 'category',
                'isEditable' => false,
                'selected' => true,
                'isStaticColumn' => true,
                'sub_fields' => [
                    [
                        'label' => 'Property Address',
                        'value' => 'property_address',
                        'isEditable' => true,
                        'selected' => true,
                    ],
                ],
            ],
            [
                'label' => 'Parent WO ID',
                'value' => 'work_order_number',
                'isEditable' => false,
                'isStaticColumn' => true,
                'selected' => true,
            ],
            [
                'label' => 'Status',
                'value' => 'status',
                'isEditable' => false,
                'isStaticColumn' => true,
                'selected' => true,
            ],
            [
                'label' => 'Total',
                'value' => 'total',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            [
                'label' => 'Approved Total',
                'value' => 'total_approved',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            [
                'label' => 'Submitted By',
                'value' => 'submitted_by',
                'isEditable' => true,
                'isStaticColumn' => false,
                'sub_fields' => [
                    [
                        'label' => 'Submitted Date',
                        'value' => 'submitted_date',
                        'isEditable' => true,
                        'selected' => true,
                    ],
                ],
                'selected' => true,
            ],
            [
                'label' => 'Assignee',
                'value' => 'assignee',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            [
                'label' => 'Approved / Rejected By',
                'value' => 'approved_by',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
            [
                'label' => 'WO Tags',
                'value' => 'tag',
                'isEditable' => true,
                'isStaticColumn' => false,
                'selected' => true,
            ],
        ],
    ],

    'users_list_default_payload' => [
        'columns' => [
            [
                'label' => 'Name',
                'value' => 'name',
                'selected' => true,
                'isEditable' => false,
                'isStaticColumn' => true,
            ],
            [
                'label' => 'Role',
                'value' => 'role',
                'selected' => true,
                'isEditable' => true,
                'isStaticColumn' => false,
            ],
            [
                'label' => 'Last Activity',
                'value' => 'lastActivity',
                'selected' => true,
                'isEditable' => true,
                'isStaticColumn' => false,
            ],
            [
                'label' => 'Account Status',
                'value' => 'accountStatus',
                'selected' => true,
                'isEditable' => true,
                'isStaticColumn' => false,
            ],

        ],
        'filters' => [
            'fields' => [
                [
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'label' => 'Role',
                    'value' => 'role',
                    'values_type' => [
                        'is' => 'multi-select',
                        'is_not' => 'multi-select',
                    ],
                ],
                [
                    'ops' => [
                        'is',
                        'is_not',
                    ],
                    'label' => 'Status',
                    'value' => 'status',
                    'values_type' => [
                        'is' => 'select',
                        'is_not' => 'select',
                    ],
                ],
            ],
            'applied' => [
                'fl_group' => [],
                'group_op' => null,
            ],
            'operators' => [
                'is' => [
                    'label' => 'is',
                    'value' => 'is',
                ],
                'is_not' => [
                    'label' => 'Is not',
                    'value' => 'is_not',
                ],
            ],
        ],
        'grouping' => [
            'g_by' => [
                'label' => 'None',
                'value' => 'none',
            ],
            'default' => 'none',
            'g_options' => [
                [
                    'label' => 'None',
                    'value' => 'none',
                ],
                [
                    'label' => 'Status',
                    'value' => 'status',
                ],
                [
                    'label' => 'Roles',
                    'value' => 'role',
                ],
            ],
        ],
    ],
    'calendar_list_default_payload' => [
        'calendarFilter' => [
            'technician' => null,
            'calendarMode' => [
                'value' => 'weekday',
                'label' => 'Weekday',
            ],
            'settings' => [
                'limitToWorkingHours' => true,
            ],
        ],
    ],
];

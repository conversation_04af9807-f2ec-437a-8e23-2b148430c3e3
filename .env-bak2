APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:FooV+qfAJn/ebH15xUUr1x22jVGtN48cXxg+RV3PcWM=
APP_DEBUG=true
APP_URL=http://lula-saas-platform.test

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

LOG_REQUESTS_TO_DB=

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=lula_saas_db
DB_USERNAME=root
DB_PASSWORD=root

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=s3
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=redis
REDIS_PASSWORD=secret_redis
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=LEcBU3OsDP6vyH8smTYu+/+juaBt8grxoIk08un2
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=test
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1
SOKETI_DEBUG=test

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

TWILIO_DEBUG_MODE=
TWILIO_FROM=
TWILIO_AUTH_TOKEN=
TWILIO_ACCOUNT_SID=
TWILIO_DEBUG_TO_NUMBER=
TWILIO_DEBUG_WHITELISTED_NUMBERS=

GOOGLE_MAP_API_KEY=test

#Provider User Pool
PROVIDER_POOL_ID=us-east-1_Bfi6g48PL
PROVIDER_CLIENT_ID=698rcut7fbpbq8uv090nkgfv1e
PROVIDER_AUTH_DOMAIN=https://vendor-local.auth.us-east-1.amazoncognito.com

UID=
GID=

LOCAL_IP=
CONTAINER_IP=

MEDIA_CACHE_DRIVER=test
MEDIA_STORAGE_DRIVER=test
MEDIA_API_ROUTES=test
MEDIA_CACHE_FILE_EXPIRE_IN_SECONDS=test

APPFOLIO_API_URL=test
APPFOLIO_DEVELOPER_ID=test
APPFOLIO_WO_SYNC_INTERVAL=test
APPFOLIO_SCRAPER_API_URL=test

HEIC_IMAGE_CONVERSION_API=test

#For basic authentication
PUBLIC_BASIC_AUTH_USERNAME=
PUBLIC_BASIC_AUTH_PASSWORD=

HORIZON_BASIC_AUTH_USERNAME=test
HORIZON_BASIC_AUTH_PASSWORD=test

DEVELOPER_DEBUGGING_SLOW_QUERY_THRESHOLD=
DEVELOPER_DEBUGGING_SLOW_QUERY_ENABLED=

DEVELOPER_NOTIFICATION_ENABLED=test
DEVELOPER_NOTIFICATION_ADDRESSES=test
DEVELOPER_ALERT_NOTIFY_EMAILS=test

INVOICE_DEFAULT_HOURLY_RATE_IN_CENTS=6500
INVOICE_DEFAULT_DRIVE_RATE_IN_CENTS=6500
INVOICE_DEFAULT_NO_SHOW_FEE_IN_CENTS=5000

AWS_LOCATION_KEY=dev
RESET_LINK_EXPIRATION=30
